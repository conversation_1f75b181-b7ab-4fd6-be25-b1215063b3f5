<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <!--<bean id="paymentPlanOverdueMQSender"
          class="com.facishare.paas.appframework.common.mq.RocketMQMessageSender"
          p:configName="payment-plan-overdue-mq"></bean>

    <bean id="paymentDHTMQSender"
          class="com.facishare.paas.appframework.common.mq.RocketMQMessageSender"
          p:configName="payment-dht-mq"></bean>-->

    <bean id="paymentPlanOverdueMQSender" class="com.fxiaoke.rocketmq.producer.AutoConfMQProducer">
        <constructor-arg name="configName" value="fs-crm-task-sfa-mq.ini"/>
        <constructor-arg name="sectionNames" value="payment-plan-overdue-mq-producer"/>
    </bean>

    <bean id="paymentDHTMQSender" class="com.fxiaoke.rocketmq.producer.AutoConfMQProducer">
        <constructor-arg name="configName" value="fs-crm-task-sfa-mq.ini"/>
        <constructor-arg name="sectionNames" value="payment-dht-mq-producer"/>
    </bean>

   <!-- <bean id="newopportunityEnginePullMQSender"
          class="com.facishare.paas.appframework.common.mq.RocketMQMessageSender"
          p:configName="newopportunity-enginepull-mq"></bean>-->



</beans>