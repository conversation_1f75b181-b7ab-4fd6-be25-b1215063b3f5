package com.facishare.crm.newpayment.predefine.action;

import com.facishare.crm.customeraccount.constants.Constants;
import com.facishare.crm.customeraccount.constants.OrderPaymentConstants;
import com.facishare.crm.customeraccount.constants.PaymentConstants;
import com.facishare.crm.customeraccount.enums.ConfigKeyEnum;
import com.facishare.crm.customeraccount.predefine.manager.BizConfigManager;
import com.facishare.crm.customeraccount.predefine.manager.NewCustomerAccountManager;
import com.facishare.crm.payment.predefine.validator.PaymentValidator;
import com.facishare.crm.newpayment.predefine.manager.PaymentManager;
import com.facishare.crm.newpayment.predefine.manager.PaymentPlanManager;
import com.facishare.crm.customeraccount.model.DataUpdateModel;
import com.facishare.crm.newpayment.predefine.validator.RedPaymentAmountValidator;
import com.facishare.crm.customeraccount.predefine.validator.Validator;
import com.facishare.crm.customeraccount.util.ConfigCenter;
import com.facishare.crm.customeraccount.util.ObjectDataUtil;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.newpayment.predefine.manager.CrmCommonAccountsReceivableNoteManager;
import com.facishare.idempotent.Idempotent;
import com.facishare.idempotent.Serializer;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.appframework.flow.ApprovalFlowStartResult;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.appframework.metadata.cache.CacheKeys;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.impl.describe.BooleanFieldDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.StopWatch;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Idempotent(serializer = Serializer.Type.java)
public class PaymentAddAction extends StandardAddAction {
    private final RedPaymentAmountValidator redPaymentAmountValidator = SpringUtil.getContext().getBean(RedPaymentAmountValidator.class);
    private final PaymentManager paymentManager = SpringUtil.getContext().getBean(PaymentManager.class);
    private final PaymentPlanManager paymentPlanManager = SpringUtil.getContext().getBean(PaymentPlanManager.class);
    private final NewCustomerAccountManager newCustomerAccountManager = SpringUtil.getContext().getBean(NewCustomerAccountManager.class);
    private final BizConfigManager configManager = SpringUtil.getContext().getBean(BizConfigManager.class);
    private final CrmCommonAccountsReceivableNoteManager crmCommonAccountsReceivableNoteManager = SpringUtil.getContext().getBean(CrmCommonAccountsReceivableNoteManager.class);
    private boolean accountCheckEnable;
    private boolean enablePaymentEnterAccount;
    private Boolean paramEnterAccount;
    private String fundAccountId;
    boolean notForceCheck = true;

    @Override
    protected void before(Arg arg) {
        ObjectDataUtil.fillAmountIfNotPresent(arg);
        super.before(arg);
        if (!Objects.isNull(arg.getObjectData().get("notForceCheck"))) {
            notForceCheck = Boolean.parseBoolean(arg.getObjectData().get("notForceCheck").toString());
        }
        BizConfigManager.ConfigHolder configHolder = configManager.getConfigValue(actionContext.getUser(),
                Lists.newArrayList(ConfigKeyEnum.PAYMENT_WITH_DETAIL_ENTER_ACCOUNT.key, ConfigKeyEnum.NEW_CUSTOMER_ACCOUNT.key,
                        ConfigKeyEnum.ACCOUNT_RECEIVABLE_CONFIG.key, ConfigKeyEnum.ACCOUNT_CHECK_RULE.key, ConfigKeyEnum.PAYMENT_ENTER_ACCOUNT_CONFIG_KEY.key));
        accountCheckEnable = configHolder.isTrue(ConfigKeyEnum.ACCOUNT_CHECK_RULE.key, "", ConfigKeyEnum.ACCOUNT_CHECK_RULE.enabledValue);
        enablePaymentEnterAccount = configHolder.isTrue(ConfigKeyEnum.PAYMENT_ENTER_ACCOUNT_CONFIG_KEY.key, ConfigKeyEnum.PAYMENT_ENTER_ACCOUNT_CONFIG_KEY.enabledValue);

        //在before中记录是否需要入账的状态，新建回款时，将是否入账设置为false，在after中在判断是否需要入账
        paramEnterAccount = this.objectData.get(PaymentConstants.Field.EnterIntoAccount.apiName, Boolean.class);
        fundAccountId = this.objectData.get(PaymentConstants.Field.FundAccount.apiName, String.class);
        //存在对接或者函数新建回款时，选择支付方式为线上支付/二维码收款
        Validator.Context context = new Validator.Context(actionContext.getRequestContext(), configHolder);
        Validator.build().context(context).describe(objectDescribe).arg(arg).add(new PaymentValidator()).validate();
        ObjectDataUtil.copyOrderPaymentCustomFieldData(this.objectDescribe, arg);
        redPaymentAmountValidator.validateAmountExcess(actionContext.getTenantId(), arg);

        if (ObjectDataUtil.isSupportRedPayment(objectData)) {
            crmCommonAccountsReceivableNoteManager.checkPaymentMatchNote(actionContext.getUser(), objectData);
            crmCommonAccountsReceivableNoteManager.checkOpeningBalance(actionContext.getUser(), objectData, notForceCheck);
        }
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        findDescribe();
        List<String> functionCodes = Lists.newArrayList(super.getFuncPrivilegeCodes());
        ObjectDataDocument objectDataDocument = this.arg.getObjectData();
        if (Objects.nonNull(objectDataDocument)) {
            Boolean enterIntoAccount = objectDataDocument.toObjectData().get(PaymentConstants.Field.EnterIntoAccount.apiName, Boolean.class);
            IFieldDescribe enterIntoAccountField = this.objectDescribe.getFieldDescribe(PaymentConstants.Field.EnterIntoAccount.apiName);
            if (!objectDataDocument.containsKey(PaymentConstants.Field.EnterIntoAccount.apiName) && Objects.nonNull(enterIntoAccountField)) {
                //计算默认值
                BooleanFieldDescribe enterIntoAccountFieldDescribe = (BooleanFieldDescribe) enterIntoAccountField;
                Object defaultValue = enterIntoAccountFieldDescribe.getDefaultValue();
                if (Objects.nonNull(defaultValue)) {
                    enterIntoAccount = Boolean.valueOf(defaultValue.toString());
                    objectDataDocument.put(PaymentConstants.Field.EnterIntoAccount.apiName, enterIntoAccount);
                }
            }
            if (BooleanUtils.isTrue(enterIntoAccount)) {
                functionCodes.add(ObjectAction.ENTER_ACCOUNT.getActionCode());
            }
        }
        return functionCodes;
    }

    @Override
    protected void doSaveData() {
        BigDecimal amount = this.objectData.get(PaymentConstants.Field.Amount.apiName, BigDecimal.class, BigDecimal.ZERO);
        List<IObjectData> orderPaymentDataList = this.detailObjectData.getOrDefault(Utils.ORDER_PAYMENT_API_NAME, Lists.newArrayList());
        BigDecimal orderPaymentUsedAmount = orderPaymentDataList.stream().map(x -> x.get(OrderPaymentConstants.Field.PaymentAmount.apiName, BigDecimal.class, BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
        this.objectData.set(PaymentConstants.Field.PaymentAmount.apiName, orderPaymentUsedAmount);
        this.objectData.set(PaymentConstants.Field.AvailableAmount.apiName, amount.subtract(orderPaymentUsedAmount));
        this.objectData.set(PaymentConstants.Field.SubmitTime.apiName, System.currentTimeMillis());
        //paramEnterAccount为null时，表示传入的参数没有值且没有默认值，此时是否入账为空
        if (Objects.nonNull(paramEnterAccount)) {
            this.objectData.set(PaymentConstants.Field.EnterIntoAccount.apiName, false);
            this.objectData.set(PaymentConstants.Field.FundAccount.apiName, null);
        }
        super.doSaveData();
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        Map<String, List<ObjectDataDocument>> details = result.getDetails();
        List<IObjectData> orderPaymentList = Lists.newArrayList();
        if (MapUtils.isNotEmpty(details)) {
            orderPaymentList = details.getOrDefault(Utils.ORDER_PAYMENT_API_NAME, Lists.newArrayList()).stream().map(ObjectDataDocument::toObjectData).collect(Collectors.toList());
        }
        String lifeStatus = ObjectDataExt.of(result.getObjectData()).getLifeStatus().getCode();
        if (enablePaymentEnterAccount && BooleanUtils.isTrue(paramEnterAccount)) {
            //入账，更新客户账户数据
            IObjectData dataCopy = ObjectDataExt.of(this.objectData).copy();
            List<String> updateFields = Lists.newArrayList(PaymentConstants.Field.FundAccount.apiName, PaymentConstants.Field.EnterIntoAccount.apiName);
            this.objectData.set(PaymentConstants.Field.FundAccount.apiName, fundAccountId);
            this.objectData.set(PaymentConstants.Field.EnterIntoAccount.apiName, paramEnterAccount);
            if (ObjectLifeStatus.NORMAL.getCode().equals(lifeStatus)) {
                List<IObjectData> paymentBeforeUpdateList = Lists.newArrayList(dataCopy);
                List<IObjectData> paymentAfterUpdateList = Lists.newArrayList(this.objectData);
                DataUpdateModel.Arg paymentUpdateArg = DataUpdateModel.Arg.builder().dataListBeforeUpdate(paymentBeforeUpdateList).dataListAfterUpdate(paymentAfterUpdateList)
                        .updateFieldList(updateFields).build();
                newCustomerAccountManager.paymentInterIntoAccount(actionContext.getRequestContext(), this.objectData, accountCheckEnable, paymentUpdateArg);
            } else {
                serviceFacade.batchUpdateByFields(actionContext.getUser(), Lists.newArrayList(this.objectData), updateFields);
            }
        }
        stopWatch.lap("EnterAccount");
        if (!startApprovalFlowResult.containsKey(objectData.getId()) || startApprovalFlowResult.get(objectData.getId()) == ApprovalFlowStartResult.APPROVAL_NOT_EXIST) {
            paymentManager.sendDhtMq(actionContext.getUser(), "no_flow", orderPaymentList.stream().map(IObjectData::getId).collect(Collectors.toList()));
        }
        stopWatch.lap("DhtMQ");
        ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
        if (ConfigCenter.syncUpdatePaymentPlanStatus(actionContext.getTenantId())) {
            List<ObjectDataDocument> orderPaymentDataList = result.getDetails().getOrDefault(Utils.ORDER_PAYMENT_API_NAME, Lists.newArrayList());
            Set<String> planIds = orderPaymentDataList.stream().map(x -> x.toObjectData().get(OrderPaymentConstants.Field.PaymentPlan.apiName, String.class)).filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
            task.submit(() -> {
                log.info("start updateOrderPayment {}", objectData.getId());
                paymentPlanManager.updatePaymentPlan(actionContext.getUser(), planIds);
                log.info("end updateOrderPayment {}", objectData.getId());
            });
        }
        try {
            task.run();
        } catch (Exception ex) {
            log.error("context:{},task error", actionContext, ex);
        }
        List<String> orderIds = orderPaymentList.stream().map(x -> x.get(OrderPaymentConstants.Field.SalesOrder.apiName, String.class)).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        calculateOrderField(orderIds);
        stopWatch.lap("CalcOrder");
        return result;
    }

    private void calculateOrderField(List<String> orderIds) {
        if (CollectionUtils.empty(orderIds)) {
            return;
        }
        String lockKey = CacheKeys.detailLifeStatusUpdateLockKey(actionContext.getTenantId(), objectData.getDescribeApiName(), objectData.getId());
        RLock lock = infraServiceFacade.tryLock(500, 10000, TimeUnit.MILLISECONDS, lockKey);
        if (Objects.isNull(lock)) {
            log.info("try lock failed:{}", lockKey);
            return;
        }
        try {
            //计算订单上的待回款金额、已回款金额
            newCustomerAccountManager.calculateAndUpdateFormulaFields(actionContext.getRequestContext(), Utils.SALES_ORDER_API_NAME, orderIds,
                    Lists.newArrayList(Constants.RECEIVABLE_AMOUNT, Constants.PAYMENT_AMOUNT));
        } finally {
            infraServiceFacade.unlock(lock);
        }
    }
}
