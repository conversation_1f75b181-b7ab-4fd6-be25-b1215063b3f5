package com.facishare.crm.newpayment.predefine.controller;

import com.facishare.crm.customeraccount.constants.Constants;
import com.facishare.crm.customeraccount.util.ButtonUtil;
import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.predef.action.StandardAction;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.BooleanUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

public class OrderPaymentWebDetailController extends StandardWebDetailController {

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        LayoutDocument layoutDocument = newResult.getLayout();
        if (Objects.isNull(layoutDocument)) {
            return newResult;
        }
        ILayout layout = layoutDocument.toLayout();
        WebDetailLayout.of(layout).removeFields(Lists.newArrayList(Constants.FIELD_APPROVE_EMPLOYEE_ID));
        //下发删除按钮：作废并删除回款明细，功能权限走回款的功能权限
        ObjectDataExt orderPaymentData = ObjectDataExt.of(result.getData());
        if (!orderPaymentData.isInvalid() && !orderPaymentData.isLock()) {
            List<String> functionCodes = StandardAction.Invalid.getFunPrivilegeCodes();
            Map<String, Boolean> functionMap = serviceFacade.funPrivilegeCheck(controllerContext.getUser(), Utils.ORDER_PAYMENT_API_NAME, functionCodes);
            boolean hasFunction = functionCodes.stream().allMatch(x -> {
                Boolean function = functionMap.getOrDefault(x, Boolean.FALSE);
                return BooleanUtils.isTrue(function);
            });
            //处理web端逻辑
            LayoutExt.of(layout).getHeadInfoComponent().ifPresent(x -> {
                List<IButton> buttonList = x.getButtons();
                boolean noInvalidButton = buttonList.stream().noneMatch(button -> ObjectAction.INVALID.getActionCode().equals(button.getAction()));
                if (hasFunction && noInvalidButton) {
                    IButton button = ButtonUtil.getButton(ObjectAction.INVALID);
                    buttonList.add(button);
                    x.setButtons(buttonList);
                }
            });
            //处理终端逻辑
            if (RequestUtil.isMobileRequest()) {
                List<IButton> buttonList = layout.getButtons();
                boolean noInvalidButton = buttonList.stream().noneMatch(button -> ObjectAction.INVALID.getActionCode().equals(button.getAction()));
                if (hasFunction && noInvalidButton) {
                    buttonList.add(ButtonUtil.getButton(ObjectAction.INVALID));
                    layout.setButtons(buttonList);
                }
            }
        }
        newResult.setLayout(LayoutDocument.of(layout));
        return newResult;
    }
}
