package com.facishare.crm.newpayment.predefine.controller;

import com.facishare.crm.customeraccount.constants.Constants;
import com.facishare.crmcommon.util.SearchUtil;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.collect.Lists;

import java.util.List;

public class PaymentRemindListController extends PaymentListController {
    @Override
    protected void before(StandardListController.Arg arg) {
        super.before(arg);
        SearchTemplateQuery query = this.serviceFacade
                .getSearchTemplateQuery(controllerContext.getUser(), objectDescribe,
                        arg.getSearchTemplateId(), arg.getSearchQueryInfo());
        List<IFilter> filters = Lists.newArrayList();
        filters.addAll(query.getFilters());
        SearchUtil.fillFilterEq(filters, Constants.FIELD_APPROVE_EMPLOYEE_ID, Lists.newArrayList(controllerContext.getUser().getUserId()));
        query.resetFilters(filters);
        arg.setSearchQueryInfo(JsonUtil.toJson(query));
    }
}
