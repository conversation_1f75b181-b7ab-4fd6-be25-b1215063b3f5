package com.facishare.crm.newpayment.predefine.manager;

import com.facishare.crm.newpayment.constants.NewPaymentConst;
import com.facishare.crm.newpayment.enums.PlanPaymentStatusEnum;
import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class PaymentPlanManager {
    public static final String FIELD_ACTUAL_PAYMENT_AMOUNT = "actual_payment_amount";
    public static final String FIELD_PLAN_PAYMENT_AMOUNT = "plan_payment_amount";
    public static final String FIELD_PLAN_PAYMENT_TIME = "plan_payment_time";
    public static final String FIELD_PLAN_PAYMENT_STATUS = "plan_payment_status";

    @Autowired
    private ServiceFacade serviceFacade;

    public void updatePaymentPlanByPaymentIds(User user, List<String> paymentIds) {
        if (CollectionUtils.isEmpty(paymentIds)) {
            return;
        }
        List<IObjectData> paymentObjectDataList = serviceFacade.findObjectDataByIdsIncludeDeletedIgnoreFormula(user, paymentIds, Utils.CUSTOMER_PAYMENT_API_NAME);
        paymentObjectDataList.forEach(o -> {
            List<IObjectData> detailDataList = serviceFacade.findDetailIncludeInvalidObjectDataListIgnoreFormula(o, user);
            updatePaymentPlanByOrderPayment(user, detailDataList);
        });
    }

    public void updatePaymentPlanByPaymentDataList(User user,List<IObjectData> paymentDataList){
        if (CollectionUtils.isEmpty(paymentDataList)){
            return;
        }
        paymentDataList.forEach(x->{
            List<IObjectData> detailDataList = serviceFacade.findDetailIncludeInvalidObjectDataListIgnoreFormula(x, user);
            updatePaymentPlanByOrderPayment(user, detailDataList);
        });
    }

    public void updatePaymentPlanByOrderPayment(User user, List<IObjectData> orderPaymentList) {
        Set<String> planIds = orderPaymentList.stream().filter(x -> {
            String objectApiName = x.getDescribeApiName();
            String planId = x.get(NewPaymentConst.PAYMENT_PLAN_ID, String.class);
            return Utils.ORDER_PAYMENT_API_NAME.equals(objectApiName) && StringUtils.isNotEmpty(planId);
        }).map(x -> x.get(NewPaymentConst.PAYMENT_PLAN_ID, String.class)).collect(Collectors.toSet());
        updatePaymentPlan(user, planIds);
    }

    public void updatePaymentPlan(User user, Set<String> planIds) {
        if (CollectionUtils.isEmpty(planIds)) {
            return;
        }
        List<IObjectData> playObjectDataList = serviceFacade.findObjectDataByIds(user.getTenantId(), Lists.newArrayList(planIds), Utils.PAYMENT_PLAN_API_NAME);
        if (CollectionUtils.isEmpty(playObjectDataList)) {
            return;
        }
        IObjectDescribe describe = serviceFacade.findObject(user.getTenantId(), Utils.PAYMENT_PLAN_API_NAME);
        List<Count> counts = ObjectDescribeExt.of(describe).getCountFields(Utils.ORDER_PAYMENT_API_NAME);

        for (IObjectData playObjectData : playObjectDataList) {
            //实际回款金额是统计字段，需要实时计算
            Map<String, Object> amount = serviceFacade.calculateCountField(user, Utils.PAYMENT_PLAN_API_NAME, playObjectData.getId(), counts);
            Object actualPaymentAmountTmp = amount.get(FIELD_ACTUAL_PAYMENT_AMOUNT);
            Object planPaymentAmountTmp = playObjectData.get(FIELD_PLAN_PAYMENT_AMOUNT);
            Object planPaymentTimeTmp = playObjectData.get(FIELD_PLAN_PAYMENT_TIME);
            BigDecimal actualPaymentAmount = new BigDecimal(actualPaymentAmountTmp == null
                    ? "0" : actualPaymentAmountTmp.toString());
            BigDecimal planPaymentAmount = new BigDecimal(planPaymentAmountTmp == null
                    ? "0" : planPaymentAmountTmp.toString());
            long planPaymentTime = Long.parseLong(planPaymentTimeTmp == null
                    ? "0" : planPaymentTimeTmp.toString());
            String planPaymentStatus;
            int result = actualPaymentAmount.compareTo(planPaymentAmount);
            if (result < 0) {
                planPaymentStatus = System.currentTimeMillis() > planPaymentTime ?
                        PlanPaymentStatusEnum.OVERDUE.value :
                        PlanPaymentStatusEnum.INCOMPLETE.value;
            } else {
                planPaymentStatus = PlanPaymentStatusEnum.COMPLETED.value;
            }
            log.info("update payment plan status {} {}", playObjectData.getId(), planPaymentStatus);
            playObjectData.set(FIELD_PLAN_PAYMENT_STATUS, planPaymentStatus);
            List<String> updateField = Lists.newArrayList();
            updateField.add(FIELD_PLAN_PAYMENT_STATUS);
            ActionContextExt actionContextext = ActionContextExt.of(user);
            IActionContext actionContext = actionContextext.getContext();
            actionContext.put("skip_version_change", true);
            serviceFacade.batchUpdateByFields(actionContext, Lists.newArrayList(playObjectData), updateField);
        }
    }
}
