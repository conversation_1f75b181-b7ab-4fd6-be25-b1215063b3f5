package com.facishare.crm.newpayment.predefine.controller;

import com.facishare.crm.customeraccount.constants.*;
import com.facishare.crm.customeraccount.enums.PayTypeEnum;
import com.facishare.crm.customeraccount.enums.PaymentTermEnum;
import com.facishare.crm.customeraccount.predefine.manager.CaAccountsReceivableManager;
import com.facishare.crm.newpayment.predefine.manager.PaymentManager;
import com.facishare.crm.customeraccount.util.LayoutUtil;
import com.facishare.crm.openapi.Utils;
import com.facishare.crmcommon.constants.LayoutConstants;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.appframework.metadata.FormComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.dto.DetailObjectListResult;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.appframework.metadata.relation.CalculateRelation;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.ui.layout.FormField;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;

public class PaymentDescribeLayoutController extends StandardDescribeLayoutController {

    private CaAccountsReceivableManager caAccountsReceivableManager = SpringUtil.getContext().getBean(CaAccountsReceivableManager.class);
    private PaymentManager paymentManager = SpringUtil.getContext().getBean(PaymentManager.class);

    @Override
    protected boolean supportSaveDraft() {
        return true;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        String layoutType = arg.getLayout_type();
        if (!"add".equals(layoutType) && !"edit".equals(layoutType)) {
            return result;
        }
        resetDefaultValueWhileAdd(layoutType, result);
        LayoutDocument layoutDocument = LayoutUtil.removeFields(result.getLayout(), Sets.newHashSet(Constants.FIELD_FINANCE_EMPLOYEE_ID, Constants.FIELD_APPROVE_EMPLOYEE_ID,
                PaymentConstants.Field.SubmitTime.apiName, PaymentConstants.Field.FinanceConfirmTime.apiName,
                PaymentConstants.Field.OrderIdText.apiName));
        result.setLayout(layoutDocument);
        if ("add".equals(layoutType)) {
            LayoutExt layoutExt = LayoutExt.of(layoutDocument);
            FormComponentExt formComponentExt = layoutExt.getFormComponent().get();
            //布局中有支付方式字段，才考虑移除支付方式的option
            if (formComponentExt.containsField(PaymentConstants.Field.PayType.apiName)) {
                //回款在线支付逻辑--下游逻辑
                if (controllerContext.getUser().isOutUser()) {
                    //如果没有在线支付的功能权限，支付方式移除在线支付
                    boolean hasPrivilege = serviceFacade.funPrivilegeCheck(controllerContext.getUser(), controllerContext.getObjectApiName(), ObjectAction.ONLINE_PAY.getActionCode());
                    if (!hasPrivilege) {
                        removeFieldOptions(result, PaymentConstants.Field.PayType.apiName, Lists.newArrayList(PayTypeEnum.OnlineCharge.value, PayTypeEnum.CodeCollection.value));
                    } else {
                        removeFieldOptions(result, PaymentConstants.Field.PayType.apiName, Lists.newArrayList(PayTypeEnum.CodeCollection.value));
                    }
                } else {
                    //上游逻辑
                    boolean hasPrivilege = serviceFacade.funPrivilegeCheck(controllerContext.getUser(), controllerContext.getObjectApiName(), ObjectAction.QR_CODE.getActionCode());
                    if (!hasPrivilege) {
                        removeFieldOptions(result, PaymentConstants.Field.PayType.apiName, Lists.newArrayList(PayTypeEnum.CodeCollection.value, PayTypeEnum.OnlineCharge.value));
                    } else {
                        removeFieldOptions(result, PaymentConstants.Field.PayType.apiName, Lists.newArrayList(PayTypeEnum.OnlineCharge.value));
                    }
                }
            }
        }
        removeFieldOptions(result, PaymentConstants.Field.PaymentTerm.apiName, Lists.newArrayList(PaymentTermEnum.PrepayAndRebate.getType(), PaymentTermEnum.Prepay.getType(), PaymentTermEnum.Rebate.getType()));
        removeDetailField(result, layoutType);
        addAmountField(result, layoutType);
        HashSet<String> fieldSet = Sets.newHashSet();
        IFieldDescribe enterIntoAccountField = this.describe.getFieldDescribe(PaymentConstants.Field.EnterIntoAccount.apiName);
        if (Objects.nonNull(enterIntoAccountField) && enterIntoAccountField.isActive()) {
            boolean hasEnterAccountFunction = serviceFacade.funPrivilegeCheck(controllerContext.getUser(), controllerContext.getObjectApiName(), ObjectAction.ENTER_ACCOUNT.getActionCode());
            if (!hasEnterAccountFunction) {
                fieldSet.add(PaymentConstants.Field.FundAccount.apiName);
                fieldSet.add(PaymentConstants.Field.EnterIntoAccount.apiName);
            }
        }
        if ("edit".equals(layoutType)) {
            if (Objects.nonNull(this.objectData)) {
                //判断是否已入账
                boolean enterIntoAccount = objectData.get("enter_into_account", Boolean.class, false);
                if (enterIntoAccount && !ObjectDataExt.of(objectData).isIneffective()) {
                    fieldSet.add(PaymentConstants.Field.EnterIntoAccount.apiName);
                    fieldSet.add(PaymentConstants.Field.FundAccount.apiName);
                    fieldSet.add(PaymentConstants.Field.Amount.apiName);
                }
                if (isAccountIdReadOnly(enterIntoAccount)) {
                    fieldSet.add(PaymentConstants.Field.Customer.apiName);
                }
                //如果线上支付和二维码收款的回款，不支持编辑回款金额
                String payType = this.objectData.get(PaymentConstants.Field.PayType.apiName, String.class);
                if (PayTypeEnum.CodeCollection.value.equals(payType) || PayTypeEnum.OnlineCharge.value.equals(payType)) {
                    fieldSet.add(PaymentConstants.Field.Amount.apiName);
                    fieldSet.add(PaymentConstants.Field.PayType.apiName);
                } else {
                    removeFieldOptions(result, PaymentConstants.Field.PayType.apiName, Lists.newArrayList(PayTypeEnum.OnlineCharge.value, PayTypeEnum.CodeCollection.value));
                }
            } else {
                //编辑布局，没传dataId场景
                fieldSet.add(PaymentConstants.Field.Amount.apiName);
                fieldSet.add(PaymentConstants.Field.PayType.apiName);
            }
            fieldSet.add(PaymentConstants.Field.CollectionType.apiName);
            fieldSet.add(PaymentConstants.Field.OpeningBalance.apiName);
            //去掉主对象上的payment_time字段描述上的relate_fields calculate_fields
            ObjectDescribeExt paymentDescribeExt = ObjectDescribeExt.of(result.getObjectDescribe());
            IFieldDescribe paymentTimeFieldDescribe = paymentDescribeExt.getFieldDescribe(PaymentConstants.Field.PaymentTime.apiName);
            Map<String, Object> calculateMap = (Map<String, Object>) paymentTimeFieldDescribe.get("calculate_relation", Map.class);
            if (Objects.nonNull(calculateMap)) {
                Map<String, Object> calculateFieldMap = (Map<String, Object>) calculateMap.get("calculate_fields");
                if (Objects.nonNull(calculateFieldMap)) {
                    Collection<String> calculateFields = (Collection<String>) calculateFieldMap.get(Utils.ORDER_PAYMENT_API_NAME);
                    calculateFields = Objects.isNull(calculateFields) ? Lists.newArrayList() : calculateFields;
                    calculateFields.removeIf(x -> x.equals(OrderPaymentConstants.Field.UsedDate.apiName));
                    calculateFieldMap.put(Utils.ORDER_PAYMENT_API_NAME, calculateFields);
                }
                Map<String, Object> relateFieldMap = (Map<String, Object>) calculateMap.get("relate_fields");
                if (Objects.nonNull(relateFieldMap)) {
                    HashSet<CalculateRelation.RelateField> relateFieldHashSet = (HashSet<CalculateRelation.RelateField>) relateFieldMap.get(Utils.ORDER_PAYMENT_API_NAME);
                    HashSet<CalculateRelation.RelateField> newRelateFieldSet = Sets.newHashSet();
                    if (Objects.nonNull(relateFieldHashSet)) {
                        relateFieldHashSet.forEach(x -> {
                            if (!OrderPaymentConstants.Field.UsedDate.apiName.equals(x.getFieldName())) {
                                newRelateFieldSet.add(x);
                            }
                        });
                        relateFieldMap.put(Utils.ORDER_PAYMENT_API_NAME, newRelateFieldSet);
                    }
                }
            }
        }
        LayoutUtil.setReadOnly(layoutDocument, fieldSet, true);
        return result;
    }

    private void resetDefaultValueWhileAdd(String layoutType, Result result) {
        ObjectDataDocument objectDataDocument = result.getObjectData();
        if (!LayoutTypes.ADD.equals(layoutType) || Objects.isNull(objectDataDocument)) {
            return;
        }
        String payType = ObjectDataExt.of(objectDataDocument).get(PaymentConstants.Field.PayType.apiName, String.class);
        if (StringUtils.isEmpty(payType)) {
            return;
        }
        if (controllerContext.getUser().isOutUser() && PayTypeEnum.CodeCollection.value.equals(payType)) {
            //下游时，默认值如果为二维码收款，则重置为空
            payType = null;
        } else if (!controllerContext.getUser().isOutUser() && PayTypeEnum.OnlineCharge.value.equals(payType)) {
            //上游时，默认值如果为在线支付，则重置为空
            payType = null;
        }
        objectDataDocument.put(PaymentConstants.Field.PayType.apiName, payType);
    }

    private void addAmountField(Result result, String layoutType) {
        if (Objects.isNull(result.getLayout())) {
            return;
        }
        //新建/编辑 布局，如果没有amount字段，则添加amount字段；如果是详情布局，并且没有回款明细数据，则添加amount字段
        boolean needAdd = "add".equals(layoutType) || "edit".equals(layoutType);
        if (!needAdd && Objects.nonNull(this.objectData)) {
            BigDecimal paymentAmount = this.objectData.get(PaymentConstants.Field.PaymentAmount.apiName, BigDecimal.class, BigDecimal.ZERO);
            needAdd = paymentAmount.compareTo(BigDecimal.ZERO) <= 0;
        }
        if (!needAdd) {
            return;
        }
        LayoutExt layoutExt = LayoutExt.of(result.getLayout());
        if (layoutExt.containsField(PaymentConstants.Field.Amount.apiName)) {
            return;
        }
        Optional<IFieldSection> baseFieldSectionOptional = layoutExt.getFieldSection(LayoutConstants.BASE_FIELD_SECTION_API_NAME);
        baseFieldSectionOptional.ifPresent(x -> {
            List<IFormField> formFields = x.getFields();
            boolean amountNotExist = formFields.stream().noneMatch(f -> f.getFieldName().equals(PaymentConstants.Field.Amount.apiName));
            if (amountNotExist) {
                List<IFormField> newFormFields = Lists.newArrayList();
                for (IFormField field : formFields) {
                    if (field.getFieldName().equals(PaymentConstants.Field.PaymentAmount.apiName)) {
                        IFormField amountFormField = new FormField();
                        amountFormField.setFieldName(PaymentConstants.Field.Amount.apiName);
                        amountFormField.setReadOnly(false);
                        amountFormField.setRequired(true);
                        amountFormField.setRenderType(SystemConstants.RenderType.Currency.renderType);
                        newFormFields.add(amountFormField);
                    }
                    newFormFields.add(field);
                }
                x.setFields(newFormFields);
            }
        });
    }

    /**
     * 编辑时是否隐藏【客户名称】字段
     */
    private boolean isAccountIdReadOnly(Boolean enterIntoAccount) {
        //如果已入账，则不可编辑【客户名称】
        if (enterIntoAccount) {
            return true;
        }

        //如果关联了回款明细，则【客户名称】不可再编辑
        String paymentId = objectData.get("_id", String.class, null);
        boolean isHasOrderPayment = paymentManager.isHasOrderPayment(controllerContext.getUser(), paymentId);
        if (isHasOrderPayment) {
            return true;
        }

        //如果已被核销单关联，则不可编辑【客户名称】（包括已作废的核销单）
        return caAccountsReceivableManager.hasLinkMatchNote(controllerContext.getUser(), controllerContext.getTenantId(), paymentId);
    }

    private void removeFieldOptions(Result result, String fieldName, List<String> optionsToRemove) {
        if (Objects.isNull(result)) {
            return;
        }
        ObjectDescribeDocument objectDescribeDocument = result.getObjectDescribe();
        ObjectDescribeDocument objectDescribeExt = result.getObjectDescribeExt();
        List<ObjectDescribeDocument> objectDescribeDocuments = Lists.newArrayList(objectDescribeDocument, objectDescribeExt);
        for (ObjectDescribeDocument describeDocument : objectDescribeDocuments) {
            if (Objects.isNull(describeDocument)) {
                continue;
            }
            SelectOneFieldDescribe paymentTermField = (SelectOneFieldDescribe) ObjectDescribeExt.of(describeDocument).getFieldDescribe(fieldName);
            if (Objects.isNull(paymentTermField)) {
                continue;
            }
            List<ISelectOption> selectOptions = paymentTermField.getSelectOptions();
            selectOptions.removeIf(x -> optionsToRemove.contains(x.getValue()));
            paymentTermField.setSelectOptions(selectOptions);
        }
    }

    private void removeDetailField(Result result, String layoutType) {
        List<DetailObjectListResult> detailObjectListResults = result.getDetailObjectList();
        if (CollectionUtils.empty(detailObjectListResults)) {
            return;
        }
        detailObjectListResults.stream().filter(x -> x.getFieldApiName().equals(OrderPaymentConstants.Field.PaymentId.apiName)).findFirst().ifPresent(x -> {
            x.getLayoutList().forEach(y -> {
                LayoutDocument detailLayout = LayoutDocument.of(new Layout(y.getDetail_layout()));
                LayoutUtil.removeFields(detailLayout, Sets.newHashSet(OrderPaymentConstants.Field.PaymentTerm.apiName, OrderPaymentConstants.Field.PaymentTime.apiName, OrderPaymentConstants.Field.Customer.apiName));
                if ("edit".equals(layoutType)) {
                    //编辑时，回款明细的客户、订单、本次回款金额不可编辑
                    LayoutUtil.setReadOnly(detailLayout, Sets.newHashSet(OrderPaymentConstants.Field.Customer.apiName
                            , OrderPaymentConstants.Field.SalesOrder.apiName, OrderPaymentConstants.Field.ReturnedGoodsInvoice.apiName), true);
                }
            });
        });
    }
}
