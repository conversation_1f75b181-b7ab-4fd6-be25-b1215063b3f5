package com.facishare.crm.newpayment.predefine.action;

import com.facishare.crm.newpayment.predefine.manager.OrderPaymentImportManager;
import com.facishare.paas.appframework.core.predef.action.StandardUnionInsertImportVerifyAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;

import java.util.List;

public class OrderPaymentUnionInsertImportVerifyAction extends StandardUnionInsertImportVerifyAction {
    private OrderPaymentImportManager orderPaymentImportManager;

    @Override
    protected void before(Arg arg) {
        orderPaymentImportManager = serviceFacade.getBean(OrderPaymentImportManager.class);
        super.before(arg);
    }

    @Override
    public List<IFieldDescribe> getValidImportFields() {
        List<IFieldDescribe> fields = super.getValidImportFields();
        return orderPaymentImportManager.getValidImportFields(fields);
    }
}
