package com.facishare.crm.newpayment.predefine.action;

import com.facishare.crm.customeraccount.constants.CAI18NKey;
import com.facishare.crm.customeraccount.constants.OrderPaymentConstants;
import com.facishare.crm.customeraccount.constants.PaymentConstants;
import com.facishare.crm.customeraccount.constants.PaymentI18NKey;
import com.facishare.crm.newpayment.predefine.manager.PaymentManager;
import com.facishare.crmcommon.util.DataUtil;
import com.facishare.crmcommon.util.GrayUtil;
import com.facishare.crmcommon.util.SearchUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardIncrementUpdateAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public class OrderPaymentIncrementUpdateAction extends StandardIncrementUpdateAction {
    private PaymentManager paymentManager = SpringUtil.getContext().getBean(PaymentManager.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        Map<String, Object> updateFields = ObjectDataExt.of(dbObjectData).diff(objectData, objectDescribe);
        DataUtil.fieldEditCheck(this.objectDescribe, this.dbObjectData, this.objectData, Lists.newArrayList(OrderPaymentConstants.Field.Customer.apiName,
                OrderPaymentConstants.Field.PaymentId.apiName, OrderPaymentConstants.Field.SalesOrder.apiName,
                OrderPaymentConstants.Field.Name.apiName));
        //修改【使用金额】校验
        if (updateFields.containsKey(OrderPaymentConstants.Field.PaymentAmount.apiName)) {
            BigDecimal paymentAmount = objectData.get(OrderPaymentConstants.Field.PaymentAmount.apiName, BigDecimal.class, BigDecimal.ZERO);
            if (!GrayUtil.supportPaymentNegative(actionContext.getTenantId()) && paymentAmount.compareTo(BigDecimal.ZERO) < 0) {
                throw new ValidateException(I18N.text(PaymentI18NKey.SO_PAYMENT_NOTFINDORDERPAYYMENTMONEYGT0));
            }
            String orderPaymentId = this.objectData.getId();
            IObjectData orderPaymentData = serviceFacade.findObjectData(actionContext.getUser(), orderPaymentId, this.objectDescribe);
            String paymentId = orderPaymentData.get(OrderPaymentConstants.Field.PaymentId.apiName, String.class);
            IObjectData paymentData = serviceFacade.findObjectData(actionContext.getUser(), paymentId, PaymentConstants.API_NAME);
            //查询回款下的所有回款明细
            List<IObjectData> dbOrderPaymentDataList = paymentManager.queryOrderPaymentByPaymentId(actionContext.getUser(), paymentId);
            //把除了这条正在更新的之外回款明细的使用金额累加
            BigDecimal usedAmount = dbOrderPaymentDataList.stream()
                    .filter(x -> !ObjectLifeStatus.INEFFECTIVE.getCode().equals(ObjectDataExt.of(x).getLifeStatusText()) && !x.getId().equals(orderPaymentId))
                    .map(x -> x.get(OrderPaymentConstants.Field.PaymentAmount.apiName, BigDecimal.class))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal amount = paymentData.get(PaymentConstants.Field.Amount.apiName, BigDecimal.class, BigDecimal.ZERO);
            if (usedAmount.add(paymentAmount).compareTo(amount) > 0) {
                throw new ValidateException(I18N.text(PaymentI18NKey.ORDER_PAYMENT_AVAILABLE_AMOUNT_NOT_ENOUGH, paymentData.getName()));
            }
        }


        String oldPaymentPlan = dbObjectData.get(OrderPaymentConstants.Field.PaymentPlan.apiName, String.class);
        String paymentPlan = objectData.get(OrderPaymentConstants.Field.PaymentPlan.apiName, String.class);
        boolean changed;
        if (StringUtils.isEmpty(paymentPlan)) {
            changed = false;
        } else {
            changed = !StringUtils.equals(oldPaymentPlan, paymentPlan);
        }
        //要修改回款计划
        if (changed) {
            //同一个回款下的回款计划不能一样
            List<IFilter> filters = Lists.newArrayList();
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            SearchUtil.fillFilterEq(filters, OrderPaymentConstants.Field.PaymentPlan.apiName, paymentPlan);
            SearchUtil.fillFilterNotEq(filters, OrderPaymentConstants.Field.Name.apiName, dbObjectData.get(OrderPaymentConstants.Field.Name.getApiName(), String.class));
            SearchUtil.fillFilterEq(filters, OrderPaymentConstants.Field.PaymentId.apiName, dbObjectData.get(OrderPaymentConstants.Field.PaymentId.apiName, String.class));
            searchTemplateQuery.setFilters(filters);
            List<IObjectData> orderPaymentData = serviceFacade.findBySearchQuery(actionContext.getUser(), OrderPaymentConstants.API_NAME, searchTemplateQuery).getData();
            //该回款下已存在相同的回款计划
            if (CollectionUtils.notEmpty(orderPaymentData)) {
                log.info("the payment_plan_id already exist in this payment_id, payment_id:{}, payment_plan_id:{}", dbObjectData.get(OrderPaymentConstants.Field.PaymentId.apiName, String.class), paymentPlan);
                throw new ValidateException(I18N.text(CAI18NKey.PARAMS_ERROR));
            }
        }
    }

}
