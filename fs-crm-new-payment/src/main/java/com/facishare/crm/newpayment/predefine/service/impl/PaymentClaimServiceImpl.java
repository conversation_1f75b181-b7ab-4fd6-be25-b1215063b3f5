package com.facishare.crm.newpayment.predefine.service.impl;

import com.facishare.crm.customeraccount.constants.CAI18NKey;
import com.facishare.crm.customeraccount.constants.PaymentConstants;
import com.facishare.crm.newpayment.enums.ClaimUponReceiptOfPaymentEnum;
import com.facishare.crm.customeraccount.enums.ConfigKeyEnum;
import com.facishare.crm.customeraccount.predefine.service.CommonService;
import com.facishare.crm.newpayment.predefine.service.PaymentClaimService;
import com.facishare.crm.customeraccount.predefine.service.dto.ClaimUponReceiptOfPaymentModel;
import com.facishare.crm.customeraccount.predefine.service.dto.ClaimUponReceiptOfPaymentTransferHistoryDataModel;
import com.facishare.crm.customeraccount.util.ServiceContextUtil;
import com.facishare.crmcommon.manager.CommonDescribeManager;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class PaymentClaimServiceImpl extends CommonService implements PaymentClaimService {

    @Autowired
    private ConfigService configService;
    @Autowired
    private CommonDescribeManager commonDescribeManager;

    @Override
    public ClaimUponReceiptOfPaymentModel.Result claimUponReceiptOfPayment(ServiceContext serviceContext) {
        String tenantId = serviceContext.getTenantId();
        User user = serviceContext.getUser();

        ClaimUponReceiptOfPaymentModel.Result enableResult = new ClaimUponReceiptOfPaymentModel.Result();

        User systemUser = User.systemUser(serviceContext.getTenantId());
        String claimUponReceiptOfPaymentValue = configService.findTenantConfig(systemUser, ConfigKeyEnum.CLAIM_UPON_RECEIPT_OF_PAYMENT.key);
        if (Objects.equals(claimUponReceiptOfPaymentValue, ClaimUponReceiptOfPaymentEnum.OPENED.getStatus())) {
            log.warn("claimUponReceiptOfPayment key of claim_upon_receipt_of_payment is already open, tenantId: {}", tenantId);
            return enableResult;
        }

        try {
            //把【回款】的【客户名称】字段，改为非必填
            commonDescribeManager.updateFieldDescribeIsRequired(user, PaymentConstants.API_NAME, PaymentConstants.Field.Customer.apiName, false);

            //修改开关
            if (Objects.isNull(claimUponReceiptOfPaymentValue)) {
                configService.createTenantConfig(serviceContext.getUser(), ConfigKeyEnum.CLAIM_UPON_RECEIPT_OF_PAYMENT.key, ClaimUponReceiptOfPaymentEnum.OPENED.getStatus(), ConfigValueType.STRING);
            } else {
                configService.updateTenantConfig(serviceContext.getUser(), ConfigKeyEnum.CLAIM_UPON_RECEIPT_OF_PAYMENT.key, ClaimUponReceiptOfPaymentEnum.OPENED.getStatus(), ConfigValueType.STRING);
            }
            return enableResult;
        } catch (Exception e) {
            log.warn("claimUponReceiptOfPayment fail tenantId[{}]", serviceContext.getTenantId(), e);
            configService.updateTenantConfig(serviceContext.getUser(), ConfigKeyEnum.CLAIM_UPON_RECEIPT_OF_PAYMENT.key, ClaimUponReceiptOfPaymentEnum.CLOSE.getStatus(), ConfigValueType.STRING);
            throw new ValidateException(I18N.text(CAI18NKey.OPEN_FAIL, e.getMessage()));
        }
    }

    @Override
    public ClaimUponReceiptOfPaymentTransferHistoryDataModel.Result claimUponReceiptOfPaymentTransferHistoryData(ServiceContext serviceContext, ClaimUponReceiptOfPaymentTransferHistoryDataModel.Arg arg) {
        if (CollectionUtils.empty(arg.getTenantIds())) {
            return new ClaimUponReceiptOfPaymentTransferHistoryDataModel.Result(Lists.newArrayList());
        }

        List<String> successTenantIds = Lists.newArrayList();
        for (String tenantId : arg.getTenantIds()) {
            ServiceContext context = ServiceContextUtil.getInnerServiceContext(tenantId, "-10000");
            claimUponReceiptOfPayment(context);
            log.info("claimUponReceiptOfPaymentTransferHistoryData tenantId[{}]", tenantId);
            successTenantIds.add(tenantId);
        }
        return new ClaimUponReceiptOfPaymentTransferHistoryDataModel.Result(successTenantIds);
    }
}