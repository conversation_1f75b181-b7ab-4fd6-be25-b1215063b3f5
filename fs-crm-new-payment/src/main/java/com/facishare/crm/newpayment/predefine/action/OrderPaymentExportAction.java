package com.facishare.crm.newpayment.predefine.action;

import com.facishare.crm.newpayment.predefine.manager.PaymentManager;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardExportAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.List;
import java.util.stream.Collectors;

public class OrderPaymentExportAction extends StandardExportAction {
    private PaymentManager paymentManager = SpringUtil.getContext()
            .getBean(PaymentManager.class);

    @Override
    protected void fillDataList(User user, IObjectDescribe describe, List<IFieldDescribe> fields, List<IObjectData> dataList) {
        paymentManager.parseDateTime(actionContext.getUser(), objectDescribe, dataList.stream().map(ObjectDataDocument::of).collect(Collectors.toList()));
        super.fillDataList(user, describe, fields, dataList);
    }

}
