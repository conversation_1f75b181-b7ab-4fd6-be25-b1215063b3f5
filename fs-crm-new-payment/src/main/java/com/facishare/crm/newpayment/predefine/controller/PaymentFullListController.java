package com.facishare.crm.newpayment.predefine.controller;

import com.facishare.paas.metadata.api.DELETE_STATUS;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;

public class PaymentFullListController extends PaymentListController {

    @Override
    protected QueryResult<IObjectData> findData(SearchTemplateQuery query) {
        IFilter filter = new Filter();
        filter.setFieldName(IObjectData.IS_DELETED);
        filter.setOperator(Operator.IN);
        filter.setFieldValues(Lists.newArrayList(String.valueOf(DELETE_STATUS.NORMAL.getValue()), String.valueOf(DELETE_STATUS.INVALID.getValue())));
        query.addFilters(Lists.newArrayList(filter));
        return serviceFacade.findBySearchQueryWithDeleted(controllerContext.getUser(), objectDescribe, query);
    }
}
