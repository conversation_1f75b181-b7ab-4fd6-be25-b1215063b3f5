package com.facishare.crm.newpayment.predefine;

import com.facishare.crm.customeraccount.predefine.CustomerAccountPredefineObject;
import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.core.model.ActionClassInfo;
import com.facishare.paas.appframework.core.model.ControllerClassInfo;
import com.facishare.paas.appframework.core.model.PreDefineObject;
import com.facishare.paas.appframework.core.model.PreDefineObjectRegistry;

public enum NewPaymentPredefineObject implements PreDefineObject {

    Payment(Utils.CUSTOMER_PAYMENT_API_NAME),

    OrderPayment(Utils.ORDER_PAYMENT_API_NAME);

    private final String apiName;

    private static String PACKAGE_NAME = NewPaymentPredefineObject.class.getPackage().getName();

    NewPaymentPredefineObject(String apiName) {
        this.apiName = apiName;
    }

    @Override
    public String getApiName() {
        return apiName;
    }

    @Override
    public String getPackageName() {
        return PACKAGE_NAME;
    }

    @Override
    public ActionClassInfo getDefaultActionClassInfo(String actionCode) {
        String className = PACKAGE_NAME + ".action." + this + actionCode + "Action";
        return new ActionClassInfo(className);
    }

    @Override
    public ControllerClassInfo getControllerClassInfo(String methodName) {
        String className = PACKAGE_NAME + ".controller." + this + methodName + "Controller";
        return new ControllerClassInfo(className);
    }

    public static void init() {
        for (NewPaymentPredefineObject object : NewPaymentPredefineObject.values()) {
            PreDefineObjectRegistry.register(object);
        }
    }
}
