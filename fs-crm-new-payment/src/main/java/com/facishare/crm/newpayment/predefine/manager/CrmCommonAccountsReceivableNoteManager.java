package com.facishare.crm.newpayment.predefine.manager;

import com.facishare.crm.openapi.Utils;
import com.facishare.crmcommon.constants.AccountsReceivableNoteObjConstants;
import com.facishare.crmcommon.constants.PaymentObjConstants;
import com.facishare.crmcommon.enums.CollectionTypeEnum;
import com.facishare.crmcommon.sfa.predefine.service.model.ExceptionInfo;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.AcceptableValidateException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CrmCommonAccountsReceivableNoteManager {

    @Autowired
    private ServiceFacade serviceFacade;

    public boolean checkPaymentMatchNote(User user, IObjectData payment) {
        if (Objects.isNull(payment)) {
            throw new ValidateException(I18N.text("accountsreceivable.validate.no_payment"));
        }
        List<Map> datas = payment.get("check_match_datas", List.class);
        if (CollectionUtils.empty(datas)) {
            return false;
        }
        BigDecimal paymentAmount = payment.get(PaymentObjConstants.Field.NoMatchAmount.apiName, BigDecimal.class, BigDecimal.ZERO);
        String collectionType = payment.get(PaymentObjConstants.Field.CollectionType.apiName, String.class);
        boolean enter_into_account = payment.get("enter_into_account", Boolean.class, false);
        if (enter_into_account) {
            throw new ValidateException(I18N.text("accountsreceivable.validate.payment.cannot.enter_into_account"));
        }
        if (paymentAmount.compareTo(BigDecimal.ZERO) == 0) {
            throw new ValidateException(I18N.text("accountsreceivable.validate.payment.no_match_amount.not.zero"));
        }
        if (Strings.isNullOrEmpty(collectionType)) {
            throw new ValidateException(I18N.text("accountsreceivable.validate.payment.collection_type.error"));
        }
        boolean isRedPayment = false;
        if (Objects.equals(collectionType, CollectionTypeEnum.Red.getValue())) {
            isRedPayment = true;
        }
        List<String> dataIds = datas.stream().map(x -> x.get("_id").toString()).collect(Collectors.toList());
        if (CollectionUtils.empty(dataIds)) {
            return false;
        }
        List<IObjectData> arDatas = serviceFacade.findObjectDataByIds(user.getTenantId(), dataIds, AccountsReceivableNoteObjConstants.API_NAME);
        if (CollectionUtils.empty(arDatas)) {
            return false;
        }
        arDatas.forEach(data -> {
            datas.stream()
                    .filter(argData -> Objects.equals(data.getId(), argData.get("_id").toString()))
                    .findFirst()
                    .ifPresent(argData -> data.set(AccountsReceivableNoteObjConstants.Field.VirThisMatchAmount.apiName, argData.get(AccountsReceivableNoteObjConstants.Field.VirThisMatchAmount.apiName)));
        });

        BigDecimal arTotalAmount = arDatas.stream()
                .map(x -> x.get(AccountsReceivableNoteObjConstants.Field.VirThisMatchAmount.apiName, BigDecimal.class, BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (isRedPayment) {
            if (arTotalAmount.compareTo(BigDecimal.ZERO) > 0 || arTotalAmount.compareTo(paymentAmount) < 0) {
                throw new ValidateException(I18N.text("accountsreceivable.validate.matchnote.payment.no_match_amount.error"));
            }
        } else {
            if (arTotalAmount.compareTo(BigDecimal.ZERO) < 0 || arTotalAmount.compareTo(paymentAmount) > 0) {
                throw new ValidateException(I18N.text("accountsreceivable.validate.matchnote.payment.no_match_amount.error"));
            }
        }
        for (IObjectData arData : arDatas) {
            BigDecimal noSettledAmount = arData.get(AccountsReceivableNoteObjConstants.Field.NoSettledAmount.apiName, BigDecimal.class, BigDecimal.ZERO);
            BigDecimal virThisMatchAmount = arData.get(AccountsReceivableNoteObjConstants.Field.VirThisMatchAmount.apiName, BigDecimal.class, BigDecimal.ZERO);
            if (noSettledAmount.multiply(virThisMatchAmount).compareTo(BigDecimal.ZERO) < 0
                    || virThisMatchAmount.abs().compareTo(noSettledAmount.abs()) > 0
                    || virThisMatchAmount.compareTo(BigDecimal.ZERO) == 0) {
                throw new ValidateException(String.format(I18N.text("accountsreceivable.validate.matchnote.this_match_amount.error"), arData.getName()));
            }
        }
        return true;
    }

    public void checkOpeningBalance(User user,IObjectData objectData,boolean notForceCheck) {
        long openingBalanceDate = 0L;
        String date = serviceFacade.findTenantConfig(user, "opening_balance_date");
        if (!StringUtils.isBlank(date)) {
            openingBalanceDate = Long.parseLong(date);
        }
        String isOpeningBalanceForceCheckStr = serviceFacade.findTenantConfig(user, "opening_balance_force_check");
        boolean isOpeningBalanceForceCheck = Objects.equals("1", isOpeningBalanceForceCheckStr);
        if (openingBalanceDate > 0L) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(openingBalanceDate);
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            long updatedTimestamp = calendar.getTimeInMillis();
            //设置了期初日期，需要校验
            Long noteDate = objectData.get(PaymentObjConstants.Field.PaymentTime.apiName, Long.class);

            boolean isOpeningBalanceData = objectData.get(PaymentObjConstants.Field.OpeningBalance.apiName, Boolean.class);
            if (isOpeningBalanceForceCheck) {
                if (isOpeningBalanceData) {
                    if (noteDate >= updatedTimestamp) {
                        throw new ValidateException(I18N.text("payment.validate.opening.balance.check.error.0"));
                    }
                } else {
                    if (noteDate < updatedTimestamp) {
                        throw new ValidateException(I18N.text("payment.validate.opening.balance.check.error.1"));
                    }
                }
            } else {
                if (!notForceCheck) {
                    if (isOpeningBalanceData) {
                        if (noteDate >= updatedTimestamp) {
                            throw new AcceptableValidateException(ExceptionInfo.builder()
                                    .apiName(Utils.CUSTOMER_PAYMENT_API_NAME)
                                    .errorCode(200)
                                    .errorMessage(I18N.text("payment.validate.opening.balance.check.error.0")).build());
                        }
                    } else {
                        if (noteDate < updatedTimestamp) {
                            throw new AcceptableValidateException(ExceptionInfo.builder()
                                    .apiName(Utils.CUSTOMER_PAYMENT_API_NAME)
                                    .errorCode(200)
                                    .errorMessage(I18N.text("payment.validate.opening.balance.check.error.1")).build());
                        }
                    }
                }
            }
        }
    }
}
