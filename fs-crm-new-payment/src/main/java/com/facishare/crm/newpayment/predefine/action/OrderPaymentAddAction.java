package com.facishare.crm.newpayment.predefine.action;

import com.facishare.crm.customeraccount.constants.Constants;
import com.facishare.crm.customeraccount.constants.OrderPaymentConstants;
import com.facishare.crm.customeraccount.constants.PaymentConstants;
import com.facishare.crm.customeraccount.constants.PaymentI18NKey;
import com.facishare.crm.customeraccount.enums.ConfigKeyEnum;
import com.facishare.crm.customeraccount.predefine.manager.BizConfigManager;
import com.facishare.crm.customeraccount.predefine.manager.NewCustomerAccountManager;
import com.facishare.crm.newpayment.predefine.manager.PaymentManager;
import com.facishare.crm.newpayment.predefine.manager.PaymentPlanManager;
import com.facishare.crm.customeraccount.util.CaGrayUtil;
import com.facishare.crm.customeraccount.util.ConfigCenter;
import com.facishare.crm.customeraccount.util.ObjectDataUtil;
import com.facishare.crm.openapi.Utils;
import com.facishare.crmcommon.util.GrayUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
public class OrderPaymentAddAction extends StandardAddAction {
    private PaymentManager paymentManager = SpringUtil.getContext().getBean(PaymentManager.class);
    private PaymentPlanManager paymentPlanManager = SpringUtil.getContext().getBean(PaymentPlanManager.class);
    private NewCustomerAccountManager newCustomerAccountManager = SpringUtil.getContext().getBean(NewCustomerAccountManager.class);
    private IObjectData masterData;
    private List<IObjectData> dbOrderPaymentDataList;
    private BizConfigManager configManager = SpringUtil.getContext().getBean(BizConfigManager.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        //校验回款金额
        String paymentId = this.objectData.get(OrderPaymentConstants.Field.PaymentId.apiName, String.class);
        masterData = serviceFacade.findObjectData(actionContext.getUser(), paymentId, Utils.CUSTOMER_PAYMENT_API_NAME);
        //已入账的回款，不支持新建回款明细(汉和可以)
        boolean enterIntoAccount = masterData.get(PaymentConstants.Field.EnterIntoAccount.apiName, Boolean.class, Boolean.FALSE);
        String configValue = serviceFacade.findTenantConfig(actionContext.getUser(), ConfigKeyEnum.PAYMENT_WITH_DETAIL_ENTER_ACCOUNT.key);
        boolean enablePaymentWithDetailEnterAccount = ConfigKeyEnum.PAYMENT_WITH_DETAIL_ENTER_ACCOUNT.enabled(configValue);
        if (enterIntoAccount && !enablePaymentWithDetailEnterAccount) {
            throw new ValidateException(I18N.text(PaymentI18NKey.PAYMENT_ENTER_INTO_ACCOUNT_CANNOT_ADD_DETAIL));
        }
        dbOrderPaymentDataList = paymentManager.queryOrderPaymentByPaymentId(actionContext.getUser(), paymentId);
        //TODO 回款明细状态 更新不及时 会导致 回款金额用超
        BigDecimal amount = masterData.get(PaymentConstants.Field.Amount.apiName, BigDecimal.class, BigDecimal.ZERO);
        BigDecimal usedAmount = dbOrderPaymentDataList.stream().filter(x -> !ObjectLifeStatus.INEFFECTIVE.getCode().equals(ObjectDataExt.of(x).getLifeStatusText())).map(x -> x.get(OrderPaymentConstants.Field.PaymentAmount.apiName, BigDecimal.class)).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal paymentAmount = this.objectData.get(OrderPaymentConstants.Field.PaymentAmount.apiName, BigDecimal.class, BigDecimal.ZERO);
        String customerId = masterData.get(PaymentConstants.Field.Customer.apiName, String.class);
        if (StringUtils.isNotEmpty(customerId)) {
            this.objectData.set(OrderPaymentConstants.Field.Customer.apiName, customerId);
        }
        //校验红蓝回款对应正负金额
        if (ObjectDataUtil.isSupportRedPayment(masterData)) {
            List<IObjectData> detailCheckList = new ArrayList<>();
            detailCheckList.add(objectData);
            if (CollectionUtils.notEmpty(dbOrderPaymentDataList)) {
                detailCheckList.addAll(dbOrderPaymentDataList);
            }
            ObjectDataUtil.checkPaymentCollectionType(masterData, detailCheckList);
        } else {
            if (usedAmount.add(paymentAmount).compareTo(amount) > 0) {
                throw new ValidateException(I18N.text(PaymentI18NKey.ORDER_PAYMENT_AVAILABLE_AMOUNT_NOT_ENOUGH, masterData.getName()));
            }

            if (paymentAmount.compareTo(BigDecimal.ZERO) <= 0 && !GrayUtil.supportPaymentNegative(actionContext.getTenantId())) {
                throw new ValidateException(I18N.text(PaymentI18NKey.SO_PAYMENT_NOTFINDORDERPAYYMENTMONEYGT0));
            }
        }
    }

    /**
     * 不触发主对象的审批流
     */
    @Override
    protected boolean needTriggerMasterApproval() {
        return CaGrayUtil.allow(actionContext.getTenantId(), CaGrayUtil.ORDER_PAYMENT_TRIGGER_MASTER_APPROVAL);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        String planId = result.getObjectData().toObjectData().get(OrderPaymentConstants.Field.PaymentPlan.apiName, String.class);
        if (StringUtils.isNotEmpty(planId) && ConfigCenter.syncUpdatePaymentPlanStatus(actionContext.getTenantId())) {
            paymentPlanManager.updatePaymentPlan(actionContext.getUser(), Sets.newHashSet(planId));
        }
        String orderId = this.objectData.get(OrderPaymentConstants.Field.SalesOrder.apiName, String.class);
        if (StringUtils.isNotEmpty(orderId)) {
            Set<String> orderIds = Sets.newHashSet();
            orderIds.addAll(dbOrderPaymentDataList.stream().map(x -> x.get(OrderPaymentConstants.Field.SalesOrder.apiName, String.class)).filter(Objects::nonNull).collect(Collectors.toSet()));
            orderIds.add(orderId);
            masterData.set(PaymentConstants.Field.OrderIdText.apiName, Joiner.on(",").join(orderIds));
            serviceFacade.batchUpdateByFields(actionContext.getUser(), Lists.newArrayList(masterData), Lists.newArrayList(PaymentConstants.Field.OrderIdText.apiName));
            newCustomerAccountManager.calculateAndUpdateFormulaFields(actionContext.getRequestContext(), Utils.SALES_ORDER_API_NAME,
                    Lists.newArrayList(orderId), Lists.newArrayList(Constants.RECEIVABLE_AMOUNT, Constants.PAYMENT_AMOUNT));
        }
        return result;
    }

}
