package com.facishare.crm.newpayment.predefine.controller;

import com.facishare.crm.customeraccount.predefine.manager.BizConfigManager;
import com.facishare.crm.customeraccount.util.ButtonUtil;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.util.SpringUtil;

public class OrderPaymentListController extends StandardListController {
    private BizConfigManager bizConfigManager = SpringUtil.getContext().getBean(BizConfigManager.class);

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        LayoutDocument layoutDocument = result.getLayout();
        layoutDocument = ButtonUtil.addPaymentButton(controllerContext.getUser(), layoutDocument, bizConfigManager);
        result.setLayout(layoutDocument);
        return result;
    }


}
