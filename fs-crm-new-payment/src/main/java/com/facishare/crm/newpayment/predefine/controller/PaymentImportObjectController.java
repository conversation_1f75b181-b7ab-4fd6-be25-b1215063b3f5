package com.facishare.crm.newpayment.predefine.controller;

import com.facishare.crm.customeraccount.util.ConfigCenter;
import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardImportObjectController;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;

import java.util.List;

public class PaymentImportObjectController extends StandardImportObjectController {
    @Override
    protected List<ObjectDescribeDocument> findDetailDescribes() {
        List<ObjectDescribeDocument> objectDescribeDocuments = super.findDetailDescribes();
        String tenantId = controllerContext.getTenantId();
        if (ConfigCenter.isInNewPaymentBlank(tenantId)) {
            return objectDescribeDocuments;
        }
        //如果是新回款
        objectDescribeDocuments = CollectionUtils.nullToEmpty(objectDescribeDocuments);
        boolean containsOrderPayment = objectDescribeDocuments.stream().anyMatch(x -> x.toObjectDescribe().getApiName().equals(Utils.ORDER_PAYMENT_API_NAME));
        if (containsOrderPayment) {
            return objectDescribeDocuments;
        }
        IObjectDescribe orderPaymentDescribe = serviceFacade.findObject(tenantId, Utils.ORDER_PAYMENT_API_NAME);
        List<IObjectDescribe> validObjectList = this.serviceFacade.filterDescribesWithActionCode(this.controllerContext.getUser(), Lists.newArrayList(orderPaymentDescribe), ObjectAction.BATCH_IMPORT.getActionCode());
        objectDescribeDocuments.addAll(ObjectDescribeDocument.ofList(validObjectList));
        return objectDescribeDocuments;
    }
}
