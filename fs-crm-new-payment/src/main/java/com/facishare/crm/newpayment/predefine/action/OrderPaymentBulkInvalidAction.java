package com.facishare.crm.newpayment.predefine.action;

import com.facishare.crm.customeraccount.constants.OrderPaymentConstants;
import com.facishare.crm.mergepayment.predefine.manager.NewPaymentManger;
import com.facishare.crm.newpayment.constants.NewPaymentConst;
import com.facishare.crm.newpayment.predefine.manager.PaymentPlanManager;
import com.facishare.crm.customeraccount.util.ConfigCenter;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class OrderPaymentBulkInvalidAction extends StandardBulkInvalidAction {
    private final PaymentPlanManager paymentPlanManager = SpringUtil.getContext().getBean(PaymentPlanManager.class);
    private final NewPaymentManger newPaymentManger = SpringUtil.getContext().getBean(NewPaymentManger.class);

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        List<ObjectDataDocument> orderPaymentDataList = result.getObjectDataList();
        if (CollectionUtils.notEmpty(orderPaymentDataList)) {
            ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
            for (ObjectDataDocument objectDataDocument : orderPaymentDataList) {
                IObjectData orderPaymentData = objectDataDocument.toObjectData();
                String orderId = orderPaymentData.get(NewPaymentConst.ORDER_ID, String.class);
                if (StringUtils.isEmpty(orderId)) {
                    continue;
                }
                parallelTask.submit(() -> {
                    String paymentId = orderPaymentData.get(NewPaymentConst.PAYMENT_ID, String.class);
                    newPaymentManger.updatePaymentOrderIdText(actionContext.getRequestContext(), paymentId);
                });
            }
            parallelTask.run();
            if (ConfigCenter.syncUpdatePaymentPlanStatus(actionContext.getTenantId())) {
                Set<String> planIds = orderPaymentDataList.stream().map(x -> ObjectDataExt.of(x).get(OrderPaymentConstants.Field.PaymentPlan.apiName, String.class)).filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
                paymentPlanManager.updatePaymentPlan(actionContext.getUser(), planIds);
            }
        }
        return result;
    }
}
