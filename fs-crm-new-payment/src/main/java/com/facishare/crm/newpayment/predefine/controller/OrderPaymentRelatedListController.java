package com.facishare.crm.newpayment.predefine.controller;

import com.facishare.crm.customeraccount.constants.Constants;
import com.facishare.crm.customeraccount.constants.PaymentConstants;
import com.facishare.crm.customeraccount.enums.ConfigKeyEnum;
import com.facishare.crm.customeraccount.predefine.manager.BizConfigManager;
import com.facishare.crm.customeraccount.util.ButtonUtil;
import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;


public class OrderPaymentRelatedListController extends StandardRelatedListController {
    private BizConfigManager bizConfigManager = SpringUtil.getContext().getBean(BizConfigManager.class);

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        LayoutDocument layoutDocument = result.getLayout();
        layoutDocument = ButtonUtil.addPaymentButton(controllerContext.getUser(), layoutDocument, bizConfigManager);
        BizConfigManager.ConfigHolder configHolder = bizConfigManager.getConfigValue(controllerContext.getUser(), Lists.newArrayList(ConfigKeyEnum.NEW_CUSTOMER_ACCOUNT.key));
        boolean newCustomerAccountEnable = configHolder.isTrue(ConfigKeyEnum.NEW_CUSTOMER_ACCOUNT.key, "", ConfigKeyEnum.NEW_CUSTOMER_ACCOUNT.enabledValue);
        String targetObjectApiName = arg.getTargetObjectApiName();
        if (Objects.nonNull(layoutDocument) && newCustomerAccountEnable && Utils.CUSTOMER_PAYMENT_API_NAME.equals(targetObjectApiName)) {
            //开启新版客户账户时，如果回款是已入账，移除回款明细相关tab页下新建回款明细按钮
            String targetObjectDataId = arg.getTargetObjectDataId();
            IObjectData paymentData = serviceFacade.findObjectData(controllerContext.getUser(), targetObjectDataId, arg.getTargetObjectApiName());
            boolean enterIntoAccount = paymentData.get(PaymentConstants.Field.EnterIntoAccount.apiName, Boolean.class, Boolean.FALSE);
            if (enterIntoAccount) {
                ILayout layout = layoutDocument.toLayout();
                List<IButton> buttonList = layout.getButtons();
                buttonList.removeIf(x -> x.getAction().equals(ObjectAction.CREATE.getActionCode()) || "AddPayment".equals(x.getAction()));
                layout.setButtons(buttonList);
                layoutDocument = LayoutDocument.of(layout);
            }
        }
        result.setLayout(layoutDocument);
        return result;
    }
}
