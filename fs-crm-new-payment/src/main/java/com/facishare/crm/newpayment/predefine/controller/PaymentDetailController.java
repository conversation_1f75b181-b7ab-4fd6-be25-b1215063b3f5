package com.facishare.crm.newpayment.predefine.controller;

import com.facishare.crm.customeraccount.constants.OrderPaymentConstants;
import com.facishare.crm.customeraccount.constants.PaymentConstants;
import com.facishare.crm.newpayment.predefine.manager.PaymentManager;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardDetailController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class PaymentDetailController extends StandardDetailController {
    private PaymentManager paymentManager = SpringUtil.getContext().getBean(PaymentManager.class);

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        ObjectDataDocument data = result.getData();
        if (Objects.nonNull(data)) {
            String paymentId = data.getId();
            List<IObjectData> orderPaymentDataList = paymentManager.queryOrderPaymentByPaymentId(controllerContext.getUser(), paymentId);
            List<String> orderIds = orderPaymentDataList.stream().map(x -> x.get(OrderPaymentConstants.Field.SalesOrder.apiName, String.class)).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            data.put(PaymentConstants.Field.OrderIdText.apiName, Joiner.on(",").join(orderIds));
            paymentManager.parseOrderName(controllerContext.getUser(), Lists.newArrayList(result.getData()));
        }
        return result;
    }
}
