package com.facishare.crm.newpayment.predefine.manager;

import com.facishare.crm.customeraccount.constants.CAI18NKey;
import com.facishare.crm.customeraccount.constants.OrderPaymentConstants;
import com.facishare.crm.customeraccount.constants.PaymentConstants;
import com.facishare.crm.customeraccount.constants.PaymentI18NKey;
import com.facishare.crm.customeraccount.enums.ConfigKeyEnum;
import com.facishare.crm.customeraccount.predefine.manager.BizConfigManager;
import com.facishare.crm.customeraccount.predefine.manager.CommonManager;
import com.facishare.crm.customeraccount.predefine.manager.FundAccountConfigManager;
import com.facishare.crm.payment.enums.PaymentCollectionTypeEnum;
import com.facishare.crm.customeraccount.util.ObjectDataUtil;
import com.facishare.crm.openapi.Utils;
import com.facishare.crmcommon.util.GrayUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseImportAction;
import com.facishare.paas.appframework.core.predef.action.BaseImportDataAction;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Component
public class OrderPaymentImportManager {
    @Autowired
    private FundAccountConfigManager fundAccountConfigManager;
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private PaymentManager paymentManager;
    @Autowired
    private CommonManager commonManager;
    @Autowired
    private BizConfigManager configManager;

    private static final List<String> ORDER_PAYMENT_UN_SUPPORT_IMPORT_FIELDS = Lists.newArrayList(
            "extend_obj_data_id", "approve_employee_id"
    );

    private static final List<String> ORDER_PAYMENT_VALIDATE_IGNORE_FIELDS = Lists.newArrayList(
            "extend_obj_data_id", "approve_employee_id"
    );

    public List<IFieldDescribe> getValidImportFields(List<IFieldDescribe> fieldDescribes) {
        fieldDescribes.removeIf(x -> ORDER_PAYMENT_VALIDATE_IGNORE_FIELDS.contains(x.getApiName()));
        return fieldDescribes;
    }

    public void customHeader(List<IFieldDescribe> headerFieldList) {
        headerFieldList.removeIf(f -> ORDER_PAYMENT_UN_SUPPORT_IMPORT_FIELDS.contains(f.getApiName()));
    }

    public List<BaseImportAction.ImportError> customValidate(RequestContext requestContext, IObjectDescribe objectDescribe, List<BaseImportDataAction.ImportData> dataList) {
        boolean newCustomerAccountEnable = fundAccountConfigManager.isFundAccountEnable(requestContext.getTenantId());
        if (!newCustomerAccountEnable) {
            return Lists.newArrayList();
        }
        boolean paymentNegative = GrayUtil.supportPaymentNegative(requestContext.getTenantId());
        List<BaseImportAction.ImportError> errorList = Lists.newArrayList();

        Map<String, BigDecimal> paymentIdAmountMap = Maps.newHashMap();
        dataList.forEach(x -> {
            String paymentId = x.getData().get(OrderPaymentConstants.Field.PaymentId.apiName, String.class);
            BigDecimal amount = x.getData().get(OrderPaymentConstants.Field.PaymentAmount.apiName, BigDecimal.class, BigDecimal.ZERO);
            if (amount.compareTo(BigDecimal.ZERO) < 0 && !paymentNegative) {
                errorList.add(new BaseImportAction.ImportError(x.getRowNo(), I18N.text(PaymentI18NKey.PAYMENT_AMOUNT_MUST_GT_ZERO)));
            } else {
                BigDecimal amountBefore = paymentIdAmountMap.computeIfAbsent(paymentId, key -> BigDecimal.ZERO);
                paymentIdAmountMap.put(paymentId, amountBefore.add(amount));
            }
        });
        Map<String, BigDecimal> availableAmountMap = paymentManager.queryPaymentAvailableAmountByPaymentId(requestContext.getUser(), Lists.newArrayList(paymentIdAmountMap.keySet()));
        Set<String> errorPaymentIds = Sets.newHashSet();
        paymentIdAmountMap.forEach((k, v) -> {
            BigDecimal availableAmount = availableAmountMap.getOrDefault(k, BigDecimal.ZERO);
            if (availableAmount.compareTo(v) < 0) {
                errorPaymentIds.add(k);
            }
        });

        dataList.forEach(x -> {
            String paymentId = x.getData().get(OrderPaymentConstants.Field.PaymentId.apiName, String.class);
            if (errorPaymentIds.contains(paymentId)) {
                errorList.add(new BaseImportAction.ImportError(x.getRowNo(), I18N.text(PaymentI18NKey.PAYMENT_AMOUNT_LT_USED_AMOUNT)));
            }
        });

        //主对象客户字段不能为空
        Set<String> paymentIds = dataList.stream().map(x -> x.getData().get(OrderPaymentConstants.Field.PaymentId.apiName, String.class)).collect(Collectors.toSet());
        List<IObjectData> paymentDatas = commonManager.findByIds(requestContext.getTenantId(), Lists.newArrayList(paymentIds), PaymentConstants.API_NAME);
        Map<String, IObjectData> paymentId2PaymentDataMap = paymentDatas.stream().collect(Collectors.toMap(
                DBRecord::getId,
                data -> data)
        );
        String configValue = serviceFacade.findTenantConfig(requestContext.getUser(), ConfigKeyEnum.PAYMENT_WITH_DETAIL_ENTER_ACCOUNT.key);
        boolean enablePaymentWithDetailEnterAccount = ConfigKeyEnum.PAYMENT_WITH_DETAIL_ENTER_ACCOUNT.enabled(configValue);

        dataList.forEach(x -> {
            String paymentId = x.getData().get(OrderPaymentConstants.Field.PaymentId.apiName, String.class);
            IObjectData paymentData = paymentId2PaymentDataMap.get(paymentId);
            if (Objects.isNull(paymentData)) {
                IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(OrderPaymentConstants.Field.PaymentId.apiName);
                String fieldLabel = Objects.nonNull(fieldDescribe) ? fieldDescribe.getLabel() : OrderPaymentConstants.Field.PaymentId.apiName;
                errorList.add(new BaseImportAction.ImportError(x.getRowNo(), I18N.text(CAI18NKey.SPECIFIED_PARAM_ERROR, fieldLabel)));
            } else {
                String customerId = paymentData.get(PaymentConstants.Field.Customer.apiName, String.class);
                boolean enterIntoAccount = paymentData.get(PaymentConstants.Field.EnterIntoAccount.apiName, Boolean.class, Boolean.FALSE);
                if (enterIntoAccount && !enablePaymentWithDetailEnterAccount) {
                    errorList.add(new BaseImportAction.ImportError(x.getRowNo(), I18N.text(PaymentI18NKey.PAYMENT_ENTER_INTO_ACCOUNT_CANNOT_ADD_DETAIL)));
                }
                if (Strings.isNullOrEmpty(customerId)) {
                    errorList.add(new BaseImportAction.ImportError(x.getRowNo(), I18N.text(PaymentI18NKey.PAYMENT_CUSTOMER_IS_EMPTY)));
                }
                if (ObjectDataUtil.isSupportRedPayment(paymentData)) {
                    checkOrderPaymentImportCollectionType(paymentData, x, errorList);
                }
            }
        });

        return errorList;
    }

    public void customAfterImport(RequestContext requestContext, IObjectDescribe paymentOrderDescribe, List<IObjectData> actualList) {
        log.debug("OrderPaymentInsertImportDataAction customAfterImport actualList:{}", actualList);
        Map<String, List<String>> customerPaymentIdWithOrderIds = Maps.newHashMap();
        actualList.forEach(x -> {
            String paymentId = x.get(OrderPaymentConstants.Field.PaymentId.apiName, String.class);
            String orderId = x.get(OrderPaymentConstants.Field.SalesOrder.apiName, String.class);
            if (StringUtils.isNotEmpty(paymentId) && StringUtils.isNotEmpty(orderId)) {
                customerPaymentIdWithOrderIds.computeIfAbsent(paymentId, k -> Lists.newArrayList()).add(orderId);
            }
        });
        String tenantId = requestContext.getTenantId();
        List<IObjectData> customerPaymentList = serviceFacade.findObjectDataByIdsIgnoreFormula(tenantId,
                Lists.newArrayList(customerPaymentIdWithOrderIds.keySet()), Utils.CUSTOMER_PAYMENT_API_NAME);
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        customerPaymentList.forEach(customerPaymentObjectData -> {
            if (customerPaymentObjectData != null) {
                parallelTask.submit(() -> {
                    String paymentId = customerPaymentObjectData.getId();
                    List<String> orderIds = customerPaymentIdWithOrderIds.get(paymentId);
                    if (CollectionUtils.isNotEmpty(orderIds)) {
                        Set<String> oldOrderIds = getOrderList(requestContext.getUser(), paymentOrderDescribe, customerPaymentObjectData);
                        oldOrderIds.addAll(orderIds);
                        Map<String, Object> paramMap = Maps.newHashMap();
                        paramMap.put(PaymentConstants.Field.OrderIdText.apiName, Joiner.on(",").join(oldOrderIds));
                        //update by quzf,父类有批量插入异步操作，所以此处更新时不关注版本号，防止和父类同时保存主数据版本号不一致报错
                        serviceFacade.updateWithMap(requestContext.getUser(), customerPaymentObjectData, paramMap);
                    }
                });
            }
        });
        try {
            parallelTask.run();
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    protected Set<String> getOrderList(User user, IObjectDescribe orderPaymentDescribe, IObjectData paymentData) {
        List<IObjectData> dataList =
                serviceFacade.findDetailObjectDataListIgnoreFormula(orderPaymentDescribe, paymentData, user);
        Set<String> orderIdList = Sets.newHashSet();
        if (CollectionUtils.isNotEmpty(dataList)) {
            orderIdList.addAll(dataList.stream().map(x -> x.get(OrderPaymentConstants.Field.SalesOrder.apiName, String.class)).filter(StringUtils::isNotEmpty).collect(Collectors.toSet()));
        }
        return orderIdList;
    }

    public void checkOrderPaymentImportCollectionType(IObjectData objectData, BaseImportDataAction.ImportData detailData
            , List<BaseImportAction.ImportError> errorList) {
        String collectionType = objectData.get(PaymentConstants.Field.CollectionType.apiName, String.class);
        BigDecimal paymentAmount = detailData.getData().get(OrderPaymentConstants.Field.PaymentAmount.apiName, BigDecimal.class, BigDecimal.ZERO);
        String orderId = detailData.getData().get(OrderPaymentConstants.Field.SalesOrder.apiName, String.class);
        String returnedGoodsInvoiceId = detailData.getData().get(OrderPaymentConstants.Field.ReturnedGoodsInvoice.apiName, String.class);

        if (com.google.common.base.Strings.isNullOrEmpty(returnedGoodsInvoiceId) == com.google.common.base.Strings.isNullOrEmpty(orderId)) {
            errorList.add(new BaseImportAction.ImportError(detailData.getRowNo(), I18N.text(PaymentI18NKey.ORDER_PAYMENT_ORDER_RETURN_ONLY_ONE)));
        }
        if (collectionType.equals(PaymentCollectionTypeEnum.Red.value)) {
            if (com.google.common.base.Strings.isNullOrEmpty(returnedGoodsInvoiceId)) {
                errorList.add(new BaseImportAction.ImportError(detailData.getRowNo(), I18N.text(PaymentI18NKey.ORDER_PAYMENT_RED_RETURN_ID_NOT_EMPTY)));
            }
            if (paymentAmount.compareTo(BigDecimal.ZERO) > 0) {
                errorList.add(new BaseImportAction.ImportError(detailData.getRowNo(), I18N.text(PaymentI18NKey.ORDER_PAYMENT_RED_AMOUNT_NOT_GT_ZERO)));
            }
        }
        if (collectionType.equals(PaymentCollectionTypeEnum.Blue.value)) {
            if (com.google.common.base.Strings.isNullOrEmpty(orderId)) {
                errorList.add(new BaseImportAction.ImportError(detailData.getRowNo(), I18N.text(PaymentI18NKey.ORDER_PAYMENT_BLUE_ORDER_ID_NOT_EMPTY)));
            }
            if (paymentAmount.compareTo(BigDecimal.ZERO) < 0) {
                errorList.add(new BaseImportAction.ImportError(detailData.getRowNo(), I18N.text(PaymentI18NKey.ORDER_PAYMENT_BLUE_AMOUNT_NOT_GT_ZERO)));
            }
        }
    }
}
