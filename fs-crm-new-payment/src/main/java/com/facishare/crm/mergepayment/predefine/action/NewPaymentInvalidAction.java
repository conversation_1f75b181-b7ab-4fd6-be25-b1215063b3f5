package com.facishare.crm.mergepayment.predefine.action;

import com.facishare.crm.newpayment.predefine.manager.PaymentPlanManager;
import com.facishare.crm.newpayment.util.NewPaymentUtil;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

public class NewPaymentInvalidAction extends StandardInvalidAction {
    private final PaymentPlanManager paymentPlanManager = SpringUtil.getContext().getBean(PaymentPlanManager.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        NewPaymentUtil.checkPaymentInvalid(this.objectDataList);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        IObjectData resultData = ObjectDataExt.of(result.getObjectData()).getObjectData();
        String paymentId = resultData.getId();
        if (NewPaymentUtil.syncUpdatePaymentPlanStatus(actionContext.getTenantId())) {
            paymentPlanManager.updatePaymentPlanByPaymentIds(actionContext.getUser(), Lists.newArrayList(paymentId));
        }
        return result;
    }
}
