package com.facishare.crm.mergepayment.predefine.controller;

import com.facishare.crm.mergepayment.predefine.manager.NewPaymentManger;
import com.facishare.paas.appframework.core.predef.controller.StandardSearchListController;
import com.facishare.paas.metadata.util.SpringUtil;

public class NewPaymentSearchListController extends StandardSearchListController {
    private final NewPaymentManger newPaymentManger = SpringUtil.getContext().getBean(NewPaymentManger.class);

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        newPaymentManger.parseOrderName(controllerContext.getUser(),result.getDataList());
        return result;
    }
}
