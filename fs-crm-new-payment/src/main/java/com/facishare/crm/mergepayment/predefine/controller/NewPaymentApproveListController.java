package com.facishare.crm.mergepayment.predefine.controller;

import com.facishare.crm.newpayment.constants.NewPaymentI18N;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.stream.Collectors;

public class NewPaymentApproveListController extends NewPaymentListController {
    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {

        SearchTemplateQuery query = getSearchTemplateQuery(controllerContext.getUser(), objectDescribe,
                getSearchTemplateId(), getSearchQueryInfo());

        List<IFilter> iFilters = query.getFilters().stream()
                .filter(f -> f.getFieldName().equals(ObjectLifeStatus.NORMAL.getCode())).collect(Collectors.toList());
        if (iFilters.isEmpty()) {
            throw new ValidateException(I18N.text(NewPaymentI18N.SO_PAYMENT_NOTFINDLIFTSTATUS));
        }
        if (iFilters.size() > 1) {
            throw new ValidateException(I18N.text(NewPaymentI18N.SO_PAYMENT_NOTSUPPORTSTATUSFILTER));
        }

        Filter filter = new Filter();
        filter.setConnector("AND");
        filter.setFieldValues(Lists.newArrayList(controllerContext.getUser().getUserId()));
        filter.setOperator(Operator.CONTAINS);
        if (iFilters.get(0).getFieldValues().contains(ObjectLifeStatus.NORMAL.getCode())) {
            filter.setFieldName("approved_employee_id");
        } else {
            filter.setFieldName("approve_employee_id");
        }
        query.addFilters(Lists.newArrayList(filter));

        //已确认的回款  不按照回款状态筛选
        if (iFilters.get(0).getFieldValues().contains(ObjectLifeStatus.NORMAL.getCode())) {
            query.getFilters().removeIf(f -> f.getFieldName().equals(ObjectLifeStatus.LIFE_STATUS_API_NAME));
        }
        return query;
    }


    private SearchTemplateQuery getSearchTemplateQuery(User user, ObjectDescribeExt objectDescribe, String templateId, String searchQueryInfo) {
        SearchTemplateQuery query = (SearchTemplateQuery) SearchTemplateQuery.fromJsonString(searchQueryInfo);
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(query);

        //自定义对象默认排序
        queryExt.setDefaultOrderBy(objectDescribe);
        ISearchTemplate searchTemplate = serviceFacade.findSearchTemplate(user, templateId, objectDescribe.getApiName());

        if (searchTemplate != null) {
            List<IFilter> filters = searchTemplate.getFilters();
            filters.removeIf(o -> "relevant_team.teamMemberEmployee".equals(o.getFieldName()));
            queryExt.addFilters(filters);
        }
        return query;
    }
}
