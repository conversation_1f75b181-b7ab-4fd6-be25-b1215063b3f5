package com.facishare.crm.mergepayment.predefine.action;

import com.facishare.crm.mergepayment.predefine.manager.NewPaymentManger;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportDataAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.List;

public class NewOrderPaymentInsertImportDataAction extends StandardInsertImportDataAction {

    private final NewPaymentManger newPaymentManger = SpringUtil.getContext().getBean(NewPaymentManger.class);

    @Override
    protected void customValidate(List<ImportData> dataList) {
        super.customValidate(dataList);
        List<ImportError> errorList = newPaymentManger.customOrderPaymentValidate(actionContext.getRequestContext(), this.objectDescribe, dataList);
        mergeErrorList(errorList);
    }

    @Override
    protected void customAfterImport(List<IObjectData> actualList) {
        super.customAfterImport(actualList);
        newPaymentManger.customOrderPaymentAfterImport(actionContext.getRequestContext(), this.objectDescribe, actualList);
    }
}
