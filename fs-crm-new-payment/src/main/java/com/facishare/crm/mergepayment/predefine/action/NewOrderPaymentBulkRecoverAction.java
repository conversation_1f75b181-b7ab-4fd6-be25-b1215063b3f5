package com.facishare.crm.mergepayment.predefine.action;

import com.facishare.crm.newpayment.constants.NewPaymentI18N;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardBulkRecoverAction;

public class NewOrderPaymentBulkRecoverAction extends StandardBulkRecoverAction {
    @Override
    protected void before(Arg arg) {
        throw new ValidateException(I18N.text(NewPaymentI18N.ORDER_PAYMENT_NOT_SUPPORT_ACTION, ObjectAction.RECOVER.getActionLabel()));
    }
}
