package com.facishare.crm.mergepayment.predefine.action;

import com.facishare.crm.mergepayment.predefine.manager.NewPaymentManger;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportTemplateAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.List;

public class NewOrderPaymentInsertImportTemplateAction extends StandardInsertImportTemplateAction {
    private final NewPaymentManger newPaymentManger = SpringUtil.getContext().getBean(NewPaymentManger.class);

    @Override
    protected void customHeader(List<IFieldDescribe> headerFieldList) {
        super.customHeader(headerFieldList);
        newPaymentManger.customOrderPaymentHeader(headerFieldList);
    }
}
