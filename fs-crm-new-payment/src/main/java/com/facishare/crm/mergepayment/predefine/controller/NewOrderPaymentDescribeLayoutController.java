package com.facishare.crm.mergepayment.predefine.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.mergepayment.predefine.manager.NewPaymentManger;
import com.facishare.crm.newpayment.util.NewPaymentUtil;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.newpayment.constants.NewPaymentConst;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import java.util.*;

public class NewOrderPaymentDescribeLayoutController extends StandardDescribeLayoutController {
    private final NewPaymentManger newPaymentManger = SpringUtil.getContext().getBean(NewPaymentManger.class);

    @Override
    protected boolean supportSaveDraft() {
        return false;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        String layoutType = arg.getLayout_type();
        LayoutDocument layoutDocument = result.getLayout();
        ObjectDescribeDocument objectDescribeDocument = result.getObjectDescribe();

        boolean newCustomerAccountEnable = newPaymentManger.isCustomerAccountEnable(controllerContext.getUser());

        if ("edit".equals(layoutType)) {
            NewPaymentUtil.setReadOnly(layoutDocument, Sets.newHashSet(NewPaymentConst.ORDER_ID, NewPaymentConst.ACCOUNT_ID), true);
        } else if ("add".equals(layoutType)) {
            NewPaymentUtil.setReadOnly(layoutDocument, Sets.newHashSet(NewPaymentConst.ACCOUNT_ID), false);
        }
        if (Objects.nonNull(objectDescribeDocument)) {
            ObjectDescribeExt objectDescribeExt = ObjectDescribeExt.of(objectDescribeDocument);
            ObjectReferenceFieldDescribe customerObjectReferenceFieldDescribe = (ObjectReferenceFieldDescribe) objectDescribeExt.getFieldDescribe(NewPaymentConst.ACCOUNT_ID);
            Map<String, Set<String>> cascadeDetailApiNameMap = (Map<String, Set<String>>) customerObjectReferenceFieldDescribe.get("cascade_detail_api_name");
            if (cascadeDetailApiNameMap == null) {
                cascadeDetailApiNameMap = Maps.newHashMap();
            }
            Collection<String> detailPaymentFields = cascadeDetailApiNameMap.computeIfAbsent(Utils.ORDER_PAYMENT_API_NAME, x -> Sets.newHashSet());
            detailPaymentFields.add(NewPaymentConst.PAYMENT_ID);
            detailPaymentFields.add(NewPaymentConst.ORDER_ID);
            customerObjectReferenceFieldDescribe.set("cascade_detail_api_name", cascadeDetailApiNameMap);

            ObjectReferenceFieldDescribe orderReferenceFieldDescribe = (ObjectReferenceFieldDescribe) objectDescribeExt.getFieldDescribe(NewPaymentConst.ORDER_ID);
            orderReferenceFieldDescribe.set("cascade_parent_api_name", Lists.newArrayList(NewPaymentConst.ACCOUNT_ID));
            List salesWheres = orderReferenceFieldDescribe.getWheres();
            if (CollectionUtils.notEmpty(salesWheres)) {
                salesWheres.forEach(x -> {
                    Map where = (Map) x;
                    Object filtersObject = where.get("filters");
                    if (filtersObject instanceof ArrayList) {
                        ArrayList arrayList = (ArrayList) filtersObject;
                        arrayList.forEach(item -> {
                            if (item instanceof Map) {
                                Map map = (Map) item;
                                String fieldName = String.valueOf(map.get("field_name"));
                                if ("account_id".equals(fieldName)) {
                                    map.put("field_values", Lists.newArrayList("$account_id$"));
                                }
                            }
                        });
                    } else if (filtersObject instanceof JSONArray) {
                        JSONArray jsonArray = (JSONArray) filtersObject;
                        jsonArray.forEach(v -> {
                            JSONObject jsonObject = (JSONObject) v;
                            String fieldName = jsonObject.getString("field_name");
                            if ("account_id".equals(fieldName)) {
                                jsonObject.put("field_values", Lists.newArrayList("$account_id$"));
                            }
                        });
                    } else if (Objects.nonNull(filtersObject)) {
                        log.warn("filtersObject:{},classType:{}", filtersObject, filtersObject.getClass().getCanonicalName());
                    }
                });
                orderReferenceFieldDescribe.setWheres(salesWheres);
            }

            MasterDetailFieldDescribe paymentMasterDetailFieldDescribe = (MasterDetailFieldDescribe) objectDescribeExt.getFieldDescribe(NewPaymentConst.PAYMENT_ID);
            paymentMasterDetailFieldDescribe.set("cascade_parent_api_name", Lists.newArrayList(NewPaymentConst.ACCOUNT_ID));

            List<LinkedHashMap> wheres = Lists.newArrayList();
            LinkedHashMap accountWhere = new LinkedHashMap();
            Map<String, Object> accountFilter = Maps.newHashMap();
            accountFilter.put("operator", "EQ");
            accountFilter.put("field_name", "account_id");
            accountFilter.put("field_values", Lists.newArrayList("$account_id$"));
            accountFilter.put("value_type", 2);

            Map<String, Object> paymentAvailableAmountFilter = Maps.newHashMap();
            paymentAvailableAmountFilter.put("operator", "GT");
            paymentAvailableAmountFilter.put("field_name", "available_amount");
            paymentAvailableAmountFilter.put("field_values", Lists.newArrayList(0));

            Map<String, Object> lifeStatusFilter = Maps.newHashMap();
            lifeStatusFilter.put("operator", "EQ");
            lifeStatusFilter.put("field_name", "life_status");
            lifeStatusFilter.put("field_values", Lists.newArrayList("normal"));

            List filters = Lists.newArrayList(accountFilter, paymentAvailableAmountFilter, lifeStatusFilter);
            if (newCustomerAccountEnable) {
                Map<String, Object> enterIntoAccountFilter = Maps.newHashMap();
                enterIntoAccountFilter.put("operator", "EQ");
                enterIntoAccountFilter.put("field_name", NewPaymentConst.ENTER_INTO_ACCOUNT);
                enterIntoAccountFilter.put("field_values", Lists.newArrayList(Boolean.FALSE));
                filters.add(enterIntoAccountFilter);
            }
            accountWhere.put("connector", "OR");
            accountWhere.put("filters", filters);
            wheres.add(accountWhere);
            paymentMasterDetailFieldDescribe.set("wheres", wheres);
        }
        return result;
    }
}
