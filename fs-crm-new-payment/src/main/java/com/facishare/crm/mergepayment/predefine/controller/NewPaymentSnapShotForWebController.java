package com.facishare.crm.mergepayment.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.newpayment.constants.NewPaymentConst;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardSnapShotForWebController;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.INameCache;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class NewPaymentSnapShotForWebController extends StandardSnapShotForWebController {
    @Override
    protected void fillFieldInfo(User user, IObjectDescribe objectDescribe, Map<String, Object> objData) {
        super.fillFieldInfo(user, objectDescribe, objData);
        objData.computeIfPresent(NewPaymentConst.ORDER_ID, (k, v) -> {
            List<String> orderIds = Arrays.stream(v.toString().split(",")).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            List<INameCache> recordName = serviceFacade.findRecordName(ActionContextExt.of(controllerContext.getUser()).getContext(), Utils.SALES_ORDER_API_NAME, orderIds);
            Map<String, String> idToNameMap = Maps.newHashMap();
            for (INameCache nameCache : recordName) {
                if (StringUtils.isNotEmpty(nameCache.getName())) {
                    idToNameMap.put(nameCache.getId(), nameCache.getName());
                }
            }
            return orderIds.stream().map(oo -> idToNameMap.getOrDefault(oo, "")).collect(Collectors.joining(","));
        });
    }
}
