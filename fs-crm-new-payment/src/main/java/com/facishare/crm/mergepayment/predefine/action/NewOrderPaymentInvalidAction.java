package com.facishare.crm.mergepayment.predefine.action;

import com.facishare.crm.customeraccount.constants.OrderPaymentConstants;
import com.facishare.crm.customeraccount.util.ConfigCenter;
import com.facishare.crm.mergepayment.predefine.manager.NewPaymentManger;
import com.facishare.crm.newpayment.constants.NewPaymentConst;
import com.facishare.crm.newpayment.predefine.manager.PaymentPlanManager;
import com.facishare.crm.newpayment.util.NewPaymentUtil;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;

public class NewOrderPaymentInvalidAction extends StandardInvalidAction {
    private final PaymentPlanManager paymentPlanManager = SpringUtil.getContext().getBean(PaymentPlanManager.class);
    private final NewPaymentManger newPaymentManger = SpringUtil.getContext().getBean(NewPaymentManger.class);

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        IObjectData objectData = result.getObjectData().toObjectData();
        String planId = objectData.get(OrderPaymentConstants.Field.PaymentPlan.apiName, String.class);
        if (ConfigCenter.syncUpdatePaymentPlanStatus(actionContext.getTenantId())) {
            paymentPlanManager.updatePaymentPlan(actionContext.getUser(), Sets.newHashSet(planId));
        }
        String orderId = objectData.get(NewPaymentConst.ORDER_ID, String.class);
        if (StringUtils.isNotEmpty(orderId)) {
            String paymentId = objectData.get(NewPaymentConst.PAYMENT_ID, String.class);
            newPaymentManger.updatePaymentOrderIdText(actionContext.getRequestContext(), paymentId);
        }
        return result;
    }
}
