package com.facishare.crm.mergepayment.predefine.service;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.newpayment.constants.NewPaymentConst;
import com.facishare.crm.newpayment.util.NewPaymentUtil;
import com.facishare.crm.newpayment.predefine.validator.RedPaymentAmountValidator;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@ServiceModule("new_customer_payment")
public class NewCustomerPaymentService {
    @Autowired
    private RedPaymentAmountValidator redPaymentAmountValidator;
    @Autowired
    private ServiceFacade serviceFacade;

    @ServiceMethod("check")
    public ObjectDataDocument checkPayment(ServiceContext serviceContext, StandardAddAction.Arg arg) {
        IObjectDescribe objectDescribe = serviceFacade.findObject(serviceContext.getTenantId(), Utils.CUSTOMER_PAYMENT_API_NAME);
        String configValue = serviceFacade.findTenantConfig(serviceContext.getUser(), NewPaymentConst.ACCOUNT_RECEIVABLE_CONFIG);
        boolean receivableEnable = NewPaymentUtil.isReceivableEnable(configValue);
        redPaymentAmountValidator.validate(serviceContext.getRequestContext(), arg, objectDescribe, receivableEnable);
        return new ObjectDataDocument();
    }
}
