package com.facishare.crm.mergepayment.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.newpayment.constants.NewPaymentConst;
import com.facishare.paas.appframework.core.predef.controller.StandardNewLogInfoListForWebController;
import com.facishare.paas.appframework.core.predef.service.dto.log.LogRecord;
import com.facishare.paas.appframework.log.dto.LogInfo;
import com.facishare.paas.appframework.log.dto.ModifyRecord;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.INameCache;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class NewPaymentNewLogInfoListForWebController extends StandardNewLogInfoListForWebController {
    @Override
    protected LogRecord modifyRecordToLogRecord(ModifyRecord record) {
        LogRecord logRecord = super.modifyRecordToLogRecord(record);
        List<LogInfo.DiffObjectData> objectDataList = logRecord.getObjectData();
        if (CollectionUtils.isEmpty(objectDataList)) {
            return logRecord;
        }

        objectDataList.forEach(o -> {
            String fieldApiName = o.getFieldApiName();
            Map<String, Object> apiNameToValue = o.getValue();
            Map<String, Object> apiNameToOldValue = o.getOldValue();
            if (NewPaymentConst.ORDER_ID.equals(fieldApiName)) {
                fillOrderName(fieldApiName, apiNameToOldValue);
                fillOrderName(fieldApiName, apiNameToValue);
            }

        });
        logRecord.setObjectData(objectDataList);
        return logRecord;
    }

    private void fillOrderName(String fieldApiName, Map<String, Object> apiNameToValue) {
        apiNameToValue.computeIfPresent(fieldApiName, (k, v) -> {
            List<String> orderIds = Arrays.stream(v.toString().split(",")).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            List<INameCache> recordName = serviceFacade.findRecordName(ActionContextExt.of(controllerContext.getUser()).getContext(), Utils.SALES_ORDER_API_NAME, orderIds);
            Map<String, String> idToName = Maps.newHashMap();
            for (INameCache nameCache : recordName) {
                if (StringUtils.isNotEmpty(nameCache.getName())) {
                    idToName.put(nameCache.getId(), nameCache.getName());
                }
            }
            return Lists.newArrayList(v.toString().split(",")).stream().map(oo -> idToName.getOrDefault(oo, "")).collect(Collectors.joining(","));
        });
    }
}
