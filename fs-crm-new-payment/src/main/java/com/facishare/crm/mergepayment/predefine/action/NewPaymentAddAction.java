package com.facishare.crm.mergepayment.predefine.action;

import com.facishare.crm.customeraccount.constants.Constants;
import com.facishare.crm.customeraccount.constants.PaymentConstants;
import com.facishare.crm.mergepayment.predefine.manager.NewPaymentManger;
import com.facishare.crm.newpayment.constants.NewPaymentConst;
import com.facishare.crm.newpayment.predefine.manager.PaymentPlanManager;
import com.facishare.crm.newpayment.predefine.validator.RedPaymentAmountValidator;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.newpayment.util.NewPaymentUtil;
import com.facishare.idempotent.Idempotent;
import com.facishare.idempotent.Serializer;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.appframework.flow.ApprovalFlowStartResult;
import com.facishare.paas.appframework.metadata.cache.CacheKeys;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Idempotent(serializer = Serializer.Type.java)
public class NewPaymentAddAction extends StandardAddAction {
    private final RedPaymentAmountValidator redPaymentAmountValidator = SpringUtil.getContext().getBean(RedPaymentAmountValidator.class);
    private final PaymentPlanManager paymentPlanManager = SpringUtil.getContext().getBean(PaymentPlanManager.class);
    private final NewPaymentManger newPaymentManger = SpringUtil.getContext().getBean(NewPaymentManger.class);

    @Override
    protected void before(Arg arg) {
        NewPaymentUtil.fillAmountIfNotPresent(arg);
        super.before(arg);
        String configValue = serviceFacade.findTenantConfig(actionContext.getUser(), NewPaymentConst.ACCOUNT_RECEIVABLE_CONFIG);
        boolean receivableEnable = NewPaymentUtil.isReceivableEnable(configValue);
        redPaymentAmountValidator.validate(actionContext.getRequestContext(), arg, objectDescribe, receivableEnable);
        NewPaymentUtil.copyOrderPaymentCustomFieldData(this.objectDescribe, arg);
    }

    @Override
    protected void doSaveData() {
        BigDecimal amount = this.objectData.get(PaymentConstants.Field.Amount.apiName, BigDecimal.class, BigDecimal.ZERO);
        List<IObjectData> orderPaymentDataList = this.detailObjectData.getOrDefault(Utils.ORDER_PAYMENT_API_NAME, Lists.newArrayList());
        BigDecimal orderPaymentUsedAmount = orderPaymentDataList.stream().map(x -> x.get(NewPaymentConst.PAYMENT_AMOUNT, BigDecimal.class, BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
        this.objectData.set(NewPaymentConst.PAYMENT_AMOUNT, orderPaymentUsedAmount);
        this.objectData.set(NewPaymentConst.AVAILABLE_AMOUNT, amount.subtract(orderPaymentUsedAmount));
        this.objectData.set(NewPaymentConst.SUBMIT_TIME, System.currentTimeMillis());
        super.doSaveData();
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        Map<String, List<ObjectDataDocument>> details = result.getDetails();
        List<IObjectData> orderPaymentList = Lists.newArrayList();
        if (MapUtils.isNotEmpty(details)) {
            orderPaymentList = details.getOrDefault(Utils.ORDER_PAYMENT_API_NAME, Lists.newArrayList()).stream().map(ObjectDataDocument::toObjectData).collect(Collectors.toList());
        }
        if (!startApprovalFlowResult.containsKey(objectData.getId()) || startApprovalFlowResult.get(objectData.getId()) == ApprovalFlowStartResult.APPROVAL_NOT_EXIST) {
            NewPaymentUtil.sendDhtMq(actionContext.getUser(), "no_flow", orderPaymentList.stream().map(IObjectData::getId).collect(Collectors.toList()));
        }
        ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
        if (NewPaymentUtil.syncUpdatePaymentPlanStatus(actionContext.getTenantId())) {
            List<ObjectDataDocument> orderPaymentDataList = result.getDetails().getOrDefault(Utils.ORDER_PAYMENT_API_NAME, Lists.newArrayList());
            Set<String> planIds = orderPaymentDataList.stream().map(x -> x.toObjectData().get(NewPaymentConst.PAYMENT_PLAN_ID, String.class)).filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
            task.submit(() -> {
                log.info("start updateOrderPayment {}", objectData.getId());
                paymentPlanManager.updatePaymentPlan(actionContext.getUser(), planIds);
                log.info("end updateOrderPayment {}", objectData.getId());
            });
        }
        try {
            task.run();
        } catch (Exception ex) {
            log.error("context:{},task error", actionContext, ex);
        }
        List<String> orderIds = orderPaymentList.stream().map(x -> x.get(NewPaymentConst.ORDER_ID, String.class)).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        calculateOrderField(orderIds);
        return result;
    }

    private void calculateOrderField(List<String> orderIds) {
        if (CollectionUtils.empty(orderIds)) {
            return;
        }
        String lockKey = CacheKeys.detailLifeStatusUpdateLockKey(actionContext.getTenantId(), objectData.getDescribeApiName(), objectData.getId());
        RLock lock = infraServiceFacade.tryLock(500, 10000, TimeUnit.MILLISECONDS, lockKey);
        if (Objects.isNull(lock)) {
            log.info("try lock failed:{}", lockKey);
            return;
        }
        try {
            //计算订单上的待回款金额、已回款金额
            newPaymentManger.calculateAndUpdateFormulaFields(actionContext.getRequestContext(), Utils.SALES_ORDER_API_NAME, orderIds,
                    Lists.newArrayList(Constants.RECEIVABLE_AMOUNT, Constants.PAYMENT_AMOUNT));
        } finally {
            infraServiceFacade.unlock(lock);
        }
    }
}
