package com.facishare.crm.mergepayment.predefine.action;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.newpayment.predefine.manager.PaymentPlanManager;
import com.facishare.crm.newpayment.util.NewPaymentUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.List;
import java.util.stream.Collectors;

public class NewPaymentBulkInvalidAction extends StandardBulkInvalidAction {
    private final PaymentPlanManager paymentPlanManager = SpringUtil.getContext().getBean(PaymentPlanManager.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        NewPaymentUtil.checkPaymentInvalid(this.objectDataList);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        //非同步作废数据时，需要在CustomerPaymentFlowStartCallbackAction中相应的审批流回调中更改数据状态
        if (CollectionUtils.notEmpty(objectDataList) && !startApprovalFlowAsynchronous) {
            List<String> paymentIds = objectDataList.stream().map(IObjectData::getId).collect(Collectors.toList());
            List<IObjectData> paymentObjectDataList = serviceFacade.findObjectDataByIdsIncludeDeletedIgnoreFormula(actionContext.getUser(), paymentIds, Utils.CUSTOMER_PAYMENT_API_NAME);
            paymentPlanManager.updatePaymentPlanByPaymentDataList(actionContext.getUser(), paymentObjectDataList);
        }
        return result;
    }
}
