<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.fxiaoke.common</groupId>
        <artifactId>fxiaoke-parent-pom</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <groupId>com.facishare</groupId>
    <artifactId>fs-crm</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <modules>
        <module>fs-crm-customeraccount</module>
        <module>fs-crm-sales</module>
        <module>fs-crm-web</module>
        <module>fs-crm-sfainterceptor</module>
        <module>fs-crm-payment</module>
        <module>fs-crm-promotion</module>
        <module>fs-crm-crmcommon</module>
        <module>fs-crm-statement</module>
        <module>fs-crm-member</module>
        <module>fs-crm-marketing</module>
        <module>fs-crm-channel-transaction</module>
        <module>fs-crm-ordering-all</module>
        <module>fs-crm-new-payment</module>
        <module>fs-crm-order</module>
        <module>fs-crm-biz-reconciliation</module>
    </modules>

    <properties>
        <revision>9.5.5-RECON-SNAPSHOT</revision>
        <java.version>1.8</java.version>
        <jdk.version>1.8</jdk.version>
        <war.name>fs</war.name>
        <appframework.version>9.5.5-SNAPSHOT</appframework.version>
        <stock.version>9.5.5-SNAPSHOT</stock.version>
        <fs-paas-function.version>9.4.0-SNAPSHOT</fs-paas-function.version>
        <i18n-util.version>1.4-SNAPSHOT</i18n-util.version>
        <validation.version>2.0.0.Final</validation.version>
<!--        <fs-metadata-provider.version>9.4.0-SNAPSHOT</fs-metadata-provider.version>-->
<!--        <fs-paas-app-flow.version>9.4.0-SNAPSHOT</fs-paas-app-flow.version>-->
        <webApp.contextPath/>
    </properties>

    <dependencyManagement>

        <dependencies>
            <dependency>
                <groupId>com.facishare.paas</groupId>
                <artifactId>fs-paas-rule-service-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.facishare.paas</groupId>
                <artifactId>fs-paas-rule-common</artifactId>
                <version>2.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-pod-client</artifactId>
                <version>${fs-pod-client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.grpc</groupId>
                        <artifactId>grpc-netty</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.grpc</groupId>
                        <artifactId>grpc-protobuf</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.grpc</groupId>
                        <artifactId>grpc-stub</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-webpage-customer-api</artifactId>
                <version>9.2.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-core</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-web</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-metadata-restdriver</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-privilege</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-privilege-temp</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-fcp</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-config</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-metadata-util</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-coordination</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-function-biz-api</artifactId>
                <version>${fs-paas-function.version}</version>
            </dependency>

            <!--租户级配置-->
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-bizconf-api</artifactId>
                <version>2.0.0-SNAPSHOT</version>
            </dependency>

<!--            <dependency>-->
<!--                <groupId>com.facishare.open</groupId>-->
<!--                <artifactId>rocketmq-spring-support</artifactId>-->
<!--                <version>1.0.1-SNAPSHOT</version>-->
<!--            </dependency>-->

            <dependency>
                <groupId>com.facishare.open</groupId>
                <artifactId>fs-open-app-center-common-utils</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.facishare.open</groupId>
                <artifactId>fs-wechat-union-core-api</artifactId>
                <version>0.0.3-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.facishare.open</groupId>
                <artifactId>fs-wechat-proxy-core-api</artifactId>
                <version>0.0.8-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-uc-api</artifactId>
                <version>${fs-uc-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-cache</artifactId>
                <version>${fs-cache.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-metadata-provider</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-paas-app-flow</artifactId>
                <version>${appframework.version}</version>
            </dependency>

            <!-- 文件系统后台接口代理 -->
            <dependency>
                <groupId>com.lowagie</groupId>
                <artifactId>itext</artifactId>
                <version>2.1.7</version>
            </dependency>

            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>fs-sandbox-api</artifactId>
                <version>${sandbox-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fxiaoke</groupId>
                <artifactId>fs-paas-gnomon-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.fxiaoke</groupId>
                <artifactId>i18n-client</artifactId>
                <version>${i18n-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.facishare</groupId>
                <artifactId>i18n-util</artifactId>
                <version>${i18n-util.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-all</artifactId>
                <version>2.4.11</version>
                <scope>compile</scope>
            </dependency>

            <dependency>
                <groupId>io.protostuff</groupId>
                <artifactId>protostuff-api</artifactId>
                <version>${protostuff.version}</version>
            </dependency>
            <dependency>
                <groupId>io.protostuff</groupId>
                <artifactId>protostuff-runtime</artifactId>
                <version>${protostuff.version}</version>
            </dependency>
            <dependency>
                <groupId>io.protostuff</groupId>
                <artifactId>protostuff-core</artifactId>
                <version>${protostuff.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fxiaoke</groupId>
                <artifactId>fs-rocketmq-support</artifactId>
                <version>${rocketmq-support.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-client</artifactId>
                <version>${apache.rocketmq.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-common</artifactId>
                <version>${apache.rocketmq.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fxiaoke</groupId>
                <artifactId>fs-hosts-record</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp3.version}</version>
            </dependency>

            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>${validation.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>3.17</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>3.17</version>
            </dependency>

            <dependency>
                <groupId>com.fxiaoke.msg</groupId>
                <artifactId>fs-message-api</artifactId>
                <version>2.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.thoughtworks.xstream</groupId>
                <artifactId>xstream</artifactId>
                <version>1.4.20</version>
            </dependency>

            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-runtime</artifactId>
                <version>1.2.71</version>
            </dependency>

            <dependency>
                <groupId>com.github.jsqlparser</groupId>
                <artifactId>jsqlparser</artifactId>
                <version>4.6</version>
            </dependency>

            <dependency>
                <groupId>com.fxiaoke.common</groupId>
                <artifactId>fs-job-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.fxiaoke</groupId>
                <artifactId>fs-enterpriserelation-rest-api2</artifactId>
                <version>2.1.6-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-all</artifactId>
                <version>1.10.19</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.2.7</version>
                <configuration>
                    <flattenedPomFilename>pom-xml-flattened</flattenedPomFilename>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <!-- compiler插件, 设定JDK版本 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.0</version>
                    <configuration>
                        <source>${jdk.version}</source>
                        <target>${jdk.version}</target>
                        <showWarnings>false</showWarnings>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>2.10.4</version>
                </plugin>
                <!-- enforcer插件, 设定环境与依赖的规则 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>1.4.1</version>
                    <executions>
                        <execution>
                            <id>enforce-banned-dependencies</id>
                            <goals>
                                <goal>enforce</goal>
                            </goals>
                            <configuration>
                                <rules>
                                    <bannedDependencies>
                                        <searchTransitive>true</searchTransitive>
                                        <!-- 避免引入过期的jar包 -->
                                        <excludes>
                                            <exclude>aspectj:aspectj*</exclude>
                                            <exclude>org.springframework:2.*</exclude>
                                            <exclude>org.springframework:3.0.*</exclude>
                                        </excludes>
                                    </bannedDependencies>
                                </rules>
                                <fail>true</fail>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>2.4</version>
                    <executions>
                        <execution>
                            <id>attach-sources</id>
                            <goals>
                                <goal>jar</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>

                <!-- resource插件 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>2.7</version>
                </plugin>

                <!-- install插件 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-install-plugin</artifactId>
                    <version>2.5.2</version>
                </plugin>

                <!-- clean插件 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-clean-plugin</artifactId>
                    <version>2.6.1</version>
                </plugin>

                <!-- dependency插件 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>2.10</version>
                </plugin>

                <plugin>
                    <groupId>org.sonarsource.scanner.maven</groupId>
                    <artifactId>sonar-maven-plugin</artifactId>
                    <version>3.2</version>
                </plugin>
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>0.7.8</version>
                    <executions>
                        <execution>
                            <id>JaCoCo Agent</id>
                            <phase>test-compile</phase>
                            <goals>
                                <goal>prepare-agent</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>JaCoCo Report</id>
                            <phase>test</phase>
                            <goals>
                                <goal>report</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-war-plugin</artifactId>
                    <version>3.3.2</version>
                    <configuration>
                        <warName>${war.name}</warName>
                    </configuration>
                </plugin>
                <!-- jetty插件, 设定context path与spring profile -->
                <plugin>
                    <groupId>org.eclipse.jetty</groupId>
                    <artifactId>jetty-maven-plugin</artifactId>
                    <version>${jetty.version}</version>
                    <configuration>
                        <scanIntervalSeconds>5</scanIntervalSeconds>
                        <useTestClasspath>true</useTestClasspath>
                        <webAppConfig>
                            <contextPath>/${webApp.contextPath}</contextPath>
                        </webAppConfig>
                    </configuration>
                </plugin>
                <!-- tomcat 插件 -->
                <plugin>
                    <groupId>org.apache.tomcat.maven</groupId>
                    <artifactId>tomcat7-maven-plugin</artifactId>
                    <version>2.2</version>
                    <configuration>
                        <port>8080</port>
                        <path>/${webApp.contextPath}</path>
                        <server>tomcat-development-server</server>
                        <useTestClasspath>true</useTestClasspath>
                    </configuration>
                </plugin>
                <!-- Mandatory plugins for using Spock -->
                <plugin>
                    <!-- The gmavenplus plugin is used to compile Groovy code. To learn more about this plugin, visit https://github.com/groovy/GMavenPlus/wiki -->
                    <groupId>org.codehaus.gmavenplus</groupId>
                    <artifactId>gmavenplus-plugin</artifactId>
                    <version>1.5</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>addTestSources</goal>
                                <goal>compile</goal>
                                <goal>testCompile</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <!-- Optional plugins for using Spock -->
                <!-- Optional plugins for using Spock -->
                <!-- Only required if names of spec classes don't match default Surefire patterns (`*Test` etc.) -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>2.19.1</version>
                    <configuration>
                        <useFile>false</useFile>
                        <includes>
                            <include>**/*Spec.java</include>
                            <include>**/*Test.java</include>
                        </includes>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <reporting>
        <plugins>
            <!-- checkstyle -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>2.17</version>
            </plugin>
        </plugins>
    </reporting>

    <distributionManagement>
        <snapshotRepository>
            <id>fs</id>
            <name>fs-snapshot</name>
            <url>https://maven.foneshare.cn/artifactory/libs-snapshot-local</url>
        </snapshotRepository>
        <repository>
            <id>fs</id>
            <name>fs-release</name>
            <url>https://maven.foneshare.cn/artifactory/libs-release-local</url>
        </repository>
    </distributionManagement>
</project>
