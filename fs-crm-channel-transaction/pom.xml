<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fs-crm</artifactId>
        <groupId>com.facishare</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>fs-crm-channel-transaction</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-crm-crmcommon</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-core</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.squareup.okhttp</groupId>
                    <artifactId>okhttp</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-webmvc</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.codehaus.groovy</groupId>
                    <artifactId>groovy-all</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-paas-gnomon-api</artifactId>
        </dependency>
    </dependencies>

</project>