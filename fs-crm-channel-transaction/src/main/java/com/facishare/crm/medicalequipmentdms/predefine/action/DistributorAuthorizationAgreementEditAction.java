package com.facishare.crm.medicalequipmentdms.predefine.action;

import com.facishare.crm.medicalequipmentdms.constants.AuthorizationAgreementLinesConst;
import com.facishare.crm.medicalequipmentdms.constants.DistributorAuthorizationAgreementConst;
import com.facishare.crm.medicalequipmentdms.predefine.manager.DistributorAuthorizationAgreementManager;
import com.facishare.crm.medicalequipmentdms.predefine.manager.MedicalEquipmentDmsTaskManager;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public class DistributorAuthorizationAgreementEditAction extends StandardEditAction {

    private final DistributorAuthorizationAgreementManager checkManager = SpringUtil.getContext().getBean(DistributorAuthorizationAgreementManager.class);
    private final MedicalEquipmentDmsTaskManager medicalEquipmentDmsTaskManager = SpringUtil.getContext().getBean(MedicalEquipmentDmsTaskManager.class);
    private String expiredStatus;
    @Override
    protected void before(Arg arg) {
        super.before(arg);
        // 经销商名称，类型、明细必填
        List<IObjectData> authorizationAgreementLinesObjectData = detailObjectData.get(AuthorizationAgreementLinesConst.OBJECT_API_NAME);
        checkManager.checkMasterAddFieldsRequired(objectData);
        checkManager.checkSlaveAddFieldsRequired(authorizationAgreementLinesObjectData);
        // 检查协议重复性
        checkManager.checkingAgreementRepeatability(actionContext.getTenantId(), objectData, authorizationAgreementLinesObjectData);
        // 检查产品与注册证的一致性
        checkManager.checkProductMatch(actionContext.getTenantId(), authorizationAgreementLinesObjectData);
        // 根据传入的ExpiryDate计算出状态值，返回是否已过期，生效中的数据需要发送mq变更状态信息
        expiredStatus = checkManager.calculateExpiredStatus(objectData);
        objectData.set(DistributorAuthorizationAgreementConst.Field.Status.apiName, expiredStatus);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        medicalEquipmentDmsTaskManager.sendDistributorAuthorizationAgreementTask(actionContext.getUser(), this.objectData, expiredStatus);
        return result;
    }
}