package com.facishare.crm.medicalequipmentdms.constants;

import lombok.Getter;

/**
 * <AUTHOR>
 * @create: 2024/8/1 11:17
 */
public interface InvoiceSubmissionLinesConst {

    String OBJECT_API_NAME = "InvoiceSubmissionLinesObj";
    String DEFAULT_LAYOUT_API_NAME = "InvoiceSubmissionLines_default_layout__c";
    String STORE_TABLE_NAME = "invoice_submission_lines";

    @Getter
    enum Field {
        // 发票名称
        Name("name"),
        // 购货医院
        PurchaserId("purchaser_id"),
        // 注册证名称
        RegCertId("reg_cert_id"),
        // 产品名称
        ProductId("product_id"),
        // 数量
        Quantity("quantity"),
        // 批次
        BatchId("batch_id"),
        // 序列号
        SerialNumberId("serial_number_id"),
        // 含税单价
        TaxPrice("tax_price"),
        // 含税金额
        TaxAmount("tax_amount"),
        // 备注
        Remark("remark"),
        // 发票上报编号
        InvoiceSubId("invoice_sub_id"),
        ;

        public final String apiName;

        public String getApiName() {
            return apiName;
        }

        Field(String apiName) {
            this.apiName = apiName;
        }

    }

}
