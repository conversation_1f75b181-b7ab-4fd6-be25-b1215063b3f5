package com.facishare.crm.medicalequipmentdms.predefine.manager;

import com.facishare.crm.medicalequipmentdms.constants.InvoiceSubmissionLinesConst;
import com.facishare.crm.medicalequipmentdms.constants.MEI18NKey;
import com.facishare.crm.medicalequipmentdms.constants.TerminalInvoiceSubmissionConst;
import com.facishare.crmcommon.util.SearchUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class TerminalInvoiceSubmissionCheckManager {

    @Autowired
    protected ServiceFacade serviceFacade;

    /**
     * 经销商、从对象产品/数量必填
     */
    public void checkMasterAddFieldsRequired(IObjectData objectData){
        String accountId = objectData.get(TerminalInvoiceSubmissionConst.Field.AccountId.apiName, String.class);
        if (StringUtils.isEmpty(accountId)){
            throw new ValidateException(I18N.text(MEI18NKey.TIS_ACCOUNT_ID_NOT_EXIST));
        }
    }

    public void checkSlaveAddFieldsRequired(List<IObjectData> detailObjectData){
        if (CollectionUtils.empty(detailObjectData)){
            return;
        }
        detailObjectData.forEach(detail -> {
            String productId = detail.get(InvoiceSubmissionLinesConst.Field.ProductId.apiName, String.class);
            if (StringUtils.isEmpty(productId)){
                throw new ValidateException(I18N.text(MEI18NKey.ISL_PRODUCT_ID_NOT_EXIST));
            }
            String quantity = detail.get(InvoiceSubmissionLinesConst.Field.Quantity.apiName, String.class);
            if (StringUtils.isEmpty(quantity)){
                throw new ValidateException(I18N.text(MEI18NKey.ISL_QUANTITY_NOT_EXIST));
            }
        });
    }

    public void fillPurchaserId(IObjectData objectData, List<IObjectData> detailObjectData){
        if (CollectionUtils.empty(detailObjectData)){
            return;
        }
        String accountId = objectData.get(TerminalInvoiceSubmissionConst.Field.AccountId.apiName, String.class);
        detailObjectData.forEach(detail -> {
            String purchaserId = detail.get(InvoiceSubmissionLinesConst.Field.PurchaserId.apiName, String.class);
            if (StringUtils.isEmpty(purchaserId) || !StringUtils.equals(purchaserId, accountId)){
                detail.set(InvoiceSubmissionLinesConst.Field.PurchaserId.apiName, accountId);
            }
        });
    }

    /**
     * 唯一性校验
     * 按【上报经销商】+【终端医院】+【上报年月】判断重复性
     */
    public void checkUnique(String tenantId, IObjectData terminalInvoiceSubmission) {
        String accountId = terminalInvoiceSubmission.get(TerminalInvoiceSubmissionConst.Field.AccountId.apiName, String.class);
        String partnerId = terminalInvoiceSubmission.get(TerminalInvoiceSubmissionConst.Field.PartnerId.apiName, String.class);
        Long submitMonth = terminalInvoiceSubmission.get(TerminalInvoiceSubmissionConst.Field.SubmitMonth.apiName, Long.class);

        List<IObjectData> existTerminalInvoiceSubmissions = queryObject(tenantId, terminalInvoiceSubmission.getId(), accountId, partnerId, submitMonth);
        if (CollectionUtils.empty(existTerminalInvoiceSubmissions)) {
            return;
        }

        throw new ValidateException(I18N.text(MEI18NKey.TIS_SUBMIT_MONTH_ALREADY_EXIST));
    }

    public List<IObjectData> queryObject(String tenantId){
        User user = new User(tenantId, "-10000");

        // 经销商名称+资质类型做唯一性校验
        List<IFilter> filterList = Lists.newArrayList();
        SearchUtil.fillFilterEq(filterList, IObjectData.TENANT_ID, tenantId);
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(filterList);
        query.setOffset(0);
        query.setLimit(1000);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, TerminalInvoiceSubmissionConst.OBJECT_API_NAME, query);
        if (CollectionUtils.notEmpty(queryResult.getData())) {
            return queryResult.getData();
        }
        return Lists.newArrayList();
    }

    public List<IObjectData> queryObject(String tenantId, String excludeTerminalInvoiceSubmissionId, String accountId, String partnerId, Long submitMonth) {
        User admin = User.systemUser(tenantId);

        List<IFilter> filterList = Lists.newArrayList();
        if (!Strings.isNullOrEmpty(excludeTerminalInvoiceSubmissionId)) {
            SearchUtil.fillFilterNotEq(filterList, IObjectData.ID, excludeTerminalInvoiceSubmissionId);
        }

        if (!Strings.isNullOrEmpty(accountId)) {
            SearchUtil.fillFilterEq(filterList, TerminalInvoiceSubmissionConst.Field.AccountId.apiName, accountId);
        }

        if (!Strings.isNullOrEmpty(partnerId)) {
            SearchUtil.fillFilterEq(filterList, TerminalInvoiceSubmissionConst.Field.PartnerId.apiName, partnerId);
        }

        if (submitMonth != null) {
            SearchUtil.fillFilterEq(filterList, TerminalInvoiceSubmissionConst.Field.SubmitMonth.apiName, submitMonth);
        }

        SearchUtil.fillFilterEq(filterList, IObjectData.TENANT_ID, tenantId);

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(filterList);
        query.setOffset(0);
        query.setLimit(1000);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(admin, TerminalInvoiceSubmissionConst.OBJECT_API_NAME, query);
        if (CollectionUtils.notEmpty(queryResult.getData())) {
            return queryResult.getData();
        }
        return Lists.newArrayList();
    }
}
