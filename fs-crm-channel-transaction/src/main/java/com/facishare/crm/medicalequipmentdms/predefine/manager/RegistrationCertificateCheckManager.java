package com.facishare.crm.medicalequipmentdms.predefine.manager;

import com.facishare.crm.medicalequipmentdms.constants.MEI18NKey;
import com.facishare.crm.medicalequipmentdms.constants.RegistrationCertificateConst;
import com.facishare.crmcommon.util.SearchUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class RegistrationCertificateCheckManager {

    @Autowired
    protected ServiceFacade serviceFacade;

    /**
     * 注册证名称+注册证编号进行唯一性校验
     */
    public void checkRegistrationCertificateUnique(String tenantId, IObjectData objectData) {
        List<IObjectData> data = queryObject(tenantId);
        if (CollectionUtils.empty(data)) {
            return;
        }
        String name = objectData.get(RegistrationCertificateConst.Field.Name.apiName, String.class);
        String regCertNumber = objectData.get(RegistrationCertificateConst.Field.RegCertNumber.apiName, String.class);

        data = data.stream()
                .filter(x -> !StringUtil.equals(x.getId(), objectData.getId()))
                .filter(x -> Objects.equals(name, x.get(RegistrationCertificateConst.Field.Name.apiName)) && Objects.equals(regCertNumber, x.get(RegistrationCertificateConst.Field.RegCertNumber.apiName))).collect(Collectors.toList());
        if (!CollectionUtils.empty(data)) {
            throw new ValidateException(I18N.text(MEI18NKey.RC_REG_CERT_NUMBER_ALREADY_EXIST));
        }
    }

    public List<IObjectData> queryObject(String tenantId) {
        User user = new User(tenantId, "-10000");

        // 经销商名称+资质类型做唯一性校验
        List<IFilter> filterList = Lists.newArrayList();
        SearchUtil.fillFilterEq(filterList, IObjectData.TENANT_ID, tenantId);
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(filterList);
        query.setOffset(0);
        query.setLimit(1000);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, RegistrationCertificateConst.OBJECT_API_NAME, query);
        if (CollectionUtils.notEmpty(queryResult.getData())) {
            return queryResult.getData();
        }
        return Lists.newArrayList();
    }
}
