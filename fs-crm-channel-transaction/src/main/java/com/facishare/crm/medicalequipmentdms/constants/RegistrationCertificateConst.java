package com.facishare.crm.medicalequipmentdms.constants;

import lombok.Getter;

/**
 * <AUTHOR>
 * @create: 2024/8/1 11:17
 */
public interface RegistrationCertificateConst {

    String OBJECT_API_NAME = "RegistrationCertificateObj";
    String DEFAULT_LAYOUT_API_NAME = "RegistrationCertificate_default_layout__c";
    String STORE_TABLE_NAME = "registration_certificate";

    @Getter
    enum Field {
        // 注册证名称
        Name("name"),
        // 注册证编号
        RegCertNumber("reg_cert_number"),
        // 有效期
        ExpiryDate("expiry_date"),
        // 渠道商家
        PartnerId("partner_id"),
        // 备注
        Remark("remark"),
        ;

        public String apiName;

        public String getApiName() {
            return apiName;
        }

        Field(String apiName) {
            this.apiName = apiName;
        }

    }
}
