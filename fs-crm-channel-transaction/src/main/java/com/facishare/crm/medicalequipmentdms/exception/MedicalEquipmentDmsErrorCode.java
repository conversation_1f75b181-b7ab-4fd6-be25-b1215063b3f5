package com.facishare.crm.medicalequipmentdms.exception;

import com.facishare.paas.appframework.core.exception.ErrorCode;

/**
 *
 * <AUTHOR>
 * @date 2024/8/2
 */
public enum MedicalEquipmentDmsErrorCode implements ErrorCode {

    MEDICAL_EQUIPMENT_DMS_SWITCH_ERROR(37, "账户校验初始化异常"),
    ;

    int code;
    String message;

    MedicalEquipmentDmsErrorCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    MedicalEquipmentDmsErrorCode(int code) {
        this.code = code;
    }

    @Override
    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

}
