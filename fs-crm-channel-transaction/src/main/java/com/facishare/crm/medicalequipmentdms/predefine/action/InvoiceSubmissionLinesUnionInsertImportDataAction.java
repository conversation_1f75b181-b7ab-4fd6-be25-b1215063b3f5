package com.facishare.crm.medicalequipmentdms.predefine.action;

import com.facishare.crm.medicalequipmentdms.constants.InvoiceSubmissionLinesConst;
import com.facishare.crm.medicalequipmentdms.predefine.manager.TerminalInvoiceSubmissionCheckManager;
import com.facishare.paas.appframework.core.predef.action.StandardUnionInsertImportDataAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 */
public class InvoiceSubmissionLinesUnionInsertImportDataAction extends StandardUnionInsertImportDataAction {

    @Override
    protected void customValidate(List<ImportData> dataList) {
        TerminalInvoiceSubmissionCheckManager checkManager = SpringUtil.getContext().getBean(TerminalInvoiceSubmissionCheckManager.class);
        super.customValidate(dataList);
        List<ImportError> errorList = Lists.newArrayList();
        for (ImportData importData : dataList) {
            IObjectData objectData = importData.getData();
            try {
                // 经销商、从对象产品/数量必填
                checkManager.checkSlaveAddFieldsRequired(Lists.newArrayList(objectData));
            } catch (Exception e) {
                errorList.add(new ImportError(importData.getRowNo(), e.getMessage()));
            }
        }
        mergeErrorList(errorList);


    }
}
