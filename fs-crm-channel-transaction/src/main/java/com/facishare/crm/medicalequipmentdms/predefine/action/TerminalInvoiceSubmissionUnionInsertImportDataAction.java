package com.facishare.crm.medicalequipmentdms.predefine.action;

import com.facishare.crm.medicalequipmentdms.predefine.manager.TerminalInvoiceSubmissionCheckManager;
import com.facishare.paas.appframework.core.predef.action.StandardUnionInsertImportDataAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 */
public class TerminalInvoiceSubmissionUnionInsertImportDataAction extends StandardUnionInsertImportDataAction {

    @Override
    protected void customValidate(List<ImportData> dataList) {
        TerminalInvoiceSubmissionCheckManager checkManager = SpringUtil.getContext().getBean(TerminalInvoiceSubmissionCheckManager.class);
        super.customValidate(dataList);
        List<ImportError> errorList = Lists.newArrayList();
        for (ImportData importData : dataList) {
            IObjectData objectData = importData.getData();
            try {
                checkManager.checkMasterAddFieldsRequired(objectData);
                // 【上报经销商】+【终端医院】+【上报年月】判断重复性
                checkManager.checkUnique(actionContext.getTenantId(), objectData);
            } catch (Exception e) {
                errorList.add(new ImportError(importData.getRowNo(), e.getMessage()));
            }
        }
        mergeErrorList(errorList);
    }
}
