package com.facishare.crm.medicalequipmentdms.predefine.service.dto;

import com.facishare.paas.metadata.api.IObjectData;
import lombok.Data;

public class MedicalEquipmentDmsUpdateStatusModel {
    @Data
    public static class Arg {
        private String objectApiName;
        private String objectDataId;
    }

    public static Arg getArg(IObjectData objectData) {
        MedicalEquipmentDmsUpdateStatusModel.Arg arg = new MedicalEquipmentDmsUpdateStatusModel.Arg();
        arg.setObjectApiName(objectData.getDescribeApiName());
        arg.setObjectDataId(objectData.getId());
        return arg;
    }

    @Data
    public static class Result {
    }
}