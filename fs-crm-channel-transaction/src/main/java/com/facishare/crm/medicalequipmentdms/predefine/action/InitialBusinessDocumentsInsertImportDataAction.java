package com.facishare.crm.medicalequipmentdms.predefine.action;

import com.facishare.crm.medicalequipmentdms.predefine.manager.InitialBusinessDocumentsManager;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportDataAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public class InitialBusinessDocumentsInsertImportDataAction extends StandardInsertImportDataAction {

    @Override
    protected void customValidate(List<ImportData> dataList) {
        InitialBusinessDocumentsManager checkManager = serviceFacade.getBean(InitialBusinessDocumentsManager.class);
        super.customValidate(dataList);
        List<ImportError> errorList = Lists.newArrayList();
        log.info("InitialBusinessDocumentsObj insertImportData validate. dataList: {}", dataList);
        for (ImportData importData : dataList) {
            IObjectData objectData = importData.getData();
            try {
                // 经销商名称，资质类型、资质执照必填
                checkManager.checkAddFieldsRequired(objectData);
                // 唯一性校验：经销商名称+资质类型，做唯一性判断，也就是一个客户一种资质类型，只有一条数据。如果过期则需要更新
                checkManager.checkExpiryDateRepeat(actionContext.getTenantId() ,objectData);
            } catch (Exception e){
                errorList.add(new ImportError(importData.getRowNo(), e.getMessage()));
            }
        }
        mergeErrorList(errorList);
    }
}
