package com.facishare.crm.medicalequipmentdms.predefine.controller;

import com.facishare.crm.medicalequipmentdms.constants.DistributorAuthorizationAgreementConst;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.google.common.collect.Sets;

import java.util.HashSet;

/**
 * <AUTHOR>
 */
public class DistributorAuthorizationAgreementDescribeLayoutController extends StandardDescribeLayoutController {
    @Override
    protected boolean supportSaveDraft() {
        return false;
    }


    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        String layoutType = arg.getLayout_type();

        HashSet<String> needHideFieldSet = Sets.newHashSet();
        needHideFieldSet.add(DistributorAuthorizationAgreementConst.Field.Status.apiName);
        HashSet<String> needReadOnlyFieldSet = Sets.newHashSet();
        needReadOnlyFieldSet.add(DistributorAuthorizationAgreementConst.Field.AccountId.apiName);

        LayoutDocument layoutDocument = LayoutDocument.of(result.getLayout());
        LayoutExt layoutExt = LayoutExt.of(layoutDocument);
        if ("edit".equals(layoutType)) {
            layoutExt.getFormComponent().ifPresent(x -> x.setReadOnly(needReadOnlyFieldSet, true));
            layoutExt.getFormComponent().ifPresent(x -> x.hideFields(needHideFieldSet));
        } else if ("add".equals(layoutType)){
            layoutExt.getFormComponent().ifPresent(x -> x.hideFields(needHideFieldSet));
        }
        result.setLayout(layoutDocument);
        return result;
    }
}