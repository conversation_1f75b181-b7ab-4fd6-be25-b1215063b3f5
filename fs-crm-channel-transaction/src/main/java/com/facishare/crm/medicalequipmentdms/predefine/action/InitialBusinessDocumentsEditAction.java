package com.facishare.crm.medicalequipmentdms.predefine.action;

import com.facishare.crm.medicalequipmentdms.constants.InitialBusinessDocumentsConst;
import com.facishare.crm.medicalequipmentdms.predefine.manager.InitialBusinessDocumentsManager;
import com.facishare.crm.medicalequipmentdms.predefine.manager.MedicalEquipmentDmsTaskManager;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public class InitialBusinessDocumentsEditAction extends StandardEditAction {

    private final InitialBusinessDocumentsManager checkManager = SpringUtil.getContext().getBean(InitialBusinessDocumentsManager.class);
    private final MedicalEquipmentDmsTaskManager medicalEquipmentDmsTaskManager = SpringUtil.getContext().getBean(MedicalEquipmentDmsTaskManager.class);
    private String expiredStatus;

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        checkManager.checkEditFieldsEditable(actionContext.getTenantId(), objectData);
//        checkManager.checkQualificationTypeUnique(data, objectData);
        checkManager.checkExpiryDateRepeat(actionContext.getTenantId(), objectData);
        // 根据传入的ExpiryDate计算出状态值，返回是否已过期，生效中的数据需要发送mq变更状态信息
        expiredStatus = checkManager.calculateExpiredStatus(objectData);
        objectData.set(InitialBusinessDocumentsConst.Field.Status.apiName, expiredStatus);
    }

    @Override
    protected Result after(Arg arg, Result result){
        medicalEquipmentDmsTaskManager.sendInitialBusinessDocumentsTask(actionContext.getUser(), this.objectData, expiredStatus);
        return result;
    }
}