package com.facishare.crm.medicalequipmentdms.predefine.action;

import com.facishare.crm.medicalequipmentdms.constants.InitialBusinessDocumentsConst;
import com.facishare.crm.medicalequipmentdms.predefine.manager.InitialBusinessDocumentsManager;
import com.facishare.crm.medicalequipmentdms.predefine.manager.MedicalEquipmentDmsTaskManager;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public class InitialBusinessDocumentsAddAction extends StandardAddAction {

    private final InitialBusinessDocumentsManager checkManager = SpringUtil.getContext().getBean(InitialBusinessDocumentsManager.class);
    private final MedicalEquipmentDmsTaskManager medicalEquipmentDmsTaskManager = SpringUtil.getContext().getBean(MedicalEquipmentDmsTaskManager.class);
    private String expiredStatus;

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        log.info("InitialBusinessDocumentsObj add validate. objectData: {}", objectData);
        // 经销商名称，资质类型、资质执照必填
        checkManager.checkAddFieldsRequired(objectData);
        // 唯一性校验：经销商名称+资质类型，做唯一性判断，也就是一个客户一种资质类型，只有一条数据。如果过期则需要更新
//        checkManager.checkQualificationTypeUnique(data ,objectData);
        checkManager.checkExpiryDateRepeat(actionContext.getTenantId() ,objectData);
        // 根据传入的ExpiryDate计算出状态值，返回是否已过期，生效中的数据需要发送mq变更状态信息
        expiredStatus = checkManager.calculateExpiredStatus(objectData);
        objectData.set(InitialBusinessDocumentsConst.Field.Status.apiName, expiredStatus);
    }

    @Override
    protected Result after(Arg arg, Result result){
        medicalEquipmentDmsTaskManager.sendInitialBusinessDocumentsTask(actionContext.getUser(), this.objectData, expiredStatus);
        return result;
    }
}