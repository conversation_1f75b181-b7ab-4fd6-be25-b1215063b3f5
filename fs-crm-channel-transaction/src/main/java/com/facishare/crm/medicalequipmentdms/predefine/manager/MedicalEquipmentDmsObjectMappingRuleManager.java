package com.facishare.crm.medicalequipmentdms.predefine.manager;

import com.facishare.crm.medicalequipmentdms.constants.DistributorAuthorizationAgreementObj2PriceBookObjConstants;
import com.facishare.crm.medicalequipmentdms.utils.ObjectMappingButtonUtil;
import com.facishare.crm.medicalequipmentdms.utils.ObjectMappingRuleUtil;
import com.facishare.crmcommon.constants.CommonConstants;
import com.facishare.crmcommon.manager.CommonMappingRuleManager;
import com.facishare.crmcommon.rest.dto.ObjectMappingRuleVO;
import com.facishare.paas.appframework.core.model.User;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 对象映射规则
 * <AUTHOR>
 */
@Service
@Slf4j
public class MedicalEquipmentDmsObjectMappingRuleManager {

    @Autowired
    private CommonMappingRuleManager commonMappingRuleManager;

    /**
     * 创建  映射规则
     * 经销商协议 –》价目表
     * 授权协议明细 –》价目表明细
     */
    public void createPriceBookObjMappingRule(User user) {
        List<Map<String, Object>> objectMappingRule = commonMappingRuleManager.query(user, DistributorAuthorizationAgreementObj2PriceBookObjConstants.RULE_API_NAME);
        if (objectMappingRule != null && !objectMappingRule.isEmpty()) {
            return;
        }

        String sourceApiName = DistributorAuthorizationAgreementObj2PriceBookObjConstants.SOURCE_API_NAME;

        List<ObjectMappingRuleVO> objectMappingRuleVos = Lists.newArrayList(ObjectMappingRuleUtil.getPriceBookObjectMappingRuleVO(), ObjectMappingRuleUtil.getPriceBookProductObjectMappingRuleVO());

        List<String> roleCodes = Lists.newArrayList();
        // CRM管理员
        roleCodes.add(CommonConstants.CRM_MANAGER_ROLE);
        // 订货管理员
        roleCodes.add("00000000000000000000000000000033");
        // 渠道管理员
        roleCodes.add("00000000000000000000000000000025");

        commonMappingRuleManager.createObjectMappingRule(user, sourceApiName, objectMappingRuleVos, ObjectMappingButtonUtil.getPriceBookButtonVO(), roleCodes);
    }

}