package com.facishare.crm.medicalequipmentdms.predefine.action;

import com.facishare.crm.medicalequipmentdms.constants.InvoiceSubmissionLinesConst;
import com.facishare.crm.medicalequipmentdms.constants.MEI18NKey;
import com.facishare.crm.medicalequipmentdms.predefine.manager.TerminalInvoiceSubmissionCheckManager;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public class TerminalInvoiceSubmissionAddAction extends StandardAddAction {

    @Override
    protected void before(Arg arg) {
        TerminalInvoiceSubmissionCheckManager checkManager = serviceFacade.getBean(TerminalInvoiceSubmissionCheckManager.class);
        super.before(arg);
        // 经销商、从对象产品/数量必填
        List<IObjectData> invoiceSubmissionLinesDetailObjectData = detailObjectData.get(InvoiceSubmissionLinesConst.OBJECT_API_NAME);
        if (CollectionUtils.empty(invoiceSubmissionLinesDetailObjectData)) {
            throw new ValidateException(I18N.text(MEI18NKey.TIS_SLAVE_OBJECT_NOT_EXIST));
        }
        checkManager.checkMasterAddFieldsRequired(objectData);
        checkManager.checkSlaveAddFieldsRequired(invoiceSubmissionLinesDetailObjectData);
        // 如果从对象购货医院未填或者与主对象不同，将覆盖从对象值
        checkManager.fillPurchaserId(objectData, invoiceSubmissionLinesDetailObjectData);
        // 【上报经销商】+【终端医院】+【上报年月】判断重复性
        checkManager.checkUnique(actionContext.getTenantId() ,objectData);
    }
}