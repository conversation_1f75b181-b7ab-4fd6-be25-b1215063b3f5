package com.facishare.crm.channeltransaction.predefine.controller;

import com.facishare.crm.channeltransaction.constants.MerchantProductRangeConstants;
import com.facishare.crmcommon.constants.SystemConstants;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.google.common.collect.Sets;

import java.util.Set;

public class MerchantProductRangeDescribeLayoutController extends StandardDescribeLayoutController {
    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        doBizAfter(arg, result);
        return result;
    }

    public void doBizAfter(Arg arg, Result result) {
        String layoutType = arg.getLayout_type();

        if (SystemConstants.LayoutType.Edit.layoutType.equals(layoutType)) {
            LayoutDocument layoutDocument = LayoutDocument.of(result.getLayout());
            LayoutExt layoutExt = LayoutExt.of(layoutDocument);
            Set<String> readOnlyFields = Sets.newHashSet(MerchantProductRangeConstants.Field.PartnerId.apiName);
            layoutExt.getFormComponent().ifPresent(x -> {
                x.setReadOnly(readOnlyFields, true);
            });
        }
    }
}
