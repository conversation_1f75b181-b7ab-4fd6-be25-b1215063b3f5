package predefine.action

import com.facishare.crm.channeltransaction.predefine.action.MerchantProductRangeInsertImportDataAction
import com.facishare.crm.channeltransaction.predefine.manager.MerchantProductRangeManager
import com.facishare.paas.appframework.core.model.ActionContext
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.RequestContextManager
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.predef.action.BaseImportDataAction
import com.facishare.paas.appframework.metadata.ObjectDataExt
import com.facishare.paas.metadata.util.SpringContextUtil
import com.google.common.collect.Lists
import common.BaseSpockTest
import org.springframework.web.context.support.XmlWebApplicationContext

class MerchantProductRangeInsertImportDataActionTest extends BaseSpockTest {

    def "testCheck"() {
        given:
        RequestContext requestContext = RequestContext.builder().ea("1").user(User.systemUser("1")).build()
        ActionContext actionContext = new ActionContext(requestContext, "MerchantProductRangeObj", "")

        RequestContextManager.setContext(requestContext)

        MerchantProductRangeManager merchantProductRangeManager = Mock(MerchantProductRangeManager)
        XmlWebApplicationContext xmlWebApplicationContext = Mock(XmlWebApplicationContext)
        xmlWebApplicationContext.getBean(MerchantProductRangeManager.class) >> merchantProductRangeManager

        new SpringContextUtil().setApplicationContext(xmlWebApplicationContext)

        merchantProductRangeManager.query(*_) >> {
            return Lists.newArrayList(ObjectDataExt.of(["partner_id": "pid"]))
        }

        MerchantProductRangeInsertImportDataAction action = new MerchantProductRangeInsertImportDataAction(
                actionContext: actionContext,
                dataList: Lists.newArrayList(new BaseImportDataAction.ImportData(
                        rowNo: 1,
                        data: ObjectDataExt.of("partner_id": partnerId)
                ))
        )

        when:
        action.check()

        then:
        noExceptionThrown()

        where:
        partnerId << ["pid"]
    }
}
