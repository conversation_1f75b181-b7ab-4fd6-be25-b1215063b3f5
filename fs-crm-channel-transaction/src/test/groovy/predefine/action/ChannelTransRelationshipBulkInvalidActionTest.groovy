package predefine.action

import com.facishare.crm.channeltransaction.enums.SupplyTypeEnum
import com.facishare.crm.channeltransaction.predefine.action.ChannelTransRelationshipBulkInvalidAction
import com.facishare.paas.appframework.metadata.ObjectDataExt
import com.google.common.collect.Lists
import common.BaseSpockTest
import common.TestResult

class ChannelTransRelationshipBulkInvalidActionTest extends BaseSpockTest {
    def "testCheck"() {
        given:
        ChannelTransRelationshipBulkInvalidAction action = new ChannelTransRelationshipBulkInvalidAction(
                objectDataList: Lists.newArrayList(ObjectDataExt.of(dataMap))
        )

        when:
        TestResult<Void> testResult = executeWrapException {
            action.check()
        } as TestResult<Void>

        then:
        String supplyType = ObjectDataExt.of(dataMap).get("supply_type", String.class)
        if (SupplyTypeEnum.Direct.value == supplyType) {
            testResult.fail()
        } else {
            testResult.success()
        }

        where:
        dataMap << [
                ["supply_type": "1"] as Map,
                ["supply_type": "2"] as Map
        ]

    }
}
