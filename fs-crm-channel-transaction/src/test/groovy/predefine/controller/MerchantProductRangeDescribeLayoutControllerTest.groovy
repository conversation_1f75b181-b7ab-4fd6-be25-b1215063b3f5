package predefine.controller

import com.facishare.crm.channeltransaction.predefine.controller.MerchantProductRangeDescribeLayoutController
import com.facishare.paas.appframework.core.model.LayoutDocument
import com.facishare.paas.appframework.core.model.ObjectDataDocument
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController
import com.facishare.paas.appframework.metadata.LayoutExt
import common.BaseSpockTest

class MerchantProductRangeDescribeLayoutControllerTest extends BaseSpockTest {

    def "testDoBizAfter"() {
        given:

        StandardDescribeLayoutController.Arg arg = new StandardDescribeLayoutController.Arg()
        arg.setLayout_type(layoutType)
        StandardDescribeLayoutController.Result result = new StandardDescribeLayoutController.Result()

        LayoutDocument layoutDocument = LayoutDocument.of([
                "components": [
                        ["type": "form", "api_name": "form_component", "field_section": [
                                ["api_name": "base_field_section__c", "form_fields": [
                                        ["field_name": "partner_id", "is_readonly": false]
                                ]]
                        ]]
                ]
        ])
        result.setLayout(layoutDocument)
        result.setObjectData(ObjectDataDocument.of([:]))

        MerchantProductRangeDescribeLayoutController controller = new MerchantProductRangeDescribeLayoutController()


        when:
        controller.doBizAfter(arg, result)

        then:
        noExceptionThrown()
        if ("edit" == layoutType) {
            LayoutExt layoutExt = LayoutExt.of(result.getLayout())
            layoutExt.getField("partner_id").ifPresent { f -> assert f.isReadOnly() }
        }

        where:
        layoutType << ["add", "edit"]

    }
}
