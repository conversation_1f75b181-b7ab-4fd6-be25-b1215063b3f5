package predefine.controller

import com.facishare.crm.channeltransaction.predefine.controller.ChannelTransRelationshipDescribeLayoutController
import com.facishare.paas.appframework.core.model.LayoutDocument
import com.facishare.paas.appframework.core.model.ObjectDataDocument
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController
import com.facishare.paas.appframework.metadata.LayoutExt
import com.facishare.paas.appframework.metadata.ObjectDataExt
import common.BaseSpockTest

class ChannelTransRelationshipDescribeLayoutControllerTest extends BaseSpockTest {

    def "testDoBizAfter"() {
        given:

        StandardDescribeLayoutController.Arg arg = new StandardDescribeLayoutController.Arg()
        arg.setLayout_type(layoutType)
        StandardDescribeLayoutController.Result result = new StandardDescribeLayoutController.Result()

        LayoutDocument layoutDocument = LayoutDocument.of([
                "components": [
                        ["type": "form", "api_name": "form_component", "field_section": [
                                ["api_name": "base_field_section__c", "form_fields": [
                                        ["field_name": "account_id", "is_readonly": false],
                                        ["field_name": "partner_id", "is_readonly": false],
                                        ["field_name": "sales_org", "is_readonly": false],
                                        ["field_name": "supply_type", "is_readonly": false],
                                ]]
                        ]]
                ]
        ])
        result.setLayout(layoutDocument)
        result.setObjectData(ObjectDataDocument.of([:]))

        ChannelTransRelationshipDescribeLayoutController controller = new ChannelTransRelationshipDescribeLayoutController()


        when:
        controller.doBizAfter(arg, result)

        then:
        noExceptionThrown()
        LayoutExt layoutExt = LayoutExt.of(result.getLayout())
        if ("add" == layoutType) {
            layoutExt.getField("sales_org").ifPresent { f ->
                assert f.isReadOnly()
            }
            assert ObjectDataExt.of(result.getObjectData()).getDepartmentFieldValue("sales_org") == "999999"
        } else if ("edit" == layoutType) {
            layoutExt.getField("account_id").ifPresent { f -> assert f.isReadOnly() }
            layoutExt.getField("partner_id").ifPresent { f -> assert f.isReadOnly() }
            layoutExt.getField("sales_org").ifPresent { f -> assert f.isReadOnly() }
            layoutExt.getField("supply_type").ifPresent { f -> assert f.isReadOnly() }
        }

        where:
        layoutType << ["add", "edit"]


    }
}
