package manager.mlo

import com.facishare.crm.channeltransaction.predefine.manager.MerchantProductRangeManager
import com.facishare.crmcommon.manager.NotifyManager
import com.facishare.paas.appframework.core.model.InfraServiceFacade
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.metadata.ObjectDataExt
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.QueryResult
import com.fxiaoke.enterpriserelation2.common.RestResult
import com.fxiaoke.enterpriserelation2.data.PublicEmployeeSimpleData
import com.fxiaoke.enterpriserelation2.result.PublicEmployeeSimplePageResult
import com.fxiaoke.enterpriserelation2.service.PublicEmployeeService
import com.google.common.collect.Lists
import common.BaseSpockTest
import common.TestResult
import org.redisson.api.RLock

class MerchantProductRangeManagerTest extends BaseSpockTest {

    def "testLock"() {
        given:

        InfraServiceFacade infraServiceFacade = Mock(InfraServiceFacade)
        infraServiceFacade.tryLock(*_) >> {
            if (locked) {
                return Mock(RLock)
            }
            return null
        }

        MerchantProductRangeManager manager = new MerchantProductRangeManager(infraServiceFacade: infraServiceFacade)

        when:
        TestResult<Void> testResult = executeWrapException {
            manager.lock(User.systemUser("1"), partnerId)
        } as TestResult<Void>

        then:
        if (!locked) {
            testResult.fail()
        } else {
            testResult.success()
        }

        where:
        locked | partnerId
        true   | "p1"
        false  | "p1"
    }

    def "testQuery"() {
        given:
        ServiceFacade serviceFacade = Mock(ServiceFacade)
        serviceFacade.findBySearchQuery(*_) >> {
            QueryResult<IObjectData> queryResult = new QueryResult<>()
            queryResult.setData(Lists.newArrayList())
            return queryResult
        }
        MerchantProductRangeManager manager = new MerchantProductRangeManager(serviceFacade: serviceFacade)
        User user = User.systemUser("1")

        when:
        manager.query(user, partnerId)

        then:
        noExceptionThrown()

        where:
        partnerId << ["p1"]

    }

    def "testQueryMerchantProductLines"() {
        given:
        ServiceFacade serviceFacade = Mock(ServiceFacade)
        serviceFacade.findBySearchQuery(*_) >> {
            QueryResult<IObjectData> queryResult = new QueryResult<>()
            queryResult.setData(Lists.newArrayList())
            return queryResult
        }

        MerchantProductRangeManager manager = new MerchantProductRangeManager(serviceFacade: serviceFacade)

        User user = User.systemUser("1")
        when:
        manager.queryMerchantProductLines(user, merchantProductRangeId)

        then:
        noExceptionThrown()

        where:
        merchantProductRangeId << ["mprId"]

    }

    def "testCheckEnableInvalid"() {
        given:
        List<IObjectData> merchantProductRangeList = Lists.newArrayList(ObjectDataExt.of(dataMap))

        ServiceFacade serviceFacade = Mock(ServiceFacade)
        serviceFacade.findObjectDataByIds(*_) >> {
            return Lists.newArrayList(ObjectDataExt.of(["product_range_control": control]))
        }

        MerchantProductRangeManager manager = new MerchantProductRangeManager(serviceFacade: serviceFacade)

        when:
        TestResult<Void> testResult = executeWrapException {
            manager.checkEnableInvalid(User.systemUser("1"), merchantProductRangeList)
        } as TestResult<Void>

        then:
        if (control) {
            testResult.fail()
        } else {
            testResult.success()
        }

        where:
        dataMap              | control
        ["partner_id": "p1"] | false

    }

    def "testSendMerchantProductRangeChangeNotice"() {
        given:
        PublicEmployeeService publicEmployeeService = Mock(PublicEmployeeService)

        publicEmployeeService.listPublicEmployeeByOuterRoleIdAndMapperObjectId(*_) >> {
            RestResult<PublicEmployeeSimplePageResult> result = new RestResult<>()
            PublicEmployeeSimplePageResult pageResult = new PublicEmployeeSimplePageResult()
            PublicEmployeeSimpleData simpleData = new PublicEmployeeSimpleData()
            simpleData.setOuterTenantId(100001)
            pageResult.setDataList(Lists.newArrayList(simpleData))
            result.setData(pageResult)
            return result
        }

        ServiceFacade serviceFacade = Mock(ServiceFacade)
        NotifyManager notifyManager = Mock(NotifyManager)

        MerchantProductRangeManager manager = new MerchantProductRangeManager(
                publicEmployeeService: publicEmployeeService,
                serviceFacade: serviceFacade,
                notifyManager: notifyManager
        )

        RequestContext requestContext = RequestContext.builder().ea("1").user(User.systemUser("1")).build()
        when:
        manager.sendMerchantProductRangeChangeNotice(requestContext, "p1")

        then:
        noExceptionThrown()
    }
}
