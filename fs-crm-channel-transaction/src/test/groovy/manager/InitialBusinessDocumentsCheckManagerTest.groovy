package groovy.manager

import com.facishare.crm.medicalequipmentdms.constants.InitialBusinessDocumentsConst
import com.facishare.crm.medicalequipmentdms.predefine.manager.InitialBusinessDocumentsManager
import com.facishare.crmcommon.constants.SystemConstants
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.metadata.ObjectDataExt
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.QueryResult
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
import com.google.common.collect.Lists
import spock.lang.Specification

/**
 * @author: zhoubt 
 * @create: 2024/8/26 19:52
 * */
class InitialBusinessDocumentsCheckManagerTest extends Specification {

    InitialBusinessDocumentsManager checkManager

    ServiceFacade serviceFacade

    def setup() {
        serviceFacade = Mock(ServiceFacade)
        checkManager = new InitialBusinessDocumentsManager(serviceFacade: serviceFacade)
    }

    def "testCheckAddFieldsRequired"() {
        given:
        Map<String, Object> ObjectDataMap = new HashMap<>()
        List<String> attachmentList = new ArrayList<>();
        attachmentList.add("123")
        ObjectDataMap.put(InitialBusinessDocumentsConst.Field.AccountId.apiName, accountId)
        ObjectDataMap.put(InitialBusinessDocumentsConst.Field.QualificationType.apiName, qualificationType)
        if (attachmentEmpty) {
            ObjectDataMap.put(InitialBusinessDocumentsConst.Field.Attachment.apiName, null)
        } else {
            ObjectDataMap.put(InitialBusinessDocumentsConst.Field.Attachment.apiName, attachmentList)
        }
        IObjectData objectData = ObjectDataExt.of(ObjectDataMap)

        when:
        Exception exception = null
        try {
            checkManager.checkAddFieldsRequired(objectData)
        } catch (Exception e) {
            exception = e
        }

        then:
        if (throwException) {
            assert exception != null && exception.class == ValidateException.class
        } else {
            assert exception == null
        }

        where:
        accountId | qualificationType | attachmentEmpty | throwException
        "12"      | "2"               | false           | false
        null      | "2"               | false           | true
        "13"      | null              | false           | true
        "13"      | "2"               | true            | true
    }


    def "testCheckEditFieldsEditable"() {
        given:
        QueryResult<IObjectData> queryResult = new QueryResult<>()
        List<IObjectData> objectDataList = new ArrayList<>()
        Map<String, Object> ObjectDataMapDB = new HashMap<>()
        ObjectDataMapDB.put("_id", id)
        ObjectDataMapDB.put(InitialBusinessDocumentsConst.Field.AccountId.apiName, accountIdDB)
        ObjectDataMapDB.put(InitialBusinessDocumentsConst.Field.QualificationType.apiName, qualificationTypeDB)
        ObjectDataMapDB.put(InitialBusinessDocumentsConst.Field.Status.apiName, "status")
        objectDataList.add(ObjectDataExt.of(ObjectDataMapDB))
        if (queryResultEmpty) {
            queryResult.setData(Lists.newArrayList())
        } else {
            queryResult.setData(objectDataList)
        }
        serviceFacade.findBySearchQuery(_ as User, _ as String, _ as SearchTemplateQuery) >> {
            return queryResult
        }

        Map<String, Object> ObjectDataMapInput = new HashMap<>()
        ObjectDataMapInput.put("_id", id)
        ObjectDataMapInput.put(InitialBusinessDocumentsConst.Field.AccountId.apiName, accountIdInput)
        ObjectDataMapInput.put(InitialBusinessDocumentsConst.Field.QualificationType.apiName, qualificationTypeInput)
        ObjectDataMapInput.put(InitialBusinessDocumentsConst.Field.Status.apiName, "status")
        IObjectData objectDataInput = ObjectDataExt.of(ObjectDataMapInput)

        when:
        Exception exception = null
        try {
            checkManager.checkEditFieldsEditable("83050", objectDataInput)
        } catch (Exception e) {
            exception = e
        }

        then:
        if (throwException) {
            assert exception != null && exception.class == ValidateException.class
        } else {
            assert exception == null
        }

        where:
        id    | accountIdDB | accountIdInput | qualificationTypeDB | qualificationTypeInput | queryResultEmpty | throwException
        "123" | "acc1"      | "acc2"         | "qualification1"    | "qualification2"       | true             | false
        "123" | "acc1"      | "acc2"         | "qualification1"    | "qualification1"       | false            | true
        "123" | "acc1"      | "acc1"         | "qualification1"    | "qualification2"       | false            | true

    }


    def "testCheckQualificationTypeUnique"() {
        given:
        List<IObjectData> objectDataList = new ArrayList<>()
        Map<String, Object> ObjectDataMapDB = new HashMap<>()
        ObjectDataMapDB.put(InitialBusinessDocumentsConst.Field.AccountId.apiName, accountIdDB)
        ObjectDataMapDB.put(InitialBusinessDocumentsConst.Field.QualificationType.apiName, qualificationTypeDB)
        ObjectDataMapDB.put(InitialBusinessDocumentsConst.Field.Status.apiName, status)
        objectDataList.add(ObjectDataExt.of(ObjectDataMapDB))

        Map<String, Object> ObjectDataMapInput = new HashMap<>()
        ObjectDataMapInput.put(InitialBusinessDocumentsConst.Field.AccountId.apiName, accountIdInput)
        ObjectDataMapInput.put(InitialBusinessDocumentsConst.Field.QualificationType.apiName, qualificationTypeInput)
        IObjectData objectDataInput = ObjectDataExt.of(ObjectDataMapInput)

        when:
        Exception exception = null
        try {
            checkManager.checkQualificationTypeUnique(objectDataList, objectDataInput)
        } catch (Exception e) {
            exception = e
        }

        then:
        if (throwException) {
            assert exception != null && exception.class == ValidateException.class
        } else {
            assert exception == null
        }

        where:
        dataEmpty | accountIdDB | accountIdInput | qualificationTypeDB | qualificationTypeInput | status | throwException
        true      | "acc1"      | "acc2"         | "qualification1"    | "qualification2"       | "0"    | false
        false     | "acc1"      | "acc2"         | "qualification1"    | "qualification1"       | "0"    | false
        false     | "acc1"      | "acc1"         | "qualification1"    | "qualification1"       | "0"    | true
        false     | "acc1"      | "acc1"         | "qualification1"    | "qualification1"       | "1"    | true

    }


    def "testCheckExpiryDateRepeat"() {
        given:
        QueryResult<IObjectData> queryResult = new QueryResult<>()
        List<IObjectData> objectDataList = new ArrayList<>()
        Map<String, Object> ObjectDataMapDB = new HashMap<>()
        ObjectDataMapDB.put(SystemConstants.Field.Id.apiName, idDB)
        ObjectDataMapDB.put(InitialBusinessDocumentsConst.Field.AccountId.apiName, accountIdDB)
        ObjectDataMapDB.put(InitialBusinessDocumentsConst.Field.QualificationType.apiName, qualificationTypeDB)
        ObjectDataMapDB.put(InitialBusinessDocumentsConst.Field.Status.apiName, "0")
        ObjectDataMapDB.put(InitialBusinessDocumentsConst.Field.ExpiryDate.apiName, expiryDateDB)
        objectDataList.add(ObjectDataExt.of(ObjectDataMapDB))
        if (queryResultEmpty) {
            queryResult.setData(Lists.newArrayList())
        } else {
            queryResult.setData(objectDataList)
        }
        serviceFacade.findBySearchQuery(_ as User, _ as String, _ as SearchTemplateQuery) >> {
            return queryResult
        }

        Map<String, Object> ObjectDataMapInput = new HashMap<>()
        ObjectDataMapInput.put(SystemConstants.Field.Id.apiName, idInput)
        ObjectDataMapInput.put(InitialBusinessDocumentsConst.Field.AccountId.apiName, accountIdInput)
        ObjectDataMapInput.put(InitialBusinessDocumentsConst.Field.QualificationType.apiName, qualificationTypeInput)
        ObjectDataMapInput.put(InitialBusinessDocumentsConst.Field.ExpiryDate.apiName, expiryDateInput)
        IObjectData objectDataInput = ObjectDataExt.of(ObjectDataMapInput)

        when:
        Exception exception = null
        try {
            checkManager.checkExpiryDateRepeat("83050", objectDataInput)
        } catch (Exception e) {
            exception = e
        }

        then:
        if (throwException) {
            assert exception != null && exception.class == ValidateException.class
        } else {
            assert exception == null
        }

        where:
        queryResultEmpty | idDB | idInput | accountIdDB | accountIdInput | qualificationTypeDB | qualificationTypeInput | expiryDateDB | expiryDateInput | throwException
        true             | "12" | "12"    | "acc1"      | "acc2"         | "qualification1"    | "qualification2"       | "********"   | "********"      | false
        false            | "12" | "12"    | "acc1"      | "acc2"         | "qualification1"    | "qualification1"       | "********"   | "********"      | false
        false            | "12" | "12"    | "acc1"      | "acc1"         | "qualification1"    | "qualification1"       | "********"   | "********"      | false
        false            | "12" | "13"    | "acc1"      | "acc1"         | "qualification1"    | "qualification1"       | "********"   | "********"      | true

    }
}
