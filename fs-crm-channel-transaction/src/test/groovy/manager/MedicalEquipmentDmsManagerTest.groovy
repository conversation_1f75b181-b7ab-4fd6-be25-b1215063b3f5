package manager

import com.facishare.crm.medicalequipmentdms.predefine.manager.MedicalEquipmentDmsManager
import com.facishare.crm.medicalequipmentdms.predefine.manager.MedicalEquipmentDmsObjectMappingRuleManager
import com.facishare.paas.appframework.config.ConfigService
import com.facishare.paas.appframework.core.model.*
import com.facishare.paas.appframework.core.predef.service.ButtonService
import com.facishare.paas.appframework.core.predef.service.dto.button.CreateButton
import com.facishare.paas.appframework.core.predef.service.dto.button.FindButtonList
import com.facishare.paas.appframework.metadata.LayoutExt
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.ui.layout.ILayout
import spock.lang.Specification

class MedicalEquipmentDmsManagerTest extends Specification {

    MedicalEquipmentDmsManager medicalEquipmentDmsManager

    ConfigService configService
    ServiceFacade serviceFacade
    ButtonService buttonService
    MedicalEquipmentDmsObjectMappingRuleManager objectMappingRuleManager

    def setup() {
        configService = Mock(ConfigService)
        serviceFacade = Mock(ServiceFacade)
        buttonService = Mock(ButtonService)
        objectMappingRuleManager = Mock(MedicalEquipmentDmsObjectMappingRuleManager)
        medicalEquipmentDmsManager = new MedicalEquipmentDmsManager(configService: configService, serviceFacade: serviceFacade, buttonService: buttonService, objectMappingRuleManager: objectMappingRuleManager)
    }

    def "testInitMedicalEquipmentDms"() {
        given:
        RequestContext requestContext = RequestContext.builder().tenantId("83050").user(new User("83050", "1000")).ea("83050").build()
        ServiceContext serviceContext = new ServiceContext(requestContext, "123", "123")

        IObjectDescribe iObjectDescribe = Mock(IObjectDescribe)

        configService.findTenantConfig(_ as User, _ as String) >> {
            return tenantConfigValue
        }

        serviceFacade.findObject(_ as String, _ as String) >> {
            return iObjectDescribe
        }

        iObjectDescribe.getFieldDescribe(_ as String) >> {
            return null
        }

        ILayout iLayout = LayoutExt.of(new HashMap())
        serviceFacade.findDefaultLayout(_ as User, _ as String, _ as String) >> {
            return iLayout
        }

        FindButtonList.Result findButtonResult = new FindButtonList.Result()
        List<ButtonDocument> buttonList = new ArrayList<>()
        findButtonResult.setButtonList(buttonList)
        buttonService.findButtonList(_ as FindButtonList.Arg, _ as ServiceContext) >> {
            return findButtonResult
        }

        CreateButton.Result createButtonResult = new CreateButton.Result()
        createButtonResult.setSuccess(success)
        buttonService.create(_ as CreateButton.Arg, _ as ServiceContext) >> {
            return createButtonResult
        }

        when:
        medicalEquipmentDmsManager.initMedicalEquipmentDms(serviceContext)

        then:
        noExceptionThrown()

        where:
        tenantConfigValue | success
        null              | true
        false             | true
        true              | true
        2                 | true
    }

}
