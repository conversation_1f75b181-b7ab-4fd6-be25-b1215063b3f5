package groovy.service

import com.facishare.crm.medicalequipmentdms.constants.AuthorizationAgreementLinesConst
import com.facishare.crm.medicalequipmentdms.exception.MedicalEquipmentDmsBusinessException
import com.facishare.crm.medicalequipmentdms.predefine.manager.MedicalEquipmentDmsManager
import com.facishare.crm.medicalequipmentdms.predefine.service.MedicalEquipmentDmsService
import com.facishare.crm.medicalequipmentdms.predefine.service.dto.QueryDistributorAuthorizationAgreementDto
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.ServiceContext
import com.facishare.paas.appframework.core.model.User
import com.fxiaoke.crmrestapi.arg.ControllerListArg
import com.fxiaoke.crmrestapi.common.data.HeaderObj
import com.fxiaoke.crmrestapi.common.data.ObjectData
import com.fxiaoke.crmrestapi.common.data.Page
import com.fxiaoke.crmrestapi.common.result.Result
import com.fxiaoke.crmrestapi.service.MetadataControllerService
import com.google.common.collect.Lists
import spock.lang.Specification

/**
 * @author: zhoubt
 * @create: 2024/8/26 19:52
 * */
class MedicalEquipmentDmsServiceTest extends Specification {

    MedicalEquipmentDmsService service

    MedicalEquipmentDmsManager medicalEquipmentDmsManager
    MetadataControllerService metadataControllerService

    def setup() {
        medicalEquipmentDmsManager = Mock(MedicalEquipmentDmsManager)
        metadataControllerService = Mock(MetadataControllerService)
        service = new MedicalEquipmentDmsService(medicalEquipmentDmsManager: medicalEquipmentDmsManager, metadataControllerService: metadataControllerService)
    }

    def "testInitMedicalEquipmentDms"() {
        given:

        if (thrown){
            medicalEquipmentDmsManager.initMedicalEquipmentDms(_ as ServiceContext) >> {
                throw new Exception("123")
            }
        }

        RequestContext requestContext = RequestContext.builder().tenantId("83050").user(new User("83050", "1000")).ea("83050").build()
        ServiceContext serviceContext = new ServiceContext(requestContext, "123", "123")

        when:
        Exception exception = null
        try {
            service.initMedicalEquipmentDms(serviceContext)
        } catch (Exception e) {
            exception = e
        }

        then:
        if (throwException) {
            assert exception != null && exception.class == MedicalEquipmentDmsBusinessException.class
        } else {
            assert exception == null
        }

        where:
        thrown | throwException
        true   | true
        false  | false

    }

    def "testGetSkuIdsByDistributorAuthorizationAgreementId"() {
        given:
        Result<Page<ObjectData>> queryResult = new Result<>()
        Page<ObjectData> dataPage = new Page<>()
        ObjectData objectData = new ObjectData()
        objectData.put(AuthorizationAgreementLinesConst.Field.ProductId.apiName, "productId")
        objectData.put(AuthorizationAgreementLinesConst.Field.RegCertId.apiName, "regCertId")
        dataPage.setDataList(Lists.asList(objectData))
        queryResult.setData(dataPage)
        queryResult.setCode(0)
        metadataControllerService.list(_ as HeaderObj, _ as String, _ as ControllerListArg) >> {
            return queryResult
        }

        RequestContext requestContext = RequestContext.builder().tenantId("83050").user(new User("83050", "1000")).ea("83050").build()
        ServiceContext serviceContext = new ServiceContext(requestContext, "123", "123")

        QueryDistributorAuthorizationAgreementDto.Arg arg = new QueryDistributorAuthorizationAgreementDto.Arg()
        arg.setDistributorAuthorizationAgreementId("123")

        when:
        QueryDistributorAuthorizationAgreementDto.Result result = service.getSkuIdsByDistributorAuthorizationAgreementId(serviceContext, arg)

        then:
        assert result.getDataList().size() == 1

    }
}
