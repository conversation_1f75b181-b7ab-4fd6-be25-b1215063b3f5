package com.facishare.crm.bizreconciliation.manager;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import com.facishare.crm.bizreconciliation.util.BizReconciliationUtil;
import com.facishare.paas.metadata.api.search.AggFunctionArg;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.restful.client.FRestApiProxyFactory;
import com.fxiaoke.Utils.ReceiverChannelUtils;
import com.fxiaoke.api.MessageServiceV2;
import com.fxiaoke.constant.ReceiverChannelType;
import com.fxiaoke.model.MessageResponse;
import com.fxiaoke.model.message.SendTextMessageArg;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.facishare.crm.bizreconciliation.consts.ReconAbnormalDataConst;
import com.facishare.crm.bizreconciliation.consts.ReconFlowConst;
import com.facishare.crm.bizreconciliation.consts.ReconLedgerConst;
import com.facishare.crm.bizreconciliation.consts.ReconPendingDataConst;
import com.facishare.crm.bizreconciliation.enums.ReconTaskTypeEnum;
import com.facishare.crm.bizreconciliation.model.ReconTaskCallModel;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.paas.gnomon.api.NomonProducer;
import com.fxiaoke.paas.gnomon.api.entity.NomonMessage;

import javax.annotation.PostConstruct;

@Slf4j
@Component
public class BizReconciliationManager {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private NomonProducer nomonProducer;

    MessageServiceV2 messageServiceV2;

    @PostConstruct
    public void init() throws Exception {
        messageServiceV2 = FRestApiProxyFactory.getInstance().create(MessageServiceV2.class);
    }

    public List<IObjectData> createLedger(User user, List<ReconLedgerConst.Model> modelList) {
        if (CollectionUtils.isEmpty(modelList)) {
            return Lists.newArrayList();
        }
        if (!BizReconciliationUtil.allow(user.getTenantId())) {
            return Lists.newArrayList();
        }
        List<IObjectData> objectDataList = modelList.stream().map(x -> x.toObjectData(user)).collect(Collectors.toList());
        return serviceFacade.bulkSaveObjectData(objectDataList, user);
    }

    public List<IObjectData> createFlow(User user, ReconFlowConst.Model... modelList) {
        List<IObjectData> objectDataList = Arrays.stream(modelList).map(x -> x.toObjectData(user)).collect(Collectors.toList());
        return serviceFacade.bulkSaveObjectData(objectDataList, user);
    }

    public List<IObjectData> createPendingData(User user, ReconPendingDataConst.Model... modelList) {
        List<IObjectData> objectDataList = Arrays.stream(modelList).map(x -> x.toObjectData(user)).collect(Collectors.toList());
        return serviceFacade.bulkSaveObjectData(objectDataList, user);
    }

    public List<IObjectData> queryAbnormalData(User user, String bizModule, String bizObjectApiName, List<String> bizObjectDataIds, ReconAbnormalDataConst.Type type, long startCreateTime) {
        List<IFilter> filters = Lists.newArrayList();
        BizReconciliationUtil.addFilter(filters, ReconAbnormalDataConst.Field.BizModule.apiName, bizModule, Operator.EQ);
        BizReconciliationUtil.addFilter(filters, ReconAbnormalDataConst.Field.BizObjectApiName.apiName, bizObjectApiName, Operator.EQ);
        BizReconciliationUtil.addFilter(filters, ReconAbnormalDataConst.Field.BizObjectDataId.apiName, bizObjectDataIds, Operator.IN);
        BizReconciliationUtil.addFilter(filters, ReconAbnormalDataConst.Field.BizType.apiName, type.value, Operator.EQ);
        BizReconciliationUtil.addFilter(filters, ReconAbnormalDataConst.Field.BizStatus.apiName, ReconAbnormalDataConst.Status.UN_PROCESSED.value, Operator.EQ);
        BizReconciliationUtil.addFilter(filters, ReconAbnormalDataConst.Field.CreateTime.apiName, startCreateTime, Operator.GT);
        return query(user, ReconAbnormalDataConst.API_NAME, filters, Lists.newArrayList(), 0);
    }

    public void createAbnormalData(User user, String bizModule, ReconAbnormalDataConst.Type type, ReconAbnormalDataConst.Model... modelList) {
        Map<String, List<IObjectData>> objectDataListMap = Maps.newHashMap();
        Arrays.stream(modelList).forEach(x -> {
            String objectApiName = x.getBizObjectApiName();
            objectDataListMap.compute(objectApiName, (k, v) -> {
                if (Objects.isNull(v)) {
                    v = Lists.newArrayList();
                }
                v.add(x.toObjectData(user));
                return v;
            });
        });
        objectDataListMap.forEach((objectApiName, dataList) -> {
            Set<String> dataIds = dataList.stream().map(x -> x.get(ReconAbnormalDataConst.Field.BizObjectDataId.apiName, String.class, "")).collect(Collectors.toSet());
            long startTime = BizReconciliationUtil.getBefore24HourTime();
            List<IObjectData> existObjectDataList = queryAbnormalData(user, bizModule, objectApiName, Lists.newArrayList(dataIds), type, startTime);
            List<String> existDataIds = existObjectDataList.stream().map(x -> x.get(ReconAbnormalDataConst.Field.BizObjectDataId.apiName, String.class, "")).collect(Collectors.toList());

            dataList.removeIf(x -> {
                String dataId = x.get(ReconAbnormalDataConst.Field.BizObjectDataId.apiName, String.class);
                return existDataIds.contains(dataId);
            });
            serviceFacade.bulkSaveObjectData(dataList, user);
            for (IObjectData abnormalData : dataList) {
                String bizDataId = abnormalData.get(ReconAbnormalDataConst.Field.BizObjectDataId.apiName, String.class);
                BizReconciliationUtil.sendAuditLog(user, bizModule, null, objectApiName, bizDataId, abnormalData.toJsonString());
            }
        });
    }


    public List<IObjectData> batchUpdateField(User user, List<IObjectData> objectDataList, String fieldName, String fieldValue) {
        CollectionUtils.emptyIfNull(objectDataList).forEach(x -> x.set(fieldName, fieldValue));
        return serviceFacade.batchUpdateByFields(user, objectDataList, Lists.newArrayList(fieldName));
    }

    public void deleteInternalObjectData(User user, List<IObjectData> internalDataList) {
        serviceFacade.bulkDeleteWithInternalDescribe(internalDataList, user);
    }

    public List<IObjectData> query(User user, String queryObjectApiName, List<IFilter> filters, List<OrderBy> orders, int offset) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setOffset(offset);
        searchTemplateQuery.setLimit(100);
        searchTemplateQuery.setFilters(filters);
        if (Objects.nonNull(orders)) {
            searchTemplateQuery.setOrders(orders);
        }
        return serviceFacade.findBySearchQuery(user, queryObjectApiName, searchTemplateQuery).getData();
    }

    public int countAbnormalUnProcessByObject(User user, String bizModule, String bizObjectApiName, String bizType, long startTime, long endTime) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        if (StringUtils.isNotEmpty(bizObjectApiName)) {
            BizReconciliationUtil.addFilter(filters, ReconAbnormalDataConst.Field.BizObjectApiName.apiName, bizObjectApiName, Operator.EQ);
        }
        BizReconciliationUtil.addFilter(filters, ReconAbnormalDataConst.Field.BizModule.apiName, bizModule, Operator.EQ);
        BizReconciliationUtil.addFilter(filters, ReconAbnormalDataConst.Field.BizType.apiName, bizType, Operator.EQ);
        BizReconciliationUtil.addFilter(filters, ReconAbnormalDataConst.Field.BizStatus.apiName, ReconAbnormalDataConst.Status.UN_PROCESSED.value, Operator.EQ);
        BizReconciliationUtil.addFilter(filters, ReconAbnormalDataConst.Field.CreateTime.apiName, startTime, Operator.GTE);
        BizReconciliationUtil.addFilter(filters, ReconAbnormalDataConst.Field.CreateTime.apiName, endTime, Operator.LTE);
        query.setFilters(filters);
        List<AggFunctionArg> aggFunctionArgs = Lists.newArrayList();
        aggFunctionArgs.add(new AggFunctionArg("count", ReconAbnormalDataConst.Field.BizObjectDataId.apiName, true));

        List<IObjectData> dataList = serviceFacade.aggregateFindBySearchQuery(user, query, ReconAbnormalDataConst.API_NAME, "", aggFunctionArgs);
        if (CollectionUtils.isEmpty(dataList)) {
            return 0;
        }
        return dataList.get(0).get("count_" + ReconAbnormalDataConst.Field.BizObjectDataId.apiName, Integer.class, 0);
    }

    public Map<String, ReconLedgerConst.SumGroupByObject> sumLedgerByBizObject(User user, String bizModule, String bizObjectApiName, long startTime, long endTime) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        BizReconciliationUtil.addFilter(filters, ReconLedgerConst.Field.BizModule.apiName, bizModule, Operator.EQ);
        BizReconciliationUtil.addFilter(filters, ReconLedgerConst.Field.BizObjectApiName.apiName, bizObjectApiName, Operator.EQ);
        BizReconciliationUtil.addFilter(filters, ReconLedgerConst.Field.BizStatus.apiName, ReconLedgerConst.Status.PENDING.value, Operator.EQ);
        BizReconciliationUtil.addFilter(filters, ReconLedgerConst.Field.OccurTime.apiName, startTime, Operator.GTE);
        BizReconciliationUtil.addFilter(filters, ReconLedgerConst.Field.OccurTime.apiName, endTime, Operator.LTE);
        query.setFilters(filters);
        List<AggFunctionArg> aggFunctionArgs = Lists.newArrayList();
        aggFunctionArgs.add(new AggFunctionArg("sum", ReconLedgerConst.Field.AmountChange.apiName, false));
        aggFunctionArgs.add(new AggFunctionArg("sum", ReconLedgerConst.Field.AvailableAmountChange.apiName, false));
        aggFunctionArgs.add(new AggFunctionArg("sum", ReconLedgerConst.Field.OccupiedAmountChange.apiName, false));
        /**
         * sum_{field}
         * groupByCount
         * select groupField,sum(field1) as sum_field1,sum(field2) as sum_field2,count(*) as groupByCount from xxx where xxx group by groupField
         */
        List<IObjectData> dataList = serviceFacade.aggregateFindBySearchQuery(user, query, ReconLedgerConst.API_NAME, ReconLedgerConst.Field.BizObjectDataId.apiName, aggFunctionArgs);

        Map<String, ReconLedgerConst.SumGroupByObject> bizDataSumMap = Maps.newHashMap();
        CollectionUtils.emptyIfNull(dataList).forEach(x -> {
            String bizObjectDataId = x.get(ReconLedgerConst.Field.BizObjectDataId.apiName, String.class);

            ReconLedgerConst.SumGroupByObject sumGroupByObject = new ReconLedgerConst.SumGroupByObject();
            sumGroupByObject.setBizObjectDataId(bizObjectDataId);
            sumGroupByObject.setTotalAmountChange(x.get("sum_" + ReconLedgerConst.Field.AmountChange.apiName, BigDecimal.class, BigDecimal.ZERO));
            sumGroupByObject.setTotalAvailableAmountChange(x.get("sum_" + ReconLedgerConst.Field.AvailableAmountChange.apiName, BigDecimal.class, BigDecimal.ZERO));
            sumGroupByObject.setTotalOccupiedAmountChange(x.get("sum_" + ReconLedgerConst.Field.OccupiedAmountChange.apiName, BigDecimal.class, BigDecimal.ZERO));
            sumGroupByObject.setGroupByCount(x.get("groupByCount", Long.class, 0L));
            bizDataSumMap.put(bizObjectDataId, sumGroupByObject);
        });
        return bizDataSumMap;
    }

    public ReconFlowConst.SumAmountSummary sumFlowByBizObject(User user, String bizModule, String bizObjectApiName, String bizObjectDataId, long startTime, long endTime) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        BizReconciliationUtil.addFilter(filters, ReconFlowConst.Field.BizModule.apiName, bizModule, Operator.EQ);
        BizReconciliationUtil.addFilter(filters, ReconFlowConst.Field.BizObjectApiName.apiName, bizObjectApiName, Operator.EQ);
        BizReconciliationUtil.addFilter(filters, ReconFlowConst.Field.BizObjectDataId.apiName, bizObjectDataId, Operator.EQ);
        BizReconciliationUtil.addFilter(filters, ReconFlowConst.Field.BizStatus.apiName, ReconFlowConst.Status.UN_PROCESSED.value, Operator.EQ);
        BizReconciliationUtil.addFilter(filters, ReconFlowConst.Field.OccurTime.apiName, startTime, Operator.GTE);
        BizReconciliationUtil.addFilter(filters, ReconFlowConst.Field.OccurTime.apiName, endTime, Operator.LTE);
        BizReconciliationUtil.addFilter(filters, ReconFlowConst.Field.BizType.apiName, Arrays.stream(ReconFlowConst.Type.values()).map(x -> x.value).collect(Collectors.toList()), Operator.IN);
        query.setFilters(filters);
        List<AggFunctionArg> aggFunctionArgs = Lists.newArrayList();
        aggFunctionArgs.add(new AggFunctionArg("sum", ReconFlowConst.Field.Amount.apiName, false));

        List<IObjectData> dataList = serviceFacade.aggregateFindBySearchQuery(user, query, ReconFlowConst.API_NAME, ReconFlowConst.Field.BizType.apiName, aggFunctionArgs);

        ReconFlowConst.SumAmountSummary sumAmountSummary = new ReconFlowConst.SumAmountSummary();
        CollectionUtils.emptyIfNull(dataList).forEach(x -> {
            String type = x.get(ReconFlowConst.Field.BizType.apiName, String.class);
            BigDecimal totalAmount = x.get("sum_" + ReconFlowConst.Field.Amount.apiName, BigDecimal.class, BigDecimal.ZERO);
            if (ReconFlowConst.Type.FLOW.value.equals(type)) {
                sumAmountSummary.setFlowTotalAmount(totalAmount);
            } else if (ReconFlowConst.Type.FREEZE.value.equals(type)) {
                sumAmountSummary.setFreezeTotalAmount(totalAmount);
            } else if (ReconFlowConst.Type.UNFREEZE.value.equals(type)) {
                sumAmountSummary.setUnfreezeTotalAmount(totalAmount);
            }
        });
        return sumAmountSummary;
    }

    public void registerLedgerTask(User user, String bizModule, String cronExp, String bizObjectApiName) {
        ReconTaskCallModel callArg = new ReconTaskCallModel();
        callArg.setTenantId(user.getTenantId());
        callArg.setUserId(StringUtils.firstNonEmpty(user.getUpstreamOwnerIdOrUserId(), User.SUPPER_ADMIN_USER_ID));
        callArg.setBizModule(bizModule);
        callArg.setType(ReconTaskTypeEnum.LedgerTask.value);
        callArg.setObjectApiName(bizObjectApiName);

        NomonMessage nomonMessage = new NomonMessage();
        nomonMessage.setBiz(bizModule);
        nomonMessage.setTenantId(user.getTenantId());
        nomonMessage.setCronExp(cronExp);
        nomonMessage.setDataId(user.getTenantId() + "#" + bizModule);
        nomonMessage.setTaskId(user.getTenantId() + "#" + bizModule + "#LedgerTask");
        nomonMessage.setCallArg(callArg.toJson());

        nomonProducer.send(nomonMessage);
    }

    public void registerPendingDataTask(User user, String bizModule, String cronExp) {
        ReconTaskCallModel callArg = new ReconTaskCallModel();
        callArg.setTenantId(user.getTenantId());
        callArg.setUserId(StringUtils.firstNonEmpty(user.getUpstreamOwnerIdOrUserId(), User.SUPPER_ADMIN_USER_ID));
        callArg.setBizModule(bizModule);
        callArg.setType(ReconTaskTypeEnum.PendingDataTask.value);

        NomonMessage nomonMessage = new NomonMessage();
        nomonMessage.setBiz(bizModule);
        nomonMessage.setTenantId(user.getTenantId());
        nomonMessage.setCronExp(cronExp);
        nomonMessage.setDataId(user.getTenantId() + "#" + bizModule);
        nomonMessage.setTaskId(user.getTenantId() + "#" + bizModule + "#PendingDataTask");
        nomonMessage.setCallArg(callArg.toJson());

        nomonProducer.send(nomonMessage);
    }

    public void sendTextMessage(String tenantId, String appId, Map<String, String> keyValueList, List<Integer> receiverIds) {
        if (CollectionUtils.isEmpty(receiverIds) || StringUtils.isEmpty(appId) || MapUtils.isEmpty(keyValueList)) {
            return;
        }
        try {
            StringBuilder sb = new StringBuilder();
            keyValueList.forEach((k, v) -> {
                sb.append(k).append(" : ").append(v).append("\n");
            });
            SendTextMessageArg arg = new SendTextMessageArg();
            arg.setUuid(UUID.randomUUID().toString());
            arg.setEi(Integer.parseInt(tenantId));
            arg.setReceiverChannelType(ReceiverChannelType.OPEN_APP);
            arg.setReceiverChannelData(ReceiverChannelUtils.buildNoticeChannelData(appId));
            arg.setMessageContent(sb.toString());
            arg.setReceiverIds(Lists.newArrayList(1000));
            MessageResponse result = messageServiceV2.sendTextMessage(arg);
            if (Objects.isNull(result) || result.getCode() != 0) {
                log.warn("sendTextMessage fail,tenantId:{},appId:{},receiverIds:{},content:{},reuslt:{}", tenantId, appId, receiverIds, keyValueList, result);
            }
        } catch (Exception e) {
            log.warn("sendTextMessage error,tenantId:{},appId:{},receiverIds:{},contentMap:{}", tenantId, appId, receiverIds, keyValueList, e);
        }

    }
}
