package com.facishare.crm.bizreconciliation.task;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.facishare.crm.bizreconciliation.config.BizReconciliationConfig;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import com.facishare.crm.bizreconciliation.checker.ReconciliationCheckerFactory;
import com.facishare.crm.bizreconciliation.consts.ReconAbnormalDataConst;
import com.facishare.crm.bizreconciliation.consts.ReconPendingDataConst;
import com.facishare.crm.bizreconciliation.enums.ReconTaskTypeEnum;
import com.facishare.crm.bizreconciliation.manager.BizReconciliationManager;
import com.facishare.crm.bizreconciliation.model.ReconTaskCallModel;
import com.facishare.crm.bizreconciliation.util.BizReconciliationUtil;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.google.common.collect.Lists;

/**
 * @IgnoreI18nFile
 */
@Slf4j
@Component
public class ReconPendingDataTaskProcessor extends ReconTaskProcessor {
    protected ReconciliationCheckerFactory reconciliationCheckerFactory;

    public ReconPendingDataTaskProcessor(BizReconciliationManager bizReconciliationManager, ReconciliationCheckerFactory reconciliationCheckerFactory) {
        super(bizReconciliationManager);
        this.reconciliationCheckerFactory = reconciliationCheckerFactory;
    }

    @Override
    public String getType() {
        return ReconTaskTypeEnum.PendingDataTask.value;
    }

    @Override
    public boolean execute(ReconTaskCallModel callArg) {
        User user = User.systemUser(callArg.getTenantId());
        String bizModule = callArg.getBizModule();

        long now = System.currentTimeMillis();
        log.info("startPendingDataTask,tenantId:{},bizModule:{},now:{}", user.getTenantId(), bizModule, now);

        String msg = String.format("startPendingDataTask now:%s,endTime : %s", now, BizReconciliationUtil.dateFormat(now));
        BizReconciliationUtil.sendAuditLog(user, bizModule, "StartPendingDataTask", null, null, msg);

        do {
            BizReconciliationConfig.acquire();
            List<IFilter> filterList = Lists.newArrayList();
            BizReconciliationUtil.addFilter(filterList, ReconPendingDataConst.Field.BizModule.apiName, bizModule, Operator.EQ);
            BizReconciliationUtil.addFilter(filterList, ReconPendingDataConst.Field.OccurTime.apiName, now, Operator.LTE);

            List<OrderBy> orderByList = Lists.newArrayList();
            orderByList.add(new OrderBy(ReconPendingDataConst.Field.BizObjectApiName.apiName, true));
            orderByList.add(new OrderBy(ReconPendingDataConst.Field.BizObjectDataId.apiName, true));

            List<IObjectData> pendingDataList = bizReconciliationManager.query(user, ReconPendingDataConst.API_NAME, filterList, orderByList, 0);
            if (CollectionUtils.isEmpty(pendingDataList)) {
                break;
            }
            Map<String, List<IObjectData>> bizObjectPendingDataListMap = pendingDataList.stream().collect(Collectors.groupingBy(x -> x.get(ReconPendingDataConst.Field.BizObjectApiName.apiName, String.class, "")));
            bizObjectPendingDataListMap.forEach((bizObjectApiName, dataList) -> {
                List<ReconAbnormalDataConst.Model> abnormalDataList = reconciliationCheckerFactory.check(user, bizModule, bizObjectApiName, dataList);
                createAbnormalData(user, bizModule, ReconAbnormalDataConst.Type.DataAbnormal, abnormalDataList);
            });
            bizReconciliationManager.deleteInternalObjectData(user, pendingDataList);
        } while (true);

        long abnormalScanStartTime = BizReconciliationUtil.getBefore24HourTime();
        long abnormalScanEndTime = System.currentTimeMillis();
        int count = bizReconciliationManager.countAbnormalUnProcessByObject(user, bizModule, null, ReconAbnormalDataConst.Type.DataAbnormal.value, abnormalScanStartTime, abnormalScanEndTime);
        if (count > 0) {
            Map<String, String> contentMap = Maps.newHashMap();
            contentMap.put("开始时间", BizReconciliationUtil.dateFormat(abnormalScanStartTime));
            contentMap.put("结束时间", BizReconciliationUtil.dateFormat(abnormalScanEndTime));
            contentMap.put("数据异常数", String.valueOf(count));
            sendAbnormalTextMessage(user, bizModule, contentMap);
        }
        log.info("endPendingDataTask,tenantId:{},bizModule:{},now:{}", user.getTenantId(), bizModule, now);
        BizReconciliationUtil.sendAuditLog(user, bizModule, "EndPendingDataTask", null, null, msg);
        return true;
    }

}
