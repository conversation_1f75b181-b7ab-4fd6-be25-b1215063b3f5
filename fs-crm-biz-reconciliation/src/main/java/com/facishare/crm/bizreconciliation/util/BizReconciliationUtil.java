package com.facishare.crm.bizreconciliation.util;


import java.text.SimpleDateFormat;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import com.fxiaoke.log.AuditLog;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.dto.AuditLogDTO;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.fxiaoke.release.FsGrayRelease;
import com.github.autoconf.helper.ConfigHelper;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.google.common.collect.Lists;

@Slf4j
public class BizReconciliationUtil {


    public static boolean allow(String tenantId) {
        return FsGrayRelease.isAllow("customeraccount", "enableBizReconciliationEis", tenantId);
    }

    public static void initSystemField(User user, IObjectData objectData) {
        long now = System.currentTimeMillis();
        String createBy = StringUtils.firstNonEmpty(user.getUpstreamOwnerIdOrUserId(), User.SUPPER_ADMIN_USER_ID);
        objectData.setTenantId(user.getTenantId());
        objectData.setCreateTime(now);
        objectData.setLastModifiedTime(now);
        objectData.setCreatedBy(createBy);
        objectData.setLastModifiedBy(createBy);
    }

    public static long getYesterdayBeginTime() {
        DateTime now = DateTime.now();
        DateTime yest = now.minusDays(1);
        return yest.dayOfWeek().roundFloorCopy().getMillis();
    }

    public static long getNowBeginTime() {
        DateTime now = DateTime.now();
        return now.dayOfWeek().roundFloorCopy().getMillis();
    }

    public static long getBefore24HourTime() {
        DateTime now = DateTime.now();
        return now.minusDays(1).dayOfWeek().roundFloorCopy().getMillis();
    }

    public static String dateFormat(long time) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return dateFormat.format(new Date(time));
    }

    public static void addFilter(List<IFilter> filterList, String fieldName, Object value, Operator operator) {
        IFilter filter = new Filter();
        filter.setOperator(operator);
        filter.setFieldName(fieldName);
        if (value instanceof Collection) {
            Collection<Object> collection = (Collection<Object>) value;
            List<String> values = collection.stream().map(Object::toString).collect(Collectors.toList());
            filter.setFieldValues(values);
        } else if (value instanceof String) {
            filter.setFieldValues(Lists.newArrayList((String) value));
        } else {
            filter.setFieldValues(Lists.newArrayList(value.toString()));
        }
        filterList.add(filter);
    }

    private static final String APP_NAME = ConfigHelper.getProcessInfo().getName();
    private static final String SERVER_IP = ConfigHelper.getProcessInfo().getIp();
    private static final String PROFILE = ConfigHelper.getProcessInfo().getProfile();

    public static void sendAuditLog(User user, String bizModule, String action, String objectApiName, String objectDataIds, String message) {
        try {
            String userId = user.isOutUser() ? user.getUserId() : user.getOutUserId();
            AuditLogDTO dto = AuditLogDTO.builder().appName(APP_NAME).serverIp(SERVER_IP).profile(PROFILE).module(bizModule).action(action)
                    .traceId(TraceContext.get().getTraceId()).createTime(System.currentTimeMillis())
                    .tenantId(user.getTenantId()).userId(userId).objectApiNames(objectApiName).objectIds(objectDataIds).message(message).build();
            BizLogClient.send("biz-audit-log-dht", Pojo2Protobuf.toMessage(dto, AuditLog.class).toByteArray());
        } catch (Exception e) {
            log.warn("sendAuditLog error,user:{},bizModule:{},objectApiName:{},dataId:{},msg:{}", user, bizModule, objectApiName, objectDataIds, message, e);
        }
    }
}
