package com.facishare.crm.bizreconciliation.task;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.bizreconciliation.config.BizReconciliationConfig;
import com.facishare.crm.bizreconciliation.consts.ReconAbnormalDataConst;
import com.facishare.crm.bizreconciliation.consts.ReconFlowConst;
import com.facishare.crm.bizreconciliation.model.BizReconCompareResult;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import com.facishare.crm.bizreconciliation.consts.ReconLedgerConst;
import com.facishare.crm.bizreconciliation.enums.ReconTaskTypeEnum;
import com.facishare.crm.bizreconciliation.manager.BizReconciliationManager;
import com.facishare.crm.bizreconciliation.model.ReconTaskCallModel;
import com.facishare.crm.bizreconciliation.util.BizReconciliationUtil;
import com.facishare.paas.appframework.core.model.User;

/**
 * @IgnoreI18nFile
 */
@Slf4j
@Component
public class ReconLedgerTaskProcessor extends ReconTaskProcessor {
    private final RedissonClient redissonClient;

    public ReconLedgerTaskProcessor(BizReconciliationManager bizReconciliationManager, RedissonClient redissonClient) {
        super(bizReconciliationManager);
        this.redissonClient = redissonClient;
    }

    @Override
    public String getType() {
        return ReconTaskTypeEnum.LedgerTask.value;
    }

    public boolean execute(ReconTaskCallModel callArg) {
        String tenantId = callArg.getTenantId();
        String bizModule = callArg.getBizModule();
        String bizObjectApiName = callArg.getObjectApiName();

        User user = User.systemUser(tenantId);
        long yesterdayBeginTime = BizReconciliationUtil.getYesterdayBeginTime();
        long todayBeginTime = BizReconciliationUtil.getNowBeginTime();
        log.info("startLedgerTask,tenantId:{},bizModule:{},bizObjectApiName:{},startTime:{},endTime:{}", tenantId, bizModule, bizObjectApiName, yesterdayBeginTime, todayBeginTime);

        String msg = String.format("startLedgerTask startTime - endTime : %s - %s", BizReconciliationUtil.dateFormat(yesterdayBeginTime), BizReconciliationUtil.dateFormat(todayBeginTime));
        BizReconciliationUtil.sendAuditLog(user, bizModule, "StartLedgerTask", bizObjectApiName, null, msg);
        doLedgerTask(user, bizModule, bizObjectApiName, yesterdayBeginTime, todayBeginTime);
        BizReconciliationUtil.sendAuditLog(user, bizModule, "EndLedgerTask", bizObjectApiName, null, msg);
        log.info("endLedgerTask,tenantId:{},bizModule:{},bizObjectApiName:{},startTime:{},endTime:{}", tenantId, bizModule, bizObjectApiName, yesterdayBeginTime, todayBeginTime);
        return true;
    }

    public void doLedgerTask(User user, String bizModule, String bizObjectApiName, long startTime, long endTime) {
        do {
            // check
            Map<String, ReconLedgerConst.SumGroupByObject> sumByGroupMap = bizReconciliationManager.sumLedgerByBizObject(user, bizModule, bizObjectApiName, startTime, endTime);
            if (sumByGroupMap.isEmpty()) {
                break;
            }
            sumByGroupMap.forEach((bizObjectDataId, sumInfo) -> {
                BizReconciliationConfig.acquire();
                //判断是否已处理
                String lockKey = String.format("CAReconLedgerLock_%s_%s_%s_%s", user.getTenantId(), bizModule, bizObjectApiName, bizObjectDataId);
                String resultKey = String.format("CAReconLedgerData:%s:%s:%s:%s", user.getTenantId(), bizModule, bizObjectApiName, bizObjectDataId);
                RLock lock = tryLock(lockKey);
                try {
                    if (Objects.nonNull(lock)) {
                        BizReconCompareResult result = new BizReconCompareResult();
                        Optional<ExecuteResult> executeResultOptional = getBucketData(resultKey);
                        if (executeResultOptional.isPresent()) {
                            ExecuteResult executeResult = executeResultOptional.get();
                            result.setError(executeResult.isError());
                            result.appendMessage(executeResult.getMessage());
                        } else {
                            ReconFlowConst.SumAmountSummary sumAmountSummary = bizReconciliationManager.sumFlowByBizObject(user, bizModule, bizObjectApiName, bizObjectDataId, startTime, endTime);
                            result = compare(user, sumInfo, sumAmountSummary);
                            setBucket(resultKey, result.message());
                        }
                        //redis保存结果
                        if (result.isError()) {
                            log.warn("ledger error,tenantId:{},objectApiName:{},dataId:{},bizModule:{},ledgerSum:{},result:{}", user.getTenantId(), bizObjectApiName, bizObjectDataId, bizModule, sumInfo, result);
                            ReconAbnormalDataConst.Model abnormal = new ReconAbnormalDataConst.Model();
                            abnormal.setBizModule(bizModule);
                            abnormal.setBizObjectApiName(bizObjectApiName);
                            abnormal.setBizObjectDataId(bizObjectDataId);
                            abnormal.setBizStatus(ReconAbnormalDataConst.Status.UN_PROCESSED.value);
                            abnormal.setBizType(ReconAbnormalDataConst.Type.LedgerAbnormal.value);
                            abnormal.setRemark(result.message());
                            createAbnormalData(user, bizModule, ReconAbnormalDataConst.Type.LedgerAbnormal, Lists.newArrayList(abnormal));
                        }
                    }
                } finally {
                    if (Objects.nonNull(lock)) {
                        lock.unlock();
                    }
                }
                updateLedgerStatus(user, bizModule, bizObjectApiName, bizObjectDataId, startTime, endTime);
                updateFlowStatus(user, bizModule, bizObjectApiName, bizObjectDataId, startTime, endTime);
                deleteBucket(resultKey);
            });
        } while (true);
        long abnormalScanStartTime = BizReconciliationUtil.getBefore24HourTime();
        long abnormalScanEndTime = System.currentTimeMillis();
        int ledgerAbnormalCount = bizReconciliationManager.countAbnormalUnProcessByObject(user, bizModule, bizObjectApiName, ReconAbnormalDataConst.Type.LedgerAbnormal.value, abnormalScanStartTime, abnormalScanEndTime);

        Map<String, String> contentMap = Maps.newLinkedHashMap();
        contentMap.put("开始时间", BizReconciliationUtil.dateFormat(startTime));
        contentMap.put("结束时间", BizReconciliationUtil.dateFormat(endTime));
        contentMap.put("总账异常数", String.valueOf(ledgerAbnormalCount));
        sendAbnormalTextMessage(user, bizModule, contentMap);
    }

    private RLock tryLock(String key) {
        RLock lock = redissonClient.getLock(key);
        try {
            boolean lockResult = lock.tryLock(3L, 30L, TimeUnit.SECONDS);
            if (lockResult) {
                return lock;
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        return null;
    }

    private Optional<ExecuteResult> getBucketData(String key) {
        RBucket<ExecuteResult> bucket = redissonClient.getBucket(key);
        ExecuteResult result = bucket.get();
        return Optional.ofNullable(result);
    }

    private void setBucket(String key, String message) {
        RBucket<ExecuteResult> bucket = redissonClient.getBucket(key);
        ExecuteResult result = new ExecuteResult();
        result.setError(StringUtils.isNotEmpty(message));
        result.setMessage(message);
        bucket.set(result, 1, TimeUnit.HOURS);
    }

    private void deleteBucket(String key) {
        RBucket<ExecuteResult> bucket = redissonClient.getBucket(key);
        if (bucket.isExists()) {
            bucket.delete();
        }
    }

    private void updateLedgerStatus(User user, String bizModule, String bizObjectApiName, String bizObjectDataId, long startTime, long endTime) {
        int updateCount = 0;
        int queryCount = 0;
        try {
            do {
                BizReconciliationConfig.acquire();
                List<IFilter> filters = Lists.newArrayList();
                BizReconciliationUtil.addFilter(filters, ReconLedgerConst.Field.BizModule.apiName, bizModule, Operator.EQ);
                BizReconciliationUtil.addFilter(filters, ReconLedgerConst.Field.BizObjectApiName.apiName, bizObjectApiName, Operator.EQ);
                BizReconciliationUtil.addFilter(filters, ReconLedgerConst.Field.BizObjectDataId.apiName, bizObjectDataId, Operator.EQ);
                BizReconciliationUtil.addFilter(filters, ReconLedgerConst.Field.BizStatus.apiName, ReconLedgerConst.Status.PENDING.value, Operator.EQ);
                BizReconciliationUtil.addFilter(filters, ReconLedgerConst.Field.OccurTime.apiName, startTime, Operator.GTE);
                BizReconciliationUtil.addFilter(filters, ReconLedgerConst.Field.OccurTime.apiName, endTime, Operator.LTE);
                List<IObjectData> dataList = bizReconciliationManager.query(user, ReconLedgerConst.API_NAME, filters, null, 0);
                queryCount += dataList.size();
                bizReconciliationManager.batchUpdateField(user, dataList, ReconLedgerConst.Field.BizStatus.apiName, ReconLedgerConst.Status.PROCESSED.value);
                updateCount += dataList.size();
                if (CollectionUtils.isEmpty(dataList)) {
                    break;
                }
            } while (true);
        } finally {
            log.info("updateLedgerStatus,tenantId:{},bizModule:{},objectApiName:{},objectDataId:{},queryCount:{},updateCount:{}", user.getTenantId(), bizModule, bizObjectApiName, bizObjectDataId, queryCount, updateCount);
        }
    }

    private void updateFlowStatus(User user, String bizModule, String bizObjectApiName, String bizObjectDataId, long startTime, long endTime) {
        int updateCount = 0;
        int queryCount = 0;
        try {
            do {
                BizReconciliationConfig.acquire();
                List<IFilter> filters = Lists.newArrayList();
                BizReconciliationUtil.addFilter(filters, ReconFlowConst.Field.BizModule.apiName, bizModule, Operator.EQ);
                BizReconciliationUtil.addFilter(filters, ReconFlowConst.Field.BizObjectApiName.apiName, bizObjectApiName, Operator.EQ);
                BizReconciliationUtil.addFilter(filters, ReconFlowConst.Field.BizObjectDataId.apiName, bizObjectDataId, Operator.EQ);
                BizReconciliationUtil.addFilter(filters, ReconFlowConst.Field.BizStatus.apiName, ReconLedgerConst.Status.PENDING.value, Operator.EQ);
                BizReconciliationUtil.addFilter(filters, ReconFlowConst.Field.OccurTime.apiName, startTime, Operator.GTE);
                BizReconciliationUtil.addFilter(filters, ReconFlowConst.Field.OccurTime.apiName, endTime, Operator.LTE);
                List<IObjectData> dataList = bizReconciliationManager.query(user, ReconFlowConst.API_NAME, filters, null, 0);
                queryCount += dataList.size();
                bizReconciliationManager.batchUpdateField(user, dataList, ReconFlowConst.Field.BizStatus.apiName, ReconFlowConst.Status.PROCESSED.value);
                updateCount += dataList.size();
                if (CollectionUtils.isEmpty(dataList)) {
                    break;
                }
            } while (true);
        } finally {
            log.info("updateFlowStatus,tenantId:{},bizModule:{},objectApiName:{},objectDataId:{},queryCount:{},updateCount:{}", user.getTenantId(), bizModule, bizObjectApiName, bizObjectDataId, queryCount, updateCount);
        }
    }

    /**
     * 客户账户余额 = 收入 - 支出 - 解冻
     * 客户账户可用 = 收入 - 支出 - 冻结
     * 客户账户冻结 = 冻结 - 解冻
     */
    private BizReconCompareResult compare(User user, ReconLedgerConst.SumGroupByObject sumInfo, ReconFlowConst.SumAmountSummary sumAmountSummary) {
        BigDecimal totalAmountChange = sumInfo.getTotalAmountChange();
        BigDecimal totalOccupiedAmountChange = sumInfo.getTotalOccupiedAmountChange();
        BigDecimal totalAvailableAmountChange = sumInfo.getTotalAvailableAmountChange();
        BizReconCompareResult result = new BizReconCompareResult();

        if (totalAmountChange.compareTo(sumAmountSummary.getFlowTotalAmount().subtract(sumAmountSummary.getUnfreezeTotalAmount())) != 0) {
            result.appendMessage("账户余额字段异常");
        }

        if (totalAvailableAmountChange.compareTo(sumAmountSummary.getFlowTotalAmount().subtract(sumAmountSummary.getFreezeTotalAmount())) != 0) {
            result.appendMessage("可用金额字段异常");
        }

        if (totalOccupiedAmountChange.compareTo(sumAmountSummary.getFreezeTotalAmount().subtract(sumAmountSummary.getUnfreezeTotalAmount())) != 0) {
            result.appendMessage("占用金额字段异常");
        }
        if (result.isError()) {
            log.warn("compare error,user:{},sumInfo:{},sumAmountSummary:{}", user, sumInfo, sumAmountSummary);
        }
        return result;
    }

    @Data
    private static class ExecuteResult implements Serializable {
        private static final long serialVersionUID = 1L;
        private boolean error;
        private String message;
    }

}
