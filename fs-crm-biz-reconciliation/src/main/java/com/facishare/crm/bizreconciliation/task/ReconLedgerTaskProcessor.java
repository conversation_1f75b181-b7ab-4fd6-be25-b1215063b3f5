package com.facishare.crm.bizreconciliation.task;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.bizreconciliation.consts.ReconAbnormalDataConst;
import com.facishare.crm.bizreconciliation.consts.ReconFlowConst;
import com.facishare.crm.bizreconciliation.model.BizReconCompareResult;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import com.facishare.crm.bizreconciliation.consts.ReconLedgerConst;
import com.facishare.crm.bizreconciliation.enums.ReconTaskTypeEnum;
import com.facishare.crm.bizreconciliation.manager.BizReconciliationManager;
import com.facishare.crm.bizreconciliation.model.ReconTaskCallModel;
import com.facishare.crm.bizreconciliation.util.BizReconciliationUtil;
import com.facishare.paas.appframework.core.model.User;

/**
 * @IgnoreI18nFile
 */
@Slf4j
@Component
public class ReconLedgerTaskProcessor extends ReconTaskProcessor {
    private  RedissonClient redissonClient;

    public ReconLedgerTaskProcessor(BizReconciliationManager bizReconciliationManager, RedissonClient redissonClient) {
        super(bizReconciliationManager);
    }

    @Override
    public String getType() {
        return ReconTaskTypeEnum.LedgerTask.value;
    }

    public boolean execute(ReconTaskCallModel callArg) {
        String tenantId = callArg.getTenantId();
        String bizModule = callArg.getBizModule();
        String bizObjectApiName = callArg.getObjectApiName();

        User user = User.systemUser(tenantId);
        long yesterdayBeginTime = BizReconciliationUtil.getYesterdayBeginTime();
        long todayBeginTime = BizReconciliationUtil.getNowBeginTime();
        log.info("startLedgerTask,tenantId:{},bizModule:{},bizObjectApiName:{},startTime:{},endTime:{}", tenantId, bizModule, bizObjectApiName, yesterdayBeginTime, todayBeginTime);

        String msg = String.format("startLedgerTask startTime - endTime : %s - %s", BizReconciliationUtil.dateFormat(yesterdayBeginTime), BizReconciliationUtil.dateFormat(todayBeginTime));
        BizReconciliationUtil.sendAuditLog(user, bizModule, "StartLedgerTask", bizObjectApiName, null, msg);

        do {
            // check
            Map<String, ReconLedgerConst.SumGroupByObject> sumByGroupMap = bizReconciliationManager.sumLedgerByBizObject(user, bizModule, bizObjectApiName, yesterdayBeginTime, todayBeginTime);
            if (sumByGroupMap.isEmpty()) {
                break;
            }
            sumByGroupMap.forEach((bizObjectDataId, sumInfo) -> {
                //判断是否已处理
                ReconFlowConst.SumAmountSummary sumAmountSummary = bizReconciliationManager.sumFlowByBizObject(user, bizModule, bizObjectApiName, bizObjectDataId, yesterdayBeginTime, todayBeginTime);
                BizReconCompareResult result = compare(sumInfo, sumAmountSummary);
                //redis保存结果
                if (result.isError()) {
                    log.warn("ledger error,tenantId:{},objectApiName:{},dataId:{},bizModule:{},ledgerSum:{},flowSum:{}", tenantId, bizObjectApiName, bizObjectDataId, bizModule, sumInfo, sumAmountSummary);
                    ReconAbnormalDataConst.Model abnormal = new ReconAbnormalDataConst.Model();
                    abnormal.setBizModule(bizModule);
                    abnormal.setBizObjectApiName(bizObjectApiName);
                    abnormal.setBizObjectDataId(bizObjectDataId);
                    abnormal.setBizStatus(ReconAbnormalDataConst.Status.UN_PROCESSED.value);
                    abnormal.setBizType(ReconAbnormalDataConst.Type.LedgerAbnormal.value);
                    abnormal.setRemark(result.message());
                    createAbnormalData(user, bizModule, ReconAbnormalDataConst.Type.LedgerAbnormal, Lists.newArrayList(abnormal));
                }
                updateLedgerStatus(user, bizModule, bizObjectApiName, bizObjectDataId, yesterdayBeginTime, todayBeginTime);
                updateFlowStatus(user, bizModule, bizObjectApiName, bizObjectDataId, yesterdayBeginTime, todayBeginTime);
            });
        } while (true);
        int ledgerAbnormalCount = bizReconciliationManager.countAbnormalUnProcessByObject(user, bizModule, bizObjectApiName, ReconAbnormalDataConst.Type.LedgerAbnormal.value);

        Map<String, String> contentMap = Maps.newHashMap();
        contentMap.put("开始时间", BizReconciliationUtil.dateFormat(yesterdayBeginTime));  //@IgnoreI18n
        contentMap.put("结束时间", BizReconciliationUtil.dateFormat(todayBeginTime));  //@IgnoreI18n
        contentMap.put("总账异常数", String.valueOf(ledgerAbnormalCount)); //@IgnoreI18n
        sendAbnormalTextMessage(user, bizModule, contentMap);
        BizReconciliationUtil.sendAuditLog(user, bizModule, "EndLedgerTask", bizObjectApiName, null, msg);
        return true;
    }

    private void updateLedgerStatus(User user, String bizModule, String bizObjectApiName, String bizObjectDataId, long startTime, long endTime) {
        int updateCount = 0;
        int queryCount = 0;
        try {
            do {
                List<IFilter> filters = Lists.newArrayList();
                BizReconciliationUtil.addFilter(filters, ReconLedgerConst.Field.BizModule.apiName, bizModule, Operator.EQ);
                BizReconciliationUtil.addFilter(filters, ReconLedgerConst.Field.BizObjectApiName.apiName, bizObjectApiName, Operator.EQ);
                BizReconciliationUtil.addFilter(filters, ReconLedgerConst.Field.BizObjectDataId.apiName, bizObjectDataId, Operator.EQ);
                BizReconciliationUtil.addFilter(filters, ReconLedgerConst.Field.BizStatus.apiName, ReconLedgerConst.Status.PENDING.value, Operator.EQ);
                BizReconciliationUtil.addFilter(filters, ReconLedgerConst.Field.OccurTime.apiName, startTime, Operator.GTE);
                BizReconciliationUtil.addFilter(filters, ReconLedgerConst.Field.OccurTime.apiName, endTime, Operator.LTE);
                List<IObjectData> dataList = bizReconciliationManager.query(user, ReconLedgerConst.API_NAME, filters, null, 0);
                queryCount += dataList.size();
                bizReconciliationManager.batchUpdateField(user, dataList, ReconLedgerConst.Field.BizStatus.apiName, ReconLedgerConst.Status.PROCESSED.value);
                updateCount += dataList.size();
                if (CollectionUtils.size(dataList) < 100) {
                    break;
                }
            } while (true);
        } finally {
            log.info("updateLedgerStatus,tenantId:{},bizModule:{},objectApiName:{},objectDataId:{},queryCount:{},updateCount:{}", user.getTenantId(), bizModule, bizObjectApiName, bizObjectDataId, queryCount, updateCount);
        }
    }

    private void updateFlowStatus(User user, String bizModule, String bizObjectApiName, String bizObjectDataId, long startTime, long endTime) {
        int updateCount = 0;
        int queryCount = 0;
        try {
            do {
                List<IFilter> filters = Lists.newArrayList();
                BizReconciliationUtil.addFilter(filters, ReconFlowConst.Field.BizModule.apiName, bizModule, Operator.EQ);
                BizReconciliationUtil.addFilter(filters, ReconFlowConst.Field.BizObjectApiName.apiName, bizObjectApiName, Operator.EQ);
                BizReconciliationUtil.addFilter(filters, ReconFlowConst.Field.BizObjectDataId.apiName, bizObjectDataId, Operator.EQ);
                BizReconciliationUtil.addFilter(filters, ReconFlowConst.Field.BizStatus.apiName, ReconLedgerConst.Status.PENDING.value, Operator.EQ);
                BizReconciliationUtil.addFilter(filters, ReconFlowConst.Field.OccurTime.apiName, startTime, Operator.GTE);
                BizReconciliationUtil.addFilter(filters, ReconFlowConst.Field.OccurTime.apiName, endTime, Operator.LTE);
                List<IObjectData> dataList = bizReconciliationManager.query(user, ReconFlowConst.API_NAME, filters, null, 0);
                queryCount += dataList.size();
                bizReconciliationManager.batchUpdateField(user, dataList, ReconFlowConst.Field.BizStatus.apiName, ReconFlowConst.Status.PROCESSED.value);
                updateCount += dataList.size();
                if (CollectionUtils.isEmpty(dataList) || CollectionUtils.size(dataList) < 100) {
                    break;
                }
            } while (true);
        } finally {
            log.info("updateFlowStatus,tenantId:{},bizModule:{},objectApiName:{},objectDataId:{},queryCount:{},updateCount:{}", user.getTenantId(), bizModule, bizObjectApiName, bizObjectDataId, queryCount, updateCount);
        }
    }

    /**
     * 客户账户余额 = 收入 - 支出 - 解冻
     * 客户账户可用 = 收入 - 支出 - 冻结
     * 客户账户冻结 = 冻结 - 解冻
     */
    private BizReconCompareResult compare(ReconLedgerConst.SumGroupByObject sumInfo, ReconFlowConst.SumAmountSummary sumAmountSummary) {
        BigDecimal totalAmountChange = sumInfo.getTotalAmountChange();
        BigDecimal totalOccupiedAmountChange = sumInfo.getTotalOccupiedAmountChange();
        BigDecimal totalAvailableAmountChange = sumInfo.getTotalAvailableAmountChange();
        BizReconCompareResult result = new BizReconCompareResult();

        if (totalAmountChange.compareTo(sumAmountSummary.getFlowTotalAmount().subtract(sumAmountSummary.getUnfreezeTotalAmount())) != 0) {
            result.appendMessage("账户余额字段异常");
        }

        if (totalAvailableAmountChange.compareTo(sumAmountSummary.getFlowTotalAmount().subtract(sumAmountSummary.getFreezeTotalAmount())) != 0) {
            result.appendMessage("可用金额字段异常");
        }

        if (totalOccupiedAmountChange.compareTo(sumAmountSummary.getFreezeTotalAmount().subtract(sumAmountSummary.getUnfreezeTotalAmount())) != 0) {
            result.appendMessage("占用金额字段异常");
        }
        return result;
    }

}
