package com.facishare.crm.bizreconciliation.task;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.facishare.crm.bizreconciliation.config.BizReconciliationConfig;
import com.facishare.crm.bizreconciliation.util.BizReconciliationUtil;
import org.apache.curator.shaded.com.google.common.collect.Maps;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import com.alibaba.fastjson.JSON;
import com.facishare.crm.bizreconciliation.model.ReconTaskCallModel;
import org.springframework.stereotype.Component;

@Component
public class ReconTaskConsumer implements MessageListenerConcurrently {

    public Map<String, ReconTaskProcessor> reconTaskProcessorMap = Maps.newConcurrentMap();

    public ReconTaskConsumer(List<ReconTaskProcessor> processors) {
        processors.forEach(x -> {
            reconTaskProcessorMap.put(x.getBizModule() + "||" + x.getType(), x);
        });
    }

    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        for (MessageExt msg : msgs) {
            String tag = msg.getTags();
            if (BizReconciliationConfig.containsBizModule(tag)) {
                String body = new String(msg.getBody(), StandardCharsets.UTF_8);
                ReconTaskCallModel reconTaskCallModel = JSON.parseObject(body, ReconTaskCallModel.class);
                String tenantId = reconTaskCallModel.getTenantId();
                if (!BizReconciliationUtil.allow(tenantId)) {
                    continue;
                }
                ReconTaskProcessor processor = getProcessor(reconTaskCallModel);
                if (Objects.nonNull(processor)) {
                    boolean success = processor.execute(reconTaskCallModel);
                    if (!success) {
                        return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                    }
                }
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    protected ReconTaskProcessor getProcessor(ReconTaskCallModel reconTaskCallModel) {
        return reconTaskProcessorMap.get(reconTaskCallModel.getBizModule() + "||" + reconTaskCallModel.getType());
    }

}
