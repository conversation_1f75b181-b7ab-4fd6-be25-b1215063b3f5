package com.facishare.crm.bizreconciliation.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.helper.ConfigHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.RateLimiter;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@Data
public class BizReconciliationConfig {

    private String app;
    private Map<String, BizModuleInfo> bizModuleInfoMap;

    private static final Map<String, BizReconciliationConfig> appBizReconciliationConfigMap = Maps.newHashMap();
    private static int tps = 500;
    private static RateLimiter tpsRateLimiter = RateLimiter.create(tps);

    static {
        ConfigFactory.getConfig("fs-crm-customeraccount").addListener(config -> {
            int newTps = config.getInt("bizReconciliationTps", 500);
            if (tps != newTps && newTps > 0) {
                tpsRateLimiter.setRate(newTps);
                tps = newTps;
            }
            String json = config.get("bizReconciliationConfig");
            List<BizReconciliationConfig> bizReconciliationConfigs = JSON.parseObject(json, new TypeReference<List<BizReconciliationConfig>>() {
            }.getType());

            CollectionUtils.emptyIfNull(bizReconciliationConfigs).forEach(x -> {
                appBizReconciliationConfigMap.put(x.getApp(), x);
            });
        });
    }

    @Data
    static class BizModuleInfo {
        private String abnormalNotifyAppId;
        private String restUrl;
        private Set<Integer> receiverIds;
        private int tps;
    }

    static String appName = ConfigHelper.getProcessInfo().getAppName();

    public static boolean containsBizModule(String bizModule) {
        BizReconciliationConfig config = appBizReconciliationConfigMap.get(appName);
        if (Objects.isNull(config) || Objects.isNull(config.getBizModuleInfoMap())) {
            return false;
        }
        return config.getBizModuleInfoMap().containsKey(bizModule);
    }

    public static String getBizModuleRestUrl(String bizModule) {
        BizReconciliationConfig config = appBizReconciliationConfigMap.get(appName);
        if (Objects.isNull(config) || Objects.isNull(config.getBizModuleInfoMap())) {
            return null;
        }
        return config.getBizModuleInfoMap().get(bizModule).getRestUrl();
    }

    public static String getNotifyAppId(String bizModule) {
        BizReconciliationConfig config = appBizReconciliationConfigMap.get(appName);
        if (Objects.isNull(config) || Objects.isNull(config.getBizModuleInfoMap())) {
            return null;
        }
        return config.getBizModuleInfoMap().get(bizModule).getAbnormalNotifyAppId();
    }

    public static List<Integer> getNotifyReceivers(String bizModule) {
        BizReconciliationConfig config = appBizReconciliationConfigMap.get(appName);
        if (Objects.isNull(config) || Objects.isNull(config.getBizModuleInfoMap())) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(CollectionUtils.emptyIfNull(config.getBizModuleInfoMap().get(bizModule).getReceiverIds()));
    }

    public static void acquire() {
        tpsRateLimiter.acquire();
    }
}
