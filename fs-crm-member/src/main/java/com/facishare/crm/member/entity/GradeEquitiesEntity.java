package com.facishare.crm.member.entity;

import com.facishare.crm.member.constants.MemberGradeConstants;
import com.facishare.crm.member.constants.MemberGradeEquitiesRuleConstants;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.collect.Lists;
import lombok.Data;

import java.util.Optional;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2018/12/3.
 */
@Data
public class GradeEquitiesEntity extends BaseEntity{
    private static final String apiName = MemberGradeEquitiesRuleConstants.API_NAME;
    private static final String recordType = "default__c";

    private String id;
    /**
     * 权益Id equitiesId
     */
    private String equities;
    /**
     * 等级Id gradeId
     */
    private String grade;
    /**
     * 等级-权益 关系编号
     */
    private String name;

    public static GradeEquitiesEntity getGradeEquitiesEntityByIObject(IObjectData iObjectData, User creator) {
        GradeEquitiesEntity gradeEquitiesEntity = new GradeEquitiesEntity();
        gradeEquitiesEntity.setId(iObjectData.getId());
        gradeEquitiesEntity.setGrade(iObjectData.get(MemberGradeEquitiesRuleConstants.Field.GradeId.apiName,String.class));
        gradeEquitiesEntity.setEquities(iObjectData.get(MemberGradeEquitiesRuleConstants.Field.EquitiesId.apiName, String.class));
        gradeEquitiesEntity.setName(iObjectData.get(MemberGradeEquitiesRuleConstants.Field.Name.apiName, String.class));
        gradeEquitiesEntity.setCreator(creator);
        gradeEquitiesEntity.setIsDeleted(false);
        gradeEquitiesEntity.setVertion(iObjectData.get(IObjectData.VERSION));
        return gradeEquitiesEntity;
    }



    public IObjectData getObjectData(User creator,IObjectDescribe iObjectDescribe) {
        IObjectData objectData = new ObjectData();
        objectData.setDescribeApiName(apiName);
        Optional.ofNullable(iObjectDescribe).ifPresent(val -> objectData.setDescribeId(iObjectDescribe.getId()));
        objectData.setCreatedBy(creator.getUserId());
        objectData.setDeleted(false);
        objectData.setTenantId(creator.getTenantId());
        objectData.setRecordType(recordType);
        objectData.setOwner(Lists.newArrayList(creator.getUserId()));
        Optional.ofNullable(this.getVertion()).ifPresent(val -> objectData.set(IObjectData.VERSION, val.toString()));
        objectData.set(MemberGradeEquitiesRuleConstants.Field.EquitiesId.apiName, equities);
        objectData.set(MemberGradeEquitiesRuleConstants.Field.GradeId.apiName, grade);
        objectData.set(MemberGradeEquitiesRuleConstants.Field.Name.apiName, name);
        objectData.set(IObjectData.ID, this.getId());
        return objectData;
    }
}
