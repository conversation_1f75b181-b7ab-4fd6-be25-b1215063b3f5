package com.facishare.crm.member.predefine.service.impl;

import com.facishare.crm.member.arg.GetGrowthValueSettingArg;
import com.facishare.crm.member.arg.UpdateGrowthValueIncreaseArg;
import com.facishare.crm.member.common.ConfigKeyUtil;
import com.facishare.crm.member.common.GsonUtil;
import com.facishare.crm.member.common.RuleRequestContextUtil;
import com.facishare.crm.member.constants.GrowthValueTriggerTypeEnum;
import com.facishare.crm.member.constants.MemberIntegralDetailConstants;
import com.facishare.crm.member.dto.ListRuleGroup;
import com.facishare.crm.member.dto.ruleconfig.GrowthValueRuleExtConfig;
import com.facishare.crm.member.enums.SceneEnum;
import com.facishare.crm.member.exception.MemberBusinessException;
import com.facishare.crm.member.exception.MemberErrorCode;
import com.facishare.crm.member.predefine.manager.ConfigServiceManager;
import com.facishare.crm.member.predefine.manager.GrowthValueManager;
import com.facishare.crm.member.predefine.manager.PaasRuleManager;
import com.facishare.crm.member.predefine.service.GrowthValueService;
import com.facishare.crm.member.util.BeanUtil;
import com.facishare.crm.member.vo.GrowthValueModel;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.rule.common.RuleEngineContext;
import com.facishare.paas.rule.common.constant.FieldEnum;
import com.facishare.paas.rule.pojo.RuleGroupPojo;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.common.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 27/11/2018
 */
@Service
@Slf4j(topic = "member")
public class GrowthValueServiceImpl implements GrowthValueService {

    @Autowired
    private ConfigServiceManager configServiceManager;
    @Autowired
    private PaasRuleManager paasRuleManager;
    @Autowired
    private GrowthValueManager growthValueManager;

    /**
     * 更改成长值增长值增长值进入规则以及config
     */
    @Override
    public GrowthValueModel.UpdateGrowthValueIncreaseResult updateGrowthValueIncrease(UpdateGrowthValueIncreaseArg updateGrowthValueIncreaseArg, ServiceContext serviceContext) throws Exception {
        updateGrowthValueIncreaseArg.validGrowthValue();
        RuleEngineContext ruleEngineContext = RuleRequestContextUtil.getRuleEngineContext(serviceContext.getUser(), SceneEnum.FS_CRM_MEMBER_GROWTH_VALUE);
        if (!validGrowthValueData(serviceContext.getUser())) {
            growthValueManager.initGrowthValueIncrease(serviceContext.getUser());
        }
        ListRuleGroup listRuleGroup = ListRuleGroup.builder()
                .entityIds(Sets.newHashSet(MemberIntegralDetailConstants.API_NAME))
                .status(FieldEnum.RuleGroupStatus.ACTIVE)
                .build();
        List<RuleGroupPojo> ruleGroupPojoList = paasRuleManager.listRuleGroup(ruleEngineContext, listRuleGroup);
        if (!ruleGroupPojoList.isEmpty()) {
            String ruleCode = ruleGroupPojoList.get(0).getRuleCode();
            String config = configServiceManager.findTenantConfig(serviceContext.getRequestContext().getUser(), ConfigKeyUtil.getRuleActionKeyByRuleCode(ruleCode));
            GrowthValueRuleExtConfig growthValueIncByIntegralRuleExtConfig = GsonUtil.fromJsonSerializingNull(config, GrowthValueRuleExtConfig.class);
            growthValueIncByIntegralRuleExtConfig.setGrowthValuePerPoint(updateGrowthValueIncreaseArg.getGrowthValuePerPoint());
            growthValueIncByIntegralRuleExtConfig.setGrowthValueTriggerType(GrowthValueTriggerTypeEnum.INTEGRAL_INC.getType());
            growthValueIncByIntegralRuleExtConfig.setScene(SceneEnum.FS_CRM_MEMBER_GROWTH_VALUE.name());
            configServiceManager.updateTenantConfig(serviceContext.getRequestContext().getUser(), ConfigKeyUtil.getRuleActionKeyByRuleCode(ruleCode),
                    GsonUtil.toJson(
                            growthValueIncByIntegralRuleExtConfig
                    )
                    , ConfigValueType.JSON);
        } else {
            throw new MemberBusinessException(MemberErrorCode.GROWTH_VALUE_NOT_INIT);
        }
        return new GrowthValueModel.UpdateGrowthValueIncreaseResult();
    }

    public Boolean validGrowthValueData(User user) {
        RuleEngineContext ruleEngineContext = RuleRequestContextUtil.getRuleEngineContext(user, SceneEnum.FS_CRM_MEMBER_GROWTH_VALUE);
        ListRuleGroup listRuleGroup = ListRuleGroup.builder()
                .entityIds(Sets.newHashSet(MemberIntegralDetailConstants.API_NAME))
                .status(FieldEnum.RuleGroupStatus.ACTIVE)
                .build();
        List<RuleGroupPojo> ruleGroupPojoList = paasRuleManager.listRuleGroup(ruleEngineContext, listRuleGroup);
        List<String> configKeyList = new ArrayList<>();
        List<String> triggerIdList = new ArrayList<>();
        if (!ruleGroupPojoList.isEmpty()) {
            for (RuleGroupPojo ruleGroupPojo : ruleGroupPojoList) {
                String config = configServiceManager
                        .findTenantConfig(user, ConfigKeyUtil.getRuleActionKeyByRuleCode(ruleGroupPojo.getRuleCode()));
                if (!Strings.isNullOrEmpty(config)) {
                    configKeyList.add(ConfigKeyUtil.getRuleActionKeyByRuleCode(ruleGroupPojo.getRuleCode()));
                    GrowthValueRuleExtConfig growthValueRuleExtConfig = GsonUtil.fromJsonSerializingNull(config, GrowthValueRuleExtConfig.class);
                    if (!Strings.isNullOrEmpty(growthValueRuleExtConfig.getTriggerId())) {
                        triggerIdList.add(growthValueRuleExtConfig.getTriggerId());
                    }
                }
            }
        }
        if (ruleGroupPojoList.size() != 1 || configKeyList.size() != 1 || triggerIdList.size() != 1)
            return false;
        else return true;
    }

    @Override
    public GrowthValueModel.GetGrowthValueSettingResult getGrowthValueSetting(GetGrowthValueSettingArg getGrowthValueSettingArg, ServiceContext serviceContext) {
        RuleEngineContext ruleEngineContext = RuleRequestContextUtil.getRuleEngineContext(serviceContext.getUser(), SceneEnum.FS_CRM_MEMBER_GROWTH_VALUE);
        ListRuleGroup listRuleGroup = ListRuleGroup.builder()
                .entityIds(Sets.newHashSet(MemberIntegralDetailConstants.API_NAME))
                .status(FieldEnum.RuleGroupStatus.ACTIVE)
                .build();
        List<RuleGroupPojo> ruleGroupPojoList = paasRuleManager.listRuleGroup(ruleEngineContext, listRuleGroup);
        GrowthValueRuleExtConfig growthValueRuleExtConfig = null;
        if (!ruleGroupPojoList.isEmpty()) {
            String ruleCode = ruleGroupPojoList.get(0).getRuleCode();
            String growthValueSettingJson = configServiceManager.findTenantConfig(serviceContext.getRequestContext().getUser(), ConfigKeyUtil.getRuleActionKeyByRuleCode(ruleCode));
            growthValueRuleExtConfig = GsonUtil.fromJsonSerializingNull(growthValueSettingJson, GrowthValueRuleExtConfig.class);
        }
        GrowthValueModel.GetGrowthValueSettingResult getGrowthValueSettingResult =  new GrowthValueModel.GetGrowthValueSettingResult();
        BeanUtils.copyProperties(growthValueRuleExtConfig,getGrowthValueSettingResult);
        return getGrowthValueSettingResult;
    }

}
