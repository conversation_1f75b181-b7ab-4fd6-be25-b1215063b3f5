package com.facishare.crm.member.predefine.privilege;

import com.facishare.crmcommon.constants.CommonConstants;
import com.facishare.crm.member.constants.MemberGrowthValueDetailConstants;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.privilege.model.DefaultFunctionPrivilegeProvider;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;

/**
 * @Auther: dzb
 * @Date: 2018/11/19
 * @Description:成长值明细
 */
@Component
public class MemberGrowthValueDetailFunctionPrivilegeProvider extends DefaultFunctionPrivilegeProvider {
    /**
     * 成长值明细的功能权限
     **/
    private final static List<String> supportActionCodes = Lists.newArrayList(

        ObjectAction.DELETE.getActionCode(),

        ObjectAction.VIEW_DETAIL.getActionCode(),

        ObjectAction.VIEW_LIST.getActionCode(),

        ObjectAction.CREATE.getActionCode(),

        ObjectAction.BATCH_EXPORT.getActionCode(),

        ObjectAction.INVALID.getActionCode(),

        ObjectAction.RECOVER.getActionCode(),

        ObjectAction.PRINT.getActionCode(),

        ObjectAction.LOCK.getActionCode(),

        ObjectAction.UNLOCK.getActionCode(),

        ObjectAction.DISCUSS.getActionCode()

    );
    /**
     * 销售人员的功能权限
     **/
    private final static List<String> salePersonActionCodes = Lists.newArrayList(ObjectAction.VIEW_DETAIL.getActionCode(), ObjectAction.VIEW_LIST.getActionCode());
    /**
     * 销后人员的功能权限
     **/
    private final static List<String> saleAfterPersonActionCodes = Lists.newArrayList(ObjectAction.VIEW_DETAIL.getActionCode(), ObjectAction.VIEW_LIST.getActionCode());

    @Override
    public String getApiName() {
        return MemberGrowthValueDetailConstants.API_NAME;
    }

    @Override
    public List<String> getSupportedActionCodes() {
        return Collections.unmodifiableList(supportActionCodes);
    }

    @Override
    public Map<String, List<String>> getCustomInitRoleActionCodes() {
        Map<String, List<String>> actionCodeMap = Maps.newHashMap();
        actionCodeMap.put(CommonConstants.MEMBER_MANAGER_ROLE, Collections.unmodifiableList(supportActionCodes));
        actionCodeMap.put(CommonConstants.SALE_PERSON_ROLE, Collections.unmodifiableList(salePersonActionCodes));
        actionCodeMap.put(CommonConstants.SALE_AFTER_PERSON_ROLE, Collections.unmodifiableList(saleAfterPersonActionCodes));
        return Collections.unmodifiableMap(actionCodeMap);
    }
}
