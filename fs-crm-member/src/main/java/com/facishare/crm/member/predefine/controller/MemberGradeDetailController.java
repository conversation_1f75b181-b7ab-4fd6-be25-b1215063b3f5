package com.facishare.crm.member.predefine.controller;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardDetailController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.IButton;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Auther: dzb
 * @Date: 2018/11/22
 * @Description:  会员等级
 */
@Slf4j
@Component
public class MemberGradeDetailController extends StandardDetailController {
    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        LayoutDocument layoutDocument = result.getLayout();
        List<IButton> buttonList = LayoutExt.of(new Layout(layoutDocument)).getButtons();
        buttonList.removeIf(button -> button.getAction().equals(ObjectAction.CREATE.getActionCode())||button.getAction().equals(ObjectAction.BATCH_IMPORT.getActionCode()) || button.getAction().equals(ObjectAction.RECOVER.getActionCode()) || button.getAction()
            .equals(ObjectAction.INVALID.getActionCode()));
        LayoutExt.of(new Layout(layoutDocument)).setButtons(buttonList);
        return result;
    }
}
