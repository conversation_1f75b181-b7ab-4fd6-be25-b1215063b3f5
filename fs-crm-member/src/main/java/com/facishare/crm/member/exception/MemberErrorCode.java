package com.facishare.crm.member.exception;

import com.facishare.paas.appframework.core.exception.ErrorCode;

/**
 * @Auther: dzb
 * @Date: 2018/11/15
 * @Description:
 */
public enum MemberErrorCode implements ErrorCode {
    //10000-20000
    RULE_NOT_EXITS(10000, "规则不存在"),
    RULE_UPDATE_ERROR(10001, "规则更新失败"),
    RULE_NAME_EXIST(10002, "规则名称已存在"),
    GROWTH_VALUE_NOT_INIT(10003, "成长值未初始化"),
    RULE_GROUP_IS_NULL(10004,"获取的规则组为空"),
    RULE_GROUP_IS_MORE_THAN_ONE(10005,"获取的规则组多一条"),
    INTEGRAL_IS_INVALID(10006,"积分不合法，积分必须为数字且不能为空，最多支持两位小数，最大支持14位整数"),
    GROWTH_VALUE_IS_INVALID(10007,"成长值不合法，成长值必须为数字且不能为空，最多支持两位小数，最大支持14位整数"),


    QUERY_ROLE_ERROR(60001, "会员角色查询异常"),
    CREATE_ROLE_ERROR(60002, "会员管理角色创建失败或者不存在");
    private String message;
    private int errorCode;

    MemberErrorCode(int errorCode, String message) {
        this.errorCode = errorCode;
        this.message = message;
    }

    @Override
    public int getCode() {
        return errorCode;
    }

    public String getMessage() {
        return message;
    }
}
