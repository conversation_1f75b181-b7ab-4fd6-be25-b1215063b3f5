package com.facishare.crm.member.predefine.manager;

import com.facishare.crm.member.common.CommonService;
import com.facishare.crm.member.constants.MemberConstants;
import com.facishare.crm.member.constants.MemberGrowthValueDetailConstants;
import com.facishare.crm.member.constants.MemberGrowthValueDetailConstants.Field;
import com.facishare.crm.member.constants.MemberIntegralDetailConstants;
import com.facishare.crm.member.dto.GetRuleGroup;
import com.facishare.crm.member.enums.AppIdEnum;
import com.facishare.crm.member.enums.SceneEnum;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.MultiRecordType;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.rule.common.RuleEngineContext;
import com.facishare.paas.rule.pojo.RuleGroupPojo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @IgnoreI18nFile
 */
@Slf4j
@Component
public class PaasManager extends CommonService {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private PaasRuleManager paasRuleManager;

    public void createGrowthValueDetail(User user, String memberId, Double changeValue, String cause) {
        IObjectDescribe growthValueDetailDescribe = serviceFacade.findObject(user.getTenantId(), MemberGrowthValueDetailConstants.API_NAME);
        if (growthValueDetailDescribe == null) {
            log.warn("createServiceRecord failed. masterDescribe is nullGrowthValueIncByIntegralRuleExtConfig. user[{}]", user);
            return;
        }
        // 主对象
        IObjectData growthValueDetailData = new ObjectData();
        growthValueDetailData.set(Field.MemberId.apiName, memberId);
        growthValueDetailData.set(Field.ChangeGrowthValue.apiName, changeValue);
        growthValueDetailData.set(Field.GrowthValueCause.apiName, cause);
        growthValueDetailData.set(Field.ChangeTime.apiName, new Date());
        growthValueDetailData.setOwner(Lists.newArrayList(user.getUserId()));
        growthValueDetailData.setTenantId(user.getTenantId());
        growthValueDetailData.setCreatedBy(user.getUserId());
        growthValueDetailData.setLastModifiedBy(user.getUserId());
        growthValueDetailData.setRecordType(MultiRecordType.RECORD_TYPE_DEFAULT);
        growthValueDetailData.set(IObjectData.DESCRIBE_ID, growthValueDetailDescribe.getId());
        growthValueDetailData.set(IObjectData.DESCRIBE_API_NAME, MemberGrowthValueDetailConstants.API_NAME);
        triggerAddAction(user, MemberGrowthValueDetailConstants.API_NAME, ObjectDataDocument.of(growthValueDetailData));
    }

    public void createIntegralDetail(User user, String memberId, Double changeValue, String cause, String ruleName) {
        IObjectDescribe integralDetailDescribe = serviceFacade.findObject(user.getTenantId(), MemberIntegralDetailConstants.API_NAME);
        if (integralDetailDescribe == null) {
            log.warn("createServiceRecord failed. masterDescribe is nullGrowthValueIncByIntegralRuleExtConfig. user[{}]", user);
            return;
        }
        // 主对象
        IObjectData integralDetailData = new ObjectData();
        integralDetailData.set(MemberIntegralDetailConstants.Field.MemberId.apiName, memberId);
        integralDetailData.set(MemberIntegralDetailConstants.Field.ChangeIntegralValue.apiName, changeValue);
        integralDetailData.set(MemberIntegralDetailConstants.Field.IntegralValueChangeCause.apiName, cause);
        integralDetailData.set(MemberIntegralDetailConstants.Field.ChangeTime.apiName, new Date());
        integralDetailData.set(MemberIntegralDetailConstants.Field.TriggerRules.apiName, ruleName);
        integralDetailData.setOwner(Lists.newArrayList(user.getUserId()));
        integralDetailData.setTenantId(user.getTenantId());
        integralDetailData.setCreatedBy(user.getUserId());
        integralDetailData.setLastModifiedBy(user.getUserId());
        integralDetailData.setRecordType(MultiRecordType.RECORD_TYPE_DEFAULT);
        integralDetailData.set(IObjectData.DESCRIBE_ID, integralDetailDescribe.getId());
        integralDetailData.set(IObjectData.DESCRIBE_API_NAME, MemberIntegralDetailConstants.API_NAME);
        triggerAddAction(user, MemberIntegralDetailConstants.API_NAME, ObjectDataDocument.of(integralDetailData));
    }

    public IObjectData getMemberObjByCustomerId(User user, String customerId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = new ArrayList<>(1);
        Filter customerFieldFilter = new Filter();
        customerFieldFilter.setFieldName(MemberConstants.Field.CustomerId.apiName);
        customerFieldFilter.setOperator(Operator.EQ);
        customerFieldFilter.setFieldValues(Arrays.asList(customerId));
        filters.add(customerFieldFilter);
        query.addFilters(filters);
        QueryResult<IObjectData> memberDataResult = serviceFacade.findBySearchQuery(user, MemberConstants.API_NAME, query);
        if (memberDataResult.getTotalNumber() == null || memberDataResult.getTotalNumber() == 0) {
            return null;
        }
        if (memberDataResult.getTotalNumber() > 1) {
            throw new IllegalStateException("匹配到多条会员信息");
        }
        return memberDataResult.getData().get(0);
    }

    public RuleGroupPojo getInteRuleGroupPojoByRuleCode(User user, String ruleCode) {
        RuleEngineContext ruleEngineContext = new RuleEngineContext();
        ruleEngineContext.setAppId(AppIdEnum.MEMBER.name());
        ruleEngineContext.setScene(SceneEnum.FS_CRM_MEMBER_INTEGRAL.name());
        ruleEngineContext.setProperties(new HashMap<>());
        ruleEngineContext.setObjectProperties(new HashMap<>());
        ruleEngineContext.setTenantId(user.getTenantId());
        ruleEngineContext.setUserId(user.getUserId());

        GetRuleGroup getRuleGroup = new GetRuleGroup();
        getRuleGroup.setRuleCodes(ruleCode);
        getRuleGroup.setStatus(1);

        return paasRuleManager.getRuleGroup(ruleEngineContext,  getRuleGroup);
    }

}
