package com.facishare.crm.member.enums;

import com.google.common.collect.Maps;
import java.util.HashMap;
import java.util.Map;
import lombok.Data;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 29/11/2018
 */
public enum RuleOperateMapEnum {
    SYMBOL_TO_CODE_OPERATOR_MAP(symbolToCode()),
    CODE_TO_SYMBOL_OPERATOR_MAP(codeToSymbol())
    ;
    RuleOperateMapEnum(Map<String, String> map){
        this.map = map;
    }
    private Map<String, String> map;
    public String getSymbol(String value){
        return map.get(value);
    }
    static Map<String, String> symbolToCode(){
        Map<String, String> symbolToCodeOperatorMap = new HashMap<>();
        symbolToCodeOperatorMap.put(OperatorEnum.EQUALS.getValue(), "equals");
        symbolToCodeOperatorMap.put(OperatorEnum.NOT_EQUALS.getValue(), "notequals");
        symbolToCodeOperatorMap.put(OperatorEnum.CONTAINS.getValue(), "contains");
        symbolToCodeOperatorMap.put(OperatorEnum.NOT_CONTAINS.getValue(), "notcontains");
        symbolToCodeOperatorMap.put(OperatorEnum.HAS_ANY_ONE.getValue(), "IN");
        symbolToCodeOperatorMap.put(OperatorEnum.HAS_NONE_OF.getValue(), "NIN");
        symbolToCodeOperatorMap.put(OperatorEnum.BETWEEN.getValue(), "between");
        symbolToCodeOperatorMap.put(OperatorEnum.NOT_BETWEEN.getValue(), "notbetween");
        symbolToCodeOperatorMap.put(OperatorEnum.GREATER_THAN.getValue(), "GT");
        symbolToCodeOperatorMap.put(OperatorEnum.LESS_THAN.getValue(), "LT");
        symbolToCodeOperatorMap.put(OperatorEnum.GREATER_THAN_OR_EQUALS.getValue(), "GTE");
        symbolToCodeOperatorMap.put(OperatorEnum.LESS_THAN_OR_EQUALS.getValue(), "LTE");
        symbolToCodeOperatorMap.put(OperatorEnum.NUMBER_EQUALS.getValue(), "EQ");
        symbolToCodeOperatorMap.put(OperatorEnum.NUMBER_NOT_EQUALS.getValue(), "N");
        symbolToCodeOperatorMap.put(OperatorEnum.EMPTY.getValue(), "IS");
        symbolToCodeOperatorMap.put(OperatorEnum.NOT_EMPTY.getValue(), "ISN");
        return symbolToCodeOperatorMap;
    }
    
    static Map<String, String> codeToSymbol(){
        Map<String, String> codeToSymbolOperatorMap = new HashMap<>();
        codeToSymbolOperatorMap.put("equals",OperatorEnum.EQUALS.getValue());
        codeToSymbolOperatorMap.put("notequals",OperatorEnum.NOT_EQUALS.getValue());
        codeToSymbolOperatorMap.put("contains",OperatorEnum.CONTAINS.getValue());
        codeToSymbolOperatorMap.put("notcontains",OperatorEnum.NOT_CONTAINS.getValue());
        codeToSymbolOperatorMap.put("IN",OperatorEnum.HAS_ANY_ONE.getValue());
        codeToSymbolOperatorMap.put("NIN",OperatorEnum.HAS_NONE_OF.getValue());
        codeToSymbolOperatorMap.put("between",OperatorEnum.BETWEEN.getValue());
        codeToSymbolOperatorMap.put("notbetween",OperatorEnum.NOT_BETWEEN.getValue());
        codeToSymbolOperatorMap.put("GT",OperatorEnum.GREATER_THAN.getValue());
        codeToSymbolOperatorMap.put("LT",OperatorEnum.LESS_THAN.getValue());
        codeToSymbolOperatorMap.put("GTE",OperatorEnum.GREATER_THAN_OR_EQUALS.getValue());
        codeToSymbolOperatorMap.put("LTE",OperatorEnum.LESS_THAN_OR_EQUALS.getValue());
        codeToSymbolOperatorMap.put("EQ",OperatorEnum.NUMBER_EQUALS.getValue());
        codeToSymbolOperatorMap.put("N",OperatorEnum.NUMBER_NOT_EQUALS.getValue());
        codeToSymbolOperatorMap.put("IS",OperatorEnum.EMPTY.getValue());
        codeToSymbolOperatorMap.put("ISN",OperatorEnum.NOT_EMPTY.getValue());
        return codeToSymbolOperatorMap;
    }
    
}
