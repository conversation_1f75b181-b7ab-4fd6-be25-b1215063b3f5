package com.facishare.crm.member.predefine.manager;

import com.facishare.appserver.trigger.api.dto.OperateCode;
import com.facishare.appserver.trigger.api.exception.TriggerBizException;
import com.facishare.crm.member.arg.RuleArg;
import com.facishare.crm.member.arg.RuleGroupArg;
import com.facishare.crm.member.common.ConfigKeyUtil;
import com.facishare.crm.member.common.GsonUtil;
import com.facishare.crm.member.common.InitRuleNameUtil;
import com.facishare.crm.member.common.RuleRequestContextUtil;
import com.facishare.crm.member.constants.GrowthValueTriggerTypeEnum;
import com.facishare.crm.member.constants.MemberIntegralDetailConstants;
import com.facishare.crm.member.constants.MemberIntegralDetailConstants.Field;
import com.facishare.crm.member.dto.DeleteRuleGroup;
import com.facishare.crm.member.dto.ListRuleGroup;
import com.facishare.crm.member.dto.ruleconfig.GrowthValueRuleExtConfig;
import com.facishare.crm.member.enums.AppIdEnum;
import com.facishare.crm.member.enums.RuleOperateMapEnum;
import com.facishare.crm.member.enums.SceneEnum;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.rule.common.RuleEngineContext;
import com.facishare.paas.rule.common.constant.FieldEnum;
import com.facishare.paas.rule.common.constant.FieldType;
import com.facishare.paas.rule.common.constant.Operate;
import com.facishare.paas.rule.pojo.RuleGroupPojo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.common.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 06/12/2018
 */
@Slf4j
@Component
public class GrowthValueManager {
    @Autowired
    private PaasRuleManager paasRuleManager;
    @Autowired
    private TriggerServiceManager triggerServiceManager;
    @Autowired
    private ConfigService configService;

    /**
     * 初始化成长值增长值进入规则以及config
     */
    public void initGrowthValueIncrease(User user) throws TriggerBizException {
        RuleEngineContext ruleEngineContext = RuleRequestContextUtil.getRuleEngineContext(user, SceneEnum.FS_CRM_MEMBER_GROWTH_VALUE);
        /**
         * 初始化失败脏数据清理
         */
        this.deleteGrowthValueUselessData(user);
        RuleGroupPojo ruleGroupPojo = this.generateRuleGroup(user);
        String ruleCode = paasRuleManager.createRuleGroup(ruleEngineContext, ruleGroupPojo);
        String triggerId = triggerServiceManager.deploy(Integer.valueOf(user.getTenantId()), Integer.valueOf(user.getUserId()), AppIdEnum.MEMBER.name(), SceneEnum.FS_CRM_MEMBER_GROWTH_VALUE.name(), MemberIntegralDetailConstants.API_NAME,
                OperateCode.INSERT, ruleCode);
        GrowthValueRuleExtConfig growthValueIncByIntegralRuleExtConfig = new GrowthValueRuleExtConfig();
        growthValueIncByIntegralRuleExtConfig.setGrowthValuePerPoint(0.0);
        growthValueIncByIntegralRuleExtConfig.setTriggerId(triggerId);
        growthValueIncByIntegralRuleExtConfig.setGrowthValueTriggerType(GrowthValueTriggerTypeEnum.INTEGRAL_INC.getType());
        growthValueIncByIntegralRuleExtConfig.setScene(SceneEnum.FS_CRM_MEMBER_GROWTH_VALUE.name());
        configService
                .createTenantConfig(user, ConfigKeyUtil.getRuleActionKeyByRuleCode(ruleCode), GsonUtil.toJson(
                        growthValueIncByIntegralRuleExtConfig
                ), ConfigValueType.JSON);
    }

    private RuleGroupPojo generateRuleGroup(User user) {
        RuleGroupPojo ruleGroupPojo = new RuleGroupPojo();
        RuleGroupArg ruleGroupArg = new RuleGroupArg();
        ruleGroupArg.setEntityId(MemberIntegralDetailConstants.API_NAME);
        ruleGroupArg.setRuleName(InitRuleNameUtil.getGrowthValueRuleNameByTenantId(user.getTenantId()));
        ruleGroupArg.setRuleParse("( 1 )");
        ruleGroupArg.setStatus(FieldEnum.RuleGroupStatus.ACTIVE);
        RuleArg ruleArg = new RuleArg();
        ruleArg.setOperate(Operate.GT);
        ruleArg.setFieldValue(Lists.newArrayList("0"));
        ruleArg.setFieldType(FieldType.NUMBER);
        ruleArg.setRuleOrder(1);
        ruleArg.setFieldName(Field.ChangeIntegralValue.apiName);
        ruleGroupArg.setRules(Lists.newArrayList(ruleArg));
        BeanUtils.copyProperties(ruleGroupArg, ruleGroupPojo);
        ruleGroupPojo.setSqlSelectFields(Lists.newArrayList(IObjectData.ID));
        return ruleGroupPojo;
    }

    public void deleteGrowthValueUselessData(User user) throws TriggerBizException {
        RuleEngineContext ruleEngineContext = RuleRequestContextUtil.getRuleEngineContext(user, SceneEnum.FS_CRM_MEMBER_GROWTH_VALUE);
        ListRuleGroup listRuleGroup = ListRuleGroup.builder()
                .entityIds(Sets.newHashSet(MemberIntegralDetailConstants.API_NAME))
                .status(FieldEnum.RuleGroupStatus.ACTIVE)
                .build();
        List<RuleGroupPojo> ruleGroupPojoList = paasRuleManager.listRuleGroup(ruleEngineContext, listRuleGroup);
        List<String> configKeyList = new ArrayList<>();
        List<String> triggerIdList = new ArrayList<>();
        if (!ruleGroupPojoList.isEmpty()) {
            for (RuleGroupPojo ruleGroupPojo : ruleGroupPojoList) {
                String config = configService
                        .findTenantConfig(user, ConfigKeyUtil.getRuleActionKeyByRuleCode(ruleGroupPojo.getRuleCode()));
                if (!Strings.isNullOrEmpty(config)) {
                    configKeyList.add(ConfigKeyUtil.getRuleActionKeyByRuleCode(ruleGroupPojo.getRuleCode()));
                    GrowthValueRuleExtConfig growthValueRuleExtConfig = GsonUtil.fromJsonSerializingNull(config, GrowthValueRuleExtConfig.class);
                    if (!Strings.isNullOrEmpty(growthValueRuleExtConfig.getTriggerId())) {
                        triggerIdList.add(growthValueRuleExtConfig.getTriggerId());
                    }
                }
            }
        }
        if (!ruleGroupPojoList.isEmpty()) {
            Set<String> ruleCodeList = new HashSet<>();
            ruleGroupPojoList.forEach(val -> {
                if (!Strings.isNullOrEmpty(val.getRuleCode())) {
                    ruleCodeList.add(val.getRuleCode());
                }
            });
            DeleteRuleGroup deleteRuleGroup = new DeleteRuleGroup();
            deleteRuleGroup.setRuleCodes(ruleCodeList);
            deleteRuleGroup.setEntityId(MemberIntegralDetailConstants.API_NAME);
            paasRuleManager.deleteRuleGroup(ruleEngineContext, deleteRuleGroup);
        }
        if (!configKeyList.isEmpty()) {
            configKeyList.forEach(val -> {
                configService.deleteTenantConfig(user, val);
            });
        }
        if (!triggerIdList.isEmpty()) {
            for (String triggerId : triggerIdList) {
                triggerServiceManager.delete(Integer.parseInt(user.getTenantId()), Integer.parseInt(user.getUserId()), AppIdEnum.MEMBER.name(), SceneEnum.FS_CRM_MEMBER_GROWTH_VALUE.name(), triggerId);
            }
        }
    }

}



