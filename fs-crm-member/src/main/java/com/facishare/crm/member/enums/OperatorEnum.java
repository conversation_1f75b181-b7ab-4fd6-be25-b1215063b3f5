package com.facishare.crm.member.enums;

import java.util.HashMap;
import java.util.Map;

public enum OperatorEnum {

    EQUALS("equals", "等于"),
    NOT_EQUALS("notEquals", "不等于"),
    CONTAINS("contains", "包含"),
    NOT_CONTAINS("notContains", "不包含"),
    EMPTY("empty", "为空", false),
    NOT_EMPTY("notEmpty", "不为空", false),

    HAS_ANY_ONE("hasAnyOf", "包含"),
    HAS_NONE_OF("hasNoneOf", "不包含"),

    BETWEEN("between","在区间内"),
    NOT_BETWEEN("notBetween","不在区间内"),

    NUMBER_EQUALS("==", "等于"),
    NUMBER_NOT_EQUALS("!=", "不等于"),
    GREATER_THAN(">", "大于"),
    LESS_THAN("<", "小于"),
    GREATER_THAN_OR_EQUALS(">=", "大于等于"),
    LESS_THAN_OR_EQUALS("<=", "小于等于");

    OperatorEnum(String value, String label) {
        this.value = value;
        this.label = label;
        this.comparable = true;
    }

    OperatorEnum(String value, String label, Boolean comparable) {
        this.value = value;
        this.label = label;
        this.comparable = comparable;
    }

    public static final Map<String, OperatorEnum> ALL_OPERATOR = new HashMap<>();

    static {
        ALL_OPERATOR.put("equals", EQUALS);
        ALL_OPERATOR.put("notEquals", NOT_EQUALS);
        ALL_OPERATOR.put("contains", CONTAINS);
        ALL_OPERATOR.put("notContains", NOT_CONTAINS);
        ALL_OPERATOR.put("empty", EMPTY);
        ALL_OPERATOR.put("notEmpty", NOT_EMPTY);
        ALL_OPERATOR.put("hasAnyOf", HAS_ANY_ONE);
        ALL_OPERATOR.put("hasNoneOf", HAS_NONE_OF);
        ALL_OPERATOR.put("==", NUMBER_EQUALS);
        ALL_OPERATOR.put("!=", NUMBER_NOT_EQUALS);
        ALL_OPERATOR.put(">", GREATER_THAN);
        ALL_OPERATOR.put("<", LESS_THAN);
        ALL_OPERATOR.put(">=", GREATER_THAN_OR_EQUALS);
        ALL_OPERATOR.put("<=", LESS_THAN_OR_EQUALS);
    }

    public static OperatorEnum parse(String value) {
        return ALL_OPERATOR.get(value);
    }

    private String label;

    private String value;

    private Boolean comparable;

    public String getLabel() {
        return this.label;
    }

    public Boolean isComparable() {
        return this.comparable;
    }

    public String getValue() {
        return this.value;
    }
}

