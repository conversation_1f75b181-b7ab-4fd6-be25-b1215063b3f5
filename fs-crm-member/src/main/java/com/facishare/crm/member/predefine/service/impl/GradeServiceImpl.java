package com.facishare.crm.member.predefine.service.impl;

import com.facishare.appserver.trigger.api.exception.TriggerBizException;
import com.facishare.crm.member.arg.RuleArg;
import com.facishare.crm.member.arg.RuleGroupArg;
import com.facishare.crm.member.common.ConfigKeyUtil;
import com.facishare.crm.member.common.GsonUtil;
import com.facishare.crm.member.common.RuleRequestContextUtil;
import com.facishare.crm.member.constants.GradeTriggerTypeEnum;
import com.facishare.crm.member.constants.MemberEquitiesConstants;
import com.facishare.crm.member.constants.MemberGradeConstants;
import com.facishare.crm.member.constants.MemberGradeEquitiesRuleConstants;
import com.facishare.crm.member.constants.MemberGradeEquitiesRuleConstants.Field;
import com.facishare.crm.member.constants.MemberGrowthValueDetailConstants;
import com.facishare.crm.member.dto.DeleteRuleGroup;
import com.facishare.crm.member.dto.ListRuleGroup;
import com.facishare.crm.member.dto.ruleconfig.GradeRuleExtConfig;
import com.facishare.crm.member.entity.EquitiesEntity;
import com.facishare.crm.member.entity.GradeEntity;
import com.facishare.crm.member.entity.GradeEquitiesEntity;
import com.facishare.crm.member.enums.AppIdEnum;
import com.facishare.crm.member.enums.SceneEnum;
import com.facishare.crm.member.predefine.manager.ConfigServiceManager;
import com.facishare.crm.member.predefine.manager.GradeManager;
import com.facishare.crm.member.predefine.manager.PaasRuleManager;
import com.facishare.crm.member.predefine.manager.ServiceFacadeManager;
import com.facishare.crm.member.predefine.manager.TriggerServiceManager;
import com.facishare.crm.member.predefine.service.GradeService;
import com.facishare.crm.member.vo.EquitiesModel;
import com.facishare.crm.member.vo.GradeModel;
import com.facishare.crm.member.vo.GradeModel.Equities;
import com.facishare.crm.member.vo.GradeModel.Grade;
import com.facishare.crm.member.vo.GradeModel.ListGradeArg;
import com.facishare.crm.member.vo.GradeModel.ListGradeResult;
import com.facishare.crm.member.vo.GradeModel.UpdateGradeEquitiesArg;
import com.facishare.crm.member.vo.GradeModel.UpdateGradeEquitiesResult;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.rule.common.RuleEngineContext;
import com.facishare.paas.rule.common.constant.FieldEnum;
import com.facishare.paas.rule.common.constant.FieldType;
import com.facishare.paas.rule.common.constant.Operate;
import com.facishare.paas.rule.pojo.RuleGroupPojo;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 27/11/2018
 */
@Service
@Slf4j
public class GradeServiceImpl implements GradeService {
    private static final SceneEnum SCENE_FS_CRM_MEMBER_GRADE = SceneEnum.FS_CRM_MEMBER_GRADE;
    @Autowired
    private ConfigServiceManager configServiceManager;
    @Autowired
    private ServiceFacadeManager serviceFacadeManager;
    @Autowired
    private PaasRuleManager paasRuleManager;
    @Autowired
    private TriggerServiceManager triggerServiceManager;
    @Autowired
    private GradeManager gradeManager;

    public EquitiesModel.GetEquitiesResult getEquities(EquitiesModel.GetEquitiesArg getEquitiesArg, ServiceContext serviceContext) {
        //查看权益
        IObjectData iObjectData = serviceFacadeManager.findObjectData(serviceContext.getRequestContext().getUser(), getEquitiesArg.getId(), MemberEquitiesConstants.API_NAME);
        EquitiesModel.GetEquitiesResult getEquitiesResult = new EquitiesModel.GetEquitiesResult();
        BeanUtils.copyProperties(EquitiesEntity.getEquitiesEntityByIObject(iObjectData, serviceContext.getUser()), getEquitiesResult);
        return getEquitiesResult;
    }

    /**
     * 删除旧规则
     *
     * @param ruleCodes 规则码组
     * @param ruleEngineContext 规则组上下文
     */
    private void deleteOldRuleGroup(Set<String> ruleCodes, RuleEngineContext ruleEngineContext) {
        DeleteRuleGroup deleteRuleGroup = new DeleteRuleGroup();
        deleteRuleGroup.setEntityId(MemberGrowthValueDetailConstants.API_NAME);
        deleteRuleGroup.setRuleCodes(ruleCodes);
        paasRuleManager.deleteRuleGroup(ruleEngineContext, deleteRuleGroup);
    }

    /**
     * 删除旧配置
     *
     * @param ruleCodes 规则码组
     * @param serviceContext 服务请求上下文
     */
    private List<GradeRuleExtConfig> deleteOldConfig(Set<String> ruleCodes, ServiceContext serviceContext) {
        List<GradeRuleExtConfig> gradeRuleExtConfigList = new ArrayList<>();
        for (String ruleCode : ruleCodes) {
            String tenantConfigJson;
            if ((tenantConfigJson = configServiceManager.findTenantConfig(serviceContext.getRequestContext().getUser(), ConfigKeyUtil.getRuleActionKeyByRuleCode(ruleCode))) != null) {
                GradeRuleExtConfig gradeRuleExtConfig = GsonUtil.fromJsonSerializingNull(tenantConfigJson, GradeRuleExtConfig.class);
                String triggerId = gradeRuleExtConfig.getTriggerId();
                if (!Strings.isNullOrEmpty(triggerId)) {
                    gradeRuleExtConfigList.add(gradeRuleExtConfig);
                }
                configServiceManager.deleteTenantConfig(serviceContext.getRequestContext().getUser(), ConfigKeyUtil.getRuleActionKeyByRuleCode(ruleCode));
            }
        }
        return gradeRuleExtConfigList;
    }

    /**
     * 批量删除等级权益
     */
    public void bulkDeleteGradeEquities(QueryResult<IObjectData> gradeIObjectDatas, ServiceContext serviceContext) {
        gradeIObjectDatas.getData().forEach(val -> {
            GradeEntity gradeEntity = GradeEntity.getGradeEntityByIObject(val, serviceContext.getUser());
            //查找等级相关权益
            GradeModel.GetGradeEquitiesArg getGradeEquitiesArg = new GradeModel.GetGradeEquitiesArg();
            getGradeEquitiesArg.setGradeId(gradeEntity.getId());
            GradeModel.GetGradeEquitiesResult getGradeEquitiesResult = this.getEquitiesByGrade(getGradeEquitiesArg, serviceContext);
            GradeModel.DeleteGradeEquitiesArg deleteGradeEquitiesArg = new GradeModel.DeleteGradeEquitiesArg();
            deleteGradeEquitiesArg.setGradeEquitiesIds(getGradeEquitiesResult.getGradeEquitiesList().stream().map(gradeEquities -> gradeEquities.getId()).collect(Collectors.toSet()));
            this.deleteGradeEquities(deleteGradeEquitiesArg, serviceContext);
        });
    }

    /**
     * 获取所有等级ObjectData列表
     */
    private QueryResult<IObjectData> queryGrades(ServiceContext serviceContext) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(Integer.MAX_VALUE);
        QueryResult<IObjectData> queryResult = serviceFacadeManager.findBySearchQuery(serviceContext.getRequestContext().getUser(), MemberGradeConstants.API_NAME, searchTemplateQuery);
        return queryResult;
    }

    /**
     * 获取等级ObjectData列表
     */
    private QueryResult<IObjectData> queryGradesByGradeIds(Set<String> gradeIds, ServiceContext serviceContext) {
        List<IFilter> filters = new ArrayList<>();
        IFilter idFileter = new Filter();
        idFileter.setOperator(Operator.EQ);
        idFileter.setFieldName(IObjectData.ID);
        idFileter.setFieldValues(Lists.newArrayList(gradeIds));
        filters.add(idFileter);
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setFilters(filters);
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(Integer.MAX_VALUE);
        QueryResult<IObjectData> queryResult = serviceFacadeManager.findBySearchQuery(serviceContext.getRequestContext().getUser(), MemberGradeConstants.API_NAME, searchTemplateQuery);
        return queryResult;
    }

    /**
     * 获取等级Entity列表
     */
    private List<GradeEntity> listGradesEntity(ServiceContext serviceContext) {
        QueryResult<IObjectData> iObjectDataQueryResult = queryGrades(serviceContext);
        List<GradeEntity> gradeEntityList = iObjectDataQueryResult.getData().stream().map(value -> GradeEntity.getGradeEntityByIObject(value, serviceContext.getUser())).collect(Collectors.toList());
        return gradeEntityList;
    }

    /**
     * 更新等级规则
     */
    private String updateGradeRule(GradeModel.Grade grade, RuleEngineContext ruleEngineContext) {
        //创建规则组
        RuleGroupArg ruleGroupArg = new RuleGroupArg();
        ruleGroupArg.setRuleCode(grade.getRuleCode());
        ruleGroupArg.setEntityId(MemberGrowthValueDetailConstants.API_NAME);
        ruleGroupArg.setRuleName(grade.getGradeName());
        ruleGroupArg.setRuleParse("( 1 )");
        ruleGroupArg.setStatus(FieldEnum.RuleGroupStatus.ACTIVE);
        RuleArg ruleArg = new RuleArg();
        ruleArg.setOperate(Operate.GT);
        ruleArg.setFieldValue(Lists.newArrayList(String.valueOf(0)));
        ruleArg.setFieldType(FieldType.NUMBER);
        ruleArg.setRuleOrder(1);
        ruleArg.setFieldName(MemberGrowthValueDetailConstants.Field.ChangeGrowthValue.apiName);
        ruleGroupArg.setRules(Lists.newArrayList(ruleArg));
        RuleGroupPojo ruleGroupPojo = new RuleGroupPojo();
        BeanUtils.copyProperties(ruleGroupArg, ruleGroupPojo);
        ruleGroupPojo.setSqlSelectFields(Lists.newArrayList(IObjectData.ID));
        String ruleCode = paasRuleManager.updateRuleGroup(ruleEngineContext, ruleGroupPojo);
        return ruleCode;
    }

    /**
     * 更新等级
     */
    public void updateGrade(List<Grade> grades, RuleEngineContext ruleEngineContext, ServiceContext serviceContext) {
        //保存数据
        for (int i = 0; i < grades.size(); i++) {
            GradeModel.Grade grade = grades.get(i);
            //保存等级规则
            String ruleCode = this.updateGradeRule(grade, ruleEngineContext);
            //更新到数据库
            GradeEntity gradeEntity = new GradeEntity();
            gradeEntity.setName(grade.getGradeName());
            gradeEntity.setGradeNumber(grade.getGradeNo());
            gradeEntity.setId(grade.getGradeId());
            IObjectDescribe iObjectDescribe = serviceFacadeManager.findObject(serviceContext.getRequestContext().getUser().getTenantId(), MemberGradeConstants.API_NAME);
            IObjectData iObjectData = gradeEntity.getObjectData(serviceContext.getRequestContext().getUser(), iObjectDescribe);
            IObjectData iObjectDataResult = serviceFacadeManager.updateObjectData(serviceContext.getRequestContext().getUser(), iObjectData);
            //更新新建配置
            GradeRuleExtConfig gradeRuleExtConfig = new GradeRuleExtConfig();
            gradeRuleExtConfig.setGradeTriggerType(GradeTriggerTypeEnum.GROWTH_VALUE_INC.getType());
            gradeRuleExtConfig.setGradeId(iObjectDataResult.getId());
            gradeRuleExtConfig.setRuleCode(grade.getRuleCode());
            gradeRuleExtConfig.setGrowthValueThreshold(grade.getGrowthValueThreshold());
            configServiceManager
                .updateTenantConfig(serviceContext.getRequestContext().getUser(), ConfigKeyUtil.getRuleActionKeyByRuleCode(ruleCode), GsonUtil.toJson(gradeRuleExtConfig), ConfigValueType.JSON);
            //更新等级权益关系
            GradeEntity gradeEntityByIObject = GradeEntity.getGradeEntityByIObject(iObjectDataResult, serviceContext.getUser());
            GradeModel.UpdateGradeEquitiesArg updateGradeEquitiesArg = new GradeModel.UpdateGradeEquitiesArg();
            updateGradeEquitiesArg.setGradeId(gradeEntityByIObject.getId());
            updateGradeEquitiesArg.setEquitiesIds(grade.getEquitiesIds());
            this.updateGradeEquities(updateGradeEquitiesArg, serviceContext);
        }
    }

    /**
     * 删除等级
     *
     * @throws TriggerBizException
     */
    public void deleteGrade(List<Grade> grades, RuleEngineContext ruleEngineContext, ServiceContext serviceContext) throws TriggerBizException {
        Set<String> ruleCodes = new HashSet<>();
        Set<String> gradeIds = new HashSet<>();
        if (grades == null) {
            return;
        }
        grades.forEach(val -> {
            ruleCodes.add(val.getRuleCode());
            gradeIds.add(val.getGradeId());
        });
        //删除旧规则
        this.deleteOldRuleGroup(ruleCodes, ruleEngineContext);
        //删除旧配置并删除事件引擎配置
        List<GradeRuleExtConfig> gradeRuleExtConfigList = this.deleteOldConfig(ruleCodes, serviceContext);
        //删除触发器
        this.deleteTrigger(serviceContext.getUser(), gradeRuleExtConfigList);
        //拉取数据等级
        QueryResult<IObjectData> gradeIObjectDatas = this.queryGradesByGradeIds(gradeIds, serviceContext);
        //删除等级权益关系
        this.bulkDeleteGradeEquities(gradeIObjectDatas, serviceContext);
        //批量删除等级数据
        //将数据标记为作废
        serviceFacadeManager.bulkInvalid(gradeIObjectDatas.getData(), serviceContext.getRequestContext().getUser());
        serviceFacadeManager.bulkDelete(gradeIObjectDatas.getData(), serviceContext.getRequestContext().getUser());
    }

    private void deleteTrigger(User user, List<GradeRuleExtConfig> gradeRuleExtConfigList) throws TriggerBizException {
        for (GradeRuleExtConfig gradeRuleExtConfig : gradeRuleExtConfigList) {
            Integer tenantId = Integer.parseInt(user.getTenantId());
            Integer userId = Integer.parseInt(user.getUserId());
            triggerServiceManager.delete(tenantId, userId, AppIdEnum.MEMBER.name(), SceneEnum.FS_CRM_MEMBER_GRADE.name(), gradeRuleExtConfig.getTriggerId());
        }
    }

    public GradeModel.SaveGradeResult saveAllGrade(GradeModel.SaveGradeArg saveGradeArg, ServiceContext serviceContext) throws TriggerBizException {
        RuleEngineContext ruleEngineContext = RuleRequestContextUtil.getRuleEngineContext(serviceContext.getUser(), SCENE_FS_CRM_MEMBER_GRADE);
        for (int i = 0; i < saveGradeArg.getGrades().size(); i++) {
            saveGradeArg.getGrades().get(i).setGradeNo(i + 1);
        }
        List<Grade> createGrade = new ArrayList<>();
        List<Grade> updateGrade = new ArrayList<>();
        saveGradeArg.getGrades().forEach(val -> {
            if (!Strings.isNullOrEmpty(val.getGradeId())) {
                updateGrade.add(val);
            } else {
                createGrade.add(val);
            }
        });
        this.deleteGrade(saveGradeArg.getToDeleteGrades(), ruleEngineContext, serviceContext);
        this.updateGrade(updateGrade, ruleEngineContext, serviceContext);
        gradeManager.createGrade(createGrade, ruleEngineContext, serviceContext);
        //拉取数据
        ListGradeResult listGradeResult = this.listGrade(new ListGradeArg(), serviceContext);
        GradeModel.SaveGradeResult saveGradeResult = new GradeModel.SaveGradeResult();
        saveGradeResult.setGrades(listGradeResult.getGrades());
        return saveGradeResult;
    }

    @Override
    public GradeModel.CreateGradeEquitiesResult createGradeEquities(GradeModel.CreateGradeEquitiesArg createGradeEquitiesArg, ServiceContext serviceContext) {
        return gradeManager.createGradeEquities(createGradeEquitiesArg, serviceContext);
    }

    @Override
    public UpdateGradeEquitiesResult updateGradeEquities(UpdateGradeEquitiesArg updateGradeEquitiesArg, ServiceContext serviceContext) {
        GradeModel.GetGradeEquitiesArg getGradeEquitiesArg = new GradeModel.GetGradeEquitiesArg();
        getGradeEquitiesArg.setGradeId(updateGradeEquitiesArg.getGradeId());
        GradeModel.GetGradeEquitiesResult getGradeEquitiesResult = this.getEquitiesByGrade(getGradeEquitiesArg, serviceContext);
        GradeModel.DeleteGradeEquitiesArg deleteGradeEquitiesArg = new GradeModel.DeleteGradeEquitiesArg();
        Set<String> gradeEquitiesIds = getGradeEquitiesResult.getGradeEquitiesList().stream().map(val -> val.getId()).collect(Collectors.toSet());
        deleteGradeEquitiesArg.setGradeEquitiesIds(gradeEquitiesIds);
        this.deleteGradeEquities(deleteGradeEquitiesArg, serviceContext);
        GradeModel.CreateGradeEquitiesArg createGradeEquitiesArg = new GradeModel.CreateGradeEquitiesArg();
        createGradeEquitiesArg.setEquitiesIds(updateGradeEquitiesArg.getEquitiesIds());
        createGradeEquitiesArg.setGradeId(updateGradeEquitiesArg.getGradeId());
        this.createGradeEquities(createGradeEquitiesArg, serviceContext);
        return new UpdateGradeEquitiesResult();
    }

    @Override
    public GradeModel.DeleteGradeEquitiesResult deleteGradeEquities(GradeModel.DeleteGradeEquitiesArg deleteGradeEquitiesArg, ServiceContext serviceContext) {
        List<IObjectData> iObjectDataList = serviceFacadeManager
            .findObjectDataByIds(serviceContext.getTenantId(), deleteGradeEquitiesArg.getGradeEquitiesIds().stream().collect(Collectors.toList()), MemberGradeEquitiesRuleConstants.API_NAME);
        serviceFacadeManager.bulkInvalid(iObjectDataList, serviceContext.getRequestContext().getUser());
        serviceFacadeManager.bulkDelete(iObjectDataList, serviceContext.getRequestContext().getUser());
        return new GradeModel.DeleteGradeEquitiesResult();
    }

    @Override
    public GradeModel.GetGradeEquitiesResult getEquitiesByGrade(GradeModel.GetGradeEquitiesArg getGradeEquitiesArg, ServiceContext serviceContext) {
        List<IFilter> filters = new ArrayList<>();
        IFilter idFileter = new Filter();
        idFileter.setOperator(Operator.EQ);
        idFileter.setFieldName(Field.GradeId.apiName);
        idFileter.setFieldValues(Lists.newArrayList(getGradeEquitiesArg.getGradeId()));
        filters.add(idFileter);
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setFilters(filters);
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(Integer.MAX_VALUE);
        QueryResult<IObjectData> queryResult = serviceFacadeManager.findBySearchQuery(serviceContext.getRequestContext().getUser(), MemberGradeEquitiesRuleConstants.API_NAME, searchTemplateQuery);
        List<GradeEquitiesEntity> equitiesEntities = queryResult.getData().stream().map(value -> GradeEquitiesEntity.getGradeEquitiesEntityByIObject(value, serviceContext.getUser()))
            .collect(Collectors.toList());
        GradeModel.GetGradeEquitiesResult getGradeEquitiesResult = new GradeModel.GetGradeEquitiesResult();
        List<GradeModel.GradeEquities> gradeEquitiesList = new ArrayList<>();
        equitiesEntities.forEach(value -> {
            EquitiesModel.GetEquitiesArg getEquitiesArg = new EquitiesModel.GetEquitiesArg();
            getEquitiesArg.setId(value.getEquities());
            //获取权益信息
            EquitiesModel.GetEquitiesResult getEquitiesResult = this.getEquities(getEquitiesArg, serviceContext);
            GradeModel.GradeEquities gradeEquities = new GradeModel.GradeEquities();
            BeanUtils.copyProperties(value, gradeEquities);
            Equities equities = new Equities();
            BeanUtils.copyProperties(getEquitiesResult, equities);
            gradeEquities.setEquities(equities);
            gradeEquitiesList.add(gradeEquities);
        });
        getGradeEquitiesResult.setGradeEquitiesList(gradeEquitiesList);
        return getGradeEquitiesResult;
    }

    @Override
    public ListGradeResult listGrade(ListGradeArg listGradeArg, ServiceContext serviceContext) {
        //拉取所有规则组
        RuleEngineContext ruleEngineContext = RuleRequestContextUtil.getRuleEngineContext(serviceContext.getUser(), SCENE_FS_CRM_MEMBER_GRADE);
        ListRuleGroup listRuleGroup = ListRuleGroup.builder().entityIds(Sets.newHashSet(MemberGrowthValueDetailConstants.API_NAME)).status(FieldEnum.RuleGroupStatus.ACTIVE).build();
        listRuleGroup.setStatus(FieldEnum.RuleGroupStatus.ACTIVE);
        Map<String, RuleGroupPojo> ruleGroupPojoMap = paasRuleManager.listRuleGroup(ruleEngineContext, listRuleGroup).stream().collect(Collectors.toMap(RuleGroupPojo::getRuleCode, value -> value));
        //拉取数据等级
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setOffset(0);
      //  searchTemplateQuery.setLimit(Integer.MAX_VALUE);
        searchTemplateQuery.setLimit(2000);
        QueryResult<IObjectData> queryResult = serviceFacadeManager.findBySearchQuery(serviceContext.getRequestContext().getUser(), MemberGradeConstants.API_NAME, searchTemplateQuery);
        Map<String, GradeEntity> gradeEntityMap = new HashMap<>();
        queryResult.getData().stream().forEach(value -> {
            GradeEntity gradeEntity = GradeEntity.getGradeEntityByIObject(value, serviceContext.getRequestContext().getUser());
            gradeEntityMap.put(gradeEntity.getId(), gradeEntity);
        });
        //拉取配置
        Map<String, GradeRuleExtConfig> gradeRuleExtConfigMap = new HashMap<>();
        ruleGroupPojoMap.values().stream().forEach(value -> {
            String configJson = configServiceManager.findTenantConfig(serviceContext.getRequestContext().getUser(), ConfigKeyUtil.getRuleActionKeyByRuleCode(value.getRuleCode()));
            GradeRuleExtConfig gradeRuleExtConfig = GsonUtil.fromJson(configJson, GradeRuleExtConfig.class);
            if (gradeRuleExtConfig != null) {
                gradeRuleExtConfigMap.put(ConfigKeyUtil.getRuleActionKeyByRuleCode(value.getRuleCode()), gradeRuleExtConfig);
            }
        });

        List<GradeModel.Grade> grades = new ArrayList<>();
        ruleGroupPojoMap.entrySet().forEach((value) -> {
            RuleGroupPojo ruleGroupPojo = value.getValue();
            GradeRuleExtConfig gradeRuleExtConfig = gradeRuleExtConfigMap.get(ConfigKeyUtil.getRuleActionKeyByRuleCode(ruleGroupPojo.getRuleCode()));
            Optional.ofNullable(gradeRuleExtConfig).ifPresent(val -> {
                GradeEntity gradeEntity = gradeEntityMap.get(gradeRuleExtConfig.getGradeId());
                GradeModel.Grade grade = new GradeModel.Grade();
                grade.setGradeId(gradeEntity.getId());
                grade.setGradeName(gradeEntity.getName());
                grade.setGradeNo(gradeEntity.getGradeNumber());
                grade.setGrowthValueThreshold(gradeRuleExtConfig.getGrowthValueThreshold());
                grade.setRuleCode(gradeRuleExtConfig.getRuleCode());
                GradeModel.GetGradeEquitiesArg getGradeEquitiesArg = new GradeModel.GetGradeEquitiesArg();
                getGradeEquitiesArg.setGradeId(gradeEntity.getId());
                //通过等级获取权益
                GradeModel.GetGradeEquitiesResult getGradeEquitiesResult = this.getEquitiesByGrade(getGradeEquitiesArg, serviceContext);
                grade.setGradeEquitiesList(getGradeEquitiesResult.getGradeEquitiesList());
                grades.add(grade);
            });
        });
        ListGradeResult listGradeResult = new ListGradeResult();
        listGradeResult.setGrades(grades);
        return listGradeResult;
    }
}
