package com.facishare.crm.member.enums;

public enum SceneEnum {


  /**
   * 积分相关规则动作
   */
  FS_CRM_MEMBER_INTEGRAL,
  /**
   * 成长值相关的规则动作
   */
  FS_CRM_MEMBER_GROWTH_VALUE,
  /**
   * 等级相关的规则动作
   */
  FS_CRM_MEMBER_GRADE;
  public static boolean isValidRuleScene(String type){
    for (SceneEnum value : SceneEnum.values()) {
      if(value.name().equals(type)){
        return true;
      }
    }
    return false;
  }
}
