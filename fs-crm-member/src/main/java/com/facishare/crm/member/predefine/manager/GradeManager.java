package com.facishare.crm.member.predefine.manager;

import com.facishare.appserver.trigger.api.dto.OperateCode;
import com.facishare.appserver.trigger.api.exception.TriggerBizException;
import com.facishare.crm.member.arg.RuleArg;
import com.facishare.crm.member.arg.RuleGroupArg;
import com.facishare.crm.member.common.ConfigKeyUtil;
import com.facishare.crm.member.common.GsonUtil;
import com.facishare.crm.member.constants.GradeTriggerTypeEnum;
import com.facishare.crm.member.constants.MemberGradeConstants;
import com.facishare.crm.member.constants.MemberGradeEquitiesRuleConstants;
import com.facishare.crm.member.constants.MemberGrowthValueDetailConstants;
import com.facishare.crm.member.dto.ruleconfig.GradeRuleExtConfig;
import com.facishare.crm.member.entity.GradeEntity;
import com.facishare.crm.member.entity.GradeEquitiesEntity;
import com.facishare.crm.member.enums.RuleOperateMapEnum;
import com.facishare.crm.member.enums.SceneEnum;
import com.facishare.crm.member.vo.GradeModel;
import com.facishare.crm.member.vo.GradeModel.CreateGradeEquitiesArg;
import com.facishare.crm.member.vo.GradeModel.Grade;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.rule.common.RuleEngineContext;
import com.facishare.paas.rule.common.constant.FieldEnum;
import com.facishare.paas.rule.common.constant.FieldType;
import com.facishare.paas.rule.pojo.RuleGroupPojo;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class GradeManager {
    @Autowired
    private ConfigServiceManager configServiceManager;
    @Autowired
    private ServiceFacadeManager serviceFacadeManager;
    @Autowired
    private PaasRuleManager paasRuleManager;
    @Autowired
    private TriggerServiceManager triggerServiceManager;

    /**
     * 创建等级
     */
    public void createGrade(List<Grade> grades, RuleEngineContext ruleEngineContext, ServiceContext serviceContext) throws TriggerBizException {
        //保存数据
        for (Grade grade : grades) {
            //保存等级规则
            String ruleCode = this.saveGradeRule(grade, ruleEngineContext);
            //将规则放入规则引擎
            String triggerId = this.deployTrigger(ruleCode, ruleEngineContext, serviceContext);
            //保存到数据库
            GradeEntity gradeEntity = new GradeEntity();
            gradeEntity.setName(grade.getGradeName());
            gradeEntity.setGradeNumber(grade.getGradeNo());
            IObjectDescribe iObjectDescribe = serviceFacadeManager.findObject(serviceContext.getRequestContext().getUser().getTenantId(), MemberGradeConstants.API_NAME);
            IObjectData iObjectData = gradeEntity.getObjectData(serviceContext.getRequestContext().getUser(), iObjectDescribe);
            IObjectData iObjectDataResult = serviceFacadeManager.saveObjectData(serviceContext.getRequestContext().getUser(), iObjectData);
            //保存新建配置
            GradeRuleExtConfig gradeRuleExtConfig = new GradeRuleExtConfig();
            gradeRuleExtConfig.setTriggerId(triggerId);
            gradeRuleExtConfig.setRuleCode(ruleCode);
            gradeRuleExtConfig.setGradeId(iObjectDataResult.getId());
            gradeRuleExtConfig.setGrowthValueThreshold(grade.getGrowthValueThreshold());
            gradeRuleExtConfig.setGradeTriggerType(GradeTriggerTypeEnum.GROWTH_VALUE_INC.getType());
            configServiceManager
                .createTenantConfig(serviceContext.getRequestContext().getUser(), ConfigKeyUtil.getRuleActionKeyByRuleCode(ruleCode), GsonUtil.toJson(gradeRuleExtConfig), ConfigValueType.JSON);
            //保存等级权益关系
            GradeEntity gradeEntityByIObject = GradeEntity.getGradeEntityByIObject(iObjectDataResult, serviceContext.getUser());
            CreateGradeEquitiesArg createGradeEquitiesArg = new CreateGradeEquitiesArg();
            createGradeEquitiesArg.setGradeId(gradeEntityByIObject.getId());
            createGradeEquitiesArg.setEquitiesIds(grade.getEquitiesIds());
            this.createGradeEquities(createGradeEquitiesArg, serviceContext);
        }
    }

    private String deployTrigger(String ruleCode, RuleEngineContext ruleEngineContext, ServiceContext serviceContext) throws TriggerBizException {
        return triggerServiceManager
            .deploy(Integer.valueOf(serviceContext.getUser().getTenantId()), Integer.valueOf(serviceContext.getUser().getUserId()), ruleEngineContext.getAppId(), SceneEnum.FS_CRM_MEMBER_GRADE.name(),
                MemberGrowthValueDetailConstants.API_NAME, OperateCode.INSERT, ruleCode);
    }

    /**
     * 保存等级规则
     */
    private String saveGradeRule(GradeModel.Grade grade, RuleEngineContext ruleEngineContext) {
        //创建规则组
        RuleGroupArg ruleGroupArg = new RuleGroupArg();
        ruleGroupArg.setEntityId(MemberGrowthValueDetailConstants.API_NAME);
        ruleGroupArg.setRuleName(grade.getGradeName());
        ruleGroupArg.setRuleParse("( 1 )");
        ruleGroupArg.setStatus(FieldEnum.RuleGroupStatus.ACTIVE);
        RuleArg ruleArg = new RuleArg();
        ruleArg.setOperate(RuleOperateMapEnum.SYMBOL_TO_CODE_OPERATOR_MAP.getSymbol(">"));
        ruleArg.setFieldValue(Lists.newArrayList(String.valueOf(0)));
        ruleArg.setFieldType(FieldType.NUMBER);
        ruleArg.setRuleOrder(1);
        ruleArg.setFieldName(MemberGrowthValueDetailConstants.Field.ChangeGrowthValue.apiName);
        ruleGroupArg.setRules(Lists.newArrayList(ruleArg));
        RuleGroupPojo ruleGroupPojo = new RuleGroupPojo();
        BeanUtils.copyProperties(ruleGroupArg, ruleGroupPojo);
        ruleGroupPojo.setSqlSelectFields(Lists.newArrayList(IObjectData.ID));
        return paasRuleManager.createRuleGroup(ruleEngineContext, ruleGroupPojo);
    }

    public GradeModel.CreateGradeEquitiesResult createGradeEquities(GradeModel.CreateGradeEquitiesArg createGradeEquitiesArg, ServiceContext serviceContext) {
        Optional.ofNullable(createGradeEquitiesArg.getEquitiesIds()).ifPresent(equitiesIds -> {
            equitiesIds.forEach(value -> {
                GradeEquitiesEntity gradeEquitiesEntity = new GradeEquitiesEntity();
                gradeEquitiesEntity.setEquities(value);
                gradeEquitiesEntity.setGrade(createGradeEquitiesArg.getGradeId());
                gradeEquitiesEntity.setName(UUID.randomUUID().toString());
                IObjectDescribe iObjectDescribe = serviceFacadeManager.findObject(serviceContext.getRequestContext().getUser().getTenantId(), MemberGradeEquitiesRuleConstants.API_NAME);
                IObjectData iObjectData = gradeEquitiesEntity.getObjectData(serviceContext.getUser(), iObjectDescribe);
                serviceFacadeManager.saveObjectData(serviceContext.getRequestContext().getUser(), iObjectData);
            });
        });
        return new GradeModel.CreateGradeEquitiesResult();
    }
}
