package com.facishare.crm.member.dto;

import com.facishare.crm.member.arg.ContextArg;
import com.facishare.crm.member.arg.RuleGroupArg;
import com.facishare.paas.rule.common.RuleEngineContext;
import com.facishare.paas.rule.pojo.RuleGroupPojo;
import lombok.Builder;
import lombok.Data;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 28/11/2018
 */
@Data
@Builder
public class CreateRuleGroupDto {
    private RuleEngineContext context;
    private RuleGroupPojo ruleGroup;

}
