package com.facishare.crm.member.constants;

/**
 * @Auther: dzb
 * @Date: 2018/11/14
 * @Description:会员权益
 * @IgnoreI18nFile
 */
public interface  MemberEquitiesConstants {
    //固定格式
    String API_NAME = "MemberEquitiesObj";//对象apiName
    String DISPLAY_NAME = "会员权益";//对象名称
    String DEFAULT_LAYOUT_API_NAME = "MemberEquities__default_layout__c";
    String DEFAULT_LAYOUT_DISPLAY_NAME = "默认布局";
    String LIST_LAYOUT_API_NAME = "MemberEquities_list_layout__c";
    String LIST_LAYOUT_DISPLAY_NAME = "移动端默认列表页";
    String STORE_TABLE_NAME = "member_equities";//映射数据库名
    int ICON_INDEX = 10;

    /**
     * label 代表标签名 apiName 代表字段名称
     **/
    enum Field {
        Name("name", "编号"),
        Equities("equities", "权益"),
        EquitiesRemark("equities_remark", "权益说明");

        Field(String apiName, String label) {
            this.apiName = apiName;
            this.label = label;
        }

        public String apiName;
        public String label;
    }
}
