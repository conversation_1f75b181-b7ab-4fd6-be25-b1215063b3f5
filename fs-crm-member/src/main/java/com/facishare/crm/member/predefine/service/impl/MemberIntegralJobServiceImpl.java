package com.facishare.crm.member.predefine.service.impl;

import com.facishare.crmcommon.constants.SystemConstants;
import com.facishare.crmcommon.constants.SystemConstants.LifeStatus;
import com.facishare.crm.member.constants.MemberGrowthValueDetailConstants;
import com.facishare.crm.member.constants.MemberIntegralDetailConstants;
import com.facishare.crm.member.enums.IntegralStatusEnum;
import com.facishare.crm.member.enums.MemberTypeEnum;
import com.facishare.crm.member.enums.MemberTypeEnum.EnableResult;
import com.facishare.crm.member.enums.MemberTypeEnum.MemberStatusEnum;
import com.facishare.crm.member.enums.ScheduledJobTypeEnum;
import com.facishare.crm.member.predefine.service.MemberIntegralJobService;
import com.facishare.crm.member.util.DateUtil;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.fxiaoke.paas.gnomon.api.NomonProducer;
import com.fxiaoke.paas.gnomon.api.entity.NomonDeleteMessage;
import com.google.common.collect.Lists;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Auther: dzb
 * @Date: 2018/11/22
 * @Description:
 * @IgnoreI18nFile
 */
@Service
@Slf4j
public class MemberIntegralJobServiceImpl implements MemberIntegralJobService {
    private static final String INTEGRAL_VALUE_CHANGE_CAUSE = "积分过期失效";
    private static final String TRIGGER_RULES = "清除过期积分";
    @Autowired
    private ObjectDataServiceImpl objectDataService;
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private NomonProducer nomonProducer;
    @Autowired
    private DescribeLogicService describeLogicService;

    @Override
    public MemberTypeEnum.EnableResult checkMemberIntegralLoseEfficacy(ServiceContext serviceContext) throws MetadataServiceException {
        EnableResult enableResult = new EnableResult();
        User user = serviceContext.getUser();
        long benginTime = System.currentTimeMillis();

        try {
            Set<String> apiNames = Sets.newHashSet(MemberIntegralDetailConstants.API_NAME);
            Map<String, IObjectDescribe> describeMap = describeLogicService.findObjects(user.getTenantId(), apiNames);
            if (describeMap == null || !describeMap.containsKey(MemberGrowthValueDetailConstants.API_NAME)) {
                return enableResult;
            }

            //根据租户查询会员积分失效数据  expire_time =今日日期"yyyy-MM-dd"
            String sql = "SELECT *  from member_integral_detail  WHERE tenant_id='%s'  and  expire_time ='%s'  and  life_status ='%s' and status=1 ";
            sql = String.format(sql, user.getTenantId(), DateUtil.getNowDateTimestamp(), LifeStatus.Normal.value);
            QueryResult<IObjectData> queryResult = objectDataService.findBySql(sql, user.getTenantId(), MemberIntegralDetailConstants.API_NAME);
            if (!queryResult.getData().isEmpty() && queryResult.getData() != null) {
                List<IObjectData> list = queryResult.getData();
                list.forEach(entity -> {
                    try {
                        //过期处理
                        createExpireObjectData(user, entity);
                        entity.set(MemberIntegralDetailConstants.Field.Status.apiName, IntegralStatusEnum.Invalid.getCode());//2 过期
                        entity.setOwner(Lists.newArrayList(user.getUserId()));
                        entity.setLastModifiedTime(DateUtil.getTimestampOfDateTime(LocalDateTime.now()));
                        entity.setLastModifiedBy(user.getUserId());
                        String id = entity.get("id", String.class);
                        if (id == null) {
                            id = entity.getId();
                        }
                        entity.setId(id);
                        serviceFacade.updateObjectData(user, entity);
                    } catch (Exception e) {
                        log.warn(" checkMemberIntegralLoseEfficacy   entity,{}, error message ,{}", entity, e);
                    }
                });
            }
        }catch (Exception e){
            log.info("checkMemberIntegralLoseEfficacy failed serviceContext:{} e:", serviceContext, e);
        }

        long endTime = System.currentTimeMillis();
        enableResult.setEnableStatus(MemberStatusEnum.CORN_JOB.status);
        enableResult.setMessage("租户" + user.getTenantId() + MemberStatusEnum.CORN_JOB.message + ",执行任务时间:" + (endTime - benginTime));
        return enableResult;
    }

    @Override
    public EnableResult removeMemberJob(ServiceContext serviceContext) throws MetadataServiceException {
        NomonDeleteMessage nomonMessage = new NomonDeleteMessage();
        nomonMessage.setTenantId(serviceContext.getTenantId());
        nomonMessage.setDataId(serviceContext.getTenantId());
        nomonMessage.setTaskId(String.valueOf(ScheduledJobTypeEnum.MemberIntegralJob.getType()));
        nomonMessage.setBiz(ScheduledJobTypeEnum.MemberIntegralJob.getBizName());
        nomonProducer.send(nomonMessage);
        return new EnableResult();
    }

    private void createExpireObjectData(User user, IObjectData entity) {
        IObjectData objectData = new ObjectData();
        objectData.setDescribeApiName(MemberIntegralDetailConstants.API_NAME);
        objectData.setDescribeId(entity.get(IObjectData.DESCRIBE_ID, String.class));
        objectData.setOwner(Lists.newArrayList(user.getUserId()));
        objectData.setPackage("CRM");
        objectData.setRecordType(IObjectData.RECORD_TYPE_DEFAULT);
        objectData.setCreatedBy(user.getUserId());
        objectData.setDeleted(false);
        objectData.setTenantId(user.getTenantId());
        objectData.setName(entity.get(MemberIntegralDetailConstants.Field.Name.apiName, String.class));
        objectData.setVersion(entity.get(IObjectData.VERSION, Integer.class));
        objectData.set(SystemConstants.Field.LifeStatus.apiName, SystemConstants.LifeStatus.Normal.value);
        objectData.set(MemberIntegralDetailConstants.Field.MemberId.apiName, entity.get(MemberIntegralDetailConstants.Field.MemberId.apiName));
        objectData.set(MemberIntegralDetailConstants.Field.TriggerRules.apiName, TRIGGER_RULES);
        objectData.set(MemberIntegralDetailConstants.Field.IntegralValueChangeCause.apiName, INTEGRAL_VALUE_CHANGE_CAUSE);
        Long value = -1 * Long.valueOf(entity.get(MemberIntegralDetailConstants.Field.ChangeIntegralValue.apiName, String.class));
        objectData.set(MemberIntegralDetailConstants.Field.ChangeIntegralValue.apiName, value);
        objectData.set(MemberIntegralDetailConstants.Field.Status.apiName, entity.get(MemberIntegralDetailConstants.Field.Status.apiName, String.class));
        objectData.set(MemberIntegralDetailConstants.Field.ChangeTime.apiName, DateUtil.getTimestampOfDateTime(LocalDateTime.now()));
        serviceFacade.saveObjectData(user, objectData);
    }
}
