package com.facishare.crm.member.result;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.io.Serializable;
import lombok.Data;

@Data
public class Result implements Serializable {
    /**
     * {@link SHErrorCode}
     */
    private int errCode;
    private String errMsg;

    public Result() {
    }

    public Result(SHErrorCode code) {
        this.errCode = code.getErrorCode();
        this.errMsg = code.getErrorMessage();
    }

    public Result(int errCode, String errMsg) {
        this.errCode = errCode;
        this.errMsg = errMsg;
    }

    public static Result newResult(int errCode, String errMsg){
        return new Result(errCode,errMsg);
    }
    public static Result newResult(SHErrorCode code){
        return new Result(code);
    }

    @JsonIgnore
    public boolean isSuccess() {
        return errCode == 0;
    }
}
