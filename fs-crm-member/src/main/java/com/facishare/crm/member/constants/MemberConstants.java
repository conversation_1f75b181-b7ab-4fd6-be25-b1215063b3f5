package com.facishare.crm.member.constants;

import lombok.Getter;
import lombok.ToString;

/**
 * @Auther: dzb
 * @Date: 2018/11/14
 * @Description: 会员
 *@IgnoreI18nFile
 */
public interface MemberConstants {
    //固定格式
    String APP_ID = "FS_CRM_MEMBER";
    String API_NAME = "MemberObj";//对象apiName
    String DISPLAY_NAME = "会员";//对象名称
    String DEFAULT_LAYOUT_API_NAME = "Member_customer_default_layout__c";
    String DEFAULT_LAYOUT_DISPLAY_NAME = "默认客户布局";
    String LIST_LAYOUT_API_NAME = "Member_list_layout__c";
    String LIST_LAYOUT_DISPLAY_NAME = "移动端默认列表页";
    String STORE_TABLE_NAME = "member";//映射数据库名
    int ICON_INDEX = 10;
    /**
     * label 代表标签名 apiName 代表字段名称
     **/
    enum Field {
        Name("name", "名称"),

        /** New */
        CardNo("card_no", "会员卡号"),
        Phone("phone", "手机"),
        Email("email", "邮件"),
        AddSource("add_source", "来源"),
        gender("gender", "性别"),
        birthday("birthday", "生日"),
        avatar("avatar", "头像"),
        areaLocation("area_location", "地区定位"),
        country("country", "国家"),
        province("province", "省"),
        city("city", "市"),
        district("district", "区"),
        address("address", "地址"),
        location("location", "定位"),

        CustomerId("customer_id", "客户","target_related_list_account__c","会员"),
        GradeId("grade_id", "等级名称","target_related_list_member_grade__c","会员"),
        GrowthValue("growth_value", "成长值"),
        IntegralValue("integral_value", "积分");

        Field(String apiName, String label) {
            this.apiName = apiName;
            this.label = label;
        }

        Field(String apiName, String label, String targetRelatedListName, String targetRelatedListLabel) {
            this.apiName = apiName;
            this.label = label;
            this.targetRelatedListName = targetRelatedListName;
            this.targetRelatedListLabel = targetRelatedListLabel;
        }

        public String getApiName() {
            return apiName;
        }

        public void setApiName(String apiName) {
            this.apiName = apiName;
        }

        public String apiName;
        public String label;
        public String targetRelatedListName;
        public String targetRelatedListLabel;

    }

    @Getter
    @ToString
    enum SourceOptionEnum{
        WECHAT("wechat", "微信"),
        STORE("store", "门店"),
        OTHER("other", "其他"),
        ;
        private String apiName;
        private String label;

        SourceOptionEnum(String apiName, String label) {
            this.apiName = apiName;
            this.label = label;
        }
    }

    @Getter
    @ToString
    enum GenderOptionEnum{
        MAN("male", "男"),
        FEMALE("female", "女");
        private String apiName;
        private String label;

        GenderOptionEnum(String apiName, String label) {
            this.apiName = apiName;
            this.label = label;
        }
    }
}
