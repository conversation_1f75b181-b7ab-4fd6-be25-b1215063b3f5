package com.facishare.crm.member.entity;

import com.facishare.crm.member.constants.MemberGradeConstants;
import com.facishare.paas.appframework.core.model.User;
import lombok.Data;
import org.aspectj.lang.annotation.DeclareAnnotation;

import java.util.List;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 30/11/2018
 */
@Data
public class BaseEntity {
    /**
     * User 包含tenantId
     */
   private User creator;
   private Boolean isDeleted;
   private Object vertion;
   private Integer lifeStatus;
   private List<String> owner;
   private String describeId;
}
