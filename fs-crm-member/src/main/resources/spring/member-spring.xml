<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns="http://www.springframework.org/schema/beans"
       xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/aop
         http://www.springframework.org/schema/aop/spring-aop.xsd
         http://www.springframework.org/schema/beans
         http://www.springframework.org/schema/beans/spring-beans.xsd">
    <import resource="member_consumer.xml"/>
    <bean id="memberAccessLog" class="com.facishare.crm.member.interceptor.LogInterceptor">
        <constructor-arg type="java.lang.String" value="memberAccess"/>
        <constructor-arg type="java.lang.String" value=""/>
    </bean>
    <import resource="classpath:otherrest/otherrest.xml"/>

    <bean id="nomonProducer" class="com.fxiaoke.paas.gnomon.api.NomonProducer"/>
    <aop:config>
        <aop:aspect id="memberLogMonitor" ref="memberAccessLog" order="1">
            <aop:pointcut id="memberLogAround"
                expression="(execution(* com.facishare.crm.member.predefine.*.*.*(..)))"/>
            <aop:around pointcut-ref="memberLogAround" method="around"/>
        </aop:aspect>
    </aop:config>
</beans>
