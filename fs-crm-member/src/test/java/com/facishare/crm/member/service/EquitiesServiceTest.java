package com.facishare.crm.member.service;

import com.facishare.crm.member.base.BaseServiceTest;
import com.facishare.crm.member.constants.MemberGradeConstants;

import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 06/12/2018
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/applicationContext.xml")
public class EquitiesServiceTest extends BaseServiceTest {
    private static final Map<String, String> OPERATOR_MAP = new HashMap<>();
    private static final String APP_ID = "CRM";
    private static final String EMPLOYEE_ID = "1000";
    @Before
    public void initUser() {
        tenantId = "61250";//2
        fsUserId = "1001";

    }


    static {
        System.setProperty("spring.profiles.active", "ceshi113");
    }

    public EquitiesServiceTest() {
        super(MemberGradeConstants.API_NAME);
    }



}
