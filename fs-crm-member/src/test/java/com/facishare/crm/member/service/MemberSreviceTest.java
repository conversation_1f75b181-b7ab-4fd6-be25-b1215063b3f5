package com.facishare.crm.member.service;

import com.facishare.crm.member.base.BaseServiceTest;
import com.facishare.crm.member.constants.MemberConstants;
import com.facishare.crm.member.constants.MemberEquitiesConstants;
import com.facishare.crm.member.constants.MemberGradeConstants;
import com.facishare.crm.member.constants.MemberGradeEquitiesRuleConstants;
import com.facishare.crm.member.constants.MemberGrowthValueDetailConstants;
import com.facishare.crm.member.constants.MemberIntegralDetailConstants;
import com.facishare.crm.member.predefine.service.GradeService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Sets;
import java.util.Map;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2018/12/14.
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/applicationContext.xml")
public class MemberSreviceTest extends BaseServiceTest {
    @Autowired
    ServiceFacade serviceFacade;
    @Autowired
    private DescribeLogicService describeLogicService;


    public MemberSreviceTest() {
        super(MemberConstants.API_NAME);
    }

    static {
        System.setProperty("spring.profiles.active", "ceshi113");
    }

    @Before
    public void initUser() {
        tenantId = "71601";//27
        fsUserId = "1000";

    }
    @Test
   public void getMember(){
       IObjectData memberData = serviceFacade.findObjectData(this.user,"5c1241355ceb4900016ab22a", MemberConstants.API_NAME );
        //serviceFacade.findObjectData(this.user, "ca6e35edafc248f7817c638f6f56826a", "SalesOrderObj");
       System.out.println(memberData);
    }


  @Test
  public void TestApiName (){
      Set<String> apiNames = Sets
          .newHashSet(MemberGradeConstants.API_NAME, MemberEquitiesConstants.API_NAME, MemberConstants.API_NAME, MemberIntegralDetailConstants.API_NAME, MemberGrowthValueDetailConstants.API_NAME,
              MemberGradeEquitiesRuleConstants.API_NAME);
      Map<String, IObjectDescribe> describeMap = describeLogicService.findObjects(user.getTenantId(), apiNames);
      System.out.println("状态:====:"+!describeMap.containsKey(MemberGradeConstants.API_NAME));
  }

}
