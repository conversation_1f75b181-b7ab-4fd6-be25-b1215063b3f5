package com.facishare.crm.member.service;

import com.facishare.converter.EIEAConverter;
import com.facishare.crm.member.base.BaseServiceTest;
import com.facishare.crm.member.common.ConfigKeyUtil;
import com.facishare.crm.member.constants.MemberConstants;
import com.facishare.crm.member.constants.MemberEquitiesConstants;
import com.facishare.crm.member.constants.MemberGradeConstants;
import com.facishare.crm.member.constants.MemberGradeEquitiesRuleConstants;
import com.facishare.crm.member.constants.MemberGrowthValueDetailConstants;
import com.facishare.crm.member.constants.MemberIntegralDetailConstants;
import com.facishare.crm.member.dto.DeleteRuleGroup;
import com.facishare.crm.member.dto.ListRuleGroup;
import com.facishare.crm.member.enums.AppIdEnum;
import com.facishare.crm.member.enums.MemberTypeEnum;
import com.facishare.crm.member.enums.MemberTypeEnum.MemberStatusEnum;
import com.facishare.crm.member.enums.SceneEnum;
import com.facishare.crm.member.predefine.manager.InitializeMemberManager;
import com.facishare.crm.member.predefine.manager.MemberConfigManager;
import com.facishare.crm.member.predefine.manager.PaasRuleManager;
import com.facishare.crm.member.predefine.privilege.MemberEquitiesFunctionPrivilegeProvider;
import com.facishare.crm.member.predefine.privilege.MemberFunctionPrivilegeProvider;
import com.facishare.crm.member.predefine.privilege.MemberGradeEquitiesRuleFunctionPrivilegeProvider;
import com.facishare.crm.member.predefine.privilege.MemberGradeFunctionPrivilegeProvider;
import com.facishare.crm.member.predefine.privilege.MemberGrowthValueDetailFunctionPrivilegeProvider;
import com.facishare.crm.member.predefine.privilege.MemberIntegralDetailFunctionPrivilegeProvider;
import com.facishare.crm.member.predefine.service.IntegralService;
import com.facishare.crm.member.predefine.service.MemberManagementService;
import com.facishare.crm.member.vo.IntegralModel;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardController;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeProxy;
import com.facishare.paas.appframework.privilege.dto.AuthContext;
import com.facishare.paas.appframework.privilege.dto.DelFuncCodeRoles;
import com.facishare.paas.appframework.privilege.model.DefaultFunctionPrivilegeProvider;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.rule.common.RuleEngineContext;
import com.facishare.paas.rule.pojo.RuleGroupPojo;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * @Auther: dzb
 * @Date: 2018/11/22
 * @Description:
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/applicationContext.xml")
public class MemberManagementTest {
    @Autowired
    private InitializeMemberManager initializeMemberManager;
    @Autowired
    private MemberConfigManager memberConfigManager;
    @Autowired
    private ConfigService configService;
    private String memberStatusKey = "member_status";
    @Autowired
    private ObjectDataServiceImpl objectDataService;
    @Autowired
    private PaasRuleManager paasRuleManager;
    @Autowired
    private FunctionPrivilegeProxy functionPrivilegeProxy;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private IntegralService integralService;

    static {
       System.setProperty("spring.profiles.active", "ceshi113");
    }

    @Before
    public void initUser() {
        String invirentment = System.getProperty("spring.profiles.active");
        if (invirentment.equals("ceshi113")) {
            log.info("ceshi113");
        } else if (invirentment.equals("fstest")) {
            log.info("fstest");
        }
    }

    @Test
    public void enableConfig() {
        String tenantId = "2";
        User user = User.builder().userId("1000").tenantId(tenantId).build();
        MemberTypeEnum.MemberStatusEnum memberStatus = memberConfigManager.getMemberStatus(tenantId);
        if (memberStatus.status == 0) {
            log.info(memberStatus.toString());
            memberConfigManager.createOrUpdateMemberStatus(user, MemberStatusEnum.OPENED);
        } else {
            log.info("statusCode:{},statusMessage:{}", memberStatus.status, memberStatus.message);
        }
       // configService.updateTenantConfig(user, memberStatusKey, String.valueOf(MemberStatusEnum.OPENED), ConfigValueType.STRING);
    }

    @Test
    public void clearAll() throws Exception {
        String ea = "fsceshi019";
        String tenantId = eieaConverter.enterpriseAccountToId(ea) + "";
        log.info("tenantId:==:"+tenantId);
       User user = User.builder().userId("1000").tenantId(tenantId).build();
       RequestContextManager.setContext(RequestContext.builder().postId(UUID.randomUUID().toString()).tenantId(tenantId).user(user).build());
        clearApiName(user, MemberConstants.API_NAME, MemberConstants.STORE_TABLE_NAME, new MemberFunctionPrivilegeProvider());
        clearApiName(user, MemberEquitiesConstants.API_NAME, MemberEquitiesConstants.STORE_TABLE_NAME, new MemberEquitiesFunctionPrivilegeProvider());
        clearApiName(user, MemberGradeConstants.API_NAME, MemberGradeConstants.STORE_TABLE_NAME, new MemberGradeFunctionPrivilegeProvider());
        clearApiName(user, MemberGradeEquitiesRuleConstants.API_NAME, MemberGradeEquitiesRuleConstants.STORE_TABLE_NAME, new MemberGradeEquitiesRuleFunctionPrivilegeProvider());
        clearApiName(user, MemberGrowthValueDetailConstants.API_NAME, MemberGrowthValueDetailConstants.STORE_TABLE_NAME, new MemberGrowthValueDetailFunctionPrivilegeProvider());
        clearApiName(user, MemberIntegralDetailConstants.API_NAME, MemberIntegralDetailConstants.STORE_TABLE_NAME, new MemberIntegralDetailFunctionPrivilegeProvider());
        memberConfigManager.createOrUpdateMemberStatus(user, MemberTypeEnum.MemberStatusEnum.NOT_OPEN);
        deleteMemberRule(user, MemberGrowthValueDetailConstants.API_NAME, SceneEnum.FS_CRM_MEMBER_GRADE);
        deleteMemberRule(user, MemberIntegralDetailConstants.API_NAME, SceneEnum.FS_CRM_MEMBER_GROWTH_VALUE);
        deleteMemberRule(user, "SalesOrderObj", SceneEnum.FS_CRM_MEMBER_INTEGRAL);
    }

    private void clearApiName(User user, String apiName, String tableName, DefaultFunctionPrivilegeProvider privilegeProvider) throws Exception {
        String tenantId = user.getTenantId();
        objectDataService.deleteBySql(tenantId, "delete from mt_data where tenant_id ='" + tenantId + "' and object_describe_api_name ='" + apiName + "'");
        objectDataService.deleteBySql(tenantId, "delete from mt_unique where tenant_id ='" + tenantId + "' and describe_api_name ='" + apiName + "'");
        objectDataService.deleteBySql(tenantId, "delete from mt_index where tenant_id ='" + tenantId + "' and describe_api_name ='" + apiName + "'");
        objectDataService.deleteBySql(tenantId, "delete from mt_auto_number where tenant_id ='" + tenantId + "' and describe_api_name ='" + apiName + "'");
        objectDataService.deleteBySql(tenantId, "delete from " + tableName + " where tenant_id ='" + tenantId + "'");
        objectDataService.deleteBySql(tenantId, "delete from mt_describe where tenant_id='" + tenantId + "' and describe_api_name='" + apiName + "'");
        objectDataService.deleteBySql(tenantId, "delete from mt_field where tenant_id='" + tenantId + "' and describe_api_name='" + apiName + "'");
        objectDataService.deleteBySql(tenantId, "delete from mt_ui_component where tenant_id='" + tenantId + "' and ref_object_api_name='" + apiName + "'");
        deleteFunctionCode(privilegeProvider);
    }

    private void deleteFunctionCode(DefaultFunctionPrivilegeProvider privilegeProvider) {
        String tenantId = "71610";
        User user = User.builder().userId("1000").tenantId(tenantId).build();
        AuthContext authContext = AuthContext.builder().tenantId(tenantId).userId(user.getUserId()).appId("CRM").build();
        List<String> funcset = privilegeProvider.getSupportedActionCodes();
        DelFuncCodeRoles.Arg arg = DelFuncCodeRoles.Arg.builder().authContext(authContext).funcSet(funcset).build();
//        DelFuncCodeRoles.Result result = functionPrivilegeProxy.delFuncCodeRoles(arg);
//        System.out.println("--------------" + result);
    }

    private void deleteMemberRule(User user, String apiName, SceneEnum scene) {
        RuleEngineContext ruleEngineContext = new RuleEngineContext();
        ruleEngineContext.setAppId(AppIdEnum.MEMBER.name());
        ruleEngineContext.setObjectProperties(Maps.newHashMap());
        ruleEngineContext.setProperties(Maps.newHashMap());
        ruleEngineContext.setScene(scene.name());
        ruleEngineContext.setTenantId(user.getTenantId());
        ruleEngineContext.setUserId(user.getUserId());
        ListRuleGroup listRuleGroup = ListRuleGroup.builder().entityIds(Sets.newHashSet(apiName)).status(1).build();
        listRuleGroup.setStatus(1);
        List<RuleGroupPojo> ruleGroups = paasRuleManager.listRuleGroup(ruleEngineContext, listRuleGroup);
        //拉取配置
        ruleGroups.stream().forEach(value -> {
            DeleteRuleGroup deleteRuleGroup = new DeleteRuleGroup();
            deleteRuleGroup.setEntityId(value.getEntityId());
            deleteRuleGroup.setRuleCodes(Sets.newHashSet(value.getRuleCode()));
            try {
                configService.deleteTenantConfig(user, ConfigKeyUtil.getRuleActionKeyByRuleCode(value.getRuleCode()));
            } catch (Exception e) {
                e.printStackTrace();
            }
            paasRuleManager.deleteRuleGroup(ruleEngineContext, deleteRuleGroup);
        });
    }

    @Test
    public void countAllAvailableIntegral(){
        RequestContext requestContext = RequestContext.builder().tenantId("2").user(new User("2", "1000")).build();
        ServiceContext serviceContext = new ServiceContext(requestContext, null, null);
        IntegralModel.CountAllAvailableIntegralResult result = integralService.countAllAvailableIntegral(serviceContext);
        log.info("result : {}", result);
    }

}
