package com.facishare.crm.member.service;

import com.facishare.appserver.trigger.api.exception.TriggerBizException;
import com.facishare.crm.member.base.BaseServiceTest;
import com.facishare.crm.member.common.GsonUtil;
import com.facishare.crm.member.common.RuleRequestContextUtil;
import com.facishare.crm.member.constants.MemberGradeConstants;
import com.facishare.crm.member.enums.SceneEnum;
import com.facishare.crm.member.predefine.service.GradeService;
import com.facishare.crm.member.predefine.service.impl.GradeServiceImpl;
import com.facishare.crm.member.vo.GradeModel;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.rule.common.RuleEngineContext;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.HashMap;
import java.util.Map;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2018/12/4.
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/applicationContext.xml")
public class GradeServiceTest extends BaseServiceTest {
    private static final Map<String, String> OPERATOR_MAP = new HashMap<>();
    private static final String APP_ID = "CRM";
    private static final String EMPLOYEE_ID = "1000";

    static {
        System.setProperty("spring.profiles.active", "fstest");
    }

    @Autowired
    GradeService gradeService;
    @Autowired
    GradeServiceImpl gradeServiceImpl;

    public GradeServiceTest() {
        super(MemberGradeConstants.API_NAME);
    }

    @Before
    public void initUser() {
        tenantId = "71615";//2
        fsUserId = "1000";

    }



    @Test
    public void saveAllGrade() throws TriggerBizException {
        RequestContext requestContext = RequestContext.builder().tenantId(user.getTenantId()).user(user).build();
        ServiceContext serviceContext = newServiceContext();
        serviceContext.setRequestContext(requestContext);
        GradeModel.SaveGradeArg saveGradeArg = GsonUtil.fromJsonSerializingNull("{\"grades\":[],\"toDeleteGrades\":[{\"ruleCode\":\"5c20a6839388700001bb3709\",\"growthValueThreshold\":12,\"gradeName\":\"12\",\"gradeId\":\"5c20a683a5083d8ce95a1570\",\"gradeNo\":1,\"equitiesIds\":[\"5c20a684a5083d8ce95a15bb\"]}]}", GradeModel.SaveGradeArg.class);
//        GradeModel.Grade grade = new GradeModel.Grade();
//        saveGradeArg.getGrades().get(0).setRuleCode("5c148f013db71da32f9a2e53");
//        saveGradeArg.getGrades().get(0).setGradeId("5c148f07a345913b18e14924");
//        grade.setGradeId("5c148281a345910a844d7350");
//        grade.setRuleCode("5c1482803db71da32f9a2e50");
//        saveGradeArg.setToDeleteGrades(Lists.newArrayList(grade));
        GradeModel.SaveGradeResult saveGradeResult = gradeService.saveAllGrade(saveGradeArg, serviceContext);
        log.info("=== result : {}", saveGradeResult);
    }

    /**
     * 新建等级
     *
     * @param
     * @return
     */
    @Test
    public void createGrade() throws TriggerBizException {

        RequestContext requestContext = RequestContext.builder().tenantId(user.getTenantId()).user(user).build();
        ServiceContext serviceContext = newServiceContext();
        serviceContext.setRequestContext(requestContext);
        RuleEngineContext ruleEngineContext = RuleRequestContextUtil.getRuleEngineContext(serviceContext.getUser(), SceneEnum.FS_CRM_MEMBER_GRADE);
        GradeModel.SaveGradeArg saveGradeArg = GsonUtil.fromJsonSerializingNull("{\"grades\":[{\"growthValueThreshold\":1,\"gradeName\":\"level2\",\"equitiesIds\":[\"5c08c5fd4df47d00017090ee\"]},{\"growthValueThreshold\":1,\"gradeName\":\"level 1\",\"equitiesIds\":[\"5c078b4e77e9210001025292\"]}]}", GradeModel.SaveGradeArg.class);
//        gradeService.createGrade(saveGradeArg.getGrades(),ruleEngineContext,serviceContext);
    }

    /**
     * 删除等级
     *
     * @param
     * @return
     */
    @Test
    public void deleteGrade() throws TriggerBizException {

        RequestContext requestContext = RequestContext.builder().tenantId(user.getTenantId()).user(user).build();
        ServiceContext serviceContext = newServiceContext();
        serviceContext.setRequestContext(requestContext);
        RuleEngineContext ruleEngineContext = RuleRequestContextUtil.getRuleEngineContext(serviceContext.getUser(), SceneEnum.FS_CRM_MEMBER_GRADE);
        GradeModel.Grade grade = new GradeModel.Grade();
        grade.setGradeId("5c179124a345912d2474960e");
        grade.setRuleCode("5c17911e3db71d911741e54d");
        gradeServiceImpl.deleteGrade(Lists.newArrayList(grade),ruleEngineContext,serviceContext);
    }

    /**
     * 新建等级
     *
     * @param
     * @return
     */
    @Test
    public void updateGrade() throws TriggerBizException {

        RequestContext requestContext = RequestContext.builder().tenantId(user.getTenantId()).user(user).build();
        ServiceContext serviceContext = newServiceContext();
        serviceContext.setRequestContext(requestContext);
        RuleEngineContext ruleEngineContext = RuleRequestContextUtil.getRuleEngineContext(serviceContext.getUser(), SceneEnum.FS_CRM_MEMBER_GRADE);
        GradeModel.SaveGradeArg saveGradeArg = GsonUtil.fromJsonSerializingNull("{\"grades\":[{\"ruleCode\":\"5c0e0d043db71da184f0004b\",\"gradeId\":\"5c0e0d01c37ee12447245960\",\"gradeName\":\"czyczy1998\",\"growthValueThreshold\":\"10\"}]}", GradeModel.SaveGradeArg.class);
//        gradeService.updateGrade(saveGradeArg.getGrades(),ruleEngineContext,serviceContext);
    }

    /**
     * 设置等级权益关系
     *
     * @param
     * @return
     */
    @Test
    public void createGradeEquities() {

        RequestContext requestContext = RequestContext.builder().tenantId(user.getTenantId()).user(user).build();
        ServiceContext serviceContext = newServiceContext();
        serviceContext.setRequestContext(requestContext);
        GradeModel.CreateGradeEquitiesArg createGradeEquitiesArg = GsonUtil.fromJsonSerializingNull("{\"gradeId\":\"5c08bb2b51693d0001ed5601\",\"equitiesIds\":[\"5c078b1c77e92100010250b5\"]}", GradeModel.CreateGradeEquitiesArg.class);
        GradeModel.CreateGradeEquitiesResult createGradeEquitiesResult = gradeService.createGradeEquities(createGradeEquitiesArg, serviceContext);
        log.info("=== result : {}", createGradeEquitiesResult);
    }

    /**
     * 获取等级权益关系
     *
     * @param
     * @return
     */
    @Test
    public void getEquitiesByGrade() {
        RequestContext requestContext = RequestContext.builder().tenantId(user.getTenantId()).user(user).build();
        ServiceContext serviceContext = newServiceContext();
        serviceContext.setRequestContext(requestContext);
        GradeModel.GetGradeEquitiesArg getGradeEquitiesArg = GsonUtil.fromJsonSerializingNull("{gradeId:\"5c0891b8ab8c1305adeeeed9\"}", GradeModel.GetGradeEquitiesArg.class);
        GradeModel.GetGradeEquitiesResult getGradeEquitiesResult = gradeService.getEquitiesByGrade(getGradeEquitiesArg, serviceContext);
        log.info("=== result : {}", getGradeEquitiesResult);
    }

    /**
     * 删除等级权益关系
     *
     * @param
     * @return
     */
    @Test
    public void deleteEquitiesByGrade() {
        RequestContext requestContext = RequestContext.builder().tenantId(user.getTenantId()).user(user).build();
        ServiceContext serviceContext = newServiceContext();
        serviceContext.setRequestContext(requestContext);
        GradeModel.DeleteGradeEquitiesArg deleteGradeEquitiesArg = GsonUtil.fromJsonSerializingNull("{gradeEquitiesIds:[\"5c089275ab8c1305dbfc0136\"]}", GradeModel.DeleteGradeEquitiesArg.class);
        GradeModel.DeleteGradeEquitiesResult deleteGradeEquitiesResult = gradeService.deleteGradeEquities(deleteGradeEquitiesArg, serviceContext);
        log.info("=== result : {}", deleteGradeEquitiesResult);
    }

    /**
     * 拉取等级列表
     *
     * @param
     * @return
     */

    @Test
    public void listGrade() {
        RequestContext requestContext = RequestContext.builder().tenantId(user.getTenantId()).user(user).build();
        ServiceContext serviceContext = newServiceContext();
        serviceContext.setRequestContext(requestContext);
        GradeModel.ListGradeArg listGradeArg = GsonUtil.fromJsonSerializingNull("", GradeModel.ListGradeArg.class);
        GradeModel.ListGradeResult listGradeResult = gradeService.listGrade(listGradeArg, serviceContext);
        log.info("=== result : {}", listGradeResult);
    }
}
