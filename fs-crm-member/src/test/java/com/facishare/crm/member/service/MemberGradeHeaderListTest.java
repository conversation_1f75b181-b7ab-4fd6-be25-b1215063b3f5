package com.facishare.crm.member.service;

import com.facishare.crm.member.base.BaseControllerTest;
import com.facishare.crm.member.constants.MemberGradeConstants;
import com.facishare.crm.member.util.MemberUtils;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * @Auther: dzb
 * @Date: 2019/1/8
 * @Description:
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath*:spring-test/applicationContext.xml")
public class MemberGradeHeaderListTest  extends BaseControllerTest {


    public MemberGradeHeaderListTest() {
        super(MemberGradeConstants.API_NAME);
    }

    static {
        System.setProperty("spring.profiles.active", "fstest");//ceshi113
    }

    @Test
    public void testListHeader() {
        StandardListHeaderController.Arg arg = new StandardListHeaderController.Arg();
        arg.setApiName(MemberGradeConstants.API_NAME);
        arg.setIncludeLayout(true);
        arg.setRecordTypeAPIName("default__c");
        arg.setLayoutType("list");
        arg.setIncludeRefDescribe(true);
        StandardListHeaderController.Result result = executeListHeader(arg);
        LayoutDocument layoutDocument = result.getLayout();
        layoutDocument = MemberUtils.clearLayoutDocument(layoutDocument);
        if (null != layoutDocument)
            result.setLayout(layoutDocument);

        ObjectDescribeDocument objectDescribeDocument = result.getObjectDescribe();
        objectDescribeDocument = MemberUtils.clearObjectDescribeDocument(objectDescribeDocument);
        if (null != objectDescribeDocument)
            result.setObjectDescribe(objectDescribeDocument);
        System.out.println("result:==:"+result);

    }
}
