package com.facishare.crm.member.service;

import com.facishare.crm.member.base.BaseServiceTest;
import com.facishare.crm.member.constants.MemberEquitiesConstants;
import com.facishare.crm.member.constants.MemberGradeConstants;
import com.facishare.crm.member.constants.MemberGradeEquitiesRuleConstants;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.metadata.RecordTypeLogicService;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeProxy;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.privilege.dto.AuthContext;
import com.facishare.paas.appframework.privilege.dto.CreateFunctionPrivilege;
import com.facishare.paas.appframework.privilege.model.FunctionCodeBuilder;
import com.facishare.paas.appframework.privilege.model.FunctionPrivilegeProvider;
import com.facishare.paas.appframework.privilege.model.FunctionPrivilegeProviderManager;
import com.facishare.paas.appframework.privilege.util.PrivilegeConstants;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * @Auther: dzb
 * @Date: 2018/12/4
 * @Description:
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/applicationContext.xml")
public class RoleFunctionTest extends BaseServiceTest {
    @Autowired
    private FunctionPrivilegeProxy functionPrivilegeProxy;
    @Autowired
    private FunctionPrivilegeProviderManager providerManager;

    @Autowired
    private RecordTypeLogicService recordTypeLogicService;

    @Autowired
    private FunctionPrivilegeService functionPrivilegeService;

    public RoleFunctionTest() {
        super(MemberGradeConstants.API_NAME);
    }

    static {
        System.setProperty("spring.profiles.active", "ceshi113");
    }

    @Before
    public void initUser() {
        String invirentment = System.getProperty("spring.profiles.active");
        if (invirentment.equals("ceshi113")) {
            tenantId = "58438";//2
            fsUserId = "1001";
        } else if (invirentment.equals("fstest")) {
            tenantId = "71594";
        }
    }

    @Test
    public void addRoleFuction() {
        AuthContext authContext = AuthContext.builder().appId("CRM").tenantId(user.getTenantId()).userId(user.getUserId()).build();
        List<String> list = new ArrayList<>();
        list.add(MemberEquitiesConstants.API_NAME);
        list.add(MemberGradeConstants.API_NAME);
        list.add(MemberGradeEquitiesRuleConstants.API_NAME);

        List<String> dlist = new ArrayList<>();
        dlist.add(MemberEquitiesConstants.DEFAULT_LAYOUT_API_NAME);
        dlist.add(MemberGradeConstants.DEFAULT_LAYOUT_API_NAME);
        dlist.add(MemberGradeEquitiesRuleConstants.DEFAULT_LAYOUT_API_NAME);
        for (int i = 0; i < list.size(); i++) {
/*
            FunctionPrivilegeProvider provider = this.providerManager.getProvider(list.get(i));
            //添加权限
            List<FunctionPojo> functionPojos = getUserDefinedFunctionPojoList(authContext.getTenantId(), list.get(i));
            if (!org.apache.commons.collections4.CollectionUtils.isEmpty(functionPojos)) {
                CreateFunctionPrivilege.Arg arg = CreateFunctionPrivilege.Arg.builder().authContext(authContext).functionPojoList(functionPojos).build();
                com.facishare.paas.appframework.privilege.dto.CreateFunctionPrivilege.Result result= this.functionPrivilegeProxy.createFunctionPrivilege(arg);
                System.out.println("functionResult:"+result);
            }
            //给角色添加权限
            addRoleEditFunctionPrivilege(authContext, CommonConstants.MEMBER_MANAGER_ROLE, list.get(i));
            System.out.println(provider.getSupportedActionCodes()+"=="+provider.getCustomInitRoleActionCodes());
*/
//            List<String>  functionlist = new ArrayList<>();
//            functionlist.add(ObjectAction.CREATE.getActionCode());
//            functionlist.add(ObjectAction.UPDATE.getActionCode());
//            List<FunctionPojo> functionPojoslist =   functionPrivilegeService.batchCreateFunc(user,list.get(i),functionlist);
//            System.out.println("functionPojoslist:"+functionPojoslist);
            //给角色添加权限
          //  addRoleEditFunctionPrivilege(authContext, CommonConstants.MEMBER_MANAGER_ROLE, list.get(i));
/*            String layoutApiName =null;
            if (list.get(i).equals(MemberEquitiesConstants.API_NAME)) {
                layoutApiName = MemberEquitiesConstants.DEFAULT_LAYOUT_API_NAME;
            }else if(list.get(i).equals(MemberGradeConstants.API_NAME)){
                layoutApiName =MemberGradeConstants.DEFAULT_LAYOUT_API_NAME;
            }else  if(list.get(i).equals(MemberGradeEquitiesRuleConstants.API_NAME)){
                layoutApiName=MemberGradeEquitiesRuleConstants.DEFAULT_LAYOUT_API_NAME;
            }
            this.recordTypeLogicService.recordTypeInit(user, layoutApiName, user.getTenantId(), list.get(i));*/
        }
    }

    private List<CreateFunctionPrivilege.FunctionPojo> getUserDefinedFunctionPojoList(String tenantId, String apiName) {
        ArrayList userDefinedFunctionPojoList = Lists.newArrayList();
        String actionCode = ObjectAction.UPDATE.getActionCode();
        String createCode = ObjectAction.CREATE.getActionCode();
        CreateFunctionPrivilege.FunctionPojo pojo = new CreateFunctionPrivilege.FunctionPojo();
        pojo.setAppId("CRM");
        pojo.setTenantId(tenantId);
        pojo.setFuncType(Integer.valueOf(1));
        pojo.setParentCode(PrivilegeConstants.PAAS_PARENT_CODE);
        pojo.setFuncName(ObjectAction.of(actionCode).getActionLabel());
        pojo.setFuncCode(FunctionCodeBuilder.build(apiName, actionCode));
        CreateFunctionPrivilege.FunctionPojo epojo = new CreateFunctionPrivilege.FunctionPojo();
        epojo.setAppId("CRM");
        epojo.setTenantId(tenantId);
        epojo.setFuncType(Integer.valueOf(1));
        epojo.setParentCode(PrivilegeConstants.PAAS_PARENT_CODE);
        epojo.setFuncName(ObjectAction.of(createCode).getActionLabel());
        epojo.setFuncCode(FunctionCodeBuilder.build(apiName, createCode));
        userDefinedFunctionPojoList.add(pojo);
        userDefinedFunctionPojoList.add(epojo);
        return userDefinedFunctionPojoList;
    }

    private void addRoleEditFunctionPrivilege(AuthContext authContext, String roleCode, String objectApiName) {
        FunctionPrivilegeProvider provider = this.providerManager.getProvider(objectApiName);
        String addfuncCode = FunctionCodeBuilder.build(objectApiName, "Add");
        String funcCode = FunctionCodeBuilder.build(objectApiName, "Edit");
        List<String> funcCodeList = new ArrayList<>();
        funcCodeList.add(funcCode);
        funcCodeList.add(addfuncCode);
        if (!org.apache.commons.collections4.CollectionUtils.isEmpty(funcCodeList)) {
            com.facishare.paas.appframework.privilege.dto.UpdateRoleModifiedFuncPrivilege.Arg arg = com.facishare.paas.appframework.privilege.dto.UpdateRoleModifiedFuncPrivilege.Arg.builder()
                .authContext(authContext).roleCode(roleCode).addFuncCode(funcCodeList).build();
            com.facishare.paas.appframework.privilege.dto.UpdateRoleModifiedFuncPrivilege.Result result =
                    this.functionPrivilegeProxy.updateRoleModifiedFuncPrivilege(arg, FunctionPrivilegeProxy.HeaderUtil.buildHeaders(authContext.getTenantId()));
            if (!result.isSuccess()) {
                log.error("addFunctionPrivilege error,arg:{},result:{}", arg, result);
            }
            System.out.println("Roleresult:"+result);
        }
    }
}
