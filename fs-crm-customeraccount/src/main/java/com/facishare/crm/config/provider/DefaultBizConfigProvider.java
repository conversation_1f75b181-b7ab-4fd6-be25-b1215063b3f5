package com.facishare.crm.config.provider;

import com.facishare.crm.config.BizConfigKey;
import com.facishare.crm.customeraccount.enums.ConfigKeyEnum;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;
import java.util.Set;

@Component("caDefaultBizConfigProvider")
public class DefaultBizConfigProvider implements BizConfigProvider {
    @Autowired
    protected ConfigService configService;

    public static ThreadLocal<Map<String, String>> bizConfigThreadLocal = ThreadLocal.withInitial(Maps::newHashMap);

    static {
        RequestContextManager.addContextRemoveListener(requestContext -> {
            bizConfigThreadLocal.remove();
        });
    }

    @Override
    public String getConfigValue(User user, String key) {
        if (!BizConfigKey.of(key).isPresent() && !ConfigKeyEnum.of(key).isPresent()) {
            throw new ValidateException(String.format("key[%s] not support", key));
        }
        String cacheKey = String.format("%s|%s", user.getTenantId(), key);
        Map<String, String> threadLocalMap = bizConfigThreadLocal.get();
        if (threadLocalMap.containsKey(cacheKey)) {
            return threadLocalMap.get(cacheKey);
        } else {
            String value = configService.findTenantConfig(user, key);
            threadLocalMap.put(cacheKey, value);
            return value;
        }
    }

    @Override
    public String getConfigValueWithDefault(User user, String key) {
        String dbConfigValue = getConfigValue(user, key);
        if (Strings.isNullOrEmpty(dbConfigValue)) {
            return getDefaultConfigValue(key);
        } else {
            return dbConfigValue;
        }
    }

    @Override
    public String getDefaultConfigValue(String key) {
        return BizConfigKey.of(key).map(BizConfigKey::getDefaultValue).orElse("");
    }

    @Override
    public String getConfigKey() {
        return null;
    }

    @Override
    public void validateSetConfig(User user, String key, String newValue) {
        Optional<BizConfigKey> bizConfigKeyOptional = BizConfigKey.of(key);
        if (!bizConfigKeyOptional.isPresent()) {
            throw new ValidateException(String.format("key[%s] not support", key));
        }
        Set<String> supportUpdateValues = getSupportUpdateValues();
        if (StringUtils.isNotEmpty(newValue) && !supportUpdateValues.contains(newValue)) {
            throw new ValidateException(String.format("key[%s],value[%s] illegal", key, newValue));
        }
    }

    protected Set<String> getSupportUpdateValues() {
        //2:启用，0关闭；可启用、可关闭
        return Sets.newHashSet("2", "0");
    }

    @Override
    public void setConfigValue(User user, String value, String oldValue, String key) {
        if (oldValue == null) {
            configService.createTenantConfig(user, key, value, ConfigValueType.STRING);
        } else {
            configService.updateTenantConfig(user, key, value, ConfigValueType.STRING);
        }
    }
}
