package com.facishare.crm.config.provider;

import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Component("caBizConfigProviderManager")
public class BizConfigProviderManager {
    @Resource(name = "caDefaultBizConfigProvider")
    private DefaultBizConfigProvider defaultBizConfigProvider;

    private final Map<String, BizConfigProvider> providerMap = Maps.newHashMap();

    public BizConfigProviderManager(List<BizConfigProvider> providers) {
        providers.forEach(x -> providerMap.put(x.getConfigKey(), x));
    }

    public BizConfigProvider getProvider(String key) {
        return providerMap.getOrDefault(key, defaultBizConfigProvider);
    }

    public BizConfigProvider getDefaultProvider(){
        return defaultBizConfigProvider;
    }

}
