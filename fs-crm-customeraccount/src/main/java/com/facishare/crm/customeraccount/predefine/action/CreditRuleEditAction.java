package com.facishare.crm.customeraccount.predefine.action;

import com.facishare.crm.consts.CreditRuleConst;
import com.facishare.crm.consts.CreditRuleDetailConst;
import com.facishare.crm.customeraccount.constants.CAI18NKey;
import com.facishare.crm.customeraccount.predefine.manager.CreditManager;
import com.facishare.crm.customeraccount.util.CreditUtil;
import com.facishare.crm.customeraccount.util.ExecuteUtil;
import com.facishare.crmcommon.util.DataUtil;
import com.facishare.crmcommon.util.SearchQueryBuilder;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.appframework.metadata.repository.model.DomainPluginInstance;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections4.MapUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

public class CreditRuleEditAction extends StandardEditAction {
    private final CreditManager creditManager = SpringUtil.getContext().getBean(CreditManager.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        boolean onlyEditMasterData = MapUtils.isEmpty(arg.getDetails());
        boolean creditRuleUsed = creditManager.creditOccupiedRuleUsed(actionContext.getUser(), this.objectData.getId());
        List<IObjectData> detailDataList = this.detailObjectData.getOrDefault(CreditRuleDetailConst.API_NAME, Lists.newArrayList());
        if (creditRuleUsed) {
            DataUtil.fieldEditCheck(this.objectDescribe, this.dbMasterData, this.objectData, CreditRuleConst.F.Status.apiName);
            List<IObjectData> dbDetailDataList = this.dbDetailDataMap.get(CreditRuleDetailConst.API_NAME);
            Map<String, IObjectData> dbDetailDataMap = dbDetailDataList.stream().collect(Collectors.toMap(IObjectData::getId, Function.identity()));
            Map<String, IObjectData> detailDataMap = detailDataList.stream().collect(Collectors.toMap(IObjectData::getId, Function.identity()));
            Set<String> notSupportEditFields = Sets.newHashSet(
                    CreditRuleDetailConst.F.CreditObject.apiName,
                    CreditRuleDetailConst.F.OccupiedAmountField.apiName,
                    CreditRuleDetailConst.F.CheckStrength.apiName,
                    CreditRuleDetailConst.F.CheckCreditLimit.apiName,
                    CreditRuleDetailConst.F.OccupiedTiming.apiName,
                    CreditRuleDetailConst.F.RuleSequence.apiName,
                    CreditRuleDetailConst.F.RuleCondition.apiName
            );
            IObjectDescribe creditDetailDescribe = this.detailDescribeMap.get(CreditRuleDetailConst.API_NAME);
            for (IObjectData dbDetailData : dbDetailDataList) {
                IObjectData detailData = detailDataMap.get(dbDetailData.getId());
                if (Objects.isNull(detailData)) {
                    throw new ValidateException(I18N.text(CAI18NKey.CREDIT_RULE_USED_NOT_EDIT));
                }
                notSupportEditFields.forEach(fieldName -> {
                    if (DataUtil.fieldChanged(creditDetailDescribe, dbDetailData, detailData, fieldName)) {
                        throw new ValidateException(I18N.text(CAI18NKey.CREDIT_RULE_USED_NOT_EDIT));
                    }
                });
            }
            for (IObjectData detailData : detailDataList) {
                if (!dbDetailDataMap.containsKey(detailData.getId())) {
                    throw new ValidateException(I18N.text(CAI18NKey.CREDIT_RULE_USED_NOT_EDIT));
                }
            }
        } else {
            if (!onlyEditMasterData) {
                CreditUtil.checkCreditRule(actionContext.getUser(), ObjectDataDocument.ofList(detailDataList), serviceFacade);
            }
        }
        if (!onlyEditMasterData) {
            computeObjectPlugin();
        }
    }

    List<DomainPluginInstance> pluginInstancesToDisable = Lists.newArrayList();
    List<DomainPluginInstance> pluginInstancesToEnable = Lists.newArrayList();
    Set<String> objectsToCreatePlugin = Sets.newHashSet();

    public void computeObjectPlugin() {
        Set<String> creditObjects = this.detailObjectData.get(CreditRuleDetailConst.API_NAME).stream().map(x -> x.get(CreditRuleDetailConst.F.CreditObject.apiName, String.class)).collect(Collectors.toSet());
        Set<String> dbCreditObjects = this.dbDetailDataMap.get(CreditRuleDetailConst.API_NAME).stream().map(x -> x.get(CreditRuleDetailConst.F.CreditObject.apiName, String.class)).collect(Collectors.toSet());
        Set<String> objectsToDisablePlugin = Sets.newHashSet();
        creditObjects.forEach(x -> {
            if (!dbCreditObjects.contains(x)) {
                objectsToCreatePlugin.add(x);
            }
        });

        dbCreditObjects.forEach(x -> {
            if (!creditObjects.contains(x)) {
                objectsToDisablePlugin.add(x);
            }
        });

        if (!objectsToDisablePlugin.isEmpty()) {
            SearchTemplateQuery query = SearchQueryBuilder.builder().in(CreditRuleDetailConst.F.CreditObject.apiName, objectsToDisablePlugin)
                    .notEq(CreditRuleDetailConst.F.CreditOccupiedRuleId.apiName, this.objectData.getId()).orderBy(CreditRuleDetailConst.F.CreditOccupiedRuleId.apiName, true).build();
            List<IObjectData> otherRuleCreditDetailList = serviceFacade.findBySearchQuery(actionContext.getUser(), CreditRuleDetailConst.API_NAME, query).getData();
            for (IObjectData otherRuleDetailData : otherRuleCreditDetailList) {
                String otherRuleCreditObject = otherRuleDetailData.get(CreditRuleDetailConst.F.CreditObject.apiName, String.class);
                objectsToDisablePlugin.remove(otherRuleCreditObject);
            }
        }

        Set<String> allCreditObjects = Sets.newHashSet();
        allCreditObjects.addAll(objectsToCreatePlugin);
        allCreditObjects.addAll(objectsToDisablePlugin);

        if (!allCreditObjects.isEmpty()) {
            Map<String, DomainPluginInstance> objectPluginMap = creditManager.findPluginInstanceByPluginAndObjectApiName(actionContext.getUser(), CreditRuleConst.PLUGIN_API_NAME, allCreditObjects);
            objectPluginMap.forEach((k, v) -> {
                if (objectsToCreatePlugin.contains(k)) {
                    objectsToCreatePlugin.remove(k);
                    if (!v.isActive()) {
                        pluginInstancesToEnable.add(v);
                    }
                }
                if (objectsToDisablePlugin.contains(k)) {
                    v.setActive(false);
                    pluginInstancesToDisable.add(v);
                }
            });
        }
    }

    @Override
    protected void updateMasterAndDetail(IObjectData objectDataCp, Map<String, List<IObjectData>> detailToAddMap, Map<String, List<IObjectData>> detailToUpdateMap, Map<String, List<IObjectData>> detailToDeleteMap) {
        ExecuteUtil.execute(() -> {
            super.updateMasterAndDetail(objectDataCp, detailToAddMap, detailToUpdateMap, detailToDeleteMap);
            //初始化插件示例
            for (String creditObjectApiName : objectsToCreatePlugin) {
                creditManager.createPluginInstance(actionContext.getUser(), CreditRuleConst.PLUGIN_API_NAME, creditObjectApiName);
            }
            for (DomainPluginInstance domainPluginInstance : pluginInstancesToEnable) {
                creditManager.enablePluginInstance(actionContext.getUser(), domainPluginInstance);
            }
            for (DomainPluginInstance domainPluginInstance : pluginInstancesToDisable) {
                creditManager.disablePluginInstance(actionContext.getUser(), domainPluginInstance);
            }
            return null;
        });
    }
}
