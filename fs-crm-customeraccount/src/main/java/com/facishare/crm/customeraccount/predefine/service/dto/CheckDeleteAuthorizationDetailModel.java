package com.facishare.crm.customeraccount.predefine.service.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 账户授权
 */
@Data
public class CheckDeleteAuthorizationDetailModel {

    @Data
    public static class Arg {
        //同一个【账户授权】的【授权明细id】
        private List<String> authorizationDetailIds;
    }

    @AllArgsConstructor
    @Data
    public static class Result {
        private List<ErrorInfo> errorInfos;
    }

    @AllArgsConstructor
    @Data
    public static class ErrorInfo {
        private String authorizationDetailId;
        private String errorMsg;
    }
}