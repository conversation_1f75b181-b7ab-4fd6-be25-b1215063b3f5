package com.facishare.crm.customeraccount.predefine.service.dto;

import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AccountAuthTransferDataModel {
    @Data
    public static class Arg {
        private List<String> tenantIds;
        private List<String> objectApiNames;
        private Map<String, Fields> objectApiName2Field = new HashMap<>();
    }

    @Data
    public static class Fields {
        private String customerFieldName;            //客户字段
        private String enterAccountAmountFieldName;  //金额字段
    }

    @Data
    public static class Result {
        private String result;
    }
}
