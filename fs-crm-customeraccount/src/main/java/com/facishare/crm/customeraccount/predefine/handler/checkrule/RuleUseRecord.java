package com.facishare.crm.customeraccount.predefine.handler.checkrule;

import com.facishare.paas.metadata.api.IObjectData;
import lombok.Getter;

@Getter
public class RuleUseRecord {
    private final HandlerTypeEnum handlerTypeEnum;

    private final IObjectData accountRuleUseRecordData;

    public RuleUseRecord(HandlerTypeEnum handlerTypeEnum,  IObjectData accountRuleUseRecordData) {
        this.handlerTypeEnum = handlerTypeEnum;
        this.accountRuleUseRecordData = accountRuleUseRecordData;
    }
}
