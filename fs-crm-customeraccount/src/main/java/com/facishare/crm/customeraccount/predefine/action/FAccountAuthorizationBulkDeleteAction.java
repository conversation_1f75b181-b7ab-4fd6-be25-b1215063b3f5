//package com.facishare.crm.customeraccount.predefine.action;
//
//import com.facishare.crm.customeraccount.constants.CAI18NKey;
//import com.facishare.crm.customeraccount.constants.FAccountAuthorizationConstants;
//import com.facishare.crm.customeraccount.constants.SystemConstants;
//import com.facishare.crm.customeraccount.enums.FAccountAuthorizationStatusEnum;
//import com.facishare.crm.customeraccount.predefine.manager.FAccountAuthorizationManager;
//import com.facishare.crm.customeraccount.util.RequestUtil;
//import com.facishare.paas.I18N;
//import com.facishare.paas.appframework.core.exception.ValidateException;
//import com.facishare.paas.appframework.core.predef.action.StandardBulkDeleteAction;
//import com.facishare.paas.metadata.api.IObjectData;
//import com.facishare.paas.metadata.api.QueryResult;
//import com.facishare.paas.metadata.util.SpringUtil;
//import lombok.extern.slf4j.Slf4j;
//
//import java.util.List;
//import java.util.Objects;
//
//@Slf4j
//public class FAccountAuthorizationBulkDeleteAction extends StandardBulkDeleteAction {
//    private FAccountAuthorizationManager fAccountAuthorizationManager = SpringUtil.getContext().getBean(FAccountAuthorizationManager.class);
//
//
//    @Override
//    protected void before(Arg arg) {
//        super.before(arg);
//        if (!RequestUtil.isFromInner(actionContext)) {
//            String objectApiName = arg.getDescribeApiName();
//            List<String> ids = arg.getIdList();
//            QueryResult<IObjectData> queryResult = fAccountAuthorizationManager.queryInvalidDataByField(actionContext.getUser(), objectApiName, SystemConstants.Field.Id.apiName, ids, 0, ids.size());
//            List<IObjectData> fAccountAuthDatas = queryResult.getData();
//            //已初始化，不能删除
//            for (IObjectData data : fAccountAuthDatas) {
//                String status = data.get(FAccountAuthorizationConstants.Field.Status.apiName, String.class);
//                if (Objects.equals(status, FAccountAuthorizationStatusEnum.HAS_INIT.getValue())) {
//                    String name = data.get(FAccountAuthorizationConstants.Field.Name.apiName, String.class);
//                    throw new ValidateException(I18N.text(CAI18NKey.HAS_INIT_CANNOT_DELETE, name));
//                }
//            }
//        }
//    }
//}
