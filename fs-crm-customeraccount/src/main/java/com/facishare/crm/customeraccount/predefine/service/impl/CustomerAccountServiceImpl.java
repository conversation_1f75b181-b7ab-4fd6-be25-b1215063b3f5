package com.facishare.crm.customeraccount.predefine.service.impl;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.config.BizConfigKey;
import com.facishare.crm.config.CrmBizConfigManager;
import com.facishare.crm.customeraccount.constants.*;
import com.facishare.crm.customeraccount.entity.CustomerAccountBill;
import com.facishare.crm.customeraccount.enums.*;
import com.facishare.crm.customeraccount.exception.CustomerAccountBusinessException;
import com.facishare.crm.customeraccount.exception.CustomerAccountErrorCode;
import com.facishare.crm.customeraccount.exception.FundAccountErrorCode;
import com.facishare.crm.customeraccount.exception.FundAccountException;
import com.facishare.crm.customeraccount.mq.producer.MQProducerManager;
import com.facishare.crm.customeraccount.predefine.action.RebateIncomeDetailFlowCompletedAction;
import com.facishare.crm.customeraccount.predefine.manager.*;
import com.facishare.crm.customeraccount.predefine.manager.objectInit.CaInitManager;
import com.facishare.crm.customeraccount.predefine.remote.CrmManager;
import com.facishare.crm.customeraccount.predefine.service.CommonService;
import com.facishare.crm.customeraccount.predefine.service.CustomerAccountService;
import com.facishare.crm.customeraccount.predefine.service.dto.*;
import com.facishare.crm.customeraccount.predefine.service.dto.CustomerAccountType.SettleType;
import com.facishare.crm.customeraccount.predefine.service.dto.FlowCompleteModel.Arg;
import com.facishare.crm.customeraccount.predefine.service.dto.FlowCompleteModel.Result;
import com.facishare.crm.customeraccount.util.*;
import com.facishare.crmcommon.manager.CommonConfigManager;
import com.facishare.crmcommon.manager.CommonLangManager;
import com.facishare.crmcommon.util.SearchUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseObjectLockAction;
import com.facishare.paas.appframework.core.predef.action.StandardAction;
import com.facishare.paas.appframework.core.predef.action.StandardBulkDeleteAction;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.appframework.core.predef.action.StandardBulkRecoverAction;
import com.facishare.paas.appframework.core.predef.action.StandardFlowCompletedAction;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.DELETE_STATUS;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.rest.core.util.JsonUtil;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.Message;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by xujf on 2017/9/25.
 */
@Slf4j
@Component
public class CustomerAccountServiceImpl extends CommonService implements CustomerAccountService {
    @Autowired
    private CustomerAccountManager customerAccountManager;
    @Autowired
    private FundAccountManager fundAccountManager;
    @Autowired
    private CustomerAccountConfigManager customerAccountConfigManager;
    @Autowired
    private CrmManager crmManager;
    @Autowired
    private CustomerAccountBillManager customerAccountBillManager;
    @Autowired
    private ConfigService configService;
    @Autowired
    private RebateIncomeDetailManager rebateIncomeDetailManager;
    @Autowired
    private BillJobManager billJobManager;
    @Autowired
    private CaInitManager caInitManager;
    @Autowired
    private AccountCheckRuleManager accountCheckRuleManager;
    @Autowired
    private MQProducerManager mqProducerManager;
    @Autowired
    CommonConfigManager commonConfigManager;
    @Autowired
    private NewCustomerAccountManager newCustomerAccountManager;
    @Autowired
    private FAccountAuthorizationManager fAccountAuthorizationManager;
    @Autowired
    private IObjectDescribeService objectDescribeService;
    @Autowired
    private CommonLangManager commonLangManager;
    @Autowired
    private UnfreezeAuthDetailManager unfreezeAuthDetailManager;
    @Autowired
    private CrmBizConfigManager crmBizConfigManager;

    private static final String CREATE_PAYMENT_SWITCH = "create_payment_while_order_created";
    private static final String CREATE_ORDER_LIMIT_CONFIG = "create_order_limit_config";
    private static final String CUSTOMER_ACCOUNT_VERSION_KEY = "customer_account_app";
    private FsGrayReleaseBiz gray = FsGrayRelease.getInstance("customeraccount");
    @Autowired
    private FundAccountConfigManager fundAccountConfigManager;

    /**
     * 调用config模块<br>
     */
    @Override
    public CustomerAccountType.IsCustomerAccountEnableResult isCustomerAccountEnable(ServiceContext serviceContext) {
        String tenantId = serviceContext.getTenantId();
        CustomerAccountType.CustomerAccountEnableSwitchStatus customerAccountEnableSwitchStatus = customerAccountConfigManager.getStatus(tenantId);
        CustomerAccountType.IsCustomerAccountEnableResult result = new CustomerAccountType.IsCustomerAccountEnableResult();
        if (customerAccountEnableSwitchStatus.getValue() != CustomerAccountType.CustomerAccountEnableSwitchStatus.ENABLE.getValue()) {
            //除了enable 状态，其他都返回false<br>
            log.info("customer account switch status is not enable,for status:{}", customerAccountEnableSwitchStatus.getValue());
            result.setEnable(false);
        } else {
            result.setEnable(true);
        }
        return result;
    }

    /**
     * 信用+可用余额>订单金额则该订单可以创建<br> 否则不可以创建订单<br> 1.信用开启的情况填：预付款+返利+信用 2.信用不开启：任何情况下都可以下单。
     */
    @Override
    public CustomerAccountType.BalanceCreditEnoughResult isBalanceAndCreditEnough(ServiceContext serviceContext, CustomerAccountType.OrderArg orderArg) {
        CustomerAccountType.BalanceCreditEnoughResult returnResult = new CustomerAccountType.BalanceCreditEnoughResult();
        User user = serviceContext.getUser();
        String customerId = orderArg.getCustomerId();

        AssertUtil.argumentNotNullOrEmpty("orderArg.customerId", customerId);
        BigDecimal minusAmount = BigDecimal.valueOf(orderArg.getOrderAmount()).subtract(BigDecimal.valueOf(orderArg.getOldOrderAmount()));
        if (minusAmount.compareTo(BigDecimal.valueOf(0)) <= 0) {
            returnResult.setIsEnough(true);
            return returnResult;
        }

        //可用信用
        boolean needCountSpecialCredit = ConfigCenter.countSpecialCredit(user.getTenantId());
        BigDecimal availableCredit = null;
        if (needCountSpecialCredit) {
            availableCredit = customerAccountManager.getAvailableCredit(user, customerId, true);
        } else {
            availableCredit = customerAccountManager.getAvailableCredit(user, customerId);
        }

        //可用余额
        IObjectData customerAccount = customerAccountManager.getCustomerAccountByCustomerId(user, customerId);
        BigDecimal availablePrepayBalance = customerAccount.get(CustomerAccountConstants.Field.PrepayAvailableBalance.getApiName(), BigDecimal.class);
        BigDecimal availableRebateBalance = customerAccount.get(CustomerAccountConstants.Field.RebateAvailableBalance.getApiName(), BigDecimal.class);
        Map<String, String> configMap = configService.queryTenantConfigs(user, Lists.newArrayList(ConfigKeyEnum.NEW_CUSTOMER_ACCOUNT.key, ConfigKeyEnum.ACCOUNT_CHECK_RULE.key));
        if (ConfigKeyEnum.NEW_CUSTOMER_ACCOUNT.enabled(configMap.get(ConfigKeyEnum.NEW_CUSTOMER_ACCOUNT.key))) {
            availablePrepayBalance = customerAccountManager.getNewCustomerAccountBalanceByCustomerId(user, customerId, ConfigKeyEnum.ACCOUNT_CHECK_RULE.enabled(configMap.get(ConfigKeyEnum.ACCOUNT_CHECK_RULE.key)));
        }

        BigDecimal availableBalance = availablePrepayBalance.add(availableRebateBalance);
        if (gray.isAllow("grayRule", customerAccount.getTenantId())) {
            BigDecimal unpaidPrepaidAmount = crmManager.getUsedCreditAmountOpt(user, Lists.newArrayList(customerId), true, false);
            availableBalance = availableBalance.subtract(unpaidPrepaidAmount);
        }
        log.info("customerAccountType->可用金额校验：availableCredit:{},availablePrepayBalance:{},availableRebateBalance:{},orderArg:{}",
                availableCredit, availablePrepayBalance, availableRebateBalance, orderArg);

        Optional<SettleTypeEnum> settleTypeEnumOptional = SettleTypeEnum.getByValue(orderArg.getSettleType());
        SettleTypeEnum settleTypeEnum = settleTypeEnumOptional.orElseThrow(() -> new ValidateException(I18N.text(CAI18NKey.SPECIFIED_PARAM_ERROR, "settleType")));
        BigDecimal total = BigDecimal.ZERO;
        switch (settleTypeEnum) {
            case Prepay:
                if (needCountSpecialCredit) {  //仅校验 赊销方式
                    total = total.add(minusAmount);
                } else {
                    total = availableBalance;
                }
                break;
            case Credit:
                if (needCountSpecialCredit) {
                    total = availableCredit.add(availableBalance);
                } else {
                    total = availableCredit.add(availableRebateBalance);
                }
                break;
            case Cash:
                //现付不校验余额
                total = total.add(minusAmount);
                break;
        }

        boolean enough = true;
        String message = "";
        String createOrderLimitConfig = configService.findTenantConfig(user, CREATE_ORDER_LIMIT_CONFIG);
        CreateOrderLimitConfigEnum createOrderLimitConfigEnum = CreateOrderLimitConfigEnum.get(createOrderLimitConfig).orElse(CreateOrderLimitConfigEnum.TERMINATE);
        if (total.compareTo(minusAmount) < 0) {//不够的情况
            switch (createOrderLimitConfigEnum) {
                case TERMINATE:
                    message = I18N.text(CAI18NKey.BALANCE_NOT_ENOUGH);
                    enough = false;
                    break;
                case HINT:
                    if (BooleanUtils.isTrue(orderArg.getForceCommit())) {//强制提交，校验通过
                        enough = true;
                    } else {
                        message = I18N.text(CAI18NKey.BALANCE_NOT_ENGOUT_STILL_COMMIT);
                        enough = false;
                    }
                    break;
                case UNCONTROLLED:
                    break;
            }
        }
        returnResult.setIsEnough(enough);
        returnResult.setMessage(message);
        return returnResult;
    }

    @Override
    public CustomerAccountType.BalanceEnoughResult isBalanceEnough(ServiceContext serviceContext, CustomerAccountType.PaymentArg paymentArg) {
        String tenantId = serviceContext.getTenantId();
        String customerId = paymentArg.getCustomerId();
        CustomerAccountType.GetByCustomerIdArg arg = new CustomerAccountType.GetByCustomerIdArg();
        arg.setCustomerId(paymentArg.getCustomerId());
        Map<String, Object> customerAccountObj = getByCustomerId(serviceContext, arg).getObjectData();
        CustomerAccountType.BalanceEnoughResult returnResult = new CustomerAccountType.BalanceEnoughResult();
        double availablePrepayBalance = Double.parseDouble(customerAccountObj.get(CustomerAccountConstants.Field.PrepayAvailableBalance.apiName).toString());
        double availableRebateBalance = Double.parseDouble(customerAccountObj.get(CustomerAccountConstants.Field.RebateAvailableBalance.apiName).toString());
        Map<String, String> configMap = configService.queryTenantConfigs(serviceContext.getUser(), Lists.newArrayList(ConfigKeyEnum.NEW_CUSTOMER_ACCOUNT.key, ConfigKeyEnum.ACCOUNT_CHECK_RULE.key));
        if (ConfigKeyEnum.NEW_CUSTOMER_ACCOUNT.enabled(configMap.get(ConfigKeyEnum.NEW_CUSTOMER_ACCOUNT.key))) {
            availablePrepayBalance = customerAccountManager.getNewCustomerAccountBalanceByCustomerId(serviceContext.getUser(), customerId, ConfigKeyEnum.ACCOUNT_CHECK_RULE.enabled(configMap.get(ConfigKeyEnum.ACCOUNT_CHECK_RULE.key))).doubleValue();
        }
        if (paymentArg.getPrepayToPay() == 0.0 || paymentArg.getPrepayToPay() <= availablePrepayBalance) {
            returnResult.setPrepayEnough(true);
        } else {
            returnResult.setPrepayEnough(false);
        }
        if (paymentArg.getRebateToPay() == 0.0) {
            returnResult.setRebateEnough(true);
        } else if (paymentArg.getRebateToPay() <= availableRebateBalance) {
            returnResult.setRebateEnough(true);
            rebateIncomeDetailManager.obtainRebateIncomeToPayList(serviceContext.getUser(), BigDecimal.valueOf(paymentArg.getRebateToPay()), paymentArg.getCustomerId());
        } else {
            returnResult.setRebateEnough(false);
        }
        return returnResult;
    }

    @Override
    public CustomerAccountType.CanInvalidByCustomerIdsResult canInvalidByCustomerIds(ServiceContext serviceContext, CustomerAccountType.CanInvalidByCustomerIdsArg canInvalidByCustomerIdsArg) {
        return customerAccountManager.canInvalidByCustomerIds(serviceContext.getUser(), canInvalidByCustomerIdsArg);
    }

    @Override
    public CustomerAccountType.GetByCustomerIdResult getByCustomerId(ServiceContext serviceContext, CustomerAccountType.GetByCustomerIdArg customerIdArg) {
        CustomerAccountType.GetByCustomerIdResult getByCustomerIdResult = new CustomerAccountType.GetByCustomerIdResult();

        IObjectData objectData = customerAccountManager.getCustomerAccountIncludeInvalidByCustomerId(serviceContext.getUser(), customerIdArg.getCustomerId())
                .orElseThrow(() -> new ValidateException(I18N.text(CAI18NKey.FUND_ACCOUNT_NOT_EXIST)));
        if (ConfigCenter.tenantIdForOpenApi.contains(serviceContext.getTenantId())) {
            //客户账户 IT对接企业 不需要动态计算 直接展示数据库中的值即可
            getByCustomerIdResult.setObjectData(ObjectDataExt.toMap(objectData));
            return getByCustomerIdResult;
        }

        String customerId = ObjectDataUtil.getReferenceId(objectData, CustomerAccountConstants.Field.Customer.apiName);
        BigDecimal usedCredit = crmManager.getUsedCreditAmount(serviceContext.getUser(), customerId);
        BigDecimal tempCreditQuota = ObjectDataUtil.getBigDecimal(objectData, CustomerAccountConstants.Field.CreditTemporaryQuota.apiName);
        BigDecimal creditQuota = ObjectDataUtil.getBigDecimal(objectData, CustomerAccountConstants.Field.CreditQuota.apiName);

        objectData.set(CustomerAccountConstants.Field.UsedCreditQuota.apiName, usedCredit);
        objectData.set(CustomerAccountConstants.Field.CreditAvailableQuota.apiName, creditQuota.add(tempCreditQuota).subtract(usedCredit));

        getByCustomerIdResult.setObjectData(ObjectDataExt.toMap(objectData));
        return getByCustomerIdResult;
    }

    @Override
    public CustomerAccountType.IsCreditEnableResult isCreditEnable(ServiceContext serviceContext) {
        CustomerAccountType.IsCreditEnableResult isCreditEnableResult = new CustomerAccountType.IsCreditEnableResult();
        isCreditEnableResult.setEnable(true);
        return isCreditEnableResult;
    }

    @Override
    public CustomerAccountType.GetAvailableCreditResult getAvailableCredit(ServiceContext serviceContext, CustomerAccountType.GetAvailableCreditArg getAvailableCreditArg) {
        String customerId = getAvailableCreditArg.getCustomerId();
        User user = serviceContext.getUser();
        CustomerAccountType.GetAvailableCreditResult getAvailableCreditResult = new CustomerAccountType.GetAvailableCreditResult();
        BigDecimal availableCredit = customerAccountManager.getAvailableCredit(user, customerId);
        getAvailableCreditResult.setAvailableCredit(availableCredit.doubleValue());
        return getAvailableCreditResult;
    }

    /**
     * 该接口是：获取其实是把3个接口合起来了，1).获取可用信用  2).信用是否开启  3).获取客户账户信息
     */
    @SuppressWarnings("unchecked")
    @Override
    public CustomerAccountType.GetCustomerAccountAndCreditInfoResult getCustomerAccountAndCreditInfo(ServiceContext serviceContext,
                                                                                                     CustomerAccountType.GetCustomerAccountAndCreditInfoArg getCustomerAccountAndCreditInfoArg) {
        BigDecimal exchangeRate = getCustomerAccountAndCreditInfoArg.getExchangeRate();
        if (Objects.isNull(exchangeRate) || exchangeRate.compareTo(BigDecimal.ZERO) <= 0) {
            exchangeRate = BigDecimal.ONE;
        }
        CustomerAccountType.GetCustomerAccountAndCreditInfoResult result = new CustomerAccountType.GetCustomerAccountAndCreditInfoResult();
        CustomerAccountType.CustomerAccountEnableSwitchStatus customerAccountEnableSwitchStatus = customerAccountConfigManager.getStatus(serviceContext.getTenantId());
        if (customerAccountEnableSwitchStatus != CustomerAccountType.CustomerAccountEnableSwitchStatus.ENABLE) {
            return result;
        }
        User user = serviceContext.getUser();
        String customerId = getCustomerAccountAndCreditInfoArg.getCustomerId();
        result.setCreditEnable(true);
        Map<String, String> configMap = configService.queryTenantConfigs(user, Lists.newArrayList(ConfigKeyEnum.CREATE_PAYMENT_WHILE_CREATE_ORDER.key, ConfigKeyEnum.CREATE_ORDER_LIMIT_CONFIG.key, ConfigKeyEnum.NEW_CUSTOMER_ACCOUNT.key, ConfigKeyEnum.ACCOUNT_CHECK_RULE.key));
        boolean newCustomerAccountEnable = ConfigKeyEnum.NEW_CUSTOMER_ACCOUNT.enabled(configMap.get(ConfigKeyEnum.NEW_CUSTOMER_ACCOUNT.key));
        //可用信用
        BigDecimal availableCredit = customerAccountManager.getAvailableCredit(user, customerId, true);
        result.setAvailableCredit(availableCredit.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        //客户账户余额 优化
        IObjectData customerAccount = customerAccountManager.getCustomerAccountByCustomerId(user, customerId);
        List<String> settleTypes = (List<String>) customerAccount.get(CustomerAccountConstants.Field.SettleType.apiName);
        List<SettleType> settleTypeEnumList = getSettleTypeList(serviceContext, settleTypes);
        BigDecimal prepayAvailableBalance = ObjectDataUtil.getBigDecimal(customerAccount, CustomerAccountConstants.Field.PrepayAvailableBalance.apiName);
        if (newCustomerAccountEnable) {
            prepayAvailableBalance = customerAccountManager.getNewCustomerAccountBalanceByCustomerId(user, customerId, ConfigKeyEnum.ACCOUNT_CHECK_RULE.enabled(configMap.get(ConfigKeyEnum.ACCOUNT_CHECK_RULE.key)));
        }
        BigDecimal availableRebateAmount = ObjectDataUtil.getBigDecimal(customerAccount, CustomerAccountConstants.Field.RebateAvailableBalance.apiName);
        BigDecimal creditQuota = ObjectDataUtil.getBigDecimal(customerAccount, CustomerAccountConstants.Field.CreditQuota.apiName);
        result.setAvailablePrepayAmount(prepayAvailableBalance.divide(exchangeRate, 2, BigDecimal.ROUND_HALF_UP).toString());
        result.setAvailableRebateAmount(availableRebateAmount.divide(exchangeRate, 2, BigDecimal.ROUND_HALF_UP).toString());
        result.setCreditQuota(creditQuota.divide(exchangeRate, 2, BigDecimal.ROUND_HALF_UP).toString());
        result.setSettleTypeEnumList(settleTypeEnumList);
        //是否需要弹窗创建回款
        String createPaymentSwitch = configMap.get(CREATE_PAYMENT_SWITCH);//configService.findTenantConfig(user, CREATE_PAYMENT_SWITCH);
        //新建订单配置
        String createOrderLimitConfig = configMap.get(CREATE_ORDER_LIMIT_CONFIG);//configService.findTenantConfig(user, CREATE_ORDER_LIMIT_CONFIG);
        if (StringUtils.isEmpty(createOrderLimitConfig)) {
            createOrderLimitConfig = CreateOrderLimitConfigEnum.TERMINATE.getConfig();
        }
        result.setNeedCreatePayment(CreatePaymentSwitchEnum.paymentOpened(createPaymentSwitch));
        result.setNeedCreateOrderPayment(CreatePaymentSwitchEnum.orderPaymentOpened(createPaymentSwitch));
        result.setCreateOrderLimitConfig(createOrderLimitConfig);
        //是否灰度
        if (gray.isAllow("grayRule", customerAccount.getTenantId())) {
            result.setNeedSpecialHandling(true);
        }
        return result;
    }

    @Override
    public GetCustomerAccountModel.Result getCustomerAccountInfo(ServiceContext serviceContext, GetCustomerAccountModel.Arg arg) {
        GetCustomerAccountModel.Result result = new GetCustomerAccountModel.Result();
        if (Objects.isNull(arg.getCustomerId())) {
            log.warn("customerId is null");
            return result;
        }
        CustomerAccountType.CustomerAccountEnableSwitchStatus customerAccountEnableSwitchStatus = customerAccountConfigManager.getStatus(serviceContext.getTenantId());
        if (!CustomerAccountType.CustomerAccountEnableSwitchStatus.ENABLE.equals(customerAccountEnableSwitchStatus)) {
            log.warn("customer account switch status is false ");
            return result;
        }
        User user = serviceContext.getUser();
        //客户账户余额
        IObjectData objectData = customerAccountManager.getCustomerAccountByCustomerId(user, arg.getCustomerId());
        if (objectData == null) {
            log.warn("getCustomizationCustomerAccountAndCreditInfo is empty");
            return result;
        }
        //  信用可用额度-----兼容老的数据---可用信用
        BigDecimal availableCredit = customerAccountManager.getCustomerAccountAvailableCredit(user, arg.getCustomerId(), objectData);
        objectData.set(CustomerAccountConstants.Field.CreditAvailableQuota.apiName, availableCredit);
        Map<String, Object> objectMap = ObjectDataExt.of(objectData).toMap();
        String createOrderLimitConfig = configService.findTenantConfig(user, CREATE_ORDER_LIMIT_CONFIG);
        //新建订单配置
        objectMap.put("create_order_limit_config", Objects.isNull(createOrderLimitConfig) ? CreateOrderLimitConfigEnum.TERMINATE.getConfig() : createOrderLimitConfig);
        objectMap.put("credit_enable", true);
        List<String> settleTypes = (List<String>) objectData.get(CustomerAccountConstants.Field.SettleType.apiName);
        List<SettleType> settleTypeEnumList = getSettleTypeList(serviceContext, settleTypes);
        result.setSettleTypeEnumList(settleTypeEnumList);
        result.setObjectData(objectMap);
        return result;
    }

    private List<CustomerAccountType.SettleType> getSettleTypeList(ServiceContext serviceContext, List<String> settleTypes) {
        Set<String> settleTyps = Sets.newHashSet(settleTypes);
        List<CustomerAccountType.SettleType> settleTypeList = new ArrayList<CustomerAccountType.SettleType>();
        for (SettleTypeEnum settleTypeObj : SettleTypeEnum.values()) {
            if (settleTyps.contains(settleTypeObj.getValue())) {
                CustomerAccountType.SettleType settleType = new CustomerAccountType.SettleType();
                settleType.setValue(settleTypeObj.getValue());
                settleType.setLabel(settleTypeObj.getLabel());
                settleType.setNotUsable(settleTypeObj.getNotUsable());
                settleTypeList.add(settleType);
            }
        }
        return settleTypeList;
    }

    /**
     * 1.初始化描述<br> 2.初始化数据<br> 3.设置开关<br>
     */
    @Override
    public CustomerAccountType.EnableCustomerAccountResult enableCustomerAccount(ServiceContext serviceContext) {
        String tenantId = serviceContext.getUser().getTenantId();
        if (!ConfigCenter.isInNewPaymentBlank(tenantId)) {
            Set<String> moduleList = serviceFacade.getModule(serviceContext.getTenantId());
            if (CollectionUtils.isEmpty(moduleList) || !moduleList.contains(CUSTOMER_ACCOUNT_VERSION_KEY)) {
                throw new ValidateException(I18N.text(CAI18NKey.CA_LICENSE_VALIDATE));
            }
            return enableNewCustomerAccount(serviceContext);
        } else {
            throw new ValidateException(I18N.text(CAI18NKey.OLD_CA_NOT_SUPPORT_UPGRADE_PAYMENT_FIRST));
        }
    }

    private CustomerAccountType.EnableCustomerAccountResult enableNewCustomerAccount(ServiceContext serviceContext) {
        long start = System.currentTimeMillis();
        String tenantId = serviceContext.getUser().getTenantId();
        User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
        CustomerAccountType.EnableCustomerAccountResult result = new CustomerAccountType.EnableCustomerAccountResult();
        //判断是否已经开启或正在开启中
        FundAccountSwitchEnum fundAccountStatus = fundAccountManager.getFundAccountStatus(tenantId);
        if (FundAccountSwitchEnum.FUND_ACCOUNT_OPEN == fundAccountStatus || FundAccountSwitchEnum.FUND_ACCOUNT_IS_OPENING == fundAccountStatus) {
            log.info("customer account is already open or is opening,tenantId:{},status:{}", tenantId, fundAccountStatus);
            result.setEnableStatus(Integer.parseInt(fundAccountStatus.getStatus()));
            result.setMessage(fundAccountStatus.getMessage());
            return result;
        }
        try {
            //开关设为【开启中】
            fundAccountConfigManager.updateFundAccountStatus(user, FundAccountSwitchEnum.FUND_ACCOUNT_IS_OPENING);
            //初始化描述和数据
            fundAccountManager.batchInitFundAccounts(serviceContext);
            //进行开启开关
            fundAccountConfigManager.updateFundAccountStatus(user, FundAccountSwitchEnum.FUND_ACCOUNT_OPEN);
            //客户账户开启后,发送MQ通知订货通做业务
            mqProducerManager.sendCustomerAccountEnableMQ(serviceContext.getUser().getTenantId(), true);
            result.setEnableStatus(Integer.parseInt(FundAccountSwitchEnum.FUND_ACCOUNT_OPEN.getStatus()));
            result.setMessage(I18N.text(CAI18NKey.MODULE_OPENED));
            log.info("init fundAccountObj success, tenantId:{}, user:{}, cost:{}", tenantId, user, System.currentTimeMillis() - start);
        } catch (Exception e) {
            //回滚系统库开放对象配置
            ArrayList<String> apiNames = Lists.newArrayList(
                    FundAccountConstants.API_NAME,
                    NewCustomerAccountConstants.API_NAME,
                    AccountTransactionFlowConst.API_NAME
            );
            commonConfigManager.updateMetaDataOpenConfig(serviceContext.getUser(), apiNames);
            //关闭客户账户开关
            fundAccountConfigManager.updateFundAccountStatus(user, FundAccountSwitchEnum.FUND_ACCOUNT_CLOSED);
            log.error("init fundAccountObj failed, tenantId:{}, user:{}, cost:{}", tenantId, user, System.currentTimeMillis() - start, e);
            throw new CustomerAccountBusinessException(CustomerAccountErrorCode.FUND_ACCOUNT_DATA_SWITCH_ERROR, e.getMessage());
        }
        return result;
    }

    @Override
    public CustomerAccountType.EnableAccountCheck enableAccountCheck(ServiceContext serviceContext) {
        RLock lock = null;
        CustomerAccountType.EnableAccountCheck result;
        try {
            //加锁
            lock = LockUtil.enableAccountCheckLock((serviceContext.getTenantId()));
            log.info("enableAccountCheck enableAccountCheckLock lock[{}]", lock);
            if (Objects.isNull(lock)) {
                throw new ValidateException(I18N.text(CAI18NKey.ENABLE_ACCOUNT_CHECK_LOCK_ERROR));
            }
            result = enableAccountCheckRule(serviceContext, false);
        } finally {
            LockUtil.unlock(lock);
            log.info("enableAccountCheck unlock lock[{}]", lock);
        }
        return result;
    }

    @Override
    public CustomerAccountType.EnableAccountCheck enableAccountCheckRule(ServiceContext serviceContext, boolean transfer870Data) {
        long begin = System.currentTimeMillis();
        String tenantId = serviceContext.getTenantId();
        User user = serviceContext.getUser();

        CustomerAccountType.EnableAccountCheck enableResult = new CustomerAccountType.EnableAccountCheck();

//        Set<String> moduleList = serviceFacade.getModule(serviceContext.getTenantId());
//        if (CollectionUtils.isEmpty(moduleList) || !moduleList.contains(ACCOUNTS_RECEIVABLE_VERSION_KEY)) {
//            throw new ValidateException(I18N.text(ArI18NKey.CA_LICENSE_VALIDATE));
//        }

        //是否已开启
        AccountCheckSwitchEnum switchStatus = fundAccountConfigManager.getAccountCheckStatus(tenantId);
        log.info("enableAccountCheck, switchStatus[{}]", switchStatus);
        if (Objects.equals(switchStatus.getStatus(), AccountCheckSwitchEnum.OPENED.getStatus())) {
            enableResult.setEnableStatus(AccountCheckSwitchEnum.OPENED.getStatus());
            enableResult.setMessage(I18N.text("AccountCheckRuleObj.errorinfo.CustomerAccountsServiceImpl.607"));
            return enableResult;
        }
        RequestContext requestContext = serviceContext.getRequestContext();
        boolean consumerCall = Constants.CA_CUSTOMER_ACCOUNT_CONSUMER_PEER_NAME.equals(requestContext.getPeerName());
        if (switchStatus == AccountCheckSwitchEnum.OPENING && !consumerCall) {
            throw new ValidateException(I18N.text(CAI18NKey.CHECK_RULE_OPENING));
        }
        //客户账户是否开启
        FundAccountSwitchEnum fundAccountSwitchEnum = fundAccountManager.getFundAccountStatus(tenantId);
        log.info("enableAccountCheck, fundAccountSwitchEnum[{}]", fundAccountSwitchEnum);
        if (Objects.equals(fundAccountSwitchEnum.getStatus(), FundAccountSwitchEnum.FUND_ACCOUNT_CLOSED.getStatus())) {
            throw new ValidateException(I18N.text(CAI18NKey.CUSTOMER_ACCOUNT_NOT_OPEN));
        }
        //账户授权需要开启
        AccountAuthSwitchEnum accountAuthSwitchStatus = fundAccountConfigManager.getAccountAuthStatus(tenantId);
        log.info("enableAccountCheck, accountAuthSwitchStatus[{}]", accountAuthSwitchStatus);
        if (!Objects.equals(accountAuthSwitchStatus.getStatus(), AccountAuthSwitchEnum.OPENED.getStatus())) {
            throw new ValidateException(I18N.text(CAI18NKey.ACCOUNT_AUTH_NOT_OPEN));
        }
        boolean countExceed = fundAccountManager.newCustomerAccountCountExceed(user);
        if (!consumerCall && countExceed) {
            AsyncEnableAccountCheckRuleModel asyncEnableAccountCheckRuleModel = new AsyncEnableAccountCheckRuleModel();
            asyncEnableAccountCheckRuleModel.setTenantId(tenantId);
            asyncEnableAccountCheckRuleModel.setUserId(user.getUserId());
            Message message = new Message(Constants.CUSTOMER_ACCOUNT_TOPIC, Constants.ASYNC_ENABLE_ACCOUNT_CHECK_RULE_TAG, JSON.toJSONBytes(asyncEnableAccountCheckRuleModel));
            fundAccountConfigManager.updateAccountCheckStatus(user, AccountCheckSwitchEnum.OPENING);
            mqProducerManager.sendCustomerAccountMessage(message);
            throw new ValidateException(I18N.text(CAI18NKey.ENABLE_CHECK_RULE_DATA_INIT_COUNT_LIMIT));
        }

        try {
            //初始化对象
            caInitManager.initAccountCheck(tenantId, serviceContext.getUser());
            log.info("initAccountCheck success");
            fundAccountManager.upgradeAdvancedCustomerAccount(user);
            log.info("upgradeAdvancedCustomerAccount success");
            fundAccountManager.fillNewCustomerAccountField(user);

            String accountCheckRuleStatus = accountCheckRuleManager.getSalesOrderAccountCheckRuleStatus(user, transfer870Data);
            //新建订单相关的支出类型的'账户授权'数据
            fAccountAuthorizationManager.saveSalesOrderInitOutcomeAuthData(serviceContext);
            //新建订单'组件扣减'的校验规则数据
            accountCheckRuleManager.saveComponentReduceAccountCheckRuleForSaleOrder(user, transfer870Data, accountCheckRuleStatus);

            //打开开关
            fundAccountConfigManager.updateAccountCheckStatus(user, AccountCheckSwitchEnum.OPENED);
            mqProducerManager.sendAccountCheckRuleEnableMQ(user);
            crmBizConfigManager.updateConfig(user, BizConfigKey.CheckRuleFlowWhetherInvalidAfterCancel.getKey(), CheckRuleFlowWhetherInvadWhenChangeEnum.NotInvalid.value, null);
            enableResult.setEnableStatus(AccountCheckSwitchEnum.OPENED.getStatus());
            enableResult.setMessage(AccountCheckSwitchEnum.OPENED.getMessage());
            log.info("enableAccountCheck init success, tenantId:{}, user:{}, cost:{}", tenantId, user, System.currentTimeMillis() - begin);
        } catch (Exception e) {
            log.error("enableAccountCheck init failed, tenantId:{}, user:{}, cost:{}", tenantId, user, System.currentTimeMillis() - begin, e);
            fundAccountConfigManager.updateAccountCheckStatus(user, AccountCheckSwitchEnum.OPEN_FAIL);
            //回滚系统库开放对象配置
            ArrayList<String> apiNames = Lists.newArrayList(
                    AccountCheckRuleConstants.API_NAME,
                    AccountFrozenRecordConstant.API_NAME,
                    UnfreezeDetailConstant.API_NAME
            );
            commonConfigManager.updateMetaDataOpenConfig(serviceContext.getUser(), apiNames);

            enableResult.setEnableStatus(AccountCheckSwitchEnum.OPEN_FAIL.getStatus());
            enableResult.setMessage(AccountCheckSwitchEnum.OPEN_FAIL.getMessage());
            throw new FundAccountException(FundAccountErrorCode.ACCOUNT_CHECK_SWITCH_ERROR, FundAccountErrorCode.ACCOUNT_CHECK_SWITCH_ERROR.getMessage() + "." + e.getMessage());
        }

        return enableResult;
    }

    @Override
    public Map<String, Object> fillNewCustomerAccountField(ServiceContext serviceContext) {
        User user = serviceContext.getUser();
        fundAccountManager.upgradeAdvancedCustomerAccount(user);
        fundAccountManager.fillNewCustomerAccountField(user);
        return Maps.newHashMap();
    }

    @Override
    public CustomerAccountType.InvalidCustomerAccountResult invalidCustomerAccount(ServiceContext serviceContext, CustomerAccountType.InvalidCustomerAccountArg invalidCustomerAccountArg) {
        IObjectData customerAccountObjectData = customerAccountManager.getCustomerAccountByCustomerId(serviceContext.getUser(), invalidCustomerAccountArg.getCustomerId());

        String lifeStatus = invalidCustomerAccountArg.getLifeStatus();
        if (Objects.isNull(lifeStatus)) {
            throw new ValidateException("While Invalid prepay id is null");
        }
        Map<String, Object> params = Maps.newHashMap();
        params.put("lifeStatus", lifeStatus);

        ObjectDataDocument resultObjectData = this.triggerInvalidAction(serviceContext, CustomerAccountConstants.API_NAME, customerAccountObjectData.getId(), null);
        CustomerAccountType.InvalidCustomerAccountResult invalidCustomerAccountResult = new CustomerAccountType.InvalidCustomerAccountResult();
        invalidCustomerAccountResult.setSuccess(Objects.nonNull(resultObjectData));
        return invalidCustomerAccountResult;
    }

    /**
     * 创建客户的时候，如果客户账户开关已经打开会调用该接口创建对应的客户账户<br>
     */
    @Override
    public CustomerAccountType.CreateCustomerAccountResult createCustomerAccount(ServiceContext serviceContext, CustomerAccountType.CreateCustomerAccountArg createCustomerAccountArg) {
        log.info("begin create CustomerAccount,for customerId:{}", createCustomerAccountArg.getCustomerId());
        Map<String, String> customerIdLifeStatusMap = Maps.newHashMap();
        customerIdLifeStatusMap.put(createCustomerAccountArg.getCustomerId(), createCustomerAccountArg.getLifeStatus());
        List<IObjectData> result = customerAccountManager.batchInitCustomerAccountDatas(serviceContext.getUser(), customerIdLifeStatusMap);
        log.debug("InitCustomerAccount,cutomerId:{}, Result:{}", createCustomerAccountArg.getCustomerId(), result);
        CustomerAccountType.CreateCustomerAccountResult createCustomerAccountResult = new CustomerAccountType.CreateCustomerAccountResult();
        createCustomerAccountResult.setCustomerAccountId(result.get(0).getId());
        createCustomerAccountResult.setCustomerId(createCustomerAccountArg.getCustomerId());
        return createCustomerAccountResult;
    }

    @Override
    public CustomerAccountType.BulkCreateCustomerAccountResult bulkCreateCustomerAccount(ServiceContext serviceContext, CustomerAccountType.BulkInitCustomerAccountArg bulkInitCustomerAccountArg) {
        CustomerAccountType.BulkCreateCustomerAccountResult bulkCreateCustomerAccountResult = new CustomerAccountType.BulkCreateCustomerAccountResult();
        List<String> customerIds = bulkInitCustomerAccountArg.getCustomerIds();
        if (CollectionUtils.isEmpty(customerIds)) {
            return bulkCreateCustomerAccountResult;
        }
        Map<String, String> customerIdLifeStatusMap = Maps.newHashMap();
        customerIds.forEach(customerId -> customerIdLifeStatusMap.put(customerId, bulkInitCustomerAccountArg.getLifeStatus()));
        customerAccountManager.batchInitCustomerAccountDatas(serviceContext.getUser(), customerIdLifeStatusMap);
        return bulkCreateCustomerAccountResult;
    }

    @Override
    public SfaBulkInvalidModel.Result bulkInvalidCustomerAccount(ServiceContext serviceContext, SfaBulkInvalidModel.Arg sfaBulkInvalidModelArg) {
        List<IObjectData> customerAccountObjectDataList = customerAccountManager.listCustomerAccountByCustomerIds(serviceContext.getUser(), sfaBulkInvalidModelArg.getDataIds());
        Map<String, IObjectData> customerAccountDataMap = customerAccountObjectDataList.stream()
                .collect(Collectors.toMap(objectData -> ObjectDataUtil.getReferenceId(objectData, CustomerAccountConstants.Field.Customer.apiName), ob -> ob));
        BulkInvalidModel.Arg bulkInvalidModelArg = new BulkInvalidModel.Arg();
        for (String customerId : sfaBulkInvalidModelArg.getDataIds()) {
            BulkInvalidModel.InvalidArg invalidArg = new BulkInvalidModel.InvalidArg();
            // 获取客户账户id
            IObjectData customerAccountObjectData = customerAccountDataMap.get(customerId);//customerAccountManger.getCustomerAccountByCustomerId(serviceContext.getUser(), customerId);
            if (Objects.isNull(customerAccountObjectData)) {
                log.warn("CustomerId[{}]对应的客户账户不存在", customerId);
                continue;
            }
            invalidArg.setId(customerAccountObjectData.getId());
            invalidArg.setLifeStatus(sfaBulkInvalidModelArg.getLifeStatus());
            invalidArg.setObjectDescribeApiName(CustomerAccountConstants.API_NAME);
            if (null == bulkInvalidModelArg.getDataList()) {
                bulkInvalidModelArg.setDataList(new ArrayList<BulkInvalidModel.InvalidArg>());
            }
            bulkInvalidModelArg.getDataList().add(invalidArg);
        }

        StandardBulkInvalidAction.Arg standardBulkInvalidActionArg = new StandardBulkInvalidAction.Arg();
        standardBulkInvalidActionArg.setJson(JsonUtil.toJson(bulkInvalidModelArg));
        Map<String, Object> params = Maps.newHashMap();
        params.put("lifeStatus", sfaBulkInvalidModelArg.getLifeStatus());
        StandardBulkInvalidAction.Result result = this.triggerAction(serviceContext, CustomerAccountConstants.API_NAME, StandardAction.BulkInvalid.name(), standardBulkInvalidActionArg, params, StandardBulkInvalidAction.Result.class);
        return new SfaBulkInvalidModel.Result(result.getObjectDataList());
    }

    @Override
    public CreatePaymentSwitchModel.Result updatePaymentSwitch(ServiceContext serviceContext, CreatePaymentSwitchModel.Arg arg) {
        CreatePaymentSwitchModel.Result result = new CreatePaymentSwitchModel.Result();
        if (!CreatePaymentSwitchEnum.contain(arg.getCreatePaymentSwitch())) {
            throw new ValidateException(I18N.text(CAI18NKey.PARAMS_ERROR));
        }
        String configValue = configService.findTenantConfig(serviceContext.getUser(), CREATE_PAYMENT_SWITCH);
        if (StringUtils.isEmpty(configValue)) {
            configService.createTenantConfig(serviceContext.getUser(), CREATE_PAYMENT_SWITCH, arg.getCreatePaymentSwitch(), ConfigValueType.STRING);
        } else if (!arg.getCreatePaymentSwitch().equals(configValue)) {
            configService.updateTenantConfig(serviceContext.getUser(), CREATE_PAYMENT_SWITCH, arg.getCreatePaymentSwitch(), ConfigValueType.STRING);
        }
        return result;
    }

    @Override
    public UpdateOrderLimitConfigModel.Result updateCreateOrderLimitConfig(ServiceContext serviceContext, UpdateOrderLimitConfigModel.Arg arg) {
        String config = arg.getCreateOrderLimitConfig();
        CreateOrderLimitConfigEnum.get(config).orElseThrow(() -> new ValidateException(I18N.text(CAI18NKey.PARAMS_ERROR)));
        String dbConfig = configService.findTenantConfig(serviceContext.getUser(), CREATE_ORDER_LIMIT_CONFIG);
        if (StringUtils.isEmpty(dbConfig)) {
            configService.createTenantConfig(serviceContext.getUser(), CREATE_ORDER_LIMIT_CONFIG, config, ConfigValueType.STRING);
        } else if (!Objects.equals(config, dbConfig)) {
            configService.updateTenantConfig(serviceContext.getUser(), CREATE_ORDER_LIMIT_CONFIG, config, ConfigValueType.STRING);
        }
        return new UpdateOrderLimitConfigModel.Result();
    }

    @Override
    public GetCustomerAccountConfigModel.Result getCustomerAccountConfig(ServiceContext serviceContext) {
        GetCustomerAccountConfigModel.Result result = new GetCustomerAccountConfigModel.Result();
        List<String> keys = Lists.newArrayList(ConfigKeyEnum.CREATE_PAYMENT_WHILE_CREATE_ORDER.key, ConfigKeyEnum.CREATE_ORDER_LIMIT_CONFIG.key, ConfigKeyEnum.NEW_CUSTOMER_ACCOUNT.key, ConfigKeyEnum.OLD_CUSTOMER_ACCOUNT.key,
                ConfigKeyEnum.ACCOUNT_CHECK_RULE.key, ConfigKeyEnum.CUSTOMER_ACCOUNT_EXCEED.key);
        Map<String, String> configMap = configService.queryTenantConfigs(serviceContext.getUser(), keys);
        String customerAccountConfig = configMap.get(ConfigKeyEnum.OLD_CUSTOMER_ACCOUNT.key);
        result.setCustomerAccountEnable(ConfigKeyEnum.OLD_CUSTOMER_ACCOUNT.enabled(customerAccountConfig));
        result.setCustomerAccountConfigValue(customerAccountConfig);

        String createPaymentSwitch = configMap.get(CREATE_PAYMENT_SWITCH);
        result.setNeedCreatePayment(CreatePaymentSwitchEnum.paymentOpened(createPaymentSwitch));
        result.setNeedCreateOrderPayment(CreatePaymentSwitchEnum.orderPaymentOpened(createPaymentSwitch));

        String createOrderLimitConfig = configMap.getOrDefault(ConfigKeyEnum.CREATE_ORDER_LIMIT_CONFIG.key, ConfigKeyEnum.CREATE_ORDER_LIMIT_CONFIG.defaultValue);
        result.setCreateOrderLimitConfig(createOrderLimitConfig);

        String newCustomerAccountConfig = configMap.get(ConfigKeyEnum.NEW_CUSTOMER_ACCOUNT.key);
        result.setNewCustomerAccountConfigValue(newCustomerAccountConfig);
        result.setNewCustomerAccountEnable(ConfigKeyEnum.NEW_CUSTOMER_ACCOUNT.enabled(newCustomerAccountConfig));

        String accountCheckConfig = configMap.get(ConfigKeyEnum.ACCOUNT_CHECK_RULE.key);
        result.setAccountCheckEnable(ConfigKeyEnum.ACCOUNT_CHECK_RULE.enabled(accountCheckConfig));
        result.setNewCustomerAccountExceedConfig(configMap.get(ConfigKeyEnum.CUSTOMER_ACCOUNT_EXCEED.key));

        return result;
    }

    @Override
    public StandardBulkDeleteAction.Result bulkDeleteCustomerAccount(ServiceContext serviceContext, CustomerAccountType.BulkDeleteCustomerAccountArg deleteCustomerAccountArg) {
        Map<String, Object> params = Maps.newHashMap();
        List<IObjectData> customerObjectDatas = customerAccountManager.listCustomerAccountIncludeInvalidByCustomerIds(serviceContext.getUser(), deleteCustomerAccountArg.getCustomerIds());
        if (CollectionUtils.isEmpty(customerObjectDatas)) {
            return StandardBulkDeleteAction.Result.builder().success(true).build();
        }
        List<String> customerAccountIds = customerObjectDatas.stream().map(IObjectData::getId).collect(Collectors.toList());
        StandardBulkDeleteAction.Arg arg = new StandardBulkDeleteAction.Arg();
        arg.setDescribeApiName(CustomerAccountConstants.API_NAME);
        arg.setIdList(customerAccountIds);
        return this.triggerAction(serviceContext, CustomerAccountConstants.API_NAME, StandardAction.BulkDelete.name(), arg, params, StandardBulkDeleteAction.Result.class);
    }

    @Override
    public EmptyResult unlockCustomerAccount(CustomerAccountType.UnlockCustomerAccountArg unlockCustomerAccountArg, ServiceContext serviceContext) {
        IObjectData customerAccountObj = customerAccountManager.getCustomerAccountByCustomerId(serviceContext.getUser(), unlockCustomerAccountArg.getCustomerId());
        BaseObjectLockAction.Arg arg = new BaseObjectLockAction.Arg();
        arg.setDataIds(Lists.newArrayList(customerAccountObj.getId()));
        this.triggerAction(serviceContext, CustomerAccountConstants.API_NAME, StandardAction.Unlock.name(), arg, BaseObjectLockAction.Result.class);
        return new EmptyResult();
    }

    @Override
    public EmptyResult lockCustomerAccount(CustomerAccountType.LockCustomerAccountArg lockCustomerAccountArg, ServiceContext serviceContext) {
        IObjectData customerAccountObj = customerAccountManager.getCustomerAccountByCustomerId(serviceContext.getUser(), lockCustomerAccountArg.getCustomerId());
        BaseObjectLockAction.Arg arg = new BaseObjectLockAction.Arg();
        arg.setDataIds(Lists.newArrayList(customerAccountObj.getId()));
        this.triggerAction(serviceContext, CustomerAccountConstants.API_NAME, StandardAction.Lock.name(), arg, BaseObjectLockAction.Result.class);
        return new EmptyResult();
    }


    @Override
    public Result flowComplete(Arg arg, ServiceContext serviceContext) {
        FlowCompleteModel.Result result = new FlowCompleteModel.Result();
        String lifeStatus = arg.getLifeStatus();
        String passStatus = RebateIncomeDetailFlowCompletedAction.Arg.PASS;
        String customerId = arg.getDataId();
        ApprovalFlowTriggerType approvalFlowTriggerType = null;
        if (ApprovalFlowTriggerType.CREATE.getId().equals(arg.getApprovalType())) {
            if (SystemConstants.LifeStatus.Ineffective.value.equals(lifeStatus)) {
                passStatus = "notPass";
            } else {
                IObjectData objectData = customerAccountManager.getCustomerAccountByCustomerId(serviceContext.getUser(), customerId);
                String oldLifeStatus = objectData.get(SystemConstants.Field.LifeStatus.apiName, String.class);
                if (SystemConstants.LifeStatus.Normal.value.equals(lifeStatus)) {
                    passStatus = "pass";
                    if (SystemConstants.LifeStatus.Ineffective.value.equals(oldLifeStatus)) {
                        //新建客戶 沒审批流 更新客户账户状态ineffective->normal
                        objectData.set(SystemConstants.Field.LifeStatus.apiName, lifeStatus);
                        serviceFacade.updateObjectData(serviceContext.getUser(), objectData);
                        return result;
                    }
                } else if (SystemConstants.LifeStatus.UnderReview.value.equals(lifeStatus)) {
                    result.setSuccess(true);
                    log.info("noChange flowComplete, arg={}", arg);
                    //新建客户驳回，然后编辑到这个分支了
                    if (!lifeStatus.equals(oldLifeStatus)) {
                        objectData.set(SystemConstants.Field.LifeStatus.apiName, lifeStatus);
                        serviceFacade.updateObjectData(serviceContext.getUser(), objectData);
                    }
                    return result;
                }
            }
            approvalFlowTriggerType = ApprovalFlowTriggerType.CREATE;
        } else if (ApprovalFlowTriggerType.INVALID.getId().equals(arg.getApprovalType())) {
            if (SystemConstants.LifeStatus.Ineffective.value.equals(lifeStatus)) {
                passStatus = "notPass";
            } else if (SystemConstants.LifeStatus.Invalid.value.equals(lifeStatus)) {
                passStatus = "pass";
            } else if (SystemConstants.LifeStatus.Normal.value.equals(lifeStatus)) {
                passStatus = "notPass";
            } else if (SystemConstants.LifeStatus.InChange.value.equals(lifeStatus)) {
                result.setSuccess(true);
                log.info("noChange flowComplete, arg={}", arg);
                return result;
            }
            approvalFlowTriggerType = ApprovalFlowTriggerType.INVALID;
        } else if (ApprovalFlowTriggerType.UPDATE.getId().equals(arg.getApprovalType())) {
            if (SystemConstants.LifeStatus.Normal.value.equals(lifeStatus)) {
                passStatus = "pass";
            } else {
                passStatus = "notPass";
            }
            approvalFlowTriggerType = ApprovalFlowTriggerType.UPDATE;
        } else {
            throw new ValidateException(I18N.text(CAI18NKey.SPECIFIED_PARAM_ERROR, "ApprovalType"));
        }
        IObjectData objectData = customerAccountManager.getCustomerAccountByCustomerId(serviceContext.getUser(), customerId);
        StandardFlowCompletedAction.Arg arg1 = new StandardFlowCompletedAction.Arg();
        arg1.setDataId(objectData.getId());
        arg1.setDescribeApiName(objectData.getDescribeApiName());
        arg1.setTenantId(serviceContext.getTenantId());
        arg1.setUserId(serviceContext.getUser().getUserId());
        arg1.setTriggerType(String.valueOf(approvalFlowTriggerType.getTriggerTypeCode()));
        arg1.setStatus(passStatus);
        StandardFlowCompletedAction.Result flowResult = this.triggerAction(serviceContext, CustomerAccountConstants.API_NAME, "FlowCompleted", arg1, StandardFlowCompletedAction.Result.class);
        result.setSuccess(flowResult.getSuccess());
        return result;
    }

    @Override
    public EmptyResult bulkUnlockCustomerAccount(CustomerAccountType.BulkUnlockCustomerAccountArg bulkUnlockCustomerAccountArg, ServiceContext serviceContext) {
        List<String> customerAccountIdList = new ArrayList<>();
        for (String customerId : bulkUnlockCustomerAccountArg.getCustomerIds()) {
            IObjectData customerAccountObj = customerAccountManager.getCustomerAccountByCustomerId(serviceContext.getUser(), customerId);
            customerAccountIdList.add(customerAccountObj.getId());
        }

        BaseObjectLockAction.Arg arg = new BaseObjectLockAction.Arg();
        arg.setDataIds(customerAccountIdList);
        this.triggerAction(serviceContext, CustomerAccountConstants.API_NAME, StandardAction.Unlock.name(), arg, BaseObjectLockAction.Result.class);
        return new EmptyResult();
    }

    @Override
    public EmptyResult bulkLockCustomerAccount(CustomerAccountType.BulkLockCustomerAccountArg bulkLockCustomerAccountArg, ServiceContext serviceContext) {
        List<String> customerAccountIdList = new ArrayList<>();
        for (String customerId : bulkLockCustomerAccountArg.getCustomerIds()) {
            IObjectData customerAccountObj = customerAccountManager.getCustomerAccountByCustomerId(serviceContext.getUser(), customerId);
            customerAccountIdList.add(customerAccountObj.getId());
        }

        BaseObjectLockAction.Arg arg = new BaseObjectLockAction.Arg();
        arg.setDataIds(customerAccountIdList);
        this.triggerAction(serviceContext, CustomerAccountConstants.API_NAME, StandardAction.Lock.name(), arg, BaseObjectLockAction.Result.class);
        return new EmptyResult();
    }

    @Override
    public CustomerAccountType.BulkRecoverCustomerAccountResult bulkRecover(ServiceContext serviceContext, CustomerAccountType.BulkRecoverCustomerAccountArg bulkRecoverCustomerAccountArg) {
        List<String> customerIds = bulkRecoverCustomerAccountArg.getCustomerIds();
        List<ObjectDataDocument> objectDataDocumentList = Lists.newArrayList();
        StandardBulkRecoverAction.Arg customerRecoverArg = new StandardBulkRecoverAction.Arg();
        customerRecoverArg.setObjectDescribeAPIName(CustomerAccountConstants.API_NAME);
        List<String> customerAccountIds = Lists.newArrayList();
        List<String> customerAccountIdsOfNotInvalidLifeStatus = Lists.newArrayList();
        for (String customerId : customerIds) {
            IObjectData customerAccountData = customerAccountManager
                    .getDeletedObjByField(serviceContext.getUser(), CustomerAccountConstants.API_NAME, CustomerAccountConstants.Field.Customer.apiName, customerId);
            String lifeStatus = customerAccountData.get(SystemConstants.Field.LifeStatus.apiName, String.class);
            if (SystemConstants.LifeStatus.Invalid.value.equals(lifeStatus)) {
                customerAccountIds.add(customerAccountData.getId());
            } else if (Sets.newHashSet(SystemConstants.LifeStatus.Ineffective.value, SystemConstants.LifeStatus.Normal.value).contains(lifeStatus)) {
                customerAccountIdsOfNotInvalidLifeStatus.add(customerAccountData.getId());
                objectDataDocumentList.add(ObjectDataDocument.of(customerAccountData));
            }
        }
        if (CollectionUtils.isNotEmpty(customerAccountIdsOfNotInvalidLifeStatus)) {
            log.warn("customerAccountIdsNotInvalidLifeStatus:{}", customerAccountIdsOfNotInvalidLifeStatus);
        }
        if (CollectionUtils.isNotEmpty(customerAccountIds)) {
            customerRecoverArg.setIdList(customerAccountIds);
            StandardBulkRecoverAction.Result result = this.triggerAction(serviceContext, CustomerAccountConstants.API_NAME, StandardAction.BulkRecover.name(), customerRecoverArg, StandardBulkRecoverAction.Result.class);
            if (result.getSuccess() && CollectionUtils.isNotEmpty(result.getDataList())) {
                objectDataDocumentList.addAll(result.getDataList());
            }
        }
        return new CustomerAccountType.BulkRecoverCustomerAccountResult(objectDataDocumentList);
    }

    @Override
    public CustomerAccountType.MergeCustomerResult merge(ServiceContext serviceContext, CustomerAccountType.MergeCustomerArg arg) {
        log.info("merge arg:{}", arg);
        User user = serviceContext.getUser();
        String tenantId = user.getTenantId();
        List<String> sourceCustomerIds = arg.getSourceCustomerIds();

        List<String> allCustomerIds = Lists.newArrayList(arg.getMainCustomerId());
        allCustomerIds.addAll(sourceCustomerIds);
        List<IObjectData> customerAccountObjectDatas = customerAccountManager.listCustomerAccountIncludeInvalidByCustomerIds(user, allCustomerIds);
        if (arg.getRelativeObjectMerge() == null || arg.getRelativeObjectMerge().equals(Boolean.FALSE)) {
            CustomerAccountType.CanInvalidByCustomerIdsArg canInvalidByCustomerIdsArg = new CustomerAccountType.CanInvalidByCustomerIdsArg();
            canInvalidByCustomerIdsArg.setCustomerIds(arg.getSourceCustomerIds());
            CustomerAccountType.CanInvalidByCustomerIdsResult result = canInvalidByCustomerIds(serviceContext, canInvalidByCustomerIdsArg);
            if (!result.isSuccess()) {
                log.info("merge fail.errorReason:{}", result.getErrorReasons().toString());
                throw new ValidateException(I18N.text(CAI18NKey.BALANCE_NOT_ZERO));
            } else {
                deleteCustomerAccounts(serviceContext, customerAccountObjectDatas, arg.getMainCustomerId());
                return new CustomerAccountType.MergeCustomerResult("0", "ok");
            }
        }
        log.info("merge customerAccountObjectDatas:{}", customerAccountObjectDatas);
        Map<String, IObjectData> customerIdMap = customerAccountObjectDatas.stream()
                .collect(Collectors.toMap(ob -> ObjectDataUtil.getReferenceId(ob, CustomerAccountConstants.Field.Customer.apiName), o -> o));
        IObjectData mainObjectData = customerIdMap.get(arg.getMainCustomerId());
        if (Objects.isNull(mainObjectData)) {
            Map<String, String> customerIdLifeStatusMap = Maps.newHashMap();
            customerIdLifeStatusMap.put(arg.getMainCustomerId(), SystemConstants.LifeStatus.Normal.value);
            List<IObjectData> result = customerAccountManager.batchInitCustomerAccountDatas(serviceContext.getUser(), customerIdLifeStatusMap);
            mainObjectData = result.get(0);
        }
        String mainCustomerId = ObjectDataUtil.getReferenceId(mainObjectData, CustomerAccountConstants.Field.Customer.apiName);
        String mainCustomerAccountId = mainObjectData.getId();

        List<IObjectData> prepayObjectDatas;
        List<IObjectData> rebateObjectDatas;
        List<IObjectData> creditObjectDatas;
        int offset = 0;
        int limit = 500;
        do {
            List<String> updateFields = Lists.newArrayList(PrepayDetailConstants.Field.Customer.apiName, PrepayDetailConstants.Field.CustomerAccount.apiName);
            List<String> orderByFields = Lists.newArrayList(PrepayDetailConstants.Field.Customer.apiName, SystemConstants.Field.Id.apiName);
            prepayObjectDatas = listByCustomerId(tenantId, PrepayDetailConstants.API_NAME, PrepayDetailConstants.Field.Customer.apiName, sourceCustomerIds, orderByFields, offset, limit);
            if (CollectionUtils.isNotEmpty(prepayObjectDatas)) {
                prepayObjectDatas.forEach(prepayObjectData -> {
                    prepayObjectData.set(PrepayDetailConstants.Field.Customer.apiName, mainCustomerId);
                    prepayObjectData.set(PrepayDetailConstants.Field.CustomerAccount.apiName, mainCustomerAccountId);
                });
                serviceFacade.batchUpdateByFields(user, prepayObjectDatas, updateFields);
            }
        } while (CollectionUtils.isNotEmpty(prepayObjectDatas));

        do {
            List<String> updateFields = Lists.newArrayList(RebateIncomeDetailConstants.Field.Customer.apiName, RebateIncomeDetailConstants.Field.CustomerAccount.apiName);
            List<String> orderByFields = Lists.newArrayList(RebateIncomeDetailConstants.Field.Customer.apiName, SystemConstants.Field.Id.apiName);
            rebateObjectDatas = listByCustomerId(tenantId, RebateIncomeDetailConstants.API_NAME, RebateIncomeDetailConstants.Field.Customer.apiName, sourceCustomerIds, orderByFields, offset, limit);
            if (CollectionUtils.isNotEmpty(rebateObjectDatas)) {
                rebateObjectDatas.forEach(rebateObjectData -> {
                    rebateObjectData.set(RebateIncomeDetailConstants.Field.Customer.apiName, mainCustomerId);
                    rebateObjectData.set(RebateIncomeDetailConstants.Field.CustomerAccount.apiName, mainCustomerAccountId);
                });
                serviceFacade.batchUpdateByFields(user, rebateObjectDatas, updateFields);
            }
        } while (CollectionUtils.isNotEmpty(rebateObjectDatas));

        String maxCustomerId = deleteOfficialCredit(user, allCustomerIds, customerIdMap);
        do {
            List<String> updateFields = Lists.newArrayList(CreditFileConstants.Field.Customer.apiName);
            List<String> orderByFields = Lists.newArrayList(CreditFileConstants.Field.Customer.apiName, SystemConstants.Field.Id.apiName);
            creditObjectDatas = listByCustomerId(tenantId, CreditFileConstants.API_NAME, CreditFileConstants.Field.Customer.apiName, sourceCustomerIds, orderByFields, offset, limit);
            if (CollectionUtils.isNotEmpty(creditObjectDatas)) {
                creditObjectDatas.forEach(creditObjectData -> {
                    creditObjectData.set(CreditFileConstants.Field.Customer.apiName, mainCustomerId);
                });
                serviceFacade.batchUpdateByFields(user, creditObjectDatas, updateFields);
            }
        } while (CollectionUtils.isNotEmpty(creditObjectDatas));

        //账户金额放在后面合并，保证最终一致性就行
        mergeCustomerAccountMoney(serviceContext, customerAccountObjectDatas, mainObjectData, maxCustomerId);

        ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
        task.submit(() -> {
            //获取源数据
            List<String> sourceCustomerAccountIds = customerAccountObjectDatas.stream().filter(x -> arg.getSourceCustomerIds().contains(x.get(CustomerAccountConstants.Field.Customer.apiName, String.class))).map(IObjectData::getId).collect(Collectors.toList());
            customerAccountBillManager.mergeBill(serviceContext.getUser(), sourceCustomerAccountIds, mainCustomerAccountId);
        });

        deleteCustomerAccounts(serviceContext, customerAccountObjectDatas, mainCustomerId);
        return new CustomerAccountType.MergeCustomerResult("0", "ok");
    }

    /**
     * 客户账户对接 OpenApi
     */
    @Override
    public UpdateCreditModel.Result updateCredit(ServiceContext serviceContext, Map<String, Object> argMap) {
        User user = serviceContext.getUser();
        String tenantId = serviceContext.getTenantId();
        ObjectDataExt objectDataExt = ObjectDataExt.of(argMap);
        String customerId = objectDataExt.get(CustomerAccountConstants.Field.Customer.apiName, String.class);//arg.getCustomerId();
        BigDecimal availableCredit = objectDataExt.get(CustomerAccountConstants.Field.CreditAvailableQuota.apiName, BigDecimal.class);// arg.getAvailableCredit();
        BigDecimal usedCredit = objectDataExt.get(CustomerAccountConstants.Field.UsedCreditQuota.apiName, BigDecimal.class);//arg.getUsedCredit();

        if (!RequestUtil.isFromOpenApi(serviceContext.getRequestContext()) || !ConfigCenter.tenantIdForOpenApi.contains(tenantId)) {
            throw new ValidateException(I18N.text(CAI18NKey.NOT_SUPPORT_OPERATION));
        }
        if (StringUtils.isEmpty(customerId)) {
            throw new ValidateException(I18N.text(CAI18NKey.SPECIFIED_PARAM_ERROR,"customer_id"));
        }
        if (Objects.isNull(availableCredit) || Objects.isNull(usedCredit)) {
            throw new ValidateException(I18N.text(CAI18NKey.SPECIFIED_PARAM_ERROR,"credit_available_quota"));
        }

        IObjectData customerAccountData = customerAccountManager.getCustomerAccountIncludeInvalidByCustomerId(user, customerId).orElseThrow(() -> new ValidateException(I18N.text(CAI18NKey.FUND_ACCOUNT_NOT_EXIST)));
        BigDecimal oldAvailableCreditQuota = ObjectDataUtil.getBigDecimal(customerAccountData, CustomerAccountConstants.Field.CreditAvailableQuota.apiName);

        argMap.forEach(customerAccountData::set);
        IObjectData objectData = serviceFacade.updateObjectData(user, customerAccountData);
        customerAccountManager.recordLog(user, objectData);

        BigDecimal creditQuotaAmount = availableCredit.subtract(oldAvailableCreditQuota);
        if (creditQuotaAmount.compareTo(BigDecimal.ZERO) != 0) {
            String info = "UPDATE FOR OPEN API";
            String nonCreditFileId = "0";
            try {
                CustomerAccountRecordLogger.logCredit(customerAccountData.getId(), creditQuotaAmount, availableCredit, info, null);
                customerAccountBillManager.addCustomerAccountBillAccordCredit(user.getTenantId(), customerAccountData.getId(), nonCreditFileId, creditQuotaAmount.doubleValue(), info);
            } catch (Exception e) {
                log.error("updateCredit failed, user={} customerAccount={}", user, objectData, e);
                CustomerAccountRecordLogger.logCredit(customerAccountData.getId(), creditQuotaAmount, availableCredit, info, e);
            }
        }

        UpdateCreditModel.Result result = new UpdateCreditModel.Result();
        result.setObjectData(ObjectDataDocument.of(objectData));
        return result;
    }

    @Override
    public BulkUpdateSettleTypeModel.Result bulkUpdateSettleType(ServiceContext serviceContext, BulkUpdateSettleTypeModel.Arg arg) {
        List<String> settleTypes = arg.getSettleTypes();
        if (CollectionUtils.isEmpty(settleTypes)) {
            throw new ValidateException(I18N.text(CAI18NKey.SPECIFIED_PARAM_ERROR, "settleType"));
        }
        settleTypes.forEach(settleType -> SettleTypeEnum.getByValue(settleType).orElseThrow(() -> new ValidateException(I18N.text(CAI18NKey.SPECIFIED_PARAM_ERROR, "settleType"))));
        List<String> customerAccountIds = arg.getCustomerAccountIds();
        if (CollectionUtils.isEmpty(customerAccountIds)) {
            return new BulkUpdateSettleTypeModel.Result();
        }
        User user = serviceContext.getUser();
        List<IObjectData> customerAccountDatas = serviceFacade
                .findObjectDataByIdsIncludeDeleted(user, customerAccountIds, CustomerAccountConstants.API_NAME);//customerAccountManager.listCustomerAccountByCustomerIds(user, customerIds);
        if (CollectionUtils.isEmpty(customerAccountDatas)) {
            throw new ValidateException(I18N.text(CAI18NKey.DATA_NOT_EXIST));
        }
        customerAccountDatas.forEach(objectData -> objectData.set(CustomerAccountConstants.Field.SettleType.apiName, settleTypes));
        serviceFacade.batchUpdateByFields(serviceContext.getUser(), customerAccountDatas, Lists.newArrayList(CustomerAccountConstants.Field.SettleType.apiName));
        return new BulkUpdateSettleTypeModel.Result();
    }

    @Override
    public CustomerAccountType.CustomerListResult queryCustomersByCreateTime(ServiceContext serviceContext, CustomerAccountType.CustomerListArg customerListArg) {
        List<IObjectData> list = billJobManager
                .queryByCreateTime(serviceContext.getTenantId(), "AccountObj", customerListArg.getFrom(), customerListArg.getTo(), customerListArg.getOffset(), customerListArg.getLimit());
        CustomerAccountType.CustomerListResult customerListResult = new CustomerAccountType.CustomerListResult();
        customerListResult.setDataList(list);
        return customerListResult;
    }

    @Override
    public UpdatePaymentAvailableAmountDto.Result updatePaymentAvailableAmount(ServiceContext serviceContext, UpdatePaymentAvailableAmountDto.Arg arg) {
        String customerAccountId = arg.getCustomerAccountId();
        customerAccountManager.updatePaymentAvailableAmount(serviceContext.getUser(), customerAccountId);
        return new UpdatePaymentAvailableAmountDto.Result();
    }

    @Override
    public GetUsedCreditDto.Result getUsedCredit(ServiceContext serviceContext, GetUsedCreditDto.Arg arg) {
        String customerId = arg.getCustomerId();
        BigDecimal usedCredit = customerAccountManager.getUsedCredit(serviceContext.getUser(), customerId);
        GetUsedCreditDto.Result result = new GetUsedCreditDto.Result();
        result.setUsedCredit(usedCredit);
        return result;
    }

    @Override
    public CustomerAccountType.GetCheckObjectsAndReduceRuleObjects getCheckObjectsAndReduceRuleObjects(ServiceContext serviceContext) {
        //   if (true) {
        //       return ConfigCenter.getCheckObjectsAndReduceRuleObjects(serviceContext.getTenantId());
        //   }
        String tenantId = serviceContext.getTenantId();
        Lang lang = serviceContext.getRequestContext().getLang(); //zh-CN

        boolean fromConfigCenter = !CaGrayUtil.accountCheckConfigFromObject(tenantId);
        if (fromConfigCenter) {
            return getCheckObjectsAndReduceRuleObjectsFromConfigCenter(tenantId, lang);
        }
        return getCheckObjectsAndReduceRuleObjects(tenantId);
    }

    public CustomerAccountType.GetCheckObjectsAndReduceRuleObjects getCheckObjectsAndReduceRuleObjectsFromConfigCenter(String tenantId, Lang lang) {
        CustomerAccountType.GetCheckObjectsAndReduceRuleObjects checkObjectsAndReduceRuleObjects = ConfigCenter.getCheckObjectsAndReduceRuleObjects(tenantId);

        //有支出授权的objectApiNames
        List<String> outcomeAuthObjectApiNames = fAccountAuthorizationManager.getOutcomeAuthObjectApiNames(tenantId);

        CustomerAccountType.GetCheckObjectsAndReduceRuleObjects newCheckObjectsAndReduceRuleObjects = handleWithOutcomeAuth(tenantId, checkObjectsAndReduceRuleObjects, outcomeAuthObjectApiNames);

        // 对象名称多语
        handleLang(tenantId, newCheckObjectsAndReduceRuleObjects, outcomeAuthObjectApiNames, lang);
        return newCheckObjectsAndReduceRuleObjects;
    }

    private CustomerAccountType.GetCheckObjectsAndReduceRuleObjects getCheckObjectsAndReduceRuleObjects(String tenantId) {
        //从【账户授权】【解冻授权明细】获取
        User admin = User.systemUser(tenantId);
        List<IObjectData> allOutcomeAuthDatas = fAccountAuthorizationManager.getFAccountAuthorizationDatas(admin, FAccountAuthAuthorizedTypeEnum.Outcome.getValue(), null);

        CustomerAccountType.GetCheckObjectsAndReduceRuleObjects newCheckObjectsAndReduceRuleObjects = new CustomerAccountType.GetCheckObjectsAndReduceRuleObjects();

        //没有支出授权
        if (CollectionUtils.isEmpty(allOutcomeAuthDatas)) {
            newCheckObjectsAndReduceRuleObjects.setCheckObjectsForCheckReduce(Lists.newArrayList());
            newCheckObjectsAndReduceRuleObjects.setReduceRuleObjectsForDirectReduce(Lists.newArrayList());
            newCheckObjectsAndReduceRuleObjects.setReduceRuleObjectsForComponentReduce(Lists.newArrayList());
            return newCheckObjectsAndReduceRuleObjects;
        }

        List<String> allOutcomeAuthDataIds = allOutcomeAuthDatas.stream().map(IObjectData::getId).collect(Collectors.toList());
        List<String> outcomeAuthObjectApiNames = allOutcomeAuthDatas.stream().map(d -> d.get(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, String.class)).collect(Collectors.toList());

        //所有的【解冻授权明细】
        List<IObjectData> unfreezeAuthDetails = unfreezeAuthDetailManager.query(tenantId, allOutcomeAuthDataIds);
        Map<String, String> objectApiNames2MultiLangName = objectApiNames2MultiLangName(tenantId, allOutcomeAuthDatas, unfreezeAuthDetails);

        //组件扣减/直接扣减 (返回所有支出授权的）
        List<CustomerAccountType.ReduceRuleObject> reduceRuleObjectsForComponentReduces = Lists.newArrayList();
        for (String apiName : outcomeAuthObjectApiNames) {
            String objectMultiLangName = objectApiNames2MultiLangName.get(apiName);

            CustomerAccountType.ReduceRuleObject reduceRuleObject = new CustomerAccountType.ReduceRuleObject();
            reduceRuleObject.setApiName(apiName);
            reduceRuleObject.setName(objectMultiLangName);

            reduceRuleObjectsForComponentReduces.add(reduceRuleObject);
        }
        newCheckObjectsAndReduceRuleObjects.setReduceRuleObjectsForComponentReduce(reduceRuleObjectsForComponentReduces);
        newCheckObjectsAndReduceRuleObjects.setReduceRuleObjectsForDirectReduce(reduceRuleObjectsForComponentReduces);

        //校验扣减
        // 开了解冻授权的【支出授权】
        List<IObjectData> openUnfreezeAuthOutcomeAuths = allOutcomeAuthDatas.stream().filter(d -> d.get(FAccountAuthorizationConstants.Field.IsUnfreezeAuth.apiName, Boolean.class, Boolean.FALSE)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(openUnfreezeAuthOutcomeAuths) || CollectionUtils.isEmpty(unfreezeAuthDetails)) {
            newCheckObjectsAndReduceRuleObjects.setCheckObjectsForCheckReduce(Lists.newArrayList());
        } else {
            //【解冻授权明细】分组
            Map<String, List<IObjectData>> accountAuthId2unfreezeAuthDetail = unfreezeAuthDetails.stream().collect(Collectors.groupingBy(d -> d.get(UnfreezeAuthDetailConstants.Field.FAccountAuthorizationId.apiName, String.class)));

            List<CustomerAccountType.CheckObject> checkObjectsForCheckReduce = Lists.newArrayList();
            for (IObjectData outcomeAuth : openUnfreezeAuthOutcomeAuths) {
                String accountAuthId = outcomeAuth.getId();
                if (!accountAuthId2unfreezeAuthDetail.containsKey(accountAuthId)) {
                    continue;
                }
                List<IObjectData> unfreezeAuthDetailList = accountAuthId2unfreezeAuthDetail.get(accountAuthId);
                String authorizedObjectApiName = outcomeAuth.get(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, String.class);
                String objectMultiLangName = objectApiNames2MultiLangName.get(authorizedObjectApiName);
                List<CustomerAccountType.ReduceRuleObject> reduceRuleObjects = getReduceRuleObjects(unfreezeAuthDetailList, objectApiNames2MultiLangName);

                CustomerAccountType.CheckObject checkObject = new CustomerAccountType.CheckObject();
                checkObject.setApiName(authorizedObjectApiName);
                checkObject.setName(objectMultiLangName);
                checkObject.setReduceRuleObjects(reduceRuleObjects);
                checkObjectsForCheckReduce.add(checkObject);
            }
            newCheckObjectsAndReduceRuleObjects.setCheckObjectsForCheckReduce(checkObjectsForCheckReduce);
        }

        return newCheckObjectsAndReduceRuleObjects;
    }

    private List<CustomerAccountType.ReduceRuleObject> getReduceRuleObjects(List<IObjectData> unfreezeAuthDetails, Map<String, String> objectApiNames2MultiLangName) {
        if (CollectionUtils.isEmpty(unfreezeAuthDetails)) {
            return Lists.newArrayList();
        }

        List<CustomerAccountType.ReduceRuleObject> reduceRuleObjects = Lists.newArrayList();
        for (IObjectData unfreezeAuthDetail : unfreezeAuthDetails) {
            String unfreezeObject = unfreezeAuthDetail.get(UnfreezeAuthDetailConstants.Field.UnfreezeObject.apiName, String.class);
            String objectMultiLangName = objectApiNames2MultiLangName.get(unfreezeObject);

            CustomerAccountType.ReduceRuleObject reduceRuleObject = new CustomerAccountType.ReduceRuleObject();
            reduceRuleObject.setApiName(unfreezeObject);
            reduceRuleObject.setName(objectMultiLangName);

            reduceRuleObjects.add(reduceRuleObject);
        }
        return reduceRuleObjects;
    }

    /**
     * 【账户授权】【解冻授权明细】上对象名称多语
     */
    private Map<String, String> objectApiNames2MultiLangName(String tenantId, List<IObjectData> outcomeAuthDatas, List<IObjectData> unfreezeAuthDetails) {
        Set<String> objectApiNames = new HashSet<>();
        if (!CollectionUtils.isEmpty(outcomeAuthDatas)) {
            Set<String> authorizedObjectApiNames = outcomeAuthDatas.stream().map(d -> d.get(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, String.class)).collect(Collectors.toSet());
            objectApiNames.addAll(authorizedObjectApiNames);
        }

        if (!CollectionUtils.isEmpty(unfreezeAuthDetails)) {
            Set<String> unfreezeObjects = unfreezeAuthDetails.stream().map(d -> d.get(UnfreezeAuthDetailConstants.Field.UnfreezeObject.apiName, String.class)).collect(Collectors.toSet());
            objectApiNames.addAll(unfreezeObjects);
        }

        return commonLangManager.getMultiLangName(tenantId, objectApiNames);
    }

    /**
     * 直接扣减、校验扣减 ：过滤掉不在支出授权里面的
     * 组件扣减         ：返回所有支出授权的
     */
    private CustomerAccountType.GetCheckObjectsAndReduceRuleObjects handleWithOutcomeAuth(String tenantId, CustomerAccountType.GetCheckObjectsAndReduceRuleObjects checkObjectsAndReduceRuleObjects, List<String> outcomeAuthObjectApiNames) {
        CustomerAccountType.GetCheckObjectsAndReduceRuleObjects newCheckObjectsAndReduceRuleObjects = new CustomerAccountType.GetCheckObjectsAndReduceRuleObjects();

        if (CollectionUtils.isEmpty(outcomeAuthObjectApiNames)) {
            return newCheckObjectsAndReduceRuleObjects;
        }

        //查对象名称
        Map<String, String> apiName2DisplayNameMap = objectDescribeService.queryDisplayNameByApiNames(tenantId, outcomeAuthObjectApiNames, null);

        //组件扣减(返回所有支出授权的）
        List<CustomerAccountType.ReduceRuleObject> reduceRuleObjectsForComponentReduces = Lists.newArrayList();
        for (String apiName : apiName2DisplayNameMap.keySet()) {
            CustomerAccountType.ReduceRuleObject reduceRuleObject = new CustomerAccountType.ReduceRuleObject();
            reduceRuleObject.setApiName(apiName);
            reduceRuleObject.setName(apiName2DisplayNameMap.get(apiName));
            reduceRuleObjectsForComponentReduces.add(reduceRuleObject);
        }
        newCheckObjectsAndReduceRuleObjects.setReduceRuleObjectsForComponentReduce(reduceRuleObjectsForComponentReduces);

        //直接扣减
        List<CustomerAccountType.ReduceRuleObject> reduceRuleObjectsForDirectReduce = checkObjectsAndReduceRuleObjects.getReduceRuleObjectsForDirectReduce();
        if (!CollectionUtils.isEmpty(reduceRuleObjectsForDirectReduce)) {
            List<CustomerAccountType.ReduceRuleObject> newReduceRuleObjectsForDirectReduce = Lists.newArrayList();

            for (CustomerAccountType.ReduceRuleObject reduceRuleObject : reduceRuleObjectsForDirectReduce) {
                if (outcomeAuthObjectApiNames.contains(reduceRuleObject.getApiName())) {
                    newReduceRuleObjectsForDirectReduce.add(reduceRuleObject);
                }
            }

            newCheckObjectsAndReduceRuleObjects.setReduceRuleObjectsForDirectReduce(newReduceRuleObjectsForDirectReduce);
        }

        //校验扣减
        List<CustomerAccountType.CheckObject> checkObjectsForCheckReduce = checkObjectsAndReduceRuleObjects.getCheckObjectsForCheckReduce();
        if (!CollectionUtils.isEmpty(checkObjectsForCheckReduce)) {
            List<CustomerAccountType.CheckObject> newCheckObjectsForCheckReduce = Lists.newArrayList();

            for (CustomerAccountType.CheckObject checkObject : checkObjectsForCheckReduce) {

                if (!outcomeAuthObjectApiNames.contains(checkObject.getApiName())) {
                    continue;
                }
                if (CollectionUtils.isEmpty(checkObject.getReduceRuleObjects())) {
                    newCheckObjectsForCheckReduce.add(checkObject);
                    continue;
                }

                List<CustomerAccountType.ReduceRuleObject> newReduceRuleObjects = Lists.newArrayList();
                for (CustomerAccountType.ReduceRuleObject reduceRuleObject : checkObject.getReduceRuleObjects()) {
                    if (outcomeAuthObjectApiNames.contains(reduceRuleObject.getApiName())) {
                        newReduceRuleObjects.add(reduceRuleObject);
                    }
                }
                checkObject.setReduceRuleObjects(newReduceRuleObjects);
                // TODO: 2023/7/20 空的要不要放出来 
                newCheckObjectsForCheckReduce.add(checkObject);
            }

            newCheckObjectsAndReduceRuleObjects.setCheckObjectsForCheckReduce(newCheckObjectsForCheckReduce);
        }

        return newCheckObjectsAndReduceRuleObjects;
    }

    /**
     * 对象名称多语
     */
    private void handleLang(String tenantId, CustomerAccountType.GetCheckObjectsAndReduceRuleObjects checkObjectsAndReduceRuleObjects, List<String> outcomeAuthObjectApiNames, Lang lang) {
        if (lang == null || Strings.isNullOrEmpty(lang.getValue())) {
            return;
        }

        //查多语
        Set<String> objectApiNames = new HashSet<>(outcomeAuthObjectApiNames);
        Map<String, String> objectApiNames2MultiLangName = commonLangManager.getMultiLangName(tenantId, objectApiNames);

        //替换多语
        handleLang(checkObjectsAndReduceRuleObjects, objectApiNames2MultiLangName);
    }

    //替换多语
    private void handleLang(CustomerAccountType.GetCheckObjectsAndReduceRuleObjects checkObjectsAndReduceRuleObjects, Map<String, String> objectApiNames2MultiLangName) {
        //组件扣减
        List<CustomerAccountType.ReduceRuleObject> reduceRuleObjectsForComponentReduce = checkObjectsAndReduceRuleObjects.getReduceRuleObjectsForComponentReduce();
        if (!CollectionUtils.isEmpty(reduceRuleObjectsForComponentReduce)) {
            for (CustomerAccountType.ReduceRuleObject reduceRuleObject : reduceRuleObjectsForComponentReduce) {
                String apiName = reduceRuleObject.getApiName();
                String multiLangName = objectApiNames2MultiLangName.get(apiName);
                if (!Strings.isNullOrEmpty(multiLangName)) {
                    reduceRuleObject.setName(multiLangName);
                }
            }
        }

        //直接扣减
        List<CustomerAccountType.ReduceRuleObject> reduceRuleObjectsForDirectReduce = checkObjectsAndReduceRuleObjects.getReduceRuleObjectsForDirectReduce();
        if (!CollectionUtils.isEmpty(reduceRuleObjectsForDirectReduce)) {
            for (CustomerAccountType.ReduceRuleObject reduceRuleObject : reduceRuleObjectsForDirectReduce) {
                String apiName = reduceRuleObject.getApiName();
                String multiLangName = objectApiNames2MultiLangName.get(apiName);
                if (!Strings.isNullOrEmpty(multiLangName)) {
                    reduceRuleObject.setName(multiLangName);
                }
            }
        }

        //校验扣减
        List<CustomerAccountType.CheckObject> checkObjectsForCheckReduce = checkObjectsAndReduceRuleObjects.getCheckObjectsForCheckReduce();
        if (!CollectionUtils.isEmpty(checkObjectsForCheckReduce)) {
            for (CustomerAccountType.CheckObject checkObject : checkObjectsForCheckReduce) {
                String apiName = checkObject.getApiName();
                String multiLangName = objectApiNames2MultiLangName.get(apiName);
                if (!Strings.isNullOrEmpty(multiLangName)) {
                    checkObject.setName(multiLangName);
                }

                for (CustomerAccountType.ReduceRuleObject reduceRuleObject : checkObject.getReduceRuleObjects()) {
                    String reduceRuleObjectApiName = reduceRuleObject.getApiName();
                    String reduceRuleObjectMultiLangName = objectApiNames2MultiLangName.get(reduceRuleObjectApiName);
                    if (!Strings.isNullOrEmpty(reduceRuleObjectMultiLangName)) {
                        reduceRuleObject.setName(reduceRuleObjectMultiLangName);
                    }
                }
            }
        }
    }

    @Override
    public CheckTriggerActionModel.GetAllCheckTriggerActionsResult getAllCheckTriggerActions(ServiceContext serviceContext) {
        return ConfigCenter.getCheckTriggerActions(serviceContext.getTenantId());
    }

    @Override
    public CheckTriggerActionModel.GetCheckTriggerActionsResult getCheckTriggerActions(ServiceContext serviceContext, CheckTriggerActionModel.GetCheckTriggerActionsArg arg) {

        //按钮多语
        Lang lang = serviceContext.getRequestContext().getLang(); //zh-CN
        String tenantId = serviceContext.getTenantId();

        boolean fromConfigCenter = !CaGrayUtil.accountCheckConfigFromObject(tenantId);
        if (fromConfigCenter) {
            return getCheckTriggerActionsFromConfigCenter(tenantId, arg, lang);
        }

        return getCheckTriggerActions(tenantId, arg, lang);
    }

    private CheckTriggerActionModel.GetCheckTriggerActionsResult getCheckTriggerActions(String tenantId, CheckTriggerActionModel.GetCheckTriggerActionsArg arg, Lang lang) {
        String authorizedObjectApiName = arg.getApiName();
        //查【支出授权】
        IObjectData fAccountAuthorizationData = fAccountAuthorizationManager.getFAccountAuthorizationData(tenantId, authorizedObjectApiName, FAccountAuthAuthorizedTypeEnum.Outcome.getValue());
        if (fAccountAuthorizationData == null) {
            throw new ValidateException(I18N.text(CAI18NKey.ACCOUNT_AUTH_NOT_FOUND));
        }

        boolean isUnfreezeAuth = fAccountAuthorizationData.get(FAccountAuthorizationConstants.Field.IsUnfreezeAuth.apiName, Boolean.class, false);
        if (!isUnfreezeAuth) {
            log.info("getCheckTriggerActions isUnfreezeAuth = false tenantId[{}], arg[{}]", tenantId, arg);
            throw new ValidateException(I18N.text(CAI18NKey.ACCOUNT_AUTH_NOT_OPEN_UNFREEZE_AUTH, fAccountAuthorizationData.getName()));
        }

        CheckTriggerActionModel.GetCheckTriggerActionsResult result = new CheckTriggerActionModel.GetCheckTriggerActionsResult();
        List<String> frozenActions = fAccountAuthorizationData.get(FAccountAuthorizationConstants.Field.FrozenActions.apiName, List.class);
        if (CollectionUtils.isEmpty(frozenActions)) {
            log.info("getCheckTriggerActions frozenActions is empty tenantId[{}], arg[{}]", tenantId, arg);
            result.setActions(Lists.newArrayList());
            return result;
        }

        //按钮名称多语（不是字段变更，就是按钮）
        Set<String> buttonApiNames = frozenActions.stream().filter(action -> !Objects.equals("fieldChange", action)).collect(Collectors.toSet());
        Map<String, String> buttonApiNames2MultiLangName = commonLangManager.getButtonMultiLangName(tenantId, buttonApiNames, lang);

        List<CheckTriggerActionModel.Actions> actions = Lists.newArrayList();
        List<CheckTriggerActionModel.Button> buttons = Lists.newArrayList();
        for (String frozenAction : frozenActions) {
            //字段变更
            if (Objects.equals(frozenAction, "fieldChange")) {
                CheckTriggerActionModel.Actions action = new CheckTriggerActionModel.Actions();
                action.setActionType(CheckTriggerActionEnum.FieldChange.getValue());
                actions.add(action);
                continue;
            }

            //按钮
            String buttonName = buttonApiNames2MultiLangName.get(frozenAction);
            CheckTriggerActionModel.Button button = new CheckTriggerActionModel.Button(frozenAction, buttonName);
            buttons.add(button);
        }

        //按钮
        if (CollectionUtils.isNotEmpty(buttons)) {
            CheckTriggerActionModel.Actions action = new CheckTriggerActionModel.Actions();
            action.setActionType(CheckTriggerActionEnum.Button.getValue());
            action.setButtons(buttons);
            actions.add(action);
        }

        result.setActions(actions);
        return result;
    }

    private CheckTriggerActionModel.GetCheckTriggerActionsResult getCheckTriggerActionsFromConfigCenter(String tenantId, CheckTriggerActionModel.GetCheckTriggerActionsArg arg, Lang lang) {
        CheckTriggerActionModel.GetCheckTriggerActionsResult result = new CheckTriggerActionModel.GetCheckTriggerActionsResult();
        CheckTriggerActionModel.GetAllCheckTriggerActionsResult allCheckTriggerActionsResult = ConfigCenter.getCheckTriggerActions(tenantId);
        List<CheckTriggerActionModel.Actions> actions = allCheckTriggerActionsResult.getObjectApiName2Buttons().get(arg.getApiName());

        //按钮多语
        handleButtonLang(tenantId, actions, lang);

        result.setActions(actions);
        return result;
    }

    private void handleButtonLang(String tenantId, List<CheckTriggerActionModel.Actions> actions, Lang lang) {
        if (lang == null || Strings.isNullOrEmpty(lang.getValue())) {
            return;
        }
        if (CollectionUtils.isEmpty(actions)) {
            return;
        }

        //按钮apiName
        Set<String> buttonApiNames = new HashSet<>();
        for (CheckTriggerActionModel.Actions action : actions) {
            List<CheckTriggerActionModel.Button> buttons = action.getButtons();
            if (CollectionUtils.isEmpty(buttons)) {
                continue;
            }
            for (CheckTriggerActionModel.Button button : buttons) {
                buttonApiNames.add(button.getApiName());
            }
        }

        //查多语
        Map<String, String> buttonApiNames2MultiLangName = commonLangManager.getButtonMultiLangName(tenantId, buttonApiNames, lang);

        //替换多语
        for (CheckTriggerActionModel.Actions action : actions) {
            List<CheckTriggerActionModel.Button> buttons = action.getButtons();
            if (CollectionUtils.isEmpty(buttons)) {
                continue;
            }
            for (CheckTriggerActionModel.Button button : buttons) {
                String apiName = button.getApiName();
                String multiLangName = buttonApiNames2MultiLangName.get(apiName);
                if (!Strings.isNullOrEmpty(multiLangName)) {
                    button.setLabel(multiLangName);
                }
            }
        }
    }

    @Override
    public ReduceRuleActionModel.GetAllReduceRuleActionsResult getAllReduceRuleActions(ServiceContext serviceContext) {
        return ConfigCenter.getAllReduceRuleActions(serviceContext.getTenantId());
    }

    @Override
    public ReduceRuleActionModel.GetReduceRuleActionsResult getReduceRuleActions(ServiceContext serviceContext, ReduceRuleActionModel.GetReduceRuleActionsArg arg) {
        //handleLang
        Lang lang = serviceContext.getRequestContext().getLang(); //zh-CN
        String tenantId = serviceContext.getTenantId();

        boolean fromConfigCenter = !CaGrayUtil.accountCheckConfigFromObject(tenantId);
        if (fromConfigCenter) {
            return getReduceRuleActionsFromConfigCenter(tenantId, arg, lang);
        }

        return getReduceRuleActions(tenantId, arg, lang);
    }

    /**
     * 【解冻授权明细】获取
     */
    private ReduceRuleActionModel.GetReduceRuleActionsResult getReduceRuleActions(String tenantId, ReduceRuleActionModel.GetReduceRuleActionsArg arg, Lang lang) {
        ReduceRuleActionModel.GetReduceRuleActionsResult result = new ReduceRuleActionModel.GetReduceRuleActionsResult();

        List<ReduceRuleActionModel.ActionTypeAndButtons> actions = new ArrayList<>();
        if (Objects.equals(arg.getRuleType(), AccountCheckRuleTypeEnum.Component_Reduce.getValue())) {
            actions = getActionsForComponentReduce(tenantId, lang);
        } else if (Objects.equals(arg.getRuleType(), AccountCheckRuleTypeEnum.Direct_Reduce.getValue())) {
            String authorizedObjectApiName = arg.getApiName();
            actions = getActionsForDirectReduce(tenantId, authorizedObjectApiName, lang);
        } else if (Objects.equals(arg.getRuleType(), AccountCheckRuleTypeEnum.Check_Reduce.getValue())) {
            String authorizedObjectApiName = arg.getCheckObjectApiName();
            String unfreezeObject = arg.getApiName();
            actions = getActionsForCheckReduce(tenantId, authorizedObjectApiName, unfreezeObject, lang);
        } else {
            log.warn("getReduceRuleActions ruleType not supported tenantId[{}], arg[{}]", tenantId, arg);
            throw new ValidateException(I18N.text(CAI18NKey.PARAM_ERROR, "ruleType"));
        }

        result.setActions(actions);
        return result;
    }

    /**
     * 组件扣减，就只有'新建'按钮
     */
    private List<ReduceRuleActionModel.ActionTypeAndButtons> getActionsForComponentReduce(String tenantId, Lang lang) {
        List<ReduceRuleActionModel.ActionTypeAndButtons> actions = new ArrayList<>();
        //组件扣减，就只有'新建'按钮
        Set<String> buttonApiNames = Sets.newHashSet("Add_button_default");
        Map<String, String> buttonApiNames2MultiLangName = commonLangManager.getButtonMultiLangName(tenantId, buttonApiNames, lang);
        String buttonMultiLangName = buttonApiNames2MultiLangName.get("Add_button_default");

        ReduceRuleActionModel.ActionTypeAndButtons actionTypeAndButtons = new ReduceRuleActionModel.ActionTypeAndButtons();
        actionTypeAndButtons.setActionType("button");
        actionTypeAndButtons.setButtons(Lists.newArrayList(new ReduceRuleActionModel.Button("Add_button_default", buttonMultiLangName)));
        actions = Lists.newArrayList(actionTypeAndButtons);
        return actions;
    }

    /**
     * 直接扣减 : 查支出授权
     * <p>
     * 类似 CustomerAccountServiceImpl#getCheckTriggerActions
     */
    private List<ReduceRuleActionModel.ActionTypeAndButtons> getActionsForDirectReduce(String tenantId, String authorizedObjectApiName, Lang lang) {
        //支出授权
        IObjectData fAccountAuthorizationData = fAccountAuthorizationManager.getFAccountAuthorizationData(tenantId, authorizedObjectApiName, FAccountAuthAuthorizedTypeEnum.Outcome.getValue());
        if (fAccountAuthorizationData == null) {
            log.warn("getActionsForDirectReduce fAccountAuthorizationData is null tenantId[{}], authorizedObjectApiName[{}]", tenantId, authorizedObjectApiName);
            throw new ValidateException(I18N.text(CAI18NKey.ACCOUNT_AUTH_NOT_FOUND));
        }

        List<String> frozenActions = fAccountAuthorizationData.get(FAccountAuthorizationConstants.Field.ReduceTriggerActions.apiName, List.class);
        return getActions(tenantId, frozenActions, lang);
    }

    /**
     * 校验扣减：查【解冻授权明细】
     */
    private List<ReduceRuleActionModel.ActionTypeAndButtons> getActionsForCheckReduce(String tenantId, String authorizedObjectApiName, String unfreezeObject, Lang lang) {
        //查支出授权
        IObjectData fAccountAuthorizationData = fAccountAuthorizationManager.getFAccountAuthorizationData(tenantId, authorizedObjectApiName, FAccountAuthAuthorizedTypeEnum.Outcome.getValue());
        if (fAccountAuthorizationData == null) {
            throw new ValidateException(I18N.text(CAI18NKey.ACCOUNT_AUTH_NOT_FOUND));
        }
        String accountAuthId = fAccountAuthorizationData.getId();

        //查【解冻授权明细】
        IObjectData unfreezeAuthDetail = unfreezeAuthDetailManager.query(tenantId, accountAuthId, unfreezeObject);
        if (unfreezeAuthDetail == null) {
            log.warn("getActionsForCheckReduce unfreezeAuthDetail is null tenantId[{}], accountAuthId[{}], unfreezeObject[{}]", tenantId, accountAuthId, unfreezeObject);
            String multiLangName = commonLangManager.getMultiLangName(tenantId, unfreezeObject);
            throw new ValidateException(I18N.text(CAI18NKey.UNFREEZE_AUTH_DETAIL_NOT_FOUND, fAccountAuthorizationData.getName(), multiLangName));
        }

        List<String> frozenActions = unfreezeAuthDetail.get(UnfreezeAuthDetailConstants.Field.UnfreezeActions.apiName, List.class);

        return getActions(tenantId, frozenActions, lang);
    }

    private List<ReduceRuleActionModel.ActionTypeAndButtons> getActions(String tenantId, List<String> frozenActions, Lang lang) {
        List<ReduceRuleActionModel.ActionTypeAndButtons> actions = new ArrayList<>();

        if (CollectionUtils.isEmpty(frozenActions)) {
            log.info("getActions frozenActions is empty tenantId[{}], frozenActions[{}]", tenantId, frozenActions);
            return actions;
        }

        //按钮名称多语（不是字段变更，就是按钮）
        Set<String> buttonApiNames = frozenActions.stream().filter(action -> !Objects.equals("fieldChange", action)).collect(Collectors.toSet());
        Map<String, String> buttonApiNames2MultiLangName = commonLangManager.getButtonMultiLangName(tenantId, buttonApiNames, lang);

        List<ReduceRuleActionModel.Button> buttons = Lists.newArrayList();
        for (String frozenAction : frozenActions) {
            //字段变更
            if (Objects.equals(frozenAction, "fieldChange")) {
                ReduceRuleActionModel.ActionTypeAndButtons action = new ReduceRuleActionModel.ActionTypeAndButtons();
                action.setActionType(ReduceTriggerActionEnum.FieldChange.getValue());
                actions.add(action);
                continue;
            }

            //按钮
            String buttonName = buttonApiNames2MultiLangName.get(frozenAction);
            ReduceRuleActionModel.Button button = new ReduceRuleActionModel.Button(frozenAction, buttonName);
            buttons.add(button);
        }

        //按钮
        if (CollectionUtils.isNotEmpty(buttons)) {
            ReduceRuleActionModel.ActionTypeAndButtons action = new ReduceRuleActionModel.ActionTypeAndButtons();
            action.setActionType(ReduceTriggerActionEnum.Button.getValue());
            action.setButtons(buttons);
            actions.add(action);
        }

        return actions;
    }

    /**
     * 从配置中心获取
     */
    private ReduceRuleActionModel.GetReduceRuleActionsResult getReduceRuleActionsFromConfigCenter(String tenantId, ReduceRuleActionModel.GetReduceRuleActionsArg arg, Lang lang) {
        ReduceRuleActionModel.GetAllReduceRuleActionsResult all = ConfigCenter.getAllReduceRuleActions(tenantId);
        ReduceRuleActionModel.GetReduceRuleActionsResult result = new ReduceRuleActionModel.GetReduceRuleActionsResult();

        List<ReduceRuleActionModel.ActionTypeAndButtons> actions = new ArrayList<>();
        if (Objects.equals(arg.getRuleType(), AccountCheckRuleTypeEnum.Component_Reduce.getValue())) {
            //组件扣减，就只有'新建'按钮
            ReduceRuleActionModel.ActionTypeAndButtons actionTypeAndButtons = new ReduceRuleActionModel.ActionTypeAndButtons();
            actionTypeAndButtons.setActionType("button");
            actionTypeAndButtons.setButtons(Lists.newArrayList(new ReduceRuleActionModel.Button("Add_button_default", "新建"))); //ignoreI18n
            actions = Lists.newArrayList(actionTypeAndButtons);

            // JSONObject jsonObject = JSON.parseObject("{\"actionType\":\"button\",\"buttons\":[{\"apiName\":\"Add_button_default\",\"label\":\"新建\"}]}");
        } else {
            actions = all.getObjectApiName2ActionAndButtons().get(arg.getApiName());
        }

        handleLang(tenantId, actions, lang);

        result.setActions(actions);
        return result;
    }

    private void handleLang(String tenantId, List<ReduceRuleActionModel.ActionTypeAndButtons> actions, Lang lang) {
        if (lang == null || Strings.isNullOrEmpty(lang.getValue())) {
            return;
        }

        if (CollectionUtils.isEmpty(actions)) {
            return;
        }

        //按钮apiName
        Set<String> buttonApiNames = new HashSet<>();
        for (ReduceRuleActionModel.ActionTypeAndButtons action : actions) {
            List<ReduceRuleActionModel.Button> buttons = action.getButtons();
            if (CollectionUtils.isEmpty(buttons)) {
                continue;
            }
            for (ReduceRuleActionModel.Button button : buttons) {
                buttonApiNames.add(button.getApiName());
            }
        }

        //查多语
        Map<String, String> buttonApiNames2MultiLangName = commonLangManager.getButtonMultiLangName(tenantId, buttonApiNames, lang);

        //替换多语
        for (ReduceRuleActionModel.ActionTypeAndButtons action : actions) {
            List<ReduceRuleActionModel.Button> buttons = action.getButtons();
            if (CollectionUtils.isEmpty(buttons)) {
                continue;
            }
            for (ReduceRuleActionModel.Button button : buttons) {
                String apiName = button.getApiName();
                String multiLangName = buttonApiNames2MultiLangName.get(apiName);
                if (!Strings.isNullOrEmpty(multiLangName)) {
                    button.setLabel(multiLangName);
                }
            }
        }
    }

    @Override
    public CustomerAccountType.GetOccupiedMappingRequiredFieldsResult getOccupiedMappingRequiredFields(ServiceContext serviceContext) {
        CustomerAccountType.GetOccupiedMappingRequiredFieldsResult result = new CustomerAccountType.GetOccupiedMappingRequiredFieldsResult();

        Map<String, List<String>> occupiedMappingRequiredFields = accountCheckRuleManager.getOccupiedMappingRequiredFields();

        result.setObjectApiName2fieldApiNames(occupiedMappingRequiredFields);

        return result;
    }

    @Override
    public CustomerAccountType.GetReduceMappingRequiredFieldsResult getReduceMappingRequiredFields(ServiceContext serviceContext) {
        CustomerAccountType.GetReduceMappingRequiredFieldsResult result = new CustomerAccountType.GetReduceMappingRequiredFieldsResult();

        Map<String, List<String>> reduceMappingRequiredFields = accountCheckRuleManager.getReduceMappingRequiredFields();

        result.setObjectApiName2fieldApiNames(reduceMappingRequiredFields);

        return result;
    }

    @Override
    public CustomerAccountType.CheckRulePriorityResult checkRulePriority(ServiceContext serviceContext, CustomerAccountType.CheckRulePriorityArg arg) {
        return accountCheckRuleManager.checkRulePriority(serviceContext, arg);
    }

    @Override
    public ConfigModel.GetResult queryConfig(ServiceContext serviceContext, ConfigModel.GetArg arg) {
        Set<String> keys = BooleanUtils.isTrue(arg.getIsAllConfig()) ? Arrays.stream(ConfigKeyEnum.values()).map(x -> x.key).collect(Collectors.toSet()) : arg.getKeys();
        ConfigModel.GetResult result = new ConfigModel.GetResult();
        if (CollectionUtils.isNotEmpty(keys)) {
            Map<String, String> configMap = configService.queryTenantConfigs(serviceContext.getUser(), Lists.newArrayList(keys));
            result.setValues(configMap.entrySet().stream().map(x -> new ConfigModel.ConfigData(x.getKey(), x.getValue())).collect(Collectors.toList()));

            setDirectReduceForceCheckAmountDefaultValue(keys, result);
        }
        return result;
    }

    /**
     * direct_reduce_force_check_amount : 没值的话，默认是 1
     */
    private void setDirectReduceForceCheckAmountDefaultValue(Set<String> keys, ConfigModel.GetResult result) {
        String key = ConfigKeyEnum.DIRECT_REDUCE_FORCE_CHECK_AMOUNT.key;
        if (CollectionUtils.isEmpty(keys) || !keys.contains(key)) {
            return;
        }

        List<String> resultExistKey = result.getValues().stream().map(ConfigModel.ConfigData::getKey).collect(Collectors.toList());
        if (resultExistKey.contains(key)) {
            return;
        }

        result.getValues().add(new ConfigModel.ConfigData(key, "1"));
    }

    private void deleteCustomerAccounts(ServiceContext serviceContext, List<IObjectData> customerAccountObjectDatas, String mainCustomerId) {
        customerAccountObjectDatas.removeIf(o -> o.get(CustomerAccountConstants.Field.Customer.apiName, String.class).equals(mainCustomerId));
        /*客户在未生效，锁定，审批中都不可合并，只有正常状态和作废状态合并*/
        serviceFacade.bulkInvalid(customerAccountObjectDatas, serviceContext.getUser());
        serviceFacade.bulkDelete(customerAccountObjectDatas, serviceContext.getUser());
    }

    private IObjectData mergeCustomerAccountMoney(ServiceContext serviceContext, List<IObjectData> customerAccountObjectDatas, IObjectData mainObjectData, String maxCustomerId) {
        BigDecimal credit = BigDecimal.ZERO;
        BigDecimal creditTemporary = BigDecimal.ZERO;
        String creditPeriod = null;

        for (IObjectData objectData : customerAccountObjectDatas) {
            String customerId = objectData.get(CustomerAccountConstants.Field.Customer.apiName, String.class);

            if (customerId.equals(maxCustomerId)) {
                credit = objectData.get(CustomerAccountConstants.Field.CreditQuota.apiName, BigDecimal.class);
                creditPeriod = objectData.get(CustomerAccountConstants.Field.CreditPeriod.apiName, String.class);
            }
            creditTemporary = creditTemporary.add(ObjectDataUtil.getBigDecimal(objectData, CustomerAccountConstants.Field.CreditTemporaryQuota.apiName));
        }
        mainObjectData.set(CustomerAccountConstants.Field.CreditQuota.apiName, credit);
        mainObjectData.set(CustomerAccountConstants.Field.CreditPeriod.apiName, creditPeriod);
        mainObjectData.set(CustomerAccountConstants.Field.CreditTemporaryQuota.apiName, creditTemporary);
        log.info("merge mainObjectData:{}", mainObjectData);
        IObjectData mainData = serviceFacade.updateObjectData(serviceContext.getUser(), mainObjectData);
        return mainData;
    }

    private List<IObjectData> listByCustomerId(String tenantId,
                                               String objectApiName,
                                               String fieldApiName,
                                               List<String> customerIds,
                                               List<String> orderByFieldNames,
                                               int offset,
                                               int limit) {
        List<IFilter> filters = Lists.newArrayList();
        List<OrderBy> orders = Lists.newArrayList();

        SearchUtil.fillFilterIn(filters, fieldApiName, customerIds);
        SearchUtil.fillFilterNotEq(filters, IObjectData.IS_DELETED, DELETE_STATUS.DELETE.getValue());
        orderByFieldNames.forEach(orderByFieldName -> SearchUtil.fillOrderBy(orders, orderByFieldName, true));

        User user = RequestUtil.getSystemUser(tenantId);
        return customerAccountManager.searchQuery(user, objectApiName, filters, orders, offset, limit).getData();
    }

    private String deleteOfficialCredit(User user, List<String> customerIds, Map<String, IObjectData> customerIdMap) {
        String maxCustomerId = null;
        List<IFilter> filters = Lists.newArrayList();
        List<OrderBy> orders = Lists.newArrayList();

        SearchUtil.fillFilterIn(filters, CreditFileConstants.Field.Customer.apiName, customerIds);
        SearchUtil.fillFilterEq(filters, CreditFileConstants.Field.CreditType.apiName, CreditTypeEnum.OfficialCredit.getValue());
        SearchUtil.fillFilterEq(filters, IObjectData.IS_DELETED, DELETE_STATUS.NORMAL.getValue());
        SearchUtil.fillOrderBy(orders, CreditFileConstants.Field.EndTime.apiName, false);
        SearchUtil.fillOrderBy(orders, CreditFileConstants.Field.CreditQuota.apiName, false);

        List<IObjectData> officialCreditObjectDatas = customerAccountManager.searchQuery(user, CreditFileConstants.API_NAME, filters, orders, 0, 500).getData();
        if (CollectionUtils.isEmpty(officialCreditObjectDatas)) {
            return maxCustomerId;
        }

        maxCustomerId = officialCreditObjectDatas.get(0).get(CreditFileConstants.Field.Customer.apiName, String.class);
        String creditFileId = officialCreditObjectDatas.get(0).getId();
        officialCreditObjectDatas.removeIf(o -> o.getId().equals(creditFileId));
        if (CollectionUtils.isEmpty(officialCreditObjectDatas)) {
            return maxCustomerId;
        }
        serviceFacade.bulkInvalid(officialCreditObjectDatas, user);
        serviceFacade.bulkDelete(officialCreditObjectDatas, user);

        // 合并时正式信用被作废，写入相应流水
        try {
            List<CustomerAccountBill> customerAccountBills = Lists.newArrayList();
            officialCreditObjectDatas.forEach(credit -> {
                String tenantId = credit.getTenantId();
                String officialCreditId = credit.getId();
                BigDecimal creditQuota = ObjectDataUtil.getBigDecimal(credit, CreditFileConstants.Field.CreditQuota.apiName);
                String customerId = credit.get(CreditFileConstants.Field.Customer.apiName, String.class);
                String customerAccountId = customerIdMap.getOrDefault(customerId, new ObjectData()).getId();
                String lifeStatus = credit.get(SystemConstants.Field.LifeStatusBeforeInvalid.apiName, String.class);
                String info = CustomerAccountRecordLogger.generateCreditInfo(officialCreditId, lifeStatus, SystemConstants.LifeStatus.Invalid.value);

                CustomerAccountBill bill = new CustomerAccountBill();
                bill.setTenantId(tenantId);
                bill.setCustomerAccountId(customerAccountId);
                bill.setRelateType(BillTypeEnum.Credit.getType());
                bill.setRelateId(officialCreditId);
                bill.setCreditAvailableQuotaChange(creditQuota.negate().doubleValue());
                bill.setRemark(info);
                bill.setBillDate(new Date());
                bill.setCreateTime(new Date());
                customerAccountBills.add(bill);
            });

            customerAccountBillManager.batchAddCustomerAccountBillAccordCredit(customerAccountBills);
        } catch (Exception e) {
            log.error("deleteOfficialCredit batch add credit bill failed, customerIds={}", customerIds, e);
        }
        return maxCustomerId;
    }

    @Override
    public CreateNewCustomerAccountModel.Result createNewCustomerAccount(ServiceContext serviceContext, CreateNewCustomerAccountModel.Arg arg) {
        CreateNewCustomerAccountModel.Result result = new CreateNewCustomerAccountModel.Result();
        if (StringUtils.isEmpty(arg.getFundAccountId()) || StringUtils.isEmpty(arg.getCustomerId())) {
            throw new ValidateException("fundAccountId or customerId is empty");
        }
        IObjectData newCustomerAccount = newCustomerAccountManager.getOrCreateNewCustomerAccount(serviceContext.getRequestContext(), arg.getFundAccountId(), arg.getCustomerId(), null);
        result.setObjectData(ObjectDataDocument.of(newCustomerAccount));
        return result;
    }
}
