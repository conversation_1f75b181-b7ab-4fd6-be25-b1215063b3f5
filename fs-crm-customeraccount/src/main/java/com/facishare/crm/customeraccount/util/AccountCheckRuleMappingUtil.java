package com.facishare.crm.customeraccount.util;

import com.facishare.crm.customeraccount.constants.AccountCheckRuleConstants;
import com.facishare.crm.customeraccount.constants.AccountTransactionFlowConst;
import com.facishare.crm.customeraccount.constants.AuthorizationDetailConstant;
import com.facishare.crm.customeraccount.constants.SystemConstants;
import com.facishare.crm.customeraccount.predefine.handler.RuleHandlerUtil;
import com.facishare.crm.customeraccount.predefine.service.dto.AccessOutcomeAuthModel;
import com.facishare.crm.customeraccount.predefine.service.dto.FieldMappingModel;
import com.facishare.crm.customeraccount.predefine.service.dto.ObjectMappingModel;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 校验规则字段映射
 */
@Slf4j
public class AccountCheckRuleMappingUtil {

    /**
     * 获取'组件扣减'的字段映射
     */
    public static List<ObjectMappingModel> getObjectMappingModelsForComponentReduce(String sourceObjectApiName, String customerMappingField, List<IObjectData> authDetails, boolean needConsiderTradeAmountFieldApiName) {
        List<ObjectMappingModel> objectMappingModels = new ArrayList<>();

        if (CollectionUtils.empty(authDetails)) {
            return objectMappingModels;
        }

        for (IObjectData detail : authDetails) {
            String authorizeAccountId = detail.get(AuthorizationDetailConstant.Field.AuthorizeAccountId.apiName, String.class);
            String tradeAmountFieldApiName = null;
            if (needConsiderTradeAmountFieldApiName) {
                if (detail.containsField(AuthorizationDetailConstant.Field.TradeAmountFieldapiname.apiName)) {
                    tradeAmountFieldApiName = detail.get(AuthorizationDetailConstant.Field.TradeAmountFieldapiname.apiName, String.class);//没有，得到是null
                    if (Strings.isNullOrEmpty(tradeAmountFieldApiName)) {
                        log.info("getObjectMappingModelsForComponentReduce tradeAmountFieldApiName = null sourceObjectApiName[{}], customerMappingField[{}], needConsiderTradeAmountFieldApiName[{}], detail[{}]",
                                sourceObjectApiName, customerMappingField, needConsiderTradeAmountFieldApiName, detail);
                    }
                }
            }

            ObjectMappingModel objectMappingModel = new ObjectMappingModel();
            objectMappingModel.setFundAccountId(authorizeAccountId);
            objectMappingModel.setSourceObjectApiName(sourceObjectApiName);
            objectMappingModel.setTargetObjectApiName(AccountTransactionFlowConst.API_NAME);
            objectMappingModel.setFieldMappingList(getFieldMappingModelsForComponentReduce(customerMappingField, tradeAmountFieldApiName));

            objectMappingModels.add(objectMappingModel);
        }

        return objectMappingModels;
    }

    /**
     * 获取'组件扣减'的字段映射
     */
    public static List<ObjectMappingModel> getObjectMappingModelsForComponentReduce(List<AccessOutcomeAuthModel.DetailFields> detailFields, String sourceObjectApiName, String customerMappingField) {
        List<ObjectMappingModel> objectMappingModels = new ArrayList<>();

        if (CollectionUtils.empty(detailFields)) {
            return objectMappingModels;
        }

        for (AccessOutcomeAuthModel.DetailFields detailField : detailFields) {
            ObjectMappingModel objectMappingModel = new ObjectMappingModel();
            objectMappingModel.setFundAccountId(detailField.getAuthorizeAccountId());
            objectMappingModel.setSourceObjectApiName(sourceObjectApiName);
            objectMappingModel.setTargetObjectApiName(AccountTransactionFlowConst.API_NAME);
            objectMappingModel.setFieldMappingList(getFieldMappingModelsForComponentReduce(customerMappingField, detailField.getTradeAmountFieldApiName()));

            objectMappingModels.add(objectMappingModel);
        }

        return objectMappingModels;
    }

    /**
     * 组件扣减的字段映射
     */
    public static List<ObjectMappingModel> getObjectMappingModelsForComponentReduce(String sourceObjectApiName, List<String> authorizeAccountIds, String customerMappingField) {
        List<ObjectMappingModel> objectMappingModels = new ArrayList<>();

        if (CollectionUtils.empty(authorizeAccountIds)) {
            return objectMappingModels;
        }

        for (String authorizeAccountId : authorizeAccountIds) {
            ObjectMappingModel objectMappingModel = new ObjectMappingModel();
            objectMappingModel.setFundAccountId(authorizeAccountId);
            objectMappingModel.setSourceObjectApiName(sourceObjectApiName);
            objectMappingModel.setTargetObjectApiName(AccountTransactionFlowConst.API_NAME);
            objectMappingModel.setFieldMappingList(getFieldMappingModelsForComponentReduce(customerMappingField, null));

            objectMappingModels.add(objectMappingModel);
        }

        return objectMappingModels;
    }

    /**
     * 获取'组件扣减'的字段映射
     */
    public static ObjectMappingModel getObjectMappingModelForComponentReduce(String fundAccountId, String sourceObjectApiName, String customerMappingField, String expenseAmountMappingField) {
        ObjectMappingModel objectMappingModel = new ObjectMappingModel();
        objectMappingModel.setFundAccountId(fundAccountId);
        objectMappingModel.setSourceObjectApiName(sourceObjectApiName);
        objectMappingModel.setTargetObjectApiName(AccountTransactionFlowConst.API_NAME);
        objectMappingModel.setFieldMappingList(getFieldMappingModelsForComponentReduce(customerMappingField, expenseAmountMappingField));
        return objectMappingModel;
    }

    /**
     * 获取'组件扣减'的字段映射
     */
    public static List<FieldMappingModel> getFieldMappingModelsForComponentReduce(String customerMappingField, String expenseAmountMappingField) {
        List<FieldMappingModel> fieldMappingModels = new ArrayList<>();

        FieldMappingModel customerFieldMappingModel = new FieldMappingModel();
        customerFieldMappingModel.setSourceFieldApiName(customerMappingField);
        customerFieldMappingModel.setTargetFieldApiName(AccountTransactionFlowConst.Field.Customer.apiName);
        customerFieldMappingModel.setOptionMappingList(Lists.newArrayList());
        fieldMappingModels.add(customerFieldMappingModel);

        FieldMappingModel expenseAmountFieldMappingModel = new FieldMappingModel();
        expenseAmountFieldMappingModel.setSourceFieldApiName(expenseAmountMappingField);
        expenseAmountFieldMappingModel.setTargetFieldApiName(AccountTransactionFlowConst.Field.ExpenseAmount.apiName);
        expenseAmountFieldMappingModel.setOptionMappingList(Lists.newArrayList());
        fieldMappingModels.add(expenseAmountFieldMappingModel);

        FieldMappingModel transactionDateFieldMappingModel = new FieldMappingModel();
        transactionDateFieldMappingModel.setSourceFieldApiName(SystemConstants.Field.CreateTime.apiName);
        transactionDateFieldMappingModel.setTargetFieldApiName(AccountTransactionFlowConst.Field.TransactionDate.apiName);
        transactionDateFieldMappingModel.setOptionMappingList(Lists.newArrayList());
        fieldMappingModels.add(transactionDateFieldMappingModel);

        return fieldMappingModels;
    }

    public static String getCustomerIdForComponentReduce(IObjectData componentReduceAccountCheckRuleData, ObjectDataDocument masterData) {
        List<ObjectMappingModel> objectMappingModels = RuleHandlerUtil.getObjectMapping(componentReduceAccountCheckRuleData, AccountCheckRuleConstants.Field.ReduceMapping.apiName);
        String entryCustomerFieldApiName = AccountCheckRuleMappingUtil.getMappingFieldApiName(objectMappingModels, AccountTransactionFlowConst.Field.Customer.apiName);
        if (Strings.isNullOrEmpty(entryCustomerFieldApiName)) {
            return null;
        }

        return String.valueOf(masterData.get(entryCustomerFieldApiName));
    }


    /**
     * 获取 targetFieldApiName 对应的映射字段apiName
     */
    public static String getMappingFieldApiNameForComponentReduce(IObjectData componentReduceAccountCheckRuleData, String targetFieldApiName) {
        List<ObjectMappingModel> objectMappingModels = RuleHandlerUtil.getObjectMapping(componentReduceAccountCheckRuleData, AccountCheckRuleConstants.Field.ReduceMapping.apiName);
        return AccountCheckRuleMappingUtil.getMappingFieldApiName(objectMappingModels, targetFieldApiName);
    }

    /**
     * 获取 targetFieldApiName 对应的映射字段apiName
     */
    public static String getMappingFieldApiName(List<ObjectMappingModel> objectMappingModels, String targetFieldApiName) {
        if (CollectionUtils.empty(objectMappingModels)) {
            return null;
        }

        for (ObjectMappingModel objectMappingModel : objectMappingModels) {
            String mappingFieldApiName = getMappingFieldApiName(objectMappingModel, targetFieldApiName);
            if (!Strings.isNullOrEmpty(mappingFieldApiName)) {
                return mappingFieldApiName;
            }
        }

        return null;
    }

    /**
     * 获取 targetFieldApiName 对应的映射字段apiName
     */
    public static String getMappingFieldApiName(ObjectMappingModel objectMappingModel, String targetFieldApiName) {
        if (objectMappingModel == null) {
            return null;
        }

        if (CollectionUtils.empty(objectMappingModel.getFieldMappingList())) {
            return null;
        }

        for (FieldMappingModel fieldMappingModel : objectMappingModel.getFieldMappingList()) {
            if (Objects.equals(fieldMappingModel.getTargetFieldApiName(), targetFieldApiName)) {
                return fieldMappingModel.getSourceFieldApiName();
            }
        }

        return null;
    }

    public static List<String> getSourceMappingFields(List<ObjectMappingModel> objectMappings, String targetFieldApiName) {
        List<String> fieldApiNames = Lists.newArrayList();
        if (CollectionUtils.empty(objectMappings)) {
            return fieldApiNames;
        }

        for (ObjectMappingModel objectMapping : objectMappings) {
            if (CollectionUtils.empty(objectMapping.getFieldMappingList())) {
                continue;
            }

            String fieldApiName = getMappingFieldApiNameFromFieldMappings(objectMapping.getFieldMappingList(), targetFieldApiName);
            if (!Strings.isNullOrEmpty(fieldApiName)) {
                fieldApiNames.add(fieldApiName);
            }
        }

        return fieldApiNames;
    }

    /**
     * 获取 targetFieldApiName 对应的映射字段apiName
     */
    public static String getMappingFieldApiNameFromFieldMappings(List<FieldMappingModel> fieldMappingList, String targetFieldApiName) {
        if (CollectionUtils.empty(fieldMappingList)) {
            return null;
        }

        for (FieldMappingModel fieldMappingModel : fieldMappingList) {
            if (Objects.equals(fieldMappingModel.getTargetFieldApiName(), targetFieldApiName)) {
                return fieldMappingModel.getSourceFieldApiName();
            }
        }

        return null;
    }

    /**
     * 设置 expense_amount 对应的映射字段apiName
     */
    public static void setExpenseAmountMappingFieldApiName(ObjectMappingModel objectMappingModel, String expenseAmountSourceFieldApiName) {
        if (objectMappingModel == null) {
            return;
        }

        if (CollectionUtils.empty(objectMappingModel.getFieldMappingList())) {
            return;
        }

        for (FieldMappingModel fieldMappingModel : objectMappingModel.getFieldMappingList()) {
            if (Objects.equals(fieldMappingModel.getTargetFieldApiName(), "expense_amount")) {
                fieldMappingModel.setSourceFieldApiName(expenseAmountSourceFieldApiName);
                return;
            }
        }
    }

    /**
     * 包含的账户
     */
    public static List<String> hasFundAccountIds(List<ObjectMappingModel> objectMappings, List<String> fundAccountIds) {
        if (CollectionUtils.empty(objectMappings) || CollectionUtils.empty(fundAccountIds)) {
            return Lists.newArrayList();
        }

        List<String> hasFundAccountIds = Lists.newArrayList();
        for (ObjectMappingModel objectMapping : objectMappings) {
            if (fundAccountIds.contains(objectMapping.getFundAccountId())) {
                hasFundAccountIds.add(objectMapping.getFundAccountId());
            }
        }

        return hasFundAccountIds;
    }

    /**
     * 是否用到了账户
     */
    public static boolean hasFundAccountId(List<ObjectMappingModel> objectMappingModels, String fundAccountId) {
        if (CollectionUtils.empty(objectMappingModels) || Strings.isNullOrEmpty(fundAccountId)) {
            return false;
        }

        for (ObjectMappingModel objectMappingModel : objectMappingModels) {
            if (Objects.equals(objectMappingModel.getFundAccountId(), fundAccountId)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 是否用到了客户id
     */
    public static boolean hasCustomerId(List<ObjectMappingModel> objectMappings, String sourceCustomerFieldApiName, String targetCustomerFieldApiName) {
        if (CollectionUtils.empty(objectMappings) || Strings.isNullOrEmpty(sourceCustomerFieldApiName)) {
            return false;
        }

        for (ObjectMappingModel objectMapping : objectMappings) {
            List<FieldMappingModel> fieldMappings = objectMapping.getFieldMappingList();
            if (CollectionUtils.empty(fieldMappings)) {
                continue;
            }

            for (FieldMappingModel fieldMapping : fieldMappings) {
                if (Objects.equals(fieldMapping.getTargetFieldApiName(), targetCustomerFieldApiName) && Objects.equals(fieldMapping.getSourceFieldApiName(), sourceCustomerFieldApiName)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 获取账户ID列表
     */
    public static List<String> getFundAccountIds(List<ObjectMappingModel> objectMappings) {
        if (CollectionUtils.empty(objectMappings)) {
            return Lists.newArrayList();
        }

        Set<String> fundAccountIds = new HashSet<>();

        for (ObjectMappingModel objectMapping : objectMappings) {
            if (!Strings.isNullOrEmpty(objectMapping.getFundAccountId())) {
                fundAccountIds.add(objectMapping.getFundAccountId());
            }
        }

        return Lists.newArrayList(fundAccountIds);
    }

    public static String getExpenseAmountSourceFieldApiName(List<FieldMappingModel> fieldMappings) {
        if (CollectionUtils.empty(fieldMappings)) {
            return null;
        }

        for (FieldMappingModel fieldMapping : fieldMappings) {
            if (Objects.equals(fieldMapping.getTargetFieldApiName(), AccountTransactionFlowConst.Field.ExpenseAmount.apiName)) {
                return fieldMapping.getSourceFieldApiName();
            }
        }

        return null;
    }

    /**
     * 获取第1个≠null的客户字段
     * @param objectMappings
     * @return
     */
    public static String getCustomerId(List<ObjectMappingModel> objectMappings, String targetCustomerFieldApiName) {
        if (CollectionUtils.empty(objectMappings)) {
            return null;
        }

        for (ObjectMappingModel objectMapping : objectMappings) {
            List<FieldMappingModel> fieldMappings = objectMapping.getFieldMappingList();
            if (CollectionUtils.empty(fieldMappings)) {
                continue;
            }

            for (FieldMappingModel fieldMapping : fieldMappings) {
                if (Objects.equals(fieldMapping.getTargetFieldApiName(), targetCustomerFieldApiName)) {
                    return fieldMapping.getSourceFieldApiName();
                }
            }
        }

        return null;
    }

    /**
     * 是否有必填字段未填写
     */
    public static boolean hasRequiredFieldEmpty(List<ObjectMappingModel> objectMappings, Map<String, List<String>> targetObjectApiName2RequiredFields) {
        if (CollectionUtils.empty(objectMappings)) {
            return false;
        }

        for (ObjectMappingModel objectMapping : objectMappings) {
            String targetObjectApiName = objectMapping.getTargetObjectApiName();
            if (!targetObjectApiName2RequiredFields.keySet().contains(targetObjectApiName)) {
                continue;
            }

            List<String> requiredFields = targetObjectApiName2RequiredFields.get(targetObjectApiName);
            if(CollectionUtils.empty(requiredFields)) {
                continue;
            }

            for (FieldMappingModel fieldMapping : objectMapping.getFieldMappingList()) {
                if (requiredFields.contains(fieldMapping.getTargetFieldApiName())) {
                    if (Strings.isNullOrEmpty(fieldMapping.getSourceFieldApiName())) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    public static Map<String, String> getFundAccountId2SourceFieldApiName(List<ObjectMappingModel> objectMappings, String targetFieldApiName) {
        Map<String, String> fundAccountId2TransactionDateField = new HashMap<>();
        if (CollectionUtils.empty(objectMappings)) {
            return fundAccountId2TransactionDateField;
        }

        for (ObjectMappingModel objectMapping : objectMappings) {
            String sourceFieldApiName = getSourceFieldApiName(objectMapping.getFieldMappingList(), targetFieldApiName);
            fundAccountId2TransactionDateField.put(objectMapping.getFundAccountId(), sourceFieldApiName);
        }

        return fundAccountId2TransactionDateField;
    }

    private static String getSourceFieldApiName(List<FieldMappingModel> fieldMappings, String targetFieldApiName) {
        if (CollectionUtils.empty(fieldMappings)) {
            return null;
        }

        for (FieldMappingModel fieldMapping : fieldMappings) {
            if (Objects.equals(fieldMapping.getTargetFieldApiName(), targetFieldApiName)) {
                return fieldMapping.getSourceFieldApiName();
            }
        }

        return null;
    }

    /**
     * 获取流水 TransactionDate("transaction_date", "交易日期"),
     * 非必填，没值就不填
     */
    public static Map<String, Long> getFundAccountId2transactionDate(IObjectData componentReduceAccountCheckRuleData, IObjectData sourceObjectData) {
        List<ObjectMappingModel> objectMappings = RuleHandlerUtil.getObjectMapping(componentReduceAccountCheckRuleData, AccountCheckRuleConstants.Field.ReduceMapping.apiName);
        Map<String, String> fundAccountId2SourceFieldApiName = AccountCheckRuleMappingUtil.getFundAccountId2SourceFieldApiName(objectMappings, AccountTransactionFlowConst.Field.TransactionDate.apiName);
        Map<String, Long> fundAccountId2transactionDate = new HashMap<>();

        for (String fundAccountId : fundAccountId2SourceFieldApiName.keySet()) {
            if (sourceObjectData == null) {
                log.info("getFundAccountId2transactionDate sourceObjectData = null");
                continue;
            } else {
                String sourceFieldApiName = fundAccountId2SourceFieldApiName.get(fundAccountId);
                if (Strings.isNullOrEmpty(sourceFieldApiName) || !sourceObjectData.containsField(sourceFieldApiName)) {
                  // fundAccountId2transactionDate.put(fundAccountId, System.currentTimeMillis());
                    continue;
                }

                String transactionDateStr = sourceObjectData.get(sourceFieldApiName, String.class);
                if (Strings.isNullOrEmpty(transactionDateStr) || "null".equalsIgnoreCase(transactionDateStr)) {
                    continue;
                }

                Long transactionDate = Long.valueOf(transactionDateStr);
                fundAccountId2transactionDate.put(fundAccountId, transactionDate);
            }
        }

        return fundAccountId2transactionDate;
    }
}
