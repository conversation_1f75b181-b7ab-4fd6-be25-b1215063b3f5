package com.facishare.crm.customeraccount.predefine.domainplugin.processor.credit;

import com.facishare.crm.customeraccount.predefine.domainplugin.model.CreditRuleInvalidContextModel;
import com.facishare.crm.customeraccount.predefine.manager.CreditManager;
import com.facishare.crm.customeraccount.predefine.manager.NewCustomerAccountManager;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.predef.domain.InvalidActionDomainPlugin;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class InvalidCreditRuleProcessor extends CreditRuleByDetailProcessor<InvalidActionDomainPlugin.Arg, InvalidActionDomainPlugin.Result, CreditRuleInvalidContextModel> {
    public InvalidCreditRuleProcessor(NewCustomerAccountManager newCustomerAccountManager, CreditManager creditManager, ServiceFacade serviceFacade) {
        super(newCustomerAccountManager, creditManager, serviceFacade);
    }

    @Override
    protected CreditRulePluginContextKey getContextKey() {
        return CreditRulePluginContextKey.Invalid;
    }

    @Override
    protected Class<CreditRuleInvalidContextModel> getContextClass() {
        return CreditRuleInvalidContextModel.class;
    }

    @Override
    protected InvalidActionDomainPlugin.Result newResultInstance() {
        return new InvalidActionDomainPlugin.Result();
    }

    @Override
    protected IObjectData getObjectData(InvalidActionDomainPlugin.Arg arg) {
        return arg.getObjectData().toObjectData();
    }

    @Override
    protected List<IObjectData> getDetailDataList(InvalidActionDomainPlugin.Arg arg, String detailApiName) {
        return ObjectDataDocument.ofDataList(CollectionUtils.nullToEmpty(arg.getDetailObjectData()).getOrDefault(detailApiName, Lists.newArrayList()));
    }

    @Override
    protected CreditRuleInvalidContextModel doPreAct(RequestContext requestContext, InvalidActionDomainPlugin.Arg arg) {
        return doInvalidPreAct(requestContext, arg.getObjectData().toObjectData());
    }

    @Override
    public void doFinallyDo(RequestContext requestContext, InvalidActionDomainPlugin.Arg arg,CreditRuleInvalidContextModel contextModel) {
        doInvalidFinally(requestContext, arg.getObjectData().toObjectData(), contextModel);
    }
}
