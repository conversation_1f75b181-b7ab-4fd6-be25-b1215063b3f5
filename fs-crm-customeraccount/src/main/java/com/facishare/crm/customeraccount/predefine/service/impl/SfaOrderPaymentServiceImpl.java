package com.facishare.crm.customeraccount.predefine.service.impl;

import com.facishare.crm.customeraccount.constants.CAI18NKey;
import com.facishare.crm.customeraccount.constants.CustomerAccountConstants;
import com.facishare.crm.customeraccount.constants.PrepayDetailConstants;
import com.facishare.crm.customeraccount.constants.RebateIncomeDetailConstants;
import com.facishare.crm.customeraccount.constants.RebateOutcomeDetailConstants;
import com.facishare.crm.customeraccount.constants.RebateUseRuleConstants;
import com.facishare.crm.customeraccount.constants.SystemConstants;
import com.facishare.crm.customeraccount.constants.SystemConstants.LifeStatus;
import com.facishare.crm.customeraccount.enums.BillTypeEnum;
import com.facishare.crm.customeraccount.enums.PaymentTermEnum;
import com.facishare.crm.customeraccount.predefine.action.PrepayDetailFlowCompletedAction;
import com.facishare.crm.customeraccount.predefine.manager.CustomerAccountConfigManager;
import com.facishare.crm.customeraccount.predefine.manager.CustomerAccountManager;
import com.facishare.crm.customeraccount.predefine.manager.PrepayDetailManager;
import com.facishare.crm.customeraccount.predefine.manager.RebateIncomeDetailManager;
import com.facishare.crm.customeraccount.predefine.manager.RebateOutcomeDetailManager;
import com.facishare.crm.customeraccount.predefine.manager.RebateUseRuleManager;
import com.facishare.crm.customeraccount.predefine.service.CommonService;
import com.facishare.crm.customeraccount.predefine.service.SfaOrderPaymentService;
import com.facishare.crm.customeraccount.predefine.service.dto.BatchGetRebateAmountModel;
import com.facishare.crm.customeraccount.predefine.service.dto.BulkInvalidModel;
import com.facishare.crm.customeraccount.predefine.service.dto.PaymentRecoverValidateModel;
import com.facishare.crm.customeraccount.predefine.service.dto.RebateIncomeModle;
import com.facishare.crm.customeraccount.predefine.service.dto.RebateUseRuleValidateModel;
import com.facishare.crm.customeraccount.predefine.service.dto.SfaOrderPaymentModel;
import com.facishare.crm.customeraccount.util.ConfigCenter;
import com.facishare.crm.customeraccount.util.CustomerAccountRecordLogger;
import com.facishare.crm.customeraccount.util.HwConfig;
import com.facishare.crm.customeraccount.util.ObjectDataUtil;
import com.facishare.crm.customeraccount.util.RequestUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContext.RequestSource;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.*;
import com.facishare.paas.appframework.core.predef.service.ObjectRecycleBinService;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @IgnoreI18n
 * Created by xujf on 2018/1/4.
 */
@Slf4j
@Component
public class SfaOrderPaymentServiceImpl extends CommonService implements SfaOrderPaymentService {
    @Autowired
    CustomerAccountManager customerAccountManager;
    @Autowired
    PrepayDetailManager prepayDetailManager;
    @Autowired
    RebateIncomeDetailManager rebateIncomeDetailManager;
    @Autowired
    RebateOutcomeDetailManager rebateOutcomeDetailManager;
    @Autowired
    ObjectRecycleBinService objectRecycleBinService;
    @Autowired
    private RebateUseRuleManager rebateUseRuleManager;
    @Autowired
    CustomerAccountConfigManager customerAccountConfigManager;

    @Override
    public SfaOrderPaymentModel.CreateResult create(SfaOrderPaymentModel.CreateArg arg, ServiceContext serviceContext) {

        if (!customerAccountConfigManager.isCustomerAccountEnable(serviceContext.getTenantId())) {
            log.debug("customer account not enable,for tenantId:{}", serviceContext.getTenantId());
            return new SfaOrderPaymentModel.CreateResult();
        }
        log.debug("createArg:{}", arg);
        User user = serviceContext.getUser();
        if (RequestUtil.isOutUser(user)) {
            //外部联系人调用替换成系统用户
            user = new User(user.getTenantId(), User.SUPPER_ADMIN_USER_ID);
            RequestContext newRequestContext = RequestContext.builder().requestSource(RequestSource.INNER).postId(serviceContext.getPostId()).tenantId(user.getTenantId()).user(user).build();
            serviceContext = new ServiceContext(newRequestContext, serviceContext.getServiceMethod(), serviceContext.getServiceName());
        }
        // 2020-3-11 底层框架采用主从对象生命状态异步同步导致传递的回款明细生命状态不准确，故直接查询回款状态
        IObjectData paymentData = serviceFacade.findObjectData(user, arg.getPaymentId(), SystemConstants.PaymentApiName);
        String lifeStatus = ObjectDataExt.of(paymentData).getLifeStatus().getCode();
        List<String> ownerList = paymentData.getOwner();
        SfaOrderPaymentModel.CreateResult result = new SfaOrderPaymentModel.CreateResult();
        Map<String, SfaOrderPaymentModel.CreateResultDetail> orderPaymentMapResult = Maps.newHashMap();
        Map<String, SfaOrderPaymentModel.CreateArgDetail> orderPaymentMap = arg.getOrderPaymentMap();
        for (Map.Entry<String, SfaOrderPaymentModel.CreateArgDetail> entry : orderPaymentMap.entrySet()) {
            ObjectDataDocument prepayDetailData = entry.getValue().getPrepayDetailData();
            ObjectDataDocument rebateOutcomeDetailData = entry.getValue().getRebateOutcomeDetailData();
            if (Objects.nonNull(prepayDetailData)) {
                prepayDetailData.put(SystemConstants.Field.LifeStatus.apiName, lifeStatus);
            }
            if (Objects.nonNull(rebateOutcomeDetailData)) {
                rebateOutcomeDetailData.put(SystemConstants.Field.LifeStatus.apiName, lifeStatus);
            }
            Map<String, SfaOrderPaymentModel.CreateResultDetail> oneResult = createOneOrderPayment(entry.getKey(), prepayDetailData, rebateOutcomeDetailData, serviceContext, ownerList, arg.isCanOverdraft());
            orderPaymentMapResult.putAll(oneResult);
        }
        result.setOrderPaymentMap(orderPaymentMapResult);
        return result;
    }

    /**
     * 回款id修改成回款明细Id<br>
     * @param orderPaymentId
     * @param prepayObjectDocument
     * @param rebateOutcomeTotalDocument
     * @param serviceContext
     * @return
     */
    private Map<String, SfaOrderPaymentModel.CreateResultDetail> createOneOrderPayment(String orderPaymentId, ObjectDataDocument prepayObjectDocument, ObjectDataDocument rebateOutcomeTotalDocument, ServiceContext serviceContext, List<String> ownerList, boolean canOverdraft) {
        log.debug("createOneOrderPayment====>prepayObjectData:{},rebateOutcomeTotalData:{}", prepayObjectDocument, rebateOutcomeTotalDocument);
        Map<String, SfaOrderPaymentModel.CreateResultDetail> result = Maps.newHashMap();
        User user = serviceContext.getUser();
        SfaOrderPaymentModel.CreateResultDetail resultDetail = new SfaOrderPaymentModel.CreateResultDetail();
        if (Objects.nonNull(prepayObjectDocument)) {
            //幂等处理，已经有预存款就不用再创建
            List<IObjectData> prepayDetailList = prepayDetailManager.listByOrderPaymentIds(serviceContext.getUser(), Lists.newArrayList(orderPaymentId));
            log.info("createOneOrderPayment listByOrderPaymentIds====>user:{},orderPaymentIds:{}", prepayObjectDocument, orderPaymentId);
            if (CollectionUtils.isEmpty(prepayDetailList)) {
                IObjectData prepayObjectData = prepayObjectDocument.toObjectData();
                prepayObjectData.setDescribeApiName(PrepayDetailConstants.API_NAME);
                checkCreateData(prepayObjectData);
                prepayObjectData.set(SystemConstants.Field.RecordType.apiName, PrepayDetailConstants.RecordType.OutcomeRecordType.apiName);
                IObjectDescribe describe = serviceFacade.findObject(serviceContext.getTenantId(), PrepayDetailConstants.API_NAME);
                prepayObjectData.setDescribeId(describe.getId());
                //add
                prepayObjectData.setOwner(ownerList);
                ObjectDataDocument prepayDataResult = this.triggerAddAction(serviceContext, PrepayDetailConstants.API_NAME, ObjectDataDocument.of(prepayObjectData));
                resultDetail.setPrepayDetailData(prepayDataResult);
            }
        }
        if (Objects.nonNull(rebateOutcomeTotalDocument)) {
            IObjectData rebateOutcomeTotalData = rebateOutcomeTotalDocument.toObjectData();
            //校验
            rebateOutcomeTotalData.setDescribeApiName(RebateOutcomeDetailConstants.API_NAME);
            checkCreateData(rebateOutcomeTotalData);
            BigDecimal totalAmountToPay = rebateOutcomeTotalData.get(RebateOutcomeDetailConstants.Field.Amount.apiName, BigDecimal.class);
            String customerId = ObjectDataUtil.getReferenceId(rebateOutcomeTotalData, "customer_id");
            //6.3
            Map<String, IObjectDescribe> objectDescribeMap = serviceFacade.findObjects(serviceContext.getTenantId(), Lists.newArrayList(RebateUseRuleConstants.API_NAME));
            String rebateUseRuleId = null;
            if (objectDescribeMap.containsKey(RebateUseRuleConstants.API_NAME)) {
                Optional<IObjectData> rebateUseRuleObjectDataOption = rebateUseRuleManager.getRebateUseRuleByCustomerId(user, customerId);
                if (rebateUseRuleObjectDataOption.isPresent()) {
                    rebateUseRuleId = rebateUseRuleObjectDataOption.get().getId();
                }
            }

            //幂等，扣掉"回款明细"已经创建的"返利支出"
            totalAmountToPay = getTotalAmountToPay(user, orderPaymentId, totalAmountToPay);
            if (totalAmountToPay.compareTo(BigDecimal.ZERO) <= 0) {
                log.info("createOneOrderPayment  totalAmountToPay <= 0, rebateOutcomeTotalDocument[{}]", rebateOutcomeTotalDocument);
                return result;
            }

            IObjectData customerAccountObj = customerAccountManager.getCustomerAccountByCustomerId(user, customerId);
            BigDecimal rebateAvailable = customerAccountObj.get(CustomerAccountConstants.Field.RebateAvailableBalance.apiName, BigDecimal.class);
            if (rebateAvailable.compareTo(totalAmountToPay) < 0) {
                log.info("customerAccountObj info:{}", customerAccountObj);
                if (!canOverdraft) {
                    throw new ValidateException("返利可用余额不足");
                }
            }

            //获取可用返利收入
            RebateIncomeModle.PayForOutcome payForOutcome = rebateIncomeDetailManager.obtainRebateIncomeToPayList(user, totalAmountToPay, customerId, canOverdraft);
            List<RebateIncomeModle.PayForOutcomeModel> incomeObjectDataListToPay = payForOutcome.getPayForOutcomes();
            log.info("rebateIncomeDetailManager.obtainRebateIncomeToPayList  user:{}, totalAmountToPay:{}, customerId:{}, canOverdraft:{}, incomeObjectDataListToPay.size:{}, payForOutcome:{}",
                    user, totalAmountToPay, customerId, canOverdraft, incomeObjectDataListToPay.size(), payForOutcome);
            BigDecimal amountLeft = payForOutcome.getAmountLeft(); //最后还差多少钱

            RebateIncomeModle.PayForOutcomeModel payForNoEnoughOutcome = null;
            //'返利'不够创建'返利支出'，随便找一条'返利'来创建'返利支出'
            if (amountLeft.compareTo(BigDecimal.ZERO) > 0) {
                if (!CollectionUtils.isEmpty(incomeObjectDataListToPay)) {
                    payForNoEnoughOutcome = incomeObjectDataListToPay.get(0);
                } else {
                    payForNoEnoughOutcome = rebateIncomeDetailManager.obtainRebateIncomeToPay(user, amountLeft, customerId);
                    if (payForNoEnoughOutcome == null) {
                        log.warn("createOneOrderPayment  payForNoEnoughOutcome = null, user:{}, amountLeft:{}, customerId:{}, orderPaymentId:{}, prepayObjectDocument:{},, rebateOutcomeTotalDocument:{},, serviceContext:{},, ownerList:{},, canOverdraft:{},",
                                user, amountLeft, customerId, orderPaymentId, prepayObjectDocument, rebateOutcomeTotalDocument, serviceContext, ownerList, canOverdraft);
                        throw new ValidateException("没有可以扣减的返利");
                    }
                }
            }
            List<ObjectDataDocument> rebateOutcomeDataResults = new ArrayList<>();
            //扣减返利收入和创建支出
            for (RebateIncomeModle.PayForOutcomeModel payForOutcomeModel : incomeObjectDataListToPay) {
                IObjectData resultData = createRebateOutcomeAndUpdateBalance(user, payForOutcomeModel, rebateOutcomeTotalData, ownerList, rebateUseRuleId);
                rebateOutcomeDataResults.add(ObjectDataDocument.of(resultData));
            }

            //'返利'不够创建'返利支出'，随便找一条'返利'来创建'返利支出'
            if (amountLeft.compareTo(BigDecimal.ZERO) > 0) {
                payForNoEnoughOutcome.setPayAmount(amountLeft);
                IObjectData resultData = createRebateOutcomeAndUpdateBalance(user, payForNoEnoughOutcome, rebateOutcomeTotalData, ownerList, rebateUseRuleId);
                rebateOutcomeDataResults.add(ObjectDataDocument.of(resultData));
            }
            resultDetail.setRebateOutcomeDetailDatas(rebateOutcomeDataResults);
        }
        result.put(orderPaymentId, resultDetail);
        return result;
    }

    BigDecimal getTotalAmountToPay(User user, String orderPaymentId, BigDecimal totalAmountToPay) {
        log.info("getTotalAmountToPay before subtract totalAmountToPay[{}]", totalAmountToPay);
        //'回款明细'已经创建的'返利支出'
        List<IObjectData> rebateOutcomeDetailList = rebateOutcomeDetailManager.listByOrderPaymentIds(user, Lists.newArrayList(orderPaymentId));
        if (CollectionUtils.isEmpty(rebateOutcomeDetailList)) {
            return totalAmountToPay;
        }

        for (IObjectData outcome : rebateOutcomeDetailList) {
            BigDecimal existAmount = outcome.get(RebateOutcomeDetailConstants.Field.Amount.apiName, BigDecimal.class);
            totalAmountToPay = totalAmountToPay.subtract(existAmount);
            log.info("getTotalAmountToPay after subtract totalAmountToPay[{}], existAmount[{}]", totalAmountToPay, existAmount);
        }


        log.info("getTotalAmountToPay after subtract totalAmountToPay[{}]", totalAmountToPay);
        return totalAmountToPay;
    }

    public IObjectData createRebateOutcomeAndUpdateBalance(User user, RebateIncomeModle.PayForOutcomeModel payForOutcome, IObjectData rebateOutcomeTotalData, List<String> ownerList, String rebateUseRuleId) {
        IObjectData rebateOutcome = new ObjectData();
        rebateOutcome.setDescribeApiName(RebateOutcomeDetailConstants.API_NAME);
        rebateOutcome.set(RebateOutcomeDetailConstants.Field.TransactionTime.apiName, rebateOutcomeTotalData.get(RebateOutcomeDetailConstants.Field.TransactionTime.apiName));
        rebateOutcome.set(RebateOutcomeDetailConstants.Field.RebateIncomeDetail.apiName, payForOutcome.getRebateIncomeObj().getId());
        rebateOutcome.set(RebateOutcomeDetailConstants.Field.Amount.apiName, payForOutcome.getPayAmount());
        rebateOutcome.set(SystemConstants.Field.LifeStatus.apiName, rebateOutcomeTotalData.get(SystemConstants.Field.LifeStatus.apiName));
        rebateOutcome.set(RebateOutcomeDetailConstants.Field.RebateUseRule.apiName, rebateUseRuleId);
        rebateOutcome.set(RebateOutcomeDetailConstants.Field.OrderPayment.apiName, rebateOutcomeTotalData.get(RebateOutcomeDetailConstants.Field.OrderPayment.apiName));
        //add
        rebateOutcome.setOwner(ownerList);
        return rebateOutcomeDetailManager.createRebateOutcomeAndUpdateBalance(user, rebateOutcome);
    }

    private void checkCreateData(IObjectData objectData) {
        log.debug("checkCreateData->objectData:{}", objectData);
        String lifeStatus = objectData.get(SystemConstants.Field.LifeStatus.apiName, String.class);
        String customerId = ObjectDataUtil.getReferenceId(objectData, "customer_id");
        String paymentId = ObjectDataUtil.getReferenceId(objectData, "payment_id");
        String refundId = ObjectDataUtil.getReferenceId(objectData, "refund_id");
        String errorMessage = "";
        if (StringUtils.isEmpty(lifeStatus)) {
            errorMessage += "LifeStatus不能为空 ";
        }
        if (PrepayDetailConstants.API_NAME.equals(objectData.getDescribeApiName())) {
            if (!ObjectDataUtil.isPrepayIncome(objectData)) {
                if (StringUtils.isEmpty(paymentId)) {
                    errorMessage += "PaymentId不能为空 ";
                }
            } else {
                if (StringUtils.isEmpty(refundId)) {
                    errorMessage += "RefundId不能为空 ";
                }
            }
        } else if (RebateOutcomeDetailConstants.API_NAME.equals(objectData.getDescribeApiName())) {
            if (StringUtils.isEmpty(paymentId)) {
                errorMessage += "PaymentId不能为空 ";
            }
        }
        if (StringUtils.isEmpty(customerId)) {
            errorMessage += "CustomerId不能为空";
        }
        if (!errorMessage.isEmpty()) {
            throw new ValidateException(errorMessage);
        }
    }

    @Override
    public SfaOrderPaymentModel.Result flowComplete(SfaOrderPaymentModel.FlowCompleteArg arg, ServiceContext serviceContext) {
        if (!customerAccountConfigManager.isCustomerAccountEnable(serviceContext.getTenantId())) {
            log.debug("customer account not enable,for tenantId:{}", serviceContext.getTenantId());
            return new SfaOrderPaymentModel.Result();
        }

        SfaOrderPaymentModel.Result result = new SfaOrderPaymentModel.Result();
        List<String> orderPaymentIds = arg.getDataIds();
        String lifeStatus = arg.getLifeStatus();
        ApprovalFlowTriggerType approvalType = getApprovalFlowTriggerTypeInstance(arg.getApprovalType());
        if (approvalType == null) {
            throw new ValidateException(String.format("ApprovalType[%s]无效", approvalType));
        }
        if (SystemConstants.LifeStatus.UnderReview.value.equals(lifeStatus) || SystemConstants.LifeStatus.InChange.value.equals(lifeStatus)) {
            result.setSuccess(true);
            log.info("nochange flowComplete, arg={}", arg);
            return result;
        }
        orderPaymentIds.forEach(orderPaymentId -> handleOneOrderPaymentFlowComplete(serviceContext, orderPaymentId, lifeStatus, approvalType));
        result.setSuccess(true);
        return result;
    }

    private boolean handleOneOrderPaymentFlowComplete(ServiceContext serviceContext, String orderPaymentId, String lifeStatus, ApprovalFlowTriggerType approvalType) {
        boolean hasRelateData = false;
        IObjectData prepayObjectData = prepayDetailManager.getByOrderPaymentId(serviceContext.getUser(), orderPaymentId);
        if (Objects.nonNull(prepayObjectData)) {
            hasRelateData = true;
            prepayFlowComplete(serviceContext, lifeStatus, approvalType, prepayObjectData);
        }
        List<IObjectData> rebateOutcomeObjectDatas = rebateOutcomeDetailManager.getByOrderPaymentId(serviceContext.getUser(), orderPaymentId);
        if (CollectionUtils.isNotEmpty(rebateOutcomeObjectDatas)) {
            hasRelateData = true;
            for (IObjectData outcome : rebateOutcomeObjectDatas) {
                String incomeId = outcome.get(RebateOutcomeDetailConstants.Field.RebateIncomeDetail.apiName, String.class);
                String outcomeId = outcome.getId();
                BigDecimal amount = outcome.get(RebateOutcomeDetailConstants.Field.Amount.apiName, BigDecimal.class);
                String oldOutcomeLifeStatus = outcome.get(SystemConstants.Field.LifeStatus.apiName, String.class);
                outcome.set(SystemConstants.Field.LifeStatus.apiName, lifeStatus);
                rebateOutcomeDetailManager.update(serviceContext.getUser(), outcome);
                if (SystemConstants.LifeStatus.Invalid.value.equals(lifeStatus)) {
                    serviceFacade.invalid(outcome, serviceContext.getUser());
                }
                rebateOutcomeDetailManager.updateBalanceForOutcome(serviceContext.getUser(), incomeId, outcomeId, amount, oldOutcomeLifeStatus, lifeStatus);
            }
        }
        if (!hasRelateData) {
            log.warn("OrderPaymentFlowComplete:{},lifeStatus:{},approvalType:{}", String.format("回款明细{%s}没有关联的交易明细", orderPaymentId), lifeStatus, approvalType);
        }
        return hasRelateData;
    }

    private boolean prepayFlowComplete(ServiceContext serviceContext, String lifeStatus, ApprovalFlowTriggerType approvalType, IObjectData prepayObjectData) {
        String passStatus = PrepayDetailFlowCompletedAction.Arg.PASS;
        ApprovalFlowTriggerType approvalFlowTriggerType = null;
        if (ApprovalFlowTriggerType.CREATE == approvalType) {
            if (SystemConstants.LifeStatus.Ineffective.value.equals(lifeStatus)) {
                passStatus = "notpass";
            } else if (SystemConstants.LifeStatus.UnderReview.value.equals(lifeStatus)) {
                return true;
            }
            approvalFlowTriggerType = ApprovalFlowTriggerType.CREATE;
        } else if (ApprovalFlowTriggerType.INVALID == approvalType) {
            if (SystemConstants.LifeStatus.Ineffective.value.equals(lifeStatus)) {
                //对未生效的数据作废，进入审批，被驳回或者撤回
                passStatus = "notpass";
            } else if (SystemConstants.LifeStatus.InChange.value.equals(lifeStatus)) {
                return true;
            } else if (SystemConstants.LifeStatus.Normal.value.equals(lifeStatus)) {
                passStatus = "notpass";
            }
            approvalFlowTriggerType = ApprovalFlowTriggerType.INVALID;
        } else if (ApprovalFlowTriggerType.UPDATE == approvalType) {
            approvalFlowTriggerType = ApprovalFlowTriggerType.UPDATE;
        }
        PrepayDetailFlowCompletedAction.Arg prepayDetailFlowCompletedActionArg = new PrepayDetailFlowCompletedAction.Arg();
        prepayDetailFlowCompletedActionArg.setTriggerType(String.valueOf(approvalFlowTriggerType.getTriggerTypeCode()));
        prepayDetailFlowCompletedActionArg.setDescribeApiName(PrepayDetailConstants.API_NAME);
        prepayDetailFlowCompletedActionArg.setDataId(prepayObjectData.getId());
        prepayDetailFlowCompletedActionArg.setTenantId(serviceContext.getTenantId());
        prepayDetailFlowCompletedActionArg.setUserId(serviceContext.getUser().getUserId());
        prepayDetailFlowCompletedActionArg.setStatus(passStatus);
        PrepayDetailFlowCompletedAction.Result flowResult = this.triggerAction(serviceContext, PrepayDetailConstants.API_NAME, "FlowCompleted", prepayDetailFlowCompletedActionArg, StandardFlowCompletedAction.Result.class);
        log.debug("PaymentFlowComplete->PrepayLifeStatus Update,ApprovalType:{},dateId:{},Result:{}", approvalFlowTriggerType.getTriggerTypeName(), prepayObjectData.getId(), flowResult.getSuccess());
        return flowResult.getSuccess();
    }

    @Override
    public SfaOrderPaymentModel.Result invalid(SfaOrderPaymentModel.InvalidArg arg, ServiceContext serviceContext) {
        if (!customerAccountConfigManager.isCustomerAccountEnable(serviceContext.getTenantId())) {
            log.debug("customer account not enable,for tenantId:{}", serviceContext.getTenantId());
            return new SfaOrderPaymentModel.Result();
        }
        String lifeStatus = arg.getLifeStatus();
        if (Objects.isNull(lifeStatus)) {
            throw new ValidateException("While Invalid prepay id is null");
        }
        List<String> orderPaymentIds = arg.getDataIds();
        orderPaymentIds.forEach(orderPaymentId -> {
            invalidOneOrderPayment(serviceContext, lifeStatus, orderPaymentId);
        });
        SfaOrderPaymentModel.Result invalidResult = new SfaOrderPaymentModel.Result();
        invalidResult.setSuccess(true);
        return invalidResult;
    }

    private void invalidOneOrderPayment(ServiceContext serviceContext, String lifeStatus, String orderPaymentId) {
        boolean hasRelateData = false;
        String id;
        IObjectData prepayData = prepayDetailManager.getByOrderPaymentId(serviceContext.getUser(), orderPaymentId);
        if (Objects.nonNull(prepayData)) {
            hasRelateData = true;
            id = prepayData.getId();
            Map<String, Object> params = Maps.newHashMap();
            params.put("lifeStatus", lifeStatus);
            ObjectDataDocument result = this.triggerInvalidAction(serviceContext, PrepayDetailConstants.API_NAME, id, params);
            log.debug("Invalid PrepayDetail paymentId:{},id:{},Result:{}", orderPaymentId, id, JsonUtil.toJson(result));
        }
        List<IObjectData> rebateOutcomes = rebateOutcomeDetailManager.getByOrderPaymentId(serviceContext.getUser(), orderPaymentId);
        if (CollectionUtils.isNotEmpty(rebateOutcomes)) {
            hasRelateData = true;
            log.debug("bulkInvalid RebateOutcomes paymentId:{},rebateOutconmes:{}", orderPaymentId, rebateOutcomes);
            rebateOutcomeDetailManager.bulkInvalid(serviceContext.getUser(), rebateOutcomes, lifeStatus);
        }
        if (!hasRelateData) {
            log.warn("无关联数据,orderPaymentId:{},lifeStatus:{}", orderPaymentId, lifeStatus);
        }
    }

    @Override
    public SfaOrderPaymentModel.Result bulkInvalid(SfaOrderPaymentModel.BulkInvalidArg arg, ServiceContext serviceContext) {

        if (!customerAccountConfigManager.isCustomerAccountEnable(serviceContext.getTenantId())) {
            log.debug("customer account not enable,for tenantId:{}", serviceContext.getTenantId());
            return new SfaOrderPaymentModel.Result();
        }

        SfaOrderPaymentModel.Result result = new SfaOrderPaymentModel.Result();
        //预存款作废

        List<SfaOrderPaymentModel.InvalidArg> invalidArgs = arg.getInvalidArgs();
        invalidArgs.forEach(invalidArg -> {
            invalidAccordPayment(invalidArg, serviceContext);
        });
        result.setSuccess(true);
        return result;
    }

    /**
     * 作废一个回款下的预存款和返利<br>
     * @param arg
     * @param serviceContext
     */
    private void invalidAccordPayment(SfaOrderPaymentModel.InvalidArg arg, ServiceContext serviceContext) {
        List<String> orderPaymentIds = arg.getDataIds();
        BulkInvalidModel.Arg prepayBulkInvalidArg = new BulkInvalidModel.Arg();
        List<BulkInvalidModel.InvalidArg> prepayInvalidArgList = Lists.newArrayList();
        boolean hasRelateData = false;
        List<IObjectData> prepayDetailList = prepayDetailManager.listByOrderPaymentIds(serviceContext.getUser(), orderPaymentIds);
        if (CollectionUtils.isNotEmpty(prepayDetailList)) {
            hasRelateData = true;
            prepayDetailList.stream().forEach(prepay -> {
                BulkInvalidModel.InvalidArg invalidArg = new BulkInvalidModel.InvalidArg();
                invalidArg.setId(prepay.getId());
                invalidArg.setLifeStatus(arg.getLifeStatus());
                invalidArg.setObjectDescribeApiName(PrepayDetailConstants.API_NAME);
                prepayInvalidArgList.add(invalidArg);
            });
            prepayBulkInvalidArg.setDataList(prepayInvalidArgList);
            log.debug("BulkInvalid PrepayDetail Arg:{},SfaArg:{}", JsonUtil.toJson(prepayBulkInvalidArg), JsonUtil.toJson(arg));
            List<ObjectDataDocument> prepayResult = bulkInvalidPrepayDetail(prepayBulkInvalidArg, serviceContext);
            log.debug("BulkInvalid PrepayDetail,Arg:{},Result:{}", JsonUtil.toJson(prepayBulkInvalidArg), JsonUtil.toJson(prepayResult));
        }
        //返利支出作废
        List<IObjectData> rebateOutcomeDetailList = rebateOutcomeDetailManager.listByOrderPaymentIds(serviceContext.getUser(), orderPaymentIds);
        if (CollectionUtils.isNotEmpty(rebateOutcomeDetailList)) {
            hasRelateData = true;
            rebateOutcomeDetailManager.bulkInvalid(serviceContext.getUser(), rebateOutcomeDetailList, arg.getLifeStatus());
        }
        if (!hasRelateData) {
            log.warn("无关联数据,arg:{}", arg);
        }
    }

    private List<ObjectDataDocument> bulkInvalidPrepayDetail(BulkInvalidModel.Arg bulkInvalidArg, ServiceContext serviceContext) {
        Map<String, String> lifeStatusMap = bulkInvalidArg.getDataList().stream().collect(Collectors.toMap(BulkInvalidModel.InvalidArg::getId, BulkInvalidModel.InvalidArg::getLifeStatus));
        StandardBulkInvalidAction.Arg arg = new StandardBulkInvalidAction.Arg();
        arg.setJson(JsonUtil.toJson(bulkInvalidArg));
        Map<String, Object> params = Maps.newHashMap();
        params.put("dataIdLifeStatusMap", lifeStatusMap);
        StandardBulkInvalidAction.Result result = this.triggerAction(serviceContext, PrepayDetailConstants.API_NAME, StandardAction.BulkInvalid.name(), arg, params, StandardBulkInvalidAction.Result.class);
        return result.getObjectDataList();
    }

    @Override
    public SfaOrderPaymentModel.Result bulkRecover(SfaOrderPaymentModel.BulkRecoverArg arg, ServiceContext serviceContext) {

        if (!customerAccountConfigManager.isCustomerAccountEnable(serviceContext.getTenantId())) {
            log.debug("customer account not enable,for tenantId:{}", serviceContext.getTenantId());
            return new SfaOrderPaymentModel.Result();
        }

        Map<String, List<String>> orderPaymentMap = arg.getOrderPaymentMap();

        List<String> orderPaymentIds = new ArrayList<String>();
        for (Map.Entry<String, List<String>> entry : orderPaymentMap.entrySet()) {
            orderPaymentIds.addAll(entry.getValue());
        }

        List<IObjectData> prepayDatas = prepayDetailManager.listInvalidDataByOrderPaymentIds(serviceContext.getUser(), orderPaymentIds);
        if (CollectionUtils.isNotEmpty(prepayDatas)) {
            List<String> prepayIds = prepayDatas.stream().map(objectData -> objectData.getId()).collect(Collectors.toList());
            StandardBulkRecoverAction.Arg prepayRecoverArg = new StandardBulkRecoverAction.Arg();
            prepayRecoverArg.setIdList(prepayIds);
            prepayRecoverArg.setObjectDescribeAPIName(PrepayDetailConstants.API_NAME);
            this.triggerAction(serviceContext, PrepayDetailConstants.API_NAME, StandardAction.BulkRecover.name(), prepayRecoverArg, StandardBulkRecoverAction.Result.class);
        }

        List<IObjectData> rebateOutcomeDatas = rebateOutcomeDetailManager.listInvalidDataByOrderPaymentIds(serviceContext.getUser(), orderPaymentIds);
        if (CollectionUtils.isNotEmpty(rebateOutcomeDatas)) {
            List<String> rebateOutcomeIds = rebateOutcomeDatas.stream().map(objectData -> objectData.getId()).collect(Collectors.toList());
            StandardBulkRecoverAction.Arg rebateOutcomeRecoverArg = new StandardBulkRecoverAction.Arg();
            rebateOutcomeRecoverArg.setIdList(rebateOutcomeIds);
            rebateOutcomeRecoverArg.setObjectDescribeAPIName(RebateOutcomeDetailConstants.API_NAME);
            this.triggerAction(serviceContext, RebateOutcomeDetailConstants.API_NAME, StandardAction.BulkRecover.name(), rebateOutcomeRecoverArg, StandardBulkRecoverAction.Result.class);
        }
        SfaOrderPaymentModel.Result result = new SfaOrderPaymentModel.Result();
        result.setSuccess(true);
        return result;
    }

    @Override
    public SfaOrderPaymentModel.Result bulkDelete(SfaOrderPaymentModel.BulkDeleteArg arg, ServiceContext serviceContext) {
        if (!customerAccountConfigManager.isCustomerAccountEnable(serviceContext.getTenantId())) {
            log.debug("customer account not enable,for tenantId:{}", serviceContext.getTenantId());
            return new SfaOrderPaymentModel.Result();
        }
        SfaOrderPaymentModel.Result result = new SfaOrderPaymentModel.Result();
        result.setSuccess(true);
        //需要增加编辑的参数，因为编辑回款引起的删除对余额会有变更。
        Map<String, List<String>> orderPaymentMap = arg.getOrderPaymentMap();
        List<String> orderPaymentIds = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : orderPaymentMap.entrySet()) {
            orderPaymentIds.addAll(entry.getValue());
        }

        boolean deleteByUpdate = ApprovalFlowTriggerType.UPDATE.getId().equals(arg.getApprovalType());
        List<IObjectData> prepayDatas = prepayDetailManager.listInvalidDataByOrderPaymentIds(serviceContext.getUser(), orderPaymentIds);
        if (CollectionUtils.isNotEmpty(prepayDatas)) {
            if (deleteByUpdate) {
                Map<String, String> idLifeStatusMap = prepayDatas.stream().collect(Collectors.toMap(IObjectData::getId, data -> ObjectDataExt.of(data).getLifeStatusText()));
                serviceFacade.bulkInvalidAndDeleteWithSuperPrivilege(prepayDatas, serviceContext.getUser());
                /**
                 * 编辑回款，会直接删除下面对应的回款明细。此时需要也应该变更余额。
                 */
                for (IObjectData prepayData : prepayDatas) {
                    try {
                        log.debug("after delete prepaydate and then update balance,for prepaydata;{}", prepayData);
                        String oldLifeStatus = idLifeStatusMap.get(prepayData.getId());//prepayData.get(SystemConstants.Field.LifeStatus.apiName, String.class);
                        prepayDetailManager.updateBalanceWhenDeleteByOrderPayment(serviceContext.getUser(), prepayData, oldLifeStatus);
                    } catch (Exception e) {
                        log.error("exception error when updateBalance,for prepayDataId:{}", prepayData.getId());
                    }
                }
            } else {
                List<String> prepayIds = prepayDatas.stream().map(IObjectData::getId).collect(Collectors.toList());
                com.facishare.paas.appframework.core.predef.action.StandardBulkDeleteAction.Arg actionArg = new com.facishare.paas.appframework.core.predef.action.StandardBulkDeleteAction.Arg();
                actionArg.setIdList(prepayIds);
                actionArg.setDescribeApiName(PrepayDetailConstants.API_NAME);
                this.triggerAction(serviceContext, PrepayDetailConstants.API_NAME, StandardAction.BulkDelete.name(), actionArg, StandardBulkDeleteAction.Result.class);
            }
        }
        List<IObjectData> rebateOutcomeDatas = rebateOutcomeDetailManager.listInvalidDataByOrderPaymentIds(serviceContext.getUser(), orderPaymentIds);
        if (CollectionUtils.isNotEmpty(rebateOutcomeDatas)) {
            if (deleteByUpdate) {
                Map<String, String> idLifeStatusMap = rebateOutcomeDatas.stream().collect(Collectors.toMap(IObjectData::getId, data -> ObjectDataExt.of(data).getLifeStatusText()));
                serviceFacade.bulkInvalidAndDeleteWithSuperPrivilege(rebateOutcomeDatas, serviceContext.getUser());
                /**
                 * 编辑回款，会直接删除下面对应的回款明细。此时需要也应该变更余额。
                 */
                for (IObjectData rebateData : rebateOutcomeDatas) {
                    try {
                        log.debug("after delete rebateData and then update balance,for rebateData;{}", rebateData);
                        String oldLifeStatus = idLifeStatusMap.get(rebateData.getId());//rebateData.get(SystemConstants.Field.LifeStatus.apiName, String.class);
                        String incomeId = rebateData.get(RebateOutcomeDetailConstants.Field.RebateIncomeDetail.apiName, String.class);
                        String outcomeId = rebateData.getId();
                        BigDecimal amount = rebateData.get(RebateOutcomeDetailConstants.Field.Amount.apiName, BigDecimal.class);
                        rebateOutcomeDetailManager.updateBalanceForOutcomeWhenOrderPaymentDelete(serviceContext.getUser(), incomeId, outcomeId, amount, oldLifeStatus);
                    } catch (Exception e) {
                        log.error("exception error when updateBalance,for prepayDataId:{}", rebateData.getId());
                    }
                }
            } else {
                List<String> rebateOutcomeIds = rebateOutcomeDatas.stream().map(IObjectData::getId).collect(Collectors.toList());
                com.facishare.paas.appframework.core.predef.action.StandardBulkDeleteAction.Arg actionArg = new com.facishare.paas.appframework.core.predef.action.StandardBulkDeleteAction.Arg();
                actionArg.setDescribeApiName(RebateOutcomeDetailConstants.API_NAME);
                actionArg.setIdList(rebateOutcomeIds);
                this.triggerAction(serviceContext, RebateOutcomeDetailConstants.API_NAME, StandardAction.BulkDelete.name(), actionArg, StandardBulkDeleteAction.Result.class);
            }
        }

        return result;
    }

    /**
     * 1.编辑有 有审批流和无审批流的情况<br>
     * 无审批流的情况：由于回款页面只能够删除回款明细，对于这种情况直接调用 bulkdelete接口。 orderPayment 由老状态变成了 删除状态<br>
     * 对于有审批流的情况：
     * 1）.先调用edit接口，此时明细的状态为inchange状态。<br>
     * 2).如果编辑审批流回调（回款编辑确认，对于删除的回款明细需要调用bulkdelete接口），此时回款明细就由inchange->变成了delete状态。
     *
     * @param arg
     * @param serviceContext
     * @return
     */
    @Override
    public SfaOrderPaymentModel.Result edit(SfaOrderPaymentModel.EditArgNew arg, ServiceContext serviceContext) {

        if (!customerAccountConfigManager.isCustomerAccountEnable(serviceContext.getTenantId())) {
            log.debug("customer account not enable,for tenantId:{}", serviceContext.getTenantId());
            return new SfaOrderPaymentModel.Result();
        }

        SfaOrderPaymentModel.Result result = new SfaOrderPaymentModel.Result();

        Map<String, String> orderPaymentMap = arg.getDataMap();

        ApprovalFlowTriggerType approvalType = getApprovalFlowTriggerTypeInstance(arg.getApprovalType());
        if (approvalType == null) {
            throw new ValidateException(String.format("ApprovalType[%s]无效", approvalType));
        }

        if (approvalType.getId().equals(ApprovalFlowTriggerType.UPDATE.getId())) {
            log.debug("SfaOrderPaymentServiceImpl.edit.arg:{}", arg);
            for (Map.Entry<String, String> entry : orderPaymentMap.entrySet()) {
                handleOneOrderPaymentEdit(serviceContext, entry.getValue(), entry.getKey());
            }
        }
        return result;
    }

    /**
     * 编辑回款 会触发 删除回款明细 如果有审批流那么传过来的状态为inchange。<br>
     * 编辑回款删除回款明细   无审批流和 审批确认两种方式都是调用bulkDeleted()接口<br>
     *  对于不动的明细：
     *  a).未生效 -> 审批中<br>
     *  b).  status1 -> status1 状态不变不进行处理<br>
     * @param serviceContext
     * @param lifeStatus
     * @param orderPaymentId
     */
    private void handleOneOrderPaymentEdit(ServiceContext serviceContext, String lifeStatus, String orderPaymentId) {
        IObjectData prepayObj = prepayDetailManager.getByOrderPaymentId(serviceContext.getUser(), orderPaymentId);
        if (null != prepayObj) {
            String prepayOldLifeStatus = prepayObj.get(SystemConstants.Field.LifeStatus.apiName, String.class);
            log.debug("begin edit prepayDetail,the prepayObj:{}", prepayObj);
            prepayObj.set(SystemConstants.Field.LifeStatus.apiName, lifeStatus);
            if (SystemConstants.LifeStatus.InChange.value.equals(lifeStatus)) {
                //underReview说明有审批流需要锁定<br>
                prepayObj.set(SystemConstants.Field.LockStatus.apiName, SystemConstants.LockStatus.Locked.value);
            }
            if (!prepayOldLifeStatus.equals(lifeStatus)) {
                IObjectData editResultObj = serviceFacade.updateObjectData(serviceContext.getUser(), prepayObj);
                //更新预付款余额<br>
                log.debug("after edit  prepaydate and then update balance,for prepaydata;{}", editResultObj);

                prepayDetailManager.updateBalance(serviceContext.getUser(), prepayObj, prepayOldLifeStatus);
                log.debug("after edit prepayDetail,the rebate editResultObj:{},prepayOldLifestatus:{},newLifestatus:{}", editResultObj, prepayOldLifeStatus, lifeStatus);
            }
        }

        List<IObjectData> rebateObjList = rebateOutcomeDetailManager.getByOrderPaymentId(serviceContext.getUser(), orderPaymentId);
        if (CollectionUtils.isNotEmpty(rebateObjList)) {
            for (IObjectData rebateObj : rebateObjList) {
                String rebateOldLifeStatus = rebateObj.get(SystemConstants.Field.LifeStatus.apiName, String.class);
                if (!lifeStatus.equals(rebateOldLifeStatus)) {
                    log.debug("begin edit rebateDetailObj,,the rebateObj:{}", rebateObj);
                    rebateObj.set(SystemConstants.Field.LifeStatus.apiName, lifeStatus);
                    if (SystemConstants.LifeStatus.InChange.value.equals(lifeStatus)) {
                        rebateObj.set(SystemConstants.Field.LockStatus.apiName, SystemConstants.LockStatus.Locked.value);
                    }
                    IObjectData updateResultObj = rebateOutcomeDetailManager.editRebateOutcomeAndUpdateBalanceFromSfa(serviceContext.getUser(), rebateObj, rebateOldLifeStatus);
                    log.debug("after edit rebateDetailObj,,the rebate updateResultObj:{},for rebateOldLifeStatus:{},newLifestatus:{}", updateResultObj, rebateOldLifeStatus, lifeStatus);
                }
            }
        }
    }

    @Override
    public SfaOrderPaymentModel.GetRelativeNameByOrderPaymentIdResult getRelativeNamesByOrderPaymentId(SfaOrderPaymentModel.GetRelativeNameByOrderPaymentIdArg arg, ServiceContext serviceContext) {
        IObjectData prepayObjectData = prepayDetailManager.getByOrderPaymentId(serviceContext.getUser(), arg.getOrderPaymentId());
        List<IObjectData> rebateOutcomeObjectDataList = rebateOutcomeDetailManager.listByOrderPaymentIds(serviceContext.getUser(), Lists.newArrayList(arg.getOrderPaymentId()));
        SfaOrderPaymentModel.GetRelativeNameByOrderPaymentIdResult result = new SfaOrderPaymentModel.GetRelativeNameByOrderPaymentIdResult();
        if (prepayObjectData != null) {
            result.setPrepayName(prepayObjectData.get(PrepayDetailConstants.Field.Name.apiName, String.class));
        }
        List<String> rebateNames = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(rebateOutcomeObjectDataList)) {
            rebateOutcomeObjectDataList.forEach(data -> {
                rebateNames.add(data.get(RebateOutcomeDetailConstants.Field.Name.apiName, String.class));
            });
            result.setRebateOutcomeNames(rebateNames);
        }
        return result;
    }

    @Override
    public SfaOrderPaymentModel.GetOrderPaymentCostByOrderPaymentIdResult getOrderPaymentCostByOrderPaymentId(SfaOrderPaymentModel.GetOrderPaymentCostByOrderPaymentIdArg arg, ServiceContext serviceContext) {
        IObjectData prepayObjectData = prepayDetailManager.getByOrderPaymentId(serviceContext.getUser(), arg.getOrderPaymentId());
        List<IObjectData> rebateOutcomeObjectDataList = rebateOutcomeDetailManager.listByOrderPaymentIds(serviceContext.getUser(), Lists.newArrayList(arg.getOrderPaymentId()));
        SfaOrderPaymentModel.GetOrderPaymentCostByOrderPaymentIdResult result = new SfaOrderPaymentModel.GetOrderPaymentCostByOrderPaymentIdResult();
        if (prepayObjectData != null) {
            result.setPrepayAmount(prepayObjectData.get(PrepayDetailConstants.Field.Amount.apiName, BigDecimal.class));
        }
        BigDecimal rebateAmounts = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(rebateOutcomeObjectDataList)) {
            for (IObjectData data : rebateOutcomeObjectDataList) {
                rebateAmounts = rebateAmounts.add(data.get(RebateOutcomeDetailConstants.Field.Amount.apiName, BigDecimal.class));
            }
            result.setRebateOutcomeAmount(rebateAmounts);
        }
        return result;
    }

    @Override
    public RebateUseRuleValidateModel.Result validateRebateUseRule(RebateUseRuleValidateModel.Arg arg, ServiceContext serviceContext) {
        RebateUseRuleValidateModel.Result result = new RebateUseRuleValidateModel.Result();
        Map<String, IObjectDescribe> objectDescribeMap = serviceFacade.findObjects(serviceContext.getTenantId(), Lists.newArrayList(RebateUseRuleConstants.API_NAME));
        if (!objectDescribeMap.containsKey(RebateUseRuleConstants.API_NAME)) {
            Map<String, RebateUseRuleValidateModel.RebateUseRuleValidateResult> resultMap = arg.getOrderIdRebateAmountMap().keySet().stream().collect(Collectors.toMap(orderId -> orderId, x -> {
                RebateUseRuleValidateModel.RebateUseRuleValidateResult temp = new RebateUseRuleValidateModel.RebateUseRuleValidateResult();
                temp.setCanUseRebate(true);
                return temp;
            }));
            result.setOrderIdValidateResultMap(resultMap);
            return result;
        }
        Map<String, RebateUseRuleValidateModel.RebateUseRuleValidateResult> orderValidateResultMap = rebateUseRuleManager.validate(serviceContext.getUser(), arg.getCustomerId(), arg.getOrderIdRebateAmountMap());
        result.setOrderIdValidateResultMap(orderValidateResultMap);
        return result;
    }

    @Override
    public BatchGetRebateAmountModel.Result batchGetRebateAmountByOrderPaymentIds(BatchGetRebateAmountModel.Arg arg, ServiceContext serviceContext) {
        BatchGetRebateAmountModel.Result result = new BatchGetRebateAmountModel.Result();
        List<String> orderPaymentIds = arg.getOrderPaymentIds();
        if (CollectionUtils.isEmpty(orderPaymentIds)) {
            result.setOrderPaymentIdRebateAmountMap(Maps.newHashMap());
            return result;
        }
        List<IObjectData> rebateOutcomeObjectData = rebateOutcomeDetailManager.listInvalidDataByOrderPaymentIds(serviceContext.getUser(), orderPaymentIds);
        Map<String, List<IObjectData>> orderPaymentIdDatasMap = rebateOutcomeObjectData.stream().collect(Collectors.groupingBy(objectData -> objectData.get(RebateOutcomeDetailConstants.Field.OrderPayment.apiName, String.class)));
        Map<String, BigDecimal> orderPaymentIdAmountMap = orderPaymentIdDatasMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> {
            BigDecimal total = BigDecimal.ZERO;
            for (IObjectData objectData : entry.getValue()) {
                total = total.add(ObjectDataUtil.getBigDecimal(objectData, RebateOutcomeDetailConstants.Field.Amount.apiName));
            }
            return total;
        }));
        result.setOrderPaymentIdRebateAmountMap(orderPaymentIdAmountMap);
        return result;
    }

    @Override
    public PaymentRecoverValidateModel.Result recoverValidate(ServiceContext serviceContext, PaymentRecoverValidateModel.Arg arg) {
        PaymentRecoverValidateModel.Result result = new PaymentRecoverValidateModel.Result();
        Map<String, Boolean> paymentIdResultMap = Maps.newHashMap();
        Map<String, List<String>> paymentOrderPaymentIdMap = arg.getPaymentOrderPaymentIdMap();
        if (Objects.isNull(paymentOrderPaymentIdMap)) {
            return result;
        }
        User user = serviceContext.getUser();
        boolean customerAccountEnable = customerAccountConfigManager.isCustomerAccountEnable(user.getTenantId());
        if (!customerAccountEnable) {
            paymentOrderPaymentIdMap.forEach((key, value) -> {
                paymentIdResultMap.put(key, true);
            });
            result.setPaymentIdResultMap(paymentIdResultMap);
            return result;
        }
        List<String> orderPaymentIds = paymentOrderPaymentIdMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        List<IObjectData> prepayDatas = prepayDetailManager.listInvalidDataByOrderPaymentIds(user, orderPaymentIds);
        Set<String> failOrderPaymentIds = Sets.newHashSet();
        if (CollectionUtils.isNotEmpty(prepayDatas)) {
            Map<String, BigDecimal> customerIdPrepayAmountMap = Maps.newHashMap();
            Map<String, Set<String>> customerOrderPaymentIdMap = Maps.newHashMap();
            for (IObjectData objectData : prepayDatas) {
                String tempOrderPaymentId = ObjectDataUtil.getReferenceId(objectData, PrepayDetailConstants.Field.OrderPayment.apiName);
                String customerId = ObjectDataUtil.getReferenceId(objectData, CustomerAccountConstants.Field.Customer.apiName);
                BigDecimal amount = ObjectDataUtil.getBigDecimal(objectData, PrepayDetailConstants.Field.Amount.apiName);
                Set<String> tempOrderPaymentIds = customerOrderPaymentIdMap.getOrDefault(customerId, Sets.newHashSet());
                tempOrderPaymentIds.add(tempOrderPaymentId);
                customerOrderPaymentIdMap.put(customerId, tempOrderPaymentIds);
                if (customerIdPrepayAmountMap.containsKey(customerId)) {
                    BigDecimal tempAmount = customerIdPrepayAmountMap.get(customerId);
                    tempAmount = tempAmount.add(amount);
                    customerIdPrepayAmountMap.put(customerId, tempAmount);
                } else {
                    customerIdPrepayAmountMap.put(customerId, amount);
                }
            }
            List<IObjectData> customerAccountDatas = customerAccountManager.listCustomerAccountIncludeInvalidByCustomerIds(user, Lists.newArrayList(customerIdPrepayAmountMap.keySet()));
            Map<String, IObjectData> customerIdPrepayDataMap = customerAccountDatas.stream().collect(Collectors.toMap(x -> ObjectDataUtil.getReferenceId(x, CustomerAccountConstants.Field.Customer.apiName), y -> y));
            customerIdPrepayAmountMap.forEach((customerId, amount) -> {
                IObjectData customerAccountData = customerIdPrepayDataMap.get(customerId);
                BigDecimal prepayAvailableAmount = ObjectDataUtil.getBigDecimal(customerAccountData, CustomerAccountConstants.Field.PrepayAvailableBalance.apiName);
                if (amount.compareTo(prepayAvailableAmount) > 0) {
                    //预存款可用余额不足，不可恢复
                    failOrderPaymentIds.addAll(customerOrderPaymentIdMap.get(customerId));
                }
            });
        }
        List<IObjectData> rebateOutcomeDatas = rebateOutcomeDetailManager.listByOrderPaymentIds(user, orderPaymentIds);
        if (CollectionUtils.isNotEmpty(rebateOutcomeDatas)) {
            List<String> rebateIncomeIds = Lists.newArrayList();
            //rebateIncomeId 需要恢复的Amount和
            Map<String, BigDecimal> rebateIncomeIdAmountMap = Maps.newHashMap();
            Map<String, Set<String>> rebateIncomeIdOrderPaymentIdMap = Maps.newHashMap();
            for (IObjectData outcomeData : rebateOutcomeDatas) {
                String rebateIncomeId = ObjectDataUtil.getReferenceId(outcomeData, RebateOutcomeDetailConstants.Field.RebateIncomeDetail.apiName);
                rebateIncomeIds.add(rebateIncomeId);
                BigDecimal tempAmount = ObjectDataUtil.getBigDecimal(outcomeData, RebateOutcomeDetailConstants.Field.Amount.apiName);
                if (rebateIncomeIdAmountMap.containsKey(rebateIncomeId)) {
                    tempAmount = tempAmount.add(rebateIncomeIdAmountMap.get(rebateIncomeId));
                }
                rebateIncomeIdAmountMap.put(rebateIncomeId, tempAmount);
                Set<String> tempOrderPaymentIds = rebateIncomeIdOrderPaymentIdMap.getOrDefault(rebateIncomeId, Sets.newHashSet());
                String orderPaymentId = ObjectDataUtil.getReferenceId(outcomeData, RebateOutcomeDetailConstants.Field.OrderPayment.apiName);
                tempOrderPaymentIds.add(orderPaymentId);
                rebateIncomeIdOrderPaymentIdMap.put(rebateIncomeId, tempOrderPaymentIds);
            }
            List<IObjectData> rebateIncomeDatas = serviceFacade.findObjectDataByIdsIncludeDeleted(user, rebateIncomeIds, RebateIncomeDetailConstants.API_NAME);
            Map<String, IObjectData> rebateIncomeIdDataMap = rebateIncomeDatas.stream().collect(Collectors.toMap(IObjectData::getId, x -> x));
            rebateIncomeIdAmountMap.forEach((incomeId, amount) -> {
                IObjectData incomeData = rebateIncomeIdDataMap.get(incomeId);
                BigDecimal availableRebateAmount = ObjectDataUtil.getBigDecimal(incomeData, RebateIncomeDetailConstants.Field.AvailableRebate.apiName);
                Date start = incomeData.get(RebateIncomeDetailConstants.Field.StartTime.apiName, Date.class);
                Date end = incomeData.get(RebateIncomeDetailConstants.Field.EndTime.apiName, Date.class);
                boolean isActive = ObjectDataUtil.isCurrentTimeActive(start, end);
                if (amount.compareTo(availableRebateAmount) > 0 || !isActive) {
                    failOrderPaymentIds.addAll(rebateIncomeIdOrderPaymentIdMap.get(incomeId));
                }
            });
        }
        paymentOrderPaymentIdMap.forEach((key, value) -> {
            boolean exist = value.stream().noneMatch(failOrderPaymentIds::contains);
            paymentIdResultMap.put(key, exist);
        });
        boolean noExistFailPaymentIds = failOrderPaymentIds.isEmpty();
        if (!noExistFailPaymentIds) {
            result.setMessage(I18N.text(CAI18NKey.PAYMENT_RELATED_CA_AMOUNT_NOT_ENOUGH));
        }
        result.setValidateResult(noExistFailPaymentIds);
        result.setPaymentIdResultMap(paymentIdResultMap);
        return result;
    }

    @Override
    public SfaOrderPaymentModel.Result updatePrepayDetail(SfaOrderPaymentModel.UpdateArg arg, ServiceContext serviceContext) {
        User user = serviceContext.getUser();
        String paymentId = arg.getPaymentId();
        if (StringUtils.isBlank(paymentId) || MapUtils.isEmpty(arg.getPrepayDetailData())) {
            throw new ValidateException("invalid parameter");
        }

        IObjectData prepayObjectData = arg.getPrepayDetailData().toObjectData();
        String orderPaymentId = prepayObjectData.get(PrepayDetailConstants.Field.OrderPayment.apiName, String.class);
        BigDecimal newAmount = prepayObjectData.get(PrepayDetailConstants.Field.Amount.apiName, BigDecimal.class);
        String newLifeStatus = prepayObjectData.get(SystemConstants.Field.LifeStatus.apiName, String.class);
        if (StringUtils.isBlank(orderPaymentId) || StringUtils.isBlank(newLifeStatus) || newAmount == null
            || newAmount.compareTo(BigDecimal.ZERO) == 0 || LifeStatus.Invalid.value.equals(newLifeStatus)) {
            throw new ValidateException("invalid prepayDetailData");
        }

        IObjectData payment = serviceFacade.findObjectData(user, paymentId, SystemConstants.PaymentApiName);
        String paymentTerm = payment.get("payment_term", String.class);
        Set<String> validPaymentTerms = Sets.newHashSet(PaymentTermEnum.Prepay.getType(), PaymentTermEnum.PrepayAndRebate.getType());
        if (!validPaymentTerms.contains(paymentTerm)) {
            throw new ValidateException("invalid paymentTerm");
        }

        IObjectData prepayOldObjectData = prepayDetailManager.getByOrderPaymentId(user, orderPaymentId);
        String oldLifeStatus = prepayOldObjectData.get(SystemConstants.Field.LifeStatus.apiName, String.class);
        if (!oldLifeStatus.equals(newLifeStatus)) {
            throw new ValidateException("invalid life status");
        }

        String customerId = payment.get("account_id", String.class);
        IObjectData customerAccountObjectData = customerAccountManager.getCustomerAccountByCustomerId(user, customerId);
        BigDecimal prepayAvailableBalance = customerAccountObjectData.get(CustomerAccountConstants.Field.PrepayAvailableBalance.apiName, BigDecimal.class);
        BigDecimal oldAmount = prepayOldObjectData.get(PrepayDetailConstants.Field.Amount.apiName, BigDecimal.class);
        BigDecimal deltaAmount = newAmount.subtract(oldAmount);
        if (deltaAmount.compareTo(BigDecimal.ZERO) == 0) { //金额未变化，不需要修改
            return new SfaOrderPaymentModel.Result(true);
        } else if (deltaAmount.compareTo(BigDecimal.ZERO) > 0) {
            Set<String> toBeHandledLifeStatus = Sets.newHashSet(LifeStatus.InChange.value, LifeStatus.Normal.value, LifeStatus.UnderReview.value);
            if (toBeHandledLifeStatus.contains(newLifeStatus) && prepayAvailableBalance.compareTo(deltaAmount) < 0) {
                throw new ValidateException("not enough prepayAvailableBalance");
            }
        }

        // 更新预存款金额数据
        prepayOldObjectData.set(PrepayDetailConstants.Field.Amount.apiName, newAmount);
        prepayDetailManager.update(user, prepayOldObjectData);

        // 更新客户账户数据: normal, in_change, under_review
        String prepayId = prepayOldObjectData.getId();
        String info = CustomerAccountRecordLogger.generatePrepayInfo(prepayId, oldLifeStatus, newLifeStatus);
        if (LifeStatus.UnderReview.value.equals(newLifeStatus)) {
            customerAccountManager.updatePrepayBalance(user, customerId, null, deltaAmount, info, prepayId);
        } else if (LifeStatus.Normal.value.equals(newLifeStatus)) {
            customerAccountManager.updatePrepayBalance(user, customerId, deltaAmount.negate(), null, info, prepayId);
        } else if (LifeStatus.InChange.value.equals(newLifeStatus)) {
            customerAccountManager.updatePrepayBalance(user, customerId, deltaAmount.negate(), null, info, prepayId);
        }

        return new SfaOrderPaymentModel.Result(true);
    }

    @Override
    public SfaOrderPaymentModel.Result updateByCustomFunction(SfaOrderPaymentModel.UpdateArg arg, ServiceContext serviceContext) {
        User user = serviceContext.getUser();
        String paymentId = arg.getPaymentId();
        String newLifeStatus = arg.getLifeStatus();
        String tenantId = user.getTenantId();
        Set<String> toBeHandledLifeStatus = Sets.newHashSet(LifeStatus.InChange.value, LifeStatus.Normal.value, LifeStatus.UnderReview.value);

        // 参数校验
        if (!HwConfig.isHw(tenantId)) {
            throw new ValidateException("not support");
        }
        if (StringUtils.isBlank(paymentId) || StringUtils.isBlank(newLifeStatus) || LifeStatus.Invalid.value.equals(newLifeStatus)) {
            throw new ValidateException("invalid parameter");
        }
        if(MapUtils.isEmpty(arg.getPrepayDetailData()) && MapUtils.isEmpty(arg.getRebateOutcomeDetailData())) {
            throw new ValidateException("invalid parameter");
        }

        IObjectData payment = serviceFacade.findObjectData(user, paymentId, SystemConstants.PaymentApiName);
        String paymentTerm = payment.get("payment_term", String.class);
        Long transactionTime = payment.get("payment_time", Long.class);
        List<String> ownerList = payment.getOwner();

        Set<String> validPaymentTerms = Sets.newHashSet(PaymentTermEnum.Prepay.getType(), PaymentTermEnum.Rebate.getType(), PaymentTermEnum.PrepayAndRebate.getType());
        if (!validPaymentTerms.contains(paymentTerm)) {
            throw new ValidateException("invalid paymentTerm");
        }
        String customerId = payment.get("account_id", String.class);
        IObjectData customerAccountObjectData = customerAccountManager.getCustomerAccountByCustomerId(user, customerId);
        BigDecimal prepayAvailableBalance = customerAccountObjectData.get(CustomerAccountConstants.Field.PrepayAvailableBalance.apiName, BigDecimal.class);
        BigDecimal rebateAvailableBalance = customerAccountObjectData.get(CustomerAccountConstants.Field.RebateAvailableBalance.apiName, BigDecimal.class);

        BigDecimal newPrepayAmount = null;
        BigDecimal prepayDeltaAmount = BigDecimal.ZERO;
        String oldPrepayLifeStatus = null;
        IObjectData prepayOldObjectData = null;

        BigDecimal newRebateOutcomeAmount = null;
        BigDecimal rebateOutcomeDeltaAmount = BigDecimal.ZERO;
        String rebateOutcomeOrderPaymentId = null;
        List<IObjectData> rebateOutcomeDetailOldObjectDatas = null;

        if (MapUtils.isNotEmpty(arg.getPrepayDetailData())) {
            IObjectData prepayObjectData = arg.getPrepayDetailData().toObjectData();
            String orderPaymentId = prepayObjectData.get(PrepayDetailConstants.Field.OrderPayment.apiName, String.class);
            newPrepayAmount = prepayObjectData.get(PrepayDetailConstants.Field.Amount.apiName, BigDecimal.class);
            if (StringUtils.isBlank(orderPaymentId) || newPrepayAmount == null || newPrepayAmount.compareTo(BigDecimal.ZERO) == 0) {
                throw new ValidateException("invalid prepayDetailData");
            }

            prepayOldObjectData = prepayDetailManager.getByOrderPaymentId(user, orderPaymentId);
            oldPrepayLifeStatus = prepayOldObjectData.get(SystemConstants.Field.LifeStatus.apiName, String.class);
            if (!oldPrepayLifeStatus.equals(newLifeStatus)) {
                throw new ValidateException("invalid life status");
            }

            BigDecimal oldAmount = prepayOldObjectData.get(PrepayDetailConstants.Field.Amount.apiName, BigDecimal.class);
            prepayDeltaAmount = newPrepayAmount.subtract(oldAmount);
            if (prepayDeltaAmount.compareTo(BigDecimal.ZERO) > 0) {
                if (toBeHandledLifeStatus.contains(newLifeStatus) && prepayAvailableBalance.compareTo(prepayDeltaAmount) < 0) {
                    throw new ValidateException("not enough prepayAvailableBalance");
                }
            }
        }

        if (MapUtils.isNotEmpty(arg.getRebateOutcomeDetailData())) {
            IObjectData rebateOutcomeDetailObjectData = arg.getRebateOutcomeDetailData().toObjectData();
            rebateOutcomeOrderPaymentId = rebateOutcomeDetailObjectData.get(RebateOutcomeDetailConstants.Field.OrderPayment.apiName, String.class);
            newRebateOutcomeAmount = rebateOutcomeDetailObjectData.get(RebateOutcomeDetailConstants.Field.Amount.apiName, BigDecimal.class);

            if (StringUtils.isBlank(rebateOutcomeOrderPaymentId) || newRebateOutcomeAmount == null || newRebateOutcomeAmount.compareTo(BigDecimal.ZERO) == 0) {
                throw new ValidateException("invalid rebateOutcomeDetailData");
            }

            BigDecimal oldAmount = BigDecimal.ZERO;
            rebateOutcomeDetailOldObjectDatas = rebateOutcomeDetailManager.getByOrderPaymentId(user, rebateOutcomeOrderPaymentId);
            if (CollectionUtils.isNotEmpty(rebateOutcomeDetailOldObjectDatas)) {
                String oldRebateOutcomeLifeStatus = rebateOutcomeDetailOldObjectDatas.get(0).get(SystemConstants.Field.LifeStatus.apiName, String.class);
                if (!oldRebateOutcomeLifeStatus.equals(newLifeStatus)) {
                    throw new ValidateException("invalid life status");
                }

                for (IObjectData rebateOutcomeDetail : rebateOutcomeDetailOldObjectDatas) {
                    BigDecimal amount = rebateOutcomeDetail.get(RebateOutcomeDetailConstants.Field.Amount.apiName, BigDecimal.class);
                    oldAmount = oldAmount.add(amount);
                }
            }

            rebateOutcomeDeltaAmount = newRebateOutcomeAmount.subtract(oldAmount);
            if (rebateOutcomeDeltaAmount.compareTo(BigDecimal.ZERO) > 0) {
                if (toBeHandledLifeStatus.contains(newLifeStatus) && rebateAvailableBalance.compareTo(rebateOutcomeDeltaAmount) < 0) {
                    throw new ValidateException("not enough rebateAvailableBalance");
                }
            }
        }

        if (rebateOutcomeDeltaAmount.compareTo(BigDecimal.ZERO) != 0) {
            // 作废旧返利支出数据
            Map<String, String> idOldLifeStatusMap = rebateOutcomeDetailOldObjectDatas.stream().collect(Collectors.toMap(objData -> objData.getId(), data -> data.get(SystemConstants.Field.LifeStatus.apiName, String.class)));
            this.serviceFacade.bulkInvalid(rebateOutcomeDetailOldObjectDatas, user);
            for (IObjectData data : rebateOutcomeDetailOldObjectDatas) {
                BillTypeEnum billTypeEnum = BillTypeEnum.RebateOutcome;
                String outcomeId = data.getId();
                String oldLifeStatus = idOldLifeStatusMap.get(outcomeId);
                String incomeId = data.get(RebateOutcomeDetailConstants.Field.RebateIncomeDetail.apiName, String.class);
                BigDecimal outcomeAmount = data.get(RebateOutcomeDetailConstants.Field.Amount.apiName, BigDecimal.class);
                rebateOutcomeDetailManager.recordLog(user, data);

                IObjectData incomeData = this.serviceFacade.findObjectData(user, incomeId, RebateIncomeDetailConstants.API_NAME);
                Date start = incomeData.get(RebateIncomeDetailConstants.Field.StartTime.apiName, Date.class);
                Date end = incomeData.get(RebateIncomeDetailConstants.Field.EndTime.apiName, Date.class);
                String incomeLifeStatus = incomeData.get(SystemConstants.Field.LifeStatus.apiName, String.class);
                boolean isRebateIncomeExpired = !ObjectDataUtil.isCurrentTimeActive(start, end);
                if (isRebateIncomeExpired && !ConfigCenter.doRebateExpired(user.getTenantId())) {
                    throw new ValidateException(I18N.text(CAI18NKey.INEFFECTIVE_REBATE_NO_USE));
                }
                if (!SystemConstants.LifeStatus.Normal.value.equals(incomeLifeStatus)) {
                    throw new ValidateException(I18N.text(CAI18NKey.REBATE_UN_NORMAL_STATUS));
                }

                // 自定义函数特殊逻辑
                String info = CustomerAccountRecordLogger.generateRebateInfo("O-SPE-" + outcomeId, oldLifeStatus, SystemConstants.LifeStatus.Invalid.value);
                if (LifeStatus.UnderReview.value.equals(newLifeStatus)) {
                    //返利收入可用增加、已用減少；客户账户总额不变，可用增加，锁定减少
                    customerAccountManager.addIncomeBalance(user, incomeData, outcomeAmount, outcomeAmount.negate());
                    customerAccountManager.updateRebateBalance(user, customerId, null, outcomeAmount.negate(), info, outcomeId, billTypeEnum);
                } else if (LifeStatus.Normal.value.equals(newLifeStatus)) {
                    //返利收入可用增加、已用減少；客户账户总额增加，可用增加，锁定不变
                    customerAccountManager.addIncomeBalance(user, incomeData, outcomeAmount, outcomeAmount.negate());
                    customerAccountManager.updateRebateBalance(user, customerId, outcomeAmount, null, info, outcomeId, billTypeEnum);
                } else if (LifeStatus.InChange.value.equals(newLifeStatus)) {
                    //返利收入可用增加，已用減少；客户账户总额增加，可用增加，锁定不变
                    customerAccountManager.addIncomeBalance(user, incomeData, outcomeAmount, outcomeAmount.negate());
                    customerAccountManager.updateRebateBalance(user, customerId, outcomeAmount, null, info, outcomeId, billTypeEnum);
                }
            }

            // 重新生成返利支出记录，并更新客户账户相关数据
            List<RebateIncomeModle.PayForOutcomeModel> incomeObjectDataListToPay = rebateIncomeDetailManager.obtainRebateIncomeToPayList(user, newRebateOutcomeAmount, customerId);
            String rebateUseRuleId = null;
            Optional<IObjectData> rebateUseRuleObjectDataOption = rebateUseRuleManager.getRebateUseRuleByCustomerId(user, customerId);
            if (rebateUseRuleObjectDataOption.isPresent()) {
                rebateUseRuleId = rebateUseRuleObjectDataOption.get().getId();
            }
            // 扣减返利收入和创建支出
            for (RebateIncomeModle.PayForOutcomeModel payForOutcome : incomeObjectDataListToPay) {
                IObjectData rebateOutcome = new ObjectData();
                rebateOutcome.setDescribeApiName(RebateOutcomeDetailConstants.API_NAME);
                rebateOutcome.set(RebateOutcomeDetailConstants.Field.TransactionTime.apiName, transactionTime);
                rebateOutcome.set(RebateOutcomeDetailConstants.Field.RebateIncomeDetail.apiName, payForOutcome.getRebateIncomeObj().getId());
                rebateOutcome.set(RebateOutcomeDetailConstants.Field.Amount.apiName, payForOutcome.getPayAmount());
                rebateOutcome.set(SystemConstants.Field.LifeStatus.apiName, newLifeStatus);
                rebateOutcome.set(RebateOutcomeDetailConstants.Field.RebateUseRule.apiName, rebateUseRuleId);
                rebateOutcome.set(RebateOutcomeDetailConstants.Field.OrderPayment.apiName, rebateOutcomeOrderPaymentId);
                rebateOutcome.setOwner(ownerList);

                if (LifeStatus.InChange.value.equals(newLifeStatus)) {
                    rebateOutcomeDetailManager.createRebateOutcomeAndUpdateBalanceFromCustomFunction(user, rebateOutcome, LifeStatus.Normal.value);
                } else {
                    rebateOutcomeDetailManager.createRebateOutcomeAndUpdateBalanceFromCustomFunction(user, rebateOutcome, newLifeStatus);
                }
            }
        }

        if (prepayDeltaAmount.compareTo(BigDecimal.ZERO) != 0) {
            prepayOldObjectData.set(PrepayDetailConstants.Field.Amount.apiName, newPrepayAmount);
            prepayDetailManager.update(user, prepayOldObjectData);

            // 更新客户账户数据: normal, in_change, under_review
            String prepayId = prepayOldObjectData.getId();
            String info = CustomerAccountRecordLogger.generatePrepayInfo(prepayId, oldPrepayLifeStatus, newLifeStatus);
            if (LifeStatus.UnderReview.value.equals(newLifeStatus)) {
                customerAccountManager.updatePrepayBalance(user, customerId, null, prepayDeltaAmount, info, prepayId);
            } else if (LifeStatus.Normal.value.equals(newLifeStatus)) {
                customerAccountManager.updatePrepayBalance(user, customerId, prepayDeltaAmount.negate(), null, info, prepayId);
            } else if (LifeStatus.InChange.value.equals(newLifeStatus)) {
                customerAccountManager.updatePrepayBalance(user, customerId, prepayDeltaAmount.negate(), null, info, prepayId);
            }
        }

        return new SfaOrderPaymentModel.Result(true);
    }

}
