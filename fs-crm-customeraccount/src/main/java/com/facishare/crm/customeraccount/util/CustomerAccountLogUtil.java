package com.facishare.crm.customeraccount.util;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.customeraccount.constants.*;
import com.facishare.crm.customeraccount.enums.StageEnum;
import com.facishare.crm.customeraccount.model.DataUpdateAndAddModel;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.fxiaoke.log.AuditLog;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.dto.AuditLogDTO;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.github.autoconf.helper.ConfigHelper;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class CustomerAccountLogUtil {
    private static final String CUSTOMER_ACCOUNT_TOPIC = "biz-audit-log";//"customer-account-log";
    private static final String DHT_TOPIC = "biz-audit-log-dht";  //biz-audit-log太大，用一个新的
    private static final String APP_NAME = ConfigHelper.getProcessInfo().getName();
    private static final String SERVER_IP = ConfigHelper.getProcessInfo().getIp();
    private static final String PROFILE = ConfigHelper.getProcessInfo().getProfile();

    public static final String DATA_ID_KEY = "account_rule_source_data_id";
    public static final String OBJECT_KEY = "account_rule_source_object";

    public static void sendAuditLog(User user, String action, String objectApiName, String objectDataIds, String message, String error) {
        String userId = user.isOutUser() ? user.getUserId() : user.getOutUserId();
        AuditLogDTO dto = AuditLogDTO.builder().appName(APP_NAME).serverIp(SERVER_IP).profile(PROFILE).module("customer-account").traceId(TraceContext.get().getTraceId()).createTime(System.currentTimeMillis())
                .tenantId(user.getTenantId()).userId(userId).objectApiNames(objectApiName).objectIds(objectDataIds).action(action).message(message).error(error).extra(objectDataIds).build();
        BizLogClient.send(CUSTOMER_ACCOUNT_TOPIC, Pojo2Protobuf.toMessage(dto, AuditLog.class).toByteArray());
    }

    public static void sendCheckRuleLockAuditLog(User user, String objectApiName, String objectDataId, String message) {
        String userId = user.isOutUser() ? user.getUserId() : user.getOutUserId();
        AuditLogDTO dto = AuditLogDTO.builder().appName(APP_NAME).serverIp(SERVER_IP).profile(PROFILE).module("customer-account").traceId(TraceContext.get().getTraceId()).createTime(System.currentTimeMillis())
                .tenantId(user.getTenantId()).userId(userId).objectApiNames(objectApiName).objectIds(objectDataId).action("check_rule.lock").message(message).extra(objectDataId).build();
        BizLogClient.send(CUSTOMER_ACCOUNT_TOPIC, Pojo2Protobuf.toMessage(dto, AuditLog.class).toByteArray());
    }

    /**
     * 自动入账失败log上报
     */
    public static void sendAutoEnterAccountFailAuditLog(User user, String objectApiName, String objectDataId, String message) {
        //部分错误信息不上报
        List<String> autoEnterAccountErrorNoSendAuditLogMsgs = ConfigCenter.getAutoEnterAccountErrorNoSendAuditLogMsgs();
        if (!CollectionUtils.isEmpty(autoEnterAccountErrorNoSendAuditLogMsgs)) {
            for (String msg : autoEnterAccountErrorNoSendAuditLogMsgs) {
                if (message.contains(msg)) {
                    return;
                }
            }
        }

        String userId = user.isOutUser() ? user.getUserId() : user.getOutUserId();
        AuditLogDTO dto = AuditLogDTO.builder().appName(APP_NAME).serverIp(SERVER_IP).profile(PROFILE).module("auto-enter-account").traceId(TraceContext.get().getTraceId()).createTime(System.currentTimeMillis())
                .tenantId(user.getTenantId()).userId(userId).objectApiNames(objectApiName).objectIds(objectDataId).action("auto_enter_account.fail").message(message).extra(objectDataId).build();
        log.info("sendAutoEnterAccountFailAuditLog user[{}], objectApiName[{}], objectDataId[{}], dto[{}]", user, objectApiName, objectDataId, dto);

        BizLogClient.send(DHT_TOPIC, Pojo2Protobuf.toMessage(dto, AuditLog.class).toByteArray());
    }

    protected static void sendAuditLog(User user, CheckRuleLogModel checkRuleLogModel) {
        String tenantId = user.getTenantId();
        String userId = user.isOutUser() ? user.getUserId() : user.getOutUserId();
        String objectApiName = checkRuleLogModel.getObjectApiName();
        String objectDataId = checkRuleLogModel.getObjectDataId();
        String action = checkRuleLogModel.getAction();
        AuditLogDTO dto = AuditLogDTO.builder().appName(APP_NAME).serverIp(SERVER_IP).profile(PROFILE).module("customer-account").traceId(TraceContext.get().getTraceId()).createTime(System.currentTimeMillis())
                .tenantId(tenantId).userId(userId).objectApiNames(objectApiName).objectIds(objectDataId).action(String.format("check_rule.%s", action)).extra(getCheckRuleSourceDataId(checkRuleLogModel))
                .error(checkRuleLogModel.getErrorMessage()).build();
        Map<String, Object> contentMap = Maps.newHashMap();
        contentMap.put("stage", checkRuleLogModel.getStage());
        contentMap.put("buttonApiName", checkRuleLogModel.getButtonApiName());
        if (Objects.nonNull(checkRuleLogModel.toMap())) {
            contentMap.putAll(checkRuleLogModel.toMap());
        }
        dto.setMessage(JSON.toJSONString(contentMap));
        //default biz-audit-log
        BizLogClient.send(CUSTOMER_ACCOUNT_TOPIC, Pojo2Protobuf.toMessage(dto, AuditLog.class).toByteArray());
    }

    public static void reportContextError(User user, String objectApiName, String objectDataId, String errorMessage) {
        log.warn("{} getContext fail,user:{},objectApiName:{},objectDataId:{}", errorMessage, user, objectApiName, objectDataId);
        CheckRuleLogModel checkRuleLogModel = new CheckRuleLogModel();
        checkRuleLogModel.setObjectApiName(objectApiName);
        checkRuleLogModel.setObjectDataId(objectDataId);
        checkRuleLogModel.setAction("check_rule_context");
        checkRuleLogModel.setErrorMessage(errorMessage + " context empty");
        sendAuditLog(user, checkRuleLogModel);
    }

    public static void report(User user, DataUpdateAndAddModel.Arg arg) {
        if (Objects.isNull(arg)) {
            return;
        }
        try {
            Map<String, Object> messageMap = Maps.newHashMap();
            messageMap.put("customerAccountColumnUpdateMap", arg.getCustomerAccountColumnUpdateMap());
            messageMap.put("customerAccountDataList", toMapByField(Lists.newArrayList(MapUtils.emptyIfNull(arg.getCustomerAccountDataMap()).values()), logFieldApiNames(NewCustomerAccountConstants.API_NAME)));

            if (Objects.nonNull(arg.getUpdateDataModelList())) {
                List<Map<String, Object>> updateDataList = arg.getUpdateDataModelList().stream().map(x -> {
                    List<String> updateFieldList = x.getUpdateFieldList();
                    List<String> logFields = Lists.newArrayList("_id", "name", "is_deleted", "life_status", "object_describe_api_name");
                    logFields.addAll(updateFieldList);
                    Map<String, Object> dataMap = Maps.newHashMap();
                    dataMap.put("updateFieldList", updateFieldList);
                    dataMap.put("dataBeforeUpdateList", toMapByField(x.getDataListBeforeUpdate(), logFields));
                    dataMap.put("dataAfterUpdateList", toMapByField(x.getDataListAfterUpdate(), logFields));
                    dataMap.put("needInvalidAfterUpdate", x.isNeedInvalidAfterUpdate());
                    dataMap.put("needTriggerInvalidFunction", x.isNeedTriggerInvalidFunction());
                    return dataMap;
                }).collect(Collectors.toList());
                messageMap.put("updateDataList", updateDataList);
            }

            if (Objects.nonNull(arg.getAddDataModelList())) {
                List<Map<String, Object>> dataMapList = arg.getAddDataModelList().stream().map(x -> {
                    Map<String, Object> dataMap = Maps.newHashMap();
                    dataMap.put("skipTriggerFunction", x.isSkipTriggerFunction());
                    dataMap.put("addData", toMapByField(x.getAddObjectData(), logFieldApiNames(x.getObjectApiName())));
                    return dataMap;
                }).collect(Collectors.toList());
                messageMap.put("addDataList", dataMapList);
            }

            messageMap.put("invalidDataList", MapUtils.emptyIfNull(arg.getInvalidDataListMap()).values().stream().map(dataInvalidModels -> dataInvalidModels.stream().map(x -> {
                        String apiName = x.getObjectData().getDescribeApiName();
                        return toMapByField(x.getObjectData(), logFieldApiNames(apiName));
                    }).collect(Collectors.toList())
            ).flatMap(Collection::stream).collect(Collectors.toList()));

            if (Objects.nonNull(arg.getIncrementUpdateFieldModelList())) {
                List<Map<String, Object>> incrementUpdataList = arg.getIncrementUpdateFieldModelList().stream().map(x -> {
                    List<String> updateFieldList = x.getUpdateFieldList();
                    Map<String, Object> incrementUpdateMap = Maps.newHashMap();
                    incrementUpdateMap.put("incrementUpdateColumnMap", x.getIncrementUpdateFieldColumnMap());
                    incrementUpdateMap.put("updateFieldList", updateFieldList);
                    List<String> logFields = Lists.newArrayList("_id", "name", "is_deleted", "life_status", "object_describe_api_name");
                    logFields.addAll(updateFieldList);
                    incrementUpdateMap.put("updateDataList", x.getUpdateDataList().stream().map(v -> toMapByField(v, logFields)).collect(Collectors.toList()));
                    return incrementUpdateMap;
                }).collect(Collectors.toList());
                messageMap.put("incrementUpdateDataList", incrementUpdataList);
            }
            messageMap.put("ignoreFunctionException", arg.getIgnoreFunctionException());
            messageMap.put("skipValidateCustomerAccount", arg.getSkipValidateCustomerAccount());
            messageMap.put("creditCustomerAccountUpdate", arg.isCreditCustomerAccountUpdate());
            String objectApiName = null;
            String objectDataIds = null;
            RequestContext requestContext = RequestContextManager.getContext();
            if (Objects.nonNull(requestContext)) {
                objectApiName = requestContext.getAttribute(OBJECT_KEY);
                objectDataIds = requestContext.getAttribute(DATA_ID_KEY);
            }
            sendAuditLog(user, "execute", objectApiName, objectDataIds, JSON.toJSONString(messageMap), null);
        } catch (Exception e) {
            log.warn("report error,user:{},arg:{}", user, arg, e);
        }
    }

    private static String getCheckRuleSourceDataId(CheckRuleLogModel checkRuleLogModel) {
        String extra = checkRuleLogModel.getExtra();
        RequestContext requestContext = RequestContextManager.getContext();
        if (Objects.isNull(requestContext)) {
            return extra;
        }
        String sDataId = requestContext.getAttribute(DATA_ID_KEY);
        if (StringUtils.isNotEmpty(sDataId)) {
            return sDataId;
        }
        if (StringUtils.isEmpty(extra)) {
            return null;
        }
        requestContext.setAttribute(OBJECT_KEY, extra);
        return extra;
    }

    private static List<String> logFieldApiNames(String objectApiName) {
        List<String> fieldApiNames = Lists.newArrayList();
        if (AccountRuleUseRecordConstants.API_NAME.equals(objectApiName)) {
            fieldApiNames = Lists.newArrayList("_id", AccountRuleUseRecordConstants.Field.CheckRuleId.apiName, AccountRuleUseRecordConstants.Field.Name.apiName, AccountRuleUseRecordConstants.Field.RuleType.apiName,
                    AccountRuleUseRecordConstants.Field.RuleStage.apiName, AccountRuleUseRecordConstants.Field.CheckRecordObjectApiName.apiName, AccountRuleUseRecordConstants.Field.CheckRecordObjectDataId.apiName);
        } else if (AccountTransactionFlowConst.API_NAME.equals(objectApiName)) {
            fieldApiNames = Arrays.stream(AccountTransactionFlowConst.Field.values()).map(x -> x.apiName).collect(Collectors.toList());
        } else if (AccountFrozenRecordConstant.API_NAME.equals(objectApiName)) {
            fieldApiNames = Arrays.stream(AccountFrozenRecordConstant.Field.values()).map(x -> x.apiName).collect(Collectors.toList());
            fieldApiNames.add("record_type");
        } else if (UnfreezeDetailConstant.API_NAME.equals(objectApiName)) {
            fieldApiNames = Arrays.stream(UnfreezeDetailConstant.Field.values()).map(x -> x.apiName).collect(Collectors.toList());
        } else if (NewCustomerAccountConstants.API_NAME.equals(objectApiName)) {
            fieldApiNames = Arrays.stream(NewCustomerAccountConstants.Field.values()).map(x -> x.apiName).collect(Collectors.toList());
        } else if (AccountCheckRuleConstants.API_NAME.equals(objectApiName)) {
            fieldApiNames = Arrays.stream(AccountCheckRuleConstants.Field.values()).map(x -> x.apiName).collect(Collectors.toList());
        }
        fieldApiNames.add("_id");
        fieldApiNames.add("name");
        fieldApiNames.add(SystemConstants.Field.LastModifiedBy.apiName);
        return fieldApiNames;
    }

    private static List<Map<String, Object>> toMapByField(List<IObjectData> dataList, List<String> fieldApiNames) {
        if (CollectionUtils.isEmpty(dataList) || CollectionUtils.isEmpty(fieldApiNames)) {
            return Lists.newArrayList();
        }
        dataList.removeIf(Objects::isNull);
        return dataList.stream().map(x -> {
            Map<String, Object> data = Maps.newHashMap();
            fieldApiNames.forEach(f -> data.put(f, x.get(f)));
            return data;
        }).collect(Collectors.toList());
    }

    private static Map<String, Object> toMapByField(IObjectData objectData, List<String> fieldApiNames) {
        Map<String, Object> data = Maps.newHashMap();
        fieldApiNames.forEach(f -> data.put(f, objectData.get(f)));
        return data;
    }

    @Data
    public static class CheckRuleLogModel {
        private String buttonApiName;
        private String stage;
        private String objectApiName;
        private String objectDataId;
        private String action;
        private String extra;
        private String errorMessage;

        public void log(User user) {
            try {
                sendAuditLog(user, this);
            } catch (Exception e) {
                log.warn("customerAccountAuditLog error:{}", this, e);
            }
        }

        public Map<String, Object> toMap() {
            return Maps.newHashMap();
        }

        public void logIfPost(User user) {
            if (StageEnum.POST.stage.equals(stage)) {
                log(user);
            }
        }
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class CustomerAccountNotEnough extends CheckRuleLogModel {
        private String fieldName;
        private BigDecimal dbAmount;
        private BigDecimal needAmount;

        @Builder
        public CustomerAccountNotEnough(String action, String fieldName, BigDecimal dbAmount, BigDecimal needAmount) {
            setAction(action);
            this.fieldName = fieldName;
            this.dbAmount = dbAmount;
            this.needAmount = needAmount;
        }

        @Override
        public Map<String, Object> toMap() {
            Map<String, Object> contentMap = Maps.newHashMap();
            contentMap.put("fieldName", fieldName);
            contentMap.put("dbAmount", dbAmount);
            contentMap.put("needAmount", needAmount);
            return contentMap;
        }
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class CheckRuleRequestLog extends CheckRuleLogModel {
        private Object arg;

        @Builder
        public CheckRuleRequestLog(String objectApiName, String objectDataId, String buttonApiName, String stage, String action, Object arg, String extra) {
            this.arg = arg;
            setStage(stage);
            setObjectApiName(objectApiName);
            setObjectDataId(objectDataId);
            setButtonApiName(buttonApiName);
            setAction(action);
            setExtra(extra);
        }

        @Override
        public Map<String, Object> toMap() {
            Map<String, Object> contentMap = Maps.newHashMap();
            contentMap.put("arg", this.arg);
            return contentMap;
        }
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class QueryDataLog extends CheckRuleLogModel {
        private List<IFilter> filters;
        private List<IObjectData> dataListResult;
        private String queryObjectApiName;

        @Builder
        public QueryDataLog(String action, String objectApiName, String objectDataId, String buttonApiName, List<IFilter> filters, List<IObjectData> dataList, String queryObjectApiName) {
            this.filters = Objects.isNull(filters) ? Lists.newArrayList() : filters;
            this.dataListResult = Objects.isNull(dataList) ? Lists.newArrayList() : dataList;
            this.queryObjectApiName = queryObjectApiName;
            setObjectApiName(objectApiName);
            setButtonApiName(buttonApiName);
            setObjectDataId(objectDataId);
            setAction(action);
        }

        @Override
        public Map<String, Object> toMap() {
            Map<String, Object> contentMap = Maps.newHashMap();
            contentMap.put("queryObjectApiName", this.queryObjectApiName);
            List<Map<String, Object>> filterMaps = Lists.newArrayList();
            com.facishare.paas.appframework.common.util.CollectionUtils.nullToEmpty(filters).forEach(x -> {
                Map<String, Object> filterMap = Maps.newHashMap();
                filterMap.put("field_name", x.getFieldName());
                filterMap.put("field_values", x.getFieldValues());
                filterMap.put("operator", x.getOperator());
                if (Objects.nonNull(x.getValueType())) {
                    filterMap.put("value_type", x.getValueType());
                }
                if (Objects.nonNull(x.getIsMasterField())) {
                    filterMap.put("is_master_field", x.getIsMasterField());
                }
                filterMaps.add(filterMap);
            });
            contentMap.put("filters", filterMaps);
            contentMap.put("dataListResult", toMapByField(this.dataListResult, logFieldApiNames(this.queryObjectApiName)));
            return contentMap;
        }
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class EditHandlerLog extends CheckRuleLogModel {
        private String customerId;
        private IObjectData accountRuleUseRecordData;

        @Builder
        public EditHandlerLog(String buttonApiName, String action, String stage, String objectApiName, String objectDataId, String customerId, IObjectData accountRuleUseRecordData) {
            setButtonApiName(buttonApiName);
            setStage(stage);
            setAction(action);
            setObjectApiName(objectApiName);
            setObjectDataId(objectDataId);
            this.customerId = customerId;
            this.accountRuleUseRecordData = accountRuleUseRecordData;
        }

        @Override
        public Map<String, Object> toMap() {
            Map<String, Object> contentMap = Maps.newHashMap();
            contentMap.put("accountRuleUseRecordData", toMapByField(Lists.newArrayList(this.accountRuleUseRecordData), logFieldApiNames(AccountRuleUseRecordConstants.API_NAME)));
            contentMap.put("customerId", this.customerId);
            return contentMap;
        }
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class DeletedRuleUseRecordLog extends CheckRuleLogModel {
        private List<IObjectData> accountRuleUseRecordList;

        @Builder
        public DeletedRuleUseRecordLog(String buttonApiName, String action, String stage, String objectApiName, String objectDataId, List<IObjectData> accountRuleUseRecordList) {
            setButtonApiName(buttonApiName);
            setStage(stage);
            setObjectApiName(objectApiName);
            setObjectDataId(objectDataId);
            setAction(action);
            this.accountRuleUseRecordList = accountRuleUseRecordList;
        }

        @Override
        public Map<String, Object> toMap() {
            Map<String, Object> contentMap = Maps.newHashMap();
            contentMap.put("accountRuleUseRecordList", toMapByField(this.accountRuleUseRecordList, logFieldApiNames(AccountRuleUseRecordConstants.API_NAME)));
            return contentMap;
        }
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class EditHandlerDataChangeLog extends CheckRuleLogModel {
        private List<IObjectData> toInvalidFlowList;
        private List<IObjectData> toInvalidUnfreezeList;
        private List<IObjectData> toInvalidFrozenList;
        private List<IObjectData> toSavaFlowList;
        private List<IObjectData> toSavaUnfreezeList;
        private List<IObjectData> toSaveFrozenList;
        private List<IObjectData> customerAccountList;
        private Map<String, Map<String, Object>> customerAccountColumnMap;

        @Builder
        public EditHandlerDataChangeLog(String action, String buttonApiName, String stage, String objectApiName, String objectDataId, List<IObjectData> toInvalidFlowList, List<IObjectData> toInvalidUnfreezeList, List<IObjectData> toInvalidFrozenList, List<IObjectData> toSavaFlowList, List<IObjectData> toSavaUnfreezeList, List<IObjectData> toSaveFrozenList, List<IObjectData> customerAccountList, Map<String, Map<String, Object>> customerAccountColumnMap) {
            setAction(action);
            setButtonApiName(buttonApiName);
            setStage(stage);
            setObjectApiName(objectApiName);
            setObjectDataId(objectDataId);
            this.toInvalidFlowList = toInvalidFlowList;
            this.toInvalidUnfreezeList = toInvalidUnfreezeList;
            this.toInvalidFrozenList = toInvalidFrozenList;
            this.toSavaFlowList = toSavaFlowList;
            this.toSavaUnfreezeList = toSavaUnfreezeList;
            this.toSaveFrozenList = toSaveFrozenList;
            this.customerAccountList = customerAccountList;
            this.customerAccountColumnMap = customerAccountColumnMap;
        }

        @Override
        public Map<String, Object> toMap() {
            Map<String, Object> contentMap = Maps.newHashMap();
            contentMap.put("customerAccountColumnMap", this.customerAccountColumnMap);
            contentMap.put("ToInvalidFlowList", toMapByField(this.toInvalidFlowList, logFieldApiNames(AccountTransactionFlowConst.API_NAME)));
            contentMap.put("ToInvalidFrozenList", toMapByField(this.toInvalidFrozenList, logFieldApiNames(AccountFrozenRecordConstant.API_NAME)));
            contentMap.put("ToInvalidUnfreezeList", toMapByField(this.toInvalidUnfreezeList, logFieldApiNames(UnfreezeDetailConstant.API_NAME)));
            contentMap.put("ToSaveFlowList", toMapByField(this.toSavaFlowList, logFieldApiNames(AccountTransactionFlowConst.API_NAME)));
            contentMap.put("ToSaveFrozenList", toMapByField(this.toSaveFrozenList, logFieldApiNames(AccountFrozenRecordConstant.API_NAME)));
            contentMap.put("ToSaveUnfreezeList", toMapByField(this.toSavaUnfreezeList, logFieldApiNames(UnfreezeDetailConstant.API_NAME)));
            contentMap.put("CustomerAccountList", toMapByField(this.customerAccountList, logFieldApiNames(NewCustomerAccountConstants.API_NAME)));
            return contentMap;
        }
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class QueryMatchedCheckRuleLog extends CheckRuleLogModel {
        private Map<String, Object> dataMap;
        private List<IObjectData> argDataList;
        private List<IObjectData> resultDataList;
        private String ruleCodeFieldName;

        @Builder
        public QueryMatchedCheckRuleLog(String action, String objectApiName, String objectDataId, Map<String, Object> dataMap, List<IObjectData> argDataList, List<IObjectData> resultDataList, String ruleCodeFieldName) {
            this.dataMap = dataMap;
            this.argDataList = argDataList;
            this.resultDataList = resultDataList;
            this.ruleCodeFieldName = ruleCodeFieldName;
            setAction(action);
            setObjectApiName(objectApiName);
            setObjectDataId(objectDataId);
        }

        @Override
        public Map<String, Object> toMap() {
            Map<String, Object> contentMap = Maps.newHashMap();
            contentMap.put("ruleCodeField", this.ruleCodeFieldName);
            contentMap.put("dataMap", this.dataMap);
            List<String> fieldApiNames = Lists.newArrayList("_id", "name");
            contentMap.put("argCheckRuleList", toMapByField(this.argDataList, fieldApiNames));
            contentMap.put("resultCheckRuleList", toMapByField(this.resultDataList, fieldApiNames));
            return contentMap;
        }
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class MatchedRuleResultLog extends CheckRuleLogModel {
        private List<IObjectData> checkRuleList;
        private IObjectData matchedRuleData;

        @Builder
        public MatchedRuleResultLog(String action, String buttonApiName, String stage, String objectApiName, String objectDataId, List<IObjectData> checkRuleList, IObjectData matchedRuleData) {
            setAction(action);
            setButtonApiName(buttonApiName);
            setStage(stage);
            setObjectApiName(objectApiName);
            setObjectDataId(objectDataId);
            this.checkRuleList = checkRuleList;
            this.matchedRuleData = matchedRuleData;
        }

        @Override
        public Map<String, Object> toMap() {
            Map<String, Object> contentMap = Maps.newHashMap();
            List<String> fieldApiNames = Lists.newArrayList("_id", "name");
            contentMap.put("argCheckRuleList", toMapByField(this.checkRuleList, fieldApiNames));
            contentMap.put("matchedRuleResult", "hasMatchedRule in key matchedRuleData");
            if (Objects.nonNull(this.matchedRuleData)) {
                contentMap.put("matchedRuleData", toMapByField(Lists.newArrayList(this.matchedRuleData), fieldApiNames));
            } else {
                contentMap.put("matchedRuleResult", "noMatchedRule");
            }
            return contentMap;
        }
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class HandlerResultLog extends CheckRuleLogModel {
        private IObjectData accountRuleUseRecordData;
        private List<IObjectData> accountTransactionFlowDataList;
        private List<IObjectData> frozenDataList;
        private List<IObjectData> unfreezeDataList;

        private Map<String, Map<String, Object>> customerAccountColumnMap;
        private Map<String, Map<String, Object>> frozenColumnMap;

        @Builder
        public HandlerResultLog(String action, String objectApiName, String objectDataId, IObjectData accountRuleUseRecordData, List<IObjectData> accountTransactionFlowDataList, List<IObjectData> frozenDataList, List<IObjectData> unfreezeDataList, Map<String, Map<String, Object>> customerAccountColumnMap, Map<String, Map<String, Object>> frozenColumnMap) {
            setAction(action);
            setObjectApiName(objectApiName);
            setObjectDataId(objectDataId);
            this.accountRuleUseRecordData = accountRuleUseRecordData;
            this.accountTransactionFlowDataList = accountTransactionFlowDataList;
            this.frozenDataList = frozenDataList;
            this.unfreezeDataList = unfreezeDataList;
            this.customerAccountColumnMap = customerAccountColumnMap;
            this.frozenColumnMap = frozenColumnMap;
        }

        @Override
        public Map<String, Object> toMap() {
            Map<String, Object> contentMap = Maps.newHashMap();
            contentMap.put("accountRuleUseRecordData", toMapByField(Lists.newArrayList(this.accountRuleUseRecordData), logFieldApiNames(AccountRuleUseRecordConstants.API_NAME)));
            contentMap.put("accountTransactionFlowDataList", toMapByField(this.accountTransactionFlowDataList, logFieldApiNames(AccountTransactionFlowConst.API_NAME)));
            contentMap.put("frozenDataList", toMapByField(this.frozenDataList, logFieldApiNames(AccountFrozenRecordConstant.API_NAME)));
            contentMap.put("unfreezeDataList", toMapByField(this.unfreezeDataList, logFieldApiNames(UnfreezeDetailConstant.API_NAME)));
            contentMap.put("customerAccountColumnMap", this.customerAccountColumnMap);
            contentMap.put("frozenColumnMap", this.frozenColumnMap);
            return contentMap;
        }
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class HandlerExceptionLog extends CheckRuleLogModel {

        @Builder
        public HandlerExceptionLog(String action, String buttonApiName, String stage, String objectApiName, String objectDataId, String errorMessage) {
            setAction(action);
            setButtonApiName(buttonApiName);
            setStage(stage);
            setObjectApiName(objectApiName);
            setObjectDataId(objectDataId);
            setErrorMessage(errorMessage);
        }
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class ChargeOffLog extends CheckRuleLogModel {
        private Map<String, Map<String, Object>> customerAccountColumnMap;
        private Map<String, List<IObjectData>> toAddDataListMap;
        private Map<String, List<IObjectData>> toUpdateDataListMap;

        @Builder
        public ChargeOffLog(String action, String buttonApiName, String stage, String objectApiName, String objectDataId, Map<String, Map<String, Object>> customerAccountColumnMap, Map<String, List<IObjectData>> toAddDataListMap, Map<String, List<IObjectData>> toUpdateDataListMap) {
            setAction(action);
            setButtonApiName(buttonApiName);
            setStage(stage);
            setObjectApiName(objectApiName);
            setObjectDataId(objectDataId);
            this.customerAccountColumnMap = customerAccountColumnMap;
            this.toAddDataListMap = toAddDataListMap;
            this.toUpdateDataListMap = toUpdateDataListMap;
        }

        @Override
        public Map<String, Object> toMap() {
            Map<String, Object> contentMap = Maps.newHashMap();
            contentMap.put("customerAccountColumnMap", this.customerAccountColumnMap);
            if (Objects.nonNull(toAddDataListMap)) {
                toAddDataListMap.forEach((objectApiName, dataList) -> {
                    List<String> fields = logFieldApiNames(objectApiName);
                    contentMap.put("add" + objectApiName, toMapByField(dataList, fields));
                });
            }
            if (Objects.nonNull(toUpdateDataListMap)) {
                toUpdateDataListMap.forEach((objectApiName, dataList) -> {
                    List<String> fields = logFieldApiNames(objectApiName);
                    contentMap.put("update" + objectApiName, toMapByField(dataList, fields));
                });
            }
            return contentMap;
        }
    }


}
