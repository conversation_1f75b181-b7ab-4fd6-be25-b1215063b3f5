package com.facishare.crm.customeraccount.predefine.reconciliationsql;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.metadata.api.IObjectData;
import org.springframework.stereotype.Component;

@Component
public class CheckRuleBulkInvalidSqlGenerator extends AbstractReconciliationSqlGenerator {

    @Override
    public void doGenerate(RequestContext requestContext, IObjectData dbCheckRuleData, IObjectData checkRuleData) {
        doGenerateByDisable(requestContext, dbCheckRuleData);
    }
}
