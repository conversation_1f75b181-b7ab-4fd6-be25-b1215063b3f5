package com.facishare.crm.customeraccount.predefine.controller;

import com.facishare.crm.customeraccount.constants.RebateIncomeDetailConstants;
import com.facishare.crm.customeraccount.util.ButtonUtil;
import com.facishare.paas.appframework.core.predef.controller.StandardNewDetailController;
import com.google.common.collect.Sets;

/**
 * <AUTHOR>
 * @date 2019/9/16
 */
public class RebateIncomeDetailNewDetailController extends StandardNewDetailController {
    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        ButtonUtil.removeFieldsFromNewDetailLayout(arg, result, Sets.newHashSet(RebateIncomeDetailConstants.Field.CustomerAccount.apiName));
        return result;
    }
}
