package com.facishare.crm.customeraccount.predefine.manager;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.customeraccount.constants.*;
import com.facishare.crm.customeraccount.enums.*;
import com.facishare.crm.customeraccount.predefine.handler.RuleHandlerUtil;
import com.facishare.crm.customeraccount.predefine.service.dto.*;
import com.facishare.crm.customeraccount.util.AccountCheckRuleMappingUtil;
import com.facishare.crm.customeraccount.util.ConfigCenter;
import com.facishare.crmcommon.manager.CommonDescribeManager;
import com.facishare.crmcommon.manager.CommonLangManager;
import com.facishare.crmcommon.manager.CommonLayoutManager;
import com.facishare.crmcommon.manager.CommonObjDataManager;
import com.facishare.crmcommon.util.ObjectDataUtil;
import com.facishare.crmcommon.util.SearchUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.crm.customeraccount.constants.AccountCheckRuleConstants;
import com.facishare.crm.customeraccount.constants.AuthorizationDetailConstant;
import com.facishare.crm.customeraccount.constants.SystemConstants;
import com.facishare.crm.customeraccount.enums.ReduceTriggerActionEnum;
import com.facishare.crm.customeraccount.predefine.service.dto.CustomerAccountType;
import com.facishare.paas.appframework.core.model.InfraServiceFacade;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class AccountCheckRuleManager extends CommonManager {

    @Autowired
    private AccountRuleUseRecordManager accountRuleUseRecordManager;
    @Autowired
    private CommonDescribeManager commonDescribeManager;
    @Autowired
    private CommonLayoutManager commonLayoutManager;
    @Autowired
    private CommonObjDataManager commonObjDataManager;
    @Autowired
    private FAccountAuthorizationManager fAccountAuthorizationManager;
    @Autowired
    private AuthorizationDetailManager authorizationDetailManager;
    @Autowired
    private FundAccountConfigManager fundAccountConfigManager;
    @Autowired
    private DomainPluginManager domainPluginManager;
    @Autowired
    private FundAccountManager fundAccountManager;
    @Autowired
    private CommonLangManager commonLangManager;
    @Autowired
    private InfraServiceFacade infraServiceFacade;

    public Map<String, List<String>> getOccupiedMappingRequiredFields() {
        return ConfigCenter.getOccupiedMappingRequiredFields();
    }

    public Map<String, List<String>> getReduceMappingRequiredFields() {
        return ConfigCenter.getReduceMappingRequiredFields();
    }

    public RLock getActionLock(User user, String dataId) {
        String lockKey = String.format("AccountCheckRule_%s_%s", user.getTenantId(), dataId);
        return infraServiceFacade.tryLock(3, 10, TimeUnit.SECONDS, lockKey);
    }

    public CustomerAccountType.CheckRulePriorityResult checkRulePriority(ServiceContext serviceContext, CustomerAccountType.CheckRulePriorityArg arg) {
        CustomerAccountType.CheckRulePriorityResult result = new CustomerAccountType.CheckRulePriorityResult(false);

        //组件扣减，预设的优先级都是10
        if (Objects.equals(arg.getRuleType(), AccountCheckRuleTypeEnum.Component_Reduce.getValue())) {
            return new CustomerAccountType.CheckRulePriorityResult(true);
        }

        boolean hasSamePriority = hasSamePriority(serviceContext.getUser(), arg.getRuleType(), arg.getPriority(), arg.getIgnoreDataIds());
        result.setIsAllow(!hasSamePriority);

        return result;
    }

    public boolean hasSamePriority(User user, String ruleType, Integer priority, List<String> ignoreDataIds) {
        IFilter priorityFilter = new Filter();
        priorityFilter.setOperator(Operator.EQ);
        priorityFilter.setFieldName(AccountCheckRuleConstants.Field.Priority.apiName);
        priorityFilter.setFieldValues(Lists.newArrayList(String.valueOf(priority)));

        IFilter ruleTypeFilter = new Filter();
        ruleTypeFilter.setOperator(Operator.EQ);
        ruleTypeFilter.setFieldName(AccountCheckRuleConstants.Field.RuleType.apiName);
        ruleTypeFilter.setFieldValues(Lists.newArrayList(ruleType));

        List<IFilter> filterList = Lists.newArrayList(priorityFilter, ruleTypeFilter);

        if (!CollectionUtils.isEmpty(ignoreDataIds)) {
            IFilter ignoreDataIdsFilter = new Filter();
            ignoreDataIdsFilter.setOperator(Operator.NEQ);
            ignoreDataIdsFilter.setFieldName(SystemConstants.Field.Id.apiName);
            ignoreDataIdsFilter.setFieldValues(ignoreDataIds);

            filterList.add(ignoreDataIdsFilter);
        }

        OrderBy startTimeOrderBy = new OrderBy(SystemConstants.Field.Id.apiName, true);
        List<IObjectData> accountCheckRuleDatas = queryByFieldFilterList(user, AccountCheckRuleConstants.API_NAME, filterList, Lists.newArrayList(startTimeOrderBy), 0, 1);
        log.info("checkRulePriority  filterList[{}], accountCheckRuleDatas[{}]", filterList, accountCheckRuleDatas);

        return !CollectionUtils.isEmpty(accountCheckRuleDatas);
    }

    /**
     * 启用时，检查映射字段完整性
     */
    public void enableCheckMappingField(IObjectData objectData) {
        String status = objectData.get(AccountCheckRuleConstants.Field.Status.apiName, String.class);
        if (Objects.equals(status, AccountCheckRuleStatusEnum.Off.getValue())) {
            return;
        }

        List<ObjectMappingModel> reduceMappingObjectMappings = RuleHandlerUtil.getObjectMapping(objectData, AccountCheckRuleConstants.Field.ReduceMapping.apiName);
        Map<String, List<String>> reduceMappingRequiredFields = getReduceMappingRequiredFields();
        boolean hasRequiredFieldEmpty = AccountCheckRuleMappingUtil.hasRequiredFieldEmpty(reduceMappingObjectMappings, reduceMappingRequiredFields);
        if (hasRequiredFieldEmpty) {
            throw new ValidateException(I18N.text(CAI18NKey.MAPPING_FIELDS_HAS_REQUIRED_FIELD_IS_EMPTY));
        }

        String ruleType = objectData.get(AccountCheckRuleConstants.Field.RuleType.apiName, String.class);
        if (Objects.equals(ruleType, AccountCheckRuleTypeEnum.Check_Reduce.getValue())) {
            List<ObjectMappingModel> occupiedMappingObjectMappings = RuleHandlerUtil.getObjectMapping(objectData, AccountCheckRuleConstants.Field.OccupiedMapping.apiName);
            Map<String, List<String>> occupiedMappingRequiredFields = getOccupiedMappingRequiredFields();
            hasRequiredFieldEmpty = AccountCheckRuleMappingUtil.hasRequiredFieldEmpty(occupiedMappingObjectMappings, occupiedMappingRequiredFields);
            if (hasRequiredFieldEmpty) {
                throw new ValidateException(I18N.text(CAI18NKey.MAPPING_FIELDS_HAS_REQUIRED_FIELD_IS_EMPTY));
            }
        }
    }

    /**
     * 组件扣减的校验规则：如果账户被'新建编辑页布局'使用，不能删
     */
    public void checkFundAccountInLayout(RequestContext requestContext, IObjectData objectData, IObjectData dbObjectData) {
        String ruleType = objectData.get(AccountCheckRuleConstants.Field.RuleType.apiName, String.class);
        if (!Objects.equals(ruleType, AccountCheckRuleTypeEnum.Component_Reduce.getValue())) {
            return;
        }

        //删掉的账户
        List<ObjectMappingModel> objectMappingModels = RuleHandlerUtil.getObjectMapping(objectData, AccountCheckRuleConstants.Field.ReduceMapping.apiName);
        List<String> authorizeAccountIds = objectMappingModels.stream().map(ObjectMappingModel::getFundAccountId).collect(Collectors.toList());

        List<ObjectMappingModel> dbObjectMappingModels = RuleHandlerUtil.getObjectMapping(dbObjectData, AccountCheckRuleConstants.Field.ReduceMapping.apiName);
        List<String> dbAuthorizeAccountIds = dbObjectMappingModels.stream().map(ObjectMappingModel::getFundAccountId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dbAuthorizeAccountIds)) {
            return;
        }

        List<String> needDeleteFundAccountIds = dbAuthorizeAccountIds.stream().filter(id -> !authorizeAccountIds.contains(id)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(needDeleteFundAccountIds)) {
            return;
        }
        String reduceRelatedObject = dbObjectData.get(AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName, String.class);

        //查所有的'新建编辑页布局'
        ServiceContext serviceContext = new ServiceContext(requestContext, null, null);
        List<ILayout> layouts = commonLayoutManager.getLayouts(serviceContext, reduceRelatedObject);
        if (CollectionUtils.isEmpty(layouts)) {
            return;
        }

        for (ILayout layout : layouts) {
            for (String fundAccountId : needDeleteFundAccountIds) {
                boolean hasFundAccount = hasFundAccount(fundAccountId, layout);
                if (hasFundAccount) {
                    String fundAccountName = commonObjDataManager.getName(requestContext.getTenantId(), fundAccountId, FundAccountConstants.API_NAME);
                    String content = I18N.text(CAI18NKey.ACCOUNT_HAS_USE_IN_LAYOUT, fundAccountName, layout.getDisplayName());
                    throw new ValidateException(content);
                }
            }
        }
    }

    /**
     * fundAccountId是否在layout中的【客户账户】组件中
     */
    public boolean hasFundAccount(String fundAccountId, ILayout layout) {
        try {
            List<IComponent> components = layout.getComponents();
            if (CollectionUtils.isEmpty(components)) {
                return false;
            }
            return hasFundAccount(fundAccountId, components);
        } catch (MetadataServiceException e) {
            log.warn("layout.getComponents fail, layout[{}]", layout);
            throw new RuntimeException(e);
        }
    }

    /**
     * fundAccountId是否在layout中的【客户账户】组件中
     */
    private boolean hasFundAccount(String fundAccountId, List<IComponent> components) {
        if (CollectionUtils.isEmpty(components)) {
            return false;
        }

        List<String> accountIds = getFundAccountIds(components);
        return accountIds.contains(fundAccountId);
    }

    /**
     * fundAccountId是否在layout中的【客户账户】组件中
     */
    private List<String> getFundAccountIds(ILayout layout) {
        List<IComponent> components;
        try {
            components = layout.getComponents();
        } catch (MetadataServiceException e) {
            log.warn("getFundAccountIds layout.getComponents() error, layout[{}]", layout);
            throw new RuntimeException(e);
        }

        return getFundAccountIds(components);
    }

    private List<String> getFundAccountIds(List<IComponent> components) {
        if (CollectionUtils.isEmpty(components)) {
            return Lists.newArrayList();
        }

        for (IComponent component : components) {
            if (!Objects.equals(component.getName(), "dht_order_customer_account")) {
                continue;
            }

            if (component.get("accountIds") == null) {
                return Lists.newArrayList();
            }
            return (List<String>) component.get("accountIds");
        }
        return Lists.newArrayList();
    }

    public void checkAccountExist(String tenantId, ILayout layout) {
        //账户校验是否开启
        boolean isAccountCheckRuleOpen = fundAccountConfigManager.isAccountCheckRuleOpen(tenantId);
        if (!isAccountCheckRuleOpen) {
            return;
        }

        //布局中的accountIds
        List<String> accountIds = getFundAccountIds(layout);
        if (CollectionUtils.isEmpty(accountIds)) {
            return;
        }

        //查组件扣减校验规则
        IObjectData componentReduceAccountCheckRuleData = getComponentReduceAccountCheckRuleData(tenantId, layout.getRefObjectApiName());
        if (componentReduceAccountCheckRuleData == null) {
            return;
        }
        List<ObjectMappingModel> objectMappingModels = RuleHandlerUtil.getObjectMapping(componentReduceAccountCheckRuleData, AccountCheckRuleConstants.Field.ReduceMapping.apiName);
        List<String> authorizeAccountIds = objectMappingModels.stream().map(ObjectMappingModel::getFundAccountId).collect(Collectors.toList());
        for (String accountId : accountIds) {
            if (!authorizeAccountIds.contains(accountId)) {
                String fundAccountName = commonObjDataManager.getName(tenantId, accountId, FundAccountConstants.API_NAME);
                String content = I18N.text(CAI18NKey.ACCOUNT_NOT_EXIST_IN_ACCOUNT_CHECK_RULE, fundAccountName, componentReduceAccountCheckRuleData.getName());
                throw new ValidateException(content);
            }
        }
    }

    public void checkForComponentReduce(String tenantId, String ruleType, IObjectData objectData) {
        if (!Objects.equals(ruleType, AccountCheckRuleTypeEnum.Component_Reduce.getValue())) {
            return;
        }

        String reduceRelatedObject = objectData.get(AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName, String.class);
        boolean hasComponentReduceAccountCheckRuleDataExist = hasComponentReduceAccountCheckRuleDataExist(tenantId, reduceRelatedObject);
        if (hasComponentReduceAccountCheckRuleDataExist) {
            throw new ValidateException(I18N.text(CAI18NKey.COMPONENT_REDUCE_ACCOUNT_CHECK_RULE_DATA_ALREADY_EXIST));
        }
    }

    /**
     * 是否有直接扣减的校验规则
     */
    public boolean hasDirectReduceAccountCheckRuleDataExist(String tenantId, String checkObject) {
        List<IObjectData> directReduceAccountCheckRuleDatas = getDirectReduceAccountCheckRuleData(tenantId, checkObject, 1);
        return !CollectionUtils.isEmpty(directReduceAccountCheckRuleDatas);
    }

    /**
     * 是否有校验扣减的校验规则
     */
    public boolean hasCheckReduceAccountCheckRuleDataExist(String tenantId, String reduceRelatedObject) {
        List<IObjectData> checkReduceAccountCheckRuleDatas = getCheckReduceAccountCheckRuleData(tenantId, reduceRelatedObject, 1);
        return !CollectionUtils.isEmpty(checkReduceAccountCheckRuleDatas);
    }

    private boolean hasComponentReduceAccountCheckRuleDataExist(String tenantId, String reduceRelatedObject) {
        IObjectData componentReduceAccountCheckRuleData = getComponentReduceAccountCheckRuleData(tenantId, reduceRelatedObject);
        return componentReduceAccountCheckRuleData != null;
    }

    /**
     * 有组件扣减规则的对象
     */
    public List<String> getHasComponentReduceAccountCheckRuleDataObjectApiNames(String tenantId, List<String> objectApiNames) {
        List<String> fields = Lists.newArrayList(SystemConstants.Field.Id.apiName, AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName);

        List<IFilter> filters = Lists.newArrayList();

        IFilter ruleTypeFilter = new Filter();
        ruleTypeFilter.setFieldName(AccountCheckRuleConstants.Field.RuleType.apiName);
        ruleTypeFilter.setFieldValues(Lists.newArrayList(AccountCheckRuleTypeEnum.Component_Reduce.getValue()));
        ruleTypeFilter.setOperator(Operator.EQ);
        filters.add(ruleTypeFilter);

        IFilter reduceObjectFilter = new Filter();
        reduceObjectFilter.setFieldName(AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName);
        reduceObjectFilter.setFieldValues(objectApiNames);
        reduceObjectFilter.setOperator(Operator.IN);
        filters.add(reduceObjectFilter);

        List<IObjectData> accountCheckRuleDatas = ObjectDataUtil.getDatas(tenantId, AccountCheckRuleConstants.API_NAME, false, fields, filters, objectApiNames.size());
        if (CollectionUtils.isEmpty(accountCheckRuleDatas)) {
            return Lists.newArrayList();
        }

        return accountCheckRuleDatas.stream().map(d -> d.get(AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName, String.class)).collect(Collectors.toList());
    }

    public boolean hasDataByCheckObject(User user, String ignoreDataId, String checkObject, String checkTriggerAction) {
        IFilter idFilter = new Filter();
        idFilter.setOperator(Operator.NEQ);
        idFilter.setFieldName(SystemConstants.Field.Id.apiName);
        idFilter.setFieldValues(Lists.newArrayList(ignoreDataId));

        IFilter checkObjectFilter = new Filter();
        checkObjectFilter.setOperator(Operator.EQ);
        checkObjectFilter.setFieldName(AccountCheckRuleConstants.Field.CheckObject.apiName);
        checkObjectFilter.setFieldValues(Lists.newArrayList(checkObject));

        List<IFilter> filterList = Lists.newArrayList(idFilter, checkObjectFilter);

        if (!Strings.isNullOrEmpty(checkTriggerAction)) {
            IFilter checkTriggerActionFilter = new Filter();
            checkTriggerActionFilter.setOperator(Operator.EQ);
            checkTriggerActionFilter.setFieldName(AccountCheckRuleConstants.Field.CheckTriggerButton.apiName);
            checkTriggerActionFilter.setFieldValues(Lists.newArrayList(checkTriggerAction));

            filterList.add(checkTriggerActionFilter);
        }

        OrderBy startTimeOrderBy = new OrderBy(SystemConstants.Field.Id.apiName, true);
        List<IObjectData> datas = queryByFieldFilterList(user, AccountCheckRuleConstants.API_NAME, filterList, Lists.newArrayList(startTimeOrderBy), 0, 1);
        log.info("hasDataByCheckObject  filterList[{}], datas[{}]", filterList, datas);

        return !CollectionUtils.isEmpty(datas);
    }


    public boolean hasDataByReduceRelatedObject(User user, String ignoreDataId, String reduceRelatedObject, String reduceTriggerButton) {
        IFilter idFilter = new Filter();
        idFilter.setOperator(Operator.NEQ);
        idFilter.setFieldName("_id");
        idFilter.setFieldValues(Lists.newArrayList(ignoreDataId));

        IFilter reduceRelatedObjectFilter = new Filter();
        reduceRelatedObjectFilter.setOperator(Operator.EQ);
        reduceRelatedObjectFilter.setFieldName(AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName);
        reduceRelatedObjectFilter.setFieldValues(Lists.newArrayList(reduceRelatedObject));

        List<IFilter> filterList = Lists.newArrayList(idFilter, reduceRelatedObjectFilter);

        if (!Strings.isNullOrEmpty(reduceTriggerButton)) {
            IFilter reduceTriggerActionFilter = new Filter();
            reduceTriggerActionFilter.setOperator(Operator.EQ);
            reduceTriggerActionFilter.setFieldName(AccountCheckRuleConstants.Field.ReduceTriggerAction.apiName);
            reduceTriggerActionFilter.setFieldValues(Lists.newArrayList(ReduceTriggerActionEnum.Button.getValue()));
            filterList.add(reduceTriggerActionFilter);

            IFilter reduceTriggerButtonFilter = new Filter();
            reduceTriggerButtonFilter.setOperator(Operator.EQ);
            reduceTriggerButtonFilter.setFieldName(AccountCheckRuleConstants.Field.ReduceTriggerButton.apiName);
            reduceTriggerButtonFilter.setFieldValues(Lists.newArrayList(reduceTriggerButton));
            filterList.add(reduceTriggerButtonFilter);
        }

        OrderBy startTimeOrderBy = new OrderBy(SystemConstants.Field.Id.apiName, true);
        List<IObjectData> datas = queryByFieldFilterList(user, AccountCheckRuleConstants.API_NAME, filterList, Lists.newArrayList(startTimeOrderBy), 0, 1);
        log.info("hasDataByReduceRelatedObject  filterList[{}], datas[{}]", filterList, datas);

        return !CollectionUtils.isEmpty(datas);
    }

    public boolean existAccountRuleUseRecord(User user, String accountCheckRuleId) {
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, AccountRuleUseRecordConstants.Field.CheckRuleId.apiName, accountCheckRuleId);
        List<IObjectData> dataList = searchQuery(user, AccountRuleUseRecordConstants.API_NAME, filters, Lists.newArrayList(), 0, 1).getData();
        return !CollectionUtils.isEmpty(dataList);
    }

    public List<IObjectData> getAllAccountCheckRuleDatas(String tenantId, boolean allFields, List<String> fields, String ruleType) {
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, AccountCheckRuleConstants.Field.RuleType.apiName, Lists.newArrayList(ruleType));

        return getAllAccountCheckRuleDatas(tenantId, allFields, fields, filters);
    }

    public List<IObjectData> getAllAccountCheckRuleDatas(String tenantId, boolean allFields, List<String> fields, String ruleType, List<String> reduceRelatedObjectApiNames) {
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, AccountCheckRuleConstants.Field.RuleType.apiName, Lists.newArrayList(ruleType));
        SearchUtil.fillFilterIn(filters, AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName, reduceRelatedObjectApiNames);

        return getAllAccountCheckRuleDatas(tenantId, allFields, fields, filters);
    }

    public List<IObjectData> getAllAccountCheckRuleDatas(String tenantId, List<String> ruleTypes, boolean allFields, List<String> fields) {
        if (CollectionUtils.isEmpty(ruleTypes)) {
            return Lists.newArrayList();
        }
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, AccountCheckRuleConstants.Field.RuleType.apiName, ruleTypes);

        return getAllAccountCheckRuleDatas(tenantId, allFields, fields, filters);
    }

    /**
     * 查询所有的校验规则
     */
    public List<IObjectData> getAllAccountCheckRuleDatas(String tenantId, boolean allFields, List<String> fields) {
        List<IFilter> filters = Lists.newArrayList();
        return getAllAccountCheckRuleDatas(tenantId, allFields, fields, filters);
    }

    public List<IObjectData> getAllAccountCheckRuleDatas(String tenantId, boolean allFields, List<String> fields, List<IFilter> filterList) {
        IFilter deleteStatusFilter = new Filter();
        deleteStatusFilter.setFieldName(ObjectData.IS_DELETED);
        deleteStatusFilter.setFieldValues(Lists.newArrayList("0"));
        deleteStatusFilter.setOperator(Operator.EQ);
        filterList.add(deleteStatusFilter);

        IFilter tenantIdFilter = new Filter();
        tenantIdFilter.setFieldName(IObjectData.TENANT_ID);
        tenantIdFilter.setFieldValues(Lists.newArrayList(tenantId));
        tenantIdFilter.setOperator(Operator.EQ);
        filterList.add(tenantIdFilter);

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(filterList);
        query.setOffset(0);
        query.setLimit(200);

        User admin = new User(tenantId, "-10000");
        String objectApiName = AccountCheckRuleConstants.API_NAME;
        if (!allFields) {
            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(admin, objectApiName, query, fields);
            return queryResult.getData();
        } else {
            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(admin, objectApiName, query);
            return queryResult.getData();
        }
    }

    public List<IObjectData> getAccountCheckRuleData(String tenantId, String ruleType, String checkObject) {
        IFilter reduceRelatedObjectFilter = new Filter();
        reduceRelatedObjectFilter.setOperator(Operator.EQ);
        reduceRelatedObjectFilter.setFieldName(AccountCheckRuleConstants.Field.CheckObject.apiName);
        reduceRelatedObjectFilter.setFieldValues(Lists.newArrayList(checkObject));

        IFilter ruleTypeFilter = new Filter();
        ruleTypeFilter.setOperator(Operator.EQ);
        ruleTypeFilter.setFieldName(AccountCheckRuleConstants.Field.RuleType.apiName);
        ruleTypeFilter.setFieldValues(Lists.newArrayList(ruleType));

        List<IFilter> filterList = Lists.newArrayList(reduceRelatedObjectFilter, ruleTypeFilter);

        User admin = new User(tenantId, "-10000");
        List<IObjectData> datas = queryByFieldFilterList(admin, AccountCheckRuleConstants.API_NAME, filterList, Lists.newArrayList(), 0, 1);
        if (CollectionUtils.isEmpty(datas)) {
            log.info("getAccountCheckRuleData tenantId[{}], filterList[{}], datas.size = 0", tenantId, filterList);
            return Lists.newArrayList();
        }

        return datas;
    }

    /**
     * 查询'直接扣减'的校验规则
     */
    public List<IObjectData> getDirectReduceAccountCheckRuleData(String tenantId, String reduceRelatedObjectApiName, int limit) {
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, AccountCheckRuleConstants.Field.RuleType.apiName, AccountCheckRuleTypeEnum.Direct_Reduce.getValue());
        SearchUtil.fillFilterEq(filters, AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName, reduceRelatedObjectApiName);

        User admin = new User(tenantId, "-10000");
        List<IObjectData> datas = queryByFieldFilterList(admin, AccountCheckRuleConstants.API_NAME, filters, Lists.newArrayList(), 0, limit);
        if (CollectionUtils.isEmpty(datas)) {
            log.info("getDirectReduceAccountCheckRuleData tenantId[{}], filters[{}], datas.size = 0", tenantId, filters);
            return Lists.newArrayList();
        }

        return datas;
    }

    /**
     * 查询'校验扣减'的校验规则
     */
    public List<IObjectData> getCheckReduceAccountCheckRuleData(String tenantId, String checkObject, int limit) {
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, AccountCheckRuleConstants.Field.RuleType.apiName, AccountCheckRuleTypeEnum.Check_Reduce.getValue());
        SearchUtil.fillFilterEq(filters, AccountCheckRuleConstants.Field.CheckObject.apiName, checkObject);

        User admin = new User(tenantId, "-10000");
        List<IObjectData> datas = queryByFieldFilterList(admin, AccountCheckRuleConstants.API_NAME, filters, Lists.newArrayList(), 0, limit);
        if (CollectionUtils.isEmpty(datas)) {
            log.info("getCheckReduceAccountCheckRuleData tenantId[{}], filters[{}], datas.size = 0", tenantId, filters);
            return Lists.newArrayList();
        }

        return datas;
    }

    /**
     * 查询'组件扣减'的校验规则
     */
    public IObjectData getComponentReduceAccountCheckRuleData(String tenantId, String reduceRelatedObjectApiName) {
        IFilter reduceRelatedObjectFilter = new Filter();
        reduceRelatedObjectFilter.setOperator(Operator.EQ);
        reduceRelatedObjectFilter.setFieldName(AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName);
        reduceRelatedObjectFilter.setFieldValues(Lists.newArrayList(reduceRelatedObjectApiName));

        IFilter ruleTypeFilter = new Filter();
        ruleTypeFilter.setOperator(Operator.EQ);
        ruleTypeFilter.setFieldName(AccountCheckRuleConstants.Field.RuleType.apiName);
        ruleTypeFilter.setFieldValues(Lists.newArrayList(AccountCheckRuleTypeEnum.Component_Reduce.getValue()));

        List<IFilter> filterList = Lists.newArrayList(reduceRelatedObjectFilter, ruleTypeFilter);

        OrderBy startTimeOrderBy = new OrderBy(SystemConstants.Field.Id.apiName, true);
        User admin = new User(tenantId, "-10000");
        List<IObjectData> datas = queryByFieldFilterList(admin, AccountCheckRuleConstants.API_NAME, filterList, Lists.newArrayList(startTimeOrderBy), 0, 1);
        if (CollectionUtils.isEmpty(datas)) {
            log.info("getComponentReduceAccountCheckRuleData tenantId[{}], filterList[{}], datas.size = 0", tenantId, filterList);
            return null;
        }

        return datas.get(0);
    }

    /**
     * 从规则使用记录里面，查到'组件扣减'类型的校验规则
     */
    public IObjectData queryComponentReduceAccountCheckRuleDataFromUseRecord(User user, String objectApiName, String objectDataId, boolean throwIfUseRecordNotExist) {
        IObjectData useRecordForComponentReduce = accountRuleUseRecordManager.queryUseRecordForComponentReduce(user, objectApiName, objectDataId, throwIfUseRecordNotExist);
        return getComponentReduceAccountCheckRuleData(useRecordForComponentReduce);
    }

    public IObjectData getComponentReduceAccountCheckRuleData(IObjectData useRecordForComponentReduce) {
        if (useRecordForComponentReduce == null) {
            return null;
        }

        IObjectData componentReduceAccountCheckRuleData = new ObjectData();
        String json = useRecordForComponentReduce.get(AccountRuleUseRecordConstants.Field.CheckRule.apiName, String.class);
        componentReduceAccountCheckRuleData.fromJsonString(json);
        return componentReduceAccountCheckRuleData;
    }

    /**
     * authorizedObjectDataId 存在，查使用记录
     * authorizedObjectDataId 不存在，查最新的'组件扣减' 校验规则
     */
    public IObjectData getComponentReduceAccountCheckRuleData(User user, String authorizedObjectApiName, String authorizedObjectDataId) {
        IObjectData componentReduceAccountCheckRuleData = new ObjectData();
        if (Strings.isNullOrEmpty(authorizedObjectDataId)) {
            componentReduceAccountCheckRuleData = getComponentReduceAccountCheckRuleData(user.getTenantId(), authorizedObjectApiName);
            if (componentReduceAccountCheckRuleData == null) {
                throw new ValidateException(I18N.text(CAI18NKey.ACCOUNT_CHECK_RULE_NOT_FOUND));
            }
        } else {
            //查使用记录
            componentReduceAccountCheckRuleData = queryComponentReduceAccountCheckRuleDataFromUseRecord(user, authorizedObjectApiName, authorizedObjectDataId, false);
            if (componentReduceAccountCheckRuleData == null) {
                componentReduceAccountCheckRuleData = getComponentReduceAccountCheckRuleData(user.getTenantId(), authorizedObjectApiName);
                if (componentReduceAccountCheckRuleData == null) {
                    throw new ValidateException(I18N.text(CAI18NKey.ACCOUNT_CHECK_RULE_NOT_FOUND));
                }
            }
        }
        return componentReduceAccountCheckRuleData;
    }

    /**
     * 如果dataId对应的数据是ineffective、invalid，查最新的校验规则，而不是规则使用记录
     */
    public boolean queryFromUseRecord(String tenantId, String objectApiName, String dataId) {
        if (Strings.isNullOrEmpty(dataId)) {
            return false;
        }

        List<IObjectData> authorizedObjectDatas = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, Lists.newArrayList(dataId), objectApiName);
        if (CollectionUtils.isEmpty(authorizedObjectDatas)) {
            return false;
        }

        IObjectData authorizedObjectData = authorizedObjectDatas.get(0);
        String lifeStatus = authorizedObjectData.get(SystemConstants.Field.LifeStatus.apiName, String.class);
        log.info("queryFromUseRecord tenantId[{}], objectApiName[{}], dataId[{}], lifeStatus[{}]", tenantId, objectApiName, dataId, lifeStatus);
        List<String> queryFromUseRecordLifeStatus = Lists.newArrayList(SystemConstants.LifeStatus.Normal.value, SystemConstants.LifeStatus.InChange.value, SystemConstants.LifeStatus.UnderReview.value);
        if (queryFromUseRecordLifeStatus.contains(lifeStatus)) {
            return true;
        }

        return false;
    }

    /**
     * 获取对应的金额字段
     */
    public String getTradeAmountFieldApiName(List<FieldMappingModel> fieldMappingList, String expenseAmountTargetFieldApiName) {
        if (CollectionUtils.isEmpty(fieldMappingList)) {
            return null;
        }

        for (FieldMappingModel fieldMappingModel : fieldMappingList) {
            if (Objects.equals(fieldMappingModel.getTargetFieldApiName(), expenseAmountTargetFieldApiName)) {
                return fieldMappingModel.getSourceFieldApiName();
            }
        }

        return null;
    }

    /**
     * 870刷数据，之前已经初始化的订单，创建的'组件扣减'的校验规则，需要启用的，其他是停用的
     */
    public String getSalesOrderAccountCheckRuleStatus(User user, boolean transfer870Data) {
        if (!transfer870Data) {
            return AccountCheckRuleStatusEnum.Off.getValue();
        }

        IObjectData fAccountAuthorizationData = fAccountAuthorizationManager.getFAccountAuthorizationData(user, "SalesOrderObj", FAccountAuthAuthorizedTypeEnum.Outcome.getValue());
        if (fAccountAuthorizationData != null) {
            String status = fAccountAuthorizationData.get(FAccountAuthorizationConstants.Field.Status.apiName, String.class);
            if (Objects.equals(status, FAccountAuthorizationStatusEnum.HAS_INIT.getValue())) {
                //之前已经初始化的, 刷为启用
                return AccountCheckRuleStatusEnum.On.getValue();
            }
        }

        return AccountCheckRuleStatusEnum.Off.getValue();
    }

    /**
     * 增加支出授权，保存'组件扣减'的校验规则
     */
    public void saveOrUpdateComponentReduceAccountCheckRule(User user, AccessOutcomeAuthModel.Arg arg) {
        String tenantId = user.getTenantId();

        //查'组件扣减'的校验规则
        String authorizedObjectApiName = arg.getObjectData().getAuthorizedObjectApiName();
        IObjectData componentReduceAccountCheckRuleData = getComponentReduceAccountCheckRuleData(tenantId, authorizedObjectApiName);

        //不存在：新建（规则未启用）
        if (componentReduceAccountCheckRuleData == null) {
            saveComponentReduceAccountCheckRule(user, arg);
        }
        //存在：更新
        else {
            updateComponentReduceAccountCheckRule(user, arg, componentReduceAccountCheckRuleData);
        }
    }

    private void saveComponentReduceAccountCheckRule(User user, AccessOutcomeAuthModel.Arg arg) {
        String authorizedObjectApiName = arg.getObjectData().getAuthorizedObjectApiName();
        List<ObjectMappingModel> objectMappingModels = AccountCheckRuleMappingUtil.getObjectMappingModelsForComponentReduce(arg.getDetails().get(authorizedObjectApiName), authorizedObjectApiName, arg.getObjectData().getEntryCustomerFieldApiName());

        saveComponentReduceAccountCheckRuleAndCreatePluginInstance(user, authorizedObjectApiName, objectMappingModels, AccountCheckRuleStatusEnum.Off.getValue());
    }

    /**
     * needConsiderTradeAmountFieldApiName 870 刷老数据，才需要考虑到 tradeAmountFieldApiName
     */
    public void saveComponentReduceAccountCheckRule(User user, IObjectData outcomeAuthData, List<IObjectData> authDetails, String accountCheckRuleStatus, boolean needConsiderTradeAmountFieldApiName) {
        String authorizedObjectApiName = outcomeAuthData.get(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, String.class);
        String entryCustomerFieldApiName = outcomeAuthData.get(FAccountAuthorizationConstants.Field.EntryCustomerFieldApiName.apiName, String.class);
        List<ObjectMappingModel> objectMappingModels = AccountCheckRuleMappingUtil.getObjectMappingModelsForComponentReduce(authorizedObjectApiName, entryCustomerFieldApiName, authDetails, needConsiderTradeAmountFieldApiName);

        saveComponentReduceAccountCheckRuleAndCreatePluginInstance(user, authorizedObjectApiName, objectMappingModels, accountCheckRuleStatus);
    }

    /**
     * 初始化支出授权时，根据支出授权，新建对应的'组件扣减'的校验规则
     */
    public void saveComponentReduceAccountCheckRule(User user, String authorizedObjectApiName, String entryCustomerFieldApiName) {
        String tenantId = user.getTenantId();

        //查'组件扣减'的校验规则
        IObjectData componentReduceAccountCheckRuleData = getComponentReduceAccountCheckRuleData(tenantId, authorizedObjectApiName);

        if (componentReduceAccountCheckRuleData != null) {
            domainPluginManager.createOrEnableLayoutCustomerAccountComponentCheckPluginInstance(user.getTenantId(), authorizedObjectApiName);
            return;
        }

        //查授权明细
        List<IObjectData> authorizationDetailDatas = authorizationDetailManager.query(tenantId, FAccountAuthAuthorizedTypeEnum.Outcome.getValue(), authorizedObjectApiName);
        List<String> authorizeAccountIds = authorizationDetailDatas.stream().map(d -> d.get(AuthorizationDetailConstant.Field.AuthorizeAccountId.apiName, String.class)).collect(Collectors.toList());

        List<ObjectMappingModel> objectMappingModels = AccountCheckRuleMappingUtil.getObjectMappingModelsForComponentReduce(authorizedObjectApiName, authorizeAccountIds, entryCustomerFieldApiName);

        saveComponentReduceAccountCheckRuleAndCreatePluginInstance(user, authorizedObjectApiName, objectMappingModels, AccountCheckRuleStatusEnum.Off.getValue());
    }

    private void saveComponentReduceAccountCheckRuleAndCreatePluginInstance(User user, String authorizedObjectApiName, List<ObjectMappingModel> objectMappingModels, String accountCheckRuleStatus) {
        saveComponentReduceAccountCheckRule(user, authorizedObjectApiName, objectMappingModels, accountCheckRuleStatus);

        domainPluginManager.createOrEnableLayoutCustomerAccountComponentCheckPluginInstance(user.getTenantId(), authorizedObjectApiName);
    }

    private void saveComponentReduceAccountCheckRule(User user, String authorizedObjectApiName, List<ObjectMappingModel> objectMappingModels, String accountCheckRuleStatus) {
        //查对象名称
        IObjectDescribe authorizedObjectDescribe = commonDescribeManager.findByTenantIdAndDescribeApiName(user.getTenantId(), authorizedObjectApiName);

        IObjectData objectData = ObjectDataUtil.getBaseObjectData(user, AccountCheckRuleConstants.API_NAME);
        String name = getComponentReduceAccountCheckRuleName(user.getTenantId(), authorizedObjectDescribe.getDisplayName());
        objectData.set(AccountCheckRuleConstants.Field.Name.apiName, name);

        objectData.set(AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName, authorizedObjectApiName);
        objectData.set(AccountCheckRuleConstants.Field.ReduceTriggerAction.apiName, ReduceTriggerActionEnum.Button.getValue());
        objectData.set(AccountCheckRuleConstants.Field.ReduceTriggerButton.apiName, "Add_button_default");
        objectData.set(AccountCheckRuleConstants.Field.ReduceMapping.apiName, JSONObject.toJSONString(objectMappingModels));

        objectData.set(AccountCheckRuleConstants.Field.Status.apiName, accountCheckRuleStatus);
        objectData.set(AccountCheckRuleConstants.Field.Priority.apiName, "10");
//        objectData.set(AccountCheckRuleConstants.Field.Specification.apiName, "基于支出授权预设的组件扣减规则，仅可在客户账户组件中使用。");
        ObjectDataUtil.setObjectDataLang(user.getTenantId(), objectData, AccountCheckRuleConstants.Field.Specification.apiName, CAI18NKey.ACCOUNT_CHECK_RULE_PRESET_COMPONENT_SPECIFICATION);
        objectData.set(AccountCheckRuleConstants.Field.RuleType.apiName, AccountCheckRuleTypeEnum.Component_Reduce.getValue());
        objectData.set("record_type", "default__c");

        try {
            IObjectData result = serviceFacade.saveObjectData(user, objectData);
            log.info("saveComponentReduceAccountCheckRule user[{}]", user);
        } catch (Exception e) {  //e.getMessage() = 规则名称字段值[预设{zs支出2}组件扣减规则]已存在
            log.warn("saveObjectData failed user[{}], objectData[{}]", user, objectData, e);
            throw new ValidateException(I18N.text(CAI18NKey.SAVE_ACCOUNT_CHECK_RULE_FAILED));
        }
    }

    /**
     * 给【组件扣减】校验规则，创建插件插件实例
     * 1、customer_account插件
     * 2、layout_customer_account_component_check
     */
    public void createPluginInstanceForComponentReduceAccountCheckRule(RequestContext requestContext, String reduceRelatedObject) {
        //customer_account
        domainPluginManager.createPluginInstanceIfNotExist(requestContext, "customer_account", reduceRelatedObject);

        //layout_customer_account_component_check
        domainPluginManager.createOrEnableLayoutCustomerAccountComponentCheckPluginInstance(requestContext.getTenantId(), reduceRelatedObject);
    }

    /**
     * 查出所有的'组件扣减'的校验规则，新建插件
     */
    public void createOrEnableLayoutCustomerAccountComponentCheckPluginInstance(String tenantId) {
        //查所有的'组件扣减'的校验规则
        List<String> fields = Lists.newArrayList(SystemConstants.Field.Id.apiName, AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName);
        List<IObjectData> allComponentReduceAccountCheckRuleDatas = getAllAccountCheckRuleDatas(tenantId, false, fields, AccountCheckRuleTypeEnum.Component_Reduce.getValue());
        if (CollectionUtils.isEmpty(allComponentReduceAccountCheckRuleDatas)) {
            log.info("createOrEnableLayoutCustomerAccountComponentCheckPluginInstance allComponentReduceAccountCheckRuleDatas is empty, tenantId[{}]", tenantId);
            return;
        }

        for (IObjectData accountCheckRule : allComponentReduceAccountCheckRuleDatas) {
            String reduceRelatedObject = accountCheckRule.get(AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName, String.class);
            domainPluginManager.createOrEnableLayoutCustomerAccountComponentCheckPluginInstance(tenantId, reduceRelatedObject);
        }
    }

    public void fixComponentReduceAccountCheckRuleData(String tenantId) {
        List<IObjectData> allComponentReduceAccountCheckRuleDatas = getAllAccountCheckRuleDatas(tenantId, true, null, AccountCheckRuleTypeEnum.Component_Reduce.getValue());
        if (CollectionUtils.isEmpty(allComponentReduceAccountCheckRuleDatas)) {
            log.info("fixComponentReduceAccountCheckRuleData allComponentReduceAccountCheckRuleDatas is empty, tenantId[{}]", tenantId);
            return;
        }

        for (IObjectData accountCheckRule : allComponentReduceAccountCheckRuleDatas) {
            String reduceTriggerAction = accountCheckRule.get(AccountCheckRuleConstants.Field.ReduceTriggerAction.apiName, String.class);
            String reduceTriggerButton = accountCheckRule.get(AccountCheckRuleConstants.Field.ReduceTriggerButton.apiName, String.class);
            String reduceTriggerCondition = accountCheckRule.get(AccountCheckRuleConstants.Field.ReduceTriggerCondition.apiName, String.class);

            boolean needUpdate = false;
            if (Strings.isNullOrEmpty(reduceTriggerAction)) {
                accountCheckRule.set(AccountCheckRuleConstants.Field.ReduceTriggerAction.apiName, "button");
                needUpdate = true;
            }
            if (Strings.isNullOrEmpty(reduceTriggerButton)) {
                accountCheckRule.set(AccountCheckRuleConstants.Field.ReduceTriggerButton.apiName, "Add_button_default");
                needUpdate = true;
            }
            if (Strings.isNullOrEmpty(reduceTriggerCondition)) {
                accountCheckRule.set(AccountCheckRuleConstants.Field.ReduceTriggerCondition.apiName, "[]");
                needUpdate = true;
            }

            if (needUpdate) {
                serviceFacade.updateObjectData(User.systemUser(tenantId), accountCheckRule);
            }
        }
/*
        if (CollectionUtils.isEmpty(needUpdateDatas)) {
            return;
        }

        User admin = User.systemUser(tenantId);
        try {
            //batchUpdateByFields 会报错  Cause: org.postgresql.util.PSQLException: ERROR: column "reduce_trigger_condition" is of type jsonb but expression is of type text
            //serviceFacade.batchUpdateByFields(admin, needUpdateDatas, updateFields);
            //serviceFacade.batchUpdate(needUpdateDatas, admin);


        } catch(Exception e) {
            log.info("fixComponentReduceAccountCheckRuleData serviceFacade.batchUpdateByFields, fail  admin[{}], needUpdateDatas[{}]", admin, needUpdateDatas, e);
            throw e;
        }*/
    }

    public void fixComponentReduceAccountCheckRuleDataReduceMapping(String targetTenantId, List<String> reduceRelatedObjectApiNames, List<IObjectData> sourceComponentReduceAccountCheckRuleDatas) {
        Map<String, IObjectData> sourceReduceRelatedObjectApiName2Data = sourceComponentReduceAccountCheckRuleDatas.stream().collect(Collectors.toMap(
                d -> d.get(AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName, String.class), p -> p));

        for (String reduceRelatedObjectApiName : reduceRelatedObjectApiNames) {
            IObjectData componentReduceAccountCheckRuleData = sourceReduceRelatedObjectApiName2Data.get(reduceRelatedObjectApiName);
            fixComponentReduceAccountCheckRuleDataReduceMapping(targetTenantId, reduceRelatedObjectApiName, componentReduceAccountCheckRuleData);
        }
    }

    public void fixComponentReduceAccountCheckRuleDataReduceMapping(String targetTenantId, String reduceRelatedObjectApiName, IObjectData sourceComponentReduceAccountCheckRuleData) {
        //找到targetTenantId组件的校验规则
        IObjectData targetComponentReduceAccountCheckRuleData = getComponentReduceAccountCheckRuleData(targetTenantId, reduceRelatedObjectApiName);
        if (targetComponentReduceAccountCheckRuleData == null) {
            log.info("fixComponentReduceAccountCheckRuleDataReduceMapping targetComponentReduceAccountCheckRuleData == null targetTenantId[{}], reduceRelatedObjectApiName[{}]", targetTenantId, reduceRelatedObjectApiName);
            return;
        }

        targetComponentReduceAccountCheckRuleData.set(AccountCheckRuleConstants.Field.ReduceMapping.apiName, sourceComponentReduceAccountCheckRuleData.get(AccountCheckRuleConstants.Field.ReduceMapping.apiName));

        User admin = User.systemUser(targetTenantId);
        serviceFacade.updateObjectData(admin, targetComponentReduceAccountCheckRuleData);
    }

    /**
     * 组件扣减类型的校验规则的名称
     */
    private String getComponentReduceAccountCheckRuleName(String tenantId, String authorizedObjectName) {
        //查所有的校验规则
        List<IObjectData> allAccountCheckRuleDatas = getAllAccountCheckRuleDatas(tenantId, false, Lists.newArrayList(AccountCheckRuleConstants.Field.Name.apiName));
        List<String> existAccountCheckRuleNames = allAccountCheckRuleDatas.stream().map(d -> d.get(AccountCheckRuleConstants.Field.Name.apiName, String.class)).collect(Collectors.toList());

        String name = "";
        for (int i = 1; i < 10000; i++) {
            if (i == 1) {
                name = I18N.text(CAI18NKey.ACCOUNT_CHECK_RULE_PRESET_COMPONENT_REDUCE_NAME, authorizedObjectName, "");
            } else {
                name = I18N.text(CAI18NKey.ACCOUNT_CHECK_RULE_PRESET_COMPONENT_REDUCE_NAME, authorizedObjectName, i);
            }

            if (!existAccountCheckRuleNames.contains(name)) {
                return name;
            }
        }
        return I18N.text(CAI18NKey.ACCOUNT_CHECK_RULE_PRESET_COMPONENT_REDUCE_NAME, authorizedObjectName, System.currentTimeMillis());
    }

    public void updateComponentReduceAccountCheckRule(User user, AccessOutcomeAuthModel.Arg arg, IObjectData componentReduceAccountCheckRuleData) {
        String authorizedObjectApiName = arg.getObjectData().getAuthorizedObjectApiName();
        String entryCustomerFieldApiName = arg.getObjectData().getEntryCustomerFieldApiName();

        //更新ReduceMapping
        List<ObjectMappingModel> objectMappingModels = RuleHandlerUtil.getObjectMapping(componentReduceAccountCheckRuleData, AccountCheckRuleConstants.Field.ReduceMapping.apiName);
        List<String> existAuthorizeAccountIds = objectMappingModels.stream().map(ObjectMappingModel::getFundAccountId).collect(Collectors.toList());

        boolean hasNewFundAccount = false;
        for (AccessOutcomeAuthModel.DetailFields detailFields : arg.getDetails().get(authorizedObjectApiName)) {
            String authorizeAccountId = detailFields.getAuthorizeAccountId();
            String tradeAmountFieldApiName = detailFields.getTradeAmountFieldApiName();
            if (existAuthorizeAccountIds.contains(authorizeAccountId)) {
                continue;
            }
            hasNewFundAccount = true;
            ObjectMappingModel objectMappingModel = AccountCheckRuleMappingUtil.getObjectMappingModelForComponentReduce(authorizeAccountId, authorizedObjectApiName, entryCustomerFieldApiName, tradeAmountFieldApiName);

            objectMappingModels.add(objectMappingModel);
        }

        if (!hasNewFundAccount) {
            return;
        }
        String reduceMappingJson = JSONObject.toJSONString(objectMappingModels);
        componentReduceAccountCheckRuleData.set(AccountCheckRuleConstants.Field.ReduceMapping.apiName, reduceMappingJson);

        //batchUpdateByFields 会报错  Cause: org.postgresql.util.PSQLException: ERROR: column "reduce_mapping" is of type jsonb but expression is of type text
        //serviceFacade.batchUpdateByFields(user, Lists.newArrayList(componentReduceAccountCheckRuleData), updateFields);

        serviceFacade.updateObjectData(user, componentReduceAccountCheckRuleData);
    }

    /**
     * 开启校验规则时，新建订单'组件扣减'类型的校验规则
     * <p>
     * needConsiderTradeAmountFieldApiName 870 刷老数据，才需要考虑到 tradeAmountFieldApiName
     */
    public void saveComponentReduceAccountCheckRuleForSaleOrder(User user, boolean needConsiderTradeAmountFieldApiName, String accountCheckRuleStatus) {
        String tenantId = user.getTenantId();
        String authorizedObjectApiName = "SalesOrderObj";
        String entryCustomerFieldApiName = "account_id";

        //是否存在
        //查'组件扣减'的校验规则
        IObjectData componentReduceAccountCheckRuleData = getComponentReduceAccountCheckRuleData(tenantId, authorizedObjectApiName);
        if (componentReduceAccountCheckRuleData != null) {
            return;
        }

        IObjectData fAccountAuthorizationData = fAccountAuthorizationManager.getFAccountAuthorizationData(user, authorizedObjectApiName, FAccountAuthAuthorizedTypeEnum.Outcome.getValue());
        if (fAccountAuthorizationData == null) {
            log.info("saveComponentReduceAccountCheckRuleForSaleOrder  fAccountAuthorizationData = null user[{}]", user);
            return;
        }

        //查授权明细
        List<IObjectData> authorizationDetails = authorizationDetailManager.query(tenantId, FAccountAuthAuthorizedTypeEnum.Outcome.getValue(), authorizedObjectApiName);
        List<ObjectMappingModel> objectMappings = AccountCheckRuleMappingUtil.getObjectMappingModelsForComponentReduce(authorizedObjectApiName, entryCustomerFieldApiName, authorizationDetails, needConsiderTradeAmountFieldApiName);

        saveComponentReduceAccountCheckRuleAndCreatePluginInstance(user, authorizedObjectApiName, objectMappings, accountCheckRuleStatus);
    }


    /**
     * 对象被哪些【校验规则】使用了
     */
    public List<String> getUseReduceRelatedObjectAccountCheckRuleNames(List<IObjectData> accountCheckRuleDatas, String targetReduceRelatedObject) {
        List<String> useReduceRelatedObjectAccountCheckRuleNames = Lists.newArrayList();

        for (IObjectData accountCheckRuleData : accountCheckRuleDatas) {
            String reduceRelatedObject = accountCheckRuleData.get(AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName, String.class);
            if (!Objects.equals(targetReduceRelatedObject, reduceRelatedObject)) {
                continue;
            }

            String accountCheckRuleName = (String) commonLangManager.getObjectMultiLangValue(accountCheckRuleData, AccountCheckRuleConstants.Field.Name.apiName);
            useReduceRelatedObjectAccountCheckRuleNames.add(accountCheckRuleName);
        }
        return useReduceRelatedObjectAccountCheckRuleNames;
    }

    /**
     * 账户被哪些【校验规则】使用了
     */
    public List<String> getUseFundAccountAccountCheckRuleNames(List<IObjectData> accountCheckRuleDatas, String authorizedObjectApiName, String authorizeAccountId) {
        List<String> useFundAccountAccountCheckRuleNames = Lists.newArrayList();

        for (IObjectData accountCheckRuleData : accountCheckRuleDatas) {
            boolean hasUseFundAccount = hasUseFundAccount(accountCheckRuleData, authorizedObjectApiName, authorizeAccountId);
            if (!hasUseFundAccount) {
                continue;
            }

            String accountCheckRuleName = (String) commonLangManager.getObjectMultiLangValue(accountCheckRuleData, AccountCheckRuleConstants.Field.Name.apiName);
            useFundAccountAccountCheckRuleNames.add(accountCheckRuleName);
        }
        return useFundAccountAccountCheckRuleNames;
    }

    public boolean hasUseFundAccount(List<IObjectData> allAccountCheckRuleDatas, String authorizedObjectApiName, String authorizeAccountId) {
        for (IObjectData accountCheckRuleData : allAccountCheckRuleDatas) {
            boolean hasUseFundAccount = hasUseFundAccount(accountCheckRuleData, authorizedObjectApiName, authorizeAccountId);
            if (hasUseFundAccount) {
                return true;
            }
        }
        return false;
    }

    /**
     * 校验规则是否用到了账户authorizeAccountId
     */
    public boolean hasUseFundAccount(IObjectData accountCheckRuleData, String authorizedObjectApiName, String authorizeAccountId) {

        String ruleType = accountCheckRuleData.get(AccountCheckRuleConstants.Field.RuleType.apiName, String.class);
        String objectFieldApiName;
        String mappingFieldApiName;

        if (Objects.equals(ruleType, AccountCheckRuleTypeEnum.Check_Reduce.getValue())) {
            //校验
            objectFieldApiName = AccountCheckRuleConstants.Field.CheckObject.apiName;
            mappingFieldApiName = AccountCheckRuleConstants.Field.OccupiedMapping.apiName;
            boolean hasUseFundAccount = hasUseFundAccount(accountCheckRuleData, authorizedObjectApiName, authorizeAccountId, objectFieldApiName, mappingFieldApiName);
            if (hasUseFundAccount) {
                return true;
            }
        }

        //扣减
        objectFieldApiName = AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName;
        mappingFieldApiName = AccountCheckRuleConstants.Field.ReduceMapping.apiName;
        boolean hasUseFundAccount = hasUseFundAccount(accountCheckRuleData, authorizedObjectApiName, authorizeAccountId, objectFieldApiName, mappingFieldApiName);
        if (hasUseFundAccount) {
            return true;
        }

        return false;
    }

    public boolean hasUseFundAccount(IObjectData accountCheckRuleData, String authorizedObjectApiName, String authorizeAccountId, String objectFieldApiName, String mappingFieldApiName) {
        String object = accountCheckRuleData.get(objectFieldApiName, String.class);
        if (!Objects.equals(object, authorizedObjectApiName)) {
            return false;
        }

        List<ObjectMappingModel> objectMappingModels = RuleHandlerUtil.getObjectMapping(accountCheckRuleData, mappingFieldApiName);
        return AccountCheckRuleMappingUtil.hasFundAccountId(objectMappingModels, authorizeAccountId);
    }

    /**
     * 返回校验规则包含的账户名称
     */
    public Map<String, List<String>> accountCheckRuleName2containFundAccountNames(List<IObjectData> accountCheckRuleDataList, List<IObjectData> fundAccounts) {
        if (CollectionUtils.isEmpty(accountCheckRuleDataList) || CollectionUtils.isEmpty(fundAccounts)) {
            return new HashMap<>();
        }

        Map<String, List<String>> accountCheckRuleName2containFundAccountNames = new HashMap<>();
        for (IObjectData accountCheckRule : accountCheckRuleDataList) {
            List<String> containFundAccountNames = getContainFundAccountNames(accountCheckRule, fundAccounts);
            if (!CollectionUtils.isEmpty(containFundAccountNames)) {
                accountCheckRuleName2containFundAccountNames.put(accountCheckRule.getName(), containFundAccountNames);
            }
        }
        return accountCheckRuleName2containFundAccountNames;
    }

    /**
     * 获取校验规则里面有的账户名称
     */
    public List<String> getContainFundAccountNames(IObjectData accountCheckRuleData, List<IObjectData> fundAccounts) {
        if (CollectionUtils.isEmpty(fundAccounts)) {
            return Lists.newArrayList();
        }

        List<String> containFundAccountIds = Lists.newArrayList();
        String ruleType = accountCheckRuleData.get(AccountCheckRuleConstants.Field.RuleType.apiName, String.class);
        if (Objects.equals(ruleType, AccountCheckRuleTypeEnum.Check_Reduce.getValue())) {
            String mappingFieldApiName = AccountCheckRuleConstants.Field.OccupiedMapping.apiName;
            List<String> hasFundAccountIds = getContainFundAccountIds(accountCheckRuleData, fundAccounts, mappingFieldApiName);
            containFundAccountIds = Lists.newArrayList(hasFundAccountIds);
        }

        String mappingFieldApiName = AccountCheckRuleConstants.Field.ReduceMapping.apiName;
        List<String> hasFundAccountIds = getContainFundAccountIds(accountCheckRuleData, fundAccounts, mappingFieldApiName);
        containFundAccountIds.addAll(hasFundAccountIds);

        if (CollectionUtils.isEmpty(containFundAccountIds)) {
            return Lists.newArrayList();
        }

        List<String> finalContainFundAccountIds = Lists.newArrayList(containFundAccountIds);
        return fundAccounts.stream().filter(d -> finalContainFundAccountIds.contains(d.getId())).map(IObjectData::getName).collect(Collectors.toList());
    }

    /**
     * 获取mappingFieldApiName里面包含的的账户id
     */
    private List<String> getContainFundAccountIds(IObjectData accountCheckRuleData, List<IObjectData> fundAccounts, String mappingFieldApiName) {
        List<ObjectMappingModel> objectMappings = RuleHandlerUtil.getObjectMapping(accountCheckRuleData, mappingFieldApiName);
        List<String> fundAccountIds = fundAccounts.stream().map(IObjectData::getId).collect(Collectors.toList());
        return AccountCheckRuleMappingUtil.hasFundAccountIds(objectMappings, fundAccountIds);
    }

    public String hasUseObjectApiNameAccountCheckRuleName(String tenantId, String authorizedObjectApiName) {
        List<String> fields = Lists.newArrayList(AccountCheckRuleConstants.Field.Name.apiName, AccountCheckRuleConstants.Field.RuleType.apiName,
                AccountCheckRuleConstants.Field.CheckObject.apiName, AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName);
        List<IObjectData> allAccountCheckRuleDatas = getAllAccountCheckRuleDatas(tenantId, false, fields);
        if (CollectionUtils.isEmpty(allAccountCheckRuleDatas)) {
            return null;
        }

        for (IObjectData accountCheckRuleData : allAccountCheckRuleDatas) {
            String ruleType = accountCheckRuleData.get(AccountCheckRuleConstants.Field.RuleType.apiName, String.class);
            String name = accountCheckRuleData.get(AccountCheckRuleConstants.Field.Name.apiName, String.class);

            if (Objects.equals(ruleType, AccountCheckRuleTypeEnum.Check_Reduce.getValue())) {
                //冻结
                String checkObject = accountCheckRuleData.get(AccountCheckRuleConstants.Field.CheckObject.apiName, String.class);
                if (Objects.equals(checkObject, authorizedObjectApiName)) {
                    return name;
                }
            }

            //扣减
            String reduceRelatedObject = accountCheckRuleData.get(AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName, String.class);
            if (Objects.equals(reduceRelatedObject, authorizedObjectApiName)) {
                return name;
            }
        }

        return null;
    }

    /**
     * 查出所有的校验规则，新建支出授权
     */
    public void transferCheckObjectAndReduceRelatedObjectToOutcomeAuth(String tenantId) {
        //查出所有的校验规则
        List<String> fields = Lists.newArrayList(SystemConstants.Field.Id.apiName, AccountCheckRuleConstants.Field.Name.apiName, AccountCheckRuleConstants.Field.RuleType.apiName,
                AccountCheckRuleConstants.Field.CheckObject.apiName, AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName,
                AccountCheckRuleConstants.Field.OccupiedMapping.apiName, AccountCheckRuleConstants.Field.ReduceMapping.apiName);
        List<IObjectData> allAccountCheckRuleDatas = getAllAccountCheckRuleDatas(tenantId, false, fields);
        if (CollectionUtils.isEmpty(allAccountCheckRuleDatas)) {
            return;
        }

        //新建支出授权
        fAccountAuthorizationManager.saveOrUpdateOutcomeAuthData(tenantId, allAccountCheckRuleDatas);
    }

    /**
     * 查出所有的校验规则，新建支出授权
     */
    public void transferMappingFieldForComponentReduceAccountCheckRule(String tenantId, boolean update) {
        List<IObjectData> allAccountCheckRuleDatas = getAllAccountCheckRuleDatas(tenantId, true, null);
        if (CollectionUtils.isEmpty(allAccountCheckRuleDatas)) {
            return;
        }

        //查出所有的已经初始化的支出授权

        for (IObjectData accountCheckRuleData : allAccountCheckRuleDatas) {
            String ruleType = accountCheckRuleData.get(AccountCheckRuleConstants.Field.RuleType.apiName, String.class);
            String status = accountCheckRuleData.get(AccountCheckRuleConstants.Field.Status.apiName, String.class);

            if (!Objects.equals(ruleType, AccountCheckRuleTypeEnum.Component_Reduce.getValue())) {
                continue;
            }
            if (Objects.equals(status, AccountCheckRuleStatusEnum.Off.getValue())) {
                continue;
            }

            updateExpenseAmountMappingField(tenantId, accountCheckRuleData, update);
        }
    }

    private void updateExpenseAmountMappingField(String tenantId, IObjectData componentReduceAccountCheckRuleData, boolean update) {
        String reduceRelatedObject = componentReduceAccountCheckRuleData.get(AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName, String.class);
        String name = componentReduceAccountCheckRuleData.getName();

        //查授权明细
        User admin = new User(tenantId, "-10000");
        List<IObjectData> authorizationDetailDatas = authorizationDetailManager.query(admin, FAccountAuthAuthorizedTypeEnum.Outcome.getValue(), reduceRelatedObject);
        if (CollectionUtils.isEmpty(authorizationDetailDatas)) {
            log.info("ruleUpdateExpenseAmountMappingField authorizationDetailManager.query empty tenantId[{}], reduceRelatedObject[{}]", tenantId, reduceRelatedObject);
            return;
        }

        Map<String, String> authorizeAccountId2TradeAmountFieldApiName = authorizationDetailManager.getAuthorizeAccountId2TradeAmountFieldApiName(authorizationDetailDatas);

        List<ObjectMappingModel> objectMappings = RuleHandlerUtil.getObjectMapping(componentReduceAccountCheckRuleData, AccountCheckRuleConstants.Field.ReduceMapping.apiName);
        if (CollectionUtils.isEmpty(objectMappings)) {
            return;
        }

        boolean needUpdate = false;
        for (ObjectMappingModel objectMapping : objectMappings) {
            List<FieldMappingModel> fieldMappings = objectMapping.getFieldMappingList();
            if (CollectionUtils.isEmpty(fieldMappings)) {
                continue;
            }

            for (FieldMappingModel fieldMapping : fieldMappings) {
                if (!Objects.equals(fieldMapping.getTargetFieldApiName(), AccountTransactionFlowConst.Field.ExpenseAmount.apiName)) {
                    continue;
                }

                String sourceFieldApiName = fieldMapping.getSourceFieldApiName();
                if (!Strings.isNullOrEmpty(sourceFieldApiName)) {
                    continue;
                }

                String tradeAmountFieldApiName = authorizeAccountId2TradeAmountFieldApiName.get(objectMapping.getFundAccountId());
                if (!Strings.isNullOrEmpty(tradeAmountFieldApiName)) {
                    fieldMapping.setSourceFieldApiName(tradeAmountFieldApiName);
                    needUpdate = true;
                    log.info("ruleUpdateExpenseAmountMappingField, needUpdate tenantId[{}], componentReduceAccountCheckRuleDataId[{}], componentReduceAccountCheckRuleDataName[{}]", tenantId, componentReduceAccountCheckRuleData.getId(), name);
                } else {
                    log.info("ruleUpdateExpenseAmountMappingField, sourceFieldApiName = null tenantId[{}], componentReduceAccountCheckRuleDataId[{}], componentReduceAccountCheckRuleDataName[{}]", tenantId, componentReduceAccountCheckRuleData.getId(), name);
                }
            }
        }

        if (!update) {
            return;
        }

        if (!needUpdate) {
            return;
        }

        String reduceMappingJson = JSONObject.toJSONString(objectMappings);
        log.info("ruleUpdateExpenseAmountMappingField tenantId[{}], name[{}], reduceMappingJson[{}]", tenantId, name, reduceMappingJson);
        componentReduceAccountCheckRuleData.set(AccountCheckRuleConstants.Field.ReduceMapping.apiName, reduceMappingJson);

        serviceFacade.updateObjectData(admin, componentReduceAccountCheckRuleData);
        log.info("ruleUpdateExpenseAmountMappingField end, tenantId[{}], componentReduceAccountCheckRuleDataId[{}], componentReduceAccountCheckRuleDataName[{}]", tenantId, componentReduceAccountCheckRuleData.getId(), name);
    }

    /**
     * 货补金额、货补数量、返利类型的账户
     * 直接扣减、校验扣减 : 不能
     * 组件扣减         ：能用
     * <p>
     * 信用账户
     * 直接扣减、校验扣减、组件扣减: 不能
     */
    public void checkNotUseFundAccount(String tenantId, IObjectData accountCheckRule) {
        String ruleType = accountCheckRule.get(AccountCheckRuleConstants.Field.RuleType.apiName, String.class);

        List<String> canNotUseAccountTypes = Lists.newArrayList();
        List<String> canNotUseAccessModules = Lists.newArrayList(AccessModuleEnum.CREDIT.value);
        if (Objects.equals(ruleType, AccountCheckRuleTypeEnum.Direct_Reduce.getValue())
                || Objects.equals(ruleType, AccountCheckRuleTypeEnum.Check_Reduce.getValue())) {
            canNotUseAccountTypes.add(FundAccountAccountTypeEnum.ReplenishmentAmount.value);
            canNotUseAccountTypes.add(FundAccountAccountTypeEnum.ReplenishmentQuantity.value);

            canNotUseAccessModules.add(AccessModuleEnum.REBATE.value);
        }

        List<IObjectData> cannotUseFundAccounts = fundAccountManager.getAccountsByAccountTypesOrAccessModules(tenantId, canNotUseAccountTypes, canNotUseAccessModules);
        if (CollectionUtils.isEmpty(cannotUseFundAccounts)) {
            return;
        }

        if (Objects.equals(ruleType, AccountCheckRuleTypeEnum.Check_Reduce.getValue())) {
            String mappingFieldApiName = AccountCheckRuleConstants.Field.OccupiedMapping.apiName;
            checkNotUseFundAccount(tenantId, accountCheckRule, cannotUseFundAccounts, mappingFieldApiName);
        }

        String mappingFieldApiName = AccountCheckRuleConstants.Field.ReduceMapping.apiName;
        checkNotUseFundAccount(tenantId, accountCheckRule, cannotUseFundAccounts, mappingFieldApiName);
    }

    public void checkNotUseFundAccount(String tenantId, IObjectData accountCheckRule, List<IObjectData> replenishmentAccounts, String mappingFieldApiName) {
        List<String> hasFundAccountIds = getContainFundAccountIds(accountCheckRule, replenishmentAccounts, mappingFieldApiName);
        if (!CollectionUtils.isEmpty(hasFundAccountIds)) {
            Optional<String> fundAccountNameOption = replenishmentAccounts.stream().filter(d -> hasFundAccountIds.contains(d.getId())).map(IObjectData::getName).findFirst();
            String fundAccountName = fundAccountNameOption.orElse("");
            log.warn("checkNotUseFundAccount tenantId[{}], hasFundAccountIds[{}], fundAccountName[{}], accountCheckRuleId[{}]", tenantId, hasFundAccountIds, fundAccountName, accountCheckRule.getId());
            throw new ValidateException(I18N.text(CAI18NKey.CAN_NOT_USE_ACCOUNT, fundAccountName));
        }
    }

    /**
     * 【组件扣减】的【校验规则】
     * sourceComponentReduceAccountCheckRuleData 复制到 targetTenantId
     * <p>
     * 需要的字段，参考 AccountCheckRuleManager#saveComponentReduceAccountCheckRule
     */
    public void copy(String targetTenantId, IObjectData sourceComponentReduceAccountCheckRuleData, boolean sameId, String newReduceMappingJson) {
        User admin = User.systemUser(targetTenantId);
        IObjectData accountCheckRule = ObjectDataUtil.getBaseObjectData(admin, AccountCheckRuleConstants.API_NAME);

        boolean idHasUse = commonObjDataManager.idHasUse(targetTenantId, AccountCheckRuleConstants.API_NAME, sourceComponentReduceAccountCheckRuleData.getId());
        if (sameId && !idHasUse) {
            accountCheckRule.set(SystemConstants.Field.Id.apiName, sourceComponentReduceAccountCheckRuleData.getId());
        }
        accountCheckRule.set(AccountCheckRuleConstants.Field.Name.apiName, sourceComponentReduceAccountCheckRuleData.getName());

        accountCheckRule.set(AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName, sourceComponentReduceAccountCheckRuleData.get(AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName));
        accountCheckRule.set(AccountCheckRuleConstants.Field.ReduceTriggerAction.apiName, sourceComponentReduceAccountCheckRuleData.get(AccountCheckRuleConstants.Field.ReduceTriggerAction.apiName));
        accountCheckRule.set(AccountCheckRuleConstants.Field.ReduceTriggerButton.apiName, sourceComponentReduceAccountCheckRuleData.get(AccountCheckRuleConstants.Field.ReduceTriggerButton.apiName));
        accountCheckRule.set(AccountCheckRuleConstants.Field.ReduceMapping.apiName, newReduceMappingJson);

        accountCheckRule.set(AccountCheckRuleConstants.Field.Status.apiName, sourceComponentReduceAccountCheckRuleData.get(AccountCheckRuleConstants.Field.Status.apiName));
        accountCheckRule.set(AccountCheckRuleConstants.Field.Priority.apiName, sourceComponentReduceAccountCheckRuleData.get(AccountCheckRuleConstants.Field.Priority.apiName));
        accountCheckRule.set(AccountCheckRuleConstants.Field.Specification.apiName, sourceComponentReduceAccountCheckRuleData.get(AccountCheckRuleConstants.Field.Specification.apiName));
        accountCheckRule.set(AccountCheckRuleConstants.Field.RuleType.apiName, sourceComponentReduceAccountCheckRuleData.get(AccountCheckRuleConstants.Field.RuleType.apiName));

        //不需要
        //  accountCheckRule.set(AccountCheckRuleConstants.Field.ReconciliationRuleIdJson.apiName, sourceComponentReduceAccountCheckRuleData.get(AccountCheckRuleConstants.Field.ReconciliationRuleIdJson.apiName));

        accountCheckRule.set("record_type", sourceComponentReduceAccountCheckRuleData.getRecordType());

        try {
            serviceFacade.saveObjectData(admin, accountCheckRule);
        } catch (Exception e) {
            log.warn("serviceFacade.saveObjectData fail admin[{}], accountCheckRule[{}]", admin, accountCheckRule, e);
            throw e;
        }
    }
}