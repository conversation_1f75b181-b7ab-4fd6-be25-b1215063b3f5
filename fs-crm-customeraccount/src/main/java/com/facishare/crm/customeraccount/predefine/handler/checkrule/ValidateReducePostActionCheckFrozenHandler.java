package com.facishare.crm.customeraccount.predefine.handler.checkrule;

import com.facishare.crm.customeraccount.constants.AccountFrozenRecordConstant;
import com.facishare.crm.customeraccount.constants.AccountRuleUseRecordConstants;
import com.facishare.crm.customeraccount.constants.NewCustomerAccountConstants;
import com.facishare.crm.customeraccount.enums.StageEnum;
import com.facishare.crm.customeraccount.predefine.handler.RuleHandlerUtil;
import com.facishare.crm.customeraccount.predefine.manager.AccountCheckManager;
import com.facishare.crm.customeraccount.predefine.manager.NewCustomerAccountManager;
import com.facishare.crm.customeraccount.util.ExecuteUtil;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ObjectDataNotFoundException;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class ValidateReducePostActionCheckFrozenHandler extends CheckRuleHandler<ValidateReducePostActionCheckFrozenHandler.Arg, HandlerResult> {
    @Autowired
    private NewCustomerAccountManager newCustomerAccountManager;
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private AccountCheckManager accountCheckManager;

    @Override
    public HandlerTypeEnum getHandlerTypeEnum() {
        return HandlerTypeEnum.ValidateReducePostActionCheckFrozen;
    }

    @Override
    protected HandlerResult doHandle(Arg arg) {
        User user = arg.getUser();
        String objectApiName = arg.getObjectApiName();
        String objectDataId = arg.getObjectDataId();
        IObjectData accountRuleUseRecordData = arg.getAccountRuleUseRecordData();
        String checkRuleId = accountRuleUseRecordData.get(AccountRuleUseRecordConstants.Field.CheckRuleId.apiName, String.class);
        List<IObjectData> accountFrozenRecordList = accountCheckManager.queryFrozenRecordDataList(user, checkRuleId, objectApiName, objectDataId);
        if (RuleHandlerUtil.isChargedOff(accountFrozenRecordList)) {
            return HandlerResult.builder().build();
        }
        IObjectData objectData = null;
        try {
            objectData = serviceFacade.findObjectData(user, objectDataId, objectApiName);
        } catch (ObjectDataNotFoundException e) {
            log.warn("user:{},objectApiName:{},objectDataId:{},data not exist", user, objectApiName, objectDataId, e);
        }
        String buttonApiName = arg.getButtonApiName();
        if (Objects.isNull(objectData) || ObjectDataExt.of(objectData).isIneffective() || ObjectDataExt.of(objectData).isInvalid()) {
            List<String> customerAccountIds = Lists.newArrayList();
            Map<String, Map<String, Object>> customerAccountColumnMap = Maps.newHashMap();
            for (IObjectData accountFrozenRecordData : accountFrozenRecordList) {
                String customerAccountId = accountFrozenRecordData.get(AccountFrozenRecordConstant.Field.CustomerAccountId.apiName, String.class);
                customerAccountIds.add(customerAccountId);
                BigDecimal frozenAmount = accountFrozenRecordData.get(AccountFrozenRecordConstant.Field.FreezeAmount.apiName, BigDecimal.class, BigDecimal.ZERO);
                Map<String, Object> fieldMap = customerAccountColumnMap.computeIfAbsent(customerAccountId, k -> Maps.newHashMap());
                fieldMap.put(NewCustomerAccountConstants.Field.OccupiedAmount.apiName, frozenAmount.negate());
                fieldMap.put(NewCustomerAccountConstants.Field.AvailableBalance.apiName, frozenAmount);
            }
            List<IObjectData> customerAccountDataList = serviceFacade.findObjectDataByIds(user.getTenantId(), customerAccountIds, NewCustomerAccountConstants.API_NAME);
            List<String> updateFields = Lists.newArrayList(NewCustomerAccountConstants.Field.OccupiedAmount.apiName, NewCustomerAccountConstants.Field.AvailableBalance.apiName);

            List<IObjectData> updatedCustomerAccountList = Lists.newArrayList();
            ExecuteUtil.execute(() -> {
                serviceFacade.bulkInvalid(accountFrozenRecordList, user);
                serviceFacade.batchUpdateByFields(user, customerAccountDataList, updateFields, customerAccountColumnMap);
                serviceFacade.bulkDeleteWithInternalDescribe(Lists.newArrayList(accountRuleUseRecordData), user);
                newCustomerAccountManager.addLedgerData(user, customerAccountColumnMap,false);
                return null;
            });
            //记录日志
            ParallelUtils.ParallelTask parallelTask = ParallelUtils.createBackgroundTask();
            parallelTask.submit(() -> {
                newCustomerAccountManager.logRecord(user, ActionType.Invalid, accountFrozenRecordList);
                newCustomerAccountManager.logModifyRecord(user, NewCustomerAccountConstants.API_NAME, customerAccountDataList, updatedCustomerAccountList, customerAccountColumnMap);
            });
            parallelTask.run();
        } else if (!StringUtils.isEmpty(buttonApiName)) {
            //post action才触发；MQ补偿时，buttonApiName为空
            newCustomerAccountManager.triggerFunctionByInner(user, buttonApiName, accountFrozenRecordList, StageEnum.POST);
        }

        return HandlerResult.builder().processed(true).build();
    }

    @EqualsAndHashCode(callSuper = true)
    public static class Arg extends HandlerArg {
        @Getter
        private final IObjectData accountRuleUseRecordData;
        @Getter
        private final String buttonApiName;
        private final String describeApiName;
        private final String dataId;

        @Override
        public String getObjectApiName() {
            return this.describeApiName;
        }

        @Override
        public String getObjectDataId() {
            return this.dataId;
        }

        public Arg(RequestContext requestContext, String buttonApiName, String objectApiName, String objectDataId, IObjectData accountRuleUseRecordData) {
            super(requestContext, null);
            this.buttonApiName = buttonApiName;
            this.describeApiName = objectApiName;
            this.dataId = objectDataId;
            this.accountRuleUseRecordData = accountRuleUseRecordData;
        }
    }
}
