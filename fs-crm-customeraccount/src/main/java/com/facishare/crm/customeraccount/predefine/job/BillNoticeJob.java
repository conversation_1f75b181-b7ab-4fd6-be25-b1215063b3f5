package com.facishare.crm.customeraccount.predefine.job;

import com.facishare.crm.customeraccount.predefine.manager.BillJobManager;
import com.facishare.crm.customeraccount.util.ConfigCenter;
import com.facishare.crm.customeraccount.util.DateUtil;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import java.util.Date;
import java.util.List;

@Slf4j
@Deprecated
public class BillNoticeJob implements Job {
    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        log.info("start billNoticeJob");
        BillJobManager billJobManager = SpringUtil.getContext().getBean(BillJobManager.class);
        String ea = ConfigCenter.billNoticeEa;
        String tenantId = ConfigCenter.billNoticeTenantId;
        String appId = ConfigCenter.billNoticeAppId;
        List<Integer> toUserList = ConfigCenter.billNoticeToUserList;
        Date yesterdayDate = DateUtil.getYesterdayDate(new Date());
        if (StringUtils.isEmpty(ea) || StringUtils.isEmpty(tenantId) || StringUtils.isEmpty(appId) || CollectionUtils.isEmpty(toUserList)) {
            log.warn("billNotice config is empty,ea:{} tenantId:{},appId:{},toUserList:{}", ea, tenantId, appId, toUserList);
            return;
        }
        billJobManager.sendDailyMessage(tenantId, ea, appId, toUserList, yesterdayDate);
    }
}
