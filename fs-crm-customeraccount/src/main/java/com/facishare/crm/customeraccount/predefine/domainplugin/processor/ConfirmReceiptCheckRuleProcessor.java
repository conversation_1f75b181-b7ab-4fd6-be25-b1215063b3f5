package com.facishare.crm.customeraccount.predefine.domainplugin.processor;

import com.facishare.crm.customeraccount.enums.AccountCheckRuleTypeEnum;
import com.facishare.crm.customeraccount.enums.ReduceTriggerActionEnum;
import com.facishare.crm.customeraccount.mq.producer.MQProducerManager;
import com.facishare.crm.customeraccount.predefine.domainplugin.model.CheckRuleConfirmReceiptContextModel;
import com.facishare.crm.customeraccount.predefine.domainplugin.model.UnfreezeRuleTriggerTypeEnum;
import com.facishare.crm.customeraccount.predefine.manager.AccountCheckManager;
import com.facishare.crm.customeraccount.predefine.manager.NewCustomerAccountManager;
import com.facishare.crm.manufacturing.common.mode.ConfirmReceiptActionDomainPlugin;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.cache.RedissonServiceImpl;
import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.transaction.tcc.api.context.BranchTransactionalContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class ConfirmReceiptCheckRuleProcessor extends AbstractCheckRuleProcessor<ConfirmReceiptActionDomainPlugin.Arg, ConfirmReceiptActionDomainPlugin.Result, CheckRuleConfirmReceiptContextModel> {
    protected ConfirmReceiptCheckRuleProcessor(AccountCheckManager accountCheckManager, NewCustomerAccountManager newCustomerAccountManager, MQProducerManager mqProducerManager, RedissonServiceImpl redissonService) {
        super(accountCheckManager, newCustomerAccountManager, mqProducerManager, redissonService);
    }

    @Override
    public ConfirmReceiptActionDomainPlugin.Result newResultInstance() {
        return new ConfirmReceiptActionDomainPlugin.Result();
    }

    @Override
    public CheckRulePluginContextKey getContextKeyEnum() {
        return CheckRulePluginContextKey.ConfirmReceipt;
    }

    @Override
    public ObjectDataDocument getObjectDataDocument(ConfirmReceiptActionDomainPlugin.Arg arg) {
        return arg.getObjectData();
    }

    @Override
    public Class<CheckRuleConfirmReceiptContextModel> getContextClass() {
        return CheckRuleConfirmReceiptContextModel.class;
    }

    @Override
    public CheckRuleConfirmReceiptContextModel doPreAct(RequestContext requestContext, ConfirmReceiptActionDomainPlugin.Arg arg) {
        IObjectData objectData = arg.getObjectData().toObjectData();
        return doPreActByAction(requestContext, objectData, false, true).toConfirmReceiptContext();
    }

    @Override
    protected IObjectData getMatchedFrozenRule(User user, IObjectDescribe objectDescribe, Map<String, Object> dataMap, List<IObjectData> checkRuleList) {
        List<IObjectData> frozenRuleByButtonList = AccountCheckManager.removeNotMatchedRuleByTypeAndButton(checkRuleList, AccountCheckRuleTypeEnum.Check_Reduce, ReduceTriggerActionEnum.Button, ObjectAction.CONFIRM_RECEIPT.getButtonApiName());
        return accountCheckManager.getMatchedCheckReduceRule(user, objectDescribe, dataMap, frozenRuleByButtonList, false).orElseGet(() -> {
            List<IObjectData> frozenRuleByFieldChangeList = AccountCheckManager.removeNotMatchedRuleByTypeAndButton(checkRuleList, AccountCheckRuleTypeEnum.Check_Reduce, ReduceTriggerActionEnum.FieldChange, null);
            return accountCheckManager.getMatchedCheckReduceRule(user, objectDescribe, dataMap, frozenRuleByFieldChangeList, false).orElse(null);
        });
    }

    @Override
    protected IObjectData getMatchedDirectReduceRule(User user, IObjectDescribe objectDescribe, Map<String, Object> dataMap, List<IObjectData> checkRuleList) {
        List<IObjectData> directReduceRuleByButtonList = AccountCheckManager.removeNotMatchedRuleByTypeAndButton(checkRuleList, AccountCheckRuleTypeEnum.Direct_Reduce, ReduceTriggerActionEnum.Button, ObjectAction.CONFIRM_RECEIPT.getButtonApiName());
        return accountCheckManager.getMatchedDirectReduceRule(user, objectDescribe, dataMap, directReduceRuleByButtonList).orElseGet(() -> {
            List<IObjectData> directReduceRuleByFieldChangeList = AccountCheckManager.removeNotMatchedRuleByTypeAndButton(checkRuleList, AccountCheckRuleTypeEnum.Direct_Reduce, ReduceTriggerActionEnum.FieldChange, null);
            return accountCheckManager.getMatchedDirectReduceRule(user, objectDescribe, dataMap, directReduceRuleByFieldChangeList).orElse(null);
        });
    }

    @Override
    public void doFinallyDo(RequestContext requestContext, ConfirmReceiptActionDomainPlugin.Arg arg) {
        boolean doActComplete = arg.isDoActComplete();
        IObjectData objectData = arg.getObjectData().toObjectData();
        CheckRuleConfirmReceiptContextModel contextModel = getContextModel(arg);
        if (doActComplete) {
            doActCompleteTrueByAction(requestContext, objectData, contextModel.toCommonContextModel(), null);
        } else {
            doActCompleteFalseByAction(requestContext, objectData, contextModel.toCommonContextModel());
        }
    }

    @Override
    protected UnfreezeRuleTriggerTypeEnum matchUnfreezeMark() {
        return UnfreezeRuleTriggerTypeEnum.ConfirmReceiptThenFieldChangeTrigger;
    }

    //TODO 需要ConfirmReceipt支持handler模式
    @Override
    public void doConfirm(BranchTransactionalContext branchContext, RequestContext requestContext, ConfirmReceiptActionDomainPlugin.Arg arg, CheckRuleConfirmReceiptContextModel contextModel) {
        IObjectData objectData = arg.getObjectData().toObjectData();
        doActCompleteTrueByAction(requestContext, objectData, contextModel.toCommonContextModel(), null);
    }

    @Override
    public void doCancel(BranchTransactionalContext branchContext, RequestContext requestContext, ConfirmReceiptActionDomainPlugin.Arg arg, CheckRuleConfirmReceiptContextModel contextModel) {
        IObjectData objectData = arg.getObjectData().toObjectData();
        doActCompleteFalseByAction(requestContext, objectData, contextModel.toCommonContextModel());
    }
}
