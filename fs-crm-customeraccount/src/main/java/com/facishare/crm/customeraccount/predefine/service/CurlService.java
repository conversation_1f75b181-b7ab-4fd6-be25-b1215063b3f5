package com.facishare.crm.customeraccount.predefine.service;

import com.facishare.crm.customeraccount.predefine.service.dto.*;
import com.facishare.crm.customeraccount.predefine.service.impl.CurlServiceImpl;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.metadata.api.IUdefButton;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeoutException;

@ServiceModule("customer_account_curl")
public interface CurlService {
    @ServiceMethod("ping")
    String ping();

    @ServiceMethod("deleteCustomerAccounts")
    CurlModel.CusAccIds deleteCustomerAccounts(CurlModel.DeleteCusAccArgs accArgs, ServiceContext serviceContext);

    @ServiceMethod("enable_customer_account")
    EmptyResult enableCustomerAccountByCurl(CurlModel.TenantIds arg, ServiceContext serviceContext);

    @ServiceMethod("query_plain_customer")
    CurlModel.QueryCustomerResult queryCustomersFromPg(CurlModel.QueryCustomerArg arg, ServiceContext serviceContext);

    @ServiceMethod("fix_customer_account_status")
    CurlModel.FixCustomerAccountLifeStatusResult fixCustomerAccountLifeStatus(CurlModel.FixCustomerAccountLifeStatusArg arg, ServiceContext serviceContext);

    @ServiceMethod("fix_prepay_layout_record_Type")
    EmptyResult initPrepayLayoutRecordType(ServiceContext serviceContext);

    @ServiceMethod("fix_init_approval")
    EmptyResult initApproval(CurlModel.ObjectApiNameArg arg, ServiceContext serviceContext);

    @ServiceMethod("fix_in_out_come_select_one")
    CurlModel.TenantIds updateSelectOneFieldDescribe(CurlModel.TenantIds tenantIdArg, ServiceContext serviceContext);

    @ServiceMethod("fix_select_one_field")
    CurlModel.FixSelectOneFieldResult fixSelectOneFieldDescribe(CurlModel.FixSelectOneFieldArg arg, ServiceContext serviceContext);

    @ServiceMethod("fix_target_related_list_label")
    EmptyResult fixRebateOutcomeTargetListLabel(ServiceContext serviceContext, CurlModel.FixSelectOneFieldArg arg);

    @ServiceMethod("update_agent_type")
    EmptyResult updateLayout(CurlModel.UpdateLayoutArg arg, ServiceContext serviceContext);

    @ServiceMethod("list_customer_status_before_invalid")
    CurlModel.CustomerStatusBeforeInvalidResult listCustomerStatusBeforeInvalid(CurlModel.CustomerStatusBeforeInvalidArg arg, ServiceContext serviceContext);

    @ServiceMethod("init_lacked_customer_account_data")
    CurlModel.LackCustomerAccountInitResult initLackedCustomerAccountData(CurlModel.TenantIds arg, ServiceContext serviceContext);

    @ServiceMethod("tenantIds_lack_customer_account_data")
    CurlModel.TenantIds listTenantIdsOfLackCustomerAccountDatas(CurlModel.TenantIds arg, ServiceContext serviceContext);

    @ServiceMethod("add_order_payment_field")
    CurlModel.AddOrderPaymentFieldResult addOrderPaymentField(CurlModel.AddOrderPaymentFieldArg arg, ServiceContext serviceContext);

    @ServiceMethod("add_payment_field")
    CurlModel.AddOrderPaymentFieldResult addPaymentField(CurlModel.AddOrderPaymentFieldArg arg, ServiceContext serviceContext);

    @ServiceMethod("del_payment_field")
    CurlModel.DelPaymentFieldResult delPaymentField(ServiceContext serviceContext);

    @ServiceMethod("add_import_functin_privilege_to_role")
    CurlModel.AddImportPrivilegeResult addImportFunctionPrivilegeToRole(CurlModel.AddImportPrivilegeArg arg);

    @ServiceMethod("del_import_functin_privilege")
    CurlModel.AddImportPrivilegeResult delImportFunctionPrivilege(CurlModel.DelImportPrivilegeArg arg);

    @ServiceMethod("del_import_functin_privilege_to_role")
    CurlModel.AddImportPrivilegeResult delImportFunctionPrivilegeToRole(CurlModel.AddImportPrivilegeArg arg1);

    @ServiceMethod("fix_rebate_income_list_layout")
    CurlModel.ListLayoutResult fixRebateIncomeListLayout(CurlModel.TenantIds tenantIdArg, ServiceContext serviceContext);

    @ServiceMethod("fix_rebate_income_label_and_transaction_time")
    EmptyResult fixRebateIncomeStartEndTimeLabelAndTransactionTime(CurlModel.TenantIds tenantIdArg, ServiceContext serviceContext);

    @ServiceMethod("init_rebate_use_rule")
    CurlServiceImpl.TenantIdModel.Result initRebateUseRule(CurlServiceImpl.TenantIdModel.Arg arg, ServiceContext serviceContext);

    @ServiceMethod("add_sales_order_and_rebate_use_rule_field")
    CurlServiceImpl.TenantIdModel.Result addSalesOrderAndRebateUseRuleField(CurlServiceImpl.TenantIdModel.Arg arg, ServiceContext serviceContext);

    @ServiceMethod("fix_customer_account_relate_balance")
    EmptyResult fixCustomerAccountRelateBalance(CurlModel.FixCustomerAccountBalanceArg arg, ServiceContext serviceContext);

    @ServiceMethod("find_object_describe_by_api_name")
    CurlModel.FixSelectOneFieldResult findDescribeByApiName(CurlModel.FixSelectOneFieldArg arg, ServiceContext serviceContext);

    @ServiceMethod("addSelectOptionInRebateIncomeType")
    CurlServiceImpl.TenantIdModel.Result addSelectOptionInRebateIncomeType(CurlServiceImpl.TenantIdModel.Arg arg, ServiceContext serviceContext);

    @ServiceMethod("addRebateUseRuleFieldInRebateOutcome")
    CurlServiceImpl.TenantIdModel.Result addRebateUseRuleFieldInRebateOutcome(CurlServiceImpl.TenantIdModel.Arg arg, ServiceContext serviceContext);

    @ServiceMethod("revisePrepayField")
    CurlModel.RevisePrepayFieldResult revisePrepayField(ServiceContext serviceContext, CurlModel.RevisePrepayFieldArg revisePrepayFieldArg);

    @ServiceMethod("clearTenantDataOfCustomerAccount")
    CurlModel.RevisePrepayFieldResult clearTenantDataOfCustomerAccount(ServiceContext serviceContext);

    @ServiceMethod("enableAssignPrepayRecordType")
    CurlServiceImpl.TenantIdModel.Result enableAssignPrepayRecordType(ServiceContext serviceContext, CurlServiceImpl.TenantIdModel.Arg arg);

    @ServiceMethod("enableAddField")
    CurlServiceImpl.TenantIdModel.Result enableAddField(ServiceContext serviceContext, CurlServiceImpl.TenantIdModel.Arg arg);

    @ServiceMethod("reviseCustomerAccountPrepayBalance")
    Boolean reviseCustomerAccountPrepayBalance(ServiceContext serviceContext, CurlModel.ReviseCustomerAccountAmountArg arg);

    @ServiceMethod("initCreditFile")
    CurlModel.TenantIds initCreditFile(ServiceContext serviceContext, CurlModel.TenantIds tenantIds);

    @ServiceMethod("addCustomerAccountFields")
    CurlModel.TenantIds addCustomerAccountFields(ServiceContext serviceContext, CurlModel.TenantIds tenantIds);

    @ServiceMethod("prepayAssignLayout")
    CurlServiceImpl.TenantIdModel.Result prepayAssingLayout(ServiceContext serviceContext, CurlServiceImpl.TenantIdModel.Arg arg);

    @ServiceMethod("getAllTenantIdsOfCustomerAccountEnabled")
    CurlModel.TenantIds getAllTenantIdsOfCustomerAccountEnabled(ServiceContext serviceContext);

    @ServiceMethod("transferCreditData")
    CurlModel.TenantIds transferCreditData(ServiceContext serviceContext, CurlModel.TenantIds tenantIds);

    @ServiceMethod("initCreditFileDescribeAndData")
    CurlModel.TenantIds initCreditFileDescribeAndData(ServiceContext serviceContext, CurlModel.TenantIds tenantIds);

    /**
     * 手动触发执行job,如果传了时间则以此时间区间为准，不然以上一天的时间为准
     *
     * @param serviceContext
     * @param type
     * @return
     */
    @ServiceMethod("triggerCustomerAccountCheckJob")
    Boolean triggerCustomerAccountCheckJob(ServiceContext serviceContext, CurlModel.Type type);

    @ServiceMethod("effectRebateIncome")
    EffectRebateIncomeModel.Result effectRebateIncome(ServiceContext serviceContext, EffectRebateIncomeModel.Arg arg);

    @ServiceMethod("triggerJobByApiName")
    Boolean triggerJob(ServiceContext serviceContext, CurlModel.Type type);

    /**
     * 修复定时任务中止期间脏数据，时间起止范围为闭区间[startTime, endTime]
     *
     * @param serviceContext
     * @param type
     * @return
     */
    @ServiceMethod("triggerCompensationDataJob")
    Boolean triggerCompensationDataJob(ServiceContext serviceContext, CurlModel.Type type);

    @ServiceMethod("init_top_info")
    Map<String, List<String>> initTopInfo(ServiceContext serviceContext, CurlModel.TenantIds arg);

    /**
     * 对比信用、返利支出、返利收入、预存款对象与流水
     *
     * @param condition tenantIds  必填，可支持多个  type 选填，字符串 "1" 信用比对 "2" 返利支出比对 "3" 返利收入比对  "4" 预存款比对
     *                  startDate 选填，开始日期 如："2018-01-01"，endDate 选填，结束日期 如："2018-10-11"
     * @return
     */
    @ServiceMethod("compareObjectAndFlowAmount")
    Boolean compareObjectAndFlowAmount(CurlModel.Condition condition);

    /**
     * 检查未回款金额超出客户账户可用余额以及回款金额与订单金额不对应的有效预付订单
     *
     * @param serviceContext
     * @param arg
     * @return
     */
    @ServiceMethod("checkPrepaidSalesOrderAndPayment")
    Boolean checkPrepaidSalesOrderAndPayment(ServiceContext serviceContext, CurlModel.TenantIds arg);


    /***
     * 创建校验和删除功能权限
     * */
    @ServiceMethod("createInvalidAndDeleteFunction")
    EmptyResult createInvalidAndDeleteFunction(ServiceContext serviceContext, CurlModel.Arg arg);


    @ServiceMethod("fix_payment_prepay_life_status")
    EmptyResult fixPaymentPrepayLifeStatus(ServiceContext serviceContext, CurlModel.FixPaymentPrepayLifeStatusArg arg);

    /**
     * 清理客户账户模块所有对象数据， 包括客户账户，预存款，返利，返利支出，返利使用规则以及信用; PS: 流水记录未删除
     *
     * @param serviceContext
     * @param arg
     * @return
     */
    @ServiceMethod("purge_customer_account_module")
    EmptyResult purgeCustomerAccount(ServiceContext serviceContext, CurlModel.TenantIds arg);

    @ServiceMethod("fix_prepay_by_standard_import")
    CurlModel.TenantIds fixPrepayByStandardImport(ServiceContext serviceContext, CurlModel.FixPrepayByStandardImportArg tenantIds);

    @ServiceMethod("upgrade_check_rule")
    CurlServiceImpl.TenantIdModel.Result upgradeAccountCheckRule(ServiceContext serviceContext, CurlServiceImpl.TenantIdModel.Arg arg);

    /**
     * 新客户账户，补上（作废、删除）权限, “回款财务”和CRM管理员角色加上这两个权限
     */
    @ServiceMethod("new_customer_account_add_privilege")
    CurlServiceImpl.TenantIdModel.Result newCustomerAccountAddPrivilege(ServiceContext serviceContext, CurlServiceImpl.TenantIdModel.Arg arg);

    /**
     * 账户冻结记录，去掉 角色和对象的（新建）权限
     */
    @ServiceMethod("account_frozen_record_delete_create_privilege")
    CurlServiceImpl.TenantIdModel.Result accountFrozenRecordDeleteCreatePrivilege(ServiceContext serviceContext, CurlServiceImpl.TenantIdModel.Arg arg);

    /**
     * 解冻明细，去掉 角色和对象的（新建）权限
     */
    @ServiceMethod("unfreeze_detail_delete_create_privilege")
    CurlServiceImpl.TenantIdModel.Result unfreezeDetailDeleteCreatePrivilege(ServiceContext serviceContext, CurlServiceImpl.TenantIdModel.Arg arg);


    @ServiceMethod("transaction_flow_field_rename")
    CurlServiceImpl.TenantIdModel.Result flowRenameField(ServiceContext serviceContext, CurlServiceImpl.TenantIdModel.Arg arg);

    @ServiceMethod("fix_payment_flow_data")
    CurlServiceImpl.TenantIdModel.Result fixPaymentFlow(ServiceContext serviceContext, CurlServiceImpl.TenantIdModel.Arg arg);

    /**
     * what字段 加上 used_in:"component"
     */
    @ServiceMethod("what_field_add_used_in")
    CurlServiceImpl.TenantIdModel.Result whatFieldAddUsedIn(ServiceContext serviceContext, CurlServiceImpl.TenantIdModel.Arg arg);

    /**
     * 账户校验规则：check_trigger_action -> fieldChange,button
     * check_trigger_button -> 原check_trigger_action按钮ApiName
     */
    @ServiceMethod("check_rule_refresh_field")
    CurlServiceImpl.TenantIdModel.Result CheckRuleRefreshField(ServiceContext serviceContext, CurlServiceImpl.TenantIdModel.Arg arg);

    /**
     * 账户校验规则描述添加check_trigger_button字段
     */
    @ServiceMethod("check_rule_add_field")
    CurlServiceImpl.TenantIdModel.Result CheckRuleAddField(ServiceContext serviceContext, CurlServiceImpl.TenantIdModel.Arg arg);

    @ServiceMethod("transfer_rule_use_record")
    CurlServiceImpl.TenantIdModel.Result transferRuleUseRecord(ServiceContext serviceContext, CurlServiceImpl.TenantIdModel.Arg arg);

    @ServiceMethod("change_enter_payment_button_filter")
    CurlServiceImpl.TenantIdModel.Result changeEnterPaymentButtonFilter(ServiceContext serviceContext, CurlServiceImpl.TenantIdModel.Arg arg);

    @ServiceMethod("query_config")
    QueryConfigModel.Result queryConfig(ServiceContext serviceContext, QueryConfigModel.Arg arg);

    /**
     * 得到开启【回款入账的企业】
     */
    @ServiceMethod("get_open_payment_enter_account_tenant")
    CurlServiceImpl.TenantIdModel.Result getOpenPaymentEnterAccountTenant(ServiceContext serviceContext, CurlServiceImpl.TenantIdModel.Arg arg);

    /**
     * 得到按钮信息
     */
    @ServiceMethod("get_button_info")
    List<IUdefButton> getButtonInfo(ServiceContext serviceContext, CurlServiceImpl.TenantIdModel.Arg arg);

    @ServiceMethod("update_field_max_length")
    CurlServiceImpl.TenantIdModel.Result updateFieldMaxLength(ServiceContext serviceContext, CurlServiceImpl.TenantIdModel.Arg arg);

    @ServiceMethod("init_customer_account_button")
    Map<String, Object> initCustomerAccountButton(ServiceContext serviceContext, CurlModel.InitCustomerAccountButton arg);

    @ServiceMethod("deleteFunc")
    Map<String, Object> deleteFunc(ServiceContext serviceContext, CurlModel.DeleteFuncCodeArg arg);

    @ServiceMethod("chargeOffUpdateDescribe")
    CurlServiceImpl.TenantIdModel.Result chargeOffUpdateFieldDescribe(ServiceContext serviceContext, CurlServiceImpl.TenantIdModel.Arg arg);

    @ServiceMethod("addBizActionByCheckRule")
    CurlServiceImpl.TenantIdModel.Result addBizActionByCheckRule(ServiceContext serviceContext, Map<String,String> arg);

    @ServiceMethod("queryFrozenRecordWithoutUseRecord")
    Map<String, Object> queryFrozenRecordWithoutUseRecord(ServiceContext serviceContext, CurlModel.FrozenRecordWithoutRuleUseRecord arg);

    @ServiceMethod("triggerAccountCheckRuleReduce")
    Map<String, Object> triggerAccountCheckRuleReduce(ServiceContext serviceContext, CurlModel.FrozenRecordWithoutRuleUseRecord arg);

    /**
     * 找到ruleCode有2条有效rule_rule_group的校验规则
     */
    @ServiceMethod("getAccountCheckRuleDirtyData")
    CurlModel.GetAccountCheckRuleDirtyDataResult getAccountCheckRuleDirtyData(ServiceContext serviceContext, CurlModel.GetAccountCheckRuleDirtyDataArg arg);

    /**
     * '校验规则'生成新的rule_code
     */
    @ServiceMethod("updateAccountCheckRuleRuleCode")
    CurlModel.UpdateAccountCheckRuleRuleCodeResult updateAccountCheckRuleRuleCode(CurlModel.UpdateAccountCheckRuleRuleCodeArg arg);

    /**
     * '校验规则使用记录account_rule_use_record'生成新的rule_code
     */
    @ServiceMethod("updateAccountRuleUseRecordRuleCode")
    CurlModel.UpdateAccountRuleUseRecordRuleCodeResult updateAccountRuleUseRecordRuleCode(CurlModel.UpdateAccountRuleUseRecordRuleCodeArg arg);

    /**
     * 865支持'支出授权'，刷'账户授权'定义
     */
    @ServiceMethod("updateAccountAuthDescribe")
    CurlModel.UpdateAccountAuthResult updateAccountAuthDescribe(CurlModel.UpdateAccountAuthArg arg);

    /**
     * 刷字段定义 fieldDescribe.setIsExtend(true);
     */
    @ServiceMethod("updateFieldDescribe")
    CurlModel.UpdateAccountAuthResult updateFieldDescribe(CurlModel.UpdateFieldArg arg);

    /**
     * 865支持'支出授权'，刷'账户授权'数据
     */
    @ServiceMethod("updateAccountAuthData")
    CurlModel.UpdateAccountAuthResult updateAccountAuthData(CurlModel.UpdateAccountAuthArg arg);

    /**
     * 865，刷数据，SalesOrderObj 加上 '账户支付金额',销售订单待回款金额公式调整
     */
    @ServiceMethod("addFAccountAmountAndUpdateReceivableAmount")
    CurlModel.AddFAccountAmountAndUpdateReceivableAmountResult addFAccountAmountAndUpdateReceivableAmount(CurlModel.AddFAccountAmountAndUpdateReceivableAmountArg arg);

    /**
     * 865，开了'账户授权'的，账户收支流水 的 'AccountTransactionFlowConst.Field.ExpenseType.apiName' 加上 ExpenseTypeEnum.ComponentDeduct("component_deduct", "组件扣减"),
     */
    @ServiceMethod("accountTransactionFlowExpenseTypeAddComponentDeductOption")
    CurlModel.AccountTransactionFlowExpenseTypeAddAuthRelateDeductOptionResult accountTransactionFlowExpenseTypeAddComponentDeductOption(CurlModel.AccountTransactionFlowExpenseTypeAddComponentDeductOptionArg arg);

    /**
     * 修改流水 ExpenseType
     */
    @ServiceMethod("updateAccountTransactionFlowExpenseType")
    CurlModel.UpdateAccountTransactionFlowExpenseTypeResult updateAccountTransactionFlowExpenseType(CurlModel.UpdateAccountTransactionFlowExpenseTypeArg arg);

    /**
     * 批量删除业务功能
     */
    @ServiceMethod("batchDelFunc")
    CurlModel.BatchDelFuncModelResult batchDelFunc(CurlModel.BatchDelFuncModelArg arg);

    /**
     * 870刷数据
     * <p>
     * '支出授权'
     * 1、有支出授权数据的：开启校验规则
     * 2、已初始化的支出授权数据：新建启用的'组件扣减'的校验规则数据
     */
    @ServiceMethod("transferForOutcomeAccount")
    CurlModel.TransferForOutcomeAccountResult transferForOutcomeAccount(CurlModel.TransferForOutcomeAccountArg arg);

    /**
     * 870刷数据
     * <p>
     * 开了校验规则的
     * 1、开启支出授权
     * 2、新建订单相关的支出类型的'账户授权'数据，
     * 3、新建订单'组件扣减'的校验规则数据
     */
    @ServiceMethod("transferForAccountCheckRule")
    CurlModel.TransferForAccountCheckRuleResult transferForAccountCheckRule(CurlModel.TransferForAccountCheckRuleArg arg);

    /**
     * 870刷数据
     * <p>
     * 开了校验规则的
     * 1、直接扣减，校验扣减的对象刷到‘支出授权’(不用做初始化）
     */
    @ServiceMethod("transferCheckObjectAndReduceRelatedObjectToOutcomeAuth")
    CurlModel.TransferCheckObjectAndReduceRelatedObjectToOutcomeAuthResult transferCheckObjectAndReduceRelatedObjectToOutcomeAuth(CurlModel.TransferCheckObjectAndReduceRelatedObjectToOutcomeAuthArg arg);

    /**
     * 870刷数据
     * <p>
     * 开了账户授权的
     * 1、如果有支出授权，如果有 ExpenseTypeEnum.ComponentDeduct("component_deduct", "组件扣减") 类型的流水，要补规则使用记录
     */
    @ServiceMethod("transferAccountRuleUseRecord")
    CurlModel.TransferAccountRuleUseRecordResult transferAccountRuleUseRecord(CurlModel.TransferAccountRuleUseRecordArg arg);

    /**
     * 870刷数据bug【只要处理前2个刷数据】
     * <p>
     * 对已经初始化的支出授权，新建组件扣减的校验规则，'账户授权流水'支出金额对应的映射字段可能没有加上
     */
    @ServiceMethod("transferMappingFieldForComponentReduceAccountCheckRule")
    CurlModel.TransferMappingFieldForComponentReduceAccountCheckRuleResult transferMappingFieldForComponentReduceAccountCheckRule(CurlModel.TransferMappingFieldForComponentReduceAccountCheckRuleArg arg);

    /**
     * 870刷数据bug【只要处理前2个刷数据】
     * <p>
     * 对已经初始化的支出授权，新建组件扣减的校验规则，'账户授权流水'支出金额对应的映射字段可能没有加上
     * 对应的规则使用记录需要补上
     */
    @ServiceMethod("transferMappingFieldForComponentReduceUseRecord")
    CurlModel.TransferMappingFieldForComponentReduceUseRecordResult transferMappingFieldForComponentReduceUseRecord(CurlModel.TransferMappingFieldForComponentReduceUseRecordArg arg);

    /**
     * 870刷数据，删掉停用的规则使用记录
     */
    @ServiceMethod("deleteOffUseRecord")
    CurlModel.DeleteOffUseRecordResult deleteOffUseRecord(CurlModel.DeleteOffUseRecordArg arg);

    /**
     * 870刷数据
     * <p>
     * 对于pluginInstance,pluginParam补充details
     */
    @ServiceMethod("pluginParamAddDetails")
    CurlModel.PluginParamAddDetailsResult pluginParamAddDetails(CurlModel.PluginParamAddDetailsArg arg);

    /**
     * 删内部对象数据
     */
    @ServiceMethod("bulkDeleteWithInternalDescribe")
    CurlModel.BulkDeleteWithInternalDescribeResult bulkDeleteWithInternalDescribe(CurlModel.BulkDeleteWithInternalDescribeArg arg);

    /**
     * 测试 saveAddFinallyDoFromMQ
     */
    @ServiceMethod("saveAddFinallyDoFromMQ")
    CurlModel.SaveAddFinallyDoFromMQResult saveAddFinallyDoFromMQ(CurlModel.SaveAddFinallyDoFromMQArg arg);

    @ServiceMethod("canEnableCheckRuleDomainPluginGray")
    CurlModel.canEnableCheckRuleDomainPluginGrayResult canEnableCheckRuleDomainPluginGray(ServiceContext serviceContext, CurlModel.TenantIds arg);

    @ServiceMethod("migrationAccountCheckRuleToDomainPlugin")
    CurlModel.Result migrationAccountCheckRuleToDomainPlugin(ServiceContext serviceContext, CurlModel.TenantIds arg) throws TimeoutException;

    @ServiceMethod("initCreditEnterAccountPromptPlugin")
    CurlModel.Result initCreditEnterAccountPromptPlugin(ServiceContext serviceContext, CurlModel.TenantIds arg);

    @ServiceMethod("updateFieldValue")
    Map<String, Object> updateFieldValue(ServiceContext serviceContext, CurlModel.FixDataWithCondition arg);

    @ServiceMethod("scanFlowRevenueType")
    Map<String, Object> scanFlowRevenueType(ServiceContext serviceContext, CurlModel.ScanFlowRevenueTypeArg arg);

    @ServiceMethod("scanUnfreezeDirectReduceSameObject")
    Map<String, Object> scanUnfreezeDirectReduceSame(ServiceContext serviceContext, CurlModel.TenantIds arg);

    /**
     * 测试crm通知
     */
    @ServiceMethod("testCrmNotify")
    CurlModel.Result testCrmNotify(ServiceContext serviceContext, CurlModel.TestCrmNotifyArg arg);

    /**
     * 测试删除按钮
     */
    @ServiceMethod("testDeleteButton")
    CurlModel.Result testDeleteButton(ServiceContext serviceContext, CurlModel.TestDeleteArg arg);

    /**
     * 测试删除字段
     */
    @ServiceMethod("testDeleteField")
    CurlModel.Result testDeleteField(ServiceContext serviceContext, CurlModel.TestDeleteFieldArg arg);

    /**
     * 880，开了【账户校验】的企业，组件扣减校验规则，新建插件实例
     */
    @ServiceMethod("addPluginInstanceForComponentReduceAccountCheckRule")
    CurlModel.AddPluginInstanceForComponentReduceAccountCheckRuleResult addPluginInstanceForComponentReduceAccountCheckRule(ServiceContext serviceContext, CurlModel.AddPluginInstanceForComponentReduceAccountCheckRuleArg arg);

    /**
     * 890，修复蒙牛云DDS同步数据的问题：已初始化的支出授权，刷customer_account插件
     */
    @ServiceMethod("addCustomerAccountPluginInstanceForInitOutComeAuth")
    CurlModel.AddCustomerAccountPluginInstanceForInitOutComeAuthResult addCustomerAccountPluginInstanceForInitOutComeAuth(ServiceContext serviceContext, CurlModel.AddCustomerAccountPluginInstanceForInitOutComeAuthArg arg);

    /**
     * 890，修复蒙牛云DDS同步数据的问题：开了【账户校验】的企业，修复组件扣减校验规则
     * 如果这3个字段没值，设置为：
     * "reduce_trigger_action":"button",
     * "reduce_trigger_button":"Add_button_default",
     * "reduce_trigger_condition":"[]"
     */
    @ServiceMethod("fixComponentReduceAccountCheckRuleData")
    CurlModel.FixComponentReduceAccountCheckRuleDataResult fixComponentReduceAccountCheckRuleData(ServiceContext serviceContext, CurlModel.FixComponentReduceAccountCheckRuleDataArg arg);

    /**
     * 890，修复蒙牛云DDS同步数据的问题：开了【账户校验】的企业，修复组件扣减校验规则
     * reduce_mapping 改为目标企业的
     */
    @ServiceMethod("fixComponentReduceAccountCheckRuleDataReduceMapping")
    CurlModel.FixComponentReduceAccountCheckRuleDataReduceMappingResult fixComponentReduceAccountCheckRuleDataReduceMapping(ServiceContext serviceContext, CurlModel.FixComponentReduceAccountCheckRuleDataReduceMappingArg arg);

    /**
     * 895，修复蒙牛云DDS同步数据的问题：开了【账户校验】的企业，修复组件扣减校验规则
     * 查找、修复组件扣减校验规则有问题的reduce_mapping
     * 账户ID、扣减金额     用本企业的
     */
    @ServiceMethod("fixSalesOrderComponentReduceAccountCheckRuleDataReduceMapping")
    CurlModel.FixSalesOrderComponentReduceAccountCheckRuleDataReduceMappingResult fixSalesOrderComponentReduceAccountCheckRuleDataReduceMapping(ServiceContext serviceContext, CurlModel.FixSalesOrderComponentReduceAccountCheckRuleDataReduceMappingArg arg);

    /**
     * 如果没有【组件扣减】的【校验规则】，复制一份
     * 也可以只做查询
     */
    @ServiceMethod("copyComponentReduceAccountCheckRuleIfNoExist")
    CurlModel.CopyComponentReduceAccountCheckRuleIfNoExistResult copyComponentReduceAccountCheckRuleIfNoExist(ServiceContext serviceContext, CurlModel.CopyComponentReduceAccountCheckRuleIfNoExistArg arg);

    /**
     * 890，修复蒙牛云DDS同步数据的问题：【账户授权】刷为初始化
     */
    @ServiceMethod("fixAccountAuthToInit")
    CurlModel.FixAccountAuthToInitResult fixAccountAuthToInit(ServiceContext serviceContext, CurlModel.FixAccountAuthToInitArg arg);

    /**
     * 885，修复蒙牛云DDS同步数据的问题：
     * 对比账户数据，跟模板企业
     * 1、label，ID 都不一样
     * 2、label一样，ID不一样
     */
    @ServiceMethod("findNoExistOrDifferentFundAccount")
    CurlModel.FindNoExistOrDifferentFundAccountResult findNoExistOrDifferentFundAccount(ServiceContext serviceContext, CurlModel.FindNoExistOrDifferentFundAccountArg arg);

    /**
     * 修复蒙牛云DDS同步数据的问题：
     * 查询企业没有哪些账户
     */
    @ServiceMethod("findNoExistFundAccount")
    CurlModel.FindNoExistFundAccountResult findNoExistFundAccount(ServiceContext serviceContext, CurlModel.FindNoExistFundAccountArg arg);

    /**
     * 900 刷账户字段
     * <p>
     * 问题：实施DDS上，【账户对象】少配了字段
     */
    @ServiceMethod("updateAccountFieldData")
    CurlModel.UpdateAccountFieldDataResult updateAccountFieldData(ServiceContext serviceContext, CurlModel.UpdateAccountFieldDataArg arg);

    /**
     * 890，修复蒙牛云DDS同步数据的问题：
     * 目标企业如果没有模板企业上的账户，直接复制
     */
    @ServiceMethod("copyFundAccountIfNoExist")
    CurlModel.CopyFundAccountIfNoExistResult copyFundAccountIfNoExist(ServiceContext serviceContext, CurlModel.CopyFundAccountIfNoExistArg arg);

    /**
     * 查不存在的【账户授权】
     */
    @ServiceMethod("queryNotExistAccountAuth")
    CurlModel.QueryNotExistAccountAuthResult queryNotExistAccountAuth(ServiceContext serviceContext, CurlModel.QueryNotExistAccountAuthArg arg);

    /**
     * 查未初始化的【账户授权】
     */
    @ServiceMethod("queryNotInitAccountAuth")
    CurlModel.QueryNotInitAccountAuthResult queryNotInitAccountAuth(ServiceContext serviceContext, CurlModel.QueryNotInitAccountAuthArg arg);

    /**
     * 890，修复蒙牛云DDS同步数据的问题：
     * 对账户授权，把目标企业的【授权明细】复制过来
     */
    @ServiceMethod("copyAuthorizationDetails")
    CurlModel.CopyAuthorizationDetailsResult copyAuthorizationDetails(ServiceContext serviceContext, CurlModel.CopyAuthorizationDetailsArg arg);

    /**
     * 注意：【入账规则】里面的账户，不能重复
     * <p>
     * <p>
     * 890, 蒙牛云同步入账类型的【账户授权】刷自动入账
     * 1、如果没对应的入账授权，同步过来
     * 2、如果有对应的入账授权，更新【入账规则】【授权明细】【账户授权】的[自动入账]字段
     */
    @ServiceMethod("transferIncomeAccountAuth")
    CurlModel.TransferIncomeAccountAuthResult transferIncomeAccountAuth(ServiceContext serviceContext, CurlModel.TransferIncomeAccountAuthArg arg);

    /**
     * 新建/更新【支出授权】
     * 1、如果没对应的支出授权，同步过来
     * 2、如果有对应的支出授权，且需要更新，则做更新
     */
    @ServiceMethod("transferOutcomeAccountAuth")
    CurlModel.TransferOutcomeAccountAuthResult transferOutcomeAccountAuth(ServiceContext serviceContext, CurlModel.TransferOutcomeAccountAuthArg arg);

    /**
     * 890，修复蒙牛云DDS同步数据的问题：
     * 如果账户满足下面任一条件，则不能作废，其他的情况可以作废
     * 1、账户对应的【客户账户余额】账户余额 > 0  或 账户可用金额 > 0
     * 2、账户有收支流水
     */
    @ServiceMethod("invalidFundAccount")
    CurlModel.InvalidFundAccountResult invalidFundAccount(ServiceContext serviceContext, CurlModel.InvalidFundAccountArg arg);

    /**
     * 890，修复蒙牛云DDS同步数据的问题：
     * 查不能作废的账户，如果账户满足下面任一条件，则不能作废
     * 1、账户对应的【客户账户余额】账户余额 > 0  或 账户可用金额 > 0
     * 2、账户有收支流水
     */
    @ServiceMethod("getCannotInvalidFundAccount")
    CurlModel.GetCannotInvalidFundAccountResult getCannotInvalidFundAccount(ServiceContext serviceContext, CurlModel.GetCannotInvalidFundAccountArg arg);

    /**
     * 作废授权明细
     */
    @ServiceMethod("invalidAuthDetail")
    CurlModel.InvalidAuthDetailResult invalidAuthDetail(ServiceContext serviceContext, CurlModel.InvalidAuthDetailArg arg);

    /**
     * 885，开了【账户授权】的企业，加新的从对象【入账规则】FAccountEntryRuleObj
     */
    @ServiceMethod("addFAccountEntryRuleObj")
    CurlModel.AddFAccountEntryRuleObjResult addFAccountEntryRuleObj(ServiceContext serviceContext, CurlModel.AddFAccountEntryRuleObjArg arg);

    /**
     * 930，开了【账户授权】的企业，加新的从对象【解冻授权明细】UnfreezeAuthDetailObj
     */
    @ServiceMethod("addUnfreezeAuthDetailObj")
    CurlModel.AddUnfreezeAuthDetailObjResult addUnfreezeAuthDetailObj(ServiceContext serviceContext, CurlModel.AddUnfreezeAuthDetailObjArg arg);

    /**
     * 885，开了【账户授权】的企业，入账授权("autoentry_status", "自动入账")，自动入账刷为false
     */
    @ServiceMethod("transferAutoEntryStatusFalse")
    CurlModel.TransferAutoEntryStatusFalseResult transferAutoEntryStatusFalse(ServiceContext serviceContext, CurlModel.TransferAutoEntryStatusFalseArg arg);

    /**
     * 885，测试自动入账
     */
    @ServiceMethod("testAutoEnterAccount")
    CurlModel.TestAutoEnterAccountResult testAutoEnterAccount(ServiceContext serviceContext, CurlModel.TestAutoEnterAccountArg arg);

    /**
     * 批量触发自动入账（后台修复使用）
     */
    @ServiceMethod("triggerAutoEnterAccount")
    CurlModel.TriggerAutoEnterAccountResult triggerAutoEnterAccount(ServiceContext serviceContext, CurlModel.TriggerAutoEnterAccountArg arg);

    @ServiceMethod("fixPaymentWithDetailEnterAccountButton")
    CurlModel.TenantIds fixPaymentWithDetailEnterAccountButton(ServiceContext serviceContext, CurlModel.TenantIds arg);

    @ServiceMethod("addField")
    CurlModel.TenantIds addCheckRuleReconciliationJsonField(ServiceContext serviceContext, CurlModel.TenantAddField arg);

    /**
     * 查询【校验规则】哪些用了什么账户（后台查数据用）
     */
    @ServiceMethod("queryAccountCheckRuleHasAccount")
    CurlModel.QueryAccountCheckRuleHasAccountResult queryAccountCheckRuleHasAccount(ServiceContext serviceContext, CurlModel.QueryAccountCheckRuleHasAccountArg arg);

    /**
     * 查询【账户授权】哪些用了什么账户（后台查数据用）
     */
    @ServiceMethod("queryAccountAuthHasAccount")
    CurlModel.QueryAccountAuthHasAccountResult queryAccountAuthHasAccount(ServiceContext serviceContext, CurlModel.QueryAccountAuthHasAccountArg arg);


    /**
     * 蒙牛云刷数据
     * <p>
     * 背景：
     * 蒙牛云的数据
     * 定义从 1端企业      复制的
     * 数据从 M/N端模版企业 复制的
     * <p>
     * 新建/编辑页布局 edit_layout_dht__c
     * 刷layout里面的 mobile_layout 的【客户账户】组件的accountIds ：
     * <p>
     * 账户都存在：不用处理
     * 账户部分存在、部分不存在：把不存在的去掉
     * 账户都不存在：替换跟1端同名的账户ID（返利账户、预收账户）
     */
    @ServiceMethod("setLayoutCustomerAccountComponentAccountIds")
    CurlModel.SetLayoutCustomerAccountComponentAccountIdsResult setLayoutCustomerAccountComponentAccountIds(ServiceContext serviceContext, CurlModel.SetLayoutCustomerAccountComponentAccountIdsArg arg);

    /**
     * 【账户】的layout中
     * 如果有account_type、access_module，is_required 刷为true
     * 如果没有，不加上
     */
    @ServiceMethod("setFundAccountLayoutFieldIsRequired")
    CurlModel.SetFundAccountLayoutFieldIsRequiredResult setFundAccountLayoutFieldIsRequired(ServiceContext serviceContext, CurlModel.SetFundAccountLayoutFieldIsRequiredArg arg);

    /**
     * 布局的base_field_section__c 加字段
     */
    @ServiceMethod("layoutAddFields")
    CurlModel.LayoutAddFieldsResult layoutAddFields(ServiceContext serviceContext, CurlModel.LayoutAddFieldsArg arg);

//    @ServiceMethod("addAdvertisementFields")
//    CurlModel.TenantIds addAdvertisementFields(ServiceContext serviceContext, CurlModel.TenantIds tenantIds);

//    @ServiceMethod("addAdvertisementLayoutFields")
//    CurlModel.TenantIds addAdvertisementLayoutFields(ServiceContext serviceContext, CurlModel.TenantIds tenantIds);

    /**
     * 补layout_customer_account_component_check插件
     * 补customer_account插件
     * 查哪些没有，没有可以创建
     */
    @ServiceMethod("findOrCreatePluginInstanceForComponentCheckAccountCheckRule")
    CurlModel.FindOrCreatePluginInstanceForComponentCheckAccountCheckRuleResult findOrCreatePluginInstanceForComponentCheckAccountCheckRule(ServiceContext serviceContext, CurlModel.FindOrCreatePluginInstanceForComponentCheckAccountCheckRuleArg arg);

    /**
     * customer_account插件
     * 新建时，customer_account插件实例不存在，导致没有走CustomerAccountAddActionDomainPlugin相关的逻辑
     * 做补偿
     */
    @ServiceMethod("triggerCustomerAccountAddActionDomainPlugin")
    CurlModel.TriggerCustomerAccountAddActionDomainPluginResult triggerCustomerAccountAddActionDomainPlugin(CurlModel.TriggerCustomerAccountAddActionDomainPluginArg arg);

    /**
     * 查【规则使用记录】条数
     */
    @ServiceMethod("queryAccountRuleUseRecordCount")
    CurlModel.QueryAccountRuleUseRecordCountResult queryAccountRuleUseRecordCount(CurlModel.QueryAccountRuleUseRecordCountArg arg);

    /**
     * 找出【组件扣减】有问题的，并修复
     * <p>
     * 1、只针对生命状态【normal】的
     * 2、只针对有【规则使用记录】的
     */
    @ServiceMethod("queryFixComponentReduceFlow")
    CurlModel.QueryFixComponentReduceFlowResult queryFixComponentReduceFlow(CurlModel.QueryFixComponentReduceFlowArg arg);

    /**
     * 修复数据：
     * 数据作废了，对应的【组件扣减】的【校验规则】生成的流水没作废
     */
    @ServiceMethod("queryAndInvalidComponentReduceFlow")
    CurlModel.QueryAndInvalidComponentReduceFlowResult queryAndInvalidComponentReduceFlow(CurlModel.QueryAndInvalidComponentReduceFlowArg arg);

    /**
     * 查询某个时间段之后，life_status=ineffective 的校验规则
     * 更新某个时间段之后，校验规则的life_status从ineffective改为normal
     */
    @ServiceMethod("getOrUpdateAccountCheckRuleLifeStatus")
    CurlModel.GetOrUpdateAccountCheckRuleLifeStatusResult getOrUpdateAccountCheckRuleLifeStatus(CurlModel.GetOrUpdateAccountCheckRuleLifeStatusArg arg);

    /**
     * 查询【校验规则】expense_amount对应的映射字段，哪些字段是计算、统计字段
     */
    @ServiceMethod("getAccountCheckRuleByFieldType")
    CurlModel.GetAccountCheckRuleByFieldTypeResult getAccountCheckRuleByFieldType(CurlModel.GetAccountCheckRuleByFieldTypeArg arg);

    @ServiceMethod("initPaymentDomainPlugin")
    Map<String, Object> initPaymentDomainPlugin(ServiceContext serviceContext, CurlModel.TenantIds arg);

    /**
     * 930
     * 校验规则配置中心配置，迁移到【账户授权】【解冻授权明细】
     * <p>
     * 和 CurlService#accountCheckRule2AccountAuth 功能差不多，不同角度
     */
    @ServiceMethod("accountCheckRuleConfigTransfer")
    CurlModel.AccountCheckRuleConfigTransferResult accountCheckRuleConfigTransfer(ServiceContext serviceContext, CurlModel.AccountCheckRuleConfigTransferArg arg);

    /**
     * 930
     * 【校验规则】配置相关选项，迁移到【账户授权】【解冻授权明细】，查询哪些交易规则没有【账户授权】
     * <p>
     * 如果缺【支出授权】，代码不加，通知客户去加
     * 如果有【支出授权】，【账户授权】【解冻授权明细】缺了字段/数据，会补上
     * <p>
     * 和 CurlService#accountCheckRuleConfigTransfer 功能差不多，不同角度
     */
    @ServiceMethod("accountCheckRule2AccountAuth")
    CurlModel.AccountCheckRule2AccountAuthResult accountCheckRule2AccountAuth(ServiceContext serviceContext, CurlModel.AccountCheckRule2AccountAuthArg arg);

    /**
     * 930 【账户授权】加了is_unfreeze_auth、frozen_actions、reduce_trigger_actions
     * <p>
     * 没有【校验规则】使用的【支出授权】：is_unfreeze_auth、frozen_actions、reduce_trigger_actions 补上默认数据
     * 先用 customer_account_curl/service/accountCheckRuleConfigTransfer 刷，剩下的【支出授权】，是有【校验规则】的，这样只需要刷is_unfreeze_auth（没值就刷为false）、reduce_trigger_actions
     */
    @ServiceMethod("transferAccountAuth930")
    CurlModel.TransferAccountAuth930Result transferAccountAuth930(ServiceContext serviceContext, CurlModel.TransferAccountAuth930Arg arg);

    /**
     * 部分历史企业，直接扣减刷为不强制校验，既维持原来的状态，需要用户手动升级
     * <p>
     * 满足其中一个条件，就刷
     * 1、有【直接扣减】的【校验规则】
     * 2、不支持插件 checkRuleEnableDomainPlugin
     */
    @ServiceMethod("direct_reduce_not_force_check_amount")
    CurlModel.DirectReduceNotForceCheckAmountResult directReduceNotForceCheckAmount(ServiceContext serviceContext, CurlModel.DirectReduceNotForceCheckAmountArg arg);

    @ServiceMethod("registerReconTask")
    Map<String, Object> registerReconTask(ServiceContext serviceContext, Map<String, Object> reconParam);
}