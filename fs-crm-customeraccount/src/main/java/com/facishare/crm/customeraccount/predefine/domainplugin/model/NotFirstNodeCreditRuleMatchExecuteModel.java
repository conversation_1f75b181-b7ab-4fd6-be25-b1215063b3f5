package com.facishare.crm.customeraccount.predefine.domainplugin.model;

import lombok.Builder;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Getter
@Builder
public class NotFirstNodeCreditRuleMatchExecuteModel {
    private String creditCustomerAccountId;
    private String creditRuleDetailDataId;

    private String preCreditObject;
    private Set<String> preObjectCreditRuleMatchRecordIds;
    private Map<String, BigDecimal> preCreditFlowDetailLeftCreditAmountChangedMap;
    private Map<String, BigDecimal> preNodeTransactionFlowToNewExpenseAmountMap;
    private List<CreditFlowDetailModel> creditFlowDetailModelList;

}
