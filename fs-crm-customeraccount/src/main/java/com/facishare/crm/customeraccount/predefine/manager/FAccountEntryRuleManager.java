package com.facishare.crm.customeraccount.predefine.manager;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.customeraccount.constants.FAccountAuthorizationConstants;
import com.facishare.crm.customeraccount.constants.FAccountEntryRuleConstants;
import com.facishare.crm.customeraccount.constants.SystemConstants;
import com.facishare.crm.customeraccount.enums.FAccountAuthAuthorizedTypeEnum;
import com.facishare.crm.customeraccount.model.FAccountEntryRuleModel;
import com.facishare.crm.customeraccount.util.OperatorUtil;
import com.facishare.crmcommon.manager.CommonDescribeManager;
import com.facishare.crmcommon.manager.CommonObjDataManager;
import com.facishare.crmcommon.util.CommonFieldDescribeUtil;
import com.facishare.crmcommon.util.ObjectDataUtil;
import com.facishare.crmcommon.util.SearchUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
public class FAccountEntryRuleManager {

    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private CommonDescribeManager commonDescribeManager;
    @Autowired
    private CommonObjDataManager commonObjDataManager;

    public List<IObjectData> query(String tenantId, String authorizedObjectApiName, boolean orderBySequence, Boolean isAsc) {
        List<IFilter> filterList = Lists.newArrayList();

        IFilter authorizedTypeFilter = new Filter();
        authorizedTypeFilter.setFieldName(FAccountAuthorizationConstants.Field.AuthorizedType.apiName);
        authorizedTypeFilter.setFieldValues(Lists.newArrayList(FAccountAuthAuthorizedTypeEnum.Income.getValue()));
        authorizedTypeFilter.setOperator(Operator.EQ);
        //  authorizedTypeFilter.setValueType(7);
        authorizedTypeFilter.setIsMasterField(true);
        filterList.add(authorizedTypeFilter);

        IFilter authorizedObjectFilter = new Filter();
        authorizedObjectFilter.setFieldName(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName);
        authorizedObjectFilter.setFieldValues(Lists.newArrayList(authorizedObjectApiName));
        authorizedObjectFilter.setOperator(Operator.IN);
        //  authorizedObjectFilter.setValueType(7);
        authorizedObjectFilter.setIsMasterField(true);
        filterList.add(authorizedObjectFilter);

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(filterList);
        query.setOffset(0);
        query.setLimit(200);

        if (orderBySequence) {
            List<OrderBy> orders =  new ArrayList<>();
            orders.add(new OrderBy(FAccountEntryRuleConstants.Field.Sequence.apiName, isAsc));
            query.setOrders(orders);
        }

        User user = new User(tenantId, "-10000");
        return serviceFacade.findBySearchQuery(user, FAccountEntryRuleConstants.API_NAME, query).getData();
    }

    public List<IObjectData> query(String tenantId, String fAccountAuthorizationIds) {
        User admin = User.systemUser(tenantId);
        return query(admin, Lists.newArrayList(fAccountAuthorizationIds));
    }

    public List<IObjectData> query(User user, List<String> fAccountAuthorizationIds) {
        if (CollectionUtils.empty(fAccountAuthorizationIds)) {
            return Lists.newArrayList();
        }
        List<IFilter> filterList = Lists.newArrayList();

        IFilter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(FAccountEntryRuleConstants.Field.FAccountAuthorizationId.apiName);
        lifeStatusFilter.setFieldValues(fAccountAuthorizationIds);
        lifeStatusFilter.setOperator(Operator.IN);
        filterList.add(lifeStatusFilter);

        IFilter deleteStatusFilter = new Filter();
        deleteStatusFilter.setFieldName(ObjectData.IS_DELETED);
        deleteStatusFilter.setFieldValues(Lists.newArrayList("0"));
        deleteStatusFilter.setOperator(Operator.EQ);
        filterList.add(deleteStatusFilter);

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(filterList);
        query.setOffset(0);
        query.setLimit(1000);

        return serviceFacade.findBySearchQuery(user, FAccountEntryRuleConstants.API_NAME, query).getData();
    }

    public List<IObjectData> query(String tenantId, List<String> fAccountAuthorizationIds, List<String> fAccountIds) {
        if (CollectionUtils.empty(fAccountAuthorizationIds) || CollectionUtils.empty(fAccountIds)) {
            return Lists.newArrayList();
        }
        List<IFilter> filterList = Lists.newArrayList();

        IFilter fAccountAuthorizationIdFilter = new Filter();
        fAccountAuthorizationIdFilter.setFieldName(FAccountEntryRuleConstants.Field.FAccountAuthorizationId.apiName);
        fAccountAuthorizationIdFilter.setFieldValues(fAccountAuthorizationIds);
        fAccountAuthorizationIdFilter.setOperator(Operator.IN);
        filterList.add(fAccountAuthorizationIdFilter);

        IFilter fAccountIdFilter = new Filter();
        fAccountIdFilter.setFieldName(FAccountEntryRuleConstants.Field.FAccountId.apiName);
        fAccountIdFilter.setFieldValues(fAccountIds);
        fAccountIdFilter.setOperator(Operator.IN);
        filterList.add(fAccountIdFilter);

        IFilter deleteStatusFilter = new Filter();
        deleteStatusFilter.setFieldName(ObjectData.IS_DELETED);
        deleteStatusFilter.setFieldValues(Lists.newArrayList("0"));
        deleteStatusFilter.setOperator(Operator.EQ);
        filterList.add(deleteStatusFilter);

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(filterList);
        query.setOffset(0);
        query.setLimit(1000);

        User user = User.systemUser(tenantId);
        return serviceFacade.findBySearchQuery(user, FAccountEntryRuleConstants.API_NAME, query).getData();
    }

    public List<String> getContainFundAccountIds(String tenantId, List<String> fAccountAuthorizationIds, List<String> authorizeAccountIds) {
        List<IObjectData> entryRules = query(tenantId, fAccountAuthorizationIds, authorizeAccountIds);
        if (CollectionUtils.empty(entryRules)) {
            return Lists.newArrayList();
        }

        return entryRules.stream().map(d -> d.get(FAccountEntryRuleConstants.Field.FAccountId.apiName, String.class)).collect(Collectors.toList());
    }

    /**
     * 自动入账到哪个账户
     */
    public String getAutoEnterAccountId(String tenantId, IObjectData objectData) {
        //查询'入账规则'
        String objectApiName = objectData.getDescribeApiName();
        List<IObjectData> accountEntryRules = query(tenantId, objectApiName, true, true);
        if (CollectionUtils.empty(accountEntryRules)) {
            return null;
        }

        IObjectDescribe objectDescribe = commonDescribeManager.findByTenantIdAndDescribeApiName(tenantId, objectApiName);

        for (IObjectData accountEntryRule : accountEntryRules) {
            boolean match = isMatch(tenantId, objectDescribe, objectData, accountEntryRule);
            if (match) {
                return accountEntryRule.get(FAccountEntryRuleConstants.Field.FAccountId.apiName, String.class);
            }
        }

        return null;
    }

    /**
     * objectData是否满足规则accountEntryRule
     */
    public boolean isMatch(String tenantId, IObjectDescribe objectDescribe, IObjectData objectData, IObjectData accountEntryRule) {
        String entryConditionJson = accountEntryRule.get(FAccountEntryRuleConstants.Field.EntryCondition.apiName, String.class);
        if (Strings.isNullOrEmpty(entryConditionJson)) {
            return false;
        }

        List<FAccountEntryRuleModel.EntryCondition> entryConditions = getRuleFilters(entryConditionJson);
        if (CollectionUtils.empty(entryConditions)) {
            return false;
        }

        for (FAccountEntryRuleModel.EntryCondition entryCondition : entryConditions) {
            boolean isMatch = isMatch(tenantId, objectDescribe, objectData, entryCondition.getFilters());
            if (isMatch) {
                return true;
            }
        }

       return false;
    }

    public List<FAccountEntryRuleModel.EntryCondition> getRuleFilters(String entryConditionJson) {
        if (Strings.isNullOrEmpty(entryConditionJson)) {
            return Lists.newArrayList();
        }
        List<FAccountEntryRuleModel.EntryCondition> entryConditions = Lists.newArrayList();
        List<String> conditions = JSON.parseArray(entryConditionJson, String.class);
        if (CollectionUtils.notEmpty(conditions)) {
            for (String condition : conditions) {
                if (Strings.isNullOrEmpty(condition)) {
                    continue;
                }
                entryConditions.add(JSON.parseObject(condition, FAccountEntryRuleModel.EntryCondition.class));
            }
        }
        return entryConditions;
    }

    private boolean isMatch(String tenantId, IObjectDescribe objectDescribe, IObjectData objectData, List<FAccountEntryRuleModel.Filter> filters) {
        List<IFilter> filterList = getFilters(filters, objectDescribe, objectData);

        SearchUtil.fillFilterEq(filterList, Tenantable.TENANT_ID, tenantId);
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(filterList);
        query.setOffset(0);
        query.setLimit(1);

        User admin = new User(tenantId, "-10000");
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryIgnoreAll(admin, objectData.getDescribeApiName(), query);
        log.info("isMatch, findBySearchQueryIgnoreAll apiName[{}], query[{}], queryResult.getData.size[{}]", objectData.getDescribeApiName(), query, queryResult.getData().size());
        return !CollectionUtils.empty(queryResult.getData());
    }

    private List<IFilter> getFilters(List<FAccountEntryRuleModel.Filter> filters, IObjectDescribe describe, IObjectData data) {
        List<IFilter> result = new ArrayList<>();

        IFilter f = new Filter();
        f.setFieldName("_id");
        f.setFieldValues(Lists.newArrayList(data.getId()));
        f.setOperator(Operator.EQ);
        result.add(f);

        for (FAccountEntryRuleModel.Filter filter : filters) {
            f = getFilters(filter, describe, data);
            result.add(f);
        }
        return result;
    }

    /**
     * fieldType 不支持 "department"
     */
    private IFilter getFilters(FAccountEntryRuleModel.Filter filter, IObjectDescribe describe, IObjectData data) {
        String fieldName = filter.getFieldName();
        List<IFieldDescribe> fieldDescribes = describe.getFieldDescribes();

        String fieldType = null;
        IFieldDescribe fieldDescribe = CommonFieldDescribeUtil.getField(fieldDescribes, fieldName);
        if (fieldDescribe != null) {
            fieldType = fieldDescribe.getType();
        }

        List<String> filterFieldValues = filter.getFieldValues();

        if (Objects.equals(fieldType, "object_reference")) {
            Integer valueType = filter.getValueType();
            if (Objects.equals(valueType, 0)) {
                fieldName = fieldName + ".name";
            } else if (Objects.equals(valueType, 2)) {
                //"field_values":["$account_id$"]
                //"$account_id$"  => "account_id"
                String filterField = filterFieldValues.get(0).replaceAll("\\$", "");
                if (data.get(filterField) == null) {
                    log.info("data.get(filterField) == null   filterField[{}], data[{}]", filterField, data);
                    filterFieldValues = Lists.newArrayList();
                } else {
                    filterFieldValues = Lists.newArrayList(String.valueOf(data.get(filterField)));
                }
            }
        }

        IFilter f = new Filter();
        f.setFieldName(fieldName);
        f.setFieldValues(filterFieldValues);
        f.setOperator(OperatorUtil.getOperator(filter.getOperator()));
        f.setValueType(filter.getValueType());
        return f;
    }

    /**
     * 注意:【入账规则】里面的账号，不能重复
     *
     *
     * targetAccountEntryRules 的账户不在  sourceAccountEntryRules 里面的作废掉
     * sourceAccountEntryRules 的账户不在  targetAccountEntryRules     里面的新增
     *
     *
     * needUpdateEntryConditionIfExist
     *      如果targetTenantId的【入账授权】存在，是否更新对应的条件
     *      默认是false，如果有入账条件变更了，刷数据时再用true
     */
    public void update(String targetTenantId, String targetAccountAuthDataId, List<IObjectData> sourceAccountEntryRules, List<IObjectData> targetAccountEntryRules, boolean needDeleteAccountEntryRuleIfSourceNotExist, boolean needUpdateEntryConditionIfExist) {
        if (sourceAccountEntryRules == null) {
            sourceAccountEntryRules = Lists.newArrayList();
        }

        if (targetAccountEntryRules == null) {
            targetAccountEntryRules = Lists.newArrayList();
        }

        if (needDeleteAccountEntryRuleIfSourceNotExist) {
            invalidNoExist(targetTenantId, sourceAccountEntryRules, targetAccountEntryRules);
        }
        if (needUpdateEntryConditionIfExist) {
            updateEntryCondition(targetTenantId, sourceAccountEntryRules, targetAccountEntryRules);
        }
        create(targetTenantId, targetAccountAuthDataId, sourceAccountEntryRules, targetAccountEntryRules);
    }

    /**
     * targetAccountEntryRules     的账户不在  sourceAccountEntryRuleDatas 里面的作废掉
     */
    public void invalidNoExist(String targetTenantId, List<IObjectData> sourceAccountEntryRules, List<IObjectData> targetAccountEntryRules) {
        User admin = User.systemUser(targetTenantId);

        if (CollectionUtils.empty(targetAccountEntryRules)) {
            return;
        }

        List<String> sourceFAccountIds = sourceAccountEntryRules.stream().map(d -> d.get(FAccountEntryRuleConstants.Field.FAccountId.apiName, String.class)).collect(Collectors.toList());

        //targetTenant有，sourceTenant没有，需要作废的
        List<IObjectData> targetNeedInvalidAccountEntryRuleDatas = targetAccountEntryRules.stream().filter(d -> !sourceFAccountIds.contains(
              d.get(FAccountEntryRuleConstants.Field.FAccountId.apiName, String.class))).collect(Collectors.toList());

        if (CollectionUtils.empty(targetNeedInvalidAccountEntryRuleDatas)) {
            return;
        }

        //作废
        try {
            serviceFacade.bulkInvalid(targetNeedInvalidAccountEntryRuleDatas, admin);
        } catch (Exception e) {
            log.info("invalid bulkInvalid fail targetNeedInvalidAccountEntryRuleDatas[{}], admin[{}]", targetNeedInvalidAccountEntryRuleDatas, admin, e);
            throw e;
        }
    }

    /**
     * 账户fundAccountId是否被使用了
     */
    public boolean hasUseFundAccount(List<IObjectData> accountEntryRules, String fundAccountId) {
        if (CollectionUtils.empty(accountEntryRules)) {
            return false;
        }

        for (IObjectData accountEntryRule : accountEntryRules) {
            String fAccountId = accountEntryRule.get(FAccountEntryRuleConstants.Field.FAccountId.apiName, String.class);
            if (Objects.equals(fAccountId, fundAccountId)) {
                return true;
            }
        }

        return false;
    }

    /**
     * sourceAccountEntryRules 的账户不在  targetAccountEntryRules     里面的新增
     */
    public void create(String targetTenantId, String targetAccountAuthDataId, List<IObjectData> sourceAccountEntryRules, List<IObjectData> targetAccountEntryRules) {
        User admin = User.systemUser(targetTenantId);

        if (CollectionUtils.empty(sourceAccountEntryRules)) {
            return;
        }

        List<String> targetFAccountIds = targetAccountEntryRules.stream().map(d -> d.get(FAccountEntryRuleConstants.Field.FAccountId.apiName, String.class)).collect(Collectors.toList());

        List<IObjectData> sourceNeedCreateAccountEntryRuleDatas = sourceAccountEntryRules.stream().filter(d -> !targetFAccountIds.contains(
                d.get(FAccountEntryRuleConstants.Field.FAccountId.apiName, String.class))).collect(Collectors.toList());

        if (CollectionUtils.empty(sourceNeedCreateAccountEntryRuleDatas)) {
            return;
        }

        //保存新数据
        List<String> sourceAccountEntryRuleIds = sourceNeedCreateAccountEntryRuleDatas.stream().map(DBRecord::getId).collect(Collectors.toList());
        List<String> targetHasUseAccountEntryRuleIds = commonObjDataManager.getHasUseIds(targetTenantId, FAccountEntryRuleConstants.API_NAME, sourceAccountEntryRuleIds);

        List<IObjectData> newAccountEntryRules = new ArrayList<>();
        for (IObjectData sourceAccountEntryRule : sourceNeedCreateAccountEntryRuleDatas) {
            IObjectData accountEntryRule = ObjectDataUtil.getBaseObjectData(admin, FAccountEntryRuleConstants.API_NAME);
            if (!targetHasUseAccountEntryRuleIds.contains(sourceAccountEntryRule.getId())) {
                accountEntryRule.set(SystemConstants.Field.Id.apiName, sourceAccountEntryRule.getId());
            }
            accountEntryRule.set(FAccountEntryRuleConstants.Field.Name.apiName, sourceAccountEntryRule.getName());
            accountEntryRule.set(FAccountEntryRuleConstants.Field.Sequence.apiName, sourceAccountEntryRule.get(FAccountEntryRuleConstants.Field.Sequence.apiName));
            accountEntryRule.set(FAccountEntryRuleConstants.Field.EntryCondition.apiName, sourceAccountEntryRule.get(FAccountEntryRuleConstants.Field.EntryCondition.apiName));
            accountEntryRule.set(FAccountEntryRuleConstants.Field.FAccountId.apiName, sourceAccountEntryRule.get(FAccountEntryRuleConstants.Field.FAccountId.apiName));
            accountEntryRule.set(FAccountEntryRuleConstants.Field.FAccountAuthorizationId.apiName, targetAccountAuthDataId);

            accountEntryRule.set("record_type", sourceAccountEntryRule.getRecordType());
            newAccountEntryRules.add(accountEntryRule);
        }
        if (CollectionUtils.empty(newAccountEntryRules)) {
            return;
        }

        try {
            serviceFacade.bulkSaveObjectData(newAccountEntryRules, admin);
        } catch (Exception e) {
            log.warn("create serviceFacade.bulkSaveObjectData fail newAccountEntryRules[{}], admin[{}]", newAccountEntryRules, admin, e);
            throw e;
        }
    }

    /**
     * targetTenantId有的规则，sourceTenant也有的，更新EntryCondition
     */
    private void updateEntryCondition(String targetTenantId, List<IObjectData> sourceAccountEntryRules, List<IObjectData> targetAccountEntryRules) {
        if (CollectionUtils.empty(targetAccountEntryRules) || CollectionUtils.empty(sourceAccountEntryRules)) {
            return;
        }

        Map<String, IObjectData> sourceFundAccountId2AccountEntryRule = sourceAccountEntryRules.stream().collect(Collectors.toMap(d -> d.get(FAccountEntryRuleConstants.Field.FAccountId.apiName, String.class), d -> d));
        List<String> sourceFAccountIds = sourceAccountEntryRules.stream().map(d -> d.get(FAccountEntryRuleConstants.Field.FAccountId.apiName, String.class)).collect(Collectors.toList());

        List<IObjectData> targetNeedUpdateAccountEntryRuleDatas = Lists.newArrayList();
        for (IObjectData targetAccountEntryRule : targetAccountEntryRules) {
            String targetFAccountId = targetAccountEntryRule.get(FAccountEntryRuleConstants.Field.FAccountId.apiName, String.class);
            if (!sourceFAccountIds.contains(targetFAccountId)) {
                continue;
            }
            IObjectData sourceAccountEntryRule = sourceFundAccountId2AccountEntryRule.get(targetFAccountId);
            boolean isSameEntryCondition = Objects.equals(targetAccountEntryRule.get(FAccountEntryRuleConstants.Field.EntryCondition.apiName), sourceAccountEntryRule.get(FAccountEntryRuleConstants.Field.EntryCondition.apiName));
            log.info("updateEntryCondition targetTenantId[{}], isSameEntryCondition[{}], targetAccountEntryRuleId[{}] sourceAccountEntryRuleId[{}], targetAccountEntryRuleEntryCondition[{}] sourceAccountEntryRuleEntryCondition[{}]",
                    targetTenantId, isSameEntryCondition, targetAccountEntryRule.getId(), sourceAccountEntryRule.getId(),
                    targetAccountEntryRule.get(FAccountEntryRuleConstants.Field.EntryCondition.apiName),
                    sourceAccountEntryRule.get(FAccountEntryRuleConstants.Field.EntryCondition.apiName));
            if (isSameEntryCondition) {
                continue;
            }

            targetAccountEntryRule.set(FAccountEntryRuleConstants.Field.EntryCondition.apiName, sourceAccountEntryRule.get(FAccountEntryRuleConstants.Field.EntryCondition.apiName));
            targetNeedUpdateAccountEntryRuleDatas.add(targetAccountEntryRule);
        }

        if (CollectionUtils.empty(targetNeedUpdateAccountEntryRuleDatas)) {
            return;
        }

        User admin = User.systemUser(targetTenantId);
        List<String> updateFieldApiNames = Lists.newArrayList(FAccountEntryRuleConstants.Field.EntryCondition.apiName);

        //更新EntryCondition
        try {
            serviceFacade.batchUpdateByFields(admin, targetNeedUpdateAccountEntryRuleDatas, updateFieldApiNames);
        } catch (Exception e) {
            log.info("updateEntryCondition fail targetTenantId[{}], targetNeedUpdateAccountEntryRuleDatas[{}]", targetTenantId, targetNeedUpdateAccountEntryRuleDatas, e);
            throw e;
        }
    }
}
