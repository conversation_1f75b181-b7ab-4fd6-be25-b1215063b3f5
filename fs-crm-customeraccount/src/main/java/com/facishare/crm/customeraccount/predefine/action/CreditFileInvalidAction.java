package com.facishare.crm.customeraccount.predefine.action;

import com.facishare.crm.customeraccount.constants.CAI18NKey;
import com.facishare.crm.customeraccount.constants.CreditFileConstants;
import com.facishare.crm.customeraccount.constants.SystemConstants;
import com.facishare.crm.customeraccount.predefine.manager.CreditFileManager;
import com.facishare.crm.customeraccount.predefine.manager.CustomerAccountManager;
import com.facishare.crm.customeraccount.util.ObjectDataUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Optional;

/**
 *  <AUTHOR>
 *  Created on 2019/1/3.
 *  支持OpenAPI调用&终端调用
 */
@Slf4j
public class CreditFileInvalidAction extends StandardInvalidAction {

    private CreditFileManager creditFileManager;
    private CustomerAccountManager customerAccountManager;

    @Override
    protected void before(Arg arg) {
        creditFileManager = SpringUtil.getContext().getBean(CreditFileManager.class);
        customerAccountManager = SpringUtil.getContext().getBean(CustomerAccountManager.class);
        super.before(arg);

        String customerId = ObjectDataUtil.getReferenceId(objectDataList.get(0), CreditFileConstants.Field.Customer.apiName);
        Optional<IObjectData> customerAccountObjectData = customerAccountManager.getCustomerAccountIncludeInvalidByCustomerId(actionContext.getUser(), customerId);
        if (customerAccountObjectData.isPresent() && customerAccountObjectData.get().isDeleted()) {
            log.warn("客户已作废,该信用已封存,无法作废,for customerId:{}", customerId);
            throw new ValidateException(I18N.text(CAI18NKey.SEAL_UP_NOT_VALID, customerAccountObjectData.get().getName()));
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        User user = actionContext.getUser();

        IObjectData objectData = serviceFacade.findObjectDataIncludeDeleted(user, arg.getObjectDataId(), objectDescribe.getApiName());
        String lifeStatusBeforeInvalid = objectData.get(SystemConstants.Field.LifeStatusBeforeInvalid.apiName, String.class);
        String lifeStatus = objectData.get(SystemConstants.Field.LifeStatus.apiName, String.class);

        boolean creditActive = creditFileManager.isCreditActive(objectData);
        if (creditActive && SystemConstants.LifeStatus.Invalid.value.equals(lifeStatus) && SystemConstants.LifeStatus.Normal.value.equals(lifeStatusBeforeInvalid)) {
            String customerId = ObjectDataUtil.getReferenceId(objectData, CreditFileConstants.Field.Customer.apiName);
            Optional<IObjectData> customerAccountObjectData = customerAccountManager.getCustomerAccountIncludeInvalidByCustomerId(actionContext.getUser(), customerId);
            if (customerAccountObjectData.isPresent()) {
                customerAccountManager.updateCreditQuotaToInvalid(user, objectData);
            }
        }
        return result;
    }

}
