package com.facishare.crm.customeraccount.predefine.service.dto;

import com.facishare.crm.consts.CustomerCreditAuthConst;
import com.facishare.crm.customeraccount.enums.CreditAuthTaskTypeEnum;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.Data;

import java.util.Date;

public class CreditAuthModel {
    @Data
    public static class Arg {
        private String objectApiName;
        private String objectDataId;
        private Long startTime;
        private Long endTime;
        //生效或者失效
        private String type;
    }

    public static Arg buildEffectiveTaskArg(IObjectData customerCreditAuthData) {
        return getBaseArg(customerCreditAuthData, CreditAuthTaskTypeEnum.Effective);
    }

    public static Arg buildIneffectiveTaskArg(IObjectData customerCreditAuthData) {
        return getBaseArg(customerCreditAuthData, CreditAuthTaskTypeEnum.Ineffective);
    }

    private static Arg getBaseArg(IObjectData customerCreditAuthData, CreditAuthTaskTypeEnum taskTypeEnum) {
        String customerCreditAuthDataId = customerCreditAuthData.getId();
        Date startTime = customerCreditAuthData.get(CustomerCreditAuthConst.F.StartTime.apiName, Date.class);
        Date endTime = customerCreditAuthData.get(CustomerCreditAuthConst.F.EndTime.apiName, Date.class);
        CreditAuthModel.Arg arg = new CreditAuthModel.Arg();
        arg.setObjectApiName(CustomerCreditAuthConst.API_NAME);
        arg.setObjectDataId(customerCreditAuthDataId);
        arg.setStartTime(startTime.getTime());
        arg.setEndTime(endTime.getTime());
        arg.setType(taskTypeEnum.type);
        return arg;
    }

    @Data
    public static class Result {

    }
}
