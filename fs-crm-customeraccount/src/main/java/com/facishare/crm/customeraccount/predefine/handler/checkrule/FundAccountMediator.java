package com.facishare.crm.customeraccount.predefine.handler.checkrule;

import com.facishare.crm.customeraccount.constants.AccountCheckRuleConstants;
import com.facishare.crm.customeraccount.constants.AccountRuleUseRecordConstants;
import com.facishare.crm.customeraccount.enums.ReduceTriggerActionEnum;
import com.facishare.crm.customeraccount.enums.StageEnum;
import com.facishare.crm.customeraccount.model.AccountRuleUseRecordsModel;
import com.facishare.crm.customeraccount.predefine.handler.HandlerFactory;
import com.facishare.crm.customeraccount.predefine.handler.RuleHandlerUtil;
import com.facishare.crm.customeraccount.predefine.manager.AccountCheckManager;
import com.facishare.crm.customeraccount.predefine.manager.CaRuleEngineManager;
import com.facishare.crm.customeraccount.predefine.manager.NewCustomerAccountManager;
import com.facishare.crm.customeraccount.predefine.service.dto.AccountCheckActionModel;
import com.facishare.crm.customeraccount.predefine.service.handler.AccountRuleFilter;
import com.facishare.crm.customeraccount.util.CaGrayUtil;
import com.facishare.crm.customeraccount.util.CustomerAccountLogUtil;
import com.facishare.crm.customeraccount.util.ExecuteUtil;
import com.facishare.crm.customeraccount.util.ObjectDataUtil;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class FundAccountMediator {
    @Autowired
    private AccountCheckManager accountCheckManager;
    @Autowired
    private CaRuleEngineManager caRuleEngineManager;
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private NewCustomerAccountManager newCustomerAccountManager;

    public AccountCheckActionModel.Result preValidate(ServiceContext serviceContext, AccountCheckActionModel.Arg arg) {
        AccountCheckActionModel.Result result = new AccountCheckActionModel.Result(arg.getObjectData(), arg.getDetails());
        User user = serviceContext.getUser();
        String buttonApiName = arg.getButtonApiName();
        IObjectData objectData = arg.getObjectData().toObjectData();
        String objectApiName = objectData.getDescribeApiName();
        String objectDataId = objectData.getId();
        String stage = arg.getActionStage();
        StageEnum stageEnum = StageEnum.of(stage);
        CustomerAccountLogUtil.CheckRuleRequestLog.builder().stage(stage).action("pre_validate").objectApiName(objectApiName).objectDataId(objectDataId).extra(objectDataId).buttonApiName(buttonApiName)
                .arg(arg).build().log(user);
        //如果不是前验证，则直接返回
        if (stageEnum != StageEnum.Pre) {
            return result;
        }
        //如果是新建前校验，并且id为空，来源于AddUI
        if (StringUtils.isEmpty(objectDataId) && ObjectAction.CREATE.getButtonApiName().equals(buttonApiName)) {
            return result;
        }

        List<IObjectData> ruleUseRecordDataList = accountCheckManager.queryCheckReduceAccountRuleUseRecord(user, objectApiName, objectDataId);
        if (RuleHandlerUtil.hasCheckValidateStageUseRecord(ruleUseRecordDataList)) {
            //已有使用记录 则不再匹配
            return result;
        }
        IObjectDescribe objectDescribe = serviceFacade.findObject(user.getTenantId(), objectApiName);
        getMatchedCheckReduceRule(user, buttonApiName, objectDescribe, objectData, true).ifPresent(checkRuleData -> {
            if (CaGrayUtil.allow(user.getTenantId(), CaGrayUtil.CHECK_RULE_PRE_ACTION_FROZEN_EIS) && isAddAction(buttonApiName)) {
                ValidateReducePreActionToFrozenHandler.Arg frozenPreArg = new ValidateReducePreActionToFrozenHandler.Arg(serviceContext.getRequestContext(), objectData, checkRuleData, buttonApiName);
                HandlerFactory.handle(HandlerTypeEnum.ValidateReducePreActionToFrozen, frozenPreArg);
            } else {
                ValidateReduceFrozenPreActionHandler.Arg frozenPreArg = new ValidateReduceFrozenPreActionHandler.Arg(serviceContext.getRequestContext(), objectData, checkRuleData);
                HandlerFactory.handle(HandlerTypeEnum.ValidateReduceFrozenPreAction, frozenPreArg);
            }
        });
        return result;
    }

    public AccountCheckActionModel.Result frozenAndDirectReduce(ServiceContext serviceContext, AccountCheckActionModel.Arg arg) {
        AccountCheckActionModel.Result result = new AccountCheckActionModel.Result(arg.getObjectData(), arg.getDetails());
        User user = serviceContext.getUser();
        String stage = arg.getActionStage();
        String buttonApiName = arg.getButtonApiName();
        IObjectData objectData = arg.getObjectData().toObjectData();
        CustomerAccountLogUtil.CheckRuleRequestLog.builder().action("frozen_and_direct_reduce").stage(stage).objectApiName(objectData.getDescribeApiName()).objectDataId(objectData.getId())
                .buttonApiName(buttonApiName).arg(arg).extra(objectData.getId()).build().log(user);
        StageEnum stageEnum = StageEnum.of(stage);
        if (stageEnum != StageEnum.POST) {
            return result;
        }
        IObjectDescribe objectDescribe = serviceFacade.findObject(user.getTenantId(), objectData.getDescribeApiName());
        //冻结操作
        RLock lock = accountCheckManager.lockUntilGet(user, objectData, StageEnum.POST);
        try {
            Map<String, IObjectData> buttonApiName2CheckRuleUseRecordMap = accountCheckManager.queryCheckReduceRecordToFrozen(user, objectData, Sets.newHashSet(buttonApiName));
            RequestContext requestContext = serviceContext.getRequestContext();
            Optional.ofNullable(buttonApiName2CheckRuleUseRecordMap.get(buttonApiName)).ifPresent(checkRuleUseRecordData -> {
                if (CaGrayUtil.allow(user.getTenantId(), CaGrayUtil.CHECK_RULE_PRE_ACTION_FROZEN_EIS) && isAddAction(buttonApiName)) {
                    ValidateReducePostActionCheckFrozenHandler.Arg frozenPostArg = new ValidateReducePostActionCheckFrozenHandler.Arg(serviceContext.getRequestContext(), buttonApiName, objectData.getDescribeApiName(), objectData.getId(), checkRuleUseRecordData);
                    HandlerFactory.handle(HandlerTypeEnum.ValidateReducePostActionCheckFrozen, frozenPostArg);
                } else {
                    HandlerFactory.handle(HandlerTypeEnum.ValidateReduceFrozenPostAction, new ValidateReduceFrozenPostActionHandler.Arg(requestContext, objectData, checkRuleUseRecordData));
                }
            });
            //匹配直接扣减 或者校验扣减的扣减
            post(serviceContext.getRequestContext(), objectDescribe, objectData, buttonApiName, null);
        } finally {
            accountCheckManager.unLock(lock);
        }
        return result;
    }

    public AccountCheckActionModel.Result postAction(ServiceContext serviceContext, AccountCheckActionModel.Arg arg) {
        AccountCheckActionModel.Result result = new AccountCheckActionModel.Result(arg.getObjectData(), arg.getDetails());
        User user = serviceContext.getUser();
        IObjectData objectData = arg.getObjectData().toObjectData();
        CustomerAccountLogUtil.CheckRuleRequestLog.builder().action("post_action").stage(arg.getActionStage()).objectApiName(objectData.getDescribeApiName()).objectDataId(objectData.getId())
                .buttonApiName(arg.getButtonApiName()).arg(arg).extra(objectData.getId()).build().log(user);
        StageEnum stageEnum = StageEnum.of(arg.getActionStage());
        if (stageEnum != StageEnum.POST) {
            return result;
        }
        IObjectDescribe objectDescribe = serviceFacade.findObject(user.getTenantId(), objectData.getDescribeApiName());
        String buttonApiName = arg.getButtonApiName();
        RLock lock = accountCheckManager.lockUntilGet(user, objectData, StageEnum.POST);
        try {
            post(serviceContext.getRequestContext(), objectDescribe, objectData, buttonApiName, null);
        } finally {
            accountCheckManager.unLock(lock);
        }
        return result;
    }

    public void post(RequestContext requestContext, IObjectDescribe objectDescribe, IObjectData objectData, String buttonApiName, AccountRuleFilter accountRuleFilter) {
        User user = requestContext.getUser();
        List<IObjectData> accountRuleUseRecordList = queryRuleUseRecordOfUnfreeze(user, objectDescribe, objectData, buttonApiName, accountRuleFilter);
        for (IObjectData accountRuleUseRecord : accountRuleUseRecordList) {
            try {
                HandlerFactory.handle(HandlerTypeEnum.ValidateReduceUnfreeze, new ValidateReduceUnfreezeHandler.Arg(requestContext, objectData, accountRuleUseRecord));
            } catch (Exception e) {
                CustomerAccountLogUtil.HandlerExceptionLog.builder().action("Handler_PostValidateRecordCheckRule").buttonApiName(buttonApiName).objectApiName(objectData.getDescribeApiName()).objectDataId(objectData.getId())
                        .errorMessage(e.toString()).build().log(user);
                log.warn("validateReduce error", e);
            }
        }
        getMatchedDirectReduceRule(user, buttonApiName, objectDescribe, objectData, accountRuleFilter).ifPresent(directCheckRule -> {
            HandlerFactory.handle(HandlerTypeEnum.DirectReduce, new DirectReduceHandler.Arg(requestContext, objectData, directCheckRule));
        });
    }

    public AccountCheckActionModel.Result editInvalid(ServiceContext serviceContext, AccountCheckActionModel.Arg arg) {
        AccountCheckActionModel.Result result = new AccountCheckActionModel.Result(arg.getObjectData(), arg.getDetails());
        IObjectData objectData = arg.getObjectData().toObjectData();
        User user = serviceContext.getUser();
        String objectApiName = objectData.getDescribeApiName();
        String buttonApiName = arg.getButtonApiName();
        RequestContext requestContext = serviceContext.getRequestContext();
        CustomerAccountLogUtil.CheckRuleRequestLog.builder().stage(arg.getActionStage()).action("edit_invalid").objectApiName(objectData.getDescribeApiName()).objectDataId(objectData.getId())
                .buttonApiName(buttonApiName).arg(arg).extra(objectData.getId()).build().log(user);

        if (!isEditAction(buttonApiName) && !ObjectAction.INVALID.getButtonApiName().equals(buttonApiName)) {
            return result;
        }
        String stage = arg.getActionStage();
        StageEnum stageEnum = StageEnum.of(stage);
        IObjectDescribe objectDescribe = serviceFacade.findObject(user.getTenantId(), objectApiName);
        //stageEnum=pre时，lock=null
        RLock lock = null;
        try {
            lock = accountCheckManager.lockUntilGet(user, objectData, stageEnum);
            //查询是否有使用记录
            AccountRuleUseRecordsModel accountRuleUseRecordsModel = accountCheckManager.queryCheckRuleUseRecordMap(user, objectApiName, objectData.getId());
            if (accountRuleUseRecordsModel.isEmpty() && isEditAction(buttonApiName) && StageEnum.Pre.equals(stageEnum) && ObjectDataExt.of(objectData).isIneffective()) {
                //匹配新建按钮;驳回又编辑时，当作新建数据，匹配新建按钮触发的校验规则
                getMatchedCheckReduceRule(user, ObjectAction.CREATE.getButtonApiName(), objectDescribe, objectData, true).ifPresent(accountCheckRule -> {
                    HandlerFactory.handle(HandlerTypeEnum.ValidateReduceFrozenPreEdit, new ValidateReduceFrozenPreEditHandler.Arg(requestContext, objectData, accountCheckRule));
                });
            } else if (accountRuleUseRecordsModel.isEmpty()) {
                return result;
            }

            List<RuleUseRecord> ruleUseRecordList = accountRuleUseRecordsModel.toRuleUseRecordList();
            if (ObjectAction.INVALID.getButtonApiName().equals(buttonApiName)) {
                //作废逻辑没有变化，就共用了
                HandlerFactory.handle(HandlerTypeEnum.Invalid, new CheckRuleInvalidHandler.Arg(requestContext, objectData, stageEnum));
                return result;
            }
            BatchCustomerAccountValidate batchCustomerAccountValidate = BatchCustomerAccountValidate.build();
            ExecuteUtil.IsolationTask isolationTask = ExecuteUtil.createIsolateTask();
            for (RuleUseRecord ruleUseRecord : ruleUseRecordList) {
                IObjectData accountRuleUseRecordData = ruleUseRecord.getAccountRuleUseRecordData();
                HandlerTypeEnum handlerTypeEnum = ruleUseRecord.getHandlerTypeEnum();
                AdaptEditArg adaptEditArg = new AdaptEditArg(requestContext, objectData, accountRuleUseRecordData, stageEnum);
                AdaptEditResult adaptEditResult = HandlerFactory.handle(handlerTypeEnum, adaptEditArg);
                batchCustomerAccountValidate.merge(adaptEditResult.getCustomerAccountDataMap().values(), adaptEditResult.getCustomerAccountHandleMap());
                if (stageEnum == StageEnum.POST) {//post才执行
                    isolationTask.submit(() -> newCustomerAccountManager.invalidAndSavaUpdateCustomerAccount(user, adaptEditResult.getToDeletedFlowDataList(), adaptEditResult.getToDeletedUnfreezeDataList(), adaptEditResult.getToDeletedFrozenDataList(),
                                    adaptEditResult.getToAddFlowDataList(), adaptEditResult.getToAddUnfreezeDataList(), adaptEditResult.getToAddFrozenDataList(), Lists.newArrayList(adaptEditResult.getCustomerAccountDataMap().values()), adaptEditResult.getCustomerAccountHandleMap())
                            , e -> CustomerAccountLogUtil.HandlerExceptionLog.builder().action("EditAdaptError").objectApiName(objectApiName).objectDataId(objectData.getId()).buttonApiName(buttonApiName).stage(stageEnum.stage)
                                    .errorMessage(String.format("RuleUseRecordId=%s,%s", accountRuleUseRecordData.getId(), e.getMessage())).build().logIfPost(user)
                    );
                }
            }
            //校验客户账户余额 pre与post都执行validate
            batchCustomerAccountValidate.validate(user, objectData, stageEnum, arg.getButtonApiName());
            //stageEnum 为pre时，isolateTask中任务为空，即post时才会有执行任务
            isolationTask.execute();
        } catch (Exception e) {
            //上报异常
            log.warn("editInvalid error,arg;{}", arg, e);
            CustomerAccountLogUtil.HandlerExceptionLog.builder().action("EditInvalidError").objectApiName(objectApiName).objectDataId(objectData.getId()).buttonApiName(buttonApiName).stage(stageEnum.stage)
                    .errorMessage(e.getMessage()).build().logIfPost(user);
            throw e;
        } finally {
            accountCheckManager.unLock(lock);
        }
        return result;
    }

    private boolean isEditAction(String buttonApiName) {
        return ObjectAction.UPDATE.getButtonApiName().equals(buttonApiName) || ObjectAction.UPDATE_SAVE.getButtonApiName().equals(buttonApiName);
    }

    private boolean isAddAction(String buttonApiName) {
        return ObjectAction.CREATE.getButtonApiName().equals(buttonApiName) || ObjectAction.CREATE_SAVE.getButtonApiName().equals(buttonApiName);
    }

    private List<IObjectData> queryRuleUseRecordOfUnfreeze(User user, IObjectDescribe objectDescribe, IObjectData objectData, String buttonApiName, AccountRuleFilter accountRuleFilter) {
        Map<String, Object> dataMap = ObjectDataUtil.toDataMap(objectDescribe, objectData);
        List<IObjectData> checkRecordList = accountCheckManager.queryCheckReduceRecordToUnfreeze(user, objectData, buttonApiName);
        List<IObjectData> checkRecordResultList = Lists.newArrayList();
        Map<String, IObjectData> ruleCode2CheckRecordMap = Maps.newHashMap();
        List<IObjectData> accountRuleList = Lists.newArrayList();
        for (IObjectData checkRecord : checkRecordList) {
            String dataJson = checkRecord.get(AccountRuleUseRecordConstants.Field.CheckRule.apiName, String.class);
            IObjectData checkRuleData = new ObjectData();
            checkRuleData.fromJsonString(dataJson);
            String ruleCode = checkRuleData.get(AccountCheckRuleConstants.Field.ReduceTriggerConditionRuleCode.apiName, String.class);
            if (StringUtils.isEmpty(ruleCode)) {
                //没有条件
                checkRecordResultList.add(checkRecord);
            } else {
                ruleCode2CheckRecordMap.put(ruleCode, checkRecord);
                accountRuleList.add(checkRuleData);
            }
        }
        Set<String> ruleCodes = Sets.newHashSet(ruleCode2CheckRecordMap.keySet());
        if (Objects.nonNull(accountRuleFilter)) {
            accountRuleList = accountRuleFilter.filter(accountRuleList, Lists.newArrayList(AccountCheckRuleConstants.Field.ReduceTriggerCondition.apiName));
            ruleCodes = accountRuleList.stream().map(x -> x.get(AccountCheckRuleConstants.Field.ReduceTriggerConditionRuleCode.apiName, String.class)).collect(Collectors.toSet());
        }
        Set<String> matchedRuleCodes = caRuleEngineManager.getMatchedRuleCodes(user, dataMap, ruleCodes);
        checkRecordResultList.addAll(matchedRuleCodes.stream().map(ruleCode2CheckRecordMap::get).collect(Collectors.toList()));
        CustomerAccountLogUtil.QueryDataLog.builder().action(String.format("Query_%s_ToUnfreeze_MatchedRecord", AccountRuleUseRecordConstants.API_NAME)).objectApiName(objectData.getDescribeApiName()).objectDataId(objectData.getId()).buttonApiName(buttonApiName)
                .queryObjectApiName(AccountRuleUseRecordConstants.API_NAME).dataList(checkRecordResultList).build().log(user);
        return checkRecordResultList;
    }

    public Optional<IObjectData> getMatchedDirectReduceRule(User user, String buttonApiName, IObjectDescribe objectDescribe, IObjectData objectData, AccountRuleFilter accountRuleFilter) {
        Map<String, Object> dataMap = ObjectDataUtil.toDataMap(objectDescribe, objectData);
        String describeApiName = objectData.getDescribeApiName();
        String dataId = objectData.getId();
        //查询是否有使用记录,只触发一次
        Optional<IObjectData> directReduceRecordOptional = accountCheckManager.queryDirectReduceRecord(user, describeApiName, dataId);
        if (directReduceRecordOptional.isPresent()) {
            return Optional.empty();
        }
        List<IObjectData> directCheckRuleList = accountCheckManager.queryDirectReduceCheckRule(user, describeApiName, buttonApiName);
        if (Objects.nonNull(accountRuleFilter)) {
            directCheckRuleList = accountRuleFilter.filter(directCheckRuleList, Lists.newArrayList(AccountCheckRuleConstants.Field.ReduceTriggerCondition.apiName));
        }
        return accountCheckManager.getMatchedDirectReduceRule(user, objectDescribe, dataMap, directCheckRuleList);
    }

    public Optional<IObjectData> getMatchedCheckReduceRule(User user, String buttonApiName, IObjectDescribe objectDescribe, IObjectData objectData, boolean throwException) {
        Map<String, Object> dataMap = ObjectDataUtil.toDataMap(objectDescribe, objectData);
        //查询是否有匹配当前动作的校验规则
        ReduceTriggerActionEnum reduceTriggerActionEnum = ReduceTriggerActionEnum.FieldChange.getValue().equals(buttonApiName) ? ReduceTriggerActionEnum.FieldChange : ReduceTriggerActionEnum.Button;
        List<IObjectData> accountCheckRuleList = accountCheckManager.queryCheckReduceRule(user, objectData.getDescribeApiName(), reduceTriggerActionEnum, buttonApiName);
        return accountCheckManager.getMatchedCheckReduceRule(user, objectDescribe, dataMap, accountCheckRuleList, throwException);
    }
}
