package com.facishare.crm.customeraccount.predefine.manager;

import com.facishare.crm.customeraccount.constants.SystemConstants;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CaAccountsReceivableManager {
    @Autowired
    private BizConfigManager bizConfigManager;

    @Autowired
    private CommonManager commonManager;

    /**
     * 是否已被核销单关联，（包括已作废的核销单）
     */
    public boolean hasLinkMatchNote(User user, String tenantId, String paymentId) {
        boolean isAccountAndReceivableOpen = bizConfigManager.isAccountAndReceivableOpen(tenantId);
        if (!isAccountAndReceivableOpen) {
            return false;
        }


        List<IObjectData> matchNoteDatas = commonManager.queryInvalidDataByField(
                user, SystemConstants.MatchNoteApiName, "payment_id", Lists.newArrayList(paymentId), 0, 1).getData();

        if (CollectionUtils.empty(matchNoteDatas)) {
            return false;
        }

        return true;
    }
}