package com.facishare.crm.customeraccount.predefine.service.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

/**
 * 账户授权
 */
@Data
public class GetOutcomeAuthAccountsModel {

    @Data
    public static class Arg {
        private String authorizedObjectApiName;
        private String authorizedObjectDataId;
        private String authorizedType;
        private String customerId;
        private Boolean needEntryCustomerFieldApiName = false;
        private Boolean needRebateAccountMappingFieldApiName = false;
        private List<String> fundAccountIds;
    }

    @Data
    public static class Result {
        private List<DetailFields> datas = Lists.newArrayList();

        @SerializedName("mc_functional_currency__r")
        @JsonProperty("mc_functional_currency__r")
        private String mcFunctionalCurrencyR;

        @SerializedName("entry_customer_fieldapiname")
        @JsonProperty("entry_customer_fieldapiname")
        private String entryCustomerFieldApiName;

        @SerializedName("account_check_rule_status")
        @JsonProperty("account_check_rule_status")
        private String accountCheckRuleStatus;

        @SerializedName("rebate_account_mapping_field_api_names")
        @JsonProperty("rebate_account_mapping_field_api_names")
        private List<String> rebateAccountMappingFieldApiNames;
    }

    @Data
    public static class DetailFields {
        @SerializedName("trade_amount_fieldapiname")
        @JsonProperty("trade_amount_fieldapiname")
        private String tradeAmountFieldApiName;

        @SerializedName("authorize_account_id")
        @JsonProperty("authorize_account_id")
        private String authorizeAccountId;

        @SerializedName("account_name")
        @JsonProperty("account_name")
        private String accountName;

        @SerializedName("type")
        @JsonProperty("type")
        private String type;

        @SerializedName("account_type")
        @JsonProperty("account_type")
        private String accountType;

        @SerializedName("access_module")
        @JsonProperty("access_module")
        private String accessModule;

//        @SerializedName("account_balance")
//        @JsonProperty("account_balance")
//        private String accountBalance;

        /**
         *  货补数量账户没有值，货补数量账户用total_unused_amount
         */
        @SerializedName("available_balance")
        @JsonProperty("available_balance")
        private String availableBalance;

        /**
         *  可用合计数量
         *  货补数量账户才有值
         */
        @SerializedName("total_unused_amount")
        @JsonProperty("total_unused_amount")
        private String totalUnusedAmount;
    }
}