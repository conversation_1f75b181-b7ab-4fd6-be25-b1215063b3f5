package com.facishare.crm.customeraccount.model;

import com.facishare.crm.customeraccount.enums.StageEnum;
import com.facishare.crm.customeraccount.predefine.manager.NewCustomerAccountManager;
import com.facishare.crm.customeraccount.util.CustomerAccountLogUtil;
import com.facishare.crm.customeraccount.util.ExecuteUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Objects;


@Slf4j
@Getter
public class DataTriggerFunctionModel {
    private final HashBasedTable<String, String, List<IObjectData>> buttonObjectDataListTable = HashBasedTable.create();

    public static DataTriggerFunctionModel create() {
        return new DataTriggerFunctionModel();
    }

    public void merge(DataTriggerFunctionModel dataTriggerFunctionModel) {
        if (Objects.isNull(dataTriggerFunctionModel)) {
            return;
        }
        dataTriggerFunctionModel.buttonObjectDataListTable.rowMap().forEach((k, v) -> v.forEach((ik, iv) -> buttonObjectDataListTable.put(k, ik, iv)));
    }

    public void appendData(String buttonApiName, IObjectData objectData) {
        String objectApiName = objectData.getDescribeApiName();
        List<IObjectData> objectDataList = buttonObjectDataListTable.get(buttonApiName, objectApiName);
        if (Objects.isNull(objectDataList)) {
            objectDataList = Lists.newArrayList();
            buttonObjectDataListTable.put(buttonApiName, objectApiName, objectDataList);
        }
        objectDataList.add(objectData);
    }

    public DataTriggerFunctionModel appendData(String buttonApiName, List<IObjectData> objectDataList) {
        CollectionUtils.nullToEmpty(objectDataList).forEach(x -> appendData(buttonApiName, x));
        return this;
    }

    public DataTriggerFunctionModel appendDataTriggerCreate(IObjectData objectData) {
        appendData(ObjectAction.CREATE.getButtonApiName(), objectData);
        return this;
    }

    public DataTriggerFunctionModel appendDataTriggerCreate(List<IObjectData> objectDataList) {
        appendData(ObjectAction.CREATE.getButtonApiName(), objectDataList);
        return this;
    }

    public DataTriggerFunctionModel appendDataTriggerInvalid(IObjectData objectData) {
        appendData(ObjectAction.INVALID.getButtonApiName(), objectData);
        return this;
    }

    public DataTriggerFunctionModel appendDataTriggerInvalid(List<IObjectData> objectDataList) {
        appendData(ObjectAction.INVALID.getButtonApiName(), objectDataList);
        return this;
    }

    public void triggerFunctionIgnoreException(User user, StageEnum stageEnum, NewCustomerAccountManager newCustomerAccountManager, boolean ignoreException) {
        if (buttonObjectDataListTable.isEmpty()) {
            return;
        }
        ExecuteUtil.IsolationTask isolationTask = ExecuteUtil.createIsolateTask();
        //先执行作废按钮的，再执行新建按钮的
        Map<String, Map<String, List<IObjectData>>> buttonObjectDataListMap = buttonObjectDataListTable.rowMap();
        Map<String, List<IObjectData>> objectDataListMap = buttonObjectDataListMap.remove(ObjectAction.INVALID.getButtonApiName());
        if (Objects.nonNull(objectDataListMap)) {
            objectDataListMap.forEach((objectApiName, dataList) ->
                    isolationTask.submit(() -> newCustomerAccountManager.triggerFunctionByInner(user, ObjectAction.INVALID.getButtonApiName(), dataList, stageEnum)));
        }
        buttonObjectDataListMap.forEach((buttonApiName, objectApiNameDataListMap) ->
                objectApiNameDataListMap.forEach((objectApiName, dataList) ->
                        isolationTask.submit(() -> newCustomerAccountManager.triggerFunctionByInner(user, buttonApiName, dataList, stageEnum))));
        try {
            isolationTask.execute();
        } catch (Exception e) {
            log.warn("triggerFunction error", e);
            CustomerAccountLogUtil.sendAuditLog(user, "checkRuleTriggerFunction", "", "", e.getMessage(), "triggerException");
            if (!ignoreException) {
                throw e;
            }
        }
    }
}
