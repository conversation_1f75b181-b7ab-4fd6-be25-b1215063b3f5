package com.facishare.crm.customeraccount.predefine.service.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import java.util.List;
import lombok.Builder;
import lombok.Data;

public class GetAuthObjectsModel {

    @Data
    public static class Arg {
        private String authorizedType;
    }

    @Data
    @Builder
    public static class Result {
        private List<AuthObject> objects;
    }

    @Data
    @Builder
    public static class AuthObject {

        @SerializedName("display_name")
        @JsonProperty("display_name")
        private String displayName;

        @SerializedName("api_name")
        @JsonProperty("api_name")
        private String apiName;
    }


}
