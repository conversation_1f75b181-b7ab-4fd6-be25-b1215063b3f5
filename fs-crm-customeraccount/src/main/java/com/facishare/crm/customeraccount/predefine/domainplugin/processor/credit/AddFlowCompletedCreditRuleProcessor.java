package com.facishare.crm.customeraccount.predefine.domainplugin.processor.credit;

import com.facishare.crm.consts.CreditRuleMatchRecordConst;
import com.facishare.crm.customeraccount.constants.CAI18NKey;
import com.facishare.crm.customeraccount.enums.CreditRuleNodeTypeEnum;
import com.facishare.crm.customeraccount.predefine.domainplugin.model.CreditRuleAddFlowCompletedContextModel;
import com.facishare.crm.customeraccount.predefine.manager.CreditManager;
import com.facishare.crm.customeraccount.predefine.manager.NewCustomerAccountManager;
import com.facishare.crm.customeraccount.util.CreditUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.domain.FlowCompletedActionDomainPlugin;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class AddFlowCompletedCreditRuleProcessor extends CreditRuleByDetailProcessor<FlowCompletedActionDomainPlugin.Arg, FlowCompletedActionDomainPlugin.Result, CreditRuleAddFlowCompletedContextModel> {
    public AddFlowCompletedCreditRuleProcessor(NewCustomerAccountManager newCustomerAccountManager, CreditManager creditManager, ServiceFacade serviceFacade) {
        super(newCustomerAccountManager, creditManager, serviceFacade);
    }

    @Override
    protected CreditRulePluginContextKey getContextKey() {
        return CreditRulePluginContextKey.FlowCompleted;
    }

    @Override
    protected Class<CreditRuleAddFlowCompletedContextModel> getContextClass() {
        return CreditRuleAddFlowCompletedContextModel.class;
    }

    @Override
    protected FlowCompletedActionDomainPlugin.Result newResultInstance() {
        return new FlowCompletedActionDomainPlugin.Result();
    }

    @Override
    protected IObjectData getObjectData(FlowCompletedActionDomainPlugin.Arg arg) {
        return arg.getObjectData().toObjectData();
    }

    @Override
    protected List<IObjectData> getDetailDataList(FlowCompletedActionDomainPlugin.Arg arg, String detailApiName) {
        return ObjectDataDocument.ofDataList(CollectionUtils.nullToEmpty(arg.getDetailObjectData()).getOrDefault(detailApiName, Lists.newArrayList()));
    }

    @Override
    protected CreditRuleAddFlowCompletedContextModel doPreAct(RequestContext requestContext, FlowCompletedActionDomainPlugin.Arg arg) {
        boolean pass = isPass(arg);
        CreditRuleAddFlowCompletedContextModel contextModel = new CreditRuleAddFlowCompletedContextModel(arg.getObjectApiName(), arg.getObjectData().getId());
        if (!pass) {
            IObjectData objectData = arg.getObjectData().toObjectData();
            User user = requestContext.getUser();
            String objectApiName = objectData.getDescribeApiName();
            String objectDataId = objectData.getId();
            List<IObjectData> creditRuleMatchRecordList = creditManager.findCreditRuleMatchRecordByData(user, objectApiName, Sets.newHashSet(objectDataId));
            if (CollectionUtils.empty(creditRuleMatchRecordList)) {
                return contextModel;
            }
            IObjectData creditRuleMatchRecordData = creditRuleMatchRecordList.get(0);
            String creditOccupiedRuleId = creditRuleMatchRecordData.get(CreditRuleMatchRecordConst.F.CreditOccupiedRuleId.apiName, String.class);
            contextModel.setCreditRuleMatchRecordId(creditRuleMatchRecordData.getId());
            contextModel.setCreditOccupiedRuleId(creditOccupiedRuleId);
            if (canInvalidOrReject(requestContext, objectData, creditRuleMatchRecordData)) {
                return contextModel;
            } else {
                throw new ValidateException(CAI18NKey.CREDIT_FLOW_TRANSFERRED_NOT_SUPPORT_ACTION);
            }
        }
        return contextModel;
    }

    @Override
    public void doFinallyDo(RequestContext requestContext, FlowCompletedActionDomainPlugin.Arg arg, CreditRuleAddFlowCompletedContextModel contextModel) {
        boolean pass = isPass(arg);
        String creditRuleMatchRecordId = contextModel.getCreditRuleMatchRecordId();
        if (StringUtils.isEmpty(creditRuleMatchRecordId) || pass) {
            return;
        }
        String objectApiName = arg.getObjectApiName();
        String creditOccupiedRuleId = contextModel.getCreditOccupiedRuleId();
        List<IObjectData> creditOccupiedRuleDetailList = creditManager.findCreditRuleDetailByMasterAndOrderBySeq(requestContext.getUser(), creditOccupiedRuleId);
        CreditRuleNodeTypeEnum nodeTypeEnum = CreditUtil.getNodeType(objectApiName, creditOccupiedRuleDetailList);
        //处理驳回逻辑
        createReject(requestContext, arg, nodeTypeEnum);
    }

    protected void createReject(RequestContext requestContext, FlowCompletedActionDomainPlugin.Arg arg, CreditRuleNodeTypeEnum nodeTypeEnum) {
        if (nodeTypeEnum.firstNode()) {
            //既是首节点，又是末节点
            doInvalidDataFirstNode(requestContext, arg.getObjectData().toObjectData());
        } else if (nodeTypeEnum.lastNode()) {
            //末节点
            String detailObjectApiName = CreditUtil.getCreditCurAndPreConfig(requestContext.getTenantId(), arg.getObjectApiName(), null).getDetailObjectApiName();
            doInvalidDataNotFirstNode(requestContext, arg.getObjectData().toObjectData(), ObjectDataDocument.ofDataList(arg.getDetailObjectData().getOrDefault(detailObjectApiName, Lists.newArrayList())));
        }
    }
}
