package com.facishare.crm.customeraccount.predefine.domainplugin.processor.credit;

import com.facishare.crm.customeraccount.predefine.domainplugin.model.CreditRuleInvalidContextModel;
import com.facishare.crm.customeraccount.predefine.manager.CreditManager;
import com.facishare.crm.customeraccount.predefine.manager.NewCustomerAccountManager;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.predef.domain.FlowCompletedActionDomainPlugin;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class InvalidFlowCompletedCreditRuleProcessor extends CreditRuleByDetailProcessor<FlowCompletedActionDomainPlugin.Arg, FlowCompletedActionDomainPlugin.Result, CreditRuleInvalidContextModel> {
    public InvalidFlowCompletedCreditRuleProcessor(NewCustomerAccountManager newCustomerAccountManager, CreditManager creditManager, ServiceFacade serviceFacade) {
        super(newCustomerAccountManager, creditManager, serviceFacade);
    }

    @Override
    protected CreditRulePluginContextKey getContextKey() {
        return CreditRulePluginContextKey.FlowCompleted;
    }

    @Override
    protected Class<CreditRuleInvalidContextModel> getContextClass() {
        return CreditRuleInvalidContextModel.class;
    }

    @Override
    protected FlowCompletedActionDomainPlugin.Result newResultInstance() {
        return new FlowCompletedActionDomainPlugin.Result();
    }

    @Override
    protected IObjectData getObjectData(FlowCompletedActionDomainPlugin.Arg arg) {
        return arg.getObjectData().toObjectData();
    }

    @Override
    protected List<IObjectData> getDetailDataList(FlowCompletedActionDomainPlugin.Arg arg, String detailApiName) {
        return ObjectDataDocument.ofDataList(CollectionUtils.nullToEmpty(arg.getDetailObjectData()).getOrDefault(detailApiName, Lists.newArrayList()));
    }

    @Override
    protected CreditRuleInvalidContextModel doPreAct(RequestContext requestContext, FlowCompletedActionDomainPlugin.Arg arg) {
        CreditRuleInvalidContextModel contextModel = new CreditRuleInvalidContextModel();
        boolean pass = isPass(arg);
        if (!pass) {
            return contextModel;
        }
        //校验逻辑，第一个阶段或者中间节点，则不能作废
        return doInvalidPreAct(requestContext, arg.getObjectData().toObjectData());
    }

    @Override
    public void doFinallyDo(RequestContext requestContext, FlowCompletedActionDomainPlugin.Arg arg, CreditRuleInvalidContextModel contextModel) {
        boolean pass = isPass(arg);
        if (pass) {
            doInvalidFinally(requestContext, arg.getObjectData().toObjectData(), contextModel);
        }
    }
}
