package com.facishare.crm.customeraccount.predefine.domainplugin;

import com.facishare.paas.appframework.core.model.GlobalTransactionConstant;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.transaction.core.status.BranchStatus;
import com.fxiaoke.transaction.core.util.GlobalTransactionConstants;
import com.fxiaoke.transaction.core.util.JacksonUtil;
import com.fxiaoke.transaction.tcc.api.context.BranchTransactionalContext;
import com.fxiaoke.transaction.tcc.api.context.GlobalTransactionApplicationData;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@Slf4j
public class BranchTransactionUtil {
    public static final String ADD_PRE_KEY = "addPreActionContext";
    public static final String EDIT_PRE_KEY = "editPreActionContext";
    public static final String START_MARK_KEY = "startMark";

    public static <T> void setContextData(String key, T t) {
        log.info("setBranchContextData key:{},value:{}", key, t);
        BranchTransactionalContext branchTransactionalContext = BranchTransactionalContext.getCurrent();
        Map<String, Object> actionContext = branchTransactionalContext.getActionContext();
        if (Objects.isNull(actionContext)) {
            actionContext = Maps.newHashMap();
            branchTransactionalContext.setActionContext(actionContext);
        }
        actionContext.put(key, t);
    }

    public static <T> T getContextData(BranchTransactionalContext branchTransactionalContext, String key, Class<T> clazz) {
        Map<String, Object> actionContext = branchTransactionalContext.getActionContext();
        if (Objects.isNull(actionContext)) {
            return null;
        }
        Object value = get(branchTransactionalContext, key);
        if (Objects.isNull(value)) {
            return null;
        }
        if (value.getClass().equals(clazz)) {
            return (T) value;
        }
        return JacksonUtil.fromJson(JacksonUtil.toJson(value), clazz);
    }

    public static boolean getWriteDbFlag(BranchTransactionalContext branchTransactionalContext) {
        GlobalTransactionApplicationData globalTransactionApplicationData = branchTransactionalContext.getGlobalTransactionApplicationData();
        return globalTransactionApplicationData.getAttribute(GlobalTransactionConstant.OBJECT_SAVE_WRITE_DB_FLAG);
    }

    public static String getSaveLifeStatus(BranchTransactionalContext branchTransactionalContext) {
        GlobalTransactionApplicationData globalTransactionApplicationData = branchTransactionalContext.getGlobalTransactionApplicationData();
        if (Objects.isNull(globalTransactionApplicationData)) {
            return null;
        }
        return globalTransactionApplicationData.getAttribute(GlobalTransactionConstant.OBJECT_SAVE_LIFE_STATUS);
    }

    public static void resetSaveLifeStatus(BranchTransactionalContext branchTransactionalContext, IObjectData objectData) {
        String saveLifeStatus = getSaveLifeStatus(branchTransactionalContext);
        if (StringUtils.isNotEmpty(saveLifeStatus)) {
            ObjectDataExt.of(objectData).setLifeStatus(saveLifeStatus);
        }
    }

    public static Set<String> getInvalidSuccessDataIds(BranchTransactionalContext context) {
        List<String> invalidDataIds = context.getGlobalTransactionApplicationData().getAttribute(GlobalTransactionConstant.OBJECT_INVALID_SUCCESS_IDS);
        return Objects.isNull(invalidDataIds) ? Sets.newHashSet() : Sets.newHashSet(invalidDataIds);
    }

    public static void markStartAndLog(BranchTransactionalContext context) {
        log(context);
        setContextData(START_MARK_KEY, new Object());
    }

    private static Object get(BranchTransactionalContext branchTransactionalContext, String key) {
        Map<String, Object> actionContext = branchTransactionalContext.getActionContext();
        if (Objects.isNull(actionContext)) {
            return null;
        }
        return actionContext.get(key);
    }

    public static boolean markStartFail(BranchTransactionalContext branchContext) {
        Object value = get(branchContext, START_MARK_KEY);
        return !Objects.nonNull(value);
    }

    public static BranchExcuteStatusEnum getExecuteStatus(BranchTransactionalContext branchContext) {
        return branchContext.getRegisteredBranchTransactions().stream().filter(x -> x.getBranchId().equals(branchContext.getBranchId()))
                .map(x -> {
                    BranchStatus branchStatus = x.getStatus();
                    if (branchStatus == BranchStatus.Registered) {
                        return BranchExcuteStatusEnum.INITIAL;
                    } else if (branchStatus == BranchStatus.Cancelled || branchStatus == BranchStatus.Confirmed) {
                        return BranchExcuteStatusEnum.SUCCESS;
                    } else {
                        return BranchExcuteStatusEnum.FAIL;
                    }
                }).findFirst().orElse(BranchExcuteStatusEnum.FAIL);
    }

    public static void log(BranchTransactionalContext branchTransactionalContext) {
        if (Objects.isNull(branchTransactionalContext)) {
            log.warn("checkRuleBranch branchContext is null");
            return;
        }
        log.info("checkRuleBranch,branchTransactionalContext:{}", branchTransactionalContext);
    }
}
