package com.facishare.crm.customeraccount.enums;

import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;

public enum PayStatementSourceAppEnum {
    ShoppingMall("shopping_mall", "商城"),
    CodeCollection("code_collection", "二维码收款"),
    Other("other", "其他"),
    ;
    private final String value;
    private final String label;

    PayStatementSourceAppEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public static PayStatementSourceAppEnum getByCode(String status) {
        for (PayStatementSourceAppEnum srcType : values()) {
            if (Objects.equals(status, srcType.value)) {
                return srcType;
            }
        }
        throw new IllegalArgumentException("status error");
    }

    public String getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }

    public static List<String> getAllStatus() {
        List<String> allStatus = Lists.newArrayList();
        for (PayStatementSourceAppEnum d : PayStatementSourceAppEnum.values()) {
            allStatus.add(d.getValue());
        }
        return allStatus;
    }
}
