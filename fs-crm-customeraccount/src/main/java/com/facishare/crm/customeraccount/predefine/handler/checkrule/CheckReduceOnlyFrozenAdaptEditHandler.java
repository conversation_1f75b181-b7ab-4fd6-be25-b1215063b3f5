package com.facishare.crm.customeraccount.predefine.handler.checkrule;

import com.facishare.crm.customeraccount.predefine.service.dto.ObjectMappingModel;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class CheckReduceOnlyFrozenAdaptEditHandler extends CheckReduceFrozenAndUnfreezeAdaptEditHandler {

    @Override
    public HandlerTypeEnum getHandlerTypeEnum() {
        return HandlerTypeEnum.FrozenOnlyAdaptEdit;
    }

    @Override
    protected Map<CustomerFundAccount, IObjectData> getNewCustomerFundAccount2FlowOfUnfreezeDataMap(User user, IObjectData objectData, List<ObjectMappingModel> reduceMappingList) {
        return Maps.newHashMap();
    }
}
