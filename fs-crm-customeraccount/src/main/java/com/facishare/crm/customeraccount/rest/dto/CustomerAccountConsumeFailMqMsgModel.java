package com.facishare.crm.customeraccount.rest.dto;

import com.facishare.crmcommon.rest.dto.SailAdminResult;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;


public class CustomerAccountConsumeFailMqMsgModel {

    @Data
    public static class CreateArg {
        private String originObjectApiName;
        private String msgId;
        private String mqMsg;
        private String errorMsg;
        private Integer failTimes;

        private String tenantId;
        private String createBy;
        private String updateBy;
    }

    @Data
    public static class UpdateArg {
        private String id;
        private String errorMsg;

        private String tenantId;
        private String updateBy;
    }

    @Data
    public static class DeleteArg {
        private String id;
        private String tenantId;
    }

    @Data
    public static class ListArg {
        private Integer idBiggerThan;
        private Integer failTimesLessThan;
        private Integer offset;
        private Integer limit;
    }

    @Data
    public static class GetByIdArg {
        private String id;
    }

    @Data
    public static class CustomerAccountConsumeFailMqMsgList {
        private List<CustomerAccountConsumeFailMqMsg> msgs;
    }

    @Data
    public static class CustomerAccountConsumeFailMqMsg {
        private String id;
        private String tenantId;
        private String originObjectApiName;
        private String msgId;
        private String mqMsg;
        private Integer failTimes;
    }

    @Data
    public static class ListResult extends SailAdminResult {
        @SerializedName("Value")
        private CustomerAccountConsumeFailMqMsgList value;
    }

    @Data
    public static class CustomerAccountConsumeFailMqMsgResult {
        private CustomerAccountConsumeFailMqMsg msg;
    }

    @Data
    public static class GetByIdResult extends SailAdminResult {
        @SerializedName("Value")
        private CustomerAccountConsumeFailMqMsgResult value;
    }
}
