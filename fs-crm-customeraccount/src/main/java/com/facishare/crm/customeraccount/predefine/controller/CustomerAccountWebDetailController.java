package com.facishare.crm.customeraccount.predefine.controller;

import com.facishare.crm.customeraccount.util.ObjectDataUtil;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.appframework.metadata.ObjectDataExt;

public class CustomerAccountWebDetailController extends StandardWebDetailController {
    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        ObjectDataUtil.setAvailableCredit(controllerContext.getUser(), ObjectDataExt.of(result.getData()));
        return result;
    }
}
