//package com.facishare.crm.customeraccount.util;
//
//import com.facishare.crm.customeraccount.enums.ConfigKeyEnum;
//import com.facishare.crm.customeraccount.predefine.service.dto.CheckTriggerActionModel;
//import com.facishare.crm.customeraccount.predefine.service.dto.CustomerAccountType;
//import com.facishare.crm.customeraccount.predefine.service.dto.ReduceRuleActionModel;
//import com.facishare.paas.appframework.common.util.Tuple;
//import com.facishare.paas.appframework.config.ConfigService;
//import com.facishare.paas.appframework.core.model.User;
//import com.facishare.paas.metadata.util.SpringUtil;
//import com.facishare.rest.core.util.JsonUtil;
//import com.google.common.base.Strings;
//import com.google.common.collect.Lists;
//import org.apache.commons.collections4.CollectionUtils;
//
//import java.util.*;
//import java.util.stream.Collectors;
//
//public class AccountCheckRuleJsonUtil {
//    private static final ConfigService configService = SpringUtil.getContext().getBean(ConfigService.class);
//
//    public static CustomerAccountType.GetCheckObjectsAndReduceRuleObjects getCheckObjects(String tenantId, String key) {
//        // 从配置中心读，如果配置中心没有，就用默认的配置；有的需要补充配置中心的默认部分
//        String checkObjectsAndReduceRuleObjectsJson = configService.findTenantConfig(new User(tenantId, User.SUPPER_ADMIN_USER_ID), key);
//
//        //如果配置中心没有，就用默认的配置
//        if (Strings.isNullOrEmpty(checkObjectsAndReduceRuleObjectsJson)) {
//            return ConfigCenter.getCheckObjectsAndReduceRuleObjects(true, null);
//        }
//
//        CustomerAccountType.GetCheckObjectsAndReduceRuleObjects tenantConfig = JsonUtil.fromJson(checkObjectsAndReduceRuleObjectsJson, CustomerAccountType.GetCheckObjectsAndReduceRuleObjects.class);
//        //不需要补充默认的配置
//        if (!tenantConfig.getNeedAddDefault()) {
//            return tenantConfig;
//        }
//
//        //需要补充默认的配置
//        CustomerAccountType.GetCheckObjectsAndReduceRuleObjects defaultConfig = ConfigCenter.getCheckObjectsAndReduceRuleObjects(true, null);
//        if (Objects.equals(key, ConfigKeyEnum.ACCOUNT_CHECK_RULE_CHECK_REDUCE.key)) {
//            return AccountCheckRuleJsonUtil.mergeForCheckReduce(tenantConfig, defaultConfig);
//        }
//        return AccountCheckRuleJsonUtil.mergeForDirectReduce(tenantConfig, defaultConfig);
//    }
//
//
//    /**
//     * defaultConfig 合并到 tenantConfig
//     * <p>
//     * 补充checkObjectsForCheckReduce
//     */
//    public static CustomerAccountType.GetCheckObjectsAndReduceRuleObjects mergeForCheckReduce(CustomerAccountType.GetCheckObjectsAndReduceRuleObjects tenantConfig, CustomerAccountType.GetCheckObjectsAndReduceRuleObjects defaultConfig) {
//        if (CollectionUtils.isEmpty(defaultConfig.getCheckObjectsForCheckReduce())) {
//            return tenantConfig;
//        }
//
//        if (CollectionUtils.isEmpty(tenantConfig.getCheckObjectsForCheckReduce())) {
//            tenantConfig.setCheckObjectsForCheckReduce(Lists.newArrayList());
//        }
//
//        List<String> tenantCheckObjectApiNames = tenantConfig.getCheckObjectsForCheckReduce().stream().map(CustomerAccountType.CheckObject::getApiName).collect(Collectors.toList());
//
//        for (CustomerAccountType.CheckObject defaultCheckObject : defaultConfig.getCheckObjectsForCheckReduce()) {
//            //整个checkObjectsForCheckReduce进来
//            if (!tenantCheckObjectApiNames.contains(defaultCheckObject.getApiName())) {
//                tenantConfig.getCheckObjectsForCheckReduce().add(defaultCheckObject);
//            }
//            //只放checkObjectsForCheckReduce里面的reduceRuleObjects、objectApiName2Buttons
//            else {
//                for (CustomerAccountType.CheckObject tenantCheckObject : tenantConfig.getCheckObjectsForCheckReduce()) {
//                    //找到一样的objectApiName
//                    if (!Objects.equals(tenantCheckObject.getApiName(), defaultCheckObject.getApiName())) {
//                        continue;
//                    }
//
//                    //补充checkObjectsForCheckReduce里面的reduceRuleObjects
//                    mergeReduceRuleObjects(tenantCheckObject, defaultCheckObject.getReduceRuleObjects());
//
//                    //补充checkObjectsForCheckReduce里面的actions
//                    mergeActions(tenantCheckObject, defaultCheckObject);
//
//                    break;
//                }
//            }
//        }
//
//        return tenantConfig;
//    }
//
//    /**
//     * 【校验扣减】
//     * defaultReduceObjects 合并到 tenantConfig的reduceRuleObjects
//     */
//    private static void mergeReduceRuleObjects(CustomerAccountType.CheckObject tenantCheckObject, List<CustomerAccountType.ReduceRuleObject> defaultReduceObjects) {
//        if (CollectionUtils.isEmpty(defaultReduceObjects)) {
//            return;
//        }
//
//        if (CollectionUtils.isEmpty(tenantCheckObject.getReduceRuleObjects())) {
//            tenantCheckObject.setReduceRuleObjects(Lists.newArrayList());
//        }
//        //已经有的checkObjectsForCheckReduce->reduceRuleObjects->apiName
//        List<String> tenantReduceRuleObjectApiNames = tenantCheckObject.getReduceRuleObjects().stream().map(CustomerAccountType.ReduceRuleObject::getApiName).collect(Collectors.toList());
//
//        for (CustomerAccountType.ReduceRuleObject defaultReduceRuleObject : defaultReduceObjects) {
//            //没有就加进来
//            if (!tenantReduceRuleObjectApiNames.contains(defaultReduceRuleObject.getApiName())) {
//                tenantCheckObject.getReduceRuleObjects().add(defaultReduceRuleObject);
//            }
//            // 只加里面的actions // TODO: 2023/2/14 test
//            else {
//                mergeActions(tenantCheckObject.getReduceRuleObjects(), defaultReduceRuleObject);
//            }
//        }
//    }
//
//    /**
//     * default的actions，加到tenant的actions
//     */
//    private static void mergeActions(CustomerAccountType.CheckObject tenantCheckObject, CustomerAccountType.CheckObject defaultCheckObject) {
//        if (CollectionUtils.isEmpty(defaultCheckObject.getActions())) {
//            return;
//        }
//
//        if (CollectionUtils.isEmpty(tenantCheckObject.getActions())) {
//            tenantCheckObject.setActions(Lists.newArrayList());
//        }
//
//        List<CheckTriggerActionModel.Actions> tenantActions = tenantCheckObject.getActions();
//
//        //租户配置是否设置了字段变更
//        boolean tenantHasFieldChange = checkHasFieldChange(tenantActions);
//        //租户配置中的按钮
//        CheckTriggerActionModel.Actions tenantButtonAction = getCheckTriggerActionButton(tenantActions);
//
//        for (CheckTriggerActionModel.Actions defaultAction : defaultCheckObject.getActions()) {
//            //加fieldChange
//            if (Objects.equals(defaultAction.getActionType(), "fieldChange") && !tenantHasFieldChange) {
//                tenantHasFieldChange = true;
//                tenantActions.add(defaultAction);
//            }
//            //加按钮
//            else if (Objects.equals(defaultAction.getActionType(), "button")) {
//                //租户配置没有设置按钮，整个action都加进来
//                if (tenantButtonAction == null) {
//                    tenantActions.add(defaultAction);
//                }
//
//                //只加action->buttons中没有的按钮
//                else {
//                    mergeButton(tenantButtonAction, defaultAction.getButtons());
//                }
//            }
//        }
//    }
//
//    /**
//     * 默认配置中有的按钮，加到租户的配置中
//     */
//    private static void mergeButton(CheckTriggerActionModel.Actions tenantButtonAction, List<CheckTriggerActionModel.Button> defaultButtons) {
//        //租户配置中已经有的按钮apiName
//        List<String> tenantButtonApiNames = Lists.newArrayList();
//        if (!CollectionUtils.isEmpty(tenantButtonAction.getButtons())) {
//            tenantButtonApiNames = tenantButtonAction.getButtons().stream().map(CheckTriggerActionModel.Button::getApiName).collect(Collectors.toList());
//        } else {
//            tenantButtonAction.setButtons(Lists.newArrayList());
//        }
//
//        //默认配置中有的按钮，加到租户配置中
//        for (CheckTriggerActionModel.Button defaultButton : defaultButtons) {
//            if (!tenantButtonApiNames.contains(defaultButton.getApiName())) {
//                tenantButtonAction.getButtons().add(defaultButton);
//            }
//        }
//    }
//
//    /**
//     * defaultConfig 合并到 tenantConfig
//     * <p>
//     * 补充reduceRuleObjectsForDirectReduce
//     */
//    public static CustomerAccountType.GetCheckObjectsAndReduceRuleObjects mergeForDirectReduce(CustomerAccountType.GetCheckObjectsAndReduceRuleObjects tenantConfig, CustomerAccountType.GetCheckObjectsAndReduceRuleObjects defaultConfig) {
//        if (CollectionUtils.isEmpty(defaultConfig.getReduceRuleObjectsForDirectReduce())) {
//            return tenantConfig;
//        }
//
//        if (CollectionUtils.isEmpty(tenantConfig.getReduceRuleObjectsForDirectReduce())) {
//            tenantConfig.setReduceRuleObjectsForDirectReduce(Lists.newArrayList());
//        }
//
//        List<String> tenantReduceRuleObjectApiNames = tenantConfig.getReduceRuleObjectsForDirectReduce().stream().map(CustomerAccountType.ReduceRuleObject::getApiName).collect(Collectors.toList());
//
//        for (CustomerAccountType.ReduceRuleObject defaultReduceRuleObject : defaultConfig.getReduceRuleObjectsForDirectReduce()) {
//            //整个reduceRuleObjectsForDirectReduce进来
//            if (!tenantReduceRuleObjectApiNames.contains(defaultReduceRuleObject.getApiName())) {
//                tenantConfig.getReduceRuleObjectsForDirectReduce().add(defaultReduceRuleObject);
//            }
//            //只放reduceRuleObjectsForDirectReduce里面的objectApiName2ActionAndButtons
//            else {
//                mergeActions(tenantConfig.getReduceRuleObjectsForDirectReduce(), defaultReduceRuleObject);
//            }
//        }
//
//        return tenantConfig;
//    }
//
//    /**
//     * defaultReduceRuleObject的actions合并到tenantConfig
//     */
//    private static void mergeActions(List<CustomerAccountType.ReduceRuleObject> tenantReduceObjects, CustomerAccountType.ReduceRuleObject defaultReduceRuleObject) {
//        if (CollectionUtils.isEmpty(defaultReduceRuleObject.getActions())) {
//            return;
//        }
//        for (CustomerAccountType.ReduceRuleObject tenantReduceRuleObject : tenantReduceObjects) {
//            //找到一样的objectApiName
//            if (!Objects.equals(tenantReduceRuleObject.getApiName(), defaultReduceRuleObject.getApiName())) {
//                continue;
//            }
//
//            mergeActions(tenantReduceRuleObject, defaultReduceRuleObject);
//            break;
//        }
//    }
//
//    /**
//     * default的actions，加到tenant的actions
//     */
//    private static void mergeActions(CustomerAccountType.ReduceRuleObject tenantReduceRuleObject, CustomerAccountType.ReduceRuleObject defaultReduceRuleObject) {
//        if (CollectionUtils.isEmpty(tenantReduceRuleObject.getActions())) {
//            tenantReduceRuleObject.setActions(Lists.newArrayList());
//        }
//
//        List<ReduceRuleActionModel.ActionTypeAndButtons> tenantActions = tenantReduceRuleObject.getActions();
//
//        //租户配置是否设置了字段变更
//        boolean tenantHasFieldChange = hasFieldChange(tenantActions);
//        //租户配置中的按钮
//        ReduceRuleActionModel.ActionTypeAndButtons tenantButtonAction = getButton(tenantActions);
//
//        for (ReduceRuleActionModel.ActionTypeAndButtons defaultAction : defaultReduceRuleObject.getActions()) {
//            //加fieldChange
//            if (Objects.equals(defaultAction.getActionType(), "fieldChange") && !tenantHasFieldChange) {
//                tenantHasFieldChange = true;
//                tenantActions.add(defaultAction);
//            }
//            //加按钮
//            else if (Objects.equals(defaultAction.getActionType(), "button")) {
//                //租户配置没有设置按钮，整个action都加进来
//                if (tenantButtonAction == null) {
//                    tenantActions.add(defaultAction);
//                }
//
//                //只加action->buttons中没有的按钮
//                else {
//                    mergeButton(tenantButtonAction, defaultAction.getButtons());
//                }
//            }
//        }
//    }
//
//    /**
//     * 默认配置中有的按钮，加到租户的配置中
//     */
//    private static void mergeButton(ReduceRuleActionModel.ActionTypeAndButtons tenantButtonAction, List<ReduceRuleActionModel.Button> defaultButtons) {
//        //租户配置中已经有的按钮apiName
//        List<String> tenantButtonApiNames = Lists.newArrayList();
//        if (!CollectionUtils.isEmpty(tenantButtonAction.getButtons())) {
//            tenantButtonApiNames = tenantButtonAction.getButtons().stream().map(ReduceRuleActionModel.Button::getApiName).collect(Collectors.toList());
//        } else {
//            tenantButtonAction.setButtons(Lists.newArrayList());
//        }
//
//        //默认配置中有的按钮，加到租户配置中
//        for (ReduceRuleActionModel.Button defaultButton : defaultButtons) {
//            if (!tenantButtonApiNames.contains(defaultButton.getApiName())) {
//                tenantButtonAction.getButtons().add(defaultButton);
//            }
//        }
//    }
//
//    /**
//     * 是否有"fieldChange"
//     */
//    private static boolean checkHasFieldChange(List<CheckTriggerActionModel.Actions> actions) {
//        for (CheckTriggerActionModel.Actions action : actions) {
//            if (Objects.equals(action.getActionType(), "fieldChange")) {
//                return true;
//            }
//        }
//        return false;
//    }
//
//    private static CheckTriggerActionModel.Actions getCheckTriggerActionButton(List<CheckTriggerActionModel.Actions> actions) {
//        for (CheckTriggerActionModel.Actions action : actions) {
//            if (Objects.equals(action.getActionType(), "button")) {
//                return action;
//            }
//        }
//        return null;
//    }
//
//    /**
//     * 是否有"fieldChange"
//     */
//    private static boolean hasFieldChange(List<ReduceRuleActionModel.ActionTypeAndButtons> actions) {
//        for (ReduceRuleActionModel.ActionTypeAndButtons action : actions) {
//            if (Objects.equals(action.getActionType(), "fieldChange")) {
//                return true;
//            }
//        }
//        return false;
//    }
//
//    private static ReduceRuleActionModel.ActionTypeAndButtons getButton(List<ReduceRuleActionModel.ActionTypeAndButtons> actions) {
//        for (ReduceRuleActionModel.ActionTypeAndButtons action : actions) {
//            if (Objects.equals(action.getActionType(), "button")) {
//                return action;
//            }
//        }
//        return null;
//    }
//
//    //校验扣减
//    public static void add(List<CustomerAccountType.ReduceRuleObject> reduceRuleObjects, Map<String, List<ReduceRuleActionModel.ActionTypeAndButtons>> objectApiName2ActionAndButtons) {
//
//        for (CustomerAccountType.ReduceRuleObject reduceRuleObject : reduceRuleObjects) {
//            if (CollectionUtils.isEmpty(reduceRuleObject.getActions())) {
//                continue;
//            }
//
//            //添加
//            AccountCheckRuleJsonUtil.add(reduceRuleObject, objectApiName2ActionAndButtons);
//        }
//    }
//
//    public static void add(CustomerAccountType.ReduceRuleObject reduceRuleObject, Map<String, List<ReduceRuleActionModel.ActionTypeAndButtons>> objectApiName2ActionAndButtons) {
//        if (CollectionUtils.isEmpty(reduceRuleObject.getActions())) {
//            return;
//        }
//
//        for (ReduceRuleActionModel.ActionTypeAndButtons objectApiName2ActionAndButton : reduceRuleObject.getActions()) {
//            String objectApiName = reduceRuleObject.getApiName();
//
//            List<ReduceRuleActionModel.ActionTypeAndButtons> allActionTypeAndButtons = objectApiName2ActionAndButtons.get(objectApiName);
//            if (allActionTypeAndButtons == null) {
//                allActionTypeAndButtons = Lists.newArrayList();
//            }
//
//            //fieldChange
//            if (Objects.equals(objectApiName2ActionAndButton.getActionType(), "fieldChange")) {
//                boolean hasFieldChange = hasFieldChange(allActionTypeAndButtons);
//                if (!hasFieldChange) {
//                    allActionTypeAndButtons.add(objectApiName2ActionAndButton);
//                }
//            }
//            //button
//            else {
//                ReduceRuleActionModel.ActionTypeAndButtons allButtons = getButton(allActionTypeAndButtons);
//                //objectApiName 第一次加按钮，全部放进来
//                if (allButtons == null) {
//                    allActionTypeAndButtons.add(objectApiName2ActionAndButton);
//                }
//                //只加没有的button
//                else {
//                    for (ReduceRuleActionModel.Button button : objectApiName2ActionAndButton.getButtons()) {
//                        boolean hasButton = hasButton(allButtons, button.getApiName());
//                        if (!hasButton) {
//                            allButtons.getButtons().add(button);
//                        }
//                    }
//                }
//            }
//            objectApiName2ActionAndButtons.put(objectApiName, allActionTypeAndButtons);
//        }
//    }
//
//    private static boolean hasButton(ReduceRuleActionModel.ActionTypeAndButtons buttons, String buttonApiName) {
//        for (ReduceRuleActionModel.Button button : buttons.getButtons()) {
//            if (Objects.equals(button.getApiName(), buttonApiName)) {
//                return true;
//            }
//        }
//        return false;
//    }
//
//    public static List<Tuple<String, String>> getTenantReduceReferenceFields(String tenantId, String reduceObjectApiName) {
//        List<Tuple<String, String>> result = new ArrayList<>();
//
//        //校验扣减才有
//        CustomerAccountType.GetCheckObjectsAndReduceRuleObjects checkObjects = AccountCheckRuleJsonUtil.getCheckObjects(tenantId, ConfigKeyEnum.ACCOUNT_CHECK_RULE_CHECK_REDUCE.key);
//        for (CustomerAccountType.CheckObject checkObject : checkObjects.getCheckObjectsForCheckReduce()) {
//            if (CollectionUtils.isEmpty(checkObject.getReduceRuleObjects())) {
//                continue;
//            }
//
//            for (CustomerAccountType.ReduceRuleObject reduceRuleObject : checkObject.getReduceRuleObjects()) {
//                if (!Objects.equals(reduceRuleObject.getApiName(), reduceObjectApiName)) {
//                    continue;
//                }
//
//                Tuple<String, String> tuple = Tuple.of(checkObject.getApiName(), reduceRuleObject.getLookupCheckObjectFieldApiName());
//                result.add(tuple);
//            }
//        }
//        return result;
//    }
//}