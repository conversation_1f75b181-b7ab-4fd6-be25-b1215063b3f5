package com.facishare.crm.customeraccount.predefine.action;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.customeraccount.constants.AccountCheckRuleConstants;
import com.facishare.crm.customeraccount.constants.CAI18NKey;
import com.facishare.crm.customeraccount.enums.AccountCheckRuleTypeEnum;
import com.facishare.crm.customeraccount.enums.ReduceTriggerActionEnum;
import com.facishare.crm.customeraccount.predefine.handler.RuleHandlerUtil;
import com.facishare.crm.customeraccount.predefine.manager.AccountCheckManager;
import com.facishare.crm.customeraccount.predefine.manager.AccountCheckRuleDomainPluginManager;
import com.facishare.crm.customeraccount.predefine.manager.AccountCheckRuleManager;
import com.facishare.crm.customeraccount.predefine.manager.CaButtonManager;
import com.facishare.crm.customeraccount.predefine.manager.CaRuleEngineManager;
import com.facishare.crm.customeraccount.predefine.reconciliationsql.CheckRuleAddSqlGenerator;
import com.facishare.crm.customeraccount.predefine.service.dto.ObjectMappingModel;
import com.facishare.crm.customeraccount.util.rule.RuleUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.util.SpringUtil;
import com.facishare.paas.rule.pojo.RulePojo;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

@Slf4j
public class AccountCheckRuleAddAction extends StandardAddAction {
    private final PlatformTransactionManager tm = SpringUtil.getContext().getBean("paasMetadataTransactionManager", PlatformTransactionManager.class);
    private final AccountCheckManager accountCheckManager = SpringUtil.getContext().getBean("accountCheckManager", AccountCheckManager.class);
    private final CaRuleEngineManager caRuleEngineManager = SpringUtil.getContext().getBean(CaRuleEngineManager.class);
    private final AccountCheckRuleManager accountCheckRuleManager = SpringUtil.getContext().getBean(AccountCheckRuleManager.class);
    private final CaButtonManager caButtonManager = SpringUtil.getContext().getBean(CaButtonManager.class);
    private final AccountCheckRuleDomainPluginManager accountCheckRuleDomainPluginManager = SpringUtil.getContext().getBean(AccountCheckRuleDomainPluginManager.class);
    private final CheckRuleAddSqlGenerator checkRuleAddSqlGenerator = SpringUtil.getContext().getBean(CheckRuleAddSqlGenerator.class);

    @Override
    protected void before(Arg arg) {
        /**
         * ReduceMapping转成字符串的形式
         * 原因：蒙牛云同步数据，添加之前，用APL函数，把ReduceMapping里面的账户和扣减字段，替换为本企业同名的账户和字段
          */
        String ruleType = arg.getObjectData().toObjectData().get(AccountCheckRuleConstants.Field.RuleType.apiName, String.class);
        if (Objects.equals(ruleType, AccountCheckRuleTypeEnum.Component_Reduce.getValue())) {
            log.info("AccountCheckRuleAddAction before objectData[{}]", arg.getObjectData());
            List<ObjectMappingModel> objectMappings = RuleHandlerUtil.getObjectMapping(arg.getObjectData().toObjectData(), AccountCheckRuleConstants.Field.ReduceMapping.apiName);
            String newReduceMappingJson = JSONObject.toJSONString(objectMappings);
            arg.getObjectData().toObjectData().set(AccountCheckRuleConstants.Field.ReduceMapping.apiName, newReduceMappingJson);
            log.info("AccountCheckRuleAddAction after objectData[{}]", arg.getObjectData());
        }

        super.before(arg);

        // 采用 domainPlugin 的企业，触发校验和触发扣减的按钮不能是自定义按钮
        accountCheckRuleDomainPluginManager.validateBtn(actionContext.getTenantId(), objectData);

        //校验：优先级不能重复
        /**
         * 正常【组件扣减】的页面上不能创建
         * 设置DDS同步数据，会走到这里，【组件扣减】的校验规则，一个对象只有一个，不用判断优先级
         */
        if (!Objects.equals(ruleType, AccountCheckRuleTypeEnum.Component_Reduce.getValue())) {
            Integer priority = objectData.get(AccountCheckRuleConstants.Field.Priority.apiName, Integer.class);
            Boolean hasSamePriority = accountCheckRuleManager.hasSamePriority(actionContext.getUser(), ruleType, priority, Lists.newArrayList());
            if (hasSamePriority) {
                throw new ValidateException(I18N.text(CAI18NKey.PRIORITY_ALREADY_EXIST));
            }
        }

        //'组件扣减'，一个对象只能有一条
        accountCheckRuleManager.checkForComponentReduce(actionContext.getTenantId(), ruleType, objectData);
        accountCheckRuleManager.checkNotUseFundAccount(actionContext.getTenantId(), objectData);

        //第一步，第三步的校验对象，触发动作，触发按钮不能都一样
        if (Objects.equals(AccountCheckRuleTypeEnum.Check_Reduce.getValue(), ruleType)) {
            String reduceTriggerAction = objectData.get(AccountCheckRuleConstants.Field.ReduceTriggerAction.apiName, String.class);
            String checkTriggerAction = objectData.get(AccountCheckRuleConstants.Field.CheckTriggerAction.apiName, String.class);
            String checkObject = objectData.get(AccountCheckRuleConstants.Field.CheckObject.apiName, String.class);
            String reduceRelatedObject = objectData.get(AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName, String.class);
            if (Objects.equals(reduceTriggerAction, checkTriggerAction)) {
                if (Objects.equals(reduceTriggerAction, ReduceTriggerActionEnum.Button.getValue())) {
                    String CheckTriggerButtonApiName = objectData.get(AccountCheckRuleConstants.Field.CheckTriggerButton.apiName, String.class);
                    String reduceTriggerButton = objectData.get(AccountCheckRuleConstants.Field.ReduceTriggerButton.apiName, String.class);
                    if (Objects.equals(checkObject, reduceRelatedObject) && Objects.equals(CheckTriggerButtonApiName, reduceTriggerButton)) {
                        throw new ValidateException(I18N.text(CAI18NKey.CHECKT_TRIGGER_ACTION_CANNOT_EQUAL_WITH_REDUCE_TRIGGER_BUTTON));
                    }
                }
                //如果校验动作和扣减动作都是字段变更，第三步的条件不能是第一步，第二步条件的和的子集
                else if (Objects.equals(reduceTriggerAction, ReduceTriggerActionEnum.FieldChange.getValue())) {
                    if (Objects.equals(checkObject, reduceRelatedObject)) {
                        List<RulePojo> triggerConditions = RuleUtil.getRuleConditions(objectData, AccountCheckRuleConstants.Field.TriggerCondition.apiName);
                        List<RulePojo> checkRules = RuleUtil.getRuleConditions(objectData, AccountCheckRuleConstants.Field.CheckRule.apiName);
                        if (CollectionUtils.isEmpty(triggerConditions) || CollectionUtils.isEmpty(checkRules)) {
                            throw new ValidateException(I18N.text(CAI18NKey.TRIGGER_CONDITION_CANNOT_IS＿EMPTY));
                        }
                        //把右边不重复的合并到左边
                        accountCheckManager.mergeCondition(triggerConditions, checkRules);
                        //和第三步条件比较，第三步的条件不能是第一步，第二步条件的和的子集
                        List<RulePojo> reduceTriggerConditions = RuleUtil.getRuleConditions(objectData, AccountCheckRuleConstants.Field.ReduceTriggerCondition.apiName);
                        if (CollectionUtils.isEmpty(reduceTriggerConditions)) {
                            throw new ValidateException(I18N.text(CAI18NKey.TRIGGER_CONDITION_CANNOT_IS＿EMPTY));
                        }
                        boolean same = accountCheckManager.sameWithCondition(triggerConditions, reduceTriggerConditions);
                        if (same) {
                            throw new ValidateException(I18N.text(CAI18NKey.CHECKT_TRIGGER_ACTION_CANNOT_EQUAL_REDUCE_TRIGGER_CTION_IS_FIELDCHANGE));
                        }
                    }
                }
            }
        }
        //补充数据：规则引擎ruleCode
        if (Objects.equals(AccountCheckRuleTypeEnum.Check_Reduce.getValue(), ruleType)) {
            String triggerCondition = objectData.get(AccountCheckRuleConstants.Field.TriggerCondition.apiName, String.class);
            if (!Strings.isNullOrEmpty(triggerCondition) && !triggerCondition.equals("[]")) {
                objectData.set(AccountCheckRuleConstants.Field.TriggerConditionRuleCode.apiName, serviceFacade.generateId());
            }

            String checkRule = objectData.get(AccountCheckRuleConstants.Field.CheckRule.apiName, String.class);
            if (!Strings.isNullOrEmpty(checkRule) && !checkRule.equals("{}")) {
                objectData.set(AccountCheckRuleConstants.Field.CheckRuleRuleCode.apiName, serviceFacade.generateId());
            }
        }
        String reduceTriggerCondition = objectData.get(AccountCheckRuleConstants.Field.ReduceTriggerCondition.apiName, String.class);
        if (!Strings.isNullOrEmpty(reduceTriggerCondition) && !reduceTriggerCondition.equals("[]")) {
            objectData.set(AccountCheckRuleConstants.Field.ReduceTriggerConditionRuleCode.apiName, serviceFacade.generateId());
        }
    }


    @Override
    protected void doSaveData() {
        TransactionTemplate template = new TransactionTemplate(tm);
        template.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(TransactionStatus status) {
                saveData();
            }
        });
    }

    private void saveData() {
        super.doSaveData();

        /**
         * 页面上不能新建【组件扣减】的校验规则
         * DDS配校验规则，会走到这里，对于【组件扣减】的校验规则，不用管：规则引擎、按钮Action的逻辑
         */
        String ruleType = objectData.get(AccountCheckRuleConstants.Field.RuleType.apiName, String.class);
        if (Objects.equals(ruleType, AccountCheckRuleTypeEnum.Component_Reduce.getValue())) {
            return;
        }

        //保存到规则引擎
        caRuleEngineManager.createRule(actionContext.getUser(), objectData);

        // 保存插件实例或按钮
        if (accountCheckRuleDomainPluginManager.isRunByDomainPlugin(actionContext.getTenantId(), objectData)) {
            accountCheckRuleDomainPluginManager.addByDomainPlugin(actionContext.getRequestContext(), objectData);
        } else {
            //按钮(字段变更不需要写）
            caButtonManager.add(actionContext.getRequestContext(), objectData);
        }
        checkRuleAddSqlGenerator.generate(actionContext.getRequestContext(), null, objectData);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        String ruleType = result.getObjectData().toObjectData().get(AccountCheckRuleConstants.Field.RuleType.apiName, String.class);
        if (Objects.equals(AccountCheckRuleTypeEnum.Component_Reduce.getValue(), ruleType)) {
            //【组件扣减】的【校验规则】，写插件实例
            //目前DDS同步数据，会走到这
            String reduceRelatedObject = result.getObjectData().toObjectData().get(AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName, String.class);
            log.info("AccountCheckRuleAddAction after createPluginInstanceForComponentReduceAccountCheckRule tenantId[{}], reduceRelatedObject[{}]", actionContext.getTenantId(), reduceRelatedObject);
            accountCheckRuleManager.createPluginInstanceForComponentReduceAccountCheckRule(actionContext.getRequestContext(), reduceRelatedObject);
        }

        return result;
    }
}
