package com.facishare.crm.customeraccount.predefine.manager;

import com.facishare.crm.customeraccount.constants.AccountCheckRuleConstants;
import com.facishare.crm.customeraccount.constants.AccountFrozenRecordConstant;
import com.facishare.crm.customeraccount.constants.UnfreezeDetailConstant;
import com.facishare.crm.customeraccount.exception.CustomerAccountBusinessException;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

@Slf4j
@Component
@Deprecated
public class CaObjectDescribeManager {

    @Resource
    private IObjectDescribeService objectDescribeService;

    /**
     * 获取已经被使用的displayName
     */
    public Set<String> getExistDisplayName(String tenantId) {
        Set<String> existDisplayNames = Sets.newHashSet();

        addExistDisplayName(tenantId, AccountCheckRuleConstants.DISPLAY_NAME, AccountCheckRuleConstants.API_NAME, existDisplayNames);
        addExistDisplayName(tenantId, AccountFrozenRecordConstant.DISPLAY_NAME, AccountFrozenRecordConstant.API_NAME, existDisplayNames);
        addExistDisplayName(tenantId, UnfreezeDetailConstant.DISPLAY_NAME, UnfreezeDetailConstant.API_NAME, existDisplayNames);

        log.debug("getExistDisplayName tenantId:{}, Result:{}", tenantId, existDisplayNames);
        return existDisplayNames;
    }

    public void addExistDisplayName(String tenantId, String displayName, String objApiName, Set<String> existDisplayNames) {
        List<String> existObjApiNames;
        try {
            existObjApiNames = objectDescribeService.checkDisplayNameExist(tenantId, displayName, "CRM");
        } catch (MetadataServiceException e) {
            log.warn("getExistDisplayName error,tenantId:{}", tenantId, e);
            throw new CustomerAccountBusinessException(() -> e.getErrorCode().getCode(), e.getMessage());
        }

        existObjApiNames.forEach(x -> {
            if (!objApiName.equals(x)) {
                existDisplayNames.add(displayName);
            }
        });
    }
}