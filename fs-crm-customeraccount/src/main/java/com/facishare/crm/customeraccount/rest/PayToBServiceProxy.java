package com.facishare.crm.customeraccount.rest;

import com.facishare.crm.customeraccount.rest.dto.RecreateCrmObjectByDomainPluginModel;
import com.fxiaoke.pay.rest.common.RestResult;
import com.github.zhxing.retrofitspring.annotation.RetrofitConfig;
import retrofit2.http.Body;
import retrofit2.http.POST;

/**
 * @IgnoreI18nFile
 */
@RetrofitConfig(baseUrl = "tobBase", desc = "支付toB相关接口")
public interface PayToBServiceProxy {

    /**
     * 支付那边，重新出发一次【支付流水】的新建
     */
    @POST("com.facishare.pay.toB.pay.service.EAPayOrderService/recreateCrmObjectByDomainPlugin/1.0")
    RestResult<RecreateCrmObjectByDomainPluginModel.Result> recreateCrmObjectByDomainPlugin(@Body RecreateCrmObjectByDomainPluginModel.Arg arg);
}