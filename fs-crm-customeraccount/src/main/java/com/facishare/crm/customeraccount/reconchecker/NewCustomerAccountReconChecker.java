package com.facishare.crm.customeraccount.reconchecker;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.bizreconciliation.checker.BizReconciliationChecker;
import com.facishare.crm.bizreconciliation.consts.ReconAbnormalDataConst;
import com.facishare.crm.bizreconciliation.enums.BizModuleEnum;
import com.facishare.crm.bizreconciliation.model.BizReconCompareResult;
import com.facishare.crm.customeraccount.constants.FundAccountConstants;
import com.facishare.crm.customeraccount.constants.NewCustomerAccountConstants;
import com.facishare.crm.customeraccount.enums.AccessModuleEnum;
import com.facishare.crm.customeraccount.enums.ConfigKeyEnum;
import com.facishare.crm.customeraccount.predefine.handler.checkrule.CustomerFundAccount;
import com.facishare.crm.customeraccount.predefine.manager.BizConfigManager;
import com.facishare.crm.customeraccount.predefine.manager.NewCustomerAccountManager;
import com.facishare.crm.customeraccount.util.ServiceContextUtil;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @IgnoreI18nFile
 */
@Slf4j
@Component
public class NewCustomerAccountReconChecker extends BizReconciliationChecker {
    @Autowired
    private CustomerAccountCheckerManager customerAccountCheckerManager;
    @Autowired
    private BizConfigManager bizConfigManager;
    @Autowired
    private NewCustomerAccountManager newCustomerAccountManager;


    @Override
    public String getObjectApiName() {
        return "NewCustomerAccountObj";
    }

    @Override
    public String getBizModule() {
        return BizModuleEnum.CUSTOMER_ACCOUNT.value;
    }

    @Override
    public List<ReconAbnormalDataConst.Model> check(User user, String bizModule, List<IObjectData> pendingDataList) {
        List<ReconAbnormalDataConst.Model> abnormalList = Lists.newArrayList();
        Map<String, IObjectData> bizPendingDataMap = toBizPendingDataMap(pendingDataList);

        Map<String, IObjectData> dataMap = customerAccountCheckerManager.findDataIncludeDeleted(user, getObjectApiName(), Lists.newArrayList(bizPendingDataMap.keySet()));

        Set<String> fundAccountIds = dataMap.values().stream().map(x -> x.get(NewCustomerAccountConstants.Field.FundAccount.apiName, String.class, "")).collect(Collectors.toSet());
        Map<String, IObjectData> fundAccountDataMap = customerAccountCheckerManager.findDataIncludeDeleted(user, FundAccountConstants.API_NAME, Lists.newArrayList(fundAccountIds));
        bizPendingDataMap.forEach((k, v) -> {
            IObjectData customerAccountData = dataMap.get(k);
            if (Objects.isNull(customerAccountData)) {
                log.warn("customerAccount not exist,user:{},pendingData:{}", user, v);
            } else {
                BizReconCompareResult compareResult = new BizReconCompareResult();
                String fundAccountId = customerAccountData.get(NewCustomerAccountConstants.Field.FundAccount.apiName, String.class, "");
                IObjectData fundAccountData = fundAccountDataMap.get(fundAccountId);
                boolean isCredit = Objects.nonNull(fundAccountData) && AccessModuleEnum.CREDIT.value.equals(fundAccountData.get(FundAccountConstants.Field.AccessModule.apiName, String.class, ""));
                BigDecimal accountBalance = customerAccountData.get(NewCustomerAccountConstants.Field.AccountBalance.apiName, BigDecimal.class, BigDecimal.ZERO);
                BigDecimal occupiedAmount = customerAccountData.get(NewCustomerAccountConstants.Field.OccupiedAmount.apiName, BigDecimal.class, BigDecimal.ZERO);
                BigDecimal availableBalance = customerAccountData.get(NewCustomerAccountConstants.Field.AvailableBalance.apiName, BigDecimal.class, BigDecimal.ZERO);
                if (isCredit) {
                    BigDecimal creditQuota = customerAccountData.get(NewCustomerAccountConstants.Field.CreditQuota.apiName, BigDecimal.class, BigDecimal.ZERO);
                    BigDecimal creditOccupiedAmount = customerAccountData.get(NewCustomerAccountConstants.Field.CreditOccupiedAmount.apiName, BigDecimal.class, BigDecimal.ZERO);
                    if (creditQuota.compareTo(accountBalance.add(creditOccupiedAmount)) != 0) {
                        compareResult.appendFormatMessage("数据[%s]信用账户异常", customerAccountData.getName());
                        log.warn("credit customerAccount data error ,tenantId:{},dataId:{},creditQuota:{},accountBalance:{},creditOccupiedAmount:{}", user.getTenantId(), k, creditQuota, accountBalance, creditOccupiedAmount);
                    }
                } else if (accountBalance.compareTo(occupiedAmount.add(availableBalance)) != 0) {
                    compareResult.appendFormatMessage("数据[%s]账户余额不等于可用加占用", customerAccountData.getName());
                    log.warn("customerAccount data error ,tenantId:{},dataId:{},accountBalance:{},occupiedAmount:{},availableBalance:{}", user.getTenantId(), k, accountBalance, occupiedAmount, availableBalance);
                } else if (occupiedAmount.compareTo(BigDecimal.ZERO) < 0) {
                    compareResult.appendFormatMessage("数据[%s]账户余额占用为负数", customerAccountData.getName());
                    log.warn("customerAccount occupiedAmount lt 0,tenantId:{},dataId:{},occupiedAmount:{}", user.getTenantId(), k, occupiedAmount);
                } else {
                    boolean skipValidate = bizConfigManager.configEqual(user, ConfigKeyEnum.CUSTOMER_ACCOUNT_EXCEED.key, ConfigKeyEnum.CUSTOMER_ACCOUNT_EXCEED.enabledValue);
                    if (!skipValidate && (availableBalance.compareTo(accountBalance) < 0 || occupiedAmount.compareTo(accountBalance) < 0)) {
                        compareResult.appendFormatMessage("数据[%s]账户余额或可用为负数", customerAccountData.getName());
                        log.warn("customerAccount accountBalance or availableAMount lt 0,tenantId:{},dataId:{},accountBalance:{},availableAMount:{}", user.getTenantId(), k, accountBalance, availableBalance);
                    }
                }

                if (!compareResult.isError()) {
                    String customerId = customerAccountData.get(NewCustomerAccountConstants.Field.Customer.apiName, String.class);
                    if (StringUtils.isAnyEmpty(customerId, fundAccountId)) {
                        compareResult.appendFormatMessage("数据[%s]客户账户余额的客户或者账户字段为空", customerAccountData.getName());
                        log.warn("customerAccount error,customer or fundAccount is empty,tenantId:{},customerAccountData:{}", user.getTenantId(), customerAccountData);
                    } else {
                        RequestContext requestContext = ServiceContextUtil.getRequestContext(user);
                        Map<CustomerFundAccount, IObjectData> customerAccountMap = newCustomerAccountManager.batchGetNewCustomerAccount(requestContext, Lists.newArrayList(CustomerFundAccount.of(customerId, fundAccountId)));
                        if (CollectionUtils.size(customerAccountMap) > 1) {
                            compareResult.appendFormatMessage("数据[%s]客户账户余额存在多条", customerAccountData.getName());
                            log.warn("customerAccount more than one,tenantId:{},customerId:{},fundAccountId:{},customerAccountData:{}", user.getTenantId(), customerId, fundAccountId, customerAccountData);
                        }
                    }
                }

                if (compareResult.isError()) {
                    abnormalList.add(generateAbnormalModel(bizModule, ReconAbnormalDataConst.Type.DataAbnormal.value, getObjectApiName(), k,
                            compareResult.message()));
                }

            }
        });

        return abnormalList;
    }
}
