package com.facishare.crm.customeraccount.predefine.action;

import com.facishare.crm.customeraccount.constants.CAI18NKey;
import com.facishare.crm.customeraccount.constants.CustomerAccountConstants;
import com.facishare.crm.customeraccount.predefine.remote.CrmManager;
import com.facishare.crm.customeraccount.util.ConfigCenter;
import com.facishare.crm.customeraccount.util.ObjectDataUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardExportAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * Created on 2018/7/24.
 */
@Slf4j
public class CustomerAccountExportAction extends StandardExportAction {

    private CrmManager crmManager = SpringUtil.getContext().getBean(CrmManager.class);

    private final int CUSTOMER_ACCOUNT_EXPORT_ROWS_THROTTLE = 5000;

    /*@Override
    protected int validateThrottle() {
        // TODO: 已用信用字段逻辑修改前暂定导出限制为5K
        int count = super.validateThrottle();
        if (count > CUSTOMER_ACCOUNT_EXPORT_ROWS_THROTTLE) {
            throw new ValidateException(I18N.text(CAI18NKey.BATCH_IMPORT_LIMIT));
        }
        return count;
    }*/

    @Override
    protected void fillDataList(User user, IObjectDescribe describe, List<IFieldDescribe> fields, List<IObjectData> dataList) {
        //客户账户 IT对接企业不需要动态计算信用相关字段
        if (!ConfigCenter.tenantIdForOpenApi.contains(user.getTenantId())) {
            // 仅灰度企业才计算字段 已用信用&剩余信用额度
            for (IObjectData customerAccount : dataList) {
                String customerId = customerAccount.get(CustomerAccountConstants.Field.Customer.getApiName(), String.class);
                BigDecimal creditQuota = ObjectDataUtil.getBigDecimal(customerAccount, CustomerAccountConstants.Field.CreditQuota.getApiName());
                BigDecimal creditTemporaryQuota = ObjectDataUtil.getBigDecimal(customerAccount, CustomerAccountConstants.Field.CreditTemporaryQuota.getApiName());

                BigDecimal usedCreditQuota = crmManager.getUsedCreditAmount(actionContext.getUser(), customerId);
                BigDecimal creditAvailableQuota = creditQuota.add(creditTemporaryQuota).subtract(usedCreditQuota);

                customerAccount.set(CustomerAccountConstants.Field.UsedCreditQuota.getApiName(), usedCreditQuota);
                customerAccount.set(CustomerAccountConstants.Field.CreditAvailableQuota.getApiName(), creditAvailableQuota);
            }

        }
        super.fillDataList(user, describe, fields, dataList);
    }

}
