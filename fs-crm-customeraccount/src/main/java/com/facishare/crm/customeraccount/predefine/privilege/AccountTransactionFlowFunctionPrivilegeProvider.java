package com.facishare.crm.customeraccount.predefine.privilege;

import com.facishare.crmcommon.constants.CommonConstants;
import com.facishare.crm.customeraccount.constants.AccountTransactionFlowConst;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.privilege.model.DefaultFunctionPrivilegeProvider;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component
@Deprecated
public class AccountTransactionFlowFunctionPrivilegeProvider extends DefaultFunctionPrivilegeProvider {

    private final static List<String> supportActionCodes = Lists.newArrayList(
            ObjectAction.VIEW_LIST.getActionCode(),
            ObjectAction.VIEW_DETAIL.getActionCode(),
            ObjectAction.CREATE.getActionCode(),
            ObjectAction.CLONE.getActionCode(),
            ObjectAction.UPDATE.getActionCode(),
            ObjectAction.INVALID.getActionCode(),
//            ObjectAction.RECOVER.getActionCode(),
            ObjectAction.DELETE.getActionCode(),
            ObjectAction.BATCH_IMPORT.getActionCode(),
            ObjectAction.BATCH_EXPORT.getActionCode(),
            ObjectAction.CHANGE_OWNER.getActionCode(),
            ObjectAction.EDIT_TEAM_MEMBER.getActionCode(),
            ObjectAction.PRINT.getActionCode(),
            ObjectAction.LOCK.getActionCode(),
            ObjectAction.UNLOCK.getActionCode(),
            ObjectAction.BULK_INVALID.getActionCode(),
            ObjectAction.BULK_DELETE.getActionCode(),
            ObjectAction.CANCEL_ENTRY.getActionCode()
    );

    private final static List<String> orderFinanceActionCodes = Lists.newArrayList(
            ObjectAction.VIEW_LIST.getActionCode(),
            ObjectAction.VIEW_DETAIL.getActionCode()
    );

    @Override
    public String getApiName() {
        return AccountTransactionFlowConst.API_NAME;
    }


    @Override
    public List<String> getSupportedActionCodes() {
        return Collections.unmodifiableList(supportActionCodes);
    }

    @Override
    public Map<String, List<String>> getCustomInitRoleActionCodes() {
        Map<String, List<String>> actionCodeMap = Maps.newHashMap();
        actionCodeMap.put(CommonConstants.PAYMENT_FINANCE_ROLE, Collections.unmodifiableList(supportActionCodes));
        actionCodeMap.put(CommonConstants.ORDER_FINANCE_ROLE, Collections.unmodifiableList(orderFinanceActionCodes));
        return Collections.unmodifiableMap(actionCodeMap);
    }
}
