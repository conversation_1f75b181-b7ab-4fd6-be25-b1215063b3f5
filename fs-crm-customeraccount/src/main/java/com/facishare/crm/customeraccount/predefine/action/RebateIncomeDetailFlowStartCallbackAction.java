package com.facishare.crm.customeraccount.predefine.action;

import com.facishare.crm.customeraccount.constants.RebateIncomeDetailConstants;
import com.facishare.crm.customeraccount.constants.SystemConstants;
import com.facishare.crm.customeraccount.predefine.manager.CustomerAccountManager;
import com.facishare.crm.customeraccount.predefine.manager.RebateIncomeDetailManager;
import com.facishare.crm.customeraccount.predefine.manager.RebateOutcomeDetailManager;
import com.facishare.crm.customeraccount.util.ObjectDataUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardFlowStartCallbackAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @IgnoreI18n
 */
@Slf4j
public class RebateIncomeDetailFlowStartCallbackAction extends StandardFlowStartCallbackAction {
    private RebateIncomeDetailManager rebateIncomeDetailManager;
    private String oldLifeStatus = null;
    private String newLifeStatus = null;
    private RebateOutcomeDetailManager rebateOutcomeDetailManager;
    private CustomerAccountManager customerAccountManager;

    @Override
    protected void before(Arg arg) {
        rebateOutcomeDetailManager = SpringUtil.getContext().getBean(RebateOutcomeDetailManager.class);
        rebateIncomeDetailManager = SpringUtil.getContext().getBean(RebateIncomeDetailManager.class);
        customerAccountManager = SpringUtil.getContext().getBean(CustomerAccountManager.class);
        super.before(arg);
        boolean hasRebateOutcome = rebateOutcomeDetailManager.hasRebateOutcomeDetails(actionContext.getUser(), objectData.getId());
        if (hasRebateOutcome) {
            log.warn("FlowStartCallback有时间间隔，返利已关联支出，无法作废");
            throw new ValidateException("返利已关联支出，无法作废！");
        }
        oldLifeStatus = objectData.get(SystemConstants.Field.LifeStatus.apiName, String.class);
    }

    @Override
    protected Result doAct(Arg arg) {
        if (arg.isTriggerSynchronous()) {
            //如果审批流回调且为同步审批流触发时，直接跳过，不走更新逻辑
            return new Result(true);
        }
        super.doAct(arg);
        //        objectData = serviceFacade.findObjectDataIncludeDeleted(actionContext.getUser(), arg.getDataId(), objectDescribe.getApiName());
        Date start = objectData.get(RebateIncomeDetailConstants.Field.StartTime.apiName, Date.class);
        Date end = objectData.get(RebateIncomeDetailConstants.Field.EndTime.apiName, Date.class);
        //        String lifeStatus = objectData.get(SystemConstants.Field.LifeStatus.apiName, String.class);
        log.info("callback objectData:{},oldLifeStatus:{},newLifeStatus:{},arg:{}", objectData, oldLifeStatus, newLifeStatus, arg);
        if (ObjectDataUtil.isCurrentTimeActive(start, end) && StringUtils.isNotEmpty(newLifeStatus) && !Objects.equals(oldLifeStatus, newLifeStatus)) {
            log.info("RebateIncomeDetailFlowStartCallbackAction.rebateIncomeDetailManager.updateBalanceForLifeStatus");
            String customerId = ObjectDataUtil.getReferenceId(objectData, RebateIncomeDetailConstants.Field.Customer.apiName);
            Optional<IObjectData> customerAccountObjectData = customerAccountManager.getCustomerAccountIncludeInvalidByCustomerId(actionContext.getUser(), customerId);
            if (customerAccountObjectData.isPresent()) {
                rebateIncomeDetailManager.updateBalanceForLifeStatus(actionContext.getUser(), objectData, oldLifeStatus, newLifeStatus);
            }
        }
        return new Result(true);
    }

    @Override
    protected void updateDataLifeStatus() {
        List<String> toUpdateFieldList = ObjectDataExt.of(this.objectData).modifyObjectDataWhenStartApprovalFlow(this.triggerType, this.triggerResult);
        if (CollectionUtils.notEmpty(toUpdateFieldList)) {
            List<IObjectData> dataList = this.serviceFacade.batchUpdateByFields(this.actionContext.getUser(), Lists.newArrayList(this.objectData), toUpdateFieldList);
            log.info("rebateIncome dataList:{}", dataList);
            dataList.stream().filter(data -> Objects.equals(objectData.getId(), data.getId())).forEach(data -> {
                newLifeStatus = ObjectDataExt.of(data).getLifeStatusText();
            });
        }
    }
}
