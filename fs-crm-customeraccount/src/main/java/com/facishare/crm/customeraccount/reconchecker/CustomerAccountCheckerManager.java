package com.facishare.crm.customeraccount.reconchecker;

import com.facishare.crm.bizreconciliation.model.BizReconCompareResult;
import com.facishare.crm.bizreconciliation.util.BizReconciliationUtil;
import com.facishare.crm.customeraccount.constants.*;
import com.facishare.crm.customeraccount.enums.*;
import com.facishare.crm.customeraccount.predefine.handler.RuleHandlerUtil;
import com.facishare.crm.customeraccount.predefine.handler.checkrule.CustomerFundAccount;
import com.facishare.crm.customeraccount.predefine.manager.FundAccountConfigManager;
import com.facishare.crm.customeraccount.predefine.service.dto.FieldMappingModel;
import com.facishare.crm.customeraccount.predefine.service.dto.ObjectMappingModel;
import com.facishare.crmcommon.util.SearchUtil;
import com.facishare.paas.appframework.core.model.FundAccountBaseService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class CustomerAccountCheckerManager {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private FundAccountConfigManager fundAccountConfigManager;

    public Optional<IObjectData> findDataIncludeDeleted(User user, String objectApiName, String objectDataId) {
        try {
            IObjectData objectData = serviceFacade.findObjectDataIgnoreStatus(user, objectDataId, objectApiName);
            return Optional.ofNullable(objectData);
        } catch (Exception e) {
            log.warn("findData error,tenantId:{},objectApiName:{},objectDataId:{}", user.getTenantId(), objectApiName, objectDataId, e);
            return Optional.empty();
        }
    }

    public Map<String, IObjectData> findDataIncludeDeleted(User user, String objectApiName, List<String> dataIds) {
        List<IObjectData> dataList = serviceFacade.findObjectDataByIdsIncludeDeleted(user, dataIds, objectApiName);
        return dataList.stream().collect(Collectors.toMap(IObjectData::getId, Function.identity()));
    }

    public BizReconCompareResult isBizDataError(User user, String objectApiName, String objectDataId) {
        BizReconCompareResult compareResult = new BizReconCompareResult();
        boolean isAuthOpen = fundAccountConfigManager.isAccountAuthOpen(user.getTenantId());
        if (!isAuthOpen) {
            return compareResult;
        }
        //查询对象授权，收入、支出；若有支出授权，则查询规则使用记录，核对冻结、解冻、流水；若有收入授权，则核对收入流水
        List<IObjectData> fAccountAuthList = queryFAccountAuth(user, objectApiName);
        if (CollectionUtils.isEmpty(fAccountAuthList)) {
            return compareResult;
        }
        Optional<IObjectData> bizDataOptional = findDataIncludeDeleted(user, objectApiName, objectDataId);
        for (IObjectData fAccountAuth : fAccountAuthList) {
            String authType = fAccountAuth.get(FAccountAuthorizationConstants.Field.AuthorizedType.apiName, String.class);
            if (FAccountAuthAuthorizedTypeEnum.Income.getValue().equals(authType)) {
                String enterAmountField = fAccountAuth.get(FAccountAuthorizationConstants.Field.EntryAmountFieldApiName.apiName, String.class);
                String customerField = fAccountAuth.get(FAccountAuthorizationConstants.Field.EntryCustomerFieldApiName.apiName, String.class);
                compareResult = isDataEnterAccountError(user, bizDataOptional.orElse(null), customerField, enterAmountField, objectApiName, objectDataId);
                if (compareResult.isError()) {
                    return compareResult;
                }
            } else if (FAccountAuthAuthorizedTypeEnum.Outcome.getValue().equals(authType)) {
                compareResult = isDataCheckRuleError(user, bizDataOptional.orElse(null), objectApiName, objectDataId);
                if (compareResult.isError()) {
                    return compareResult;
                }
            }
        }
        return compareResult;
    }

    public BizReconCompareResult isDataCheckRuleError(User user, String objectApiName, String objectDataId) {
        Optional<IObjectData> optional = findDataIncludeDeleted(user, objectApiName, objectDataId);
        return isDataCheckRuleError(user, optional.orElse(null), objectApiName, objectDataId);
    }

    public BizReconCompareResult isDataCheckRuleError(User user, IObjectData objectData, String objectApiName, String objectDataId) {
        BizReconCompareResult compareResult = new BizReconCompareResult();
        List<IObjectData> accountRuleUseRecordList = queryRuleUseRecord(user, objectApiName, objectDataId);
        if (CollectionUtils.isEmpty(accountRuleUseRecordList)) {
            return compareResult;
        }

        for (IObjectData ruleUseRecordData : accountRuleUseRecordList) {
            String ruleType = ruleUseRecordData.get(AccountRuleUseRecordConstants.Field.RuleType.apiName, String.class);
            String json = ruleUseRecordData.get(AccountRuleUseRecordConstants.Field.CheckRule.apiName, String.class);
            IObjectData checkRuleData = new ObjectData();
            checkRuleData.fromJsonString(json);
            String checkRuleId = ruleUseRecordData.get(AccountRuleUseRecordConstants.Field.CheckRuleId.apiName, String.class);

            if (AccountCheckRuleTypeEnum.Check_Reduce.getValue().equals(ruleType)) {
                String ruleStage = ruleUseRecordData.get(AccountRuleUseRecordConstants.Field.RuleStage.apiName, String.class);
                if (RuleStageEnum.CheckValidate.value.equals(ruleStage)) {
                    //冻结
                    List<IObjectData> frozenRecordDataList = queryFrozenRecord(user, objectApiName, objectDataId);
                    compareResult = isDataFrozenError(user, objectData, checkRuleData, frozenRecordDataList);
                } else {
                    //解冻
                    List<IObjectData> unfreezeDataList = queryUnfreezeDetail(user, objectApiName, objectDataId, checkRuleId);
                    compareResult = isDataUnfreezeError(user, objectData, checkRuleData, unfreezeDataList);
                }
            } else if (AccountCheckRuleTypeEnum.Direct_Reduce.getValue().equals(ruleType)) {
                //直接扣减
                List<IObjectData> outComeFlowList = queryOutcomeFlow(user, objectApiName, objectDataId, ExpenseTypeEnum.ValidateDeduct.getValue());
                List<String> flowIds = outComeFlowList.stream().map(IObjectData::getId).collect(Collectors.toList());
                List<IObjectData> unfreezeDataList = queryUnfreezeByFlow(user, flowIds);
                Set<String> checkReduceFlowIds = unfreezeDataList.stream().map(x -> x.get(UnfreezeDetailConstant.Field.AccountTransactionFlow.apiName, String.class, "")).collect(Collectors.toSet());
                List<IObjectData> directFlowList = outComeFlowList.stream().filter(x -> !checkReduceFlowIds.contains(x.getId())).collect(Collectors.toList());
                compareResult = isDataDirectComponentError(user, objectData, checkRuleData, directFlowList);
            } else if (AccountCheckRuleTypeEnum.Component_Reduce.getValue().equals(ruleType)) {
                //组件扣减
                List<IObjectData> outComeFlowList = queryOutcomeFlow(user, objectApiName, objectDataId, ExpenseTypeEnum.ComponentDeduct.getValue());
                compareResult = isDataDirectComponentError(user, objectData, checkRuleData, outComeFlowList);
            }
            if (compareResult.isError()) {
                break;
            }
        }
        return compareResult;
    }

    public BizReconCompareResult isDataDirectComponentError(User user, IObjectData objectData, IObjectData checkRuleData, List<IObjectData> accountTransactionFlowList) {
        BizReconCompareResult compareResult = new BizReconCompareResult();
        if (Objects.isNull(objectData) || objectData.isDeleted() || ObjectDataExt.of(objectData).isIneffective()) {
            int flowCount = CollectionUtils.size(accountTransactionFlowList);
            if (flowCount > 0) {
                compareResult.appendFormatMessage("数据[%s]未生效或已作废，仍有生效流水", Objects.nonNull(objectData) ? objectData.getName() : "");
                log.warn("component or direct error,data not effective but has flow,tenantId:{},objectData:{}", user.getTenantId(), objectData);
            }
        } else {
            List<ObjectMappingModel> mappingModels = RuleHandlerUtil.getObjectMapping(checkRuleData, AccountCheckRuleConstants.Field.ReduceMapping.apiName);
            Map<CustomerFundAccount, BigDecimal> dataCustomerFundAccountAmountMap = parseMappingModel(objectData, mappingModels);
            Map<CustomerFundAccount, BigDecimal> flowCustomerFundAccountAmountMap = Maps.newHashMap();
            for (IObjectData flowData : accountTransactionFlowList) {
                String customerId = flowData.get(AccountTransactionFlowConst.Field.Customer.apiName, String.class, "");
                String fundAccountId = flowData.get(AccountTransactionFlowConst.Field.FundAccount.apiName, String.class, "");
                BigDecimal expenseAmount = flowData.get(AccountTransactionFlowConst.Field.ExpenseAmount.apiName, BigDecimal.class, BigDecimal.ZERO);
                BigDecimal oldAmount = flowCustomerFundAccountAmountMap.put(CustomerFundAccount.of(customerId, fundAccountId), expenseAmount);
                if (Objects.nonNull(oldAmount)) {
                    compareResult.appendFormatMessage("数据[%s]同一个客户账户余额存在多条流水", objectData.getName());
                    log.warn("component or direct flow more than one,tenantId:{},objectData:{},flowData:{},oldAmount:{}", user.getTenantId(), objectData, flowData, oldAmount);
                    break;
                }
            }

            if (!compareResult.isError()) {
                if (CollectionUtils.size(dataCustomerFundAccountAmountMap) != CollectionUtils.size(flowCustomerFundAccountAmountMap)) {
                    compareResult.appendFormatMessage("数据[%s]直接扣减或组件扣减流水异常", objectData.getName());
                    log.warn("component or direct flow error,tenantId:{},objectData:{},dataAmountMap:{},flowAmountMap:{}", user.getTenantId(), objectData, dataCustomerFundAccountAmountMap, flowCustomerFundAccountAmountMap);
                } else {
                    dataCustomerFundAccountAmountMap.forEach((k, v) -> {
                        BigDecimal flowAmount = flowCustomerFundAccountAmountMap.getOrDefault(k, BigDecimal.ZERO);
                        if (flowAmount.compareTo(v) != 0 && !compareResult.isError()) {
                            compareResult.appendFormatMessage("数据[%s]组件扣减或直接扣减金额[%s]与流水金额[%s]不一致", objectData.getName(), v, flowAmount);
                        }
                    });
                }
            }

        }

        return compareResult;
    }

    public BizReconCompareResult isDataUnfreezeError(User user, IObjectData objectData, IObjectData checkRuleData, List<IObjectData> unfreezeDetailDataList) {
        BizReconCompareResult compareResult = new BizReconCompareResult();
        if (Objects.isNull(objectData) || objectData.isDeleted() || ObjectDataExt.of(objectData).isIneffective()) {
            int unfreezeCount = CollectionUtils.size(unfreezeDetailDataList);
            if (unfreezeCount > 0) {
                compareResult.appendFormatMessage("数据[%s]已作废或未生效，仍有生效解冻", Objects.nonNull(objectData.getName()) ? objectData.getName() : "");
                log.warn("unfreeze error,data not effective,but has unfreeze,tenantId:{},objectData:{},unfreezeCount:{}", user.getTenantId(), objectData, unfreezeCount);
                return compareResult;
            }
        } else {
            List<ObjectMappingModel> mappingModels = RuleHandlerUtil.getObjectMapping(checkRuleData, AccountCheckRuleConstants.Field.ReduceMapping.apiName);

            Map<CustomerFundAccount, BigDecimal> dataCustomerFundAccountAmountMap = parseMappingModel(objectData, mappingModels);
            Map<CustomerFundAccount, BigDecimal> unfreezeCustomerFundAccountAmountMap = Maps.newHashMap();
            List<String> flowIds = unfreezeDetailDataList.stream().map(x -> x.get(UnfreezeDetailConstant.Field.AccountTransactionFlow.apiName, String.class, "")).collect(Collectors.toList());
            List<IObjectData> flowDataList = serviceFacade.findObjectDataByIds(user.getTenantId(), flowIds, AccountTransactionFlowConst.API_NAME);
            Map<String, IObjectData> flowDataMap = flowDataList.stream().collect(Collectors.toMap(IObjectData::getId, Function.identity()));
            for (IObjectData unfreezeDetailData : unfreezeDetailDataList) {
                String flowId = unfreezeDetailData.get(UnfreezeDetailConstant.Field.AccountTransactionFlow.apiName, String.class);
                IObjectData flowData = flowDataMap.get(flowId);
                if (Objects.isNull(flowData)) {
                    compareResult.appendFormatMessage("数据[%s]的解冻[%s]与流水不一致", objectData.getName(), unfreezeDetailData.getName());
                    log.warn("unfreeze relate flow not exist, tenantId:{},objectData:{},unfreezeData:{}", user.getTenantId(), objectData, unfreezeDetailData);
                    break;
                }
                BigDecimal expenseAmount = flowData.get(AccountTransactionFlowConst.Field.ExpenseAmount.apiName, BigDecimal.class, BigDecimal.ZERO);
                BigDecimal unfreezeAmount = unfreezeDetailData.get(UnfreezeDetailConstant.Field.UnfreezeAmount.apiName, BigDecimal.class, BigDecimal.ZERO);
                if (expenseAmount.compareTo(unfreezeAmount) != 0) {
                    compareResult.appendFormatMessage("数据[%s]的解冻金额[%s]与账户收支流水金额[%s]不一致", objectData.getName(), unfreezeAmount, expenseAmount);
                    log.warn("unfreeze and flow diff, tenantId:{},objectData:{},unfreezeData:{},flowData:{}", user.getTenantId(), objectData, unfreezeDetailData, flowData);
                    break;
                }
                String customerId = flowData.get(AccountTransactionFlowConst.Field.Customer.apiName, String.class);
                String fundAccountId = flowData.get(AccountTransactionFlowConst.Field.FundAccount.apiName, String.class);

                BigDecimal oldValue = unfreezeCustomerFundAccountAmountMap.put(CustomerFundAccount.of(customerId, fundAccountId), unfreezeDetailData.get(UnfreezeDetailConstant.Field.UnfreezeAmount.apiName, BigDecimal.class, BigDecimal.ZERO));
                if (Objects.nonNull(oldValue)) {
                    compareResult.appendFormatMessage("数据[%s]同一个客户账户余额存在多条解冻", objectData.getName());
                    log.warn("unfreeze exist more than noe, tenantId:{},objectData:{},unfreezeData:{},flowData:{}", user.getTenantId(), objectData, unfreezeDetailData, flowData);
                    break;
                }
            }

            if (!compareResult.isError()) {
                if (CollectionUtils.size(dataCustomerFundAccountAmountMap) != CollectionUtils.size(unfreezeCustomerFundAccountAmountMap)) {
                    compareResult.appendFormatMessage("数据[%s]解冻流水异常", objectData.getName());
                    log.warn("unfreeze size error,tenantId:{},objectData:{},dataCFAAmountMap:{},unfreezeCFAAmountMap:{}", user.getTenantId(), objectData, dataCustomerFundAccountAmountMap, unfreezeCustomerFundAccountAmountMap);
                } else {
                    dataCustomerFundAccountAmountMap.forEach((x, v) -> {
                        BigDecimal unfreezeAmount = unfreezeCustomerFundAccountAmountMap.getOrDefault(x, BigDecimal.ZERO);
                        if (unfreezeAmount.compareTo(v) != 0 && !compareResult.isError()) {
                            compareResult.appendFormatMessage("数据[%]解冻字段金额[%s]与解冻金额[%s]不一致", objectData.getName(), x, unfreezeAmount);
                            log.warn("unfreeze amount diff,tenantId:{},objectData:{},dataAmount:{},unfreezeAmount:{}", user.getTenantId(), objectData, v, unfreezeAmount);
                        }
                    });
                }
            }
        }

        return compareResult;
    }

    public BizReconCompareResult isDataFrozenError(User user, IObjectData objectData, IObjectData checkRuleData, List<IObjectData> frozenRecordDataList) {
        BizReconCompareResult compareResult = new BizReconCompareResult();
        List<IObjectData> frozenRecordDataListOfNoChargeOff = CollectionUtils.emptyIfNull(frozenRecordDataList).stream().filter(x -> !RuleHandlerUtil.isChargedOff(x)).collect(Collectors.toList());

        if (Objects.isNull(objectData) || objectData.isDeleted() || ObjectDataExt.of(objectData).isIneffective()) {
            int frozenCount = CollectionUtils.size(frozenRecordDataList);
            if (frozenCount > 0) {
                compareResult.appendFormatMessage("数据[%s]未生效或已作废，仍有生效冻结", Objects.nonNull(objectData) ? objectData.getName() : "");
                log.warn("frozen error,data not effective,but has frozen,tenantId:{},objectData:{},frozenCount:{}", user.getTenantId(), objectData, frozenCount);
            }
        } else {
            List<ObjectMappingModel> mappingModels = RuleHandlerUtil.getObjectMapping(checkRuleData, AccountCheckRuleConstants.Field.OccupiedMapping.apiName);
            Map<CustomerFundAccount, BigDecimal> dataCustomerFundAccountAmountMap = parseMappingModel(objectData, mappingModels);
            Map<CustomerFundAccount, BigDecimal> frozenCustomerFundAccountAmountMap = Maps.newHashMap();
            List<String> customerAccountIds = frozenRecordDataListOfNoChargeOff.stream().map(x -> x.get(AccountFrozenRecordConstant.Field.CustomerAccountId.apiName, String.class, "")).collect(Collectors.toList());
            List<IObjectData> customerAccountDataList = serviceFacade.findObjectDataByIds(user.getTenantId(), customerAccountIds, NewCustomerAccountConstants.API_NAME);
            Map<String, IObjectData> customerAccountDataMap = customerAccountDataList.stream().collect(Collectors.toMap(IObjectData::getId, Function.identity()));
            for (IObjectData frozenData : frozenRecordDataListOfNoChargeOff) {
                String customerAccountId = frozenData.get(AccountFrozenRecordConstant.Field.CustomerAccountId.apiName, String.class);
                IObjectData customerAccountData = customerAccountDataMap.get(customerAccountId);
                if (Objects.nonNull(customerAccountData)) {
                    String fundAccountId = customerAccountData.get(NewCustomerAccountConstants.Field.FundAccount.apiName, String.class, "");
                    String customerId = customerAccountData.get(NewCustomerAccountConstants.Field.Customer.apiName, String.class, "");
                    CustomerFundAccount customerFundAccount = CustomerFundAccount.of(customerId, fundAccountId);
                    BigDecimal oldAmount = frozenCustomerFundAccountAmountMap.put(customerFundAccount, frozenData.get(AccountFrozenRecordConstant.Field.FreezeAmount.apiName, BigDecimal.class, BigDecimal.ZERO));
                    if (Objects.nonNull(oldAmount)) {
                        compareResult.appendFormatMessage("数据[%s]存在多条冻结记录", objectData.getName());
                        log.warn("frozen more than one,tenantId:{},objectData:{}", user.getTenantId(), objectData);
                        break;
                    }
                }
            }
            if (!compareResult.isError()) {
                if (dataCustomerFundAccountAmountMap.size() != frozenCustomerFundAccountAmountMap.size()) {
                    compareResult.appendFormatMessage("数据[%s]冻结记录异常");
                    log.warn("frozen size error,tenantId:{},objectData:{},dataCFAAmountMap:{},frozenCFAAmountMap:{}", user.getTenantId(), objectData, dataCustomerFundAccountAmountMap, frozenCustomerFundAccountAmountMap);
                } else {
                    dataCustomerFundAccountAmountMap.forEach((k, v) -> {
                        BigDecimal freezeAmount = frozenCustomerFundAccountAmountMap.getOrDefault(k, BigDecimal.ZERO);
                        if (freezeAmount.compareTo(v) != 0 && !compareResult.isError()) {
                            compareResult.appendFormatMessage("数据[%s]冻结字段金额[%s]与冻结金额[%s]不一致", objectData.getName(), v, freezeAmount);
                            log.warn("frozen amount diff,tenantId:{},objectData:{},dataAmount:{},freezeAmount:{}", user.getTenantId(), objectData, v, freezeAmount);
                        }
                    });
                }
            }
        }
        return compareResult;

    }

    public Map<CustomerFundAccount, BigDecimal> parseMappingModel(IObjectData objectData, List<ObjectMappingModel> mappingModels) {
        Map<CustomerFundAccount, BigDecimal> map = Maps.newHashMap();
        for (ObjectMappingModel mappingModel : mappingModels) {
            String fundAccountId = mappingModel.getFundAccountId();
            String amountField = "";
            String customerField = "";
            for (FieldMappingModel fieldMappingModel : CollectionUtils.emptyIfNull(mappingModel.getFieldMappingList())) {
                String targetFieldApiName = fieldMappingModel.getTargetFieldApiName();
                if (AccountFrozenRecordConstant.Field.FreezeAmount.apiName.equals(targetFieldApiName) || AccountTransactionFlowConst.Field.ExpenseAmount.apiName.equals(targetFieldApiName)) {
                    amountField = fieldMappingModel.getSourceFieldApiName();
                }
                if (AccountFrozenRecordConstant.Field.AccountId.apiName.equals(targetFieldApiName) || AccountTransactionFlowConst.Field.Customer.apiName.equals(targetFieldApiName)) {
                    customerField = fieldMappingModel.getSourceFieldApiName();
                }
            }
            String customerId = objectData.get(customerField, String.class, "");
            if (StringUtils.isNotEmpty(customerId) && StringUtils.isNotEmpty(fundAccountId)) {
                BigDecimal amount = objectData.get(amountField, BigDecimal.class, BigDecimal.ZERO);
                if (amount.compareTo(BigDecimal.ZERO) != 0) {
                    map.put(CustomerFundAccount.of(customerId, fundAccountId), amount);
                }
            }
        }
        return map;
    }

    public BizReconCompareResult isDataEnterAccountError(User user, IObjectData objectData, String customerField, String amountField, String objectApiName, String objectDataId) {
        BizReconCompareResult compareResult = new BizReconCompareResult();
        String revenueType = FundAccountBaseService.getRevenueType(objectApiName);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, AccountTransactionFlowConst.Field.RelateRecordObjectApiName.apiName, objectApiName);
        SearchUtil.fillFilterEq(filters, AccountTransactionFlowConst.Field.RelateRecordObjectDataId.apiName, objectDataId);
        SearchUtil.fillFilterEq(filters, AccountTransactionFlowConst.Field.RevenueType.apiName, revenueType);
        SearchUtil.fillFilterEq(filters, AccountTransactionFlowConst.Field.EntryStatus.apiName, EntryStatusEnum.AlreadyEntry.getValue());
        SearchUtil.fillFilterEq(filters, "record_type", AccountTransactionFlowConst.RecordType.IncomeRecordType.apiName);
        List<IObjectData> accountTransactionFlowList = query(user, AccountTransactionFlowConst.API_NAME, filters);
        int flowCount = CollectionUtils.size(accountTransactionFlowList);
        if (Objects.isNull(objectData) || objectData.isDeleted() || ObjectDataExt.of(objectData).isIneffective()) {
            //数据已作废或已取消入账
            if (flowCount > 0) {
                compareResult.appendMessage(String.format("数据[%s]未生效或已作废或取消入账，但存在入账类型流水", Objects.nonNull(objectData) ? objectData.getName() : ""));
                log.warn("enterAccount error,data not effective but exist flow,tenantId:{},objectData:{},flowCount:{}", user.getTenantId(), objectData, flowCount);
            }
        } else {
            String entryStatus = objectData.get("entry_status", String.class);
            boolean enterAccount = EntryStatusEnum.AlreadyEntry.getValue().equals(entryStatus);
            if (flowCount > 1) {
                compareResult.appendMessage(String.format("数据[%s]存在多条入账类型流水", objectData.getName()));
                log.warn("enterAccount flow more than one,tenantId:{},objectData:{},flowCount:{}", user.getTenantId(), objectData, flowCount);
            } else if (CollectionUtils.size(accountTransactionFlowList) < 1 && enterAccount) {
                compareResult.appendMessage(String.format("数据[%s]已入账，无入账流水", objectData.getName()));
                log.warn("enterAccount but no flow,tenantId:{},objectData:{}", user.getTenantId(), objectData);
            } else if (enterAccount) {
                String dataCustomerId = objectData.get(customerField, String.class);
                BigDecimal dataEnterAmount = objectData.get(amountField, BigDecimal.class, BigDecimal.ZERO);
                IObjectData transactionFlowData = accountTransactionFlowList.get(0);
                BigDecimal revenueAmount = transactionFlowData.get(AccountTransactionFlowConst.Field.RevenueAmount.apiName, BigDecimal.class, BigDecimal.ZERO);
                String customerId = transactionFlowData.get(AccountTransactionFlowConst.Field.Customer.apiName, String.class);
                if (StringUtils.equals(customerId, dataCustomerId) || revenueAmount.compareTo(dataEnterAmount) != 0) {
                    compareResult.appendMessage(String.format("数据[%s]与流水的客户或金额不一致", objectData.getName()));
                    log.warn("enterAccount amount diff,tenantId:{},objectData:{},flowCustomerId:{},dataAmount:{},revenueAmount:{}", user.getTenantId(), objectData, customerId, dataEnterAmount, revenueAmount);
                }
            }
        }
        return compareResult;
    }

    public List<IObjectData> queryFAccountAuth(User user, String objectApiName) {
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, objectApiName);
        return query(user, FAccountAuthorizationConstants.API_NAME, filters);
    }

    public List<IObjectData> queryFrozenRecord(User user, String objectApiName, String objectDataId) {
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, AccountFrozenRecordConstant.Field.CheckRecordObjectApiName.apiName, objectApiName);
        SearchUtil.fillFilterEq(filters, AccountFrozenRecordConstant.Field.CheckRecordObjectDataId.apiName, objectDataId);
        SearchUtil.fillFilterNotEq(filters, AccountFrozenRecordConstant.Field.EntryStatus.apiName, EntryStatusEnum.Cancelled.getValue());
        return query(user, AccountFrozenRecordConstant.API_NAME, filters);
    }

    public List<IObjectData> queryUnfreezeDetail(User user, String objectApiName, String objectDataId, String frozenId) {
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, UnfreezeDetailConstant.Field.UnfreezeObjectApiName.apiName, objectApiName);
        SearchUtil.fillFilterEq(filters, UnfreezeDetailConstant.Field.UnfreezeObjectDataId.apiName, objectDataId);
        SearchUtil.fillFilterEq(filters, UnfreezeDetailConstant.Field.AccountFrozenRecordId.apiName, frozenId);
        SearchUtil.fillFilterEq(filters, UnfreezeDetailConstant.Field.EntryStatus.apiName, EntryStatusEnum.AlreadyEntry.getValue());
        return query(user, UnfreezeDetailConstant.API_NAME, filters);
    }

    public List<IObjectData> queryUnfreezeByFlow(User user, List<String> flowIds) {
        if (CollectionUtils.isEmpty(flowIds)) {
            return Lists.newArrayList();
        }
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, UnfreezeDetailConstant.Field.AccountTransactionFlow.apiName, flowIds);
        SearchUtil.fillFilterEq(filters, UnfreezeDetailConstant.Field.EntryStatus.apiName, EntryStatusEnum.AlreadyEntry.getValue());
        return query(user, UnfreezeDetailConstant.API_NAME, filters);
    }

    public List<IObjectData> queryOutcomeFlow(User user, String objectApiName, String objectDataId, String expenseType) {
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, AccountTransactionFlowConst.Field.RelateRecordObjectApiName.apiName, objectApiName);
        SearchUtil.fillFilterEq(filters, AccountTransactionFlowConst.Field.RelateRecordObjectDataId.apiName, objectDataId);
        SearchUtil.fillFilterEq(filters, AccountTransactionFlowConst.Field.ExpenseType.apiName, expenseType);
        SearchUtil.fillFilterEq(filters, "record_type", AccountTransactionFlowConst.RecordType.OutcomeRecordType.apiName);
        SearchUtil.fillFilterNotEq(filters, AccountTransactionFlowConst.Field.EntryStatus.apiName, EntryStatusEnum.Cancelled.getValue());
        return query(user, AccountTransactionFlowConst.API_NAME, filters);
    }

    public List<IObjectData> queryRuleUseRecord(User user, String objectApiName, String objectDataId) {
        List<IFilter> filters = Lists.newArrayList();
        BizReconciliationUtil.addFilter(filters, AccountRuleUseRecordConstants.Field.CheckRecordObjectApiName.apiName, objectApiName, Operator.EQ);
        BizReconciliationUtil.addFilter(filters, AccountRuleUseRecordConstants.Field.CheckRecordObjectDataId.apiName, objectDataId, Operator.EQ);
        return query(user, AccountRuleUseRecordConstants.API_NAME, filters);
    }

    public List<IObjectData> query(User user, String objectApiName, List<IFilter> filters) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(filters);
        query.setLimit(100);
        return serviceFacade.findBySearchQuery(user, objectApiName, query).getData();
    }

}
