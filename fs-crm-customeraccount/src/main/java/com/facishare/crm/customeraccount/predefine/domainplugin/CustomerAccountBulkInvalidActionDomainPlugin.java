package com.facishare.crm.customeraccount.predefine.domainplugin;

import com.facishare.crm.customeraccount.model.CustomerAccountPluginModel;
import com.facishare.crm.customeraccount.predefine.manager.CustomerAccountDomainPluginManager;
import com.facishare.crmcommon.util.DomainPluginDescribeExt;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.domain.BulkInvalidActionDomainPlugin;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
//@DomainProvider(name = "customer_account_BulkInvalid")
@ServiceModule("customer_account_BulkInvalid")
public class CustomerAccountBulkInvalidActionDomainPlugin extends EmptyBulkInvalidActionDomainPlugin {

    @Autowired
    private CustomerAccountDomainPluginManager customerAccountDomainPluginManager;

    @ServiceMethod("before")
    public BulkInvalidActionDomainPlugin.Result beforeService(ServiceContext context, BulkInvalidActionDomainPlugin.Arg arg) {
        ActionContext actionContext = new ActionContext(context.getRequestContext(), arg.getObjectApiName(), ObjectAction.BULK_INVALID.getActionCode());
        return before(actionContext, arg);
    }

    @ServiceMethod("after")
    public BulkInvalidActionDomainPlugin.Result afterService(ServiceContext context, BulkInvalidActionDomainPlugin.Arg arg) {
        ActionContext actionContext = new ActionContext(context.getRequestContext(), arg.getObjectApiName(), ObjectAction.BULK_INVALID.getActionCode());
        return this.after(actionContext, arg);
    }

    @ServiceMethod("finally")
    public BulkInvalidActionDomainPlugin.Result finallyService(ServiceContext context, BulkInvalidActionDomainPlugin.Arg arg) {
        ActionContext actionContext = new ActionContext(context.getRequestContext(), arg.getObjectApiName(), ObjectAction.BULK_INVALID.getActionCode());
        return this.finallyDo(actionContext, arg);
    }

    @Override
    public BulkInvalidActionDomainPlugin.Result finallyDo(ActionContext context, BulkInvalidActionDomainPlugin.Arg arg) {
        CustomerAccountPluginModel.Arg pluginArg = getArg(context, arg);
        customerAccountDomainPluginManager.pluginAction(pluginArg);
        return new BulkInvalidActionDomainPlugin.Result();
    }

    protected CustomerAccountPluginModel.Arg getArg(ActionContext context, BulkInvalidActionDomainPlugin.Arg arg) {
        DomainPluginDescribeExt ext = DomainPluginDescribeExt.of(arg.getObjectApiName(), arg.getPluginDescribe());

        return CustomerAccountPluginModel.Arg.builder().action(ObjectAction.BULK_INVALID.getActionCode())
                .dmExt(ext)
                .objectDataList(arg.getObjectDataList())
                .masterObjectApiName(arg.getObjectApiName())
                .contextData(arg.getContextData())
                .user(context.getUser())
                .build();
    }
}
