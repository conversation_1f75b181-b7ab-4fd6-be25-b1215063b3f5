package com.facishare.crm.customeraccount.predefine.service.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;


public class CreateTargetObjectModel {

    @Data
    @AllArgsConstructor
    public static class Result {
        private Integer returnFlagNoSuccessPayStatements;

        private Integer successPayStatementSize;
        private Integer failPayStatementSize;
        private Integer payCacheNoExistPayStatementSize;

        private List<PayStatementInfo> successPayStatements;
        private List<PayStatementInfo> failPayStatements;
        private List<PayStatementInfo> payCacheNoExistPayStatements;
    }

    @Data
    @AllArgsConstructor
    public static class PayStatementInfo {
        private String id;
        private String name;
        private String failInfo;
    }
}
