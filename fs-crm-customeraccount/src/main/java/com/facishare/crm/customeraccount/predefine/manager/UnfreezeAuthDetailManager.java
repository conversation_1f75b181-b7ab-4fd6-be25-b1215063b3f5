package com.facishare.crm.customeraccount.predefine.manager;

import com.facishare.crm.customeraccount.constants.*;
import com.facishare.crm.customeraccount.enums.AccountCheckRuleTypeEnum;
import com.facishare.crm.customeraccount.enums.FAccountAuthAuthorizedTypeEnum;
import com.facishare.crm.customeraccount.predefine.service.dto.CheckDeleteUnfreezeAuthDetailModel;
import com.facishare.crmcommon.manager.CommonLangManager;
import com.facishare.crmcommon.manager.CommonObjDataManager;
import com.facishare.crmcommon.util.ObjectDataUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class UnfreezeAuthDetailManager {

    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private AccountCheckRuleManager accountCheckRuleManager;
    @Autowired
    private FAccountAuthorizationManager fAccountAuthorizationManager;
    @Autowired
    private CommonLangManager commonLangManager;
    @Autowired
    private CommonObjDataManager commonObjDataManager;


    public List<IObjectData> query(String tenantId, List<String> fAccountAuthorizationIds) {
        if (CollectionUtils.empty(fAccountAuthorizationIds)) {
            return Lists.newArrayList();
        }

        return query(tenantId, fAccountAuthorizationIds, null);
    }

    public List<IObjectData> query(String tenantId, String unfreezeObject) {
        if (Strings.isNullOrEmpty(unfreezeObject)) {
            return Lists.newArrayList();
        }

        return query(tenantId, Lists.newArrayList(), Lists.newArrayList(unfreezeObject));
    }

    public IObjectData query(String tenantId, String fAccountAuthorizationId, String unfreezeObject) {
        List<IObjectData> list = query(tenantId, Lists.newArrayList(fAccountAuthorizationId), Lists.newArrayList(unfreezeObject));
        if (CollectionUtils.empty(list)) {
            return null;
        }

        return list.get(0);
    }

    public List<IObjectData> query(String tenantId, String fAccountAuthorizationId, List<String> unfreezeObjects) {
        return query(tenantId, Lists.newArrayList(fAccountAuthorizationId), unfreezeObjects);
    }

    public List<IObjectData> query(String tenantId, List<String> fAccountAuthorizationIds, List<String> unfreezeObjects) {
        if (CollectionUtils.empty(fAccountAuthorizationIds) && CollectionUtils.empty(unfreezeObjects)) {
            return Lists.newArrayList();
        }
        List<IFilter> filterList = Lists.newArrayList();

        if (!CollectionUtils.empty(fAccountAuthorizationIds)) {
            IFilter accountAuthIdFilter = new Filter();
            accountAuthIdFilter.setFieldName(UnfreezeAuthDetailConstants.Field.FAccountAuthorizationId.apiName);
            accountAuthIdFilter.setFieldValues(fAccountAuthorizationIds);
            accountAuthIdFilter.setOperator(Operator.IN);
            filterList.add(accountAuthIdFilter);
        }

        if (!CollectionUtils.empty(unfreezeObjects)) {
            IFilter unfreezeObjectFilter = new Filter();
            unfreezeObjectFilter.setFieldName(UnfreezeAuthDetailConstants.Field.UnfreezeObject.apiName);
            unfreezeObjectFilter.setFieldValues(unfreezeObjects);
            unfreezeObjectFilter.setOperator(Operator.IN);
            filterList.add(unfreezeObjectFilter);
        }

        IFilter deleteStatusFilter = new Filter();
        deleteStatusFilter.setFieldName(ObjectData.IS_DELETED);
        deleteStatusFilter.setFieldValues(Lists.newArrayList("0"));
        deleteStatusFilter.setOperator(Operator.EQ);
        filterList.add(deleteStatusFilter);

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(filterList);
        query.setOffset(0);
        query.setLimit(1000);

        User admin = User.systemUser(tenantId);
        return serviceFacade.findBySearchQuery(admin, UnfreezeAuthDetailConstants.API_NAME, query).getData();
    }

    public List<IObjectData> get(String tenantId, String unfreezeObject, List<String> excludeFAccountAuthorizationIds) {
        if (Strings.isNullOrEmpty(unfreezeObject)) {
            return Lists.newArrayList();
        }
        List<IFilter> filterList = Lists.newArrayList();

        if (!CollectionUtils.empty(excludeFAccountAuthorizationIds)) {
            IFilter accountAuthIdFilter = new Filter();
            accountAuthIdFilter.setFieldName(UnfreezeAuthDetailConstants.Field.FAccountAuthorizationId.apiName);
            accountAuthIdFilter.setFieldValues(excludeFAccountAuthorizationIds);
            accountAuthIdFilter.setOperator(Operator.NIN);
            filterList.add(accountAuthIdFilter);
        }

        IFilter unfreezeObjectFilter = new Filter();
        unfreezeObjectFilter.setFieldName(UnfreezeAuthDetailConstants.Field.UnfreezeObject.apiName);
        unfreezeObjectFilter.setFieldValues(Lists.newArrayList(unfreezeObject));
        unfreezeObjectFilter.setOperator(Operator.EQ);
        filterList.add(unfreezeObjectFilter);

        IFilter deleteStatusFilter = new Filter();
        deleteStatusFilter.setFieldName(ObjectData.IS_DELETED);
        deleteStatusFilter.setFieldValues(Lists.newArrayList("0"));
        deleteStatusFilter.setOperator(Operator.EQ);
        filterList.add(deleteStatusFilter);

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(filterList);
        query.setOffset(0);
        query.setLimit(1000);

        User admin = User.systemUser(tenantId);
        return serviceFacade.findBySearchQuery(admin, UnfreezeAuthDetailConstants.API_NAME, query).getData();
    }

    /*
     * 【解冻授权明细】被哪些【校验规则】使用了
     */
    public List<String> getHasUseUnfreezeAuthDetailAccountCheckRules(String tenantId, String unfreezeAuthDetailId) {
        Map<String, List<String>> unfreezeAuthDetailId2AccountCheckRules = getHasUseUnfreezeAuthDetailAccountCheckRules(tenantId, Lists.newArrayList(unfreezeAuthDetailId));
        return unfreezeAuthDetailId2AccountCheckRules.get(unfreezeAuthDetailId);
    }

    /*
     * 【解冻授权明细】被哪些【校验规则】使用了
     */
    public Map<String, List<String>> getHasUseUnfreezeAuthDetailAccountCheckRules(String tenantId, List<String> unfreezeAuthDetailIds) {
        Map<String, List<String>> unfreezeAuthDetailId2AccountCheckRules = new HashMap<>();
        if (CollectionUtils.empty(unfreezeAuthDetailIds)) {
            return unfreezeAuthDetailId2AccountCheckRules;
        }

        //【解冻授权明细】
        List<IObjectData> unfreezeAuthDetails = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, unfreezeAuthDetailIds, UnfreezeAuthDetailConstants.API_NAME);
        if (CollectionUtils.empty(unfreezeAuthDetails)) {
            return unfreezeAuthDetailId2AccountCheckRules;
        }

        //账户授权主对象
        String fAccountAuthorizationId = unfreezeAuthDetails.get(0).get(UnfreezeAuthDetailConstants.Field.FAccountAuthorizationId.apiName, String.class);
        List<IObjectData> fAccountAuthorizations = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, Lists.newArrayList(fAccountAuthorizationId), FAccountAuthorizationConstants.API_NAME);
        if (CollectionUtils.empty(fAccountAuthorizations)) {
            throw new ValidateException(I18N.text(CAI18NKey.ACCOUNT_AUTH_NOT_FOUND));
        }
        IObjectData fAccountAuthorizationData = fAccountAuthorizations.get(0);
        String authorizedObjectApiName = fAccountAuthorizationData.get(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, String.class);

        //查出所有的【校验扣减】的【校验规则】
        List<IObjectData> allCheckRuleAccountCheckRules = accountCheckRuleManager.getAccountCheckRuleData(tenantId, AccountCheckRuleTypeEnum.Check_Reduce.getValue(), authorizedObjectApiName);
        if (CollectionUtils.empty(allCheckRuleAccountCheckRules)) {
            return unfreezeAuthDetailId2AccountCheckRules;
        }

        //【解冻授权明细】被哪些【校验规则】使用了
        for (IObjectData unfreezeAuthDetail : unfreezeAuthDetails) {
            String unfreezeObject = unfreezeAuthDetail.get(UnfreezeAuthDetailConstants.Field.UnfreezeObject.apiName, String.class);
            List<String> useReduceRelatedObjectAccountCheckRuleNames = accountCheckRuleManager.getUseReduceRelatedObjectAccountCheckRuleNames(allCheckRuleAccountCheckRules, unfreezeObject);
            if (!CollectionUtils.empty(useReduceRelatedObjectAccountCheckRuleNames)) {
                unfreezeAuthDetailId2AccountCheckRules.put(unfreezeAuthDetail.getId(), useReduceRelatedObjectAccountCheckRuleNames);
            }
        }
        return unfreezeAuthDetailId2AccountCheckRules;
    }

    public List<CheckDeleteUnfreezeAuthDetailModel.ErrorInfo> getErrorInfos(String tenantId, List<String> unfreezeAuthDetailIds) {
        List<CheckDeleteUnfreezeAuthDetailModel.ErrorInfo> errorInfos = Lists.newArrayList();
        if (CollectionUtils.empty(unfreezeAuthDetailIds)) {
            return errorInfos;
        }

        Map<String, List<String>> hasUseUnfreezeAuthDetailAccountCheckRules = getHasUseUnfreezeAuthDetailAccountCheckRules(tenantId, unfreezeAuthDetailIds);
        if (hasUseUnfreezeAuthDetailAccountCheckRules == null || hasUseUnfreezeAuthDetailAccountCheckRules.size() == 0) {
            return errorInfos;
        }

        //【解冻授权明细】
        List<IObjectData> unfreezeAuthDetails = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, unfreezeAuthDetailIds, UnfreezeAuthDetailConstants.API_NAME);
        Map<String, IObjectData> unfreezeAuthDetailId2UnfreezeAuthDetail = unfreezeAuthDetails.stream().collect(Collectors.toMap(IObjectData::getId, d -> d));

        for (String unfreezeAuthDetailId : unfreezeAuthDetailIds) {
            if (!hasUseUnfreezeAuthDetailAccountCheckRules.containsKey(unfreezeAuthDetailId)) {
                continue;
            }

            List<String> hasUseUnfreezeAuthDetailAccountCheckRuleNameList = hasUseUnfreezeAuthDetailAccountCheckRules.get(unfreezeAuthDetailId);
            if (CollectionUtils.empty(hasUseUnfreezeAuthDetailAccountCheckRuleNameList)) {
                continue;
            }

            IObjectData unfreezeAuthDetail = unfreezeAuthDetailId2UnfreezeAuthDetail.get(unfreezeAuthDetailId);
            String unfreezeObject = unfreezeAuthDetail.get(UnfreezeAuthDetailConstants.Field.UnfreezeObject.apiName, String.class);
            String hasUseUnfreezeAuthDetailAccountCheckRuleNames = String.join(",", hasUseUnfreezeAuthDetailAccountCheckRuleNameList);
            String multiLangName = commonLangManager.getMultiLangName(tenantId, unfreezeObject);

            String errorMsg = I18N.text(CAI18NKey.OBJECT_HAS_BE_USED_UNFREEZE_AUTH_DETAIL_CAN_NOT_DELETED, multiLangName, hasUseUnfreezeAuthDetailAccountCheckRuleNames);
            CheckDeleteUnfreezeAuthDetailModel.ErrorInfo errorInfo = new CheckDeleteUnfreezeAuthDetailModel.ErrorInfo(unfreezeAuthDetailId, errorMsg);
            errorInfos.add(errorInfo);
        }

        return errorInfos;
    }

    /**
     * 删掉的UnfreezeObject"解冻对象"
     */
    public List<String> getToDeleteUnfreezeObjects(Map<String, List<IObjectData>> detailObjectData, Map<String, List<IObjectData>> dbDetailDataMap) {
        String objectApiName = UnfreezeAuthDetailConstants.API_NAME;
        List<String> newUnfreezeObjects = !detailObjectData.containsKey(objectApiName) ?
                new ArrayList<>() : detailObjectData.get(objectApiName).stream().map(d -> d.get(UnfreezeAuthDetailConstants.Field.UnfreezeObject.apiName, String.class)).collect(Collectors.toList());

        List<String> dbUnfreezeObjects = !dbDetailDataMap.containsKey(objectApiName) ?
                new ArrayList<>() : dbDetailDataMap.get(objectApiName).stream().map(d -> d.get(UnfreezeAuthDetailConstants.Field.UnfreezeObject.apiName, String.class)).collect(Collectors.toList());

        return dbUnfreezeObjects.stream().filter(id -> !newUnfreezeObjects.contains(id)).collect(Collectors.toList());
    }

    /**
     * RelatedField("关联字段")不能被修改 被修改了的 UnfreezeObject"解冻对象"
     */
    public List<String> getRelatedFieldChangeUnfreezeObjects(Map<String, List<IObjectData>> detailObjectData, Map<String, List<IObjectData>> dbDetailDataMap) {
        List<String> relatedFieldChangeUnfreezeObjects = Lists.newArrayList();

        String objectApiName = UnfreezeAuthDetailConstants.API_NAME;
        Map<String, String> unfreezeObject2RelatedField = !detailObjectData.containsKey(objectApiName) ?
                new HashMap<>() : detailObjectData.get(objectApiName).stream().collect(Collectors.toMap(
                d -> d.get(UnfreezeAuthDetailConstants.Field.UnfreezeObject.apiName, String.class),
                d -> d.get(UnfreezeAuthDetailConstants.Field.RelatedField.apiName, String.class)));

        if (unfreezeObject2RelatedField.size() == 0) {
            return relatedFieldChangeUnfreezeObjects;
        }

        Map<String, String> dbUnfreezeObject2RelatedField = !dbDetailDataMap.containsKey(objectApiName) ?
                new HashMap<>() : dbDetailDataMap.get(objectApiName).stream().collect(Collectors.toMap(
                d -> d.get(UnfreezeAuthDetailConstants.Field.UnfreezeObject.apiName, String.class),
                d -> d.get(UnfreezeAuthDetailConstants.Field.RelatedField.apiName, String.class)));

        for (String unfreezeObject : unfreezeObject2RelatedField.keySet()) {
            if (!dbUnfreezeObject2RelatedField.containsKey(unfreezeObject)) {
                continue;
            }
            String newRelatedField = unfreezeObject2RelatedField.get(unfreezeObject);
            String dbRelatedField = dbUnfreezeObject2RelatedField.get(unfreezeObject);
            if (!Objects.equals(newRelatedField, dbRelatedField)) {
                relatedFieldChangeUnfreezeObjects.add(unfreezeObject);
            }
        }

        return relatedFieldChangeUnfreezeObjects;
    }

    /**
     * 是否被【解冻授权明细】使用了（账户授权是自己除外）
     */
    public void checkHasUseByUnfreezeAuthDetail(String tenantId, String unfreezeObject) {
        //查【账户授权】
        IObjectData fAccountAuthorizationData = fAccountAuthorizationManager.getFAccountAuthorizationData(tenantId, unfreezeObject, FAccountAuthAuthorizedTypeEnum.Outcome.getValue());
        List<String> excludeFAccountAuthorizationIds = Lists.newArrayList();
        if (fAccountAuthorizationData != null) {
            excludeFAccountAuthorizationIds.add(fAccountAuthorizationData.getId());
        }

        //查使用了unfreezeObject的【解冻授权明细】
        List<IObjectData> unfreezeAuthDetails = get(tenantId, unfreezeObject, excludeFAccountAuthorizationIds);
        if (CollectionUtils.empty(unfreezeAuthDetails)) {
            return;
        }

        List<String> fAccountAuthorizationIds = unfreezeAuthDetails.stream().map(d -> d.get(UnfreezeAuthDetailConstants.Field.FAccountAuthorizationId.apiName, String.class)).collect(Collectors.toList());
        List<IObjectData> accountAuths = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, fAccountAuthorizationIds, FAccountAuthorizationConstants.API_NAME);
        List<String> accountAuthNames = accountAuths.stream().map(d -> d.get(FAccountAuthorizationConstants.Field.Name.apiName, String.class)).collect(Collectors.toList());

        String multiLangName = commonLangManager.getMultiLangName(tenantId, unfreezeObject);
        throw new ValidateException(I18N.text(CAI18NKey.AUTHORIZED_OBJECT_HAS_BE_USED_CAN_NOT_INVALID, multiLangName, accountAuthNames));
    }

    /**
     * 设置默认值
     */
    public void setDefaultValue(IObjectData fAccountAuthorization, Map<String, List<IObjectData>> detailObjectData) {
        String authorizedType = fAccountAuthorization.get(FAccountAuthorizationConstants.Field.AuthorizedType.apiName, String.class);

        //支出授权：frozen_actions、reduce_trigger_actions 默认值
        if (Objects.equals(authorizedType, FAccountAuthAuthorizedTypeEnum.Outcome.getValue())) {
            boolean isUnfreezeAuth = fAccountAuthorization.get(FAccountAuthorizationConstants.Field.IsUnfreezeAuth.apiName, Boolean.class, false);

            if (!isUnfreezeAuth) {
                detailObjectData.remove(UnfreezeAuthDetailConstants.API_NAME);
            } else {
                List<IObjectData> unfreezeAuthDetails = detailObjectData.get(UnfreezeAuthDetailConstants.API_NAME);
                if (!CollectionUtils.empty(unfreezeAuthDetails)) {
                    for (IObjectData unfreezeAuthDetail : unfreezeAuthDetails) {
                        String unfreezeObject = unfreezeAuthDetail.get(UnfreezeAuthDetailConstants.Field.UnfreezeObject.apiName, String.class);
                        List<String> unfreezeActions = getUnfreezeActions(unfreezeObject);
                        unfreezeAuthDetail.set(UnfreezeAuthDetailConstants.Field.UnfreezeActions.apiName, unfreezeActions);
                    }
                }
            }
        }
    }

    public boolean create(String tenantId, String fAccountAuthId, String authorizedObjectApiName, List<String> unfreezeObjects) {
        //查【解冻授权明细】
        List<IObjectData> unfreezeAuthDetails = query(tenantId, fAccountAuthId, unfreezeObjects);
        List<String> existUnfreezeObjects = unfreezeAuthDetails.stream().map(d -> d.get(UnfreezeAuthDetailConstants.Field.UnfreezeObject.apiName, String.class)).collect(Collectors.toList());
        //【解冻授权明细】缺少的，需要补充的对象
        List<String> needCreateUnfreezeObjects = unfreezeObjects.stream().filter(d -> !existUnfreezeObjects.contains(d)).collect(Collectors.toList());
        if (CollectionUtils.empty(needCreateUnfreezeObjects)) {
            return false;
        }

        User admin = User.systemUser(tenantId);
        List<IObjectData> addUnfreezeAuthDetails = new ArrayList<>();
        for (String needCreateUnfreezeObject : needCreateUnfreezeObjects) {
            List<String> unfreezeActions = getUnfreezeActions(needCreateUnfreezeObject);
            Optional<String> optional = com.facishare.crm.customeraccount.util.ObjectDataUtil.getReferenceFieldName(tenantId, authorizedObjectApiName, needCreateUnfreezeObject);
            if (!optional.isPresent()) {
                log.warn("create unfreezeAuthDetails getReferenceFieldName not exist tenantId[{}], authorizedObjectApiName[{}], needCreateUnfreezeObject[{}]", tenantId, authorizedObjectApiName, needCreateUnfreezeObject);
                continue;
            }
            String relatedField = optional.get();

            IObjectData unfreezeAuthDetail = ObjectDataUtil.getBaseObjectData(admin, UnfreezeAuthDetailConstants.API_NAME);
            unfreezeAuthDetail.set(UnfreezeAuthDetailConstants.Field.UnfreezeObject.apiName, needCreateUnfreezeObject);
            unfreezeAuthDetail.set(UnfreezeAuthDetailConstants.Field.RelatedField.apiName, relatedField);
            unfreezeAuthDetail.set(UnfreezeAuthDetailConstants.Field.UnfreezeActions.apiName, unfreezeActions);
            unfreezeAuthDetail.set(UnfreezeAuthDetailConstants.Field.FAccountAuthorizationId.apiName, fAccountAuthId);
            unfreezeAuthDetail.set("record_type", "default__c");
            addUnfreezeAuthDetails.add(unfreezeAuthDetail);
        }

        if (CollectionUtils.empty(addUnfreezeAuthDetails)) {
            return false;
        }

        try {
            serviceFacade.bulkSaveObjectData(addUnfreezeAuthDetails, admin);
        } catch (Exception e) {
            log.warn("create unfreezeAuthDetails serviceFacade.bulkSaveObjectData fail addUnfreezeAuthDetails[{}], admin[{}]", addUnfreezeAuthDetails, admin, e);
            throw e;
        }

        return true;
    }

    public List<String> getUnfreezeActions(String unfreezeObject) {
        List<String> unfreezeActions = Lists.newArrayList("Add_button_default", "fieldChange");
        if (Objects.equals(unfreezeObject, "DeliveryNoteObj")) {
            unfreezeActions.add("ConfirmReceipt_button_default");
        }

        return unfreezeActions;
    }

    /**
     * sourceUnfreezeAuthDetailDatas 复制到 targetTenantId 的 targetAccountAuthDataId 的从对象上(已经有的解冻对象排除掉）
     */
    public void copy(String targetTenantId, String targetAccountAuthDataId, List<IObjectData> sourceUnfreezeAuthDetailDatas, List<IObjectData> targetExistUnfreezeAuthDetailDatas, boolean useSourceId) {
        User admin = User.systemUser(targetTenantId);

        //已经存在的解冻对象
        List<String> targetExistUnfreezeObjects = targetExistUnfreezeAuthDetailDatas == null ? Lists.newArrayList() : targetExistUnfreezeAuthDetailDatas.stream()
                .map(d -> d.get(UnfreezeAuthDetailConstants.Field.UnfreezeObject.apiName, String.class)).collect(Collectors.toList());

        //保存新数据
        List<IObjectData> newUnfreezeAuthDetails = new ArrayList<>();
        for (IObjectData sourceUnfreezeAuthDetailData : sourceUnfreezeAuthDetailDatas) {
            String sourceUnfreezeObject = sourceUnfreezeAuthDetailData.get(UnfreezeAuthDetailConstants.Field.UnfreezeObject.apiName, String.class);
            if (targetExistUnfreezeObjects.contains(sourceUnfreezeObject)) {
                continue;
            }

            IObjectData unfreezeAuthDetailData = ObjectDataUtil.getBaseObjectData(admin, UnfreezeAuthDetailConstants.API_NAME);
            boolean idHasUse = commonObjDataManager.idHasUse(targetTenantId, UnfreezeAuthDetailConstants.API_NAME, sourceUnfreezeAuthDetailData.getId());
            log.info("copy tenantId[{}], objectApiName[{}], dataId[{}], idHasUse[{}]", targetTenantId, UnfreezeAuthDetailConstants.API_NAME, sourceUnfreezeAuthDetailData.getId(), idHasUse);
            if (useSourceId && !idHasUse) {
                unfreezeAuthDetailData.set(SystemConstants.Field.Id.apiName, sourceUnfreezeAuthDetailData.getId());
            }
            unfreezeAuthDetailData.set(UnfreezeAuthDetailConstants.Field.Name.apiName, sourceUnfreezeAuthDetailData.getName());

            unfreezeAuthDetailData.set(UnfreezeAuthDetailConstants.Field.UnfreezeObject.apiName, sourceUnfreezeAuthDetailData.get(UnfreezeAuthDetailConstants.Field.UnfreezeObject.apiName));
            unfreezeAuthDetailData.set(UnfreezeAuthDetailConstants.Field.RelatedField.apiName, sourceUnfreezeAuthDetailData.get(UnfreezeAuthDetailConstants.Field.RelatedField.apiName));
            unfreezeAuthDetailData.set(UnfreezeAuthDetailConstants.Field.UnfreezeActions.apiName, sourceUnfreezeAuthDetailData.get(UnfreezeAuthDetailConstants.Field.UnfreezeActions.apiName));
            unfreezeAuthDetailData.set(UnfreezeAuthDetailConstants.Field.Remark.apiName, sourceUnfreezeAuthDetailData.get(UnfreezeAuthDetailConstants.Field.Remark.apiName));
            unfreezeAuthDetailData.set(UnfreezeAuthDetailConstants.Field.FAccountAuthorizationId.apiName, targetAccountAuthDataId);
            unfreezeAuthDetailData.set("record_type", sourceUnfreezeAuthDetailData.getRecordType());
            newUnfreezeAuthDetails.add(unfreezeAuthDetailData);
        }
        if (CollectionUtils.empty(newUnfreezeAuthDetails)) {
            return;
        }

        try {
            serviceFacade.bulkSaveObjectData(newUnfreezeAuthDetails, admin);
        } catch (Exception e) {
            log.warn("copy serviceFacade.bulkSaveObjectData fail newUnfreezeAuthDetails[{}], admin[{}]", newUnfreezeAuthDetails, admin, e);
            throw e;
        }
    }
}