package com.facishare.crm.customeraccount.predefine;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeProxy;
import com.facishare.paas.appframework.privilege.dto.AuthContext;
import com.facishare.paas.appframework.privilege.dto.FuncPermiss;
import com.facishare.paas.appframework.privilege.dto.UpdateRoleModifiedFuncPrivilege;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Component
public class FunctionPrivilegeManager {
    @Autowired
    private FunctionPrivilegeProxy functionPrivilegeProxy;

    public void assignFunctionToRole(User user, String roleCode, List<String> functionCodes) {
        if (CollectionUtils.isEmpty(functionCodes)){
            return;
        }
        String tenantId = user.getTenantId();
        AuthContext authContext = AuthContext.builder().tenantId(tenantId).userId(user.getUserId()).build();
        FuncPermiss.RoleFuncPermissArg roleFuncPermissArg = new FuncPermiss.RoleFuncPermissArg(authContext, roleCode);
        Map<String, String> headers = FunctionPrivilegeProxy.HeaderUtil.buildHeaders(tenantId);
        FuncPermiss.Result funcPermissionResult = functionPrivilegeProxy.roleFuncPermiss(roleFuncPermissArg, headers);
        if (Objects.isNull(funcPermissionResult.getErrCode()) || funcPermissionResult.getErrCode() != 0) {
            log.warn("roleFuncPermission,user:{},roleCode:{},result:{}", user, roleCode, funcPermissionResult);
            return;
        }
        List<FuncPermiss.FunctionPojo> functionPojoList = funcPermissionResult.getResult();
        List<String> toAddFunctionCodes = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(functionPojoList)) {
            Set<String> existFunctionCodes = functionPojoList.stream().map(FuncPermiss.FunctionPojo::getFuncCode).collect(Collectors.toSet());
            functionCodes.forEach(x -> {
                if (!existFunctionCodes.contains(x)) {
                    toAddFunctionCodes.add(x);
                }
            });
        }
        if (toAddFunctionCodes.isEmpty()) {
            return;
        }
        UpdateRoleModifiedFuncPrivilege.Arg updateRoleFuncArg = new UpdateRoleModifiedFuncPrivilege.Arg(authContext, roleCode, toAddFunctionCodes, Lists.newArrayList(), null);
        UpdateRoleModifiedFuncPrivilege.Result updateRoleFuncResult = functionPrivilegeProxy.updateRoleModifiedFuncPrivilege(updateRoleFuncArg, headers);
        log.info("updateRoleModifiedFuncPrivilege result:{}", updateRoleFuncResult);
    }
}
