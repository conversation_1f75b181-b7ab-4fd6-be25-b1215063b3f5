package com.facishare.crm.customeraccount.predefine.domainplugin.rulecompute;

import com.facishare.crm.customeraccount.constants.AccountFrozenRecordConstant;
import com.facishare.crm.customeraccount.constants.AccountTransactionFlowConst;
import com.facishare.crm.customeraccount.constants.NewCustomerAccountConstants;
import com.facishare.crm.customeraccount.constants.UnfreezeDetailConstant;
import com.facishare.crm.customeraccount.model.AccountRuleUseRecordsModel;
import com.facishare.crm.customeraccount.predefine.domainplugin.model.AddRejectCheckRuleResult;
import com.facishare.crm.customeraccount.predefine.manager.AccountCheckManager;
import com.facishare.crm.customeraccount.util.CustomerAccountLogUtil;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Builder;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Builder
public class AddRejectCalculator {
    private RequestContext requestContext;
    private AccountCheckManager accountCheckManager;
    private IObjectData objectData;
    private List<IObjectData> accountRuleUseRecordDataList;

    public AddRejectCheckRuleResult compute() {
        User user = requestContext.getUser();
        String objectApiName = objectData.getDescribeApiName();
        String objectDataId = objectData.getId();

        CustomerAccountLogUtil.EditHandlerLog.builder().action("DataReject").objectApiName(objectApiName).objectDataId(objectDataId).build().log(user);
        //查询使用记录
        if (Objects.isNull(accountRuleUseRecordDataList)) {
            AccountRuleUseRecordsModel accountRuleUseRecordsModel = accountCheckManager.queryCheckRuleUseRecordMap(user, objectApiName, objectDataId);
            accountRuleUseRecordDataList = accountRuleUseRecordsModel.toList();
        }
        //校验扣减
        List<IObjectData> frozenList = accountCheckManager.queryByWhatField(user, AccountFrozenRecordConstant.API_NAME, objectApiName, objectDataId);
        List<IObjectData> toInvalidFrozenList = Lists.newArrayList();
        List<IObjectData> toInvalidUnfreezeList = Lists.newArrayList();
        List<IObjectData> toInvalidFlowList = Lists.newArrayList();
        Map<String, Map<String, Object>> customerAccountHandleMap = Maps.newHashMap();
        Map<String, IObjectData> frozenDataMap = Maps.newHashMap();
        frozenList.forEach(x -> {
            String customerAccountId = x.get(AccountFrozenRecordConstant.Field.CustomerAccountId.apiName, String.class);
            BigDecimal freezeAmount = x.get(AccountFrozenRecordConstant.Field.FreezeAmount.apiName, BigDecimal.class, BigDecimal.ZERO);
            Map<String, Object> columnMap = customerAccountHandleMap.computeIfAbsent(customerAccountId, k -> Maps.newHashMap());
            BigDecimal occupiedAmount = (BigDecimal) columnMap.computeIfAbsent(NewCustomerAccountConstants.Field.OccupiedAmount.apiName, k -> BigDecimal.ZERO);
            BigDecimal availableAmount = (BigDecimal) columnMap.computeIfAbsent(NewCustomerAccountConstants.Field.AvailableBalance.apiName, k -> BigDecimal.ZERO);
            columnMap.put(NewCustomerAccountConstants.Field.OccupiedAmount.apiName, occupiedAmount.subtract(freezeAmount));
            columnMap.put(NewCustomerAccountConstants.Field.AvailableBalance.apiName, availableAmount.add(freezeAmount));
            frozenDataMap.put(x.getId(), x);
            toInvalidFrozenList.add(x);
        });
        List<IObjectData> unfreezeDataList = accountCheckManager.queryUnfreezeDetailByRelate(user, AccountFrozenRecordConstant.API_NAME, Lists.newArrayList(frozenDataMap.keySet()));

        List<String> flowIds = Lists.newArrayList();
        unfreezeDataList.forEach(x -> {
            String flowId = x.get(UnfreezeDetailConstant.Field.AccountTransactionFlow.apiName, String.class);
            String frozenId = x.get(UnfreezeDetailConstant.Field.AccountFrozenRecordId.apiName, String.class);
            flowIds.add(flowId);
            IObjectData frozenData = frozenDataMap.get(frozenId);
            String customerAccountId = frozenData.get(AccountFrozenRecordConstant.Field.CustomerAccountId.apiName, String.class);
            BigDecimal unfreezeAmount = x.get(UnfreezeDetailConstant.Field.UnfreezeAmount.apiName, BigDecimal.class, BigDecimal.ZERO);
            Map<String, Object> columnMap = customerAccountHandleMap.computeIfAbsent(customerAccountId, k -> Maps.newHashMap());
            BigDecimal occupiedAmount = (BigDecimal) columnMap.computeIfAbsent(NewCustomerAccountConstants.Field.OccupiedAmount.apiName, k -> BigDecimal.ZERO);
            BigDecimal accountBalance = (BigDecimal) columnMap.computeIfAbsent(NewCustomerAccountConstants.Field.AccountBalance.apiName, k -> BigDecimal.ZERO);
            columnMap.put(NewCustomerAccountConstants.Field.OccupiedAmount.apiName, occupiedAmount.add(unfreezeAmount));
            columnMap.put(NewCustomerAccountConstants.Field.AccountBalance.apiName, accountBalance.add(unfreezeAmount));
            toInvalidUnfreezeList.add(x);

        });
        List<IObjectData> flowDataList = accountCheckManager.findObjectDataByIds(user, flowIds, AccountTransactionFlowConst.API_NAME);
        toInvalidFlowList.addAll(flowDataList);

        //直接扣减
        List<IObjectData> flowDataListOfDirect = accountCheckManager.queryByWhatField(user, AccountTransactionFlowConst.API_NAME, objectApiName, objectDataId);
        if (CollectionUtils.isNotEmpty(flowDataListOfDirect)) {
            List<String> flowIdsAll = flowDataListOfDirect.stream().map(IObjectData::getId).collect(Collectors.toList());
            List<IObjectData> unfreezeListOfCheck = accountCheckManager.queryUnfreezeDetailByRelate(user, AccountTransactionFlowConst.API_NAME, flowIdsAll);
            Set<String> flowIdsOfCheck = unfreezeListOfCheck.stream().map(x -> x.get(UnfreezeDetailConstant.Field.AccountTransactionFlow.apiName, String.class)).collect(Collectors.toSet());
            flowDataListOfDirect.removeIf(x -> flowIdsOfCheck.contains(x.getId()));
        }
        flowDataListOfDirect.forEach(x -> {
            String customerAccountId = x.get(AccountTransactionFlowConst.Field.CustomerAccount.apiName, String.class);
            Map<String, Object> columnMap = customerAccountHandleMap.computeIfAbsent(customerAccountId, k -> Maps.newHashMap());
            BigDecimal availableAmount = (BigDecimal) columnMap.computeIfAbsent(NewCustomerAccountConstants.Field.AvailableBalance.apiName, k -> BigDecimal.ZERO);
            BigDecimal accountBalance = (BigDecimal) columnMap.computeIfAbsent(NewCustomerAccountConstants.Field.AccountBalance.apiName, k -> BigDecimal.ZERO);
            BigDecimal expenseAmount = x.get(AccountTransactionFlowConst.Field.ExpenseAmount.apiName, BigDecimal.class, BigDecimal.ZERO);
            columnMap.put(NewCustomerAccountConstants.Field.AvailableBalance.apiName, availableAmount.add(expenseAmount));
            columnMap.put(NewCustomerAccountConstants.Field.AccountBalance.apiName, accountBalance.add(expenseAmount));
            toInvalidFlowList.add(x);
        });
        return AddRejectCheckRuleResult.builder().toDeleteFrozenDataList(toInvalidFrozenList).toDeleteFlowDataList(toInvalidFlowList).toDeleteUnfreezeDataList(toInvalidUnfreezeList)
                .toDeleteRuleUseRecordDataList(accountRuleUseRecordDataList).customerAccountUpdateColumnMap(customerAccountHandleMap).build();
    }
}
