package com.facishare.crm.customeraccount.predefine.manager;

import com.facishare.crm.customeraccount.predefine.service.dto.CustomerAccountType;
import com.facishare.crm.customeraccount.util.ConfigCenter;
import com.facishare.crmcommon.rest.CustomerAccountConfigProxy;
import com.facishare.crmcommon.rest.dto.CustomerAccountConfigModel;
import com.facishare.paas.appframework.core.model.User;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class CustomerAccountConfigManager {
    @Autowired
    private CustomerAccountConfigProxy configProxy;

    public void updateStatus(User user, CustomerAccountType.CustomerAccountEnableSwitchStatus stauts) {
        if (ConfigCenter.dedicatedCloud) {
            return;
        }
        CustomerAccountConfigModel.SaveArg saveArg = new CustomerAccountConfigModel.SaveArg();
        saveArg.setTenantId(user.getTenantId());
        saveArg.setUserId(user.getUserId());
        saveArg.setStatus(stauts.getValue());
        configProxy.save(saveArg);
    }

    public CustomerAccountType.CustomerAccountEnableSwitchStatus getStatus(String tenantId) {
        if (ConfigCenter.dedicatedCloud) {
            return CustomerAccountType.CustomerAccountEnableSwitchStatus.UNABLE;
        }
        CustomerAccountConfigModel.CustomerAccountConfig config = getConfigByTenantId(tenantId);
        if (config == null) {
            log.debug("CustomerAccountConfig ==null,for tenantId:{}", tenantId);
            return CustomerAccountType.CustomerAccountEnableSwitchStatus.UNABLE;
        }
        return CustomerAccountType.CustomerAccountEnableSwitchStatus.valueOf(config.getStatus());
    }

    public boolean isCustomerAccountEnable(String tenantId) {
        CustomerAccountType.CustomerAccountEnableSwitchStatus customerAccountEnableSwitchStatus = this.getStatus(tenantId);
        if (customerAccountEnableSwitchStatus.getValue() != CustomerAccountType.CustomerAccountEnableSwitchStatus.ENABLE.getValue()) {
            //除了enable 状态，其他都返回false<br>
            log.info("customer account switch status is not enable,for status:{}", customerAccountEnableSwitchStatus.getValue());
            return false;
        } else {
            return true;
        }
    }

    public CustomerAccountConfigModel.CustomerAccountConfig getConfigByTenantId(String tenantId) {
        if (ConfigCenter.dedicatedCloud) {
            return null;
        }
        CustomerAccountConfigModel.DetailArg arg = CustomerAccountConfigModel.DetailArg.builder().tenantId(tenantId).build();
        CustomerAccountConfigModel.DetailResult detailResult = configProxy.get(arg);
        if (detailResult.isSuccess()) {
            return detailResult.getValue();
        }
        return null;
    }

    private List<String> list(int customerAccountEnable, List<String> tenantIds, int offset, int limit) {
        if (ConfigCenter.dedicatedCloud) {
            return Lists.newArrayList();
        }
        CustomerAccountConfigModel.ListArg listArg = CustomerAccountConfigModel.ListArg.builder()
                                                                                   .status(customerAccountEnable)
                                                                                   .tenantIds(tenantIds)
                                                                                   .offset(offset)
                                                                                   .limit(limit)
                                                                                   .build();
        CustomerAccountConfigModel.ListResult listResult = configProxy.list(listArg);
        if (listResult.isSuccess()) {
            return listResult.getValue().getTenantIds();
        }
        return Lists.newArrayList();
    }

    public List<String> list(int customerAccountEnable, int offset, int limit) {
        return list(customerAccountEnable, null, offset, limit);
    }

    public List<String> list(int customerAccountEnable, List<String> tenantIds) {
        List<String> totalTenantIds = Lists.newArrayList();
        int offset = 0;
        int limit = 500;
        for (;;) {
            List<String> actualTenantIds = list(customerAccountEnable, tenantIds, offset, limit);
            totalTenantIds.addAll(actualTenantIds);
            if (actualTenantIds.size() < 500) {
                break;
            } else {
                offset += 500;
            }
        }
        return totalTenantIds;
    }

    public List<String> list(int customerAccountEnable) {
        return list(customerAccountEnable, null);
    }

}
