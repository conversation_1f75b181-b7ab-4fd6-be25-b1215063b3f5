package com.facishare.crm.customeraccount.predefine.service;

import com.facishare.crm.customeraccount.predefine.service.dto.*;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;

/**
 * 账户授权
 */
@ServiceModule("account_auth")
public interface AccountAuthService {

    /**
     * 账户授权
     */
    @ServiceMethod("enable_account_auth")
    AccountAuthType.EnableAccountAuthResult enableAccountAuth(ServiceContext serviceContext);

    /**
     * 查询授权账户
     * sfa使用
     */
    @ServiceMethod("get_authorization_detail")
    GetAuthorizationDetailModel.Result getAuthorizationDetail(ServiceContext serviceContext, GetAuthorizationDetailModel.Arg arg);

    /**
     * 拉取账户列表
     * 场景：新建编辑账户校验规则
     */
    @ServiceMethod("get_accounts_for_account_check")
    GetAccountsForAccountCheckModel.Result getAccountsForAccountCheck(ServiceContext serviceContext, GetAccountsForAccountCheckModel.Arg arg);

    /**
     * 查询支出授权账户信息
     */
    @ServiceMethod("get_outcome_auth_accounts")
    GetOutcomeAuthAccountsModel.Result getOutcomeAuthAccounts(ServiceContext serviceContext, GetOutcomeAuthAccountsModel.Arg arg);

    /**
     * 支出授权明细是否可删除
     */
    @ServiceMethod("outcome_auth_detail_can_delete")
    OutcomeAuthDetailCanDeleteModel.Result outcomeAuthDetailCanDelete(ServiceContext serviceContext, OutcomeAuthDetailCanDeleteModel.Arg arg);

    /**
     * 获取不能删除的支出授权明细
     */
    @ServiceMethod("get_can_not_delete_auth_details")
    GetCanNotDeleteAuthDetailsModel.Result getCanNotDeleteAuthDetails(ServiceContext serviceContext, GetCanNotDeleteAuthDetailsModel.Arg arg);

    /*
     * 【授权明细】是否能删除
     * 支出：
     *    被哪些【校验规则】使用了
     *
     * 收入
     *    【入账规则】是否使用了
     */
    @ServiceMethod("check_delete_authorization_detail")
    CheckDeleteAuthorizationDetailModel.Result checkDeleteAuthorizationDetail(ServiceContext serviceContext, CheckDeleteAuthorizationDetailModel.Arg arg);

    /*
     * 【解冻授权明细】是否能删除
     *    被哪些【校验规则】使用了
     */
    @ServiceMethod("check_delete_unfreeze_auth_detail")
    CheckDeleteUnfreezeAuthDetailModel.Result checkDeleteUnfreezeAuthDetail(ServiceContext serviceContext, CheckDeleteUnfreezeAuthDetailModel.Arg arg);

    /**
     * 给授权明细加上‘现金账户’
     */
    @ServiceMethod("detail_add_cash_account")
    DetailAddCashAccountModel.Result detailAddCashAccount(ServiceContext serviceContext, DetailAddCashAccountModel.Arg arg);

    /**
     * 入账授权增加授权明细
     */
    @ServiceMethod("add_income_authorization_details")
    AddIncomeAuthDetailModel.Result addIncomeAuthDetail(ServiceContext serviceContext, AddIncomeAuthDetailModel.Arg arg);

    @ServiceMethod("get_objects")
    GetAuthObjectsModel.Result getAuthObjects(ServiceContext serviceContext, GetAuthObjectsModel.Arg arg);

    /**
     * 新建/编辑【解冻授权明细】，能选择的对象apiNames和动作
     */
    @ServiceMethod("get_unfreeze_auth_detail_info")
    GetUnfreezeAuthDetailInfo.Result getUnfreezeAuthDetailInfo(ServiceContext serviceContext, GetUnfreezeAuthDetailInfo.Arg arg);

    /**
     * 蒙牛云同步，通过DDS同步数据之后调用
     * 对【未初始化】的【入账授权】，执行初始化
     * 对【未初始化】的【支出授权】，不执行初始化（因为初始化会创建【组件扣减】的校验规则，而蒙牛云的【组件扣减】的【校验规则】，需要通过DDS同步过来
     */
    @ServiceMethod("init_after_dds")
    InitAfterDdsModel.Result initAfterDds(ServiceContext serviceContext, InitAfterDdsModel.Arg arg);

    /**
     * 账户授权是否能使用返利类型的账户
     */
    @ServiceMethod("can_use_rebate_fund_account")
    CanUseRebateFundAccountModel.Result canUseRebateFundAccount(ServiceContext serviceContext, CanUseRebateFundAccountModel.Arg arg);

    /**
     * 账户授权, 添加能使用返利类型账户的对象
     *
     * 手动调用，给客户添加灰度对象
     */
    @ServiceMethod("add_can_use_rebate_fund_account")
    AddCanUseRebateFundAccountModel.Result addCanUseRebateFundAccount(ServiceContext serviceContext, AddCanUseRebateFundAccountModel.Arg arg);

    /**
     * 账户授权, 查询能使用返利类型账户的对象
     *
     * 后台查询使用，前端没用到
     */
    @ServiceMethod("get_can_use_rebate_fund_account_objects")
    GetCanUseRebateFundAccountObjectsModel.Result getCanUseRebateFundAccountObjects(ServiceContext serviceContext, GetCanUseRebateFundAccountObjectsModel.Arg arg);
}