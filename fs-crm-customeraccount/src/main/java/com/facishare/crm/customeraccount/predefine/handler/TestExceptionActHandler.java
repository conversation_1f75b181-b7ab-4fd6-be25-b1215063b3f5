package com.facishare.crm.customeraccount.predefine.handler;

import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.fxiaoke.api.IdGenerator;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@ServiceModule("wf_test_handler")
public class TestExceptionActHandler {

    @ServiceMethod("handle")
    public Map<String, Object> handle(ServiceContext serviceContext, Map<String, Object> params) {
        throw new ValidateException("wf test act exception");
    }

    public static void main(String[] args) {
        System.out.println(IdGenerator.get()+","+System.currentTimeMillis());
    }
}
