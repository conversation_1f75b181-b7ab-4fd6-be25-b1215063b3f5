package com.facishare.crm.customeraccount.predefine.domainplugin.rulecompute;

import com.facishare.crm.customeraccount.constants.AccountTransactionFlowConst;
import com.facishare.crm.customeraccount.constants.NewCustomerAccountConstants;
import com.facishare.crm.customeraccount.constants.UnfreezeDetailConstant;
import com.facishare.crm.customeraccount.predefine.domainplugin.model.UnfreezeRollbackResult;
import com.facishare.crm.customeraccount.predefine.manager.AccountCheckManager;
import com.facishare.crm.customeraccount.util.ObjectDataUtil;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Builder;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Builder
public class UnfreezeRollbackCalculator {
    private AccountCheckManager accountCheckManager;
    private RequestContext requestContext;
    private String objectApiName;
    private String objectDataId;
    //同一条数据的解冻记录
    private List<IObjectData> unfreezeRuleUseRecordDataList;

    public UnfreezeRollbackResult compute() {
        User user = requestContext.getUser();
        List<IObjectData> unfreezeDataList = accountCheckManager.queryUnfreezeDetailByRelate(user, objectApiName, Lists.newArrayList(objectDataId));
        List<String> flowIds = unfreezeDataList.stream().map(x -> x.get(UnfreezeDetailConstant.Field.AccountTransactionFlow.apiName, String.class)).collect(Collectors.toList());
        List<IObjectData> flowDataList = accountCheckManager.findObjectDataByIds(user, flowIds, AccountTransactionFlowConst.API_NAME);
        Map<String, Map<String, Object>> customerAccountColumnMap = Maps.newHashMap();
        for (IObjectData flowData : flowDataList) {
            BigDecimal unfreezeAmount = flowData.get(AccountTransactionFlowConst.Field.ExpenseAmount.apiName, BigDecimal.class, BigDecimal.ZERO);
            String customerAccountId = flowData.get(AccountTransactionFlowConst.Field.CustomerAccount.apiName, String.class);
            ObjectDataUtil.incrementUpdateCustomerAccountUpdateField(customerAccountColumnMap, customerAccountId, NewCustomerAccountConstants.Field.AccountBalance.apiName, unfreezeAmount);
            ObjectDataUtil.incrementUpdateCustomerAccountUpdateField(customerAccountColumnMap, customerAccountId, NewCustomerAccountConstants.Field.OccupiedAmount.apiName, unfreezeAmount);
        }
        return UnfreezeRollbackResult.builder().toDeleteRuleUseRecordDataList(unfreezeRuleUseRecordDataList).toDeleteUnfreezeDataList(unfreezeDataList).toDeleteFlowDataList(flowDataList).customerAccountUpdateFieldMap(customerAccountColumnMap).build();
    }
}
