package com.facishare.crm.customeraccount.predefine.domainplugin.processor.credit;

import com.facishare.crm.consts.CreditFlowDetailConst;
import com.facishare.crm.consts.CreditRuleDetailConst;
import com.facishare.crm.customeraccount.constants.AccountTransactionFlowConst;
import com.facishare.crm.customeraccount.constants.CAI18NKey;
import com.facishare.crm.customeraccount.enums.CheckStrengthEnum;
import com.facishare.crm.customeraccount.predefine.domainplugin.model.CreditFlowDetailModel;
import com.facishare.crm.customeraccount.predefine.domainplugin.model.CreditRuleAddContextModel;
import com.facishare.crm.customeraccount.predefine.domainplugin.model.ObjectDataCreditRuleInfo;
import com.facishare.crm.customeraccount.predefine.manager.CreditManager;
import com.facishare.crm.customeraccount.predefine.manager.NewCustomerAccountManager;
import com.facishare.crm.customeraccount.predefine.service.dto.CreditCurAndPreObjectConfig;
import com.facishare.crm.customeraccount.util.CreditUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.domain.DomainPlugin;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public abstract class CreditRuleByDetailProcessor<A extends DomainPlugin.Arg, R extends DomainPlugin.Result, C> extends CreditRuleProcessor<A, R, C> {

    protected CreditRuleByDetailProcessor(NewCustomerAccountManager newCustomerAccountManager, CreditManager creditManager, ServiceFacade serviceFacade) {
        super(newCustomerAccountManager, creditManager, serviceFacade);
    }

    @Override
    public CreditRuleAddContextModel firstNodeMatchRule(RequestContext requestContext, IObjectData objectData, Map<String, List<ObjectDataDocument>> details, ObjectDataCreditRuleInfo objectDataCreditRuleInfo, boolean skipValidateCredit) {
        CreditRuleFirstNodeMatchResult creditRuleFirstNodeMatchResult;
        User user = requestContext.getUser();
        CreditCurAndPreObjectConfig creditCurAndPreConfig = objectDataCreditRuleInfo.getCreditCurAndPreObjectConfig();

        String customerFieldApiName = creditCurAndPreConfig.getCustomerFieldApiName();
        String customerId = objectData.get(customerFieldApiName, String.class);

        IObjectData creditCustomerAccountData = newCustomerAccountManager.findCreditCustomerAccountData(requestContext, customerId);
        String occupiedAmountField = objectDataCreditRuleInfo.getOccupiedAmountField();
        List<IObjectData> curCreditFlowDetailList = Lists.newArrayList();

        List<ObjectDataDocument> detailDataList = details.getOrDefault(creditCurAndPreConfig.getDetailObjectApiName(), Lists.newArrayList());
        for (ObjectDataDocument detailDataDocument : detailDataList) {
            IObjectData detailData = detailDataDocument.toObjectData();
            BigDecimal detailCreditAmount = detailData.get(occupiedAmountField, BigDecimal.class, BigDecimal.ZERO);
            if (detailCreditAmount.compareTo(BigDecimal.ZERO) < 0) {
                log.warn("detailCreditAmount:{}", detailCreditAmount);
                throw new ValidateException(I18N.text(CAI18NKey.AMOUNT_CAN_NOT_LESS_THAN_ZERO, I18N.text(CAI18NKey.CREDIT_AMOUNT)));
            }
            if (detailCreditAmount.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            curCreditFlowDetailList.add(CreditUtil.generateCreditFlowDetailData(user, objectData, detailData, creditCustomerAccountData, detailCreditAmount, objectDataCreditRuleInfo.getCreditOccupiedRuleId()));
        }
        creditRuleFirstNodeMatchResult = CreditRuleFirstNodeMatchResult.ofByFirstNodeDetailMode(user, objectData, creditCustomerAccountData, curCreditFlowDetailList, objectDataCreditRuleInfo.getCurCreditOccupiedRuleDetailData());
        IObjectData creditRuleDetailData = creditRuleFirstNodeMatchResult.getMatchedCreditRuleDetailData();
        String checkStrength = creditRuleDetailData.get(CreditRuleDetailConst.F.CheckStrength.apiName, String.class);
        newCustomerAccountManager.executeCredit(user, creditRuleFirstNodeMatchResult.to(skipValidateCredit), CheckStrengthEnum.of(checkStrength).orElse(null));

        return creditRuleFirstNodeMatchResult.toContextModel(objectData);
    }

    @Override
    public CreditRuleAddContextModel notFirstNodeMatchRule(RequestContext requestContext, IObjectData objectData, Map<String, List<ObjectDataDocument>> details, ObjectDataCreditRuleInfo objectDataCreditRuleInfo) {
        String tenantId = requestContext.getTenantId();
        User user = requestContext.getUser();
        String preCreditObject = objectDataCreditRuleInfo.getPreCreditObjectApiName();

        CreditCurAndPreObjectConfig creditCurAndPreConfig = objectDataCreditRuleInfo.getCreditCurAndPreObjectConfig();
        String lookUpPreFieldName = creditCurAndPreConfig.getLookUpPreObjectFieldApiName();
        String customerFieldApiName = creditCurAndPreConfig.getCustomerFieldApiName();
        String customerId = objectData.get(customerFieldApiName, String.class);
        CreditRuleNotFirstNodeMatchResult creditRuleMatchResult;
        List<IObjectData> preRuleMatchRecordList;

        String occupiedAmountField = objectDataCreditRuleInfo.getOccupiedAmountField();
        String creditOccupiedRuleId = objectDataCreditRuleInfo.getCreditOccupiedRuleId();
        Set<String> preNodeMasterDataIds = Sets.newHashSet();
        List<ObjectDataDocument> detaildataList = details.getOrDefault(creditCurAndPreConfig.getDetailObjectApiName(), Lists.newArrayList());
        Set<String> lookUpPreObjectDataIds = Sets.newHashSet();
        HashBasedTable<String, String, IObjectData> preIdCurIdCurDetailDataMap = HashBasedTable.create();

        for (ObjectDataDocument detailDataDocument : detaildataList) {
            IObjectData detailData = ObjectDataExt.of(detailDataDocument);
            String lookUpPreDataId = detailData.get(lookUpPreFieldName, String.class);
            if (StringUtils.isEmpty(lookUpPreDataId)) {
                continue;
            }
            lookUpPreObjectDataIds.add(lookUpPreDataId);
            preIdCurIdCurDetailDataMap.put(lookUpPreDataId, detailData.getId(), detailData);
        }
        List<IObjectData> preNodeDetailDataList = serviceFacade.findObjectDataByIds(tenantId, Lists.newArrayList(lookUpPreObjectDataIds), creditCurAndPreConfig.getPreDetailObjectApiName());
        String lookUpPreObjectMasterFieldApiName = creditCurAndPreConfig.getPreDetailLookUpMasterFieldApiName();
        preNodeMasterDataIds.addAll(preNodeDetailDataList.stream().map(x -> x.get(lookUpPreObjectMasterFieldApiName, String.class)).filter(StringUtils::isNotEmpty).collect(Collectors.toList()));
        //查找上一节点是否有匹配记录
        preRuleMatchRecordList = creditManager.findCreditRuleMatchRecordByData(user, preCreditObject, preNodeMasterDataIds);
        if (CollectionUtils.empty(preRuleMatchRecordList)) {
            return new CreditRuleAddContextModel(objectData.getDescribeApiName(), objectData.getId());
        }
        String preMatchedCreditRuleId = CreditUtil.checkPreNodeCreditRuleMatchRecordSameRule(preRuleMatchRecordList);
        if (!StringUtils.equals(preMatchedCreditRuleId, creditOccupiedRuleId)) {
            //存在多个信用占用规则时，若前一个节点匹配的不是当前规则，则不处理；与当前规则一致时再处理
            return new CreditRuleAddContextModel(objectData.getDescribeApiName(), objectData.getId());
        }
        //有匹配记录，查找上一个节点的流水
        List<IObjectData> preObjectCreditFlowDetailList = creditManager.findCreditFlowDetailByRelateDetail(user, creditCurAndPreConfig.getPreDetailObjectApiName(), lookUpPreObjectDataIds, creditOccupiedRuleId);
        Map<String, IObjectData> preIdCreditFlowDetailDataMap = preObjectCreditFlowDetailList.stream().collect(Collectors.toMap(x -> x.get(CreditFlowDetailConst.F.RelateObjectDataId.apiName, String.class), Function.identity()));

        List<CreditFlowDetailModel> creditFlowDetailModelList = Lists.newArrayList();
        Map<String, BigDecimal> preNodeDataIdTransferAmountMap = Maps.newHashMap();
        Map<String, BigDecimal> preCreditFlowDetailChangedLeftCreditAmountMap = Maps.newHashMap();
        preIdCurIdCurDetailDataMap.rowMap().forEach((preId, curIdDetailData) -> {
            IObjectData preCreditFlowDetailData = preIdCreditFlowDetailDataMap.get(preId);
            BigDecimal curAmount = BigDecimal.ZERO;
            for (Map.Entry<String, IObjectData> detailEntry : curIdDetailData.entrySet()) {
                IObjectData detailData = detailEntry.getValue();
                BigDecimal creditAmount = detailData.get(occupiedAmountField, BigDecimal.class, BigDecimal.ZERO);
                if (creditAmount.compareTo(BigDecimal.ZERO) < 0) {
                    log.warn("curDetailCreditAmount:{}", creditAmount);
                    throw new ValidateException(I18N.text(CAI18NKey.AMOUNT_CAN_NOT_LESS_THAN_ZERO, I18N.text(CAI18NKey.CREDIT_AMOUNT)));
                }
                curAmount = curAmount.add(creditAmount);
                if (creditAmount.compareTo(BigDecimal.ZERO) != 0) {
                    creditFlowDetailModelList.add(CreditFlowDetailModel.build(objectData, detailData, occupiedAmountField, preCreditFlowDetailData, creditOccupiedRuleId, null));
                }
            }
            //前面已经校验过不小于0
            if (Objects.isNull(preCreditFlowDetailData) && curAmount.compareTo(BigDecimal.ZERO) > 0) {
                //curAmount是>0的
                log.warn("preNode credit flow is null,curAmount:{}", curAmount);
                throw new ValidateException(I18N.text(CAI18NKey.CREDIT_AMOUNT_NOT_GT_PRE_NODE));
            }
            BigDecimal preLeftCreditAmount = preCreditFlowDetailData.get(CreditFlowDetailConst.F.LeftCreditAmount.apiName, BigDecimal.class);
            if (curAmount.compareTo(preLeftCreditAmount) > 0) {
                log.warn("curAmount:{},preLeftCreditAmount:{}", curAmount, preLeftCreditAmount);
                throw new ValidateException(I18N.text(CAI18NKey.CREDIT_AMOUNT_NOT_GT_PRE_NODE));
            }
            if (curAmount.compareTo(BigDecimal.ZERO) != 0) {
                String preNodeMasterDataId = preCreditFlowDetailData.get(CreditFlowDetailConst.F.RelateMasterDataId.apiName, String.class);
                BigDecimal preNodeDataIdTransferAmount = preNodeDataIdTransferAmountMap.computeIfAbsent(preNodeMasterDataId, k -> BigDecimal.ZERO);
                preNodeDataIdTransferAmountMap.put(preNodeMasterDataId, preNodeDataIdTransferAmount.add(curAmount));
                preCreditFlowDetailChangedLeftCreditAmountMap.put(preCreditFlowDetailData.getId(), curAmount.negate());
            }
        });

        Map<String, BigDecimal> preNodeTransactionFlowToNewExpenseAmountMap = Maps.newHashMap();
        if (!preNodeDataIdTransferAmountMap.isEmpty()) {
            List<IObjectData> preNodeTransactionFlowDataList = creditManager.findCreditTransactionExpenseFlowByRelateObject(user, preCreditObject, preNodeMasterDataIds);
            Map<String, IObjectData> preNodeDataIdTransactionFlowDataMap = preNodeTransactionFlowDataList.stream().collect(Collectors.toMap(
                    x -> x.get(AccountTransactionFlowConst.Field.RelateRecordObjectDataId.apiName, String.class), Function.identity()));
            preNodeDataIdTransferAmountMap.forEach((preNodeMasterDataId, transferAmount) -> {
                IObjectData preNodeTransactionFlowData = preNodeDataIdTransactionFlowDataMap.get(preNodeMasterDataId);
                BigDecimal expenseAmount = preNodeTransactionFlowData.get(AccountTransactionFlowConst.Field.ExpenseAmount.apiName, BigDecimal.class);
                preNodeTransactionFlowToNewExpenseAmountMap.put(preNodeTransactionFlowData.getId(), expenseAmount.subtract(transferAmount));
            });
        }
        IObjectData creditCustomerAccountData = newCustomerAccountManager.findCreditCustomerAccountData(requestContext, customerId);
        creditRuleMatchResult = CreditRuleNotFirstNodeMatchResult.ofByDetail(preCreditObject, preRuleMatchRecordList, creditCustomerAccountData,
                objectDataCreditRuleInfo.getCurCreditOccupiedRuleDetailData(), preCreditFlowDetailChangedLeftCreditAmountMap, preNodeTransactionFlowToNewExpenseAmountMap,
                creditFlowDetailModelList);
        return creditRuleMatchResult.to(objectData);
    }

}
