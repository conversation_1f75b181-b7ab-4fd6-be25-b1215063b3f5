package com.facishare.crm.customeraccount.predefine.manager;

import com.facishare.crm.customeraccount.constants.AccountTransactionFlowConst;
import com.facishare.crm.customeraccount.constants.SystemConstants;
import com.facishare.crm.customeraccount.enums.EntryStatusEnum;
import com.facishare.crm.customeraccount.enums.ExpenseTypeEnum;
import com.facishare.crm.customeraccount.enums.FundAccountSwitchEnum;
import com.facishare.crm.customeraccount.model.CustomerAccountPluginModel;
import com.facishare.crm.customeraccount.predefine.handler.RuleHandlerUtil;
import com.facishare.crm.customeraccount.util.AccountCheckRuleMappingUtil;
import com.facishare.crm.openapi.Utils;
import com.facishare.crmcommon.manager.CommonDescribeManager;
import com.facishare.crmcommon.util.ObjectDataUtil;
import com.facishare.crmcommon.util.SearchUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import de.lab4inf.math.util.Strings;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class TransactionFlowDataManager extends CommonManager {

    @Autowired
    private FundAccountManager fundAccountManager;
    @Autowired
    private CommonDescribeManager commonDescribeManager;

    /**
     * @param setSourceObjectDataId 在sourceObjectDataId保存之前，数据库里面还没有这个数据
     */
    public List<IObjectData> getTransactionFlowDataList(User user, Map<String, String> newFundAccountId2customerAccountId, String sourceObjectDescribeApiName, String sourceObjectDataId, boolean setSourceObjectDataId,
                                                        String customerId, Map<String, BigDecimal> fundAccountId2ExpenseAmount, String expenseType,
                                                        IObjectData componentReduceAccountCheckRuleData, IObjectData sourceObjectData) {
        List<IObjectData> transactionFlowDataList = Lists.newArrayList();

        Map<String, String> fundAccountId2AccessModule = fundAccountManager.getFundAccountId2AccessModule(user.getTenantId(), Lists.newArrayList(fundAccountId2ExpenseAmount.keySet()));
        log.info("getTransactionFlowDataList, tenantId[{}], fundAccountIds[{}], fundAccountId2AccessModule[{}]", user.getTenantId(), fundAccountId2ExpenseAmount.keySet(), fundAccountId2AccessModule);

        Map<String, Long> fundAccountId2transactionDate = AccountCheckRuleMappingUtil.getFundAccountId2transactionDate(componentReduceAccountCheckRuleData, sourceObjectData);

        for (String fundAccountId : fundAccountId2ExpenseAmount.keySet()) {
            BigDecimal expenseAmount = fundAccountId2ExpenseAmount.get(fundAccountId);

            if (expenseAmount.compareTo(BigDecimal.ZERO) == 0) {
                //支出金额 = 0, 不用创建流水
                continue;
            }

            //支出流水
            IObjectData transactionFlowData = ObjectDataUtil.getBaseObjectData(user, AccountTransactionFlowConst.API_NAME);
            if (StringUtils.isEmpty(transactionFlowData.getId())) {
                transactionFlowData.setId(serviceFacade.generateId());
            }

            transactionFlowData.setRecordType(AccountTransactionFlowConst.RecordType.OutcomeRecordType.apiName);
            transactionFlowData.set(AccountTransactionFlowConst.Field.RelateRecordObjectApiName.apiName, sourceObjectDescribeApiName);  
            transactionFlowData.set(AccountTransactionFlowConst.Field.RelateRecordObjectDataId.apiName, sourceObjectDataId);  //what字段

            if (setSourceObjectDataId) {
                if (Utils.SALES_ORDER_API_NAME.equals(sourceObjectDescribeApiName)) {
                    transactionFlowData.set(AccountTransactionFlowConst.Field.SalesOrder.apiName, sourceObjectDataId);  //查找关联会校验
                } else if (Utils.CUSTOMER_PAYMENT_API_NAME.equals(sourceObjectDescribeApiName)) {
                    transactionFlowData.set(AccountTransactionFlowConst.Field.Payment.apiName, sourceObjectDataId);     //查找关联会校验
                }
            }

            transactionFlowData.set(AccountTransactionFlowConst.Field.ExpenseType.apiName, expenseType);
            transactionFlowData.set(AccountTransactionFlowConst.Field.EntryStatus.apiName, EntryStatusEnum.AlreadyEntry.getValue());
            transactionFlowData.set(AccountTransactionFlowConst.Field.FundAccount.apiName, fundAccountId);
            transactionFlowData.set(AccountTransactionFlowConst.Field.Customer.apiName, customerId);
            transactionFlowData.set(AccountTransactionFlowConst.Field.CustomerAccount.apiName, newFundAccountId2customerAccountId.get(fundAccountId));
            transactionFlowData.set(AccountTransactionFlowConst.Field.ExpenseAmount.apiName, expenseAmount);
            if (fundAccountId2transactionDate.containsKey(fundAccountId)) {
                transactionFlowData.set(AccountTransactionFlowConst.Field.TransactionDate.apiName, fundAccountId2transactionDate.get(fundAccountId));
            }
            transactionFlowData.set(AccountTransactionFlowConst.Field.AccessModule.apiName, fundAccountId2AccessModule.get(fundAccountId));

            transactionFlowDataList.add(transactionFlowData);
        }
        return transactionFlowDataList;
    }

    public List<IObjectData> getTransactionFlowDatasByExpenseType(String tenantId, String expenseType) {
        User admin = new User(tenantId, "-10000");

        String objectApiName = AccountTransactionFlowConst.API_NAME;

        List<IFilter> filters = new ArrayList<>();
        SearchUtil.fillFilterIn(filters, AccountTransactionFlowConst.Field.ExpenseType.apiName, Lists.newArrayList(expenseType));
        SearchUtil.fillFilterEq(filters, ObjectData.IS_DELETED, Lists.newArrayList("0"));

        return ObjectDataUtil.getObjectDatasWithDeleted(admin, objectApiName, filters);
    }

    public List<IObjectData> getTransactionFlowDataList(String tenantId, String sourceObjectDataId, String customerId, Map<String, BigDecimal> fundAccountId2ExpenseAmount, List<String> expenseTypes) {
        User user = new User(tenantId, "-10000");
        if (fundAccountId2ExpenseAmount.isEmpty()) {
            return Lists.newArrayList();
        }

        List<IFilter> filterList = Lists.newArrayList();
        SearchUtil.fillFilterEq(filterList, AccountTransactionFlowConst.Field.Customer.apiName, customerId);
        SearchUtil.fillFilterIn(filterList, AccountTransactionFlowConst.Field.FundAccount.apiName, Lists.newArrayList(fundAccountId2ExpenseAmount.keySet()));
        SearchUtil.fillFilterEq(filterList, AccountTransactionFlowConst.Field.RelateRecordObjectDataId.apiName, sourceObjectDataId);
        SearchUtil.fillFilterIn(filterList, AccountTransactionFlowConst.Field.ExpenseAmount.apiName, Lists.newArrayList(fundAccountId2ExpenseAmount.values()));
        SearchUtil.fillFilterIn(filterList, AccountTransactionFlowConst.Field.ExpenseType.apiName, expenseTypes);

        QueryResult<IObjectData> queryResult = searchQuery(user, AccountTransactionFlowConst.API_NAME, filterList, Lists.newArrayList(), 0, fundAccountId2ExpenseAmount.keySet().size());
        log.info("getTransactionFlowDataList user[{}], objectApiName[{}], filters[{}], orders[{}], offset[{}], limit[{}], result[{}]",
                user, AccountTransactionFlowConst.API_NAME, filterList, Lists.newArrayList(), 0, fundAccountId2ExpenseAmount.keySet().size(), queryResult);
        return queryResult.getData();
    }

    public List<IObjectData> getTransactionFlowDataList(String tenantId, String sourceObjectDataId, List<String> fundAccountIds, List<String> expenseTypes) {
        User user = new User(tenantId, "-10000");

        List<IFilter> filterList = Lists.newArrayList();
        if (!CollectionUtils.empty(fundAccountIds)) {
            SearchUtil.fillFilterIn(filterList, AccountTransactionFlowConst.Field.FundAccount.apiName, fundAccountIds);
        }
        SearchUtil.fillFilterEq(filterList, AccountTransactionFlowConst.Field.RelateRecordObjectDataId.apiName, sourceObjectDataId);
        SearchUtil.fillFilterIn(filterList, AccountTransactionFlowConst.Field.ExpenseType.apiName, expenseTypes);

        QueryResult<IObjectData> queryResult = searchQueryByDbIgnoreAll(user, AccountTransactionFlowConst.API_NAME, filterList, Lists.newArrayList(), 0, 1000);
        log.info("getTransactionFlowDataList user[{}], objectApiName[{}], filters[{}], orders[{}], result[{}]",
                user, AccountTransactionFlowConst.API_NAME, filterList, Lists.newArrayList(), queryResult);
        return queryResult.getData();
    }

    public List<IObjectData> getTransactionFlowDataList(String tenantId, String sourceObjectDataId, String customerId, List<String> fundAccountIds, List<String> expenseTypes) {
        User user = new User(tenantId, "-10000");

        List<IFilter> filterList = Lists.newArrayList();
        SearchUtil.fillFilterEq(filterList, AccountTransactionFlowConst.Field.Customer.apiName, customerId);
        if (!CollectionUtils.empty(fundAccountIds)) {
            SearchUtil.fillFilterIn(filterList, AccountTransactionFlowConst.Field.FundAccount.apiName, fundAccountIds);
        }
        SearchUtil.fillFilterEq(filterList, AccountTransactionFlowConst.Field.RelateRecordObjectDataId.apiName, sourceObjectDataId);
        SearchUtil.fillFilterIn(filterList, AccountTransactionFlowConst.Field.ExpenseType.apiName, expenseTypes);

        QueryResult<IObjectData> queryResult = searchQueryByDbIgnoreAll(user, AccountTransactionFlowConst.API_NAME, filterList, Lists.newArrayList(), 0, 1000);
        log.info("getTransactionFlowDataList user[{}], objectApiName[{}], filters[{}], orders[{}], result[{}]",
                user, AccountTransactionFlowConst.API_NAME, filterList, Lists.newArrayList(), queryResult);
        return queryResult.getData();
    }

    public List<IObjectData> getTransactionFlowDataList(String tenantId, String sourceObjectDataId, String noCustomerId, List<String> expenseTypes) {
        User user = new User(tenantId, "-10000");

        List<IFilter> filterList = Lists.newArrayList();
        SearchUtil.fillFilterNotEq(filterList, AccountTransactionFlowConst.Field.Customer.apiName, noCustomerId);

        SearchUtil.fillFilterEq(filterList, AccountTransactionFlowConst.Field.RelateRecordObjectDataId.apiName, sourceObjectDataId);
        SearchUtil.fillFilterIn(filterList, AccountTransactionFlowConst.Field.ExpenseType.apiName, expenseTypes);

        QueryResult<IObjectData> queryResult = searchQueryByDbIgnoreAll(user, AccountTransactionFlowConst.API_NAME, filterList, Lists.newArrayList(), 0, 1000);
        log.info("getTransactionFlowDataList user[{}], objectApiName[{}], filters[{}], orders[{}], offset[{}], result[{}]",
                user, AccountTransactionFlowConst.API_NAME, filterList, Lists.newArrayList(), 0, queryResult);
        return queryResult.getData();
    }

    public List<IObjectData> getTransactionFlowDataList(String tenantId, String sourceObjectDataId, List<String> expenseTypes) {
        User user = new User(tenantId, "-10000");

        List<IFilter> filterList = Lists.newArrayList();
    //    SearchUtil.fillFilterEq(filterList, AccountTransactionFlowConst.Field.Customer.apiName, customerId);
        SearchUtil.fillFilterEq(filterList, AccountTransactionFlowConst.Field.RelateRecordObjectDataId.apiName, sourceObjectDataId);
        SearchUtil.fillFilterIn(filterList, AccountTransactionFlowConst.Field.ExpenseType.apiName, expenseTypes);

        QueryResult<IObjectData> queryResult = searchQueryByDbIgnoreAll(user, AccountTransactionFlowConst.API_NAME, filterList, Lists.newArrayList(), 0, 200);
        log.info("getTransactionFlowDataList user[{}], objectApiName[{}], filters[{}], orders[{}], result[{}]",
                user, AccountTransactionFlowConst.API_NAME, filterList, Lists.newArrayList(), queryResult);
        return queryResult.getData();
    }

    /**
     * 是否有【入账状态 entry_status】=【已取消】的【流水】
     * 包括已删除的（在回收在里的）
     */
    public List<String> getHasCancelledTransactionFlowIds(String tenantId, String sourceObjectDataId, int limit) {
        List<IObjectData> transactionFlowDataListWithDeleted = getTransactionFlowDataListWithDeleted(tenantId, sourceObjectDataId, EntryStatusEnum.Cancelled, limit);
        if (CollectionUtils.empty(transactionFlowDataListWithDeleted)) {
            return Lists.newArrayList();
        }

        return transactionFlowDataListWithDeleted.stream().map(IObjectData::getId).collect(Collectors.toList());
    }

    public List<IObjectData> getTransactionFlowDataListWithDeleted(String tenantId, String sourceObjectDataId, EntryStatusEnum entryStatusEnum, int limit) {
        User admin = User.systemUser(tenantId);

        List<IFilter> filterList = Lists.newArrayList();
        SearchUtil.fillFilterEq(filterList, AccountTransactionFlowConst.Field.RelateRecordObjectDataId.apiName, sourceObjectDataId);
        SearchUtil.fillFilterEq(filterList, AccountTransactionFlowConst.Field.EntryStatus.apiName, entryStatusEnum.getValue());

        IObjectDescribe objectDescribe = commonDescribeManager.findByTenantIdAndDescribeApiName(tenantId, AccountTransactionFlowConst.API_NAME);

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(filterList);
        query.setOffset(0);
        query.setLimit(limit);
        QueryResult<IObjectData> result = serviceFacade.findBySearchQueryWithDeleted(admin, objectDescribe, query);
        return result.getData();
    }

    public List<IObjectData> getTransactionFlowDataList(String tenantId, String sourceObjectDataId, List<String> expenseTypes, EntryStatusEnum entryStatusEnum) {
        User user = new User(tenantId, "-10000");

        List<IFilter> filterList = Lists.newArrayList();
        SearchUtil.fillFilterEq(filterList, AccountTransactionFlowConst.Field.RelateRecordObjectDataId.apiName, sourceObjectDataId);
        SearchUtil.fillFilterIn(filterList, AccountTransactionFlowConst.Field.ExpenseType.apiName, expenseTypes);
        SearchUtil.fillFilterEq(filterList, AccountTransactionFlowConst.Field.EntryStatus.apiName, entryStatusEnum.getValue());

        QueryResult<IObjectData> queryResult = searchQueryByDbIgnoreAll(user, AccountTransactionFlowConst.API_NAME, filterList, Lists.newArrayList(), 0, 200);
        log.info("getTransactionFlowDataList user[{}], objectApiName[{}], filters[{}], orders[{}], result[{}]",
                user, AccountTransactionFlowConst.API_NAME, filterList, Lists.newArrayList(), queryResult);
        return queryResult.getData();
    }

    /**
     * 是否有已入账的流水
     */
    public boolean hasIncomeTransactionFlow(String tenantId, String dataId) {
        User user = new User(tenantId, "-10000");

        List<IFilter> filterList = Lists.newArrayList();
        SearchUtil.fillFilterEq(filterList, AccountTransactionFlowConst.Field.RelateRecordObjectDataId.apiName, dataId);
        SearchUtil.fillFilterEq(filterList, AccountTransactionFlowConst.Field.EntryStatus.apiName, EntryStatusEnum.AlreadyEntry.getValue());
        SearchUtil.fillFilterEq(filterList, SystemConstants.Field.RecordType.apiName, AccountTransactionFlowConst.RecordType.IncomeRecordType.apiName);
        SearchUtil.fillFilterEq(filterList, ObjectData.IS_DELETED, "0");

        QueryResult<IObjectData> queryResult = searchQuery(user, AccountTransactionFlowConst.API_NAME, filterList, Lists.newArrayList(), 0, 1);
        return !CollectionUtils.empty(queryResult.getData());
    }

    /**
     * 是否有入账的流水
     */
    public boolean hasTransactionFlow(String tenantId, String dataId) {
        User user = new User(tenantId, "-10000");

        List<IFilter> filterList = Lists.newArrayList();
        SearchUtil.fillFilterEq(filterList, AccountTransactionFlowConst.Field.FundAccount.apiName, dataId);
        SearchUtil.fillFilterEq(filterList, ObjectData.IS_DELETED, "0");

        QueryResult<IObjectData> queryResult = searchQuery(user, AccountTransactionFlowConst.API_NAME, filterList, Lists.newArrayList(), 0, 1);
        return !CollectionUtils.empty(queryResult.getData());
    }

    /**
     * '组件扣减'是否有红冲，有红冲plugin不做处理
     */
    public boolean isChargedOff(String tenantId, String sourceObjectDataId) {
        List<IObjectData> transactionFlowDataList = getTransactionFlowDataList(tenantId, sourceObjectDataId, Lists.newArrayList(ExpenseTypeEnum.ComponentDeduct.getValue(), ExpenseTypeEnum.ComponentChargeOff.getValue()), EntryStatusEnum.AlreadyEntry);
        if (CollectionUtils.empty(transactionFlowDataList)) {
            return false;
        }

        return RuleHandlerUtil.isChargedOff(transactionFlowDataList);
    }

    public void updateSalesOrderIdOrPaymentId(CustomerAccountPluginModel.Arg arg) {
        String sourceObjectDescribeApiName = arg.getMasterObjectApiName();
        String sourceObjectDataId = arg.getDataId();

        updateSalesOrderIdOrPaymentId(arg.getUser(), sourceObjectDescribeApiName, sourceObjectDataId);
    }

    public void updateSalesOrderIdOrPaymentId(User user, String sourceObjectDescribeApiName, String sourceObjectDataId) {
        if (!Utils.SALES_ORDER_API_NAME.equals(sourceObjectDescribeApiName) && !Utils.CUSTOMER_PAYMENT_API_NAME.equals(sourceObjectDescribeApiName)) {
            return;
        }

        //查询要更新的流水
        List<IObjectData> transactionFlowDataList = getTransactionFlowDataList(user.getTenantId(), sourceObjectDataId, Lists.newArrayList(ExpenseTypeEnum.ComponentDeduct.getValue()));

        updateSalesOrderIdOrPaymentId(user, sourceObjectDescribeApiName, sourceObjectDataId, transactionFlowDataList);
    }

    public void updateSalesOrderIdOrPaymentId(User user, String sourceObjectDescribeApiName, String sourceObjectDataId, List<IObjectData> transactionFlowDataList) {
        if (!Utils.SALES_ORDER_API_NAME.equals(sourceObjectDescribeApiName) && !Utils.CUSTOMER_PAYMENT_API_NAME.equals(sourceObjectDescribeApiName)) {
            return;
        }

        if (CollectionUtils.empty(transactionFlowDataList)) {
            return;
        }

        List<IObjectData> needUpdates = Lists.newArrayList();
        List<String> updateFieldList = Lists.newArrayList();
        for (IObjectData transactionFlowData : transactionFlowDataList) {
            if (Utils.SALES_ORDER_API_NAME.equals(sourceObjectDescribeApiName)) {
                if (Strings.isNullOrEmpty(transactionFlowData.get(AccountTransactionFlowConst.Field.SalesOrder.apiName, String.class))) {
                    transactionFlowData.set(AccountTransactionFlowConst.Field.SalesOrder.apiName, sourceObjectDataId);
                    updateFieldList.add(AccountTransactionFlowConst.Field.SalesOrder.apiName);
                    needUpdates.add(transactionFlowData);
                }
            } else if (Strings.isNullOrEmpty(transactionFlowData.get(AccountTransactionFlowConst.Field.Payment.apiName, String.class))) {
                transactionFlowData.set(AccountTransactionFlowConst.Field.Payment.apiName, sourceObjectDataId);
                updateFieldList.add(AccountTransactionFlowConst.Field.Payment.apiName);
                needUpdates.add(transactionFlowData);
            }
        }

        if (CollectionUtils.empty(needUpdates)) {
            return;
        }

        serviceFacade.batchUpdateByFields(user, transactionFlowDataList, updateFieldList);
    }

    /**
     * 865支持'支出授权'，刷'账户授权'定义
     *
     * 主对象加新增的字段（authorized_type）= income
     */
    public void updateAccountTransactionFlowExpenseType(String tenantId, String oldExpenseType, String newExpenseType) {
        //是否开启了账户授权
        FundAccountSwitchEnum fundAccountStatus = fundAccountManager.getFundAccountStatus(tenantId);
        log.info("updateAccountTransactionFlowExpenseType fundAccountStatus[{}]", fundAccountStatus);
        if (FundAccountSwitchEnum.FUND_ACCOUNT_OPEN != fundAccountStatus) {
            return;
        }

        User user = new User(tenantId, "-10000");
        List<String> updateFields = Lists.newArrayList(DBRecord.ID);

        //查数据
        List<IObjectData> accountTransactionFlowDatas = getFAccountAuthorizationDatasForUpdate(user, oldExpenseType);
        log.info("getFAccountAuthorizationDatasForUpdate  accountTransactionFlowDatas.size[{}]", accountTransactionFlowDatas.size());
        if (CollectionUtils.empty(accountTransactionFlowDatas)) {
            return;
        }

        //更新
        for (IObjectData accountTransactionFlowData : accountTransactionFlowDatas) {
            accountTransactionFlowData.set(AccountTransactionFlowConst.Field.ExpenseType.apiName, newExpenseType);
            accountTransactionFlowData.setDescribeApiName(AccountTransactionFlowConst.API_NAME);
            accountTransactionFlowData.setTenantId(tenantId);
        }

        updateFields.add(AccountTransactionFlowConst.Field.ExpenseType.apiName);
        serviceFacade.batchUpdateByFields(user, accountTransactionFlowDatas, updateFields);
    }

    public List<IObjectData> getFAccountAuthorizationDatasForUpdate(User user, String oldExpenseType) {
        String objectApiName = AccountTransactionFlowConst.API_NAME;

        List<IFilter> filters = new ArrayList<>();

        IFilter expenseTypeFilter = new Filter();
        expenseTypeFilter.setFieldName(AccountTransactionFlowConst.Field.ExpenseType.apiName);
        expenseTypeFilter.setOperator(Operator.IN);
        expenseTypeFilter.setFieldValues(Lists.newArrayList(oldExpenseType));
        filters.add(expenseTypeFilter);

        return ObjectDataUtil.getObjectDatasWithDeleted(user, objectApiName, filters);
    }

    public Map<String, BigDecimal> getFundAccountId2ExpenseAmount(List<IObjectData> transactionFlowDatas) {
        Map<String, BigDecimal> fundAccountId2ExpenseAmount = new HashMap<>();
        if (CollectionUtils.empty(transactionFlowDatas)) {
            return fundAccountId2ExpenseAmount;
        }

        for (IObjectData t : transactionFlowDatas) {
            String fundAccountId = t.get(AccountTransactionFlowConst.Field.FundAccount.apiName, String.class);
            String expenseAmountStr = t.get(AccountTransactionFlowConst.Field.ExpenseAmount.apiName, String.class);

            if (Strings.isNullOrEmpty(expenseAmountStr) || Strings.isNullOrEmpty(fundAccountId)) {
                continue;
            }
            BigDecimal expenseAmount = new BigDecimal(expenseAmountStr);

            if (!fundAccountId2ExpenseAmount.containsKey(fundAccountId)) {
                fundAccountId2ExpenseAmount.put(fundAccountId, expenseAmount);
            } else {
                BigDecimal originalExpenseAmount = fundAccountId2ExpenseAmount.get(fundAccountId);
                fundAccountId2ExpenseAmount.put(fundAccountId, expenseAmount.add(originalExpenseAmount));
            }
        }

        log.info("getFundAccountId2ExpenseAmount transactionFlowDataList[{}], fundAccountId2ExpenseAmount[{}]", transactionFlowDatas, fundAccountId2ExpenseAmount);
        return fundAccountId2ExpenseAmount;
    }

    public Map<String, BigDecimal> getCustomerAccountId2ExpenseAmount(List<IObjectData> transactionFlowDataList) {
        Map<String, BigDecimal> customerAccountId2ExpenseAmount = new HashMap<>();
        if (CollectionUtils.empty(transactionFlowDataList)) {
            return customerAccountId2ExpenseAmount;
        }

        for (IObjectData t : transactionFlowDataList) {
            String customerAccountId = t.get(AccountTransactionFlowConst.Field.CustomerAccount.apiName, String.class);
            String expenseAmountStr = t.get(AccountTransactionFlowConst.Field.ExpenseAmount.apiName, String.class);

            if (Strings.isNullOrEmpty(expenseAmountStr) || Strings.isNullOrEmpty(customerAccountId)) {
                continue;
            }
            BigDecimal expenseAmount = new BigDecimal(expenseAmountStr);

            if (!customerAccountId2ExpenseAmount.containsKey(customerAccountId)) {
                customerAccountId2ExpenseAmount.put(customerAccountId, expenseAmount);
            } else {
                BigDecimal originalExpenseAmount = customerAccountId2ExpenseAmount.get(customerAccountId);
                customerAccountId2ExpenseAmount.put(customerAccountId, expenseAmount.add(originalExpenseAmount));
            }
        }

        log.info("getCustomerAccountId2ExpenseAmount transactionFlowDataList[{}], customerAccountId2ExpenseAmount[{}]", transactionFlowDataList, customerAccountId2ExpenseAmount);
        return customerAccountId2ExpenseAmount;
    }

    public boolean existTransactionFlow(String tenantId, List<IFilter> filters) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setFilters(filters);
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(1);
        List<IObjectData> resultList = serviceFacade.findBySearchQuery(new User(tenantId, "-10000"), AccountTransactionFlowConst.API_NAME, searchTemplateQuery).getData();
        return resultList != null && !resultList.isEmpty();
    }

}
