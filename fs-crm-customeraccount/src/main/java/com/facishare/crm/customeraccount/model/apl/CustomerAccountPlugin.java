package com.facishare.crm.customeraccount.model.apl;

import com.fxiaoke.functions.FunctionContext;

/**
 * 使用审批  https://www.fxiaoke.com/XV/Home/Index#stream/showfeed2019/=/id-7793421
 */
public interface CustomerAccountPlugin {

    /**
     * account_auth/service/get_outcome_auth_accounts 接口调用
     * 返回满足条件的账户ID
     *
     * @param  arg fundAccountIds 账户ID
     * @return 返回满足条件的账户ID
     */
    GetFundAccountIdsForGetOutcomeAuthAccountsModel.Result getFundAccountIdsForGetOutcomeAuthAccounts(FunctionContext context, GetFundAccountIdsForGetOutcomeAuthAccountsModel.Arg arg);
}