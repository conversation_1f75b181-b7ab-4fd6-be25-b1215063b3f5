package com.facishare.crm.customeraccount.predefine.action;

import com.facishare.crm.customeraccount.constants.*;
import com.facishare.crm.customeraccount.enums.AccessModuleEnum;
import com.facishare.crm.customeraccount.enums.ConfigKeyEnum;
import com.facishare.crm.customeraccount.enums.EntryStatusEnum;
import com.facishare.crm.customeraccount.enums.ExpenseTypeEnum;
import com.facishare.crm.customeraccount.predefine.manager.BizConfigManager;
import com.facishare.crm.customeraccount.predefine.manager.NewCustomerAccountManager;
import com.facishare.crm.customeraccount.predefine.validator.AccountTransactionFlowValidator;
import com.facishare.crm.customeraccount.predefine.validator.Validator;
import com.facishare.crm.customeraccount.util.ObjectDataUtil;
import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class AccountTransactionFlowAddAction extends StandardAddAction {
    private NewCustomerAccountManager newCustomerAccountManager = SpringUtil.getContext().getBean(NewCustomerAccountManager.class);
    private BizConfigManager bizConfigManager = SpringUtil.getContext().getBean(BizConfigManager.class);
    private IObjectData customerAccountData;
    private boolean accountCheckEnable;
    private BizConfigManager.ConfigHolder configHolder;
    private boolean isCreditCustomerAccount;

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        configHolder = bizConfigManager.getConfigValue(actionContext.getUser(), Lists.newArrayList(ConfigKeyEnum.ACCOUNT_CHECK_RULE.key, ConfigKeyEnum.CUSTOMER_ACCOUNT_EXCEED.key));
        accountCheckEnable = configHolder.isTrue(ConfigKeyEnum.ACCOUNT_CHECK_RULE.key, ConfigKeyEnum.ACCOUNT_CHECK_RULE.enabledValue);

        Map<String, Object> attributes = Maps.newHashMap();
        String customerId = this.objectData.get(AccountTransactionFlowConst.Field.Customer.apiName, String.class);
        String fundAccountId = this.objectData.get(AccountTransactionFlowConst.Field.FundAccount.apiName, String.class);
        ObjectDataUtil.checkNotNullOrEmpty(customerId, AccountTransactionFlowConst.Field.Customer.apiName);
        ObjectDataUtil.checkNotNullOrEmpty(fundAccountId, AccountTransactionFlowConst.Field.FundAccount.apiName);
        String orderId = this.objectData.get(AccountTransactionFlowConst.Field.SalesOrder.apiName, String.class);
        IObjectData fundAccountData = serviceFacade.findObjectData(actionContext.getUser(), fundAccountId, FundAccountConstants.API_NAME);
        customerAccountData = newCustomerAccountManager.getOrCreateNewCustomerAccount(actionContext.getRequestContext(), fundAccountId, customerId, objectData);

        if (StringUtils.isNotEmpty(orderId)) {
            IObjectData orderData = serviceFacade.findObjectData(actionContext.getUser(), orderId, Utils.SALES_ORDER_API_NAME);
            attributes.put("orderData", orderData);
            this.objectData.set(AccountTransactionFlowConst.Field.ExpenseType.apiName, ExpenseTypeEnum.SalesDeduct.getValue());
        }
        attributes.put("fundAccountData", fundAccountData);
        attributes.put("customerAccountData", customerAccountData);
        Validator.Context context = new Validator.Context(actionContext.getRequestContext(), configHolder, attributes);
        Validator.build().arg(arg).describe(this.objectDescribe).context(context).add(new AccountTransactionFlowValidator()).validate();

        String accessModule = fundAccountData.get(FundAccountConstants.Field.AccessModule.apiName, String.class);
        this.objectData.set(AccountTransactionFlowConst.Field.AccessModule.apiName, accessModule);
        this.objectData.set(AccountTransactionFlowConst.Field.CustomerAccount.apiName, customerAccountData.getId());

        this.isCreditCustomerAccount = AccessModuleEnum.CREDIT.value.equals(accessModule);
    }

    @Override
    protected void doSaveData() {
        IObjectDescribe customerAccountDescribe = serviceFacade.findObject(actionContext.getTenantId(), NewCustomerAccountConstants.API_NAME);
        PlatformTransactionManager tm = (PlatformTransactionManager) SpringUtil.getContext().getBean("paasMetadataTransactionManager");
        TransactionTemplate template = new TransactionTemplate(tm);
        Map<String, Object> fieldAmountMap = Maps.newHashMap();
        IObjectData updateCustomerAccountData = template.execute(transactionStatus -> {
            String recordType = this.objectData.getRecordType();
            BigDecimal addAmount = BigDecimal.ZERO;
            if (AccountTransactionFlowConst.RecordType.IncomeRecordType.apiName.equals(recordType)) {
                //如果为收入
                addAmount = this.objectData.get(AccountTransactionFlowConst.Field.RevenueAmount.apiName, BigDecimal.class, BigDecimal.ZERO);
            } else if (AccountTransactionFlowConst.RecordType.OutcomeRecordType.apiName.equals(recordType)) {
                //如果为支出
                addAmount = this.objectData.get(AccountTransactionFlowConst.Field.ExpenseAmount.apiName, BigDecimal.class, BigDecimal.ZERO);
                addAmount = addAmount.negate();
            }
            BigDecimal accountBalance = customerAccountData.get(NewCustomerAccountConstants.Field.AccountBalance.apiName, BigDecimal.class, BigDecimal.ZERO);
            this.objectData.set(AccountTransactionFlowConst.Field.AccountBalance.apiName, accountBalance.add(addAmount));
            //需要放在客户账户更新之前，因为保存数据会处理默认值，默认值为已入账时，需要更新客户账户余额
            super.doSaveData();
            //这里获取的entry_status为已处理过默认值后的
            String entryStatus = this.objectData.get(AccountTransactionFlowConst.Field.EntryStatus.apiName, String.class);
            Map<String, Map<String, Object>> columnHandleMap = Maps.newHashMap();
            IObjectData customerAccountDataResult = null;
            if (addAmount.compareTo(BigDecimal.ZERO) != 0 && EntryStatusEnum.AlreadyEntry.getValue().equals(entryStatus)) {
                List<String> updateFieldList = Lists.newArrayList();
                fieldAmountMap.put(NewCustomerAccountConstants.Field.AccountBalance.apiName, addAmount);
                updateFieldList.add(NewCustomerAccountConstants.Field.AccountBalance.apiName);
                if (isCreditCustomerAccount) {
                    updateFieldList.add(NewCustomerAccountConstants.Field.CreditOccupiedAmount.apiName);
                    fieldAmountMap.put(NewCustomerAccountConstants.Field.CreditOccupiedAmount.apiName, addAmount.negate());
                } else if (accountCheckEnable) {
                    updateFieldList.add(NewCustomerAccountConstants.Field.AvailableBalance.apiName);
                    fieldAmountMap.put(NewCustomerAccountConstants.Field.AvailableBalance.apiName, addAmount);
                }
                columnHandleMap.put(customerAccountData.getId(), fieldAmountMap);
                serviceFacade.batchUpdateByFields(actionContext.getUser(), Lists.newArrayList(customerAccountData), updateFieldList, columnHandleMap);
                boolean needSkipValidate = configHolder.isTrue(ConfigKeyEnum.CUSTOMER_ACCOUNT_EXCEED.key, ConfigKeyEnum.CUSTOMER_ACCOUNT_EXCEED.enabledValue);
                List<IObjectData> updateCustomerAccountDataResult = newCustomerAccountManager.queryAndValidateCustomerAccount(actionContext.getUser(), Lists.newArrayList(customerAccountData.getId()), needSkipValidate, this.isCreditCustomerAccount);
                customerAccountDataResult = updateCustomerAccountDataResult.get(0);
                newCustomerAccountManager.addLedgerData(actionContext.getUser(), columnHandleMap, isCreditCustomerAccount);
            }
            return customerAccountDataResult;
        });
        if (Objects.nonNull(updateCustomerAccountData) && MapUtils.isNotEmpty(fieldAmountMap)) {
            serviceFacade.log(this.actionContext.getUser(), EventType.MODIFY, ActionType.Modify, customerAccountDescribe, updateCustomerAccountData, fieldAmountMap, customerAccountData);
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        String orderId = this.objectData.get(AccountTransactionFlowConst.Field.SalesOrder.apiName, String.class);
        if (StringUtils.isNotEmpty(orderId)) {
            //实时计算订单上的receivable_amount、payment_status、payment_amount、faccount_amount字段
            newCustomerAccountManager.calculateAndUpdateFormulaFields(actionContext.getRequestContext(), Utils.SALES_ORDER_API_NAME, Lists.newArrayList(orderId),
                    Lists.newArrayList(Constants.RECEIVABLE_AMOUNT, Constants.PAYMENT_STATUS, Constants.PAYMENT_AMOUNT, Constants.F_ACCOUNT_AMOUNT));
        }
        return result;
    }
}
