package com.facishare.crm.customeraccount.predefine.action;

import com.facishare.crm.customeraccount.constants.AccountTransactionFlowConst;
import com.facishare.crm.customeraccount.enums.EntryStatusEnum;
import com.facishare.crm.customeraccount.enums.ExpenseTypeEnum;
import com.facishare.crm.customeraccount.enums.RevenueTypeEnum;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportTemplateAction;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.google.common.collect.Sets;

import java.util.Iterator;
import java.util.List;
import java.util.Set;

public class AccountTransactionFlowInsertImportTemplateAction extends StandardInsertImportTemplateAction {
    private Set<String> fieldsToRemove = Sets.newHashSet(AccountTransactionFlowConst.Field.Payment.apiName,
            AccountTransactionFlowConst.Field.SalesOrder.apiName,
            AccountTransactionFlowConst.Field.RelateRecordObjectApiName.apiName,
            AccountTransactionFlowConst.Field.RelateRecordObjectDataId.apiName,
            AccountTransactionFlowConst.Field.AccessModule.apiName,
            AccountTransactionFlowConst.Field.RevenueSource.apiName);

    @Override
    protected void customHeader(List<IFieldDescribe> headerFieldList) {
        Iterator<IFieldDescribe> iterator = headerFieldList.iterator();
        while (iterator.hasNext()) {
            IFieldDescribe fieldDescribe = iterator.next();
            if (fieldsToRemove.contains(fieldDescribe.getApiName())) {
                iterator.remove();
            }
            if (AccountTransactionFlowConst.Field.RevenueType.apiName.equals(fieldDescribe.getApiName())) {
                SelectOneFieldDescribe selectOneFieldDescribe = (SelectOneFieldDescribe) fieldDescribe;
                List<ISelectOption> selectOptions = selectOneFieldDescribe.getSelectOptions();
                selectOptions.removeIf(x -> !RevenueTypeEnum.supportUserInput(x.getValue()));
                selectOneFieldDescribe.setSelectOptions(selectOptions);
            } else if (AccountTransactionFlowConst.Field.ExpenseType.apiName.equals(fieldDescribe.getApiName())) {
                SelectOneFieldDescribe selectOneFieldDescribe = (SelectOneFieldDescribe) fieldDescribe;
                List<ISelectOption> selectOptions = selectOneFieldDescribe.getSelectOptions();
                selectOptions.removeIf(x -> !ExpenseTypeEnum.supportUserInput(x.getValue()) || ExpenseTypeEnum.SalesDeduct.getValue().equals(x.getValue()));
                selectOneFieldDescribe.setSelectOptions(selectOptions);
            } else if (AccountTransactionFlowConst.Field.EntryStatus.apiName.equals(fieldDescribe.getApiName())) {
                SelectOneFieldDescribe selectOneFieldDescribe = (SelectOneFieldDescribe) fieldDescribe;
                List<ISelectOption> selectOptions = selectOneFieldDescribe.getSelectOptions();
                selectOptions.removeIf(x -> !EntryStatusEnum.AlreadyEntry.getValue().equals(x.getValue()));
                selectOneFieldDescribe.setSelectOptions(selectOptions);
            }
        }
        super.customHeader(headerFieldList);
    }
}
