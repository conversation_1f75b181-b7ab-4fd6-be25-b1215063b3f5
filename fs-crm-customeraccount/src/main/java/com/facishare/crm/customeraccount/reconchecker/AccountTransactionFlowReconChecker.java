package com.facishare.crm.customeraccount.reconchecker;

import com.facishare.crm.bizreconciliation.checker.BizReconciliationChecker;
import com.facishare.crm.bizreconciliation.consts.ReconAbnormalDataConst;
import com.facishare.crm.bizreconciliation.enums.BizModuleEnum;
import com.facishare.crm.bizreconciliation.model.BizReconCompareResult;
import com.facishare.crm.customeraccount.constants.AccountTransactionFlowConst;
import com.facishare.crm.customeraccount.enums.AccessModuleEnum;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @IgnoreI18nFile
 */
@Slf4j
@Component
public class AccountTransactionFlowReconChecker extends BizReconciliationChecker {
    @Autowired
    private CustomerAccountCheckerManager customerAccountCheckerManager;

    @Override
    public String getObjectApiName() {
        return AccountTransactionFlowConst.API_NAME;
    }

    @Override
    public String getBizModule() {
        return BizModuleEnum.CUSTOMER_ACCOUNT.value;
    }

    @Override
    public List<ReconAbnormalDataConst.Model> check(User user, String bizModule, List<IObjectData> pendingDataList) {
        List<ReconAbnormalDataConst.Model> abnormalList = Lists.newArrayList();
        Map<String, IObjectData> bizPendingDataMap = toBizPendingDataMap(pendingDataList);
        Map<String, IObjectData> flowDataMap = customerAccountCheckerManager.findDataIncludeDeleted(user, getObjectApiName(), Lists.newArrayList(bizPendingDataMap.keySet()));

        bizPendingDataMap.forEach((flowId, pendingData) -> {
            IObjectData flowData = flowDataMap.get(flowId);
            if (Objects.isNull(flowData)) {
                log.warn("flowData not exist,tenantId:{},pendingData:{}", user.getTenantId(), pendingData);
            } else {
                String accessModule = flowData.get(AccountTransactionFlowConst.Field.AccessModule.apiName, String.class);
                if (!AccessModuleEnum.CREDIT.value.equals(accessModule)) {
                    String relateObjectApiName = flowData.get(AccountTransactionFlowConst.Field.RelateRecordObjectApiName.apiName, String.class);
                    String relateObjectDataId = flowData.get(AccountTransactionFlowConst.Field.RelateRecordObjectDataId.apiName, String.class);
                    BizReconCompareResult compareResult = customerAccountCheckerManager.isBizDataError(user, relateObjectApiName, relateObjectDataId);
                    if (compareResult.isError()) {
                        ReconAbnormalDataConst.Model model = generateAbnormalModel(bizModule, ReconAbnormalDataConst.Type.DataAbnormal.value, relateObjectApiName, relateObjectDataId,
                                compareResult.message());
                        abnormalList.add(model);
                    }
                }
            }
        });

        return abnormalList;
    }
}
