package com.facishare.crm.customeraccount.tools.model;

import lombok.Data;

import java.util.List;

public class GenerateDataLangSqlModel {
    @Data
    public static class Arg {
        private List<LangObjectFieldModel> objectModelList;
    }

    @Data
    public static class Result {
        private List<String> objectPgSqlList;
        private List<String> objectChSqlList;
    }

    @Data
    public static class EnableLangResult {

    }

    @Data
    public static class DisableLangResult {

    }
}
