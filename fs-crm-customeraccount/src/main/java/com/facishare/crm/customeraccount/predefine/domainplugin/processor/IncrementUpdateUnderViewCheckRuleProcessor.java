package com.facishare.crm.customeraccount.predefine.domainplugin.processor;

import com.facishare.crm.customeraccount.model.DataUpdateAndAddModel;
import com.facishare.crm.customeraccount.mq.producer.MQProducerManager;
import com.facishare.crm.customeraccount.predefine.domainplugin.BranchTransactionUtil;
import com.facishare.crm.customeraccount.predefine.domainplugin.model.*;
import com.facishare.crm.customeraccount.predefine.manager.AccountCheckManager;
import com.facishare.crm.customeraccount.predefine.manager.NewCustomerAccountManager;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.predef.domain.IncrementUpdateActionDomainPlugin;
import com.facishare.paas.appframework.metadata.cache.RedissonServiceImpl;
import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.transaction.tcc.api.context.BranchTransactionalContext;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

@Component
public class IncrementUpdateUnderViewCheckRuleProcessor extends AbstractCheckRuleProcessor<IncrementUpdateActionDomainPlugin.Arg, IncrementUpdateActionDomainPlugin.Result, CheckRuleIncrementUpdateContextModel> {
    public IncrementUpdateUnderViewCheckRuleProcessor(AccountCheckManager accountCheckManager, NewCustomerAccountManager newCustomerAccountManager, MQProducerManager mqProducerManager, RedissonServiceImpl redissonService) {
        super(accountCheckManager, newCustomerAccountManager, mqProducerManager, redissonService);
    }

    @Override
    public IncrementUpdateActionDomainPlugin.Result newResultInstance() {
        return new IncrementUpdateActionDomainPlugin.Result();
    }

    @Override
    public CheckRulePluginContextKey getContextKeyEnum() {
        return CheckRulePluginContextKey.IncrementUpdate;
    }

    @Override
    public ObjectDataDocument getObjectDataDocument(IncrementUpdateActionDomainPlugin.Arg arg) {
        fillDataByDbDataWhenIncrementUpdate(arg.getObjectData().toObjectData(), arg.getDbObjectData().toObjectData());
        return arg.getObjectData();
    }

    @Override
    public Class<CheckRuleIncrementUpdateContextModel> getContextClass() {
        return CheckRuleIncrementUpdateContextModel.class;
    }

    @Override
    public CheckRuleIncrementUpdateContextModel doPreAct(RequestContext requestContext, IncrementUpdateActionDomainPlugin.Arg arg) {
        String objectApiName = arg.getObjectApiName();
        IObjectData objectData = arg.getObjectData().toObjectData();
        IObjectData dbObjectData = arg.getDbObjectData().toObjectData();
        fillDataByDbDataWhenIncrementUpdate(objectData, dbObjectData);
        String objectDataId = objectData.getId();
        CheckRuleIncrementUpdateContextModel contextModel = new CheckRuleIncrementUpdateContextModel(objectApiName, objectDataId);
        if (arg.isUseSnapshotForApproval()) {
            return contextModel;
        }
        AllRuleUseRecordModel allRuleUseRecordModel = queryAllRuleUseRecord(requestContext, objectApiName, objectDataId);

        DataUpdateAndAddModel.Arg updateAddArg = DataUpdateAndAddModel.Arg.create();
        RuleUseRecordModel frozenRuleUseRecord = allRuleUseRecordModel.getFrozenRuleUseRecordModel();
        if (Objects.nonNull(frozenRuleUseRecord)) {
            CheckRuleTuple<CheckRuleAdaptModel> frozenAdaptResult = adaptFrozenRuleAndGetContext(requestContext, frozenRuleUseRecord, objectData, true, false);
            contextModel.setFrozenRuleAdaptModel(frozenAdaptResult.getContext());
            updateAddArg.merge(frozenAdaptResult.getArg());
        }
        RuleUseRecordModel directReduceRuleUseRecord = allRuleUseRecordModel.getDirectReduceRuleUseRecordModel();
        if (Objects.nonNull(directReduceRuleUseRecord)) {
            CheckRuleTuple<CheckRuleAdaptModel> directReduceAdaptResult = adaptDirectReduceRuleAndGetContext(requestContext, directReduceRuleUseRecord, objectData, true, false);
            contextModel.setDirectReduceAdaptModel(directReduceAdaptResult.getContext());
            updateAddArg.merge(directReduceAdaptResult.getArg());
        }

        contextModel.setUnfreezeRuleTriggerTypeEnum(matchUnfreezeMark());
        newCustomerAccountManager.execute(requestContext.getUser(), updateAddArg);
        return contextModel;
    }

    @Override
    public void doFinallyDo(RequestContext requestContext, IncrementUpdateActionDomainPlugin.Arg arg) {
        //不需要处理
        IObjectData objectData = arg.getObjectData().toObjectData();
        IObjectData dbObjectData = arg.getDbObjectData().toObjectData();
        fillDataByDbDataWhenIncrementUpdate(objectData, dbObjectData);
        CheckRuleIncrementUpdateContextModel contextModel = getContextModel(arg);
        boolean doActComplete = arg.isDoActComplete();

        if (doActComplete) {
            doActCompleteTrue(requestContext, objectData, contextModel);
        } else {
            doActCompleteFalse(requestContext, dbObjectData, contextModel);
        }
    }

    @Override
    public void doConfirm(BranchTransactionalContext branchContext, RequestContext requestContext, IncrementUpdateActionDomainPlugin.Arg arg, CheckRuleIncrementUpdateContextModel contextModel) {
        IObjectData objectData = arg.getObjectData().toObjectData();
        IObjectData dbObjectData = arg.getDbObjectData().toObjectData();
        fillDataByDbDataWhenIncrementUpdate(objectData, dbObjectData);
        doActCompleteTrue(requestContext, objectData, contextModel);
    }

    @Override
    public void doCancel(BranchTransactionalContext branchContext, RequestContext requestContext, IncrementUpdateActionDomainPlugin.Arg arg, CheckRuleIncrementUpdateContextModel contextModel) {
        IObjectData objectData = arg.getObjectData().toObjectData();
        IObjectData dbObjectData = arg.getDbObjectData().toObjectData();
        fillDataByDbDataWhenIncrementUpdate(objectData, dbObjectData);
        boolean writeDb = BranchTransactionUtil.getWriteDbFlag(branchContext);
        if (writeDb) {
            doActCompleteTrue(requestContext, objectData, contextModel);
        } else {
            doActCompleteFalse(requestContext, dbObjectData, contextModel);
        }
    }

    private void doActCompleteFalse(RequestContext requestContext, IObjectData dbObjectData, CheckRuleIncrementUpdateContextModel contextModel) {
        Map<String, IObjectData> accountRuleUseRecordDataMap = queryAccountRuleUseRecord(requestContext, contextModel.fetchAccountRuleUseRecordIds());
        DataUpdateAndAddModel.Arg updateAddArg = DataUpdateAndAddModel.Arg.create();
        CheckRuleAdaptModel frozenAdaptModel = contextModel.getFrozenRuleAdaptModel();
        if (Objects.nonNull(frozenAdaptModel)) {
            IObjectData accountRuleUseRecordData = accountRuleUseRecordDataMap.get(frozenAdaptModel.getAccountRuleUseRecordId());
            RuleUseRecordModel frozenRuleUseRecord = toRuleUseRecordModel(requestContext.getUser(), accountRuleUseRecordData, frozenAdaptModel.getDataMatchedRuleStage());
            CheckRuleTuple<CheckRuleAdaptModel> frozenAdaptTuple = adaptFrozenRuleAndGetContext(requestContext, frozenRuleUseRecord, dbObjectData, false, false);
            updateAddArg.merge(frozenAdaptTuple.getArg());
        }

        CheckRuleAdaptModel directReduceAdaptModel = contextModel.getDirectReduceAdaptModel();
        if (Objects.nonNull(directReduceAdaptModel)) {
            IObjectData accountRuleUseRecordData = accountRuleUseRecordDataMap.get(directReduceAdaptModel.getAccountRuleUseRecordId());
            RuleUseRecordModel directReduceRuleUseRecord = toRuleUseRecordModel(requestContext.getUser(), accountRuleUseRecordData, directReduceAdaptModel.getDataMatchedRuleStage());
            CheckRuleTuple<CheckRuleAdaptModel> directReduceAdaptTuple = adaptDirectReduceRuleAndGetContext(requestContext, directReduceRuleUseRecord, dbObjectData, false, false);
            updateAddArg.merge(directReduceAdaptTuple.getArg());
        }

        newCustomerAccountManager.execute(requestContext.getUser(), updateAddArg);
    }

    private void doActCompleteTrue(RequestContext requestContext, IObjectData objectData, CheckRuleIncrementUpdateContextModel contextModel) {
        Map<String, IObjectData> accountRuleUseRecordDataMap = queryAccountRuleUseRecord(requestContext, contextModel.fetchAccountRuleUseRecordIds());

        DataUpdateAndAddModel.Arg updateAddArg = DataUpdateAndAddModel.Arg.create();
        CheckRuleAdaptModel frozenAdaptModel = contextModel.getFrozenRuleAdaptModel();
        if (Objects.nonNull(frozenAdaptModel)) {
            IObjectData accountRuleUseRecordData = accountRuleUseRecordDataMap.get(frozenAdaptModel.getAccountRuleUseRecordId());
            RuleUseRecordModel frozenRuleUseRecord = toRuleUseRecordModel(requestContext.getUser(), accountRuleUseRecordData, frozenAdaptModel.getDataMatchedRuleStage());
            CheckRuleTuple<CheckRuleAdaptModel> frozenAdaptTuple = adaptFrozenRuleAndGetContext(requestContext, frozenRuleUseRecord, objectData, false, false);
            updateAddArg.merge(frozenAdaptTuple.getArg());
        }

        CheckRuleAdaptModel directReduceAdaptModel = contextModel.getDirectReduceAdaptModel();
        if (Objects.nonNull(directReduceAdaptModel)) {
            IObjectData accountRuleUseRecordData = accountRuleUseRecordDataMap.get(directReduceAdaptModel.getAccountRuleUseRecordId());
            RuleUseRecordModel directReduceRuleUseRecord = toRuleUseRecordModel(requestContext.getUser(), accountRuleUseRecordData, directReduceAdaptModel.getDataMatchedRuleStage());
            CheckRuleTuple<CheckRuleAdaptModel> directReduceAdaptTuple = adaptDirectReduceRuleAndGetContext(requestContext, directReduceRuleUseRecord, objectData, false, false);
            updateAddArg.merge(directReduceAdaptTuple.getArg());
        }

        newCustomerAccountManager.execute(requestContext.getUser(), updateAddArg);
        triggerUnfreezeIgnoreException(requestContext, objectData, contextModel.getUnfreezeRuleTriggerTypeEnum());
    }
}
