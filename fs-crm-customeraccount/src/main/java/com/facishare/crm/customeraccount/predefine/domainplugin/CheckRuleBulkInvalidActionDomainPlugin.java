package com.facishare.crm.customeraccount.predefine.domainplugin;

import com.facishare.crm.customeraccount.predefine.domainplugin.processor.CheckRuleProcessorFactory;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.fxiaoke.transaction.tcc.annotation.TccTransactional;
import com.fxiaoke.transaction.tcc.api.context.BranchTransactionalContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
@ServiceModule("check_rule_plugin_bulk_invalid")
public class CheckRuleBulkInvalidActionDomainPlugin extends EmptyBulkInvalidActionDomainPlugin {
    @Autowired
    private CheckRuleProcessorFactory checkRuleProcessorFactory;

    @ServiceMethod("pre_act")
    @TccTransactional(name = "preAct", confirmMethod = "confirmPreAct", cancelMethod = "cancelPreAct")
    public Result preAct(ServiceContext serviceContext, Arg arg) {
        Result result = checkRuleProcessorFactory.process(serviceContext.getRequestContext(), arg, true);
        if (Objects.isNull(result)) {
            result = new Result();
        }
        return result;
    }

    @ServiceMethod("finally_do")
    public Result finallyDo(ServiceContext serviceContext, Arg arg) {
        checkRuleProcessorFactory.process(serviceContext.getRequestContext(), arg, false);
        return new Result();
    }

    public void confirmPreAct(BranchTransactionalContext branchContext, ServiceContext serviceContext, Arg arg) {
        checkRuleProcessorFactory.confirm(branchContext, serviceContext.getRequestContext(), arg);
    }

    public void cancelPreAct(BranchTransactionalContext branchContext, ServiceContext serviceContext, Arg arg) {
        checkRuleProcessorFactory.cancel(branchContext, serviceContext.getRequestContext(), arg);
    }
}
