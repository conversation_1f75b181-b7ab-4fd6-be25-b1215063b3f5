package com.facishare.crm.customeraccount.constants;

/**
 * @IgnoreI18nFile
 */
public interface NewCustomerAccountConstants {
    String API_NAME = "NewCustomerAccountObj";

    String DISPLAY_NAME = "客户账户余额";
    String DETAIL_LAYOUT_API_NAME = "NewCustomerAccountObj_default_layout__c";
    String DETAIL_LAYOUT_DISPLAY_NAME = "默认布局";
    String LIST_LAYOUT_API_NAME = "NewCustomerAccountObj_list_layout__c";
    String LIST_LAYOUT_DISPLAY_NAME = "移动端默认列表页";
    String CREDIT_SCENE_API_NAME="scene_credit__c";
    String STORE_TABLE_NAME = "new_customer_account";

    enum Field {
        Name("name", "账户编号"),

        Customer("customer_id", "客户名称"),

        FundAccount("fund_account_id", "账户"),

        AccountBalance("account_balance", "账户余额"),

        Partner("partner_id", "合作伙伴"),

        OutResources("out_resources", "外部来源"),
        /**
         * 金额字段，高级模式的字段
         */
        OccupiedAmount("occupied_amount", "占用金额"),
        /**
         * 金额字段，高级模式的字段
         */
        AvailableBalance("available_balance", "账户可用金额"),

        /**
         * credit_quota = account_balance + credit_occupied_amount
         */
        CreditQuota("credit_quota", "信用额度"),
        CreditOccupiedAmount("credit_occupied_amount", "信用占用金额");

        public final String apiName;
        public final String label;

        Field(String apiName, String label) {
            this.apiName = apiName;
            this.label = label;
        }

    }
}
