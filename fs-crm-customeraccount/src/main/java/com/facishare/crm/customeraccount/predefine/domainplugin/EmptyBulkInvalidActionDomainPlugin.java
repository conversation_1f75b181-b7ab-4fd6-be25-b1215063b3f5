package com.facishare.crm.customeraccount.predefine.domainplugin;

import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.predef.action.StandardAction;
import com.facishare.paas.appframework.core.predef.domain.BulkInvalidActionDomainPlugin;

public class EmptyBulkInvalidActionDomainPlugin implements BulkInvalidActionDomainPlugin {
    @Override
    public Result before(ActionContext actionContext, Arg arg) {
        return new Result();
    }

    @Override
    public Result after(ActionContext actionContext, Arg arg) {
        return new Result();
    }

    @Override
    public Result finallyDo(ActionContext actionContext, Arg arg) {
        return new Result();
    }

    public StandardAction getStandardAction() {
        return StandardAction.BulkInvalid;
    }
}
