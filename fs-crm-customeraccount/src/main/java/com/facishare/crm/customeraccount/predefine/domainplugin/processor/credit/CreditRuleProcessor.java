package com.facishare.crm.customeraccount.predefine.domainplugin.processor.credit;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.condition.ConditionUtil;
import com.facishare.crm.consts.*;
import com.facishare.crm.customeraccount.constants.AccountTransactionFlowConst;
import com.facishare.crm.customeraccount.constants.CAI18NKey;
import com.facishare.crm.customeraccount.constants.NewCustomerAccountConstants;
import com.facishare.crm.customeraccount.enums.CreditRuleNodeTypeEnum;
import com.facishare.crm.customeraccount.enums.CreditTypeEnum;
import com.facishare.crm.customeraccount.enums.EntryStatusEnum;
import com.facishare.crm.customeraccount.enums.StageEnum;
import com.facishare.crm.customeraccount.exception.CreditNotEnoughNotBlockException;
import com.facishare.crm.customeraccount.model.DataTriggerFunctionModel;
import com.facishare.crm.customeraccount.model.DataUpdateAndAddModel;
import com.facishare.crm.customeraccount.predefine.domainplugin.model.*;
import com.facishare.crm.customeraccount.predefine.manager.CreditManager;
import com.facishare.crm.customeraccount.predefine.manager.NewCustomerAccountManager;
import com.facishare.crm.customeraccount.predefine.service.dto.CreditCurAndPreObjectConfig;
import com.facishare.crm.customeraccount.predefine.service.dto.CreditObjectConfig;
import com.facishare.crm.customeraccount.util.CreditUtil;
import com.facishare.crm.customeraccount.util.ObjectDataUtil;
import com.facishare.crm.openapi.Utils;
import com.facishare.crmcommon.util.SearchQueryBuilder;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.model.domain.DomainPlugin;
import com.facishare.paas.appframework.core.predef.domain.FlowCompletedActionDomainPlugin;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public abstract class CreditRuleProcessor<A extends DomainPlugin.Arg, R extends DomainPlugin.Result, C> {
    protected NewCustomerAccountManager newCustomerAccountManager;
    protected CreditManager creditManager;
    protected ServiceFacade serviceFacade;

    private static final String creditValidateStepKey = "CreditBalanceValidate";

    protected CreditRuleProcessor(NewCustomerAccountManager newCustomerAccountManager, CreditManager creditManager, ServiceFacade serviceFacade) {
        this.newCustomerAccountManager = newCustomerAccountManager;
        this.creditManager = creditManager;
        this.serviceFacade = serviceFacade;
    }

    protected boolean isPass(FlowCompletedActionDomainPlugin.Arg arg) {
        return "pass".equals(arg.getStatus());
    }

    public final boolean skipValidateCredit(DomainPlugin.Arg arg) {
        List<String> skippedStepKeys = CollectionUtils.nullToEmpty(arg.getSkippedStepKeys());
        return skippedStepKeys.contains(creditValidateStepKey);
    }

    protected abstract CreditRulePluginContextKey getContextKey();

    protected abstract Class<C> getContextClass();

    protected C getContextModel(DomainPlugin.Arg arg) {
        CreditRulePluginContextKey contextKey = getContextKey();
        String json = arg.getContextData(contextKey.key);
        return JSON.parseObject(json, getContextClass());
    }

    protected abstract R newResultInstance();

    public final R preAct(RequestContext requestContext, A arg) {
        R result = newResultInstance();
        Set<String> lockKeys = getLockKeys(requestContext, arg);
        RLock lock = newCustomerAccountManager.lockAndBlock(requestContext, lockKeys);
        try {
            C contextModel = doPreAct(requestContext, arg);
            if (contextModel instanceof LockContextModel) {
                LockContextModel lockContextModel = (LockContextModel) contextModel;
                lockContextModel.setLockTime(System.currentTimeMillis());
                lockContextModel.setLockKeys(lockKeys);
            }
            Map<String, String> contextData = Maps.newHashMap();
            contextData.put(getContextKey().key, contextModel instanceof String ? (String) contextModel : JSON.toJSONString(contextModel));
            result.setContextData(contextData);
        } catch (Exception e) {
            log.warn("credit rule error tenantId:{}", requestContext.getTenantId(), e);
            newCustomerAccountManager.unlock(lock);
            CreditRulePluginContextKey contextKey = getContextKey();
            if (e instanceof CreditNotEnoughNotBlockException && (contextKey == CreditRulePluginContextKey.Add || contextKey == CreditRulePluginContextKey.Edit)) {
                //仅Add Edit支持二次确认
                ValidationResult validationResult = new ValidationResult();
                validationResult.setBlock(false);
                validationResult.setMessage(e.getMessage());
                validationResult.setStepKey(creditValidateStepKey);
                validationResult.setSkipWhenForceSubmit(false);
                result.setValidationResult(validationResult);
                return result;
            }
            throw e;
        }
        return result;
    }

    protected abstract IObjectData getObjectData(A arg);

    protected abstract List<IObjectData> getDetailDataList(A arg, String detailApiName);

    protected Set<String> getLockKeys(RequestContext requestContext, A arg) {
        IObjectData objectData = getObjectData(arg);
        Set<String> lockKeys = Sets.newHashSet();
        if (Objects.isNull(objectData)) {
            return lockKeys;
        }
        List<IObjectData> creditRuleMatchRecordDataList = creditManager.findCreditRuleMatchRecordByData(requestContext.getUser(), arg.getObjectApiName(), Sets.newHashSet(objectData.getId()));
        creditRuleMatchRecordDataList.forEach(x -> {
            String sourceObjectApiName = x.get(CreditRuleMatchRecordConst.F.SourceObject.apiName, String.class);
            List<String> sourceDataIds = CreditUtil.getTagFieldValues(x, CreditRuleMatchRecordConst.F.SourceDataIds.apiName);
            sourceDataIds.forEach(dataId -> lockKeys.add(generateLockKey(requestContext.getTenantId(), sourceObjectApiName, dataId)));
        });
        return lockKeys;
    }

    protected Set<String> getLockKeysByAdd(RequestContext requestContext, A arg) {
        String tenantId = requestContext.getTenantId();
        IObjectData objectData = getObjectData(arg);
        String objectApiName = arg.getObjectApiName();
        return CreditUtil.getCreditObjectConfig(tenantId, objectApiName).map(x -> {
            List<CreditObjectConfig.PreObjectConfig> preObjectConfigs = x.getPreObjectConfigs();
            Set<String> lockKeys = Sets.newHashSet();
            if (StringUtils.isNotEmpty(x.getDetailObjectApiName())) {
                if (CollectionUtils.empty(preObjectConfigs)) {
                    //当前数据id
                    lockKeys.add(generateLockKey(tenantId, objectApiName, objectData.getId()));
                } else {
                    for (CreditObjectConfig.PreObjectConfig preObjectConfig : preObjectConfigs) {
                        String lookUpPreObjectFieldName = preObjectConfig.getLookUpPreObjectFieldApiName();
                        String lookUpPreObjectMasterFieldName = preObjectConfig.getPreDetailLookUpMasterFieldApiName();

                        Set<String> preDetailDataIds = getDetailDataList(arg, x.getDetailObjectApiName()).stream().map(data -> data.get(lookUpPreObjectFieldName, String.class)).filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
                        List<IObjectData> preDetailDataList = serviceFacade.findObjectDataByIds(tenantId, Lists.newArrayList(preDetailDataIds), preObjectConfig.getPreDetailObjectApiName());
                        lockKeys.addAll(preDetailDataList.stream().map(data -> data.get(lookUpPreObjectMasterFieldName, String.class)).filter(StringUtils::isNotEmpty)
                                .map(dataId -> generateLockKey(tenantId, preObjectConfig.getPreMasterObjectApiName(), dataId)).collect(Collectors.toSet()));
                    }
                }
            }
            return lockKeys;
        }).orElse(Sets.newHashSet());
    }

    protected String generateLockKey(String tenantId, String objectApiName, String objectId) {
        return "CreditRule_" + tenantId + "_" + objectApiName + "_" + objectId;
    }

    protected abstract C doPreAct(RequestContext requestContext, A arg);

    public final void finallyDo(RequestContext requestContext, A arg) {
        String contextString = arg.getContextData(getContextKey().key);
        if (StringUtils.isEmpty(contextString)) {
            log.warn("context is empty,tenantId:{},creditRuleProcessor:{}", requestContext.getTenantId(), getClass().getSimpleName());
            return;
        }
        C contextModel = getContextModel(arg);
        try {
            doFinallyDo(requestContext, arg, contextModel);
        } finally {
            newCustomerAccountManager.unlock(contextModel);
        }
    }

    public abstract void doFinallyDo(RequestContext requestContext, A arg, C contextModel);

    protected boolean notNeedMatch(RequestContext requestContext, IObjectData objectData) {
        String objectApiName = objectData.getDescribeApiName();
        CreditObjectConfig creditObjectConfig = CreditUtil.getCreditObjectConfig(requestContext.getTenantId(), objectApiName).orElse(null);
        if (Objects.isNull(creditObjectConfig)) {
            return true;
        }
        String customerId = objectData.get(creditObjectConfig.getCustomerFieldApiName(), String.class);
        return StringUtils.isEmpty(customerId);
    }

    public CreditRuleAddContextModel matchRule(RequestContext requestContext, IObjectData objectData, Map<String, List<ObjectDataDocument>> details, boolean skipValidateCredit) {
        User user = requestContext.getUser();
        String objectApiName = objectData.getDescribeApiName();
        CreditRuleAddContextModel creditRuleAddContextModel = new CreditRuleAddContextModel(objectApiName, objectData.getId());
        if (notNeedMatch(requestContext, objectData)) {
            return creditRuleAddContextModel;
        }
        List<IObjectData> allCreditRuleDetailList = creditManager.findCreditRuleDetailContainAllByObject(user, objectApiName);
        if (CollectionUtils.empty(allCreditRuleDetailList)) {
            return creditRuleAddContextModel;
        }
        IObjectDescribe objectDescribe = serviceFacade.findObject(user.getTenantId(), objectApiName);
        Tuple<List<ObjectDataCreditRuleInfo>, List<ObjectDataCreditRuleInfo>> tuple = ObjectDataCreditRuleInfo.convertGroupBy(objectApiName, allCreditRuleDetailList);
        List<ObjectDataCreditRuleInfo> firstNodeList = tuple.getKey();
        List<ObjectDataCreditRuleInfo> notFirstNodeList = tuple.getValue();
        for (ObjectDataCreditRuleInfo objectCreditRuleInfo : notFirstNodeList) {
            //作为非首节点时，前置节点是否有匹配信用规则，若前置节点匹配了，则当前节点作为流转节点，进行信用的流转；
            if (!ConditionUtil.matched(objectDescribe, objectData, objectCreditRuleInfo.getFilters())) {
                continue;
            }
            CreditRuleAddContextModel contextModel = notFirstNodeMatchRule(requestContext, objectData, details, objectCreditRuleInfo);
            //作为非首节点没有匹配，则继续匹配下一个非节点的规则或者匹配作为首节点时的规则
            if (contextModel.matched()) {
                return contextModel;
            }
        }
        Map<String, IObjectData> customerAccountDataMap = Maps.newHashMap();
        Map<String, String> customerSpecifiedRuleIdMap = Maps.newHashMap();
        for (ObjectDataCreditRuleInfo objectDataCreditRuleInfo : firstNodeList) {
            //作为首个节点时，直接匹配，进行信用占用
            CreditCurAndPreObjectConfig creditCurAndPreConfig = objectDataCreditRuleInfo.getCreditCurAndPreObjectConfig();
            String customerFieldApiName = creditCurAndPreConfig.getCustomerFieldApiName();
            String customerId = objectData.get(customerFieldApiName, String.class);
            if (StringUtils.isEmpty(customerId)) {
                continue;
            }
            IObjectData customerData = customerAccountDataMap.computeIfAbsent(customerId, k -> serviceFacade.findObjectData(requestContext.getUser(), customerId, Utils.ACCOUNT_API_NAME));
            Boolean creditControl = customerData.get(AccountConst.CREDIT_CONTROL, Boolean.class);
            if (BooleanUtils.isFalse(creditControl)) {
                continue;
            }
            String specifiedCreditRuleId = customerSpecifiedRuleIdMap.computeIfAbsent(customerId, k -> getSpecifiedCreditRuleId(requestContext, customerId));
            if (!StringUtils.equals(specifiedCreditRuleId, objectDataCreditRuleInfo.getCreditOccupiedRuleId())) {
                continue;
            }
            if (!ConditionUtil.matched(objectDescribe, objectData, objectDataCreditRuleInfo.getFilters())) {
                continue;
            }
            return firstNodeMatchRule(requestContext, objectData, details, objectDataCreditRuleInfo, skipValidateCredit);
        }
        return creditRuleAddContextModel;
    }

    protected String getSpecifiedCreditRuleId(RequestContext requestContext, String customerId) {
        List<IObjectData> customerCreditAuthOfficialList = creditManager.findCustomerCreditAuth(requestContext.getUser(), Utils.ACCOUNT_API_NAME, customerId, CreditTypeEnum.OfficialCredit.getValue());
        if (CollectionUtils.empty(customerCreditAuthOfficialList)) {
            return null;
        }
        return customerCreditAuthOfficialList.get(0).get(CustomerCreditAuthConst.F.CreditOccupiedRuleId.apiName, String.class);
    }

    protected abstract CreditRuleAddContextModel firstNodeMatchRule(RequestContext requestContext, IObjectData objectData, Map<String, List<ObjectDataDocument>> details, ObjectDataCreditRuleInfo objectDataCreditRuleInfo, boolean skipValidateCredit);

    public abstract CreditRuleAddContextModel notFirstNodeMatchRule(RequestContext requestContext, IObjectData objectData, Map<String, List<ObjectDataDocument>> details, ObjectDataCreditRuleInfo objectDataCreditRuleInfo);

    public void executeNotFirstNodeCreditRuleMatch(RequestContext requestContext, IObjectData objectData, Map<String, List<ObjectDataDocument>> details, NotFirstNodeCreditRuleMatchExecuteModel executeModel) {
        String creditRuleDetailDataId = executeModel.getCreditRuleDetailDataId();
        Set<String> preObjectCreditRuleMatchRecordIds = executeModel.getPreObjectCreditRuleMatchRecordIds();
        String creditCustomerAccountDataId = executeModel.getCreditCustomerAccountId();
        Map<String, BigDecimal> preCreditFlowDetailLeftCreditAmountChangedMap = executeModel.getPreCreditFlowDetailLeftCreditAmountChangedMap();
        Map<String, BigDecimal> preNodeTransactionFlowToNewExpenseAmountMap = executeModel.getPreNodeTransactionFlowToNewExpenseAmountMap();

        User user = requestContext.getUser();
        String objectApiName = objectData.getDescribeApiName();

        IObjectData creditCustomerAccountData = serviceFacade.findObjectData(user, creditCustomerAccountDataId, NewCustomerAccountConstants.API_NAME);
        IObjectData creditRuleDetailData = serviceFacade.findObjectData(user, creditRuleDetailDataId, CreditRuleDetailConst.API_NAME);
        String creditOccupiedRuleId = creditRuleDetailData.get(CreditRuleDetailConst.F.CreditOccupiedRuleId.apiName, String.class);

        DataUpdateAndAddModel.Arg creditArg = DataUpdateAndAddModel.Arg.create().updateCreditCustomerAccount(true);
        BigDecimal expenseAmount = BigDecimal.ZERO;
        for (CreditFlowDetailModel creditFlowDetailModel : CollectionUtils.nullToEmpty(executeModel.getCreditFlowDetailModelList())) {
            BigDecimal creditAmount = creditFlowDetailModel.getCreditAmount();
            expenseAmount = expenseAmount.add(creditAmount);
            creditArg.appendAddData(creditFlowDetailModel.toObjectData(user, creditCustomerAccountData));
        }
        if (expenseAmount.compareTo(BigDecimal.ZERO) != 0) {
            IObjectData creditTransactionFlowData = CreditUtil.generateCreditAccountTransactionFlow(user, objectApiName, objectData.getId(), expenseAmount, creditCustomerAccountData);
            creditArg.appendAddData(creditTransactionFlowData, false);
        }

        List<IObjectData> preObjectCreditRuleMatchRecordDataList = serviceFacade.findObjectDataByIds(user.getTenantId(), Lists.newArrayList(preObjectCreditRuleMatchRecordIds), CreditRuleMatchRecordConst.API_NAME);
        IObjectData curCreditRuleMatchRecord = CreditUtil.generateCreditRuleMatchRecord(user, creditOccupiedRuleId, preObjectCreditRuleMatchRecordDataList, objectApiName, objectData.getId());
        creditArg.appendAddData(curCreditRuleMatchRecord);

        if (MapUtils.isNotEmpty(preNodeTransactionFlowToNewExpenseAmountMap)) {
            List<IObjectData> preNodeTransactionFlowDataList = serviceFacade.findObjectDataByIds(user.getTenantId(), Lists.newArrayList(preNodeTransactionFlowToNewExpenseAmountMap.keySet()), AccountTransactionFlowConst.API_NAME);
            preNodeTransactionFlowDataList.forEach(preNodeTransactionFlowData -> {
                BigDecimal newExpenseAmount = preNodeTransactionFlowToNewExpenseAmountMap.get(preNodeTransactionFlowData.getId());
                if (newExpenseAmount.compareTo(BigDecimal.ZERO) > 0) {
                    IObjectData newPreNodeTransactionData = CreditUtil.generateTransactionFlowDataFromOtherWithAmount(user, preNodeTransactionFlowData, newExpenseAmount);
                    creditArg.appendAddData(newPreNodeTransactionData, false);
                }
                IObjectData preNodeTransactionFlowDataCopy = ObjectDataExt.of(preNodeTransactionFlowData).copy();
                preNodeTransactionFlowData.set(AccountTransactionFlowConst.Field.EntryStatus.apiName, EntryStatusEnum.Cancelled.getValue());
                creditArg.appendUpdateData(AccountTransactionFlowConst.Field.EntryStatus.apiName, preNodeTransactionFlowDataCopy, preNodeTransactionFlowData, true);
            });
        }
        if (MapUtils.isNotEmpty(preCreditFlowDetailLeftCreditAmountChangedMap)) {
            List<IObjectData> preCreditFlowDetailDataList = serviceFacade.findObjectDataByIds(user.getTenantId(), Lists.newArrayList(preCreditFlowDetailLeftCreditAmountChangedMap.keySet()), CreditFlowDetailConst.API_NAME);
            for (IObjectData preCreditFlowDetailData : preCreditFlowDetailDataList) {
                BigDecimal changedLiftCreditAmount = preCreditFlowDetailLeftCreditAmountChangedMap.get(preCreditFlowDetailData.getId());
                IObjectData preCreditFlowDetailDataCopy = ObjectDataExt.of(preCreditFlowDetailData).copy();
                BigDecimal leftCreditAmount = preCreditFlowDetailData.get(CreditFlowDetailConst.F.LeftCreditAmount.apiName, BigDecimal.class);
                preCreditFlowDetailData.set(CreditFlowDetailConst.F.LeftCreditAmount.apiName, leftCreditAmount.add(changedLiftCreditAmount));
                creditArg.appendUpdateData(CreditFlowDetailConst.F.LeftCreditAmount.apiName, preCreditFlowDetailDataCopy, preCreditFlowDetailData, false);
            }
        }
        newCustomerAccountManager.executeCredit(user, creditArg);
    }

    public void matchRuleFinallyDoActCompleteTrue(RequestContext requestContext, IObjectData objectData, Map<String, List<ObjectDataDocument>> details, CreditRuleAddContextModel creditRuleAddContextModel) {
        User user = requestContext.getUser();
        if (BooleanUtils.isTrue(creditRuleAddContextModel.getFirstNodeMatch())) {
            String flowId = creditRuleAddContextModel.getCurAddCreditTransactionFlowId();
            if (StringUtils.isEmpty(flowId)) {
                return;
            }
            IObjectData creditTransactionFlowData = serviceFacade.findObjectData(user, AccountTransactionFlowConst.API_NAME, flowId);
            DataTriggerFunctionModel dataTriggerFunctionModel = DataTriggerFunctionModel.create().appendDataTriggerCreate(creditTransactionFlowData);
            dataTriggerFunctionModel.triggerFunctionIgnoreException(user, StageEnum.POST, newCustomerAccountManager, true);
        } else {
            //非首节点的匹配
            //前一个节点的  收支流水  流水明细
            //当前节点的   收支流水 流水明细  规则匹配记录
            executeNotFirstNodeCreditRuleMatch(requestContext, objectData, details, creditRuleAddContextModel.toExecuteModel());
        }
    }

    public void matchRuleFinallyDoActCompleteFalse(RequestContext requestContext, CreditRuleAddContextModel creditRuleAddContextModel) {
        if (!BooleanUtils.isTrue(creditRuleAddContextModel.getFirstNodeMatch())) {
            //没匹配上，不需要处理，中间节点的也不需要处理
            return;
        }
        User user = requestContext.getUser();
        DataUpdateAndAddModel.Arg arg = DataUpdateAndAddModel.Arg.create().updateCreditCustomerAccount(true);
        //匹配上了，则规则匹配记录一定不为空
        String creditRuleMatchRecordDataId = creditRuleAddContextModel.getCreditRuleMatchRecordDataId();
        IObjectData creditRuleMatchRecordData = serviceFacade.findObjectData(user, creditRuleMatchRecordDataId, CreditRuleMatchRecordConst.API_NAME);
        arg.appendInvalidData(creditRuleMatchRecordData, true);
        String creditTransactionFlowId = creditRuleAddContextModel.getCurAddCreditTransactionFlowId();
        if (StringUtils.isNotEmpty(creditTransactionFlowId)) {
            IObjectData creditTransactionFlowData = serviceFacade.findObjectData(user, creditTransactionFlowId, AccountTransactionFlowConst.API_NAME);
            BigDecimal creditAmount = creditTransactionFlowData.get(AccountTransactionFlowConst.Field.ExpenseAmount.apiName, BigDecimal.class);
            arg.appendInvalidData(creditTransactionFlowData, false, true);

            String customerAccountId = creditRuleAddContextModel.getCreditCustomerAccountId();
            Map<String, Map<String, Object>> customerAccountUpdateColumnMap = CreditUtil.buildCustomerAccountCreditOccupiedAmountUpdateFieldMap(creditAmount.negate(), customerAccountId);
            arg.customerAccountColumnUpdateMap(customerAccountUpdateColumnMap);
        }
        List<String> creditFlowDetailDataIds = creditRuleAddContextModel.getCurAddCreditFlowDetailDataIds();

        if (CollectionUtils.notEmpty(creditFlowDetailDataIds)) {
            List<IObjectData> creditFlowDetailDataList = serviceFacade.findObjectDataByIds(user.getTenantId(), creditFlowDetailDataIds, CreditFlowDetailConst.API_NAME);
            arg.appendInvalidData(creditFlowDetailDataList, true, true);
        }
        newCustomerAccountManager.executeCredit(user, arg);
    }

    public void doEditActTrueAtFinally(RequestContext requestContext, CreditRuleEditContextModel contextModel) {
        //若金额变大 且 doActCompleted=true，则不需要处理，因为在before中处理过了
        if (contextModel.creditChangedLte()) {
            //若金额变小 且 doActCompleted=true，需要处理，因为变小时before不处理
            String tenantId = requestContext.getTenantId();
            Map<String, Map<String, Object>> creditFlowDetailLeftCreditAmountUpdateFieldMap = CollectionUtils.nullToEmpty(contextModel.getCreditFlowDetailLeftCreditAmountUpdateFieldMap());
            Set<String> allCreditFlowDetailIds = Sets.newHashSet();
            allCreditFlowDetailIds.addAll(contextModel.getToDeleteCreditFlowDetailDataIds());
            allCreditFlowDetailIds.addAll(creditFlowDetailLeftCreditAmountUpdateFieldMap.keySet());

            List<IObjectData> toDeleteCreditTransactionFlowDataList = serviceFacade.findObjectDataByIds(tenantId, contextModel.getToDeleteCreditTransactionFlowDataIds(), AccountTransactionFlowConst.API_NAME);
            List<IObjectData> allCreditFlowDetailList = serviceFacade.findObjectDataByIds(tenantId, Lists.newArrayList(allCreditFlowDetailIds), CreditFlowDetailConst.API_NAME);
            Map<String, IObjectData> allCreditFlowDetailDataMap = allCreditFlowDetailList.stream().collect(Collectors.toMap(DBRecord::getId, Function.identity()));

            List<IObjectData> toDeleteCreditFlowDataList = contextModel.getToDeleteCreditFlowDetailDataIds().stream().map(allCreditFlowDetailDataMap::get).collect(Collectors.toList());
            List<IObjectData> incrementUpdateCreditFlowDetailDataList = creditFlowDetailLeftCreditAmountUpdateFieldMap.keySet().stream().map(allCreditFlowDetailDataMap::get).collect(Collectors.toList());

            DataUpdateAndAddModel.Arg editArg = DataUpdateAndAddModel.Arg.create();
            editArg.appendInvalidData(toDeleteCreditTransactionFlowDataList, false, false);
            editArg.appendInvalidData(toDeleteCreditFlowDataList, true);
            editArg.appendAddData(ObjectDataDocument.ofDataList(contextModel.getToAddCreditFlowDetailDataList()));
            editArg.appendAddData(ObjectDataDocument.ofDataList(contextModel.getToAddCreditTransactionFlowDataList()));
            editArg.customerAccountColumnUpdateMap(contextModel.getCustomerAccountFieldUpdateColumnMap());
            editArg.appendIncrementUpdateFieldData(CreditFlowDetailConst.F.LeftCreditAmount.apiName, creditFlowDetailLeftCreditAmountUpdateFieldMap, incrementUpdateCreditFlowDetailDataList, true);
            newCustomerAccountManager.executeCredit(requestContext.getUser(), editArg);
        }
    }

    public boolean canInvalidOrReject(RequestContext requestContext, IObjectData objectData, IObjectData creditRuleMatchRecordData) {
        User user = requestContext.getUser();
        String objectApiName = objectData.getDescribeApiName();
        String objectDataId = objectData.getId();
        String creditOccupiedRuleId = creditRuleMatchRecordData.get(CreditRuleMatchRecordConst.F.CreditOccupiedRuleId.apiName, String.class);
        List<IObjectData> creditFlowDetailDataList = creditManager.findCreditFlowDetailByMasterData(user, creditOccupiedRuleId, objectApiName, objectDataId);

        if (CollectionUtils.empty(creditFlowDetailDataList)) {
            return true;
        }
        return creditFlowDetailDataList.stream().allMatch(creditFlowDetailData -> {
            BigDecimal creditAmount = creditFlowDetailData.get(CreditFlowDetailConst.F.CreditAmount.apiName, BigDecimal.class);
            BigDecimal leftCreditAmount = creditFlowDetailData.get(CreditFlowDetailConst.F.LeftCreditAmount.apiName, BigDecimal.class);
            return creditAmount.compareTo(leftCreditAmount) == 0;
        });
    }

    public CreditRuleInvalidContextModel doInvalidPreAct(RequestContext requestContext, IObjectData objectData) {
        CreditRuleInvalidContextModel contextModel = new CreditRuleInvalidContextModel();
        String objectApiName = objectData.getDescribeApiName();
        String objectDataId = objectData.getId();
        List<IObjectData> creditRuleMatchRecordList = creditManager.findCreditRuleMatchRecordByData(requestContext.getUser(), objectApiName, Sets.newHashSet(objectDataId));
        if (CollectionUtils.empty(creditRuleMatchRecordList)) {
            return contextModel;
        }
        IObjectData creditRuleMatchRecordData = creditRuleMatchRecordList.get(0);
        String creditOccupiedRuleId = creditRuleMatchRecordData.get(CreditRuleMatchRecordConst.F.CreditOccupiedRuleId.apiName, String.class);
        contextModel.setCreditRuleMatchRecordId(creditRuleMatchRecordData.getId());
        contextModel.setCreditOccupiedRuleId(creditOccupiedRuleId);
        if (canInvalidOrReject(requestContext, objectData, creditRuleMatchRecordData)) {
            CreditCurAndPreObjectConfig creditCurAndPreObjectConfig = CreditUtil.getCreditCurAndPreConfig(requestContext.getTenantId(), objectApiName, null);
            if (creditCurAndPreObjectConfig.setByDetail()) {
                contextModel.setDetailObjectApiName(creditCurAndPreObjectConfig.getDetailObjectApiName());
                SearchTemplateQuery query = SearchQueryBuilder.builder().eq(creditCurAndPreObjectConfig.getMasterFieldApiName(), objectDataId).build();
                List<IObjectData> detailDataList = serviceFacade.findBySearchQuery(requestContext.getUser(), creditCurAndPreObjectConfig.getDetailObjectApiName(), query).getData();
                contextModel.setDetailDataIds(detailDataList.stream().map(IObjectData::getId).collect(Collectors.toSet()));
            }
            return contextModel;
        } else {
            throw new ValidateException(CAI18NKey.CREDIT_FLOW_TRANSFERRED_NOT_SUPPORT_ACTION);
        }
    }

    public void doInvalidFinally(RequestContext requestContext, IObjectData objectData, CreditRuleInvalidContextModel contextModel) {
        String creditOccupiedRuleId = contextModel.getCreditOccupiedRuleId();
        String objectApiName = objectData.getDescribeApiName();
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        if (objectDataExt.isInvalid() && StringUtils.isNotEmpty(creditOccupiedRuleId)) {
            List<IObjectData> creditOccupiedRuleDetailList = creditManager.findCreditRuleDetailByMasterAndOrderBySeq(requestContext.getUser(), creditOccupiedRuleId);
            CreditRuleNodeTypeEnum creditRuleNodeTypeEnum = CreditUtil.getNodeType(objectApiName, creditOccupiedRuleDetailList);

            if (StringUtils.isNotEmpty(contextModel.getDetailObjectApiName())) {
                if (creditRuleNodeTypeEnum.firstNode()) {
                    doInvalidDataFirstNode(requestContext, objectDataExt);
                } else {
                    List<String> detailIds = Lists.newArrayList(CollectionUtils.nullToEmpty(contextModel.getDetailDataIds()));
                    List<IObjectData> detailDataList = serviceFacade.findObjectDataByIdsIncludeDeleted(requestContext.getUser(), detailIds, contextModel.getDetailObjectApiName());
                    doInvalidDataNotFirstNode(requestContext, objectDataExt, detailDataList);
                }
            }
        }
    }

    public final void doInvalidDataNotFirstNode(RequestContext requestContext, IObjectData objectData, List<IObjectData> detailDataList) {
        String objectApiName = objectData.getDescribeApiName();
        String objectDataId = objectData.getId();
        //查询信用规则匹配记录
        //查询信用流水
        //查询信用流水明细
        User user = requestContext.getUser();
        List<IObjectData> creditRuleMatchRecordList = creditManager.findCreditRuleMatchRecordByData(user, objectApiName, Sets.newHashSet(objectDataId));
        if (CollectionUtils.empty(creditRuleMatchRecordList)) {
            return;
        }
        DataUpdateAndAddModel.Arg updateAddArg = DataUpdateAndAddModel.Arg.create();
        IObjectData creditRuleMatchRecordData = creditRuleMatchRecordList.get(0);
        String creditOccupiedRuleId = creditRuleMatchRecordData.get(CreditRuleMatchRecordConst.F.CreditOccupiedRuleId.apiName, String.class);
        List<IObjectData> creditTransactionFlowList = creditManager.findCreditTransactionExpenseFlowByRelateObject(user, objectApiName, Sets.newHashSet(objectDataId));

        List<IObjectData> creditRuleDetailList = creditManager.findCreditRuleDetailByMasterAndOrderBySeq(requestContext.getUser(), creditOccupiedRuleId);
        String preCreditObject = CreditUtil.getPreObjectApiName(objectApiName, creditRuleDetailList);

        updateAddArg.appendInvalidData(creditRuleMatchRecordData, true);
        if (CollectionUtils.notEmpty(creditTransactionFlowList)) {
            IObjectData creditTransactionFlowData = creditTransactionFlowList.get(0);
            List<IObjectData> creditFlowDetailDataList = creditManager.findCreditFlowDetailByMasterData(user, creditOccupiedRuleId, objectApiName, objectDataId);
            //查询上一个节点的明细
            CreditCurAndPreObjectConfig creditCurAndPreObjectConfig = CreditUtil.getCreditCurAndPreConfig(requestContext.getTenantId(), objectApiName, preCreditObject);
            Set<String> preObjectDataIds = detailDataList.stream().map(x -> x.get(creditCurAndPreObjectConfig.getLookUpPreObjectFieldApiName(), String.class)).filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
            Map<String, IObjectData> detailDataMap = detailDataList.stream().collect(Collectors.toMap(DBRecord::getId, Function.identity()));

            String customerId = objectData.get(creditCurAndPreObjectConfig.getCustomerFieldApiName(), String.class);
            IObjectData creditCustomerAccountData = newCustomerAccountManager.findCreditCustomerAccountData(requestContext, customerId);
            String lookUpPreObjectApiName = creditCurAndPreObjectConfig.getPreDetailObjectApiName();
            List<IObjectData> preCreditFlowDetailList = creditManager.findCreditFlowDetailByRelateDetail(user, lookUpPreObjectApiName, preObjectDataIds, creditOccupiedRuleId);
            Map<String, IObjectData> preObjectDataIdCreditFlowDetailMap = preCreditFlowDetailList.stream().collect(Collectors.toMap(x -> x.get(CreditFlowDetailConst.F.RelateObjectDataId.apiName, String.class), Function.identity()));

            Map<String, BigDecimal> preObjectLeftCreditAmountChangeMap = Maps.newHashMap();
            for (IObjectData creditFlowDetailData : creditFlowDetailDataList) {
                String relateObjectDataId = creditFlowDetailData.get(CreditFlowDetailConst.F.RelateObjectDataId.apiName, String.class);
                BigDecimal creditAmount = creditFlowDetailData.get(CreditFlowDetailConst.F.CreditAmount.apiName, BigDecimal.class);
                IObjectData detailData = detailDataMap.get(relateObjectDataId);
                String lookUpPreObjectDataId = Objects.isNull(detailData) ? null : detailData.get(creditCurAndPreObjectConfig.getLookUpPreObjectFieldApiName(), String.class);
                if (StringUtils.isEmpty(lookUpPreObjectDataId)) {
                    continue;
                }
                BigDecimal changedAmount = preObjectLeftCreditAmountChangeMap.computeIfAbsent(lookUpPreObjectDataId, k -> BigDecimal.ZERO).add(creditAmount);
                preObjectLeftCreditAmountChangeMap.put(lookUpPreObjectDataId, changedAmount);
            }
            Map<String, BigDecimal> preMasterIdChangedTransactionAmountMap = Maps.newHashMap();
            Map<String, Map<String, Object>> preCreditFlowDetailChangedLeftAmountMap = Maps.newHashMap();
            List<IObjectData> updatePreCreditFlowDetailList = Lists.newArrayList();

            preObjectLeftCreditAmountChangeMap.forEach((preObjectDataId, preLeftCreditAmountChange) -> {
                IObjectData preCreditFlowDetailData = preObjectDataIdCreditFlowDetailMap.get(preObjectDataId);
                String preMasterDataId = preCreditFlowDetailData.get(CreditFlowDetailConst.F.RelateMasterDataId.apiName, String.class);
                BigDecimal changedExpenseAmount = preMasterIdChangedTransactionAmountMap.computeIfAbsent(preMasterDataId, k -> BigDecimal.ZERO);
                preMasterIdChangedTransactionAmountMap.put(preMasterDataId, changedExpenseAmount.add(preLeftCreditAmountChange));

                Map<String, Object> updateFieldMap = Maps.newHashMap();
                updateFieldMap.put(CreditFlowDetailConst.F.LeftCreditAmount.apiName, preLeftCreditAmountChange);
                preCreditFlowDetailChangedLeftAmountMap.put(preCreditFlowDetailData.getId(), updateFieldMap);
                updatePreCreditFlowDetailList.add(preCreditFlowDetailData);
            });

            List<IObjectData> preTransactionFlowList = creditManager.findCreditTransactionExpenseFlowByRelateObject(user, preCreditObject, preMasterIdChangedTransactionAmountMap.keySet());
            Map<String, IObjectData> preMasterDataTransactionFlowMap = preTransactionFlowList.stream().collect(Collectors.toMap(x -> x.get(AccountTransactionFlowConst.Field.RelateRecordObjectDataId.apiName, String.class), Function.identity()));
            preMasterIdChangedTransactionAmountMap.forEach((preMasterDataId, changedAmount) -> {
                IObjectData preMasterTransactionFlowData = preMasterDataTransactionFlowMap.get(preMasterDataId);
                IObjectData transactionFlowData;
                if (Objects.isNull(preMasterTransactionFlowData)) {
                    transactionFlowData = CreditUtil.generateCreditAccountTransactionFlow(user, preCreditObject, preMasterDataId, changedAmount, creditCustomerAccountData);
                    updateAddArg.appendAddData(transactionFlowData, false);
                } else {
                    BigDecimal expenseAmount = preMasterTransactionFlowData.get(AccountTransactionFlowConst.Field.ExpenseAmount.apiName, BigDecimal.class);
                    transactionFlowData = CreditUtil.generateTransactionFlowDataFromOtherWithAmount(user, preMasterTransactionFlowData, expenseAmount.add(changedAmount));
                    updateAddArg.transactionFlowCancelEntryAndInvalid(preMasterTransactionFlowData, true);
                    updateAddArg.appendAddData(transactionFlowData, false);
                }
            });
            updateAddArg.appendIncrementUpdateFieldData(CreditFlowDetailConst.F.LeftCreditAmount.apiName, preCreditFlowDetailChangedLeftAmountMap, updatePreCreditFlowDetailList, true);
            updateAddArg.appendInvalidData(creditFlowDetailDataList, true);
            updateAddArg.transactionFlowCancelEntryAndInvalid(creditTransactionFlowData, true);
        }
        newCustomerAccountManager.executeCredit(user, updateAddArg);
    }

    public final void doInvalidDataFirstNode(RequestContext requestContext, IObjectData objectData) {
        String objectApiName = objectData.getDescribeApiName();
        String objectDataId = objectData.getId();
        //查询信用规则匹配记录
        //查询信用流水
        //查询信用流水明细
        User user = requestContext.getUser();
        List<IObjectData> creditRuleMatchRecordList = creditManager.findCreditRuleMatchRecordByData(user, objectApiName, Sets.newHashSet(objectDataId));
        if (CollectionUtils.empty(creditRuleMatchRecordList)) {
            return;
        }
        DataUpdateAndAddModel.Arg updateAddArg = DataUpdateAndAddModel.Arg.create();
        IObjectData creditRuleMatchRecordData = creditRuleMatchRecordList.get(0);
        updateAddArg.appendInvalidData(creditRuleMatchRecordData, true);
        String creditOccupiedRuleId = creditRuleMatchRecordData.get(CreditRuleMatchRecordConst.F.CreditOccupiedRuleId.apiName, String.class);
        List<IObjectData> creditTransactionFlowList = creditManager.findCreditTransactionExpenseFlowByRelateObject(user, objectApiName, Sets.newHashSet(objectDataId));
        if (CollectionUtils.notEmpty(creditTransactionFlowList)) {
            IObjectData creditTransactionFlowData = creditTransactionFlowList.get(0);
            String customerAccountId = creditTransactionFlowData.get(AccountTransactionFlowConst.Field.CustomerAccount.apiName, String.class);
            BigDecimal expenseAmount = creditTransactionFlowData.get(AccountTransactionFlowConst.Field.ExpenseAmount.apiName, BigDecimal.class, BigDecimal.ZERO);
            Map<String, Map<String, Object>> customerAcountColumnMap = Maps.newHashMap();
            ObjectDataUtil.incrementUpdateCustomerAccountUpdateField(customerAcountColumnMap, customerAccountId, NewCustomerAccountConstants.Field.AccountBalance.apiName, expenseAmount);
            ObjectDataUtil.incrementUpdateCustomerAccountUpdateField(customerAcountColumnMap, customerAccountId, NewCustomerAccountConstants.Field.CreditOccupiedAmount.apiName, expenseAmount.negate());
            updateAddArg.customerAccountColumnUpdateMap(customerAcountColumnMap);

            List<IObjectData> creditFlowDetailDataList = creditManager.findCreditFlowDetailByMasterData(user, creditOccupiedRuleId, objectApiName, objectDataId);
            updateAddArg.appendInvalidData(creditFlowDetailDataList, true);
            IObjectData creditTransactionFlowDataCopy = ObjectDataExt.of(creditTransactionFlowData).copy();
            creditTransactionFlowDataCopy.set(AccountTransactionFlowConst.Field.EntryStatus.apiName, EntryStatusEnum.Cancelled.getValue());
            updateAddArg.appendUpdateData(AccountTransactionFlowConst.Field.EntryStatus.apiName, creditTransactionFlowData, creditTransactionFlowDataCopy, true, true);
        }
        newCustomerAccountManager.executeCredit(user, updateAddArg);
    }

}
