package com.facishare.crm.customeraccount.predefine.job;

import com.facishare.crm.customeraccount.constants.CreditFileConstants;
import com.facishare.crm.customeraccount.constants.CustomerAccountConstants;
import com.facishare.crm.customeraccount.constants.SystemConstants;
import com.facishare.crm.customeraccount.entity.CustomerAccountBill;
import com.facishare.crm.customeraccount.enums.BillTypeEnum;
import com.facishare.crm.customeraccount.enums.CreditTypeEnum;
import com.facishare.crm.customeraccount.predefine.manager.CustomerAccountBillManager;
import com.facishare.crm.customeraccount.predefine.manager.CustomerAccountConfigManager;
import com.facishare.crm.customeraccount.predefine.manager.CustomerAccountManager;
import com.facishare.crm.customeraccount.predefine.service.dto.CustomerAccountType;
import com.facishare.crm.customeraccount.util.CustomerAccountRecordLogger;
import com.facishare.crm.customeraccount.util.DateUtil;
import com.facishare.crm.customeraccount.util.ObjectDataUtil;
import com.facishare.crmcommon.util.SearchUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.math.IntMath;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * Created on 2018/7/20.
 */
@Slf4j
public class CreditFileDetectionJob implements Job {

    private final List<String> customerAccountFields = Lists.newArrayList(CustomerAccountConstants.Field.CreditQuota.apiName, CustomerAccountConstants.Field.CreditTemporaryQuota.apiName, CustomerAccountConstants.Field.CreditPeriod.apiName, CustomerAccountConstants.Field.CreditAvailableQuota.apiName, SystemConstants.Field.LastModifiedBy.apiName, SystemConstants.Field.LastModifiedTime.apiName);

    /**
     * 信用档案巡检任务
     *
     * @param jobExecutionContext
     * @throws JobExecutionException
     */
    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        log.info("start CreditFileDetectionJob");
        CustomerAccountManager customerAccountManager = SpringUtil.getContext().getBean(CustomerAccountManager.class);
        CustomerAccountBillManager customerAccountBillManager = SpringUtil.getContext().getBean(CustomerAccountBillManager.class);
        ServiceFacade serviceFacade = SpringUtil.getContext().getBean(ServiceFacade.class);
        IObjectDescribeService objectDescribeService = SpringUtil.getContext().getBean(IObjectDescribeService.class);
        CustomerAccountConfigManager customerAccountConfigManager = SpringUtil.getContext().getBean(CustomerAccountConfigManager.class);

        // STEP1: 查询所有开启客户账户的企业
        List<String> tenantIds = customerAccountConfigManager.list(CustomerAccountType.CustomerAccountEnableSwitchStatus.ENABLE.getValue());

        // STEP2: 循环并查找所有状态为normal的信用档案，根据日期是否过期更新相应的客户档数据
        for (String tenantId : tenantIds) {
            User user = User.builder().tenantId(tenantId).userId(User.SUPPER_ADMIN_USER_ID).build();

            // 忽略非灰度企业 TODO: 全网上线后会移除
            try {
                IObjectDescribe describe = objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, CreditFileConstants.API_NAME);
                if (describe == null) {
                    log.warn("No CreditFileObj, corpId={}", tenantId);
                    continue;
                }
                log.info("CreditFileObj task,corpId:{}", tenantId);
            } catch (MetadataServiceException e) {
                log.warn("query CreditFileObj failed, corpId={}", tenantId);
                continue;
            }

            List<IObjectData> totalCreditFiles = queryCreditFiles(user, customerAccountManager);
            if (totalCreditFiles == null) {
                log.warn("query failed, corpId={}", tenantId);
                continue;
            }
            if (totalCreditFiles.isEmpty()) {
                log.warn("no creditFiles, corpId={}", tenantId);
                continue;
            }

            Map<String, List<IObjectData>> accountCreditFileMapping = Maps.newLinkedHashMap();
            Set<String> customerIds = Sets.newHashSet();
            for (IObjectData creditFile : totalCreditFiles) {
                String customerId = creditFile.get(CreditFileConstants.Field.Customer.apiName, String.class);

                customerIds.add(customerId);
                if (accountCreditFileMapping.containsKey(customerId)) {
                    accountCreditFileMapping.get(customerId).add(creditFile);
                    continue;
                }
                accountCreditFileMapping.put(customerId, Lists.newArrayList(creditFile));
            }

            // 包括已作废的客户账户
            List<IObjectData> customerAccounts = customerAccountManager.listCustomerAccountOnlyIncludeInvalidByCustomerIds(user, Lists.newArrayList(customerIds));
            Map<String, IObjectData> customerAccountMapping = Maps.newHashMap();
            for (IObjectData customerAccount : customerAccounts) {
                String customerId = customerAccount.get(CustomerAccountConstants.Field.Customer.apiName, String.class);
                customerAccountMapping.put(customerId, customerAccount);
            }

            List<IObjectData> toBeUpdatedCustomerAccounts = Lists.newArrayList();
            List<IObjectData> toBeUpdatedInvalidCustomerAccounts = Lists.newArrayList();
            List<CustomerAccountBill> customerAccountBills = Lists.newArrayList();
            customerAccountMapping.entrySet().forEach(entry -> {
                String customerId = entry.getKey();
                IObjectData customerAccount = entry.getValue();
                List<IObjectData> creditFiles = accountCreditFileMapping.get(customerId);

                BigDecimal creditQuota = ObjectDataUtil.getBigDecimal(customerAccount, CustomerAccountConstants.Field.CreditQuota.apiName);
                BigDecimal creditTemporaryQuota = ObjectDataUtil.getBigDecimal(customerAccount, CustomerAccountConstants.Field.CreditTemporaryQuota.apiName);
                Integer creditPeriod = getInteger(customerAccount, CustomerAccountConstants.Field.CreditPeriod.apiName);
                BigDecimal creditAvailableQuota = ObjectDataUtil.getBigDecimal(customerAccount, CustomerAccountConstants.Field.CreditAvailableQuota.apiName);
                String lifeStatus = customerAccount.get(SystemConstants.Field.LifeStatus.apiName, String.class);

                boolean isChanged = false;
                for (IObjectData creditFile : creditFiles) {
                    BigDecimal quota = ObjectDataUtil.getBigDecimal(creditFile, CreditFileConstants.Field.CreditQuota.apiName);
                    BigDecimal temporaryQuota = ObjectDataUtil.getBigDecimal(creditFile, CreditFileConstants.Field.TemporaryCreditLimit.apiName);
                    Integer period = getInteger(creditFile, CreditFileConstants.Field.CreditPeriod.apiName);
                    String creditType = creditFile.get(CreditFileConstants.Field.CreditType.apiName, String.class);
                    Long startTime = creditFile.get(CreditFileConstants.Field.StartTime.apiName, Long.class);
                    Long endTime = creditFile.get(CreditFileConstants.Field.EndTime.apiName, Long.class) + CreditFileConstants.ONE_DAY_TIME;
                    String info = CustomerAccountRecordLogger.generateCreditInfo(creditFile.getId(), SystemConstants.LifeStatus.Normal.value, SystemConstants.LifeStatus.Normal.value);
                    BigDecimal creditQuotaAmount = BigDecimal.ZERO;

                    if (endTime < System.currentTimeMillis()) {
                        // 该信用档案已过期且需要更新
                        isChanged = true;
                        if (CreditTypeEnum.OfficialCredit.getValue().equals(creditType)) {
                            creditQuota = creditQuota.subtract(quota);
                            creditAvailableQuota = creditAvailableQuota.subtract(quota);
                            creditPeriod = creditPeriod - period;

                            creditQuotaAmount = quota.negate();
                        } else if (CreditTypeEnum.TemporaryCredit.getValue().equals(creditType)) {
                            creditTemporaryQuota = creditTemporaryQuota.subtract(temporaryQuota);
                            creditAvailableQuota = creditAvailableQuota.subtract(temporaryQuota);

                            creditQuotaAmount = temporaryQuota.negate();
                        }
                        info = info + ",expire";
                        CustomerAccountRecordLogger.logCredit(customerAccount.getId(), creditQuotaAmount, creditAvailableQuota, info, null);
                        addCustomerAccountBills(customerAccountBills, tenantId, customerAccount.getId(), creditFile.getId(), creditQuotaAmount.doubleValue(), info);
                    } else if (isCurrentDay(startTime)) {
                        // 该信用档案第一次匹配启用
                        isChanged = true;
                        if (CreditTypeEnum.OfficialCredit.getValue().equals(creditType)) {
                            creditQuota = creditQuota.add(quota);
                            creditAvailableQuota = creditAvailableQuota.add(quota);
                            creditPeriod = creditPeriod + period;

                            creditQuotaAmount = quota;
                        } else if (CreditTypeEnum.TemporaryCredit.getValue().equals(creditType)) {
                            creditTemporaryQuota = creditTemporaryQuota.add(temporaryQuota);
                            creditAvailableQuota = creditAvailableQuota.add(temporaryQuota);

                            creditQuotaAmount = temporaryQuota;
                        }
                        info = info + ",effective";
                        CustomerAccountRecordLogger.logCredit(customerAccount.getId(), creditQuotaAmount, creditAvailableQuota, info, null);
                        addCustomerAccountBills(customerAccountBills, tenantId, customerAccount.getId(), creditFile.getId(), creditQuotaAmount.doubleValue(), info);
                    }
                }

                if (isChanged) {
                    customerAccount.setLastModifiedBy(user.getUserId());
                    customerAccount.setLastModifiedTime(System.currentTimeMillis());
                    customerAccount.set(CustomerAccountConstants.Field.CreditQuota.apiName, creditQuota);
                    customerAccount.set(CustomerAccountConstants.Field.CreditTemporaryQuota.apiName, creditTemporaryQuota);
                    customerAccount.set(CustomerAccountConstants.Field.CreditPeriod.apiName, creditPeriod);
                    customerAccount.set(CustomerAccountConstants.Field.CreditAvailableQuota.apiName, creditAvailableQuota);

                    if (SystemConstants.LifeStatus.Invalid.value.equals(lifeStatus)) {
                        toBeUpdatedInvalidCustomerAccounts.add(customerAccount);
                    } else {
                        toBeUpdatedCustomerAccounts.add(customerAccount);
                    }
                }
            });

            batchUpdate(user, toBeUpdatedCustomerAccounts, customerAccountFields, serviceFacade, customerAccountManager);
            batchUpdateInvalid(user, toBeUpdatedInvalidCustomerAccounts, serviceFacade, customerAccountManager);
            batchUpdateCustomerAccountBills(customerAccountBills, customerAccountBillManager);
        }
    }

    /**
     * 获取Integer类型的数据
     * @param objectData
     * @param fieldApiName
     * @return
     */
    private Integer getInteger(IObjectData objectData, String fieldApiName) {
        Integer value = objectData.get(fieldApiName, Integer.class);
        return value == null ? 0 : value;
    }

    /**
     * 查询某一企业下所有客户当天即将失效或生效的信用档案
     * @param user
     * @param customerAccountManager
     * @return
     */
    private List<IObjectData> queryCreditFiles(User user, CustomerAccountManager customerAccountManager) {
        List<IFilter> filters = Lists.newArrayList();
        List<OrderBy> orders = Lists.newArrayList();

        SearchUtil.fillFilterEq(filters, SystemConstants.Field.LifeStatus.apiName, SystemConstants.LifeStatus.Normal.value);
        SearchUtil.fillFilterEq(filters, CreditFileConstants.Field.EndTime.apiName, DateUtil.getYesterdayBeginTime());
        SearchUtil.fillFilterLT(filters, SystemConstants.Field.CreateTime.apiName, DateUtil.getNowBeginTime()); //忽略任务调度期间产生的数据, 避免重复计算
        SearchUtil.fillOrderBy(orders, CreditFileConstants.Field.Customer.apiName, true);
        SearchUtil.fillOrderBy(orders, CreditFileConstants.Field.StartTime.apiName, true);

        List<IObjectData> toBeInvalidCreditFiles = paginationQuery(user, filters, orders, customerAccountManager);
        if (toBeInvalidCreditFiles == null) {
            return null;
        }

        filters.clear();
        SearchUtil.fillFilterEq(filters, SystemConstants.Field.LifeStatus.apiName, SystemConstants.LifeStatus.Normal.value);
        SearchUtil.fillFilterEq(filters, CreditFileConstants.Field.StartTime.apiName, DateUtil.getNowBeginTime());
        SearchUtil.fillFilterLT(filters, SystemConstants.Field.CreateTime.apiName, DateUtil.getNowBeginTime()); //忽略任务调度期间产生的数据, 避免重复计算
        List<IObjectData> toBeValidCreditFiles = paginationQuery(user, filters, orders, customerAccountManager);
        if (toBeValidCreditFiles == null) {
            return null;
        }
        toBeInvalidCreditFiles.addAll(toBeValidCreditFiles);
        return toBeInvalidCreditFiles;
    }

    /**
     * 分页查询
     * @param user
     * @param filters
     * @param orders
     * @param customerAccountManager
     * @return
     */
    private List<IObjectData> paginationQuery(User user, List<IFilter> filters, List<OrderBy> orders, CustomerAccountManager customerAccountManager) {
        int offset = 0;
        int limit = 500;

        QueryResult<IObjectData> creditFilesResult = customerAccountManager.searchQuery(user, CreditFileConstants.API_NAME, filters, orders, offset, limit);

        int totalNumber = creditFilesResult.getTotalNumber();
        if (totalNumber == 0) {
            return Lists.newArrayList();
        }

        List<IObjectData> creditFiles = creditFilesResult.getData();
        int batchNumber = IntMath.divide(totalNumber, limit, RoundingMode.UP);
        for (int index = 1; index < batchNumber; index++) {
            offset = index * limit;

            creditFilesResult = customerAccountManager.searchQuery(user, CreditFileConstants.API_NAME, filters, orders, offset, limit);
            if (creditFilesResult.getTotalNumber() == 0) {
                return null;
            }
            creditFiles.addAll(creditFilesResult.getData());
        }

        return creditFiles;
    }

    /**
     * 是否当天
     * @param dateTime
     * @return
     */
    private boolean isCurrentDay(long dateTime) {
        Date specifiedTime = new Date(dateTime);
        return DateUtils.isSameDay(specifiedTime, new Date());
    }

    /**
     * 添加账户流水-信用
     * @param customerAccountBills
     * @param tenantId
     * @param customerAccountId
     * @param creditFileId
     * @param creditQuotaAmount
     * @param info
     */
    private void addCustomerAccountBills(List<CustomerAccountBill> customerAccountBills, String tenantId, String customerAccountId, String creditFileId, double creditQuotaAmount, String info) {
        CustomerAccountBill bill = new CustomerAccountBill();
        bill.setTenantId(tenantId);
        bill.setCustomerAccountId(customerAccountId);
        bill.setRelateType(BillTypeEnum.Credit.getType());
        bill.setRelateId(creditFileId);
        bill.setCreditAvailableQuotaChange(creditQuotaAmount);
        bill.setBillDate(new Date());
        bill.setRemark(info);
        bill.setCreateTime(new Date());

        customerAccountBills.add(bill);
    }

    /**
     * 批量更新，并写入审计日志
     * @param user
     * @param dataList
     * @param updateFieldList
     * @param serviceFacade
     * @param customerAccountManager
     */
    private void batchUpdate(User user, List<IObjectData> dataList, List<String> updateFieldList, ServiceFacade serviceFacade, CustomerAccountManager customerAccountManager) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }

        int fromIndex = 0;
        int toIndex = 0;
        int limit = 500;

        int totalNumber = dataList.size();
        int batchNumber = IntMath.divide(totalNumber, limit, RoundingMode.UP);
        int leftNumber = totalNumber % limit;

        for (int index = 0; index < batchNumber; index++) {
            fromIndex = index * limit;
            toIndex = fromIndex + limit;
            if (index == batchNumber - 1 && leftNumber > 0) {
                toIndex = fromIndex + leftNumber;
            }

            List<IObjectData> subDataList = dataList.subList(fromIndex, toIndex);
            serviceFacade.batchUpdateByFields(user, subDataList, updateFieldList);
            customerAccountManager.recordLogs(user, subDataList);
        }
    }

    /**
     * 批量更新作废数据，并写入审计日志
     * @param user
     * @param dataList
     * @param serviceFacade
     * @param customerAccountManager
     */
    private void batchUpdateInvalid(User user, List<IObjectData> dataList, ServiceFacade serviceFacade, CustomerAccountManager customerAccountManager) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }

        for (IObjectData objectData : dataList) {
            serviceFacade.updateObjectData(user, objectData, true);
            customerAccountManager.recordLog(user, objectData);
        }
    }

    /**
     * 批量插入信用-客户账户流水
     * @param customerAccountBills
     * @param customerAccountBillManager
     */
    private void batchUpdateCustomerAccountBills(List<CustomerAccountBill> customerAccountBills, CustomerAccountBillManager customerAccountBillManager) {
        if (CollectionUtils.isEmpty(customerAccountBills)) {
            return;
        }

        int fromIndex = 0;
        int toIndex = 0;
        int limit = 500;

        int totalNumber = customerAccountBills.size();
        int batchNumber = IntMath.divide(totalNumber, limit, RoundingMode.UP);
        int leftNumber = totalNumber % limit;

        for (int index = 0; index < batchNumber; index++) {
            fromIndex = index * limit;
            toIndex = fromIndex + limit;
            if (index == batchNumber - 1 && leftNumber > 0) {
                toIndex = fromIndex + leftNumber;
            }

            List<CustomerAccountBill> subBills = customerAccountBills.subList(fromIndex, toIndex);
            customerAccountBillManager.batchAddCustomerAccountBillAccordCredit(subBills);
        }
    }

}
