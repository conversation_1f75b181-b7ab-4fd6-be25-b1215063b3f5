package com.facishare.crm.customeraccount.enums;

import com.facishare.crm.customeraccount.constants.AccountFrozenRecordConstant;
import com.facishare.crm.customeraccount.constants.AccountTransactionFlowConst;

import java.util.Optional;

public enum ChargeOffTypeEnum {
    FROZEN(AccountFrozenRecordConstant.API_NAME, "1"),
    /**
     * 仅支持了对象入账流水的红冲
     */
    INCOME_FLOW(AccountTransactionFlowConst.API_NAME, "2"),
    /**
     * 仅支持组件扣减、直接扣减支出流水的红冲
     */
    OUTCOME_FLOW(AccountTransactionFlowConst.API_NAME, "3");

    public final String chargeOffObjectApiName;
    public final String type;

    ChargeOffTypeEnum(String chargeOffObjectApiName, String type) {
        this.chargeOffObjectApiName = chargeOffObjectApiName;
        this.type = type;
    }

    public static Optional<ChargeOffTypeEnum> of(String type) {
        for (ChargeOffTypeEnum chargeOffTypeEnum : values()) {
            if (chargeOffTypeEnum.type.equals(type)) {
                return Optional.of(chargeOffTypeEnum);
            }
        }
        return Optional.empty();
    }
}
