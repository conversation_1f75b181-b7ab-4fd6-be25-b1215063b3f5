package com.facishare.crm.customeraccount.predefine.domainplugin.processor.credit;

import com.facishare.crm.customeraccount.constants.NewCustomerAccountConstants;
import com.facishare.crm.customeraccount.enums.StageEnum;
import com.facishare.crm.customeraccount.predefine.domainplugin.model.CreditRuleIncrementUpdateContextModel;
import com.facishare.crm.customeraccount.predefine.manager.CreditManager;
import com.facishare.crm.customeraccount.predefine.manager.NewCustomerAccountManager;
import com.facishare.crm.customeraccount.predefine.service.dto.CreditCurAndPreObjectConfig;
import com.facishare.crm.customeraccount.util.CreditUtil;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.predef.domain.IncrementUpdateActionDomainPlugin;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.MasterDetail;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
public class IncrementUpdateCreditRuleProcessor extends CreditRuleByDetailProcessor<IncrementUpdateActionDomainPlugin.Arg, IncrementUpdateActionDomainPlugin.Result, CreditRuleIncrementUpdateContextModel> {
    public IncrementUpdateCreditRuleProcessor(NewCustomerAccountManager newCustomerAccountManager, CreditManager creditManager, ServiceFacade serviceFacade) {
        super(newCustomerAccountManager, creditManager, serviceFacade);
    }

    @Override
    protected CreditRulePluginContextKey getContextKey() {
        return CreditRulePluginContextKey.IncrementUpdate;
    }

    @Override
    protected Class<CreditRuleIncrementUpdateContextModel> getContextClass() {
        return CreditRuleIncrementUpdateContextModel.class;
    }

    @Override
    protected IncrementUpdateActionDomainPlugin.Result newResultInstance() {
        return new IncrementUpdateActionDomainPlugin.Result();
    }

    @Override
    protected IObjectData getObjectData(IncrementUpdateActionDomainPlugin.Arg arg) {
        return arg.getObjectData().toObjectData();
    }

    @Override
    protected List<IObjectData> getDetailDataList(IncrementUpdateActionDomainPlugin.Arg arg, String detailApiName) {
        return Lists.newArrayList();
    }

    @Override
    protected CreditRuleIncrementUpdateContextModel doPreAct(RequestContext requestContext, IncrementUpdateActionDomainPlugin.Arg arg) {
        CreditRuleIncrementUpdateContextModel contextModel = new CreditRuleIncrementUpdateContextModel();
        String tenantId = requestContext.getTenantId();
        String objectApiName = arg.getObjectApiName();
        IObjectData objectData = arg.getObjectData().toObjectData();
        IObjectDescribe objectDescribe = serviceFacade.findObject(tenantId, objectApiName);
        if (ObjectDescribeExt.of(objectDescribe).isSlaveObject()) {
            //编辑从对象
            String masterObjectApiName = ObjectDescribeExt.of(objectDescribe).getMasterDetailField().map(MasterDetail::getTargetApiName).orElse(null);
            CreditCurAndPreObjectConfig creditCurAndPreObjectConfig = CreditUtil.getCreditCurAndPreConfig(tenantId, masterObjectApiName, null);
            if (Objects.isNull(creditCurAndPreObjectConfig) || !creditCurAndPreObjectConfig.setByDetail() || !StringUtils.equals(objectApiName, creditCurAndPreObjectConfig.getDetailObjectApiName())) {
                return contextModel;
            }
            //编辑从对象，主对象为按从对象设置模式
            String masterFieldApiName = creditCurAndPreObjectConfig.getMasterFieldApiName();
            String masterDataId = objectData.get(masterFieldApiName, String.class);
            String customerField = creditCurAndPreObjectConfig.getCustomerFieldApiName();
            IObjectData masterData = serviceFacade.findObjectData(requestContext.getUser(), masterDataId, masterObjectApiName);
            String customerId = masterData.get(customerField, String.class);
            if (StringUtils.isEmpty(customerId)) {
                return contextModel;
            }
            IObjectData customerAccountData = newCustomerAccountManager.findCreditCustomerAccountData(requestContext, customerId);
            return CreditRuleIncrementUpdateByDetailCalculator.builder().requestContext(requestContext).masterData(masterData).objectData(objectData)
                    .customerAccountData(customerAccountData).newCustomerAccountManager(newCustomerAccountManager).creditManager(creditManager).serviceFacade(serviceFacade).stageEnum(StageEnum.Pre).build().compute();
        } else {
            //编辑主对象
            CreditCurAndPreObjectConfig creditCurAndPreObjectConfig = CreditUtil.getCreditCurAndPreConfig(tenantId, objectApiName, null);
            if (Objects.isNull(creditCurAndPreObjectConfig) || creditCurAndPreObjectConfig.setByDetail()) {
                return contextModel;
            }
            //TODO 编辑主对象时，按主对象设置
        }
        return contextModel;
    }

    @Override
    public void doFinallyDo(RequestContext requestContext, IncrementUpdateActionDomainPlugin.Arg arg, CreditRuleIncrementUpdateContextModel contextModel) {
        IObjectData objectData = arg.getObjectData().toObjectData();
        if (BooleanUtils.isTrue(contextModel.getSetByDetail())) {
            //编辑从对象，主对象为按从对象设置模式
            String masterObjectApiName = contextModel.getMasterObjectApiName();
            String masterDataId = contextModel.getMasterDataId();
            String customerId = contextModel.getCustomerId();
            if (StringUtils.isEmpty(customerId)) {
                return;
            }
            IObjectData masterData = serviceFacade.findObjectData(requestContext.getUser(), masterDataId, masterObjectApiName);
            IObjectData customerAccountData = serviceFacade.findObjectData(requestContext.getUser(), contextModel.getCreditCustomerAccountId(), NewCustomerAccountConstants.API_NAME);
            IObjectData actualObjectData = arg.isDoActComplete() ? objectData : arg.getDbObjectData().toObjectData();
            CreditRuleIncrementUpdateByDetailCalculator.builder().requestContext(requestContext).masterData(masterData).objectData(actualObjectData)
                    .customerAccountData(customerAccountData).newCustomerAccountManager(newCustomerAccountManager).creditManager(creditManager).serviceFacade(serviceFacade).stageEnum(StageEnum.POST).build().compute();
        } else {
            //TODO 主对象设置模式
        }
    }
}
