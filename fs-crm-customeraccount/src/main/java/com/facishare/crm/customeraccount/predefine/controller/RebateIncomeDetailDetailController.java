package com.facishare.crm.customeraccount.predefine.controller;

import com.facishare.crm.customeraccount.constants.RebateIncomeDetailConstants;
import com.facishare.crm.customeraccount.util.ButtonUtil;
import com.facishare.paas.appframework.core.predef.controller.StandardDetailController;

import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class RebateIncomeDetailDetailController extends StandardDetailController {

    @Override
    protected Result after(StandardDetailController.Arg arg, StandardDetailController.Result result) {
        result = super.after(arg, result);
        ButtonUtil.removeFieldsFromDetailLayout(arg, result, Sets.newHashSet(RebateIncomeDetailConstants.Field.CustomerAccount.apiName));
        return result;
    }
}
