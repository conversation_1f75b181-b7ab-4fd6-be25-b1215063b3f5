package com.facishare.crm.customeraccount.predefine.action;

import com.facishare.crm.consts.CreditRuleConst;
import com.facishare.crm.customeraccount.constants.CAI18NKey;
import com.facishare.crm.customeraccount.predefine.manager.CreditManager;
import com.facishare.crmcommon.util.DataUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardIncrementUpdateAction;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Sets;

import java.util.Set;

public class CreditRuleIncrementUpdateAction extends StandardIncrementUpdateAction {
    private final CreditManager creditManager = SpringUtil.getContext().getBean(CreditManager.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        boolean creditRuleUsed = creditManager.creditOccupiedRuleUsed(actionContext.getUser(), this.objectData.getId());
        if (creditRuleUsed) {
            Set<String> notSupportEditFields = Sets.newHashSet(CreditRuleConst.F.Status.apiName);
            notSupportEditFields.forEach(fieldName->{
                if (DataUtil.fieldChanged(this.objectDescribe,this.dbObjectData,this.objectData,fieldName)) {
                    throw new ValidateException(I18N.text(CAI18NKey.CREDIT_RULE_USED_NOT_EDIT));
                }
            });
        }
    }
}
