package com.facishare.crm.customeraccount.predefine.domainplugin.model;

import com.facishare.crm.customeraccount.constants.AccountTransactionFlowConst;
import com.facishare.crm.customeraccount.enums.EntryStatusEnum;
import com.facishare.crm.customeraccount.model.DataUpdateAndAddModel;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.Builder;
import lombok.Getter;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Getter
@Builder
public class DirectReduceRollbackResult {
    private IObjectData accountRuleUseRecordData;
    private List<IObjectData> toDeleteFlowDataList;
    private Map<String, Map<String, Object>> customerAccountUpdateColumnMap;

    public void mergeTo(DataUpdateAndAddModel.Arg arg, boolean needInvalidFlowAfterCancel,boolean needTriggerInvalidFunction) {
        CollectionUtils.nullToEmpty(toDeleteFlowDataList).forEach(flowData -> {
            IObjectData flowDataCopy = ObjectDataExt.of(flowData);
            flowDataCopy.set(AccountTransactionFlowConst.Field.EntryStatus.apiName, EntryStatusEnum.Cancelled.getValue());
            arg.appendUpdateData(AccountTransactionFlowConst.Field.EntryStatus.apiName, flowData, flowDataCopy, needInvalidFlowAfterCancel, needTriggerInvalidFunction);
        });
        if (Objects.nonNull(accountRuleUseRecordData)) {
            arg.appendInvalidData(accountRuleUseRecordData, true);
        }
        arg.customerAccountColumnUpdateMap(customerAccountUpdateColumnMap);
    }
}
