package com.facishare.crm.customeraccount.predefine.handler.checkrule;

import com.facishare.crm.customeraccount.constants.AccountFrozenRecordConstant;
import com.facishare.crm.customeraccount.constants.AccountTransactionFlowConst;
import com.facishare.crm.customeraccount.constants.UnfreezeDetailConstant;
import com.facishare.crm.customeraccount.enums.EntryStatusEnum;
import com.facishare.crm.customeraccount.model.DataUpdateAndAddModel;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.common.MapUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Data
@EqualsAndHashCode(callSuper = true)
public class AdaptEditResult extends HandlerResult {
    private Map<String, Map<String, Object>> customerAccountHandleMap = Maps.newHashMap();
    private Map<String, IObjectData> customerAccountDataMap = Maps.newHashMap();

    private List<IObjectData> toAddFrozenDataList = Lists.newArrayList();
    private List<IObjectData> toAddUnfreezeDataList = Lists.newArrayList();
    private List<IObjectData> toAddFlowDataList = Lists.newArrayList();
    private List<IObjectData> toDeletedFrozenDataList = Lists.newArrayList();
    private List<IObjectData> toDeletedUnfreezeDataList = Lists.newArrayList();
    private List<IObjectData> toDeletedFlowDataList = Lists.newArrayList();

    private boolean changed;

    public boolean changed() {
        return changed;
    }

    public void merge(AmountChangerResult result) {
        toAddFrozenDataList.addAll(result.getToAddFrozenDataList());
        toAddFlowDataList.addAll(result.getToAddFlowDataList());
        toAddUnfreezeDataList.addAll(result.getToAddUnfreezeDataList());
        toDeletedFrozenDataList.addAll(result.getToDeletedFrozenDataList());
        toDeletedFlowDataList.addAll(result.getToDeletedFlowDataList());
        toDeletedUnfreezeDataList.addAll(result.getToDeletedUnfreezeDataList());

        Map<String, Object> fieldColumnMap = result.getCustomerAccountColumnMap();
        if (Objects.nonNull(result.getCustomerAccountData()) && !MapUtils.isNullOrEmpty(fieldColumnMap)) {
            IObjectData customerAccountData = result.getCustomerAccountData();
            customerAccountDataMap.put(customerAccountData.getId(), customerAccountData);
            Map<String, Object> map = customerAccountHandleMap.computeIfAbsent(customerAccountData.getId(), k -> Maps.newHashMap());
            fieldColumnMap.forEach((field, n) -> {
                BigDecimal amount = (BigDecimal) n;
                BigDecimal oldAmount = (BigDecimal) map.get(field);
                if (Objects.isNull(oldAmount)) {
                    oldAmount = BigDecimal.ZERO;
                }
                map.put(field, oldAmount.add(amount));
            });
        }

        if (result.isActualChanged()) {
            changed = true;
        }
    }

    public void mergeTo(DataUpdateAndAddModel.Arg arg, boolean needInvalidFlowAfterCancel) {
        if (Objects.isNull(arg)) {
            return;
        }
        arg.customerAccountColumnUpdateMap(this.customerAccountHandleMap);
        arg.mergeCustomerAccountData(this.customerAccountDataMap);
        arg.appendAddData(toAddFrozenDataList, false);
        arg.appendAddData(toAddFlowDataList, false);
        arg.appendAddData(toAddUnfreezeDataList, false);
        if (CollectionUtils.notEmpty(toDeletedFrozenDataList)) {
            List<IObjectData> updateBeforeFrozenList = toDeletedFrozenDataList.stream().map(x -> {
                IObjectData copy = ObjectDataExt.of(x).copy();
                x.set(AccountFrozenRecordConstant.Field.EntryStatus.apiName, EntryStatusEnum.Cancelled.getValue());
                return copy;
            }).collect(Collectors.toList());
            arg.appendUpdateData(AccountFrozenRecordConstant.Field.EntryStatus.apiName, updateBeforeFrozenList, toDeletedFrozenDataList, true);
        }
        if (CollectionUtils.notEmpty(toDeletedFlowDataList)) {
            List<IObjectData> updateBeforeFlowList = toDeletedFlowDataList.stream().map(x -> {
                IObjectData copy = ObjectDataExt.of(x).copy();
                x.set(AccountTransactionFlowConst.Field.EntryStatus.apiName, EntryStatusEnum.Cancelled.getValue());
                return copy;
            }).collect(Collectors.toList());
            arg.appendUpdateData(AccountTransactionFlowConst.Field.EntryStatus.apiName, updateBeforeFlowList, toDeletedFlowDataList, needInvalidFlowAfterCancel);
        }
        if (CollectionUtils.notEmpty(toDeletedUnfreezeDataList)) {
            List<IObjectData> updateBeforeUnfreezeList = toDeletedUnfreezeDataList.stream().map(x -> {
                IObjectData copy = ObjectDataExt.of(x).copy();
                x.set(UnfreezeDetailConstant.Field.EntryStatus.apiName, EntryStatusEnum.Cancelled.getValue());
                return copy;
            }).collect(Collectors.toList());
            arg.appendUpdateData(UnfreezeDetailConstant.Field.EntryStatus.apiName, updateBeforeUnfreezeList, toDeletedUnfreezeDataList, true);
        }
    }
}
