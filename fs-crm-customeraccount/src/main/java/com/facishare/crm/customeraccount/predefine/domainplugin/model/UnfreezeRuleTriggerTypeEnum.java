package com.facishare.crm.customeraccount.predefine.domainplugin.model;

import java.util.Arrays;

public enum UnfreezeRuleTriggerTypeEnum {
    //先匹配新建按钮触发的解冻规则，然后匹配字段变更触发的解冻
    AddThenFieldChangeTrigger("1"),
    FieldChangeTrigger("2"),
    ConfirmReceiptThenFieldChangeTrigger("3");

    public final String type;

    UnfreezeRuleTriggerTypeEnum(String type) {
        this.type = type;
    }

    public static UnfreezeRuleTriggerTypeEnum of(String type) {
        return Arrays.stream(values()).filter(x -> x.type.equals(type)).findAny().orElse(null);
    }
}
