package com.facishare.crm.customeraccount.predefine.action;

import com.facishare.crm.consts.CreditRuleConst;
import com.facishare.crm.consts.CreditRuleDetailConst;
import com.facishare.crm.customeraccount.predefine.manager.CreditManager;
import com.facishare.crm.customeraccount.util.CreditUtil;
import com.facishare.crm.customeraccount.util.ExecuteUtil;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.repository.model.DomainPluginInstance;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.*;
import java.util.stream.Collectors;

public class CreditRuleAddAction extends StandardAddAction {
    private final CreditManager creditManager = SpringUtil.getContext().getBean(CreditManager.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        List<ObjectDataDocument> objectDataDocumentList = arg.getDetails().get(CreditRuleDetailConst.API_NAME);
        CreditUtil.checkCreditRule(actionContext.getUser(), objectDataDocumentList, serviceFacade);
    }

    @Override
    protected void doSaveData() {
        //查询插件，已存在则不需要新建，否则新建
        Set<String> creditPluginObjects = this.arg.getDetails().getOrDefault(CreditRuleDetailConst.API_NAME, Lists.newArrayList()).stream().map(x ->
                ObjectDataExt.of(x).get(CreditRuleDetailConst.F.CreditObject.apiName, String.class)).collect(Collectors.toSet());
        Map<String, DomainPluginInstance> objectPluginMap = creditManager.findPluginInstanceByPluginAndObjectApiName(actionContext.getUser(), CreditRuleConst.PLUGIN_API_NAME, creditPluginObjects);
        ExecuteUtil.execute(() -> {
            super.doSaveData();
            //初始化插件示例
            for (String creditObjectApiName : creditPluginObjects) {
                if (objectPluginMap.containsKey(creditObjectApiName)) {
                    creditManager.enablePluginInstance(actionContext.getUser(), objectPluginMap.get(creditObjectApiName));
                } else {
                    creditManager.createPluginInstance(actionContext.getUser(), CreditRuleConst.PLUGIN_API_NAME, creditObjectApiName);
                }
            }
            return null;
        });
    }
}
