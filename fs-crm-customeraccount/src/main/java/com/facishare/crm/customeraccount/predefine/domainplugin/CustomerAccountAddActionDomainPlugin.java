package com.facishare.crm.customeraccount.predefine.domainplugin;
import com.facishare.crm.customeraccount.model.CustomerAccountPluginModel;
import com.facishare.crm.customeraccount.predefine.manager.CustomerAccountDomainPluginManager;
import com.facishare.crmcommon.util.DomainPluginDescribeExt;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.domain.ActionDomainPlugin;
import com.facishare.paas.appframework.core.model.domain.DomainProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@DomainProvider(name = "customer_account_Add")
@ServiceModule("customer_account_add")
public class CustomerAccountAddActionDomainPlugin extends EmptyAddActionDomainPlugin {

    @Autowired
    private CustomerAccountDomainPluginManager customerAccountDomainPluginManager;

    @ServiceMethod("before")
    public Result beforeService(ServiceContext context, Arg arg) {
        ActionContext actionContext = new ActionContext(context.getRequestContext(), arg.getObjectApiName(), ObjectAction.CREATE.getActionCode());
        return before(actionContext, arg);
    }

    @Override
    public Result before(ActionContext context, Arg arg) {
        CustomerAccountPluginModel.Arg pluginArg = getArg(context, arg, ActionDomainPlugin.BEFORE);
        customerAccountDomainPluginManager.pluginAction(pluginArg);
        return new Result();
    }

    @ServiceMethod("after")
    public Result afterService(ServiceContext context, Arg arg) {
        ActionContext actionContext = new ActionContext(context.getRequestContext(), arg.getObjectApiName(), ObjectAction.CREATE.getActionCode());
        return after(actionContext, arg);
    }

    @Override
    public Result after(ActionContext context, Arg arg) {
        CustomerAccountPluginModel.Arg pluginArg = getArg(context, arg, ActionDomainPlugin.AFTER);
        customerAccountDomainPluginManager.pluginAction(pluginArg);
        return new Result();
    }

    @ServiceMethod("finally")
    public Result finallyService(ServiceContext context, Arg arg) {
        ActionContext actionContext = new ActionContext(context.getRequestContext(), arg.getObjectApiName(), ObjectAction.CREATE.getActionCode());
        return finallyDo(actionContext, arg);
    }

    @Override
    public Result finallyDo(ActionContext context, Arg arg) {
        CustomerAccountPluginModel.Arg pluginArg = getArg(context, arg, ActionDomainPlugin.FINALLY_DO);
        customerAccountDomainPluginManager.pluginAction(pluginArg);
        return new Result();
    }

    protected CustomerAccountPluginModel.Arg getArg(ActionContext context, Arg arg, String method) {
        DomainPluginDescribeExt ext = DomainPluginDescribeExt.of(arg.getObjectApiName(), arg.getPluginDescribe());

        return CustomerAccountPluginModel.Arg.builder().action(ObjectAction.CREATE.getActionCode()).method(method)
                .dataId(arg.getObjectData().getId())
                .detailDataList(arg.getDetailObjectData() != null ? arg.getDetailObjectData().get(ext.getDefaultDetailObjectApiName()) : null)
                .dmExt(ext)
                .masterData(arg.getObjectData())
                .masterObjectApiName(arg.getObjectApiName())
                .requestId((String) arg.getObjectData().get("requestId"))
                .user(context.getUser())
                .build();
    }
}