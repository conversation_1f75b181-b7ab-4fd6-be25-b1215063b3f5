package com.facishare.crm.customeraccount.predefine.controller;

import com.facishare.crm.customeraccount.constants.AccountFrozenRecordConstant;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class AccountFrozenRecordListHeaderController extends StandardListHeaderController {

    @Override
    protected Result after(Arg arg, Result result){
        result = super.after(arg, result);

        List<String> visibleFields =  result.getVisibleFields();
        log.info("AccountFrozenRecordListHeaderController, visibleFields[{}]", visibleFields);

        if (!CollectionUtils.empty(visibleFields)) {
            List<String> needFilterFields = Lists.newArrayList(AccountFrozenRecordConstant.Field.CheckRecordObjectApiName.apiName,
                    AccountFrozenRecordConstant.Field.CheckRecordObjectDataId.apiName);

            visibleFields = visibleFields.stream().filter(field -> !needFilterFields.contains(field)).collect(Collectors.toList());

            log.info("AccountFrozenRecordListHeaderController, visibleFields[{}]", visibleFields);
            result.setVisibleFields(visibleFields);
        }

        return result;
    }
}