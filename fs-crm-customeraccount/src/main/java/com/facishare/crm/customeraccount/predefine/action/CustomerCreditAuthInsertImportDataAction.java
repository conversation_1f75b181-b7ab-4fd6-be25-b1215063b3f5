package com.facishare.crm.customeraccount.predefine.action;

import com.facishare.crm.consts.CustomerCreditAuthConst;
import com.facishare.crm.customeraccount.constants.CAI18NKey;
import com.facishare.crm.customeraccount.constants.NewCustomerAccountConstants;
import com.facishare.crm.customeraccount.enums.CreditTypeEnum;
import com.facishare.crm.customeraccount.model.DataUpdateAndAddModel;
import com.facishare.crm.customeraccount.predefine.handler.checkrule.CustomerFundAccount;
import com.facishare.crm.customeraccount.predefine.manager.CreditManager;
import com.facishare.crm.customeraccount.predefine.manager.NewCustomerAccountManager;
import com.facishare.crm.customeraccount.util.CreditUtil;
import com.facishare.crm.customeraccount.util.ExecuteUtil;
import com.facishare.crm.customeraccount.util.ObjectDataUtil;
import com.facishare.crm.openapi.Utils;
import com.facishare.crmcommon.constants.CommonI18NKey;
import com.facishare.crmcommon.util.SearchQueryBuilder;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportDataAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

public class CustomerCreditAuthInsertImportDataAction extends StandardInsertImportDataAction {
    private final NewCustomerAccountManager newCustomerAccountManager = SpringUtil.getContext().getBean(NewCustomerAccountManager.class);
    private final CreditManager creditManager = SpringUtil.getContext().getBean(CreditManager.class);

    private IObjectData creditFundAccountData;
    private Map<CustomerFundAccount, IObjectData> customerAccountDataMap;

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        this.creditFundAccountData = newCustomerAccountManager.getCreditFundAccount(actionContext.getRequestContext());
    }

    @Override
    protected void customValidate(List<ImportData> dataList) {
        super.customValidate(dataList);

        List<CustomerFundAccount> customerFundAccounts = Lists.newArrayList();
        List<ImportError> errorList = Lists.newArrayList();
        Map<Integer, String> rowNoCustomerIdMap = Maps.newHashMap();
        Map<String, List<ImportData>> customerImportDataListMap = Maps.newHashMap();
        Set<String> customerIds = Sets.newHashSet();
        for (ImportData importData : dataList) {
            IObjectData objectData = importData.getData();
            String creditAuthObject = objectData.get(CustomerCreditAuthConst.F.CreditAuthObjectApiName.apiName, String.class);
            String creditAuthDataId = objectData.get(CustomerCreditAuthConst.F.CreditAuthObjectDataId.apiName, String.class);
            if (!Utils.ACCOUNT_API_NAME.equals(creditAuthObject)) {
                errorList.add(new ImportError(importData.getRowNo(), I18N.text(CAI18NKey.CREDIT_AUTH_ONLY_SUPPORT_CUSTOMER)));
                continue;
            }
            BigDecimal creditQuota = objectData.get(CustomerCreditAuthConst.F.CreditQuota.apiName, BigDecimal.class, BigDecimal.ZERO);
            if (creditQuota.compareTo(BigDecimal.ZERO) < 0) {
                IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(CustomerCreditAuthConst.F.CreditQuota.apiName);
                errorList.add(new ImportError(importData.getRowNo(), I18N.text(CAI18NKey.FIELD_MUST_GT_ZERO, fieldDescribe.getLabel())));
                continue;
            }

            List<ImportData> customerImportDataList = customerImportDataListMap.computeIfAbsent(creditAuthDataId, k -> Lists.newArrayList());
            customerImportDataList.add(importData);

            rowNoCustomerIdMap.put(importData.getRowNo(), creditAuthDataId);
            customerIds.add(creditAuthDataId);
            if (StringUtils.isNotEmpty(creditAuthDataId)) {
                customerFundAccounts.add(CustomerFundAccount.of(creditAuthDataId, this.creditFundAccountData.getId()));
            }
        }
        List<IObjectData> customerDataList = serviceFacade.findObjectDataByIdsIgnoreAll(actionContext.getTenantId(), Lists.newArrayList(customerIds), Utils.ACCOUNT_API_NAME);
        Map<String, IObjectData> customerIdMap = customerDataList.stream().collect(Collectors.toMap(IObjectData::getId, Function.identity()));
        rowNoCustomerIdMap.forEach((rowNo, customerId) -> {
            if (!customerIdMap.containsKey(customerId)) {
                errorList.add(new ImportError(rowNo, I18N.text(CAI18NKey.CUSTOMER_NOT_EXIST)));
            } else {
                customerFundAccounts.add(CustomerFundAccount.of(customerId, this.creditFundAccountData.getId()));
            }
        });

        customerImportDataListMap.forEach((customerId, importDataList) -> {
            Map<String, List<ImportData>> creditTypeImportDataListMap = Maps.newHashMap();
            for (ImportData importData : importDataList) {
                IObjectData objectData = importData.getData();
                String creditType = objectData.get(CustomerCreditAuthConst.F.CreditType.apiName, String.class);
                String creditOccupiedRuleId = objectData.get(CustomerCreditAuthConst.F.CreditOccupiedRuleId.apiName, String.class);
                if (CreditTypeEnum.OfficialCredit.getValue().equals(creditType) && StringUtils.isEmpty(creditOccupiedRuleId)) {
                    errorList.add(new ImportError(importData.getRowNo(), I18N.text(CommonI18NKey.FIELD_IS_REQUIRED, this.objectDescribe.getFieldDescribe(CustomerCreditAuthConst.F.CreditOccupiedRuleId.apiName).getLabel())));
                    continue;
                }
                List<ImportData> importDataListByType = creditTypeImportDataListMap.computeIfAbsent(creditType, k -> Lists.newArrayList());
                importDataListByType.add(importData);
            }

            creditTypeImportDataListMap.forEach((creditType, importDataListByType) -> {
                if (CreditTypeEnum.OfficialCredit.getValue().equals(creditType)) {
                    if (CollectionUtils.size(importDataListByType) > 1) {
                        importDataListByType.forEach(x -> errorList.add(new ImportError(x.getRowNo(), I18N.text(CAI18NKey.CUSTOMER_OFFICIAL_CREDIT_NOT_DUPLICATE))));
                    } else {
                        SearchTemplateQuery query = SearchQueryBuilder.builder().eq(CustomerCreditAuthConst.F.CreditType.apiName, CreditTypeEnum.OfficialCredit.getValue())
                                .eq(CustomerCreditAuthConst.F.CreditAuthObjectApiName.apiName, Utils.ACCOUNT_API_NAME)
                                .eq(CustomerCreditAuthConst.F.CreditAuthObjectDataId.apiName, customerId).build();
                        List<IObjectData> officalCreditDataList = serviceFacade.findBySearchQuery(actionContext.getUser(), CustomerCreditAuthConst.API_NAME, query).getData();
                        if (CollectionUtils.notEmpty(officalCreditDataList)) {
                            importDataListByType.forEach(x -> errorList.add(new ImportError(x.getRowNo(), I18N.text(CAI18NKey.CUSTOMER_OFFICIAL_CREDIT_NOT_DUPLICATE))));
                        }
                    }
                } else {
                    List<ImportData> needDbCmpImportDataList = Lists.newArrayList();
                    Map<Integer, String> rowNoCrossMsp = Maps.newHashMap();
                    for (int i = 0; i < importDataListByType.size(); i++) {
                        ImportData tmpCreditImportData = importDataListByType.get(0);
                        if (rowNoCrossMsp.containsKey(tmpCreditImportData.getRowNo())) {
                            continue;
                        }
                        IObjectData tmpCreditData = tmpCreditImportData.getData();
                        Long start = tmpCreditData.get(CustomerCreditAuthConst.F.StartTime.apiName, Long.class);
                        Long end = tmpCreditData.get(CustomerCreditAuthConst.F.EndTime.apiName, Long.class);
                        if (Objects.isNull(start) || Objects.isNull(end)) {
                            IFieldDescribe fieldDescribe = this.objectDescribe.getFieldDescribe(Objects.isNull(start) ? CustomerCreditAuthConst.F.StartTime.apiName : CustomerCreditAuthConst.F.EndTime.apiName);
                            rowNoCrossMsp.put(tmpCreditImportData.getRowNo(), I18N.text(CommonI18NKey.FIELD_IS_REQUIRED, fieldDescribe.getLabel()));
                            continue;
                        }
                        boolean cross = false;
                        for (int j = i + 1; j < importDataListByType.size(); j++) {
                            ImportData toCmpCreditImportData = importDataListByType.get(j);
                            if (CreditUtil.validityPeriodCross(tmpCreditImportData.getData(), toCmpCreditImportData.getData())) {
                                rowNoCrossMsp.put(tmpCreditImportData.getRowNo(), I18N.text(CAI18NKey.VALIDITY_PERIOD_CROSS_OVER, toCmpCreditImportData.getRowNo()));
                                rowNoCrossMsp.put(toCmpCreditImportData.getRowNo(), I18N.text(CAI18NKey.VALIDITY_PERIOD_CROSS_OVER, tmpCreditImportData.getRowNo()));
                                cross = true;
                                break;
                            }
                        }
                        if (!cross) {
                            needDbCmpImportDataList.add(tmpCreditImportData);
                        }
                    }
                    //临时信用校验
                    if (!needDbCmpImportDataList.isEmpty()) {
                        SearchTemplateQuery query = SearchQueryBuilder.builder().eq(CustomerCreditAuthConst.F.CreditType.apiName, CreditTypeEnum.TemporaryCredit.getValue())
                                .eq(CustomerCreditAuthConst.F.CreditAuthObjectApiName.apiName, Utils.ACCOUNT_API_NAME)
                                .eq(CustomerCreditAuthConst.F.CreditAuthObjectDataId.apiName, customerId).build();
                        List<IObjectData> dbTemporaryCustomerCreditAuthList = serviceFacade.findBySearchQuery(actionContext.getUser(), CustomerCreditAuthConst.API_NAME, query).getData();

                        for (ImportData needDbCmpImportData : needDbCmpImportDataList) {
                            for (IObjectData dbTmpCustomerCreditData : dbTemporaryCustomerCreditAuthList) {
                                if (CreditUtil.validityPeriodCross(needDbCmpImportData.getData(), dbTmpCustomerCreditData)) {
                                    rowNoCrossMsp.put(needDbCmpImportData.getRowNo(), I18N.text(CAI18NKey.VALIDITY_PERIOD_CROSS_OVER, dbTmpCustomerCreditData.getName()));
                                }
                            }
                        }
                    }
                    rowNoCrossMsp.forEach((rowNo, errMsg) -> errorList.add(new ImportError(rowNo, errMsg)));
                }
            });
        });
        mergeErrorList(errorList);
        customerAccountDataMap = newCustomerAccountManager.batchGetOrCreateNewCustomerAccount(actionContext.getRequestContext(), customerFundAccounts);
    }

    @Override
    protected List<IObjectData> importData(List<IObjectData> validList) {
        for (IObjectData validData : validList) {
            String creditAuthDataId = validData.get(CustomerCreditAuthConst.F.CreditAuthObjectDataId.apiName, String.class);
            IObjectData customerAccountData = customerAccountDataMap.get(CustomerFundAccount.of(creditAuthDataId, this.creditFundAccountData.getId()));
            validData.set(CustomerCreditAuthConst.F.CustomerAccountId.apiName, customerAccountData.getId());
            validData.set(CustomerCreditAuthConst.F.FundAccountId.apiName, this.creditFundAccountData.getId());
        }
        return super.importData(validList);
    }

    @Override
    protected void customAfterImport(List<IObjectData> actualList) {
        super.customAfterImport(actualList);
        Map<String, Map<String, Object>> customerAccountUpdateColumnMap = Maps.newHashMap();
        List<IObjectData> taskDataList = Lists.newArrayList();

        for (IObjectData objectData : actualList) {
            BigDecimal creditQuota = objectData.get(CustomerCreditAuthConst.F.CreditQuota.apiName, BigDecimal.class, BigDecimal.ZERO);
            if (creditQuota.compareTo(BigDecimal.ZERO) != 0 && CreditUtil.creditAuthActive(objectData)) {
                String creditAuthDataId = objectData.get(CustomerCreditAuthConst.F.CreditAuthObjectDataId.apiName, String.class);
                IObjectData customerAccountData = customerAccountDataMap.get(CustomerFundAccount.of(creditAuthDataId, this.creditFundAccountData.getId()));
                ObjectDataUtil.incrementUpdateCustomerAccountUpdateField(customerAccountUpdateColumnMap, customerAccountData.getId(), NewCustomerAccountConstants.Field.CreditQuota.apiName, creditQuota);
                ObjectDataUtil.incrementUpdateCustomerAccountUpdateField(customerAccountUpdateColumnMap, customerAccountData.getId(), NewCustomerAccountConstants.Field.AccountBalance.apiName, creditQuota);
            }
            taskDataList.add(objectData);
        }
        ExecuteUtil.IsolationTask isolationTask = ExecuteUtil.createIsolateTask();
        isolationTask.submit(() -> {
            DataUpdateAndAddModel.Arg customerAccountUpdateArg = DataUpdateAndAddModel.Arg.create().customerAccountColumnUpdateMap(customerAccountUpdateColumnMap);
            newCustomerAccountManager.executeCredit(actionContext.getUser(), customerAccountUpdateArg);
        });
        taskDataList.forEach(objectData -> isolationTask.submit(() -> creditManager.addCreditAuthTask(actionContext.getUser(), objectData)));
        isolationTask.execute();
    }
}
