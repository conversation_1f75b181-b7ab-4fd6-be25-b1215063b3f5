package com.facishare.crm.customeraccount.predefine.action;

import com.facishare.crm.customeraccount.constants.CustomerAccountConstants;
import com.facishare.crm.customeraccount.constants.SystemConstants;
import com.facishare.crm.customeraccount.exception.CustomerAccountBusinessException;
import com.facishare.crm.customeraccount.exception.CustomerAccountErrorCode;
import com.facishare.crm.customeraccount.predefine.manager.CustomerAccountManager;
import com.facishare.crm.customeraccount.util.RequestUtil;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.facishare.rest.core.util.JsonUtil;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

/**
 * 批量走的是 CUstomerAccountInvalidAction一个一个调<br>
 */
@Slf4j
public class CustomerAccountBulkInvalidAction extends StandardBulkInvalidAction {
    private CustomerAccountManager customerAccountManager;
    private String lifeStatus;

    @Override
    protected void validateObjectStatus() {
        if (!RequestUtil.isFromInner(actionContext)) {
            super.validateObjectStatus();
        }
    }

    @Override
    protected void before(Arg arg) {
        RequestUtil.openApiUnsupportedRequest(actionContext);
        super.before(arg);
        customerAccountManager = SpringUtil.getContext().getBean(CustomerAccountManager.class);
        if (actionContext.getAttributes() != null) {
            lifeStatus = actionContext.getAttribute("lifeStatus");
        }
        List<IObjectData> masterObjectDatas = objectDataList.stream().filter(objectData -> objectDescribe.getApiName().equals(objectData.getDescribeApiName())).collect(Collectors.toList());
        for (IObjectData customerAccountObj : masterObjectDatas) {
            String errorReason = customerAccountManager.canInvalidCustomerAccount(actionContext.getUser(), customerAccountObj);
            if (null != errorReason) {
                log.warn("customerAccount cannot invalid,because {},for id:{} name:{},customerId:{}", errorReason, customerAccountObj.getId(), customerAccountObj.getName(), customerAccountObj.get(CustomerAccountConstants.Field.Customer.apiName));
                throw new CustomerAccountBusinessException(CustomerAccountErrorCode.CAN_NOT_INVALID_CUSTOMER_ACCOUNT, errorReason);
            }
        }
    }

    @Override
    public Result doAct(Arg arg) {
        log.debug("CustomerAccount BuklInvalid,objectDataList:{},lifeStatus:{}", JsonUtil.toJson(objectDataList), lifeStatus);
        if (SystemConstants.LifeStatus.Invalid.value.equals(lifeStatus)) {
            bulkInvalidObjects(this.objectDescribe, objectDataList);
            return Result.builder().objectDataList(ObjectDataDocument.ofList(objectDataList)).build();
        } else if (SystemConstants.LifeStatus.InChange.value.equals(lifeStatus)) {
            customerAccountManager.batchUpdateLifeStatus(actionContext.getUser(), objectDataList, lifeStatus);
            return Result.builder().objectDataList(ObjectDataDocument.ofList(objectDataList)).build();
        }
        return super.doAct(arg);
    }

    @Override
    protected void doFunPrivilegeCheck() {

    }

    @Override
    protected void doDataPrivilegeCheck() {
        if (!RequestUtil.isFromInner(actionContext)) {
            super.doDataPrivilegeCheck();
        }
    }
}
