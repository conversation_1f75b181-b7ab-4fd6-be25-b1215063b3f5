package com.facishare.crm.customeraccount.predefine.action;

import com.facishare.crm.customeraccount.constants.CAI18NKey;
import com.facishare.crm.customeraccount.constants.CreditFileConstants;
import com.facishare.crm.customeraccount.constants.SystemConstants;
import com.facishare.crm.customeraccount.enums.CreditTypeEnum;
import com.facishare.crm.customeraccount.predefine.manager.CreditFileManager;
import com.facishare.crm.customeraccount.predefine.manager.CustomerAccountManager;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportDataAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Range;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * Created on 2018/7/24.
 */
@Slf4j
public class CreditFileInsertImportDataAction extends StandardInsertImportDataAction {

    private CreditFileManager creditFileManager;
    private CustomerAccountManager customerAccountManager;

    @Override
    protected void customValidate(List<ImportData> dataList) {
        super.customValidate(dataList);

        creditFileManager = SpringUtil.getContext().getBean(CreditFileManager.class);
        customerAccountManager = SpringUtil.getContext().getBean(CustomerAccountManager.class);
        List<ImportError> errorList = Lists.newArrayList();

        Map<String, List<Range>> validOfficialCreditTimeRanges = Maps.newHashMap();
        Map<String, List<Range>> validTemporaryCreditTimeRanges = Maps.newHashMap();
        for (ImportData importData : dataList) {
            int rowNo = importData.getRowNo();
            IObjectData objectData = importData.getData();

            //参数初始化
            String creditType = objectData.get(CreditFileConstants.Field.CreditType.apiName, String.class);
            if (CreditTypeEnum.OfficialCredit.getValue().equals(creditType)) {
                objectData.set(CreditFileConstants.Field.TemporaryCreditLimit.apiName, null);
            } else if (CreditTypeEnum.TemporaryCredit.getValue().equals(creditType)) {
                objectData.set(CreditFileConstants.Field.CreditQuota.apiName, null);
                objectData.set(CreditFileConstants.Field.CreditPeriod.apiName, null);
            }

            // 校验生效日期与失效日期，和当前日期的关系
            Long startTime = objectData.get(CreditFileConstants.Field.StartTime.apiName, Long.class);
            Long endTime = objectData.get(CreditFileConstants.Field.EndTime.apiName, Long.class) + CreditFileConstants.ONE_DAY_TIME;
            if (startTime >= endTime) {
                errorList.add(new ImportError(rowNo, CreditFileConstants.START_END_TIME_RULE_DESCRIPTION));
                continue;
            }

            // 业务逻辑校验
            try {
                if (CreditTypeEnum.OfficialCredit.getValue().equals(creditType)) {
                    checkValidTimeRange(objectData, startTime, endTime, validOfficialCreditTimeRanges);
                } else if (CreditTypeEnum.TemporaryCredit.getValue().equals(creditType)) {
                    checkValidTimeRange(objectData, startTime, endTime, validTemporaryCreditTimeRanges);
                }
            } catch (ValidateException ve) {
                errorList.add(new ImportError(rowNo, ve.getMessage()));
            }
        }

        mergeErrorList(errorList);
    }

    @Override
    protected void customAfterImport(List<IObjectData> actualList) {
        super.customAfterImport(actualList);

        for (IObjectData objectData : actualList) {
            String lifeStatus = objectData.get(SystemConstants.Field.LifeStatus.apiName, String.class);

            boolean creditActive = creditFileManager.isCreditActive(objectData);
            if (SystemConstants.LifeStatus.Normal.value.equals(lifeStatus) && creditActive) {
                customerAccountManager.updateCreditQuotaToNormal(actionContext.getUser(), objectData, SystemConstants.LifeStatus.Ineffective.value);
            }
        }
    }

    /**
     * 检查文件内记录是否存在有效期交叉情况
     * @param objectData
     * @param startTime
     * @param endTime
     * @param customerCreditTimeRanges
     * @throws ValidateException
     */
    private void checkValidTimeRange(IObjectData objectData, Long startTime, Long endTime, Map<String, List<Range>> customerCreditTimeRanges) throws ValidateException {
        String customerId = objectData.get(CreditFileConstants.Field.Customer.apiName, String.class);

        List<Range> validTimeRanges = customerCreditTimeRanges.get(customerId);
        Range currentTimeRange = Range.closed(startTime, endTime);
        if (CollectionUtils.isEmpty(validTimeRanges)) {
            creditFileManager.foreachCheck(actionContext.getUser(), objectData);

            validTimeRanges = Lists.newArrayList(currentTimeRange);
            customerCreditTimeRanges.put(customerId, validTimeRanges);
            return;
        }
        for (Range validTimeRange : validTimeRanges) {
            if (validTimeRange.contains(startTime) || validTimeRange.contains(endTime)) {
                throw new ValidateException(I18N.text(CAI18NKey.VALID_PERIOD_CROSSING));
            }
        }
        creditFileManager.foreachCheck(actionContext.getUser(), objectData);
        validTimeRanges.add(currentTimeRange);
    }

}
