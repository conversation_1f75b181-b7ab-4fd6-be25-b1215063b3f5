package com.facishare.crm.customeraccount.enums;

import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;

public enum FAccountAuthorizationStatusEnum {
    UN_INIT("un_init", "未完成"),
    HAS_INIT("has_init", "已完成"),
    ;
    private final String value;
    private final String label;

    FAccountAuthorizationStatusEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public static FAccountAuthorizationStatusEnum getByCode(String status) {
        for (FAccountAuthorizationStatusEnum srcType : values()) {
            if (Objects.equals(status, srcType.value)) {
                return srcType;
            }
        }
        throw new IllegalArgumentException("status error");
    }

    public String getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }

    public static List<String> getAllStatus() {
        List<String> allStatus = Lists.newArrayList();
        for (FAccountAuthorizationStatusEnum d : FAccountAuthorizationStatusEnum.values()) {
            allStatus.add(d.getValue());
        }
        return allStatus;
    }
}
