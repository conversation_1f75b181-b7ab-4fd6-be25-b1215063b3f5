package com.facishare.crm.customeraccount.predefine.domainplugin.processor;

import com.facishare.crm.customeraccount.enums.AccountCheckRuleTypeEnum;
import com.facishare.crm.customeraccount.enums.ReduceTriggerActionEnum;
import com.facishare.crm.customeraccount.enums.StageEnum;
import com.facishare.crm.customeraccount.model.DataTriggerFunctionModel;
import com.facishare.crm.customeraccount.model.DataUpdateAndAddModel;
import com.facishare.crm.customeraccount.mq.producer.MQProducerManager;
import com.facishare.crm.customeraccount.predefine.domainplugin.BranchTransactionUtil;
import com.facishare.crm.customeraccount.predefine.domainplugin.model.*;
import com.facishare.crm.customeraccount.predefine.manager.AccountCheckManager;
import com.facishare.crm.customeraccount.predefine.manager.NewCustomerAccountManager;
import com.facishare.crm.customeraccount.util.ObjectDataUtil;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.domain.IncrementUpdateActionDomainPlugin;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.appframework.metadata.cache.RedissonServiceImpl;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fxiaoke.transaction.tcc.api.context.BranchTransactionalContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class IncrementUpdateInChangeCheckRuleProcessor extends AbstractCheckRuleProcessor<IncrementUpdateActionDomainPlugin.Arg, IncrementUpdateActionDomainPlugin.Result, CheckRuleIncrementUpdateContextModel> {
    public IncrementUpdateInChangeCheckRuleProcessor(AccountCheckManager accountCheckManager, NewCustomerAccountManager newCustomerAccountManager, MQProducerManager mqProducerManager, RedissonServiceImpl redissonService) {
        super(accountCheckManager, newCustomerAccountManager, mqProducerManager, redissonService);
    }

    @Override
    public IncrementUpdateActionDomainPlugin.Result newResultInstance() {
        return new IncrementUpdateActionDomainPlugin.Result();
    }

    @Override
    public CheckRulePluginContextKey getContextKeyEnum() {
        return CheckRulePluginContextKey.IncrementUpdate;
    }

    @Override
    public Class<CheckRuleIncrementUpdateContextModel> getContextClass() {
        return CheckRuleIncrementUpdateContextModel.class;
    }

    @Override
    public ObjectDataDocument getObjectDataDocument(IncrementUpdateActionDomainPlugin.Arg arg) {
        fillDataByDbDataWhenIncrementUpdate(arg.getObjectData().toObjectData(), arg.getDbObjectData().toObjectData());
        return arg.getObjectData();
    }

    @Override
    public CheckRuleIncrementUpdateContextModel doPreAct(RequestContext requestContext, IncrementUpdateActionDomainPlugin.Arg arg) {
        IObjectData objectData = arg.getObjectData().toObjectData();
        IObjectData dbObjectData = arg.getDbObjectData().toObjectData();
        String objectApiName = arg.getObjectApiName();
        CheckRuleIncrementUpdateContextModel contextModel = new CheckRuleIncrementUpdateContextModel(objectApiName, objectData.getId());
        if (arg.isUseSnapshotForApproval()) {
            return contextModel;
        }
        fillDataByDbDataWhenIncrementUpdate(objectData, dbObjectData);
        IObjectDescribe objectDescribe = accountCheckManager.findObject(requestContext.getTenantId(), objectApiName);
        AllRuleUseRecordModel allRuleUseRecordModel = queryAllRuleUseRecord(requestContext, objectApiName, objectData.getId());
        Map<String, Object> dataMap = ObjectDataUtil.toDataMap(objectDescribe, objectData);
        ObjectDataUtil.setLifeStatus(dataMap, ObjectLifeStatus.NORMAL);
        List<IObjectData> checkRuleList = allRuleUseRecordModel.getCheckRuleList(requestContext.getUser(), objectApiName, accountCheckManager);

        DataUpdateAndAddModel.Arg updateAddArg = DataUpdateAndAddModel.Arg.create();

        RuleUseRecordModel frozenRuleUseRecord = allRuleUseRecordModel.getFrozenRuleUseRecordModel();
        if (Objects.nonNull(frozenRuleUseRecord)) {
            //变更审批中，禁止编辑相关金额字段
            adaptFrozenRule(requestContext, frozenRuleUseRecord, objectData, true, true);
        } else {
            CheckRuleTuple<FrozenRuleMatchedModel> frozenMatchTuple = matchFrozenAndGetContext(requestContext, objectDescribe, objectData, dataMap, checkRuleList);
            updateAddArg.merge(frozenMatchTuple.getArg());
            contextModel.setFrozenRuleMatchedModel(frozenMatchTuple.getContext());
        }
        RuleUseRecordModel directReduceRuleUseRecord = allRuleUseRecordModel.getDirectReduceRuleUseRecordModel();
        if (Objects.nonNull(directReduceRuleUseRecord)) {
            //变更审批中，禁止编辑相关金额字段
            adaptDirectReduceRule(requestContext, directReduceRuleUseRecord, objectData, true, true);
        } else {
            CheckRuleTuple<CheckRuleOrModel<DirectReduceRuleMatchedModel, DirectReduceRuleToMatchModel>> directReduceMatchResult = matchDirectReduceGetContextAtPre(requestContext, objectDescribe, objectData, dataMap, checkRuleList);
            updateAddArg.merge(directReduceMatchResult.getArg());
            contextModel.setDirectReduceRuleMatchedModel(directReduceMatchResult.getContext().getFirst());
            contextModel.setDirectReduceRuleToMatchModel(directReduceMatchResult.getContext().getSecond());
        }
        contextModel.setUnfreezeRuleTriggerTypeEnum(matchUnfreezeMark());
        newCustomerAccountManager.execute(requestContext.getUser(), updateAddArg);
        return contextModel;
    }

    @Override
    protected IObjectData getMatchedFrozenRule(User user, IObjectDescribe objectDescribe, Map<String, Object> dataMap, List<IObjectData> checkRuleList) {
        List<IObjectData> frozenRuleByFieldChangeList = AccountCheckManager.removeNotMatchedRuleByTypeAndButton(checkRuleList, AccountCheckRuleTypeEnum.Check_Reduce, ReduceTriggerActionEnum.FieldChange, null);
        return accountCheckManager.getMatchedCheckReduceRule(user, objectDescribe, dataMap, frozenRuleByFieldChangeList, false).orElse(null);
    }

    @Override
    protected IObjectData getMatchedDirectReduceRule(User user, IObjectDescribe objectDescribe, Map<String, Object> dataMap, List<IObjectData> checkRuleList) {
        List<IObjectData> directReduceRuleByFieldChangeList = AccountCheckManager.removeNotMatchedRuleByTypeAndButton(checkRuleList, AccountCheckRuleTypeEnum.Direct_Reduce, ReduceTriggerActionEnum.FieldChange, null);
        return accountCheckManager.getMatchedDirectReduceRule(user, objectDescribe, dataMap, directReduceRuleByFieldChangeList).orElse(null);
    }

    @Override
    protected UnfreezeRuleTriggerTypeEnum matchUnfreezeMark() {
        return UnfreezeRuleTriggerTypeEnum.FieldChangeTrigger;
    }

    @Override
    public void doFinallyDo(RequestContext requestContext, IncrementUpdateActionDomainPlugin.Arg arg) {
        IObjectData objectData = arg.getObjectData().toObjectData();
        fillDataByDbDataWhenIncrementUpdate(objectData, arg.getDbObjectData().toObjectData());
        CheckRuleIncrementUpdateContextModel contextModel = getContextModel(arg);
        boolean doActComplete = arg.isDoActComplete();
        if (doActComplete) {
            doActCompleteTrue(requestContext, objectData, contextModel, arg.isUseSnapshotForApproval());
        } else {
            doActCompleteFalse(requestContext, contextModel);
        }
    }

    @Override
    public void doConfirm(BranchTransactionalContext branchContext, RequestContext requestContext, IncrementUpdateActionDomainPlugin.Arg arg, CheckRuleIncrementUpdateContextModel contextModel) {
        IObjectData objectData = arg.getObjectData().toObjectData();
        IObjectData dbObjectData = arg.getDbObjectData().toObjectData();
        fillDataByDbDataWhenIncrementUpdate(objectData, dbObjectData);
        BranchTransactionUtil.resetSaveLifeStatus(branchContext, objectData);
        doActCompleteTrue(requestContext, objectData, contextModel, arg.isUseSnapshotForApproval());
    }

    @Override
    public void doCancel(BranchTransactionalContext branchContext, RequestContext requestContext, IncrementUpdateActionDomainPlugin.Arg arg, CheckRuleIncrementUpdateContextModel contextModel) {
        IObjectData objectData = arg.getObjectData().toObjectData();
        IObjectData dbObjectData = arg.getDbObjectData().toObjectData();
        fillDataByDbDataWhenIncrementUpdate(objectData, dbObjectData);
        BranchTransactionUtil.resetSaveLifeStatus(branchContext, objectData);
        boolean writeDb = BranchTransactionUtil.getWriteDbFlag(branchContext);
        if (writeDb) {
            doActCompleteTrue(requestContext, objectData, contextModel, arg.isUseSnapshotForApproval());
        } else {
            doActCompleteFalse(requestContext, contextModel);
        }
    }

    private void doActCompleteTrue(RequestContext requestContext, IObjectData objectData, CheckRuleIncrementUpdateContextModel contextModel, boolean useSnapshotForApproval) {
        DataTriggerFunctionModel dataTriggerFunctionModel = DataTriggerFunctionModel.create();

        FrozenRuleMatchedModel frozenRuleMatchedModel = contextModel.getFrozenRuleMatchedModel();
        if (Objects.nonNull(frozenRuleMatchedModel)) {
            DataTriggerFunctionModel frozenTriggerData = frozenMatchedBuildTriggerData(requestContext, frozenRuleMatchedModel);
            dataTriggerFunctionModel.merge(frozenTriggerData);
        }
        DirectReduceRuleMatchedModel directReduceRuleMatchedModel = contextModel.getDirectReduceRuleMatchedModel();
        if (Objects.nonNull(directReduceRuleMatchedModel)) {
            DataTriggerFunctionModel directReduceTriggerData = directReduceMatchedBuildTriggerData(requestContext, directReduceRuleMatchedModel);
            dataTriggerFunctionModel.merge(directReduceTriggerData);
        }
        dataTriggerFunctionModel.triggerFunctionIgnoreException(requestContext.getUser(), StageEnum.POST, newCustomerAccountManager, true);
        if (!useSnapshotForApproval) {
            ObjectDataUtil.setLifeStatus(objectData, ObjectLifeStatus.NORMAL);
            triggerUnfreezeIgnoreException(requestContext, objectData, contextModel.getUnfreezeRuleTriggerTypeEnum());
            triggerDirectReduceIgnoreException(requestContext, objectData, contextModel.getDirectReduceRuleToMatchModel());
        }
    }

    private void doActCompleteFalse(RequestContext requestContext, CheckRuleIncrementUpdateContextModel contextModel) {
        DataUpdateAndAddModel.Arg updateAddArg = DataUpdateAndAddModel.Arg.create();

        Map<String, IObjectData> accountRuleUseRecordDataMap = queryAccountRuleUseRecord(requestContext, contextModel.fetchAccountRuleUseRecordIds());
        FrozenRuleMatchedModel frozenRuleMatchedModel = contextModel.getFrozenRuleMatchedModel();
        boolean invalidFlowAfterCancel = needInvalidFlowAfterCancel(requestContext.getUser());
        if (Objects.nonNull(frozenRuleMatchedModel)) {
            FrozenRollbackResult frozenRollbackResult = rollbackMatchedFrozen(requestContext, accountRuleUseRecordDataMap.get(frozenRuleMatchedModel.getAccountRuleUseRecordId()), true);
            frozenRollbackResult.mergeTo(updateAddArg, invalidFlowAfterCancel, false);
        }
        DirectReduceRuleMatchedModel directReduceRuleMatchedModel = contextModel.getDirectReduceRuleMatchedModel();
        if (Objects.nonNull(directReduceRuleMatchedModel)) {
            DirectReduceRollbackResult directReduceRollbackResult = rollbackDirectReduce(requestContext, accountRuleUseRecordDataMap.get(directReduceRuleMatchedModel.getAccountRuleUseRecordId()), true);
            directReduceRollbackResult.mergeTo(updateAddArg, invalidFlowAfterCancel, false);
        }
        newCustomerAccountManager.execute(requestContext.getUser(), updateAddArg);
    }
}
