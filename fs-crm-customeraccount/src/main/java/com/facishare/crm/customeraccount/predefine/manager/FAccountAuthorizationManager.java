package com.facishare.crm.customeraccount.predefine.manager;

import com.facishare.crm.customeraccount.constants.*;
import com.facishare.crm.customeraccount.enums.AccessModuleEnum;
import com.facishare.crm.customeraccount.enums.AccountCheckRuleTypeEnum;
import com.facishare.crm.customeraccount.enums.FAccountAuthAuthorizedTypeEnum;
import com.facishare.crm.customeraccount.enums.FAccountAuthorizationStatusEnum;
import com.facishare.crm.customeraccount.exception.CustomerAccountBusinessException;
import com.facishare.crm.customeraccount.predefine.action.FAccountAuthorizationInitAction;
import com.facishare.crm.customeraccount.predefine.handler.RuleHandlerUtil;
import com.facishare.crm.customeraccount.predefine.service.dto.AccessOutcomeAuthModel;
import com.facishare.crm.customeraccount.predefine.service.dto.ObjectMappingModel;
import com.facishare.crm.customeraccount.util.*;
import com.facishare.crmcommon.constants.SystemConstants;
import com.facishare.crmcommon.describebuilder.CurrencyFieldDescribeBuilder;
import com.facishare.crmcommon.describebuilder.FormFieldBuilder;
import com.facishare.crmcommon.describebuilder.SelectOneFieldDescribeBuilder;
import com.facishare.crmcommon.describebuilder.SelectOptionBuilder;
import com.facishare.crmcommon.describebuilder.TextFieldDescribeBuilder;
import com.facishare.crmcommon.manager.CommonDescribeManager;
import com.facishare.crmcommon.manager.CommonDetailLayoutManager;
import com.facishare.crmcommon.manager.CommonLangManager;
import com.facishare.crmcommon.manager.CommonObjDataManager;
import com.facishare.crmcommon.util.ObjectDataUtil;
import com.facishare.crmcommon.util.SearchUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ObjectDataNotFoundException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.action.CustomButtonAction;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.CurrencyFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.describe.TextFieldDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.fxiaoke.crmrestapi.common.contants.LifeStatusEnum;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.stereotype.Component;

/**
 * @IgnoreI18nFile
 * 待继续处理多语问题
 */
@EnableRetry(proxyTargetClass = true)
@Component
@Slf4j
public class FAccountAuthorizationManager extends CommonManager {

    @Autowired
    private IObjectDescribeService objectDescribeService;

    @Resource
    private CommonDescribeManager commonDescribeManager;

    @Resource
    private CommonDetailLayoutManager commonDetailLayoutManager;

    @Autowired
    private FundAccountConfigManager fundAccountConfigManager;

    @Autowired
    private AccountCheckRuleManager accountCheckRuleManager;

    @Autowired
    private DomainPluginManager domainPluginManager;

    @Autowired
    private FAccountAuthorizationManager fAccountAuthorizationManager;

    @Autowired
    private FundAccountManager fundAccountManager;

    @Autowired
    private AuthorizationDetailManager authorizationDetailManager;

    @Autowired
    private CommonObjDataManager commonObjDataManager;
    @Autowired
    private TransactionFlowDataManager transactionFlowDataManager;
    @Autowired
    private FAccountEntryRuleManager fAccountEntryRuleManager;
    @Autowired
    private CaNotifyManager caNotifyManager;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private NewCustomerAccountManager newCustomerAccountManager;
    @Autowired
    private FundAccountBaseService fundAccountBaseService;
    @Autowired
    private UnfreezeAuthDetailManager unfreezeAuthDetailManager;
    @Autowired
    private CommonLangManager commonLangManager;



    private static String accountAuthAutoEnterAccountObjectApiNamesKeyPrefix = "accountAuthAutoEnterAccountObjectApiNames-";


    public boolean exist(String tenantId, String objectApiName, String authorizedType) {
        User admin = new User(tenantId, "-10000");
        return exist(admin, objectApiName, authorizedType);
    }

    public boolean exist(User user, String objectApiName, String authorizedType) {
        return getFAccountAuthorizationData(user, objectApiName, authorizedType) != null;
    }

    public IObjectData getFAccountAuthorizationData(String tenantId, String objectApiName, String authorizedType) {
        User admin = User.systemUser(tenantId);
        return getFAccountAuthorizationData(admin, objectApiName, authorizedType);
    }

    public IObjectData getFAccountAuthorizationData(User user, String objectApiName, String authorizedType) {
        List<IObjectData> fAccountAuthorizationDatas = getFAccountAuthorizationDatas(user, Lists.newArrayList(objectApiName), authorizedType);
        if (CollectionUtils.empty(fAccountAuthorizationDatas)) {
            return null;
        }
        return fAccountAuthorizationDatas.get(0);
    }

    public List<IObjectData> getFAccountAuthorizationDatas(User user, List<String> objectApiNames, String authorizedType) {
        if (CollectionUtils.empty(objectApiNames)) {
            return Lists.newArrayList();
        }

        List<IFilter> filterList = Lists.newArrayList();

        if (!Strings.isNullOrEmpty(authorizedType)) {
            IFilter authorizedTypeFilter = new Filter();
            authorizedTypeFilter.setFieldName(FAccountAuthorizationConstants.Field.AuthorizedType.apiName);
            authorizedTypeFilter.setFieldValues(Lists.newArrayList(authorizedType));
            authorizedTypeFilter.setOperator(Operator.EQ);
            filterList.add(authorizedTypeFilter);
        }

        IFilter objectFilter = new Filter();
        objectFilter.setFieldName("authorized_object_api_name");
        objectFilter.setFieldValues(objectApiNames);
        objectFilter.setOperator(Operator.IN);
        filterList.add(objectFilter);

        IFilter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName("life_status");
        lifeStatusFilter.setFieldValues(Lists.newArrayList("normal"));
        lifeStatusFilter.setOperator(Operator.EQ);
        filterList.add(lifeStatusFilter);

        IFilter deleteStatusFilter = new Filter();
        deleteStatusFilter.setFieldName(ObjectData.IS_DELETED);
        deleteStatusFilter.setFieldValues(Lists.newArrayList("0"));
        deleteStatusFilter.setOperator(Operator.EQ);
        filterList.add(deleteStatusFilter);

        IFilter tenantIdFilter = new Filter();
        tenantIdFilter.setFieldName(IObjectData.TENANT_ID);
        tenantIdFilter.setFieldValues(Lists.newArrayList(user.getTenantId()));
        tenantIdFilter.setOperator(Operator.EQ);
        filterList.add(tenantIdFilter);

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(filterList);
        query.setOffset(0);
        query.setLimit(1000);

        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, "FAccountAuthorizationObj", query);
        if (CollectionUtils.empty(queryResult.getData())) {
            return Lists.newArrayList();
        }
        return queryResult.getData();
    }

    public List<IObjectData> getFAccountAuthorizationDatas(String tenantId, List<String> authorizedTypes) {
        User admin = User.systemUser(tenantId);
        return getFAccountAuthorizationDatas(admin, authorizedTypes);
    }

    public List<IObjectData> getFAccountAuthorizationDatas(User user, List<String> authorizedTypes) {
        if (CollectionUtils.empty(authorizedTypes)) {
            return Lists.newArrayList();
        }

        List<IFilter> filterList = Lists.newArrayList();

        IFilter authorizedTypeFilter = new Filter();
        authorizedTypeFilter.setFieldName(FAccountAuthorizationConstants.Field.AuthorizedType.apiName);
        authorizedTypeFilter.setFieldValues(authorizedTypes);
        authorizedTypeFilter.setOperator(Operator.IN);
        filterList.add(authorizedTypeFilter);

        IFilter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName("life_status");
        lifeStatusFilter.setFieldValues(Lists.newArrayList("normal"));
        lifeStatusFilter.setOperator(Operator.EQ);
        filterList.add(lifeStatusFilter);

        IFilter deleteStatusFilter = new Filter();
        deleteStatusFilter.setFieldName(ObjectData.IS_DELETED);
        deleteStatusFilter.setFieldValues(Lists.newArrayList("0"));
        deleteStatusFilter.setOperator(Operator.EQ);
        filterList.add(deleteStatusFilter);

        IFilter tenantIdFilter = new Filter();
        tenantIdFilter.setFieldName(IObjectData.TENANT_ID);
        tenantIdFilter.setFieldValues(Lists.newArrayList(user.getTenantId()));
        tenantIdFilter.setOperator(Operator.EQ);
        filterList.add(tenantIdFilter);

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(filterList);
        query.setOffset(0);
        query.setLimit(100);

        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, "FAccountAuthorizationObj", query);
        if (CollectionUtils.empty(queryResult.getData())) {
            return Lists.newArrayList();
        }
        return queryResult.getData();
    }

    public List<IObjectData> getFAccountAuthorizationDatas(User user, String authorizedType, String status) {
        List<IFilter> filterList = Lists.newArrayList();

        IFilter authorizedTypeFilter = new Filter();
        authorizedTypeFilter.setFieldName(FAccountAuthorizationConstants.Field.AuthorizedType.apiName);
        authorizedTypeFilter.setFieldValues(Lists.newArrayList(authorizedType));
        authorizedTypeFilter.setOperator(Operator.EQ);
        filterList.add(authorizedTypeFilter);

        if (!Strings.isNullOrEmpty(status)) {
            IFilter statusFilter = new Filter();
            statusFilter.setFieldName(FAccountAuthorizationConstants.Field.Status.apiName);
            statusFilter.setFieldValues(Lists.newArrayList(status));
            statusFilter.setOperator(Operator.EQ);
            filterList.add(statusFilter);
        }

        IFilter deleteStatusFilter = new Filter();
        deleteStatusFilter.setFieldName(ObjectData.IS_DELETED);
        deleteStatusFilter.setFieldValues(Lists.newArrayList("0"));
        deleteStatusFilter.setOperator(Operator.EQ);
        filterList.add(deleteStatusFilter);

        IFilter tenantIdFilter = new Filter();
        tenantIdFilter.setFieldName(IObjectData.TENANT_ID);
        tenantIdFilter.setFieldValues(Lists.newArrayList(user.getTenantId()));
        tenantIdFilter.setOperator(Operator.EQ);
        filterList.add(tenantIdFilter);

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(filterList);
        query.setOffset(0);
        query.setLimit(1000);

        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, "FAccountAuthorizationObj", query);
        if (CollectionUtils.empty(queryResult.getData())) {
            return null;
        }
        return queryResult.getData();
    }

    public List<IObjectData> getReduceTriggerActionsIsEmptyOutcomeDatas(String tenantId) {
        List<IObjectData> outcomeAccountAuths = fAccountAuthorizationManager.getFAccountAuthorizationDatas(tenantId, Lists.newArrayList(FAccountAuthAuthorizedTypeEnum.Outcome.getValue()));
        if (CollectionUtils.empty(outcomeAccountAuths)) {
            return Lists.newArrayList();
        }

        List<IObjectData> reduceTriggerActionsIsEmptyOutcomeDatas = Lists.newArrayList();
        for (IObjectData outcomeAccountAuth : outcomeAccountAuths) {
            List<String> dbReduceTriggerActions = outcomeAccountAuth.get(FAccountAuthorizationConstants.Field.ReduceTriggerActions.apiName, List.class);
            if (CollectionUtils.empty(dbReduceTriggerActions)) {
                reduceTriggerActionsIsEmptyOutcomeDatas.add(outcomeAccountAuth);
            }
        }
        return reduceTriggerActionsIsEmptyOutcomeDatas;
    }

    /**
     * 所有的支出授权的AuthorizedObjectApiName
     */
    public Set<String> getAllOutComeAuthorizedObjectApiName(String tenantId) {
        User admin = User.systemUser(tenantId);
        List<String> authorizedTypes = Lists.newArrayList(FAccountAuthAuthorizedTypeEnum.Outcome.getValue());
        List<IObjectData> outcomeAccountAuths = getFAccountAuthorizationDatas(admin, authorizedTypes);
        if (CollectionUtils.empty(outcomeAccountAuths)) {
            return new HashSet<>();
        }
        return outcomeAccountAuths.stream().map(d -> d.get(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, String.class)).collect(Collectors.toSet());
    }

    /**
     * 是否开启了自动入账
     */
    public boolean isOpenAutoEnterAccount(String tenantId, String apiName) {
        List<IFilter> filterList = Lists.newArrayList();

        SearchUtil.fillFilterEq(filterList, FAccountAuthorizationConstants.Field.AuthorizedType.apiName, Lists.newArrayList(FAccountAuthAuthorizedTypeEnum.Income.getValue()));
        SearchUtil.fillFilterEq(filterList, FAccountAuthorizationConstants.Field.Status.apiName, Lists.newArrayList(FAccountAuthorizationStatusEnum.HAS_INIT.getValue()));
        SearchUtil.fillFilterEq(filterList, FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, Lists.newArrayList(apiName));
        SearchUtil.fillFilterEq(filterList, FAccountAuthorizationConstants.Field.AutoEntryStatus.apiName, Lists.newArrayList(true));
        SearchUtil.fillFilterEq(filterList, ObjectData.IS_DELETED, Lists.newArrayList("0"));
        SearchUtil.fillFilterEq(filterList, ObjectData.TENANT_ID, Lists.newArrayList(tenantId));

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(filterList);
        query.setOffset(0);
        query.setLimit(10);

        User admin = new User(tenantId, "-10000");
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(admin, FAccountAuthorizationConstants.API_NAME, query);
        return !CollectionUtils.empty(queryResult.getData());
    }

    /**
     * 能够自动入账的对象
     */
    public List<String> getOpenAutoEnterAccountObjectApiNames(String tenantId) {
        List<IFilter> filterList = Lists.newArrayList();

        SearchUtil.fillFilterEq(filterList, FAccountAuthorizationConstants.Field.AuthorizedType.apiName, Lists.newArrayList(FAccountAuthAuthorizedTypeEnum.Income.getValue()));
        SearchUtil.fillFilterEq(filterList, FAccountAuthorizationConstants.Field.Status.apiName, Lists.newArrayList(FAccountAuthorizationStatusEnum.HAS_INIT.getValue()));
        SearchUtil.fillFilterEq(filterList, FAccountAuthorizationConstants.Field.AutoEntryStatus.apiName, Lists.newArrayList(true));
        SearchUtil.fillFilterEq(filterList, "life_status", Lists.newArrayList("normal"));
        SearchUtil.fillFilterEq(filterList, ObjectData.IS_DELETED, Lists.newArrayList("0"));
        SearchUtil.fillFilterEq(filterList, ObjectData.TENANT_ID, Lists.newArrayList(tenantId));

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(filterList);
        query.setOffset(0);
        query.setLimit(1000);

        User admin = new User(tenantId, "-10000");
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(admin, FAccountAuthorizationConstants.API_NAME, query);
        if (CollectionUtils.empty(queryResult.getData())) {
            return Lists.newArrayList();
        }
        return queryResult.getData().stream().map(d -> d.get(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, String.class)).collect(Collectors.toList());
    }

    public List<IObjectData> findFAccountAuthorizationList(User user, String authorizedType, List<String> fieldApiNames) {
        List<IFilter> filterList = Lists.newArrayList();
        IFilter authorizedTypeFilter = new Filter();
        authorizedTypeFilter.setFieldName(FAccountAuthorizationConstants.Field.AuthorizedType.apiName);
        authorizedTypeFilter.setFieldValues(Lists.newArrayList(authorizedType));
        authorizedTypeFilter.setOperator(Operator.EQ);
        filterList.add(authorizedTypeFilter);

        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.addFilters(filterList);
        return serviceFacade.findBySearchQueryWithFieldsIgnoreAll(user, FAccountAuthorizationConstants.API_NAME, searchTemplateQuery, fieldApiNames).getData();
    }

    public String getEntryCustomerFieldApiName(String tenantId, String objectApiName, String authorizedType) {
        User user = new User(tenantId, "-10000");
        return getEntryCustomerFieldApiName(user, objectApiName, authorizedType);
    }

    public String getEntryCustomerFieldApiName(User user, String objectApiName, String authorizedType) {
        IObjectData fAccountAuthorizationData = getFAccountAuthorizationData(user, objectApiName, authorizedType);
        if (fAccountAuthorizationData == null) {
            return null;
        }
        return fAccountAuthorizationData.get(FAccountAuthorizationConstants.Field.EntryCustomerFieldApiName.apiName, String.class);
    }

    public void updateInitStatus(User user, String dataId) {
        List<String> updateFields = Lists.newArrayList(FAccountAuthorizationConstants.Field.Status.apiName);

        IObjectData fAccountAuthorizationData = new ObjectData();
        fAccountAuthorizationData.set(DBRecord.ID, dataId);
        fAccountAuthorizationData.set(FAccountAuthorizationConstants.Field.Status.apiName, FAccountAuthorizationStatusEnum.HAS_INIT.getValue());
        fAccountAuthorizationData.setDescribeApiName(FAccountAuthorizationConstants.API_NAME);
        fAccountAuthorizationData.setTenantId(user.getTenantId());

        serviceFacade.batchUpdateByFields(user, Lists.newArrayList(fAccountAuthorizationData), updateFields);
    }

    /**
     * 把校验规则对应的对象和账户，加到'支出授权'上
     */
    public void saveOrUpdateOutcomeAuthData(String tenantId, List<IObjectData> accountCheckRuleDatas) {
        if (CollectionUtils.empty(accountCheckRuleDatas)) {
            return;
        }

        for (IObjectData accountCheckRuleData : accountCheckRuleDatas) {
            String name = accountCheckRuleData.get(AccountCheckRuleConstants.Field.Name.apiName, String.class);
            String ruleType = accountCheckRuleData.get(AccountCheckRuleConstants.Field.RuleType.apiName, String.class);

            log.info("saveOrUpdateOutcomeAuthData tenantId[{}], name[{}], ruleType[{}], ", tenantId, name, ruleType);

            if (Objects.equals(AccountCheckRuleTypeEnum.Check_Reduce.getValue(), ruleType)) {
                //校验对象
                String checkObject = accountCheckRuleData.get(AccountCheckRuleConstants.Field.CheckObject.apiName, String.class);
                List<ObjectMappingModel> occupiedMappingObjectMappings = RuleHandlerUtil.getObjectMapping(accountCheckRuleData, AccountCheckRuleConstants.Field.OccupiedMapping.apiName);
                saveOrUpdateOutcomeAuthData(tenantId, checkObject, occupiedMappingObjectMappings);
            }

            //扣减对象
            String reduceRelatedObject = accountCheckRuleData.get(AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName, String.class);
            List<ObjectMappingModel> reduceMappingObjectMappings = RuleHandlerUtil.getObjectMapping(accountCheckRuleData, AccountCheckRuleConstants.Field.ReduceMapping.apiName);
            saveOrUpdateOutcomeAuthData(tenantId, reduceRelatedObject, reduceMappingObjectMappings);
        }
    }

    public void saveOrUpdateOutcomeAuthData(String tenantId, String authorizedObjectApiName, List<ObjectMappingModel> objectMappings) {
        User admin = new User(tenantId, "-10000");

        //支出授权是否存在
        IObjectData fAccountAuthorizationData = fAccountAuthorizationManager.getFAccountAuthorizationData(admin, authorizedObjectApiName, FAccountAuthAuthorizedTypeEnum.Outcome.getValue());

        ServiceContext serviceContext = new ServiceContext(RequestContext.builder().tenantId(tenantId).user(new User(tenantId, "-10000")).build(), null, null);
        List<String> fundAccountIds = AccountCheckRuleMappingUtil.getFundAccountIds(objectMappings);
        if (CollectionUtils.empty(fundAccountIds)) {
            log.info("saveOrUpdateOutcomeAuthData fundAccountIds empty. tenantId[{}], authorizedObjectApiName[{}], objectMappings[{}]", tenantId, authorizedObjectApiName, objectMappings);
            return;
        }
        if (fAccountAuthorizationData == null) {
            //新建
            String customerId = AccountCheckRuleMappingUtil.getCustomerId(objectMappings, AccountTransactionFlowConst.Field.Customer.apiName);
            saveOutcomeMasterAndDetail(serviceContext, authorizedObjectApiName, customerId, fundAccountIds);
        } else {
            //更新新加的客户到从对象'支出授权'
            authorizationDetailManager.addNewDetails(admin, fAccountAuthorizationData.getId(), authorizedObjectApiName, fundAccountIds);
        }
    }

    /**
     * 开启校验规则，新建订单的'支出'类型的账户授权数据(所有账户都加过来），需要初始化，如果没有账户，不会新建（授权明细不能为空）
     */
    public void saveSalesOrderInitOutcomeAuthData(ServiceContext serviceContext) {
        String authorizedObjectApiName = "SalesOrderObj";
        String entryCustomerFieldApiName = "account_id";

        saveInitOutcomeAuthData(serviceContext, authorizedObjectApiName, entryCustomerFieldApiName);
    }

    public void saveInitOutcomeAuthData(ServiceContext serviceContext, String authorizedObjectApiName, String entryCustomerFieldApiName) {
        String tenantId = serviceContext.getTenantId();
        User user = serviceContext.getUser();

        //是否有订单的支出授权
        IObjectData fAccountAuthorizationData = getFAccountAuthorizationData(user, authorizedObjectApiName, FAccountAuthAuthorizedTypeEnum.Outcome.getValue());
        if (fAccountAuthorizationData != null) {
            log.info("saveInitOutcomeAuthData exist, tenantId[{}]", tenantId);

            String status = fAccountAuthorizationData.get(FAccountAuthorizationConstants.Field.Status.apiName, String.class);
            log.info("saveInitOutcomeAuthData, tenantId[{}], status[{}]", tenantId, status);
            if (Objects.equals(status, FAccountAuthorizationStatusEnum.UN_INIT.getValue())) {
                fAccountAuthorizationManager.initForOutCome(serviceContext.getUser(), authorizedObjectApiName, entryCustomerFieldApiName, false, true, fAccountAuthorizationData);
            }

            return;
        }

        //所有账户
        List<IObjectData> allFundAccounts = fundAccountManager.getAccountsExcludeCredit(user, false);
        if (CollectionUtils.empty(allFundAccounts)) {
            log.warn("saveInitOutcomeAuthData fundAccounts is empty tenantId[{}]", tenantId);
            return;
        }
        if (allFundAccounts.size() > 20) {
            log.warn("saveInitOutcomeAuthData fundAccounts.size > 20, tenantId[{}]", tenantId);
            // 超过20个, 只保存现金账户
            allFundAccounts = fundAccountManager.getAccountsExcludeCredit(user, true);
            if (CollectionUtils.empty(allFundAccounts)) {
                log.warn("saveInitOutcomeAuthData onlyCashAccount fundAccounts is empty tenantId[{}]", tenantId);
                return;
            }
        }

        List<String> fundAccountIds = allFundAccounts.stream().map(IObjectData::getId).collect(Collectors.toList());
        fAccountAuthorizationData = saveOutcomeMasterAndDetail(serviceContext, authorizedObjectApiName, entryCustomerFieldApiName, fundAccountIds);

        //初始化
        fAccountAuthorizationManager.initForOutCome(serviceContext.getUser(), authorizedObjectApiName, entryCustomerFieldApiName, false, true, fAccountAuthorizationData);
    }

    public IObjectData saveOutcomeMasterAndDetail(ServiceContext serviceContext, String authorizedObjectApiName, String entryCustomerFieldApiName, List<String> fundAccountIds) {
        List<String> reduceTriggerActions = getReduceTriggerActions(authorizedObjectApiName);

        User user = serviceContext.getUser();
        IObjectData masterObjectData = ObjectDataUtil.getBaseObjectData(user, FAccountAuthorizationConstants.API_NAME);
        masterObjectData.set(FAccountAuthorizationConstants.Field.AuthorizedType.apiName, FAccountAuthAuthorizedTypeEnum.Outcome.getValue());
        masterObjectData.set(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, authorizedObjectApiName);
        masterObjectData.set(FAccountAuthorizationConstants.Field.EntryCustomerFieldApiName.apiName, entryCustomerFieldApiName);
        masterObjectData.set(FAccountAuthorizationConstants.Field.IsUnfreezeAuth.apiName, false);
        masterObjectData.set(FAccountAuthorizationConstants.Field.ReduceTriggerActions.apiName, reduceTriggerActions);
        masterObjectData.set(FAccountAuthorizationConstants.Field.Status.apiName, FAccountAuthorizationStatusEnum.HAS_INIT.getValue());
        masterObjectData.set("record_type", "default__c");

        Map<String, List<ObjectDataDocument>> detailObjectData = new HashMap<>();
        if (!CollectionUtils.empty(fundAccountIds)) {
            List<ObjectDataDocument> authorizationDetails = Lists.newArrayList();
            for (String fundAccountId : fundAccountIds) {
                IObjectData authorizationDetail = ObjectDataUtil.getBaseObjectData(user, AuthorizationDetailConstant.API_NAME);
                authorizationDetail.set(AuthorizationDetailConstant.Field.AuthorizeAccountId.apiName, fundAccountId);
                authorizationDetail.set("record_type", "default__c");

                authorizationDetails.add(ObjectDataDocument.of(authorizationDetail));
            }
            detailObjectData.put(AuthorizationDetailConstant.API_NAME, authorizationDetails);
        }

        RequestContext requestContext = serviceContext.getRequestContext();
        ActionContext actionContext = new ActionContext(requestContext, FAccountAuthorizationConstants.API_NAME, ObjectAction.CREATE.getActionCode());

        BaseObjectSaveAction.Arg saveArg = new BaseObjectSaveAction.Arg();
        saveArg.setObjectData(ObjectDataDocument.of(masterObjectData));
        saveArg.setDetails(detailObjectData);

        BaseObjectSaveAction.Result saveResult = serviceFacade.triggerAction(actionContext, saveArg, BaseObjectSaveAction.Result.class);
        return saveResult.getObjectData().toObjectData();
    }

    public IObjectData saveOutcomeMasterAndDetail(ServiceContext serviceContext, AccessOutcomeAuthModel.Arg arg) {
        String authorizedObjectApiName = arg.getObjectData().getAuthorizedObjectApiName();
        List<String> reduceTriggerActions = getReduceTriggerActions(authorizedObjectApiName);

        User user = serviceContext.getUser();
        IObjectData masterObjectData = ObjectDataUtil.getBaseObjectData(user, FAccountAuthorizationConstants.API_NAME);
        masterObjectData.set(FAccountAuthorizationConstants.Field.AuthorizedType.apiName, FAccountAuthAuthorizedTypeEnum.Outcome.getValue());
        masterObjectData.set(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, authorizedObjectApiName);
        masterObjectData.set(FAccountAuthorizationConstants.Field.EntryCustomerFieldApiName.apiName, arg.getObjectData().getEntryCustomerFieldApiName());
        masterObjectData.set(FAccountAuthorizationConstants.Field.IsUnfreezeAuth.apiName, false);
        masterObjectData.set(FAccountAuthorizationConstants.Field.ReduceTriggerActions.apiName, reduceTriggerActions);
        masterObjectData.set(FAccountAuthorizationConstants.Field.Status.apiName, FAccountAuthorizationStatusEnum.UN_INIT.getValue());
        masterObjectData.set("record_type", "default__c");

        Map<String, List<ObjectDataDocument>> detailObjectData = new HashMap<>();
        List<AccessOutcomeAuthModel.DetailFields> detailFields = arg.getDetails().get(arg.getObjectData().getAuthorizedObjectApiName());
        if (!CollectionUtils.empty(detailFields)) {
            List<ObjectDataDocument> authorizationDetails = Lists.newArrayList();
            for (AccessOutcomeAuthModel.DetailFields fields : detailFields) {
                IObjectData authorizationDetail = ObjectDataUtil.getBaseObjectData(user, AuthorizationDetailConstant.API_NAME);
                authorizationDetail.set(AuthorizationDetailConstant.Field.AuthorizeAccountId.apiName, fields.getAuthorizeAccountId());
                authorizationDetail.set("record_type", "default__c");

                authorizationDetails.add(ObjectDataDocument.of(authorizationDetail));
            }
            detailObjectData.put(AuthorizationDetailConstant.API_NAME, authorizationDetails);
        }

        RequestContext requestContext = serviceContext.getRequestContext();
        ActionContext actionContext = new ActionContext(requestContext, FAccountAuthorizationConstants.API_NAME, ObjectAction.CREATE.getActionCode());

        BaseObjectSaveAction.Arg saveArg = new BaseObjectSaveAction.Arg();
        saveArg.setObjectData(ObjectDataDocument.of(masterObjectData));
        saveArg.setDetails(detailObjectData);

        BaseObjectSaveAction.Result saveResult = serviceFacade.triggerAction(actionContext, saveArg, BaseObjectSaveAction.Result.class);
        return saveResult.getObjectData().toObjectData();
    }

    /**
     * 设置默认值
     */
    public void setDefaultValue(IObjectData fAccountAuthorization) {
        String authorizedObjectApiName = fAccountAuthorization.get(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, String.class);
        String authorizedType = fAccountAuthorization.get(FAccountAuthorizationConstants.Field.AuthorizedType.apiName, String.class);

        //支出授权：frozen_actions、reduce_trigger_actions 默认值
        if (Objects.equals(authorizedType, FAccountAuthAuthorizedTypeEnum.Outcome.getValue())) {
            boolean isUnfreezeAuth = fAccountAuthorization.get(FAccountAuthorizationConstants.Field.IsUnfreezeAuth.apiName, Boolean.class, false);
            if (isUnfreezeAuth) {
                List<String> frozenActions =  Lists.newArrayList("Add_button_default", "fieldChange");
                fAccountAuthorization.set(FAccountAuthorizationConstants.Field.FrozenActions.apiName, frozenActions);
            }

            List<String> reduceTriggerActions = fAccountAuthorizationManager.getReduceTriggerActions(authorizedObjectApiName);
            fAccountAuthorization.set(FAccountAuthorizationConstants.Field.ReduceTriggerActions.apiName, reduceTriggerActions);
        }
    }

    public void checkForAddAndEdit(User user, IObjectData objectData, Map<String, List<IObjectData>> detailObjectData) {
        String authorizedType = objectData.get(FAccountAuthorizationConstants.Field.AuthorizedType.apiName, String.class);
        try {
            String authorizedObjectApiName = objectData.get(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, String.class);
            IObjectDescribe objectDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(user.getTenantId(), authorizedObjectApiName);
            if (objectDescribe == null) {
                log.warn("[{}] not exist", authorizedObjectApiName);
                throw new ValidateException(I18N.text(CAI18NKey.AUTHORIZED_OBJECT_NOT_EXIST));
            }

            String entryCustomerFieldApiName = objectData.get(FAccountAuthorizationConstants.Field.EntryCustomerFieldApiName.apiName, String.class);
            IFieldDescribe entryCustomerField = objectDescribe.getFieldDescribe(entryCustomerFieldApiName);
            if (entryCustomerField == null) {
                log.info("checkForAddAndEdit authorizedObjectApiName[{}], entryCustomerFieldApiName[{}]", authorizedObjectApiName, entryCustomerFieldApiName);
                throw new ValidateException(I18N.text(CAI18NKey.ENTRY_CUSTOMER_FIELD_NOT_EXIST));
            }

            if (Objects.equals(authorizedType, FAccountAuthAuthorizedTypeEnum.Income.getValue())) {
                String entryAmountFieldApiName = objectData.get(FAccountAuthorizationConstants.Field.EntryAmountFieldApiName.apiName, String.class);
                IFieldDescribe entryAmountField= objectDescribe.getFieldDescribe(entryAmountFieldApiName);
                if (entryAmountField == null) {
                    log.info("checkForAddAndEdit authorizedObjectApiName[{}], entryAmountFieldApiName[{}]", authorizedObjectApiName, entryAmountFieldApiName);
                    throw new ValidateException(I18N.text(CAI18NKey.ENTRY_AMOUNT_FIELD_NOT_EXIST));
                }

                // 是否金额、数组、计算、统计字段
                List<String> entryAmountFieldType = Lists.newArrayList("currency", "number", "formula", "count");
                if (!entryAmountFieldType.contains(entryAmountField.getType())) {
                    String entryAmountFieldLabel = commonDescribeManager.getFieldLabel(user.getTenantId(), authorizedObjectApiName, entryAmountFieldApiName);
                    throw new ValidateException(I18N.text(CAI18NKey.IS_NOT_CURRENCY_OR_NUMBER, entryAmountFieldLabel));
                }
            }

            if (Objects.equals(authorizedType, FAccountAuthAuthorizedTypeEnum.Outcome.getValue())) {
                // TODO: 2024/11/7 灰度项
                /**
                 * 在黑名单的企业（不支持插件，走的fs-crm-task-web)
                 * 新建编辑【校验规则】时，选择的配置还是从配置中心读（迁移之后，才能从【账户授权】上读）
                 * 所以【支出授权】的IsUnfreezeAuth("解冻授权")不能设置为true
                 */
                if (!CaGrayUtil.supportCheckRuleDomain(user.getTenantId())) {
                    Boolean isUnfreezeAuth = objectData.get(FAccountAuthorizationConstants.Field.IsUnfreezeAuth.apiName, Boolean.class, false);
                    if (isUnfreezeAuth) {
                        //【解冻授权】不能选【是】，联系纷享订货通团队，升级校验规则
                        throw new ValidateException(I18N.text(CAI18NKey.UPDATE_ACCOUNT_CHECK_RULE));
                    }
                }
            }

            // 是否查找关联客户
            if (!Objects.equals(entryCustomerField.getType(), "object_reference")) {
                String entryCustomerFieldLabel = commonDescribeManager.getFieldLabel(user.getTenantId(), authorizedObjectApiName, entryCustomerFieldApiName);
                throw new ValidateException(I18N.text(CAI18NKey.IS_NOT_OBJECT_REFERENCE, entryCustomerFieldLabel));
            }
            if (!Objects.equals(entryCustomerField.get("target_api_name", String.class), "AccountObj")) {
                String entryCustomerFieldLabel = commonDescribeManager.getFieldLabel(user.getTenantId(), authorizedObjectApiName, entryCustomerFieldApiName);
                throw new ValidateException(I18N.text(CAI18NKey.NOT_REFERENCE_TO_ACCOUNTOBJ, entryCustomerFieldLabel));
            }


            //从对象不能为空
            if (CollectionUtils.empty(detailObjectData)) {
                throw new ValidateException(I18N.text(CAI18NKey.DETAIL_OBJECT_IS_EMPTY));
            }

            //校验是否能选返利账户
            checkRebateAccount(user, objectData, detailObjectData);
        } catch (MetadataServiceException e) {
            log.warn("objectDescribeService.findByTenantIdAndDescribeApiName failed ,tenantId:{}, objectDescribeApiName:{}", user.getTenantId(), FAccountAuthorizationConstants.API_NAME, e);
            throw new CustomerAccountBusinessException(() -> e.getErrorCode().getCode(),  I18N.text(CAI18NKey.QUERY_OBJECT_DESCRIBE_FAILED) + e.getMessage());
        }

        String tenantId = user.getTenantId();
        checkAuthorizationDetail(tenantId, authorizedType, detailObjectData);
        checkAndSetAccountEntryRule(tenantId, objectData, detailObjectData);
        checkUnfreezeAuthDetail(tenantId, objectData, detailObjectData);
    }

    /**
     * 订单、合同…… 之外的对象，不能选返利账户
     */
    private void checkRebateAccount(User user, IObjectData objectData, Map<String, List<IObjectData>> detailObjectData) {
        String tenantId = user.getTenantId();
        String authorizedObjectApiName = objectData.get(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, String.class);
        String authorizedType = objectData.get(FAccountAuthorizationConstants.Field.AuthorizedType.apiName, String.class);

        if (Objects.equals(authorizedType, FAccountAuthAuthorizedTypeEnum.Income.getValue())) {
            //配置中心公共的对象
            List<String> incomeAccountAuthCanUseRebateAccountObjectApiNames = ConfigCenter.incomeAccountAuthCanUseRebateAccountObjectApiNames;
            if (!CollectionUtils.empty(incomeAccountAuthCanUseRebateAccountObjectApiNames)
                    && incomeAccountAuthCanUseRebateAccountObjectApiNames.contains(authorizedObjectApiName)) {
                return;
            }

            //mt_bizconf中本企业的对象
            Set<String> allowIncomeObjectApiNames = fundAccountConfigManager.getAccountAuthCanUseRebateAccountObjectApiNames(tenantId, FAccountAuthAuthorizedTypeEnum.Income.getValue());
            if (!CollectionUtils.empty(allowIncomeObjectApiNames)
                    && allowIncomeObjectApiNames.contains(authorizedObjectApiName)) {
                return;
            }
        } else if (Objects.equals(authorizedType, FAccountAuthAuthorizedTypeEnum.Outcome.getValue())) {
            //配置中心公共的对象
            List<String> outcomeAccountAuthCanUseRebateAccountObjectApiNames = ConfigCenter.outcomeAccountAuthCanUseRebateAccountObjectApiNames;
            if (!CollectionUtils.empty(outcomeAccountAuthCanUseRebateAccountObjectApiNames)
                    && outcomeAccountAuthCanUseRebateAccountObjectApiNames.contains(authorizedObjectApiName)) {
                return;
            }

            //mt_bizconf中本企业的对象
            Set<String> allowOutcomeObjectApiNames = fundAccountConfigManager.getAccountAuthCanUseRebateAccountObjectApiNames(tenantId, FAccountAuthAuthorizedTypeEnum.Outcome.getValue());
            if (!CollectionUtils.empty(allowOutcomeObjectApiNames)
                    && allowOutcomeObjectApiNames.contains(authorizedObjectApiName)) {
                return;
            }
        }

        List<IObjectData> details = detailObjectData.get(AuthorizationDetailConstant.API_NAME);
        if (CollectionUtils.empty(details)) {
            return;
        }

        List<String> authorizeAccountIds = details.stream().map(d -> d.get(AuthorizationDetailConstant.Field.AuthorizeAccountId.apiName, String.class)).collect(Collectors.toList());
        if (CollectionUtils.empty(authorizeAccountIds)) {
            return;
        }

        //authorizeAccountIds中是否有返利类型的
        List<String> fields = Lists.newArrayList(FundAccountConstants.Field.Name.apiName, SystemConstants.Field.Id.apiName);
        List<IObjectData> rebateAccounts = fundAccountManager.getAccountsByAccessModule(user.getTenantId(), authorizeAccountIds, AccessModuleEnum.REBATE.value, fields);
        if (!CollectionUtils.empty(rebateAccounts)) {
            List<String> rebateAccountNames = rebateAccounts.stream().map(d -> d.get(FundAccountConstants.Field.Name.apiName, String.class)).collect(Collectors.toList());
            String rebateAccountNameStr = Joiner.on("','").join(rebateAccountNames);
            throw new ValidateException(I18N.text(CAI18NKey.CANNOT_USE_REBATE_ACCOUNT, rebateAccountNameStr));
        }
    }

    private void checkAuthorizationDetail(String tenantId, String authorizedType, Map<String, List<IObjectData>> detailObjectData) {
        //授权明细
        List<IObjectData> details = detailObjectData.get(AuthorizationDetailConstant.API_NAME);
        if (!CollectionUtils.empty(details)) {
            if (details.size() > 1) {
                //从对象只能一个默认入账账户
                List<IObjectData> defaultAccounts = details.stream().filter(d -> Objects.equals(true, d.get(AuthorizationDetailConstant.Field.IsDefaultEntryAccount.apiName, Boolean.class))).collect(Collectors.toList());
                if (!org.apache.commons.collections.CollectionUtils.isEmpty(defaultAccounts) && defaultAccounts.size() > 1) {
                    throw new ValidateException(I18N.text(CAI18NKey.MORE_THAN_ONE_DEFAULT_ENTRY_ACCOUNT));
                }

                //最多20个
                if (details.size() > ConfigCenter.authorizationDetailCountLimit) {
                    throw new ValidateException(I18N.text(CAI18NKey.ACCOUNT_NUM_MORE_THAN_LIMIT, ConfigCenter.authorizationDetailCountLimit));
                }
            }

            // 支出授权不能用【信用账户】
            if (Objects.equals(authorizedType, FAccountAuthAuthorizedTypeEnum.Outcome.getValue())) {
                List<String> authorizeAccountIds = details.stream().map(d -> d.get(AuthorizationDetailConstant.Field.AuthorizeAccountId.apiName, String.class)).collect(Collectors.toList());
                checkNoUseCreditFundAccount(tenantId, authorizeAccountIds);
            }
        }
    }

    private void checkUnfreezeAuthDetail(String tenantId, IObjectData accountAuth, Map<String, List<IObjectData>> detailObjectData) {
        String authorizedType = accountAuth.get(FAccountAuthorizationConstants.Field.AuthorizedType.apiName, String.class);
        if (!Objects.equals(authorizedType, FAccountAuthAuthorizedTypeEnum.Outcome.getValue())) {
            return;
        }

        //授权明细˚
        List<IObjectData> unfreezeAuthDetails = detailObjectData.get(UnfreezeAuthDetailConstants.API_NAME);
        if (!CollectionUtils.empty(unfreezeAuthDetails)) {
            //最多3个
            if (unfreezeAuthDetails.size() > ConfigCenter.unfreezeAuthDetailObjCountLimit) {
                log.warn("checkUnfreezeAuthDetail tenantId[{}], unfreezeAuthDetails.size[{}]", tenantId, unfreezeAuthDetails.size());
                throw new ValidateException(I18N.text(CAI18NKey.UNFREEZE_AUTH_DETAIL_NUM_MORE_THAN_LIMIT, ConfigCenter.unfreezeAuthDetailObjCountLimit));
            }

            //从对象不能重复
            List<String> unfreezeObjects = unfreezeAuthDetails.stream().map(d -> d.get(UnfreezeAuthDetailConstants.Field.UnfreezeObject.apiName, String.class)).collect(Collectors.toList());
            Set<String> unfreezeObjectsSet = Sets.newHashSet(unfreezeObjects);
            if (!Objects.equals(unfreezeObjects.size(), unfreezeObjectsSet.size())) {
                log.warn("checkUnfreezeAuthDetail tenantId[{}], unfreezeObjects[{}], unfreezeObjectsSet[{}]", tenantId, unfreezeObjects, unfreezeObjectsSet);
                throw new ValidateException(I18N.text(CAI18NKey.UNFREEZE_AUTH_DETAIL_UNFREEZE_OBJECT_CAN_NOT_REPEAT));
            }

            //一个对象，作为解冻对象的次数限制（看下线上，尽量不超过2个）
            for (IObjectData detail : unfreezeAuthDetails) {
                String unfreezeObject = detail.get(UnfreezeAuthDetailConstants.Field.UnfreezeObject.apiName, String.class);
                String unfreezeAuthDetailId = detail.getId();

                //做为解冻对象的数据
                List<IObjectData> beUnfreezeObjects = unfreezeAuthDetailManager.query(tenantId, unfreezeObject);
                if (CollectionUtils.empty(beUnfreezeObjects)) {
                    continue;
                }
                int count = beUnfreezeObjects.size();
                if (Strings.isNullOrEmpty(unfreezeAuthDetailId)) {
                    //保存完会加一条
                    count = count + 1;
                }
                if (count > ConfigCenter.beUnfreezeObjectCountLimit) {
                    List<String> unfreezeObjectNameList = beUnfreezeObjects.stream().map(IObjectData::getName).collect(Collectors.toList());
                    String unfreezeObjectNames = String.join(",", unfreezeObjectNameList);
                    String multiLangName = commonLangManager.getMultiLangName(tenantId, unfreezeObject);
                    log.warn("checkUnfreezeAuthDetail tenantId[{}], detailObjectData[{}], count[{}], unfreezeObjectNameList[{}]", tenantId, detailObjectData, count, unfreezeObjectNameList);
                    throw new ValidateException(I18N.text(CAI18NKey.BE_UNFREEZE_OBJECT_LIMIT, multiLangName, ConfigCenter.beUnfreezeObjectCountLimit, unfreezeObjectNames));
                }
            }
        } else {
            boolean isUnfreezeAuth = accountAuth.get(FAccountAuthorizationConstants.Field.IsUnfreezeAuth.apiName, Boolean.class, false);
            if (isUnfreezeAuth) {
                throw new ValidateException(I18N.text(CAI18NKey.UNFREEZE_AUTH_DETAIL_CANNOT_EMPTY));
            }
        }
    }

    /**
     * 不能用【信用账户】
     */
    private void checkNoUseCreditFundAccount(String tenantId, List<String> fundAccountIds) {
        if (CollectionUtils.empty(fundAccountIds)) {
            return;
        }

        List<String> fields = Lists.newArrayList(FundAccountConstants.Field.Name.apiName, SystemConstants.Field.Id.apiName);
        List<IObjectData> creditAccounts = fundAccountManager.getAccountsByAccessModule(tenantId, fundAccountIds, AccessModuleEnum.CREDIT.value, fields);
        if (!CollectionUtils.empty(creditAccounts)) {
            List<String> creditAccountNameList = creditAccounts.stream().map(IObjectData::getName).collect(Collectors.toList());
            log.warn("checkAuthorizationDetail tenantId[{}], creditAccountNameList[{}]", tenantId, creditAccountNameList);
            String creditAccountNames = Joiner.on(",").join(creditAccountNameList);
            throw new ValidateException(I18N.text(CAI18NKey.CAN_NOT_USE_CREDIT_FUND_ACCOUNT, creditAccountNames));
        }
    }

    private void checkAndSetAccountEntryRule(String tenantId, IObjectData objectData, Map<String, List<IObjectData>> detailObjectData) {
        boolean autoEntryStatus = objectData.get(FAccountAuthorizationConstants.Field.AutoEntryStatus.apiName, Boolean.class, false);
        if (!autoEntryStatus) {
            List<IObjectData> fAccountEntryRules = detailObjectData.get(FAccountEntryRuleConstants.API_NAME);
            if (!CollectionUtils.empty(fAccountEntryRules)) {
                detailObjectData.put(FAccountEntryRuleConstants.API_NAME, Lists.newArrayList());
            }
            return;
        }

        List<IObjectData> fAccountEntryRules = detailObjectData.get(FAccountEntryRuleConstants.API_NAME);
        List<IObjectData> authorizationDetails = detailObjectData.get(AuthorizationDetailConstant.API_NAME);

        //'入账规则'不能为空
        if (CollectionUtils.empty(fAccountEntryRules)) {
            throw new ValidateException(I18N.text(CAI18NKey.FACCOUNT_ENTRY_RULE_CAN_NOT_BE_EMPTY));
        }

        //最多n组规则
        if (fAccountEntryRules.size() > ConfigCenter.fAccountEntryRuleObjCountLimit) {
            throw new ValidateException(I18N.text(CAI18NKey.FACCOUNT_ENTRY_RULE_MORE_THAN_LIMIT, ConfigCenter.fAccountEntryRuleObjCountLimit));
        }

        //账户要在'授权明细'里面
        List<String> authorizeAccountIds = authorizationDetails.stream().map(d -> d.get(AuthorizationDetailConstant.Field.AuthorizeAccountId.apiName, String.class)).collect(Collectors.toList());
        for (IObjectData fAccountEntryRule : fAccountEntryRules) {
            String fAccountId = fAccountEntryRule.get(FAccountEntryRuleConstants.Field.FAccountId.apiName, String.class);
            if (!authorizeAccountIds.contains(fAccountId)) {
                String fundAccountName = commonObjDataManager.getName(tenantId, fAccountId, FundAccountConstants.API_NAME);
                throw new ValidateException(I18N.text(CAI18NKey.FACCOUNT_NOT_IN_AUTHORIZATION_DETAIL, fundAccountName));
            }
        }

        // 支出授权不能用【信用账户】
        String authorizedType = objectData.get(FAccountAuthorizationConstants.Field.AuthorizedType.apiName, String.class);
        if (Objects.equals(authorizedType, FAccountAuthAuthorizedTypeEnum.Outcome.getValue())) {
            List<String> fAccountIds = fAccountEntryRules.stream().map(d -> d.get(FAccountEntryRuleConstants.Field.FAccountId.apiName, String.class)).collect(Collectors.toList());
            checkNoUseCreditFundAccount(tenantId, fAccountIds);
        }

        //补充顺序
        for (int i = 0; i < fAccountEntryRules.size(); i++) {
            IObjectData fAccountEntryRule = fAccountEntryRules.get(i);
            fAccountEntryRule.set(FAccountEntryRuleConstants.Field.Sequence.apiName, i);
        }
    }

    /**
     * 未初始化的支出授权，如果被校验规则使用了，不可用修改'对象'、'客户字段'、'账户'
     */
    public void checkForEdit(User user, IObjectData objectData, IObjectData dbMasterData, Map<String, List<IObjectData>> detailObjectData, Map<String, List<IObjectData>> dbDetailDataMap) {
        String status = objectData.get(FAccountAuthorizationConstants.Field.Status.apiName, String.class);

        String authorizedObjectApiName = objectData.get(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, String.class);
        String dbAuthorizedObjectApiName = dbMasterData.get(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, String.class);

        String entryCustomerFieldApiName = objectData.get(FAccountAuthorizationConstants.Field.EntryCustomerFieldApiName.apiName, String.class);
        String dbEntryCustomerFieldApiName = dbMasterData.get(FAccountAuthorizationConstants.Field.EntryCustomerFieldApiName.apiName, String.class);

        if (Objects.equals(status, FAccountAuthorizationStatusEnum.HAS_INIT.getValue())) {
            if (!Objects.equals(authorizedObjectApiName, dbAuthorizedObjectApiName)) {
                throw new ValidateException(I18N.text(CAI18NKey.HAS_INIT_AUTHORIZED_OBJECT_API_NAME_CAN_NOT_CHANGE));
            }

            if (!Objects.equals(entryCustomerFieldApiName, dbEntryCustomerFieldApiName)) {
                throw new ValidateException(I18N.text(CAI18NKey.HAS_INIT_ENTRY_CUSTOMER_FIELD_API_NAME_CAN_NOT_CHANGE));
            }
        }

        checkForEditOutcomeAuth(user, objectData, dbMasterData, detailObjectData, dbDetailDataMap);
    }

    //支出授权，是否有校验规则用到了(对象、客户字段、账户）
    private void checkForEditOutcomeAuth(User user, IObjectData objectData, IObjectData dbMasterData, Map<String, List<IObjectData>> detailObjectData, Map<String, List<IObjectData>> dbDetailDataMap) {
        String authorizedType = objectData.get(FAccountAuthorizationConstants.Field.AuthorizedType.apiName, String.class);
        if (!Objects.equals(authorizedType, FAccountAuthAuthorizedTypeEnum.Outcome.getValue())) {
            return;
        }

        String authorizedObjectApiName = objectData.get(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, String.class);
        String dbAuthorizedObjectApiName = dbMasterData.get(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, String.class);

        String entryCustomerFieldApiName = objectData.get(FAccountAuthorizationConstants.Field.EntryCustomerFieldApiName.apiName, String.class);
        String dbEntryCustomerFieldApiName = dbMasterData.get(FAccountAuthorizationConstants.Field.EntryCustomerFieldApiName.apiName, String.class);

        checkIsUnfreezeAuth(user, objectData, dbMasterData);
        checkForEditUnfreezeAuthDetail(user, objectData, detailObjectData, dbDetailDataMap);

        //支出授权，是否有校验规则用到了(对象、客户字段、账户）
        boolean needCheckAuthorizedObjectApiName = !Objects.equals(authorizedObjectApiName, dbAuthorizedObjectApiName);
        boolean needEntryCustomerFieldApiName = !Objects.equals(entryCustomerFieldApiName, dbEntryCustomerFieldApiName);
        List<String> toDeleteFundAccountIds = toDeleteFundAccountIds(detailObjectData, dbDetailDataMap);

        if (!needCheckAuthorizedObjectApiName && !needEntryCustomerFieldApiName && CollectionUtils.empty(toDeleteFundAccountIds)) {
            return;
        }

        List<String> fields = Lists.newArrayList(AccountCheckRuleConstants.Field.Name.apiName, AccountCheckRuleConstants.Field.RuleType.apiName,
                AccountCheckRuleConstants.Field.CheckObject.apiName, AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName,
                AccountCheckRuleConstants.Field.OccupiedMapping.apiName, AccountCheckRuleConstants.Field.ReduceMapping.apiName);
        List<IObjectData> allAccountCheckRuleDatas = accountCheckRuleManager.getAllAccountCheckRuleDatas(user.getTenantId(), false, fields);
        if (CollectionUtils.empty(allAccountCheckRuleDatas)) {
            return;
        }

        for (IObjectData accountCheckRuleData : allAccountCheckRuleDatas) {
            String ruleType = accountCheckRuleData.get(AccountCheckRuleConstants.Field.RuleType.apiName, String.class);
            String name = accountCheckRuleData.get(AccountCheckRuleConstants.Field.Name.apiName, String.class);

            if (Objects.equals(ruleType, AccountCheckRuleTypeEnum.Check_Reduce.getValue())) {
                //冻结
                String checkObject = accountCheckRuleData.get(AccountCheckRuleConstants.Field.CheckObject.apiName, String.class);
                if (Objects.equals(checkObject, authorizedObjectApiName)) {
                    //对象
                    if (needCheckAuthorizedObjectApiName) {
                        throw new ValidateException(I18N.text(CAI18NKey.ACCOUNT_CHECK_RULE_HAS_USE_OBJECT, name));
                    }

                    List<ObjectMappingModel> occupiedMappingObjectMappings = RuleHandlerUtil.getObjectMapping(accountCheckRuleData, AccountCheckRuleConstants.Field.OccupiedMapping.apiName);
                    //是否用了客户字段
                    if (needEntryCustomerFieldApiName) {
                        boolean hasCustomerId = AccountCheckRuleMappingUtil.hasCustomerId(occupiedMappingObjectMappings, entryCustomerFieldApiName, AccountFrozenRecordConstant.Field.AccountId.apiName);
                        if (hasCustomerId) {
                            throw new ValidateException(I18N.text(CAI18NKey.ACCOUNT_CHECK_RULE_HAS_USE_ENTRY_CUSTOMER_FIELD, name));
                        }
                    }

                    //是否用了要删的账户
                    if(!CollectionUtils.empty(toDeleteFundAccountIds)) {
                        for (String toDeleteFundAccountId : toDeleteFundAccountIds) {
                            boolean hasFundAccountId = AccountCheckRuleMappingUtil.hasFundAccountId(occupiedMappingObjectMappings, toDeleteFundAccountId);
                            if (hasFundAccountId) {
                                //查账户名称
                                String fundAccountName = commonObjDataManager.getName(user.getTenantId(), toDeleteFundAccountId, FundAccountConstants.API_NAME);
                                throw new ValidateException(I18N.text(CAI18NKey.ACCOUNT_CHECK_RULE_HAS_USE_FUND_ACCOUNT, name, fundAccountName));
                            }
                        }
                    }
                }
            }


            //扣减
            String reduceRelatedObject = accountCheckRuleData.get(AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName, String.class);
            if (Objects.equals(reduceRelatedObject, authorizedObjectApiName)) {
                if (needCheckAuthorizedObjectApiName) {
                    throw new ValidateException(I18N.text(CAI18NKey.ACCOUNT_CHECK_RULE_HAS_USE_OBJECT, name));
                }

                List<ObjectMappingModel> reduceMappingObjectMappings = RuleHandlerUtil.getObjectMapping(accountCheckRuleData, AccountCheckRuleConstants.Field.ReduceMapping.apiName);
                if (needEntryCustomerFieldApiName) {
                    //是否用了客户字段
                    boolean hasCustomerId = AccountCheckRuleMappingUtil.hasCustomerId(reduceMappingObjectMappings, entryCustomerFieldApiName, AccountTransactionFlowConst.Field.Customer.apiName);
                    if (hasCustomerId) {
                        throw new ValidateException(I18N.text(CAI18NKey.ACCOUNT_CHECK_RULE_HAS_USE_ENTRY_CUSTOMER_FIELD, name));
                    }
                }

                //是否用了要删的账户
                if(!CollectionUtils.empty(toDeleteFundAccountIds)) {
                    for (String toDeleteFundAccountId : toDeleteFundAccountIds) {
                        boolean hasFundAccountId = AccountCheckRuleMappingUtil.hasFundAccountId(reduceMappingObjectMappings, toDeleteFundAccountId);
                        if (hasFundAccountId) {
                            //查账户名称
                            String fundAccountName = commonObjDataManager.getName(user.getTenantId(), toDeleteFundAccountId, FundAccountConstants.API_NAME);
                            throw new ValidateException(I18N.text(CAI18NKey.ACCOUNT_CHECK_RULE_HAS_USE_FUND_ACCOUNT, name, fundAccountName));
                        }
                    }
                }
            }
        }
    }

    /**
     * 【支出授权】编辑，校验IsUnfreezeAuth（是->否）时 校验是否被【校验扣减】的【校验规则】使用了
     */
    private void checkIsUnfreezeAuth(User user, IObjectData objectData, IObjectData dbMasterData) {
        String authorizedType = objectData.get(FAccountAuthorizationConstants.Field.AuthorizedType.apiName, String.class);
        if (!Objects.equals(authorizedType, FAccountAuthAuthorizedTypeEnum.Outcome.getValue())) {
            return;
        }
        String tenantId = user.getTenantId();

        Boolean isUnfreezeAuth = objectData.get(FAccountAuthorizationConstants.Field.IsUnfreezeAuth.apiName, Boolean.class);
        Boolean dbIsUnfreezeAuth = dbMasterData.get(FAccountAuthorizationConstants.Field.IsUnfreezeAuth.apiName, Boolean.class, false);
        if (dbIsUnfreezeAuth && !isUnfreezeAuth) {
            String authorizedObjectApiName = objectData.get(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, String.class);

            //查出所有的【校验扣减】的【校验规则】
            List<IObjectData> allCheckRuleAccountCheckRules = accountCheckRuleManager.getAccountCheckRuleData(tenantId, AccountCheckRuleTypeEnum.Check_Reduce.getValue(), authorizedObjectApiName);
            if (CollectionUtils.empty(allCheckRuleAccountCheckRules)) {
                return;
            }
            List<Object> names = commonLangManager.getObjectMultiLangValues(allCheckRuleAccountCheckRules, AccountCheckRuleConstants.Field.Name.apiName);
            List<String> nameList = names.stream().map(n -> (String) n).collect(Collectors.toList());
            String checkRuleAccountCheckRuleNames = String.join(",", nameList);

            String multiLangName = commonLangManager.getMultiLangName(tenantId, authorizedObjectApiName);
            throw new ValidateException(I18N.text(CAI18NKey.IS_UNFREEZE_AUTH_CAN_NOT_CLOSED, multiLangName, checkRuleAccountCheckRuleNames));
        }
    }

    /**
     * 校验【解冻授权明细】
     * 有校验扣减的【校验规则】，就不能删、不能更换从对象里面的对象和字段
     */
    private void checkForEditUnfreezeAuthDetail(User user, IObjectData fAccountAuthorization, Map<String, List<IObjectData>> detailObjectData, Map<String, List<IObjectData>> dbDetailDataMap) {
        String tenantId = user.getTenantId();

        boolean isUnfreezeAuth = fAccountAuthorization.get(FAccountAuthorizationConstants.Field.IsUnfreezeAuth.apiName, Boolean.class, false);
        if (!isUnfreezeAuth) {
            return;
        }

        String objectApiName = UnfreezeAuthDetailConstants.API_NAME;
        List<IObjectData> unfreezeAuthDetails = detailObjectData.get(objectApiName);
        Map<String, String> unfreezeObject2UnfreezeAuthDetailId = new HashMap<>();
        if (!CollectionUtils.empty(unfreezeAuthDetails)) {
            unfreezeObject2UnfreezeAuthDetailId = unfreezeAuthDetails.stream().collect(Collectors.toMap(d -> d.get(UnfreezeAuthDetailConstants.Field.UnfreezeObject.apiName, String.class), IObjectData::getId));
        }

        //删掉的UnfreezeObject"解冻对象",是否被使用了
        List<String> toDeleteUnfreezeObjects = unfreezeAuthDetailManager.getToDeleteUnfreezeObjects(detailObjectData, dbDetailDataMap);
        checkForEditUnfreezeAuthDetail(tenantId, unfreezeObject2UnfreezeAuthDetailId, toDeleteUnfreezeObjects, CAI18NKey.OBJECT_HAS_BE_USED_UNFREEZE_AUTH_DETAIL_CAN_NOT_DELETED);

        //"解冻对象"被【校验扣减】的校验规则使用了的话，RelatedField("关联字段")不能被修改
        List<String> relatedFieldChangeUnfreezeObjects = unfreezeAuthDetailManager.getRelatedFieldChangeUnfreezeObjects(detailObjectData, dbDetailDataMap);
        checkForEditUnfreezeAuthDetail(tenantId, unfreezeObject2UnfreezeAuthDetailId, relatedFieldChangeUnfreezeObjects, CAI18NKey.OBJECT_HAS_BE_USED_RELATED_FIELD_CAN_NOT_MODIFY);
    }

    /**
     * 校验【解冻授权明细】能否调整（删除、修改RelatedField("关联字段")……）
     */
    private void checkForEditUnfreezeAuthDetail(String tenantId, Map<String, String> unfreezeObject2UnfreezeAuthDetailId, List<String> checkUnfreezeObjects, String key) {
        if (CollectionUtils.empty(checkUnfreezeObjects)) {
            return;
        }

        for (String checkUnfreezeObject : checkUnfreezeObjects) {
            String unfreezeAuthDetailId = unfreezeObject2UnfreezeAuthDetailId.get(checkUnfreezeObject);
            List<String> hasUseUnfreezeAuthDetailAccountCheckRuleList = unfreezeAuthDetailManager.getHasUseUnfreezeAuthDetailAccountCheckRules(tenantId, unfreezeAuthDetailId);
            if (!CollectionUtils.empty(hasUseUnfreezeAuthDetailAccountCheckRuleList)) {
                log.info("checkForEditUnfreezeAuthDetail tenantId[{}], unfreezeAuthDetailId[{}] has user by hasUseUnfreezeAuthDetailAccountCheckRuleList[{}], toDeleteUnfreezeObject[{}], key[{}]",
                        tenantId, unfreezeAuthDetailId, hasUseUnfreezeAuthDetailAccountCheckRuleList, checkUnfreezeObject, key);
                String hasUseUnfreezeAuthDetailAccountCheckRules = String.join(",", hasUseUnfreezeAuthDetailAccountCheckRuleList);
                String multiLangName = commonLangManager.getMultiLangName(tenantId, checkUnfreezeObject);
                throw new ValidateException(I18N.text(key, multiLangName, hasUseUnfreezeAuthDetailAccountCheckRules));
            }
        }
    }

    /**
     * 删掉的账户
     */
    private List<String> toDeleteFundAccountIds(Map<String, List<IObjectData>> detailObjectData, Map<String, List<IObjectData>> dbDetailDataMap) {
        String objectApiName = AuthorizationDetailConstant.API_NAME;
        List<String> newAuthorizeAccountIds = !detailObjectData.containsKey(objectApiName) ?
                new ArrayList<>() : detailObjectData.get(objectApiName).stream().map(d -> d.get(AuthorizationDetailConstant.Field.AuthorizeAccountId.apiName, String.class)).collect(Collectors.toList());

        List<String> dbAuthorizeAccountIds = !dbDetailDataMap.containsKey(objectApiName) ?
                new ArrayList<>() : dbDetailDataMap.get(objectApiName).stream().map(d -> d.get(AuthorizationDetailConstant.Field.AuthorizeAccountId.apiName, String.class)).collect(Collectors.toList());

        return dbAuthorizeAccountIds.stream().filter(id -> !newAuthorizeAccountIds.contains(id)).collect(Collectors.toList());
    }

    /**
     * 865支持'支出授权'，刷'账户授权'定义
     *
     * 1 主对象加新增的字段（authorized_type）
     * 2 从对象加新增的字段（trade_amount_fieldapiname）
     * 3 主对象“金额字段apiName"改为 非必填 (describe+layout)
     * 4 FAccountAuthorizationObj业务类型 label和description改为:预设业务类型
     */
    public void updateAccountAuthDescribe(String tenantId, Boolean updateEntryAmountFieldApiNameLayoutRequire) {
        //是否开启了账户授权
        boolean isAccountAuthOpen = fundAccountConfigManager.isAccountAuthOpen(tenantId);
        if (!isAccountAuthOpen) {
            return;
        }

        User user = new User(tenantId, "-10000");

        //1 主对象加新增的字段（authorized_type）
        describeAddAuthorizedTypeField(tenantId);
        ILayout accountAuthDetailLayout = serviceFacade.findDefaultLayout(user, SystemConstants.LayoutType.Detail.layoutType, FAccountAuthorizationConstants.API_NAME);
        detailLayoutAddAuthorizedTypeField(tenantId, accountAuthDetailLayout);

        //2 从对象加新增的字段（trade_amount_fieldapiname）
        describeAddTradeAmountField(tenantId);
        detailLayoutAddTradeAmountField(user);

        //3 主对象“金额字段apiName"改为 非必填 (describe+layout)
        //4 FAccountAuthorizationObj业务类型 label和description改为:预设业务类型
        updateFAccountAuthorizationDescribeField(tenantId);
        log.info("commonDetailLayoutManager.updateFieldRequired user[{}], updateEntryAmountFieldApiNameLayoutRequire[{}]", user, updateEntryAmountFieldApiNameLayoutRequire);
        if (updateEntryAmountFieldApiNameLayoutRequire) {
            String entryAmountFieldApiName = FAccountAuthorizationConstants.Field.EntryAmountFieldApiName.apiName;
            commonDetailLayoutManager.updateFieldRequired(user, accountAuthDetailLayout, entryAmountFieldApiName, false);
        }
    }

    public void updateField(String tenantId, String objectApiName, String fieldApiName) {
        IObjectDescribe objectDescribe = commonDescribeManager.findByTenantIdAndDescribeApiName(tenantId, objectApiName);
        List<IFieldDescribe> fieldDescribes = objectDescribe.getFieldDescribes();
        for (int i =0; i< fieldDescribes.size(); i++) {
            IFieldDescribe fieldDescribe = fieldDescribes.get(i);
            if (fieldDescribe.getApiName().equals(fieldApiName)) {
                if (fieldDescribe.get("is_extend", Boolean.class)) {
                    return;
                }
                fieldDescribe.setIsExtend(true);
                break;
            }
        }
        objectDescribe.setFieldDescribes(fieldDescribes);
        commonDescribeManager.replace(objectDescribe);
    }

    public void deleteField(String tenantId, String objectApiName, String fieldApiName) {
        IObjectDescribe objectDescribe = commonDescribeManager.findByTenantIdAndDescribeApiName(tenantId, objectApiName);
        List<IFieldDescribe> fieldDescribes = objectDescribe.getFieldDescribes();
        for (int i =0; i< fieldDescribes.size(); i++) {
            IFieldDescribe fieldDescribe = fieldDescribes.get(i);
            if (fieldDescribe.getApiName().equals(fieldApiName)) {
                fieldDescribes.remove(i);
                break;
            }
        }

        objectDescribe.setFieldDescribes(fieldDescribes);

        commonDescribeManager.replace(objectDescribe);
    }

    public void createField(String tenantId, String objectApiName, String fieldApiName, String fieldLabel) {
        IObjectDescribe objectDescribe = commonDescribeManager.findByTenantIdAndDescribeApiName(tenantId, objectApiName);
        List<IFieldDescribe> fieldDescribes = objectDescribe.getFieldDescribes();
        CurrencyFieldDescribe fieldDescribe = CurrencyFieldDescribeBuilder.builder()
                .apiName(fieldApiName).label(fieldLabel)
                .required(false).maxLength(14).length(12).decimalPlaces(2).roundMode(4).currencyUnit("￥").build(); // 本位币是美元的企业，页面上加自定义字段，currencyUnit也是￥
        fieldDescribe.setDefineType("custom");
        fieldDescribe.setStatus("new");
        fieldDescribe.setIsExtend(true);
        fieldDescribes.add(fieldDescribe);
        objectDescribe.setFieldDescribes(fieldDescribes);

        commonDescribeManager.replace(objectDescribe);
    }

    private void describeAddAuthorizedTypeField(String tenantId) {
        String objectApiName = FAccountAuthorizationConstants.API_NAME;
        String addFieldApiName = FAccountAuthorizationConstants.Field.AuthorizedType.apiName;

        IObjectDescribe objectDescribe = commonDescribeManager.findByTenantIdAndDescribeApiName(tenantId, objectApiName);

        List<ISelectOption> authorizedTypeSelectOptions = Arrays.stream(FAccountAuthAuthorizedTypeEnum.values())
                .map(authorizedTypeEnum -> SelectOptionBuilder.builder().value(authorizedTypeEnum.getValue()).label(authorizedTypeEnum.getLabel()).build()).collect(Collectors.toList());
        SelectOneFieldDescribe authorizedType = SelectOneFieldDescribeBuilder.builder()
                .apiName(addFieldApiName).label(FAccountAuthorizationConstants.Field.AuthorizedType.label)
                .selectOptions(authorizedTypeSelectOptions).required(true).defaultValud(FAccountAuthAuthorizedTypeEnum.Income.getValue()).build();

        commonDescribeManager.addFieldDescribe(tenantId, objectApiName, objectDescribe, addFieldApiName, authorizedType);
    }

    private void describeAddTradeAmountField(String tenantId) {
        String objectApiName = AuthorizationDetailConstant.API_NAME;
        String addFieldApiName = AuthorizationDetailConstant.Field.TradeAmountFieldapiname.apiName;

        IObjectDescribe objectDescribe = commonDescribeManager.findByTenantIdAndDescribeApiName(tenantId, objectApiName);

        TextFieldDescribe TradeAmountFieldApiName = TextFieldDescribeBuilder.builder()
                .apiName(addFieldApiName)
                .label(AuthorizationDetailConstant.Field.TradeAmountFieldapiname.label)
                .maxLength(200).required(false).build();

        commonDescribeManager.addFieldDescribe(tenantId, objectApiName, objectDescribe, addFieldApiName, TradeAmountFieldApiName);
    }

    private void detailLayoutAddAuthorizedTypeField(String tenantId, ILayout accountAuthDetailLayout) {
        User user = new User(tenantId, "-10000");
        String addFieldApiName = FAccountAuthorizationConstants.Field.AuthorizedType.apiName;

        IFormField authorizedType = FormFieldBuilder.builder().fieldName(addFieldApiName).renderType(SystemConstants.RenderType.SelectOne.renderType).readOnly(false).required(true).build();

        //detailLayout 添加字段
        commonDetailLayoutManager.detailLayoutAddField(user, accountAuthDetailLayout, addFieldApiName, authorizedType);
    }

    private void detailLayoutAddTradeAmountField(User user) {
        String objectApiName = AuthorizationDetailConstant.API_NAME;
        String addFieldApiName = AuthorizationDetailConstant.Field.TradeAmountFieldapiname.apiName;

        IFormField tradeAmountFieldApiName = FormFieldBuilder.builder().fieldName(addFieldApiName).renderType(SystemConstants.RenderType.Text.renderType).readOnly(false).required(false).build();

        //detailLayout 添加字段
        commonDetailLayoutManager.detailLayoutAddField(user, objectApiName, addFieldApiName, tradeAmountFieldApiName);
    }

    /**
     * 主对象“金额字段apiName"改为 非必填 (describe)
     * FAccountAuthorizationObj业务类型 label和description改为:预设业务类型
     */
    private void updateFAccountAuthorizationDescribeField(String tenantId) {
        String objectApiName = FAccountAuthorizationConstants.API_NAME;

        IObjectDescribe objectDescribe = commonDescribeManager.findByTenantIdAndDescribeApiName(tenantId, objectApiName);

        boolean needReplace = false;
        IFieldDescribe entryAmountField = objectDescribe.getFieldDescribe(FAccountAuthorizationConstants.Field.EntryAmountFieldApiName.apiName);
        if (entryAmountField.isRequired()) {
            needReplace = true;
            entryAmountField.setRequired(false);
        }


        IFieldDescribe recordType = objectDescribe.getFieldDescribe(SystemConstants.Field.RecordType.apiName);
        List<Map<String, Object>> options = (List) recordType.get("options");
        if (!CollectionUtils.empty(options)) {
            for (int i = 0; i < options.size(); i++) {
                String apiName = (String) options.get(i).get("api_name");
                if (Objects.equals("default__c", apiName)) {
                    if (!Objects.equals(options.get(i).get("description"), "预设业务类型") || !Objects.equals(options.get(i).get("label"), "预设业务类型")) { //ignoreI18n
                        options.get(i).put("description", "预设业务类型");
                        options.get(i).put("label", "预设业务类型");   //ignoreI18n 这个要修改多语系统'FAccountAuthorizationObj.field.record_type.option.default__c'
                        needReplace = true;
                    }
                    break;
                }
            }
        }

        if (!needReplace) {
            return;
        }

        commonDescribeManager.replace(objectDescribe);
    }

    /**
     * 更新AutoEntryStatus
     */
    public boolean updateAutoEntryStatus(String tenantId, IObjectData accountAuthData, boolean autoEntryStatus, boolean needCheck) {
        boolean needUpdate = false;
        if (needCheck) {
            if (!accountAuthData.containsField(FAccountAuthorizationConstants.Field.AutoEntryStatus.apiName)
                 || accountAuthData.get(FAccountAuthorizationConstants.Field.AutoEntryStatus.apiName, Boolean.class) == null) {
                needUpdate = true;
            }

            boolean dbAutoEntryStatus = accountAuthData.get(FAccountAuthorizationConstants.Field.AutoEntryStatus.apiName, Boolean.class);
            if (!Objects.equals(dbAutoEntryStatus, autoEntryStatus)) {
                needUpdate = true;
            }
        }

        if (!needUpdate) {
            return false;
        }

        User user = new User(tenantId, "-10000");
        List<String> updateFields = Lists.newArrayList(DBRecord.ID);

        accountAuthData.set(FAccountAuthorizationConstants.Field.AutoEntryStatus.apiName, autoEntryStatus);
        List<IObjectData> fAccountAuthorizationDatas = Lists.newArrayList(accountAuthData);

        updateFields.add(FAccountAuthorizationConstants.Field.AutoEntryStatus.apiName);
        serviceFacade.batchUpdateByFields(user, fAccountAuthorizationDatas, updateFields);
        return true;
    }

    /**
     * 865支持'支出授权'，刷'账户授权'定义
     *
     * 主对象加新增的字段（authorized_type）= income
     */
    public void updateAccountAuthData(String tenantId) {
        //是否开启了账户授权
        boolean isAccountAuthOpen = fundAccountConfigManager.isAccountAuthOpen(tenantId);
        if (!isAccountAuthOpen) {
            return;
        }

        User user = new User(tenantId, "-10000");
        List<String> updateFields = Lists.newArrayList(DBRecord.ID);

        //查数据
        List<IObjectData> fAccountAuthorizationDatas = getFAccountAuthorizationDatasForUpdate(user);
        log.info("getFAccountAuthorizationDatasForUpdate  fAccountAuthorizationDatas.size[{}]", fAccountAuthorizationDatas.size());
        if (CollectionUtils.empty(fAccountAuthorizationDatas)) {
            return;
        }

        //更新
        for (IObjectData fAccountAuthorizationData : fAccountAuthorizationDatas) {
            fAccountAuthorizationData.set(FAccountAuthorizationConstants.Field.AuthorizedType.apiName, FAccountAuthAuthorizedTypeEnum.Income.getValue());
            fAccountAuthorizationData.setDescribeApiName(FAccountAuthorizationConstants.API_NAME);
            fAccountAuthorizationData.setTenantId(tenantId);
        }

        updateFields.add(FAccountAuthorizationConstants.Field.AuthorizedType.apiName);
        serviceFacade.batchUpdateByFields(user, fAccountAuthorizationDatas, updateFields);
    }

    public List<IObjectData> getFAccountAuthorizationDatasForUpdate(User user) {
        String objectApiName = FAccountAuthorizationConstants.API_NAME;

        List<IFilter> filters = new ArrayList<>();
        IFilter authorizedTypeFilter = new Filter();
        authorizedTypeFilter.setFieldName(FAccountAuthorizationConstants.Field.AuthorizedType.apiName);
        authorizedTypeFilter.setOperator(Operator.IS);
        authorizedTypeFilter.setFieldValues(Lists.newArrayList());
        filters.add(authorizedTypeFilter);

        return ObjectDataUtil.getObjectDatasWithDeleted(user, objectApiName, filters);
    }

    /**
     * 所有支出授权的对象
     */
    public List<String> getOutcomeAuthObjectApiNames(String tenantId) {
        User admin = new User(tenantId, "-10000");

        List<IObjectData> allOutcomeAuthDatas = getFAccountAuthorizationDatas(admin, FAccountAuthAuthorizedTypeEnum.Outcome.getValue(), null);
        if (CollectionUtils.empty(allOutcomeAuthDatas)) {
            return Lists.newArrayList();
        }

        return allOutcomeAuthDatas.stream().map(d -> d.get(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, String.class)).collect(Collectors.toList());
    }

    /**
     * 支出授权初始化
     */
    public void initForOutCome(User user, String authorizedObjectApiName, String entryCustomerFieldApiName, boolean needSaveComponentReduceAccountCheckRule, boolean needUpdateStatus, IObjectData fAccountAuthData) {
        // 不用新建字段字段，由调用端传，或者用户到管理后台自己设置
        // validateOrCreateTradeAmountField();

        //新建'组件扣减'的校验规则
        if (needSaveComponentReduceAccountCheckRule) {
            accountCheckRuleManager.saveComponentReduceAccountCheckRule(user, authorizedObjectApiName, entryCustomerFieldApiName);
        }

        //新建插件
        RequestContext requestContext = ServiceContextUtil.getRequestContext(user);
        domainPluginManager.createPluginInstanceIfNotExist(requestContext, "customer_account", authorizedObjectApiName);

        //更新状态
        if (needUpdateStatus) {
            fAccountAuthData.set(FAccountAuthorizationConstants.Field.Status.apiName, FAccountAuthorizationStatusEnum.HAS_INIT.getValue());
            List<String> updateFields = Lists.newArrayList(DBRecord.ID, FAccountAuthorizationConstants.Field.Status.apiName);
            serviceFacade.batchUpdateByFields(user, Lists.newArrayList(fAccountAuthData), updateFields);
        }
    }

    /**
     * 查所有的'已初始化'的支出授权，新建插件
     */
    public void createOrEnableCustomerAccountPluginInstance(String tenantId) {
        //查所有的'已初始化'的支出授权
        User admin = User.systemUser(tenantId);
        List<IObjectData> allInitOutcomeAuthDatas = fAccountAuthorizationManager.getFAccountAuthorizationDatas(admin, FAccountAuthAuthorizedTypeEnum.Outcome.getValue(), FAccountAuthorizationStatusEnum.HAS_INIT.getValue());
        if (CollectionUtils.empty(allInitOutcomeAuthDatas)) {
            log.info("createOrEnableCustomerAccountPluginInstance allInitOutcomeAuthDatas is empty, tenantId[{}]", tenantId);
            return;
        }
        RequestContext requestContext = ServiceContextUtil.getRequestContext(User.systemUser(tenantId));
        for (IObjectData outcomeAuthDatas : allInitOutcomeAuthDatas) {
            String authorizedObjectApiName = outcomeAuthDatas.get(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, String.class);
            domainPluginManager.createOrEnablePluginInstance(requestContext, "customer_account", authorizedObjectApiName);
        }
    }

    /**
     * 把【账户授权】改为已初始化
     */
    public void fixAccountAuthToInit(String tenantId, List<String> incomeObjectApiNames, List<String> outcomeObjectApiNames) {
        //查所有的'已初始化'的账户授权
        Set<String> allObjectApiNames = new HashSet<>();
        allObjectApiNames.addAll(incomeObjectApiNames);
        allObjectApiNames.addAll(outcomeObjectApiNames);
        User admin = User.systemUser(tenantId);
        List<IObjectData> allInitAuthDatas = fAccountAuthorizationManager.getFAccountAuthorizationDatas(admin, Lists.newArrayList(allObjectApiNames), null);
        if (CollectionUtils.empty(allInitAuthDatas)) {
            log.info("fixAccountAuthToInit allInitAuthDatas is empty, admin[{}]", admin);
            return;
        }

        List<IObjectData> needUpdateDatas = Lists.newArrayList();
        for (IObjectData authData : allInitAuthDatas) {
            String authorizedObjectApiName = authData.get(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, String.class);
            String authorizedType = authData.get(FAccountAuthorizationConstants.Field.AuthorizedType.apiName, String.class);
            String status = authData.get(FAccountAuthorizationConstants.Field.Status.apiName, String.class);
            if (Objects.equals(status, FAccountAuthorizationStatusEnum.HAS_INIT.getValue())) {
                continue;
            }
            authData.set(FAccountAuthorizationConstants.Field.Status.apiName, FAccountAuthorizationStatusEnum.HAS_INIT.getValue());

            if (Objects.equals(authorizedType, FAccountAuthAuthorizedTypeEnum.Income.getValue())) {
                if (incomeObjectApiNames.contains(authorizedObjectApiName)) {
                    needUpdateDatas.add(authData);
                }
            } else if (Objects.equals(authorizedType, FAccountAuthAuthorizedTypeEnum.Outcome.getValue())) {
                if (outcomeObjectApiNames.contains(authorizedObjectApiName)) {
                    needUpdateDatas.add(authData);
                }
            }
        }

        if (CollectionUtils.empty(needUpdateDatas)) {
            return;
        }

        List<String> updateFields = Lists.newArrayList(FAccountAuthorizationConstants.Field.Status.apiName);
        try {
            serviceFacade.batchUpdateByFields(admin, needUpdateDatas, updateFields);
        } catch(Exception e) {
            log.info("fixAccountAuthToInit serviceFacade.batchUpdateByFields failed, admin[{}], needUpdateDatas[{}], updateFields[{}]", admin, needUpdateDatas, updateFields, e);
            throw e;
        }
    }

    /**
     * 885刷数据，入账授权'("autoentry_status", "自动入账")'刷为false
     */
    public void transferAutoEntryStatusFalse(String tenantId) {
        //查询所有'入账授权'
        User admin = new User(tenantId, "-10000");
        List<IObjectData> allIncomeAuthDatas = getFAccountAuthorizationDatas(admin, FAccountAuthAuthorizedTypeEnum.Income.getValue(), null);

        if (CollectionUtils.empty(allIncomeAuthDatas)) {
            return;
        }

        List<IObjectData> needUpdateIncomeAuthDatas = Lists.newArrayList();
        for (IObjectData data : allIncomeAuthDatas) {
            Boolean autoEntryStatus = data.get(FAccountAuthorizationConstants.Field.AutoEntryStatus.apiName, Boolean.class);
            if (autoEntryStatus == null) {
                data.set(FAccountAuthorizationConstants.Field.AutoEntryStatus.apiName, false);
                needUpdateIncomeAuthDatas.add(data);
            }
        }

        if (CollectionUtils.empty(needUpdateIncomeAuthDatas)) {
            log.info("transferAutoEntryStatusFalse needUpdateIncomeAuthDatas empty tenantId[{}]", tenantId);
            return;
        }
        List<String> updateFieldList = Lists.newArrayList(FAccountAuthorizationConstants.Field.AutoEntryStatus.apiName);
        serviceFacade.batchUpdateByFields(admin, needUpdateIncomeAuthDatas, updateFieldList);
    }

    /**
     * 对象是否开了自动入账
     * needCheckAccountAuthSwitch 是否需要查【账户授权】开关
     */
    public boolean isOpenAuthEnterAccount(String tenantId, String objectApiName, boolean needCheckAccountAuthSwitch) {
        if (needCheckAccountAuthSwitch) {
            boolean isAccountAuthOpen = fundAccountConfigManager.isAccountAuthOpen(tenantId);
            if (!isAccountAuthOpen) {
                return false;
            }
        }

        String key = accountAuthAutoEnterAccountObjectApiNamesKeyPrefix + tenantId;

        RSet<String> autoEnterAccountObjectApiNames = redissonClient.getSet(key);
        if (autoEnterAccountObjectApiNames.isEmpty()) {
            //查已经开了自动入账的对象, 放到缓存里
            List<String> openAutoEnterAccountObjectApiNames = getOpenAutoEnterAccountObjectApiNames(tenantId);

            //如果为空，放一个控制，避免这里频繁查询
            if (CollectionUtils.empty(openAutoEnterAccountObjectApiNames)) {
                autoEnterAccountObjectApiNames.add("noInitedAndOpenAutoEnterAccountObject");
            } else {
                autoEnterAccountObjectApiNames.addAll(openAutoEnterAccountObjectApiNames);
            }

            autoEnterAccountObjectApiNames.expire(ConfigCenter.accountAuthAutoEnterAccountObjectApiNamesCacheExpireHours, TimeUnit.HOURS);
            log.info("isOpenAuthEnterAccount tenantId[{}], autoEnterAccountObjectApiNames[{}]", tenantId, autoEnterAccountObjectApiNames);
            return openAutoEnterAccountObjectApiNames.contains(objectApiName);
        } else {
            return autoEnterAccountObjectApiNames.contains(objectApiName);
        }
    }

    /**
     * 元数据变更MQ
     * 对【账户授权】对象的变更，更新【对象是否开了自动入账】缓存
     */
    public void updateAutoEnterAccountCache(String tenantId) {
        List<String> newOpenAutoEnterAccountObjectApiNames = getOpenAutoEnterAccountObjectApiNames(tenantId);

        String key = accountAuthAutoEnterAccountObjectApiNamesKeyPrefix + tenantId;
        RSet<String> autoEnterAccountObjectApiNames = redissonClient.getSet(key);

        log.info("updateAutoEnterAccountCache before tenantId[{}], autoEnterAccountObjectApiNames[{}]", tenantId, autoEnterAccountObjectApiNames);
        //没有的去掉
        autoEnterAccountObjectApiNames.removeIf(autoEnterAccountObjectApiName -> !newOpenAutoEnterAccountObjectApiNames.contains(autoEnterAccountObjectApiName));
        //加新的
        autoEnterAccountObjectApiNames.addAll(newOpenAutoEnterAccountObjectApiNames);

        //如果为空，放一个控制，避免 FAccountAuthorizationManager.isOpenAuthEnterAccount 频繁查询
        if (autoEnterAccountObjectApiNames.isEmpty()) {
            autoEnterAccountObjectApiNames.add("noInitedAndOpenAutoEnterAccountObject");
        }
        log.info("updateAutoEnterAccountCache after tenantId[{}], autoEnterAccountObjectApiNames[{}]", tenantId, autoEnterAccountObjectApiNames);

        autoEnterAccountObjectApiNames.expire(ConfigCenter.accountAuthAutoEnterAccountObjectApiNamesCacheExpireHours, TimeUnit.HOURS);
    }

    /**
     * 自动入账MQ
     */
    public void autoEntryAccount(String tenantId, String objectApiName, String dataId) {
        log.info("autoEntryAccount tenantId[{}], objectApiName[{}], dataId[{}]", tenantId, objectApiName, dataId);

        User admin = new User(tenantId, "-10000");

        IObjectData objectData = null;
        try {
            objectData = serviceFacade.findObjectData(admin, dataId, objectApiName);
        } catch (ObjectDataNotFoundException e) {
            log.warn("serviceFacade.findObjectData ObjectDataNotFound admin[{}], id[{}], apiName[{}]", admin, dataId, objectApiName);
            return;
        }

        //生命状态正常，才能入账
        String lifeStatus = (String) objectData.get(SystemConstants.Field.LifeStatus.apiName);
        if (!Objects.equals(lifeStatus, LifeStatusEnum.Normal.getValue())) {
            log.info("autoEntryAccount tenantId[{}], objectApiName[{}], dataId[{}], lifeStatus[{}]", tenantId, objectApiName, dataId, lifeStatus);
            return;
        }

        //mq已经判断了
        /*
        boolean isAccountAuthOpen = fundAccountConfigManager.isAccountAuthOpen(tenantId);
        if (!isAccountAuthOpen) {
            return;
        }
        */

        String apiName = objectData.getDescribeApiName();
        //是否开了自动入账,mq已经判断了
        /**
        boolean isOpenAutoEnterAccount = isOpenAutoEnterAccount(tenantId, apiName);
        if (!isOpenAutoEnterAccount) {
            return;
        }
         */

        //是否已经入账
        Boolean enterIntoAccount = objectData.get("enter_into_account", Boolean.class, false);
        if (enterIntoAccount) {
            log.info("autoEntryAccount enterIntoAccount tenantId[{}], objectApiName[{}], dataId[{}]", tenantId, apiName, objectData.getId());
            return;
        }
        boolean hasIncomeTransactionFlow = transactionFlowDataManager.hasIncomeTransactionFlow(tenantId, objectData.getId());
        if (hasIncomeTransactionFlow) {
            log.info("autoEntryAccount hasIncomeTransactionFlow tenantId[{}], objectApiName[{}], dataId[{}]", tenantId, apiName, objectData.getId());
            return;
        }

        /**
         * 取消入账的，不让入账了
         *
         * 避免问题：取消入账时，收到object_data变更消息，又进行入账了
         *
         * 处理方法：数据上分不清是否【取消入账】，只能看是否有对应的【入账状态 entry_status】=【已取消】的【流水】
         *     如果流水被放到【回收站】，还能查得到，这种不会再自动入账
         *     如果流水在【回收站】中被删掉了，这种查不到，不能避免再自动入账
         */
        List<String> hasCancelledTransactionFlowIds = transactionFlowDataManager.getHasCancelledTransactionFlowIds(tenantId, objectData.getId(), 1);
        if (!CollectionUtils.empty(hasCancelledTransactionFlowIds)) {
            log.info("autoEntryAccount hasCancelledTransactionFlow tenantId[{}], objectApiName[{}], dataId[{}], hasCancelledTransactionFlowIds[{}]", tenantId, objectApiName, dataId, hasCancelledTransactionFlowIds);
            return;
        }

        String autoEnterAccountId = fAccountEntryRuleManager.getAutoEnterAccountId(tenantId, objectData);
        if (Strings.isNullOrEmpty(autoEnterAccountId)) {
            return;
        }

        //调入账接口
        RequestContext requestContext = RequestContext.builder().tenantId(tenantId).user(admin).build();
        ActionContext actionContext = new ActionContext(requestContext, apiName, ObjectAction.ENTER_ACCOUNT.getActionCode());

        ObjectDataDocument args = new ObjectDataDocument();
        args.put("form_fund_account_id", autoEnterAccountId);
        args.put("form_is_auto_enter_account", true);

        CustomButtonAction.Arg enterArg = new CustomButtonAction.Arg();
        enterArg.setObjectDataId(objectData.getId());
        enterArg.setArgs(args);

        try {
            testThrowError(tenantId, objectApiName);

            CustomButtonAction.Result saveResult = serviceFacade.triggerRemoteAction(actionContext, enterArg, CustomButtonAction.Result.class);
            log.info("autoEntryAccount triggerRemoteAction tenantId[{}], objectApiName[{}], dataId[{}]", tenantId, objectApiName, dataId);
        } catch (Exception e) {
            log.warn("triggerRemoteAction tenantId[{}], objectApiName[{}], dataId[{}], errMsg[{}]", tenantId, objectApiName, dataId, e.getMessage(), e);

            String errMsg = e.getMessage();
            //发通知
            if (!CollectionUtils.empty(objectData.getOwner())) {
                String receiverId = objectData.getOwner().get(0);
                User receiver = new User(tenantId, receiverId);
                caNotifyManager.notifyForAutoEnterAccountFail(receiver, objectData, errMsg);
            }

            // 通知开发人员 (部分错误信息不通知）
            newCustomerAccountManager.sendAutoEnterAccountNoticeErrorToInner(tenantId, apiName, dataId, objectData.getName(), "自动入账失败", errMsg);//ignoreI18n

            // 错误上报 (部分错误信息不上报）
            CustomerAccountLogUtil.sendAutoEnterAccountFailAuditLog(admin, apiName, dataId, errMsg);

            // 报错，重试 (部分错误信息不重试）
            boolean reThrowError = reThrowError(errMsg);
            log.info("autoEntryAccount, tenantId[{}], objectDataId[{}], reThrowError[{}], errMsg[{}]", tenantId, objectData.getId(), reThrowError, errMsg);
            if (reThrowError) {
                throw e;
            }
        }
        log.info("autoEntryAccount, tenantId[{}], objectDataId[{}]", tenantId, objectData.getId());
    }

    //为了做测试
    private void testThrowError(String tenantId, String objectApiName) {
        boolean isOpenAutoEnterAccountThrowErrorTest = ConfigCenter.isOpenAutoEnterAccountThrowErrorTest;
        if (!isOpenAutoEnterAccountThrowErrorTest) {
            return;
        }

        Map<String, List<String>> openAutoEnterAccountThrowErrorTestTenantId2ObjectApiNames = ConfigCenter.getOpenAutoEnterAccountThrowErrorTestTenantId2ObjectApiNames();
        if (openAutoEnterAccountThrowErrorTestTenantId2ObjectApiNames == null
                || openAutoEnterAccountThrowErrorTestTenantId2ObjectApiNames.size() == 0
                || !openAutoEnterAccountThrowErrorTestTenantId2ObjectApiNames.containsKey(tenantId)) {
            return;
        }

        List<String> objectApiNames = openAutoEnterAccountThrowErrorTestTenantId2ObjectApiNames.get(tenantId);
        if (CollectionUtils.empty(objectApiNames) || !objectApiNames.contains(objectApiName)) {
            return;
        }

        throw new ValidateException(ConfigCenter.autoEnterAccountThrowErrorMsg);
    }

    private boolean reThrowError(String errMsg) {
        List<String> autoEnterAccountErrorNoRetryMsgs = ConfigCenter.getAutoEnterAccountErrorNoRetryMsgs();
        if (CollectionUtils.notEmpty(autoEnterAccountErrorNoRetryMsgs)) {
            for (String msg : autoEnterAccountErrorNoRetryMsgs) {
                if (errMsg.contains(msg)) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * sourceAuthDetailDatas, sourceAuthDetailDatas, sourceAccountEntryRuleDatas 复制到 targetTenantId 的 targetAccountAuthDataId 的从对象上(已经有的账户排除掉）
     */
    public void copy(String targetTenantId, IObjectData sourceAccountAuthData, List<IObjectData> sourceAuthDetailDatas, List<IObjectData> sourceUnfreezeAuthDetails, List<IObjectData> sourceAccountEntryRuleDatas, boolean needInitIfSourceTenantIdHasInit) {
        User admin = User.systemUser(targetTenantId);

        //主对象【账户授权】
        IObjectData masterObjectData = ObjectDataUtil.getBaseObjectData(admin, FAccountAuthorizationConstants.API_NAME);

        boolean accountAuthIdHasUse = commonObjDataManager.idHasUse(targetTenantId, FAccountAuthorizationConstants.API_NAME, sourceAccountAuthData.getId());
        if (!accountAuthIdHasUse) {
            masterObjectData.set(SystemConstants.Field.Id.apiName, sourceAccountAuthData.getId());
        }
        masterObjectData.set(FAccountAuthorizationConstants.Field.Name.apiName, sourceAccountAuthData.get(FAccountAuthorizationConstants.Field.Name.apiName));
        masterObjectData.set(FAccountAuthorizationConstants.Field.AuthorizedType.apiName, sourceAccountAuthData.get(FAccountAuthorizationConstants.Field.AuthorizedType.apiName));
        masterObjectData.set(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, sourceAccountAuthData.get(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName));
        masterObjectData.set(FAccountAuthorizationConstants.Field.EntryCustomerFieldApiName.apiName, sourceAccountAuthData.get(FAccountAuthorizationConstants.Field.EntryCustomerFieldApiName.apiName));
        masterObjectData.set(FAccountAuthorizationConstants.Field.EntryAmountFieldApiName.apiName, sourceAccountAuthData.get(FAccountAuthorizationConstants.Field.EntryAmountFieldApiName.apiName));
        masterObjectData.set(FAccountAuthorizationConstants.Field.AutoEntryStatus.apiName, sourceAccountAuthData.get(FAccountAuthorizationConstants.Field.AutoEntryStatus.apiName));

        masterObjectData.set(FAccountAuthorizationConstants.Field.IsUnfreezeAuth.apiName, sourceAccountAuthData.get(FAccountAuthorizationConstants.Field.IsUnfreezeAuth.apiName));
        masterObjectData.set(FAccountAuthorizationConstants.Field.FrozenActions.apiName, sourceAccountAuthData.get(FAccountAuthorizationConstants.Field.FrozenActions.apiName));
        masterObjectData.set(FAccountAuthorizationConstants.Field.ReduceTriggerActions.apiName, sourceAccountAuthData.get(FAccountAuthorizationConstants.Field.ReduceTriggerActions.apiName));

        masterObjectData.set(FAccountAuthorizationConstants.Field.Status.apiName, sourceAccountAuthData.get(FAccountAuthorizationConstants.Field.Status.apiName));
        if (!needInitIfSourceTenantIdHasInit) {
            masterObjectData.set(FAccountAuthorizationConstants.Field.Status.apiName, FAccountAuthorizationStatusEnum.UN_INIT.getValue());
        }
        masterObjectData.set(FAccountAuthorizationConstants.Field.Remark.apiName, sourceAccountAuthData.get(FAccountAuthorizationConstants.Field.Remark.apiName));
        masterObjectData.set("record_type", "default__c");

        Map<String, List<ObjectDataDocument>> detailObjectData = new HashMap<>();

        //从对象【授权明细】
        if (!CollectionUtils.empty(sourceAuthDetailDatas)) {
            List<String> sourceAuthDetailIds = sourceAuthDetailDatas.stream().map(DBRecord::getId).collect(Collectors.toList());
            List<String> targetHasUseAuthDetailIds = commonObjDataManager.getHasUseIds(targetTenantId, AuthorizationDetailConstant.API_NAME, sourceAuthDetailIds);

            List<ObjectDataDocument> authorizationDetails = Lists.newArrayList();
            for (IObjectData sourceAuthDetailData : sourceAuthDetailDatas) {
                IObjectData authorizationDetail = ObjectDataUtil.getBaseObjectData(admin, AuthorizationDetailConstant.API_NAME);
                if (!targetHasUseAuthDetailIds.contains(sourceAuthDetailData.getId())) {
                    authorizationDetail.set(SystemConstants.Field.Id.apiName, sourceAuthDetailData.getId());
                }
                authorizationDetail.set(AuthorizationDetailConstant.Field.Name.apiName, sourceAuthDetailData.getName());
                if (!accountAuthIdHasUse) {
                    authorizationDetail.set(AuthorizationDetailConstant.Field.FAccountAuthorizationId.apiName, sourceAccountAuthData.getId());
                }

                authorizationDetail.set(AuthorizationDetailConstant.Field.AuthorizeAccountId.apiName, sourceAuthDetailData.get(AuthorizationDetailConstant.Field.AuthorizeAccountId.apiName));
                authorizationDetail.set(AuthorizationDetailConstant.Field.IsDefaultEntryAccount.apiName, sourceAuthDetailData.get(AuthorizationDetailConstant.Field.IsDefaultEntryAccount.apiName));
                authorizationDetail.set(AuthorizationDetailConstant.Field.Status.apiName, sourceAuthDetailData.get(AuthorizationDetailConstant.Field.Status.apiName));
                authorizationDetail.set(AuthorizationDetailConstant.Field.Remark.apiName, sourceAuthDetailData.get(AuthorizationDetailConstant.Field.Remark.apiName));
                authorizationDetail.set("record_type", sourceAuthDetailData.getRecordType());

                authorizationDetails.add(ObjectDataDocument.of(authorizationDetail));
            }

            detailObjectData.put(AuthorizationDetailConstant.API_NAME, authorizationDetails);
        }

        //从对象【解冻授权明细】
        if (!CollectionUtils.empty(sourceUnfreezeAuthDetails)) {
            List<String> sourceUnfreezeAuthDetailIds = sourceUnfreezeAuthDetails.stream().map(DBRecord::getId).collect(Collectors.toList());
            List<String> targetHasUseUnfreezeAuthDetailIds = commonObjDataManager.getHasUseIds(targetTenantId, UnfreezeAuthDetailConstants.API_NAME, sourceUnfreezeAuthDetailIds);

            List<ObjectDataDocument> unfreezeAuthDetails = Lists.newArrayList();
            for (IObjectData sourceUnfreezeAuthDetail : sourceUnfreezeAuthDetails) {
                IObjectData unfreezeAuthDetail = ObjectDataUtil.getBaseObjectData(admin, UnfreezeAuthDetailConstants.API_NAME);
                if (!targetHasUseUnfreezeAuthDetailIds.contains(sourceUnfreezeAuthDetail.getId())) {
                    unfreezeAuthDetail.set(SystemConstants.Field.Id.apiName, sourceUnfreezeAuthDetail.getId());
                }
                unfreezeAuthDetail.set(UnfreezeAuthDetailConstants.Field.Name.apiName, sourceUnfreezeAuthDetail.getName());
                if (!accountAuthIdHasUse) {
                    unfreezeAuthDetail.set(UnfreezeAuthDetailConstants.Field.FAccountAuthorizationId.apiName, sourceAccountAuthData.getId());
                }

                unfreezeAuthDetail.set(UnfreezeAuthDetailConstants.Field.UnfreezeObject.apiName, sourceUnfreezeAuthDetail.get(UnfreezeAuthDetailConstants.Field.UnfreezeObject.apiName));
                unfreezeAuthDetail.set(UnfreezeAuthDetailConstants.Field.RelatedField.apiName, sourceUnfreezeAuthDetail.get(UnfreezeAuthDetailConstants.Field.RelatedField.apiName));
                unfreezeAuthDetail.set(UnfreezeAuthDetailConstants.Field.UnfreezeActions.apiName, sourceUnfreezeAuthDetail.get(UnfreezeAuthDetailConstants.Field.UnfreezeActions.apiName));
                unfreezeAuthDetail.set(UnfreezeAuthDetailConstants.Field.Remark.apiName, sourceUnfreezeAuthDetail.get(UnfreezeAuthDetailConstants.Field.Remark.apiName));
                unfreezeAuthDetail.set("record_type", sourceUnfreezeAuthDetail.getRecordType());

                unfreezeAuthDetails.add(ObjectDataDocument.of(unfreezeAuthDetail));
            }

            detailObjectData.put(UnfreezeAuthDetailConstants.API_NAME, unfreezeAuthDetails);
        }

        //从对象【入账规则】
        if (!CollectionUtils.empty(sourceAccountEntryRuleDatas)) {
            List<String> sourceAccountEntryRuleIds = sourceAccountEntryRuleDatas.stream().map(DBRecord::getId).collect(Collectors.toList());
            List<String> targetHasUseAccountEntryRuleIds = commonObjDataManager.getHasUseIds(targetTenantId, FAccountEntryRuleConstants.API_NAME, sourceAccountEntryRuleIds);

            List<ObjectDataDocument> accountEntryRules = Lists.newArrayList();
            for (IObjectData sourceAccountEntryRule : sourceAccountEntryRuleDatas) {
                IObjectData accountEntryRule = ObjectDataUtil.getBaseObjectData(admin, FAccountEntryRuleConstants.API_NAME);
                if (!targetHasUseAccountEntryRuleIds.contains(sourceAccountEntryRule.getId())) {
                    accountEntryRule.set(SystemConstants.Field.Id.apiName, sourceAccountEntryRule.getId());
                }
                accountEntryRule.set(FAccountEntryRuleConstants.Field.Name.apiName, sourceAccountEntryRule.getName());
                if (!accountAuthIdHasUse) {
                    accountEntryRule.set(FAccountEntryRuleConstants.Field.FAccountAuthorizationId.apiName, sourceAccountAuthData.getId());
                }

                accountEntryRule.set(FAccountEntryRuleConstants.Field.Sequence.apiName, sourceAccountEntryRule.get(FAccountEntryRuleConstants.Field.Sequence.apiName));
                accountEntryRule.set(FAccountEntryRuleConstants.Field.EntryCondition.apiName, sourceAccountEntryRule.get(FAccountEntryRuleConstants.Field.EntryCondition.apiName));
                accountEntryRule.set(FAccountEntryRuleConstants.Field.FAccountId.apiName, sourceAccountEntryRule.get(FAccountEntryRuleConstants.Field.FAccountId.apiName));
                accountEntryRule.set("record_type", sourceAccountEntryRule.getRecordType());

                accountEntryRules.add(ObjectDataDocument.of(accountEntryRule));
            }

            detailObjectData.put(FAccountEntryRuleConstants.API_NAME, accountEntryRules);
        }

        RequestContext requestContext = RequestContext.builder().tenantId(admin.getTenantId()).user(admin).build();
        ActionContext actionContext = new ActionContext(requestContext, FAccountAuthorizationConstants.API_NAME, ObjectAction.CREATE.getActionCode());

        BaseObjectSaveAction.Arg saveArg = new BaseObjectSaveAction.Arg();
        saveArg.setObjectData(ObjectDataDocument.of(masterObjectData));
        saveArg.setDetails(detailObjectData);

        BaseObjectSaveAction.Result saveResult;
        try {
            saveResult = serviceFacade.triggerAction(actionContext, saveArg, BaseObjectSaveAction.Result.class);  //保存之后是未初始化
        } catch (Exception e) {
            log.warn("copy save fail saveArg[{}]", saveArg, e);
            throw e;
        }
        IObjectData fAccountAuthorizationData = saveResult.getObjectData().toObjectData();
        String sourceStatus = sourceAccountAuthData.get(FAccountAuthorizationConstants.Field.Status.apiName, String.class);
        if (Objects.equals(sourceStatus, FAccountAuthorizationStatusEnum.UN_INIT.getValue())) {
            return;
        }
        if (!needInitIfSourceTenantIdHasInit) {
            return;
        }

        String status = fAccountAuthorizationData.get(FAccountAuthorizationConstants.Field.Status.apiName, String.class);
        if (Objects.equals(status, FAccountAuthorizationStatusEnum.HAS_INIT.getValue())) {
            return;
        }

        init(requestContext, fAccountAuthorizationData.getId());
    }

    public void init(RequestContext requestContext, String objectDataId) {
        ActionContext actionContext = new ActionContext(requestContext, FAccountAuthorizationConstants.API_NAME, ObjectAction.Init.getActionCode());

        CustomButtonAction.Arg initArg = new CustomButtonAction.Arg();
        initArg.setObjectDataId(objectDataId);

        try {
            FAccountAuthorizationInitAction.Result result = serviceFacade.triggerAction(actionContext, initArg, FAccountAuthorizationInitAction.Result.class);
        } catch (Exception e) {
            log.warn("copy init fail initArg[{}]", initArg, e);
            throw e;
        }
    }

    /**
     * 930 【校验规则】选择的对象、按钮放到【账户授权】【解冻授权明细】上
     * 【校验规则】的信息，放到【账户授权】【解冻授权明细】上
     */
    public boolean updateForAccountCheckRule(String tenantId, IObjectData accountCheckRule, IObjectData fAccountAuthorizationData) {
        String ruleType = accountCheckRule.get(AccountCheckRuleConstants.Field.RuleType.apiName, String.class);

        //直接扣减、组件扣减
        if (Objects.equals(ruleType, AccountCheckRuleTypeEnum.Direct_Reduce.getValue())
                || Objects.equals(ruleType, AccountCheckRuleTypeEnum.Component_Reduce.getValue())) {
            return updateForDirectOrComponentAccountCheckRule(tenantId, accountCheckRule, fAccountAuthorizationData);
        }

        //校验扣减
        if (Objects.equals(ruleType, AccountCheckRuleTypeEnum.Check_Reduce.getValue())) {
            return updateForCheckAccountCheckRule(tenantId, accountCheckRule, fAccountAuthorizationData);
        }

        return false;
    }

    /**
     * 针对【直接扣减】【组件扣减】的校验规则
     */
    public boolean updateForDirectOrComponentAccountCheckRule(String tenantId, IObjectData accountCheckRule, IObjectData fAccountAuthorizationData) {
        String reduceRelatedObject = accountCheckRule.get(AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName, String.class);

        //更新【账户授权】
        /**
         * 直接扣减
         *   这些已经有支出授权了，不用补支出授权
         *   is_unfreeze_auth       ：用不到，不用管 (如果最后没有值，可以调另外一个接口CurlService#transferAccountAuth930，刷为false）
         *   frozen_actions         ：用不到，不用管
         *   reduce_trigger_actions ：刷为"Add_button_default", "ConfirmReceipt_button_default", "fieldChange"
         */
        List<String> reduceTriggerActions = fAccountAuthorizationManager.getReduceTriggerActions(reduceRelatedObject);
        return fAccountAuthorizationManager.updateReduceTriggerActions(tenantId, fAccountAuthorizationData, reduceTriggerActions);
    }

    public boolean updateForCheckAccountCheckRule(String tenantId, IObjectData accountCheckRule, IObjectData fAccountAuthorizationData) {
        String authorizedObjectApiName = accountCheckRule.get(AccountCheckRuleConstants.Field.CheckObject.apiName, String.class);

        //更新【账户授权】
        /**
         * 校验扣减
         *   【账户授权】
         *       这些已经有支出授权了，不用补支出授权
         *       is_unfreeze_auth       ：true
         *       frozen_actions         ："Add_button_default", "fieldChange"
         *       reduce_trigger_actions ：用不到
         *   【解冻授权明细】
         *       不存在的话，要加一条数据
         */
        boolean hasModify = false;

        List<String> reduceTriggerActions = fAccountAuthorizationManager.getReduceTriggerActions(authorizedObjectApiName);
        List<String> frozenActions =  Lists.newArrayList("Add_button_default", "fieldChange");
        boolean update = fAccountAuthorizationManager.update(tenantId, fAccountAuthorizationData, true, frozenActions, reduceTriggerActions);
        if (update) {
            hasModify = true;
        }

        String reduceRelatedObject = accountCheckRule.get(AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName, String.class);
        List<String> unfreezeObjects = Lists.newArrayList(reduceRelatedObject);
        boolean create = unfreezeAuthDetailManager.create(tenantId, fAccountAuthorizationData.getId(), authorizedObjectApiName, unfreezeObjects);
        if (create) {
            hasModify = true;
        }

        return hasModify;
    }

    /**
     * 返回账户授权从对象【授权明细】【入账规则】包含的账户名称
     */
    public Map<String, List<String>> accountAuthName2containFundAccountNames(String tenantId, List<IObjectData> accountAuthList, List<IObjectData> fundAccounts) {
        if (CollectionUtils.empty(accountAuthList) || CollectionUtils.empty(fundAccounts)) {
            return new HashMap<>();
        }

        Map<String, List<String>> accountAuthName2containFundAccountNames = new HashMap<>();
        for (IObjectData accountAuth : accountAuthList) {
            List<String> containFundAccountNames = getContainFundAccountNames(tenantId, accountAuth, fundAccounts);
            if (!CollectionUtils.empty(containFundAccountNames)) {
                accountAuthName2containFundAccountNames.put(accountAuth.getName(), containFundAccountNames);
            }
        }
        return accountAuthName2containFundAccountNames;
    }

    /**
     * 返回账户授权从对象【授权明细】【入账规则】包含的账户名称
     */
    public List<String> getContainFundAccountNames(String tenantId, IObjectData accountAuth, List<IObjectData> fundAccounts) {
        if (CollectionUtils.empty(fundAccounts)) {
            return Lists.newArrayList();
        }

        List<String> fundAccountIds = fundAccounts.stream().map(IObjectData::getId).collect(Collectors.toList());


        List<String> containFundAccountIds = Lists.newArrayList();
        String authorizedType = accountAuth.get(FAccountAuthorizationConstants.Field.AuthorizedType.apiName, String.class);
        if (Objects.equals(authorizedType, FAccountAuthAuthorizedTypeEnum.Income.getValue())) {
            //【入账规则】包含的账户
            List<String> hasFundAccountIds = fAccountEntryRuleManager.getContainFundAccountIds(tenantId, Lists.newArrayList(accountAuth.getId()), fundAccountIds);
            containFundAccountIds = Lists.newArrayList(hasFundAccountIds);
        }

        //【授权明细】包含的账户
        List<String> hasFundAccountIds = authorizationDetailManager.getContainFundAccountIds(tenantId, Lists.newArrayList(accountAuth.getId()), fundAccountIds);
        containFundAccountIds.addAll(hasFundAccountIds);

        if (CollectionUtils.empty(containFundAccountIds)) {
            return Lists.newArrayList();
        }

        List<String> finalContainFundAccountIds = Lists.newArrayList(containFundAccountIds);
        return fundAccounts.stream().filter(d -> finalContainFundAccountIds.contains(d.getId())).map(IObjectData::getName).collect(Collectors.toList());
    }

    public void checkFundAccountInFundAuthorizedDetail(User user, String fundAccountId) {
        if (StringUtils.isNotEmpty(fundAccountId)) { // 已开启回款入账
            // 校验：fundAccountId 是否在入账授权的授权明细里面
            IObjectData fundAccountData = serviceFacade.findObjectData(user, fundAccountId, FundAccountConstants.API_NAME);
            fundAccountBaseService.checkFundAccountId(user.getTenantId(), PaymentConstants.API_NAME, fundAccountId, fundAccountData.getName());
        }
    }

    public List<String> getReduceTriggerActions(String authorizedObjectApiName) {
        List<String> reduceTriggerActions = Lists.newArrayList("Add_button_default", "fieldChange");
        if (Objects.equals(authorizedObjectApiName, "DeliveryNoteObj")) {
            reduceTriggerActions.add("ConfirmReceipt_button_default");
        }

        return reduceTriggerActions;
    }

    /**
     * IsUnfreezeAuth 如果不存在，更新
     * 更新ReduceTriggerActions
     */
    public boolean updateIsUnfreezeAuthIfNotExistAndReduceTriggerActions(String tenantId, List<IObjectData> fAccountAuthorizations, boolean isUnfreezeAuth) {
        if (CollectionUtils.empty(fAccountAuthorizations)) {
            return false;
        }

        List<IObjectData> needUpdateFAccountAuthorizations = Lists.newArrayList();
        List<String> updateFields = Lists.newArrayList(FAccountAuthorizationConstants.Field.IsUnfreezeAuth.apiName, FAccountAuthorizationConstants.Field.ReduceTriggerActions.apiName);
        for (IObjectData fAccountAuthorization : fAccountAuthorizations) {

            //IsUnfreezeAuth
            boolean needUpdateIsUnfreezeAuth = false;
            if (!fAccountAuthorization.containsField(FAccountAuthorizationConstants.Field.IsUnfreezeAuth.apiName)
                 || fAccountAuthorization.get(FAccountAuthorizationConstants.Field.IsUnfreezeAuth.apiName, Boolean.class) == null) {

                Boolean dbIsUnfreezeAuth = fAccountAuthorization.get(FAccountAuthorizationConstants.Field.IsUnfreezeAuth.apiName, Boolean.class);
                if (!Objects.equals(dbIsUnfreezeAuth, isUnfreezeAuth)) {
                    needUpdateIsUnfreezeAuth = true;
                }
            }
            if (needUpdateIsUnfreezeAuth) {
                fAccountAuthorization.set(FAccountAuthorizationConstants.Field.IsUnfreezeAuth.apiName, isUnfreezeAuth);
            }

            //ReduceTriggerActions
            String authorizedObjectApiName = fAccountAuthorization.get(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, String.class);
            List<String> reduceTriggerActions = fAccountAuthorizationManager.getReduceTriggerActions(authorizedObjectApiName);
            boolean isReduceTriggerActionsDifferent = isReduceTriggerActionsDifferent(fAccountAuthorization, reduceTriggerActions);
            if (isReduceTriggerActionsDifferent) {
                fAccountAuthorization.set(FAccountAuthorizationConstants.Field.ReduceTriggerActions.apiName, reduceTriggerActions);
            }

            if (needUpdateIsUnfreezeAuth || isReduceTriggerActionsDifferent) {
                needUpdateFAccountAuthorizations.add(fAccountAuthorization);
            }
        }

        if (CollectionUtils.empty(needUpdateFAccountAuthorizations)) {
            return false;
        }

        User admin = User.systemUser(tenantId);
        serviceFacade.batchUpdateByFields(admin, needUpdateFAccountAuthorizations, updateFields);
        return true;
    }

    public boolean updateReduceTriggerActions(String tenantId, IObjectData fAccountAuthorization, List<String> reduceTriggerActions) {
        if (CollectionUtils.empty(reduceTriggerActions)) {
            return false;
        }
        boolean needUpdate = isReduceTriggerActionsDifferent(fAccountAuthorization, reduceTriggerActions);
        if (!needUpdate) {
            return false;
        }
        List<String> updateFields = Lists.newArrayList(FAccountAuthorizationConstants.Field.ReduceTriggerActions.apiName);
        fAccountAuthorization.set(FAccountAuthorizationConstants.Field.ReduceTriggerActions.apiName, reduceTriggerActions);

        User admin = User.systemUser(tenantId);
        serviceFacade.batchUpdateByFields(admin, Lists.newArrayList(fAccountAuthorization), updateFields);
        return true;
    }

    /**
     *
     */
    public boolean update(String tenantId, IObjectData fAccountAuthorization, boolean isUnfreezeAuth, List<String> frozenActions, List<String> reduceTriggerActions) {
        List<String> updateFields = Lists.newArrayList();

        //isUnfreezeAuth
        if (!fAccountAuthorization.containsField(FAccountAuthorizationConstants.Field.IsUnfreezeAuth.apiName)) {
            updateFields.add(FAccountAuthorizationConstants.Field.IsUnfreezeAuth.apiName);
            fAccountAuthorization.set(FAccountAuthorizationConstants.Field.IsUnfreezeAuth.apiName, isUnfreezeAuth);
        } else {
            Boolean dbIsUnfreezeAuth = fAccountAuthorization.get(FAccountAuthorizationConstants.Field.IsUnfreezeAuth.apiName, Boolean.class);
            if (!Objects.equals(dbIsUnfreezeAuth, isUnfreezeAuth)) {
                updateFields.add(FAccountAuthorizationConstants.Field.IsUnfreezeAuth.apiName);
                fAccountAuthorization.set(FAccountAuthorizationConstants.Field.IsUnfreezeAuth.apiName, isUnfreezeAuth);
            }
        }

        //frozenActions
        boolean isFrozenActionsDifferent = isFrozenActionsDifferent(fAccountAuthorization, frozenActions);
        if (isFrozenActionsDifferent) {
            updateFields.add(FAccountAuthorizationConstants.Field.FrozenActions.apiName);
            fAccountAuthorization.set(FAccountAuthorizationConstants.Field.FrozenActions.apiName, frozenActions);
        }

        //reduceTriggerActions
        boolean isReduceTriggerActionsDifferent = isReduceTriggerActionsDifferent(fAccountAuthorization, reduceTriggerActions);
        if (isReduceTriggerActionsDifferent) {
            updateFields.add(FAccountAuthorizationConstants.Field.ReduceTriggerActions.apiName);
            fAccountAuthorization.set(FAccountAuthorizationConstants.Field.ReduceTriggerActions.apiName, reduceTriggerActions);
        }

        if (CollectionUtils.empty(updateFields)) {
            return false;
        }

        User admin = User.systemUser(tenantId);
        serviceFacade.batchUpdateByFields(admin, Lists.newArrayList(fAccountAuthorization), updateFields);
        return true;
    }

    public boolean isReduceTriggerActionsDifferent(IObjectData fAccountAuthorization, List<String> reduceTriggerActions) {
        if (!fAccountAuthorization.containsField(FAccountAuthorizationConstants.Field.ReduceTriggerActions.apiName)
           || fAccountAuthorization.get(FAccountAuthorizationConstants.Field.ReduceTriggerActions.apiName) == null) {
            return reduceTriggerActions != null;
        }

        List<String> dbReduceTriggerActions = fAccountAuthorization.get(FAccountAuthorizationConstants.Field.ReduceTriggerActions.apiName, List.class);
        if (reduceTriggerActions == null && !CollectionUtils.empty(dbReduceTriggerActions)) {
            return true;
        }
        if (dbReduceTriggerActions.containsAll(reduceTriggerActions) && dbReduceTriggerActions.size() == reduceTriggerActions.size()) {
            return false;
        }

        return true;
    }

    public boolean isFrozenActionsDifferent(IObjectData fAccountAuthorization, List<String> frozenActions) {
        if (!fAccountAuthorization.containsField(FAccountAuthorizationConstants.Field.FrozenActions.apiName)
                || fAccountAuthorization.get(FAccountAuthorizationConstants.Field.FrozenActions.apiName) == null) {
            return frozenActions != null;
        }

        List<String> dbFrozenActions = fAccountAuthorization.get(FAccountAuthorizationConstants.Field.FrozenActions.apiName, List.class);
        if (frozenActions == null && !CollectionUtils.empty(dbFrozenActions)) {
            return true;
        }
        if (dbFrozenActions.containsAll(frozenActions) && dbFrozenActions.size() == frozenActions.size()) {
            return false;
        }

        return true;
    }
}
