package com.facishare.crm.customeraccount.enums;

/**
 * 流水类型
 */
public enum BillTypeEnum {
    Prepay(1, "预存款"), RebateIncome(2, "返利收入"), RebateOutcome(3, "返利支出"), Credit(4, "信用"), MergeCustomer(5, "合并客户"), WriteOff(6, "冲销");

    private int type;
    private String desc;

    BillTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }
}
