package com.facishare.crm.customeraccount.predefine.handler.checkrule;

public class AmountChangeExecutor {
    private final AmountChanger amountChanger;

    public AmountChangeExecutor(AmountChanger amountChanger) {
        this.amountChanger = amountChanger;
    }

    public static AmountChangerResult execute(int frozenCompare, int unfreezeCompare, AmountChanger amountChanger) {
        return new AmountChangeExecutor(amountChanger).execute(frozenCompare, unfreezeCompare);
    }

    private AmountChangerResult execute(int frozenCompare, int unfreezeCompare) {
        if (frozenCompare == 0 && unfreezeCompare == 0) {
            return amountChanger.frozenEqAndReduceEq();
        } else if (frozenCompare == 0 && unfreezeCompare > 0) {
            return amountChanger.frozenEqAndReduceGt();
        } else if (frozenCompare == 0) {
            return amountChanger.frozenEqAndReduceLt();
        } else if (frozenCompare > 0 && unfreezeCompare == 0) {
            return amountChanger.frozenGtAndReduceEq();
        } else if (frozenCompare > 0 && unfreezeCompare > 0) {
            return amountChanger.frozenGtAndReduceGt();
        } else if (frozenCompare > 0) {
            return amountChanger.frozenGtAndReduceLt();
        } else if (unfreezeCompare == 0) {
            return amountChanger.frozenLtAndReduceEq();
        } else if (unfreezeCompare > 0) {
            return amountChanger.frozenLtAndReduceGt();
        } else {
            return amountChanger.frozenLtAndReduceLt();
        }
    }
}
