package com.facishare.crm.customeraccount.predefine.domainplugin.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class CheckRuleConfirmReceiptContextModel extends LockContextModel {
    private String objectApiName;
    private String objectDataId;

    private FrozenRuleMatchedModel frozenRuleMatchedModel;
    private UnfreezeRuleTriggerTypeEnum unfreezeRuleTriggerTypeEnum;
    private DirectReduceRuleMatchedModel directReduceRuleMatchedModel;
    private DirectReduceRuleToMatchModel directReduceRuleToMatchModel;

    private CheckRuleAdaptModel frozenRuleAdaptModel;
    private CheckRuleAdaptModel directReduceAdaptModel;
    private List<CheckRuleAdaptModel> unfreezeAdaptModelList;

    public CheckRuleConfirmReceiptContextModel(String objectApiName, String objectDataId) {
        this.objectApiName = objectApiName;
        this.objectDataId = objectDataId;
    }

    public CheckRuleCommonContextModel toCommonContextModel() {
        CheckRuleCommonContextModel commonContextModel = new CheckRuleCommonContextModel(objectApiName, objectDataId);
        commonContextModel.setFrozenRuleMatchedModel(frozenRuleMatchedModel);
        commonContextModel.setFrozenRuleAdaptModel(frozenRuleAdaptModel);
        commonContextModel.setDirectReduceRuleMatchedModel(directReduceRuleMatchedModel);
        commonContextModel.setDirectReduceAdaptModel(directReduceAdaptModel);
        commonContextModel.setDirectReduceRuleToMatchModel(directReduceRuleToMatchModel);
        commonContextModel.setUnfreezeAdaptModelList(unfreezeAdaptModelList);
        commonContextModel.setUnfreezeRuleTriggerTypeEnum(unfreezeRuleTriggerTypeEnum);
        return commonContextModel;
    }
}
