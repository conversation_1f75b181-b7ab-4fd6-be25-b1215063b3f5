package com.facishare.crm.customeraccount.predefine.domainplugin;

import com.facishare.crm.customeraccount.predefine.domainplugin.processor.credit.CreditRuleProcessorFactory;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@ServiceModule("credit_rule_plugin_invalid")
public class CreditRuleInvalidActionDomainPlugin extends EmptyInvalidActionDomainPlugin {
    @Autowired
    private CreditRuleProcessorFactory creditRuleProcessorFactory;

    @ServiceMethod("pre_act")
    public Result preAct(ServiceContext serviceContext, Arg arg) {
        Result result = new Result();
        return creditRuleProcessorFactory.process(serviceContext.getRequestContext(), arg, true, result);
    }

    @ServiceMethod("finally_do")
    public Result finallyDo(ServiceContext serviceContext, Arg arg) {
        Result result = new Result();
        creditRuleProcessorFactory.process(serviceContext.getRequestContext(), arg, false, result);
        return result;
    }
}
