package com.facishare.crm.customeraccount.predefine.action;

import com.facishare.crm.customeraccount.constants.AccountCheckRuleConstants;
import com.facishare.crm.customeraccount.enums.AccountCheckRuleStatusEnum;
import com.facishare.crm.customeraccount.enums.AccountCheckRuleTypeEnum;
import com.facishare.crm.customeraccount.predefine.manager.AccountCheckRuleDomainPluginManager;
import com.facishare.crm.customeraccount.predefine.manager.AccountCheckRuleManager;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAction;
import com.facishare.paas.appframework.core.predef.action.CustomButtonAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;

import java.util.List;

public class AccountCheckRuleDisableAccountCheckRuleAction extends AbstractStandardAction<CustomButtonAction.Arg, AccountCheckRuleDisableAccountCheckRuleAction.Result> {
    private final AccountCheckRuleManager accountCheckRuleManager = SpringUtil.getContext().getBean(AccountCheckRuleManager.class);
    private AccountCheckRuleDomainPluginManager accountCheckRuleDomainPluginManager = SpringUtil.getContext().getBean(AccountCheckRuleDomainPluginManager.class);
    private IObjectData objectData;
    private RLock lock;

    @Override
    protected IObjectData getPreObjectData() {
        return this.objectData;
    }

    @Override
    protected IObjectData getPostObjectData() {
        return this.objectData;
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.ENABLE_ACCOUNT_CHECK_RULE.getButtonApiName();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.ENABLE_ACCOUNT_CHECK_RULE.getActionCode());
    }

    @Override
    protected List<String> getDataPrivilegeIds(CustomButtonAction.Arg arg) {
        String objectDataId = arg.getObjectDataId();
        return StringUtils.isEmpty(objectDataId) ? Lists.newArrayList() : Lists.newArrayList(objectDataId);
    }

    @Override
    protected void init() {
        super.init();
        if (CollectionUtils.notEmpty(dataList)) {
            objectData = dataList.get(0);
        }
    }

    @Override
    protected void before(CustomButtonAction.Arg arg) {
        super.before(arg);
        this.lock = accountCheckRuleManager.getActionLock(actionContext.getUser(), this.objectData.getId());
    }

    @Override
    protected Result doAct(CustomButtonAction.Arg arg) {
        String originalStatus = this.objectData.get(AccountCheckRuleConstants.Field.Status.apiName, String.class);
        if (AccountCheckRuleStatusEnum.On.getValue().equals(originalStatus)) {
            objectData.set(AccountCheckRuleConstants.Field.Status.apiName, AccountCheckRuleStatusEnum.Off.getValue());
        }
        this.objectData = serviceFacade.updateObjectData(actionContext.getUser(), this.objectData);
        log.info("AccountCheckRule status changed from {} to {}. dataId: {}", originalStatus, AccountCheckRuleStatusEnum.Off.getValue(), this.objectData.getId());

        // 组件扣减在编辑时，不需要更改绑定事件
        if (AccountCheckRuleTypeEnum.Component_Reduce.getValue().equals(objectData.get(AccountCheckRuleConstants.Field.RuleType.apiName, String.class))) {
            return new Result(ObjectDataDocument.of(this.objectData));
        }

        // 绑定事件
        if (accountCheckRuleDomainPluginManager.isRunByDomainPlugin(actionContext.getTenantId(), objectData)) {
            accountCheckRuleDomainPluginManager.editByDomainPlugin(actionContext.getRequestContext(), originalStatus, objectData);
        }
        return new Result(ObjectDataDocument.of(this.objectData));
    }

    @Override
    protected void finallyDo() {
        infraServiceFacade.unlock(this.lock);
        super.finallyDo();
    }

    @Data
    public static class Result {
        public Result(ObjectDataDocument objectData) {
            this.objectData = objectData;
        }

        private ObjectDataDocument objectData;
    }
}
