package com.facishare.crm.customeraccount.predefine.action;

import com.facishare.crm.customeraccount.constants.CreditFileConstants;
import com.facishare.crm.customeraccount.constants.SystemConstants;
import com.facishare.crm.customeraccount.predefine.manager.CreditFileManager;
import com.facishare.crm.customeraccount.predefine.manager.CustomerAccountManager;
import com.facishare.crm.customeraccount.util.DateUtil;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

@Slf4j
public class CreditFileAddAction extends StandardAddAction {
    private CreditFileManager creditFileManager;
    private CustomerAccountManager customerAccountManager;

    @Override
    protected void before(Arg arg) {
        creditFileManager = SpringUtil.getContext().getBean(CreditFileManager.class);
        customerAccountManager = SpringUtil.getContext().getBean(CustomerAccountManager.class);
        if (Objects.nonNull(arg.getObjectData()) && StringUtils.isEmpty(arg.getObjectData().toObjectData().getDescribeApiName())) {
            arg.getObjectData().put("object_describe_api_name", actionContext.getObjectApiName());
        }
        super.before(arg);

        //预处理开始时间或结束时间，只保存为整天数据
        Long startTime = objectData.get(CreditFileConstants.Field.StartTime.apiName, Long.class);
        Long endTime = objectData.get(CreditFileConstants.Field.EndTime.apiName, Long.class);
        objectData.set(CreditFileConstants.Field.StartTime.apiName, DateUtil.getMillisecondsOfDayStart(startTime));
        objectData.set(CreditFileConstants.Field.EndTime.apiName, DateUtil.getMillisecondsOfDayStart(endTime));

        ObjectDataExt objectDataExt = ObjectDataExt.of(this.objectData);
        if (StringUtils.isEmpty(objectDataExt.getDescribeId())) {
            objectDataExt.setDescribeId(this.objectDescribe.getId());
        }
        log.debug("user:{},objectData:{}", actionContext.getUser(), objectData);
        creditFileManager.isValidCreditFile(objectData);
        creditFileManager.foreachCheck(actionContext.getUser(), objectData);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result result1 = super.after(arg, result);
        //有效期内，修改客户账户
        String lifeStatus = result1.getObjectData().toObjectData().get(SystemConstants.Field.LifeStatus.apiName, String.class);

        boolean creditActive = creditFileManager.isCreditActive(objectData);
        if (creditActive && lifeStatus.equals(SystemConstants.LifeStatus.Normal.value)) {
            customerAccountManager.updateCreditQuotaToNormal(actionContext.getUser(), objectData, SystemConstants.LifeStatus.Ineffective.value);
        }

        return result1;
    }
}
