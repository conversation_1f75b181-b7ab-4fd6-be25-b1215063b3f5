package com.facishare.crm.customeraccount.constants;

/**
 * <AUTHOR>
 * Created on 2018/12/20.
 */
public interface Constants {
    /**
     * 信用比对
     */
    String CREDIT = "1";
    /**
     * 返利支出比对
     */
    String REBATE_OUT_COME = "2";
    /**
     * 返利收入比对
     */
    String REBATE_IN_COME = "3";
    /**
     * 预存款比对
     */
    String PREPAY = "4";

    String CUSTOMER_ACCOUNT_RULE_ENGINE_SCENE = "AccountCheckRule";
    String CUSTOMER_ACCOUNT_RULE_ENGINE_APP_ID = "CustomerAccount";

    String FIELD_APPROVE_EMPLOYEE_ID = "approve_employee_id";
    String FIELD_APPROVED_EMPLOYEE_ID = "approved_employee_id";
    String FIELD_FINANCE_EMPLOYEE_ID = "finance_employee_id";

    String PRE_VALIDATE_BIZ_KEY = "PreValidate";
    String POST_FROZEN_BIZ_KEY = "FrozenAndDirectReduce";
    String POST_UNFREEZE_BIZ_KEY = "UnfreezeAndDirectReduce";

    String PRE_EDIT_KEY = "AccountCheckRulePreEdit";
    String POST_EDIT_KEY = "AccountCheckRulePostEdit";
    String POST_INVALID_KEY = "AccountCheckRulePostInvalid";

    String ENABLE_CUSTOMER_ACCOUNT_TAG = "customer_account_enabled";
    String ENABLE_ACCOUNT_CHECK_RULE_TAG = "account_check_rule_enabled";
    String ASYNC_ENABLE_ACCOUNT_CHECK_RULE_TAG = "async_enable_account_check_rule";

    String CUSTOMER_ACCOUNT_TOPIC = "customer_account";
    String CA_CUSTOMER_ACCOUNT_CONSUMER_PEER_NAME = "CACustomerAccountMQConsumer";

    String F_ACCOUNT_AMOUNT = "faccount_amount";
    String RECEIVABLE_AMOUNT = "receivable_amount";
    String PAYMENT_STATUS = "payment_status";
    //已回款金额
    String PAYMENT_AMOUNT = "payment_amount";

    //回款整合支付
    String MERCHANT_CODE = "**************";
    String PAYMENT_GOODS_ID = "1014";

    String DHT_PLAT_FORM_ID = "dht";

    String SKIP_RELATE_OBJECT_FIELD_VALIDATE = "skipFlowRelateObjectFieldValidate";

    String THIRD_APP_ID = "third_app_id";
    String THIRD_USER_ID = "third_user_id";

    String CUSTOMER_ACCOUNT_NOT_ENOUGH = "CustomerAccountNotEnough";

    String CUSTOMER_ACCOUNT_PLUGIN_API_NAME = "customer_account";
    String ENTER_ACCOUNT_PLUGIN_API_NAME = "enter_account";
    String PAYMENT_PAY_PLUGIN_API_NAME = "payment_pay";
}
