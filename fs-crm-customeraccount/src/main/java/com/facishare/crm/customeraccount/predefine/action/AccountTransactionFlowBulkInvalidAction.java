package com.facishare.crm.customeraccount.predefine.action;

import com.facishare.crm.customeraccount.util.ObjectDataUtil;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;

public class AccountTransactionFlowBulkInvalidAction extends StandardBulkInvalidAction {

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        ObjectDataUtil.checkAccountTransactionFlow(actionContext.getUser(), this.objectDataList);
    }
}
