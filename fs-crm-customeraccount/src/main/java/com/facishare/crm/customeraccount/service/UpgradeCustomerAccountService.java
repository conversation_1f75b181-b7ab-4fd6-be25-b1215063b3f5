package com.facishare.crm.customeraccount.service;

import com.facishare.crm.customeraccount.constants.*;
import com.facishare.crm.customeraccount.enums.*;
import com.facishare.crm.customeraccount.predefine.manager.*;
import com.facishare.crm.customeraccount.predefine.service.FundAccountService;
import com.facishare.crm.customeraccount.predefine.service.impl.CurlServiceImpl;
import com.facishare.crm.customeraccount.util.ConfigCenter;
import com.facishare.crmcommon.describebuilder.ObjectReferenceFieldDescribeBuilder;
import com.facishare.crmcommon.describebuilder.SelectOptionBuilder;
import com.facishare.crmcommon.enums.PaymentPurposeEnum;
import com.facishare.crm.openapi.Utils;
import com.facishare.crmcommon.util.SearchUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.*;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.RateLimiter;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @IgnoreI18n
 */
@Slf4j
@Service
@ServiceModule("upgrade_customer_account")
public class UpgradeCustomerAccountService {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private ObjectDataServiceImpl objectDataService;
    @Resource
    private SpecialTableMapper specialTableMapper;
    @Autowired
    private PrepayDetailManager prepayDetailManager;
    @Autowired
    private BizConfigManager bizConfigManager;
    @Autowired
    private FundAccountManager fundAccountManager;
    @Autowired
    private FundAccountService fundAccountService;

    public static String PAYMENT_ID_FIELD_OF_PREPAY = "field_payment_id__c";
    public static String PREPAY_ID_FIELD_OF_PAYMENT = "field_prepay_id__c";
    public static String PREPAY_ID_FIELD_OF_ACCOUNT_FLOW = "field_prepay_id__c";

    @ServiceMethod("transfer_all")
    public Result transferCustomerAccountData(ServiceContext serviceContext, Arg arg) {
        Set<String> eis = arg.getEis();
        Result result = new Result();
        if (CollectionUtils.empty(eis)) {
            return result;
        }
        Set<String> errorEis = Sets.newHashSet();
        RateLimiter rateLimiter = RateLimiter.create(ConfigCenter.customerAccountRefreshTps);

        boolean stepSuccess = true;
        for (String tenantId : eis) {
            if (!ConfigCenter.running.get()) {
                break;
            }
            boolean canTransfer = canTransfer(tenantId);
            if (!canTransfer) {
                errorEis.add(tenantId);
                continue;
            }
            Tuple<String, String> fundAccountId2Owner = getFundAccountId(tenantId);
            if (Objects.isNull(fundAccountId2Owner)) {
                errorEis.add(tenantId);
                continue;
            }
            String fundAccountId = fundAccountId2Owner.getKey();
            String newCustomerAccountOwner = fundAccountId2Owner.getValue();
            Map<String, IObjectDescribe> describeMap = serviceFacade.findObjects(tenantId, Lists.newArrayList(AccountTransactionFlowConst.API_NAME, Utils.SALES_ORDER_API_NAME));
            IObjectDescribe accountFlowDescribe = describeMap.get(AccountTransactionFlowConst.API_NAME);
            IObjectDescribe salesOrderDescribe = describeMap.get(Utils.SALES_ORDER_API_NAME);
            //将订单的结算方式修改为非必填
            if (salesOrderDescribe.containsField("settle_type")) {
                SelectOneFieldDescribe settleTypeField = (SelectOneFieldDescribe) salesOrderDescribe.getFieldDescribe("settle_type");
                if (settleTypeField.isActive() && settleTypeField.isRequired()) {
                    settleTypeField.setRequired(false);
                    serviceFacade.updateFieldDescribe(salesOrderDescribe, Lists.newArrayList(settleTypeField));
                    log.info("tenantId:{},set SalesOrderObj settleType required false", tenantId);
                }
                List<ILayout> layoutByObjectApiName = serviceFacade.findLayoutByObjectApiName(tenantId, Utils.SALES_ORDER_API_NAME);
                for (ILayout iLayout : layoutByObjectApiName) {
                    LayoutExt of = LayoutExt.of(iLayout);
                    Optional<IFormField> settle_type = of.getField("settle_type");
                    if (settle_type.isPresent()) {
                        IFormField iFormField = settle_type.get();
                        if (iFormField.isRequired()) {
                            iFormField.setRequired(false);
                            serviceFacade.updateLayout(User.systemUser(tenantId), iLayout);
                        }
                    }
                }
            }
            //在收支流水中新增关联预存款的字段
            if (!accountFlowDescribe.containsField(PREPAY_ID_FIELD_OF_ACCOUNT_FLOW)) {
                ObjectReferenceFieldDescribe prepayUdfField = ObjectReferenceFieldDescribeBuilder.builder().apiName(PREPAY_ID_FIELD_OF_ACCOUNT_FLOW).label("预存款").targetApiName(PrepayDetailConstants.API_NAME).targetRelatedListLabel("账户收支流水").targetRelatedListName("target_related_list_prepay_account_flow").required(false).unique(false).build();
                prepayUdfField.setIsExtend(true);
                prepayUdfField.setDefineType("custom");
                prepayUdfField.setStatus("new");
                fundAccountManager.addField(User.systemUser(tenantId), AccountTransactionFlowConst.API_NAME, prepayUdfField.toJsonString(), "object_reference", false, true, false, null);
            }
            log.info("tenantId:{},start transfer PrepayIncome", tenantId);
            stepSuccess = transferIncomePrepay(tenantId, fundAccountId, newCustomerAccountOwner, rateLimiter);
            if (!stepSuccess) {
                log.warn("transfer prepayIncome error tenantId:{}", tenantId);
                errorEis.add(tenantId);
                continue;
            }
            log.info("tenantId:{},start transfer PrepayOutcome", tenantId);
            stepSuccess = transferOutcomePrepay(tenantId, fundAccountId, newCustomerAccountOwner, rateLimiter);
            if (!stepSuccess) {
                log.warn("transfer prepayOutcome error tenantId:{}", tenantId);
                errorEis.add(tenantId);
            }
            log.info("tenantId:{},start transfer RebateOutcome", tenantId);
            //返利支出迁移  TODO 关联退货单的如何处理呢？
            stepSuccess = transferRebateOutcome(tenantId, rateLimiter);
            if (!stepSuccess) {
                log.warn("transfer rebateOutcome error tenantId:{}", tenantId);
                errorEis.add(tenantId);
                continue;
            }
            log.info("tenantId:{},start addOrUpdateNewCustomerAccount", tenantId);
            //更新现金客户账户余额
            stepSuccess = addOrUpdateNewCustomerAccount(tenantId, fundAccountId, newCustomerAccountOwner, rateLimiter);
            if (!stepSuccess) {
                log.warn("transfer newCustomerAccount error tenantId:{}", tenantId);
                errorEis.add(tenantId);
                continue;
            }

            //预存款和返利迁移完了后，再将回款方式为预存款、返利、预存款+返利的回款作废掉
            log.info("tenantId:{},start deletePaymentMasterDetail", tenantId);
            stepSuccess = deletePaymentMasterAndDetail(tenantId, rateLimiter);
            if (!stepSuccess) {
                log.warn("delete order payment error tenantId:{}", tenantId);
                errorEis.add(tenantId);
            }
            /*//再处理回款
            log.info("tenantId:{},start deletePayment", tenantId);
            stepSuccess = deletePayment(tenantId);
            if (!stepSuccess) {
                log.warn("delete payment error tenantId:{}", tenantId);
                errorEis.add(tenantId);
            }*/
        }
        result.setErrorEis(errorEis);
        return result;
    }

    @ServiceMethod("transferByType")
    public Result transferByType(ServiceContext serviceContext, Arg arg) {
        Set<String> eis = arg.getEis();
        Result result = new Result();
        if (CollectionUtils.empty(eis)) {
            return result;
        }
        String transferType = arg.getType();
        RateLimiter rateLimiter = RateLimiter.create(ConfigCenter.customerAccountRefreshTps);
        Set<String> errorEis = Sets.newHashSet();
        for (String tenantId : eis) {
            Tuple<String, String> tuple = getFundAccountId(tenantId);
            String fundAccountId = tuple.getKey();
            String fundAccountOwner = tuple.getValue();
            boolean flag = true;
            if ("transferIncomePrepay".equals(transferType)) {
                flag = transferIncomePrepay(tenantId, fundAccountId, fundAccountOwner, rateLimiter);
            } else if ("transferOutcomePrepay".equals(transferType)) {
                flag = transferOutcomePrepay(tenantId, fundAccountId, fundAccountOwner, rateLimiter);
            } else if ("transferRebateOutcome".equals(transferType)) {
                flag = transferRebateOutcome(tenantId, rateLimiter);
            } else if ("addOrUpdateNewCustomerAccount".equals(transferType)) {
                flag = addOrUpdateNewCustomerAccount(tenantId, fundAccountId, fundAccountOwner, rateLimiter);
            } else if ("deleteOrderPayment".equals(transferType)) {
                flag = deleteOrderPayment(tenantId);
            } else if ("deletePayment".equals(transferType)) {
                flag = deletePayment(tenantId);
            } else if ("checkPaymentEnterIntoAccount".equals(transferType)) {
                flag = checkPaymentEnterIntoAccount(tenantId, fundAccountId);
            } else if ("checkTenants".equals(transferType)) {
                flag = canTransfer(tenantId);
            } else if ("deletePaymentMasterDetail".equals(transferType)) {
                flag = deletePaymentMasterAndDetail(tenantId, rateLimiter);
            }
            if (!flag) {
                errorEis.add(tenantId);
            }
        }
        result.setErrorEis(errorEis);
        return result;
    }

    @ServiceMethod("checkTenants")
    public Result checkEnterpriseData(ServiceContext serviceContext, Arg arg) {
        Result result = new Result();
        Set<String> eis = arg.getEis();
        if (CollectionUtils.empty(eis)) {
            return result;
        }
        Set<String> errorEis = Sets.newHashSet();
        for (String tenantId : eis) {
            boolean canTransfer = canTransfer(tenantId);
            if (!canTransfer) {
                errorEis.add(tenantId);
            }
        }
        result.setErrorEis(errorEis);
        return result;
    }

    public boolean transferIncomePrepay(String tenantId, String fundAccountId, String newCustomerAccountOwner, RateLimiter rateLimiter) {
        //处理关联回款的预存款
        int limit = ConfigCenter.customerAccountUpgradeQueryLimit, offset = 0, size = 0;
        List<IFilter> filters = Lists.newArrayList();
        List<OrderBy> orderByList = Lists.newArrayList();
        orderByList.add(new OrderBy("_id", true));
        User user = User.systemUser(tenantId);
        int totalPrepay = 0;
        int totalPayment = 0;
        int totalFlow = 0;
        do {
            rateLimiter.acquire();
            try {
                filters.clear();
                SearchUtil.fillFilterIsNull(filters, PAYMENT_ID_FIELD_OF_PREPAY);
                SearchUtil.fillFilterEq(filters, "record_type", PrepayDetailConstants.RecordType.IncomeRecordType.apiName);
                List<IObjectData> prepayIncomeDataList = prepayDetailManager.queryByFieldFilterList(user, PrepayDetailConstants.API_NAME, filters, orderByList, offset, limit);
                if (CollectionUtils.empty(prepayIncomeDataList)) {
                    break;
                }
                size = prepayIncomeDataList.size();
                Set<String> customerIds = Sets.newHashSet();
                prepayIncomeDataList.forEach(x -> {
                    String customerId = x.get(PrepayDetailConstants.Field.Customer.apiName, String.class);
                    customerIds.add(customerId);
                });

                //预存款收入  ->迁移到 回款，并进行入账（会生成回款和账户收支流水）
                //TODO 迁移到回款 这里如何保证幂等？
                Map<String, String> prepayId2PaymentIdMap = Maps.newHashMap();
                List<IObjectData> paymentDataList = prepayIncomeDataList.stream().map(x -> {
                    IObjectData paymentData = getPaymentDataFormPrepay(tenantId, x, fundAccountId);
                    prepayId2PaymentIdMap.put(x.getId(), paymentData.getId());
                    paymentData.set(PREPAY_ID_FIELD_OF_PAYMENT, x.getId());
                    return paymentData;
                }).collect(Collectors.toList());

                filters.clear();
                List<IObjectData> paymentList = Lists.newArrayList();
                if (MapUtils.isNotEmpty(prepayId2PaymentIdMap)) {
                    SearchUtil.fillFilterIn(filters, PREPAY_ID_FIELD_OF_PAYMENT, Lists.newArrayList(prepayId2PaymentIdMap.keySet()));
                    paymentList = prepayDetailManager.queryByFieldFilterList(user, "PaymentObj", filters, Lists.newArrayList(), 0, 1000);
                }
                Set<String> prepayIdsExist = paymentList.stream().map(x -> {
                    String prepayId = x.get(PREPAY_ID_FIELD_OF_PAYMENT, String.class);
                    prepayId2PaymentIdMap.put(prepayId, x.getId());
                    return prepayId;
                }).collect(Collectors.toSet());
                paymentDataList = paymentDataList.stream().filter(x -> {
                    String prepayId = x.get(PREPAY_ID_FIELD_OF_PAYMENT, String.class);
                    return !prepayIdsExist.contains(prepayId);
                }).collect(Collectors.toList());
                totalPayment += paymentDataList.size();
                paymentDataList = serviceFacade.bulkSaveObjectData(paymentDataList, user);

                //立即更新预存款中的关联回款的自定义字段  如果用sql更新需要考虑schema迁移企业
                for (IObjectData x : prepayIncomeDataList) {
                    String prepayId = x.getId();
                    String paymentId = x.get(PAYMENT_ID_FIELD_OF_PREPAY, String.class);
                    if (StringUtils.isEmpty(paymentId)) {
                        paymentId = prepayId2PaymentIdMap.get(prepayId);
                        x.set(PAYMENT_ID_FIELD_OF_PREPAY, paymentId);
                        rateLimiter.acquire();
                        serviceFacade.bulkUpdateObjectDataOneField(PAYMENT_ID_FIELD_OF_PREPAY, Lists.newArrayList(x), paymentId, User.systemUser(tenantId));
                        totalPrepay++;
                    }
                }
                //如果在这个节点失败了，会导致回款新建了 但是账户收支流水没有新建

                //回款入账生成账户收支流水
                Set<String> paymentIds = paymentDataList.stream().map(IObjectData::getId).collect(Collectors.toSet());
                List<Map> accountTransactionFlowWithPaymentList = Lists.newArrayList();
                if (CollectionUtils.notEmpty(paymentIds)) {
                    String accountTransactionFlowSql = String.format("select id,payment_id from account_transaction_flow where tenant_id='%s' and entry_status = '1' and is_deleted=0 and payment_id in(%s)", tenantId, Joiner.on(",").join(paymentIds.stream().map(x -> String.format("'%s'", x)).collect(Collectors.toList())));
                    accountTransactionFlowWithPaymentList = objectDataService.findBySql(tenantId, accountTransactionFlowSql);
                }
                Set<String> paymentIdsEnterAccount = Sets.newHashSet();
                if (CollectionUtils.notEmpty(accountTransactionFlowWithPaymentList)) {
                    accountTransactionFlowWithPaymentList.forEach(x -> {
                        if (Objects.nonNull(x)) {
                            String paymentId = String.valueOf(x.get("payment_id"));
                            paymentIdsEnterAccount.add(paymentId);
                        }
                    });
                }

                filters.clear();
                SearchUtil.fillFilterEq(filters, "fund_account_id", fundAccountId);
                SearchUtil.fillFilterIn(filters, "customer_id", Lists.newArrayList(customerIds));
                List<IObjectData> newCustomerAccountDataList = prepayDetailManager.queryByFieldFilterList(user, "NewCustomerAccountObj", filters, Lists.newArrayList(), 0, 1000);

                Map<String, BigDecimal> customerIdCustomerAccountBalanceMap = Maps.newHashMap();
                //新建客戶賬戶
                List<IObjectData> newCustomerAccountDataToAddList = Lists.newArrayList();
                Set<String> customerIdsExistNewCustomerAccount = newCustomerAccountDataList.stream().map(x -> x.get("customer_id", String.class)).collect(Collectors.toSet());
                customerIds.forEach(x -> {
                    if (!customerIdsExistNewCustomerAccount.contains(x)) {
                        newCustomerAccountDataToAddList.add(getNewCustomerAccountData(tenantId, x, fundAccountId, newCustomerAccountOwner, BigDecimal.ZERO));
                    }
                });
                newCustomerAccountDataList.addAll(serviceFacade.bulkSaveObjectData(newCustomerAccountDataToAddList, user));

                Map<String, String> customerIdCustomerAccountIdMap = Maps.newHashMap();
                newCustomerAccountDataList.forEach(x -> {
                    String customerId = x.get(NewCustomerAccountConstants.Field.Customer.apiName, String.class);
                    BigDecimal accountBalance = x.get(NewCustomerAccountConstants.Field.AccountBalance.apiName, BigDecimal.class, BigDecimal.ZERO);
                    customerIdCustomerAccountBalanceMap.put(customerId, accountBalance);
                    customerIdCustomerAccountIdMap.put(customerId, x.getId());
                });

                Set<String> customerIdsNeedUpdate = Sets.newHashSet();
                List<IObjectData> incomeAccountTransactionFlowList = paymentDataList.stream().filter(x -> !paymentIdsEnterAccount.contains(x.getId())).map(x -> getIncomeAccountTransactionFlow(tenantId, x, fundAccountId, customerIdCustomerAccountIdMap)).collect(Collectors.toList());
                incomeAccountTransactionFlowList.forEach(x -> {
                    String customerId = x.get("customer_id", String.class);
                    BigDecimal accountBalance = customerIdCustomerAccountBalanceMap.getOrDefault(customerId, BigDecimal.ZERO);
                    BigDecimal amount = x.get(AccountTransactionFlowConst.Field.RevenueAmount.apiName, BigDecimal.class);
                    BigDecimal resultAmount = accountBalance.add(amount);
                    customerIdCustomerAccountBalanceMap.put(customerId, resultAmount);
                    x.set(AccountTransactionFlowConst.Field.AccountBalance.apiName, resultAmount);
                    customerIdsNeedUpdate.add(customerId);
                });
                serviceFacade.bulkSaveObjectData(incomeAccountTransactionFlowList, user);
                totalFlow += incomeAccountTransactionFlowList.size();

                //更新客戶账户余额
                customerIdsNeedUpdate.forEach(x -> {
                    BigDecimal amount = customerIdCustomerAccountBalanceMap.getOrDefault(x, BigDecimal.ZERO);
                    String updateSql = String.format("update new_customer_account set account_balance =%f where tenant_id='%s' and customer_id='%s' and fund_account_id='%s'", amount, tenantId, x, fundAccountId);
                    rateLimiter.acquire();
                    specialTableMapper.setTenantId(tenantId).batchUpdateBySql(updateSql);
                });
                customerIdsNeedUpdate.clear();
            } catch (Exception e) {
                log.warn("transferPrepayIncome error tenantId:{},totalPrepay:{},totalPayment:{},totalFlow:{}", tenantId, totalPrepay, totalPayment, totalFlow, e);
                return false;
            }
        } while (size == limit);
        log.info("tenantId:{},transferPrepayIncome,totalPrepay:{},totalPayment:{},totalFlow:{}", tenantId, totalPrepay, totalPayment, totalFlow);
        return true;
    }

    public boolean transferOutcomePrepay(String tenantId, String fundAccountId, String newCustomerAccountOwner, RateLimiter rateLimiter) {
        int size = 0, limit = ConfigCenter.customerAccountUpgradeQueryLimit, offset = 0;
        List<IFilter> filters = Lists.newArrayList();
        List<OrderBy> orderByList = Lists.newArrayList();
        orderByList.add(new OrderBy("_id", true));
        User user = User.systemUser(tenantId);
        int totalPrepayOutcome = 0;
        do {
            rateLimiter.acquire();
            try {
                filters.clear();
                SearchUtil.fillFilterEq(filters, "record_type", PrepayDetailConstants.RecordType.OutcomeRecordType.apiName);
                List<IObjectData> prepayOutcomeDataList = prepayDetailManager.queryByFieldFilterList(user, PrepayDetailConstants.API_NAME, filters, orderByList, offset, limit);
                if (CollectionUtils.empty(prepayOutcomeDataList)) {
                    break;
                }
                size = prepayOutcomeDataList.size();
                offset += limit;
                List<String> prepayIdsOfOutcome = Lists.newArrayList();
                Set<String> orderPaymentIds = Sets.newHashSet();
                Set<String> customerIds = Sets.newHashSet();
                prepayOutcomeDataList.forEach(x -> {
                    String orderPaymentId = x.get(PrepayDetailConstants.Field.OrderPayment.apiName, String.class);
                    if (StringUtils.isNotEmpty(orderPaymentId)) {
                        orderPaymentIds.add(orderPaymentId);
                    }
                    prepayIdsOfOutcome.add(x.getId());
                    String customerId = x.get(PrepayDetailConstants.Field.Customer.apiName, String.class);
                    customerIds.add(customerId);
                });

                //查询回款明细 对应的订单id
                List<Map> orderPaymentList = Lists.newArrayList();
                if (CollectionUtils.notEmpty(orderPaymentIds)) {
                    String orderPaymentSql = String.format("select id,order_id,payment_id from payment_order where tenant_id='%s' and id in(%s)", tenantId, Joiner.on(",").join(orderPaymentIds.stream().map(x -> String.format("'%s'", x)).collect(Collectors.toSet())));
                    orderPaymentList = objectDataService.findBySql(tenantId, orderPaymentSql);
                }
                Map<String, String> orderPaymentOrderMap = Maps.newHashMap();
                if (CollectionUtils.notEmpty(orderPaymentList)) {
                    orderPaymentList.forEach(x -> {
                        if (Objects.nonNull(x)) {
                            String orderPaymentId = String.valueOf(x.get("id"));
                            String orderId = String.valueOf(x.get("order_id"));
                            orderPaymentOrderMap.put(orderPaymentId, orderId);
                        }
                    });
                }
                filters.clear();
                SearchUtil.fillFilterIn(filters, PREPAY_ID_FIELD_OF_ACCOUNT_FLOW, prepayIdsOfOutcome);
                List<IObjectData> accountFlowDataList = prepayDetailManager.queryByFieldFilterList(user, AccountTransactionFlowConst.API_NAME, filters, Lists.newArrayList(), 0, 1000);

                filters.clear();
                SearchUtil.fillFilterEq(filters, "fund_account_id", fundAccountId);
                SearchUtil.fillFilterIn(filters, "customer_id", Lists.newArrayList(customerIds));
                List<IObjectData> newCustomerAccountDataList = prepayDetailManager.queryByFieldFilterList(user, "NewCustomerAccountObj", filters, Lists.newArrayList(), 0, 1000);

                Map<String, BigDecimal> customerIdCustomerAccountBalanceMap = Maps.newHashMap();
                Map<String, String> customerIdCustomerAccountIdMap = Maps.newHashMap();
                //新建客戶賬戶
                List<IObjectData> newCustomerAccountDataToAddList = Lists.newArrayList();
                Set<String> customerIdsExistNewCustomerAccount = newCustomerAccountDataList.stream().map(x -> x.get("customer_id", String.class)).collect(Collectors.toSet());
                customerIds.forEach(x -> {
                    if (!customerIdsExistNewCustomerAccount.contains(x)) {
                        newCustomerAccountDataToAddList.add(getNewCustomerAccountData(tenantId, x, fundAccountId, newCustomerAccountOwner, BigDecimal.ZERO));
                    }
                });
                newCustomerAccountDataList.addAll(serviceFacade.bulkSaveObjectData(newCustomerAccountDataToAddList, user));
                newCustomerAccountDataList.forEach(x -> {
                    String customerId = x.get(NewCustomerAccountConstants.Field.Customer.apiName, String.class);
                    BigDecimal accountBalance = x.get(NewCustomerAccountConstants.Field.AccountBalance.apiName, BigDecimal.class, BigDecimal.ZERO);
                    customerIdCustomerAccountBalanceMap.put(customerId, accountBalance);
                    customerIdCustomerAccountIdMap.put(customerId, x.getId());
                });

                Set<String> customerIdsNeedUpdate = Sets.newHashSet();
                Set<String> prepayIdsOfOutcomeExist = accountFlowDataList.stream().map(x -> x.get(PREPAY_ID_FIELD_OF_ACCOUNT_FLOW, String.class)).collect(Collectors.toSet());
                //预存款支出  迁移到 账户收支流水上  并删除对应的回款
                List<IObjectData> outcomeAccountTransactionFlowList = prepayOutcomeDataList.stream().filter(x -> !prepayIdsOfOutcomeExist.contains(x.getId())).map(x -> getOutcomeAccountTransactionFlow(tenantId, x, orderPaymentOrderMap, fundAccountId, customerIdCustomerAccountIdMap)).collect(Collectors.toList());
                outcomeAccountTransactionFlowList.forEach(x -> {
                    String customerId = x.get("customer_id", String.class);
                    BigDecimal amount = x.get(AccountTransactionFlowConst.Field.ExpenseAmount.apiName, BigDecimal.class, BigDecimal.ZERO);
                    BigDecimal accountBalance = customerIdCustomerAccountBalanceMap.getOrDefault(customerId, BigDecimal.ZERO);
                    BigDecimal resultAmount = accountBalance.subtract(amount);
                    customerIdCustomerAccountBalanceMap.put(customerId, resultAmount);
                    x.set(AccountTransactionFlowConst.Field.AccountBalance.apiName, resultAmount);
                    customerIdsNeedUpdate.add(customerId);
                });
                totalPrepayOutcome += outcomeAccountTransactionFlowList.size();
                serviceFacade.bulkSaveObjectData(outcomeAccountTransactionFlowList, user);
                customerIdsNeedUpdate.forEach(x -> {
                    BigDecimal amount = customerIdCustomerAccountBalanceMap.getOrDefault(x, BigDecimal.ZERO);
                    String updateSql = String.format("update new_customer_account set account_balance =%f where tenant_id='%s' and customer_id='%s' and fund_account_id='%s'", amount, tenantId, x, fundAccountId);
                    rateLimiter.acquire();
                    specialTableMapper.setTenantId(tenantId).batchUpdateBySql(updateSql);
                });
                customerIdsNeedUpdate.clear();
            } catch (Exception e) {
                log.warn("transferOutcomePrepay error tenantId:{},totalPrepayOutcome:{}", tenantId, totalPrepayOutcome, e);
                return false;
            }
        } while (limit == size);
        log.info("transferPrepayOutcome,tenantId:{},totalPrepayOutcome:{}", tenantId, totalPrepayOutcome);
        return true;
    }

    public static String enterAccountPaymentSql = "select * from payment_customer where tenant_id='%s' and enter_into_account = true and is_deleted = 0 order by id limit %d offset %d";
    public static String accountTransactionFlowRelatePaymentSql = "select distinct(payment_id),id,customer_account_id,customer_id from account_transaction_flow where tenant_id='%s' and entry_status = '1' and is_deleted=0 and payment_id in(%s)";

    //如果回款入账了 但是没有生产收支流水
    public boolean checkPaymentEnterIntoAccount(String tenantId, String fundAccountId) {
        int limit = 500, size, offset = 0;
        do {
            String paymentSql = String.format(enterAccountPaymentSql, tenantId, limit, offset);
            try {
                List<Map> paymentList = objectDataService.findBySql(tenantId, paymentSql);
                if (CollectionUtils.empty(paymentList)) {
                    break;
                }
                size = paymentList.size();
                offset += limit;
                Set<String> customerIds = Sets.newHashSet();
                Map<String, Map> paymentIdDataMap = paymentList.stream().peek(x -> {
                    String customerId = x.get(PaymentConstants.Field.Customer.apiName).toString();
                    customerIds.add(customerId);
                }).collect(Collectors.toMap(x -> String.valueOf(x.get("id")), Function.identity()));

                Map<String, String> customerId2CustomerAccountIdMap = Maps.newHashMap();
                String customerIdsParam = Joiner.on(",").join(customerIds.stream().map(x -> String.format("'%s'", x)).collect(Collectors.toSet()));
                String newCustomerAccountSql = String.format(newCustomerAccountSqlFormat, tenantId, fundAccountId, customerIdsParam);
                List<Map> newCustomerAccountList = objectDataService.findBySql(tenantId, newCustomerAccountSql);

                newCustomerAccountList.forEach(x -> {
                    String id = x.get("id").toString();
                    String customerId = x.get("customer_id").toString();
                    customerId2CustomerAccountIdMap.put(customerId, id);
                });
                String paymentIdParam = Joiner.on(",").join(paymentIdDataMap.keySet().stream().map(x -> String.format("'%s'", x)).collect(Collectors.toSet()));
                String accountFlowSql = String.format(accountTransactionFlowRelatePaymentSql, tenantId, paymentIdParam);
                List<Map> accountFlowList = objectDataService.findBySql(tenantId, accountFlowSql);
                Set<String> paymentIdsHasFlow = Sets.newHashSet();
                if (CollectionUtils.notEmpty(accountFlowList)) {
                    accountFlowList.forEach(x -> {
                        String paymentId = String.valueOf(x.get("payment_id"));
                        paymentIdsHasFlow.add(paymentId);
                    });
                }
                List<IObjectData> accountFlowDataListToAdd = Lists.newArrayList();
                paymentIdDataMap.forEach((paymentId, paymentData) -> {
                    if (!paymentIdsHasFlow.contains(paymentId)) {
                        accountFlowDataListToAdd.add(getIncomeAccountTransactionFlow(tenantId, paymentMapToObjectData(paymentData), fundAccountId, customerId2CustomerAccountIdMap));
                    }
                });
                serviceFacade.bulkSaveObjectData(accountFlowDataListToAdd, User.systemUser(tenantId));
            } catch (Exception e) {
                log.warn("create lack account flow data error tenantId:{}", tenantId, e);
                return false;
            }
        } while (size == limit);
        return true;
    }

    public boolean transferRebateOutcome(String tenantId, RateLimiter rateLimiter) {
        int limit = ConfigCenter.customerAccountUpgradeQueryLimit, offset = 0, size;
        do {
            try {
                String rebateOutcomeSql = String.format("select * from rebate_outcome_detail where tenant_id='%s' and order_payment_id is not null and order_id is null order by id limit %d offset %d", tenantId, limit, offset);
                List<Map> rebateOutcomeList = objectDataService.findBySql(tenantId, rebateOutcomeSql);
                if (CollectionUtils.notEmpty(rebateOutcomeList)) {
                    size = rebateOutcomeList.size();
                    offset += limit;
                } else {
                    break;
                }
                Map<String, String> rebateOutcomeId2OrderPaymentIdMap = Maps.newHashMap();
                Set<String> orderPaymentIds = rebateOutcomeList.stream().map(x -> {
                    String orderPaymentId = (String) x.get(RebateOutcomeDetailConstants.Field.OrderPayment.apiName);
                    String rebateOutcomeId = String.valueOf(x.get("id"));
                    rebateOutcomeId2OrderPaymentIdMap.put(rebateOutcomeId, orderPaymentId);
                    return orderPaymentId;
                }).collect(Collectors.toSet());
                String orderPaymentSql = String.format("select id,order_id from payment_order where tenant_id='%s' and id in(%s)", tenantId, Joiner.on(",").join(orderPaymentIds.stream().map(x -> String.format("'%s'", x)).collect(Collectors.toSet())));
                List<Map> orderPaymentList = objectDataService.findBySql(tenantId, orderPaymentSql);
                Map<String, String> orderPaymentId2OrderIdMap = Maps.newHashMap();
                if (CollectionUtils.notEmpty(orderPaymentList)) {
                    orderPaymentList.forEach(x -> {
                        String orderId = String.valueOf(x.get("order_id"));
                        String orderPaymentId = String.valueOf(x.get("id"));
                        orderPaymentId2OrderIdMap.put(orderPaymentId, orderId);
                    });
                }
                String rebateOutcomeUpdateSqlFormat = "update rebate_outcome_detail set order_id='%s' where id = '%s' and tenant_id='%s'";
                rebateOutcomeId2OrderPaymentIdMap.forEach((rebateOutcomeId, orderPaymentId) -> {
                    String orderId = orderPaymentId2OrderIdMap.get(orderPaymentId);
                    if (StringUtils.isNotEmpty(orderId)) {
                        rateLimiter.acquire();
                        specialTableMapper.setTenantId(tenantId).batchUpdateBySql(String.format(rebateOutcomeUpdateSqlFormat, orderId, rebateOutcomeId, tenantId));
                    }
                });
            } catch (Exception e) {
                log.warn("transfer rebate outcome error tenantId:{}", tenantId, e);
                return false;
            }
        } while (size == limit);
        return true;
    }

    /**
     * rebate_outcome prepay 只有在 10002时不为空
     *
     * @param tenantId
     * @return
     */
    @Deprecated
    public boolean deleteOrderPayment(String tenantId) {
        int offset = 0, limit = 500, size;
        try {
            do {
                String orderPaymentSql = String.format("select id, rebate_outcome,prepay from payment_order where tenant_id='%s' and is_deleted = 0 and (rebate_outcome >0 or prepay>0) order by id limit %d offset %d", tenantId, limit, offset);
                List<Map> orderPaymentList = objectDataService.findBySql(tenantId, orderPaymentSql);
                if (CollectionUtils.empty(orderPaymentList)) {
                    break;
                }
                offset += limit;
                size = orderPaymentList.size();
                Set<String> orderPaymentIdsToDelete = orderPaymentList.stream().map(x -> String.valueOf(x.get("id"))).collect(Collectors.toSet());
                specialTableMapper.setTenantId(tenantId).updateDeleteStatus("payment_order", DELETE_STATUS.DELETE.getValue(), System.currentTimeMillis(), orderPaymentIdsToDelete, tenantId);
            } while (size == limit);
        } catch (Exception e) {
            log.warn("transfer delete order payment error tenantId:{}", tenantId, e);
            return false;
        }
        return true;
    }

    public boolean deletePaymentMasterAndDetail(String tenantId, RateLimiter rateLimiter) {
        int offset = 0, limit = ConfigCenter.customerAccountUpgradeQueryLimit, total = 0, size;

        try {
            do {
                String paymentSql = String.format("select id,payment_term,payment_amount from payment_customer where tenant_id='%s' and payment_term in('10000','10001','10002') and is_deleted = 0 order by id limit %d offset %d", tenantId, limit, offset);
                List<Map> paymentDataList = objectDataService.findBySql(tenantId, paymentSql);
                if (CollectionUtils.empty(paymentDataList)) {
                    break;
                }
                size = paymentDataList.size();
                offset += limit;
                total += size;

                Set<String> paymentIds = paymentDataList.stream().filter(Objects::nonNull).map(x -> String.valueOf(x.get("id"))).collect(Collectors.toSet());
                if (CollectionUtils.notEmpty(paymentIds)) {
                    rateLimiter.acquire(paymentIds.size());
                    specialTableMapper.setTenantId(tenantId).updateDeleteStatus("payment_customer", DELETE_STATUS.DELETE.getValue(), System.currentTimeMillis(), paymentIds, tenantId);
                    total += paymentIds.size();
                    String orderPaymentSql = String.format("select id from payment_order where tenant_id='%s' and is_deleted = 0 and payment_id in(%s)", tenantId, Joiner.on(",").join(paymentIds.stream().map(x -> String.format("'%s'", x)).collect(Collectors.toList())));
                    List<Map> orderPaymentList = objectDataService.findBySql(tenantId, orderPaymentSql);
                    if (CollectionUtils.notEmpty(orderPaymentList)) {
                        Set<String> orderPaymentIds = orderPaymentList.stream().filter(Objects::nonNull).map(x -> String.valueOf(x.get("id"))).collect(Collectors.toSet());
                        specialTableMapper.setTenantId(tenantId).updateDeleteStatus("payment_order", DELETE_STATUS.DELETE.getValue(), System.currentTimeMillis(), orderPaymentIds, tenantId);
                    }
                }
            } while (limit == size);
        } catch (Exception e) {
            log.warn("deletePaymentMasterDetail error,tenantId:{},totalPayment:{}", tenantId, total, e);
            return false;
        }
        log.info("deletePaymentMasterDetail tenantId:{},totalPayment:{}", tenantId, total);
        return true;
    }

    public boolean deletePayment(String tenantId) {
        int offset = 0, limit = ConfigCenter.customerAccountUpgradeQueryLimit, size;
        try {
            do {
                String paymentSql = String.format("select id,payment_term from payment_customer where tenant_id='%s' and  payment_term in('10000','10001','10002') and is_deleted = 0 order by id limit %d offset %d", tenantId, limit, offset);
                List<Map> paymentDataList = objectDataService.findBySql(tenantId, paymentSql);
                if (CollectionUtils.empty(paymentDataList)) {
                    break;
                }
                Set<String> paymentIds = paymentDataList.stream().filter(Objects::nonNull).map(x -> String.valueOf(x.get("id"))).collect(Collectors.toSet());
                specialTableMapper.setTenantId(tenantId).updateDeleteStatus("payment_customer", DELETE_STATUS.DELETE.getValue(), System.currentTimeMillis(), paymentIds, tenantId);
                size = paymentDataList.size();
                offset += limit;
            } while (size == limit);
        } catch (Exception e) {
            log.warn("transfer delete payment error tenantId:{}", tenantId, e);
            return false;
        }
        return true;
    }

    public static String accountTransactionFlowSqlFormat = "select distinct(customer_id) from account_transaction_flow where tenant_id='%s' limit %d offset %d";
    public static String incomeFlowSqlFormat = "select sum(revenue_amount) as revenue_amount,customer_id from account_transaction_flow where tenant_id='%s' and is_deleted=0 and life_status='normal' and entry_status = '1' and customer_id in(%s) group by customer_id";
    public static String outcomeFlowSqlFormat = "select sum(expense_amount) as expense_amount,customer_id from account_transaction_flow where tenant_id='%s' and is_deleted=0 and life_status='normal' and entry_status = '1' and customer_id in(%s) group by customer_id";
    public static String newCustomerAccountSqlFormat = "select id,customer_id,fund_account_id,account_balance from new_customer_account where tenant_id='%s' and customer_id in(%s) and fund_account_id = '%s'";
    public static String newCustomerAccountUpdateSqlFormat = "update new_customer_account set account_balance=%f where id ='%s' and tenant_id = '%s'";

    /**
     * 预存款迁移到账户收支流水后，需要同步更新客户账户余额(对于没有现金客户账户的客户，需要新建现金客户账户)
     *
     * @param tenantId
     * @param newCustomerAccountOwner
     * @return
     */
    public boolean addOrUpdateNewCustomerAccount(String tenantId, String fundAccountId, String newCustomerAccountOwner, RateLimiter rateLimiter) {
        int limit = 500;
        int offset = 0, size = 0;

        do {
            String accountTransactionFlowSql = String.format(accountTransactionFlowSqlFormat, tenantId, limit, offset);
            try {
                List<Map> accountTransactionFlowDataList = objectDataService.findBySql(tenantId, accountTransactionFlowSql);
                if (CollectionUtils.empty(accountTransactionFlowDataList)) {
                    break;
                }
                size = accountTransactionFlowDataList.size();
                offset += size;
                Set<String> customerIds = accountTransactionFlowDataList.stream().filter(Objects::nonNull).map(x -> String.valueOf(x.get("customer_id"))).collect(Collectors.toSet());
                List<Map> newCustomerAccountDataList = Lists.newArrayList();
                String customerIdSqlParam = null;
                if (CollectionUtils.notEmpty(customerIds)) {
                    customerIdSqlParam = Joiner.on(",").join(customerIds.stream().filter(StringUtils::isNotEmpty).map(x -> String.format("'%s'", x)).collect(Collectors.toSet()));
                    String newCustomerAccountSql = String.format(newCustomerAccountSqlFormat, tenantId, customerIdSqlParam, fundAccountId);
                    newCustomerAccountDataList = objectDataService.findBySql(tenantId, newCustomerAccountSql);
                }
                Map<String, Tuple<String, BigDecimal>> newCustomerAccountCustomerAmountMap = Maps.newHashMap();
                if (CollectionUtils.notEmpty(newCustomerAccountDataList)) {
                    newCustomerAccountDataList.forEach(x -> {
                        if (Objects.nonNull(x)) {
                            String customerId = String.valueOf(x.get("customer_id"));
                            String newCustomerAccountId = String.valueOf(x.get("id"));
                            BigDecimal accountBalance = new BigDecimal(String.valueOf(x.get("account_balance")));
                            Tuple<String, BigDecimal> tuple = Tuple.of(newCustomerAccountId, accountBalance);
                            newCustomerAccountCustomerAmountMap.put(customerId, tuple);
                        }
                    });
                }
                List<Map> incomeDataList = Lists.newArrayList();
                if (CollectionUtils.notEmpty(customerIds)) {
                    String incomeSql = String.format(incomeFlowSqlFormat, tenantId, customerIdSqlParam);
                    incomeDataList = objectDataService.findBySql(tenantId, incomeSql);
                }
                List<Map> outcomeDataList = Lists.newArrayList();
                if (CollectionUtils.notEmpty(customerIds)) {
                    String outcomeSql = String.format(outcomeFlowSqlFormat, tenantId, customerIdSqlParam);
                    outcomeDataList = objectDataService.findBySql(tenantId, outcomeSql);
                }
                Map<String, BigDecimal> amountMap = Maps.newHashMap();
                if (CollectionUtils.notEmpty(incomeDataList)) {
                    incomeDataList.forEach(x -> {
                        if (Objects.nonNull(x)) {
                            String customerId = String.valueOf(x.get("customer_id"));
                            Object value = x.get("revenue_amount");
                            if (Objects.nonNull(value)) {
                                BigDecimal valueAmount = new BigDecimal(value.toString());
                                BigDecimal amount = amountMap.get(customerId);
                                if (Objects.nonNull(amount)) {
                                    amount = amount.add(valueAmount);
                                } else {
                                    amount = valueAmount;
                                }
                                amountMap.put(customerId, amount);
                            }
                        }
                    });
                }
                if (CollectionUtils.notEmpty(outcomeDataList)) {
                    outcomeDataList.forEach(x -> {
                        if (Objects.nonNull(x)) {
                            String customerId = String.valueOf(x.get("customer_id"));
                            Object value = x.get("expense_amount");
                            if (Objects.nonNull(value)) {
                                BigDecimal valueAmount = new BigDecimal(value.toString());
                                BigDecimal amount = amountMap.get(customerId);
                                if (Objects.nonNull(amount)) {
                                    amount = amount.subtract(valueAmount);
                                } else {
                                    amount = valueAmount.negate();
                                }
                                amountMap.put(customerId, amount);
                            }
                        }
                    });
                }
                List<IObjectData> newCustomerAccountAddList = Lists.newArrayList();
                customerIds.forEach(x -> {
                    BigDecimal addAmount = amountMap.getOrDefault(x, BigDecimal.ZERO);
                    if (newCustomerAccountCustomerAmountMap.containsKey(x)) {
                        Tuple<String, BigDecimal> idAccountBalanceTuple = newCustomerAccountCustomerAmountMap.get(x);
                        String id = idAccountBalanceTuple.getKey();
                        if (idAccountBalanceTuple.getValue().compareTo(addAmount) != 0) {
                            rateLimiter.acquire();
                            specialTableMapper.setTenantId(tenantId).batchUpdateBySql(String.format(newCustomerAccountUpdateSqlFormat, addAmount.doubleValue(), id, tenantId));
                        }
                    } else {
                        newCustomerAccountAddList.add(getNewCustomerAccountData(tenantId, x, fundAccountId, newCustomerAccountOwner, addAmount));
                    }
                });
                if (CollectionUtils.notEmpty(newCustomerAccountAddList)) {
                    rateLimiter.acquire(newCustomerAccountAddList.size());
                    serviceFacade.bulkSaveObjectData(newCustomerAccountAddList, User.systemUser(tenantId));
                }
            } catch (Exception e) {
                log.warn("transfer new customer account error tenantId:{},sql:{}", tenantId, accountTransactionFlowSql, e);
                return false;
            }
        } while (size == limit);
        return true;
    }

    public static String PREPAY_CHECK_SQL = "select count(1) from prepay_detail where tenant_id='%s' and is_deleted = 0 and life_status <> 'normal'";
    public static String PAYMENT_CHECK_SQL = "select count(1) from payment_customer where tenant_id='%s' and is_deleted = 0 and life_status <> 'normal' and payment_term in('10000','10001','10002')";
    public static String ORDER_PAYMENT_CHECK_SQL = "select count(1) from payment_order where tenant_id='%s' and is_deleted = 0 and life_status <> 'normal' and (COALESCE(prepay,0) >0 or COALESCE(rebate_outcome)>0)";

    public static Set<String> FIELD_TYPES_NOT_NEED_FILL = Sets.newHashSet(IFieldType.RECORD_TYPE, IFieldType.DEPARTMENT, IFieldType.FORMULA, IFieldType.AUTO_NUMBER, IFieldType.COUNT);
    public static Set<String> PAYMENT_FIELDS_NOT_NEED_FILL = Sets.newHashSet(PaymentConstants.Field.EnterIntoAccount.apiName, PaymentConstants.Field.PaymentAmount.apiName, PaymentConstants.Field.Amount.apiName,
            PaymentConstants.Field.AvailableAmount.apiName, PaymentConstants.Field.Customer.apiName, PaymentConstants.Field.PaymentPurpose.apiName, PaymentConstants.Field.PaymentTime.apiName,
            "created_by", "last_modified_by", "create_time", "last_modified_by", "owner", "tenant_id", "life_status", "object_describe_api_name");

    private boolean canTransfer(String tenantId) {
        String prepayCheckSql = String.format(PREPAY_CHECK_SQL, tenantId);
        String paymentCheckSql = String.format(PAYMENT_CHECK_SQL, tenantId);
        String orderPaymentCheckSql = String.format(ORDER_PAYMENT_CHECK_SQL, tenantId);
        try {
            IObjectDescribe paymentDescribe = serviceFacade.findObject(tenantId, Utils.CUSTOMER_PAYMENT_API_NAME);
            IFieldDescribe enterIntoAccountField = paymentDescribe.getFieldDescribe(PaymentConstants.Field.EnterIntoAccount.apiName);
            if (Objects.isNull(enterIntoAccountField)) {
                log.info("tenantId:{},enterIntoAccount field is not exist", tenantId);
                return false;
            }
            List<IFieldDescribe> paymentFieldDescribes = paymentDescribe.getFieldDescribes();
            paymentFieldDescribes = paymentFieldDescribes.stream().filter(x -> x.isActive() && !FIELD_TYPES_NOT_NEED_FILL.contains(x.getType()) && x.isRequired() && !PAYMENT_FIELDS_NOT_NEED_FILL.contains(x.getApiName())).collect(Collectors.toList());
            if (CollectionUtils.notEmpty(paymentFieldDescribes)) {
                log.info("tenantId:{},paymentFields is required:{}", tenantId, paymentFieldDescribes);
                return false;
            }
            List<Map> prepayResult = objectDataService.findBySql(tenantId, prepayCheckSql);
            long count = Long.parseLong(prepayResult.get(0).get("count").toString());
            if (count > 0) {
                log.info("tenantId={}, prepay count={}", tenantId, count);
                return false;
            }
            List<Map> paymentResult = objectDataService.findBySql(tenantId, paymentCheckSql);
            count = Long.parseLong(paymentResult.get(0).get("count").toString());
            if (count > 0) {
                log.info("tenantId={}, payment count={}", tenantId, count);
                return false;
            }
            List<Map> orderPaymentResult = objectDataService.findBySql(tenantId, orderPaymentCheckSql);
            count = Long.parseLong(orderPaymentResult.get(0).get("count").toString());
            if (count > 0) {
                log.info("tenantId={}, orderPayment count={}", tenantId, count);
                return false;
            }
        } catch (Exception e) {
            log.warn("checkDataStatus tenantId:{},prepayCheckSql:{},paymentCheckSql:{},orderPaymentCheckSql:{}", tenantId, prepayCheckSql, paymentCheckSql, orderPaymentCheckSql, e);
            return false;
        }
        return true;
    }

    public Tuple<String, String> getFundAccountId(String tenantId) {
        List<Map> fundAccountList;
        try {
            fundAccountList = objectDataService.findBySql(tenantId, String.format("select id,owner from fund_account where tenant_id='%s' and type='1'", tenantId));
        } catch (Exception e) {
            log.warn("query fund account error,tenantId:{}", tenantId, e);
            return null;
        }
        if (CollectionUtils.size(fundAccountList) != 1) {
            log.warn("query fund account error,tenantId:{},fundAccountList:{}", tenantId, fundAccountList);
            return null;
        }
        return Tuple.of(String.valueOf(fundAccountList.get(0).get("id")), String.valueOf(fundAccountList.get(0).get("owner")));
    }

    @ServiceMethod("invalidAndDelete")
    public Set<String> bulkInvalidDelete(ServiceContext serviceContext, ObjectArg arg) {
        String objectApiName = arg.getObjectApiName();
        Set<String> ids = arg.getIds();
        Set<String> result = Sets.newHashSet();
        if (StringUtils.isEmpty(objectApiName) || CollectionUtils.empty(ids)) {
            return result;
        }
        List<IObjectData> dataList = serviceFacade.findObjectDataByIdsIgnoreAll(serviceContext.getTenantId(), Lists.newArrayList(ids), objectApiName);
        Map<String, BigDecimal> customerId2AmountMap = Maps.newHashMap();
        Tuple<String, String> tuple = getFundAccountId(serviceContext.getTenantId());
        if (Utils.CUSTOMER_PAYMENT_API_NAME.equals(objectApiName)) {
            dataList.forEach(x -> {
                BigDecimal amount = x.get(PaymentConstants.Field.Amount.apiName, BigDecimal.class);
                String customerId = x.get(PaymentConstants.Field.Customer.apiName, String.class);
                BigDecimal oldAmount = customerId2AmountMap.computeIfAbsent(customerId, k -> BigDecimal.ZERO);
                customerId2AmountMap.put(customerId, oldAmount.add(amount));
            });
        }
        Map<String, Map<String, Object>> columnMap = Maps.newHashMap();
        List<IObjectData> newCustomerAccountDataList = Lists.newArrayList();
        if (!customerId2AmountMap.isEmpty()) {
            String fundAccountId = tuple.getKey();
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, "fund_account_id", fundAccountId);
            SearchUtil.fillFilterIn(filters, "customer_id", Lists.newArrayList(customerId2AmountMap.keySet()));
            newCustomerAccountDataList = prepayDetailManager.queryByFieldFilterList(serviceContext.getUser(), "NewCustomerAccountObj", filters, Lists.newArrayList(), 0, customerId2AmountMap.size());
            newCustomerAccountDataList.forEach(x -> {
                String id = x.getId();
                String customerId = x.get(NewCustomerAccountConstants.Field.Customer.apiName, String.class);
                BigDecimal amount = customerId2AmountMap.get(customerId);
                Map<String, Object> dataMap = Maps.newHashMap();
                dataMap.put(NewCustomerAccountConstants.Field.AccountBalance.apiName, amount.negate());
                columnMap.put(id, dataMap);
            });
        }
        serviceFacade.bulkInvalidAndDeleteWithSuperPrivilege(dataList, serviceContext.getUser());
        if (!newCustomerAccountDataList.isEmpty()) {
            log.info("tenantId:{},updateCustomerAccountMap:{}", serviceContext.getTenantId(), columnMap);
            serviceFacade.batchUpdateByFields(serviceContext.getUser(), newCustomerAccountDataList, Lists.newArrayList(NewCustomerAccountConstants.Field.AccountBalance.apiName), columnMap);
        }
        return result;
    }

    @ServiceMethod("update_rule_use_record_is_udef")
    public Map<String, Object> updateIsUdefField(ServiceContext serviceContext) {
        Map<String, Object> result = Maps.newHashMap();
        String tenantId = serviceContext.getTenantId();
        int ei = Integer.parseInt(tenantId);
        if (ei >= 0) {
            IObjectDescribe objectDescribe = serviceFacade.findObject(tenantId, AccountRuleUseRecordConstants.API_NAME);
            IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(AccountRuleUseRecordConstants.Field.RuleStage.apiName);
            boolean isUdef = objectDescribe.isUdef();
            result.put("is_udef", isUdef);
            result.put("rule_stage", fieldDescribe);
            return result;
        }
        String updateSql = String.format("update mt_describe set is_udef = false where tenant_id='%s' and describe_api_name='%s'", tenantId, AccountRuleUseRecordConstants.API_NAME);
        specialTableMapper.setTenantId(tenantId).batchUpdateBySql(updateSql);
        return result;
    }

    @ServiceMethod("batch_init_payment_enter_account")
    public Set<String> batchInitPaymentEnterAccount(ServiceContext serviceContext, CurlServiceImpl.TenantIdModel.Arg arg) {
        List<String> tenantIds = arg.getTenantIds();
        Set<String> errorEis = Sets.newHashSet();
        if (CollectionUtils.empty(tenantIds)) {
            return errorEis;
        }
        ServiceContext tenantContext;
        User user;
        for (String tenantId : tenantIds) {
            user = User.systemUser(tenantId);
            tenantContext = new ServiceContext(RequestContext.builder().tenantId(tenantId).user(user).build(), null, null);
            try {
                fundAccountService.paymentEnterAccountInit(tenantContext);
            } catch (Exception e) {
                log.warn("initPaymentEnterAccount error,tenantId:{}", tenantId, e);
                errorEis.add(tenantId);
            }
        }
        return errorEis;
    }

    @ServiceMethod("transferPaymentFundAccountField")
    public Set<String> transferPaymentFundAccount(ServiceContext serviceContext, CurlServiceImpl.TenantIdModel.Arg arg) {
        List<String> tenantIds = arg.getTenantIds();
        Set<String> failTenantIds = Sets.newHashSet();
        if (CollectionUtils.empty(tenantIds)) {
            return failTenantIds;
        }
        int limit = 500;
        int offset = 0, size;
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setOrders(Lists.newArrayList(new OrderBy("_id", true)));
        searchTemplateQuery.setLimit(limit);
        searchTemplateQuery.setOffset(offset);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, PaymentConstants.Field.EnterIntoAccount.apiName, Lists.newArrayList(Boolean.TRUE));
        SearchUtil.fillFilterIsNull(filters, PaymentConstants.Field.FundAccount.apiName);
        searchTemplateQuery.setFilters(filters);
        RateLimiter rateLimiter = RateLimiter.create(limit);
        for (String tenantId : tenantIds) {
            filters.clear();
            try {
                User user = User.systemUser(tenantId);
                IObjectData fundData = fundAccountManager.batchInitFundAccountDatas(user);
                String fundAccountId = fundData.getId();
                do {
                    List<IObjectData> paymentDataList = serviceFacade.findBySearchQuery(user, Utils.CUSTOMER_PAYMENT_API_NAME, searchTemplateQuery).getData();
                    size = CollectionUtils.size(paymentDataList);
                    paymentDataList.removeIf(x -> {
                        String dataFundAccountId = x.get(PaymentConstants.Field.FundAccount.apiName, String.class);
                        return StringUtils.isNotEmpty(dataFundAccountId);
                    });
                    if (CollectionUtils.empty(paymentDataList)) {
                        break;
                    }
                    rateLimiter.acquire(paymentDataList.size());
                    paymentDataList.forEach(x -> x.set(PaymentConstants.Field.FundAccount.apiName, fundAccountId));
                    serviceFacade.bulkUpdateObjectDataOneField(PaymentConstants.Field.FundAccount.apiName, paymentDataList, fundAccountId, user);
                } while (size == limit);
                bizConfigManager.createOrUpdate(user, ConfigKeyEnum.PAYMENT_ENTER_ACCOUNT_CONFIG_KEY.key, ConfigKeyEnum.PAYMENT_ENTER_ACCOUNT_CONFIG_KEY.enabledValue);
            } catch (Exception e) {
                log.warn("tenantId:{} transferPaymentFundAccountField error", tenantId, e);
                failTenantIds.add(tenantId);
            }
        }
        return failTenantIds;
    }

    /**
     * 冠缇雅 迁移数据
     *
     * @param serviceContext
     */
    @ServiceMethod("syncGtyIncomeFlowType")
    public Result syncGtyIncomeFlowType(ServiceContext serviceContext, UpdateGtyFlowArg arg) {
        String tenantId = serviceContext.getTenantId();
        User user = User.systemUser(tenantId);
        Result result = new Result();
        String flowId = arg.getFlowId();
        if (StringUtils.isNotEmpty(flowId)) {
            IObjectData flowData = serviceFacade.findObjectData(user, flowId, AccountTransactionFlowConst.API_NAME);
            transferGtyFlowIncomeType(user, Lists.newArrayList(flowData));
            return result;
        }
        if (!BooleanUtils.isTrue(arg.getAllFlow())) {
            return result;
        }
        int size;
        int limit = 1000;
        do {
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, AccountTransactionFlowConst.Field.RevenueType.apiName, RevenueTypeEnum.PaymentCharge.getValue());
            SearchUtil.fillFilterEq(filters, "record_type", AccountTransactionFlowConst.RecordType.IncomeRecordType.apiName);
            List<OrderBy> orderByList = Lists.newArrayList();
            SearchUtil.fillOrderBy(orderByList, "_id", true);
            searchTemplateQuery.setFilters(filters);
            searchTemplateQuery.setOrders(orderByList);
            searchTemplateQuery.setLimit(limit);
            searchTemplateQuery.setOffset(0);
            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, AccountTransactionFlowConst.API_NAME, searchTemplateQuery);
            List<IObjectData> flowDataList = queryResult.getData();
            size = CollectionUtils.size(flowDataList);
            transferGtyFlowIncomeType(user, flowDataList);
        } while (size == limit);
        return result;
    }

    @ServiceMethod("syncGtyOutcomeFlowType")
    public Result syncGtyOutcomeFlowType(ServiceContext serviceContext, UpdateGtyFlowArg arg) {
        String flowId = arg.getFlowId();
        User user = User.systemUser(serviceContext.getTenantId());
        Result result = new Result();
        if (StringUtils.isNotEmpty(flowId)) {
            IObjectData outcomeFlowData = serviceFacade.findObjectData(user, flowId, AccountTransactionFlowConst.API_NAME);
            transferGtyFlowOutcomeType(user, Lists.newArrayList(outcomeFlowData));
            return result;
        }
        if (!BooleanUtils.isTrue(arg.getAllFlow())) {
            return result;
        }
        int size, limit = 1000;
        do {
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, AccountTransactionFlowConst.Field.ExpenseType.apiName, ExpenseTypeEnum.ManualDeduct.getValue());
            SearchUtil.fillFilterEq(filters, "record_type", AccountTransactionFlowConst.RecordType.OutcomeRecordType.apiName);
            SearchUtil.fillFilterNotNull(filters, PREPAY_ID_FIELD_OF_ACCOUNT_FLOW);
            List<OrderBy> orderByList = Lists.newArrayList();
            SearchUtil.fillOrderBy(orderByList, "_id", true);
            searchTemplateQuery.setFilters(filters);
            searchTemplateQuery.setOrders(orderByList);
            searchTemplateQuery.setLimit(limit);
            searchTemplateQuery.setOffset(0);
            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, AccountTransactionFlowConst.API_NAME, searchTemplateQuery);
            List<IObjectData> flowDataList = queryResult.getData();
            size = CollectionUtils.size(flowDataList);
            transferGtyFlowOutcomeType(user, flowDataList);
        } while (size == limit);
        return result;
    }

    @ServiceMethod("update_describe_by_tenants")
    public Set<String> updateDescribe(ServiceContext serviceContext, UpdateDescribeArg arg) {
        Set<String> failEis = Sets.newHashSet();
        Set<String> eis = CollectionUtils.nullToEmpty(arg.getEis());
        String objectApiName = arg.getObjectApiName();
        for (String tenantId : eis) {
            try {
                List<Map<String, Object>> updateFieldList = CollectionUtils.nullToEmpty(arg.getFieldList());
                if (CollectionUtils.empty(updateFieldList)) {
                    continue;
                }
                IObjectDescribe objectDescribe = serviceFacade.findObject(tenantId, objectApiName);
                updateFieldList.forEach(x -> {
                    String apiName = (String) x.get("api_name");
                    IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(apiName);
                    if (Objects.nonNull(fieldDescribe)) {
                        x.put("type", fieldDescribe.getType());
                    }
                    IFieldDescribe updateFieldDescribe = FieldDescribeFactory.newInstance(x);
                    if (Objects.isNull(fieldDescribe)) {
                        ObjectDescribeExt.of(objectDescribe).addFieldDescribe(updateFieldDescribe);
                    } else if (IFieldType.SELECT_ONE.equals(fieldDescribe.getType()) || IFieldType.SELECT_MANY.equals(fieldDescribe.getType())) {
                        SelectOneFieldDescribe updateSelectOneFieldDescribe = (SelectOneFieldDescribe) updateFieldDescribe;
                        List<ISelectOption> updateSelectOptions = updateSelectOneFieldDescribe.getSelectOptions();
                        SelectOneFieldDescribe selectOneFieldDescribe = (SelectOneFieldDescribe) fieldDescribe;
                        List<ISelectOption> selectOptions = selectOneFieldDescribe.getSelectOptions();
                        Map<String, ISelectOption> selectOptionMap = selectOptions.stream().collect(Collectors.toMap(ISelectOption::getValue, Function.identity()));
                        updateSelectOptions.forEach(updateOption -> {
                            String updateLabel = updateOption.getLabel();
                            String updateValue = updateOption.getValue();
                            ISelectOption selectOption = selectOptionMap.get(updateValue);
                            if (Objects.nonNull(selectOption)) {
                                if (!StringUtils.equals(updateLabel, selectOption.getLabel()) && StringUtils.isNotEmpty(updateLabel)) {
                                    selectOption.setLabel(updateLabel);
                                }
                            } else {
                                selectOptions.add(SelectOptionBuilder.builder().label(updateLabel).value(updateValue).build());
                            }
                        });
                        selectOneFieldDescribe.setSelectOptions(selectOptions);
                    } else {
                        x.forEach(fieldDescribe::set);
                    }
                });
                serviceFacade.update(objectDescribe);
            } catch (Exception e) {
                failEis.add(tenantId);
                log.warn("updateDescribe fail tenantId:{}", tenantId, e);
            }
        }
        return failEis;
    }

    @ServiceMethod("update_fund_account_data")
    public Set<String> updateFundAccountData(ServiceContext serviceContext, Arg arg) {
        Set<String> eis = CollectionUtils.nullToEmpty(arg.getEis());
        Set<String> failEis = Sets.newHashSet();
        for (String tenantId : eis) {
            int size, offset = 0;
            User user = User.systemUser(tenantId);
            try {
                do {
                    SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
                    searchTemplateQuery.setOffset(offset);
                    searchTemplateQuery.setLimit(100);
                    List<IFilter> filters = Lists.newArrayList();
                    SearchUtil.fillFilterIsNull(filters, FundAccountConstants.Field.AccountType.apiName);
                    searchTemplateQuery.setFilters(filters);
                    List<IObjectData> fundAccountDataList = serviceFacade.findBySearchQuery(user, FundAccountConstants.API_NAME, searchTemplateQuery).getData();
                    size = CollectionUtils.size(fundAccountDataList);
                    List<IObjectData> toUpdateDataList = Lists.newArrayList();
                    fundAccountDataList.forEach(x -> {
                        String accountType = x.get(FundAccountConstants.Field.AccountType.apiName, String.class);
                        if (StringUtils.isEmpty(accountType)) {
                            toUpdateDataList.add(x);
                        }
                    });
                    serviceFacade.bulkUpdateObjectDataOneField(FundAccountConstants.Field.AccountType.apiName, toUpdateDataList, FundAccountAccountTypeEnum.Amount.value, user);
                    offset += 100;
                } while (size == 100);
            } catch (Exception e) {
                log.warn("updateFundAccountData error tenantId:{}", tenantId, e);
                failEis.add(tenantId);
            }
        }
        return failEis;
    }

    private void transferGtyFlowOutcomeType(User user, List<IObjectData> flowDataList) {
        List<String> prepayIds = Lists.newArrayList();
        for (IObjectData flowData : flowDataList) {
            String prepayId = flowData.get(PREPAY_ID_FIELD_OF_ACCOUNT_FLOW, String.class);
            if (StringUtils.isEmpty(prepayId)) {
                continue;
            }
            prepayIds.add(prepayId);
        }
        RateLimiter rateLimiter = RateLimiter.create(400);
        List<IObjectData> prepayDataList = serviceFacade.findObjectDataByIds(user.getTenantId(), prepayIds, PrepayDetailConstants.API_NAME);
        Map<String, IObjectData> prepayDataMap = prepayDataList.stream().collect(Collectors.toMap(IObjectData::getId, Function.identity()));
        for (IObjectData flowData : flowDataList) {
            String prepayId = flowData.get(PREPAY_ID_FIELD_OF_ACCOUNT_FLOW, String.class);
            IObjectData prepayData = prepayDataMap.get(prepayId);
            if (Objects.isNull(prepayData) || !PrepayDetailConstants.RecordType.OutcomeRecordType.apiName.equals(prepayData.getRecordType())) {
                continue;
            }
            String outcomeType = prepayData.get(PrepayDetailConstants.Field.OutcomeType.apiName, String.class);
            if (StringUtils.isEmpty(outcomeType)) {
                continue;
            }
            String expenseType = null;
            boolean needUpdate = false;
            switch (outcomeType) {
                case "yG314Y23v":
                    //调账下分
                    needUpdate = true;
                    expenseType = "2vDVbY661";
                    break;
                case "3":
                    //手动扣减
                    break;
                default:
                    if (outcomeType.startsWith("other")) {
                        needUpdate = true;
                        expenseType = "other";
                        String outcomeTypeExtra = prepayData.get(PrepayDetailConstants.Field.OutcomeType.apiName + "__o", String.class);
                        flowData.set(AccountTransactionFlowConst.Field.ExpenseType.apiName + "__o", outcomeTypeExtra);
                    }
                    break;
            }
            if (needUpdate) {
                rateLimiter.acquire();
                flowData.set(AccountTransactionFlowConst.Field.ExpenseType.apiName, expenseType);
                serviceFacade.batchUpdateByFields(user, Lists.newArrayList(flowData), Lists.newArrayList(AccountTransactionFlowConst.Field.ExpenseType.apiName,
                        AccountTransactionFlowConst.Field.ExpenseType.apiName + "__o"));
            }
        }
    }

    private void transferGtyFlowIncomeType(User user, List<IObjectData> flowDataList) {
        String tenantId = user.getTenantId();
        List<String> paymentIds = Lists.newArrayList();
        for (IObjectData flowData : flowDataList) {
            String paymentId = flowData.get(AccountTransactionFlowConst.Field.Payment.apiName, String.class);
            if (StringUtils.isEmpty(paymentId)) {
                continue;
            }
            paymentIds.add(paymentId);
        }
        List<IObjectData> paymentDataList = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, paymentIds, PaymentConstants.API_NAME);

        Map<String, String> paymentId2PrepayIdMap = Maps.newHashMap();
        List<String> prepayIds = Lists.newArrayList();
        for (IObjectData paymentData : paymentDataList) {
            String prepayId = paymentData.get(PREPAY_ID_FIELD_OF_PAYMENT, String.class);
            if (StringUtils.isEmpty(prepayId)) {
                continue;
            }
            prepayIds.add(prepayId);
            paymentId2PrepayIdMap.put(paymentData.getId(), prepayId);
        }

        List<IObjectData> prepayDataList = serviceFacade.findObjectDataByIds(tenantId, prepayIds, PrepayDetailConstants.API_NAME);
        Map<String, IObjectData> prepayIdDataMap = Maps.newHashMap();
        for (IObjectData prepayData : prepayDataList) {
            String incomeType = prepayData.get(PrepayDetailConstants.Field.IncomeType.apiName, String.class);
            if (StringUtils.isEmpty(incomeType)) {
                continue;
            }
            if (incomeType.startsWith("other")) {
                prepayIdDataMap.put(prepayData.getId(), prepayData);
            }
        }
        RateLimiter rateLimiter = RateLimiter.create(400);
        for (IObjectData flowData : flowDataList) {
            String paymentId = flowData.get(AccountTransactionFlowConst.Field.Payment.apiName, String.class);
            if (StringUtils.isEmpty(paymentId)) {
                continue;
            }
            String prepayId = paymentId2PrepayIdMap.get(paymentId);
            if (StringUtils.isEmpty(prepayId)) {
                continue;
            }
            IObjectData prepayData = prepayIdDataMap.get(prepayId);
            if (Objects.isNull(prepayData)) {
                continue;
            }
            String incomeType = prepayData.get(PrepayDetailConstants.Field.IncomeType.apiName, String.class);
            if (StringUtils.isEmpty(incomeType) || !incomeType.startsWith("other")) {
                continue;
            }
            String incomeTypeExtra = prepayData.get(PrepayDetailConstants.Field.IncomeType.apiName + "__o", String.class);
            flowData.set(AccountTransactionFlowConst.Field.RevenueType.apiName, incomeType);
            flowData.set(AccountTransactionFlowConst.Field.RevenueType.apiName + "__o", incomeTypeExtra);
            rateLimiter.acquire();
            serviceFacade.batchUpdateByFields(user, Lists.newArrayList(flowData), Lists.newArrayList(AccountTransactionFlowConst.Field.RevenueType.apiName,
                    AccountTransactionFlowConst.Field.RevenueType.apiName + "__o"));
        }
    }

    /**
     * 如果回款新建了自定义必填字段那么就无法迁移成功
     *
     * @param tenantId
     * @param prepayIncome
     * @return
     */
    private IObjectData getPaymentDataFormPrepay(String tenantId, IObjectData prepayIncome, String fundAccountId) {
        IObjectData paymentData = new ObjectData();
        paymentData.setTenantId(tenantId);
        paymentData.setCreatedBy(prepayIncome.getCreatedBy());
        paymentData.setCreateTime(prepayIncome.getCreateTime());
        paymentData.setLastModifiedTime(prepayIncome.getLastModifiedTime());
        paymentData.setRecordType("default__c");
        paymentData.set(PaymentConstants.Field.EnterIntoAccount.apiName, true);
        paymentData.set(PaymentConstants.Field.FundAccount.apiName, fundAccountId);
        paymentData.set(PaymentConstants.Field.PaymentAmount.apiName, BigDecimal.ZERO);
        paymentData.set(PaymentConstants.Field.Amount.apiName, prepayIncome.get(PrepayDetailConstants.Field.Amount.apiName));
        paymentData.set(PaymentConstants.Field.AvailableAmount.apiName, prepayIncome.get(PrepayDetailConstants.Field.Amount.apiName));
        paymentData.set(PaymentConstants.Field.Customer.apiName, prepayIncome.get(PrepayDetailConstants.Field.Customer.apiName));
        paymentData.set(PaymentConstants.Field.PaymentTime.apiName, prepayIncome.get(PrepayDetailConstants.Field.TransactionTime.apiName));
        paymentData.set(PaymentConstants.Field.PaymentPurpose.apiName, PaymentPurposeEnum.Prepay.value);
        paymentData.set("life_status", prepayIncome.get("life_status", String.class));
        paymentData.setDescribeApiName(Utils.CUSTOMER_PAYMENT_API_NAME);
        //防止设置默认值
        paymentData.set(PaymentConstants.Field.PaymentTerm.apiName, null);
        paymentData.set(PaymentConstants.Field.PayType.apiName, null);
        paymentData.setOwner(prepayIncome.getOwner());
        paymentData.setId(serviceFacade.generateId());
        return paymentData;
    }

    private IObjectData getIncomeAccountTransactionFlow(String tenantId, IObjectData paymentData, String fundAccountId, Map<String, String> customerIdCustomerAccountIdMap) {
        IObjectData objectData = new ObjectData();
        objectData.setTenantId(tenantId);
        String customerId = paymentData.get(PaymentConstants.Field.Customer.apiName, String.class);
        objectData.set(AccountTransactionFlowConst.Field.Customer.apiName, customerId);
        objectData.set(AccountTransactionFlowConst.Field.FundAccount.apiName, fundAccountId);
        objectData.set(AccountTransactionFlowConst.Field.TransactionDate.apiName, paymentData.get(PaymentConstants.Field.PaymentTime.apiName));
        objectData.set(AccountTransactionFlowConst.Field.RevenueType.apiName, RevenueTypeEnum.PaymentCharge.getValue());
        objectData.set(AccountTransactionFlowConst.Field.RevenueAmount.apiName, paymentData.get(PaymentConstants.Field.Amount.apiName));
        objectData.set(AccountTransactionFlowConst.Field.Payment.apiName, paymentData.getId());
        objectData.set(AccountTransactionFlowConst.Field.RelateRecordObjectApiName.apiName, PaymentConstants.API_NAME);
        objectData.set(AccountTransactionFlowConst.Field.RelateRecordObjectDataId.apiName, paymentData.getId());
        objectData.set(AccountTransactionFlowConst.Field.AccessModule.apiName, AccessModuleEnum.DEFAULT.value);
        objectData.setDescribeApiName(AccountTransactionFlowConst.API_NAME);
        objectData.setOwner(paymentData.getOwner());
        objectData.setCreateTime(paymentData.getCreateTime());
        objectData.setLastModifiedTime(paymentData.getLastModifiedTime());
        objectData.setCreatedBy(paymentData.getCreatedBy());
        objectData.setLastModifiedBy(paymentData.getLastModifiedBy());
        objectData.setRecordType(AccountTransactionFlowConst.RecordType.IncomeRecordType.apiName);
        objectData.set(AccountTransactionFlowConst.Field.CustomerAccount.apiName, customerIdCustomerAccountIdMap.get(customerId));
        objectData.set(AccountTransactionFlowConst.Field.EntryStatus.apiName, EntryStatusEnum.AlreadyEntry.getValue());
        return objectData;
    }

    private IObjectData getOutcomeAccountTransactionFlow(String tenantId, IObjectData prepayOutcomeData, Map<String, String> orderPaymentOrderMap, String fundAccountId, Map<String, String> customerIdCustomerAccountIdMap) {
        IObjectData objectData = new ObjectData();
        objectData.setTenantId(tenantId);
        String customerId = prepayOutcomeData.get(PrepayDetailConstants.Field.Customer.apiName, String.class);
        objectData.set(AccountTransactionFlowConst.Field.Customer.apiName, customerId);
        objectData.set(AccountTransactionFlowConst.Field.FundAccount.apiName, fundAccountId);
        objectData.set(AccountTransactionFlowConst.Field.TransactionDate.apiName, prepayOutcomeData.get(PrepayDetailConstants.Field.TransactionTime.apiName));

        String relateOrderId = orderPaymentOrderMap.get(String.valueOf(prepayOutcomeData.get(PrepayDetailConstants.Field.OrderPayment.apiName)));
        String expenseType = ExpenseTypeEnum.ManualDeduct.getValue();
        if (StringUtils.isNotEmpty(relateOrderId)) {
            expenseType = ExpenseTypeEnum.SalesDeduct.getValue();
            objectData.set(AccountTransactionFlowConst.Field.SalesOrder.apiName, relateOrderId);
        }
        objectData.set(AccountTransactionFlowConst.Field.ExpenseType.apiName, expenseType);
        objectData.set(AccountTransactionFlowConst.Field.ExpenseAmount.apiName, prepayOutcomeData.get(PrepayDetailConstants.Field.Amount.apiName));
        objectData.set(PREPAY_ID_FIELD_OF_ACCOUNT_FLOW, prepayOutcomeData.getId());
        objectData.setDescribeApiName(AccountTransactionFlowConst.API_NAME);
        objectData.setOwner(prepayOutcomeData.getOwner());
        objectData.setCreateTime(prepayOutcomeData.getCreateTime());
        objectData.setLastModifiedTime(prepayOutcomeData.getLastModifiedTime());
        objectData.setCreatedBy(prepayOutcomeData.getCreatedBy());
        objectData.setLastModifiedBy(prepayOutcomeData.getLastModifiedBy());
        objectData.setRecordType(AccountTransactionFlowConst.RecordType.OutcomeRecordType.apiName);
        objectData.set(AccountTransactionFlowConst.Field.EntryStatus.apiName, EntryStatusEnum.AlreadyEntry.getValue());
        objectData.set(AccountTransactionFlowConst.Field.CustomerAccount.apiName, customerIdCustomerAccountIdMap.get(customerId));
        return objectData;
    }


    private IObjectData getNewCustomerAccountData(String tenantId, String customerId, String fundAccountId, String newCustomerAccountOwner, BigDecimal accountBalance) {
        IObjectData objectData = new ObjectData();
        objectData.setDescribeApiName(NewCustomerAccountConstants.API_NAME);
        objectData.setCreatedBy(newCustomerAccountOwner);
        objectData.setLastModifiedBy(newCustomerAccountOwner);
        objectData.setTenantId(tenantId);
        objectData.setRecordType(MultiRecordType.RECORD_TYPE_DEFAULT);
        objectData.set(NewCustomerAccountConstants.Field.Customer.apiName, customerId);
        objectData.set(NewCustomerAccountConstants.Field.FundAccount.apiName, fundAccountId);
        //TODO
        objectData.set(NewCustomerAccountConstants.Field.AccountBalance.apiName, accountBalance);
        objectData.set(SystemConstants.Field.LockStatus.apiName, "0");
        objectData.set(SystemConstants.Field.LifeStatus.apiName, "normal");
        objectData.setOwner(Lists.newArrayList(newCustomerAccountOwner));
        return objectData;
    }

    private IObjectData paymentMapToObjectData(Map map) {
        IObjectData paymentData = new ObjectData();
        String owner = String.valueOf(map.get("owner"));
        paymentData.setOwner(Lists.newArrayList(owner));
        paymentData.set(PaymentConstants.Field.Customer.apiName, map.get(PaymentConstants.Field.Customer.apiName));
        paymentData.setCreatedBy(String.valueOf(map.get("created_by")));
        paymentData.set(PaymentConstants.Field.PaymentTime.apiName, map.get(PaymentConstants.Field.PaymentTime.apiName));
        paymentData.set(PaymentConstants.Field.Amount.apiName, map.get(PaymentConstants.Field.Amount.apiName));
        paymentData.setId(String.valueOf(map.get("id")));
        return paymentData;
    }

    @Data
    public static class Arg {
        private Set<String> eis = Sets.newHashSet();
        private String type;
    }

    @Data
    public static class Result {
        private Set<String> errorEis = Sets.newHashSet();
    }

    @Data
    public static class ObjectArg {
        private String objectApiName;
        private Set<String> ids;
    }

    @Data
    public static class UpdateGtyFlowArg {
        private String flowId;
        private Boolean allFlow;
    }

    @Data
    public static class UpdateDescribeArg {
        private Set<String> eis = Sets.newHashSet();
        private List<Map<String, Object>> fieldList = Lists.newArrayList();
        private String objectApiName;
    }
}
