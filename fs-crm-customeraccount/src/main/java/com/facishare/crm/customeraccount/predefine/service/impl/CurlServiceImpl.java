package com.facishare.crm.customeraccount.predefine.service.impl;

import static com.facishare.crmcommon.constants.CommonConstants.CRM_MANAGER_ROLE;
import static com.facishare.crmcommon.constants.CommonConstants.ORDERING_PERSON_ROLE;
import static com.facishare.crmcommon.constants.CommonConstants.ORDER_FINANCE_ROLE;
import static com.facishare.crmcommon.constants.CommonConstants.ORDER_MANAGER_ROLE;

import com.facishare.converter.EIEAConverter;
import com.facishare.crm.config.provider.PaymentWithDetailEnterAccountConfigProvider;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.customeraccount.constants.*;
import com.facishare.crm.customeraccount.entity.CustomerAccountBillStatistics;
import com.facishare.crm.customeraccount.enums.*;
import com.facishare.crm.customeraccount.exception.CustomerAccountBusinessException;
import com.facishare.crm.customeraccount.exception.CustomerAccountErrorCode;
import com.facishare.crm.customeraccount.predefine.handler.RuleHandlerUtil;
import com.facishare.crm.customeraccount.predefine.handler.checkrule.FundAccountMediator;
import com.facishare.crm.customeraccount.predefine.job.CustomerAndCustomerAccountCompareJob;
import com.facishare.crm.customeraccount.predefine.job.PaymentAndCustomerAccountCompareJob;
import com.facishare.crm.customeraccount.predefine.job.RefundAndCustomerAccountCompareJob;
import com.facishare.crm.customeraccount.predefine.manager.*;
import com.facishare.crm.customeraccount.predefine.manager.objectInit.AccountAuthInitManager;
import com.facishare.crm.customeraccount.predefine.remote.CrmManager;
import com.facishare.crm.customeraccount.predefine.service.AccountAuthService;
import com.facishare.crm.customeraccount.predefine.service.CurlService;
import com.facishare.crm.customeraccount.predefine.service.CustomerAccountService;
import com.facishare.crm.customeraccount.predefine.service.InitService;
import com.facishare.crm.customeraccount.predefine.service.SfaOrderPaymentService;
import com.facishare.crm.customeraccount.predefine.service.dto.*;
import com.facishare.crm.customeraccount.predefine.service.dto.CurlModel.BatchDelFuncModelResult;
import com.facishare.crm.customeraccount.predefine.service.dto.SfaOrderPaymentModel.EditArgNew;
import com.facishare.crm.customeraccount.util.*;
import com.facishare.crmcommon.constants.CommonConstants;
import com.facishare.crmcommon.constants.LayoutConstants;
import com.facishare.crmcommon.describebuilder.*;
import com.facishare.crmcommon.manager.CommonDescribeManager;
import com.facishare.crmcommon.manager.CommonObjDataManager;
import com.facishare.crmcommon.rest.ApprovalInitProxy;
import com.facishare.crmcommon.rest.DailyAbnormalCustomerAccountProxy;
import com.facishare.crmcommon.rest.dto.ApprovalInitModel;
import com.facishare.crmcommon.rest.dto.CustomerAccountConfigModel;
import com.facishare.crmcommon.rest.dto.DailyAbnormalCustomerAccountModel;
import com.facishare.crmcommon.rest.dto.GetCurInstanceStateModel;
import com.facishare.crmcommon.util.CrmLayoutUtil;
import com.facishare.crmcommon.util.SearchUtil;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.FundAccountBaseService;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeProxy;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.privilege.UserDefinedButtonService;
import com.facishare.paas.appframework.privilege.dto.AuthContext;
import com.facishare.paas.appframework.privilege.dto.CreateFunctionPrivilege;
import com.facishare.paas.appframework.privilege.dto.DelFuncCodeRoles.Arg;
import com.facishare.paas.appframework.privilege.dto.UpdateRoleModifiedFuncPrivilege;
import com.facishare.paas.appframework.privilege.model.FunctionCodeBuilder;
import com.facishare.paas.appframework.privilege.model.FunctionPrivilegeProvider;
import com.facishare.paas.appframework.privilege.model.FunctionPrivilegeProviderManager;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.Tenantable;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IObjectReferenceField;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.api.service.ILayoutService;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.*;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.impl.ui.layout.FieldSection;
import com.facishare.paas.metadata.impl.ui.layout.FormField;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.component.CommonComponent;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.facishare.paas.metadata.impl.ui.layout.component.SimpleComponent;
import com.facishare.paas.metadata.impl.ui.layout.component.TableComponent;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.ui.layout.ITableColumn;
import com.facishare.paas.metadata.util.SpringUtil;
import com.facishare.rest.core.util.JsonUtil;
import com.github.trace.TraceContext;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.quartz.Job;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @IgnoreI18nFile
 */
@Service
@Slf4j
public class CurlServiceImpl implements CurlService {
    @Autowired
    private CustomerAccountManager customerAccountManager;
    @Autowired
    private CustomerAccountConfigManager customerAccountConfigManager;
    @Autowired
    private CrmManager crmManager;
    @Autowired
    private InitService initService;
    @Autowired
    private IObjectDescribeService objectDescribeService;
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private ILayoutService layoutService;
    @Autowired
    private FunctionPrivilegeService functionPrivilegeService;
    @Autowired
    private FunctionPrivilegeProxy functionPrivilegeProxy;
    @Autowired
    private FunctionPrivilegeProviderManager providerManager;
    @Autowired
    private CommonManager commonManager;
    @Autowired
    private CommonObjDataManager commonObjDataManager;
    @Autowired
    private BillJobManager billJobManager;
    @Autowired
    private FundAccountManager fundAccountManager;
    @Autowired
    private FundAccountMediator fundAccountMediator;
    @Autowired
    private DailyAbnormalCustomerAccountProxy dailyAbnormalCustomerAccountProxy;
    @Autowired
    private CustomerAccountBillManager customerAccountBillManager;
    @Autowired
    private RebateIncomeDetailManager rebateIncomeDetailManager;
    @Autowired
    private ObjectDataServiceImpl objectDataService;
    @Resource(type = ApprovalInitProxy.class)
    private ApprovalInitProxy approvalInitProxy;
    @Autowired
    private CreditFileManager creditFileManager;
    @Autowired
    private PrepayDetailManager prepayDetailManager;
    @Autowired
    private SfaOrderPaymentService sfaOrderPaymentService;
    @Autowired
    private FundAccountConfigManager fundAccountConfigManager;
    @Autowired
    private CaFieldManager caFieldManager;
    @Autowired
    private ConfigService configService;
    @Autowired
    private UserDefinedButtonService userDefinedButtonService;
    @Autowired
    private CaButtonManager caButtonManager;
    @Autowired
    private CaRuleEngineManager caRuleEngineManager;
    @Autowired
    private FAccountAuthorizationManager fAccountAuthorizationManager;
    @Autowired
    private CommonDescribeManager commonDescribeManager;
    @Autowired
    private TransactionFlowDataManager transactionFlowDataManager;
    @Autowired
    private CustomerAccountService customerAccountService;
    @Autowired
    private CustomerAccountServiceImpl customerAccountServiceImpl;
    @Autowired
    private CustomerAccountDomainPluginManager customerAccountDomainPluginManager;

    @Autowired
    private AccountAuthService accountAuthService;
    @Autowired
    private AccountCheckRuleManager accountCheckRuleManager;
    @Autowired
    private AuthorizationDetailManager authorizationDetailManager;
    @Autowired
    private FAccountEntryRuleManager fAccountEntryRuleManager;
    @Autowired
    private AccountRuleUseRecordManager accountRuleUseRecordManager;
    @Autowired
    private AccountCheckRuleDomainPluginManager accountCheckRuleDomainPluginManager;
    @Autowired
    private DomainPluginManager caObjectDomainPluginManager;
    @Autowired
    private CaNotifyManager caNotifyManager;
    @Autowired
    private AccountAuthInitManager accountAuthInitManager;
    @Autowired
    private NewCustomerAccountManager newCustomerAccountManager;
    @Autowired
    private CreditManager creditManager;
    @Autowired
    private EIEAConverter eIEAConverter;
    @Autowired
    private UnfreezeAuthDetailManager unfreezeAuthDetailManager;
    @Autowired
    private DomainPluginManager domainPluginManager;

    private Logger logger = LoggerFactory.getLogger("compareObjectAndFlowAmount");

    @Override
    public String ping() {
        return "success";
    }

    @Override
    public CurlModel.CusAccIds deleteCustomerAccounts(CurlModel.DeleteCusAccArgs accArgs, ServiceContext serviceContext) {
        CurlModel.CusAccIds cusAccIds = new CurlModel.CusAccIds();
        List<String> notDelIds = Lists.newArrayList();
        User user = serviceContext.getUser();
        int limit = 500;
        int offset = 0;
        while (true) {
            DailyAbnormalCustomerAccountModel.ListArg listArg = new DailyAbnormalCustomerAccountModel.ListArg();
            listArg.setTenantId(accArgs.getTenantId());
            listArg.setBillDate(accArgs.getBillDate().getTime());
            listArg.setRelateType(accArgs.getRelateType());
            listArg.setOffset(offset);
            listArg.setLimit(limit);
            DailyAbnormalCustomerAccountModel.ListResult listResult = dailyAbnormalCustomerAccountProxy.list(listArg);
            if (!listResult.isSuccess() || Objects.isNull(listResult.getValue())) {
                break;
            }
            List<String> customerAccountIds = listResult.getValue().getCustomerAccountIds();
            for (String customerAccountId : customerAccountIds) {
                QueryResult<IObjectData> queryCustomerAccountList = new QueryResult<>();
                try {
                    queryCustomerAccountList = customerAccountManager.queryInvalidDataByField(user, CustomerAccountConstants.API_NAME, "_id", Lists.newArrayList(customerAccountId), 0, 10);
                } catch (Exception e) {
                    notDelIds.add(customerAccountId);
                    log.warn("queryInvalidData customerAccountObj failed", e);
                    continue;
                }
                List<IObjectData> customerAccountList = queryCustomerAccountList.getData();
                if (CollectionUtils.isNotEmpty(customerAccountList)) {
                    IObjectData objectData = customerAccountList.get(0);
                    String customerId = objectData.get("customer_id", String.class);
                    String lifeStatus = objectData.get("life_status", String.class);
                    Boolean isDeleted = objectData.get("is_deleted", Boolean.class);
//                    String sql = "select * from customer where customer_id='%s';";
                    String sql = "select * from biz_account where id='%s';";
                    sql = String.format(sql, customerId);
                    QueryResult<IObjectData> customerListResult;
                    try {
                        customerListResult = objectDataService.findBySql(sql, accArgs.getTenantId(), "AccountObj");
                    } catch (MetadataServiceException e) {
                        notDelIds.add(customerAccountId);
                        log.warn("findBySql exception,tenantId:{} sql:{}", accArgs.getTenantId(), sql, e);
                        continue;
                    }
                    if (CollectionUtils.isEmpty(customerListResult.getData())) {
                        try {
                            if (!"invalid".equals(lifeStatus)) {
                                serviceFacade.invalid(objectData, user);
                            }
                            if (!isDeleted) {
                                serviceFacade.bulkDelete(Lists.newArrayList(objectData), user);
                            }
                        } catch (Exception e) {
                            notDelIds.add(customerAccountId);
                            log.warn("invalid or delete data error,customerAccountId:{}", customerAccountId, e);
                        }
                    }
                } else {
                    notDelIds.add(customerAccountId);
                }
            }
            if (customerAccountIds.size() == 500) {
                offset += 500;
            } else {
                break;
            }
        }
        cusAccIds.setCustomerAccountIds(notDelIds);
        return cusAccIds;
    }

    @Override
    public EmptyResult enableCustomerAccountByCurl(CurlModel.TenantIds arg, ServiceContext serviceContext) {
        List<String> tenantIds = arg.getTenantIds();
        List<String> openingList = customerAccountConfigManager.list(CustomerAccountType.CustomerAccountEnableSwitchStatus.OPENING.getValue(), tenantIds);

        for (String tenantId : openingList) {
            try {
                boolean success = customerAccountManager.batchInitCustomerAccounts(tenantId);
                log.info("set customerAccount Enable,for tenantId:{},result={}", tenantId, success);
            } catch (Exception e) {
                log.error("error opening customerAccount,for tenantId:{}", tenantId);
            }
        }
        return new EmptyResult();
    }

    @Override
    public CurlModel.LackCustomerAccountInitResult initLackedCustomerAccountData(CurlModel.TenantIds arg, ServiceContext serviceContext) {
        CurlModel.LackCustomerAccountInitResult result = new CurlModel.LackCustomerAccountInitResult();
        List<String> resultTenantIds = Lists.newArrayList();
        //初始化客户账户
        int offset = 0;
        int limit = ConfigCenter.batchCreateSize;
        int fetchSize = 0;
        Set<String> tenantIds = Sets.newHashSet(arg.getTenantIds());
        tenantIds.add(serviceContext.getTenantId());
        for (String tenantId : tenantIds) {
            User tempUser = new User(tenantId, tenantId.equals(serviceContext.getTenantId()) ? serviceContext.getUser().getUserId() : User.SUPPER_ADMIN_USER_ID);
            do {
                List<IObjectData> customerObjectDatas = crmManager.listPlainCustomersFromPg(tempUser, null, offset, limit);
                Map<String, String> customerIdLifeStatusMap = customerObjectDatas.stream().collect(Collectors.toMap(o -> o.get("id", String.class), o -> o.get("life_status", String.class)));
                try {
                    customerAccountManager.batchInitCustomerAccountDatas(tempUser, customerIdLifeStatusMap);
                } catch (Exception e) {
                    log.warn("customerObjectDatas={}", customerObjectDatas, e);
                }
                fetchSize = customerObjectDatas.size();
                offset += limit;
            } while (fetchSize == limit);
            resultTenantIds.add(tenantId);
            log.info("tenantId:{} lackCustomerAccountDatasInit success", tenantId);
        }
        result.setTenantIds(resultTenantIds);
        return result;
    }

    @Override
    public CurlModel.TenantIds listTenantIdsOfLackCustomerAccountDatas(CurlModel.TenantIds arg, ServiceContext serviceContext) {
        CurlModel.TenantIds result = new CurlModel.TenantIds();
        List<String> tenantIds = crmManager.listTenantIdsOfLackCustomerAccountDatas(arg.getTenantIds());
        result.setTenantIds(tenantIds);
        return result;
    }

    @Override
    public CurlModel.QueryCustomerResult queryCustomersFromPg(CurlModel.QueryCustomerArg arg, ServiceContext serviceContext) {
        List<IObjectData> customerObjectDatas = crmManager.listPlainCustomersFromPg(serviceContext.getUser(), arg.getCustomerIds(), arg.getOffset(), arg.getLimit());
        CurlModel.QueryCustomerResult result = new CurlModel.QueryCustomerResult();
        result.setCustomerObjectDatas(ObjectDataDocument.ofList(customerObjectDatas));
        return result;
    }

    @Override
    public CurlModel.CustomerStatusBeforeInvalidResult listCustomerStatusBeforeInvalid(CurlModel.CustomerStatusBeforeInvalidArg arg, ServiceContext serviceContext) {
        Map<String, Integer> oldStatusMap = crmManager.listCustomerStatusBeforeInvalid(serviceContext.getUser(), arg.getCustomerIds());
        CurlModel.CustomerStatusBeforeInvalidResult result = new CurlModel.CustomerStatusBeforeInvalidResult();
        result.setLifeStatusBeforeInvalid(oldStatusMap);
        return result;
    }

    @Override
    public CurlModel.FixCustomerAccountLifeStatusResult fixCustomerAccountLifeStatus(CurlModel.FixCustomerAccountLifeStatusArg arg, ServiceContext serviceContext) {
        CurlModel.FixCustomerAccountLifeStatusResult result = new CurlModel.FixCustomerAccountLifeStatusResult();
        List<ObjectDataDocument> objectDataDocumentList = Lists.newArrayList();
        User user = serviceContext.getUser();
        int offset = 0;
        int limit = ConfigCenter.batchCreateSize;
        int fetchSize;
        do {
            List<IObjectData> recoverObjectDatas = Lists.newArrayList();
            List<IObjectData> customerObjectDatas = crmManager.listPlainCustomersFromPg(user, arg.getCustomerIds(), offset, limit);
            Map<String, String> customerIdLifeStatusMap = customerObjectDatas.stream().collect(Collectors.toMap(o -> o.get("id", String.class), o -> o.get("life_status", String.class)));

            List<String> invalidCustomerIds = customerIdLifeStatusMap.entrySet().stream().filter(entry -> SystemConstants.LifeStatus.Invalid.value.equals(entry.getValue())).map(Map.Entry::getKey).collect(Collectors.toList());
            Map<String, String> customerLifeStatusBeforeInvalidMap = customerAccountManager.getCustomerLifeStatusBeforeInvalid(user, invalidCustomerIds);
            List<String> customerIds = Lists.newArrayList(customerIdLifeStatusMap.keySet());
            List<IObjectData> customerAccountObjectDatas = customerAccountManager.listCustomerAccountIncludeInvalidByCustomerIds(user, customerIds);
            for (IObjectData customerAccountObjectData : customerAccountObjectDatas) {
                String customerId = ObjectDataUtil.getReferenceId(customerAccountObjectData, CustomerAccountConstants.Field.Customer.apiName);
                String dbLifeStatus = customerAccountObjectData.get(SystemConstants.Field.LifeStatus.apiName, String.class);
                String fixLifeStatus = customerIdLifeStatusMap.get(customerId);
                if (!dbLifeStatus.equals(fixLifeStatus)) {
                    customerAccountObjectData.set(SystemConstants.Field.LifeStatus.apiName, fixLifeStatus);
                    if (SystemConstants.LifeStatus.Invalid.value.equals(fixLifeStatus)) {
                        customerAccountObjectData.set("life_status_before_invalid", customerLifeStatusBeforeInvalidMap.get(customerId));
                    }
                    log.info("customerAccountObjectData:{}", customerAccountObjectData.toJsonString());
                    IObjectData objectData = serviceFacade.updateObjectData(user, customerAccountObjectData, true);
                    if (SystemConstants.LifeStatus.Invalid.value.equals(fixLifeStatus) && !objectData.isDeleted()) {
                        objectData = serviceFacade.invalid(objectData, serviceContext.getUser());
                    }
                    if (customerAccountObjectData.isDeleted() && !fixLifeStatus.equals(SystemConstants.LifeStatus.Invalid.value)) {
                        recoverObjectDatas.add(objectData);
                    }
                    objectDataDocumentList.add(ObjectDataDocument.of(objectData));
                }
            }
            if (CollectionUtils.isNotEmpty(recoverObjectDatas)) {
                recoverObjectDatas = serviceFacade.bulkRecover(recoverObjectDatas, user);
                log.info("recoverObjectDatas:{}", JsonUtil.toJson(recoverObjectDatas));
            }
            fetchSize = customerObjectDatas.size();
            offset += limit;
        } while (fetchSize == limit);
        result.setCustomerAccountObjectDatas(objectDataDocumentList);
        return result;
    }

    @Override
    public EmptyResult initPrepayLayoutRecordType(ServiceContext serviceContext) {
        initService.initPrepayLayoutRecordType(serviceContext);
        return new EmptyResult();
    }

    @Override
    public EmptyResult initApproval(CurlModel.ObjectApiNameArg arg, ServiceContext serviceContext) {
        ApprovalInitModel.Result result = initService.initApproval(arg.getObjectApiName(), HeaderUtil.getApprovalHeader(serviceContext.getUser()));
        log.info("fix initApproval result:{}", result);
        return new EmptyResult();
    }

    //客户账户结算方式，信用额度；预存款收入支出类型；返利收入的收入类型，刷描述开放指定功能
    @Override
    public CurlModel.TenantIds updateSelectOneFieldDescribe(CurlModel.TenantIds tenantIdArg, ServiceContext serviceContext) {

        List<String> tenantIds = tenantIdArg.getTenantIds();
        if (tenantIds == null) {
            tenantIds = Lists.newArrayList();
        }
        if (!tenantIds.contains(serviceContext.getTenantId())) {
            tenantIds.add(serviceContext.getTenantId());
        }
        List<String> failTenantIds = Lists.newArrayList();
        for (String tenantId : tenantIds) {
            try {
                //客户账户
                IObjectDescribe customerAccountDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, CustomerAccountConstants.API_NAME);
                SelectManyFieldDescribe settleTypeSelectManyFieldDescribe = (SelectManyFieldDescribe) customerAccountDescribe.getFieldDescribe(CustomerAccountConstants.Field.SettleType.apiName);
                CurrencyFieldDescribe creditQuotaCurrencyFieldDescribe = (CurrencyFieldDescribe) customerAccountDescribe.getFieldDescribe(CustomerAccountConstants.Field.CreditQuota.apiName);
                Map<String, Object> settleTypeFieldConfig = FieldConfig.builder().attrs(Lists.newArrayList("help_text", "default_value"), 1).build();
                Map<String, Object> creditQuotaFieldConfig = FieldConfig.builder().attrs(Lists.newArrayList("help_text", "default_value", "decimal_places", "max_length"), 1).build();
                settleTypeSelectManyFieldDescribe.setConfig(settleTypeFieldConfig);
                creditQuotaCurrencyFieldDescribe.setConfig(creditQuotaFieldConfig);
                IObjectDescribe updatedCustomerAccountDescribe = objectDescribeService.updateFieldDescribe(customerAccountDescribe, Lists.newArrayList(settleTypeSelectManyFieldDescribe, creditQuotaCurrencyFieldDescribe));
                log.info("tenantId:{},updatedCustomerAccountDescribe:{}", tenantId, updatedCustomerAccountDescribe.toJsonString());
                //预存款
                IObjectDescribe objectDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, PrepayDetailConstants.API_NAME);
                SelectOneFieldDescribe prepayIncomeField = (SelectOneFieldDescribe) objectDescribe.getFieldDescribe(PrepayDetailConstants.Field.IncomeType.apiName);
                SelectOneFieldDescribe prepayOutcomeField = (SelectOneFieldDescribe) objectDescribe.getFieldDescribe(PrepayDetailConstants.Field.OutcomeType.apiName);

                List<ISelectOption> prepayIncomSelectOptions = prepayIncomeField.getSelectOptions();
                prepayIncomSelectOptions.forEach(selectOption -> {
                    if (PrepayIncomeTypeEnum.OnlineCharge.getValue().equals(selectOption.getValue())) {

                    } else if (PrepayIncomeTypeEnum.OrderRefund.getValue().equals(selectOption.getValue())) {

                    } else if (PrepayIncomeTypeEnum.contain(selectOption.getValue())) {
                        Map<String, Object> config = getOptionConfig();
                        selectOption.set("config", config);
                    }
                });
                prepayIncomeField.setSelectOptions(prepayIncomSelectOptions);
                prepayIncomeField.setConfig(getSelectOneFieldConfig());

                List<ISelectOption> prepayOutcomeSelectOptions = prepayOutcomeField.getSelectOptions();
                prepayOutcomeSelectOptions.forEach(selectOption -> {
                    if (PrepayOutcomeTypeEnum.OffsetOrder.getValue().equals(selectOption.getValue())) {

                    } else if (PrepayOutcomeTypeEnum.contain(selectOption.getValue())) {
                        Map<String, Object> config = getOptionConfig();
                        selectOption.set("config", config);
                    }
                });
                prepayOutcomeField.setSelectOptions(prepayOutcomeSelectOptions);
                prepayOutcomeField.setConfig(getSelectOneFieldConfig());
                IObjectDescribe objectDescribeResult = objectDescribeService.updateFieldDescribe(objectDescribe, Lists.newArrayList(prepayIncomeField, prepayOutcomeField));
                log.info("prepayDetailObj update option describe,tennatId:{}, result:{}", tenantId, objectDescribeResult.toJsonString());

                //返利
                IObjectDescribe rebateIncomeDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, RebateIncomeDetailConstants.API_NAME);
                SelectOneFieldDescribe rebateIncomeField = (SelectOneFieldDescribe) rebateIncomeDescribe.getFieldDescribe(RebateIncomeDetailConstants.Field.IncomeType.apiName);

                List<ISelectOption> rebateIncomeSelectOptions = rebateIncomeField.getSelectOptions();
                for (ISelectOption selectOption : rebateIncomeSelectOptions) {
                    if (Lists.newArrayList(RebateIncomeTypeEnum.OrderRefund.getValue(), RebateIncomeTypeEnum.OrderRebate.getValue()).contains(selectOption.getValue())) {

                    } else if (RebateIncomeTypeEnum.contain(selectOption.getValue())) {
                        Map<String, Object> config = getOptionConfig();
                        selectOption.set("config", config);
                    }
                }
                rebateIncomeField.setSelectOptions(rebateIncomeSelectOptions);
                rebateIncomeField.setConfig(getSelectOneFieldConfig());
                IObjectDescribe rebateIncomeObjectDescribeResult = objectDescribeService.updateFieldDescribe(rebateIncomeDescribe, Lists.newArrayList(rebateIncomeField));
                log.info("rebateIncomeDetailObj update option describe,tenantId:{}, result:{}", tenantId, rebateIncomeObjectDescribeResult.toJsonString());
            } catch (Exception e) {
                failTenantIds.add(tenantId);
                log.warn("", e);
            }
        }
        CurlModel.TenantIds result = new CurlModel.TenantIds();
        result.setTenantIds(failTenantIds);
        return result;
    }

    private Map<String, Object> getSelectOneFieldConfig() {
        Map<String, Object> config = Maps.newHashMap();
        Map<String, Object> attrMap = Maps.newHashMap();
        attrMap.put("options", 1);
        attrMap.put("default_value", 1);
        attrMap.put("is_required", 0);
        attrMap.put("is_readonly", 0);
        config.put("add", 1);
        config.put("attrs", attrMap);
        //        config.put("edit", 1);
        return config;
    }

    private Map<String, Object> getOptionConfig() {
        Map<String, Object> config = Maps.newHashMap();
        config.put("edit", 1);
        config.put("remove", 1);
        config.put("enable", 1);
        return config;
    }

    @Override
    public EmptyResult fixRebateOutcomeTargetListLabel(ServiceContext serviceContext, CurlModel.FixSelectOneFieldArg arg) {
        String tenantId = serviceContext.getTenantId();
        try {
            IObjectDescribe objectDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, arg.getObjectApiName());
            if (objectDescribe != null) {
                IFieldDescribe fieldDescribe = ObjectDescribeExt.of(objectDescribe).getFieldDescribe(arg.getFieldApiName());
                String type = fieldDescribe.getType();
                if ("object_reference".equals(type)) {
                    IObjectReferenceField objectReferenceField = (ObjectReferenceFieldDescribe) fieldDescribe;
                    String targetRelatedListLabel = getTargetRelatedListLabel(arg.getObjectApiName(), arg.getFieldApiName());
                    if (StringUtils.isNotEmpty(targetRelatedListLabel)) {
                        objectReferenceField.setTargetRelatedListLabel(targetRelatedListLabel);
                        objectDescribeService.updateFieldDescribe(objectDescribe, Lists.newArrayList(objectReferenceField));
                    }
                }
            }
        } catch (MetadataServiceException e) {
            log.warn("", e);
        }
        return new EmptyResult();
    }

    private String getTargetRelatedListLabel(String objectApiName, String fieldApiName) {
        if (RebateOutcomeDetailConstants.API_NAME.equals(objectApiName)) {
            for (RebateOutcomeDetailConstants.Field field : RebateOutcomeDetailConstants.Field.values()) {
                if (field.apiName.equals(fieldApiName)) {
                    return field.targetRelatedListLabel;
                }
            }
        } else if (RebateIncomeDetailConstants.API_NAME.equals(objectApiName)) {
            for (RebateIncomeDetailConstants.Field field : RebateIncomeDetailConstants.Field.values()) {
                if (field.apiName.equals(fieldApiName)) {
                    return field.targetRelatedListLabel;
                }
            }
        } else if (PrepayDetailConstants.API_NAME.equals(objectApiName)) {
            for (PrepayDetailConstants.Field field : PrepayDetailConstants.Field.values()) {
                if (field.apiName.equals(fieldApiName)) {
                    return field.targetRelatedListLabel;
                }
            }
        } else if (CustomerAccountConstants.API_NAME.equals(objectApiName)) {
            for (CustomerAccountConstants.Field field : CustomerAccountConstants.Field.values()) {
                if (field.apiName.equals(fieldApiName)) {
                    return field.targetRelatedListLabel;
                }
            }
        }
        return null;
    }

    @Override
    public CurlModel.FixSelectOneFieldResult fixSelectOneFieldDescribe(CurlModel.FixSelectOneFieldArg arg, ServiceContext serviceContext) {
        CurlModel.FixSelectOneFieldResult result = new CurlModel.FixSelectOneFieldResult();
        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            arg.setTenantIds(Lists.newArrayList(serviceContext.getTenantId()));
        } else if (!arg.getTenantIds().contains(serviceContext.getTenantId())) {
            arg.getTenantIds().add(serviceContext.getTenantId());
        }
        List<String> fixedTenantIds = Lists.newArrayList();
        for (String tenantId : arg.getTenantIds()) {
            try {
                String fieldApiName = arg.getFieldApiName();
                List<Map<String, Object>> options = getOptions(arg.getObjectApiName(), fieldApiName);
                IObjectDescribe objectDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, arg.getObjectApiName());
                SelectOneFieldDescribe selectOneFieldDescribe = (SelectOneFieldDescribe) objectDescribe.getFieldDescribe(arg.getFieldApiName());
                selectOneFieldDescribe.set("options", options);
                objectDescribe = objectDescribeService.updateFieldDescribe(objectDescribe, Lists.newArrayList(selectOneFieldDescribe));
                fixedTenantIds.add(tenantId);
                log.info("fixPrepayOutcomeType objectDescribe:{}", objectDescribe);
            } catch (MetadataServiceException e) {
                log.warn("fixPrepayOutcomeType,tenantId:{}", tenantId, e);
            }
        }
        result.setTenantIds(fixedTenantIds);
        return result;
    }

    private List<Map<String, Object>> getOptions(String objectApiName, String fieldApiName) {
        List<Map<String, Object>> options = null;
        if (PrepayDetailConstants.API_NAME.equals(objectApiName)) {
            if (PrepayDetailConstants.Field.IncomeType.apiName.equals(fieldApiName)) {
                options = Arrays.stream(PrepayIncomeTypeEnum.values()).map(x -> {
                    Map<String, Object> map = Maps.newHashMap();
                    map.put("label", x.getLabel());
                    map.put("value", x.getValue());
                    map.put("not_usable", x.getNotUsable());
                    return map;
                }).collect(Collectors.toList());
            } else if (PrepayDetailConstants.Field.OutcomeType.apiName.equals(fieldApiName)) {
                options = Arrays.stream(PrepayOutcomeTypeEnum.values()).map(x -> {
                    Map<String, Object> map = Maps.newHashMap();
                    map.put("label", x.getLabel());
                    map.put("value", x.getValue());
                    map.put("not_usable", x.getNotUsable());
                    return map;
                }).collect(Collectors.toList());
            } else {
                throw new ValidateException(String.format("{%s}不存在", fieldApiName));
            }
        } else if (RebateIncomeDetailConstants.API_NAME.equals(objectApiName)) {
            if (RebateIncomeDetailConstants.Field.IncomeType.apiName.equals(fieldApiName)) {
                options = Arrays.stream(RebateIncomeTypeEnum.values()).map(x -> {
                    Map<String, Object> map = Maps.newHashMap();
                    map.put("label", x.getLabel());
                    map.put("value", x.getValue());
                    map.put("not_usable", x.getNotUsable());
                    return map;
                }).collect(Collectors.toList());
            } else {
                throw new ValidateException(String.format("{%s}不存在", fieldApiName));
            }
        }
        return options;
    }

    @Override
    public EmptyResult updateLayout(CurlModel.UpdateLayoutArg arg, ServiceContext serviceContext) {
        try {
            ActionContextExt actionContextext = ActionContextExt.of(serviceContext.getUser());
            IActionContext actionContext = actionContextext.getContext();

            ILayout layout = layoutService.findByNameAndObjectDescribeApiNameAndTenantId(arg.getLayoutApiName(), arg.getObjectApiName(), serviceContext.getTenantId(), actionContext);
            layout.setIsShowFieldname(true);
            layout.setAgentType("agent_type_mobile");
            layout = layoutService.update(layout);
            log.warn("updateLayout user:{},layout:{}", serviceContext.getUser(), layout);
            return new EmptyResult();
        } catch (MetadataServiceException e) {
            log.warn("", e);
            throw new ValidateException("updatelayout error," + e.getMessage());
        }
    }

    @Override
    public CurlModel.AddOrderPaymentFieldResult addOrderPaymentField(CurlModel.AddOrderPaymentFieldArg arg, ServiceContext serviceContext) {
        CurlModel.AddOrderPaymentFieldResult result = new CurlModel.AddOrderPaymentFieldResult();

        try {

            //预存款增加orderPayment 字段<br>
            IObjectDescribe prepayDetailDescribe = serviceFacade.findObject(arg.getTenantId(), PrepayDetailConstants.API_NAME);
            ObjectReferenceFieldDescribe customerObjectReferenceFieldDescribe = ObjectReferenceFieldDescribeBuilder.builder().apiName(PrepayDetailConstants.Field.OrderPayment.apiName).label(PrepayDetailConstants.Field.OrderPayment.label).required(false).targetApiName(SystemConstants.OrderPaymentApiname).targetRelatedListName(PrepayDetailConstants.Field.OrderPayment.targetRelatedListName).targetRelatedListLabel(PrepayDetailConstants.Field.OrderPayment.targetRelatedListLabel).build();
            objectDescribeService.addCustomFieldDescribe(prepayDetailDescribe, Lists.newArrayList(customerObjectReferenceFieldDescribe));

            //返利增加orderPayment 字段
            IObjectDescribe rebateOutcomeDetailDescribe = serviceFacade.findObject(arg.getTenantId(), RebateOutcomeDetailConstants.API_NAME);
            ObjectReferenceFieldDescribe orderPaymentReferenceField = ObjectReferenceFieldDescribeBuilder.builder().apiName(RebateOutcomeDetailConstants.Field.OrderPayment.apiName).label(RebateOutcomeDetailConstants.Field.OrderPayment.label).required(false).targetApiName(SystemConstants.OrderPaymentApiname).targetRelatedListName(RebateOutcomeDetailConstants.Field.OrderPayment.targetRelatedListName).targetRelatedListLabel(RebateOutcomeDetailConstants.Field.OrderPayment.targetRelatedListLabel).build();
            objectDescribeService.addCustomFieldDescribe(rebateOutcomeDetailDescribe, Lists.newArrayList(orderPaymentReferenceField));

            User user = serviceContext.getUser();
            //            deletePaymentFieldOfPrepayDetail(user);
            //            deletePaymentFieldOfRebateOutcome(user);
            updateOrderPaymentLayout(user);
        } catch (MetadataServiceException e) {
            log.error("addOrderPaymentField->for tenantId:{},exception:{}", e);
        }
        return result;
    }

    @Override
    public CurlModel.DelPaymentFieldResult delPaymentField(ServiceContext serviceContext) {
        deletePaymentFieldOfPrepayDetail(serviceContext.getUser());
        deletePaymentFieldOfRebateOutcome(serviceContext.getUser());
        CurlModel.DelPaymentFieldResult result = new CurlModel.DelPaymentFieldResult();
        result.setSuccess(true);
        return result;
    }

    @Override
    public CurlModel.AddOrderPaymentFieldResult addPaymentField(CurlModel.AddOrderPaymentFieldArg arg, ServiceContext serviceContext) {
        CurlModel.AddOrderPaymentFieldResult result = new CurlModel.AddOrderPaymentFieldResult();

        try {

            //预存款增加orderPayment 字段<br>
            IObjectDescribe prepayDetailDescribe = serviceFacade.findObject(arg.getTenantId(), PrepayDetailConstants.API_NAME);

            ObjectReferenceFieldDescribe paymentFieldDescribe = ObjectReferenceFieldDescribeBuilder.builder().apiName(PrepayDetailConstants.Field.Payment.apiName).label(PrepayDetailConstants.Field.Payment.label).required(false).targetApiName(SystemConstants.PaymentApiName).targetRelatedListName(PrepayDetailConstants.Field.Payment.targetRelatedListName).targetRelatedListLabel(PrepayDetailConstants.Field.Payment.targetRelatedListLabel).build();
            objectDescribeService.addCustomFieldDescribe(prepayDetailDescribe, Lists.newArrayList(paymentFieldDescribe));

            //返利增加orderPayment 字段
            IObjectDescribe rebateOutcomeDetailDescribe = serviceFacade.findObject(arg.getTenantId(), RebateOutcomeDetailConstants.API_NAME);

            //FIXME 临时 可以删除掉
            ObjectReferenceFieldDescribe rebatePaymentFieldDescribe = ObjectReferenceFieldDescribeBuilder.builder().apiName(RebateOutcomeDetailConstants.Field.Payment.apiName).label(RebateOutcomeDetailConstants.Field.Payment.label).required(false).targetApiName(SystemConstants.PaymentApiName).targetRelatedListName(RebateOutcomeDetailConstants.Field.Payment.targetRelatedListName).targetRelatedListLabel(RebateOutcomeDetailConstants.Field.Payment.targetRelatedListLabel).build();
            objectDescribeService.addCustomFieldDescribe(rebateOutcomeDetailDescribe, Lists.newArrayList(rebatePaymentFieldDescribe));
        } catch (MetadataServiceException e) {
            log.error("addOrderPaymentField->for tenantId:{},exception:{}", e);
        }
        return result;
    }

    private void updateOrderPaymentLayout(User user) {
        IObjectDescribe rebateOutcomeDescribe = serviceFacade.findObject(user.getTenantId(), RebateOutcomeDetailConstants.API_NAME);

        IObjectDescribe prepayDetailDescribe = serviceFacade.findObject(user.getTenantId(), PrepayDetailConstants.API_NAME);

        ILayout prepayOutcomeLayout = serviceFacade.findLayoutByApiName(user, PrepayDetailConstants.OUTCOME_LAYOUT_API_NAME, prepayDetailDescribe.getApiName());
        prepayOutcomeLayout = InitUtil.updatePrepayDetailLayoutForOrderPaymentReplace(user, prepayOutcomeLayout);
        serviceFacade.updateLayout(user, prepayOutcomeLayout);

        ILayout prepayDefaultLayout = serviceFacade.findLayoutByApiName(user, PrepayDetailConstants.DEFAULT_LAYOUT_API_NAME, prepayDetailDescribe.getApiName());
        prepayDefaultLayout = InitUtil.updatePrepayDetailLayoutForOrderPaymentReplace(user, prepayDefaultLayout);
        serviceFacade.updateLayout(user, prepayDefaultLayout);

        ILayout rebateOutcomeDefaultLayout = serviceFacade.findLayoutByApiName(user, RebateOutcomeDetailConstants.DEFAULT_LAYOUT_API_NAME, rebateOutcomeDescribe.getApiName());
        rebateOutcomeDefaultLayout = InitUtil.updateRebateOutcomeLayoutForOrderPaymentReplace(user, rebateOutcomeDefaultLayout);
        serviceFacade.updateLayout(user, rebateOutcomeDefaultLayout);
    }

    //删除payment字段
    private void deletePaymentFieldOfPrepayDetail(User user) {
        IObjectDescribe prepayDetailDescribe = serviceFacade.findObject(user.getTenantId(), PrepayDetailConstants.API_NAME);
        try {
            IFieldDescribe dbPaymentFieldDescribe = prepayDetailDescribe.getFieldDescribe(PrepayDetailConstants.Field.Payment.apiName);
            if (dbPaymentFieldDescribe != null) {
                ObjectReferenceFieldDescribe paymentFieldDescribe = ObjectReferenceFieldDescribeBuilder.builder().apiName(PrepayDetailConstants.Field.Payment.apiName).label(PrepayDetailConstants.Field.Payment.label).required(false).targetApiName(SystemConstants.PaymentApiName).targetRelatedListName(PrepayDetailConstants.Field.Payment.targetRelatedListName).targetRelatedListLabel(PrepayDetailConstants.Field.Payment.targetRelatedListLabel).build();
                List<IFieldDescribe> describeListTobeDeleted = new ArrayList<>();
                describeListTobeDeleted.add(paymentFieldDescribe);
                objectDescribeService.deleteCustomFieldDescribe(prepayDetailDescribe, describeListTobeDeleted);
            }
        } catch (MetadataServiceException e) {
            log.warn("deletePaymentFieldOfPrepayDetail user:{}", user, e);
            throw new CustomerAccountBusinessException(CustomerAccountErrorCode.PREPAY_TRANSFER_ERROR, e.getErrorCode().getMessage());
        }
    }

    //删除payment字段
    private void deletePaymentFieldOfRebateOutcome(User user) {
        IObjectDescribe rebateOutcomeDetailDescribe = serviceFacade.findObject(user.getTenantId(), RebateOutcomeDetailConstants.API_NAME);
        try {
            IFieldDescribe dbPaymentFieldDescribe = rebateOutcomeDetailDescribe.getFieldDescribe(RebateOutcomeDetailConstants.Field.Payment.apiName);
            if (dbPaymentFieldDescribe != null) {
                ObjectReferenceFieldDescribe paymentFieldDescribe = ObjectReferenceFieldDescribeBuilder.builder().apiName(RebateOutcomeDetailConstants.Field.Payment.apiName).label(RebateOutcomeDetailConstants.Field.Payment.label).required(false).targetApiName(SystemConstants.PaymentApiName).targetRelatedListName(RebateOutcomeDetailConstants.Field.Payment.targetRelatedListName).targetRelatedListLabel(RebateOutcomeDetailConstants.Field.Payment.targetRelatedListLabel).build();
                List<IFieldDescribe> describeListTobeDeleted = new ArrayList<>();
                describeListTobeDeleted.add(paymentFieldDescribe);
                objectDescribeService.deleteCustomFieldDescribe(rebateOutcomeDetailDescribe, describeListTobeDeleted);
            }
        } catch (MetadataServiceException e) {
            log.warn("deletePaymentFieldOfRebateOutcome user:{}", user, e);
            throw new CustomerAccountBusinessException(CustomerAccountErrorCode.REBATE_OUTCOME_TRANSFER_ERROR, e.getErrorCode().getMessage());
        }
    }

    @Override
    public CurlModel.AddImportPrivilegeResult addImportFunctionPrivilegeToRole(CurlModel.AddImportPrivilegeArg arg1) {
        String[] tenantIdArray = arg1.getTenantIds().split(",");
        for (String tenantId : tenantIdArray) {
            log.debug("begin addImportPrivilege for tenantId:{}", tenantId);
            ServiceContext ctx = generateServiceContext(tenantId);
            //查询是否有导入权限,User user, String objectApiName, String actionCode<br>
            List<String> privileges = functionPrivilegeService.getHavePrivilegeRolesByActionCode(ctx.getUser(), CustomerAccountConstants.API_NAME, "Import");

            if (CollectionUtils.isEmpty(privileges)) {
                log.debug("没有初始化相关权限，现在添加 batchImport权限");
                addImportFunctionPrivilegeByObjctApinName(ctx.getUser(), CustomerAccountConstants.API_NAME);
                addImportFunctionPrivilegeByObjctApinName(ctx.getUser(), PrepayDetailConstants.API_NAME);
                addImportFunctionPrivilegeByObjctApinName(ctx.getUser(), RebateIncomeDetailConstants.API_NAME);
            }
        }
        CurlModel.AddImportPrivilegeResult result = new CurlModel.AddImportPrivilegeResult();
        result.setSuccess(true);
        return result;
    }

    @Override
    public CurlModel.AddImportPrivilegeResult delImportFunctionPrivilegeToRole(CurlModel.AddImportPrivilegeArg arg1) {
        String[] tenantIdArray = arg1.getTenantIds().split(",");
        for (String tenantId : tenantIdArray) {
            log.debug("begin addImportPrivilege for tenantId:{}", tenantId);
            ServiceContext ctx = generateServiceContext(tenantId);
            //查询是否有导入权限,User user, String objectApiName, String actionCode<br>
            List<String> privileges = functionPrivilegeService.getHavePrivilegeRolesByActionCode(ctx.getUser(), CustomerAccountConstants.API_NAME, "Import");

            if (CollectionUtils.isNotEmpty(privileges)) {
                log.debug("有初始化相关权限，现在删除batchImport权限");
                delImportFunctionPrivilegeByObjctApinName(ctx.getUser(), CustomerAccountConstants.API_NAME);
                delImportFunctionPrivilegeByObjctApinName(ctx.getUser(), PrepayDetailConstants.API_NAME);
                delImportFunctionPrivilegeByObjctApinName(ctx.getUser(), RebateIncomeDetailConstants.API_NAME);
            }
        }
        CurlModel.AddImportPrivilegeResult result = new CurlModel.AddImportPrivilegeResult();
        result.setSuccess(true);
        return result;
    }

    /**
     * 纯粹的删除 权限元数据<br>
     */
    @Override
    public CurlModel.AddImportPrivilegeResult delImportFunctionPrivilege(CurlModel.DelImportPrivilegeArg arg) {
        String[] tenantIdArray = arg.getTenantIds().split(",");
        for (String tenantId : tenantIdArray) {
            log.debug("begin addImportPrivilege for tenantId:{}", tenantId);
            ServiceContext ctx = generateServiceContext(tenantId);
            //查询是否有导入权限,User user, String objectApiName, String actionCode<br>
            functionPrivilegeService.deleteUserDefinedActionCode(ctx.getUser(), CustomerAccountConstants.API_NAME, "Import");
            functionPrivilegeService.deleteUserDefinedActionCode(ctx.getUser(), PrepayDetailConstants.API_NAME, "Import");
            functionPrivilegeService.deleteUserDefinedActionCode(ctx.getUser(), RebateIncomeDetailConstants.API_NAME, "Import");
        }

        CurlModel.AddImportPrivilegeResult result = new CurlModel.AddImportPrivilegeResult();
        result.setSuccess(true);
        return result;
    }

    private AuthContext buildAuthContext(User user) {
        return AuthContext.builder().appId("CRM").tenantId(user.getTenantId()).userId(user.getUserId()).build();
    }

    private void addImportFunctionPrivilegeByObjctApinName(User user, String objectApiName) {
        AuthContext authContext = buildAuthContext(user);
        FunctionPrivilegeProvider provider = this.providerManager.getProvider(objectApiName);

        //添加权限
        List<CreateFunctionPrivilege.FunctionPojo> functionPojos = getUserDefinedFunctionPojoList(authContext.getTenantId(), objectApiName);
        //这个是添加纯粹的权限
        if (!org.apache.commons.collections4.CollectionUtils.isEmpty(functionPojos)) {
            CreateFunctionPrivilege.Arg arg = CreateFunctionPrivilege.Arg.builder().authContext(authContext).functionPojoList(functionPojos).build();
            this.functionPrivilegeProxy.createFunctionPrivilege(arg, FunctionPrivilegeProxy.HeaderUtil.buildHeaders(user.getTenantId()));
        }

        //给角色添加权限<br>
        //回款财务
        String paymentFinacailRole = "00000000000000000000000000000002";
        //销售人员
        String salesRole = "00000000000000000000000000000015";
        addRoleFunctionPrivilege(authContext, "00000000000000000000000000000006", objectApiName);
        addRoleFunctionPrivilege(authContext, paymentFinacailRole, objectApiName);
        addRoleFunctionPrivilege(authContext, salesRole, objectApiName);
    }

    private void delImportFunctionPrivilegeByObjctApinName(User user, String objectApiName) {
        AuthContext authContext = buildAuthContext(user);
        FunctionPrivilegeProvider provider = this.providerManager.getProvider(objectApiName);

        //添加权限
        List<CreateFunctionPrivilege.FunctionPojo> functionPojos = getUserDefinedFunctionPojoList(authContext.getTenantId(), objectApiName);
        //这个是添加纯粹的权限
        if (!org.apache.commons.collections4.CollectionUtils.isEmpty(functionPojos)) {
            CreateFunctionPrivilege.Arg arg = CreateFunctionPrivilege.Arg.builder().authContext(authContext).functionPojoList(functionPojos).build();
            this.functionPrivilegeProxy.createFunctionPrivilege(arg, FunctionPrivilegeProxy.HeaderUtil.buildHeaders(user.getTenantId()));
        }

        //给角色添加权限<br>
        //回款财务
        String paymentFinacailRole = "00000000000000000000000000000002";
        //销售人员
        String salesRole = "00000000000000000000000000000015";
        delRoleFunctionPrivilege(authContext, "00000000000000000000000000000006", objectApiName);
        delRoleFunctionPrivilege(authContext, paymentFinacailRole, objectApiName);
        delRoleFunctionPrivilege(authContext, salesRole, objectApiName);
    }

    private void addRoleFunctionPrivilege(AuthContext authContext, String roleCode, String objectApiName) {
        String funcCode = FunctionCodeBuilder.build(objectApiName, "Import");
        List<String> funcCodeList = new ArrayList<>();
        funcCodeList.add(funcCode);
        this.addRoleFunctionPrivilege(authContext, roleCode, funcCodeList);
    }

    private void delRoleFunctionPrivilege(AuthContext authContext, String roleCode, String objectApiName) {
        String funcCode = FunctionCodeBuilder.build(objectApiName, "Import");
        List<String> funcCodeList = new ArrayList<>();
        funcCodeList.add(funcCode);
        this.delRoleFunctionPrivilege(authContext, roleCode, funcCodeList);
    }

    private void addRoleFunctionPrivilege(AuthContext authContext, String roleCode, List<String> addFuncCodes) {
        if (!CollectionUtils.isEmpty(addFuncCodes)) {
            UpdateRoleModifiedFuncPrivilege.Arg arg = UpdateRoleModifiedFuncPrivilege.Arg.builder().authContext(authContext).roleCode(roleCode).addFuncCode(addFuncCodes).build();
            UpdateRoleModifiedFuncPrivilege.Result result = this.functionPrivilegeProxy.updateRoleModifiedFuncPrivilege(arg, FunctionPrivilegeProxy.HeaderUtil.buildHeaders(authContext.getTenantId()));
            if (!result.isSuccess()) {
                log.error("addFunctionPrivilege error,arg:{},result:{}", arg, result);
            }
        }
    }

    private void delRoleFunctionPrivilege(AuthContext authContext, String roleCode, List<String> delFuncCodes) {
        if (!CollectionUtils.isEmpty(delFuncCodes)) {
            UpdateRoleModifiedFuncPrivilege.Arg arg = UpdateRoleModifiedFuncPrivilege.Arg.builder().authContext(authContext).roleCode(roleCode).delFuncCode(delFuncCodes).build();
            UpdateRoleModifiedFuncPrivilege.Result result = this.functionPrivilegeProxy.updateRoleModifiedFuncPrivilege(arg, FunctionPrivilegeProxy.HeaderUtil.buildHeaders(authContext.getTenantId()));
            if (!result.isSuccess()) {
                log.error("addFunctionPrivilege error,arg:{},result:{}", arg, result);
            }
        }
    }

    private List<CreateFunctionPrivilege.FunctionPojo> getUserDefinedFunctionPojoList(String tenantId, String apiName) {
        ArrayList userDefinedFunctionPojoList = Lists.newArrayList();
        String actionCode = ObjectAction.BATCH_IMPORT.getActionCode();
        CreateFunctionPrivilege.FunctionPojo pojo = new CreateFunctionPrivilege.FunctionPojo();
        pojo.setAppId("CRM");
        pojo.setTenantId(tenantId);
        pojo.setFuncType(Integer.valueOf(1));
        pojo.setParentCode("00000000000000000000000000000000");
        pojo.setFuncName(ObjectAction.of(actionCode).getActionLabel());
        pojo.setFuncCode(FunctionCodeBuilder.build(apiName, actionCode));
        userDefinedFunctionPojoList.add(pojo);

        return userDefinedFunctionPojoList;
    }

    private ServiceContext generateServiceContext(String tenantId) {
        RequestContext requestContext = RequestContext.builder().tenantId(tenantId).user(new User(tenantId, User.SUPPER_ADMIN_USER_ID)).build();
        return new ServiceContext(requestContext, null, null);
    }

    @Override
    public CurlModel.ListLayoutResult fixRebateIncomeListLayout(CurlModel.TenantIds tenantIdArg, ServiceContext serviceContext) {
        List<String> tenantIds = tenantIdArg.getTenantIds();
        CurlModel.ListLayoutResult listLayoutResult = new CurlModel.ListLayoutResult();
        try {
            if (CollectionUtils.isEmpty(tenantIds)) {
                return listLayoutResult;
            }
            ActionContextExt actionContextext = ActionContextExt.of(serviceContext.getUser());
            IActionContext actionContext = actionContextext.getContext();

            for (String tenantId : tenantIds) {
                ILayout layout = layoutService.findByNameAndObjectDescribeApiNameAndTenantId(RebateIncomeDetailConstants.LIST_LAYOUT_API_NAME, RebateIncomeDetailConstants.API_NAME, tenantId, actionContext);
                if (CollectionUtils.isNotEmpty(layout.getComponents())) {
                    continue;
                }
                List<IComponent> components = Lists.newArrayList();
                List<ITableColumn> tableColumns = Lists.newArrayList();
                tableColumns.add(TableColumnBuilder.builder().name(RebateIncomeDetailConstants.Field.Customer.apiName).lableName(RebateIncomeDetailConstants.Field.Customer.label).renderType(SystemConstants.RenderType.ObjectReference.renderType).build());
                tableColumns.add(TableColumnBuilder.builder().name(RebateIncomeDetailConstants.Field.Amount.apiName).lableName(RebateIncomeDetailConstants.Field.Amount.label).renderType(SystemConstants.RenderType.Currency.renderType).build());
                tableColumns.add(TableColumnBuilder.builder().name(SystemConstants.Field.LifeStatus.apiName).lableName(SystemConstants.Field.LifeStatus.label).renderType(SystemConstants.RenderType.SelectOne.renderType).build());
                tableColumns.add(TableColumnBuilder.builder().name(RebateIncomeDetailConstants.Field.TransactionTime.apiName).lableName(RebateIncomeDetailConstants.Field.TransactionTime.label).renderType(SystemConstants.RenderType.DateTime.renderType).build());

                TableComponent tableComponent = TableComponentBuilder.builder().refObjectApiName(RebateIncomeDetailConstants.API_NAME).includeFields(tableColumns).buttons(null).build();
                components.add(tableComponent);
                layout.setComponents(components);
                layout = layoutService.update(layout);
                listLayoutResult.add(layout);
                log.info("updateLayout user:{},layout:{}", serviceContext.getUser(), layout);
            }
            return listLayoutResult;
        } catch (MetadataServiceException e) {
            log.warn("", e);
            throw new ValidateException("updatelayout error," + e.getMessage());
        }
    }

    @Override
    public EmptyResult fixRebateIncomeStartEndTimeLabelAndTransactionTime(CurlModel.TenantIds tenantIdArg, ServiceContext serviceContext) {
        List<String> tenantIds = tenantIdArg.getTenantIds();
        try {
            ActionContextExt actionContextext = ActionContextExt.of(serviceContext.getUser());
            IActionContext actionContext = actionContextext.getContext();

            for (String tenantId : tenantIds) {
                IObjectDescribe objectDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, RebateIncomeDetailConstants.API_NAME);
                List<IFieldDescribe> fieldDescribeListToUpdate = Lists.newArrayList();
                IFieldDescribe startTimeFieldDescribe = objectDescribe.getFieldDescribe(RebateIncomeDetailConstants.Field.StartTime.apiName);
                IFieldDescribe endTimeFieldDescribe = objectDescribe.getFieldDescribe(RebateIncomeDetailConstants.Field.EndTime.apiName);
                if (!RebateIncomeDetailConstants.Field.StartTime.label.equals(startTimeFieldDescribe.getLabel())) {
                    startTimeFieldDescribe.setLabel(RebateIncomeDetailConstants.Field.StartTime.label);
                    fieldDescribeListToUpdate.add(startTimeFieldDescribe);
                }
                if (!RebateIncomeDetailConstants.Field.EndTime.label.equals(endTimeFieldDescribe.getLabel())) {
                    endTimeFieldDescribe.setLabel(RebateIncomeDetailConstants.Field.EndTime.label);
                    fieldDescribeListToUpdate.add(endTimeFieldDescribe);
                }
                if (CollectionUtils.isNotEmpty(fieldDescribeListToUpdate)) {
                    IObjectDescribe updateObjectDescribe = objectDescribeService.updateFieldDescribe(objectDescribe, fieldDescribeListToUpdate);
                    log.info("Updated ObjectDescribe :{}", updateObjectDescribe.toJsonString());
                }

                ILayout layout = layoutService.findByNameAndObjectDescribeApiNameAndTenantId(RebateIncomeDetailConstants.DEFAULT_LAYOUT_API_NAME, RebateIncomeDetailConstants.API_NAME, tenantId, actionContext);
                if (Objects.nonNull(layout)) {
                    List<IComponent> components = Lists.newArrayList();
                    List<IFormField> formFields = Lists.newArrayList();
                    formFields.add(FormFieldBuilder.builder().fieldName(RebateIncomeDetailConstants.Field.Name.apiName).renderType(SystemConstants.RenderType.AutoNumber.renderType).required(true).readOnly(true).build());
                    formFields.add(FormFieldBuilder.builder().fieldName(RebateIncomeDetailConstants.Field.Customer.apiName).renderType(SystemConstants.RenderType.ObjectReference.renderType).required(true).readOnly(false).build());
                    formFields.add(FormFieldBuilder.builder().fieldName(RebateIncomeDetailConstants.Field.IncomeType.apiName).renderType(SystemConstants.RenderType.SelectOne.renderType).required(true).readOnly(false).build());
                    formFields.add(FormFieldBuilder.builder().fieldName(RebateIncomeDetailConstants.Field.Amount.apiName).renderType(SystemConstants.RenderType.Currency.renderType).required(true).readOnly(false).build());
                    formFields.add(FormFieldBuilder.builder().fieldName(RebateIncomeDetailConstants.Field.AvailableRebate.apiName).renderType(SystemConstants.RenderType.Currency.renderType).required(false).readOnly(true).build());
                    formFields.add(FormFieldBuilder.builder().fieldName(RebateIncomeDetailConstants.Field.UsedRebate.apiName).renderType(SystemConstants.RenderType.Currency.renderType).required(false).readOnly(true).build());
                    formFields.add(FormFieldBuilder.builder().fieldName(RebateIncomeDetailConstants.Field.StartTime.apiName).renderType(SystemConstants.RenderType.Date.renderType).required(true).readOnly(false).build());
                    formFields.add(FormFieldBuilder.builder().fieldName(RebateIncomeDetailConstants.Field.EndTime.apiName).renderType(SystemConstants.RenderType.Date.renderType).required(true).readOnly(false).build());
                    formFields.add(FormFieldBuilder.builder().fieldName(RebateIncomeDetailConstants.Field.TransactionTime.apiName).renderType(SystemConstants.RenderType.DateTime.renderType).required(true).readOnly(false).build());
                    formFields.add(FormFieldBuilder.builder().fieldName(RebateIncomeDetailConstants.Field.Refund.apiName).renderType(SystemConstants.RenderType.ObjectReference.renderType).required(false).readOnly(true).build());
                    formFields.add(FormFieldBuilder.builder().fieldName(RebateIncomeDetailConstants.Field.Remark.apiName).renderType(SystemConstants.RenderType.LongText.renderType).required(false).readOnly(false).build());
                    formFields.add(FormFieldBuilder.builder().fieldName(RebateIncomeDetailConstants.Field.Attach.apiName).renderType(SystemConstants.RenderType.FileAttachment.renderType).required(false).readOnly(false).build());
                    formFields.add(FormFieldBuilder.builder().fieldName(SystemConstants.Field.LifeStatus.apiName).renderType(SystemConstants.RenderType.SelectOne.renderType).required(true).readOnly(false).build());
                    formFields.add(FormFieldBuilder.builder().fieldName(SystemConstants.Field.RecordType.apiName).renderType(SystemConstants.RenderType.RecordType.renderType).required(true).readOnly(false).build());
                    formFields.add(FormFieldBuilder.builder().fieldName(SystemConstants.Field.Owner.apiName).renderType(SystemConstants.RenderType.Employee.renderType).required(true).readOnly(false).build());
                    FieldSection baseFieldSection = FieldSectionBuilder.builder().name(LayoutConstants.BASE_FIELD_SECTION_API_NAME).header(LayoutConstants.BASE_FIELD_SECTION_DISPLAY_NAME).showHeader(true).fields(formFields).build();
                    FieldSection systemFieldSection = InitUtil.getSystemFieldSection();
                    List<IFieldSection> fieldSections = Lists.newArrayList();
                    fieldSections.add(baseFieldSection);
                    fieldSections.add(systemFieldSection);

                    FormComponent formComponent = FormComponentBuilder.builder().name(LayoutConstants.FORM_COMPONENT_API_NAME).fieldSections(fieldSections).buttons(null).build();
                    components.add(formComponent);
                    layout.setComponents(components);
                    layout = layoutService.update(layout);
                    log.info("updated layout:{}", layout.toJsonString());
                }
            }
        } catch (MetadataServiceException e) {
            log.warn("", e);
        }
        return new EmptyResult();
    }

    @Override
    public EmptyResult fixCustomerAccountRelateBalance(CurlModel.FixCustomerAccountBalanceArg arg, ServiceContext serviceContext) {
        ObjectDataDocument objectDataDocument = arg.getObjectDataDocument();
        IObjectData objectData = new ObjectData(objectDataDocument);
        String apiName = objectData.getDescribeApiName();
        String id = objectData.getId();
        if (StringUtils.isEmpty(apiName) || StringUtils.isEmpty(id)) {
            return new EmptyResult();
        }
        objectData = serviceFacade.updateObjectData(serviceContext.getUser(), objectData, true);
        log.info("updated objectData:{}", objectData.toJsonString());
        return new EmptyResult();
    }

    @Override
    public CurlModel.FixSelectOneFieldResult findDescribeByApiName(CurlModel.FixSelectOneFieldArg arg, ServiceContext serviceContext) {
        IObjectDescribe objectDescribe = serviceFacade.findObject(serviceContext.getTenantId(), arg.getObjectApiName());
        CurlModel.FixSelectOneFieldResult fixSelectOneFieldResult = new CurlModel.FixSelectOneFieldResult();
        fixSelectOneFieldResult.setObjectDescribe(ObjectDescribeDocument.of(objectDescribe));
        return fixSelectOneFieldResult;
    }

    @Override
    public TenantIdModel.Result initRebateUseRule(TenantIdModel.Arg arg, ServiceContext serviceContext) {
        TenantIdModel.Result result = new TenantIdModel.Result();
        List<String> tenantIdsList = Lists.newArrayList();
        for (String tenantId : arg.getTenantIds()) {
            CustomerAccountType.CustomerAccountEnableSwitchStatus customerAccountEnableSwitchStatus = customerAccountConfigManager.getStatus(tenantId);
            if (customerAccountEnableSwitchStatus != CustomerAccountType.CustomerAccountEnableSwitchStatus.ENABLE) {
                continue;
            }
            User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
            Set<String> apiNames = Sets.newHashSet(RebateUseRuleConstants.API_NAME);
            Map<String, IObjectDescribe> describeMap = serviceFacade.findObjects(user.getTenantId(), apiNames);
            if (!describeMap.containsKey(RebateUseRuleConstants.API_NAME)) {
                initService.initRebateUseRule(user);
                tenantIdsList.add(tenantId);
            } else {
                log.info("already init tenantId:{}", tenantId);
            }
        }
        result.setTenantIds(tenantIdsList);
        return result;
    }

    @Override
    public TenantIdModel.Result addSelectOptionInRebateIncomeType(TenantIdModel.Arg arg, ServiceContext serviceContext) {
        TenantIdModel.Result result = new TenantIdModel.Result();
        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return result;
        }
        List<String> failedTenantIds = Lists.newArrayList();
        for (String tenantId : arg.getTenantIds()) {
            User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
            try {
                addSelectOptionInRebateIncomeType(user);
            } catch (Exception e) {
                log.warn("addSelectOptionInRebateIncomeType", e);
                failedTenantIds.add(tenantId);
            }
        }
        result.setTenantIds(failedTenantIds);
        return result;
    }

    @Override
    public TenantIdModel.Result addRebateUseRuleFieldInRebateOutcome(TenantIdModel.Arg arg, ServiceContext serviceContext) {
        TenantIdModel.Result result = new TenantIdModel.Result();
        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return result;
        }
        List<String> failedTenantIds = Lists.newArrayList();
        for (String tenantId : arg.getTenantIds()) {
            User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
            try {
                addRebateUseRuleFieldInRebateOutcome(user);
            } catch (Exception e) {
                log.warn("addSelectOptionInRebateIncomeType", e);
                failedTenantIds.add(tenantId);
            }
        }
        result.setTenantIds(failedTenantIds);
        return result;
    }

    @Override
    public Map<String, Object> queryFrozenRecordWithoutUseRecord(ServiceContext serviceContext, CurlModel.FrozenRecordWithoutRuleUseRecord arg) {
        Map<String, Object> resultMap = Maps.newHashMap();
        String tenantId = serviceContext.getTenantId();
        User user = User.systemUser(tenantId);
        List<String> frozenIds = arg.getFrozenIds();
        if (CollectionUtils.isEmpty(frozenIds)) {
            return resultMap;
        }
        List<IObjectData> frozenDataList = serviceFacade.findObjectDataByIds(tenantId, frozenIds, AccountFrozenRecordConstant.API_NAME);

        for (IObjectData frozenData : frozenDataList) {
            try {
                List<IFilter> filters = Lists.newArrayList();
                String ruleId = frozenData.get(AccountFrozenRecordConstant.Field.AccountCheckRuleId.apiName, String.class);
                String checkRecordObjectApiName = frozenData.get(AccountFrozenRecordConstant.Field.CheckRecordObjectApiName.apiName, String.class);
                String checkRecordObjectDataId = frozenData.get(AccountFrozenRecordConstant.Field.CheckRecordObjectDataId.apiName, String.class);
                SearchUtil.fillFilterEq(filters, AccountRuleUseRecordConstants.Field.CheckRuleId.apiName, ruleId);
                SearchUtil.fillFilterEq(filters, AccountRuleUseRecordConstants.Field.CheckRecordObjectApiName.apiName, checkRecordObjectApiName);
                SearchUtil.fillFilterEq(filters, AccountRuleUseRecordConstants.Field.CheckRecordObjectDataId.apiName, checkRecordObjectDataId);
                SearchUtil.fillFilterEq(filters, AccountRuleUseRecordConstants.Field.RuleStage.apiName, RuleStageEnum.CheckValidate.value);

                List<IObjectData> ruleUseRecordList = fundAccountManager.searchQuery(user, AccountRuleUseRecordConstants.API_NAME, filters, Lists.newArrayList(), 0, 1000).getData();
                if (CollectionUtils.isEmpty(ruleUseRecordList)) {
                    log.info("hasFrozenRecordWithoutUseRecord tenantId:{},checkRecordObjectApiName:{},checkRecordObjectDataId:{},ruleId:{}", tenantId, checkRecordObjectApiName, checkRecordObjectDataId, ruleId);
                    IObjectData checkRuleData = serviceFacade.findObjectData(user, ruleId, AccountCheckRuleConstants.API_NAME);
                    String checkObjectApiName = checkRuleData.get(AccountCheckRuleConstants.Field.CheckObject.apiName, String.class);
                    String reduceObjectApiName = checkRuleData.get(AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName, String.class);
                    String frozenTriggerAction = checkRuleData.get(AccountCheckRuleConstants.Field.CheckTriggerAction.apiName, String.class);
                    String reduceTriggerAction = checkRuleData.get(AccountCheckRuleConstants.Field.ReduceTriggerAction.apiName, String.class);
                    String frozenTriggerButton = checkRuleData.get(AccountCheckRuleConstants.Field.CheckTriggerButton.apiName, String.class);
                    String reduceTriggerButton = checkRuleData.get(AccountCheckRuleConstants.Field.ReduceTriggerButton.apiName, String.class);

                    String frozenJson = checkRuleData.get(AccountCheckRuleConstants.Field.OccupiedMapping.apiName, String.class);
                    String reduceJson = checkRuleData.get(AccountCheckRuleConstants.Field.ReduceMapping.apiName, String.class);

                    log.info("checkRuleInfo,checkObjectApiName:{},frozenTriggerAction:{},frozenTriggerButton:{},frozenMappingJson:{};reduceObjectApiName:{},reduceTriggerAction:{},reduceTriggerButton:{},reduceMappingJson:{},referenceFieldName:{}", checkObjectApiName, frozenTriggerAction, frozenTriggerButton, frozenJson, reduceObjectApiName, reduceTriggerAction, reduceTriggerButton, reduceJson, ObjectDataUtil.getReferenceFieldName(tenantId, checkObjectApiName, reduceObjectApiName).orElse(null));

                    if (BooleanUtils.isTrue(arg.getNeedCreateRuleUseRecord())) {
                        IObjectData accountRuleUseRecordData = RuleHandlerUtil.getAccountCheckRecordData(user, checkRecordObjectApiName, checkRecordObjectDataId, checkRuleData, RuleStageEnum.CheckValidate);
                        serviceFacade.saveObjectData(user, accountRuleUseRecordData);
                        log.info("tenantId:{},createRuleUseRecordData:{}", tenantId, accountRuleUseRecordData);
                        triggerAccountCheckRuleReduce(tenantId, frozenData, checkRuleData);
                    }
                }
            } catch (Exception e) {
                log.warn("frozenData:{}", frozenData);
                resultMap.compute(frozenData.getId(), (k, v) -> {
                    if (v == null) {
                        v = Lists.newArrayList();
                    }
                    ((List<String>) v).add(k);
                    return v;
                });
            }
        }
        return resultMap;
    }

    @Override
    public Map<String, Object> triggerAccountCheckRuleReduce(ServiceContext serviceContext, CurlModel.FrozenRecordWithoutRuleUseRecord arg) {
        List<String> frozenIds = arg.getFrozenIds();
        Map<String, Object> result = Maps.newHashMap();
        if (CollectionUtils.isEmpty(frozenIds)) {
            return result;
        }
        User user = serviceContext.getUser();
        List<IObjectData> frozenDataList = serviceFacade.findObjectDataByIds(user.getTenantId(), frozenIds, AccountFrozenRecordConstant.API_NAME);
        for (IObjectData frozenData : frozenDataList) {
            String checkObjectApiName = frozenData.get(AccountFrozenRecordConstant.Field.CheckRecordObjectApiName.apiName, String.class);
            String checkObjectDataId = frozenData.get(AccountFrozenRecordConstant.Field.CheckRecordObjectDataId.apiName, String.class);
            String checkRuleId = frozenData.get(AccountFrozenRecordConstant.Field.AccountCheckRuleId.apiName, String.class);

            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, AccountRuleUseRecordConstants.Field.CheckRuleId.apiName, checkRuleId);
            SearchUtil.fillFilterEq(filters, AccountRuleUseRecordConstants.Field.RuleType.apiName, AccountCheckRuleTypeEnum.Check_Reduce.getValue());
            SearchUtil.fillFilterEq(filters, AccountRuleUseRecordConstants.Field.RuleStage.apiName, RuleStageEnum.CheckValidate.value);
            SearchUtil.fillFilterEq(filters, AccountRuleUseRecordConstants.Field.CheckRecordObjectApiName.apiName, checkObjectApiName);
            SearchUtil.fillFilterEq(filters, AccountRuleUseRecordConstants.Field.CheckRecordObjectDataId.apiName, checkObjectDataId);
            List<IObjectData> ruleUseRecordList = fundAccountManager.searchQuery(user, AccountRuleUseRecordConstants.API_NAME, filters, Lists.newArrayList(), 0, 1000).getData();

            if (CollectionUtils.size(ruleUseRecordList) != 1) {
                log.info("ruleUseRecordList:{}", ruleUseRecordList);
                continue;
            }
            IObjectData ruleUseRecordData = ruleUseRecordList.get(0);
            String checkRuleJson = ruleUseRecordData.get(AccountRuleUseRecordConstants.Field.CheckRule.apiName, String.class);
            IObjectData checkRuleData = new ObjectData();
            checkRuleData.fromJsonString(checkRuleJson);
            triggerAccountCheckRuleReduce(user.getTenantId(), frozenData, checkRuleData);
        }
        return result;
    }

    @Override
    public CurlModel.GetAccountCheckRuleDirtyDataResult getAccountCheckRuleDirtyData(ServiceContext serviceContext, CurlModel.GetAccountCheckRuleDirtyDataArg arg) {
        CurlModel.GetAccountCheckRuleDirtyDataResult result = new CurlModel.GetAccountCheckRuleDirtyDataResult();
        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return new CurlModel.GetAccountCheckRuleDirtyDataResult();
        }

        result.setTenantId2accountCheckRuleDatas(new HashMap<>());
        for (String tenantId : arg.getTenantIds()) {
            List<IObjectData> accountCheckRuleDatas = getAccountCheckRule(tenantId, arg.getLastModifiedTime());

            if (arg.isAllField()) {
                result.getTenantId2accountCheckRuleDatas().put(tenantId, accountCheckRuleDatas);
                continue;
            }

            List<IObjectData> newAccountCheckRuleDatas = Lists.newArrayList();
            for (IObjectData accountCheckRuleData : accountCheckRuleDatas) {
                IObjectData newAccountCheckRuleData = new ObjectData();
                newAccountCheckRuleData.set("_id", accountCheckRuleData.getId());
                newAccountCheckRuleData.set(AccountCheckRuleConstants.Field.TriggerConditionRuleCode.apiName, accountCheckRuleData.get(AccountCheckRuleConstants.Field.TriggerConditionRuleCode.apiName));
                newAccountCheckRuleData.set(AccountCheckRuleConstants.Field.CheckRuleRuleCode.apiName, accountCheckRuleData.get(AccountCheckRuleConstants.Field.CheckRuleRuleCode.apiName));
                newAccountCheckRuleData.set(AccountCheckRuleConstants.Field.ReduceTriggerConditionRuleCode.apiName, accountCheckRuleData.get(AccountCheckRuleConstants.Field.ReduceTriggerConditionRuleCode.apiName));
                newAccountCheckRuleDatas.add(newAccountCheckRuleData);
            }
            result.getTenantId2accountCheckRuleDatas().put(tenantId, newAccountCheckRuleDatas);
        }
        return result;
    }


    @Override
    public CurlModel.UpdateAccountCheckRuleRuleCodeResult updateAccountCheckRuleRuleCode(CurlModel.UpdateAccountCheckRuleRuleCodeArg arg) {
        User admin = new User(arg.getTenantId(), "-10000");

        //查校验规则
        List<IObjectData> accountCheckRuleDatas = serviceFacade.findObjectDataByIds(arg.getTenantId(), Lists.newArrayList(arg.getAccountCheckRuleId()), AccountCheckRuleConstants.API_NAME);
        if (CollectionUtils.isEmpty(accountCheckRuleDatas)) {
            return new CurlModel.UpdateAccountCheckRuleRuleCodeResult(false, "校验规则数据不存在");
        }
        IObjectData accountCheckRuleData = accountCheckRuleDatas.get(0);

        updateRuleCodeAndCreateRule(admin, accountCheckRuleData, arg.getRuleCodeFieldApiNames(), new HashMap<>(), true);

        return new CurlModel.UpdateAccountCheckRuleRuleCodeResult(true, "ok");
    }

    private void updateRuleCodeAndCreateRule(User user, IObjectData accountCheckRuleData, List<String> ruleCodeFieldApiNames, Map<String, String> ruleCodeFieldApiName2ruleCode, boolean needUpdateAccountCheckRuleData) {
        Map<String, String> fieldApiName2RuleGroupRuleCode = new HashMap<>();
        Map<String, String> fieldApiName2ObjectApiName = new HashMap<>();
        List<String> updateFieldList = Lists.newArrayList();
        //生成新的ruleCode
        for (String ruleCodeFieldApiName : ruleCodeFieldApiNames) {
            if (ruleCodeFieldApiName2ruleCode.containsKey(ruleCodeFieldApiName)) {
                //入参有ruleCode，就用老的ruleCode
                String ruleCode = ruleCodeFieldApiName2ruleCode.get(ruleCodeFieldApiName);
                accountCheckRuleData.set(ruleCodeFieldApiName, ruleCode);
            } else {
                if (Objects.equals(ruleCodeFieldApiName, AccountCheckRuleConstants.Field.TriggerConditionRuleCode.apiName)) {
                    accountCheckRuleData.set(AccountCheckRuleConstants.Field.TriggerConditionRuleCode.apiName, serviceFacade.generateId());
                    updateFieldList.add(AccountCheckRuleConstants.Field.TriggerConditionRuleCode.apiName);
                    fieldApiName2RuleGroupRuleCode.put(AccountCheckRuleConstants.Field.TriggerCondition.apiName, accountCheckRuleData.get(AccountCheckRuleConstants.Field.TriggerConditionRuleCode.apiName, String.class));
                    fieldApiName2ObjectApiName.put(AccountCheckRuleConstants.Field.TriggerCondition.apiName, accountCheckRuleData.get(AccountCheckRuleConstants.Field.CheckObject.apiName, String.class));
                } else if (Objects.equals(ruleCodeFieldApiName, AccountCheckRuleConstants.Field.CheckRuleRuleCode.apiName)) {
                    accountCheckRuleData.set(AccountCheckRuleConstants.Field.CheckRuleRuleCode.apiName, serviceFacade.generateId());
                    updateFieldList.add(AccountCheckRuleConstants.Field.CheckRuleRuleCode.apiName);
                    fieldApiName2RuleGroupRuleCode.put(AccountCheckRuleConstants.Field.CheckRule.apiName, accountCheckRuleData.get(AccountCheckRuleConstants.Field.CheckRuleRuleCode.apiName, String.class));
                    fieldApiName2ObjectApiName.put(AccountCheckRuleConstants.Field.CheckRule.apiName, accountCheckRuleData.get(AccountCheckRuleConstants.Field.CheckObject.apiName, String.class));
                } else if (Objects.equals(ruleCodeFieldApiName, AccountCheckRuleConstants.Field.ReduceTriggerConditionRuleCode.apiName)) {
                    accountCheckRuleData.set(AccountCheckRuleConstants.Field.ReduceTriggerConditionRuleCode.apiName, serviceFacade.generateId());
                    updateFieldList.add(AccountCheckRuleConstants.Field.ReduceTriggerConditionRuleCode.apiName);
                    fieldApiName2RuleGroupRuleCode.put(AccountCheckRuleConstants.Field.ReduceTriggerCondition.apiName, accountCheckRuleData.get(AccountCheckRuleConstants.Field.ReduceTriggerConditionRuleCode.apiName, String.class));
                    fieldApiName2ObjectApiName.put(AccountCheckRuleConstants.Field.ReduceTriggerCondition.apiName, accountCheckRuleData.get(AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName, String.class));
                }
            }
        }

        //新建规则引擎
        caRuleEngineManager.createRule(user, accountCheckRuleData, fieldApiName2RuleGroupRuleCode, fieldApiName2ObjectApiName);

        //更新ruleCode
        if (needUpdateAccountCheckRuleData) {
            serviceFacade.batchUpdateByFields(user, Lists.newArrayList(accountCheckRuleData), updateFieldList);
        }
    }

    @Override
    public CurlModel.UpdateAccountRuleUseRecordRuleCodeResult updateAccountRuleUseRecordRuleCode(CurlModel.UpdateAccountRuleUseRecordRuleCodeArg arg) {
        User admin = new User(arg.getTenantId(), "-10000");

        for (String accountRuleUseRecordId : arg.getAccountRuleUseRecordIds()) {
            updateAccountRuleUseRecordRuleCode(admin, arg, accountRuleUseRecordId);
        }

        return new CurlModel.UpdateAccountRuleUseRecordRuleCodeResult(true, "ok");
    }

    @Override
    public CurlModel.UpdateAccountAuthResult updateAccountAuthDescribe(CurlModel.UpdateAccountAuthArg arg) {
        log.info("updateAccountAuthDescribe, arg[{}]", arg);
        CurlModel.UpdateAccountAuthResult result = new CurlModel.UpdateAccountAuthResult(true, "", Lists.newArrayList());
        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return result;
        }

        List<String> successTenantIds = new ArrayList<>();
        for (String tenantId : arg.getTenantIds()) {
            fAccountAuthorizationManager.updateAccountAuthDescribe(tenantId, arg.getUpdateEntryAmountFieldApiNameLayoutRequire());
            successTenantIds.add(tenantId);
            log.info("updateAccountAuthDescribe, tenantId[{}]", tenantId);
        }
        return new CurlModel.UpdateAccountAuthResult(true, "", successTenantIds);
    }

    @Override
    public CurlModel.UpdateAccountAuthResult updateFieldDescribe(CurlModel.UpdateFieldArg arg) {
        if (arg.isUpdate()) {
            fAccountAuthorizationManager.updateField(arg.getTenantId(), arg.getObjectApiName(), arg.getFieldApiName());
        } else {
            fAccountAuthorizationManager.deleteField(arg.getTenantId(), arg.getObjectApiName(), arg.getFieldApiName());
            fAccountAuthorizationManager.createField(arg.getTenantId(), arg.getObjectApiName(), arg.getFieldApiName(), arg.getFieldLabel());
        }

        return new CurlModel.UpdateAccountAuthResult(true, "", Lists.newArrayList());
    }

    @Override
    public CurlModel.UpdateAccountAuthResult updateAccountAuthData(CurlModel.UpdateAccountAuthArg arg) {
        CurlModel.UpdateAccountAuthResult result = new CurlModel.UpdateAccountAuthResult(true, "", Lists.newArrayList());
        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return result;
        }

        List<String> successTenantIds = new ArrayList<>();
        for (String tenantId : arg.getTenantIds()) {
            fAccountAuthorizationManager.updateAccountAuthData(tenantId);
            successTenantIds.add(tenantId);
            log.info("updateAccountAuthData, tenantId[{}]", tenantId);
        }
        return new CurlModel.UpdateAccountAuthResult(true, "", successTenantIds);
    }

    @Override
    public CurlModel.AddFAccountAmountAndUpdateReceivableAmountResult addFAccountAmountAndUpdateReceivableAmount(CurlModel.AddFAccountAmountAndUpdateReceivableAmountArg arg) {
        CurlModel.AddFAccountAmountAndUpdateReceivableAmountResult result = new CurlModel.AddFAccountAmountAndUpdateReceivableAmountResult(true, "", Lists.newArrayList(), Lists.newArrayList());
        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return result;
        }

        List<String> successTenantIds = new ArrayList<>();
        List<String> noOpenTenantIds = new ArrayList<>();
        for (String tenantId : arg.getTenantIds()) {
            //新版客户账户是否开启
            FundAccountSwitchEnum fundAccountStatus = fundAccountManager.getFundAccountStatus(tenantId);
            if (FundAccountSwitchEnum.FUND_ACCOUNT_OPEN != fundAccountStatus) {
                noOpenTenantIds.add(tenantId);
                continue;
            }

            User user = new User(tenantId, "-10000");
            fundAccountManager.addFieldWhenNewCustomerAccount(user, true);
            successTenantIds.add(tenantId);
            log.info("addFAccountAmountAndUpdateReceivableAmount, tenantId[{}]", tenantId);
        }
        return new CurlModel.AddFAccountAmountAndUpdateReceivableAmountResult(true, "", successTenantIds, noOpenTenantIds);
    }

    @Override
    public CurlModel.AccountTransactionFlowExpenseTypeAddAuthRelateDeductOptionResult accountTransactionFlowExpenseTypeAddComponentDeductOption(CurlModel.AccountTransactionFlowExpenseTypeAddComponentDeductOptionArg arg) {
        CurlModel.AccountTransactionFlowExpenseTypeAddAuthRelateDeductOptionResult result = new CurlModel.AccountTransactionFlowExpenseTypeAddAuthRelateDeductOptionResult(true, "", Lists.newArrayList());
        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return result;
        }

        List<String> successTenantIds = new ArrayList<>();
        for (String tenantId : arg.getTenantIds()) {
            //是否开启了账户授权
            boolean isAccountAuthOpen = fundAccountConfigManager.isAccountAuthOpen(tenantId);
            if (!isAccountAuthOpen) {
                continue;
            }

            commonDescribeManager.selectOneFieldAddOption(tenantId, AccountTransactionFlowConst.API_NAME, AccountTransactionFlowConst.Field.ExpenseType.apiName, ExpenseTypeEnum.ComponentDeduct.getValue(), ExpenseTypeEnum.ComponentDeduct.getLabel());
            successTenantIds.add(tenantId);
            log.info("addFAccountAmountAndUpdateReceivableAmount, tenantId[{}]", tenantId);
        }
        return new CurlModel.AccountTransactionFlowExpenseTypeAddAuthRelateDeductOptionResult(true, "", successTenantIds);
    }

    @Override
    public CurlModel.UpdateAccountTransactionFlowExpenseTypeResult updateAccountTransactionFlowExpenseType(CurlModel.UpdateAccountTransactionFlowExpenseTypeArg arg) {
        CurlModel.UpdateAccountTransactionFlowExpenseTypeResult result = new CurlModel.UpdateAccountTransactionFlowExpenseTypeResult(true, "", Lists.newArrayList());
        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return result;
        }

        List<String> successTenantIds = new ArrayList<>();
        for (String tenantId : arg.getTenantIds()) {
            transactionFlowDataManager.updateAccountTransactionFlowExpenseType(tenantId, arg.getOldExpenseType(), arg.getNewExpenseType());
            successTenantIds.add(tenantId);
            log.info("updateAccountTransactionFlowExpenseType, tenantId[{}]", tenantId);
        }
        return new CurlModel.UpdateAccountTransactionFlowExpenseTypeResult(true, "", successTenantIds);
    }

    @Override
    public CurlModel.BatchDelFuncModelResult batchDelFunc(CurlModel.BatchDelFuncModelArg arg) {
        String format = "%s||%s";
        Set<String> funcCodes = Sets.newHashSet();
        List<String> objectDescribes = arg.getObjectDescribes();
        List<String> codes = arg.getCodes();
        for (String objectDescribe : objectDescribes) {
            for (String code : codes) {
                String funcCode = String.format(format, objectDescribe, code);
                funcCodes.add(funcCode);
            }
        }

        List<String> tenantIds = arg.getTenantIds();
        List<String> errTenantId = Lists.newArrayList();
        tenantIds.forEach(tenantId -> {
            try {
                AuthContext authContext = AuthContext.builder().tenantId(tenantId).appId("CRM").userId("-10000").build();
                Arg proxyArg = Arg.builder().authContext(authContext).funcSet(Lists.newArrayList(funcCodes)).build();
                Map<String, String> header = Maps.newHashMap();
                header.put("x-fs-ei", tenantId);
                header.put("x-userInfo", "-10000");
                functionPrivilegeProxy.delFuncCodes(proxyArg, header);
            } catch (Exception e) {
                log.warn("batchDelFunc error,tenantId:{},funcCodes:{}", tenantId, funcCodes, e);
                errTenantId.add(tenantId);
            }
        });

        if (errTenantId.isEmpty()) {
            return BatchDelFuncModelResult.success();
        } else {
            return BatchDelFuncModelResult.error(errTenantId);
        }
    }

    @Override
    public CurlModel.TransferForOutcomeAccountResult transferForOutcomeAccount(CurlModel.TransferForOutcomeAccountArg arg) {
        CurlModel.TransferForOutcomeAccountResult result = new CurlModel.TransferForOutcomeAccountResult(true, "", 0, 0, 0, 0, 0, 0, Lists.newArrayList(), Lists.newArrayList(), Lists.newArrayList(), Lists.newArrayList(), Lists.newArrayList());
        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return result;
        }

        List<String> noOpenTenantIds = new ArrayList<>();
        List<String> noHasOutcomeAuthDataTenantIds = new ArrayList<>();
        List<String> noHasInitDataTenantIds = new ArrayList<>();
        List<String> noNeedCreateAccountCheckRuleTenantIds = new ArrayList<>();
        List<String> successTenantIds = new ArrayList<>();
        for (String tenantId : arg.getTenantIds()) {
            log.info("transferForOutcomeAccount, begin tenantId[{}]", tenantId);
            //账户授权是否开启
            AccountAuthSwitchEnum switchStatus = fundAccountConfigManager.getAccountAuthStatus(tenantId);
            log.info("transferForOutcomeAccount  enableAccountAuth, switchStatus[{}]", switchStatus);
            if (!Objects.equals(switchStatus.getStatus(), AccountAuthSwitchEnum.OPENED.getStatus())) {
                noOpenTenantIds.add(tenantId);
                log.info("transferForOutcomeAccount, noOpenTenantIds tenantId[{}]", tenantId);
                continue;
            }

            User admin = new User(tenantId, "-10000");
            RequestContext requestContext = RequestContext.builder().user(admin).tenantId(tenantId).build();
            ServiceContext serviceContext = new ServiceContext(requestContext, null, null);

            //查出所有支出授权
            List<IObjectData> allOutcomeAuthDatas = fAccountAuthorizationManager.getFAccountAuthorizationDatas(admin, FAccountAuthAuthorizedTypeEnum.Outcome.getValue(), null);
            if (CollectionUtils.isEmpty(allOutcomeAuthDatas)) {
                noHasOutcomeAuthDataTenantIds.add(tenantId);
                log.info("transferForOutcomeAccount, noHasOutcomeAuthDataTenantIds tenantId[{}]", tenantId);
                continue;
            }

            //有支出授权的，要开启账户授权
            customerAccountService.enableAccountCheckRule(serviceContext, true);

            //所有已经初始化的支出授权
            List<IObjectData> allHasInitOutcomeAuthDatas = allOutcomeAuthDatas.stream().filter(d -> Objects.equals(d.get(FAccountAuthorizationConstants.Field.Status.apiName, String.class), FAccountAuthorizationStatusEnum.HAS_INIT.getValue())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(allHasInitOutcomeAuthDatas)) {
                noHasInitDataTenantIds.add(tenantId);
                log.info("transferForOutcomeAccount, noHasInitDataTenantIds tenantId[{}]", tenantId);
                continue;
            }

            //已经存在'组件扣减'的校验规则的对象
            List<String> authorizedObjectApiNames = allHasInitOutcomeAuthDatas.stream().map(d -> d.get(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, String.class)).collect(Collectors.toList());
            List<String> hasComponentReduceAccountCheckRuleDataObjectApiNames = accountCheckRuleManager.getHasComponentReduceAccountCheckRuleDataObjectApiNames(tenantId, authorizedObjectApiNames);
            List<IObjectData> needCreateAccountCheckRuleOutcomeAuthDatas;
            if (CollectionUtils.isEmpty(hasComponentReduceAccountCheckRuleDataObjectApiNames)) {
                needCreateAccountCheckRuleOutcomeAuthDatas = allHasInitOutcomeAuthDatas;
            } else {
                needCreateAccountCheckRuleOutcomeAuthDatas = allHasInitOutcomeAuthDatas.stream().filter(d -> !hasComponentReduceAccountCheckRuleDataObjectApiNames.contains(d.get(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, String.class))).collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(needCreateAccountCheckRuleOutcomeAuthDatas)) {
                noNeedCreateAccountCheckRuleTenantIds.add(tenantId);
                log.info("transferForOutcomeAccount, noNeedCreateAccountCheckRuleTenantIds tenantId[{}]", tenantId);
                continue;
            }

            //需要新建'启用'的'组件扣减'的校验规则
            for (IObjectData authData : needCreateAccountCheckRuleOutcomeAuthDatas) {
                log.info("transferForOutcomeAccount tenantId[{}]", tenantId);
                String authorizedObjectApiName = authData.get(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, String.class);
                //查授权明细
                List<IObjectData> authDetails = authorizationDetailManager.query(admin, FAccountAuthAuthorizedTypeEnum.Outcome.getValue(), authorizedObjectApiName);
                accountCheckRuleManager.saveComponentReduceAccountCheckRule(admin, authData, authDetails, AccountCheckRuleStatusEnum.On.getValue(), true);
            }

            successTenantIds.add(tenantId);
            log.info("transferForOutcomeAccount end, tenantId[{}]", tenantId);
        }
        return new CurlModel.TransferForOutcomeAccountResult(true, "", arg.getTenantIds().size(), noOpenTenantIds.size(), noHasOutcomeAuthDataTenantIds.size(), noHasInitDataTenantIds.size(), noNeedCreateAccountCheckRuleTenantIds.size(), successTenantIds.size(), noOpenTenantIds, noHasOutcomeAuthDataTenantIds, noHasInitDataTenantIds, noNeedCreateAccountCheckRuleTenantIds, successTenantIds);
    }

    @Override
    public CurlModel.TransferForAccountCheckRuleResult transferForAccountCheckRule(CurlModel.TransferForAccountCheckRuleArg arg) {
        CurlModel.TransferForAccountCheckRuleResult result = new CurlModel.TransferForAccountCheckRuleResult(true, "", 0, 0, 0, Lists.newArrayList(), Lists.newArrayList());
        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return result;
        }

        List<String> successTenantIds = new ArrayList<>();
        List<String> noOpenTenantIds = new ArrayList<>();
        for (String tenantId : arg.getTenantIds()) {
            log.info("transferForAccountCheckRule begin, tenantId[{}]", tenantId);
            //校验规则是否开启
            AccountCheckSwitchEnum switchStatus = fundAccountConfigManager.getAccountCheckStatus(tenantId);
            if (!Objects.equals(switchStatus.getStatus(), AccountCheckSwitchEnum.OPENED.getStatus())) {
                noOpenTenantIds.add(tenantId);
                log.info("transferForAccountCheckRule noOpenTenantIds, tenantId[{}]", tenantId);
                continue;
            }

            User user = new User(tenantId, "-10000");
            RequestContext requestContext = RequestContext.builder().user(user).tenantId(tenantId).build();
            ServiceContext serviceContext = new ServiceContext(requestContext, null, null);

            //1、开启账户授权
            accountAuthService.enableAccountAuth(serviceContext);
            log.info("transferForAccountCheckRule enableAccountAuth, tenantId[{}]", tenantId);

            String accountCheckRuleStatus = accountCheckRuleManager.getSalesOrderAccountCheckRuleStatus(user, true);
            log.info("transferForAccountCheckRule getSalesOrderAccountCheckRuleStatus, tenantId[{}], accountCheckRuleStatus[{}]", tenantId, accountCheckRuleStatus);

            //2、新建订单相关的支出类型的'账户授权'数据
            fAccountAuthorizationManager.saveSalesOrderInitOutcomeAuthData(serviceContext);
            log.info("transferForAccountCheckRule saveSalesOrderInitOutcomeAuthData tenantId[{}]", tenantId);

            //3、新建订单'组件扣减'的校验规则数据
            accountCheckRuleManager.saveComponentReduceAccountCheckRuleForSaleOrder(user, true, accountCheckRuleStatus);
            log.info("transferForAccountCheckRule saveComponentReduceAccountCheckRuleForSaleOrder tenantId[{}]", tenantId);

            successTenantIds.add(tenantId);
            log.info("transferForAccountCheckRule end, tenantId[{}]", tenantId);
        }
        return new CurlModel.TransferForAccountCheckRuleResult(true, "", arg.getTenantIds().size(), noOpenTenantIds.size(), successTenantIds.size(), noOpenTenantIds, successTenantIds);
    }

    @Override
    public CurlModel.TransferCheckObjectAndReduceRelatedObjectToOutcomeAuthResult transferCheckObjectAndReduceRelatedObjectToOutcomeAuth(CurlModel.TransferCheckObjectAndReduceRelatedObjectToOutcomeAuthArg arg) {
        CurlModel.TransferCheckObjectAndReduceRelatedObjectToOutcomeAuthResult result = new CurlModel.TransferCheckObjectAndReduceRelatedObjectToOutcomeAuthResult(true, "", 0, 0, 0, Lists.newArrayList(), Lists.newArrayList());
        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return result;
        }

        List<String> successTenantIds = new ArrayList<>();
        List<String> noOpenAccountCheckRuleTenantIds = new ArrayList<>();
        for (String tenantId : arg.getTenantIds()) {
            log.info("transferCheckObjectAndReduceRelatedObjectToOutcomeAuth begin, tenantId[{}]", tenantId);
            //校验规则是否开启
            AccountCheckSwitchEnum switchStatus = fundAccountConfigManager.getAccountCheckStatus(tenantId);
            if (!Objects.equals(switchStatus.getStatus(), AccountCheckSwitchEnum.OPENED.getStatus())) {
                noOpenAccountCheckRuleTenantIds.add(tenantId);
                log.info("transferCheckObjectAndReduceRelatedObjectToOutcomeAuth noOpenAccountCheckRuleTenantIds, tenantId[{}]", tenantId);
                continue;
            }

            User user = new User(tenantId, "-10000");
            RequestContext requestContext = RequestContext.builder().user(user).tenantId(tenantId).build();
            ServiceContext serviceContext = new ServiceContext(requestContext, null, null);

            //1、开启账户授权
            accountAuthService.enableAccountAuth(serviceContext);
            log.info("transferCheckObjectAndReduceRelatedObjectToOutcomeAuth enableAccountAuth, tenantId[{}]", tenantId);

            //2、查出所有的校验规则，新建支出授权
            accountCheckRuleManager.transferCheckObjectAndReduceRelatedObjectToOutcomeAuth(tenantId);
            log.info("transferCheckObjectAndReduceRelatedObjectToOutcomeAuth transferCheckObjectAndReduceRelatedObjectToOutcomeAuth, tenantId[{}]", tenantId);

            successTenantIds.add(tenantId);
            log.info("transferCheckObjectAndReduceRelatedObjectToOutcomeAuth end, tenantId[{}]", tenantId);
        }
        return new CurlModel.TransferCheckObjectAndReduceRelatedObjectToOutcomeAuthResult(true, "", arg.getTenantIds().size(), noOpenAccountCheckRuleTenantIds.size(), successTenantIds.size(), noOpenAccountCheckRuleTenantIds, successTenantIds);
    }

    @Override
    public CurlModel.TransferAccountRuleUseRecordResult transferAccountRuleUseRecord(CurlModel.TransferAccountRuleUseRecordArg arg) {
        CurlModel.TransferAccountRuleUseRecordResult result = new CurlModel.TransferAccountRuleUseRecordResult(true, "", 0, 0, 0, 0, 0, Lists.newArrayList(), Lists.newArrayList(), Lists.newArrayList(), Lists.newArrayList());
        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return result;
        }

        List<String> noOpenAccountAuthTenantIds = new ArrayList<>();
        List<String> noHasInitOutcomeAuthDataTenantIds = new ArrayList<>();
        List<String> noComponentDeduceTransactionFlowDatasTenantIds = new ArrayList<>();
        List<String> successTenantIds = new ArrayList<>();
        for (String tenantId : arg.getTenantIds()) {
            log.info("transferAccountRuleUseRecord begin, tenantId[{}]", tenantId);

            //账户授权是否开启
            AccountAuthSwitchEnum switchStatus = fundAccountConfigManager.getAccountAuthStatus(tenantId);
            log.info("transferAccountRuleUseRecord getAccountAuthStatus, switchStatus[{}]", switchStatus);
            if (!Objects.equals(switchStatus.getStatus(), AccountAuthSwitchEnum.OPENED.getStatus())) {
                noOpenAccountAuthTenantIds.add(tenantId);
                log.info("transferAccountRuleUseRecord noOpenAccountAuthTenantIds, tenantId[{}]", tenantId);
                continue;
            }

            User admin = new User(tenantId, "-10000");
            RequestContext requestContext = RequestContext.builder().user(admin).tenantId(tenantId).build();
            ServiceContext serviceContext = new ServiceContext(requestContext, null, null);

            //查出所有支出授权
            List<IObjectData> allInitOutcomeAuthDatas = fAccountAuthorizationManager.getFAccountAuthorizationDatas(admin, FAccountAuthAuthorizedTypeEnum.Outcome.getValue(), FAccountAuthorizationStatusEnum.HAS_INIT.getValue());
            if (CollectionUtils.isEmpty(allInitOutcomeAuthDatas)) {
                noHasInitOutcomeAuthDataTenantIds.add(tenantId);
                log.info("transferAccountRuleUseRecord noHasInitOutcomeAuthDataTenantIds, tenantId[{}]", tenantId);
                continue;
            }

            //有支出授权的，要开启账户授权
            customerAccountService.enableAccountCheckRule(serviceContext, true);

            //是否有 ExpenseTypeEnum.ComponentDeduct("component_deduct", "组件扣减") 类型的流水
            List<IObjectData> componentDeduceTransactionFlowDatas = transactionFlowDataManager.getTransactionFlowDatasByExpenseType(tenantId, ExpenseTypeEnum.ComponentDeduct.getValue());
            if (CollectionUtils.isEmpty(componentDeduceTransactionFlowDatas)) {
                noComponentDeduceTransactionFlowDatasTenantIds.add(tenantId);
                log.info("transferAccountRuleUseRecord noComponentDeduceTransactionFlowDatasTenantIds, tenantId[{}]", tenantId);
                continue;
            }

            accountRuleUseRecordManager.repairForComponentReduce(tenantId, componentDeduceTransactionFlowDatas);
            log.info("transferAccountRuleUseRecord repairForComponentReduce, tenantId[{}]", tenantId);

            successTenantIds.add(tenantId);
            log.info("transferAccountRuleUseRecord end, tenantId[{}]", tenantId);
        }
        return new CurlModel.TransferAccountRuleUseRecordResult(true, "", arg.getTenantIds().size(), noOpenAccountAuthTenantIds.size(), noHasInitOutcomeAuthDataTenantIds.size(), noComponentDeduceTransactionFlowDatasTenantIds.size(), successTenantIds.size(), noOpenAccountAuthTenantIds, noHasInitOutcomeAuthDataTenantIds, noComponentDeduceTransactionFlowDatasTenantIds, successTenantIds);
    }

    @Override
    public CurlModel.TransferMappingFieldForComponentReduceAccountCheckRuleResult transferMappingFieldForComponentReduceAccountCheckRule(CurlModel.TransferMappingFieldForComponentReduceAccountCheckRuleArg arg) {
        CurlModel.TransferMappingFieldForComponentReduceAccountCheckRuleResult result = new CurlModel.TransferMappingFieldForComponentReduceAccountCheckRuleResult(true, "", 0, 0, 0, Lists.newArrayList(), Lists.newArrayList());
        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return result;
        }

        List<String> noOpenAccountCheckRuleTenantIds = new ArrayList<>();
        List<String> successTenantIds = new ArrayList<>();
        for (String tenantId : arg.getTenantIds()) {
            log.info("transferMappingFieldForComponentReduceAccountCheckRule begin, tenantId[{}]", tenantId);
            //校验规则是否开启
            AccountCheckSwitchEnum switchStatus = fundAccountConfigManager.getAccountCheckStatus(tenantId);
            if (!Objects.equals(switchStatus.getStatus(), AccountCheckSwitchEnum.OPENED.getStatus())) {
                noOpenAccountCheckRuleTenantIds.add(tenantId);
                log.info("transferMappingFieldForComponentReduceAccountCheckRule noOpenAccountCheckRuleTenantIds, tenantId[{}]", tenantId);
                continue;
            }

            //查出所有的启用的组件扣减的校验规则，把缺'支出金额'的映射字段的，补上
            accountCheckRuleManager.transferMappingFieldForComponentReduceAccountCheckRule(tenantId, arg.isUpdate());
            log.info("transferMappingFieldForComponentReduceAccountCheckRule transferForComponentReduceAccountCheckRule, tenantId[{}]", tenantId);

            successTenantIds.add(tenantId);
            log.info("transferMappingFieldForComponentReduceAccountCheckRule end, tenantId[{}]", tenantId);
        }

        return new CurlModel.TransferMappingFieldForComponentReduceAccountCheckRuleResult(true, "", arg.getTenantIds().size(), noOpenAccountCheckRuleTenantIds.size(), successTenantIds.size(), noOpenAccountCheckRuleTenantIds, successTenantIds);
    }

    @Override
    public CurlModel.TransferMappingFieldForComponentReduceUseRecordResult transferMappingFieldForComponentReduceUseRecord(CurlModel.TransferMappingFieldForComponentReduceUseRecordArg arg) {
        CurlModel.TransferMappingFieldForComponentReduceUseRecordResult result = new CurlModel.TransferMappingFieldForComponentReduceUseRecordResult(true, "", 0, 0, 0, Lists.newArrayList(), Lists.newArrayList());
        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return result;
        }

        List<String> noOpenAccountCheckRuleTenantIds = new ArrayList<>();
        List<String> successTenantIds = new ArrayList<>();
        for (String tenantId : arg.getTenantIds()) {
            log.info("transferMappingFieldForComponentReduceUseRecord begin, tenantId[{}]", tenantId);
            //校验规则是否开启
            AccountCheckSwitchEnum switchStatus = fundAccountConfigManager.getAccountCheckStatus(tenantId);
            if (!Objects.equals(switchStatus.getStatus(), AccountCheckSwitchEnum.OPENED.getStatus())) {
                noOpenAccountCheckRuleTenantIds.add(tenantId);
                log.info("transferMappingFieldForComponentReduceUseRecord noOpenAccountCheckRuleTenantIds, tenantId[{}]", tenantId);
                continue;
            }

            //查出所有的组件扣减的'规则使用记录'，把缺'支出金额'的映射字段的，补上
            accountRuleUseRecordManager.repairForComponentReduce(tenantId, arg.isUpdate());

            successTenantIds.add(tenantId);
            log.info("transferMappingFieldForComponentReduceUseRecord end, tenantId[{}]", tenantId);
        }

        return new CurlModel.TransferMappingFieldForComponentReduceUseRecordResult(true, "", arg.getTenantIds().size(), noOpenAccountCheckRuleTenantIds.size(), successTenantIds.size(), noOpenAccountCheckRuleTenantIds, successTenantIds);
    }

    @Override
    public CurlModel.DeleteOffUseRecordResult deleteOffUseRecord(CurlModel.DeleteOffUseRecordArg arg) {
        CurlModel.DeleteOffUseRecordResult result = new CurlModel.DeleteOffUseRecordResult(true, "", 0, 0, 0, Lists.newArrayList(), Lists.newArrayList());
        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return result;
        }

        List<String> noOpenAccountCheckRuleTenantIds = new ArrayList<>();
        List<String> successTenantIds = new ArrayList<>();
        for (String tenantId : arg.getTenantIds()) {
            log.info("deleteOffUseRecord begin, tenantId[{}]", tenantId);
            //校验规则是否开启
            AccountCheckSwitchEnum switchStatus = fundAccountConfigManager.getAccountCheckStatus(tenantId);
            if (!Objects.equals(switchStatus.getStatus(), AccountCheckSwitchEnum.OPENED.getStatus())) {
                noOpenAccountCheckRuleTenantIds.add(tenantId);
                log.info("deleteOffUseRecord noOpenAccountCheckRuleTenantIds, tenantId[{}]", tenantId);
                continue;
            }

            accountRuleUseRecordManager.deleteOffUseRecord(tenantId, arg.isDelete());

            successTenantIds.add(tenantId);
            log.info("deleteOffUseRecord end, tenantId[{}]", tenantId);
        }

        return new CurlModel.DeleteOffUseRecordResult(true, "", arg.getTenantIds().size(), noOpenAccountCheckRuleTenantIds.size(), successTenantIds.size(), noOpenAccountCheckRuleTenantIds, successTenantIds);
    }

    @Override
    public CurlModel.PluginParamAddDetailsResult pluginParamAddDetails(CurlModel.PluginParamAddDetailsArg arg) {
        CurlModel.PluginParamAddDetailsResult result = new CurlModel.PluginParamAddDetailsResult(true, "", 0, 0, 0, 0, Lists.newArrayList(), Lists.newArrayList(), Lists.newArrayList());
        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return result;
        }

        List<String> noOpenTenantIds = new ArrayList<>();
        List<String> noHasInitDataTenantIds = new ArrayList<>();
        List<String> successTenantIds = new ArrayList<>();
        for (String tenantId : arg.getTenantIds()) {
            log.info("pluginParamAddDetails, begin tenantId[{}]", tenantId);
            //账户授权是否开启
            AccountAuthSwitchEnum switchStatus = fundAccountConfigManager.getAccountAuthStatus(tenantId);
            log.info("pluginParamAddDetails  enableAccountAuth, switchStatus[{}]", switchStatus);
            if (!Objects.equals(switchStatus.getStatus(), AccountAuthSwitchEnum.OPENED.getStatus())) {
                noOpenTenantIds.add(tenantId);
                log.info("pluginParamAddDetails, noOpenTenantIds tenantId[{}]", tenantId);
                continue;
            }

            User admin = new User(tenantId, "-10000");

            //查出所有已初始化的支出授权
            List<IObjectData> allInitOutcomeAuthDatas = fAccountAuthorizationManager.getFAccountAuthorizationDatas(admin, FAccountAuthAuthorizedTypeEnum.Outcome.getValue(), FAccountAuthorizationStatusEnum.HAS_INIT.getValue());
            if (CollectionUtils.isEmpty(allInitOutcomeAuthDatas)) {
                noHasInitDataTenantIds.add(tenantId);
                log.info("pluginParamAddDetails noHasInitDataTenantIds, tenantId[{}]", tenantId);
                continue;
            }

            for (IObjectData outcomeAuthData : allInitOutcomeAuthDatas) {
                String authorizedObjectApiName = outcomeAuthData.get(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, String.class);
                String pluginApiName = "customer_account";
                caObjectDomainPluginManager.pluginParamAddDetails(tenantId, authorizedObjectApiName, pluginApiName);
            }

            successTenantIds.add(tenantId);
            log.info("pluginParamAddDetails end, tenantId[{}]", tenantId);
        }
        return new CurlModel.PluginParamAddDetailsResult(true, "", arg.getTenantIds().size(), noOpenTenantIds.size(), noHasInitDataTenantIds.size(), successTenantIds.size(), noOpenTenantIds, noHasInitDataTenantIds, successTenantIds);
    }

    @Override
    public CurlModel.BulkDeleteWithInternalDescribeResult bulkDeleteWithInternalDescribe(CurlModel.BulkDeleteWithInternalDescribeArg arg) {

        List<IObjectData> dataList = serviceFacade.findObjectDataByIds(arg.getTenantId(), arg.getDataIds(), arg.getObjectApiName());
        if (CollectionUtils.isEmpty(dataList)) {
            log.info("bulkDeleteWithInternalDescribe dataList empty arg[{}}", arg);
            return new CurlModel.BulkDeleteWithInternalDescribeResult(0, Lists.newArrayList());
        }

        User user = new User(arg.getTenantId(), "-10000");
        serviceFacade.bulkDeleteWithInternalDescribe(dataList, user);
        List<String> dataIds = dataList.stream().map(IObjectData::getId).collect(Collectors.toList());
        return new CurlModel.BulkDeleteWithInternalDescribeResult(dataList.size(), dataIds);
    }

    @Override
    public CurlModel.SaveAddFinallyDoFromMQResult saveAddFinallyDoFromMQ(CurlModel.SaveAddFinallyDoFromMQArg arg) {
        User user = new User(arg.getTenantId(), "-10000");
        customerAccountDomainPluginManager.saveAddFinallyDoFromMQ(user, arg.getAuthorizedObjectDataId(), arg.getAuthorizedObjectApiName(), arg.getTransactionFlowDataIds());
        return new CurlModel.SaveAddFinallyDoFromMQResult(true);
    }

    @Override
    public CurlModel.canEnableCheckRuleDomainPluginGrayResult canEnableCheckRuleDomainPluginGray(ServiceContext serviceContext, CurlModel.TenantIds arg) {
        List<String> tenantIds = arg.getTenantIds();
        List<String> allowedTenantIds = Lists.newCopyOnWriteArrayList();
        List<String> errorTenantIds = Lists.newCopyOnWriteArrayList();
        for (int idx = 0; idx < tenantIds.size(); idx += 200) {
            int curSize = Math.min(tenantIds.size() - idx, 200);
            ParallelUtils.ParallelTask parallelTask = curSize <= 50 ? ParallelUtils.createBackgroundTask() : ParallelUtils.createParallelTask();
            for (int i = idx; i < idx + 200 && i < tenantIds.size(); i++) {
                String tenantId = tenantIds.get(i);
                parallelTask.submit(() -> {
                    if (accountCheckRuleDomainPluginManager.isAllowedObjectLevelGray(tenantId)) {
                        allowedTenantIds.add(tenantId);
                    } else {
                        errorTenantIds.add(tenantId);
                    }
                });
            }
            try {
                parallelTask.await(300, TimeUnit.SECONDS);
            } catch (TimeoutException e) {
                log.warn("canEnableCheckRuleDomainPluginGray timeout, tenantIds[{}]", tenantIds.subList(idx, Math.min(idx + 200, tenantIds.size())), e);
            }
        }
        CurlModel.canEnableCheckRuleDomainPluginGrayResult result = new CurlModel.canEnableCheckRuleDomainPluginGrayResult();
        result.setErrorTenantIds(errorTenantIds);
        result.setAllowedTenantIds(allowedTenantIds);
        return result;
    }

    @Override
    public CurlModel.Result migrationAccountCheckRuleToDomainPlugin(ServiceContext serviceContext, CurlModel.TenantIds arg) {
        List<String> tenantIds = arg.getTenantIds();
        if (CollectionUtils.isEmpty(tenantIds)) {
            throw new ValidateException("tenantIds is empty");
        }
        List<String> successTenantIds = Lists.newCopyOnWriteArrayList();
        for (List<String> subTenantIds : Lists.partition(tenantIds, 200)) {
            ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
            subTenantIds.forEach(tenantId -> {
                parallelTask.submit(() -> {
                    try {
                        if (accountCheckRuleDomainPluginManager.migrationAccountCheckRuleToDomainPluginWithObjectGray(tenantId)) {
                            successTenantIds.add(tenantId);
                        }
                    } catch (Exception e) {
                        log.info("migrationAccountCheckRuleToDomainPluginWithObjectGray error, tenantId[{}]", tenantId, e);
                    }
                });
            });
            try {
                parallelTask.await(300, TimeUnit.SECONDS);
            } catch (TimeoutException e) {
                log.warn("migrationAccountCheckRuleToDomainPlugin timeout, tenantIds[{}]", subTenantIds, e);
            }
        }
        CurlModel.Result result = new CurlModel.Result();
        List<String> errorTenantIds = ListUtils.removeAll(tenantIds, successTenantIds);
        result.setSuccess(errorTenantIds.isEmpty());
        result.setMsg(result.isSuccess() ? "success" : "here are some error tenantIds");
        result.setTenantIds(errorTenantIds);
        return result;
    }

    @Override
    public CurlModel.Result initCreditEnterAccountPromptPlugin(ServiceContext serviceContext, CurlModel.TenantIds arg) {
        List<String> tenantIds = arg.getTenantIds();
        List<String> failEis = Lists.newArrayList();
        for (String tenantId : tenantIds) {
            try {
                creditManager.initCreditEnterAccountPromptPlugin(User.systemUser(tenantId));
            } catch (Exception e) {
                failEis.add(tenantId);
                log.warn("initCreditEnterAccountPrompt error tenantId:{}", tenantId, e);
            }
        }
        log.info("initCreditEnterAccountPrompt error tenantIds:{}", failEis);
        CurlModel.Result result = new CurlModel.Result();
        result.setTenantIds(failEis);
        result.setMsg(failEis.isEmpty() ? "success" : "here are some error tenantIds");
        return result;
    }

    @Override
    public Map<String, Object> updateFieldValue(ServiceContext serviceContext, CurlModel.FixDataWithCondition arg) {
        List<CurlModel.FieldValue> updateFieldList = arg.getUpdateFieldList();
        Map<String, Object> result = Maps.newHashMap();
        result.put("updateDataNum", 0);
        String objectApiName = arg.getObjectApiName();
        if (CollectionUtils.isEmpty(updateFieldList) || StringUtils.isEmpty(objectApiName)) {
            return result;
        }
        User user = serviceContext.getUser();
        IObjectDescribe objectDescribe = serviceFacade.findObject(user.getTenantId(), objectApiName);
        List<IFilter> filters = Lists.newArrayList();
        Map<String, CurlModel.FieldValue> fieldNameNewValueMap = Maps.newHashMap();
        updateFieldList.forEach(x -> {
            String fieldName = x.getFieldName();
            if (StringUtils.isEmpty(fieldName)) {
                throw new ValidateException("fieldName is empty");
            }
            if (objectDescribe.containsField(fieldName)) {
                SearchUtil.fillFilterEq(filters, x.getFieldName(), x.getDbValue());
                fieldNameNewValueMap.put(x.getFieldName(), x);
            }
        });
        if (filters.isEmpty()) {
            return result;
        }
        int size;
        int updateNum = 0;
        int limit = 1000;
        do {
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            searchTemplateQuery.setFilters(filters);
            searchTemplateQuery.setOffset(0);
            searchTemplateQuery.setLimit(limit);
            List<IObjectData> dataList = serviceFacade.findBySearchQuery(user, objectApiName, searchTemplateQuery).getData();
            size = CollectionUtils.size(dataList);
            List<IObjectData> toUpdateList = Lists.newArrayList();
            dataList.forEach(x -> {
                boolean matched = fieldNameNewValueMap.entrySet().stream().anyMatch(entry -> {
                    String fieldName = entry.getKey();
                    String value = x.get(fieldName, String.class);
                    return !StringUtils.equals(value, entry.getValue().getNewValue());
                });
                if (matched) {
                    fieldNameNewValueMap.forEach((k, v) -> x.set(k, v.getNewValue()));
                    toUpdateList.add(x);
                }
            });
            serviceFacade.batchUpdate(toUpdateList, user);
            updateNum += toUpdateList.size();
        } while (size == limit);
        result.put("updateDataNum", updateNum);
        log.info("updateDataFieldValue result:{}", result);
        return result;
    }

    private void updateAccountRuleUseRecordRuleCode(User admin, CurlModel.UpdateAccountRuleUseRecordRuleCodeArg arg, String accountRuleUseRecordId) {
        //查校验规则
        List<IObjectData> accountRuleUseRecordDatas = serviceFacade.findObjectDataByIds(arg.getTenantId(), Lists.newArrayList(accountRuleUseRecordId), AccountRuleUseRecordConstants.API_NAME);
        if (CollectionUtils.isEmpty(accountRuleUseRecordDatas)) {
            return;
        }
        IObjectData accountRuleUseRecordData = accountRuleUseRecordDatas.get(0);

        //参考 com.facishare.crm.customeraccount.predefine.handler.checkrule.CheckReduceFrozenAndUnfreezeAdaptEditHandler.doHandle
        IObjectData checkRuleData = new ObjectData();
        checkRuleData.fromJsonString(accountRuleUseRecordData.get(AccountRuleUseRecordConstants.Field.CheckRule.apiName, String.class));

        //更新ruleCode
        updateRuleCodeAndCreateRule(admin, checkRuleData, arg.getRuleCodeFieldApiNames(), arg.getRuleCodeFieldApiName2ruleCode(), false);
        //参考 ： com.facishare.crm.customeraccount.predefine.service.impl.CurlServiceImpl.transferRuleUseRecord
        accountRuleUseRecordData.set(AccountRuleUseRecordConstants.Field.CheckRule.apiName, ObjectDataDocument.of(checkRuleData));

        List<String> updateFieldList = Lists.newArrayList(AccountRuleUseRecordConstants.Field.CheckRule.apiName);
        serviceFacade.batchUpdateByFields(admin, Lists.newArrayList(accountRuleUseRecordData), updateFieldList);
    }

    private List<IObjectData> getAccountCheckRule(String tenantId, Long lastModifiedTime) {
        List<IFilter> filterList = Lists.newArrayList();

        IFilter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName("life_status");
        lifeStatusFilter.setFieldValues(Lists.newArrayList("normal"));
        lifeStatusFilter.setOperator(Operator.EQ);
        filterList.add(lifeStatusFilter);

        IFilter deleteStatusFilter = new Filter();
        deleteStatusFilter.setFieldName(ObjectData.IS_DELETED);
        deleteStatusFilter.setFieldValues(Lists.newArrayList("0"));
        deleteStatusFilter.setOperator(Operator.EQ);
        filterList.add(deleteStatusFilter);

        IFilter lastModifiedTimeFilter = new Filter();
        lastModifiedTimeFilter.setFieldName(ObjectData.LAST_MODIFIED_TIME);
        lastModifiedTimeFilter.setFieldValues(Lists.newArrayList(String.valueOf(lastModifiedTime)));
        lastModifiedTimeFilter.setOperator(Operator.GTE);
        filterList.add(lastModifiedTimeFilter);

        IFilter versionFilter = new Filter();
        versionFilter.setFieldName(ObjectData.VERSION);
        versionFilter.setFieldValues(Lists.newArrayList("1"));
        versionFilter.setOperator(Operator.GT);
        filterList.add(versionFilter);

        SearchUtil.fillFilterEq(filterList, Tenantable.TENANT_ID, tenantId);
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(filterList);
        query.setOffset(0);
        query.setLimit(1000);
        query.setOrders(Lists.newArrayList(new OrderBy(DBRecord.ID, Boolean.TRUE)));

        User user = new User(tenantId, "-10000");
        QueryResult<IObjectData> result = serviceFacade.findBySearchQuery(user, AccountCheckRuleConstants.API_NAME, query);
        return result.getData();
    }

    public void triggerAccountCheckRuleReduce(String tenantId, IObjectData frozenData, IObjectData checkRuleData) {
        //查询冻结记录的解冻明细
        String frozenId = frozenData.getId();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, UnfreezeDetailConstant.Field.AccountFrozenRecordId.apiName, frozenId);
        User user = User.systemUser(tenantId);
        List<IObjectData> unfreezeDataList = fundAccountManager.searchQuery(user, UnfreezeDetailConstant.API_NAME, filters, Lists.newArrayList(), 0, 1000).getData();
        if (CollectionUtils.isNotEmpty(unfreezeDataList)) {
            return;
        }
        String checkObjectDataId = frozenData.get(AccountFrozenRecordConstant.Field.CheckRecordObjectDataId.apiName, String.class);

        String checkObjectApiName = checkRuleData.get(AccountCheckRuleConstants.Field.CheckObject.apiName, String.class);
        String reduceObjectApiName = checkRuleData.get(AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName, String.class);
        Optional<String> optional = ObjectDataUtil.getReferenceFieldName(tenantId, checkObjectApiName, reduceObjectApiName);

        log.info("getReferenceFieldName tenantId:{},checkObjectApiName:{},reduceObjectApiName:{},referenceFieldName:{}", tenantId, checkObjectApiName, reduceObjectApiName, optional.orElse(null));
        optional.ifPresent(x -> {
            filters.clear();
            SearchUtil.fillFilterEq(filters, x, checkObjectDataId);
            List<IObjectData> reduceDataList = fundAccountManager.searchQuery(user, reduceObjectApiName, filters, Lists.newArrayList(), 0, 1000).getData();
            IObjectDescribe objectDescribe = serviceFacade.findObject(tenantId, reduceObjectApiName);
            for (IObjectData reduceData : reduceDataList) {
                RequestContext requestContext = RequestContext.builder().user(user).tenantId(user.getTenantId()).build();
                fundAccountMediator.post(requestContext, objectDescribe, reduceData, ReduceTriggerActionEnum.FieldChange.getValue(), null);
            }
        });
    }

    private void addSelectOptionInRebateIncomeType(User user) throws MetadataServiceException {
        IObjectDescribe rebateIncomeObjectDescribe = serviceFacade.findObject(user.getTenantId(), RebateIncomeDetailConstants.API_NAME);
        SelectOneFieldDescribe fieldDescribe = (SelectOneFieldDescribe) rebateIncomeObjectDescribe.getFieldDescribe(RebateIncomeDetailConstants.Field.IncomeType.apiName);
        List<ISelectOption> selectOptions = fieldDescribe.getSelectOptions();
        boolean hasOrderRebate = false;
        for (ISelectOption selectOption : selectOptions) {
            if (!Lists.newArrayList(RebateIncomeTypeEnum.OrderRefund.getValue(), RebateIncomeTypeEnum.OrderRebate.getValue()).contains(selectOption.getValue())) {
                selectOption.set("config", getOptionConfig());
            }
            if (RebateIncomeTypeEnum.OrderRebate.getValue().equals(selectOption.getValue())) {
                hasOrderRebate = true;
            }
        }
        if (!hasOrderRebate) {
            selectOptions.add(SelectOptionBuilder.builder().label(RebateIncomeTypeEnum.OrderRebate.getLabel()).value(RebateIncomeTypeEnum.OrderRebate.getValue()).build());
        }
        fieldDescribe.setConfig(getSelectOneFieldConfig());
        fieldDescribe.setSelectOptions(selectOptions);
        rebateIncomeObjectDescribe = objectDescribeService.updateFieldDescribe(rebateIncomeObjectDescribe, Lists.newArrayList(fieldDescribe));
        log.info("addSelectOptionInRebateIncomeType:{}", rebateIncomeObjectDescribe.toJsonString());
    }

    private void addRebateUseRuleFieldInRebateOutcome(User user) throws MetadataServiceException {
        IObjectDescribe rebateOutcomeObjectDescribe = serviceFacade.findObject(user.getTenantId(), RebateOutcomeDetailConstants.API_NAME);
        IFieldDescribe rebateUseRuleField = rebateOutcomeObjectDescribe.getFieldDescribe(RebateOutcomeDetailConstants.Field.RebateUseRule.apiName);
        if (rebateUseRuleField == null) {
            IFieldDescribe toAddFieldDescribe = getRebateUseField();

            rebateOutcomeObjectDescribe.addFieldDescribe(toAddFieldDescribe);
            rebateOutcomeObjectDescribe = objectDescribeService.replace(rebateOutcomeObjectDescribe, false);
            //rebateOutcomeObjectDescribe = objectDescribeService.addCustomFieldDescribe(rebateOutcomeObjectDescribe, Lists.newArrayList(toAddFieldDescribe));
            log.info("addRebateUseRuleField:{}", rebateOutcomeObjectDescribe.toJsonString());
            ILayout layout = serviceFacade.findLayoutByApiName(user, RebateOutcomeDetailConstants.DEFAULT_LAYOUT_API_NAME, rebateOutcomeObjectDescribe.getApiName());
            LayoutExt layoutExt = LayoutExt.of(layout);
            FieldLayoutPojo fieldLayoutPojo = new FieldLayoutPojo();
            fieldLayoutPojo.setRenderType(SystemConstants.RenderType.ObjectReference.renderType);
            fieldLayoutPojo.setRequired(false);
            fieldLayoutPojo.setReadonly(false);
            layoutExt.addField(toAddFieldDescribe, fieldLayoutPojo);
            layout = serviceFacade.updateLayout(user, layout);
            log.info("addRebateUseRuloLayout:{}", layout.toJsonString());
        }
    }

    private IFieldDescribe getRebateUseField() {
        ObjectReferenceFieldDescribe rebateUseRuleObjectReferenceFieldDescribe = ObjectReferenceFieldDescribeBuilder.builder().apiName(RebateOutcomeDetailConstants.Field.RebateUseRule.apiName).label(RebateOutcomeDetailConstants.Field.RebateUseRule.label).required(false).targetApiName(RebateUseRuleConstants.API_NAME).targetRelatedListName(RebateOutcomeDetailConstants.Field.RebateUseRule.targetRelatedListName).targetRelatedListLabel(RebateOutcomeDetailConstants.Field.RebateUseRule.targetRelatedListLabel).build();
        return rebateUseRuleObjectReferenceFieldDescribe;
    }

    //返利收入 新增字段lookup销售订单，detail_layout也增加,listByPage layout不增加
    @Override
    public TenantIdModel.Result addSalesOrderAndRebateUseRuleField(TenantIdModel.Arg arg, ServiceContext serviceContext) {
        TenantIdModel.Result result = new TenantIdModel.Result();
        List<String> tenantIds = arg.getTenantIds();
        List<String> failedTenantIds = Lists.newArrayList();
        for (String tenantId : tenantIds) {
            CustomerAccountType.CustomerAccountEnableSwitchStatus customerAccountEnableSwitchStatus = customerAccountConfigManager.getStatus(tenantId);
            if (customerAccountEnableSwitchStatus != CustomerAccountType.CustomerAccountEnableSwitchStatus.ENABLE) {
                continue;
            }
            User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
            IObjectDescribe rebateIncomeObjectDescribe = serviceFacade.findObject(tenantId, RebateIncomeDetailConstants.API_NAME);
            try {
                IFieldDescribe fieldDescribe = rebateIncomeObjectDescribe.getFieldDescribe(RebateIncomeDetailConstants.Field.SalesOrder.apiName);
                if (fieldDescribe == null) {
                    IFieldDescribe salesOrderField = getSalesOrderField();
                    //修改为replace
                    rebateIncomeObjectDescribe.addFieldDescribe(salesOrderField);
                    rebateIncomeObjectDescribe = objectDescribeService.replace(rebateIncomeObjectDescribe, false);
                    //rebateIncomeObjectDescribe = objectDescribeService.addCustomFieldDescribe(rebateIncomeObjectDescribe, Lists.newArrayList(salesOrderField));
                    log.info("addSalesOrderField:{}", rebateIncomeObjectDescribe.toJsonString());
                    ILayout layout = serviceFacade.findLayoutByApiName(user, RebateIncomeDetailConstants.DEFAULT_LAYOUT_API_NAME, rebateIncomeObjectDescribe.getApiName());
                    LayoutExt layoutExt = LayoutExt.of(layout);
                    FieldLayoutPojo fieldLayoutPojo = new FieldLayoutPojo();
                    fieldLayoutPojo.setRenderType(SystemConstants.RenderType.ObjectReference.renderType);
                    fieldLayoutPojo.setRequired(false);
                    fieldLayoutPojo.setReadonly(false);
                    layoutExt.addField(salesOrderField, fieldLayoutPojo);
                    layout = serviceFacade.updateLayout(user, layout);
                    log.info("updateLayout:{}", layout.toJsonString());
                    //返利收入类型 增加订单返利选项
                    addSelectOptionInRebateIncomeType(user);
                    //返利支出 新增返利使用规则字段
                    addRebateUseRuleFieldInRebateOutcome(user);
                }
            } catch (MetadataServiceException e) {
                failedTenantIds.add(tenantId);
                log.warn("", e);
            }
        }
        result.setTenantIds(failedTenantIds);
        return result;
    }

    private IFieldDescribe getSalesOrderField() {
        ObjectReferenceFieldDescribe orderObjectReferenceFieldDescribe = ObjectReferenceFieldDescribeBuilder.builder().apiName(RebateIncomeDetailConstants.Field.SalesOrder.apiName).label(RebateIncomeDetailConstants.Field.SalesOrder.label).required(false).targetApiName(SystemConstants.SalesOrderApiName).targetRelatedListName(RebateIncomeDetailConstants.Field.SalesOrder.targetRelatedListName).targetRelatedListLabel(RebateIncomeDetailConstants.Field.SalesOrder.targetRelatedListLabel).build();
        return orderObjectReferenceFieldDescribe;
    }

    @Data
    public static class TenantIdModel {
        private int offset = 0;
        private int limit = 20;

        @Data
        public static class Arg {
            private List<String> tenantIds;
            private Boolean ignoreDhtAppEnable;
            private Boolean ignoreOldCustomerAccount;
            private Boolean ignoreUpdateDescribe;

            private String objectApiName;
            private String fieldName;
            private int maxLength;
        }

        @Data
        public static class Result {
            private List<String> tenantIds;
        }
    }

    @Override
    public CurlModel.RevisePrepayFieldResult revisePrepayField(ServiceContext serviceContext, CurlModel.RevisePrepayFieldArg revisePrepayFieldArg) {
        if (revisePrepayFieldArg == null) {
            throw new ValidateException("参数不能为空");
        }
        if (StringUtils.isBlank(revisePrepayFieldArg.getApiName()) || StringUtils.isBlank(revisePrepayFieldArg.getFieldApiName())) {
            throw new ValidateException("apiName或者fieldApiName参数不能为空");
        }
        String tenantId = serviceContext.getRequestContext().getTenantId();
        CurlModel.RevisePrepayFieldResult revisePrepayFieldResult = new CurlModel.RevisePrepayFieldResult();
        log.warn("Curl revisePrepayField tenantId:{}", tenantId);
        try {
            IObjectDescribe objectDescribe = serviceFacade.findObject(tenantId, revisePrepayFieldArg.getApiName());
            IFieldDescribe iFieldDescribe = objectDescribe.getFieldDescribe(revisePrepayFieldArg.getFieldApiName());
            Boolean isRequired = iFieldDescribe.isRequired();
            if (!isRequired.equals(revisePrepayFieldArg.getNeedBool())) {
                iFieldDescribe.setRequired(revisePrepayFieldArg.getNeedBool());
                IObjectDescribe objectDescribe1 = objectDescribeService.updateFieldDescribe(objectDescribe, Lists.newArrayList(iFieldDescribe));
                if (objectDescribe1 == null) {
                    log.warn("updateCustomFieldDescribe return null");
                }
            }
            revisePrepayFieldResult.setSuccess(true);
        } catch (Exception e) {
            log.warn("curl revisePrepayField exception");
            revisePrepayFieldResult.setSuccess(false);
        }
        return revisePrepayFieldResult;
    }

    //清理预存款、返利、归零客户账户、清除流水--针对某个企业定制清除,不具有通用性
    @Override
    public CurlModel.RevisePrepayFieldResult clearTenantDataOfCustomerAccount(ServiceContext serviceContext) {
        String tenantId = serviceContext.getTenantId();
        CurlModel.RevisePrepayFieldResult revisePrepayFieldResult = new CurlModel.RevisePrepayFieldResult();
        revisePrepayFieldResult.setSuccess(true);

        //delete rebate
        try {
            List<IObjectData> rebateOutcomeObjectData = commonManager.queryByFieldList(serviceContext.getUser(), RebateOutcomeDetailConstants.API_NAME, "tenant_id", Lists.newArrayList(tenantId), 0, 1000).getData();
            if (CollectionUtils.isNotEmpty(rebateOutcomeObjectData)) {
                List<IObjectData> rebateOutComeInvalidObjects = serviceFacade.bulkInvalid(rebateOutcomeObjectData, serviceContext.getUser());
                if (CollectionUtils.isNotEmpty(rebateOutComeInvalidObjects)) {
                    serviceFacade.bulkDelete(rebateOutComeInvalidObjects, serviceContext.getUser());
                }
            }
            log.info("curl clearTenantDataOfCustomerAccount rebateOutcomeObjectData end");
        } catch (Exception e) {
            log.warn("curl clearTenantDataOfCustomerAccount delete rebateOutcomeData exception.");
            revisePrepayFieldResult.setSuccess(false);
        }

        try {
            List<IObjectData> rebateObjectDatas = commonManager.queryByFieldList(serviceContext.getUser(), RebateIncomeDetailConstants.API_NAME, "tenant_id", Lists.newArrayList(tenantId), 0, 1000).getData();
            if (CollectionUtils.isNotEmpty(rebateObjectDatas)) {
                List<IObjectData> rebateInvalidObjects = serviceFacade.bulkInvalid(rebateObjectDatas, serviceContext.getUser());
                if (CollectionUtils.isNotEmpty(rebateInvalidObjects)) {
                    serviceFacade.bulkDelete(rebateInvalidObjects, serviceContext.getUser());
                }
            }
            log.info("curl clearTenantDataOfCustomerAccount rebateIncomeObjectData end");
        } catch (Exception e) {
            log.warn("curl clearTenantDataOfCustomerAccount delete rebateIncomeData exception.error info:{}", e);
            revisePrepayFieldResult.setSuccess(false);
        }

        //delete prepay
        try {
            List<IObjectData> prepayObjectDatas = commonManager.queryByFieldList(serviceContext.getUser(), PrepayDetailConstants.API_NAME, "tenant_id", Lists.newArrayList(tenantId), 0, 1000).getData();
            if (CollectionUtils.isNotEmpty(prepayObjectDatas)) {
                List<IObjectData> prepayInvalidObjects = serviceFacade.bulkInvalid(prepayObjectDatas, serviceContext.getUser());
                if (CollectionUtils.isNotEmpty(prepayInvalidObjects)) {
                    serviceFacade.bulkDelete(prepayInvalidObjects, serviceContext.getUser());
                }
            }
            log.info("curl clearTenantDataOfCustomerAccount prepay end");
        } catch (Exception e) {
            log.warn("curl clearTenantDataOfCustomerAccount delete prepayData exception.error info:{}", e);
            revisePrepayFieldResult.setSuccess(false);
        }

        int offset = 0;
        int limit = 500;
        try {
            while (true) {
                List<IObjectData> customerAccountData = commonManager.queryByFieldList(serviceContext.getUser(), CustomerAccountConstants.API_NAME, "tenant_id", Lists.newArrayList(tenantId), offset, limit).getData();
                int size = customerAccountData.size();
                log.info("curl clearTenantDataOfCustomerAccount offset:{},limit:{},size:{}", offset, limit, size);
                Iterator iterator = customerAccountData.iterator();
                while (iterator.hasNext()) {
                    IObjectData prepayData = (IObjectData) iterator.next();
                    BigDecimal prepayBalance = prepayData.get(CustomerAccountConstants.Field.PrepayBalance.apiName, BigDecimal.class);
                    BigDecimal prepayAvailBalance = prepayData.get(CustomerAccountConstants.Field.PrepayAvailableBalance.apiName, BigDecimal.class);
                    BigDecimal prepayLockBalance = prepayData.get(CustomerAccountConstants.Field.PrepayLockedBalance.apiName, BigDecimal.class);
                    BigDecimal rebateBalance = prepayData.get(CustomerAccountConstants.Field.RebateBalance.apiName, BigDecimal.class);
                    BigDecimal rebateAvailableBalance = prepayData.get(CustomerAccountConstants.Field.RebateAvailableBalance.apiName, BigDecimal.class);
                    BigDecimal rebateLockedBalance = prepayData.get(CustomerAccountConstants.Field.RebateLockedBalance.apiName, BigDecimal.class);
                    BigDecimal credit = prepayData.get(CustomerAccountConstants.Field.CreditQuota.apiName, BigDecimal.class);
                    BigDecimal zero = BigDecimal.valueOf(0);

                    if (prepayBalance.compareTo(zero) != 0 || prepayAvailBalance.compareTo(zero) != 0 || prepayLockBalance.compareTo(zero) != 0 || rebateAvailableBalance.compareTo(zero) != 0 || rebateBalance.compareTo(zero) != 0 || rebateLockedBalance.compareTo(zero) != 0 || credit.compareTo(zero) != 0) {
                        prepayData.set(CustomerAccountConstants.Field.PrepayBalance.apiName, zero);
                        prepayData.set(CustomerAccountConstants.Field.PrepayAvailableBalance.apiName, zero);
                        prepayData.set(CustomerAccountConstants.Field.PrepayLockedBalance.apiName, zero);
                        prepayData.set(CustomerAccountConstants.Field.RebateBalance.apiName, zero);
                        prepayData.set(CustomerAccountConstants.Field.RebateAvailableBalance.apiName, zero);
                        prepayData.set(CustomerAccountConstants.Field.RebateLockedBalance.apiName, zero);
                        prepayData.set(CustomerAccountConstants.Field.CreditQuota.apiName, zero);
                    } else {
                        iterator.remove();
                    }
                }
                if (CollectionUtils.isNotEmpty(customerAccountData)) {
                    log.info("curl clearTenantDataOfCustomerAccount update size :{}", customerAccountData.size());
                    serviceFacade.batchUpdate(customerAccountData, serviceContext.getUser());
                }
                if (size < 500) {
                    break;
                }

                offset += limit;

                if (offset > 8000) {
                    break;
                }
            }

            log.info("curl clearTenantDataOfCustomerAccount customerAccount end.");
        } catch (Exception e) {
            log.warn("curl clearTenantDataOfCustomerAccount zero customerAccount exception.error info:{}", e);
            revisePrepayFieldResult.setSuccess(false);
        }
        try {
            customerAccountBillManager.delete(tenantId);
        } catch (Exception e) {
            log.warn("curl clearTenantDataOfCustomerAccount clear bill exception,error info:{}", e);
            revisePrepayFieldResult.setSuccess(false);
        }
        return revisePrepayFieldResult;
    }

    @Override
    public TenantIdModel.Result enableAssignPrepayRecordType(ServiceContext serviceContext, TenantIdModel.Arg arg) {
        TenantIdModel.Result result = new TenantIdModel.Result();
        List<String> tenantIds = arg.getTenantIds();
        if (CollectionUtils.isEmpty(tenantIds)) {
            return result;
        }
        List<String> failTenantIds = Lists.newArrayList();
        for (String tenantId : tenantIds) {
            try {
                IObjectDescribe prepayDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, PrepayDetailConstants.API_NAME);
                Map<String, Object> config = Maps.newHashMap();
                Map<String, Object> assignMap = Maps.newHashMap();
                assignMap.put("assign", 1);
                config.put("record_type", assignMap);
                Map map = prepayDescribe.getConfig();
                if (map == null) {
                    map = new HashMap();
                }
                map.putAll(config);
                prepayDescribe.setConfig(map);

                objectDescribeService.updateDescribe(prepayDescribe);
            } catch (Exception e) {
                log.warn("enableAssignPrepayRecordType,tenantId:{}", tenantId, e);
                failTenantIds.add(tenantId);
            }
        }
        result.setTenantIds(failTenantIds);
        return result;
    }

    //新增字段
    @Override
    public TenantIdModel.Result enableAddField(ServiceContext serviceContext, TenantIdModel.Arg arg) {
        List<String> tenantIds = arg.getTenantIds();
        TenantIdModel.Result result = new TenantIdModel.Result();
        if (CollectionUtils.isEmpty(tenantIds)) {
            return result;
        }
        List<String> failTenantIds = Lists.newArrayList();
        for (String tenantId : tenantIds) {
            try {
                List<IObjectDescribe> describeList = objectDescribeService.findDescribeListByApiNames(tenantId, Lists.newArrayList(PrepayDetailConstants.API_NAME, CustomerAccountConstants.API_NAME, RebateIncomeDetailConstants.API_NAME));
                Map<String, Object> configMap = Maps.newHashMap();
                Map<String, Object> addMap = Maps.newHashMap();
                addMap.put("add", 1);
                configMap.put("fields", addMap);
                for (IObjectDescribe describe : describeList) {
                    Map map = describe.getConfig();
                    if (map == null) {
                        map = new HashMap();
                    }
                    map.putAll(configMap);
                    describe.setConfig(map);
                    objectDescribeService.updateDescribe(describe);
                }
            } catch (Exception e) {
                log.warn("enableAddField exception,tenantId:{}", tenantId, e);
                failTenantIds.add(tenantId);
            }
        }
        result.setTenantIds(failTenantIds);
        return result;
    }

    @Override
    public Boolean reviseCustomerAccountPrepayBalance(ServiceContext serviceContext, CurlModel.ReviseCustomerAccountAmountArg arg) {
        String tenantId = serviceContext.getTenantId();
        IObjectData objectData = serviceFacade.findObjectData(tenantId, arg.getCustomerAccountId(), serviceFacade.findObject(tenantId, CustomerAccountConstants.API_NAME));
        BigDecimal prepayBalance = objectData.get(CustomerAccountConstants.Field.PrepayBalance.apiName, BigDecimal.class);
        BigDecimal prepayAvailable = objectData.get(CustomerAccountConstants.Field.PrepayAvailableBalance.apiName, BigDecimal.class);
        BigDecimal amount = BigDecimal.valueOf(arg.getPrepayAmount());
        prepayAvailable = prepayAvailable.add(amount);
        prepayBalance = prepayBalance.add(amount);
        objectData.set(CustomerAccountConstants.Field.PrepayAvailableBalance.apiName, prepayAvailable);
        objectData.set(CustomerAccountConstants.Field.PrepayBalance.apiName, prepayBalance);
        serviceFacade.updateObjectData(serviceContext.getUser(), objectData, false);
        customerAccountBillManager.addCustomerAccountBillAccordPrepay(arg.getCustomerAccountId(), arg.getPrepayId(), arg.getPrepayAmount(), arg.getPrepayAmount(), serviceContext.getTenantId(), arg.getInfo());
        return true;
    }

    @Override
    public TenantIdModel.Result prepayAssingLayout(ServiceContext serviceContext, TenantIdModel.Arg arg) {
        List<String> tenantIds = arg.getTenantIds();
        TenantIdModel.Result result = new TenantIdModel.Result();
        if (CollectionUtils.isEmpty(tenantIds)) {
            return result;
        }
        List<String> failTenantIds = Lists.newArrayList();
        for (String tenantId : tenantIds) {
            try {
                List<IObjectDescribe> describeList = objectDescribeService.findDescribeListByApiNames(tenantId, Lists.newArrayList(PrepayDetailConstants.API_NAME));
                for (IObjectDescribe objectDescribe : describeList) {
                    if (!PrepayDetailConstants.API_NAME.equals(objectDescribe.getApiName())) {
                        continue;
                    }
                    Map config = objectDescribe.getConfig();
                    if (config == null) {
                        config = Maps.newHashMap();
                    }
                    Map<String, Object> layoutAssignConfig = Maps.newHashMap();
                    layoutAssignConfig.put("assign", 1);
                    config.put("layout", layoutAssignConfig);
                    objectDescribe.setConfig(config);
                    objectDescribeService.updateDescribe(objectDescribe);
                }
            } catch (Exception e) {
                failTenantIds.add(tenantId);
                log.warn("", e);
            }
        }
        result.setTenantIds(failTenantIds);
        return result;
    }

    @Override
    public CurlModel.TenantIds getAllTenantIdsOfCustomerAccountEnabled(ServiceContext serviceContext) {
        List<String> tenantIds = customerAccountConfigManager.list(CustomerAccountType.CustomerAccountEnableSwitchStatus.ENABLE.getValue());
        CurlModel.TenantIds result = new CurlModel.TenantIds();
        result.setTenantIds(tenantIds);
        return result;
    }

    @Override
    public CurlModel.TenantIds initCreditFileDescribeAndData(ServiceContext serviceContext, CurlModel.TenantIds tenantIds) {
        if (CollectionUtils.isEmpty(tenantIds.getTenantIds())) {
            throw new ValidateException("tenantId is null");
        }
        List<String> failTenantId = new ArrayList<>();
        String fsUserId = serviceContext.getUser().getUserId();
        final Long deadLine = DateUtil.getDateStartTime(2020, 12, 31); //2020-12-31
        for (String tenantId : tenantIds.getTenantIds()) {
            try {
                CustomerAccountConfigModel.CustomerAccountConfig customerAccountConfig;
                try {
                    customerAccountConfig = customerAccountConfigManager.getConfigByTenantId(tenantId);
                } catch (Exception e) {
                    log.error("initCreditFile search customerAccountConfig error, tenantId:{}", tenantId, e);
                    throw new RuntimeException(e);
                }
                if (customerAccountConfig != null && customerAccountConfig.getStatus() == CustomerAccountType.CustomerAccountEnableSwitchStatus.ENABLE.getValue()) {
                    User user = User.builder().tenantId(tenantId).userId("-10000").build();
                    try {
                        initService.initCreditFile(user);
                    } catch (Exception e) {
                        log.error("initCreditFile failed tenantId:{}", tenantId, e);
                        throw new RuntimeException(e);
                    }
                    try {
                        IObjectDescribe objectDescribe = serviceFacade.findObject(tenantId, CustomerAccountConstants.API_NAME);
                        addCustomerAccountFieldDes(user, objectDescribe);
                    } catch (Exception e) {
                        log.error("addCustomerAccountFields failed tenantId:{}", tenantId, e);
                        throw new RuntimeException(e);
                    }

                    Long today = DateUtil.getMillisecondsOfDayStart(new Date().getTime());
                    int offset = 0;
                    int limit = 500;
                    IObjectDescribe creditObjectDescribe;
                    try {
                        creditObjectDescribe = serviceFacade.findObject(tenantId, CreditFileConstants.API_NAME);
                    } catch (Exception e) {
                        log.error("客户账户配置已开启，但是查不到信用档案描述,tenantId:{}", tenantId, e);
                        throw new RuntimeException(e);
                    }
                    try {
                        initCreditData(fsUserId, deadLine, tenantId, user, today, offset, limit, creditObjectDescribe);
                    } catch (Exception e) {
                        log.error("transferCreditData,tenantId={}", tenantId, e);
                        throw new RuntimeException(e);
                    }
                }
            } catch (Exception e) {
                failTenantId.add(tenantId);
            }
        }
        CurlModel.TenantIds result = new CurlModel.TenantIds();
        result.setTenantIds(failTenantId);
        return result;
    }

    @Deprecated
    private void initCreditData(String fsUserId, Long deadLine, String tenantId, User user, Long today, int offset, int limit, IObjectDescribe creditObjectDescribe) {
        for (; ; ) {
            List<IObjectData> creditFileDatas = Lists.newArrayList();
            List<IFilter> filters = new ArrayList<>();
            List<OrderBy> orders = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, SystemConstants.Field.TennantID.apiName, tenantId);
            SearchUtil.fillFilterGT(filters, CustomerAccountConstants.Field.CreditQuota.apiName, 0);
            SearchUtil.fillOrderBy(orders, SystemConstants.Field.Id.apiName, Boolean.TRUE);
            List<IObjectData> customerAccountDatas = commonManager.queryByFieldFilterList(user, CustomerAccountConstants.API_NAME, filters, orders, offset, limit);
            if (CollectionUtils.isNotEmpty(customerAccountDatas)) {
                for (IObjectData objectData : customerAccountDatas) {
                    String customerId = objectData.get(CustomerAccountConstants.Field.Customer.apiName, String.class);
                    List<IObjectData> creditObj = commonManager.queryByField(user, CreditFileConstants.API_NAME, CreditFileConstants.Field.Customer.apiName, customerId, 0, 1).getData();
                    if (CollectionUtils.isEmpty(creditObj)) {
                        Object ownerId = objectData.get(SystemConstants.Field.Owner.apiName);
                        BigDecimal creditQuota = ObjectDataUtil.getBigDecimal(objectData, CustomerAccountConstants.Field.CreditQuota.apiName);
                        IObjectData creditFileData = setObjectData(fsUserId, today, deadLine, tenantId, customerId, creditQuota, creditObjectDescribe, ownerId);
                        creditFileDatas.add(creditFileData);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(creditFileDatas)) {
                serviceFacade.bulkSaveObjectData(creditFileDatas, user);
            }
            if (customerAccountDatas != null && customerAccountDatas.size() >= 500) {
                offset += 500;
                limit = 500;
            } else {
                break;
            }
        }
    }

    private void addCustomerAccountFieldDes(User user, IObjectDescribe objectDescribe) throws MetadataServiceException {
        IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(CustomerAccountConstants.Field.CreditTemporaryQuota.apiName);
        List<IFieldDescribe> fieldDescribeList = Lists.newArrayList();
        CurrencyFieldDescribe creditTemporaryQuotaCurrencyFieldDescribe = CurrencyFieldDescribeBuilder.builder().apiName(CustomerAccountConstants.Field.CreditTemporaryQuota.apiName).label(CustomerAccountConstants.Field.CreditTemporaryQuota.label).required(false).maxLength(14).length(12).decimalPlaces(2).currencyUnit("￥").roundMode(4).build();
        fieldDescribeList.add(creditTemporaryQuotaCurrencyFieldDescribe);
        CurrencyFieldDescribe usedCreditQuotaCurrencyFieldDescribe = CurrencyFieldDescribeBuilder.builder().apiName(CustomerAccountConstants.Field.UsedCreditQuota.apiName).label(CustomerAccountConstants.Field.UsedCreditQuota.label).required(false).maxLength(14).length(12).decimalPlaces(2).currencyUnit("￥").roundMode(4).build();
        fieldDescribeList.add(usedCreditQuotaCurrencyFieldDescribe);

        CurrencyFieldDescribe creditAvailableQuotaCurrencyFieldDescribe = CurrencyFieldDescribeBuilder.builder().apiName(CustomerAccountConstants.Field.CreditAvailableQuota.apiName).label(CustomerAccountConstants.Field.CreditAvailableQuota.label).required(false).maxLength(14).length(12).decimalPlaces(2).currencyUnit("￥").roundMode(4).build();
        fieldDescribeList.add(creditAvailableQuotaCurrencyFieldDescribe);

        NumberFieldDescribe availableStockNumberFieldDescribe = NumberFieldDescribeBuilder.builder().apiName(CreditFileConstants.Field.CreditPeriod.apiName).label(CreditFileConstants.Field.CreditPeriod.label).length(12).maxLength(14).required(false).roundMode(4).decimalPalces(0).build();
        fieldDescribeList.add(availableStockNumberFieldDescribe);

        ILayout layout = serviceFacade.findLayoutByApiName(user, CustomerAccountConstants.DETAIL_LAYOUT_API_NAME, objectDescribe.getApiName());
        LayoutExt layoutExt = LayoutExt.of(layout);
        Optional<IFormField> formField = layoutExt.getField(CustomerAccountConstants.Field.CreditTemporaryQuota.apiName);
        FieldLayoutPojo fieldLayoutPojo = new FieldLayoutPojo();
        fieldLayoutPojo.setRenderType(SystemConstants.RenderType.Currency.renderType);
        fieldLayoutPojo.setReadonly(true);
        fieldLayoutPojo.setRequired(false);
        fieldLayoutPojo.setApiName(CustomerAccountConstants.Field.CreditTemporaryQuota.apiName);
        layoutExt.addField(creditTemporaryQuotaCurrencyFieldDescribe, fieldLayoutPojo);
        FieldLayoutPojo fieldLayoutPojo1 = new FieldLayoutPojo();
        fieldLayoutPojo1.setRenderType(SystemConstants.RenderType.Currency.renderType);
        fieldLayoutPojo1.setReadonly(true);
        fieldLayoutPojo1.setRequired(false);
        fieldLayoutPojo1.setApiName(CustomerAccountConstants.Field.CreditAvailableQuota.apiName);
        layoutExt.addField(creditAvailableQuotaCurrencyFieldDescribe, fieldLayoutPojo1);
        FieldLayoutPojo fieldLayoutPojo2 = new FieldLayoutPojo();
        fieldLayoutPojo2.setRenderType(SystemConstants.RenderType.Currency.renderType);
        fieldLayoutPojo2.setReadonly(true);
        fieldLayoutPojo2.setRequired(false);
        fieldLayoutPojo2.setApiName(CustomerAccountConstants.Field.UsedCreditQuota.apiName);
        layoutExt.addField(usedCreditQuotaCurrencyFieldDescribe, fieldLayoutPojo2);
        FieldLayoutPojo fieldLayoutPojo3 = new FieldLayoutPojo();
        fieldLayoutPojo3.setRenderType(SystemConstants.RenderType.Number.renderType);
        fieldLayoutPojo3.setReadonly(true);
        fieldLayoutPojo3.setRequired(false);
        fieldLayoutPojo3.setApiName(CustomerAccountConstants.Field.CreditPeriod.apiName);
        layoutExt.addField(availableStockNumberFieldDescribe, fieldLayoutPojo3);

        if (fieldDescribe == null) {
            IObjectDescribe newObj = objectDescribeService.addCustomFieldDescribe(objectDescribe, fieldDescribeList);
            log.debug("updated describe:{}", newObj.toJsonString());
        }
        if (!formField.isPresent()) {
            layout = serviceFacade.updateLayout(user, layout);
            log.debug("updated layout:{}", layout.toJsonString());
        }
    }

    private Optional<IFormField> getiFormField(CurrencyFieldDescribe creditTemporaryQuotaCurrencyFieldDescribe, CurrencyFieldDescribe usedCreditQuotaCurrencyFieldDescribe, CurrencyFieldDescribe creditAvailableQuotaCurrencyFieldDescribe, NumberFieldDescribe availableStockNumberFieldDescribe, ILayout layout) {
        LayoutExt layoutExt = LayoutExt.of(layout);
        Optional<IFormField> formField = layoutExt.getField(CustomerAccountConstants.Field.CreditTemporaryQuota.apiName);
        FieldLayoutPojo fieldLayoutPojo = new FieldLayoutPojo();
        fieldLayoutPojo.setRenderType(SystemConstants.RenderType.Currency.renderType);
        fieldLayoutPojo.setReadonly(true);
        fieldLayoutPojo.setRequired(false);
        fieldLayoutPojo.setApiName(CustomerAccountConstants.Field.CreditTemporaryQuota.apiName);
        layoutExt.addField(creditTemporaryQuotaCurrencyFieldDescribe, fieldLayoutPojo);
        FieldLayoutPojo fieldLayoutPojo1 = new FieldLayoutPojo();
        fieldLayoutPojo1.setRenderType(SystemConstants.RenderType.Currency.renderType);
        fieldLayoutPojo1.setReadonly(true);
        fieldLayoutPojo1.setRequired(false);
        fieldLayoutPojo1.setApiName(CustomerAccountConstants.Field.CreditAvailableQuota.apiName);
        layoutExt.addField(creditAvailableQuotaCurrencyFieldDescribe, fieldLayoutPojo1);
        FieldLayoutPojo fieldLayoutPojo2 = new FieldLayoutPojo();
        fieldLayoutPojo2.setRenderType(SystemConstants.RenderType.Currency.renderType);
        fieldLayoutPojo2.setReadonly(true);
        fieldLayoutPojo2.setRequired(false);
        fieldLayoutPojo2.setApiName(CustomerAccountConstants.Field.UsedCreditQuota.apiName);
        layoutExt.addField(usedCreditQuotaCurrencyFieldDescribe, fieldLayoutPojo2);
        FieldLayoutPojo fieldLayoutPojo3 = new FieldLayoutPojo();
        fieldLayoutPojo3.setRenderType(SystemConstants.RenderType.Number.renderType);
        fieldLayoutPojo3.setReadonly(true);
        fieldLayoutPojo3.setRequired(false);
        fieldLayoutPojo3.setApiName(CustomerAccountConstants.Field.CreditPeriod.apiName);
        layoutExt.addField(availableStockNumberFieldDescribe, fieldLayoutPojo3);
        return formField;
    }

    //632信用档案刷库
    @Override
    public CurlModel.TenantIds initCreditFile(ServiceContext serviceContext, CurlModel.TenantIds tenantIds) {
        if (CollectionUtils.isEmpty(tenantIds.getTenantIds())) {
            throw new ValidateException("tenantId is null");
        }
        List<String> failTenantId = new ArrayList<>();
        tenantIds.getTenantIds().forEach(o -> {
            CustomerAccountConfigModel.CustomerAccountConfig customerAccountConfig;
            try {
                try {
                    customerAccountConfig = customerAccountConfigManager.getConfigByTenantId(o);
                } catch (Exception e) {
                    log.error("initCreditFile search customerAccountConfig error, tenantId:{}", o, e);
                    throw new RuntimeException(e);
                }
                if (customerAccountConfig != null && customerAccountConfig.getStatus() == CustomerAccountType.CustomerAccountEnableSwitchStatus.ENABLE.getValue()) {//客户账户开启状态才能刷信用对象
                    User user = User.builder().tenantId(o).userId("-10000").build();
                    try {
                        initService.initCreditFile(user);
                    } catch (Exception e) {
                        log.error("initCreditFile failed tenantId:{}", o, e);
                        throw new RuntimeException(e);
                    }
                }
            } catch (RuntimeException e) {
                failTenantId.add(o);
            }
        });
        CurlModel.TenantIds result = new CurlModel.TenantIds();
        result.setTenantIds(failTenantId);
        return result;
    }

    //632客户账户刷库
    @Override
    public CurlModel.TenantIds addCustomerAccountFields(ServiceContext serviceContext, CurlModel.TenantIds tenantIds) {
        if (CollectionUtils.isEmpty(tenantIds.getTenantIds())) {
            throw new ValidateException("tenantId is null");
        }
        List<String> failTenantId = new ArrayList<>();
        tenantIds.getTenantIds().forEach(o -> {
            CustomerAccountConfigModel.CustomerAccountConfig customerAccountConfig;
            try {
                try {
                    customerAccountConfig = customerAccountConfigManager.getConfigByTenantId(o);
                } catch (Exception e) {
                    log.error("addCustomerAccountFields search customerAccountConfig error, tenantId:{}", o, e);
                    throw new RuntimeException(e);
                }
                if (customerAccountConfig != null && customerAccountConfig.getStatus() == CustomerAccountType.CustomerAccountEnableSwitchStatus.ENABLE.getValue()) {//客户账户开启状态才能刷信用对象
                    User user = User.builder().tenantId(o).userId("-10000").build();
                    try {
                        IObjectDescribe objectDescribe = serviceFacade.findObject(o, CustomerAccountConstants.API_NAME);
                        IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(CustomerAccountConstants.Field.CreditTemporaryQuota.apiName);
                        List<IFieldDescribe> fieldDescribeList = Lists.newArrayList();
                        CurrencyFieldDescribe creditTemporaryQuotaCurrencyFieldDescribe = CurrencyFieldDescribeBuilder.builder().apiName(CustomerAccountConstants.Field.CreditTemporaryQuota.apiName).label(CustomerAccountConstants.Field.CreditTemporaryQuota.label).required(false).maxLength(14).length(12).decimalPlaces(2).currencyUnit("￥").roundMode(4).build();
                        fieldDescribeList.add(creditTemporaryQuotaCurrencyFieldDescribe);
                        CurrencyFieldDescribe usedCreditQuotaCurrencyFieldDescribe = CurrencyFieldDescribeBuilder.builder().apiName(CustomerAccountConstants.Field.UsedCreditQuota.apiName).label(CustomerAccountConstants.Field.UsedCreditQuota.label).required(false).maxLength(14).length(12).decimalPlaces(2).currencyUnit("￥").roundMode(4).build();
                        fieldDescribeList.add(usedCreditQuotaCurrencyFieldDescribe);

                        CurrencyFieldDescribe creditAvailableQuotaCurrencyFieldDescribe = CurrencyFieldDescribeBuilder.builder().apiName(CustomerAccountConstants.Field.CreditAvailableQuota.apiName).label(CustomerAccountConstants.Field.CreditAvailableQuota.label).required(false).maxLength(14).length(12).decimalPlaces(2).currencyUnit("￥").roundMode(4).build();
                        fieldDescribeList.add(creditAvailableQuotaCurrencyFieldDescribe);

                        NumberFieldDescribe availableStockNumberFieldDescribe = NumberFieldDescribeBuilder.builder().apiName(CreditFileConstants.Field.CreditPeriod.apiName).label(CreditFileConstants.Field.CreditPeriod.label).length(12).maxLength(14).required(false).roundMode(4).decimalPalces(0).build();
                        fieldDescribeList.add(availableStockNumberFieldDescribe);

                        ILayout layout = serviceFacade.findLayoutByApiName(user, CustomerAccountConstants.DETAIL_LAYOUT_API_NAME, objectDescribe.getApiName());
                        Optional<IFormField> formField = getiFormField(creditTemporaryQuotaCurrencyFieldDescribe, usedCreditQuotaCurrencyFieldDescribe, creditAvailableQuotaCurrencyFieldDescribe, availableStockNumberFieldDescribe, layout);

                        if (fieldDescribe == null) {
                            IObjectDescribe newObj = objectDescribeService.addCustomFieldDescribe(objectDescribe, fieldDescribeList);
                            log.info("updated describe:{}", newObj.toJsonString());
                        }
                        if (!formField.isPresent()) {
                            layout = serviceFacade.updateLayout(user, layout);
                            log.info("updated layout:{}", layout.toJsonString());
                        }
                    } catch (Exception e) {
                        log.error("addCustomerAccountFields failed tenantId:{}", o, e);
                        throw new RuntimeException(e);
                    }
                }
            } catch (RuntimeException e) {
                failTenantId.add(o);
            }
        });
        CurlModel.TenantIds result = new CurlModel.TenantIds();
        result.setTenantIds(failTenantId);
        return result;
    }

    //632信用数据刷库
    @Override
    public CurlModel.TenantIds transferCreditData(ServiceContext serviceContext, CurlModel.TenantIds tenantIds) {
        List<String> list = tenantIds.getTenantIds();
        if (CollectionUtils.isEmpty(list)) {
            throw new ValidateException("param error");
        }
        List<String> failTenantId = new ArrayList<>();
        String fsUserId = serviceContext.getUser().getUserId();
        Long today = DateUtil.getMillisecondsOfDayStart(new Date().getTime());
        Long deadLine = DateUtil.getDateStartTime(2020, 12, 31); //2020-12-31
        for (String tenantId : tenantIds.getTenantIds()) {
            int offset = 0;
            int limit = 500;
            User user = User.builder().tenantId(tenantId).userId("-10000").build();
            CustomerAccountConfigModel.CustomerAccountConfig customerAccountConfig;
            try {
                try {
                    customerAccountConfig = customerAccountConfigManager.getConfigByTenantId(tenantId);
                } catch (Exception e) {
                    log.error("transferCreditData search customerAccountConfig error, tenantId:{}", tenantId, e);
                    throw new RuntimeException(e);
                }
                if (customerAccountConfig != null && customerAccountConfig.getStatus() == CustomerAccountType.CustomerAccountEnableSwitchStatus.ENABLE.getValue()) {
                    IObjectDescribe objectDescribe = null;
                    try {
                        objectDescribe = serviceFacade.findObject(tenantId, CreditFileConstants.API_NAME);
                    } catch (Exception e) {
                        log.error("客户账户配置已开启，但是查不到描述", e);
                        throw new RuntimeException(e);
                    }
                    initCreditData(fsUserId, deadLine, tenantId, user, today, offset, limit, objectDescribe);
                }
            } catch (Exception e) {
                failTenantId.add(tenantId);
                log.error("transferCreditData,tenantId={}", tenantId, e);
            }
        }
        CurlModel.TenantIds result = new CurlModel.TenantIds();
        result.setTenantIds(failTenantId);
        return result;
    }

    private IObjectData setObjectData(String fsUserId, Long today, Long deadLine, String tenantId, String customerId, BigDecimal creditQuota, IObjectDescribe objectDescribe, Object ownerIds) {
        IObjectData creditFileData = new ObjectData();
        creditFileData.setCreatedBy(fsUserId);
        creditFileData.setDeleted(false);
        creditFileData.setTenantId(tenantId);
        creditFileData.setRecordType("default__c");
        creditFileData.set(CreditFileConstants.Field.CreditType.apiName, CreditTypeEnum.OfficialCredit.getValue());
        creditFileData.set(CreditFileConstants.Field.CreditQuota.apiName, creditQuota);
        creditFileData.set(CreditFileConstants.Field.StartTime.apiName, today);
        creditFileData.set(CreditFileConstants.Field.EndTime.apiName, deadLine);
        creditFileData.set(CreditFileConstants.Field.CreditPeriod.apiName, 0);
        creditFileData.set(CreditFileConstants.Field.Customer.apiName, customerId);
        creditFileData.set(SystemConstants.Field.LockUser.apiName, ownerIds);
        creditFileData.set(SystemConstants.Field.Owner.apiName, ownerIds);
        creditFileData.set(SystemConstants.Field.LockStatus.apiName, "0");
        creditFileData.set(SystemConstants.Field.LifeStatus.apiName, SystemConstants.LifeStatus.Normal.value);
        creditFileData.setDescribeApiName(CreditFileConstants.API_NAME);
        if (objectDescribe != null) {
            creditFileData.setDescribeId(objectDescribe.getId());
        }
        return creditFileData;
    }

    @Override
    public Boolean triggerCustomerAccountCheckJob(ServiceContext serviceContext, CurlModel.Type type) {
        Job job = null;
        if (JobTypeEnum.CustomerAndCustomerAccountCompareJob.getType().equals(type.getType())) {
            job = new CustomerAndCustomerAccountCompareJob(type.getStartTime(), type.getEndTime());
        } else if (JobTypeEnum.PaymentAndCustomerAccountCompareJob.getType().equals(type.getType())) {
            job = new PaymentAndCustomerAccountCompareJob(type.getStartTime(), type.getEndTime());
        } else if (JobTypeEnum.Refund.getType().equals(type.getType())) {
            job = new RefundAndCustomerAccountCompareJob(type.getStartTime(), type.getEndTime());
        }
        try {
            job.execute(null);
        } catch (JobExecutionException e) {
            log.error("triggerCustomerAccountCheckJob", e);
        }
        return Boolean.TRUE;
    }

    @Override
    public EffectRebateIncomeModel.Result effectRebateIncome(ServiceContext serviceContext, EffectRebateIncomeModel.Arg arg) {
        EffectRebateIncomeModel.Result result = new EffectRebateIncomeModel.Result();
        Set<String> rebateIncomeIds = arg.getRebateIncomeIds();
        if (CollectionUtils.isEmpty(rebateIncomeIds)) {
            return result;
        }
        String tenantId = serviceContext.getTenantId();
        List<IObjectData> rebateIncomeDataList = serviceFacade.findObjectDataByIds(tenantId, Lists.newArrayList(rebateIncomeIds), RebateIncomeDetailConstants.API_NAME);
        int argSize = CollectionUtils.size(rebateIncomeDataList);
        int successSize = rebateIncomeDetailManager.batchEnableRebateIncomeDetails(rebateIncomeDataList);
        log.info("tenantId:{},total:{},success:{}", tenantId, argSize, successSize);
        return result;
    }

    @Override
    public Boolean triggerJob(ServiceContext serviceContext, CurlModel.Type type) {
        String tenantId = serviceContext.getTenantId();
        int listSize = 0;
        int size = 100;
        int offset = 0;
        try {
            if ("EffectiveRebateIncomeJob".equals(type.getType())) {
                do {
                    List<IObjectData> enabledRebateList = rebateIncomeDetailManager.listNowDayEnableRebateIncomeDetails(tenantId, offset, size);
                    rebateIncomeDetailManager.batchEnableRebateIncomeDetails(enabledRebateList);
                    listSize = enabledRebateList.size();
                    offset = offset + listSize;
                } while (listSize == size);
                log.info("EffectiveRebateIncomeJob,tenantId={},totalSize={}", tenantId, offset);
            } else if ("InvalidRebateIncomeJob".equals(type.getType())) {
                try {
                    do {
                        List<IObjectData> list = rebateIncomeDetailManager.listYestdayInvalidRebateIncomeDetails(tenantId, offset, size);
                        rebateIncomeDetailManager.batchInvalidRebateIncomeDatails(list);
                        listSize = list.size();
                        offset = offset + listSize;
                    } while (listSize == size);
                    log.info("InvalidRebateIncomeJob,tenantId={},totalSize={}", tenantId, offset);
                } catch (Exception e) {
                    log.error("InvalidRebateIncomeJob error,for tenantId:{}", tenantId, e);
                }

                offset = 0;
                do {
                    List<IObjectData> list = rebateIncomeDetailManager.listYestDayInvalidInChangeRebateIncomeDetails(tenantId, offset, size);
                    rebateIncomeDetailManager.batchInvalidRebateIncomeDatails(list);
                    listSize = list.size();
                    offset = offset + listSize;
                } while (listSize == size);
            }
        } catch (Exception e) {
            log.error(type.getType() + " error correct  customerAccount balance,for tenantId:{}", tenantId);
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean triggerCompensationDataJob(ServiceContext serviceContext, CurlModel.Type type) {
        Long startTime = type.getStartTime();
        Long endTime = type.getEndTime();

        List<String> tenantIds = customerAccountConfigManager.list(CustomerAccountType.CustomerAccountEnableSwitchStatus.ENABLE.getValue());
        for (String tenantId : tenantIds) {
            int size = 100;
            int listSize = 0;
            int offset = 0;
            try {
                if ("EffectiveRebateIncomeJob".equals(type.getType())) {
                    do {
                        List<IObjectData> enabledRebateList = rebateIncomeDetailManager.listNowDayEnableRebateIncomeDetails(tenantId, startTime, endTime, offset, size);
                        rebateIncomeDetailManager.batchEnableRebateIncomeDetails(enabledRebateList);

                        listSize = enabledRebateList.size();
                        offset = offset + listSize;
                    } while (listSize == size);
                    log.info("EffectiveRebateIncomeJob,tenantId={},totalSize={}", tenantId, offset);
                } else if ("InvalidRebateIncomeJob".equals(type.getType())) {
                    List<String> dataIds = Lists.newArrayList();
                    do {
                        List<IObjectData> list = rebateIncomeDetailManager.listYestdayInvalidRebateIncomeDetails(tenantId, startTime, endTime, offset, size);
                        list.forEach(data -> dataIds.add(data.getId()));

                        rebateIncomeDetailManager.batchInvalidRebateIncomeDatails(list);
                        listSize = list.size();
                        offset = offset + listSize;
                    } while (listSize == size);
                    log.info("InvalidRebateIncomeJob,tenantId={},dataIds={} totalSize={}", tenantId, dataIds, offset);
                } else if ("EffectiveCreditFileJob".equals(type.getType())) {
                    // 促销启用
                    do {
                        Set<String> customerIds = Sets.newHashSet();
                        Map<String, List<IObjectData>> accountIdCreditFileMapping = Maps.newLinkedHashMap();

                        List<IObjectData> list = creditFileManager.listToBeEffectiveCreditFiles(tenantId, startTime, endTime, offset, size);
                        list.forEach(data -> {
                            String customerId = data.get(CreditFileConstants.Field.Customer.apiName, String.class);
                            customerIds.add(customerId);
                            if (accountIdCreditFileMapping.containsKey(customerId)) {
                                accountIdCreditFileMapping.get(customerId).add(data);
                            } else {
                                accountIdCreditFileMapping.put(customerId, Lists.newArrayList(data));
                            }
                        });

                        User user = RequestUtil.getSystemUser(tenantId);
                        List<IObjectData> customerAccounts = customerAccountManager.listCustomerAccountOnlyIncludeInvalidByCustomerIds(user, Lists.newArrayList(customerIds));
                        Map<String, IObjectData> customerAccountMapping = Maps.newHashMap();
                        customerAccounts.forEach(customerAccount -> {
                            String customerId = customerAccount.get(CustomerAccountConstants.Field.Customer.apiName, String.class);
                            customerAccountMapping.put(customerId, customerAccount);
                        });

                        customerAccountMapping.entrySet().forEach(entry -> {
                            String customerId = entry.getKey();
                            IObjectData customerAccount = entry.getValue();
                            List<IObjectData> creditFiles = accountIdCreditFileMapping.get(customerId);

                            creditFileManager.batchUpdateCreditFiles(tenantId, creditFiles, customerAccount, 2);
                        });

                        listSize = list.size();
                        offset = offset + listSize;
                    } while (listSize == size);
                    log.info("Effective CreditFile,tenantId={},totalSize={}", tenantId, offset);
                } else if ("InvalidCreditFileJob".equals(type.getType())) {
                    // 促销失效过期
                    List<String> dataIds = Lists.newArrayList();
                    do {
                        Set<String> customerIds = Sets.newHashSet();
                        Map<String, List<IObjectData>> accountIdCreditFileMapping = Maps.newLinkedHashMap();

                        List<IObjectData> list = creditFileManager.listToBeInvalidCreditFiles(tenantId, startTime, endTime, offset, size);
                        list.forEach(data -> {
                            dataIds.add(data.getId());
                            customerIds.add(data.get(CreditFileConstants.Field.Customer.apiName, String.class));

                            String customerId = data.get(CreditFileConstants.Field.Customer.apiName, String.class);
                            if (accountIdCreditFileMapping.containsKey(customerId)) {
                                accountIdCreditFileMapping.get(customerId).add(data);
                            } else {
                                accountIdCreditFileMapping.put(customerId, Lists.newArrayList(data));
                            }
                        });

                        User user = RequestUtil.getSystemUser(tenantId);
                        List<IObjectData> customerAccounts = customerAccountManager.listCustomerAccountOnlyIncludeInvalidByCustomerIds(user, Lists.newArrayList(customerIds));
                        Map<String, IObjectData> customerAccountMapping = Maps.newHashMap();
                        customerAccounts.forEach(customerAccount -> {
                            String customerId = customerAccount.get(CustomerAccountConstants.Field.Customer.apiName, String.class);
                            customerAccountMapping.put(customerId, customerAccount);
                        });

                        customerAccountMapping.entrySet().forEach(entry -> {
                            String customerId = entry.getKey();
                            IObjectData customerAccount = entry.getValue();
                            List<IObjectData> creditFiles = accountIdCreditFileMapping.get(customerId);

                            creditFileManager.batchUpdateCreditFiles(tenantId, creditFiles, customerAccount, 1);
                        });

                        listSize = list.size();
                        offset = offset + listSize;
                    } while (listSize == size);
                    log.info("Invalid CreditFile,tenantId={},dataIds={} totalSize={}", tenantId, dataIds, offset);
                }
            } catch (Exception e) {
                log.error("triggerCompensationDataJob error,for type:{} tenantId:{} exception:", type.getType(), tenantId, e);
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public Map<String, List<String>> initTopInfo(ServiceContext serviceContext, CurlModel.TenantIds arg) {
        List<String> tenantIds = arg.getTenantIds();
        if (CollectionUtils.isEmpty(tenantIds)) {
            return Maps.newHashMap();
        }
        if (tenantIds.contains("all")) {
            tenantIds = customerAccountConfigManager.list(CustomerAccountType.CustomerAccountEnableSwitchStatus.ENABLE.getValue());
        }
        Map<String, List<String>> failMap = Maps.newHashMap();
        for (String tenantId : tenantIds) {
            User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
            boolean isCustomerAccountEnable = customerAccountConfigManager.isCustomerAccountEnable(tenantId);
            if (!isCustomerAccountEnable) {
                continue;
            }
            updateTopInfo(user, CustomerAccountConstants.DETAIL_LAYOUT_API_NAME, CustomerAccountConstants.API_NAME, failMap);
            updateTopInfo(user, PrepayDetailConstants.DEFAULT_LAYOUT_API_NAME, PrepayDetailConstants.API_NAME, failMap);
            updateTopInfo(user, PrepayDetailConstants.INCOME_LAYOUT_API_NAME, PrepayDetailConstants.API_NAME, failMap);
            updateTopInfo(user, PrepayDetailConstants.OUTCOME_LAYOUT_API_NAME, PrepayDetailConstants.API_NAME, failMap);
            updateTopInfo(user, RebateOutcomeDetailConstants.DEFAULT_LAYOUT_API_NAME, RebateOutcomeDetailConstants.API_NAME, failMap);
            updateTopInfo(user, CreditFileConstants.DEFAULT_LAYOUT_API_NAME, CreditFileConstants.API_NAME, failMap);
            updateTopInfo(user, RebateIncomeDetailConstants.DEFAULT_LAYOUT_API_NAME, RebateIncomeDetailConstants.API_NAME, failMap);
            updateTopInfo(user, RebateUseRuleConstants.DEFAULT_LAYOUT_API_NAME, RebateUseRuleConstants.API_NAME, failMap);
        }
        return failMap;
    }

    @Override
    public Boolean compareObjectAndFlowAmount(CurlModel.Condition condition) {
        if (Objects.isNull(condition) || CollectionUtils.isEmpty(condition.getTenantIds())) {
            throw new ValidateException("tenantIds is empty");
        }
        try {
            for (String tenantId : condition.getTenantIds()) {
                //信用查询sql
                String baseCreditSql = String.format("select id as _id,name,customer_id,credit_type,credit_quota,temporary_credit_limit,credit_period,start_time,end_time,life_status, last_modified_time from credit_file where tenant_id='%s'", tenantId);
                //返利支出查询sql
                String baseRebateOutcomeSql = String.format("select id as _id,name,amount,order_payment_id,rebate_income_detail_id,life_status, last_modified_time from rebate_outcome_detail where tenant_id='%s'", tenantId);
                //返利收入查询sql
                String baseRebateIncomeSql = String.format("select id as _id,name,customer_id,amount,used_rebate,available_rebate,customer_account_id,start_time,end_time,life_status, last_modified_time from rebate_income_detail where tenant_id='%s'", tenantId);
                //预存款查询sql
                String basePrepayIncrementSql = String.format("select id as _id,name,customer_id,amount,customer_account_id,record_type,life_status, last_modified_time from prepay_detail where tenant_id='%s'", tenantId);
                if (Objects.nonNull(condition.getStartDate())) {
                    long startTime = DateUtil.parseDateStr(condition.getStartDate());
                    baseCreditSql += " and last_modified_time >= " + startTime;
                    baseRebateOutcomeSql += " and last_modified_time >= " + startTime;
                    baseRebateIncomeSql += " and last_modified_time >= " + startTime;
                    basePrepayIncrementSql += " and last_modified_time >= " + startTime;
                }
                if (Objects.nonNull(condition.getEndDate())) {
                    long endTime = DateUtil.parseDateStr(condition.getEndDate());
                    baseCreditSql += " and last_modified_time < " + endTime;
                    baseRebateOutcomeSql += " and last_modified_time < " + endTime;
                    baseRebateIncomeSql += " and last_modified_time < " + endTime;
                    basePrepayIncrementSql += " and last_modified_time < " + endTime;
                }
                if (StringUtils.isNotBlank(condition.getType())) {
                    String type = condition.getType();
                    if (type.equals(Constants.CREDIT)) {//比较信用与流水
                        compareCreditAndFlowAmount(tenantId, baseCreditSql);
                    } else if (type.equals(Constants.REBATE_OUT_COME)) { //比较返利支出与流水
                        compareRebateOutcomeAndFlowAmount(tenantId, baseRebateOutcomeSql);
                    } else if (type.equals(Constants.REBATE_IN_COME)) {//比较返利收入与流水
                        compareRebateIncomeAndFlowAmount(tenantId, baseRebateIncomeSql);
                    } else if (type.equals(Constants.PREPAY)) {//比较预存款与流水
                        comparePrepayAndFlowAmount(tenantId, basePrepayIncrementSql);
                    }
                } else {//都要比较
                    compareCreditAndFlowAmount(tenantId, baseCreditSql);
                    compareRebateOutcomeAndFlowAmount(tenantId, baseRebateOutcomeSql);
                    compareRebateIncomeAndFlowAmount(tenantId, baseRebateIncomeSql);
                    comparePrepayAndFlowAmount(tenantId, basePrepayIncrementSql);
                }
            }
        } catch (Exception e) {
            logger.warn("服务调用错误,请重试", e);
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean checkPrepaidSalesOrderAndPayment(ServiceContext serviceContext, CurlModel.TenantIds arg) {
        List<String> tenantIds = arg.getTenantIds();
        if (CollectionUtils.isEmpty(tenantIds)) {
            throw new ValidateException("param error");
        }
        for (String tenantId : tenantIds) {
            try {
                CustomerAccountConfigModel.CustomerAccountConfig customerAccountConfig = customerAccountConfigManager.getConfigByTenantId(tenantId);
                if (customerAccountConfig != null && customerAccountConfig.getStatus() == CustomerAccountType.CustomerAccountEnableSwitchStatus.ENABLE.getValue()) {
                    User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
                    String customerAccountSql = "select * from customer_account where tenant_id='%s' and life_status != 'invalid' limit %d offset %d ";
                    int limit = 100;
                    int offset = 0;
                    int size = 0;
                    do {
                        List<Map> validCustomerAccounts = findBySql(tenantId, String.format(customerAccountSql, tenantId, limit, offset));
                        if (CollectionUtils.isEmpty(validCustomerAccounts)) {
                            break;
                        }
                        size = validCustomerAccounts.size();
                        offset += limit;

                        for (Map objectData : validCustomerAccounts) {
                            IObjectData customerAccount = new ObjectData(objectData);
                            checkUnpaidSalesOrders(user, customerAccount);
                        }
                    } while (limit == size);
                }
            } catch (Exception e) {
                log.error("checkPrepaidSalesOrderAndPayment failed, tenantId={}", tenantId, e);
            }
        }
        return Boolean.TRUE;
    }

    private void updateTopInfo(User user, String layoutApiName, String objectApiName, Map<String, List<String>> failMap) {
        String tenantId = user.getTenantId();
        try {
            ILayout layout = serviceFacade.findLayoutByApiName(user, layoutApiName, objectApiName);
            if (Objects.isNull(layout)) {
                return;
            }
            SimpleComponent newTopInfo = null;
            if (Objects.equals(objectApiName, CustomerAccountConstants.API_NAME)) {
                newTopInfo = InitUtil.getCustomerAccountTopInfo();
            } else if (Objects.equals(objectApiName, PrepayDetailConstants.API_NAME)) {
                newTopInfo = InitUtil.getPrepayTopInfo();
            } else if (Objects.equals(objectApiName, RebateIncomeDetailConstants.API_NAME)) {
                newTopInfo = InitUtil.getRebateIncomeTopInfo();
            } else if (Objects.equals(objectApiName, RebateOutcomeDetailConstants.API_NAME)) {
                newTopInfo = InitUtil.getRebateOutcomeTopInfo();
            } else if (Objects.equals(objectApiName, CreditFileConstants.API_NAME)) {
                newTopInfo = InitUtil.getCreditTopInfo();
            }
            if (Objects.nonNull(newTopInfo)) {
                layout.setTopInfo(newTopInfo);
                serviceFacade.updateLayout(user, layout);
            }
        } catch (Exception e) {
            log.warn("updateTopInfo error,user:{},objectApiName:{},layoutApiName:{}", user, layoutApiName);
            if (failMap.containsKey(tenantId)) {
                failMap.get(tenantId).add(layoutApiName);
            } else {
                List<String> layoutApiNames = Lists.newArrayList();
                layoutApiNames.add(layoutApiName);
                failMap.put(tenantId, layoutApiNames);
            }
        }
    }

    private void checkUnpaidSalesOrders(User user, IObjectData customerAccount) {
        String customerId = ObjectDataUtil.getReferenceId(customerAccount, CustomerAccountConstants.Field.Customer.apiName);
        String customerAccountId = customerAccount.getId();
        String customerAccountName = customerAccount.getName();
        BigDecimal prepayAvailableBalance = ObjectDataUtil.getBigDecimal(customerAccount, CustomerAccountConstants.Field.PrepayAvailableBalance.apiName);
        BigDecimal rebateAvailableBalance = ObjectDataUtil.getBigDecimal(customerAccount, CustomerAccountConstants.Field.RebateAvailableBalance.apiName);

        BigDecimal unpaidAmount = crmManager.getUsedCreditAmountOpt(user, Lists.newArrayList(customerId), true, false);
        if (prepayAvailableBalance.add(rebateAvailableBalance).compareTo(unpaidAmount) < 0) {
            logger.warn("客户账户ID{}-{} 预存款可用金额+返利可用金额不足以抵扣未回款订单", customerAccountId, customerAccountName);
        }
    }

    private void compareCreditAndFlowAmount(String tenantId, String baseSql) throws MetadataServiceException {
        String pageSqlFormat = " limit %d offset %d";
        int limit = 100;
        int offset = 0;
        int size;
        boolean check;
        do {
            String sql = baseSql.concat(String.format(pageSqlFormat, limit, offset));
            List<Map> objectDataList = findBySql(tenantId, sql);
            if (CollectionUtils.isEmpty(objectDataList)) {
                break;
            }
            size = objectDataList.size();
            offset += limit;
            for (Map map : objectDataList) {
                IObjectData objectData = new ObjectData(map);
                String customerId = ObjectDataUtil.getReferenceId(objectData, CreditFileConstants.Field.Customer.apiName);
                String customerAccountId = getCustomerAccountIdByCustomerId(tenantId, customerId);
                String id = objectData.getId();
                ObjectLifeStatus objectLifeStatus = ObjectDataExt.of(objectData).getLifeStatus();
                Date startTime = objectData.get(CreditFileConstants.Field.StartTime.apiName, Date.class);
                Date endTime = objectData.get(CreditFileConstants.Field.EndTime.apiName, Date.class);
                String creditType = objectData.get(CreditFileConstants.Field.CreditType.apiName, String.class);
                //信用数据creditFileId的所有流水数据统计
                CustomerAccountBillStatistics customerAccountBillStatistics = billJobManager.countBillStatistics(tenantId, BillTypeEnum.Credit.getType(), id);
                BigDecimal creditAvailableQuoteChange = Objects.isNull(customerAccountBillStatistics) ? BigDecimal.ZERO : customerAccountBillStatistics.getCreditAvailableQuotaChange();
                BigDecimal expectCredit = BigDecimal.ZERO;
                boolean isActive = ObjectDataUtil.isCurrentTimeActive(startTime, endTime);
                if (isActive) {
                    switch (objectLifeStatus) {
                        case UNDER_REVIEW:
                        case INEFFECTIVE:
                        case INVALID:
                            break;
                        case IN_CHANGE:
                        case NORMAL:
                            if (CreditTypeEnum.OfficialCredit.getValue().equals(creditType)) {
                                expectCredit = ObjectDataUtil.getBigDecimal(objectData, CreditFileConstants.Field.CreditQuota.apiName);
                            } else {
                                expectCredit = ObjectDataUtil.getBigDecimal(objectData, CreditFileConstants.Field.TemporaryCreditLimit.apiName);
                            }
                            break;
                    }
                }
                check = expectCredit.compareTo(creditAvailableQuoteChange) == 0;
                if (!check) {
                    logger.warn("企业{}-{}信用记录与流水不一致,客户账户ID为：{},关联信用ID为：{}", tenantId, CreditFileConstants.API_NAME, customerAccountId, id);
                }
            }
        } while (limit == size);
    }

    private void compareRebateOutcomeAndFlowAmount(String tenantId, String baseSql) throws MetadataServiceException {
        String pageSqlFormat = " limit %d offset %d";
        int limit = 100;
        int offset = 0;
        int size;
        boolean check;
        do {
            String sql = baseSql.concat(String.format(pageSqlFormat, limit, offset));
            List<Map> objectDataList = findBySql(tenantId, sql);
            if (CollectionUtils.isEmpty(objectDataList)) {
                break;
            }
            size = objectDataList.size();
            offset += limit;
            for (Map map : objectDataList) {
                IObjectData objectData = new ObjectData(map);
                String id = objectData.getId();
                String rebateIncomeId = ObjectDataUtil.getReferenceId(objectData, RebateOutcomeDetailConstants.Field.RebateIncomeDetail.apiName);
                String customerAccountId = getCustomerAccountByRebateIncomeId(tenantId, rebateIncomeId);
                String rebateOutcomeId = objectData.getId();
                BigDecimal amount = ObjectDataUtil.getBigDecimal(objectData, RebateOutcomeDetailConstants.Field.Amount.apiName);
                CustomerAccountBillStatistics customerAccountBillStatistics = billJobManager.countBillStatistics(tenantId, BillTypeEnum.RebateOutcome.getType(), rebateOutcomeId);
                BigDecimal rebateAmountChange = BigDecimal.ZERO;
                BigDecimal rebateLockedAmountChange = BigDecimal.ZERO;
                if (Objects.nonNull(customerAccountBillStatistics)) {
                    rebateAmountChange = customerAccountBillStatistics.getRebateAmountChange();
                    rebateLockedAmountChange = customerAccountBillStatistics.getRebateLockedAmountChange();
                }
                BigDecimal expectAmountChange = BigDecimal.ZERO;
                BigDecimal expectLockedAmountChange = BigDecimal.ZERO;
                ObjectLifeStatus objectLifeStatus = ObjectDataExt.of(objectData).getLifeStatus();
                switch (objectLifeStatus) {
                    case INEFFECTIVE:
                    case INVALID:
                        break;
                    case UNDER_REVIEW:
                        expectAmountChange = BigDecimal.ZERO;
                        expectLockedAmountChange = amount;
                        break;
                    case NORMAL:
                    case IN_CHANGE:
                        expectAmountChange = amount.negate();
                        expectLockedAmountChange = BigDecimal.ZERO;
                        break;
                }
                check = (rebateAmountChange.compareTo(expectAmountChange) == 0 && rebateLockedAmountChange.compareTo(expectLockedAmountChange) == 0);
                if (!check) {
                    logger.warn("企业{}-{}返利支出记录与流水不一致,客户账户ID为：{},关联返利支出ID为：{}", tenantId, RebateOutcomeDetailConstants.API_NAME, customerAccountId, id);
                }
            }
        } while (limit == size);
    }

    private void compareRebateIncomeAndFlowAmount(String tenantId, String baseSql) throws MetadataServiceException {
        String pageSqlFormat = " limit %d offset %d";
        int limit = 100;
        int offset = 0;
        int size;
        boolean check;
        do {
            String sql = baseSql.concat(String.format(pageSqlFormat, limit, offset));
            List<Map> objectDataList = findBySql(tenantId, sql);
            if (CollectionUtils.isEmpty(objectDataList)) {
                break;
            }
            size = objectDataList.size();
            offset += limit;
            List<String> inChangeIds = objectDataList.stream().map(ObjectData::new).filter(objectData -> ObjectDataExt.of(objectData).getLifeStatus() == ObjectLifeStatus.IN_CHANGE).map(IObjectData::getId).collect(Collectors.toList());
            Map<String, ApprovalFlowTriggerType> inChangeIdsApprovalFlowType = getInChangeDataApprovalFlowType(tenantId, inChangeIds);
            for (Map map : objectDataList) {
                IObjectData objectData = new ObjectData(map);
                String id = objectData.getId();
                ApprovalFlowTriggerType approvalFlowTriggerType = inChangeIdsApprovalFlowType.get(id);
                String customerAccountId = ObjectDataUtil.getReferenceId(objectData, RebateIncomeDetailConstants.Field.CustomerAccount.apiName);
                String rebateIncomeId = objectData.getId();
                CustomerAccountBillStatistics customerAccountBillStatistics = billJobManager.countBillStatistics(tenantId, BillTypeEnum.RebateIncome.getType(), rebateIncomeId);
                BigDecimal rebateAmountChange = BigDecimal.ZERO;
                BigDecimal rebateLockedAmountChange = BigDecimal.ZERO;
                if (Objects.nonNull(customerAccountBillStatistics)) {
                    rebateAmountChange = customerAccountBillStatistics.getRebateAmountChange();
                    rebateLockedAmountChange = customerAccountBillStatistics.getRebateLockedAmountChange();
                }
                BigDecimal expectAmountChange = BigDecimal.ZERO;
                BigDecimal expectLockedAmountChange = BigDecimal.ZERO;
                BigDecimal amount = ObjectDataUtil.getBigDecimal(objectData, RebateIncomeDetailConstants.Field.Amount.apiName);
                BigDecimal usedAmount = ObjectDataUtil.getBigDecimal(objectData, RebateIncomeDetailConstants.Field.UsedRebate.apiName);
                BigDecimal availableAmount = ObjectDataUtil.getBigDecimal(objectData, RebateIncomeDetailConstants.Field.AvailableRebate.apiName);
                ObjectLifeStatus objectLifeStatus = ObjectDataExt.of(objectData).getLifeStatus();
                Date startTime = objectData.get(RebateIncomeDetailConstants.Field.StartTime.apiName, Date.class);
                Date endTime = objectData.get(RebateIncomeDetailConstants.Field.EndTime.apiName, Date.class);
                boolean isActive = ObjectDataUtil.isCurrentTimeActive(startTime, endTime);
                switch (objectLifeStatus) {
                    case INEFFECTIVE:
                    case INVALID:
                    case UNDER_REVIEW:
                        break;
                    case NORMAL:
                        expectAmountChange = availableAmount.add(usedAmount);
                        break;
                    case IN_CHANGE:
                        logger.info("checkRebateIncomeBill,dataId:{},approvalFlowTriggerType:{}", rebateIncomeId, approvalFlowTriggerType);
                        if (ApprovalFlowTriggerType.INVALID == approvalFlowTriggerType) {
                            //作废审批中
                            if (isActive) {
                                expectLockedAmountChange = amount;
                                expectAmountChange = amount;
                            }
                        } else if (ApprovalFlowTriggerType.UPDATE == approvalFlowTriggerType) {
                            //编辑审批中
                            if (isActive) {
                                expectAmountChange = availableAmount.add(usedAmount);
                            }
                        }
                        break;
                }
                check = (expectAmountChange.compareTo(rebateAmountChange) == 0 && expectLockedAmountChange.compareTo(rebateLockedAmountChange) == 0);
                if (!check) {
                    logger.warn("企业{}-{}返利收入记录与流水不一致,客户账户ID为：{},关联返利收入ID为：{}", tenantId, RebateIncomeDetailConstants.API_NAME, customerAccountId, id);
                }
            }
        } while (limit == size);
    }

    private void comparePrepayAndFlowAmount(String tenantId, String baseSql) throws MetadataServiceException {
        String pageSqlFormat = " limit %d offset %d";
        int limit = 100;
        int offset = 0;
        int size;
        boolean check;
        do {
            String sql = baseSql.concat(String.format(pageSqlFormat, limit, offset));
            List<Map> objectDataList = findBySql(tenantId, sql);
            if (CollectionUtils.isEmpty(objectDataList)) {
                break;
            }
            size = objectDataList.size();
            offset += limit;
            List<String> inChangeIds = objectDataList.stream().map(ObjectData::new).filter(objectData -> ObjectDataExt.of(objectData).getLifeStatus() == ObjectLifeStatus.IN_CHANGE).map(IObjectData::getId).collect(Collectors.toList());
            Map<String, ApprovalFlowTriggerType> inChangeIdsApprovalFlowType = getInChangeDataApprovalFlowType(tenantId, inChangeIds);
            for (Map map : objectDataList) {
                IObjectData objectData = new ObjectData(map);
                String id = objectData.getId();
                ApprovalFlowTriggerType approvalFlowTriggerType = inChangeIdsApprovalFlowType.get(id);
                String customerAccountId = ObjectDataUtil.getReferenceId(objectData, PrepayDetailConstants.Field.CustomerAccount.apiName);
                String prepayId = objectData.getId();
                CustomerAccountBillStatistics customerAccountBillStatistics = billJobManager.countBillStatistics(tenantId, BillTypeEnum.Prepay.getType(), prepayId);
                BigDecimal prepayAmountChange = BigDecimal.ZERO;
                BigDecimal prepayLockedAmountChange = BigDecimal.ZERO;
                if (Objects.nonNull(customerAccountBillStatistics)) {
                    prepayAmountChange = customerAccountBillStatistics.getPrepayAmountChange();
                    prepayLockedAmountChange = customerAccountBillStatistics.getPrepayLockedAmountChange();
                }
                BigDecimal amount = ObjectDataUtil.getBigDecimal(objectData, PrepayDetailConstants.Field.Amount.apiName);
                BigDecimal expectedAmountChange = BigDecimal.ZERO;
                BigDecimal expectedLockedAmountChange = BigDecimal.ZERO;
                ObjectLifeStatus objectLifeStatus = ObjectDataExt.of(objectData).getLifeStatus();
                boolean isPrepayIncome = ObjectDataUtil.isPrepayIncome(objectData);
                if (isPrepayIncome) {
                    switch (objectLifeStatus) {
                        case INEFFECTIVE:
                        case UNDER_REVIEW:
                        case INVALID:
                            break;
                        case NORMAL:
                            expectedAmountChange = amount;
                            break;
                        case IN_CHANGE:
                            logger.info("checkPrepayBill,dataId:{},approvalFlowTriggerType:{}", prepayId, approvalFlowTriggerType);
                            if (ApprovalFlowTriggerType.INVALID == approvalFlowTriggerType) {
                                expectedLockedAmountChange = amount;
                                expectedAmountChange = amount;
                            } else if (ApprovalFlowTriggerType.UPDATE == approvalFlowTriggerType) {
                                expectedAmountChange = amount;
                            }
                            break;
                    }
                } else {
                    switch (objectLifeStatus) {
                        case INEFFECTIVE:
                        case INVALID:
                            break;
                        case IN_CHANGE:
                        case NORMAL:
                            expectedAmountChange = amount.negate();
                            break;
                        case UNDER_REVIEW:
                            expectedLockedAmountChange = amount;
                            break;
                    }
                }
                check = expectedAmountChange.compareTo(prepayAmountChange) == 0 && expectedLockedAmountChange.compareTo(prepayLockedAmountChange) == 0;
                if (!check) {
                    logger.warn("企业{}-{}预存款记录与流水不一致,客户账户ID为：{},关联预存款ID为：{}", tenantId, PrepayDetailConstants.API_NAME, customerAccountId, id);
                }
            }
        } while (limit == size);
    }

    private List<Map> findBySql(String tenantId, String sql) throws MetadataServiceException {
        return objectDataService.findBySql(tenantId, sql);
    }

    private Map<String, ApprovalFlowTriggerType> getInChangeDataApprovalFlowType(String tenantId, List<String> dataIds) {
        if (CollectionUtils.isEmpty(dataIds)) {
            return Maps.newHashMap();
        }
        GetCurInstanceStateModel.Arg arg = new GetCurInstanceStateModel.Arg();
        arg.setObjectIds(dataIds);
        Map<String, String> headers = Maps.newHashMap();
        headers.put("x-tenant-id", tenantId);
        headers.put("x-user-id", User.SUPPER_ADMIN_USER_ID);
        GetCurInstanceStateModel.Result getCurInstancesStateResult = approvalInitProxy.getCurInstanceStateByObjectIds(arg, headers);
        logger.info("getCurInstanceStateByObjectIds,headers:{},arg:{},result:{}", headers, arg, getCurInstancesStateResult);
        if (!getCurInstancesStateResult.success()) {
            Map<String, ApprovalFlowTriggerType> approvalFlowTriggerTypeMap = Maps.newHashMap();
            dataIds.forEach(id -> approvalFlowTriggerTypeMap.put(id, ApprovalFlowTriggerType.INVALID));
            return approvalFlowTriggerTypeMap;
        } else {
            return getCurInstancesStateResult.getData().stream().collect(Collectors.toMap(GetCurInstanceStateModel.IntanceStatus::getObjectId, instanceStatus -> {
                String triggerType = instanceStatus.getTriggerType();
                return getInChangeApprovalFlowTriggerType(triggerType);
            }));
        }
    }

    private ApprovalFlowTriggerType getInChangeApprovalFlowTriggerType(String triggerType) {
        for (ApprovalFlowTriggerType approvalFlowTriggerType : ApprovalFlowTriggerType.values()) {
            if (approvalFlowTriggerType.getId().equals(triggerType)) {
                return approvalFlowTriggerType;
            }
        }
        return ApprovalFlowTriggerType.INVALID;
    }

    private String getCustomerAccountIdByCustomerId(String tenantId, String customerId) {
        String sql = String.format("select id from customer_account where tenant_id='%s' and customer_id='%s'", tenantId, customerId);
        try {
            List<Map> list = findBySql(tenantId, sql);
            if (CollectionUtils.isEmpty(list)) {
                logger.warn("getCustomerAccountIdByCustomerId findBySql:{}, data isEmpty", sql);
                return null;
            }
            Map data = list.get(0);
            return (String) data.get("id");
        } catch (MetadataServiceException e) {
            logger.warn("getCustomerAccountIdByCustomerId findBySql error,sql:{}", sql, e);
        }
        return null;
    }

    private String getCustomerAccountByRebateIncomeId(String tenantId, String rebateIncomeId) {
        String sql = String.format("select customer_account_id from rebate_income_detail where tenant_id='%s' and id='%s'", tenantId, rebateIncomeId);
        try {
            List<Map> list = findBySql(tenantId, sql);
            if (CollectionUtils.isEmpty(list)) {
                logger.warn("getCustomerAccountByRebateIncomeId findBySql:{}, data isEmpty", sql);
                return null;
            }
            Map data = list.get(0);
            return (String) data.get("customer_account_id");
        } catch (MetadataServiceException e) {
            logger.warn("getCustomerAccountByRebateIncomeId findBySql error,sql:{}", sql, e);
        }
        return null;
    }

    @Override
    public EmptyResult createInvalidAndDeleteFunction(ServiceContext serviceContext, CurlModel.Arg arg) {
        List<String> tenantIds = arg.getTenantIds() == null ? Lists.newArrayList(serviceContext.getTenantId()) : arg.getTenantIds();
        if (!tenantIds.contains(serviceContext.getTenantId())) {
            tenantIds.add(serviceContext.getTenantId());
        }
        List<String> objectApiNames = arg.getObjectApiNames();
        if (com.facishare.paas.appframework.common.util.CollectionUtils.empty(objectApiNames)) {
            return new EmptyResult();
        }

        for (String tenantId : tenantIds) {
            User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
            for (String objectApiName : objectApiNames) {
                invalidAndDeleteFunction(user, objectApiName);
            }
        }
        return new EmptyResult();
    }

    @Override
    public EmptyResult fixPaymentPrepayLifeStatus(ServiceContext serviceContext, CurlModel.FixPaymentPrepayLifeStatusArg arg) {
        List<String> tenantIds = arg.getTenantIds();
        Long startTime = arg.getStartTime();
        if (CollectionUtils.isEmpty(tenantIds) || Objects.isNull(startTime)) {
            throw new ValidateException("param error");
        }
        String baseSql = "SELECT po.id as order_payment_id, po.payment_id, pd.last_modified_by  " + "FROM prepay_detail pd JOIN payment_order po ON po.id = pd.order_payment_id " + "WHERE pd.tenant_id = '%s' AND pd.life_status = 'ineffective' AND po.life_status = 'normal' " + "AND pd.create_time >= %d ";
        try {
            for (String tenantId : tenantIds) {
                String querySql = String.format(baseSql, tenantId, startTime);
                List<Map> result = findBySql(tenantId, querySql);
                for (Map map : result) {
                    String paymentId = (String) map.get("payment_id");
                    String orderPaymentId = (String) map.get("order_payment_id");
                    String userId = (String) map.get("last_modified_by");

                    Map<String, String> dataMap = Maps.newHashMap();
                    dataMap.put(orderPaymentId, "normal");

                    RequestContext requestContext = RequestContext.builder().tenantId(tenantId).user(new User(tenantId, userId)).build();
                    ServiceContext newServiceContext = new ServiceContext(requestContext, null, null);

                    EditArgNew editArgNew = new EditArgNew();
                    editArgNew.setApprovalType("Update");
                    editArgNew.setPaymentId(paymentId);
                    editArgNew.setDataMap(dataMap);

                    sfaOrderPaymentService.edit(editArgNew, newServiceContext);
                }
            }
        } catch (Exception e) {
            log.error("query failed", e);
        }
        return new EmptyResult();
    }

    @Override
    public EmptyResult purgeCustomerAccount(ServiceContext serviceContext, CurlModel.TenantIds arg) {
        List<String> tenantIds = arg.getTenantIds();
        if (CollectionUtils.isEmpty(tenantIds)) {
            throw new ValidateException("param error");
        }
        for (String tenantId : tenantIds) {
            User user = User.systemUser(tenantId);
            purgeData(user, CustomerAccountConstants.API_NAME);
            purgeData(user, PrepayDetailConstants.API_NAME);
            purgeData(user, RebateIncomeDetailConstants.API_NAME);
            purgeData(user, RebateOutcomeDetailConstants.API_NAME);
            purgeData(user, RebateUseRuleConstants.API_NAME);
            purgeData(user, CreditFileConstants.API_NAME);
        }
        return new EmptyResult();
    }

    @Override
    public CurlModel.TenantIds fixPrepayByStandardImport(ServiceContext serviceContext, CurlModel.FixPrepayByStandardImportArg arg) {
        List<String> tenantIds = arg.getTenantIds();
        CurlModel.TenantIds result = new CurlModel.TenantIds();
        if (CollectionUtils.isEmpty(tenantIds)) {
            return result;
        }
        List<String> failTenantIds = Lists.newArrayList();
        int offset = 0, limit = 500, size = 0;
        long start = arg.getStart();
        long end = arg.getEnd();
        String sqlFormat = "select * from prepay_detail where tenant_id='%s' and create_time > %d and create_time < %d offset %d limit %d";
        for (String tenantId : tenantIds) {
            offset = 0;
            try {
                do {
                    String sql = String.format(sqlFormat, tenantId, start, end, offset, limit);
                    List<Map> dataList = objectDataService.findBySql(tenantId, sql);
                    if (CollectionUtils.isEmpty(dataList)) {
                        break;
                    }
                    offset += dataList.size();
                    size = dataList.size();
                    User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
                    for (Map prepayData : dataList) {
                        String id = String.valueOf(prepayData.get("id"));
                        prepayData.put("_id", id);
                        try {
                            prepayDetailManager.updateBalance(user, ObjectDataExt.of(prepayData).getObjectData(), SystemConstants.LifeStatus.Ineffective.value);
                        } catch (Exception e) {
                            log.warn("update PrepayByStandardImport error,tenantId:{},prepayId:{}", tenantId, id, e);
                        }
                    }
                } while (size == limit);
            } catch (Exception e) {
                failTenantIds.add(tenantId);
                log.warn("fix prepayByStandardImport error,tenantId:{}", tenantId, e);
            }
        }
        result.setTenantIds(failTenantIds);
        return null;
    }

    @Override
    public TenantIdModel.Result flowRenameField(ServiceContext serviceContext, TenantIdModel.Arg arg) {
        List<String> tenantIds = arg.getTenantIds();
        TenantIdModel.Result result = new TenantIdModel.Result();
        List<String> failEis = Lists.newArrayList();
        if (CollectionUtils.isEmpty(tenantIds)) {
            return result;
        }
        for (String tenantId : tenantIds) {
            User user = User.systemUser(tenantId);
            FundAccountSwitchEnum fundAccountSwitchEnum = fundAccountManager.getFundAccountStatus(tenantId);
            if (fundAccountSwitchEnum != FundAccountSwitchEnum.FUND_ACCOUNT_OPEN) {
                continue;
            }
            try {
                //新增描述
                IObjectDescribe flowDescribe = serviceFacade.findObject(tenantId, AccountTransactionFlowConst.API_NAME);
                if (!flowDescribe.containsField(AccountTransactionFlowConst.Field.RelateObject.apiName)) {
                    WhatFieldDescribe outcomeRecordField = WhatFieldDescribeBuilder.builder().apiName(AccountTransactionFlowConst.Field.RelateObject.apiName).label(AccountTransactionFlowConst.Field.RelateObject.label).apiNameField(AccountTransactionFlowConst.Field.RelateRecordObjectApiName.apiName).idField(AccountTransactionFlowConst.Field.RelateRecordObjectDataId.apiName).isRequired(false).build();

                    TextFieldDescribe objectApiNameField = TextFieldDescribeBuilder.builder().apiName(AccountTransactionFlowConst.Field.RelateRecordObjectApiName.apiName).label(AccountTransactionFlowConst.Field.RelateRecordObjectApiName.label).maxLength(100).required(false).usedIn("component").build();

                    TextFieldDescribe objectDataIdField = TextFieldDescribeBuilder.builder().apiName(AccountTransactionFlowConst.Field.RelateRecordObjectDataId.apiName).label(AccountTransactionFlowConst.Field.RelateRecordObjectDataId.label).maxLength(100).required(false).usedIn("component").build();
                    flowDescribe = fundAccountManager.addField(user, AccountTransactionFlowConst.API_NAME, AccountTransactionFlowConst.OUTCOME_LAYOUT_API_NAME, outcomeRecordField.toJsonString(), Lists.newArrayList(objectApiNameField, objectDataIdField), outcomeRecordField.getType(), false, true, false);
                }
                //删除描述
                List<IFieldDescribe> toDeleteField = Lists.newArrayList();
                IFieldDescribe outcomeRecordField = flowDescribe.getFieldDescribe("outcome_record_id");
                IFieldDescribe outcomeRecordObjectApiNameField = flowDescribe.getFieldDescribe("outcome_object_api_name");
                IFieldDescribe outcomeRecordObjectDataIdField = flowDescribe.getFieldDescribe("outcome_object_data_id");
                if (Objects.nonNull(outcomeRecordField) && outcomeRecordField.isActive()) {
                    toDeleteField.add(outcomeRecordField);
                }
                if (Objects.nonNull(outcomeRecordObjectApiNameField) && outcomeRecordObjectApiNameField.isActive()) {
                    toDeleteField.add(outcomeRecordObjectApiNameField);
                }
                if (Objects.nonNull(outcomeRecordObjectDataIdField) && outcomeRecordObjectDataIdField.isActive()) {
                    toDeleteField.add(outcomeRecordObjectDataIdField);
                }
                objectDescribeService.deleteCustomFieldDescribe(flowDescribe, toDeleteField);
            } catch (Exception e) {
                log.warn("accountFlowFieldRename,tenantId:{}", tenantId, e);
                failEis.add(tenantId);
            }
        }
        result.setTenantIds(failEis);
        return result;
    }

    @Override
    public TenantIdModel.Result fixPaymentFlow(ServiceContext serviceContext, TenantIdModel.Arg arg) {
        List<String> tenantIds = arg.getTenantIds();
        List<String> failEis = Lists.newArrayList();
        for (String tenantId : tenantIds) {
            try {
                List<IFilter> filters = Lists.newArrayList();
                SearchUtil.fillFilterEq(filters, "revenue_type", RevenueTypeEnum.PaymentCharge.getValue());
                SearchUtil.fillFilterEq(filters, "entry_status", "1");
                SearchUtil.fillFilterEq(filters, "record_type", "income_record_type__c");
                SearchUtil.fillFilterIsNull(filters, "relate_object_data_id");
                SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
                searchTemplateQuery.setFilters(filters);
                searchTemplateQuery.setLimit(1000);
                searchTemplateQuery.setOffset(0);
                User user = User.systemUser(tenantId);
                List<IObjectData> accountTransactionFlowList = this.serviceFacade.findBySearchQuery(user, "AccountTransactionFlowObj", searchTemplateQuery).getData();
                if (CollectionUtils.isEmpty(accountTransactionFlowList)) {
                    continue;
                }
                for (IObjectData flowData : accountTransactionFlowList) {
                    String paymentId = flowData.get(AccountTransactionFlowConst.Field.Payment.apiName, String.class);
                    if (StringUtils.isEmpty(paymentId)) {
                        continue;
                    }
                    flowData.set(AccountTransactionFlowConst.Field.RelateRecordObjectApiName.apiName, "PaymentObj");
                    flowData.set(AccountTransactionFlowConst.Field.RelateRecordObjectDataId.apiName, paymentId);
                    serviceFacade.updateObjectData(user, flowData);
                }
            } catch (Exception e) {
                log.warn("fixPaymentFlowData error,tenantId:{}", tenantId, e);
                failEis.add(tenantId);
            }
        }
        TenantIdModel.Result result = new TenantIdModel.Result();
        result.setTenantIds(failEis);
        return result;
    }


    @Override
    public TenantIdModel.Result whatFieldAddUsedIn(ServiceContext serviceContext, TenantIdModel.Arg arg) {
        log.info("whatFieldAddUsedIn begin, arg[{}]", arg);
        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return new TenantIdModel.Result();
        }

        TenantIdModel.Result result = new TenantIdModel.Result();
        List<String> successTenantIds = new ArrayList<>();

        for (int i = 0; i < arg.getTenantIds().size(); i++) {
            String tenantId = arg.getTenantIds().get(i);

            if (!ConfigCenter.iswhatFieldAddUsedInContinue) {
                log.info("whatFieldAddUsedIn stop, successTenantIds[{}], tenantId[{}], arg[{}]", successTenantIds, tenantId, arg);
                result.setTenantIds(successTenantIds);
                return result;
            }

            //是否开启客户校验
            AccountCheckSwitchEnum switchStatus = fundAccountConfigManager.getAccountCheckStatus(tenantId);
            log.info("getAccountCheckStatus, tenantId[{}], switchStatus[{}]", tenantId, switchStatus);
            if (!Objects.equals(switchStatus.getStatus(), AccountCheckSwitchEnum.OPENED.getStatus())) {
                continue;
            }

            caFieldManager.updateFieldUsedIn(tenantId, AccountFrozenRecordConstant.API_NAME, AccountFrozenRecordConstant.Field.CheckRecordObjectApiName.apiName);
            caFieldManager.updateFieldUsedIn(tenantId, AccountFrozenRecordConstant.API_NAME, AccountFrozenRecordConstant.Field.CheckRecordObjectDataId.apiName);
            caFieldManager.updateFieldUsedIn(tenantId, UnfreezeDetailConstant.API_NAME, UnfreezeDetailConstant.Field.UnfreezeObjectApiName.apiName);
            caFieldManager.updateFieldUsedIn(tenantId, UnfreezeDetailConstant.API_NAME, UnfreezeDetailConstant.Field.UnfreezeObjectDataId.apiName);
            caFieldManager.updateFieldUsedIn(tenantId, AccountTransactionFlowConst.API_NAME, AccountTransactionFlowConst.Field.RelateRecordObjectApiName.apiName);
            caFieldManager.updateFieldUsedIn(tenantId, AccountTransactionFlowConst.API_NAME, AccountTransactionFlowConst.Field.RelateRecordObjectDataId.apiName);
            successTenantIds.add(tenantId);
        }

        log.info("whatFieldAddUsedIn finish, successTenantIds.size[{}], successTenantIds[{}], arg[{}]", successTenantIds.size(), successTenantIds, arg);
        result.setTenantIds(successTenantIds);
        return result;
    }

    @Override
    public TenantIdModel.Result upgradeAccountCheckRule(ServiceContext serviceContext, TenantIdModel.Arg arg) {
        List<String> tenantIds = arg.getTenantIds();
        TenantIdModel.Result result = new TenantIdModel.Result();
        List<String> failEis = Lists.newArrayList();
        if (CollectionUtils.isEmpty(tenantIds)) {
            return result;
        }
        tenantIds.forEach(x -> {
            try {
                IObjectDescribe unfreezeDescribe = serviceFacade.findObject(x, UnfreezeDetailConstant.API_NAME);
                Map<String, IFieldDescribe> fieldDescribeMap = unfreezeDescribe.getFieldDescribeMap();
                User user = User.systemUser(x);
                if (!fieldDescribeMap.containsKey(UnfreezeDetailConstant.Field.UnfreezeObject.apiName)) {
                    WhatFieldDescribe unfreezeObject = WhatFieldDescribeBuilder.builder().apiName(UnfreezeDetailConstant.Field.UnfreezeObject.apiName).label(UnfreezeDetailConstant.Field.UnfreezeObject.label).apiNameField(UnfreezeDetailConstant.Field.UnfreezeObjectApiName.apiName).idField(UnfreezeDetailConstant.Field.UnfreezeObjectDataId.apiName).isRequired(false).build();
                    serviceFacade.addDescribeCustomField(user, UnfreezeDetailConstant.API_NAME, unfreezeObject.toJsonString(), Lists.newArrayList(), Lists.newArrayList());
                }
                if (!fieldDescribeMap.containsKey(UnfreezeDetailConstant.Field.UnfreezeObjectApiName.apiName)) {
                    TextFieldDescribe unfreezeObjectApiName = TextFieldDescribeBuilder.builder().apiName(UnfreezeDetailConstant.Field.UnfreezeObjectApiName.apiName).label(UnfreezeDetailConstant.Field.UnfreezeObjectApiName.label).maxLength(100).required(true).build();
                    serviceFacade.addDescribeCustomField(user, UnfreezeDetailConstant.API_NAME, unfreezeObjectApiName.toJsonString(), Lists.newArrayList(), Lists.newArrayList());

                }
                if (!fieldDescribeMap.containsKey(UnfreezeDetailConstant.Field.UnfreezeObjectDataId.apiName)) {
                    TextFieldDescribe unfreezeObjectDataId = TextFieldDescribeBuilder.builder().apiName(UnfreezeDetailConstant.Field.UnfreezeObjectDataId.apiName).label(UnfreezeDetailConstant.Field.UnfreezeObjectDataId.label).maxLength(100).required(true).build();
                    serviceFacade.addDescribeCustomField(user, UnfreezeDetailConstant.API_NAME, unfreezeObjectDataId.toJsonString(), Lists.newArrayList(), Lists.newArrayList());
                }
                List<IFilter> filters = Lists.newArrayList();
                SearchUtil.fillFilterIsNull(filters, AccountRuleUseRecordConstants.Field.RuleType.apiName);
                int size = 0;
                do {
                    SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
                    searchTemplateQuery.setFilters(filters);
                    searchTemplateQuery.setOrders(Lists.newArrayList());
                    searchTemplateQuery.setOffset(0);
                    searchTemplateQuery.setLimit(500);
                    List<IObjectData> ruleUseRecordList = serviceFacade.findBySearchQuery(user, AccountRuleUseRecordConstants.API_NAME, searchTemplateQuery).getData();
                    size = ruleUseRecordList.size();
                    ruleUseRecordList.forEach(ruleUseRecord -> {
                        String json = ruleUseRecord.get(AccountRuleUseRecordConstants.Field.CheckRule.apiName, String.class);
                        IObjectData ruleData = new ObjectData();
                        ruleData.fromJsonString(json);
                        String ruleType = ruleData.get(AccountCheckRuleConstants.Field.RuleType.apiName, String.class);
                        ruleUseRecord.set(AccountRuleUseRecordConstants.Field.RuleType.apiName, ruleType);
                        ruleUseRecord.set(AccountRuleUseRecordConstants.Field.RuleStage.apiName, RuleStageEnum.CheckValidate.value);
                    });
                    serviceFacade.batchUpdateByFields(user, ruleUseRecordList, Lists.newArrayList(AccountRuleUseRecordConstants.Field.RuleType.apiName, AccountRuleUseRecordConstants.Field.RuleStage.apiName));
                } while (size == 500);
                filters.clear();
                SearchUtil.fillFilterIsNull(filters, UnfreezeDetailConstant.Field.UnfreezeObjectApiName.apiName);
                do {
                    SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
                    searchTemplateQuery.setFilters(filters);
                    searchTemplateQuery.setOrders(Lists.newArrayList());
                    searchTemplateQuery.setOffset(0);
                    searchTemplateQuery.setLimit(500);
                    List<IObjectData> unfreezeList = serviceFacade.findBySearchQuery(user, UnfreezeDetailConstant.API_NAME, searchTemplateQuery).getData();
                    List<String> flowIds = Lists.newArrayList();
                    unfreezeList.forEach(unfreezeData -> {
                        String flowId = unfreezeData.get(UnfreezeDetailConstant.Field.AccountTransactionFlow.apiName, String.class);
                        flowIds.add(flowId);
                    });
                    size = unfreezeList.size();
                    List<IObjectData> flowDataList = serviceFacade.findObjectDataByIds(x, flowIds, AccountTransactionFlowConst.API_NAME);
                    Map<String, IObjectData> flowDataMap = flowDataList.stream().collect(Collectors.toMap(IObjectData::getId, Function.identity()));
                    unfreezeList.forEach(unfreezeData -> {
                        String flowId = unfreezeData.get(UnfreezeDetailConstant.Field.AccountTransactionFlow.apiName, String.class);
                        IObjectData flowData = flowDataMap.get(flowId);
                        String objectApiName = flowData.get(AccountTransactionFlowConst.Field.RelateRecordObjectApiName.apiName, String.class);
                        String objectDataId = flowData.get(AccountTransactionFlowConst.Field.RelateRecordObjectDataId.apiName, String.class);
                        unfreezeData.set(UnfreezeDetailConstant.Field.UnfreezeObjectApiName.apiName, objectApiName);
                        unfreezeData.set(UnfreezeDetailConstant.Field.UnfreezeObjectDataId.apiName, objectDataId);
                    });
                    serviceFacade.batchUpdateByFields(user, unfreezeList, Lists.newArrayList(UnfreezeDetailConstant.Field.UnfreezeObjectApiName.apiName, UnfreezeDetailConstant.Field.UnfreezeObjectDataId.apiName));
                } while (size == 500);
            } catch (Exception e) {
                log.warn("", e);
                failEis.add(x);
            }
        });
        result.setTenantIds(failEis);
        return result;
    }

    @Override
    public TenantIdModel.Result newCustomerAccountAddPrivilege(ServiceContext serviceContext, TenantIdModel.Arg arg) {
        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return new TenantIdModel.Result();
        }

        TenantIdModel.Result result = new TenantIdModel.Result();
        List<String> roleCodes = Lists.newArrayList(CommonConstants.PAYMENT_FINANCE_ROLE, CommonConstants.CRM_MANAGER_ROLE);   // 需要给CRM管理员加权限

        for (int i = 0; i < arg.getTenantIds().size(); i++) {
            String tenantId = arg.getTenantIds().get(i);
            ServiceContext ctx = generateServiceContext(tenantId);

            List<String> actionCodes = Lists.newArrayList("Abolish", "Delete");

            /**
             * 权限是否存在
             * 没有权限，查出来是：{"NewCustomerAccountObj||Abolish":[],"NewCustomerAccountObj||Delete":[]}
             * 有权限，查出来；{"NewCustomerAccountObj||Abolish":["00000000000000000000000000000006","00000000000000000000000000000002"],"NewCustomerAccountObj||Delete":["00000000000000000000000000000006","00000000000000000000000000000002"]}
             */
            Map<String, List<String>> privilegesMap = functionPrivilegeService.getHavePrivilegeRolesByActionCodes(ctx.getUser(), NewCustomerAccountConstants.API_NAME, actionCodes);

            actionCodes.forEach(actionCode -> {
                List<String> privileges = privilegesMap.get(FunctionCodeBuilder.build(NewCustomerAccountConstants.API_NAME, actionCode));
                if (CollectionUtils.isEmpty(privileges)) {
                    addPrivilegeToObjectAndRole(ctx.getUser(), actionCode, NewCustomerAccountConstants.API_NAME, roleCodes);
                }
            });
        }

        return result;
    }

    private boolean isAccountCheckRuleEnable(String tenantId) {
        User user = User.systemUser(tenantId);
        String configValue = configService.findTenantConfig(user, ConfigKeyEnum.ACCOUNT_CHECK_RULE.key);
        return ConfigKeyEnum.ACCOUNT_CHECK_RULE.enabled(configValue);
    }

    @Override
    public TenantIdModel.Result CheckRuleAddField(ServiceContext serviceContext, TenantIdModel.Arg arg) {
        TenantIdModel.Result result = new TenantIdModel.Result();
        List<String> eis = arg.getTenantIds();
        if (CollectionUtils.isEmpty(eis)) {
            return result;
        }
        User user;
//        List<String> openAccountCheckRuleTenantIds = new ArrayList<>();
//        for (String ei : eis) {
//            FundAccountSwitchEnum fundAccountStatusEnum = fundAccountManager.getFundAccountStatus(ei);
//            if (FundAccountSwitchEnum.FUND_ACCOUNT_OPEN.equals(fundAccountStatusEnum)) {
//                openAccountCheckRuleTenantIds.add(ei);
//            }
//        }
        List<String> failEis = Lists.newArrayList();
        for (String ei : eis) {
            if (!isAccountCheckRuleEnable(ei)) {
                continue;
            }
            user = User.systemUser(ei);
            IObjectDescribe objectDescribe = serviceFacade.findObject(ei, AccountCheckRuleConstants.API_NAME);
            TextFieldDescribe fieldDescribe = (TextFieldDescribe) objectDescribe.getFieldDescribe(AccountCheckRuleConstants.Field.CheckTriggerButton.apiName);
            if (Objects.isNull(fieldDescribe)) {
                TextFieldDescribe checkTriggerAction = TextFieldDescribeBuilder.builder().apiName(AccountCheckRuleConstants.Field.CheckTriggerButton.apiName).label(AccountCheckRuleConstants.Field.CheckTriggerButton.label).maxLength(200).build();
                List<FieldLayoutPojo> fieldLayoutPojoList = Lists.newArrayList();
                try {
                    serviceFacade.addDescribeCustomField(user, objectDescribe.getApiName(), checkTriggerAction.toJsonString(), fieldLayoutPojoList, Lists.newArrayList());
                } catch (Exception e) {
                    log.warn("check rule add field error tenantId:{}", ei, e);
                    failEis.add(ei);
                }
            }
        }
        result.setTenantIds(failEis);
        return result;
    }

    @Override
    public TenantIdModel.Result transferRuleUseRecord(ServiceContext serviceContext, TenantIdModel.Arg arg) {
        List<String> tenantIds = arg.getTenantIds();
        TenantIdModel.Result result = new TenantIdModel.Result();
        if (CollectionUtils.isEmpty(tenantIds)) {
            return result;
        }
        int limit = 1, offset;
        List<String> failEis = Lists.newArrayList();
        for (String tenantId : tenantIds) {
            if (!isAccountCheckRuleEnable(tenantId)) {
                continue;
            }
            offset = 0;
            List<IObjectData> dataList;
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            searchTemplateQuery.setLimit(limit);
            List<OrderBy> orderByList = Lists.newArrayList();
            SearchUtil.fillOrderBy(orderByList, "_id", true);
            searchTemplateQuery.setOrders(orderByList);
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, AccountRuleUseRecordConstants.Field.RuleType.apiName, AccountCheckRuleTypeEnum.Check_Reduce.getValue());
            searchTemplateQuery.setFilters(filters);
            try {
                do {
                    searchTemplateQuery.setOffset(offset);

                    dataList = serviceFacade.findBySearchQuery(User.systemUser(tenantId), AccountRuleUseRecordConstants.API_NAME, searchTemplateQuery).getData();
                    offset += CollectionUtils.size(dataList);
                    if (CollectionUtils.isEmpty(dataList)) {
                        break;
                    }
                    List<IObjectData> toUpdateList = Lists.newArrayList();
                    for (IObjectData objectData : dataList) {
                        String json = objectData.get(AccountRuleUseRecordConstants.Field.CheckRule.apiName, String.class);
                        IObjectData ruleData = new ObjectData();
                        ruleData.fromJsonString(json);
                        String checkTriggerAction = ruleData.get(AccountCheckRuleConstants.Field.CheckTriggerAction.apiName, String.class);
                        if (!ReduceTriggerActionEnum.Button.getValue().equals(checkTriggerAction)) {
                            ruleData.set(AccountCheckRuleConstants.Field.CheckTriggerAction.apiName, ReduceTriggerActionEnum.Button.getValue());
                            objectData.set(AccountRuleUseRecordConstants.Field.CheckRule.apiName, ObjectDataDocument.of(ruleData));
                            toUpdateList.add(objectData);
                        }
                    }
                    serviceFacade.batchUpdate(toUpdateList, User.systemUser(tenantId));
                } while (CollectionUtils.size(dataList) == limit);
            } catch (Exception e) {
                log.warn("updateRuleUseRecord error,tenantId:{}", tenantId, e);
                failEis.add(tenantId);
            }
        }
        result.setTenantIds(failEis);
        return result;
    }

    @Override
    public TenantIdModel.Result CheckRuleRefreshField(ServiceContext serviceContext, TenantIdModel.Arg arg) {
        TenantIdModel.Result result = new TenantIdModel.Result();
        List<String> eis = arg.getTenantIds();
        if (CollectionUtils.isEmpty(eis)) {
            return result;
        }

        List<String> failEis = Lists.newArrayList();
        int limit = 100;
        int offset;
        List<OrderBy> orderByList = Lists.newArrayList();
        SearchUtil.fillOrderBy(orderByList, "_id", true);
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(limit);
        searchTemplateQuery.setOrders(orderByList);
        IFilter filter = new Filter();
        IFilter filter2 = new Filter();
        filter.setFieldName("is_deleted");
        filter.setOperator(Operator.IN);
        filter.setFieldValues(Lists.newArrayList("0", "1"));
        filter2.setFieldName("tenant_id");
        filter2.setOperator(Operator.EQ);
        User user;
        List<IObjectData> dataList;
        for (String ei : eis) {
            if (!isAccountCheckRuleEnable(ei)) {
                continue;
            }
            offset = 0;
            user = User.systemUser(ei);
            List<IFilter> filters = new ArrayList<>();
            filter2.setFieldValues(Lists.newArrayList(ei));
            filters.add(filter);
            filters.add(filter2);
            try {
                do {
                    searchTemplateQuery.setFilters(filters);
                    searchTemplateQuery.setOffset(offset);
                    dataList = serviceFacade.findBySearchQuery(user, AccountCheckRuleConstants.API_NAME, searchTemplateQuery).getData();
                    offset += limit;
                    dataList.removeIf(x -> {
                        //直接扣减不用改
                        if (x.get(AccountCheckRuleConstants.Field.RuleType.apiName).equals(AccountCheckRuleTypeEnum.Direct_Reduce.getValue())) {
                            return true;
                        }
                        //校验扣减需要改
                        else if (x.get(AccountCheckRuleConstants.Field.RuleType.apiName).equals(AccountCheckRuleTypeEnum.Check_Reduce.getValue())) {
                            String checkTriggerAction = x.get(AccountCheckRuleConstants.Field.CheckTriggerAction.apiName, String.class);
                            String checkTriggerButton = x.get(AccountCheckRuleConstants.Field.CheckTriggerButton.apiName, String.class);
                            if (Strings.isNullOrEmpty(checkTriggerButton) && !Objects.equals(checkTriggerAction, ReduceTriggerActionEnum.FieldChange.getValue())) {
                                //第一步：修改check_trigger_button字段
                                x.set(AccountCheckRuleConstants.Field.CheckTriggerButton.apiName, checkTriggerAction);
                                //第二步：修改check_trigger_action字段
                                x.set(AccountCheckRuleConstants.Field.CheckTriggerAction.apiName, ReduceTriggerActionEnum.Button.getValue());
                                return false;
                            }
                        }
                        return true;
                    });
                    //更新该企业的账户校验规则数据
                    serviceFacade.batchUpdateByFields(user, dataList, Lists.newArrayList(AccountCheckRuleConstants.Field.CheckTriggerButton.apiName, AccountCheckRuleConstants.Field.CheckTriggerAction.apiName));
                } while (CollectionUtils.isNotEmpty(dataList));

            } catch (Exception e) {
                log.warn("check rule refresh data error tenantId:{}", ei, e);
                failEis.add(ei);
            }
        }
        result.setTenantIds(failEis);
        return result;
    }

    @Override
    public QueryConfigModel.Result queryConfig(ServiceContext serviceContext, QueryConfigModel.Arg arg) {
        QueryConfigModel.Result result = new QueryConfigModel.Result();
        Map<String, List<String>> configResultMap = Maps.newHashMap();
        List<String> tenantIds = arg.getTenantIds();
        String key = arg.getKey();
        if (CollectionUtils.isEmpty(tenantIds) || StringUtils.isEmpty(key)) {
            return result;
        }
        tenantIds.forEach(tenantId -> {
            String value = configService.findTenantConfig(User.systemUser(tenantId), key);
            if (StringUtils.isNotEmpty(value)) {
                List<String> eiResult = configResultMap.computeIfAbsent(value, k -> Lists.newArrayList());
                eiResult.add(tenantId);
            }
        });
        result.setConfigMap(configResultMap);
        return result;
    }

    @Override
    public CurlServiceImpl.TenantIdModel.Result changeEnterPaymentButtonFilter(ServiceContext serviceContext, TenantIdModel.Arg arg) {
        TenantIdModel.Result result = new TenantIdModel.Result();
        List<String> eis = arg.getTenantIds();
        if (CollectionUtils.isEmpty(eis)) {
            return result;
        }
        IObjectDescribe paymentObj = null;
        IUdefButton enterAccount_button_default = null;
        List<String> failEis = Lists.newArrayList();
        for (String ei : eis) {
            if (notOpenPaymentEnter(ei)) {
                continue;
            }
            try {
                paymentObj = serviceFacade.findObject(ei, PaymentConstants.API_NAME);
                enterAccount_button_default = serviceFacade.findButtonByApiName(User.systemUser(ei), ObjectAction.ENTER_ACCOUNT.getButtonApiName(), paymentObj);
                if (Objects.isNull(enterAccount_button_default)) {
                    continue;
                }
                List<IFilter> filters = Lists.newArrayList();
                List<Wheres> wheres = Lists.newArrayList();
                Wheres wheres1 = new Wheres();
                addFilter(filters, FundAccountBaseService.Const.ENTER_INTO_ACCOUNT, Operator.N, Lists.newArrayList(Boolean.TRUE.toString()));
                addFilter(filters, "life_status", Operator.EQ, Lists.newArrayList(ObjectLifeStatus.NORMAL.getCode()));
                addFilter(filters, PaymentConstants.Field.PaymentAmount.apiName, Operator.LTE, Lists.newArrayList("0"));
                addFilter(filters, PaymentConstants.Field.Customer.apiName, Operator.ISN, Lists.newArrayList());
                wheres1.setFilters(filters);
                wheres.add(wheres1);
                enterAccount_button_default.setWheres(wheres);
                serviceFacade.updateCustomButton(User.systemUser(ei), enterAccount_button_default);
            } catch (Exception e) {
                failEis.add(ei);
                log.warn("updateButtonFilter error,tenantId:{}", ei, e);
            }
        }
        result.setTenantIds(failEis);
        return result;
    }

    @Override
    public TenantIdModel.Result getOpenPaymentEnterAccountTenant(ServiceContext serviceContext, TenantIdModel.Arg arg) {
        List<String> eis = arg.getTenantIds();
        TenantIdModel.Result result = new TenantIdModel.Result();
        List<String> openPaymentEis = new ArrayList<>();
        for (String ei : eis) {
            if (notOpenPaymentEnter(ei)) {
                continue;
            }
            openPaymentEis.add(ei);
        }
        result.setTenantIds(openPaymentEis);
        return result;
    }

    @Override
    public List<IUdefButton> getButtonInfo(ServiceContext serviceContext, TenantIdModel.Arg arg) {
        List<IUdefButton> result = new ArrayList<>();
        List<String> eis = arg.getTenantIds();
        if (CollectionUtils.isEmpty(eis)) {
            return result;
        }
        IObjectDescribe paymentObj = null;
        IUdefButton enterAccount_button_default = null;
        List<String> failEis = Lists.newArrayList();
        for (String ei : eis) {
            if (notOpenPaymentEnter(ei)) {
                continue;
            }
            try {
                paymentObj = serviceFacade.findObject(ei, PaymentConstants.API_NAME);
                enterAccount_button_default = serviceFacade.findButtonByApiName(User.systemUser(ei), ObjectAction.ENTER_ACCOUNT.getButtonApiName(), paymentObj);
                if (Objects.isNull(enterAccount_button_default)) {
                    continue;
                }
                result.add(enterAccount_button_default);
            } catch (Exception e) {
                failEis.add(ei);
                log.warn("updateButtonFilter error,tenantId:{}", ei, e);
            }
        }
        return result;
    }

    private void addFilter(List<IFilter> filters, String fieldName, Operator operator, List<String> fieldValues) {
        IFilter filter = new Filter();
        filter.setFieldName(fieldName);
        filter.setFieldValues(fieldValues);
        filter.setOperator(operator);
        filters.add(filter);
    }

    public boolean notOpenPaymentEnter(String tenantId) {
        Map<String, String> configMap = configService.queryTenantConfigs(User.systemUser(tenantId), Arrays.asList(ConfigKeyEnum.NEW_CUSTOMER_ACCOUNT.key, ConfigKeyEnum.PAYMENT_ENTER_ACCOUNT_CONFIG_KEY.key));
        String fundAccountSwitchValue = configMap.get(ConfigKeyEnum.NEW_CUSTOMER_ACCOUNT.key);
        String paymentEnterAccountSwitchValue = configMap.get(ConfigKeyEnum.PAYMENT_ENTER_ACCOUNT_CONFIG_KEY.key);
        if (!FundAccountSwitchEnum.FUND_ACCOUNT_OPEN.getStatus().equals(fundAccountSwitchValue)) {
            return true;
        }
        if (!ConfigKeyEnum.PAYMENT_ENTER_ACCOUNT_CONFIG_KEY.enabled(paymentEnterAccountSwitchValue)) {
            return true;
        }
        logger.info("This tenant open the PaymentEnterAccount,tenantId: " + tenantId);
        return false;

    }

    private void addPrivilegeToObjectAndRole(User user, String actionCode, String objectApiName, List<String> roleCodes) {
        AuthContext authContext = buildAuthContext(user);

        //给对象加权限
        List<CreateFunctionPrivilege.FunctionPojo> functions = getFunctions(authContext.getTenantId(), objectApiName, actionCode);
        CreateFunctionPrivilege.Arg arg = CreateFunctionPrivilege.Arg.builder().authContext(authContext).functionPojoList(functions).build();
        Map<String, String> header = FunctionPrivilegeProxy.HeaderUtil.buildHeaders(user.getTenantId());
        log.info("functionPrivilegeProxy.createFunctionPrivilege, arg[{}], header[{}]", arg, header);
        CreateFunctionPrivilege.Result result = functionPrivilegeProxy.createFunctionPrivilege(arg, header);
        if (!result.isSuccess()) {
            log.warn("functionPrivilegeProxy.createFunctionPrivilege, failed arg[{}], header[{}], result[{}]", arg, header, result);
        }

        //给角色加权限
        roleCodes.forEach(roleCode -> {
            String funcCode = FunctionCodeBuilder.build(objectApiName, actionCode);
            addRoleFunctionPrivilege(authContext, roleCode, Lists.newArrayList(funcCode));
        });
    }

    private List<CreateFunctionPrivilege.FunctionPojo> getFunctions(String tenantId, String apiName, String actionCode) {
        ArrayList userDefinedFunctionPojoList = Lists.newArrayList();
        CreateFunctionPrivilege.FunctionPojo pojo = new CreateFunctionPrivilege.FunctionPojo();
        pojo.setAppId("CRM");
        pojo.setTenantId(tenantId);
        pojo.setFuncType(Integer.valueOf(1));
        pojo.setParentCode("00000000000000000000000000000000");
        pojo.setFuncName(ObjectAction.of(actionCode).getActionLabel());
        pojo.setFuncCode(FunctionCodeBuilder.build(apiName, actionCode));
        userDefinedFunctionPojoList.add(pojo);

        return userDefinedFunctionPojoList;
    }


    @Override
    public TenantIdModel.Result accountFrozenRecordDeleteCreatePrivilege(ServiceContext serviceContext, TenantIdModel.Arg arg) {
        log.info("accountFrozenRecordDeleteCreatePrivilege begin arg[{}]", arg);
        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return new TenantIdModel.Result();
        }

        TenantIdModel.Result result = new TenantIdModel.Result();

        List<String> roleCodes = Lists.newArrayList(CommonConstants.PAYMENT_FINANCE_ROLE, CommonConstants.CRM_MANAGER_ROLE);   // 需要给CRM管理员加权限

        for (int i = 0; i < arg.getTenantIds().size(); i++) {
            String tenantId = arg.getTenantIds().get(i);

            log.info("accountFrozenRecordDeleteCreatePrivilege begin i[{}], tenantId[{}], arg[{}]", i, tenantId, arg);

            ServiceContext ctx = generateServiceContext(tenantId);

            String actionCode = "Add";
            /**
             * 权限是否存在
             * 没权限，查出来：{"AccountFrozenRecordObj||Add":[]}
             * 有权限，查出来；{"AccountFrozenRecordObj||Abolish":["00000000000000000000000000000006"]}
             */
            Map<String, List<String>> privilegesMap = functionPrivilegeService.getHavePrivilegeRolesByActionCodes(ctx.getUser(), AccountFrozenRecordConstant.API_NAME, Lists.newArrayList(actionCode));

            List<String> privileges = privilegesMap.get(FunctionCodeBuilder.build(AccountFrozenRecordConstant.API_NAME, actionCode));
            log.info("accountFrozenRecordDeleteCreatePrivilege begin i[{}], tenantId[{}], arg[{}], privileges[{}]", i, tenantId, arg, privileges);
            if (!CollectionUtils.isEmpty(privileges)) {
                deletePrivilege(ctx.getUser(), actionCode, AccountFrozenRecordConstant.API_NAME, roleCodes);
            }
        }

        log.info("accountFrozenRecordDeleteCreatePrivilege end arg[{}]", arg);
        return result;
    }

    @Override
    public TenantIdModel.Result unfreezeDetailDeleteCreatePrivilege(ServiceContext serviceContext, TenantIdModel.Arg arg) {
        log.info("unfreezeDetailDeleteCreatePrivilege begin arg[{}]", arg);
        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return new TenantIdModel.Result();
        }

        TenantIdModel.Result result = new TenantIdModel.Result();

        List<String> roleCodes = Lists.newArrayList(CommonConstants.PAYMENT_FINANCE_ROLE, CommonConstants.CRM_MANAGER_ROLE);   // 需要给CRM管理员加权限

        for (int i = 0; i < arg.getTenantIds().size(); i++) {
            String tenantId = arg.getTenantIds().get(i);

            log.info("unfreezeDetailDeleteCreatePrivilege begin i[{}], tenantId[{}], arg[{}]", i, tenantId, arg);

            ServiceContext ctx = generateServiceContext(tenantId);

            String actionCode = "Add";
            /**
             * 权限是否存在
             * 没权限，查出来：{"UnfreezeDetailObj||Add":[]}
             * 有权限，查出来；{"UnfreezeDetailObj||Add":["00000000000000000000000000000006"]}
             */
            Map<String, List<String>> privilegesMap = functionPrivilegeService.getHavePrivilegeRolesByActionCodes(ctx.getUser(), UnfreezeDetailConstant.API_NAME, Lists.newArrayList(actionCode));

            List<String> privileges = privilegesMap.get(FunctionCodeBuilder.build(UnfreezeDetailConstant.API_NAME, actionCode));
            log.info("unfreezeDetailDeleteCreatePrivilege begin i[{}], tenantId[{}], arg[{}], privileges[{}]", i, tenantId, arg, privileges);
            if (!CollectionUtils.isEmpty(privileges)) {
                deletePrivilege(ctx.getUser(), actionCode, UnfreezeDetailConstant.API_NAME, roleCodes);
            }
        }

        log.info("unfreezeDetailDeleteCreatePrivilege end arg[{}]", arg);
        return result;
    }

    @Override
    public TenantIdModel.Result updateFieldMaxLength(ServiceContext serviceContext, TenantIdModel.Arg arg) {
        List<String> tenantIds = arg.getTenantIds();
        TenantIdModel.Result result = new TenantIdModel.Result();
        int toMaxLength = arg.getMaxLength();
        String objectApiName = arg.getObjectApiName();
        String fieldApiName = arg.getFieldName();
        if (CollectionUtils.isEmpty(tenantIds) || toMaxLength <= 0 || StringUtils.isAnyEmpty(objectApiName, fieldApiName)) {
            return result;
        }
        List<String> failEis = Lists.newArrayList();
        for (String tenantId : tenantIds) {
            try {
                IObjectDescribe objectDescribe = serviceFacade.findObject(tenantId, objectApiName);
                IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(fieldApiName);
                if (fieldDescribe instanceof TextFieldDescribe) {
                    TextFieldDescribe textFieldDescribe = ((TextFieldDescribe) fieldDescribe);
                    Integer maxLength = textFieldDescribe.getMaxLength();
                    if (Objects.nonNull(maxLength) && maxLength != toMaxLength) {
                        textFieldDescribe.setMaxLength(toMaxLength);
                        serviceFacade.updateFieldDescribe(objectDescribe, Lists.newArrayList(textFieldDescribe));
                    }
                }
            } catch (Exception e) {
                log.warn("update extend content max length error:{}", tenantId, e);
                failEis.add(tenantId);
            }
        }
        result.setTenantIds(failEis);
        return result;
    }

    @Override
    public Map<String, Object> initCustomerAccountButton(ServiceContext serviceContext, CurlModel.InitCustomerAccountButton arg) {
        Set<String> tenantIds = arg.getTenantIds();
        Map<String, Object> result = Maps.newHashMap();
        if (CollectionUtils.isEmpty(tenantIds)) {
            return result;
        }
        Set<String> failEis = Sets.newHashSet();
        for (String tenantId : tenantIds) {
            try {
                boolean fundAccountEnable = fundAccountConfigManager.isFundAccountEnable(tenantId);
                if (!fundAccountEnable) {
                    continue;
                }
                User user = User.systemUser(tenantId);
                String ea = serviceFacade.getEAByEI(user.getTenantId());
                RequestContext requestContext = RequestContext.builder().tenantId(tenantId).user(user).ea(ea).build();
                ServiceContext eiServiceContext = new ServiceContext(requestContext, null, null);
                boolean dhtEnable = fundAccountManager.dhtEnable(eiServiceContext);
                //初始化对象的功能权限 账户收支流水
                if (BooleanUtils.isTrue(arg.getNeedCreateFlowFuncCode())) {
                    userDefinedButtonService.createUserDefinedButton(user, AccountTransactionFlowConst.API_NAME, ObjectAction.MULTI_ACCOUNT_REDUCE.getButtonApiName(), ObjectAction.MULTI_ACCOUNT_REDUCE.getActionLabel(), Lists.newArrayList(CRM_MANAGER_ROLE));
                    if (dhtEnable) {
                        try {
                            functionPrivilegeService.rolesAddFuncAccess(user, AccountTransactionFlowConst.API_NAME, ObjectAction.MULTI_ACCOUNT_REDUCE.getActionCode(), Lists.newArrayList(ORDERING_PERSON_ROLE));
                        } catch (Exception e) {
                            log.warn("assign dht role function error,tenantId:{}", tenantId, e);
                            failEis.add(tenantId);
                        }
                    }
                }
                fundAccountManager.initCustomerAccountButton(eiServiceContext, dhtEnable);
            } catch (Exception e) {
                failEis.add(tenantId);
                log.warn("initCustomerAccountButton error,tenantId:{}", tenantId, e);
            }
        }
        result.put("failEis", failEis);
        return result;
    }

    @Override
    public Map<String, Object> deleteFunc(ServiceContext serviceContext, CurlModel.DeleteFuncCodeArg arg) {
        Map<String, Object> result = Maps.newHashMap();
        List<String> tenantIds = arg.getTenantIds();
        List<String> funcCodes = arg.getFuncCodes();
        if (CollectionUtils.isEmpty(tenantIds) || CollectionUtils.isEmpty(funcCodes)) {
            return result;
        }
        Set<String> failEis = Sets.newHashSet();
        for (String tenantId : tenantIds) {
            User user = User.systemUser(tenantId);
            try {
                functionPrivilegeService.batchDelFunc(user, funcCodes);
            } catch (Exception e) {
                failEis.add(tenantId);
                log.warn("deleteFuncCode error,tenantId:{},funcCodes:{}", tenantId, funcCodes, e);
            }
        }
        result.put("failEis", failEis);
        return result;
    }

    @Override
    public TenantIdModel.Result chargeOffUpdateFieldDescribe(ServiceContext serviceContext, TenantIdModel.Arg arg) {
        List<String> tenantIds = arg.getTenantIds();
        TenantIdModel.Result result = new TenantIdModel.Result();
        if (CollectionUtils.isEmpty(tenantIds)) {
            return result;
        }
        boolean ignoreUpdateDescribe = BooleanUtils.isTrue(arg.getIgnoreUpdateDescribe());
        List<String> failEis = Lists.newArrayList();
        for (String tenantId : tenantIds) {
            try {
                if (!ignoreUpdateDescribe) {
                    IObjectDescribe objectDescribe = serviceFacade.findObject(tenantId, AccountFrozenRecordConstant.API_NAME);
                    SelectOneFieldDescribe entryStatusFieldDescribe = (SelectOneFieldDescribe) objectDescribe.getFieldDescribe(AccountFrozenRecordConstant.Field.EntryStatus.apiName);
                    RecordTypeFieldDescribe recordTypeFieldDescribe = (RecordTypeFieldDescribe) objectDescribe.getFieldDescribe(SystemConstants.Field.RecordType.apiName);
                    List<ISelectOption> entryStatusOptions = entryStatusFieldDescribe.getSelectOptions();
                    List<IFieldDescribe> updateFieldDescribeList = Lists.newArrayList();
                    if (entryStatusOptions.stream().noneMatch(x -> x.getValue().equals(EntryStatusEnum.PART_CHARGE_OFF.getValue()))) {
                        entryStatusOptions.add(SelectOptionBuilder.builder().value(EntryStatusEnum.PART_CHARGE_OFF.getValue()).label(EntryStatusEnum.PART_CHARGE_OFF.getLabel()).build());
                        entryStatusFieldDescribe.setSelectOptions(entryStatusOptions);
                        updateFieldDescribeList.add(entryStatusFieldDescribe);
                    }
                    List<IRecordTypeOption> recordTypeOptions = recordTypeFieldDescribe.getRecordTypeOptions();
                    if (recordTypeOptions.stream().noneMatch(x -> x.getApiName().equals(AccountFrozenRecordConstant.RecordType.CHARGE_OFF_RECORD_TYPE.value))) {
                        recordTypeOptions.add(RecordTypeOptionBuilder.builder().apiName(AccountFrozenRecordConstant.RecordType.CHARGE_OFF_RECORD_TYPE.value).label("红冲单据").build());
                        recordTypeFieldDescribe.setRecordTypeOptions(recordTypeOptions);
                        updateFieldDescribeList.add(recordTypeFieldDescribe);
                    }
                    if (!updateFieldDescribeList.isEmpty()) {
                        serviceFacade.updateFieldDescribe(objectDescribe, updateFieldDescribeList);
                    }
                }
                User user = User.systemUser(tenantId);
                RequestContext requestContext = RequestContext.builder().tenantId(tenantId).user(user).build();
                ServiceContext tenantServiceContext = new ServiceContext(requestContext, serviceContext.getServiceName(), serviceContext.getServiceMethod());

                List<IObjectData> checkRuleDataList = queryCheckRules(user);
                Set<String> reduceObjectApiNames = checkRuleDataList.stream().map(x -> {
                    String reduceObjectApiName = x.get(AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName, String.class);
                    return reduceObjectApiName == null ? "" : reduceObjectApiName;
                }).filter(StringUtils::isNotEmpty).collect(Collectors.toSet());
                reduceObjectApiNames.forEach(reduceObjectApiName -> {
                    caButtonManager.addForEditAndInvalid(tenantServiceContext, reduceObjectApiName, "Abolish_button_default", true);
                });
            } catch (Exception e) {
                log.warn("chargeOff updateFieldDescribe error,tenantId:{}", tenantId, e);
                failEis.add(tenantId);
            }
        }
        result.setTenantIds(failEis);
        return result;
    }

    @Override
    public TenantIdModel.Result addBizActionByCheckRule(ServiceContext serviceContext, Map<String,String> arg) {
        TenantIdModel.Result result = new TenantIdModel.Result();
        String checkRuleId = arg.get("id");
        IObjectData objectData = serviceFacade.findObjectData(serviceContext.getUser(), checkRuleId, AccountCheckRuleConstants.API_NAME);
        caButtonManager.add(serviceContext.getRequestContext(), objectData);
        return result;
    }

    @Override
    public Map<String, Object> scanFlowRevenueType(ServiceContext serviceContext, CurlModel.ScanFlowRevenueTypeArg arg) {
        Set<String> tenantIds = arg.getTenantIds();
        Map<String, Object> result = Maps.newHashMap();
        Optional<RevenueTypeEnum> revenueTypeOptional = RevenueTypeEnum.of(arg.getRevenueType());
        if (CollectionUtils.isEmpty(tenantIds) || !revenueTypeOptional.isPresent()) {
            return result;
        }
        String revenueType = revenueTypeOptional.get().getValue();
        List<String> failEis = Lists.newArrayList();
        for (String tenantId : tenantIds) {
            try {
                User user = User.systemUser(tenantId);
                SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
                List<IFilter> filters = Lists.newArrayList();
                SearchUtil.fillFilterEq(filters, AccountTransactionFlowConst.Field.RevenueType.apiName, revenueType);
                SearchUtil.fillFilterEq(filters, AccountTransactionFlowConst.Field.EntryStatus.apiName, EntryStatusEnum.AlreadyEntry.getValue());
                if (RevenueTypeEnum.PaymentCharge.getValue().equals(revenueType)) {
                    SearchUtil.fillFilterNotEq(filters, AccountTransactionFlowConst.Field.RelateRecordObjectApiName.apiName, "PaymentObj");
                } else if (RevenueTypeEnum.Fee.getValue().equals(revenueType)) {
                    SearchUtil.fillFilterNotEq(filters, AccountTransactionFlowConst.Field.RelateRecordObjectApiName.apiName, "TPMDealerActivityCostObj");
                }
                searchTemplateQuery.setFilters(filters);
                searchTemplateQuery.setLimit(1);
                QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, AccountTransactionFlowConst.API_NAME, searchTemplateQuery);
                Integer total = queryResult.getTotalNumber();
                if (total > 0) {
                    result.put(tenantId, total);
                }
            } catch (Exception e) {
                failEis.add(tenantId);
                log.warn("tenantId:{}", tenantId, e);
            }
        }
        log.info("scanRevenueType,result:{},failEis:{}", result, failEis);
        result.put("failEis", failEis);
        return result;
    }

    @Override
    public Map<String, Object> scanUnfreezeDirectReduceSame(ServiceContext serviceContext, CurlModel.TenantIds arg) {
        Map<String, Object> result = Maps.newHashMap();
        List<String> tenantIds = arg.getTenantIds();
        if (CollectionUtils.isEmpty(tenantIds)) {
            return result;
        }
        for (String tenantId : tenantIds) {
            User user = User.systemUser(tenantId);
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            searchTemplateQuery.setLimit(1000);
            searchTemplateQuery.setOffset(0);
            try {
                List<IObjectData> dataList = serviceFacade.findBySearchQuery(user, AccountCheckRuleConstants.API_NAME, searchTemplateQuery).getData();
                Set<String> unfreezeObjects = Sets.newHashSet();
                Set<String> directReduceObjects = Sets.newHashSet();
                for (IObjectData objectData : dataList) {
                    String ruleType = objectData.get(AccountCheckRuleConstants.Field.RuleType.apiName, String.class);
                    String thirdStageObject = objectData.get(AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName, String.class);
                    if (AccountCheckRuleTypeEnum.Direct_Reduce.getValue().equals(ruleType)) {
                        directReduceObjects.add(thirdStageObject);
                    } else if (AccountCheckRuleTypeEnum.Check_Reduce.getValue().equals(ruleType)) {
                        unfreezeObjects.add(thirdStageObject);
                    }
                }
                Set<String> sameObjects = unfreezeObjects.stream().filter(directReduceObjects::contains).collect(Collectors.toSet());
                log.info("scanUnfreezeDirectReduceSame tenantId:{},sameObjects:{}", tenantId, sameObjects);
                if (!sameObjects.isEmpty()) {
                    result.put(tenantId, sameObjects);
                }
            } catch (Exception e) {
                log.warn("tenantId:{}", tenantId, e);
            }
        }
        return result;
    }

    @Override
    public CurlModel.Result testCrmNotify(ServiceContext serviceContext, CurlModel.TestCrmNotifyArg arg) {
        String tenantId = arg.getTenantId();
        User user = User.systemUser(tenantId);
        List<IObjectData> objectDatas = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, Lists.newArrayList(arg.getObjectDataId()), arg.getObjectApiName());
        caNotifyManager.notifyForPaymentCreateFail(user, objectDatas.get(0), "--失败原因--");
        return new CurlModel.Result();
    }

    @Override
    public CurlModel.Result testDeleteButton(ServiceContext serviceContext, CurlModel.TestDeleteArg arg) {
        caButtonManager.deleteButton(serviceContext, arg.getObjectApiName(), arg.getButtonApiName());
        return new CurlModel.Result();
    }

    @Override
    public CurlModel.Result testDeleteField(ServiceContext serviceContext, CurlModel.TestDeleteFieldArg arg) {
        try {
            commonDescribeManager.deleteField(serviceContext.getTenantId(), arg.getObjectApiName(), arg.getFieldApiName());
        } catch (MetadataServiceException e) {
            log.warn("testDeleteField arg[{}]", arg, e);
        }
        return new CurlModel.Result();
    }

    @Override
    public CurlModel.AddPluginInstanceForComponentReduceAccountCheckRuleResult addPluginInstanceForComponentReduceAccountCheckRule(ServiceContext serviceContext, CurlModel.AddPluginInstanceForComponentReduceAccountCheckRuleArg arg) {
        List<String> notOpenTenantIds = Lists.newArrayList();
        List<String> successTenantIds = Lists.newArrayList();

        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return new CurlModel.AddPluginInstanceForComponentReduceAccountCheckRuleResult(notOpenTenantIds, successTenantIds);
        }

        for (String tenantId : arg.getTenantIds()) {
            log.info("addPluginInstanceForComponentReduceAccountCheckRule begin tenantId[{}]", tenantId);

            boolean isAccountCheckRuleOpen = fundAccountConfigManager.isAccountCheckRuleOpen(tenantId);
            if (!isAccountCheckRuleOpen) {
                notOpenTenantIds.add(tenantId);
                continue;
            }

            accountCheckRuleManager.createOrEnableLayoutCustomerAccountComponentCheckPluginInstance(tenantId);
            successTenantIds.add(tenantId);
            log.info("addPluginInstanceForComponentReduceAccountCheckRule end   tenantId[{}]", tenantId);
        }

        return new CurlModel.AddPluginInstanceForComponentReduceAccountCheckRuleResult(notOpenTenantIds, successTenantIds);
    }

    @Override
    public CurlModel.AddCustomerAccountPluginInstanceForInitOutComeAuthResult addCustomerAccountPluginInstanceForInitOutComeAuth(ServiceContext serviceContext, CurlModel.AddCustomerAccountPluginInstanceForInitOutComeAuthArg arg) {
        List<String> notOpenTenantIds = Lists.newArrayList();
        List<String> successTenantIds = Lists.newArrayList();

        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return new CurlModel.AddCustomerAccountPluginInstanceForInitOutComeAuthResult(notOpenTenantIds, successTenantIds);
        }

        for (String tenantId : arg.getTenantIds()) {
            log.info("addCustomerAccountPluginInstanceForInitOutComeAuth begin tenantId[{}]", tenantId);

            boolean isAccountAuthOpen = fundAccountConfigManager.isAccountAuthOpen(tenantId);
            if (!isAccountAuthOpen) {
                notOpenTenantIds.add(tenantId);
                continue;
            }

            fAccountAuthorizationManager.createOrEnableCustomerAccountPluginInstance(tenantId);
            successTenantIds.add(tenantId);
            log.info("addCustomerAccountPluginInstanceForInitOutComeAuth end   tenantId[{}]", tenantId);
        }

        return new CurlModel.AddCustomerAccountPluginInstanceForInitOutComeAuthResult(notOpenTenantIds, successTenantIds);
    }

    @Override
    public CurlModel.FixComponentReduceAccountCheckRuleDataResult fixComponentReduceAccountCheckRuleData(ServiceContext serviceContext, CurlModel.FixComponentReduceAccountCheckRuleDataArg arg) {
        List<String> notOpenTenantIds = Lists.newArrayList();
        List<String> successTenantIds = Lists.newArrayList();

        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return new CurlModel.FixComponentReduceAccountCheckRuleDataResult(notOpenTenantIds, successTenantIds);
        }

        for (String tenantId : arg.getTenantIds()) {
            log.info("fixComponentReduceAccountCheckRuleData begin tenantId[{}]", tenantId);

            boolean isAccountCheckRuleOpen = fundAccountConfigManager.isAccountCheckRuleOpen(tenantId);
            if (!isAccountCheckRuleOpen) {
                notOpenTenantIds.add(tenantId);
                continue;
            }

            accountCheckRuleManager.fixComponentReduceAccountCheckRuleData(tenantId);
            successTenantIds.add(tenantId);
            log.info("fixComponentReduceAccountCheckRuleData end   tenantId[{}]", tenantId);
        }

        return new CurlModel.FixComponentReduceAccountCheckRuleDataResult(notOpenTenantIds, successTenantIds);
    }

    @Override
    public CurlModel.FixComponentReduceAccountCheckRuleDataReduceMappingResult fixComponentReduceAccountCheckRuleDataReduceMapping(ServiceContext serviceContext, CurlModel.FixComponentReduceAccountCheckRuleDataReduceMappingArg arg) {
        List<String> notOpenTenantIds = Lists.newArrayList();
        List<String> successTenantIds = Lists.newArrayList();

        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return new CurlModel.FixComponentReduceAccountCheckRuleDataReduceMappingResult(notOpenTenantIds, successTenantIds);
        }

        //查询sourceTenantId的【组件扣减】的【校验规则】
        String sourceTenantId = arg.getSourceTenantId();
        List<String> fields = Lists.newArrayList(SystemConstants.Field.Id.apiName, AccountCheckRuleConstants.Field.Name.apiName, AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName, AccountCheckRuleConstants.Field.ReduceMapping.apiName);
        List<IObjectData> sourceComponentReduceAccountCheckRuleDatas = accountCheckRuleManager.getAllAccountCheckRuleDatas(sourceTenantId, false, fields, AccountCheckRuleTypeEnum.Component_Reduce.getValue(), arg.getReduceRelatedObjectApiNames());
        if (CollectionUtils.isEmpty(sourceComponentReduceAccountCheckRuleDatas)) {
            log.info("fixComponentReduceAccountCheckRuleDataReduceMapping allComponentReduceAccountCheckRuleDatas is empty, sourceTenantId[{}]", sourceTenantId);
            return new CurlModel.FixComponentReduceAccountCheckRuleDataReduceMappingResult(notOpenTenantIds, successTenantIds);
        }

        for (String tenantId : arg.getTenantIds()) {
            log.info("fixComponentReduceAccountCheckRuleDataReduceMapping begin tenantId[{}]", tenantId);

            boolean isAccountCheckRuleOpen = fundAccountConfigManager.isAccountCheckRuleOpen(tenantId);
            if (!isAccountCheckRuleOpen) {
                notOpenTenantIds.add(tenantId);
                continue;
            }

            accountCheckRuleManager.fixComponentReduceAccountCheckRuleDataReduceMapping(tenantId, arg.getReduceRelatedObjectApiNames(), sourceComponentReduceAccountCheckRuleDatas);
            successTenantIds.add(tenantId);
            log.info("fixComponentReduceAccountCheckRuleDataReduceMapping end   tenantId[{}]", tenantId);
        }

        return new CurlModel.FixComponentReduceAccountCheckRuleDataReduceMappingResult(notOpenTenantIds, successTenantIds);
    }

    @Override
    public CurlModel.FixSalesOrderComponentReduceAccountCheckRuleDataReduceMappingResult fixSalesOrderComponentReduceAccountCheckRuleDataReduceMapping(ServiceContext serviceContext, CurlModel.FixSalesOrderComponentReduceAccountCheckRuleDataReduceMappingArg arg) {
        List<String> notOpenTenantIds = Lists.newArrayList();
        List<String> accountCheckRuleNoExistTenantIds = Lists.newArrayList();

        //sourceTenantId有的【账户】，在targetTenantId不存在
        Map<String, List<String>> tenantId2NotExistSourceFundAccountNames = Maps.newHashMap();
        //sourceTenantId有的【扣减字段】，在targetTenantId不存在
        Map<String, List<String>> tenantId2NotExistSourceFieldLabels = Maps.newHashMap();

        //targetTenantId 的 reduce_mapping 为空的
        Set<String> reduceMappingIsEmptyTenantIds = new HashSet<>();

        //targetTenantId 的 reduce_mapping，里面有的账户Id没有值
        Set<String> reduceMappingAccountIdEmtpyTenantIds = new HashSet<>();
        //targetTenantId 的 reduce_mapping，里面有的账户ID有值，但是对应的账户不存在
        Map<String, List<String>> tenantId2reduceMappingAccountIdNotExistTenantIds = Maps.newHashMap();

        //targetTenantId 的 reduce_mapping，里面expense_amount对应的字段apiName为空
        Set<String> reduceMappingExpenseAmountFieldApiNameEmptyTenantIds = new HashSet<>();
        //targetTenantId 的 reduce_mapping，里面expense_amount对应的字段apiName有值，对应的账户不存在
        Map<String, List<String>> tenantId2educeMappingNotExistExpenseAmountApiNames = Maps.newHashMap();

        List<String> successTenantIds = Lists.newArrayList();
        List<String> failTenantIds = Lists.newArrayList();

        if (CollectionUtils.isEmpty(arg.getTargetTenantIds())) {
            return new CurlModel.FixSalesOrderComponentReduceAccountCheckRuleDataReduceMappingResult(notOpenTenantIds, accountCheckRuleNoExistTenantIds,
                    tenantId2NotExistSourceFundAccountNames.keySet(), tenantId2NotExistSourceFundAccountNames,
                    tenantId2NotExistSourceFieldLabels.keySet(), tenantId2NotExistSourceFieldLabels,
                    reduceMappingIsEmptyTenantIds,
                    reduceMappingAccountIdEmtpyTenantIds,
                    tenantId2reduceMappingAccountIdNotExistTenantIds.keySet(), tenantId2reduceMappingAccountIdNotExistTenantIds,
                    reduceMappingExpenseAmountFieldApiNameEmptyTenantIds,
                    tenantId2educeMappingNotExistExpenseAmountApiNames.keySet(), tenantId2educeMappingNotExistExpenseAmountApiNames,
                    successTenantIds, failTenantIds);
        }

        String reduceRelatedObjectApiName = arg.getReduceRelatedObjectApiName();
        String sourceTenantId = arg.getSourceTenantId();
        IObjectData sourceComponentReduceAccountCheckRuleData = accountCheckRuleManager.getComponentReduceAccountCheckRuleData(sourceTenantId, reduceRelatedObjectApiName);
        List<IObjectData> sourceFundAccounts = fundAccountManager.getAccounts(sourceTenantId, false);

        for (String targetTenantId : arg.getTargetTenantIds()) {
            try {
                log.info("fixSalesOrderComponentReduceAccountCheckRuleDataReduceMapping begin targetTenantId[{}]", targetTenantId);

                boolean isAccountCheckRuleOpen = fundAccountConfigManager.isAccountCheckRuleOpen(targetTenantId);
                if (!isAccountCheckRuleOpen) {
                    notOpenTenantIds.add(targetTenantId);
                    continue;
                }

                List<ObjectMappingModel> sourceObjectMappings = RuleHandlerUtil.getObjectMapping(sourceComponentReduceAccountCheckRuleData, AccountCheckRuleConstants.Field.ReduceMapping.apiName);

                boolean hasNotExistCannotReplace = false;
                // '模板企业' 有的账户，targetTenantId中是否存在
                List<IObjectData> targetFundAccounts = fundAccountManager.getAccounts(targetTenantId, false);
                Set<String> sourceFundAccountIds = sourceObjectMappings.stream().map(ObjectMappingModel::getFundAccountId).collect(Collectors.toSet());
                List<String> targetNotExistFundAccountNames = fundAccountManager.getTargetNotExistFundAccountNames(sourceFundAccountIds, sourceFundAccounts, targetFundAccounts);
                if (CollectionUtils.isNotEmpty(targetNotExistFundAccountNames)) {
                    tenantId2NotExistSourceFundAccountNames.put(targetTenantId, targetNotExistFundAccountNames);
                    hasNotExistCannotReplace = true;
                }

                //'模板企业' 扣减字段，在targetTenantId中不存在
                Set<String> sourceFieldApiNames = getExpenseAmountFieldApiNames(sourceObjectMappings);
                List<String> sourceFieldLabels = commonDescribeManager.getFieldLabelList(sourceTenantId, reduceRelatedObjectApiName, Lists.newArrayList(sourceFieldApiNames));
                List<String> notExistFieldLabels = commonDescribeManager.getNotExistFieldLabelList(targetTenantId, reduceRelatedObjectApiName, sourceFieldLabels);
                if (!CollectionUtils.isEmpty(notExistFieldLabels)) {
                    tenantId2NotExistSourceFieldLabels.put(targetTenantId, notExistFieldLabels);
                    hasNotExistCannotReplace = true;
                }

                //查出订单的'组件扣减'的校验规则
                IObjectData targetComponentReduceAccountCheckRuleData = accountCheckRuleManager.getComponentReduceAccountCheckRuleData(targetTenantId, reduceRelatedObjectApiName);
                if (targetComponentReduceAccountCheckRuleData == null) {
                    accountCheckRuleNoExistTenantIds.add(targetTenantId);
                    continue;
                }

                boolean needReplace = false;
                //reduce_mapping是否有账户ID不存在
                List<ObjectMappingModel> targetObjectMappings = RuleHandlerUtil.getObjectMapping(targetComponentReduceAccountCheckRuleData, AccountCheckRuleConstants.Field.ReduceMapping.apiName);
                if (CollectionUtils.isEmpty(targetObjectMappings)) {
                    reduceMappingIsEmptyTenantIds.add(targetTenantId);
                    needReplace = true;
                } else {
                    List<String> targetFundAccountIds = Lists.newArrayList();
                    List<String> expenseAmountMappingFieldApiNames = Lists.newArrayList();
                    for (ObjectMappingModel targetObjectMapping : targetObjectMappings) {
                        if (Strings.isNullOrEmpty(targetObjectMapping.getFundAccountId())) {
                            reduceMappingAccountIdEmtpyTenantIds.add(targetTenantId);
                            needReplace = true;
                        } else {
                            targetFundAccountIds.add(targetObjectMapping.getFundAccountId());
                        }

                        String expenseAmountMappingFieldApiName = AccountCheckRuleMappingUtil.getMappingFieldApiNameFromFieldMappings(targetObjectMapping.getFieldMappingList(), "expense_amount");
                        if (Strings.isNullOrEmpty(expenseAmountMappingFieldApiName)) {
                            reduceMappingExpenseAmountFieldApiNameEmptyTenantIds.add(targetTenantId);
                            needReplace = true;
                        } else {
                            expenseAmountMappingFieldApiNames.add(expenseAmountMappingFieldApiName);
                        }
                    }

                    //targetFundAccountIds哪些不存在的
                    List<String> notExistTargetFundAccountIds = fundAccountManager.getNoExistFundAccountIds(targetFundAccountIds, targetFundAccounts);
                    if (CollectionUtils.isNotEmpty(notExistTargetFundAccountIds)) {
                        tenantId2reduceMappingAccountIdNotExistTenantIds.put(targetTenantId, notExistTargetFundAccountIds);
                        needReplace = true;
                    }

                    //expenseAmountMappingFieldApiNames哪些不存在的
                    List<String> notExistFieldApiNames = commonDescribeManager.getNotExistFieldApiNames(targetTenantId, reduceRelatedObjectApiName, expenseAmountMappingFieldApiNames);
                    if (CollectionUtils.isNotEmpty(notExistFieldApiNames)) {
                        tenantId2educeMappingNotExistExpenseAmountApiNames.put(targetTenantId, notExistFieldApiNames);
                        needReplace = true;
                    }
                }
                log.info("fixSalesOrderComponentReduceAccountCheckRuleDataReduceMapping targetTenantId[{}], hasNotExistCannotReplace[{}], needReplace[{}]", targetTenantId, hasNotExistCannotReplace, needReplace);

                if (hasNotExistCannotReplace) {
                    continue;
                }

                if (!arg.isDoUpdate()) {
                    continue;
                }

                if (!arg.isReplaceDirectly()) {
                    if (!needReplace) {
                        continue;
                    }
                }

                //账户ID 和 扣减字段，换成 targetTenantId 上的
                Map<String, String> sourceFundAccountId2TargetFundAccountId = fundAccountManager.getSourceFundAccountId2TargetFundAccountId(sourceFundAccountIds, sourceFundAccounts, targetFundAccounts);
                Map<String, String> sourceFieldApiName2TargetFieldApiName = commonDescribeManager.getSourceFieldApiName2TargetFieldApiName(sourceTenantId, targetTenantId, reduceRelatedObjectApiName, sourceFieldApiNames);
                List<ObjectMappingModel> newTargetObjectMappings = replaceSourceObjectMappings(sourceObjectMappings, sourceFundAccountId2TargetFundAccountId, sourceFieldApiName2TargetFieldApiName);
                String newReduceMappingJson = JSONObject.toJSONString(newTargetObjectMappings);


                targetComponentReduceAccountCheckRuleData.set(AccountCheckRuleConstants.Field.ReduceMapping.apiName, newReduceMappingJson);

                User admin = User.systemUser(targetTenantId);
                serviceFacade.updateObjectData(admin, targetComponentReduceAccountCheckRuleData);

                successTenantIds.add(targetTenantId);
                log.info("fixSalesOrderComponentReduceAccountCheckRuleDataReduceMapping end   targetTenantId[{}]", targetTenantId);
            } catch (Exception e) {
                log.info("fixSalesOrderComponentReduceAccountCheckRuleDataReduceMapping error   targetTenantId[{}]", targetTenantId, e);
                failTenantIds.add(targetTenantId);
            }
        }

        CurlModel.FixSalesOrderComponentReduceAccountCheckRuleDataReduceMappingResult result = new CurlModel.FixSalesOrderComponentReduceAccountCheckRuleDataReduceMappingResult(notOpenTenantIds, accountCheckRuleNoExistTenantIds,
                tenantId2NotExistSourceFundAccountNames.keySet(), tenantId2NotExistSourceFundAccountNames,
                tenantId2NotExistSourceFieldLabels.keySet(), tenantId2NotExistSourceFieldLabels,
                reduceMappingIsEmptyTenantIds,
                reduceMappingAccountIdEmtpyTenantIds,
                tenantId2reduceMappingAccountIdNotExistTenantIds.keySet(), tenantId2reduceMappingAccountIdNotExistTenantIds,
                reduceMappingExpenseAmountFieldApiNameEmptyTenantIds,
                tenantId2educeMappingNotExistExpenseAmountApiNames.keySet(), tenantId2educeMappingNotExistExpenseAmountApiNames,
                successTenantIds, failTenantIds);
        log.info("fixSalesOrderComponentReduceAccountCheckRuleDataReduceMapping  result[{}]", result);
        return result;
    }

    @Override
    public CurlModel.CopyComponentReduceAccountCheckRuleIfNoExistResult copyComponentReduceAccountCheckRuleIfNoExist(ServiceContext serviceContext, CurlModel.CopyComponentReduceAccountCheckRuleIfNoExistArg arg) {
        List<String> notOpenTenantIds = Lists.newArrayList();

        //【组件扣减】校验规则已存在
        List<String> accountCheckRuleExistTenantIds = Lists.newArrayList();
        //【组件扣减】校验规则不存在
        List<String> accountCheckRuleNotExistTenantIds = Lists.newArrayList();
        //【账户】不存在
        Map<String, List<String>> tenantId2NotExistFundAccountNames = Maps.newHashMap();
        //扣减字段不存在
        Map<String, List<String>> tenantId2NotExistFieldLabels = Maps.newHashMap();

        List<String> successTenantIds = Lists.newArrayList();

        if (CollectionUtils.isEmpty(arg.getTargetTenantIds())) {
            return new CurlModel.CopyComponentReduceAccountCheckRuleIfNoExistResult(notOpenTenantIds, accountCheckRuleExistTenantIds, accountCheckRuleNotExistTenantIds,
                    tenantId2NotExistFundAccountNames.keySet(), tenantId2NotExistFundAccountNames,
                    tenantId2NotExistFieldLabels.keySet(), tenantId2NotExistFieldLabels,
                    successTenantIds);
        }

        String reduceRelatedObjectApiName = arg.getReduceRelatedObjectApiName();
        String sourceTenantId = arg.getSourceTenantId();
        IObjectData sourceComponentReduceAccountCheckRuleData = accountCheckRuleManager.getComponentReduceAccountCheckRuleData(sourceTenantId, reduceRelatedObjectApiName);
        List<IObjectData> sourceFundAccounts = fundAccountManager.getAccounts(sourceTenantId, false);

        for (String targetTenantId : arg.getTargetTenantIds()) {
            log.info("copyComponentReduceAccountCheckRuleIfNoExist begin targetTenantId[{}]", targetTenantId);

            boolean isAccountCheckRuleOpen = fundAccountConfigManager.isAccountCheckRuleOpen(targetTenantId);
            if (!isAccountCheckRuleOpen) {
                notOpenTenantIds.add(targetTenantId);
                continue;
            }

            //查'组件扣减'的校验规则
            IObjectData targetComponentReduceAccountCheckRuleData = accountCheckRuleManager.getComponentReduceAccountCheckRuleData(targetTenantId, reduceRelatedObjectApiName);
            if (targetComponentReduceAccountCheckRuleData != null) {
                accountCheckRuleExistTenantIds.add(targetTenantId);
                continue;
            }
            accountCheckRuleNotExistTenantIds.add(targetTenantId);

            List<ObjectMappingModel> sourceObjectMappings = RuleHandlerUtil.getObjectMapping(sourceComponentReduceAccountCheckRuleData, AccountCheckRuleConstants.Field.ReduceMapping.apiName);

            boolean hasNotExist = false;
            //'模板企业' 有的账户，targetTenantId中不存在
            List<IObjectData> targetFundAccounts = fundAccountManager.getAccounts(targetTenantId, false);
            Set<String> sourceFundAccountIds = sourceObjectMappings.stream().map(ObjectMappingModel::getFundAccountId).collect(Collectors.toSet());
            List<String> targetNotExistFundAccountNames = fundAccountManager.getTargetNotExistFundAccountNames(sourceFundAccountIds, sourceFundAccounts, targetFundAccounts);
            if (CollectionUtils.isNotEmpty(targetNotExistFundAccountNames)) {
                tenantId2NotExistFundAccountNames.put(targetTenantId, targetNotExistFundAccountNames);
                hasNotExist = true;
            }

            //'模板企业' 扣减字段，在targetTenantId中不存在
            Set<String> sourceFieldApiNames = getExpenseAmountFieldApiNames(sourceObjectMappings);
            List<String> sourceFieldLabels = commonDescribeManager.getFieldLabelList(sourceTenantId, reduceRelatedObjectApiName, Lists.newArrayList(sourceFieldApiNames));
            List<String> notExistFieldLabels = commonDescribeManager.getNotExistFieldLabelList(targetTenantId, reduceRelatedObjectApiName, sourceFieldLabels);
            if (!CollectionUtils.isEmpty(notExistFieldLabels)) {
                tenantId2NotExistFieldLabels.put(targetTenantId, notExistFieldLabels);
                hasNotExist = true;
            }

            if (hasNotExist) {
                continue;
            }

            if (!arg.isDoUpdate()) {
                continue;
            }

            //账户ID 和 扣减字段，换成 targetTenantId 上的
            Map<String, String> sourceFundAccountId2TargetFundAccountId = fundAccountManager.getSourceFundAccountId2TargetFundAccountId(sourceFundAccountIds, sourceFundAccounts, targetFundAccounts);
            Map<String, String> sourceFieldApiName2TargetFieldApiName = commonDescribeManager.getSourceFieldApiName2TargetFieldApiName(sourceTenantId, targetTenantId, reduceRelatedObjectApiName, sourceFieldApiNames);
            List<ObjectMappingModel> newTargetObjectMappings = replaceSourceObjectMappings(sourceObjectMappings, sourceFundAccountId2TargetFundAccountId, sourceFieldApiName2TargetFieldApiName);
            String newReduceMappingJson = JSONObject.toJSONString(newTargetObjectMappings);
            RequestContext requestContext = ServiceContextUtil.getRequestContext(User.systemUser(targetTenantId));
            accountCheckRuleManager.copy(targetTenantId, sourceComponentReduceAccountCheckRuleData, arg.isSameId(), newReduceMappingJson);
            accountCheckRuleManager.createPluginInstanceForComponentReduceAccountCheckRule(requestContext, reduceRelatedObjectApiName);

            successTenantIds.add(targetTenantId);
            log.info("copyComponentReduceAccountCheckRuleIfNoExist end   targetTenantId[{}]", targetTenantId);
        }

        CurlModel.CopyComponentReduceAccountCheckRuleIfNoExistResult result = new CurlModel.CopyComponentReduceAccountCheckRuleIfNoExistResult(notOpenTenantIds, accountCheckRuleExistTenantIds, accountCheckRuleNotExistTenantIds,
                tenantId2NotExistFundAccountNames.keySet(), tenantId2NotExistFundAccountNames,
                tenantId2NotExistFieldLabels.keySet(), tenantId2NotExistFieldLabels,
                successTenantIds);
        log.info("copyComponentReduceAccountCheckRuleIfNoExist finish   result[{}]", result);
        return result;
    }

    public Set<String> getExpenseAmountFieldApiNames(List<ObjectMappingModel> objectMappings) {
        Set<String> expenseAmountFieldApiNames = Sets.newHashSet();
        for (ObjectMappingModel sourceObjectMapping : objectMappings) {
            List<FieldMappingModel> fieldMappingList = sourceObjectMapping.getFieldMappingList();
            if (CollectionUtils.isEmpty(fieldMappingList)) {
                continue;
            }

            for (FieldMappingModel fieldMapping : fieldMappingList) {
                if (Objects.equals(fieldMapping.getTargetFieldApiName(), "expense_amount")) {
                    if (!Strings.isNullOrEmpty(fieldMapping.getSourceFieldApiName())) {
                        expenseAmountFieldApiNames.add(fieldMapping.getSourceFieldApiName());
                    }
                }
            }
        }

        return expenseAmountFieldApiNames;
    }

    private List<ObjectMappingModel> replaceSourceObjectMappings(List<ObjectMappingModel> sourceObjectMappings, Map<String, String> sourceFundAccountId2TargetFundAccountId, Map<String, String> sourceFieldApiName2TargetFieldApiName) {
        List<ObjectMappingModel> newTargetObjectMappings = Lists.newArrayList();
        for (ObjectMappingModel sourceObjectMapping : sourceObjectMappings) {
            //替换FundAccountId
            String sourceFundAccountId = sourceObjectMapping.getFundAccountId();
            String targetFundAccountId = sourceFundAccountId2TargetFundAccountId.get(sourceFundAccountId);
            sourceObjectMapping.setFundAccountId(targetFundAccountId);

            //替换fieldApiName
            for (FieldMappingModel fieldMapping : sourceObjectMapping.getFieldMappingList()) {
                if (Objects.equals(fieldMapping.getTargetFieldApiName(), "expense_amount")) {
                    String sourceSourceFieldName = fieldMapping.getSourceFieldApiName();
                    String targetSourceFieldName = sourceFieldApiName2TargetFieldApiName.get(sourceSourceFieldName);
                    fieldMapping.setSourceFieldApiName(targetSourceFieldName);
                }
            }

            newTargetObjectMappings.add(sourceObjectMapping);
        }
        return newTargetObjectMappings;
    }

    private boolean isNeedUpdate(String tenantId, IObjectData componentReduceAccountCheckRuleData, List<ObjectMappingModel> newTargetObjectMappings) {
        boolean isNeedUpdate = false;

        List<ObjectMappingModel> objectMappings = RuleHandlerUtil.getObjectMapping(componentReduceAccountCheckRuleData, AccountCheckRuleConstants.Field.ReduceMapping.apiName);

        //存在的账号
        Set<String> authorizeAccountIds = objectMappings.stream().map(ObjectMappingModel::getFundAccountId).collect(Collectors.toSet());
        List<IObjectData> existFundAccounts = Lists.newArrayList();
        List<String> existFundAccountIds = Lists.newArrayList();
        Map<String, String> fundAccountId2Name = new HashMap<>();
        if (!CollectionUtils.isEmpty(authorizeAccountIds)) {
            existFundAccounts = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, Lists.newArrayList(authorizeAccountIds), FundAccountConstants.API_NAME);
            existFundAccountIds = existFundAccounts.stream().map(IObjectData::getId).collect(Collectors.toList());
            fundAccountId2Name = existFundAccounts.stream().collect(Collectors.toMap(IObjectData::getId, IObjectData::getName));
        }

        //订单上的字段信息
        String apiName = "SalesOrderObj";
        Map<String, String> fieldLabel2ApiNameMap = commonDescribeManager.getLabel2ApiNameMap(tenantId, apiName, Lists.newArrayList("返利账户-扣减金额", "预收账户-扣减金额"));

        //已经有的映射规则替换
        List<String> existFundAccountName = Lists.newArrayList();
        List<ObjectMappingModel> newObjectMappings = Lists.newArrayList();
        for (ObjectMappingModel objectMapping : objectMappings) {
            String fundAccountId = objectMapping.getFundAccountId();
            if (existFundAccountIds.contains(fundAccountId)) {
                //不是'预收账户'或'返利账户'，不管
                String fundAccountName = fundAccountId2Name.get(fundAccountId);
                if (!Objects.equals("预收账户", fundAccountName) && !Objects.equals("返利账户", fundAccountName)) {
                    newObjectMappings.add(objectMapping);
                    continue;
                }

                //'预收账户'或'返利账户' ： 扣减金额字段是否需要替换
                String fieldLabel = Objects.equals(fundAccountName, "预收账户") ? "预收账户-扣减金额" : "返利账户-扣减金额";
                List<FieldMappingModel> fieldMappings = objectMapping.getFieldMappingList();
                for (FieldMappingModel fieldMapping : fieldMappings) {
                    if (Objects.equals(fieldMapping.getTargetFieldApiName(), AccountTransactionFlowConst.Field.ExpenseAmount.apiName)) {
                        String fieldApiName = fieldLabel2ApiNameMap.get(fieldLabel);
                        String sourceFieldApiName = fieldMapping.getSourceFieldApiName();
                        if (!Objects.equals(fieldApiName, sourceFieldApiName)) {
                            //需要替换
                            fieldMapping.setSourceFieldApiName(fieldApiName);
                            isNeedUpdate = true;
                        }
                    }
                }
                existFundAccountName.add(fundAccountName);
                newObjectMappings.add(objectMapping);
            }
        }

        if (isNeedUpdate) {
            String newReduceMappingJson = JSONObject.toJSONString(newObjectMappings);
            componentReduceAccountCheckRuleData.set(AccountCheckRuleConstants.Field.ReduceMapping.apiName, newReduceMappingJson);
        }

        return isNeedUpdate;
    }

    /**
     * {
     * "fundAccountId": "fundAccountId",
     * "fieldMappingList": [
     * {
     * "optionMapping": [
     * ],
     * "sourceFieldApiName": "account_id",
     * "targetFieldApiName": "customer_id"
     * },
     * {
     * "optionMapping": [
     * ],
     * "sourceFieldApiName": "expenseAmountSourceFieldApiName",
     * "targetFieldApiName": "expense_amount"
     * },
     * {
     * "optionMapping": [
     * ],
     * "sourceFieldApiName": "create_time",
     * "targetFieldApiName": "transaction_date"
     * }
     * ],
     * "sourceObjectApiName": "SalesOrderObj",
     * "targetObjectApiName": "AccountTransactionFlowObj"
     * }
     * <p>
     * fundAccountId、expenseAmountSourceFieldApiName 替换成参数的
     */
    private ObjectMappingModel getObjectMapping(String fundAccountId, String expenseAmountSourceFieldApiName) {
        String json = "{\"fundAccountId\":\"fundAccountId\",\"fieldMappingList\":[{\"optionMapping\":[],\"sourceFieldApiName\":\"account_id\",\"targetFieldApiName\":\"customer_id\"},{\"optionMapping\":[],\"sourceFieldApiName\":\"expenseAmountSourceFieldApiName\",\"targetFieldApiName\":\"expense_amount\"},{\"optionMapping\":[],\"sourceFieldApiName\":\"create_time\",\"targetFieldApiName\":\"transaction_date\"}],\"sourceObjectApiName\":\"SalesOrderObj\",\"targetObjectApiName\":\"AccountTransactionFlowObj\"}";

        ObjectMappingModel objectMapping = JsonUtil.fromJson(json, ObjectMappingModel.class);
        objectMapping.setFundAccountId(fundAccountId);
        for (FieldMappingModel fieldMapping : objectMapping.getFieldMappingList()) {
            if (Objects.equals(fieldMapping.getTargetFieldApiName(), AccountTransactionFlowConst.Field.ExpenseAmount.apiName)) {
                fieldMapping.setSourceFieldApiName(expenseAmountSourceFieldApiName);
            }
        }
        objectMapping.setSourceObjectApiName(AccountTransactionFlowConst.API_NAME);
        return objectMapping;
    }

    @Override
    public CurlModel.FixAccountAuthToInitResult fixAccountAuthToInit(ServiceContext serviceContext, CurlModel.FixAccountAuthToInitArg arg) {
        List<String> notOpenTenantIds = Lists.newArrayList();
        List<String> successTenantIds = Lists.newArrayList();

        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return new CurlModel.FixAccountAuthToInitResult(notOpenTenantIds, successTenantIds);
        }

        for (String tenantId : arg.getTenantIds()) {
            log.info("addCustomerAccountPluginInstanceForInitOutComeAuth begin tenantId[{}]", tenantId);

            boolean isAccountAuthOpen = fundAccountConfigManager.isAccountAuthOpen(tenantId);
            if (!isAccountAuthOpen) {
                notOpenTenantIds.add(tenantId);
                continue;
            }

            fAccountAuthorizationManager.fixAccountAuthToInit(tenantId, arg.getIncomeObjectApiNames(), arg.getOutcomeObjectApiNames());
            successTenantIds.add(tenantId);
            log.info("addCustomerAccountPluginInstanceForInitOutComeAuth end   tenantId[{}]", tenantId);
        }

        return new CurlModel.FixAccountAuthToInitResult(notOpenTenantIds, successTenantIds);
    }

    @Override
    public CurlModel.FindNoExistOrDifferentFundAccountResult findNoExistOrDifferentFundAccount(ServiceContext serviceContext, CurlModel.FindNoExistOrDifferentFundAccountArg arg) {
        List<String> notOpenTenantIds = Lists.newArrayList();
        List<String> successTenantIds = Lists.newArrayList();

        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return new CurlModel.FindNoExistOrDifferentFundAccountResult(Lists.newArrayList(), Lists.newArrayList(), new HashMap<>(), new HashSet<>());
        }

        //查'模板企业'的所有账户
        String objectApiName = FundAccountConstants.API_NAME;
        List<IObjectData> sourceFundAccountDatas = commonObjDataManager.queryAllObjectDataList(arg.getSourceTenantId(), objectApiName);

        Map<String, CurlModel.FundAccountInfos> tenantId2FundAccountInfos = new HashMap<>();
        for (String tenantId : arg.getTenantIds()) {
            log.info("findNoExistOrDifferentFundAccount begin tenantId[{}]", tenantId);

            boolean isAccountAuthOpen = fundAccountConfigManager.isAccountAuthOpen(tenantId);
            if (!isAccountAuthOpen) {
                notOpenTenantIds.add(tenantId);
                continue;
            }

            //查被复制的企业
            List<IObjectData> fundAccountDatas = commonObjDataManager.queryAllObjectDataList(tenantId, objectApiName);

            /**
             * 找出
             * 1、模板企业有，这没有
             * 2、name 和模板企业一样，Id不一样的
             */
            List<CurlModel.FundAccountInfo> noExistFundAccounts = new ArrayList<>();
            List<CurlModel.FundAccountInfo> differentIdFundAccounts = new ArrayList<>();
            for (IObjectData sourceFundAccountData : sourceFundAccountDatas) {
                String fundAccountName = sourceFundAccountData.get(FundAccountConstants.Field.Name.apiName, String.class);
                String fundAccountId = sourceFundAccountData.get(SystemConstants.Field.Id.apiName, String.class);

                //Id不同, name 一样
                boolean hasDifferentIdAndSameName = hasDifferentIdAndSameName(fundAccountDatas, sourceFundAccountData);
                if (hasDifferentIdAndSameName) {
                    differentIdFundAccounts.add(new CurlModel.FundAccountInfo(fundAccountId, fundAccountName));
                } else {
                    //Id, name 都不一样
                    boolean idAndNameAllDifferent = idAndNameAllDifferent(fundAccountDatas, sourceFundAccountData);
                    if (idAndNameAllDifferent) {
                        noExistFundAccounts.add(new CurlModel.FundAccountInfo(fundAccountId, fundAccountName));
                    }
                }
            }
            CurlModel.FundAccountInfos accountInfos = new CurlModel.FundAccountInfos(noExistFundAccounts, differentIdFundAccounts);
            tenantId2FundAccountInfos.put(tenantId, accountInfos);

            successTenantIds.add(tenantId);
            log.info("findNoExistOrDifferentFundAccount end   tenantId[{}]", tenantId);
        }

        Set<String> tenantId2AccountInfosKeySet = tenantId2FundAccountInfos.keySet();
        if (!arg.isReturnTenantId2AccountInfos()) {
            tenantId2FundAccountInfos = new HashMap<>();
        }
        CurlModel.FindNoExistOrDifferentFundAccountResult result = new CurlModel.FindNoExistOrDifferentFundAccountResult(notOpenTenantIds, successTenantIds, tenantId2FundAccountInfos, tenantId2AccountInfosKeySet);
        log.info("findNoExistOrDifferentFundAccount end arg[{}], result[{}]", arg, result);
        return result;
    }

    @Override
    public CurlModel.FindNoExistFundAccountResult findNoExistFundAccount(ServiceContext serviceContext, CurlModel.FindNoExistFundAccountArg arg) {
        List<String> notOpenTenantIds = Lists.newArrayList();
        List<String> failTenantIds = Lists.newArrayList();

        //所有账户都有的企业
        List<String> allExistFundAccountTenantIds = Lists.newArrayList();

        //所有账户都没有的企业
        List<String> allNotExistFundAccountTenantIds = Lists.newArrayList();
        //部分账户都没有的企业
        Map<String, List<String>> tenantId2PartNotExistFundAccountNames = new HashMap<>();

        if (CollectionUtils.isEmpty(arg.getTargetTenantIds())) {
            return new CurlModel.FindNoExistFundAccountResult(notOpenTenantIds, failTenantIds, allExistFundAccountTenantIds, allNotExistFundAccountTenantIds, tenantId2PartNotExistFundAccountNames.keySet(), tenantId2PartNotExistFundAccountNames);
        }

        //查'模板企业'的所有账户
        String objectApiName = FundAccountConstants.API_NAME;

        for (String targetTenantId : arg.getTargetTenantIds()) {
            try {
                log.info("findNoExistFundAccount begin tenantId[{}]", targetTenantId);

                boolean isNewCustomerAccountEnable = fundAccountConfigManager.isNewCustomerAccountEnable(targetTenantId);
                if (!isNewCustomerAccountEnable) {
                    notOpenTenantIds.add(targetTenantId);
                    continue;
                }

                //查账户
                List<IObjectData> targetFundAccountDatas = commonObjDataManager.queryAllObjectDataList(targetTenantId, objectApiName);
                List<String> targetExistFundAccountNames = targetFundAccountDatas.stream().map(IObjectData::getName).collect(Collectors.toList());

                //不存在的账户名称
                List<String> notExistFundAccountNames = arg.getFundAccountNames().stream().filter(n -> !targetExistFundAccountNames.contains(n)).collect(Collectors.toList());

                if (CollectionUtils.isEmpty(notExistFundAccountNames)) {
                    allExistFundAccountTenantIds.add(targetTenantId);
                } else if (Objects.equals(notExistFundAccountNames.size(), arg.getFundAccountNames().size())) {
                    allNotExistFundAccountTenantIds.add(targetTenantId);
                } else {
                    tenantId2PartNotExistFundAccountNames.put(targetTenantId, notExistFundAccountNames);
                }

                log.info("findNoExistFundAccount end   targetTenantId[{}]", targetTenantId);
            } catch (Exception e) {
                log.info("findNoExistFundAccount error   targetTenantId[{}]", targetTenantId, e);
                failTenantIds.add(targetTenantId);
            }
        }
        log.info("findNoExistFundAccount finish  allExistFundAccountTenantIds[{}]", allExistFundAccountTenantIds);
        if (arg.isSetAllExistFundAccountTenantIdsEmpty()) {
            allExistFundAccountTenantIds = Lists.newArrayList();
        }

        CurlModel.FindNoExistFundAccountResult result = new CurlModel.FindNoExistFundAccountResult(notOpenTenantIds, failTenantIds, allExistFundAccountTenantIds, allNotExistFundAccountTenantIds, tenantId2PartNotExistFundAccountNames.keySet(), tenantId2PartNotExistFundAccountNames);
        log.info("findNoExistFundAccount end arg[{}], result[{}]", arg, result);
        return result;
    }

    @Override
    public CurlModel.UpdateAccountFieldDataResult updateAccountFieldData(ServiceContext serviceContext, CurlModel.UpdateAccountFieldDataArg arg) {
        List<String> noFundAccountDataTenantIds = Lists.newArrayList();
        List<String> successTenantIds = Lists.newArrayList();
        List<String> noNeedUpdateTenantIds = Lists.newArrayList();

        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return new CurlModel.UpdateAccountFieldDataResult(Lists.newArrayList(), Lists.newArrayList(), Lists.newArrayList());
        }

        for (String tenantId : arg.getTenantIds()) {
            log.info("updateAccountFieldData begin tenantId[{}]", tenantId);

            //要处理的账户name
            List<String> accountNames = arg.getUpdateAccountFieldDataList().stream().map(CurlModel.UpdateAccountFieldData::getAccountName).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(accountNames)) {
                log.info("updateAccountFieldData accountNames isEmpty tenantId[{}]，updateAccountFieldDataList[{}]", tenantId, arg.getUpdateAccountFieldDataList());
                continue;
            }

            //查账户
            List<IObjectData> fundAccountDataList = commonObjDataManager.queryAllObjectDataList(tenantId, FundAccountConstants.API_NAME, accountNames);
            if (CollectionUtils.isEmpty(fundAccountDataList)) {
                log.info("updateAccountFieldData fundAccountDataList isEmpty tenantId[{}]，accountNames[{}]", tenantId, accountNames);
                noFundAccountDataTenantIds.add(tenantId);
                continue;
            }

            //需要update的
            List<IObjectData> needUpdateDataList = getNeedUpdateDataList(fundAccountDataList, arg.getUpdateAccountFieldDataList());
            if (CollectionUtils.isEmpty(needUpdateDataList)) {
                noNeedUpdateTenantIds.add(tenantId);
                continue;
            }
            User admin = User.systemUser(tenantId);
            serviceFacade.batchUpdateByFields(admin, needUpdateDataList, arg.getUpdateFieldApiNames());
            successTenantIds.add(tenantId);
            log.info("updateAccountFieldData end tenantId[{}]", tenantId);
        }

        return new CurlModel.UpdateAccountFieldDataResult(noFundAccountDataTenantIds, successTenantIds, noNeedUpdateTenantIds);
    }

    private List<IObjectData> getNeedUpdateDataList(List<IObjectData> fundAccountDataList, List<CurlModel.UpdateAccountFieldData> updateAccountFieldDataList) {
        List<IObjectData> needUpdateDataList = Lists.newArrayList();

        Map<String, List<CurlModel.FieldData>> accountName2FieldDataListMap = updateAccountFieldDataList.stream().collect(Collectors.toMap(CurlModel.UpdateAccountFieldData::getAccountName, CurlModel.UpdateAccountFieldData::getFieldDataList));

        for (IObjectData fundAccount : fundAccountDataList) {
            String fundAccountName = fundAccount.getName();
            if (!accountName2FieldDataListMap.containsKey(fundAccountName)) {
                continue;
            }

            List<CurlModel.FieldData> fieldDataList = accountName2FieldDataListMap.get(fundAccountName);
            boolean hasDifferent = false;
            for (CurlModel.FieldData fieldData : fieldDataList) {
                if (!Objects.equals(fieldData.getFieldData(), fundAccount.get(fieldData.getFieldApiName()))) {
                    fundAccount.set(fieldData.getFieldApiName(), fieldData.getFieldData());
                    hasDifferent = true;
                }
            }

            if (hasDifferent) {
                needUpdateDataList.add(fundAccount);
            }
        }

        return needUpdateDataList;
    }

    @Override
    public CurlModel.CopyFundAccountIfNoExistResult copyFundAccountIfNoExist(ServiceContext serviceContext, CurlModel.CopyFundAccountIfNoExistArg arg) {
        List<String> notOpenTenantIds = Lists.newArrayList();
        List<String> successTenantIds = Lists.newArrayList();

        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return new CurlModel.CopyFundAccountIfNoExistResult(Lists.newArrayList(), Lists.newArrayList(), new HashSet<>(), new HashSet<>());
        }

        //查'模板企业'的所有账户
        String objectApiName = FundAccountConstants.API_NAME;
        List<IObjectData> sourceFundAccountDatas = serviceFacade.findObjectDataByIdsIgnoreAll(arg.getSourceTenantId(), arg.getSourceFundAccountIds(), objectApiName);
        if (CollectionUtils.isEmpty(sourceFundAccountDatas)) {
            log.info("copyFundAccountIfNoExist sourceFundAccountDatas is empty tenantId[{}], sourceFundAccountIds[{}], objectApiName[{}]", arg.getSourceTenantId(), arg.getSourceFundAccountIds(), objectApiName);
            return new CurlModel.CopyFundAccountIfNoExistResult(Lists.newArrayList(), Lists.newArrayList(), new HashSet<>(), new HashSet<>());
        }

        Set<String> hasDifferentIdAndSameNameTenantIds = new HashSet<>();
        Set<String> hasCopyTenantIds = new HashSet<>();
        for (String targetTenantId : arg.getTenantIds()) {
            log.info("copyFundAccount begin targetTenantId[{}]", targetTenantId);

            boolean isAccountAuthOpen = fundAccountConfigManager.isAccountAuthOpen(targetTenantId);
            if (!isAccountAuthOpen) {
                notOpenTenantIds.add(targetTenantId);
                continue;
            }

            //查本企业的
            List<IObjectData> fundAccountDatas = commonObjDataManager.queryAllObjectDataList(targetTenantId, objectApiName);

            for (IObjectData sourceFundAccountData : sourceFundAccountDatas) {
                /**
                 * 1、有id一样，不处理
                 */
                boolean hasSameId = hasSameId(fundAccountDatas, sourceFundAccountData);
                if (hasSameId) {
                    continue;
                }

                /**
                 * 2、有id不一样，name一样的，记录下来，需要作废
                 */
                boolean hasDifferentIdAndSameName = hasDifferentIdAndSameName(fundAccountDatas, sourceFundAccountData);
                if (hasDifferentIdAndSameName) {
                    hasDifferentIdAndSameNameTenantIds.add(targetTenantId);
                    continue;
                }

                /**
                 * 3、id、name都不一样，新建
                 */
                fundAccountManager.copy(targetTenantId, sourceFundAccountData, arg.isSameId());
                hasCopyTenantIds.add(targetTenantId);
            }

            successTenantIds.add(targetTenantId);
            log.info("copyFundAccount end   tenantId[{}]", targetTenantId);
        }

        return new CurlModel.CopyFundAccountIfNoExistResult(notOpenTenantIds, successTenantIds, hasDifferentIdAndSameNameTenantIds, hasCopyTenantIds);
    }

    @Override
    public CurlModel.QueryNotExistAccountAuthResult queryNotExistAccountAuth(ServiceContext serviceContext, CurlModel.QueryNotExistAccountAuthArg arg) {
        List<String> notOpenTenantIds = Lists.newArrayList();
        List<String> failTenantIds = Lists.newArrayList();

        /**
         * 【入账授权】都不存在的
         */
        List<String> incomeAuthAllNoExistTenantIds = Lists.newArrayList();
        /**
         * 【入账授权】部分缺失的
         */
        Map<String, List<String>> tenantId2NotExistIncomeAuthorizedObjectApiNames = new HashMap<>();

        /**
         * 【支出授权】都不存在的
         */
        List<String> outcomeAuthAllNoExistTenantIds = Lists.newArrayList();
        /**
         * 【支出授权】部分缺失的
         */
        Map<String, List<String>> tenantId2NotExistOutcomeAuthorizedObjectApiNames = new HashMap<>();

        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return new CurlModel.QueryNotExistAccountAuthResult(notOpenTenantIds, failTenantIds,
                    incomeAuthAllNoExistTenantIds, tenantId2NotExistIncomeAuthorizedObjectApiNames.keySet(), tenantId2NotExistIncomeAuthorizedObjectApiNames,
                    outcomeAuthAllNoExistTenantIds, tenantId2NotExistOutcomeAuthorizedObjectApiNames.keySet(), tenantId2NotExistOutcomeAuthorizedObjectApiNames);
        }

        for (String tenantId : arg.getTenantIds()) {
            try {
                log.info("queryNotExistAccountAuth begin tenantId[{}]", tenantId);

                boolean isAccountAuthOpen = fundAccountConfigManager.isAccountAuthOpen(tenantId);
                if (!isAccountAuthOpen) {
                    notOpenTenantIds.add(tenantId);
                    continue;
                }

                User systemUser = User.systemUser(tenantId);
                //查【入账授权】
                if (!CollectionUtils.isEmpty(arg.getIncomeAuthorizedObjectApiNames())) {
                    List<IObjectData> incomeAuthList = fAccountAuthorizationManager.getFAccountAuthorizationDatas(systemUser, arg.getIncomeAuthorizedObjectApiNames(), FAccountAuthAuthorizedTypeEnum.Income.getValue());

                    if (CollectionUtils.isEmpty(incomeAuthList)) {
                        //都不存在的
                        incomeAuthAllNoExistTenantIds.add(tenantId);
                    } else {
                        List<String> existIncomeAuthorizedObjectApiNames = incomeAuthList.stream().map(d -> d.get(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, String.class)).collect(Collectors.toList());
                        List<String> notExistIncomeAuthorizedObjectApiNames = arg.getIncomeAuthorizedObjectApiNames().stream().filter(n -> !existIncomeAuthorizedObjectApiNames.contains(n)).collect(Collectors.toList());
                        //部分不存在
                        if (CollectionUtils.isNotEmpty(notExistIncomeAuthorizedObjectApiNames)) {
                            tenantId2NotExistIncomeAuthorizedObjectApiNames.put(tenantId, notExistIncomeAuthorizedObjectApiNames);
                        }
                    }
                }

                //查【支出授权】
                if (!CollectionUtils.isEmpty(arg.getOutcomeAuthorizedObjectApiNames())) {
                    List<IObjectData> outcomeAuthList = fAccountAuthorizationManager.getFAccountAuthorizationDatas(systemUser, arg.getOutcomeAuthorizedObjectApiNames(), FAccountAuthAuthorizedTypeEnum.Outcome.getValue());

                    if (CollectionUtils.isEmpty(outcomeAuthList)) {
                        //都不存在
                        outcomeAuthAllNoExistTenantIds.add(tenantId);
                    } else {
                        List<String> existOutcomeAuthorizedObjectApiNames = outcomeAuthList.stream().map(d -> d.get(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, String.class)).collect(Collectors.toList());
                        List<String> notExistOutcomeAuthorizedObjectApiNames = arg.getOutcomeAuthorizedObjectApiNames().stream().filter(n -> !existOutcomeAuthorizedObjectApiNames.contains(n)).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(notExistOutcomeAuthorizedObjectApiNames)) {
                            //部分不存在
                            tenantId2NotExistOutcomeAuthorizedObjectApiNames.put(tenantId, notExistOutcomeAuthorizedObjectApiNames);
                        }
                    }
                }

                log.info("queryNotExistAccountAuth end   tenantId[{}]", tenantId);
            } catch (Exception e) {
                log.info("queryNotExistAccountAuth error   tenantId[{}]", tenantId, e);
                failTenantIds.add(tenantId);
            }
        }

        CurlModel.QueryNotExistAccountAuthResult result = new CurlModel.QueryNotExistAccountAuthResult(notOpenTenantIds, failTenantIds,
                incomeAuthAllNoExistTenantIds, tenantId2NotExistIncomeAuthorizedObjectApiNames.keySet(), tenantId2NotExistIncomeAuthorizedObjectApiNames,
                outcomeAuthAllNoExistTenantIds, tenantId2NotExistOutcomeAuthorizedObjectApiNames.keySet(), tenantId2NotExistOutcomeAuthorizedObjectApiNames);
        log.info("queryNotExistAccountAuth end arg[{}], result[{}]", arg, result);
        return result;
    }

    @Override
    public CurlModel.QueryNotInitAccountAuthResult queryNotInitAccountAuth(ServiceContext serviceContext, CurlModel.QueryNotInitAccountAuthArg arg) {
        List<String> notOpenTenantIds = Lists.newArrayList();
        List<String> failTenantIds = Lists.newArrayList();

        /**
         * 【入账授权】都没初始化
         */
        List<String> incomeAuthAllNotInitTenantIds = Lists.newArrayList();
        /**
         * 【入账授权】部分没初始化
         */
        Map<String, List<String>> tenantId2NotInitIncomeAuthorizedObjectApiNames = new HashMap<>();
        /**
         * 【入账授权】部分缺失的
         */
        Map<String, List<String>> tenantId2NotExistIncomeAuthorizedObjectApiNames = new HashMap<>();


        /**
         * 【支出授权】都没初始化
         */
        List<String> outcomeAuthAllNotInitTenantIds = Lists.newArrayList();
        /**
         * 【支出授权】部分没初始化
         */
        Map<String, List<String>> tenantId2NotInitOutcomeAuthorizedObjectApiNames = new HashMap<>();
        /**
         * 【支出授权】部分缺失的
         */
        Map<String, List<String>> tenantId2NotExistOutcomeAuthorizedObjectApiNames = new HashMap<>();

        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return new CurlModel.QueryNotInitAccountAuthResult(notOpenTenantIds, failTenantIds,
                    incomeAuthAllNotInitTenantIds, tenantId2NotInitIncomeAuthorizedObjectApiNames.keySet(), tenantId2NotInitIncomeAuthorizedObjectApiNames, tenantId2NotExistIncomeAuthorizedObjectApiNames.keySet(), tenantId2NotExistIncomeAuthorizedObjectApiNames,
                    outcomeAuthAllNotInitTenantIds, tenantId2NotInitOutcomeAuthorizedObjectApiNames.keySet(), tenantId2NotInitOutcomeAuthorizedObjectApiNames, tenantId2NotExistOutcomeAuthorizedObjectApiNames.keySet(), tenantId2NotExistOutcomeAuthorizedObjectApiNames);
        }

        for (String tenantId : arg.getTenantIds()) {
            try {
                log.info("queryNotInitAccountAuth begin tenantId[{}]", tenantId);

                boolean isAccountAuthOpen = fundAccountConfigManager.isAccountAuthOpen(tenantId);
                if (!isAccountAuthOpen) {
                    notOpenTenantIds.add(tenantId);
                    continue;
                }

                User systemUser = User.systemUser(tenantId);
                //查【入账授权】
                if (!CollectionUtils.isEmpty(arg.getIncomeAuthorizedObjectApiNames())) {
                    List<IObjectData> incomeAuthList = fAccountAuthorizationManager.getFAccountAuthorizationDatas(systemUser, arg.getIncomeAuthorizedObjectApiNames(), FAccountAuthAuthorizedTypeEnum.Income.getValue());
                    List<String> notInitIncomeAuthorizedObjectApiNames = incomeAuthList.stream().filter(d -> !Objects.equals(d.get(FAccountAuthorizationConstants.Field.Status.apiName, String.class), FAccountAuthorizationStatusEnum.HAS_INIT.getValue()))
                            .map(d -> d.get(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, String.class)).collect(Collectors.toList());

                    //都未初始化
                    if (Objects.equals(notInitIncomeAuthorizedObjectApiNames.size(), arg.getIncomeAuthorizedObjectApiNames().size())) {
                        incomeAuthAllNotInitTenantIds.add(tenantId);
                    }
                    //部分未初始化
                    else if (CollectionUtils.isNotEmpty(notInitIncomeAuthorizedObjectApiNames)) {
                        tenantId2NotInitIncomeAuthorizedObjectApiNames.put(tenantId, notInitIncomeAuthorizedObjectApiNames);
                    }

                    //不存在的
                    List<String> existIncomeAuthorizedObjectApiNames = incomeAuthList.stream().map(d -> d.get(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, String.class)).collect(Collectors.toList());
                    List<String> notExistIncomeAuthorizedObjectApiNames = arg.getIncomeAuthorizedObjectApiNames().stream().filter(n -> !existIncomeAuthorizedObjectApiNames.contains(n)).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(notExistIncomeAuthorizedObjectApiNames)) {
                        tenantId2NotExistIncomeAuthorizedObjectApiNames.put(tenantId, notExistIncomeAuthorizedObjectApiNames);
                    }
                }

                //查【支出授权】
                if (!CollectionUtils.isEmpty(arg.getOutcomeAuthorizedObjectApiNames())) {
                    List<IObjectData> outcomeAuthList = fAccountAuthorizationManager.getFAccountAuthorizationDatas(systemUser, arg.getOutcomeAuthorizedObjectApiNames(), FAccountAuthAuthorizedTypeEnum.Outcome.getValue());
                    List<String> notInitOutcomeAuthorizedObjectApiNames = outcomeAuthList.stream().filter(d -> !Objects.equals(d.get(FAccountAuthorizationConstants.Field.Status.apiName, String.class), FAccountAuthorizationStatusEnum.HAS_INIT.getValue()))
                            .map(d -> d.get(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, String.class)).collect(Collectors.toList());

                    //都未初始化
                    if (Objects.equals(notInitOutcomeAuthorizedObjectApiNames.size(), arg.getOutcomeAuthorizedObjectApiNames().size())) {
                        outcomeAuthAllNotInitTenantIds.add(tenantId);
                    }
                    //部分未初始化
                    else if (CollectionUtils.isNotEmpty(notInitOutcomeAuthorizedObjectApiNames)) {
                        tenantId2NotInitOutcomeAuthorizedObjectApiNames.put(tenantId, notInitOutcomeAuthorizedObjectApiNames);
                    }

                    //不存在的
                    List<String> existOutcomeAuthorizedObjectApiNames = outcomeAuthList.stream().map(d -> d.get(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, String.class)).collect(Collectors.toList());
                    List<String> notExistOutcomeAuthorizedObjectApiNames = arg.getOutcomeAuthorizedObjectApiNames().stream().filter(n -> !existOutcomeAuthorizedObjectApiNames.contains(n)).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(notExistOutcomeAuthorizedObjectApiNames)) {
                        tenantId2NotExistOutcomeAuthorizedObjectApiNames.put(tenantId, notExistOutcomeAuthorizedObjectApiNames);
                    }
                }

                log.info("queryNotInitAccountAuth end   tenantId[{}]", tenantId);
            } catch (Exception e) {
                log.info("queryNotInitAccountAuth error   tenantId[{}]", tenantId, e);
                failTenantIds.add(tenantId);
            }
        }

        CurlModel.QueryNotInitAccountAuthResult result = new CurlModel.QueryNotInitAccountAuthResult(notOpenTenantIds, failTenantIds,
                incomeAuthAllNotInitTenantIds, tenantId2NotInitIncomeAuthorizedObjectApiNames.keySet(), tenantId2NotInitIncomeAuthorizedObjectApiNames, tenantId2NotExistIncomeAuthorizedObjectApiNames.keySet(), tenantId2NotExistIncomeAuthorizedObjectApiNames,
                outcomeAuthAllNotInitTenantIds, tenantId2NotInitOutcomeAuthorizedObjectApiNames.keySet(), tenantId2NotInitOutcomeAuthorizedObjectApiNames, tenantId2NotExistOutcomeAuthorizedObjectApiNames.keySet(), tenantId2NotExistOutcomeAuthorizedObjectApiNames);

        log.info("queryNotInitAccountAuth end arg[{}], result[{}]", arg, result);
        return result;
    }

    @Override
    public CurlModel.CopyAuthorizationDetailsResult copyAuthorizationDetails(ServiceContext serviceContext, CurlModel.CopyAuthorizationDetailsArg arg) {
        List<String> notOpenTenantIds = Lists.newArrayList();
        List<String> successTenantIds = Lists.newArrayList();

        if (CollectionUtils.isEmpty(arg.getTargetTenantIds())) {
            return new CurlModel.CopyAuthorizationDetailsResult(Lists.newArrayList(), Lists.newArrayList(), new HashSet<>());
        }

        String sourceTenantId = arg.getSourceTenantId();
        //查'模板企业'的账户授权
        User sourceTenantAdmin = User.systemUser(sourceTenantId);
        List<String> authorizedObjectApiNames = arg.getAuthInfos().stream().map(CurlModel.AuthInfo::getAuthorizedObjectApiName).collect(Collectors.toList());
        List<IObjectData> sourceAccountAuthorizationDatas = fAccountAuthorizationManager.getFAccountAuthorizationDatas(sourceTenantAdmin, authorizedObjectApiNames, null);
        if (CollectionUtils.isEmpty(sourceAccountAuthorizationDatas)) {
            log.info("copyAuthorizationDetails sourceAccountAuthorizationDatas is empty label[{}]", arg.getLabel());
            return new CurlModel.CopyAuthorizationDetailsResult(Lists.newArrayList(), Lists.newArrayList(), new HashSet<>());
        }
        List<String> sourceAccountAuthDataIds = sourceAccountAuthorizationDatas.stream().map(DBRecord::getId).collect(Collectors.toList());

        //查'模板企业'的授权明细
        List<IObjectData> allSourceAuthDetailDatas = authorizationDetailManager.query(sourceTenantAdmin, sourceAccountAuthDataIds);
        Map<String, List<IObjectData>> sourceAccountAuthDataId2AuthDetails = allSourceAuthDetailDatas.stream()
                .collect(Collectors.groupingBy(d -> d.get(AuthorizationDetailConstant.Field.FAccountAuthorizationId.apiName, String.class)));

        Set<String> targetAccountAuthorizationDatasIsEmptyTenantIds = new HashSet<>();
        for (String targetTenantId : arg.getTargetTenantIds()) {
            log.info("copyAuthorizationDetails begin targetTenantId[{}], label[{}]", targetTenantId, arg.getLabel());

            boolean isAccountAuthOpen = fundAccountConfigManager.isAccountAuthOpen(targetTenantId);
            if (!isAccountAuthOpen) {
                notOpenTenantIds.add(targetTenantId);
                continue;
            }

            //查targetTenantId的账户授权
            User targetTenantAdmin = User.systemUser(targetTenantId);
            List<IObjectData> targetAccountAuthorizationDatas = fAccountAuthorizationManager.getFAccountAuthorizationDatas(targetTenantAdmin, authorizedObjectApiNames, null);
            if (CollectionUtils.isEmpty(targetAccountAuthorizationDatas)) {
                log.info("copyAuthorizationDetails targetAccountAuthorizationDatas is emtpy targetTenantId[{}], label[{}]", targetTenantId, arg.getLabel());
                continue;
            }
            List<String> targetAccountAuthDataIds = targetAccountAuthorizationDatas.stream().map(DBRecord::getId).collect(Collectors.toList());

            //查targetTenantId的授权明细
            List<IObjectData> allTargetAuthDetailDatas = authorizationDetailManager.query(targetTenantAdmin, targetAccountAuthDataIds);
            Map<String, List<IObjectData>> targetAccountAuthDataId2AuthDetails = allTargetAuthDetailDatas.stream()
                    .collect(Collectors.groupingBy(d -> d.get(AuthorizationDetailConstant.Field.FAccountAuthorizationId.apiName, String.class)));

            //把模板企业的从对象复制过来
            for (CurlModel.AuthInfo authInfo : arg.getAuthInfos()) {
                String authorizedObjectApiName = authInfo.getAuthorizedObjectApiName();
                String authorizedType = authInfo.getAuthorizedType();

                IObjectData targetAccountAuthData = getAccountAuthData(targetAccountAuthorizationDatas, authorizedObjectApiName, authorizedType);
                if (targetAccountAuthData == null) {
                    log.info("copyAuthorizationDetails targetAccountAuthData == null authorizedObjectApiName[{}], authorizedType[{}]", authorizedObjectApiName, authorizedType);
                    continue;
                }
                String targetAccountAuthDataId = targetAccountAuthData.getId();

                IObjectData sourceAccountAuthData = getAccountAuthData(sourceAccountAuthorizationDatas, authorizedObjectApiName, authorizedType);
                if (sourceAccountAuthData == null) {
                    log.info("copyAuthorizationDetails sourceAccountAuthData == null authorizedObjectApiName[{}], authorizedType[{}]", authorizedObjectApiName, authorizedType);
                    continue;
                }
                String sourceAccountAuthDataId = sourceAccountAuthData.getId();

                List<IObjectData> targetAuthDetailDatas = targetAccountAuthDataId2AuthDetails.get(targetAccountAuthDataId);
                List<IObjectData> sourceAuthDetailDatas = sourceAccountAuthDataId2AuthDetails.get(sourceAccountAuthDataId);
                authorizationDetailManager.copy(targetTenantId, targetAccountAuthDataId, sourceAuthDetailDatas, targetAuthDetailDatas, arg.isUseSourceId());
            }

            successTenantIds.add(targetTenantId);
            log.info("copyAuthorizationDetails end tenantId[{}], label[{}]", targetTenantId, arg.getLabel());
        }

        return new CurlModel.CopyAuthorizationDetailsResult(notOpenTenantIds, successTenantIds, targetAccountAuthorizationDatasIsEmptyTenantIds);
    }

    @Override
    public CurlModel.TransferIncomeAccountAuthResult transferIncomeAccountAuth(ServiceContext serviceContext, CurlModel.TransferIncomeAccountAuthArg arg) {
        List<String> notOpenTenantIds = Lists.newArrayList();
        List<String> accountNoExistTenantIds = Lists.newArrayList();
        List<String> successTenantIds = Lists.newArrayList();
        Map<String, List<String>> objectApiName2copyTenantIds = new HashMap<>();
        Map<String, List<String>> objectApiName2updateTenantIds = new HashMap<>();
        Map<String, List<String>> targetTenantId2NotExistFundAccountNames = new HashMap<>();
        for (String incomeAuthorizedObjectApiName : arg.getIncomeAuthorizedObjectApiNames()) {
            objectApiName2copyTenantIds.put(incomeAuthorizedObjectApiName, Lists.newArrayList());
            objectApiName2updateTenantIds.put(incomeAuthorizedObjectApiName, Lists.newArrayList());
        }
        List<String> sourceNotExistIncomeAuthObjectApiNames = Lists.newArrayList();

        if (CollectionUtils.isEmpty(arg.getTargetTenantIds()) || CollectionUtils.isEmpty(arg.getIncomeAuthorizedObjectApiNames())) {
            return new CurlModel.TransferIncomeAccountAuthResult(Lists.newArrayList(), Lists.newArrayList(), Lists.newArrayList(), objectApiName2copyTenantIds, objectApiName2updateTenantIds, targetTenantId2NotExistFundAccountNames, sourceNotExistIncomeAuthObjectApiNames);
        }

        String sourceTenantId = arg.getSourceTenantId();
        //查'模板企业'的【账户授权】
        User sourceTenantAdmin = User.systemUser(sourceTenantId);
        List<String> incomeAuthorizedObjectApiNames = arg.getIncomeAuthorizedObjectApiNames();
        List<IObjectData> sourceAccountAuthorizationDatas = fAccountAuthorizationManager.getFAccountAuthorizationDatas(sourceTenantAdmin, incomeAuthorizedObjectApiNames, FAccountAuthAuthorizedTypeEnum.Income.getValue());
        //'模版企业'不存在的【入账授权】
        List<String> sourceExistIncomeAuthObjectApiNames = sourceAccountAuthorizationDatas.stream().map(d -> d.get(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, String.class)).collect(Collectors.toList());
        sourceNotExistIncomeAuthObjectApiNames = arg.getIncomeAuthorizedObjectApiNames().stream().filter(d -> !sourceExistIncomeAuthObjectApiNames.contains(d)).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(sourceAccountAuthorizationDatas)) {
            log.info("transferIncomeAccountAuth sourceAccountAuthorizationDatas is empty label[{}]", arg.getLabel());
            return new CurlModel.TransferIncomeAccountAuthResult(Lists.newArrayList(), Lists.newArrayList(), Lists.newArrayList(), objectApiName2copyTenantIds, objectApiName2updateTenantIds, targetTenantId2NotExistFundAccountNames, sourceNotExistIncomeAuthObjectApiNames);
        }
        List<String> sourceAccountAuthDataIds = sourceAccountAuthorizationDatas.stream().map(DBRecord::getId).collect(Collectors.toList());
        Map<String, List<IObjectData>> sourceAuthorizedObjectApiName2AccountAuths = sourceAccountAuthorizationDatas.stream().collect(Collectors.groupingBy(d -> d.get(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, String.class)));


        //查'模板企业'的【授权明细】
        List<IObjectData> allSourceAuthDetailDatas = authorizationDetailManager.query(sourceTenantAdmin, sourceAccountAuthDataIds);
        Map<String, List<IObjectData>> sourceAccountAuthDataId2AuthDetails = allSourceAuthDetailDatas.stream().collect(Collectors.groupingBy(d -> d.get(AuthorizationDetailConstant.Field.FAccountAuthorizationId.apiName, String.class)));

        //查'模板企业'的【入账规则】FAccountEntryRuleObj
        List<IObjectData> allSourceAccountEntryRuleDatas = fAccountEntryRuleManager.query(sourceTenantAdmin, sourceAccountAuthDataIds);
        Map<String, List<IObjectData>> sourceAccountAuthDataId2AccountEntryRules = allSourceAccountEntryRuleDatas.stream().collect(Collectors.groupingBy(d -> d.get(FAccountEntryRuleConstants.Field.FAccountAuthorizationId.apiName, String.class)));

        //查'模板企业'的【账户】
        List<IObjectData> sourceFundAccounts = fundAccountManager.getAccounts(sourceTenantId, false);

        for (String targetTenantId : arg.getTargetTenantIds()) {
            log.info("transferIncomeAccountAuth begin targetTenantId[{}], label[{}]", targetTenantId, arg.getLabel());

            boolean isAccountAuthOpen = fundAccountConfigManager.isAccountAuthOpen(targetTenantId);
            if (!isAccountAuthOpen) {
                notOpenTenantIds.add(targetTenantId);
                continue;
            }

            // '模板企业' 有的账户，targetTenantId中是否存在
            List<IObjectData> targetFundAccounts = fundAccountManager.getAccounts(targetTenantId, false);
            Set<String> sourceFundAccountIds1 = allSourceAuthDetailDatas.stream().map(d -> d.get(AuthorizationDetailConstant.Field.AuthorizeAccountId.apiName, String.class)).collect(Collectors.toSet());
            Set<String> sourceFundAccountIds2 = allSourceAccountEntryRuleDatas.stream().map(d -> d.get(FAccountEntryRuleConstants.Field.FAccountId.apiName, String.class)).collect(Collectors.toSet());
            Set<String> sourceFundAccountIds = new HashSet<>();
            sourceFundAccountIds.addAll(sourceFundAccountIds1);
            sourceFundAccountIds.addAll(sourceFundAccountIds2);
            Map<String, String> sourceFundAccountId2TargetFundAccountId = fundAccountManager.getSourceFundAccountId2TargetFundAccountId(sourceFundAccountIds, sourceFundAccounts, targetFundAccounts);
            //在targetTenantId中找不到同名的账户ID
            List<String> notExistSameNameSourceFundAccountIds = sourceFundAccountIds.stream().filter(id -> !sourceFundAccountId2TargetFundAccountId.containsKey(id)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(notExistSameNameSourceFundAccountIds)) {
                log.warn("transferIncomeAccountAuth targetTenantId[{}], notExistSameNameSourceFundAccountIds[{}]", targetTenantId, notExistSameNameSourceFundAccountIds);
                accountNoExistTenantIds.add(targetTenantId);
                List<String> notExistFundAccountNames = sourceFundAccounts.stream().filter(d -> notExistSameNameSourceFundAccountIds.contains(d.getId())).map(IObjectData::getName).collect(Collectors.toList());
                targetTenantId2NotExistFundAccountNames.put(targetTenantId, notExistFundAccountNames);
                continue;
            }

            //查targetTenantId的【账户授权】
            User targetTenantAdmin = User.systemUser(targetTenantId);
            List<IObjectData> targetAccountAuthorizationDatas = fAccountAuthorizationManager.getFAccountAuthorizationDatas(targetTenantAdmin, incomeAuthorizedObjectApiNames, FAccountAuthAuthorizedTypeEnum.Income.getValue());
            if (targetAccountAuthorizationDatas == null) {
                targetAccountAuthorizationDatas = Lists.newArrayList();
            }
            List<String> targetAccountAuthDataIds = targetAccountAuthorizationDatas.stream().map(DBRecord::getId).collect(Collectors.toList());

            //查targetTenantId的【入账规则】FAccountEntryRuleObj
            List<IObjectData> allTargetAccountEntryRuleDatas = fAccountEntryRuleManager.query(targetTenantAdmin, targetAccountAuthDataIds);
            Map<String, List<IObjectData>> targetAccountAuthDataId2AccountEntryRules = allTargetAccountEntryRuleDatas.stream().collect(Collectors.groupingBy(d -> d.get(FAccountEntryRuleConstants.Field.FAccountAuthorizationId.apiName, String.class)));

            //查targetTenantId的【授权明细】
            List<IObjectData> allTargetAuthDetailDatas = authorizationDetailManager.query(targetTenantAdmin, targetAccountAuthDataIds);
            Map<String, List<IObjectData>> targetAccountAuthDataId2AuthDetails = allTargetAuthDetailDatas.stream().collect(Collectors.groupingBy(d -> d.get(AuthorizationDetailConstant.Field.FAccountAuthorizationId.apiName, String.class)));

            /**
             * 1、如果没对应的入账授权，同步过来
             * 2、如果有对应的入账授权，如果开启自动入账，更新自动入账，同步【入账规则】FAccountEntryRuleObj
             */
            for (String incomeAuthorizedObjectApiName : arg.getIncomeAuthorizedObjectApiNames()) {
                log.info("transferIncomeAccountAuth begin targetTenantId[{}], incomeAuthorizedObjectApiName[{}], label[{}]", targetTenantId, incomeAuthorizedObjectApiName, arg.getLabel());
                String authorizedType = FAccountAuthAuthorizedTypeEnum.Income.getValue();

                IObjectData targetAccountAuthData = getAccountAuthData(targetAccountAuthorizationDatas, incomeAuthorizedObjectApiName, authorizedType);
                if (targetAccountAuthData == null) {
                    //如果没对应的入账授权，同步过来
                    log.info("transferIncomeAccountAuth targetAccountAuthData == null authorizedObjectApiName[{}], authorizedType[{}]", incomeAuthorizedObjectApiName, authorizedType);
                    if (!sourceAuthorizedObjectApiName2AccountAuths.containsKey(incomeAuthorizedObjectApiName)) {
                        log.warn("transferIncomeAccountAuth sourceAuthorizedObjectApiName2AccountAuths[{}] not contain incomeAuthorizedObjectApiName[{}], arg[{}]", sourceAuthorizedObjectApiName2AccountAuths, incomeAuthorizedObjectApiName, arg);
                        continue;
                    }
                    IObjectData sourceAccountAuthData = sourceAuthorizedObjectApiName2AccountAuths.get(incomeAuthorizedObjectApiName).get(0);
                    List<IObjectData> sourceAuthDetailDatas = sourceAccountAuthDataId2AuthDetails.get(sourceAccountAuthData.getId());
                    List<IObjectData> sourceAccountEntryRuleDatas = sourceAccountAuthDataId2AccountEntryRules.get(sourceAccountAuthData.getId());
                    List<IObjectData> tempSourceAuthDetailDatas = ObjectDataExt.copyList(sourceAuthDetailDatas);
                    List<IObjectData> tempSourceAccountEntryRuleDatas = ObjectDataExt.copyList(sourceAccountEntryRuleDatas);
                    //账户ID换成本企业的
                    replaceFundAccountId(targetTenantId, tempSourceAuthDetailDatas, tempSourceAccountEntryRuleDatas, sourceFundAccountId2TargetFundAccountId);
                    fAccountAuthorizationManager.copy(targetTenantId, sourceAccountAuthData, tempSourceAuthDetailDatas, null, tempSourceAccountEntryRuleDatas, arg.isNeedInitIfSourceTenantIdHasInit());
                    List<String> tenantIds = objectApiName2copyTenantIds.get(incomeAuthorizedObjectApiName);
                    tenantIds.add(targetTenantId);
                    objectApiName2copyTenantIds.put(incomeAuthorizedObjectApiName, tenantIds);
                } else {
                    if (!arg.isNeedUpdateAutoEnterAccount()) {
                        continue;
                    }
                    IObjectData sourceAccountAuthData = getAccountAuthData(sourceAccountAuthorizationDatas, incomeAuthorizedObjectApiName, authorizedType);
                    if (sourceAccountAuthData == null) {
                        log.info("transferIncomeAccountAuth sourceAccountAuthData == null authorizedObjectApiName[{}], authorizedType[{}]", incomeAuthorizedObjectApiName, authorizedType);
                        continue;
                    }

                    /**
                     * 【授权明细】
                     *     accountId 缺的补上
                     */
                    String targetAccountAuthDataId = targetAccountAuthData.getId();
                    String sourceAccountAuthDataId = sourceAccountAuthData.getId();

                    List<IObjectData> targetAuthDetails = targetAccountAuthDataId2AuthDetails.get(targetAccountAuthDataId);
                    List<IObjectData> sourceAuthDetails = sourceAccountAuthDataId2AuthDetails.get(sourceAccountAuthDataId);
                    List<IObjectData> tempSourceAuthDetails = ObjectDataExt.copyList(sourceAuthDetails);
                    replaceFundAccountId(targetTenantId, tempSourceAuthDetails, null, sourceFundAccountId2TargetFundAccountId);
                    authorizationDetailManager.copy(targetTenantId, targetAccountAuthDataId, tempSourceAuthDetails, targetAuthDetails, true);

                    /**
                     * 【入账规则】
                     * 【账户授权】的【自动入账】
                     */
                    boolean sourceAutoEntryStatus = sourceAccountAuthData.get(FAccountAuthorizationConstants.Field.AutoEntryStatus.apiName, Boolean.class, false);
                    if (!sourceAutoEntryStatus) {
                        //更新AutoEntryStatus为false
                        fAccountAuthorizationManager.updateAutoEntryStatus(targetTenantId, targetAccountAuthData, false, true);
                    } else {
                        /**
                         * 【入账规则】FAccountEntryRuleObj
                         *     accountId 不一样的删掉（可选）
                         *     accountId EntryCondition不一样的替换（可选）
                         *     accountId 缺的补上
                         */
                        List<IObjectData> targetAccountEntryRules = targetAccountAuthDataId2AccountEntryRules.get(targetAccountAuthDataId);
                        List<IObjectData> sourceAccountEntryRules = sourceAccountAuthDataId2AccountEntryRules.get(sourceAccountAuthDataId);
                        List<IObjectData> tempSourceAccountEntryRules = ObjectDataExt.copyList(sourceAccountEntryRules);
                        replaceFundAccountId(targetTenantId, null, tempSourceAccountEntryRules, sourceFundAccountId2TargetFundAccountId);
                        fAccountEntryRuleManager.update(targetTenantId, targetAccountAuthDataId, tempSourceAccountEntryRules, targetAccountEntryRules, arg.isNeedDeleteAccountEntryRuleIfSourceNotExist(), arg.isNeedUpdateEntryConditionIfExist());

                        //更新AutoEntryStatus为true
                        boolean targetAutoEntryStatus = targetAccountAuthData.get(FAccountAuthorizationConstants.Field.AutoEntryStatus.apiName, Boolean.class, false);
                        if (!targetAutoEntryStatus) {
                            log.info("transferIncomeAccountAuth updateAutoEntryStatus targetTenantId[{}], targetAccountAuthDataId[{}]", targetTenantId, targetAccountAuthData.getId());
                            fAccountAuthorizationManager.updateAutoEntryStatus(targetTenantId, targetAccountAuthData, true, false);
                        }
                    }

                    List<String> tenantIds = objectApiName2updateTenantIds.get(incomeAuthorizedObjectApiName);
                    tenantIds.add(targetTenantId);
                    objectApiName2updateTenantIds.put(incomeAuthorizedObjectApiName, tenantIds);
                }

                log.info("transferIncomeAccountAuth end targetTenantId[{}], incomeAuthorizedObjectApiName[{}], label[{}]", targetTenantId, incomeAuthorizedObjectApiName, arg.getLabel());
            }

            successTenantIds.add(targetTenantId);
            log.info("transferIncomeAccountAuth end targetTenantId[{}], label[{}]", targetTenantId, arg.getLabel());
        }

        CurlModel.TransferIncomeAccountAuthResult result = new CurlModel.TransferIncomeAccountAuthResult(notOpenTenantIds, accountNoExistTenantIds, successTenantIds, objectApiName2copyTenantIds, objectApiName2updateTenantIds, targetTenantId2NotExistFundAccountNames, sourceNotExistIncomeAuthObjectApiNames);
        log.info("transferIncomeAccountAuth finish result[{}], arg[{}]", result, arg);
        return result;
    }

    @Override
    public CurlModel.TransferOutcomeAccountAuthResult transferOutcomeAccountAuth(ServiceContext serviceContext, CurlModel.TransferOutcomeAccountAuthArg arg) {
        String traceId = TraceContext.get().getTraceId();

        List<String> notOpenTenantIds = Lists.newArrayList();
        List<String> accountNoExistTenantIds = Lists.newArrayList();
        List<String> successTenantIds = Lists.newArrayList();

        //复制了哪些
        Map<String, List<String>> objectApiName2copyTenantIds = new HashMap<>();
        //更新了哪些
        Map<String, List<String>> objectApiName2updateTenantIds = new HashMap<>();

        //目前企业缺少哪些账户
        Map<String, List<String>> targetTenantId2NotExistFundAccountNames = new HashMap<>();

        for (String objectApiName : arg.getOutcomeAuthorizedObjectApiNames()) {
            objectApiName2copyTenantIds.put(objectApiName, Lists.newArrayList());
            objectApiName2updateTenantIds.put(objectApiName, Lists.newArrayList());
        }

        List<String> sourceNotExistOutcomeAuthObjectApiNames = Lists.newArrayList();

        if (CollectionUtils.isEmpty(arg.getTargetTenantIds())) {
            return new CurlModel.TransferOutcomeAccountAuthResult(traceId, Lists.newArrayList(), Lists.newArrayList(), Lists.newArrayList(), objectApiName2copyTenantIds, objectApiName2updateTenantIds, targetTenantId2NotExistFundAccountNames, sourceNotExistOutcomeAuthObjectApiNames);
        }

        String sourceTenantId = arg.getSourceTenantId();
        //查'模板企业'的【账户授权】
        User sourceTenantAdmin = User.systemUser(sourceTenantId);
        List<String> outcomeAuthorizedObjectApiNames = arg.getOutcomeAuthorizedObjectApiNames();
        List<IObjectData> sourceAccountAuthorizationDatas = fAccountAuthorizationManager.getFAccountAuthorizationDatas(sourceTenantAdmin, outcomeAuthorizedObjectApiNames, FAccountAuthAuthorizedTypeEnum.Outcome.getValue());

        //'模版企业'不存在的【支出授权】
        List<String> sourceExistOutcomeAuthObjectApiNames = sourceAccountAuthorizationDatas.stream().map(d -> d.get(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, String.class)).collect(Collectors.toList());
        sourceNotExistOutcomeAuthObjectApiNames = arg.getOutcomeAuthorizedObjectApiNames().stream().filter(d -> !sourceExistOutcomeAuthObjectApiNames.contains(d)).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(sourceAccountAuthorizationDatas)) {
            log.info("transferOutcomeAccountAuth sourceAccountAuthorizationDatas is empty label[{}]", arg.getLabel());
            return new CurlModel.TransferOutcomeAccountAuthResult(traceId, Lists.newArrayList(), Lists.newArrayList(), Lists.newArrayList(), objectApiName2copyTenantIds, objectApiName2updateTenantIds, targetTenantId2NotExistFundAccountNames, sourceNotExistOutcomeAuthObjectApiNames);
        }

        List<String> sourceAccountAuthDataIds = sourceAccountAuthorizationDatas.stream().map(DBRecord::getId).collect(Collectors.toList());
        Map<String, List<IObjectData>> sourceAuthorizedObjectApiName2AccountAuths = sourceAccountAuthorizationDatas.stream().collect(Collectors.groupingBy(d -> d.get(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, String.class)));


        //查'模板企业'的【授权明细】
        List<IObjectData> allSourceAuthDetailDatas = authorizationDetailManager.query(sourceTenantAdmin, sourceAccountAuthDataIds);
        Map<String, List<IObjectData>> sourceAccountAuthDataId2AuthDetails = allSourceAuthDetailDatas.stream().collect(Collectors.groupingBy(d -> d.get(AuthorizationDetailConstant.Field.FAccountAuthorizationId.apiName, String.class)));

        //查'模板企业'的【解冻授权明细】
        List<IObjectData> allSourceUnfreezeAuthDetails = unfreezeAuthDetailManager.query(sourceTenantId, sourceAccountAuthDataIds);
        Map<String, List<IObjectData>> sourceAccountAuthDataId2UnfreezeAuthDetails = allSourceUnfreezeAuthDetails.stream().collect(Collectors.groupingBy(d -> d.get(UnfreezeAuthDetailConstants.Field.FAccountAuthorizationId.apiName, String.class)));

        //查'模板企业'的【账户】
        List<IObjectData> sourceFundAccounts = fundAccountManager.getAccounts(sourceTenantId, false);

        for (String targetTenantId : arg.getTargetTenantIds()) {
            log.info("transferOutcomeAccountAuth begin targetTenantId[{}], label[{}]", targetTenantId, arg.getLabel());

            boolean isAccountAuthOpen = fundAccountConfigManager.isAccountAuthOpen(targetTenantId);
            if (!isAccountAuthOpen) {
                notOpenTenantIds.add(targetTenantId);
                continue;
            }

            // '模板企业' 有的账户，targetTenantId中是否存在
            List<IObjectData> targetFundAccounts = fundAccountManager.getAccounts(targetTenantId, false);
            Set<String> sourceFundAccountIds = allSourceAuthDetailDatas.stream().map(d -> d.get(AuthorizationDetailConstant.Field.AuthorizeAccountId.apiName, String.class)).collect(Collectors.toSet());
            Map<String, String> sourceFundAccountId2TargetFundAccountId = fundAccountManager.getSourceFundAccountId2TargetFundAccountId(sourceFundAccountIds, sourceFundAccounts, targetFundAccounts);
            //在targetTenantId中找不到同名的账户ID
            List<String> notExistSameNameSourceFundAccountIds = sourceFundAccountIds.stream().filter(id -> !sourceFundAccountId2TargetFundAccountId.containsKey(id)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(notExistSameNameSourceFundAccountIds)) {
                log.warn("transferOutcomeAccountAuth targetTenantId[{}], notExistSameNameSourceFundAccountIds[{}]", targetTenantId, notExistSameNameSourceFundAccountIds);
                accountNoExistTenantIds.add(targetTenantId);
                List<String> notExistFundAccountNames = sourceFundAccounts.stream().filter(d -> notExistSameNameSourceFundAccountIds.contains(d.getId())).map(IObjectData::getName).collect(Collectors.toList());
                targetTenantId2NotExistFundAccountNames.put(targetTenantId, notExistFundAccountNames);
                continue;
            }

            //查targetTenantId的【账户授权】
            User targetTenantAdmin = User.systemUser(targetTenantId);
            List<IObjectData> targetAccountAuthorizationDatas = fAccountAuthorizationManager.getFAccountAuthorizationDatas(targetTenantAdmin, outcomeAuthorizedObjectApiNames, FAccountAuthAuthorizedTypeEnum.Outcome.getValue());
            if (targetAccountAuthorizationDatas == null) {
                targetAccountAuthorizationDatas = Lists.newArrayList();
            }

            List<String> targetAccountAuthDataIds = targetAccountAuthorizationDatas.stream().map(DBRecord::getId).collect(Collectors.toList());

            //查targetTenantId的【授权明细】
            List<IObjectData> allTargetAuthDetailDatas = authorizationDetailManager.query(targetTenantAdmin, targetAccountAuthDataIds);
            Map<String, List<IObjectData>> targetAccountAuthDataId2AuthDetails = allTargetAuthDetailDatas.stream().collect(Collectors.groupingBy(d -> d.get(AuthorizationDetailConstant.Field.FAccountAuthorizationId.apiName, String.class)));

            //查targetTenantId的【解冻授权明细】
            List<IObjectData> allTargetUnfreezeAuthDetails = unfreezeAuthDetailManager.query(targetTenantId, targetAccountAuthDataIds);
            Map<String, List<IObjectData>> targetAccountAuthDataId2UnfreezeAuthDetails = allTargetUnfreezeAuthDetails.stream().collect(Collectors.groupingBy(d -> d.get(UnfreezeAuthDetailConstants.Field.FAccountAuthorizationId.apiName, String.class)));


            /**
             * 新建/更新【支出授权】
             * 1、如果没对应的支出授权，同步过来
             * 2、如果有对应的支出授权，且需要更新，则做更新
             */
            for (String outcomeAuthorizedObjectApiName : arg.getOutcomeAuthorizedObjectApiNames()) {
                log.info("transferOutcomeAccountAuth begin targetTenantId[{}], outcomeAuthorizedObjectApiName[{}], label[{}]", targetTenantId, outcomeAuthorizedObjectApiName, arg.getLabel());
                String authorizedType = FAccountAuthAuthorizedTypeEnum.Outcome.getValue();

                IObjectData targetAccountAuthData = getAccountAuthData(targetAccountAuthorizationDatas, outcomeAuthorizedObjectApiName, authorizedType);
                if (targetAccountAuthData == null) {
                    //如果没对应的支出授权，同步过来
                    log.info("transferOutcomeAccountAuth targetAccountAuthData == null authorizedObjectApiName[{}], authorizedType[{}]", outcomeAuthorizedObjectApiName, authorizedType);
                    if (!sourceAuthorizedObjectApiName2AccountAuths.containsKey(outcomeAuthorizedObjectApiName)) {
                        log.warn("transferOutcomeAccountAuth sourceAuthorizedObjectApiName2AccountAuths[{}] not contain outcomeAuthorizedObjectApiName[{}], arg[{}]", sourceAuthorizedObjectApiName2AccountAuths, outcomeAuthorizedObjectApiName, arg);
                        continue;
                    }
                    IObjectData sourceAccountAuthData = sourceAuthorizedObjectApiName2AccountAuths.get(outcomeAuthorizedObjectApiName).get(0);
                    if (sourceAccountAuthData == null) {
                        log.info("transferOutcomeAccountAuth sourceAccountAuthData == null outcomeAuthorizedObjectApiName[{}]", outcomeAuthorizedObjectApiName);
                        continue;
                    }
                    List<IObjectData> sourceAuthDetailDatas = sourceAccountAuthDataId2AuthDetails.get(sourceAccountAuthData.getId());
                    List<IObjectData> tempSourceAuthDetailDatas = ObjectDataExt.copyList(sourceAuthDetailDatas);
                    List<IObjectData> sourceUnfreezeAuthDetails = sourceAccountAuthDataId2UnfreezeAuthDetails.get(sourceAccountAuthData.getId());

                    //账户ID换成本企业的
                    replaceFundAccountId(targetTenantId, tempSourceAuthDetailDatas, null, sourceFundAccountId2TargetFundAccountId);
                    fAccountAuthorizationManager.copy(targetTenantId, sourceAccountAuthData, tempSourceAuthDetailDatas, sourceUnfreezeAuthDetails, null, arg.isNeedInitIfSourceTenantIdHasInit());
                    List<String> tenantIds = objectApiName2copyTenantIds.get(outcomeAuthorizedObjectApiName);
                    tenantIds.add(targetTenantId);
                    objectApiName2copyTenantIds.put(outcomeAuthorizedObjectApiName, tenantIds);
                } else {
                    if (!arg.isExistUpdate()) {
                        continue;
                    }
                    IObjectData sourceAccountAuthData = getAccountAuthData(sourceAccountAuthorizationDatas, outcomeAuthorizedObjectApiName, authorizedType);
                    if (sourceAccountAuthData == null) {
                        log.info("transferOutcomeAccountAuth sourceAccountAuthData == null authorizedObjectApiName[{}], authorizedType[{}]", outcomeAuthorizedObjectApiName, authorizedType);
                        continue;
                    }

                    /**
                     * 【授权明细】
                     *     accountId 缺的补上
                     */
                    String targetAccountAuthDataId = targetAccountAuthData.getId();
                    String sourceAccountAuthDataId = sourceAccountAuthData.getId();

                    List<IObjectData> targetAuthDetails = targetAccountAuthDataId2AuthDetails.get(targetAccountAuthDataId);
                    List<IObjectData> sourceAuthDetails = sourceAccountAuthDataId2AuthDetails.get(sourceAccountAuthDataId);
                    List<IObjectData> tempSourceAuthDetails = ObjectDataExt.copyList(sourceAuthDetails);
                    replaceFundAccountId(targetTenantId, tempSourceAuthDetails, null, sourceFundAccountId2TargetFundAccountId);
                    authorizationDetailManager.copy(targetTenantId, targetAccountAuthDataId, tempSourceAuthDetails, targetAuthDetails, true);

                    /**
                     * 【解冻授权明细】
                     *     UnfreezeObject 缺的补上
                     */
                    List<IObjectData> targetUnfreezeAuthDetails = targetAccountAuthDataId2UnfreezeAuthDetails.get(targetAccountAuthDataId);
                    List<IObjectData> sourceUnfreezeAuthDetails = sourceAccountAuthDataId2UnfreezeAuthDetails.get(sourceAccountAuthDataId);
                    unfreezeAuthDetailManager.copy(targetTenantId, targetAccountAuthDataId, sourceUnfreezeAuthDetails, targetUnfreezeAuthDetails, true);

                    List<String> tenantIds = objectApiName2updateTenantIds.get(outcomeAuthorizedObjectApiName);
                    tenantIds.add(targetTenantId);
                    objectApiName2updateTenantIds.put(outcomeAuthorizedObjectApiName, tenantIds);
                }

                log.info("transferOutcomeAccountAuth end targetTenantId[{}], outcomeAuthorizedObjectApiName[{}], label[{}]", targetTenantId, outcomeAuthorizedObjectApiName, arg.getLabel());
            }

            successTenantIds.add(targetTenantId);
            log.info("transferOutcomeAccountAuth end targetTenantId[{}], label[{}]", targetTenantId, arg.getLabel());
        }

        CurlModel.TransferOutcomeAccountAuthResult result = new CurlModel.TransferOutcomeAccountAuthResult(traceId, notOpenTenantIds, accountNoExistTenantIds, successTenantIds, objectApiName2copyTenantIds, objectApiName2updateTenantIds, targetTenantId2NotExistFundAccountNames, sourceNotExistOutcomeAuthObjectApiNames);
        log.info("transferOutcomeAccountAuth finish result[{}], arg[{}]", result, arg);
        return result;
    }

    private IObjectData getAccountAuthData(List<IObjectData> accountAuthorizationDatas, String authorizedObjectApiName, String authorizedType) {
        for (IObjectData accountAuthorizationData : accountAuthorizationDatas) {
            String authorizedObjectApiNameTemp = accountAuthorizationData.get(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, String.class);
            String authorizedTypeTemp = accountAuthorizationData.get(FAccountAuthorizationConstants.Field.AuthorizedType.apiName, String.class);
            if (Objects.equals(authorizedObjectApiName, authorizedObjectApiNameTemp) && Objects.equals(authorizedType, authorizedTypeTemp)) {
                return accountAuthorizationData;
            }
        }
        return null;
    }

    //Id不同, name 一样
    private boolean hasSameId(List<IObjectData> fundAccountDatas, IObjectData sourceFundAccountData) {
        String sourceFundAccountId = sourceFundAccountData.get(SystemConstants.Field.Id.apiName, String.class);

        for (IObjectData fundAccountData : fundAccountDatas) {
            String fundAccountId = fundAccountData.get(SystemConstants.Field.Id.apiName, String.class);

            //Id不同, name 一样
            if (Objects.equals(sourceFundAccountId, fundAccountId)) {
                return true;
            }
        }
        return false;
    }

    //Id不同, name 一样
    private boolean hasDifferentIdAndSameName(List<IObjectData> fundAccountDatas, IObjectData sourceFundAccountData) {
        String sourceFundAccountName = sourceFundAccountData.get(FundAccountConstants.Field.Name.apiName, String.class);
        String sourceFundAccountId = sourceFundAccountData.get(SystemConstants.Field.Id.apiName, String.class);

        for (IObjectData fundAccountData : fundAccountDatas) {
            String fundAccountName = fundAccountData.get(FundAccountConstants.Field.Name.apiName, String.class);
            String fundAccountId = fundAccountData.get(SystemConstants.Field.Id.apiName, String.class);

            //Id不同, name 一样
            if (!Objects.equals(sourceFundAccountId, fundAccountId) && Objects.equals(sourceFundAccountName, fundAccountName)) {
                return true;
            }
        }
        return false;
    }

    //id name 都不一样
    private boolean idAndNameAllDifferent(List<IObjectData> fundAccountDatas, IObjectData sourceFundAccountData) {
        String sourceFundAccountName = sourceFundAccountData.get(FundAccountConstants.Field.Name.apiName, String.class);
        String sourceFundAccountId = sourceFundAccountData.get(SystemConstants.Field.Id.apiName, String.class);

        for (IObjectData fundAccountData : fundAccountDatas) {
            String fundAccountName = fundAccountData.get(FundAccountConstants.Field.Name.apiName, String.class);
            String fundAccountId = fundAccountData.get(SystemConstants.Field.Id.apiName, String.class);

            //Id, name 都一样
            if (Objects.equals(sourceFundAccountId, fundAccountId) || Objects.equals(sourceFundAccountName, fundAccountName)) {
                return false;
            }
        }
        return true;
    }

    /**
     * sourceAuthDetailDatas 和 sourceAccountEntryRuleDatas 里面的账户ID，根据账户名称，换成targetTenantId里面的
     */
    private void replaceFundAccountId(String targetTenantId, List<IObjectData> sourceAuthDetailDatas, List<IObjectData> sourceAccountEntryRuleDatas, Map<String, String> sourceFundAccountId2TargetFundAccountId) {
        if (!CollectionUtils.isEmpty(sourceAuthDetailDatas)) {
            for (IObjectData sourceAuthDetailData : sourceAuthDetailDatas) {
                String sourceAuthorizeAccountId = sourceAuthDetailData.get(AuthorizationDetailConstant.Field.AuthorizeAccountId.apiName, String.class);
                String targetAuthorizeAccountId = sourceFundAccountId2TargetFundAccountId.get(sourceAuthorizeAccountId);
                if (Strings.isNullOrEmpty(targetAuthorizeAccountId)) {
                    log.warn("replaceFundAccountId targetTenantId[{}], sourceAuthDetailData[{}], sourceFundAccountId2TargetFundAccountId[{}]", targetTenantId, sourceAuthDetailData, sourceFundAccountId2TargetFundAccountId);
                }
                sourceAuthDetailData.set(AuthorizationDetailConstant.Field.AuthorizeAccountId.apiName, targetAuthorizeAccountId);
            }
        }

        if (!CollectionUtils.isEmpty(sourceAccountEntryRuleDatas)) {
            for (IObjectData sourceAccountEntryRuleData : sourceAccountEntryRuleDatas) {
                String sourceFAccountId = sourceAccountEntryRuleData.get(FAccountEntryRuleConstants.Field.FAccountId.apiName, String.class);
                String targetFAccountId = sourceFundAccountId2TargetFundAccountId.get(sourceFAccountId);
                if (Strings.isNullOrEmpty(targetFAccountId)) {
                    log.warn("replaceFundAccountId targetTenantId[{}], sourceAccountEntryRuleData[{}], sourceFundAccountId2TargetFundAccountId[{}]", targetTenantId, sourceAccountEntryRuleData, sourceFundAccountId2TargetFundAccountId);
                }
                sourceAccountEntryRuleData.set(FAccountEntryRuleConstants.Field.FAccountId.apiName, targetFAccountId);
            }
        }
    }

    @Override
    public CurlModel.InvalidFundAccountResult invalidFundAccount(ServiceContext serviceContext, CurlModel.InvalidFundAccountArg arg) {
        List<String> notOpenTenantIds = Lists.newArrayList();
        List<String> successTenantIds = Lists.newArrayList();

        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return new CurlModel.InvalidFundAccountResult(Lists.newArrayList(), Lists.newArrayList(), new HashSet<>());
        }

        String objectApiName = FundAccountConstants.API_NAME;

        Set<String> hasCanNotInvalidTenantIds = new HashSet<>();
        for (String targetTenantId : arg.getTenantIds()) {
            log.info("invalidFundAccount begin targetTenantId[{}]", targetTenantId);

            User admin = User.systemUser(targetTenantId);
            boolean isAccountAuthOpen = fundAccountConfigManager.isAccountAuthOpen(targetTenantId);
            if (!isAccountAuthOpen) {
                notOpenTenantIds.add(targetTenantId);
                continue;
            }

            //查本企业的
            List<IObjectData> fundAccountDatas = commonObjDataManager.queryAllObjectDataList(targetTenantId, objectApiName);

            for (IObjectData fundAccountData : fundAccountDatas) {
                if (!arg.getInvalidFundAccountNames().contains(fundAccountData.getName())) {
                    continue;
                }

                if (arg.getNotInvalidAccountIds().contains(fundAccountData.getId())) {
                    continue;
                }

                boolean canInvalid = canInvalid(targetTenantId, fundAccountData);
                if (!canInvalid) {
                    log.info("invalidFundAccount can not Invalid fundAccountDataId[{}]", fundAccountData.getId());
                    hasCanNotInvalidTenantIds.add(targetTenantId);
                } else {
                    //作废账户
                    serviceFacade.invalid(fundAccountData, admin);
                    serviceFacade.bulkDelete(Lists.newArrayList(fundAccountData), admin);
                }
            }

            successTenantIds.add(targetTenantId);
            log.info("invalidFundAccount end   tenantId[{}]", targetTenantId);
        }

        CurlModel.InvalidFundAccountResult result = new CurlModel.InvalidFundAccountResult(notOpenTenantIds, successTenantIds, hasCanNotInvalidTenantIds);
        log.info("invalidFundAccount finish  label[{}], result[{}]", arg.getLabel(), result);
        return result;
    }

    @Override
    public CurlModel.GetCannotInvalidFundAccountResult getCannotInvalidFundAccount(ServiceContext serviceContext, CurlModel.GetCannotInvalidFundAccountArg arg) {
        List<String> notOpenTenantIds = Lists.newArrayList();
        List<String> successTenantIds = Lists.newArrayList();

        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return new CurlModel.GetCannotInvalidFundAccountResult(Lists.newArrayList(), Lists.newArrayList(),
                    new HashSet<>(), new HashSet<>(), new HashSet<>(),
                    new HashMap<>(), new HashMap<>(), new HashMap<>());
        }

        String objectApiName = FundAccountConstants.API_NAME;

        Set<String> hasFlowTenantIds = new HashSet<>();
        Set<String> hasAccountBalanceMoreThanZeroTenantIds = new HashSet<>();
        Set<String> hasAvailableBalanceMoreThanZeroTenantIds = new HashSet<>();

        Map<String, List<CurlModel.Account>> tenantId2HasFlowAccounts = new HashMap<>();
        Map<String, List<CurlModel.Account>> tenantId2HasAccountBalanceMoreThanZeroAccounts = new HashMap<>();
        Map<String, List<CurlModel.Account>> tenantId2HasAvailableBalanceMoreThanZeroAccounts = new HashMap<>();

        for (String targetTenantId : arg.getTenantIds()) {
            log.info("getCannotInvalidFundAccount begin targetTenantId[{}]", targetTenantId);

            User admin = User.systemUser(targetTenantId);
            boolean isAccountAuthOpen = fundAccountConfigManager.isAccountAuthOpen(targetTenantId);
            if (!isAccountAuthOpen) {
                notOpenTenantIds.add(targetTenantId);
                continue;
            }

            List<CurlModel.Account> hasFlowAccounts = Lists.newArrayList();
            List<CurlModel.Account> hasAccountBalanceMoreThanZeroAccounts = Lists.newArrayList();
            List<CurlModel.Account> hasAvailableBalanceMoreThanZeroAccounts = Lists.newArrayList();

            //查本企业的
            List<IObjectData> fundAccountDatas = commonObjDataManager.queryAllObjectDataList(targetTenantId, objectApiName);

            for (IObjectData fundAccountData : fundAccountDatas) {
                if (!arg.getInvalidFundAccountNames().contains(fundAccountData.getName())) {
                    continue;
                }

                if (arg.getNotInvalidAccountIds().contains(fundAccountData.getId())) {
                    continue;
                }

                //有流水
                boolean hasTransactionFlow = transactionFlowDataManager.hasTransactionFlow(targetTenantId, fundAccountData.getId());
                if (hasTransactionFlow) {
                    hasFlowTenantIds.add(targetTenantId);
                    hasFlowAccounts.add(new CurlModel.Account(fundAccountData.getId(), fundAccountData.getName()));
                    continue;
                }

                //【客户账户余额】账户余额 > 0
                boolean hasAccountBalanceMoreThanZero = newCustomerAccountManager.hasAccountBalanceMoreThanZero(admin, fundAccountData.getId());
                if (hasAccountBalanceMoreThanZero) {
                    hasAccountBalanceMoreThanZeroTenantIds.add(targetTenantId);
                    hasAccountBalanceMoreThanZeroAccounts.add(new CurlModel.Account(fundAccountData.getId(), fundAccountData.getName()));
                    continue;
                }

                //【客户账户余额】账户可用金额 > 0
                boolean hasAvailableBalanceMoreThanZero = newCustomerAccountManager.hasAvailableBalanceMoreThanZero(admin, fundAccountData.getId());
                if (hasAvailableBalanceMoreThanZero) {
                    hasAvailableBalanceMoreThanZeroTenantIds.add(targetTenantId);
                    hasAvailableBalanceMoreThanZeroAccounts.add(new CurlModel.Account(fundAccountData.getId(), fundAccountData.getName()));
                }
            }

            if (!CollectionUtils.isEmpty(hasFlowTenantIds)) {
                tenantId2HasFlowAccounts.put(targetTenantId, hasFlowAccounts);
            }
            if (!CollectionUtils.isEmpty(hasAccountBalanceMoreThanZeroAccounts)) {
                tenantId2HasAccountBalanceMoreThanZeroAccounts.put(targetTenantId, hasAccountBalanceMoreThanZeroAccounts);
            }
            if (!CollectionUtils.isEmpty(hasAvailableBalanceMoreThanZeroAccounts)) {
                tenantId2HasAvailableBalanceMoreThanZeroAccounts.put(targetTenantId, hasAvailableBalanceMoreThanZeroAccounts);
            }

            successTenantIds.add(targetTenantId);
            log.info("getCannotInvalidFundAccount end tenantId[{}]", targetTenantId);
        }

        CurlModel.GetCannotInvalidFundAccountResult result = new CurlModel.GetCannotInvalidFundAccountResult(notOpenTenantIds, successTenantIds,
                hasFlowTenantIds, hasAccountBalanceMoreThanZeroTenantIds, hasAvailableBalanceMoreThanZeroTenantIds,
                tenantId2HasFlowAccounts, tenantId2HasAccountBalanceMoreThanZeroAccounts, tenantId2HasAvailableBalanceMoreThanZeroAccounts);

        log.info("getCannotInvalidFundAccount finish label[{}], result[{}]", arg.getLabel(), result);

        return result;
    }

    private boolean canInvalid(String tenantId, IObjectData fundAccountData) {
        //有流水不能作废
        boolean hasTransactionFlow = transactionFlowDataManager.hasTransactionFlow(tenantId, fundAccountData.getId());
        if (hasTransactionFlow) {
            log.info("canInvalid hasTransactionFlow tenantId[{}], dataId[{}]", tenantId, fundAccountData.getId());
            return false;
        }

        //【客户账户余额】账户余额 > 0  或 账户可用金额 > 0的，不能作废
        User admin = User.systemUser(tenantId);
        boolean hasAccountBalanceMoreThanZero = newCustomerAccountManager.hasAccountBalanceMoreThanZero(admin, fundAccountData.getId());
        if (hasAccountBalanceMoreThanZero) {
            log.info("canInvalid hasAccountBalanceMoreThanZero  user[{}], fundAccountId[{}]", admin, fundAccountData.getId());
            return false;
        }
        boolean hasAvailableBalanceMoreThanZero = newCustomerAccountManager.hasAvailableBalanceMoreThanZero(admin, fundAccountData.getId());
        if (hasAvailableBalanceMoreThanZero) {
            log.info("canInvalid hasAccountBalanceMoreThanZero  user[{}], fundAccountId[{}]", admin, fundAccountData.getId());
            return false;
        }

        return true;
    }

    @Override
    public CurlModel.InvalidAuthDetailResult invalidAuthDetail(ServiceContext serviceContext, CurlModel.InvalidAuthDetailArg arg) {
        List<String> notOpenTenantIds = Lists.newArrayList();
        List<String> successTenantIds = Lists.newArrayList();

        if (CollectionUtils.isEmpty(arg.getTargetTenantIds())) {
            return new CurlModel.InvalidAuthDetailResult(Lists.newArrayList(), Lists.newArrayList());
        }

        for (String targetTenantId : arg.getTargetTenantIds()) {
            log.info("invalidAuthDetail begin targetTenantId[{}]", targetTenantId);

            User admin = User.systemUser(targetTenantId);
            boolean isAccountAuthOpen = fundAccountConfigManager.isAccountAuthOpen(targetTenantId);
            if (!isAccountAuthOpen) {
                notOpenTenantIds.add(targetTenantId);
                continue;
            }

            for (CurlModel.AuthInfo authInfo : arg.getAuthInfos()) {
                //查所有的授权明细
                List<IObjectData> authDetails = authorizationDetailManager.query(targetTenantId, authInfo.getAuthorizedType(), authInfo.getAuthorizedObjectApiName());
                if (CollectionUtils.isEmpty(authDetails)) {
                    continue;
                }
                //排除掉不需要作废的
                if (!CollectionUtils.isEmpty(arg.getNoInvalidAuthorizeAccountIds())) {
                    authDetails = authDetails.stream().filter(d -> !arg.getNoInvalidAuthorizeAccountIds().contains(d.get(AuthorizationDetailConstant.Field.AuthorizeAccountId.apiName, String.class))).collect(Collectors.toList());
                }

                //作废
                try {
                    serviceFacade.bulkInvalid(authDetails, admin);
                } catch (Exception e) {
                    log.info("invalidAuthDetail bulkInvalid fail authDetails[{}], admin[{}]", authDetails, admin, e);
                    throw e;
                }
            }

            successTenantIds.add(targetTenantId);
            log.info("invalidAuthDetail end   tenantId[{}]", targetTenantId);
        }

        return new CurlModel.InvalidAuthDetailResult(notOpenTenantIds, successTenantIds);
    }

    @Override
    public CurlModel.AddFAccountEntryRuleObjResult addFAccountEntryRuleObj(ServiceContext serviceContext, CurlModel.AddFAccountEntryRuleObjArg arg) {
        List<String> notOpenTenantIds = Lists.newArrayList();
        List<String> successTenantIds = Lists.newArrayList();

        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return new CurlModel.AddFAccountEntryRuleObjResult(notOpenTenantIds, successTenantIds);
        }

        for (String tenantId : arg.getTenantIds()) {
            log.info("addFAccountEntryRuleObj begin tenantId[{}]", tenantId);

            boolean isAccountAuthOpen = fundAccountConfigManager.isAccountAuthOpen(tenantId);
            if (!isAccountAuthOpen) {
                notOpenTenantIds.add(tenantId);
                continue;
            }

            User admin = new User(tenantId, "-10000");
            accountAuthInitManager.initFAccountEntryRuleObj(tenantId, admin);
            successTenantIds.add(tenantId);
            log.info("addFAccountEntryRuleObj end   tenantId[{}]", tenantId);
        }

        return new CurlModel.AddFAccountEntryRuleObjResult(notOpenTenantIds, successTenantIds);
    }

    @Override
    public CurlModel.AddUnfreezeAuthDetailObjResult addUnfreezeAuthDetailObj(ServiceContext serviceContext, CurlModel.AddUnfreezeAuthDetailObjArg arg) {
        List<String> notOpenTenantIds = Lists.newArrayList();
        List<String> successTenantIds = Lists.newArrayList();

        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return new CurlModel.AddUnfreezeAuthDetailObjResult(notOpenTenantIds, successTenantIds);
        }

        for (String tenantId : arg.getTenantIds()) {
            log.info("addUnfreezeAuthDetailObj begin tenantId[{}]", tenantId);

            boolean isAccountAuthOpen = fundAccountConfigManager.isAccountAuthOpen(tenantId);
            if (!isAccountAuthOpen) {
                notOpenTenantIds.add(tenantId);
                continue;
            }

            User admin = new User(tenantId, "-10000");
            accountAuthInitManager.initUnfreezeAuthDetailObj(tenantId, admin);
            successTenantIds.add(tenantId);
            log.info("addUnfreezeAuthDetailObj end   tenantId[{}]", tenantId);
        }

        return new CurlModel.AddUnfreezeAuthDetailObjResult(notOpenTenantIds, successTenantIds);
    }

    @Override
    public CurlModel.TransferAutoEntryStatusFalseResult transferAutoEntryStatusFalse(ServiceContext serviceContext, CurlModel.TransferAutoEntryStatusFalseArg arg) {
        List<String> notOpenTenantIds = Lists.newArrayList();
        List<String> successTenantIds = Lists.newArrayList();

        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return new CurlModel.TransferAutoEntryStatusFalseResult(notOpenTenantIds, successTenantIds);
        }

        for (String tenantId : arg.getTenantIds()) {
            log.info("transferAutoEntryStatusFalse begin tenantId[{}]", tenantId);

            boolean isAccountAuthOpen = fundAccountConfigManager.isAccountAuthOpen(tenantId);
            if (!isAccountAuthOpen) {
                notOpenTenantIds.add(tenantId);
                continue;
            }

            fAccountAuthorizationManager.transferAutoEntryStatusFalse(tenantId);
            successTenantIds.add(tenantId);
            log.info("transferAutoEntryStatusFalse end   tenantId[{}]", tenantId);
        }

        return new CurlModel.TransferAutoEntryStatusFalseResult(notOpenTenantIds, successTenantIds);
    }

    @Override
    public CurlModel.TestAutoEnterAccountResult testAutoEnterAccount(ServiceContext serviceContext, CurlModel.TestAutoEnterAccountArg arg) {
        fAccountAuthorizationManager.autoEntryAccount(arg.getTenantId(), arg.getApiName(), arg.getDataId());
        return new CurlModel.TestAutoEnterAccountResult();
    }

    @Override
    public CurlModel.TriggerAutoEnterAccountResult triggerAutoEnterAccount(ServiceContext serviceContext, CurlModel.TriggerAutoEnterAccountArg arg) {
        List<String> successDataIds = Lists.newArrayList();
        List<String> failDataIds = Lists.newArrayList();
        Map<String, String> failDataId2ErrorMsg = new HashMap<>();

        if (CollectionUtils.isEmpty(arg.getDataIds())) {
            return new CurlModel.TriggerAutoEnterAccountResult(successDataIds, failDataIds, failDataId2ErrorMsg);
        }

        for (String dataId : arg.getDataIds()) {
            log.info("triggerAutoEnterAccount begin dataId[{}]", dataId);
            try {
                fAccountAuthorizationManager.autoEntryAccount(arg.getTenantId(), arg.getApiName(), dataId);
                successDataIds.add(dataId);
            } catch (Exception e) {
                log.warn("triggerAutoEnterAccount fail tenantId[{}], dataId[{}], errorMsg[{}]", arg.getTenantId(), dataId, e.getMessage(), e);
                failDataIds.add(dataId);
                failDataId2ErrorMsg.put(dataId, e.getMessage());
            }
            log.info("triggerAutoEnterAccount end dataId[{}]", dataId);
        }

        CurlModel.TriggerAutoEnterAccountResult result =  new CurlModel.TriggerAutoEnterAccountResult(successDataIds, failDataIds, failDataId2ErrorMsg);
        log.info("triggerAutoEnterAccount finish arg[{}] result[{}]", arg, result);
        return result;
    }

    @Override
    public CurlModel.TenantIds fixPaymentWithDetailEnterAccountButton(ServiceContext serviceContext, CurlModel.TenantIds arg) {
        PaymentWithDetailEnterAccountConfigProvider paymentWithDetailEnterAccountConfigProvider = SpringUtil.getContext().getBean(PaymentWithDetailEnterAccountConfigProvider.class);
        List<String> tenantIds = arg.getTenantIds();
        List<String> failEis = Lists.newArrayList();
        for (String tenantId : tenantIds) {
            User user = User.systemUser(tenantId);
            try {
                String configValue = serviceFacade.findTenantConfig(user, ConfigKeyEnum.PAYMENT_WITH_DETAIL_ENTER_ACCOUNT.key);
                if (!ConfigKeyEnum.PAYMENT_WITH_DETAIL_ENTER_ACCOUNT.enabled(configValue)) {
                    continue;
                }
                paymentWithDetailEnterAccountConfigProvider.removeButtonFilter(user);
            } catch (Exception e) {
                failEis.add(tenantId);
                log.warn("paymentWithDetailEnterAccount removeButtonFilter error,tenantId：{}", tenantId, e);
            }
        }
        CurlModel.TenantIds result = new CurlModel.TenantIds();
        result.setTenantIds(failEis);
        return result;
    }

    @Override
    public CurlModel.TenantIds addCheckRuleReconciliationJsonField(ServiceContext serviceContext, CurlModel.TenantAddField arg) {
        Set<String> tenantIds = arg.getTenantIds();
        List<String> failEis = Lists.newArrayList();
        String objectApiName = arg.getObjectApiName();
        IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance(arg.getFieldDescribe());
        String persistentDataCalc = "{\"evaluate_range\":\"none\"}";
        for (String tenantId : tenantIds) {
            User user = User.systemUser(tenantId);
            try {
                serviceFacade.addDescribeCustomField(user, objectApiName, fieldDescribe.toJsonString(), persistentDataCalc, Lists.newArrayList(), Lists.newArrayList());
            } catch (Exception e) {
                log.warn("addField error,tenantId：{},field:{}", tenantId, fieldDescribe, e);
                failEis.add(tenantId);
            }
        }
        CurlModel.TenantIds result = new CurlModel.TenantIds();
        result.setTenantIds(failEis);
        return result;
    }

//    /**
//     * 920 广告新增弹窗展示字段，刷库布局信息
//     * @param serviceContext
//     * @param tenantIds
//     * @return
//     */
//    @Override
//    public CurlModel.TenantIds addAdvertisementFields(ServiceContext serviceContext, CurlModel.TenantIds tenantIds) {
//        if (CollectionUtils.isEmpty(tenantIds.getTenantIds())) {
//            throw new ValidateException("tenantId is null");
//        }
//        List<String> failTenantId = new ArrayList<>();
//        tenantIds.getTenantIds().forEach(tenantId -> {
//            User user = User.builder().tenantId(tenantId).userId("-10000").build();
//            LayoutLogicService.LayoutContext layoutContext = LayoutLogicService.LayoutContext.of(user, serviceContext.getAppId());
//            try {
//                List<ILayout> layoutDetailList = serviceFacade.findLayoutByObjectApiNameAndLayoutType(layoutContext, NewAdvertisementConst.OBJECT_API_NAME, SystemConstants.LayoutType.Detail.layoutType);
//                if (CollectionUtils.isEmpty(layoutDetailList)) {
//                    log.warn("layout detail list is empty. tenantId：{}", tenantId);
//                    return;
//                }
//                layoutDetailList.forEach(layout -> {
//                    LayoutExt layoutExt = LayoutExt.of(layout);
//                    Optional<IFormField> formField = layoutExt.getField(NewAdvertisementConst.Field.EnablePopup.apiName);
//                    FieldLayoutPojo fieldLayoutPojo = new FieldLayoutPojo();
//                    fieldLayoutPojo.setRenderType(SystemConstants.RenderType.TrueOrFalse.renderType);
//                    fieldLayoutPojo.setReadonly(false);
//                    fieldLayoutPojo.setRequired(true);
//                    fieldLayoutPojo.setApiName(NewAdvertisementConst.Field.EnablePopup.apiName);
//                    BooleanFieldDescribe enablePopupFieldDescribe = BooleanFieldDescribeBuilder.builder().apiName(NewAdvertisementConst.Field.EnablePopup.apiName).label(NewAdvertisementConst.Field.EnablePopup.apiName).required(true).build();
//                    layoutExt.addField(enablePopupFieldDescribe, fieldLayoutPojo);
//                    if (!formField.isPresent()) {
//                        serviceFacade.updateLayout(user, layout);
//                    }
//                });
//            } catch (Exception e) {
//                log.warn("addAdvertisementFields occur exception. tenantId:{}", tenantId, e);
//                failTenantId.add(tenantId);
//            }
//        });
//        CurlModel.TenantIds result = new CurlModel.TenantIds();
//        result.setTenantIds(failTenantId);
//        log.info("addAdvertisementFields failed tenantIds:{}", failTenantId);
//        return result;
//    }

    /**
     * 930 广告：
     * 1. 新增页面地址
     * 2. 适用终端改为平铺
     */
//    @Override
//    public CurlModel.TenantIds addAdvertisementLayoutFields(ServiceContext serviceContext, CurlModel.TenantIds tenantIds) {
//        if (CollectionUtils.isEmpty(tenantIds.getTenantIds())) {
//            throw new ValidateException("tenantId is null");
//        }
//        List<String> failTenantId = new ArrayList<>();
//        tenantIds.getTenantIds().forEach(tenantId -> {
//            User user = User.builder().tenantId(tenantId).userId("-10000").build();
//            LayoutLogicService.LayoutContext layoutContext = LayoutLogicService.LayoutContext.of(user, serviceContext.getAppId());
//            try {
//                List<ILayout> layoutDetailList = serviceFacade.findLayoutByObjectApiNameAndLayoutType(layoutContext, NewAdvertisementConst.OBJECT_API_NAME, SystemConstants.LayoutType.Detail.layoutType);
//                if (CollectionUtils.isEmpty(layoutDetailList)) {
//                    log.warn("layout detail list is empty. tenantId：{}", tenantId);
//                    return;
//                }
//                layoutDetailList.forEach(layout -> {
//                    LayoutExt layoutExt = LayoutExt.of(layout);
//                    // 适用终端改为平铺
//                    Optional<IFormField> applicableTerminalFieldOpt = layoutExt.getField(NewAdvertisementConst.Field.ApplicableTerminal.apiName);
//                    applicableTerminalFieldOpt.ifPresent(iFormField -> FormFieldExt.of(iFormField).setTiled(true));
//                    // 新增页面地址
//                    Optional<IFormField> formField = layoutExt.getField(NewAdvertisementConst.Field.CustomPageId.apiName);
//                    FieldLayoutPojo fieldLayoutPojo = new FieldLayoutPojo();
//                    fieldLayoutPojo.setRenderType(SystemConstants.RenderType.Text.renderType);
//                    fieldLayoutPojo.setReadonly(false);
//                    fieldLayoutPojo.setRequired(false);
//                    fieldLayoutPojo.setApiName(NewAdvertisementConst.Field.CustomPageId.apiName);
//                    TextFieldDescribe customPageIdFieldDescribe = TextFieldDescribeBuilder.builder().apiName(NewAdvertisementConst.Field.CustomPageId.apiName).build();
//                    layoutExt.addField(customPageIdFieldDescribe, fieldLayoutPojo);
//
//                    // 修改布局中字段顺序
//                    Optional<IFormField> customPageIdFieldOpt = layoutExt.getField(NewAdvertisementConst.Field.CustomPageId.apiName);
//                    layoutExt.getFormComponent().ifPresent(x -> {
//                        Optional<IFieldSection> baseFieldSectionOpt = x.getBaseFieldSection();
//                        IFieldSection baseFieldSection;
//                        if (baseFieldSectionOpt.isPresent()){
//                            baseFieldSection = baseFieldSectionOpt.get();
//                        } else {
//                            return;
//                        }
//                        List<IFormField> fields = baseFieldSection.getFields();
//                        // 在布局里去掉这2个字段
//                        fields.removeIf(field -> StringUtils.equalsAny(field.getFieldName(), NewAdvertisementConst.Field.CustomPageId.apiName, NewAdvertisementConst.Field.ApplicableTerminal.apiName));
//                        List<IFormField> newFields = new ArrayList<>();
//                        for (IFormField field : fields){
//                            newFields.add(field);
//                            String fieldName = field.getFieldName();
//                            if (StringUtils.equals(fieldName, NewAdvertisementConst.Field.JumpType.apiName) && applicableTerminalFieldOpt.isPresent()){
//                                newFields.add(applicableTerminalFieldOpt.get());
//                            } else if (StringUtils.equals(fieldName, NewAdvertisementConst.Field.ExternalAddress.apiName) && customPageIdFieldOpt.isPresent()){
//                                newFields.add(customPageIdFieldOpt.get());
//                            }
//                        }
//                        baseFieldSection.setFields(newFields);
//                    });
//                    if (!formField.isPresent()) {
//                        serviceFacade.updateLayout(user, layout);
//                    }
//                });
//            } catch (Exception e) {
//                log.warn("addAdvertisementLayoutFields occur exception. tenantId:{}", tenantId, e);
//                failTenantId.add(tenantId);
//            }
//        });
//        CurlModel.TenantIds result = new CurlModel.TenantIds();
//        result.setTenantIds(failTenantId);
//        log.info("addAdvertisementLayoutFields end. failed tenantIds:{}", failTenantId);
//        return result;
//    }

    @Override
    public CurlModel.QueryAccountCheckRuleHasAccountResult queryAccountCheckRuleHasAccount(ServiceContext serviceContext, CurlModel.QueryAccountCheckRuleHasAccountArg arg) {
        List<String> notOpenTenantIds = Lists.newArrayList();
        List<String> notFundAccountTenantIds = Lists.newArrayList();
        List<String> notAccountCheckRuleTenantIds = Lists.newArrayList();
        List<String> notAccountCheckRuleName2containFundAccountNamesTenantIds = Lists.newArrayList();
        List<String> successTenantIds = Lists.newArrayList();
        List<String> failTenantIds = Lists.newArrayList();
        Map<String, Map<String, List<String>>> ea2AccountAccountCheckRuleName2FundAccountNames = new HashMap<>();

        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return new CurlModel.QueryAccountCheckRuleHasAccountResult(notOpenTenantIds, notFundAccountTenantIds, notAccountCheckRuleTenantIds, successTenantIds, failTenantIds, notAccountCheckRuleName2containFundAccountNamesTenantIds,
                    Sets.newHashSet(), ea2AccountAccountCheckRuleName2FundAccountNames);
        }

        for (String tenantId : arg.getTenantIds()) {
            log.info("queryAccountCheckRuleHasAccount begin tenantId[{}]", tenantId);

            try {
                boolean isAccountCheckRuleOpen = fundAccountConfigManager.isAccountCheckRuleOpen(tenantId);
                if (!isAccountCheckRuleOpen) {
                    notOpenTenantIds.add(tenantId);
                    continue;
                }

                //货补的账户信息
                //   List<String> accountTypes = Lists.newArrayList(FundAccountAccountTypeEnum.ReplenishmentAmount.value, FundAccountAccountTypeEnum.ReplenishmentQuantity.value);
                List<String> accountTypes = arg.getAccountTypes();
                List<String> accessModules = arg.getAccessModules();
                List<IObjectData> fundAccounts = fundAccountManager.getAccountsByAccountTypesOrAccessModules(tenantId, accountTypes, accessModules);
                if (CollectionUtils.isEmpty(fundAccounts)) {
                    notFundAccountTenantIds.add(tenantId);
                    continue;
                }

                //查校验规则
                List<String> ruleTypes = arg.getRuleTypes();
                List<String> fields = Lists.newArrayList(SystemConstants.Field.Id.apiName, AccountCheckRuleConstants.Field.Name.apiName,
                        AccountCheckRuleConstants.Field.RuleType.apiName, AccountCheckRuleConstants.Field.OccupiedMapping.apiName, AccountCheckRuleConstants.Field.ReduceMapping.apiName);
                List<IObjectData> accountCheckRuleDataList = accountCheckRuleManager.getAllAccountCheckRuleDatas(tenantId, ruleTypes, false, fields);
                if (CollectionUtils.isEmpty(accountCheckRuleDataList)) {
                    notAccountCheckRuleTenantIds.add(tenantId);
                    continue;
                }

                //获取有的账户名称
                String ea = eIEAConverter.enterpriseIdToAccount(Integer.parseInt(tenantId));
                Map<String, List<String>> accountCheckRuleName2containFundAccountNames = accountCheckRuleManager.accountCheckRuleName2containFundAccountNames(accountCheckRuleDataList, fundAccounts);
                if (accountCheckRuleName2containFundAccountNames.size() == 0) {
                    notAccountCheckRuleName2containFundAccountNamesTenantIds.add(tenantId);
                } else {
                    ea2AccountAccountCheckRuleName2FundAccountNames.put(ea, accountCheckRuleName2containFundAccountNames);
                }

                successTenantIds.add(tenantId);
                log.info("queryAccountCheckRuleHasAccount success  tenantId[{}]", tenantId);
            } catch (Exception e) {
                log.info("queryAccountCheckRuleHasAccount fail  tenantId[{}]", tenantId, e);
                failTenantIds.add(tenantId);
            }
            log.info("queryAccountCheckRuleHasAccount end  tenantId[{}]", tenantId);
        }
        log.info("queryAccountCheckRuleHasAccount finish notOpenTenantIds[{}], notFundAccountTenantIds[{}], notAccountCheckRuleTenantIds[{}], successTenantIds[{}], failTenantIds[{}], " +
                        "ea2AccountAccountCheckRuleName2FundAccountNameKeys[{}], ea2AccountAccountCheckRuleName2FundAccountNames[{}]",
                notOpenTenantIds, notFundAccountTenantIds, notAccountCheckRuleTenantIds, successTenantIds, failTenantIds, ea2AccountAccountCheckRuleName2FundAccountNames.keySet(), ea2AccountAccountCheckRuleName2FundAccountNames);

        return new CurlModel.QueryAccountCheckRuleHasAccountResult(notOpenTenantIds, notFundAccountTenantIds, notAccountCheckRuleTenantIds, notAccountCheckRuleName2containFundAccountNamesTenantIds, successTenantIds, failTenantIds,
                ea2AccountAccountCheckRuleName2FundAccountNames.keySet(), ea2AccountAccountCheckRuleName2FundAccountNames);
    }

    @Override
    public CurlModel.QueryAccountAuthHasAccountResult queryAccountAuthHasAccount(ServiceContext serviceContext, CurlModel.QueryAccountAuthHasAccountArg arg) {
        List<String> notOpenTenantIds = Lists.newArrayList();
        List<String> notFundAccountTenantIds = Lists.newArrayList();
        List<String> notAccountAuthTenantIds = Lists.newArrayList();
        List<String> notAccountAuthName2containFundAccountNamesTenantIds = Lists.newArrayList();
        List<String> successTenantIds = Lists.newArrayList();
        List<String> failTenantIds = Lists.newArrayList();
        Map<String, Map<String, List<String>>> ea2AccountAccountAuthName2FundAccountNames = new HashMap<>();

        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return new CurlModel.QueryAccountAuthHasAccountResult(notOpenTenantIds, notFundAccountTenantIds, notAccountAuthTenantIds, successTenantIds, failTenantIds, notAccountAuthName2containFundAccountNamesTenantIds,
                    Sets.newHashSet(), ea2AccountAccountAuthName2FundAccountNames);
        }

        for (String tenantId : arg.getTenantIds()) {
            log.info("queryAccountAuthHasAccount begin tenantId[{}]", tenantId);

            try {
                boolean isAccountAuthOpen = fundAccountConfigManager.isAccountAuthOpen(tenantId);
                if (!isAccountAuthOpen) {
                    notOpenTenantIds.add(tenantId);
                    continue;
                }

                //账户信息
                List<IObjectData> fundAccounts = fundAccountManager.getAccountsByAccountTypesOrAccessModules(tenantId, arg.getAccountTypes(), arg.getAccessModules());
                if (CollectionUtils.isEmpty(fundAccounts)) {
                    notFundAccountTenantIds.add(tenantId);
                    continue;
                }

                //查【账户授权】
                User admin = User.systemUser(tenantId);
                List<IObjectData> accountAuthList = fAccountAuthorizationManager.getFAccountAuthorizationDatas(admin, arg.getAuthorizedTypes());
                if (CollectionUtils.isEmpty(accountAuthList)) {
                    notAccountAuthTenantIds.add(tenantId);
                    continue;
                }

                //获取有的账户名称
                String ea = eIEAConverter.enterpriseIdToAccount(Integer.parseInt(tenantId));
                Map<String, List<String>> accountAuthName2containFundAccountNames = fAccountAuthorizationManager.accountAuthName2containFundAccountNames(tenantId, accountAuthList, fundAccounts);

                if (accountAuthName2containFundAccountNames.size() == 0) {
                    notAccountAuthName2containFundAccountNamesTenantIds.add(tenantId);
                } else {
                    ea2AccountAccountAuthName2FundAccountNames.put(ea, accountAuthName2containFundAccountNames);
                }

                successTenantIds.add(tenantId);
                log.info("queryAccountAuthHasAccount success  tenantId[{}]", tenantId);
            } catch (Exception e) {
                log.info("queryAccountAuthHasAccount fail  tenantId[{}]", tenantId);
                failTenantIds.add(tenantId);
            }
            log.info("queryAccountAuthHasAccount end  tenantId[{}]", tenantId);
        }
        log.info("queryAccountAuthHasAccount finish notOpenTenantIds[{}], notFundAccountTenantIds[{}], notAccountAuthTenantIds[{}], successTenantIds[{}], failTenantIds[{}], " +
                        "ea2AccountAccountAuthName2FundAccountNameKeys[{}], ea2AccountAccountAuthName2FundAccountNames[{}]",
                notOpenTenantIds, notFundAccountTenantIds, notAccountAuthTenantIds, successTenantIds, failTenantIds, ea2AccountAccountAuthName2FundAccountNames.keySet(), ea2AccountAccountAuthName2FundAccountNames);

        return new CurlModel.QueryAccountAuthHasAccountResult(notOpenTenantIds, notFundAccountTenantIds, notAccountAuthTenantIds, notAccountAuthName2containFundAccountNamesTenantIds, successTenantIds, failTenantIds,
                ea2AccountAccountAuthName2FundAccountNames.keySet(), ea2AccountAccountAuthName2FundAccountNames);
    }

    @Override
    public Map<String, Object> initPaymentDomainPlugin(ServiceContext serviceContext, CurlModel.TenantIds arg) {
        Map<String, Object> result = Maps.newHashMap();
        List<String> tenantIds = arg.getTenantIds();
        if (CollectionUtils.isEmpty(tenantIds)) {
            return result;
        }
        Set<String> failEis = Sets.newHashSet();
        tenantIds.forEach(tenantId -> {
            try {
                User user = User.systemUser(tenantId);
                RequestContext requestContext = ServiceContextUtil.getRequestContext(user);
                Map<String, String> configMap = serviceFacade.queryTenantConfigs(user, Lists.newArrayList(ConfigKeyEnum.PAYMENT_ENTER_ACCOUNT_CONFIG_KEY.key, ConfigKeyEnum.PAYMENT_PAY.key, ConfigKeyEnum.SALES_ORDER_PAY_DIRECTLY.key));
                boolean paymentEnterAccountEnable = ConfigKeyEnum.PAYMENT_ENTER_ACCOUNT_CONFIG_KEY.enabled(configMap.get(ConfigKeyEnum.PAYMENT_ENTER_ACCOUNT_CONFIG_KEY.key));
                boolean paymentPayEnable = ConfigKeyEnum.PAYMENT_PAY.enabled(configMap.get(ConfigKeyEnum.PAYMENT_PAY.key));
                boolean salesOrderPayEnable = ConfigKeyEnum.SALES_ORDER_PAY_DIRECTLY.enabled(configMap.get(ConfigKeyEnum.SALES_ORDER_PAY_DIRECTLY.key));
                if (paymentEnterAccountEnable) {
                    caObjectDomainPluginManager.createPluginInstanceIfNotExist(requestContext, Constants.ENTER_ACCOUNT_PLUGIN_API_NAME, PaymentConstants.API_NAME);
                    caObjectDomainPluginManager.createPluginInstanceIfNotExist(requestContext, Constants.ENTER_ACCOUNT_PLUGIN_API_NAME, OrderPaymentConstants.API_NAME);
                }
                if (paymentPayEnable || salesOrderPayEnable) {
                    caObjectDomainPluginManager.createPluginInstanceIfNotExist(requestContext, Constants.PAYMENT_PAY_PLUGIN_API_NAME, PaymentConstants.API_NAME);
                }
            } catch (Exception e) {
                failEis.add(tenantId);
                log.warn("init payment plugin error,tenantId:{}", tenantId, e);
            }
        });
        log.info("init payment plugin fail eis:{}", failEis);
        result.put("failEis", failEis);
        return result;
    }

    @Override
    public CurlModel.SetLayoutCustomerAccountComponentAccountIdsResult setLayoutCustomerAccountComponentAccountIds(ServiceContext serviceContext, CurlModel.SetLayoutCustomerAccountComponentAccountIdsArg arg) {
        List<String> notOpenTenantIds = Lists.newArrayList();
        List<String> notLayoutTenantIds = Lists.newArrayList();
        List<String> notNeedUpdateTenantIds = Lists.newArrayList();
        //没有【客户账户】组件
        List<String> notHasDhtOrderCustomerAccountComponentTenantIds = Lists.newArrayList();
        //【客户账户】组件里面，accountIds是空的
        List<String> accountIdsEmptyTenantIds = Lists.newArrayList();
        //【客户账户】组件里面，accountIds都存在
        List<String> accountIdsAllExistTenantIds = Lists.newArrayList();
        List<String> successTenantIds = Lists.newArrayList();
        List<String> failTenantIds = Lists.newArrayList();

        //所有账户都不存在
        Map<String, List<String>> tenantId2NoExistFundAccountIds = new HashMap<>();

        //部分账户不存在、部分存在
        Map<String, List<String>> tenantId2PartNoExistFundAccountIds = new HashMap<>();
        Map<String, List<String>> tenantId2PartExistFundAccountIds = new HashMap<>();

        if (CollectionUtils.isEmpty(arg.getTenantIds()) || Strings.isNullOrEmpty(arg.getLayoutApiName())) {
            return new CurlModel.SetLayoutCustomerAccountComponentAccountIdsResult(notOpenTenantIds, notLayoutTenantIds, notNeedUpdateTenantIds, notHasDhtOrderCustomerAccountComponentTenantIds, accountIdsEmptyTenantIds, accountIdsAllExistTenantIds, successTenantIds, failTenantIds
                    , tenantId2NoExistFundAccountIds, tenantId2PartNoExistFundAccountIds, tenantId2PartExistFundAccountIds);
        }

        for (String tenantId : arg.getTenantIds()) {
            log.info("setLayoutCustomerAccountComponentAccountIds begin tenantId[{}]", tenantId);

            try {
                //查layout
                String layoutApiName = arg.getLayoutApiName();
                ILayout layout = layoutService.findByNameAndObjectDescribeApiNameAndTenantId(layoutApiName, arg.getObjectApiName(), tenantId, null);
                if (layout == null) {
                    notLayoutTenantIds.add(tenantId);
                    continue;
                }

                //参考com.facishare.paas.appframework.metadata.ListLayoutExt.of
                Layout mobileLayout = new Layout(layout.getMobileLayout());
                List<IComponent> components = mobileLayout.getComponents();
                if (CollectionUtils.isEmpty(components)) {
                    //肯定没有【客户账户】组件dht_order_customer_account，不用处理
                    notHasDhtOrderCustomerAccountComponentTenantIds.add(tenantId);
                    continue;
                }

                IComponent dhtOrderCustomerAccountComponent = null;
                for (IComponent component : components) {
                    String apiName = (String) component.get("api_name");
                    if (Objects.equals("dht_order_customer_account", apiName)) {
                        dhtOrderCustomerAccountComponent = component;
                        break;
                    }
                }
                if (dhtOrderCustomerAccountComponent == null) {
                    notHasDhtOrderCustomerAccountComponentTenantIds.add(tenantId);
                    continue;
                }

                //看【客户账户】组件dht_order_customer_account里面的accountIds，账户ID是否存在
                /**
                 *                     "accountIds": [
                 *                         "621cb7151bb4c400013a9537",
                 *                         "62fa181e2ea521000142e2d1",
                 *                         "663af15b28d7f500011f9455"
                 *                     ],
                 */
                List<String> accountIds = Lists.newArrayList();
                if (((CommonComponent) dhtOrderCustomerAccountComponent).containsKey("accountIds")) {
                    accountIds = (List<String>) dhtOrderCustomerAccountComponent.get("accountIds");
                }
                if (CollectionUtils.isEmpty(accountIds)) {
                    accountIdsEmptyTenantIds.add(tenantId);
                }

                //查本企业所有的账户
                List<IObjectData> fundAccounts = fundAccountManager.getAccounts(tenantId, false);
                List<String> noExistFundAccountIds = fundAccountManager.getNoExistFundAccountIds(accountIds, fundAccounts);
                List<String> existFundAccountIds = accountIds.stream().filter(id -> !noExistFundAccountIds.contains(id)).collect(Collectors.toList());

                boolean accountIdsIsEmpty = false;
                boolean allNotExist = true;
                if (CollectionUtils.isEmpty(accountIds)) {
                    //accountIds为空
                    accountIdsIsEmpty = true;
                } else if (CollectionUtils.isEmpty(noExistFundAccountIds)) {
                    //所有账户都存在，不用处理
                    accountIdsAllExistTenantIds.add(tenantId);
                    continue;
                } else {
                    //都不存在
                    if (Objects.equals(noExistFundAccountIds.size(), accountIds.size())) {
                        tenantId2NoExistFundAccountIds.put(tenantId, noExistFundAccountIds);
                    }
                    //部分存在，部分不存在
                    else {
                        allNotExist = false;
                        tenantId2PartNoExistFundAccountIds.put(tenantId, noExistFundAccountIds);
                        tenantId2PartExistFundAccountIds.put(tenantId, existFundAccountIds);
                    }
                }


                if (!arg.isDoUpdate()) {
                    continue;
                }

                Set<String> newFundAccountIds = new HashSet<>();
                //accountIds为空
                if (accountIdsIsEmpty) {
                    if (!arg.isDoUpdateIfAccountIdIsEmpty()) {
                        continue;
                    }
                    newFundAccountIds = fundAccounts.stream().filter(d -> arg.getAccountNames().contains(d.getName())).map(IObjectData::getId).collect(Collectors.toSet());
                } else {
                    //都不存在 ：只保留跟模板企业同名的
                    if (allNotExist) {
                        newFundAccountIds = fundAccounts.stream().filter(d -> arg.getAccountNames().contains(d.getName())).map(IObjectData::getId).collect(Collectors.toSet());
                    }

                    //部分存在、部分不存在 : 只留存在的
                    else {
                        newFundAccountIds = accountIds.stream().filter(id -> !noExistFundAccountIds.contains(id)).collect(Collectors.toSet());
                    }
                }

                log.info("setLayoutCustomerAccountComponentAccountIds set accountIds newFundAccountIds[{}]", newFundAccountIds);
                dhtOrderCustomerAccountComponent.set("accountIds", newFundAccountIds);
                //    layout.setMobileLayout(LayoutExt.of(mobileLayout).toMap()); 不用set
                layoutService.replace(layout);
                successTenantIds.add(tenantId);
                log.info("setLayoutCustomerAccountComponentAccountIds success  tenantId[{}]", tenantId);
            } catch (Exception e) {
                log.info("setLayoutCustomerAccountComponentAccountIds fail  tenantId[{}]", tenantId, e);
                failTenantIds.add(tenantId);
            }
            log.info("setLayoutCustomerAccountComponentAccountIds end  tenantId[{}]", tenantId);
        }
        log.info("setLayoutCustomerAccountComponentAccountIds finish notOpenTenantIds[{}], notLayoutTenantIds[{}], notNeedUpdateTenantIds[{}], successTenantIds[{}], failTenantIds[{}]",
                notOpenTenantIds, notLayoutTenantIds, notNeedUpdateTenantIds, successTenantIds, failTenantIds);

        return new CurlModel.SetLayoutCustomerAccountComponentAccountIdsResult(notOpenTenantIds, notLayoutTenantIds, notNeedUpdateTenantIds, notHasDhtOrderCustomerAccountComponentTenantIds, accountIdsEmptyTenantIds, accountIdsAllExistTenantIds, successTenantIds, failTenantIds
                , tenantId2NoExistFundAccountIds, tenantId2PartNoExistFundAccountIds, tenantId2PartExistFundAccountIds);
    }

    @Override
    public CurlModel.SetFundAccountLayoutFieldIsRequiredResult setFundAccountLayoutFieldIsRequired(ServiceContext serviceContext, CurlModel.SetFundAccountLayoutFieldIsRequiredArg arg) {
        List<String> notOpenTenantIds = Lists.newArrayList();
        List<String> notLayoutTenantIds = Lists.newArrayList();
        List<String> notNeedUpdateTenantIds = Lists.newArrayList();
        List<String> successTenantIds = Lists.newArrayList();
        List<String> failTenantIds = Lists.newArrayList();

        if (CollectionUtils.isEmpty(arg.getTenantIds()) || CollectionUtils.isEmpty(arg.getFieldApiNames())) {
            return new CurlModel.SetFundAccountLayoutFieldIsRequiredResult(notOpenTenantIds, notLayoutTenantIds, notNeedUpdateTenantIds, successTenantIds, failTenantIds);
        }

        for (String tenantId : arg.getTenantIds()) {
            log.info("setFundAccountLayoutFieldIsRequired begin tenantId[{}]", tenantId);

            try {
                boolean isNewCustomerAccountEnable = fundAccountConfigManager.isNewCustomerAccountEnable(tenantId);
                if (!isNewCustomerAccountEnable) {
                    notOpenTenantIds.add(tenantId);
                    continue;
                }

                //查layout
                String layoutApiName = "FundAccountObj_default_layout__c";
                ILayout layout = layoutService.findByNameAndObjectDescribeApiNameAndTenantId(layoutApiName, FundAccountConstants.API_NAME, tenantId, null);
                if (layout == null) {
                    notLayoutTenantIds.add(tenantId);
                    continue;
                }

                List<IComponent> components = layout.getComponents();
                if (CollectionUtils.isEmpty(components)) {
                    log.info("components isEmpty tenantId[{}]", tenantId);
                    continue;
                }
                boolean needUpdate = false;
                for (IComponent component : components) {
                    if (component.getType().equals("form")) {
                        FormComponent formComponent = (FormComponent) component;
                        for (IFieldSection iFieldSection : formComponent.getFieldSections()) {
                            List<IFormField> fieldList = iFieldSection.getFields();
                            for (IFormField field : fieldList) {
                                if (arg.getFieldApiNames().contains(field.getFieldName())) {
                                    Boolean isRequired = field.isRequired();
                                    if (!isRequired) {
                                        field.setRequired(true);
                                        needUpdate = true;
                                    }
                                }
                            }
                        }
                    }
                }

                if (!needUpdate) {
                    notNeedUpdateTenantIds.add(tenantId);
                    continue;
                }

                layoutService.replace(layout);
                successTenantIds.add(tenantId);
                log.info("setFundAccountLayoutFieldIsRequired success  tenantId[{}]", tenantId);
            } catch (Exception e) {
                log.info("setFundAccountLayoutFieldIsRequired fail  tenantId[{}]", tenantId, e);
                failTenantIds.add(tenantId);
            }
            log.info("setFundAccountLayoutFieldIsRequired end  tenantId[{}]", tenantId);
        }
        log.info("setFundAccountLayoutFieldIsRequired finish notOpenTenantIds[{}], notLayoutTenantIds[{}], notNeedUpdateTenantIds[{}], successTenantIds[{}], failTenantIds[{}]",
                notOpenTenantIds, notLayoutTenantIds, notNeedUpdateTenantIds, successTenantIds, failTenantIds);

        return new CurlModel.SetFundAccountLayoutFieldIsRequiredResult(notOpenTenantIds, notLayoutTenantIds, notNeedUpdateTenantIds, successTenantIds, failTenantIds);
    }

    @Override
    public CurlModel.LayoutAddFieldsResult layoutAddFields(ServiceContext serviceContext, CurlModel.LayoutAddFieldsArg arg) {
        List<String> notOpenTenantIds = Lists.newArrayList();
        List<String> notLayoutTenantIds = Lists.newArrayList();
        List<String> componentsEmptyTenantIds = Lists.newArrayList();
        List<String> notNeedUpdateTenantIds = Lists.newArrayList();
        List<String> successTenantIds = Lists.newArrayList();
        List<String> failTenantIds = Lists.newArrayList();

        CurlModel.LayoutAddFieldsResult result = new CurlModel.LayoutAddFieldsResult(notOpenTenantIds, notLayoutTenantIds, componentsEmptyTenantIds, notNeedUpdateTenantIds, successTenantIds, failTenantIds);

        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return result;
        }

        List<String> addFieldApiNames = arg.getFields().stream().map(CurlModel.Field::getFieldName).collect(Collectors.toList());
        Map<String, CurlModel.Field> fieldApiName2FieldMap = arg.getFields().stream().collect(Collectors.toMap(CurlModel.Field::getFieldName, p -> p));

        for (String tenantId : arg.getTenantIds()) {
            log.info("layoutAddFields begin tenantId[{}]", tenantId);
            try {
                boolean hasOpen = hasOpen(tenantId, arg.getBizConfigKey(), arg.getNeedAddValues());
                if (!hasOpen) {
                    notOpenTenantIds.add(tenantId);
                    continue;
                }

                String layoutApiName = arg.getLayoutApiName();
                //查layout
                ILayout layout = layoutService.findByNameAndObjectDescribeApiNameAndTenantId(layoutApiName, arg.getObjectApiName(), tenantId, null);
                if (layout == null) {
                    notLayoutTenantIds.add(tenantId);
                    continue;
                }

                List<IComponent> components = layout.getComponents();
                if (CollectionUtils.isEmpty(components)) {
                    log.info("components isEmpty tenantId[{}]", tenantId);
                    componentsEmptyTenantIds.add(tenantId);
                    continue;
                }

                boolean needUpdate = false;
                for(IComponent component : components) {
                    if (!Objects.equals("form_component", component.getName())) {
                        continue;
                    }
                    List<IFieldSection> fieldSections = ((FormComponent) component).getFieldSections();
                    for (IFieldSection fieldSection : fieldSections) {
                        if (!"base_field_section__c".equals(fieldSection.getName())) {
                            continue;
                        }

                        List<String> notExistFieldApiNames = CrmLayoutUtil.getNotExistFieldApiName(fieldSection, addFieldApiNames);
                        if (CollectionUtils.isEmpty(notExistFieldApiNames)) {
                            break;
                        }
                        needUpdate = true;

                        //缺的字段，补到formFields
                        List<IFormField> formFields = fieldSection.getFields();
                        for (String notExistFieldApiName : notExistFieldApiNames) {
                            CurlModel.Field field = fieldApiName2FieldMap.get(notExistFieldApiName);
                            IFormField formField = new FormField();
                            formField.setFieldName(field.getFieldName());
                            formField.setRenderType(field.getRenderType());
                            formField.setRequired(field.getIsRequired());
                            formField.setReadOnly(field.getIsReadonly());
                            formFields.add(formField);
                        }
                        fieldSection.setFields(formFields);
                    }
                }

                if (!needUpdate) {
                    notNeedUpdateTenantIds.add(tenantId);
                    continue;
                }

                layoutService.replace(layout);
                successTenantIds.add(tenantId);
                log.info("layoutAddFields success  tenantId[{}]", tenantId);
            } catch (Exception e) {
                log.info("layoutAddFields fail  tenantId[{}]", tenantId, e);
                failTenantIds.add(tenantId);
            }

            log.info("layoutAddFields end  tenantId[{}]", tenantId);
        }
        log.info("layoutAddFields finish result[{}]", JSONObject.toJSONString(result));

        return result;
    }

    private boolean hasOpen(String tenantId, String key, List<String> values) {
        User admin = User.systemUser(tenantId);
        Map<String, String> configMap = configService.queryTenantConfigs(admin, Lists.newArrayList(key));
        String value = configMap.get(key);
        return values.contains(value);
    }

    @Override
    public CurlModel.FindOrCreatePluginInstanceForComponentCheckAccountCheckRuleResult findOrCreatePluginInstanceForComponentCheckAccountCheckRule(ServiceContext serviceContext, CurlModel.FindOrCreatePluginInstanceForComponentCheckAccountCheckRuleArg arg) {
        List<String> notOpenTenantIds = Lists.newArrayList();
        List<String> failTenantIds = Lists.newArrayList();
        List<String> pluginExistTenantIds = Lists.newArrayList();
        List<String> pluginNotExistTenantIds = Lists.newArrayList();
        List<String> successTenantIds = Lists.newArrayList();

        //【组件扣减】的【校验规则】不存在
        List<String> componentReduceAccountCheckRuleNotExistTenantIds = Lists.newArrayList();

        if (CollectionUtils.isEmpty(arg.getTargetTenantIds())) {
            return new CurlModel.FindOrCreatePluginInstanceForComponentCheckAccountCheckRuleResult(notOpenTenantIds, failTenantIds, pluginExistTenantIds, pluginNotExistTenantIds, successTenantIds, componentReduceAccountCheckRuleNotExistTenantIds);
        }

        for (String targetTenantId : arg.getTargetTenantIds()) {
            try {
                log.info("findOrCreatePluginInstanceForComponentCheckAccountCheckRule begin tenantId[{}], pluginApiName[{}]", targetTenantId, arg.getPluginApiName());

                boolean isAccountCheckRuleOpen = fundAccountConfigManager.isAccountCheckRuleOpen(targetTenantId);
                if (!isAccountCheckRuleOpen) {
                    notOpenTenantIds.add(targetTenantId);
                    continue;
                }

                //【组件扣减】的【校验规则】
                IObjectData componentReduceAccountCheckRuleData = accountCheckRuleManager.getComponentReduceAccountCheckRuleData(targetTenantId, arg.getReduceRelatedObjectApiName());
                if (componentReduceAccountCheckRuleData == null) {
                    componentReduceAccountCheckRuleNotExistTenantIds.add(targetTenantId);
                    continue;
                }

                //插件是否存在
                String pluginApiName = arg.getPluginApiName();
                boolean pluginExist = domainPluginManager.exist(targetTenantId, pluginApiName, arg.getReduceRelatedObjectApiName());
                if (pluginExist) {
                    pluginExistTenantIds.add(targetTenantId);
                    continue;
                }
                pluginNotExistTenantIds.add(targetTenantId);

                if (!arg.isDoCreateIfNotExist()) {
                    continue;
                }
                RequestContext requestContext = ServiceContextUtil.getRequestContext(User.systemUser(targetTenantId));
                if (Objects.equals(pluginApiName, "layout_customer_account_component_check")) {
                    domainPluginManager.createOrEnableLayoutCustomerAccountComponentCheckPluginInstance(targetTenantId, arg.getReduceRelatedObjectApiName());
                } else if (Objects.equals(pluginApiName, "customer_account")) {
                    domainPluginManager.createPluginInstanceIfNotExist(requestContext, "customer_account", arg.getReduceRelatedObjectApiName());
                }

                successTenantIds.add(targetTenantId);
                log.info("findOrCreatePluginInstanceForComponentCheckAccountCheckRule end   targetTenantId[{}], pluginApiName[{}]", targetTenantId, pluginApiName);
            } catch (Exception e) {
                log.info("findOrCreatePluginInstanceForComponentCheckAccountCheckRule error   targetTenantId[{}], pluginApiName[{}]", targetTenantId, arg.getPluginApiName(), e);
                failTenantIds.add(targetTenantId);
            }
        }

        CurlModel.FindOrCreatePluginInstanceForComponentCheckAccountCheckRuleResult result
                = new CurlModel.FindOrCreatePluginInstanceForComponentCheckAccountCheckRuleResult(notOpenTenantIds, failTenantIds, pluginExistTenantIds, pluginNotExistTenantIds, successTenantIds, componentReduceAccountCheckRuleNotExistTenantIds);
        log.info("findOrCreatePluginInstanceForComponentCheckAccountCheckRule end arg[{}], result[{}]", arg, result);
        return result;
    }

    @Override
    public CurlModel.TriggerCustomerAccountAddActionDomainPluginResult triggerCustomerAccountAddActionDomainPlugin(CurlModel.TriggerCustomerAccountAddActionDomainPluginArg arg) {
        return customerAccountDomainPluginManager.triggerCustomerAccountAddActionDomainPlugin(arg);
    }

    @Override
    public CurlModel.QueryAccountRuleUseRecordCountResult queryAccountRuleUseRecordCount(CurlModel.QueryAccountRuleUseRecordCountArg arg) {
        List<String> failTenantIds = Lists.newArrayList();
        List<String> accountAuthNotOpenTenantIds = Lists.newArrayList();
        List<String> numberNotEnoughTenantIds = Lists.newArrayList();
        Map<String, Integer> tenantId2AccountRuleUseRecordCount = new HashMap<>();

        CurlModel.QueryAccountRuleUseRecordCountResult result = new CurlModel.QueryAccountRuleUseRecordCountResult(failTenantIds, accountAuthNotOpenTenantIds, numberNotEnoughTenantIds, tenantId2AccountRuleUseRecordCount.keySet(), tenantId2AccountRuleUseRecordCount);

        if (CollectionUtils.isEmpty(arg.getTenantIds()) || CollectionUtils.isEmpty(arg.getRuleTypes())) {
            return result;
        }

        for (String tenantId : arg.getTenantIds()) {
            boolean isAccountAuthOpen = fundAccountConfigManager.isAccountAuthOpen(tenantId);
            if (!isAccountAuthOpen) {
                accountAuthNotOpenTenantIds.add(tenantId);
                continue;
            }

            //参考 GoalRuleOperatorService.initGoalData
            String ruleTypeStr = Joiner.on("','").join(arg.getRuleTypes());
            String dataSql = "select count(1) as number from account_rule_use_record where is_deleted = '0' and tenant_id = " + "'" + tenantId + "'" + " and rule_type in ('" + ruleTypeStr + "')";

            QueryResult<IObjectData> dataQueryResult = null;
            try {
                dataQueryResult = objectDataService.findBySql(dataSql, tenantId, AccountRuleUseRecordConstants.API_NAME);
            } catch (MetadataServiceException e) {
                log.warn("queryAccountRuleUseRecordCount  findBySql error, dataSql[{}], tenantId[{}]", dataSql, tenantId, e);
                failTenantIds.add(tenantId);
                continue;
            }

            if (dataQueryResult == null || CollectionUtils.isEmpty(dataQueryResult.getData())) {
                failTenantIds.add(tenantId);
                continue;
            }

            IObjectData count = dataQueryResult.getData().get(0);
            Integer number = (Integer) count.get("number");
            if (number > arg.getBiggerThanCount()) {
                tenantId2AccountRuleUseRecordCount.put(tenantId, number);
            } else {
                numberNotEnoughTenantIds.add(tenantId);
            }
        }

        return result;
    }

    @Override
    public CurlModel.QueryFixComponentReduceFlowResult queryFixComponentReduceFlow(CurlModel.QueryFixComponentReduceFlowArg arg) {
        return customerAccountDomainPluginManager.queryFixComponentReduceFlow(arg);
    }

    @Override
    public CurlModel.QueryAndInvalidComponentReduceFlowResult queryAndInvalidComponentReduceFlow(CurlModel.QueryAndInvalidComponentReduceFlowArg arg) {
        return customerAccountDomainPluginManager.queryAndInvalidComponentReduceFlow(arg);
    }

    @Override
    public CurlModel.GetOrUpdateAccountCheckRuleLifeStatusResult getOrUpdateAccountCheckRuleLifeStatus(CurlModel.GetOrUpdateAccountCheckRuleLifeStatusArg arg) {
        String traceId = TraceContext.get().getTraceId();

        List<String> notOpenTenantIds = new ArrayList<>();
        List<String> emptyTenantIds = new ArrayList<>();
        List<String> successTenantIds = new ArrayList<>();
        List<String> failTenantIds = new ArrayList<>();

        Map<String, List<String>> tenantId2AccountCheckRuleIds = new HashMap<>();
        Map<String, List<CurlModel.AccountCheckRuleInfo>> tenantId2AccountCheckRules = new HashMap<>();

        CurlModel.GetOrUpdateAccountCheckRuleLifeStatusResult result = new CurlModel.GetOrUpdateAccountCheckRuleLifeStatusResult(traceId, notOpenTenantIds, emptyTenantIds, successTenantIds, failTenantIds, tenantId2AccountCheckRuleIds, tenantId2AccountCheckRules);
        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return result;
        }

        for (String tenantId : arg.getTenantIds()) {
            try {
                log.info("getOrUpdateAccountCheckRuleLifeStatus begin tenantId[{}]", tenantId);

                boolean isAccountCheckRuleOpen = fundAccountConfigManager.isAccountCheckRuleOpen(tenantId);
                if (!isAccountCheckRuleOpen) {
                    notOpenTenantIds.add(tenantId);
                    continue;
                }

                //查未生效的
                List<IObjectData> ineffectiveAccountCheckRules = getIneffectiveAccountCheckRules(tenantId, arg.getAccountCheckRuleIds(), arg.getCreateTimeStartTime(), arg.getCreateTimeEndTime());

                //没有要处理的
                if (CollectionUtils.isEmpty(ineffectiveAccountCheckRules)) {
                    emptyTenantIds.add(tenantId);
                    continue;
                }

                //tenantId2AccountCheckRuleIds
                List<String> ineffectiveAccountCheckRuleIds = ineffectiveAccountCheckRules.stream().map(IObjectData::getId).collect(Collectors.toList());
                tenantId2AccountCheckRuleIds.put(tenantId, ineffectiveAccountCheckRuleIds);

                //tenantId2AccountCheckRules
                if (arg.isShowTenantId2AccountCheckRules()) {
                    List<CurlModel.AccountCheckRuleInfo> accountCheckRuleInfos = Lists.newArrayList();
                    for (IObjectData ineffectiveAccountCheckRule : ineffectiveAccountCheckRules) {
                        String ruleType = ineffectiveAccountCheckRule.get(AccountCheckRuleConstants.Field.RuleType.apiName, String.class);
                        String status = ineffectiveAccountCheckRule.get(AccountCheckRuleConstants.Field.Status.apiName, String.class);
                        CurlModel.AccountCheckRuleInfo info = new CurlModel.AccountCheckRuleInfo(ineffectiveAccountCheckRule.getId(), ineffectiveAccountCheckRule.getName(), ruleType, status);
                        accountCheckRuleInfos.add(info);
                    }
                    tenantId2AccountCheckRules.put(tenantId, accountCheckRuleInfos);
                }

                if (!arg.isDoUpdate()) {
                    continue;
                }

                for (IObjectData ineffectiveAccountCheckRule : ineffectiveAccountCheckRules) {
                    ineffectiveAccountCheckRule.set(SystemConstants.Field.LifeStatus.apiName, SystemConstants.LifeStatus.Normal.value);
                }

                //更新
                User admin = User.systemUser(tenantId);
                List<String> updateFields = Lists.newArrayList(SystemConstants.Field.LifeStatus.apiName);
                serviceFacade.batchUpdateByFields(admin, ineffectiveAccountCheckRules, updateFields);

                log.info("getOrUpdateAccountCheckRuleLifeStatus end tenantId[{}]", tenantId);
            } catch (Exception e) {
                log.info("getOrUpdateAccountCheckRuleLifeStatus fail tenantId[{}]", tenantId);
                failTenantIds.add(tenantId);
            }
        }

        log.info("getOrUpdateAccountCheckRuleLifeStatus finish result[{}]", result);

        return result;
    }

    private List<IObjectData> getIneffectiveAccountCheckRules(String tenantId, List<String> accountCheckRuleIds, Long createTimeStartTime, Long createTimeEndTime) {
        List<IFilter> filters = new ArrayList<>();
        SearchUtil.fillFilterEq(filters, SystemConstants.Field.LifeStatus.apiName, SystemConstants.LifeStatus.Ineffective.value);
        SearchUtil.fillFilterEq(filters, ObjectData.IS_DELETED, Lists.newArrayList("0"));

        if (!CollectionUtils.isEmpty(accountCheckRuleIds)) {
            SearchUtil.fillFilterIn(filters, ObjectData.ID, accountCheckRuleIds);
        }
        if (createTimeStartTime != null) {
            SearchUtil.fillFilterGTE(filters, SystemConstants.Field.CreateTime.apiName, createTimeStartTime);
        }
        if (createTimeEndTime != null) {
            SearchUtil.fillFilterLTE(filters, SystemConstants.Field.CreateTime.apiName, createTimeEndTime);
        }

        User admin = User.systemUser(tenantId);
        String objectApiName = AccountCheckRuleConstants.API_NAME;
        return com.facishare.crmcommon.util.ObjectDataUtil.getObjectDatasWithDeleted(admin, objectApiName, filters);
    }

    private List<IObjectData> queryCheckRules(User user) {
        List<IFilter> filters = Lists.newArrayList();
//        SearchUtil.fillFilterEq(filters, AccountCheckRuleConstants.Field.Status.apiName, AccountCheckRuleStatusEnum.On.getValue());
        SearchUtil.fillFilterEq(filters, AccountCheckRuleConstants.Field.RuleType.apiName, AccountCheckRuleTypeEnum.Check_Reduce.getValue());
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setFilters(filters);
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(1000);
        return serviceFacade.findBySearchQuery(user, AccountCheckRuleConstants.API_NAME, searchTemplateQuery).getData();
    }

    /**
     * 参考 com.facishare.paas.appframework.privilege.FunctionPrivilegeServiceImpl#deleteFunctionPrivilege
     */
    private void deletePrivilege(User user, String actionCode, String objectApiName, List<String> roleCodes) {
        AuthContext authContext = buildAuthContext(user);
        List<String> delFuncCodes = Lists.newArrayList(FunctionCodeBuilder.build(objectApiName, actionCode));

        //给角色删权限
        roleCodes.forEach(roleCode -> {
            delRoleFunctionPrivilege(authContext, roleCode, delFuncCodes);
        });

        //给对象删权限
        functionPrivilegeService.batchDelFunc(user, delFuncCodes);
    }

    private void purgeData(User user, String objectApiName) {
        int limit = 100;
        int offset = 0;
        int size;

        try {
            do {
                List<IObjectData> toBeDeletedData = serviceFacade.findDataWithWhere(user, objectApiName, Lists.newArrayList(), Lists.newArrayList(), offset, limit);
                size = toBeDeletedData.size();
                serviceFacade.bulkDeleteDirect(toBeDeletedData, user);
            } while (limit == size);
        } catch (Exception e) {
            log.warn("purge data errror, tenantId={} objectApiName={}", user.getTenantId(), objectApiName, e);
        }
    }

    private void invalidAndDeleteFunction(User user, String objectApiName) {
        AuthContext authContext = AuthContext.builder().appId("CRM").tenantId(user.getTenantId()).userId(user.getUserId()).build();
        //添加权限
        List<CreateFunctionPrivilege.FunctionPojo> functionPojos = getUserInvalidAndDeleteFunctionPojoList(authContext.getTenantId(), objectApiName);
        if (CollectionUtils.isNotEmpty(functionPojos)) {
            CreateFunctionPrivilege.Arg arg = CreateFunctionPrivilege.Arg.builder().authContext(authContext).functionPojoList(functionPojos).build();
            this.functionPrivilegeProxy.createFunctionPrivilege(arg, FunctionPrivilegeProxy.HeaderUtil.buildHeaders(user.getTenantId()));
        }
        //给角色添加权限
        addRoleInvalidAndDeleteFunctionPrivilege(authContext, CRM_MANAGER_ROLE, objectApiName);
        addRoleInvalidAndDeleteFunctionPrivilege(authContext, ORDER_FINANCE_ROLE, objectApiName);
        addRoleInvalidAndDeleteFunctionPrivilege(authContext, ORDER_MANAGER_ROLE, objectApiName);
    }

    private List<CreateFunctionPrivilege.FunctionPojo> getUserInvalidAndDeleteFunctionPojoList(String tenantId, String apiName) {
        ArrayList userDefinedFunctionPojoList = Lists.newArrayList();
        String deleteActionCode = ObjectAction.DELETE.getActionCode();
        CreateFunctionPrivilege.FunctionPojo deletePojo = new CreateFunctionPrivilege.FunctionPojo();
        deletePojo.setAppId("CRM");
        deletePojo.setTenantId(tenantId);
        deletePojo.setFuncType(Integer.valueOf(1));
        deletePojo.setParentCode("00000000000000000000000000000000");
        deletePojo.setFuncName(ObjectAction.of(deleteActionCode).getActionLabel());
        deletePojo.setFuncCode(FunctionCodeBuilder.build(apiName, deleteActionCode));
        userDefinedFunctionPojoList.add(deletePojo);

        String invalidActionCode = ObjectAction.INVALID.getActionCode();
        CreateFunctionPrivilege.FunctionPojo invalidPojo = new CreateFunctionPrivilege.FunctionPojo();
        invalidPojo.setAppId("CRM");
        invalidPojo.setTenantId(tenantId);
        invalidPojo.setFuncType(Integer.valueOf(1));
        invalidPojo.setParentCode("00000000000000000000000000000000");
        invalidPojo.setFuncName(ObjectAction.of(invalidActionCode).getActionLabel());
        invalidPojo.setFuncCode(FunctionCodeBuilder.build(apiName, invalidActionCode));
        userDefinedFunctionPojoList.add(invalidPojo);
        return userDefinedFunctionPojoList;
    }

    private void addRoleInvalidAndDeleteFunctionPrivilege(AuthContext authContext, String roleCode, String objectApiName) {
        String deleteFuncCode = FunctionCodeBuilder.build(objectApiName, "Delete");
        String invalidFuncCode = FunctionCodeBuilder.build(objectApiName, "Abolish");
        List<String> funcCodeList = new ArrayList<>();
        funcCodeList.add(deleteFuncCode);
        funcCodeList.add(invalidFuncCode);
        if (CollectionUtils.isNotEmpty(funcCodeList)) {
            com.facishare.paas.appframework.privilege.dto.UpdateRoleModifiedFuncPrivilege.Arg arg = com.facishare.paas.appframework.privilege.dto.UpdateRoleModifiedFuncPrivilege.Arg.builder().authContext(authContext).roleCode(roleCode).addFuncCode(funcCodeList).build();
            com.facishare.paas.appframework.privilege.dto.UpdateRoleModifiedFuncPrivilege.Result result = this.functionPrivilegeProxy.updateRoleModifiedFuncPrivilege(arg, FunctionPrivilegeProxy.HeaderUtil.buildHeaders(authContext.getTenantId()));
            if (!result.isSuccess()) {
                log.error("addRoleInvalidAndDeleteFunctionPrivilege error,arg:{},result:{}", arg, result);
            }
        }
    }

    // TODO: 2024/10/18 特殊情况
    /**
     * 对象不存在
     */
    @Override
    public CurlModel.AccountCheckRuleConfigTransferResult accountCheckRuleConfigTransfer(ServiceContext serviceContext, CurlModel.AccountCheckRuleConfigTransferArg arg) {
        String traceId = TraceContext.get().getTraceId();
        List<String> notOpenTenantIds = new ArrayList<>();
        List<String> successTenantIds = new ArrayList<>();
        List<String> notNeedUpdateTenantIds = new ArrayList<>();
        List<String> failTenantIds = new ArrayList<>();
        Set<String> notExistOutComeAuthObjects = new HashSet<>();
        CurlModel.AccountCheckRuleConfigTransferResult result = new CurlModel.AccountCheckRuleConfigTransferResult(traceId, notOpenTenantIds, successTenantIds, notNeedUpdateTenantIds, failTenantIds, notExistOutComeAuthObjects);
        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return result;
        }

        for (String tenantId : arg.getTenantIds()) {
            try {
                log.info("accountCheckRuleConfigTransfer begin tenantId[{}]", tenantId);

                boolean hasUpdate = false;
                //是否开了校验规则
                boolean isAccountCheckRuleOpen = fundAccountConfigManager.isAccountCheckRuleOpen(tenantId);
                if (!isAccountCheckRuleOpen) {
                    notOpenTenantIds.add(tenantId);
                    continue;
                }

                //读配置中心
                CustomerAccountType.GetCheckObjectsAndReduceRuleObjects checkObjectsAndReduceRuleObjects = ConfigCenter.getCheckObjectsAndReduceRuleObjects(tenantId);

                /**
                 * 组件扣减 ：不用处理
                 *   这些已经有支出授权了，不用补支出授权
                 *   is_unfreeze_auth       ：用不到
                 *   frozen_actions         ：用不到
                 *   reduce_trigger_actions ：在另外一个接口，刷为默认数据
                 */

                /**
                 * 直接扣减
                 *   这些已经有支出授权了，不用补支出授权
                 *   is_unfreeze_auth       ：用不到，不用管 (如果最后没有值，可以调另外一个接口CurlService#transferAccountAuth930，刷为false）
                 *   frozen_actions         ：用不到，不用管
                 *   reduce_trigger_actions ：刷为"Add_button_default", "ConfirmReceipt_button_default", "fieldChange"
                 */
                for (CustomerAccountType.ReduceRuleObject reduceRuleObject : checkObjectsAndReduceRuleObjects.getReduceRuleObjectsForDirectReduce()) {
                    String authorizedObjectApiName = reduceRuleObject.getApiName();
                    IObjectData fAccountAuthorizationData = fAccountAuthorizationManager.getFAccountAuthorizationData(tenantId, authorizedObjectApiName, FAccountAuthAuthorizedTypeEnum.Outcome.getValue());
                    if (fAccountAuthorizationData == null) {
                        //是否有对一个的【直接扣减】的【校验规则】
                        boolean hasDirectReduceAccountCheckRuleDataExist = accountCheckRuleManager.hasDirectReduceAccountCheckRuleDataExist(tenantId, authorizedObjectApiName);
                        if (hasDirectReduceAccountCheckRuleDataExist) {
                            notExistOutComeAuthObjects.add(tenantId + "-" + authorizedObjectApiName);
                        }
                        //不知道用什么账号，用户自己新建
                        continue;
                    }
                    List<String> reduceTriggerActions = fAccountAuthorizationManager.getReduceTriggerActions(authorizedObjectApiName);
                    boolean update = fAccountAuthorizationManager.updateReduceTriggerActions(tenantId, fAccountAuthorizationData, reduceTriggerActions);
                    if (update) {
                        hasUpdate = true;
                    }
                }

                /**
                 * 校验扣减
                 *   【账户授权】
                 *       这些已经有支出授权了，不用补支出授权
                 *       is_unfreeze_auth       ：true
                 *       frozen_actions         ："Add_button_default", "fieldChange"
                 *       reduce_trigger_actions ：用不到
                 *   【解冻授权明细】
                 *       不存在的话，要加一条数据
                 */
                for (CustomerAccountType.CheckObject checkObject : checkObjectsAndReduceRuleObjects.getCheckObjectsForCheckReduce()) {
                    String authorizedObjectApiName = checkObject.getApiName();
                    IObjectData fAccountAuthorizationData = fAccountAuthorizationManager.getFAccountAuthorizationData(tenantId, authorizedObjectApiName, FAccountAuthAuthorizedTypeEnum.Outcome.getValue());
                    if (fAccountAuthorizationData == null) {
                        //是否有对一个的【直接扣减】的【校验规则】
                        boolean hasCheckReduceAccountCheckRuleDataExist = accountCheckRuleManager.hasCheckReduceAccountCheckRuleDataExist(tenantId, authorizedObjectApiName);
                        if (hasCheckReduceAccountCheckRuleDataExist) {
                            notExistOutComeAuthObjects.add(tenantId + "-" + authorizedObjectApiName);
                        }
                        //不知道用什么账号，用户自己新建
                        continue;
                    }

                    List<String> reduceTriggerActions = fAccountAuthorizationManager.getReduceTriggerActions(authorizedObjectApiName);

                    List<CustomerAccountType.ReduceRuleObject> reduceRuleObjects = checkObject.getReduceRuleObjects();
                    if (CollectionUtils.isEmpty(reduceRuleObjects)) {
                        continue;
                    }

                    List<String> frozenActions =  Lists.newArrayList("Add_button_default", "fieldChange");
                    boolean update = fAccountAuthorizationManager.update(tenantId, fAccountAuthorizationData, true, frozenActions, reduceTriggerActions);
                    if (update) {
                        hasUpdate = true;
                    }

                    //不存在的话，要加一条数据【解冻授权明细】
                    List<String> unfreezeObjects = reduceRuleObjects.stream().map(CustomerAccountType.ReduceRuleObject::getApiName).collect(Collectors.toList());
                    boolean create = unfreezeAuthDetailManager.create(tenantId, fAccountAuthorizationData.getId(), authorizedObjectApiName, unfreezeObjects);
                    if (create) {
                        hasUpdate = true;
                    }
                }
                if (hasUpdate) {
                    successTenantIds.add(tenantId);
                } else {
                    notNeedUpdateTenantIds.add(tenantId);
                }
                log.info("accountCheckRuleConfigTransfer end tenantId[{}]", tenantId);
            } catch (Exception e) {
                log.info("accountCheckRuleConfigTransfer fail tenantId[{}]", tenantId, e);
                failTenantIds.add(tenantId);
            }
        }
        log.info("accountCheckRuleConfigTransfer finish result[{}]", JSONObject.toJSONString(result));
        return result;
    }

    @Override
    public CurlModel.AccountCheckRule2AccountAuthResult accountCheckRule2AccountAuth(ServiceContext serviceContext, CurlModel.AccountCheckRule2AccountAuthArg arg) {
        String traceId = TraceContext.get().getTraceId();
        List<String> notOpenTenantIds = new ArrayList<>();
        List<String> notAccountCheckRuleTenantIds = new ArrayList<>();
        List<String> notNeedUpdateTenantIds = new ArrayList<>();
        List<String> successTenantIds = new ArrayList<>();
        List<String> failTenantIds = new ArrayList<>();
        Map<String, List<CurlModel.AccountCheckRule>> tenantId2NotExistAccountAuthAccountCheckRules = new HashMap<>();
        Set<String> tenantId2NotExistAccountAuthAccountCheckRulesKeys = tenantId2NotExistAccountAuthAccountCheckRules.keySet();

        CurlModel.AccountCheckRule2AccountAuthResult result = new CurlModel.AccountCheckRule2AccountAuthResult(traceId, notOpenTenantIds, notAccountCheckRuleTenantIds, notNeedUpdateTenantIds, successTenantIds, failTenantIds, tenantId2NotExistAccountAuthAccountCheckRulesKeys, tenantId2NotExistAccountAuthAccountCheckRules);
        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return result;
        }

        for (String tenantId : arg.getTenantIds()) {
            try {
                log.info("accountCheckRule2AccountAuth begin tenantId[{}]", tenantId);

                //是否开了校验规则
                boolean isAccountCheckRuleOpen = fundAccountConfigManager.isAccountCheckRuleOpen(tenantId);
                if (!isAccountCheckRuleOpen) {
                    notOpenTenantIds.add(tenantId);
                    continue;
                }

                //查所有的【校验规则】
                List<String> ruleTypes = Lists.newArrayList(AccountCheckRuleTypeEnum.Direct_Reduce.getValue(), AccountCheckRuleTypeEnum.Check_Reduce.getValue(), AccountCheckRuleTypeEnum.Component_Reduce.getValue());
                List<String> fields = Lists.newArrayList(SystemConstants.Field.Id.apiName, AccountCheckRuleConstants.Field.Name.apiName, AccountCheckRuleConstants.Field.RuleType.apiName, AccountCheckRuleConstants.Field.Status.apiName,
                        AccountCheckRuleConstants.Field.CheckObject.apiName, AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName,
                        AccountCheckRuleConstants.Field.OccupiedMapping.apiName, AccountCheckRuleConstants.Field.ReduceMapping.apiName);
                List<IObjectData> accountCheckRuleDatas = accountCheckRuleManager.getAllAccountCheckRuleDatas(tenantId, ruleTypes, false, fields);
                if (CollectionUtils.isEmpty(accountCheckRuleDatas)) {
                    notAccountCheckRuleTenantIds.add(tenantId);
                    continue;
                }

                boolean hasUpdate = false;
                List<CurlModel.AccountCheckRule> notExistAccountAuthAccountCheckRules = Lists.newArrayList();
                for (IObjectData accountCheckRule : accountCheckRuleDatas) {
                    String ruleType = accountCheckRule.get(AccountCheckRuleConstants.Field.RuleType.apiName, String.class);

                    String authorizedObjectApiName = null;
                    //直接扣减、组件扣减
                    if (Objects.equals(ruleType, AccountCheckRuleTypeEnum.Direct_Reduce.getValue())
                         || Objects.equals(ruleType, AccountCheckRuleTypeEnum.Component_Reduce.getValue())) {
                        authorizedObjectApiName = accountCheckRule.get(AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName, String.class);
                    }
                    //校验扣减
                    else if (Objects.equals(ruleType, AccountCheckRuleTypeEnum.Check_Reduce.getValue())) {
                        authorizedObjectApiName = accountCheckRule.get(AccountCheckRuleConstants.Field.CheckObject.apiName, String.class);
                    }

                    //是否有【账户授权】
                    IObjectData fAccountAuthorizationData = fAccountAuthorizationManager.getFAccountAuthorizationData(tenantId, authorizedObjectApiName, FAccountAuthAuthorizedTypeEnum.Outcome.getValue());
                    if (fAccountAuthorizationData == null) {
                        String status = accountCheckRule.get(AccountCheckRuleConstants.Field.Status.apiName, String.class);
                        CurlModel.AccountCheckRule notExistAccountAuthAccountCheckRule = new CurlModel.AccountCheckRule(accountCheckRule.getId(), accountCheckRule.getName(), ruleType, status);
                        notExistAccountAuthAccountCheckRules.add(notExistAccountAuthAccountCheckRule);
                        continue;
                    } else {
                        if (!arg.isDoUpdate()) {
                            continue;
                        }
                        boolean update = fAccountAuthorizationManager.updateForAccountCheckRule(tenantId, accountCheckRule, fAccountAuthorizationData);
                        if (update) {
                            hasUpdate = true;
                        }
                    }
                }
                if (!CollectionUtils.isEmpty(notExistAccountAuthAccountCheckRules)) {
                    tenantId2NotExistAccountAuthAccountCheckRules.put(tenantId, notExistAccountAuthAccountCheckRules);
                }

                if (hasUpdate) {
                    successTenantIds.add(tenantId);
                } else {
                    notNeedUpdateTenantIds.add(tenantId);
                }
                log.info("accountCheckRule2AccountAuth end tenantId[{}]", tenantId);
            } catch (Exception e) {
                log.info("accountCheckRule2AccountAuth fail tenantId[{}]", tenantId, e);
                failTenantIds.add(tenantId);
            }
        }
        log.info("accountCheckRule2AccountAuth finish result[{}]", JSONObject.toJSONString(result));
        return result;
    }

    @Override
    public CurlModel.TransferAccountAuth930Result transferAccountAuth930(ServiceContext serviceContext, CurlModel.TransferAccountAuth930Arg arg) {
        String traceId = TraceContext.get().getTraceId();
        List<String> notOpenTenantIds = new ArrayList<>();
        List<String> successTenantIds = new ArrayList<>();
        List<String> notNeedUpdateTenantIds = new ArrayList<>();
        List<String> failTenantIds = new ArrayList<>();
        CurlModel.TransferAccountAuth930Result result = new CurlModel.TransferAccountAuth930Result(traceId, notOpenTenantIds, successTenantIds, notNeedUpdateTenantIds, failTenantIds);
        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return result;
        }

        for (String tenantId : arg.getTenantIds()) {
            try {
                log.info("transferAccountAuth930 begin tenantId[{}]", tenantId);

                //是否开了账户授权
                boolean isAccountAuthOpen = fundAccountConfigManager.isAccountAuthOpen(tenantId);
                if (!isAccountAuthOpen) {
                    notOpenTenantIds.add(tenantId);
                    continue;
                }

                List<String> authorizedTypes = Lists.newArrayList(FAccountAuthAuthorizedTypeEnum.Outcome.getValue());
                List<IObjectData> outcomeAccountAuths = fAccountAuthorizationManager.getFAccountAuthorizationDatas(tenantId, authorizedTypes);
                if (CollectionUtils.isEmpty(outcomeAccountAuths)) {
                    notNeedUpdateTenantIds.add(tenantId);
                    continue;
                }

                boolean update = fAccountAuthorizationManager.updateIsUnfreezeAuthIfNotExistAndReduceTriggerActions(tenantId, outcomeAccountAuths, false);
                if (update) {
                    successTenantIds.add(tenantId);
                } else {
                    notNeedUpdateTenantIds.add(tenantId);
                }
                log.info("transferAccountAuth930 end tenantId[{}]", tenantId);
            } catch (Exception e) {
                log.info("transferAccountAuth930 fail tenantId[{}]", tenantId);
                failTenantIds.add(tenantId);
            }
        }
        return result;
    }

    @Override
    public CurlModel.DirectReduceNotForceCheckAmountResult directReduceNotForceCheckAmount(ServiceContext serviceContext, CurlModel.DirectReduceNotForceCheckAmountArg arg) {
        String traceId = TraceContext.get().getTraceId();
        List<String> notOpenTenantIds = new ArrayList<>();
        List<String> notSupportCheckRuleDomainTenantIds = new ArrayList<>();
        List<String> hasDirectReduceAccountCheckRuleTenantIds = new ArrayList<>();
        List<String> notNeedUpdateTenantIds = new ArrayList<>();
        List<String> hasUpdateNotNeedUpdateTenantIds = new ArrayList<>();
        List<String> hasUpdateByHandNotNeedUpdateTenantIds = new ArrayList<>();
        List<String> successTenantIds = new ArrayList<>();
        List<String> failTenantIds = new ArrayList<>();
        CurlModel.DirectReduceNotForceCheckAmountResult result = new CurlModel.DirectReduceNotForceCheckAmountResult(traceId, notOpenTenantIds, notSupportCheckRuleDomainTenantIds, hasDirectReduceAccountCheckRuleTenantIds,
                notNeedUpdateTenantIds, hasUpdateNotNeedUpdateTenantIds, hasUpdateByHandNotNeedUpdateTenantIds, successTenantIds, failTenantIds);
        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return result;
        }

        for (String tenantId : arg.getTenantIds()) {
            try {
                log.info("directReduceNotForceCheckAmount begin tenantId[{}]", tenantId);

                //是否开了校验规则
                boolean isAccountCheckRuleOpen = fundAccountConfigManager.isAccountCheckRuleOpen(tenantId);
                if (!isAccountCheckRuleOpen) {
                    notOpenTenantIds.add(tenantId);
                    continue;
                }

                boolean needUpdate = false;
                //是否支持插件
                if (!CaGrayUtil.supportCheckRuleDomain(tenantId)) {
                    notSupportCheckRuleDomainTenantIds.add(tenantId);
                    needUpdate = true;
                }

                //是否有【直接扣减】的【校验规则】
                List<String> fields = Lists.newArrayList(SystemConstants.Field.Id.apiName, AccountCheckRuleConstants.Field.Name.apiName);
                List<IObjectData> dirctReduceAccountCheck = accountCheckRuleManager.getAllAccountCheckRuleDatas(tenantId, false, fields, AccountCheckRuleTypeEnum.Direct_Reduce.getValue());
                if (!CollectionUtils.isEmpty(dirctReduceAccountCheck)) {
                    hasDirectReduceAccountCheckRuleTenantIds.add(tenantId);
                    needUpdate = true;
                }

                if (!needUpdate) {
                    notNeedUpdateTenantIds.add(tenantId);
                    continue;
                }

                //查状态
                String key = ConfigKeyEnum.DIRECT_REDUCE_FORCE_CHECK_AMOUNT.key;
                String status = configService.findTenantConfig(new User(tenantId, User.SUPPER_ADMIN_USER_ID), key);
                if (Objects.equals(status, DirectReduceForceCheckAmountEnum.Not_Force.getValue())) {
                    hasUpdateNotNeedUpdateTenantIds.add(tenantId);
                    continue;
                }

                //已经【手动】升级为 DirectReduceForceCheckAmountEnum.Force的不用改
                if (Objects.equals(status, DirectReduceForceCheckAmountEnum.Force.getValue())) {
                    hasUpdateByHandNotNeedUpdateTenantIds.add(tenantId);
                    continue;
                }

                //更新状态
                fundAccountConfigManager.updateDirectReduceForceCheckAmountStatus(tenantId, DirectReduceForceCheckAmountEnum.Not_Force);
                successTenantIds.add(tenantId);
                log.info("directReduceNotForceCheckAmount end tenantId[{}]", tenantId);
            } catch (Exception e) {
                log.info("directReduceNotForceCheckAmount fail tenantId[{}]", tenantId, e);
                failTenantIds.add(tenantId);
            }
        }

        log.info("directReduceNotForceCheckAmount finish result[{}]", JSONObject.toJSONString(result));
        return result;
    }

    @Override
    public CurlModel.GetAccountCheckRuleByFieldTypeResult getAccountCheckRuleByFieldType(CurlModel.GetAccountCheckRuleByFieldTypeArg arg) {
        String traceId = TraceContext.get().getTraceId();

        List<String> notOpenTenantIds = new ArrayList<>();
        List<String> notHasAccountCheckRuleTenantIds = new ArrayList<>();
        List<String> successTenantIds = new ArrayList<>();
        List<String> failTenantIds = new ArrayList<>();

        Map<String, List<CurlModel.AccountCheckRuleFieldInfo>> tenantId2AccountCheckRules = new HashMap<>();

        CurlModel.GetAccountCheckRuleByFieldTypeResult result = new CurlModel.GetAccountCheckRuleByFieldTypeResult(traceId, notOpenTenantIds, notHasAccountCheckRuleTenantIds, successTenantIds, failTenantIds, tenantId2AccountCheckRules.keySet(), tenantId2AccountCheckRules);
        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return result;
        }

        List<String> fieldTypes = arg.getFieldTypes();
        for (String tenantId : arg.getTenantIds()) {
            try {
                log.info("getAccountCheckRuleByFieldType begin tenantId[{}]", tenantId);

                boolean isAccountCheckRuleOpen = fundAccountConfigManager.isAccountCheckRuleOpen(tenantId);
                if (!isAccountCheckRuleOpen) {
                    notOpenTenantIds.add(tenantId);
                    continue;
                }

                //所有的【校验规则】
                List<String> ruleTypes = Lists.newArrayList(AccountCheckRuleTypeEnum.Direct_Reduce.getValue(), AccountCheckRuleTypeEnum.Check_Reduce.getValue(), AccountCheckRuleTypeEnum.Component_Reduce.getValue());
                List<String> fields = Lists.newArrayList(SystemConstants.Field.Id.apiName, AccountCheckRuleConstants.Field.Name.apiName,
                        AccountCheckRuleConstants.Field.RuleType.apiName, AccountCheckRuleConstants.Field.CheckObject.apiName, AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName,
                        AccountCheckRuleConstants.Field.OccupiedMapping.apiName, AccountCheckRuleConstants.Field.ReduceMapping.apiName);
                List<IObjectData> accountCheckRuleDataList = accountCheckRuleManager.getAllAccountCheckRuleDatas(tenantId, ruleTypes, false, fields);
                if (CollectionUtils.isEmpty(accountCheckRuleDataList)) {
                    notHasAccountCheckRuleTenantIds.add(tenantId);
                    continue;
                }

                List<CurlModel.AccountCheckRuleFieldInfo> accountCheckRuleFieldInfos = Lists.newArrayList();
                for (IObjectData accountCheckRule : accountCheckRuleDataList) {
                    log.info("getAccountCheckRuleByFieldType tenantId[{}], accountCheckRuleId[{}], accountCheckRuleName[{}], begin", tenantId, accountCheckRule.getId(), accountCheckRule.getName());
                    if (!CollectionUtils.isEmpty(arg.getExcludeAccountCheckRuleIds()) && arg.getExcludeAccountCheckRuleIds().contains(accountCheckRule.getId())) {
                        continue;
                    }

                    if (!CollectionUtils.isEmpty(arg.getIncludeAccountCheckRuleIds()) && !arg.getIncludeAccountCheckRuleIds().contains(accountCheckRule.getId())) {
                        continue;
                    }

                    String ruleType = accountCheckRule.get(AccountCheckRuleConstants.Field.RuleType.apiName, String.class);
                    String status = accountCheckRule.get(AccountCheckRuleConstants.Field.Status.apiName, String.class);

                    //OccupiedMapping
                    List<CurlModel.FieldInfo> occupiedMappingFieldInfos = Lists.newArrayList();
                    if (Objects.equals(ruleType, AccountCheckRuleTypeEnum.Check_Reduce.getValue())) {
                        String checkObject = accountCheckRule.get(AccountCheckRuleConstants.Field.CheckObject.apiName, String.class);
                        List<ObjectMappingModel> occupiedMappingObjectMappings = RuleHandlerUtil.getObjectMapping(accountCheckRule, AccountCheckRuleConstants.Field.OccupiedMapping.apiName);
                        List<String> sourceOccupiedMappingFields = AccountCheckRuleMappingUtil.getSourceMappingFields(occupiedMappingObjectMappings, "freeze_amount");
                        List<IFieldDescribe> occupiedFieldDescribes = commonDescribeManager.getFieldDescribes(tenantId, checkObject, sourceOccupiedMappingFields, fieldTypes);
                        occupiedMappingFieldInfos = getFieldInfos(occupiedFieldDescribes);
                    }

                    //ReduceMapping
                    String reduceRelatedObject = accountCheckRule.get(AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName, String.class);
                    List<ObjectMappingModel> reduceMappingObjectMappings = RuleHandlerUtil.getObjectMapping(accountCheckRule, AccountCheckRuleConstants.Field.ReduceMapping.apiName);
                    List<String> sourceReduceMappingFields = AccountCheckRuleMappingUtil.getSourceMappingFields(reduceMappingObjectMappings, "expense_amount");
                    List<IFieldDescribe> reduceFieldDescribes = commonDescribeManager.getFieldDescribes(tenantId, reduceRelatedObject, sourceReduceMappingFields, fieldTypes);
                    List<CurlModel.FieldInfo> reduceMappingFieldInfos = getFieldInfos(reduceFieldDescribes);

                    if (CollectionUtils.isEmpty(occupiedMappingFieldInfos) && CollectionUtils.isEmpty(reduceMappingFieldInfos)) {
                        continue;
                    }

                    CurlModel.AccountCheckRuleFieldInfo accountCheckRuleFieldInfo = new CurlModel.AccountCheckRuleFieldInfo(accountCheckRule.getId(), accountCheckRule.getName(), ruleType, status, occupiedMappingFieldInfos, reduceMappingFieldInfos);
                    accountCheckRuleFieldInfos.add(accountCheckRuleFieldInfo);
                    log.info("getAccountCheckRuleByFieldType tenantId[{}], accountCheckRuleId[{}], accountCheckRuleName[{}], end", tenantId, accountCheckRule.getId(), accountCheckRule.getName());
                }
                if (!CollectionUtils.isEmpty(accountCheckRuleFieldInfos)) {
                    tenantId2AccountCheckRules.put(tenantId, accountCheckRuleFieldInfos);
                }

                successTenantIds.add(tenantId);
                log.info("getAccountCheckRuleByFieldType end tenantId[{}]", tenantId);
            } catch (Exception e) {
                log.info("getAccountCheckRuleByFieldType fail tenantId[{}]", tenantId);
                failTenantIds.add(tenantId);
            }
        }

        log.info("getAccountCheckRuleByFieldType finish result[{}]", result);

        return result;
    }

    private List<CurlModel.FieldInfo> getFieldInfos(List<IFieldDescribe> fieldDescribes) {
        List<CurlModel.FieldInfo> fieldInfos = Lists.newArrayList();
        if (CollectionUtils.isEmpty(fieldDescribes)) {
            return fieldInfos;
        }

        for (IFieldDescribe fieldDescribe : fieldDescribes) {
            CurlModel.FieldInfo fieldInfo = new CurlModel.FieldInfo(fieldDescribe.getApiName(), fieldDescribe.getLabel(), fieldDescribe.getType());
            fieldInfos.add(fieldInfo);
        }

        return fieldInfos;
    }
}
