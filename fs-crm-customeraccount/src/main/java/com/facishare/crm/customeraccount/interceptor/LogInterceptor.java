package com.facishare.crm.customeraccount.interceptor;

import com.facishare.crm.customeraccount.constants.*;
import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.functions.utils.Maps;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StopWatch;

import java.lang.reflect.Method;
import java.util.*;

public class LogInterceptor {
    private static final String DEFAULT_LOG_NAME = "common-logger";
    private final Logger log;
    private String logName;

    public LogInterceptor(String logName, String rule) {
        if (StringUtils.isEmpty(logName)) {
            logName = DEFAULT_LOG_NAME;
        }
        this.logName = logName;
        this.log = LoggerFactory.getLogger(logName);
    }

    public Object around(ProceedingJoinPoint point) throws Throwable {
        String methodName = point.getSignature().getName();
        String className = point.getTarget().getClass().getSimpleName();
        Method method = point.getTarget().getClass().getMethod(methodName, ((MethodSignature) point.getSignature()).getParameterTypes());
        IgnoreLogInterceptor ignoreLogInterceptor = method.getAnnotation(IgnoreLogInterceptor.class);
        StopWatch totalStopWatch = new StopWatch();
        totalStopWatch.start();
        Object result = null;
        try {
            result = point.proceed();
            Object logResult = toLogResult(result, ignoreLogInterceptor);
            totalStopWatch.stop();
            log.info("{}-{}, cost:{} , args:{}, result:{}", className, methodName, totalStopWatch.getTotalTimeMillis(), point.getArgs(), logResult);
        } catch (Throwable e) {
            totalStopWatch.stop();
            if (e instanceof AppBusinessException) {
                log.warn("{}-{}, cost:{} , args:{}, exception=", className, methodName, totalStopWatch.getTotalTimeMillis(), point.getArgs(), e);
            } else {
                log.error("{}-{}, cost:{} , args:{}, exception=", className, methodName, totalStopWatch.getTotalTimeMillis(), point.getArgs(), e);
            }
            throw e;
        }
        return result;
    }

    public Object toLogResult(Object result, IgnoreLogInterceptor ignoreLogInterceptor) {
        if (Objects.isNull(result)) {
            return null;
        }
        if (Objects.isNull(ignoreLogInterceptor)) {
            return result;
        }
        if (!ignoreLogInterceptor.log()) {
            return "...";
        }
        String[] logFields = getLogFields(ignoreLogInterceptor);
        if (logFields == null || logFields.length == 0) {
            return result;
        }
        if (result instanceof List) {
            List items = (List) result;
            return toLogList(items, logFields);
        } else if (result instanceof Map) {
            Map<String, Object> resultMap = (Map<String, Object>) result;
            return reduceMap(resultMap, logFields);
        } else if (result instanceof Optional) {
            Optional optional = (Optional) result;
            return toLogOptional(optional, logFields);
        } else if (result instanceof IObjectData) {
            IObjectData objectData = (IObjectData) result;
            return toLogObjectData(objectData, logFields);
        }
        return result;
    }

    public String[] getLogFields(IgnoreLogInterceptor ignoreLogInterceptor) {
        if (Objects.isNull(ignoreLogInterceptor)) {
            return new String[0];
        }
        String[] objectApiNames = ignoreLogInterceptor.value();
        Set<String> logFields = Sets.newHashSet("_id", "name", "life_status", "is_deleted");
        if (objectApiNames == null) {
            return logFields.toArray(new String[0]);
        }
        for (String objectApiName : objectApiNames) {
            logFields.addAll(objectLogFieldsMap.getOrDefault(objectApiName, Sets.newHashSet()));
        }
        return logFields.toArray(new String[0]);
    }

    public List toLogList(List result, String[] logFields) {
        List logResultList = Lists.newArrayList();
        for (Object item : result) {
            if (item instanceof Map) {
                Map<String, Object> itemMap = (Map<String, Object>) item;
                logResultList.add(reduceMap(itemMap, logFields));
            } else if (item instanceof IObjectData) {
                logResultList.add(toLogObjectData((IObjectData) item, logFields));
            } else {
                logResultList.add(item);
            }
        }
        return logResultList;
    }

    public Object toLogOptional(Optional optional, String[] logFields) {
        if (!optional.isPresent()) {
            return optional;
        }
        Object value = optional.get();
        if (value instanceof List) {
            return toLogList(((List) value), logFields);
        } else if (value instanceof Map) {
            return reduceMap((Map<String, Object>) value, logFields);
        }
        return optional;
    }

    public Map<String, Object> toLogObjectData(IObjectData objectData, String[] fields) {
        Map<String, Object> logData = Maps.newHashMap();
        for (String logField : fields) {
            if (objectData.containsField(logField)) {
                logData.put(logField, objectData.get(logField));
            }
        }
        return logData;
    }

    public Map<String, Object> reduceMap(Map<String, Object> originalMap, String[] fields) {
        Map<String, Object> logData = Maps.newHashMap();
        for (String logField : fields) {
            if (originalMap.containsKey(logField)) {
                logData.put(logField, originalMap.get(logField));
            }
        }
        return logData;
    }

    static Map<String, Set<String>> objectLogFieldsMap = Maps.newHashMap();

    static {
        Set<String> newCustomerAccountLogFields = Sets.newHashSet(NewCustomerAccountConstants.Field.Name.apiName, NewCustomerAccountConstants.Field.Customer.apiName
                , NewCustomerAccountConstants.Field.FundAccount.apiName, NewCustomerAccountConstants.Field.AccountBalance.apiName
                , NewCustomerAccountConstants.Field.AvailableBalance.apiName, NewCustomerAccountConstants.Field.OccupiedAmount.apiName
                , NewCustomerAccountConstants.Field.CreditQuota.apiName, NewCustomerAccountConstants.Field.CreditOccupiedAmount.apiName);

        Set<String> accountTransactionFlowLogFields = Sets.newHashSet(AccountTransactionFlowConst.Field.Name.apiName, AccountTransactionFlowConst.Field.Customer.apiName
                , AccountTransactionFlowConst.Field.FundAccount.apiName, AccountTransactionFlowConst.Field.CustomerAccount.apiName
                , AccountTransactionFlowConst.Field.AccountBalance.apiName, AccountTransactionFlowConst.Field.RevenueType.apiName
                , AccountTransactionFlowConst.Field.RevenueAmount.apiName, AccountTransactionFlowConst.Field.ExpenseType.apiName
                , AccountTransactionFlowConst.Field.ExpenseAmount.apiName, AccountTransactionFlowConst.Field.RelateRecordObjectApiName.apiName
                , AccountTransactionFlowConst.Field.RelateRecordObjectDataId.apiName, AccountTransactionFlowConst.Field.EntryStatus.apiName);

        Set<String> accountFrozenRecordLogFields = Sets.newHashSet(AccountFrozenRecordConstant.Field.Name.apiName, AccountFrozenRecordConstant.Field.CustomerAccountId.apiName
                , AccountFrozenRecordConstant.Field.AccountId.apiName, AccountFrozenRecordConstant.Field.AccountType.apiName, AccountFrozenRecordConstant.Field.FreezeAmount.apiName
                , AccountFrozenRecordConstant.Field.UnfreezeAmount.apiName, AccountFrozenRecordConstant.Field.LeftFreezeAmount.apiName, AccountFrozenRecordConstant.Field.EntryStatus.apiName
                , AccountFrozenRecordConstant.Field.CheckRecordObjectApiName.apiName, AccountFrozenRecordConstant.Field.CheckRecordObjectDataId.apiName, AccountFrozenRecordConstant.Field.AccountCheckRuleId.apiName);

        Set<String> unfreezeLogFields = Sets.newHashSet(UnfreezeDetailConstant.Field.Name.apiName, UnfreezeDetailConstant.Field.AccountFrozenRecordId.apiName
                , UnfreezeDetailConstant.Field.UnfreezeObjectApiName.apiName, UnfreezeDetailConstant.Field.UnfreezeObjectDataId.apiName
                , UnfreezeDetailConstant.Field.EntryStatus.apiName, UnfreezeDetailConstant.Field.AccountTransactionFlow.apiName
                , UnfreezeDetailConstant.Field.UnfreezeAmount.apiName);

        Set<String> accountRuleUseRecordFields = Sets.newHashSet(AccountRuleUseRecordConstants.Field.Name.apiName, AccountRuleUseRecordConstants.Field.CheckRuleId.apiName
                , AccountRuleUseRecordConstants.Field.CheckRecordObjectApiName.apiName, AccountRuleUseRecordConstants.Field.CheckRecordObjectDataId.apiName
                , AccountRuleUseRecordConstants.Field.RuleType.apiName, AccountRuleUseRecordConstants.Field.RuleStage.apiName);

        objectLogFieldsMap.put(NewCustomerAccountConstants.API_NAME, newCustomerAccountLogFields);
        objectLogFieldsMap.put(AccountTransactionFlowConst.API_NAME, accountTransactionFlowLogFields);
        objectLogFieldsMap.put(AccountFrozenRecordConstant.API_NAME, accountFrozenRecordLogFields);
        objectLogFieldsMap.put(UnfreezeDetailConstant.API_NAME, unfreezeLogFields);
        objectLogFieldsMap.put(AccountRuleUseRecordConstants.API_NAME, accountRuleUseRecordFields);
    }
}
