package com.facishare.crm.customeraccount.util.rule;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.customeraccount.constants.AccountCheckRuleConstants;
import com.facishare.crm.customeraccount.constants.AccountRuleUseRecordConstants;
import com.facishare.crm.customeraccount.enums.AccountCheckRuleTypeEnum;
import com.facishare.crm.customeraccount.enums.ReduceTriggerActionEnum;
import com.facishare.crm.customeraccount.model.RuleFilter;
import com.facishare.crm.customeraccount.model.RuleWhere;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.rule.common.constant.Operate;
import com.facishare.paas.rule.pojo.RulePojo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import de.lab4inf.math.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * 没有计算公式的组件
 * <p>
 * 比如
 * [{"connector":"OR","filters":[{"operator_name":"等于","field_values__s":"预付","field_name__s":"结算方式","type":"select_one","operator":"EQ","field_name":"settle_type","operator__s":"等于","field_values":["1"]},{"operator_name":"等于","field_values__s":"2021-05-27","field_name__s":"下单日期","type":"date","operator":"EQ","field_name":"order_time","operator__s":"等于","field_values":[1622044800000]}]},{"connector":"OR","filters":[{"operator_name":"属于","field_values__s":"1级部门","field_name__s":"负责人所在部门","type":"department","operator":"IN","field_name":"owner_department","operator__s":"属于","field_values":["1003"]}]}]
 */
@Slf4j
public class RuleUtil {
    public static List<RulePojo> getRuleConditions(IObjectData objectData, String fieldApiName) {
        List<RulePojo> rules = Lists.newArrayList();
        String fieldJson = (String) objectData.get(fieldApiName);
        if (com.google.common.base.Strings.isNullOrEmpty(fieldJson) || fieldJson.equals("[]") || fieldJson.equals("{}")) {
            return rules;
        }
        rules = getRuleGroup(objectData, fieldApiName);
        return rules;
    }

    public static List<RulePojo> getRuleGroup(IObjectData objectData, String fieldApiName) {
        String fieldJson = (String) objectData.get(fieldApiName);
        List<RulePojo> rules;
        if (Objects.equals(fieldApiName, AccountCheckRuleConstants.Field.CheckRule.apiName)) {
            RuleUtil2.Rule checkRule = JSON.parseObject(fieldJson, RuleUtil2.Rule.class);
            rules = RuleUtil2.getRules(checkRule);
        } else {
            List<RuleWhere> ruleWheres = getRuleWheres(fieldJson);
            rules = getRules(ruleWheres);
            log.info("getRules ruleWheres[{}], rules[{}]", ruleWheres, rules);
        }

        return rules;
    }

    public static List<RulePojo> getRules(List<RuleWhere> ruleWheres) {
        return getRulesWithInitOrder(ruleWheres, 1);
    }

    public static List<RulePojo> getRulesWithInitOrder(List<RuleWhere> ruleWheres, int initOrder) {
        if (CollectionUtils.empty(ruleWheres)) {
            return Lists.newArrayList();
        }
        List<RulePojo> rules = new ArrayList<>();
        int ruleOrder = initOrder;
        for (RuleWhere ruleWhere : ruleWheres) {
            List<RuleFilter> filters = ruleWhere.getFilters();
            if (CollectionUtils.empty(filters)) {
                continue;
            }
            for (RuleFilter filter : filters) {
                RulePojo rule = new RulePojo();
                rule.setRuleOrder(ruleOrder);
                rule.setOperate(filter.getOperator().name());
                rule.setFieldName(filter.getFieldName());
                rule.setFieldType(filter.getFieldType());
                rule.setFieldValue(filter.getFieldValues());
                rules.add(rule);
                ruleOrder++;
            }
        }
        return rules;
    }

    /**
     * 获取 ruleParser 比如  ( (1) or (2 and 3) )
     */
    public static String getRuleParse(List<RuleWhere> ruleWheres) {
        return getRuleParseWithInitOrder(ruleWheres, 1);
    }

    public static String getRuleParseWithInitOrder(List<RuleWhere> ruleWheres, int initOrder) {
        if (CollectionUtils.empty(ruleWheres)) {
            return "()";
        }
        int ruleOrder = initOrder;
        StringBuffer ruleParse = new StringBuffer();
        if (ruleWheres.size() > 1) {
            ruleParse.append("(");
        }
        for (int i = 0; i < ruleWheres.size(); i++) {
            RuleWhere ruleWhere = ruleWheres.get(i);
            if (CollectionUtils.empty(ruleWhere.getFilters())) {
                continue;
            }
            if (i != 0) {
                ruleParse.append(" or ");
            }
            ruleParse.append(getSubRuleParse(ruleWhere, ruleOrder));
            ruleOrder = ruleOrder + ruleWhere.getFilters().size();
        }

        if (ruleWheres.size() > 1) {
            ruleParse.append(")");
        }
        return ruleParse.toString();
    }

    private static String getSubRuleParse(RuleWhere ruleWhere, int ruleOrder) {
        StringBuffer ruleParse = new StringBuffer();
        ruleParse.append("(");

        for (int i = 0; i < ruleWhere.getFilters().size(); i++) {
            if (i != 0) {
                ruleParse.append(" and ");
            }
            ruleParse.append(ruleOrder);
            ruleOrder++;
        }

        ruleParse.append(")");
        return ruleParse.toString();
    }

    public static List<RuleWhere> getRuleWheres(String ruleCondition) {
        if (Strings.isNullOrEmpty(ruleCondition)) {
            return Lists.newArrayList();
        }
        List<RuleWhere> ruleWheres = Lists.newArrayList();
        List<String> conditions = JSON.parseArray(ruleCondition, String.class);
        if (CollectionUtils.notEmpty(conditions)) {
            for (String condition : conditions) {
                if (Strings.isNullOrEmpty(condition)) {
                    continue;
                }
                ruleWheres.add(JSON.parseObject(condition, RuleWhere.class));
            }
        }
        return ruleWheres;
    }

    public static boolean needRollback(IObjectData checkRuleData, String lifeStatus) {
        String ruleType = checkRuleData.get(AccountCheckRuleConstants.Field.RuleType.apiName, String.class);
        if (AccountCheckRuleTypeEnum.Check_Reduce.getValue().equals(ruleType)) {
            String checkTriggerAction = checkRuleData.get(AccountCheckRuleConstants.Field.CheckTriggerAction.apiName, String.class);
            if (ReduceTriggerActionEnum.Button.getValue().equals(checkTriggerAction)) {
                //校验扣减的按钮触发
                String checkTriggerButton = checkRuleData.get(AccountCheckRuleConstants.Field.CheckTriggerButton.apiName, String.class);
                return ObjectAction.CREATE.getButtonApiName().equals(checkTriggerButton) && frozenRuleConditionHasLifeStatusEqNormal(checkRuleData) && !ObjectLifeStatus.NORMAL.getCode().equals(lifeStatus);
            } else {
                //校验扣减的字段变更触发
                return !ObjectLifeStatus.NORMAL.getCode().equals(lifeStatus);
            }
        } else if (AccountCheckRuleTypeEnum.Direct_Reduce.getValue().equals(ruleType)) {
            String reduceTriggerAction = checkRuleData.get(AccountCheckRuleConstants.Field.ReduceTriggerAction.apiName, String.class);
            if (ReduceTriggerActionEnum.Button.getValue().equals(reduceTriggerAction)) {
                //直接扣减的按钮触发
                String reduceTriggerButton = checkRuleData.get(AccountCheckRuleConstants.Field.ReduceTriggerButton.apiName, String.class);
                return ObjectAction.CREATE.getButtonApiName().equals(reduceTriggerButton) && !ObjectLifeStatus.NORMAL.getCode().equals(lifeStatus);
            } else {
                //直接扣减的字段变更触发
                return !ObjectLifeStatus.NORMAL.getCode().equals(lifeStatus);
            }
        }
        return false;
    }

    public static boolean frozenConditionHasLifeStatusEqNormal(IObjectData accountRuleUseRecordData) {
        String json = accountRuleUseRecordData.get(AccountRuleUseRecordConstants.Field.CheckRule.apiName, String.class);
        IObjectData accountCheckRuleData = new ObjectData();
        accountCheckRuleData.fromJsonString(json);
        return frozenRuleConditionHasLifeStatusEqNormal(accountCheckRuleData);
    }

    public static boolean frozenRuleConditionHasLifeStatusEqNormal(IObjectData accountCheckRuleData) {
        List<RulePojo> conditionList = Lists.newArrayList();
        String ruleType = accountCheckRuleData.get(AccountCheckRuleConstants.Field.RuleType.apiName, String.class);
        if (AccountCheckRuleTypeEnum.Check_Reduce.getValue().equals(ruleType)) {
            String triggerCondition = accountCheckRuleData.get(AccountCheckRuleConstants.Field.TriggerCondition.apiName, String.class);
            List<RulePojo> triggerConditionList = getRulesByCondition(AccountCheckRuleConstants.Field.TriggerCondition.apiName, triggerCondition);

            String checkRuleCondition = accountCheckRuleData.get(AccountCheckRuleConstants.Field.CheckRule.apiName, String.class);
            List<RulePojo> checkRuleConditionList = getRulesByCondition(AccountCheckRuleConstants.Field.CheckRule.apiName, checkRuleCondition);

            conditionList.addAll(triggerConditionList);
            conditionList.addAll(checkRuleConditionList);
        }
        return conditionList.stream().anyMatch(x -> {
            List<String> values = CollectionUtils.nullToEmpty(x.getFieldValue());
            return "life_status".equals(x.getFieldName()) && Operate.EQ.equals(x.getOperate()) && values.size() == 1 && values.contains(ObjectLifeStatus.NORMAL.getCode());
        });
    }

    public static void parseRuleReferenceField(Map<String, Object> dataMap, List<RulePojo> rulePojos, Map<String, String> sourceField2TargetObjectMap, Map<String, Set<String>> targetObject2SourceFieldMap, Map<String, Set<String>> targetObject2IdsMap) {
        for (RulePojo rule : rulePojos) {
            String fieldName = rule.getFieldName();
            if (fieldName.contains(".")) {
                String[] fields = fieldName.split("\\.");
                String sourceField = fields[0];
                String targetApiName = sourceField2TargetObjectMap.get(sourceField);
                Object value = dataMap.get(sourceField);
                if (value != null && !value.toString().isEmpty()) {
                    Set<String> sourceFields = targetObject2SourceFieldMap.computeIfAbsent(targetApiName, k -> Sets.newHashSet());
                    sourceFields.add(sourceField);
                    Set<String> dataIds = targetObject2IdsMap.computeIfAbsent(targetApiName, k -> Sets.newHashSet());
                    dataIds.add(value.toString());
                }
            }
            List<String> fieldValues = rule.getFieldValue();
            for (String fieldValue : fieldValues) {
                if (!fieldValue.contains(".")) {
                    continue;
                }
                sourceField2TargetObjectMap.forEach((apiName, targetApiName) -> {
                    Object value = dataMap.get(apiName);
                    if (fieldValue.contains(apiName) && value != null && !value.toString().isEmpty()) {
                        Set<String> sourceFields = targetObject2SourceFieldMap.computeIfAbsent(targetApiName, k -> Sets.newHashSet());
                        sourceFields.add(apiName);
                        Set<String> dataIds = targetObject2IdsMap.computeIfAbsent(targetApiName, k -> Sets.newHashSet());
                        dataIds.add(value.toString());
                    }
                });
            }
        }
    }

    public static List<RulePojo> getRulesByCondition(String conditionFieldName, String condition) {
        List<RulePojo> rules;
        if (AccountCheckRuleConstants.Field.CheckRule.apiName.equals(conditionFieldName)) {
            RuleUtil2.Rule checkRule = JSON.parseObject(condition, RuleUtil2.Rule.class);
            rules = Objects.isNull(checkRule) ? Lists.newArrayList() : RuleUtil2.getRules(checkRule);
        } else {
            List<RuleWhere> ruleWheres = getRuleWheres(condition);
            rules = getRules(ruleWheres);
        }
        return rules;
    }

    //call by fs-crm-task
    public static List<IObjectData> filterCheckRuleByUpdateEvent(List<IObjectData> accountCheckRuleDataList, Map<String, Object> beforeTriggerData, Map<String, Object> afterTriggerData, List<String> conditionFieldNames) {
        List<IObjectData> ruleResultList = Lists.newArrayList();
        if (CollectionUtils.empty(accountCheckRuleDataList)) {
            return ruleResultList;
        }
        for (IObjectData checkRuleData : accountCheckRuleDataList) {
            for (String conditionFieldName : conditionFieldNames) {
                if (needMatch(checkRuleData, beforeTriggerData, afterTriggerData, conditionFieldName)) {
                    ruleResultList.add(checkRuleData);
                    break;
                }
            }
        }
        return ruleResultList;
    }

    public static boolean needMatch(IObjectData accountCheckRuleData, Map<String, Object> beforeTriggerData, Map<String, Object> afterTriggerData, String conditionFieldName) {
        String condition = accountCheckRuleData.get(conditionFieldName, String.class, "");
        if (StringUtils.isNotEmpty(condition)) {
            if (Objects.nonNull(beforeTriggerData) && beforeTriggerData.containsKey(ObjectLifeStatus.LIFE_STATUS_API_NAME) || Objects.nonNull(afterTriggerData) && afterTriggerData.containsKey(ObjectLifeStatus.LIFE_STATUS_API_NAME)) {
                return true;
            }
            List<RulePojo> rules = getRulesByCondition(conditionFieldName, condition);
            return rules.stream().anyMatch(x -> {
                String fieldName = x.getFieldName();
                return (Objects.nonNull(beforeTriggerData) && beforeTriggerData.containsKey(fieldName)) || (Objects.nonNull(afterTriggerData) && afterTriggerData.containsKey(fieldName));
            });
        }
        return true;
    }
}