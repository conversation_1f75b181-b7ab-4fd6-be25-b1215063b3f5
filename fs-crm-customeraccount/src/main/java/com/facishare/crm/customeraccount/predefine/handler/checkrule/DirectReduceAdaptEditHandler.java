package com.facishare.crm.customeraccount.predefine.handler.checkrule;

import com.facishare.crm.customeraccount.constants.AccountTransactionFlowConst;
import com.facishare.crm.customeraccount.constants.UnfreezeDetailConstant;
import com.facishare.crm.customeraccount.enums.EntryStatusEnum;
import com.facishare.crm.customeraccount.enums.ExpenseTypeEnum;
import com.facishare.crm.customeraccount.predefine.handler.RuleHandlerUtil;
import com.facishare.crm.customeraccount.predefine.manager.AccountCheckManager;
import com.facishare.crm.customeraccount.predefine.manager.NewCustomerAccountManager;
import com.facishare.crmcommon.util.SearchUtil;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class DirectReduceAdaptEditHandler extends CheckRuleHandler<AdaptEditArg, AdaptEditResult> {
    @Autowired
    private AccountCheckManager accountCheckManager;
    @Autowired
    private NewCustomerAccountManager newCustomerAccountManager;

    @Override
    public HandlerTypeEnum getHandlerTypeEnum() {
        return HandlerTypeEnum.DirectReduceAdaptEdit;
    }

    @Override
    protected AdaptEditResult doHandle(AdaptEditArg arg) {
        User user = arg.getUser();
        IObjectData objectData = arg.getObjectData();
        IObjectData accountRuleUseRecordData = arg.getAccountRuleUseRecordData();
        Map<CustomerFundAccount, IObjectData> newCustomerFundAccount2FlowDataMap = newCustomerAccountManager.getCustomerFundAccountFlowMap(user, objectData, accountRuleUseRecordData);

        String objectApiName = objectData.getDescribeApiName();
        String objectDataId = objectData.getId();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, AccountTransactionFlowConst.Field.RelateRecordObjectApiName.apiName, objectApiName);
        SearchUtil.fillFilterEq(filters, AccountTransactionFlowConst.Field.RelateRecordObjectDataId.apiName, objectDataId);
        SearchUtil.fillFilterEq(filters, AccountTransactionFlowConst.Field.EntryStatus.apiName, EntryStatusEnum.AlreadyEntry.getValue());
        SearchUtil.fillFilterIn(filters, AccountTransactionFlowConst.Field.ExpenseType.apiName, Lists.newArrayList(ExpenseTypeEnum.ValidateDeduct.getValue(), ExpenseTypeEnum.DirectRuleChargeOff.getValue()));
        //流水可能包含 解冻明细关联的流水
        List<IObjectData> flowDataList = accountCheckManager.query(user, AccountTransactionFlowConst.API_NAME, filters, Lists.newArrayList());
        if (RuleHandlerUtil.isChargedOff(flowDataList)) {
            //红冲过，就不再适配编辑
            return new AdaptEditResult();
        }
        filters.clear();
        SearchUtil.fillFilterEq(filters, UnfreezeDetailConstant.Field.UnfreezeObjectApiName.apiName, objectApiName);
        SearchUtil.fillFilterEq(filters, UnfreezeDetailConstant.Field.UnfreezeObjectDataId.apiName, objectDataId);
        SearchUtil.fillFilterEq(filters, UnfreezeDetailConstant.Field.EntryStatus.apiName, EntryStatusEnum.AlreadyEntry.getValue());
        List<IObjectData> unfreezeDataList = accountCheckManager.query(user, UnfreezeDetailConstant.API_NAME, filters, Lists.newArrayList());
        Set<String> flowIdsOfUnfreeze = unfreezeDataList.stream().map(x -> x.get(UnfreezeDetailConstant.Field.AccountTransactionFlow.apiName, String.class)).collect(Collectors.toSet());

        List<CustomerFundAccount> cfList = Lists.newArrayList(newCustomerFundAccount2FlowDataMap.keySet());
        List<IObjectData> actualFlowDataList = Lists.newArrayList();
        for (IObjectData oldFlowData : flowDataList) {
            String flowId = oldFlowData.getId();
            if (flowIdsOfUnfreeze.contains(flowId)) {
                //排除解冻明细关联的流水
                continue;
            }
            actualFlowDataList.add(oldFlowData);
            String customerId = oldFlowData.get(AccountTransactionFlowConst.Field.Customer.apiName, String.class);
            String fundAccountId = oldFlowData.get(AccountTransactionFlowConst.Field.FundAccount.apiName, String.class);
            CustomerFundAccount cf = CustomerFundAccount.of(customerId, fundAccountId);
            cfList.add(cf);
        }
        Map<CustomerFundAccount, IObjectData> customerFundAccountIObjectDataMap = newCustomerAccountManager.batchGetOrCreateNewCustomerAccount(arg.getRequestContext(), cfList);

        Set<CustomerFundAccount> processedCfList = Sets.newHashSet();

        AdaptEditResult editResult = new AdaptEditResult();
        for (IObjectData oldFlowData : actualFlowDataList) {
            String customerId = oldFlowData.get(AccountTransactionFlowConst.Field.Customer.apiName, String.class);
            String fundAccountId = oldFlowData.get(AccountTransactionFlowConst.Field.FundAccount.apiName, String.class);
            CustomerFundAccount cf = CustomerFundAccount.of(customerId, fundAccountId);
            IObjectData toFlowData = newCustomerFundAccount2FlowDataMap.get(cf);
            IObjectData customerAccountData = customerFundAccountIObjectDataMap.get(cf);

            BigDecimal oldFlowAmount = oldFlowData.get(AccountTransactionFlowConst.Field.ExpenseAmount.apiName, BigDecimal.class, BigDecimal.ZERO);
            BigDecimal toFlowAmount = Objects.isNull(toFlowData) ? BigDecimal.ZERO : toFlowData.get(AccountTransactionFlowConst.Field.ExpenseAmount.apiName, BigDecimal.class, BigDecimal.ZERO);

            ReduceAmountChanger reduceAmountChanger = new ReduceAmountChanger(oldFlowData, toFlowData, customerAccountData);
            AmountChangerResult amountChangerResult = AmountChangeExecutor.execute(0, toFlowAmount.compareTo(oldFlowAmount), reduceAmountChanger);
            editResult.merge(amountChangerResult);
            processedCfList.add(cf);
        }

        for (Map.Entry<CustomerFundAccount, IObjectData> entry : newCustomerFundAccount2FlowDataMap.entrySet()) {
            CustomerFundAccount cf = entry.getKey();
            if (processedCfList.contains(cf)) {
                continue;
            }
            processedCfList.add(cf);
            IObjectData toFlowData = entry.getValue();

            BigDecimal toFlowAmount = toFlowData.get(AccountTransactionFlowConst.Field.ExpenseAmount.apiName, BigDecimal.class, BigDecimal.ZERO);
            BigDecimal oldFlowAmount = BigDecimal.ZERO;

            IObjectData customerAccountData = customerFundAccountIObjectDataMap.get(cf);

            ReduceAmountChanger reduceAmountChanger = new ReduceAmountChanger(null, toFlowData, customerAccountData);
            AmountChangerResult amountChangerResult = AmountChangeExecutor.execute(0, toFlowAmount.compareTo(oldFlowAmount), reduceAmountChanger);
            editResult.merge(amountChangerResult);
        }
        return editResult;
    }
}
