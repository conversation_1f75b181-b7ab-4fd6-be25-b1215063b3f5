package com.facishare.crm.customeraccount.predefine.service.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

public class CanUseRebateFundAccountModel {

    @Data
    public static class Arg {
        private String authorizedObjectApiName;
        private String authorizedType;

    }

    @AllArgsConstructor
    @Data
    public static class Result {
        private boolean canUseRebateFundAccount;
    }
}