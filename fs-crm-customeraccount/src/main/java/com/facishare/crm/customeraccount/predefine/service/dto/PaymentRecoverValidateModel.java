package com.facishare.crm.customeraccount.predefine.service.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

public class PaymentRecoverValidateModel {
    @Data
    public static class Arg {
        /**
         * key:paymentId, value:orderPaymentId
         */
        private Map<String, List<String>> paymentOrderPaymentIdMap;
    }

    @Data
    public static class Result {
        /** true全部通过校验，false存在未通过校验的回款 **/
        private Boolean validateResult = Boolean.TRUE;

        /** 存在未通过校验的回款时，返回的提示文案 **/
        private String message;

        /** key:paymentId, value:true 通过校验，可以恢复，false 未通过校验不可恢复 */
        private Map<String, Boolean> paymentIdResultMap;
    }
}
