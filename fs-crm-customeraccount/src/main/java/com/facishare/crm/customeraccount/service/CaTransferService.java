package com.facishare.crm.customeraccount.service;

import com.facishare.crm.customeraccount.service.transfer.BizTransferByEi;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@Slf4j
@Component
@ServiceModule("ca_upgrade")
public class CaTransferService {
    private final Map<BizTransferTypeEnum, BizTransferByEi> bizTransferTypeEnumMap = Maps.newHashMap();

    public CaTransferService(List<BizTransferByEi> bizTransferByEiList) {
        for (BizTransferByEi bizTransferByEi : CollectionUtils.nullToEmpty(bizTransferByEiList)) {
            bizTransferTypeEnumMap.put(bizTransferByEi.getType(), bizTransferByEi);
        }
    }

    @ServiceMethod("by_tenants")
    public Map<String, Object> batchUpgradeByTenant(ServiceContext serviceContext, UpdateByTypeByEisArg arg) {
        Map<String, Object> resultMap = Maps.newHashMap();
        BizTransferByEi bizTransferByEi = bizTransferTypeEnumMap.get(BizTransferTypeEnum.ofType(arg.getType()));
        if (Objects.isNull(bizTransferByEi)) {
            resultMap.put(arg.getType(), "transfer not exist");
        } else {
            Set<String> failEis = executeByEi(arg.getTenantIds(), arg.getType(), ei -> {
                User user = User.systemUser(ei);
                bizTransferByEi.transfer(user);
            });
            resultMap.put("failEsi", failEis);
        }
        return resultMap;
    }

    private Set<String> executeByEi(Set<String> tenantIds, String type, EiFunction function) {
        Set<String> failEis = Sets.newHashSet();
        for (String tenantId : tenantIds) {
            try {
                function.run(tenantId);
            } catch (Exception e) {
                failEis.add(tenantId);
                log.warn("execute type:{},tenantId:{} fail", type, tenantId, e);
            }
        }
        log.info("finish execute type:{},failEis:{}", type, failEis);
        return failEis;
    }

    interface EiFunction {
        void run(String tenantId);
    }

    @Data
    public static class UpdateByTypeByEisArg {
        private Set<String> tenantIds;
        private String type;
    }

}
