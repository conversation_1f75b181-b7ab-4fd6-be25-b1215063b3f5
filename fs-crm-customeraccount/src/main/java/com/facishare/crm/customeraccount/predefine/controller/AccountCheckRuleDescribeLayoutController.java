package com.facishare.crm.customeraccount.predefine.controller;

import cn.hutool.core.util.BooleanUtil;
import com.facishare.crm.customeraccount.constants.AccountCheckRuleConstants;
import com.facishare.crm.customeraccount.enums.AccountCheckRuleTypeEnum;
import com.facishare.crm.customeraccount.util.LayoutUtil;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.google.common.collect.Sets;

import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

public class AccountCheckRuleDescribeLayoutController extends StandardDescribeLayoutController {
    @Override
    protected boolean supportSaveDraft() {
        return false;
    }


    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        String layoutType = arg.getLayout_type();
        ObjectDescribeDocument objectDescribeDocument = result.getObjectDescribe();
        ObjectDescribeExt objectDescribeExt = ObjectDescribeExt.of(objectDescribeDocument);

        if ("edit".equals(layoutType)) {
            LayoutDocument layoutDocument = LayoutDocument.of(result.getLayout());

            HashSet<String> fieldSet = Sets.newHashSet();
            fieldSet.add(AccountCheckRuleConstants.Field.RuleType.apiName);
            LayoutUtil.setReadOnly(layoutDocument, fieldSet, true);

            result.setLayout(layoutDocument);
        } else if ("add".equals(layoutType)) {
            SelectOneFieldDescribe ruleTypeFieldDescribe = (SelectOneFieldDescribe) objectDescribeExt.getFieldDescribe(AccountCheckRuleConstants.Field.RuleType.apiName);
            List<ISelectOption> ruleTypeOptions = ruleTypeFieldDescribe.getSelectOptions();
            List<ISelectOption> ruleTypeOptionsAfterFilter = ruleTypeOptions.stream().filter(option -> BooleanUtil.isFalse(AccountCheckRuleTypeEnum.Component_Reduce.getLabel().equals(option.getLabel()))).collect(Collectors.toList());
            ruleTypeFieldDescribe.setSelectOptions(ruleTypeOptionsAfterFilter);
        }

        return result;
    }
}