package com.facishare.crm.customeraccount.predefine.domainplugin.enteraccount;

import com.facishare.crm.customeraccount.constants.CAI18NKey;
import com.facishare.crm.customeraccount.exception.FundAccountErrorCode;
import com.facishare.crm.customeraccount.exception.FundAccountException;
import com.facishare.crm.customeraccount.predefine.domainplugin.processor.AbstractPluginProcessor;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.model.FundAccountBaseService;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.domain.BulkInvalidActionDomainPlugin;
import com.facishare.paas.appframework.core.predef.domain.EditActionDomainPlugin;
import com.facishare.paas.appframework.core.predef.domain.InvalidActionDomainPlugin;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class CustomObjectEnterAccountPluginProcessor extends AbstractPluginProcessor {
    private final ServiceFacade serviceFacade;
    private final FundAccountBaseService fundAccountBaseService;

    public CustomObjectEnterAccountPluginProcessor(ServiceFacade serviceFacade, FundAccountBaseService fundAccountBaseService) {
        this.serviceFacade = serviceFacade;
        this.fundAccountBaseService = fundAccountBaseService;
    }

    @Override
    public EditActionDomainPlugin.Result editBefore(RequestContext requestContext, EditActionDomainPlugin.Arg arg) {
        EditActionDomainPlugin.Result result = new EditActionDomainPlugin.Result();

        User user = requestContext.getUser();
        String objectApiName = arg.getObjectApiName();
        String enterIntoAccountFieldName = FundAccountBaseService.Const.ENTER_INTO_ACCOUNT;
        IObjectData dbObjectData = arg.getDbMasterData().toObjectData();
        IObjectData objectData = arg.getObjectData().toObjectData();
        boolean dbEnterIntoAccount = dbObjectData.get(enterIntoAccountFieldName, Boolean.class, Boolean.FALSE);

        IObjectDescribe objectDescribe = serviceFacade.findObject(user.getTenantId(), objectApiName);
        Map<String, IFieldDescribe> fieldDescribeMap = objectDescribe.getFieldDescribeMap();

        List<String> fieldLabelNotEditList = Lists.newArrayList();
        boolean enterIntoAccount = objectData.get(enterIntoAccountFieldName, Boolean.class, Boolean.FALSE);
        if (enterIntoAccount != dbEnterIntoAccount && objectData.containsField(enterIntoAccountFieldName)) {
            log.warn("user:{},enterIntoAccountField:{},enterIntoAccount:{},dbEnterIntoAccount:{}", user, enterIntoAccountFieldName, enterIntoAccount, dbEnterIntoAccount);
            String fieldLabel = fieldDescribeMap.get(enterIntoAccountFieldName).getLabel();
            fieldLabelNotEditList.add(fieldLabel);
        }
        if (dbEnterIntoAccount) {
            //已入账 客户、入账金额不可编辑校验
            FundAccountBaseService.FundAuthConfigModel accessAuthModel = fundAccountBaseService.getAccessAuthInfo(user, objectApiName);
            String customerFieldName = accessAuthModel.getCustomerFieldName();
            String customerId = objectData.get(customerFieldName, String.class);
            String dbCustomerId = dbObjectData.get(customerFieldName, String.class);
            if (!StringUtils.equals(dbCustomerId, customerId) && objectData.containsField(customerFieldName)) {
                log.warn("user:{},customerFieldName:{},customerId:{},dbCustomerId:{}", user, customerFieldName, customerId, dbCustomerId);
                String fieldLabel = fieldDescribeMap.get(customerFieldName).getLabel();
                fieldLabelNotEditList.add(fieldLabel);
            }
            String enterAccountAmountFieldName = accessAuthModel.getEnterAccountAmountFieldName();
            BigDecimal enterAccountAmount = objectData.get(enterAccountAmountFieldName, BigDecimal.class, BigDecimal.ZERO);
            BigDecimal dbEnterAccountAmount = dbObjectData.get(enterAccountAmountFieldName, BigDecimal.class, BigDecimal.ZERO);
            if (objectData.containsField(enterAccountAmountFieldName) && enterAccountAmount.compareTo(dbEnterAccountAmount) != 0 && !"RebateObj".equals(objectApiName)) {
                log.warn("user:{},enterAccountAmountFieldName:{},enterAccountAmount:{},dbEnterAccountAmount:{}", user, enterAccountAmountFieldName, enterAccountAmount, dbEnterAccountAmount);
                String fieldLabel = fieldDescribeMap.get(enterAccountAmountFieldName).getLabel();
                fieldLabelNotEditList.add(fieldLabel);
            }
            String dbFundAccountId = dbObjectData.get(FundAccountBaseService.Const.FUND_ACCOUNT, String.class);
            String fundAccountId = objectData.get(FundAccountBaseService.Const.FUND_ACCOUNT, String.class);
            if (objectData.containsField(FundAccountBaseService.Const.FUND_ACCOUNT) && !StringUtils.equals(fundAccountId, dbFundAccountId)) {
                log.warn("user:{},fundAccountId:{},dbFundAccountId:{}", user, fundAccountId, dbFundAccountId);
                String fieldLabel = fieldDescribeMap.get(FundAccountBaseService.Const.FUND_ACCOUNT).getLabel();
                fieldLabelNotEditList.add(fieldLabel);
            }
        }
        if (!fieldLabelNotEditList.isEmpty()) {
            throw new FundAccountException(I18N.text(CAI18NKey.CANNOT_EDIT, String.join(",", fieldLabelNotEditList)), FundAccountErrorCode.FUND_ACCOUNT_ACCESS_NOT_EDIT.getCode());
        }

        return result;
    }

    @Override
    public InvalidActionDomainPlugin.Result invalidBefore(RequestContext requestContext, InvalidActionDomainPlugin.Arg arg) {
        InvalidActionDomainPlugin.Result result = new InvalidActionDomainPlugin.Result();
        validateByInvalid(requestContext.getUser(), arg.getObjectData().toObjectData());
        return result;
    }

    @Override
    public BulkInvalidActionDomainPlugin.Result bulkInvalidBefore(RequestContext requestContext, BulkInvalidActionDomainPlugin.Arg arg) {
        BulkInvalidActionDomainPlugin.Result result = new BulkInvalidActionDomainPlugin.Result();
        arg.getObjectDataList().forEach(x -> validateByInvalid(requestContext.getUser(), x.toObjectData()));
        return result;
    }

    void validateByInvalid(User user, IObjectData objectData) {
        String enterIntoAccountFieldName = FundAccountBaseService.Const.ENTER_INTO_ACCOUNT;
        boolean dbEnterIntoAccount = objectData.get(enterIntoAccountFieldName, Boolean.class, Boolean.FALSE);
        if (dbEnterIntoAccount) {
            log.warn("already enterIntoAccount not invalid,user:{},enterIntoAccountFieldName:{}", user, enterIntoAccountFieldName);
            throw new FundAccountException(I18N.text(CAI18NKey.ALREADY_ENTER_ACCOUNT_NOT_INVALID), FundAccountErrorCode.FUND_ACCOUNT_ACCESS_NOT_EDIT.getCode());
        }
    }
}
