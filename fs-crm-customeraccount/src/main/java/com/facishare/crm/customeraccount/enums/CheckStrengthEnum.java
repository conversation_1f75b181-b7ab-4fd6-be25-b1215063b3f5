package com.facishare.crm.customeraccount.enums;

import java.util.Arrays;
import java.util.Optional;

public enum CheckStrengthEnum {
    CancelTransaction("1", "取消交易"),
    AlertNotification("2", "预警提示");

    public final String value;
    public final String label;

    CheckStrengthEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public static Optional<CheckStrengthEnum> of(String value) {
        return Arrays.stream(values()).filter(x -> x.value.equals(value)).findAny();
    }
}
