package com.facishare.crm.customeraccount.predefine.action;

import com.facishare.crm.customeraccount.constants.CAI18NKey;
import com.facishare.crm.customeraccount.constants.NewCustomerAccountConstants;
import com.facishare.crm.customeraccount.predefine.manager.NewCustomerAccountManager;
import com.facishare.crmcommon.util.SearchUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class NewCustomerAccountAddAction extends StandardAddAction {
    private NewCustomerAccountManager newCustomerAccountManager = SpringUtil.getContext().getBean(NewCustomerAccountManager.class);

    @Override
    protected void before(Arg arg) {
        if (!supportCreate()) {
            throw new ValidateException(I18N.text(CAI18NKey.CUSTOMERACCOUNTBALANCE_DOESNOT_SUPPORT_USER_CREATION));
        }
        super.before(arg);
        String fundAccountId = this.objectData.get(NewCustomerAccountConstants.Field.FundAccount.apiName, String.class);
        String customerId = this.objectData.get(NewCustomerAccountConstants.Field.Customer.apiName, String.class);
        if (StringUtils.isEmpty(fundAccountId) || StringUtils.isEmpty(customerId)) {
            throw new ValidateException(I18N.text(CAI18NKey.PARAMS_ERROR));
        }
        List<IFilter> filterList = Lists.newArrayList();
        SearchUtil.fillFilterEq(filterList, NewCustomerAccountConstants.Field.FundAccount.apiName, fundAccountId);
        SearchUtil.fillFilterEq(filterList, NewCustomerAccountConstants.Field.Customer.apiName, customerId);
        QueryResult<IObjectData> customerAccountResult = newCustomerAccountManager.searchQueryByDbIgnoreAll(actionContext.getUser(), NewCustomerAccountConstants.API_NAME, filterList, Lists.newArrayList(), 0, 1);
        List<IObjectData> customerAccountList = customerAccountResult.getData();
        if (CollectionUtils.notEmpty(customerAccountList)) {
            throw new ValidateException(I18N.text(CAI18NKey.CUSTOMER_ALREADY_EXIST_CUSTOMER_ACCOUNT, customerAccountList.get(0).getName()));
        }
    }

    @Override
    protected boolean needTriggerApprovalFlow() {
        if (supportCreate()) {
            return false;
        }
        return super.needTriggerApprovalFlow();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {

        if (supportCreate()) {
            return Lists.newArrayList();
        }
        return super.getFuncPrivilegeCodes();
    }

    private boolean supportCreate() {
        String resource = this.actionContext.getRequestContext().getAttribute("innerRequestResourceWhereFrom");
        return StringUtils.equals(resource, "fs-crm-inner");
    }
}
