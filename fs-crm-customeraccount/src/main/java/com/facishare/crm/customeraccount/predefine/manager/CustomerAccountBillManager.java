package com.facishare.crm.customeraccount.predefine.manager;

import com.facishare.crm.customeraccount.entity.CustomerAccountBill;
import com.facishare.crm.customeraccount.entity.CustomerAccountBillStatistics;
import com.facishare.crm.customeraccount.enums.BillTypeEnum;
import com.facishare.crmcommon.rest.CustomerAccountBillProxy;
import com.facishare.crmcommon.rest.dto.CustomerAccountBillModel;
import com.facishare.enterprise.common.util.JsonUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @IgnoreI18n
 */
@Slf4j
@Component
public class CustomerAccountBillManager {

    @Autowired
    private CustomerAccountBillProxy billProxy;


    /**
     * 把sourceAccountId对应的客户账户流水合并到destAccountId对应的客户账户下<br>
     * 合并流水的具体流程为：<br>
     * 1.查询出sourceBillList.
     * 2.遍历sourceBillList,在原客户账户明细下新生成一条冲销流水，克隆原明细下的流水修改其客户账户Id<br>
     * FIXME 如果部分成功，我们通过事物保证。
     * @param sourceAccountIds
     * @param destAccountId
     */
    public void mergeBill(User user, List<String> sourceAccountIds, String destAccountId) {
        List<CustomerAccountBill> toAddBillList = Lists.newArrayList();
        for (String sourceAccountId : sourceAccountIds) {
            CustomerAccountBillModel.CountArg countArg = new CustomerAccountBillModel.CountArg();
            countArg.setTenantId(user.getTenantId());
            countArg.setCustomerAccountId(sourceAccountId);
            CustomerAccountBillModel.CountResult countResult = billProxy.count(countArg);
            if (countResult.isSuccess()) {
                CustomerAccountBillStatistics customerAccountBillStatistics = new CustomerAccountBillStatistics();
                customerAccountBillStatistics.setTenantId(countResult.getValue().getTenantId());
                customerAccountBillStatistics.setCustomerAccountId(countResult.getValue().getCustomerAccountId());
                customerAccountBillStatistics.setPrepayAmountChange(countResult.getValue().getPrepayAmountChange());
                customerAccountBillStatistics.setPrepayLockedAmountChange(countResult.getValue().getPrepayLockedAmountChange());
                customerAccountBillStatistics.setRebateAmountChange(countResult.getValue().getRebateAmountChange());
                customerAccountBillStatistics.setRebateLockedAmountChange(countResult.getValue().getRebateLockedAmountChange());
                customerAccountBillStatistics.setCreditAvailableQuotaChange(countResult.getValue().getCreditAvailableQuotaChange());

                toAddBillList.add(getWriteOffBill(user, destAccountId, customerAccountBillStatistics));
                toAddBillList.add(getDestBill(user, destAccountId, customerAccountBillStatistics));
            }
        }
        if (CollectionUtils.empty(toAddBillList)) {
            return;
        }
        try {
            List<CustomerAccountBillModel.CreateArg> billArgs = Lists.newArrayList();
            toAddBillList.forEach(bill -> {
                CustomerAccountBillModel.CreateArg createArg = new CustomerAccountBillModel.CreateArg();
                createArg.setTenantId(bill.getTenantId());
                createArg.setCustomerAccountId(bill.getCustomerAccountId());
                createArg.setRelateType(bill.getRelateType());
                createArg.setRelateId(bill.getRelateId());
                createArg.setPrepayAmount(bill.getPrepayAmountChange());
                createArg.setPrepayLockAmount(bill.getPrepayLockedAmountChange());
                createArg.setRebateAmount(bill.getRebateAmountChange());
                createArg.setRebateLockAmount(bill.getRebateLockedAmountChange());
                createArg.setCreditQuotaAmount(bill.getCreditAvailableQuotaChange());
                createArg.setRepair(bill.getRepair());
                createArg.setInfo(bill.getRemark());
                billArgs.add(createArg);
            });
            CustomerAccountBillModel.BatchCreateArg batchCreateArg = new CustomerAccountBillModel.BatchCreateArg();
            batchCreateArg.setBillArgs(billArgs);
            billProxy.batchCreate(batchCreateArg);
        } catch (Exception e) {
            log.warn("error occur when mergeBill,for sourAccountIds:{},destAccountId:{},toAddBillList:{}", sourceAccountIds, destAccountId, JsonUtil.toJson(toAddBillList), e);
        }
    }

    /**
     * 增加预存款-客户账户流水
     * @param customerAccountId
     * @param prepayId
     * @param prepayAmount
     * @param prepayLockAmount
     * @param tenantId
     * @param info
     */
    public void addCustomerAccountBillAccordPrepay(String customerAccountId, String prepayId, double prepayAmount, double prepayLockAmount, String tenantId, String info) {
        try {
            CustomerAccountBillModel.CreateArg arg = new CustomerAccountBillModel.CreateArg();
            arg.setTenantId(tenantId);
            arg.setCustomerAccountId(customerAccountId);
            arg.setRelateType(BillTypeEnum.Prepay.getType());
            arg.setRelateId(prepayId);
            arg.setPrepayAmount(prepayAmount);
            arg.setPrepayLockAmount(prepayLockAmount);
            arg.setInfo(info);
            billProxy.create(arg);
        } catch (Exception e) {
            log.warn("addCustomerAccountBillAccordPrepay error,tenantId:{},customerAccountId:{},prepayId:{},prepayAmount:{},prepayLockAmount:{},info:{}", tenantId, customerAccountId, prepayId, prepayAmount, prepayLockAmount, info, e);
        }
    }

    /**
     * 增加返利-客户账户流水<br>
     * @param customerAccountId
     * @param billTypeEnum
     * @param rebateId
     * @param rebateAmount
     * @param rebateLockAmount
     * @param tenantId
     * @param info
     */
    public void addCustomerAccountBillAccordRebate(String customerAccountId, BillTypeEnum billTypeEnum, String rebateId, double rebateAmount, double rebateLockAmount, String tenantId, String info) {
        try {
            CustomerAccountBillModel.CreateArg arg = new CustomerAccountBillModel.CreateArg();
            arg.setTenantId(tenantId);
            arg.setCustomerAccountId(customerAccountId);
            arg.setRelateType(billTypeEnum.getType());
            arg.setRelateId(rebateId);
            arg.setRebateAmount(rebateAmount);
            arg.setRebateLockAmount(rebateLockAmount);
            arg.setInfo(info);
            billProxy.create(arg);
        } catch (Exception e) {
            log.warn("addCustomerAccountBillAccordRebate, tenantId{},customerAccountId:{},rebateId:{},rebateAmount:{},rebateLockAmount:{},info:{}", tenantId, customerAccountId, rebateId, rebateAmount, rebateLockAmount, info, e);
        }
    }

    /**
     * 增加信用-客户账户流水
     * @param tenantId
     * @param customerAccountId
     * @param creditFileId
     * @param creditQuotaAmount
     * @param info
     */
    public void addCustomerAccountBillAccordCredit(String tenantId, String customerAccountId, String creditFileId, double creditQuotaAmount, String info) {
        try {
            CustomerAccountBillModel.CreateArg arg = new CustomerAccountBillModel.CreateArg();
            arg.setTenantId(tenantId);
            arg.setCustomerAccountId(customerAccountId);
            arg.setRelateType(BillTypeEnum.Credit.getType());
            arg.setRelateId(creditFileId);
            arg.setCreditQuotaAmount(creditQuotaAmount);
            arg.setInfo(info);
            billProxy.create(arg);
        } catch (Exception e) {
            log.error("addCustomerAccountBillAccordCredit, tenantId{},customerAccountId:{},creditFileId:{},creditQuotaAmount:{},info:{}", tenantId, customerAccountId, creditFileId, creditQuotaAmount, info, e);
        }
    }

    /**
     * 批量增加信用-客户账户流水
     * @param bills
     */
    public void batchAddCustomerAccountBillAccordCredit(List<CustomerAccountBill> bills) {
        if (CollectionUtils.empty(bills)) {
            return;
        }
        try {
            List<CustomerAccountBillModel.CreateArg> billArgs = Lists.newArrayList();
            bills.forEach(bill -> {
                CustomerAccountBillModel.CreateArg createArg = new CustomerAccountBillModel.CreateArg();
                createArg.setTenantId(bill.getTenantId());
                createArg.setCustomerAccountId(bill.getCustomerAccountId());
                createArg.setRelateType(bill.getRelateType());
                createArg.setRelateId(bill.getRelateId());
                createArg.setPrepayAmount(bill.getPrepayAmountChange());
                createArg.setPrepayLockAmount(bill.getPrepayLockedAmountChange());
                createArg.setRebateAmount(bill.getRebateAmountChange());
                createArg.setRebateLockAmount(bill.getRebateLockedAmountChange());
                createArg.setCreditQuotaAmount(bill.getCreditAvailableQuotaChange());
                createArg.setRepair(bill.getRepair());
                createArg.setInfo(bill.getRemark());
                billArgs.add(createArg);
            });
            CustomerAccountBillModel.BatchCreateArg batchCreateArg = new CustomerAccountBillModel.BatchCreateArg();
            batchCreateArg.setBillArgs(billArgs);
            billProxy.batchCreate(batchCreateArg);
        } catch (Exception e) {
            log.error("", e);
        }
    }

    /**
     * 删除指定企业流水数据
     * @param tenantId
     */
    public void delete(String tenantId) {
        CustomerAccountBillModel.DeleteArg deleteArg = new CustomerAccountBillModel.DeleteArg();
        deleteArg.setTenantId(tenantId);
        billProxy.delete(deleteArg);
    }

    /**
     * 获取昨天有流水记录的企业
     * @param billDate
     * @return
     */
    public List<String> listTenantIdsByBillDate(Date billDate) {
        CustomerAccountBillModel.ListTenantIdsArg listTenantIdsArg = new CustomerAccountBillModel.ListTenantIdsArg();
        listTenantIdsArg.setBillDate(billDate.getTime());
        CustomerAccountBillModel.ListTenantIdsResult result = billProxy.list(listTenantIdsArg);
        if (result.isSuccess() && Objects.nonNull(result.getValue())) {
            return result.getValue().getTenantIds();
        }
        return Lists.newArrayList();
    }

    /**
     * 获取企业指定日期有流水记录的客户账户
     * @param tenantId
     * @param billDate
     * @return
     */
    public List<String> listCustomerAccountIdsByTenantIdAndBillDate(String tenantId, Date billDate) {
        CustomerAccountBillModel.ListCustomerAccountIdsArg listCustomerAccountIdsArg = new CustomerAccountBillModel.ListCustomerAccountIdsArg();
        listCustomerAccountIdsArg.setTenantId(tenantId);
        listCustomerAccountIdsArg.setBillDate(billDate.getTime());

        CustomerAccountBillModel.ListCustomerAccountIdsResult result = billProxy.list(listCustomerAccountIdsArg);
        if (result.isSuccess() && Objects.nonNull(result.getValue())) {
            return result.getValue().getCustomerAccountIds();
        }
        return Lists.newArrayList();
    }

    /**
     * 生成一条冲销流水的目的是对账的时候需要，当原有的客户账户作废了其对应的明细也作废了。<br>
     * 此时为了保持对账对平需要使其流水总和为0<br>
     * @param sourceBill
     * @return
     */
    private CustomerAccountBill generateHedgingBill(CustomerAccountBill sourceBill) {
        CustomerAccountBill hedgingBill = new CustomerAccountBill();
        BeanUtils.copyProperties(sourceBill, hedgingBill);
        hedgingBill.setId(null);
        //把所有金额置换成其相反数,对于0这种情况-0还是零。
        hedgingBill.setPrepayAmountChange(reverseValue(hedgingBill.getPrepayAmountChange()));
        hedgingBill.setPrepayLockedAmountChange(reverseValue(hedgingBill.getPrepayLockedAmountChange()));
        hedgingBill.setRebateAmountChange(reverseValue(hedgingBill.getRebateAmountChange()));
        hedgingBill.setRebateLockedAmountChange(reverseValue(hedgingBill.getRebateLockedAmountChange()));
        hedgingBill.setCreateTime(new Date());
        hedgingBill.setRemark("冲销流水，原流水id为" + sourceBill.getId());
        log.debug("generateHedgingBill(),customeraccountbill:{}", hedgingBill);

        //hedgingBill.setBillDate(new Date());
        return hedgingBill;
    }

    /**
     * 生成冲销流水，用于抵消被合并的客户流水
     * @param user
     * @param destCustomerAccountId
     * @param customerAccountBillStatistics
     * @return
     */
    private CustomerAccountBill getWriteOffBill(User user, String destCustomerAccountId, CustomerAccountBillStatistics customerAccountBillStatistics) {
        String srcCustomerAccountId = customerAccountBillStatistics.getCustomerAccountId();
        CustomerAccountBill customerAccountBill = new CustomerAccountBill();
        customerAccountBill.setTenantId(customerAccountBillStatistics.getTenantId());
        //用于冲销的流水，relateId保存目标customerAccountId，即合并客户的主客户账户id
        customerAccountBill.setCustomerAccountId(srcCustomerAccountId);
        customerAccountBill.setRelateId(destCustomerAccountId);
        customerAccountBill.setRelateType(BillTypeEnum.WriteOff.getType());
        customerAccountBill.setBillDate(new Date());
        customerAccountBill.setCreditAvailableQuotaChange(customerAccountBillStatistics.getCreditAvailableQuotaChange().negate().doubleValue());
        customerAccountBill.setPrepayAmountChange(customerAccountBillStatistics.getPrepayAmountChange().negate().doubleValue());
        customerAccountBill.setPrepayLockedAmountChange(customerAccountBillStatistics.getPrepayLockedAmountChange().negate().doubleValue());
        customerAccountBill.setRebateAmountChange(customerAccountBillStatistics.getRebateAmountChange().negate().doubleValue());
        customerAccountBill.setRebateLockedAmountChange(customerAccountBillStatistics.getRebateLockedAmountChange().negate().doubleValue());
        customerAccountBill.setRemark(String.format("合并客户src->dest，冲销流水srcCustomerAccountId=%s,destCustomerAccountId=%s", srcCustomerAccountId, destCustomerAccountId));
        customerAccountBill.init(user.getUserId());
        return customerAccountBill;
    }

    private double reverseValue(double value) {
        return -value;
    }

    private CustomerAccountBill generateNewDestBill(CustomerAccountBill sourceBill, String destAccountId) {
        CustomerAccountBill newDestBill = new CustomerAccountBill();
        BeanUtils.copyProperties(sourceBill, newDestBill);
        newDestBill.setId(null);
        //明细id不变，客户账户id换成新的客户账户Id<br>
        newDestBill.setRelateId(sourceBill.getRelateId());
        newDestBill.setCustomerAccountId(destAccountId);
        newDestBill.setCreateTime(new Date());
        newDestBill.setRemark("合并流水，原流水id为" + sourceBill.getId());
        log.debug("generateNewDestBill(),customeraccountbill:{}", newDestBill);
        return newDestBill;
    }

    private CustomerAccountBill getDestBill(User user, String destCustomerAccountId, CustomerAccountBillStatistics customerAccountBillStatistics) {
        String srcCustomerAccountId = customerAccountBillStatistics.getCustomerAccountId();
        CustomerAccountBill customerAccountBill = new CustomerAccountBill();
        customerAccountBill.setTenantId(customerAccountBillStatistics.getTenantId());
        customerAccountBill.setCustomerAccountId(destCustomerAccountId);
        customerAccountBill.setRelateId(srcCustomerAccountId);
        customerAccountBill.setRelateType(BillTypeEnum.MergeCustomer.getType());
        customerAccountBill.setBillDate(new Date());
        customerAccountBill.setCreditAvailableQuotaChange(customerAccountBillStatistics.getCreditAvailableQuotaChange().doubleValue());
        customerAccountBill.setPrepayAmountChange(customerAccountBillStatistics.getPrepayAmountChange().doubleValue());
        customerAccountBill.setPrepayLockedAmountChange(customerAccountBillStatistics.getPrepayLockedAmountChange().doubleValue());
        customerAccountBill.setRebateAmountChange(customerAccountBillStatistics.getRebateAmountChange().doubleValue());
        customerAccountBill.setRebateLockedAmountChange(customerAccountBillStatistics.getRebateLockedAmountChange().doubleValue());
        customerAccountBill.setRemark(String.format("合并客户src->dest，生成流水srcCustomerAccountId=%s,destCustomerAccountId=%s", srcCustomerAccountId, destCustomerAccountId));
        customerAccountBill.init(user.getUserId());
        return customerAccountBill;
    }

}
