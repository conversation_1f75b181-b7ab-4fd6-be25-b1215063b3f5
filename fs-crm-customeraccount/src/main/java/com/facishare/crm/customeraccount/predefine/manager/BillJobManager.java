package com.facishare.crm.customeraccount.predefine.manager;

import com.facishare.crm.customeraccount.constants.CreditFileConstants;
import com.facishare.crm.customeraccount.constants.CustomerAccountConstants;
import com.facishare.crm.customeraccount.constants.PrepayDetailConstants;
import com.facishare.crm.customeraccount.constants.RebateIncomeDetailConstants;
import com.facishare.crm.customeraccount.constants.RebateOutcomeDetailConstants;
import com.facishare.crm.customeraccount.entity.CustomerAccountBillStatistics;
import com.facishare.crm.customeraccount.entity.DailyBill;
import com.facishare.crm.customeraccount.enums.AbnormalTypeEnum;
import com.facishare.crm.customeraccount.enums.BillTypeEnum;
import com.facishare.crm.customeraccount.enums.CreditTypeEnum;
import com.facishare.crm.customeraccount.util.AssertUtil;
import com.facishare.crm.customeraccount.util.ConfigCenter;
import com.facishare.crm.customeraccount.util.DateUtil;
import com.facishare.crm.customeraccount.util.HeaderUtil;
import com.facishare.crm.customeraccount.util.HttpUtil;
import com.facishare.crm.customeraccount.util.ObjectDataUtil;
import com.facishare.crmcommon.rest.ApprovalInitProxy;
import com.facishare.crmcommon.rest.CustomerAccountBillProxy;
import com.facishare.crmcommon.rest.DailyAbnormalCustomerAccountProxy;
import com.facishare.crmcommon.rest.DailyBillProxy;
import com.facishare.crmcommon.rest.dto.CustomerAccountBillModel;
import com.facishare.crmcommon.rest.dto.DailyAbnormalCustomerAccountModel;
import com.facishare.crmcommon.rest.dto.DailyBillModel;
import com.facishare.crmcommon.rest.dto.GetCurInstanceStateModel;
import com.facishare.open.msg.result.MessageResult;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.quartz.SimpleTrigger;
import org.quartz.Trigger;
import org.quartz.TriggerBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static org.quartz.SimpleScheduleBuilder.simpleSchedule;

/**
 * @IgnoreI18n
 */
@Component
@Slf4j
public class BillJobManager extends CommonManager {
    @Autowired
    private ObjectDataServiceImpl objectDataService;
    @Resource(type = ApprovalInitProxy.class)
    private ApprovalInitProxy approvalInitProxy;
    @Autowired
    private CustomerAccountBillProxy customerAccountBillProxy;
    @Autowired
    private DailyBillProxy dailyBillProxy;
    @Autowired
    private DailyAbnormalCustomerAccountProxy dailyAbnormalCustomerAccountProxy;

    public List<IObjectData> queryRefund(String tenantId, String apiName, long startTime, long endTime, int limit, int offset) {
        String sql = "SELECT * from biz_refund WHERE tenant_id='%s' and last_modified_time>'%s' and last_modified_time<'%s' and refunded_method IN ('10000','10001') ";
        sql = String.format(sql, tenantId, startTime, endTime);
        log.info("queryRefund sql:{}", sql);
        QueryResult<IObjectData> queryResult = new QueryResult<>();
        try {
            queryResult = objectDataService.findBySql(sql, tenantId, apiName);
        } catch (MetadataServiceException e) {
            log.warn("jobManager query exception,tenantId:{} sql:{}", tenantId, sql, e);
        }
        return queryResult.getData();
    }

    public List<IObjectData> queryPayment(String tenantId, String apiName, long startTime, long endTime, int limit, int offset) {
        //payment_order表没有存储回款方式所以需要拼接
        String sql = " select p.payment_term,o.id,o.life_status,o.payment_amount,o.account_id FROM payment_customer p,payment_order o WHERE p.id = o.payment_id AND o.last_modified_time >'%s' AND o.last_modified_time<'%s' AND o.tenant_id='%s' AND p.payment_term IN ('10000','10001','10002') limit %d offset %d ;";
        sql = String.format(sql, String.valueOf(startTime), String.valueOf(endTime), tenantId, limit, offset);
        log.info("queryPayment sql:{}", sql);
        QueryResult<IObjectData> queryResult = new QueryResult<>();
        try {
            queryResult = objectDataService.findBySql(sql, tenantId, apiName);
        } catch (MetadataServiceException e) {
            log.warn("jobManager query exception,tenantId:{} sql:{}", tenantId, sql, e);
        }
        return queryResult.getData();
    }

    //根据创建时间，查询客户或者客户账户列表
    public List<IObjectData> queryByCreateTime(String tenantId, String apiName, long startTime, long endTime, int offset, int limit) {
        String sql;
        if ("AccountObj".equals(apiName)) {
            sql = "select * from biz_account where tenant_id='%s' and object_describe_api_name = 'AccountObj' and create_time between '%s' and '%s' limit %d offset %d ;";
            sql = String.format(sql, tenantId, startTime, endTime, limit, offset);
        } else {
            sql = "select * from customer_account where tenant_id='%s' and create_time between '%s' and '%s' limit %d offset %d ;";
            sql = String.format(sql, tenantId, startTime, endTime, limit, offset);
        }
        log.info("queryByCreateTime sql:{}", sql);
        QueryResult<IObjectData> queryResult = new QueryResult<>();
        try {
            queryResult = objectDataService.findBySql(sql, tenantId, apiName);
        } catch (MetadataServiceException e) {
            log.warn("queryByCreateTime exception,tenantId:{} sql:{}", tenantId, sql, e);
        }
        return queryResult.getData();
    }

    public List<IObjectData> queryWithInCondition(String tenantId, String apiName, List<String> ids, int offset, int limit) {
        AssertUtil.argumentNotNullOrEmpty("ids can not be empty", ids);
        StringBuilder stringBuilder = getInConditionString(ids);
        String sql;
        if ("AccountObj".equals(apiName)) {
            sql = "select * from biz_account where tenant_id='%s' and id in " + stringBuilder + " limit %d offset %d ;";
        } else {
            sql = "select * from customer_account where tenant_id='%s' and customer_id in " + stringBuilder + " limit %d offset %d ;";
        }
        sql = String.format(sql, tenantId, limit, offset);
        log.info("queryWithInCondition sql:{}", sql);
        QueryResult<IObjectData> queryResult = new QueryResult<>();
        try {
            queryResult = objectDataService.findBySql(sql, tenantId, apiName);
        } catch (MetadataServiceException e) {
            log.warn("queryWithInCondition exception,tenantId:{} sql:{}", tenantId, sql, e);
        }
        return queryResult.getData();
    }

    private static StringBuilder getInConditionString(List<String> ids) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("(");
        for (String id : ids) {
            stringBuilder.append("'").append(id).append("',");
        }
        stringBuilder.deleteCharAt(stringBuilder.length() - 1);
        stringBuilder.append(")");
        return stringBuilder;
    }

    public static Trigger fireAfterEvery5SecondsRepeatThrice() {
        SimpleTrigger trigger = TriggerBuilder.newTrigger().withIdentity("trigger1", "enableCustomerAccountTrigger").startNow()
                .withSchedule(simpleSchedule().withIntervalInSeconds(5).withRepeatCount(2)).build();
        return trigger;
    }

//    @Deprecated
//    public void fillRelateType(String ei) throws MetadataServiceException {
//        int limit = 100;
//        int offset = 0;
//        int size = 0;
//        int still = 0;
//        String pgSqlFormat = "select id,tenant_id from %s where id='%s' and tenant_id='%s'";
//        List<String> tableNames = Lists.newArrayList("prepay_detail", "rebate_income_detail", "rebate_outcome_detail", "credit_file", "customer_account");
//        do {
//            List<CustomerAccountBill> customerAccountBills = customerAccountBillDao.listDefaultRelateTypeByPage(ei, limit, offset);
//            if (CollectionUtils.isEmpty(customerAccountBills)) {
//                size = 0;
//            } else {
//                size = customerAccountBills.size();
//                for (CustomerAccountBill customerAccountBill : customerAccountBills) {
//                    String relateId = customerAccountBill.getRelateId();
//                    String tenantId = customerAccountBill.getTenantId();
//                    String id = customerAccountBill.getId();
//                    Integer relateType = null;
//                    for (String table : tableNames) {
//                        String sql = String.format(pgSqlFormat, table, relateId, tenantId);
//                        List<Map> list = findBySql(tenantId, sql);
//                        if (CollectionUtils.isNotEmpty(list)) {
//                            if (table.equals("prepay_detail")) {
//                                relateType = BillTypeEnum.Prepay.getType();
//                            } else if (table.equals("rebate_income_detail")) {
//                                relateType = BillTypeEnum.RebateIncome.getType();
//                            } else if (table.equals("rebate_outcome_detail")) {
//                                relateType = BillTypeEnum.RebateOutcome.getType();
//                            } else if (table.equals("credit_file")) {
//                                relateType = BillTypeEnum.Credit.getType();
//                            } else if (table.equals("customer_account")) {
//                                //合并 与 冲销
//                                relateType = 0;
//                            }
//                            break;
//                        }
//                    }
//                    if (Objects.nonNull(relateType)) {
//                        customerAccountBillDao.setRelateType(id, relateType);
//                    } else {
//                        still++;
//                        log.warn("relateType not match,tenantId:{},id:{},customerAccountBill:{},still:{}", tenantId, id, customerAccountBill, still);
//                    }
//                }
//            }
//        } while (size == limit && still == 0);
//    }

//    @Deprecated
//    public int deleteCreditBillByTenantId(String tenantId) {
//        return customerAccountBillDao.deleteCreidtBillByTenantId(tenantId);
//    }

//    @Deprecated
//    public void repairCreditFileBill(String tenantId) {
//        String baseSql = String
//                .format("select id,tenant_id,customer_id,life_status,credit_type,credit_quota,temporary_credit_limit,start_time,end_time from credit_file where tenant_id='%s' order by create_time",
//                        tenantId);
//        String pageSqlFormat = " limit %d offset %d";
//        int limit = 100;
//        int offset = 0;
//        int size = 0;
//        Calendar calendar = Calendar.getInstance();
//        calendar.set(2018, Calendar.JULY, 1);
//        do {
//            String sql = baseSql.concat(String.format(pageSqlFormat, limit, offset));
//            try {
//                List<Map> dataList = findBySql(tenantId, sql);
//                if (CollectionUtils.isEmpty(dataList)) {
//                    break;
//                }
//                for (Map map : dataList) {
//                    String creditId = (String) map.get("id");
//                    Date startTime = new Date(Long.parseLong(map.get("start_time").toString()));
//                    Date endTime = new Date(Long.parseLong(map.get("end_time").toString()));
//                    String creditType = (String) map.get("credit_type");
//                    String customerId = (String) map.get("customer_id");
//                    String lifeStatus = (String) map.get("life_status");
//                    String customerAccountSql = String.format("select id,tenant_id,customer_id from customer_account where tenant_id='%s' and customer_id='%s'", tenantId, customerId);
//                    List<Map> customerAccountList = findBySql(tenantId, customerAccountSql);
//                    if (CollectionUtils.isEmpty(customerAccountList)) {
//                        continue;
//                    }
//                    String customerAccountId = (String) customerAccountList.get(0).get("id");
//                    BigDecimal creditAmount = BigDecimal.ZERO;
//                    if (ObjectDataUtil.isCurrentTimeActive(startTime, endTime)) {
//                        if (CreditTypeEnum.OfficialCredit.getValue().equals(creditType)) {
//                            creditAmount = new BigDecimal(String.valueOf(map.get("credit_quota")));
//                        } else {
//                            creditAmount = new BigDecimal(String.valueOf(map.get("temporary_credit_limit")));
//                        }
//                        if (SystemConstants.LifeStatus.Ineffective.value.equals(lifeStatus) || SystemConstants.LifeStatus.UnderReview.value.equals(lifeStatus)
//                                || SystemConstants.LifeStatus.Invalid.value.equals(lifeStatus)) {
//                            creditAmount = BigDecimal.ZERO;
//                        }
//                    }
//                    CustomerAccountBill customerAccountBill = new CustomerAccountBill();
//                    customerAccountBill.setTenantId(tenantId);
//                    customerAccountBill.setCustomerAccountId(customerAccountId);
//                    customerAccountBill.setRelateType(BillTypeEnum.Credit.getType());
//                    customerAccountBill.setRelateId(creditId);
//                    customerAccountBill.setBillDate(calendar.getTime());
//                    customerAccountBill.setCreditAvailableQuotaChange(creditAmount.doubleValue());
//                    customerAccountBill.init("-10000");
//                    customerAccountBill.setRemark("流水初始化");
//                    customerAccountBillDao.insert(customerAccountBill);
//                }
//                size = dataList.size();
//                offset += limit;
//            } catch (MetadataServiceException e) {
//                log.warn("query credit error,sql:{}", sql, e);
//                break;
//            }
//        } while (limit == size);
//    }

    public void doCustomerAccountBillIncrementJob(User user, String customerAccountId, Date yesterdayDate) {
        String tenantId = user.getTenantId();
        try {
            //获取最近一天的日账单数据
            DailyBill nearYesterdayDailyBill = null;
            DailyBillModel.GetLatestDailyBillArg billArg = new DailyBillModel.GetLatestDailyBillArg();
            billArg.setTenantId(tenantId);
            billArg.setCustomerAccountId(customerAccountId);
            billArg.setBillDate(yesterdayDate.getTime());
            DailyBillModel.Result result = dailyBillProxy.getLatestDailyBill(billArg);
            if (result.isSuccess() && Objects.nonNull(result.getValue())) {
                nearYesterdayDailyBill = new DailyBill();
                nearYesterdayDailyBill.setId(result.getValue().getId());
                nearYesterdayDailyBill.setTenantId(result.getValue().getTenantId());
                nearYesterdayDailyBill.setCustomerAccountId(result.getValue().getCustomerAccountId());
                nearYesterdayDailyBill.setBillDate(new Date(result.getValue().getBillDate()));
                nearYesterdayDailyBill.setPrepayBalance(result.getValue().getPrepayBalance());
                nearYesterdayDailyBill.setPrepayLockedBalance(result.getValue().getPrepayLockedBalance());
                nearYesterdayDailyBill.setRebateBalance(result.getValue().getRebateBalance());
                nearYesterdayDailyBill.setRebateLockedBalance(result.getValue().getRebateLockedBalance());
                nearYesterdayDailyBill.setCreditAvailableQuota(result.getValue().getCreditAvailableQuota());
                nearYesterdayDailyBill.setCreateBy(result.getValue().getCreateBy());
                nearYesterdayDailyBill.setCreateTime(new Date(result.getValue().getCreateTime()));
            }

            //生成昨天的日账单
            DailyBill yesterdayDailyBill = generateDateDailyBill(user, customerAccountId, yesterdayDate);
            if (Objects.isNull(nearYesterdayDailyBill)) {
                log.warn("near YesterdayDailyBill is Null,tenantId:{} customerAccountId:{},yesterday[{}] DailyBill[{}]", tenantId, customerAccountId, DateUtil.formatYYYYMMDD(yesterdayDate),
                        yesterdayDailyBill);
                return;
            }
            //获取昨日流水统计
            Date from = nearYesterdayDailyBill.getCreateTime();
            Date to = yesterdayDailyBill.getCreateTime();
            CustomerAccountBillStatistics customerAccountBillStatistics = countBillStatistics(tenantId, customerAccountId, from, to);
            //不一致 则记录异常
            int consistentType = consistent(customerAccountBillStatistics, yesterdayDailyBill, nearYesterdayDailyBill);
            if (consistentType > 0) {
                log.warn("doCustomerAccountBillIncrementJob,user:{},customerAccountId:{},date:{},consistentType:{},customerAccountBillStatistics:{},nearYesterdayDailyBill:{},yesterdayDailyBill:{}", user, customerAccountId,
                        yesterdayDate, consistentType, customerAccountBillStatistics, nearYesterdayDailyBill, yesterdayDailyBill);
                String remark = getDailyBillRemark(tenantId, customerAccountId, yesterdayDate, consistentType);
                createDailyAbnormal(user, customerAccountId, yesterdayDate, customerAccountId, AbnormalTypeEnum.DailyBill, remark);
            }
        } catch (Exception e) {
            log.warn("doCustomerAccountBillIncrementJob error,tenantId:{},customerAccountId:{},billDate:{}", tenantId, customerAccountId, yesterdayDate, e);
        }
    }

    public void sendDailyMessage(String tenantId, String ea, String appId, List<Integer> toUserList, Date yesterdayDate) {
        String content = getMsgContent(yesterdayDate);
        if (StringUtils.isBlank(content)) {
            return;
        }
        String postId = String.format("E.%s-%s-%s", ea, appId, System.currentTimeMillis());
        String url = ConfigCenter.openMessageUrl + "/fs-open-msg/send/sendTextMessage";

        Map<String, String> headers = HeaderUtil.getCrmHeader(tenantId, "-10000");
        Map<String, Object> textMessageVO = Maps.newHashMap();
        textMessageVO.put("type", "TEXT");
        textMessageVO.put("appId", appId);
        textMessageVO.put("content", content);
        textMessageVO.put("enterpriseAccount", ea);
        textMessageVO.put("lowPriority", true);
        textMessageVO.put("postId", postId);
        textMessageVO.put("toUserList", toUserList);

        Map<String, Object> sendTextMessage = Maps.newHashMap();
        sendTextMessage.put("messageSendType", "ADMINISTRATOR_PUSH");
        sendTextMessage.put("textMessageVO", textMessageVO);
        try {
            MessageResult messageResult = HttpUtil.post(url, headers, sendTextMessage, MessageResult.class);
            log.info("MessageResult:{}", messageResult);
        } catch (Exception e) {
            log.warn("sendTextMessage failed, tenantId={} exception=", tenantId, e);
        }
    }

    //根据更新时间，获取信用对象昨天的增量数据
    public void doCreditIncrementJob(String tenantId, Date date) throws MetadataServiceException {
        long yesterdayBeginTime = DateUtil.getDateBeginTime(date);
        long yesterdayEndTime = DateUtil.getDateEndTime(date);
        String baseSql = String.format(
                "select id as _id,name,customer_id,credit_type,credit_quota,temporary_credit_limit,credit_period,start_time,end_time,life_status, last_modified_time from credit_file where tenant_id='%s' and last_modified_time>=%d and last_modified_time<%d",
                tenantId, yesterdayBeginTime, yesterdayEndTime);
        doIncrementJob(tenantId, date, baseSql, CreditFileConstants.API_NAME);
    }

    public void doRebateOutcomeIncrementJob(String tenantId, Date date) throws MetadataServiceException {
        long yesterdayBeginTime = DateUtil.getDateBeginTime(date);
        long yesterdayEndTime = DateUtil.getDateEndTime(date);
        String baseSql = String.format(
                "select id as _id,name,amount,order_payment_id,rebate_income_detail_id,life_status, last_modified_time from rebate_outcome_detail where tenant_id='%s' and last_modified_time>=%d and last_modified_time<%d",
                tenantId, yesterdayBeginTime, yesterdayEndTime);
        doIncrementJob(tenantId, date, baseSql, RebateOutcomeDetailConstants.API_NAME);
    }

    public void doRebateIncomeIncrementJob(String tenantId, Date date) throws MetadataServiceException {
        long yesterdayBeginTime = DateUtil.getDateBeginTime(date);
        long yesterdayEndTime = DateUtil.getDateEndTime(date);
        String baseSql = String.format(
                "select id as _id,name,customer_id,refund_id,amount,used_rebate,available_rebate,customer_account_id,start_time,end_time,life_status, last_modified_time from rebate_income_detail where tenant_id='%s' and last_modified_time>=%d and last_modified_time<%d",
                tenantId, yesterdayBeginTime, yesterdayEndTime);
        doIncrementJob(tenantId, date, baseSql, RebateIncomeDetailConstants.API_NAME);
    }

    public void doPrepayIncrementJob(String tenantId, Date date) throws MetadataServiceException {
        long yesterdayBeginTime = DateUtil.getDateBeginTime(date);
        long yesterdayEndTime = DateUtil.getDateEndTime(date);
        String baseSql = String.format(
                "select id as _id,name,customer_id,order_payment_id,refund_id,amount,customer_account_id,record_type,life_status, last_modified_time from prepay_detail where tenant_id='%s' and last_modified_time>=%d and last_modified_time<%d",
                tenantId, yesterdayBeginTime, yesterdayEndTime);
        doIncrementJob(tenantId, date, baseSql, PrepayDetailConstants.API_NAME);
    }

    private void doIncrementJob(String tenantId, Date date, String baseSql, String objectApiName) throws MetadataServiceException {
        String pageSqlFormat = " limit %d offset %d";
        int limit = 100;
        int offset = 0;
        int size = 0;
        boolean ok;
        do {
            String sql = baseSql.concat(String.format(pageSqlFormat, limit, offset));
            //查询企业增量数据
            List<Map> objectDataList = findBySql(tenantId, sql);
            if (CollectionUtils.isEmpty(objectDataList)) {
                break;
            }
            size = objectDataList.size();
            offset += limit;
            Map<String, ApprovalFlowTriggerType> inChangeIdsApprovalFlowType = Maps.newHashMap();
            if (RebateIncomeDetailConstants.API_NAME.equals(objectApiName) || PrepayDetailConstants.API_NAME.equals(objectApiName)) {
                //                List<String> inChangeIds = objectDataList.stream().map(ObjectData::new).filter(objectData -> ObjectDataExt.of(objectData).getLifeStatus() == ObjectLifeStatus.IN_CHANGE).map(IObjectData::getId).collect(Collectors.toList());
                inChangeIdsApprovalFlowType = realApprovalInChange(tenantId, objectApiName, objectDataList);//getInChangeDataApprovalFlowType(tenantId, inChangeIds);
            }
            for (Map map : objectDataList) {
                IObjectData objectData = new ObjectData(map);
                String id = objectData.getId();
                String customerAccountId = null;
                ok = true;
                try {
                    if (CreditFileConstants.API_NAME.equals(objectApiName)) {
                        String customerId = ObjectDataUtil.getReferenceId(objectData, CreditFileConstants.Field.Customer.apiName);
                        customerAccountId = getCustomerAccountIdByCustomerId(tenantId, customerId);
                        ok = checkCreditBill(tenantId, objectData);
                    } else if (RebateOutcomeDetailConstants.API_NAME.equals(objectApiName)) {
                        String rebateIncomeId = ObjectDataUtil.getReferenceId(objectData, RebateOutcomeDetailConstants.Field.RebateIncomeDetail.apiName);
                        customerAccountId = getCustomerAccountByRebateIncomeId(tenantId, rebateIncomeId);
                        ok = checkRebateOutcomeBill(tenantId, objectData);
                    } else if (RebateIncomeDetailConstants.API_NAME.equals(objectApiName)) {
                        customerAccountId = ObjectDataUtil.getReferenceId(objectData, RebateIncomeDetailConstants.Field.CustomerAccount.apiName);
                        ok = checkRebateIncomeBill(tenantId, objectData, inChangeIdsApprovalFlowType.get(id));
                    } else if (PrepayDetailConstants.API_NAME.equals(objectApiName)) {
                        customerAccountId = ObjectDataUtil.getReferenceId(objectData, PrepayDetailConstants.Field.CustomerAccount.apiName);
                        ok = checkPrepayBill(tenantId, objectData, inChangeIdsApprovalFlowType.get(id));
                    }
                    if (!ok) {
                        String remark = String.format("企业[%s]-[%s]-DataID[%s]记录与流水不一致", tenantId, objectApiName, id);
                        createDailyAbnormal(User.builder().tenantId(tenantId).userId(User.SUPPER_ADMIN_USER_ID).build(), customerAccountId, date, id, getAbnormalType(objectApiName), remark);
                    }
                } catch (Exception e) {
                    log.warn("do {} IncrementJob error,tenantId:{},date:{},objectData:{}", objectApiName, tenantId, DateUtil.formatYYYYMMDD(date), objectData, e);
                    String remark = String.format("企业[%s]-[%s]-DataID[%s]检查流水记录发生异常", tenantId, objectApiName, id);
                    createDailyAbnormal(User.builder().tenantId(tenantId).userId(User.SUPPER_ADMIN_USER_ID).build(), customerAccountId, date, id, getAbnormalType(objectApiName), remark);
                }
            }
        } while (limit == size);
    }

    private CustomerAccountBillStatistics countBillStatistics(String tenantId, String customerAccountId, Date from, Date to) {
        CustomerAccountBillModel.CountArg countArg = new CustomerAccountBillModel.CountArg();
        countArg.setTenantId(tenantId);
        countArg.setCustomerAccountId(customerAccountId);
        countArg.setFromTime(from.getTime());
        countArg.setToTime(to.getTime());
        CustomerAccountBillModel.CountResult countResult = customerAccountBillProxy.count(countArg);
        return convertCountResult(countResult);
    }

    public CustomerAccountBillStatistics countBillStatistics(String tenantId, Integer relateType, String relateId) {
        CustomerAccountBillModel.CountArg countArg = new CustomerAccountBillModel.CountArg();
        countArg.setTenantId(tenantId);
        countArg.setRelateType(relateType);
        countArg.setRelateId(relateId);
        CustomerAccountBillModel.CountResult countResult = customerAccountBillProxy.count(countArg);
        return convertCountResult(countResult);
    }

    private CustomerAccountBillStatistics convertCountResult(CustomerAccountBillModel.CountResult countResult) {
        CustomerAccountBillStatistics customerAccountBillStatistics = null;
        if (countResult.isSuccess()) {
            customerAccountBillStatistics = new CustomerAccountBillStatistics();
            customerAccountBillStatistics.setTenantId(countResult.getValue().getTenantId());
            customerAccountBillStatistics.setCustomerAccountId(countResult.getValue().getCustomerAccountId());
            customerAccountBillStatistics.setPrepayAmountChange(countResult.getValue().getPrepayAmountChange());
            customerAccountBillStatistics.setPrepayLockedAmountChange(countResult.getValue().getPrepayLockedAmountChange());
            customerAccountBillStatistics.setRebateAmountChange(countResult.getValue().getRebateAmountChange());
            customerAccountBillStatistics.setRebateLockedAmountChange(countResult.getValue().getRebateLockedAmountChange());
            customerAccountBillStatistics.setCreditAvailableQuotaChange(countResult.getValue().getCreditAvailableQuotaChange());
        }
        return customerAccountBillStatistics;
    }

    private boolean checkPrepayBill(String tenantId, IObjectData objectData, ApprovalFlowTriggerType approvalFlowTriggerType) {
        String prepayId = objectData.getId();
        CustomerAccountBillStatistics customerAccountBillStatistics = countBillStatistics(tenantId, BillTypeEnum.Prepay.getType(), prepayId);
        BigDecimal prepayAmountChange = BigDecimal.ZERO;
        BigDecimal prepayLockedAmountChange = BigDecimal.ZERO;
        if (Objects.nonNull(customerAccountBillStatistics)) {
            prepayAmountChange = customerAccountBillStatistics.getPrepayAmountChange();
            prepayLockedAmountChange = customerAccountBillStatistics.getPrepayLockedAmountChange();
        }
        BigDecimal amount = ObjectDataUtil.getBigDecimal(objectData, PrepayDetailConstants.Field.Amount.apiName);
        BigDecimal expectedAmountChange = BigDecimal.ZERO;
        BigDecimal expectedLockedAmountChange = BigDecimal.ZERO;
        ObjectLifeStatus objectLifeStatus = ObjectDataExt.of(objectData).getLifeStatus();
        boolean isPrepayIncome = ObjectDataUtil.isPrepayIncome(objectData);
        if (isPrepayIncome) {
            switch (objectLifeStatus) {
                case INEFFECTIVE:
                case UNDER_REVIEW:
                case INVALID:
                    break;
                case NORMAL:
                    expectedAmountChange = amount;
                    break;
                case IN_CHANGE:
                    log.info("checkPrepayBill,dataId:{},approvalFlowTriggerType:{}", prepayId, approvalFlowTriggerType);
                    if (ApprovalFlowTriggerType.INVALID == approvalFlowTriggerType) {
                        expectedLockedAmountChange = amount;
                        expectedAmountChange = amount;
                    } else if (ApprovalFlowTriggerType.UPDATE == approvalFlowTriggerType) {
                        expectedAmountChange = amount;
                    }
                    break;
            }
        } else {
            switch (objectLifeStatus) {
                case INEFFECTIVE:
                case INVALID:
                    break;
                case IN_CHANGE:
                case NORMAL:
                    expectedAmountChange = amount.negate();
                    break;
                case UNDER_REVIEW:
                    expectedLockedAmountChange = amount;
                    break;
            }
        }
        boolean ok = expectedAmountChange.compareTo(prepayAmountChange) == 0 && expectedLockedAmountChange.compareTo(prepayLockedAmountChange) == 0;
        if (!ok) {
            log.warn("checkPrepayBill false,tenantId:{},prepayId:{},lifeStatus:{},prepayAmountChange:{},expectAmountChange:{},prepayLockedAmountChange:{},expectedLockedAmountChange:{}", tenantId,
                    prepayId, objectLifeStatus, prepayAmountChange, expectedAmountChange, prepayLockedAmountChange, expectedLockedAmountChange);
        }
        return ok;
    }

    /**
     * （返利支出扣减收入，返利收入不会产生关联的流水） 1.生命状态=normal情况 在有效期内： --已使用返利 流水有：创建时流水 --未使用返利 流水有：创建时流水 不在有效期内（已经失效，还未生效）： --已使用返利（已经失效） 流水有：创建时流水，过期时流水 --未使用返利（还未生效和已经失效） 还未生效 流水有：无 已经失效 流水有：创建时流水，过期时流水
     * 四种情况下，若流水统计合=可用返利+已用返利，则表示无异常 2.生命状态=in_change情况(作废或者编辑审批) 作废审批中： 在有效期内： --已使用返利 已使用返利的收入，不可作废，不存在此情况 --未使用返利（作废审批中->生效，生效->作废） 流水有：新建时流水(返利金额增加)，作废时流水(返利锁定金额增加) --流水统计合：金额，锁定金额 不在有效期内： --已使用返利
     * 已使用的收入，不可作废，不存在此情况 --未使用返利 先失效->作废时流水有：新建时流水（返利金额增加），过期时流水（返利金额减少） 先作废->失效时流水有：新建时流水（返利金额增加），作废时流水（锁定金额增加），过期时流水（返利金额和锁定金额减少） --流水统计合为0 编辑审批中：（与normal状态时一致） 在有效期内： --已使用返利 流水：新建时流水 --未使用返利
     * 流水：新建时流水 不在有效期内：（未生效，已失效） --已使用返利（已失效） 流水：新建时流水，过期流水 --未使用返利（未生效和已失效） 未生效时 流水：无 已失效时 流水：新建时流水，过期流水 3.生命状态=ineffective,under_view,invalid 该场景下，流水统计为0
     * <p>
     * 用countLtDateByRelateTypeAndId统计 过滤掉因为返利收入定时任务产生的流水
     */
    private boolean checkRebateIncomeBill(String tenantId, IObjectData objectData, ApprovalFlowTriggerType approvalFlowTriggerType) {
        String rebateIncomeId = objectData.getId();
        CustomerAccountBillStatistics customerAccountBillStatistics = countBillStatistics(tenantId, BillTypeEnum.RebateIncome.getType(), rebateIncomeId);
        BigDecimal rebateAmountChange = BigDecimal.ZERO;
        BigDecimal rebateLockedAmountChange = BigDecimal.ZERO;
        if (Objects.nonNull(customerAccountBillStatistics)) {
            rebateAmountChange = customerAccountBillStatistics.getRebateAmountChange();
            rebateLockedAmountChange = customerAccountBillStatistics.getRebateLockedAmountChange();
        }
        BigDecimal expectAmountChange = BigDecimal.ZERO;
        BigDecimal expectLockedAmountChange = BigDecimal.ZERO;
        BigDecimal amount = ObjectDataUtil.getBigDecimal(objectData, RebateIncomeDetailConstants.Field.Amount.apiName);
        BigDecimal usedAmount = ObjectDataUtil.getBigDecimal(objectData, RebateIncomeDetailConstants.Field.UsedRebate.apiName);
        BigDecimal availableAmount = ObjectDataUtil.getBigDecimal(objectData, RebateIncomeDetailConstants.Field.AvailableRebate.apiName);
        ObjectLifeStatus objectLifeStatus = ObjectDataExt.of(objectData).getLifeStatus();
        ObjectLifeStatus objectLifeStatusBeforeInvalid = ObjectLifeStatus.of(ObjectDataExt.of(objectData).getLifeStatusBeforeInvalid());
        Date startTime = objectData.get(RebateIncomeDetailConstants.Field.StartTime.apiName, Date.class);
        Date endTime = objectData.get(RebateIncomeDetailConstants.Field.EndTime.apiName, Date.class);
        boolean isActive = ObjectDataUtil.isCurrentTimeActive(startTime, endTime);//ObjectDataUtil.isYesterdayActive(startTime, endTime, yesterday);
        switch (objectLifeStatus) {
            case INEFFECTIVE:
                break;
            case INVALID:
                if (ObjectLifeStatus.NORMAL == objectLifeStatusBeforeInvalid) {
                    expectAmountChange = availableAmount.add(usedAmount);
                }
                break;
            case UNDER_REVIEW:
                break;
            case NORMAL:
                expectAmountChange = availableAmount.add(usedAmount);
                break;
            case IN_CHANGE:
                log.info("checkRebateIncomeBill,dataId:{},approvalFlowTriggerType:{}", rebateIncomeId, approvalFlowTriggerType);
                if (ApprovalFlowTriggerType.INVALID == approvalFlowTriggerType) {
                    //作废审批中
                    if (isActive) {
                        expectLockedAmountChange = amount;
                        expectAmountChange = amount;
                    }
                } else if (ApprovalFlowTriggerType.UPDATE == approvalFlowTriggerType) {
                    //编辑审批中
                    if (isActive) {
                        expectAmountChange = availableAmount.add(usedAmount);
                    }
                }
                break;
        }

        boolean ok = (expectAmountChange.compareTo(rebateAmountChange) == 0 && expectLockedAmountChange.compareTo(rebateLockedAmountChange) == 0);
        if (!ok) {
            log.warn(
                    "checkRebateIncomeBill false,tenantId:{},rebateIncomeId:{},isYestActive:{},lifeStatus:{},rebateAmountChange:{},expectAmountChange:{},rebateLockedAmountChange:{},expectLockAmountChange:{}",
                    tenantId, rebateIncomeId, isActive, objectLifeStatus, rebateAmountChange, expectAmountChange, rebateLockedAmountChange, expectLockedAmountChange);
        }
        return ok;
    }

    private boolean checkRebateOutcomeBill(String tenantId, IObjectData objectData) {
        String rebateOutcomeId = objectData.getId();
        BigDecimal amount = ObjectDataUtil.getBigDecimal(objectData, RebateOutcomeDetailConstants.Field.Amount.apiName);
        CustomerAccountBillStatistics customerAccountBillStatistics = countBillStatistics(tenantId, BillTypeEnum.RebateOutcome.getType(), rebateOutcomeId);
        BigDecimal rebateAmountChange = BigDecimal.ZERO;
        BigDecimal rebateLockedAmountChange = BigDecimal.ZERO;
        if (Objects.nonNull(customerAccountBillStatistics)) {
            rebateAmountChange = customerAccountBillStatistics.getRebateAmountChange();
            rebateLockedAmountChange = customerAccountBillStatistics.getRebateLockedAmountChange();
        }
        BigDecimal expectAmountChange = BigDecimal.ZERO;
        BigDecimal expectLockedAmountChange = BigDecimal.ZERO;
        ObjectLifeStatus objectLifeStatus = ObjectDataExt.of(objectData).getLifeStatus();
        switch (objectLifeStatus) {
            case INEFFECTIVE:
            case INVALID:
                break;
            case UNDER_REVIEW:
                expectAmountChange = BigDecimal.ZERO;
                expectLockedAmountChange = amount;
                break;
            case NORMAL:
            case IN_CHANGE:
                expectAmountChange = amount.negate();
                expectLockedAmountChange = BigDecimal.ZERO;
                break;
        }
        boolean ok = (rebateAmountChange.compareTo(expectAmountChange) == 0 && rebateLockedAmountChange.compareTo(expectLockedAmountChange) == 0);
        if (!ok) {
            log.warn("checkRebateOutcomeBill false,tenantId:{},rebateOutcomeId:{},lifeStatus:{},rebateAmountChange:{},expectAmountChange:{},rebateLockedAmountChange:{},expectLockAmountChange:{}",
                    tenantId, rebateOutcomeId, objectLifeStatus, rebateAmountChange, expectAmountChange, rebateLockedAmountChange, expectLockedAmountChange);
        }
        return ok;
    }

    /**
     * countLtDateByRelateTypeAndId统计 过滤因为信用定时任务产生的流水
     */
    private boolean checkCreditBill(String tenantId, IObjectData objectData) {
        String id = objectData.getId();
        //        Date date = DateUtil.getTomorrowBeginDate(yesterday);
        ObjectLifeStatus objectLifeStatus = ObjectDataExt.of(objectData).getLifeStatus();
        Date startTime = objectData.get(CreditFileConstants.Field.StartTime.apiName, Date.class);
        Date endTime = objectData.get(CreditFileConstants.Field.EndTime.apiName, Date.class);
        String creditType = objectData.get(CreditFileConstants.Field.CreditType.apiName, String.class);
        //信用数据creditFileId的所有流水数据统计
        CustomerAccountBillStatistics customerAccountBillStatistics = countBillStatistics(tenantId, BillTypeEnum.Credit.getType(), id);
        BigDecimal creditAvailableQuoteChange = Objects.isNull(customerAccountBillStatistics) ? BigDecimal.ZERO : customerAccountBillStatistics.getCreditAvailableQuotaChange();
        BigDecimal expectCredit = BigDecimal.ZERO;
        boolean isActive = ObjectDataUtil.isCurrentTimeActive(startTime, endTime);//ObjectDataUtil.isYesterdayActive(startTime, endTime, yesterday);
        if (isActive) {
            switch (objectLifeStatus) {
                case UNDER_REVIEW:
                case INEFFECTIVE:
                case INVALID:
                    break;
                case IN_CHANGE:
                case NORMAL:
                    if (CreditTypeEnum.OfficialCredit.getValue().equals(creditType)) {
                        expectCredit = ObjectDataUtil.getBigDecimal(objectData, CreditFileConstants.Field.CreditQuota.apiName);
                    } else {
                        expectCredit = ObjectDataUtil.getBigDecimal(objectData, CreditFileConstants.Field.TemporaryCreditLimit.apiName);
                    }
                    break;
            }
        }
        boolean ok = expectCredit.compareTo(creditAvailableQuoteChange) == 0;
        if (!ok) {
            log.warn("checkCreditBill false,tenantId:{},creditId:{},isYestActive:{},lifeStatus:{},creditAvailableQuoteChange:{},expectCredit:{}", tenantId, id, isActive, objectLifeStatus,
                    creditAvailableQuoteChange, expectCredit);
        }
        return ok;
    }

    private String getCustomerAccountIdByCustomerId(String tenantId, String customerId) {
        String sql = String.format("select id from customer_account where tenant_id='%s' and customer_id='%s'", tenantId, customerId);
        try {
            List<Map> list = findBySql(tenantId, sql);
            if (CollectionUtils.isEmpty(list)) {
                log.warn("getCustomerAccountIdByCustomerId findBySql:{}, data isEmpty", sql);
                return null;
            }
            Map data = list.get(0);
            return (String) data.get("id");
        } catch (MetadataServiceException e) {
            log.warn("getCustomerAccountIdByCustomerId findBySql error,sql:{}", sql, e);
        }
        return null;
    }

    private String getCustomerAccountByRebateIncomeId(String tenantId, String rebateIncomeId) {
        String sql = String.format("select customer_account_id from rebate_income_detail where tenant_id='%s' and id='%s'", tenantId, rebateIncomeId);
        try {
            List<Map> list = findBySql(tenantId, sql);
            if (CollectionUtils.isEmpty(list)) {
                log.warn("getCustomerAccountByRebateIncomeId findBySql:{}, data isEmpty", sql);
                return null;
            }
            Map data = list.get(0);
            return (String) data.get("customer_account_id");
        } catch (MetadataServiceException e) {
            log.warn("getCustomerAccountByRebateIncomeId findBySql error,sql:{}", sql, e);
        }
        return null;
    }

    /**
     * 关联了退款，且lifeStatus=in_change的数据，需要查退款的审批流 关联回款明细的不用查询审批流
     */
    private Map<String, ApprovalFlowTriggerType> realApprovalInChange(String tenantId, String objectApiName, List<Map> objectDataList) {
        if (CollectionUtils.isEmpty(objectDataList)) {
            return Maps.newHashMap();
        }
        List<String> idList = Lists.newArrayList();
        Map<String, String> refundIdMap = Maps.newHashMap();
        for (Map map : objectDataList) {
            IObjectData objectData = new ObjectData(map);
            if (ObjectDataExt.of(objectData).getLifeStatus() != ObjectLifeStatus.IN_CHANGE) {
                continue;
            }
            String id = objectData.getId();
            if (PrepayDetailConstants.API_NAME.equals(objectApiName)) {
                String orderPaymentId = ObjectDataUtil.getReferenceId(objectData, PrepayDetailConstants.Field.OrderPayment.apiName);
                String refundId = ObjectDataUtil.getReferenceId(objectData, PrepayDetailConstants.Field.Refund.apiName);
                if (StringUtils.isNotEmpty(orderPaymentId)) {
                    //回款明细的状态是跟回款一致，所以需要查回款的审批流
                    //对应回款明细的不需要查询审批类型
                } else if (StringUtils.isNotEmpty(refundId)) {
                    refundIdMap.put(refundId, id);
                } else {
                    idList.add(id);
                }
            } else if (RebateIncomeDetailConstants.API_NAME.equals(objectApiName)) {
                String refundId = ObjectDataUtil.getReferenceId(objectData, RebateIncomeDetailConstants.Field.Refund.apiName);
                if (StringUtils.isNotEmpty(refundId)) {
                    refundIdMap.put(refundId, id);
                } else {
                    idList.add(id);
                }
            } else {
                idList.add(id);
            }
        }
        List<String> refundIdList = Lists.newArrayList(refundIdMap.keySet());
        idList.addAll(refundIdList);
        Map<String, ApprovalFlowTriggerType> approvalFlowTriggerTypeMap = getInChangeDataApprovalFlowType(tenantId, idList);
        for (String refundId : refundIdList) {
            ApprovalFlowTriggerType approvalFlowTriggerType = approvalFlowTriggerTypeMap.remove(refundId);
            if (Objects.nonNull(approvalFlowTriggerType)) {
                approvalFlowTriggerTypeMap.put(refundIdMap.get(refundId), approvalFlowTriggerType);
            }
        }
        return approvalFlowTriggerTypeMap;
    }

    private Map<String, ApprovalFlowTriggerType> getInChangeDataApprovalFlowType(String tenantId, List<String> dataIds) {
        if (CollectionUtils.isEmpty(dataIds)) {
            return Maps.newHashMap();
        }
        GetCurInstanceStateModel.Arg arg = new GetCurInstanceStateModel.Arg();
        arg.setObjectIds(dataIds);
        Map<String, String> headers = Maps.newHashMap();
        headers.put("x-tenant-id", tenantId);
        headers.put("x-user-id", User.SUPPER_ADMIN_USER_ID);
        GetCurInstanceStateModel.Result getCurInstancesStateResult = approvalInitProxy.getCurInstanceStateByObjectIds(arg, headers);
        log.info("getCurInstanceStateByObjectIds,headers:{},arg:{},result:{}", headers, arg, getCurInstancesStateResult);
        if (!getCurInstancesStateResult.success()) {
            Map<String, ApprovalFlowTriggerType> approvalFlowTriggerTypeMap = Maps.newHashMap();
            dataIds.forEach(id -> approvalFlowTriggerTypeMap.put(id, ApprovalFlowTriggerType.INVALID));
            return approvalFlowTriggerTypeMap;
        } else {
            return getCurInstancesStateResult.getData().stream().collect(Collectors.toMap(GetCurInstanceStateModel.IntanceStatus::getObjectId, instanceStatus -> {
                String triggerType = instanceStatus.getTriggerType();
                return getInChangeApprovalFlowTriggerType(triggerType);
            }));
        }
    }

    private ApprovalFlowTriggerType getInChangeApprovalFlowTriggerType(String triggerType) {
        for (ApprovalFlowTriggerType approvalFlowTriggerType : ApprovalFlowTriggerType.values()) {
            if (approvalFlowTriggerType.getId().equals(triggerType)) {
                return approvalFlowTriggerType;
            }
        }
        return ApprovalFlowTriggerType.INVALID;
    }

    private AbnormalTypeEnum getAbnormalType(String objectApiName) {
        if (CreditFileConstants.API_NAME.equals(objectApiName)) {
            return AbnormalTypeEnum.Credit;
        } else if (RebateOutcomeDetailConstants.API_NAME.equals(objectApiName)) {
            return AbnormalTypeEnum.RebateOutcome;
        } else if (RebateIncomeDetailConstants.API_NAME.equals(objectApiName)) {
            return AbnormalTypeEnum.RebateIncome;
        } else if (PrepayDetailConstants.API_NAME.equals(objectApiName)) {
            return AbnormalTypeEnum.Prepay;
        }
        return null;
    }

    private DailyBill generateDateDailyBill(User user, String customerAccountId, Date date) {
        IObjectData customerAccountData = serviceFacade.findObjectDataIncludeDeleted(user, customerAccountId, CustomerAccountConstants.API_NAME);
        String tenantId = user.getTenantId();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String yesterdayDateStr = simpleDateFormat.format(date);

        DailyBillModel.CreateArg createArg = new DailyBillModel.CreateArg();
        createArg.setTenantId(tenantId);
        createArg.setCustomerAccountId(customerAccountId);
        createArg.setBillDate(date.getTime());
        createArg.setPrepayBalance(ObjectDataUtil.getBigDecimal(customerAccountData, CustomerAccountConstants.Field.PrepayBalance.apiName));
        createArg.setPrepayLockedBalance(ObjectDataUtil.getBigDecimal(customerAccountData, CustomerAccountConstants.Field.PrepayLockedBalance.apiName));
        createArg.setRebateBalance(ObjectDataUtil.getBigDecimal(customerAccountData, CustomerAccountConstants.Field.RebateBalance.apiName));
        createArg.setRebateLockedBalance(ObjectDataUtil.getBigDecimal(customerAccountData, CustomerAccountConstants.Field.RebateLockedBalance.apiName));
        createArg.setCreditAvailableQuota(ObjectDataUtil.getBigDecimal(customerAccountData, CustomerAccountConstants.Field.CreditAvailableQuota.apiName));
        createArg.setRemark(String.format("生成[TenantId:%s-CustomerAccountId:%s] %s日账单", tenantId, customerAccountId, yesterdayDateStr));
        createArg.setCreateBy(user.getUserId());

        DailyBillModel.Result createResult = dailyBillProxy.create(createArg);
        DailyBill dailyBill = null;
        if (createResult.isSuccess()) {
            dailyBill = new DailyBill();
            dailyBill.setTenantId(tenantId);
            dailyBill.setCustomerAccountId(customerAccountId);
            dailyBill.setBillDate(new Date(createResult.getValue().getBillDate()));
            dailyBill.setPrepayBalance(createResult.getValue().getPrepayBalance());
            dailyBill.setPrepayLockedBalance(createResult.getValue().getPrepayLockedBalance());
            dailyBill.setRebateBalance(createResult.getValue().getRebateBalance());
            dailyBill.setRebateLockedBalance(createResult.getValue().getRebateLockedBalance());
            dailyBill.setCreditAvailableQuota(createResult.getValue().getCreditAvailableQuota());
            dailyBill.setRemark(createResult.getValue().getRemark());
            dailyBill.setCreateBy(createResult.getValue().getCreateBy());
            dailyBill.setCreateTime(new Date(createResult.getValue().getCreateTime()));
        }
        return dailyBill;
    }

    // 忽略日账单信用可用额度检查
    private int consistent(CustomerAccountBillStatistics customerAccountBillStatistics, DailyBill yesterdayDailyBill, DailyBill yesterdayYestDailyBill) {
        BigDecimal prepayAmountChange = customerAccountBillStatistics.getPrepayAmountChange();
        BigDecimal prepayLockedAmountChange = customerAccountBillStatistics.getPrepayLockedAmountChange();
        BigDecimal rebateAmountChange = customerAccountBillStatistics.getRebateAmountChange();
        BigDecimal rebateLockedAmountChange = customerAccountBillStatistics.getRebateLockedAmountChange();

        BigDecimal diffPrepayAmount = diff(yesterdayDailyBill.getPrepayBalance(), yesterdayYestDailyBill.getPrepayBalance());
        BigDecimal diffPrepayLockedAmount = diff(yesterdayDailyBill.getPrepayLockedBalance(), yesterdayYestDailyBill.getPrepayLockedBalance());
        BigDecimal diffRebateAmount = diff(yesterdayDailyBill.getRebateBalance(), yesterdayYestDailyBill.getRebateBalance());
        BigDecimal diffRebateLockedAmount = diff(yesterdayDailyBill.getRebateLockedBalance(), yesterdayYestDailyBill.getRebateLockedBalance());
        int consistent = 0;
        if (prepayAmountChange.compareTo(diffPrepayAmount) != 0) {
            consistent = 1;
        }
        if (prepayLockedAmountChange.compareTo(diffPrepayLockedAmount) != 0) {
            consistent = 10 + consistent;
        }
        if (rebateAmountChange.compareTo(diffRebateAmount) != 0) {
            consistent = 100 + consistent;
        }
        if (rebateLockedAmountChange.compareTo(diffRebateLockedAmount) != 0) {
            consistent = 1000 + consistent;
        }
        return consistent;
    }

    private BigDecimal diff(BigDecimal yesterday, BigDecimal yesterdayYest) {
        return yesterday.subtract(yesterdayYest);
    }

    private String getDailyBillRemark(String tenantId, String customerAccountId, Date date, int consistentType) {
        StringBuilder remarkBuilder = new StringBuilder(String.format("TenantID[%s]-CustomerAccountID[%s]-Date[%s]", tenantId, customerAccountId, DateUtil.formatYYYYMMDD(date)));
        int n = 1;
        while (consistentType > 0) {
            int l = consistentType % 10;
            if (l > 0) {
                switch (n) {
                    case 1:
                        remarkBuilder.append("，预存款余额日账单异常");
                        break;
                    case 2:
                        remarkBuilder.append("，预存款锁定余额日账单异常");
                        break;
                    case 3:
                        remarkBuilder.append("，返利余额日账单异常");
                        break;
                    case 4:
                        remarkBuilder.append("，返利锁定余额日账单异常");
                        break;
                    case 5:
                        remarkBuilder.append("，信用额度日账单异常");
                        break;
                    default:
                        break;
                }
            }
            n++;
            consistentType = consistentType / 10;
        }
        return remarkBuilder.toString();
    }

    private String getMsgContent(Date yesterdayDate) {
        DailyAbnormalCustomerAccountModel.CountArg countArg = new DailyAbnormalCustomerAccountModel.CountArg();
        countArg.setYesterdayDate(yesterdayDate.getTime());
        DailyAbnormalCustomerAccountModel.CountResult countResult = dailyAbnormalCustomerAccountProxy.count(countArg);
        if (!countResult.isSuccess() || Objects.isNull(countResult.getValue())) {
            log.warn("daily abnormal customer account count failed, result={}", countResult);
            return null;
        }
        int tenantCount = countResult.getValue().getTenantCount();
        int customerAccountCount = countResult.getValue().getCustomerAccountCount();
        Map<Integer, Integer> abnormalTypeCounts = countResult.getValue().getAbnormalTypeCounts();
        StringBuilder stringBuilder = new StringBuilder();
        for (AbnormalTypeEnum abnormalTypeEnum : AbnormalTypeEnum.values()) {
            int count = abnormalTypeCounts.getOrDefault(abnormalTypeEnum.getType(), 0);
            if (count <= 0) {
                continue;
            }
            switch (abnormalTypeEnum) {
                case Prepay:
                    stringBuilder.append(String.format("\n预存款异常账户数：%d", count));
                    break;
                case RebateIncome:
                    stringBuilder.append(String.format("\n返利收入异常账户数：%d", count));
                    break;
                case Credit:
                    stringBuilder.append(String.format("\n信用异常账户数：%d", count));
                    break;
                case RebateOutcome:
                    stringBuilder.append(String.format("\n返利支出异常账户数：%d", count));
                    break;
                case DailyBill:
                    stringBuilder.append(String.format("\n日账单异常账户数：%d", count));
                    break;
                case CustomerAccountCreate:
                    stringBuilder.append(String.format("\n客户账户新建异常数：%d", count));
                    break;
                case Payment:
                    stringBuilder.append(String.format("\n回款关联预存款返利异常账户数：%d", count));
                    break;
                case Refund:
                    stringBuilder.append(String.format("\n退款关联预存款返利异常账户数：%d", count));
                    break;
                default:
                    break;
            }
        }
        return String.format("日期：%s\n客户账户异常企业数：%d\n客户账户异常账户数：%d%s", DateUtil.formatYYYYMMDD(yesterdayDate), tenantCount, customerAccountCount, stringBuilder.toString());
    }

    public void createDailyAbnormal(User user, String customerAccountId, Date billDate, String relateId, AbnormalTypeEnum abnormalTypeEnum, String remark) {
        try {
            DailyAbnormalCustomerAccountModel.CreateArg createArg = new DailyAbnormalCustomerAccountModel.CreateArg();
            createArg.setTenantId(user.getTenantId());
            createArg.setCustomerAccountId(customerAccountId);
            createArg.setBillDate(billDate.getTime());
            createArg.setRelateType(abnormalTypeEnum.getType());
            createArg.setRelateId(relateId);
            createArg.setRemark(remark);
            createArg.setCreateBy(user.getUserId());
            dailyAbnormalCustomerAccountProxy.create(createArg);
        } catch (Exception e) {
            log.warn("createDailyAbnormal error,tenantId:{},customerAccountId:{},billDate:{},relateType:{},relateId:{},remark:{}",
                    user.getTenantId(), customerAccountId, billDate, abnormalTypeEnum.getType(), relateId, remark, e);
        }
    }
}
