package com.facishare.crm.customeraccount.predefine.action;

import com.facishare.crm.consts.CreditRuleDetailConst;
import com.facishare.crm.consts.CreditRuleMatchRecordConst;
import com.facishare.crm.customeraccount.constants.CAI18NKey;
import com.facishare.crm.customeraccount.predefine.manager.CreditManager;
import com.facishare.crmcommon.util.SearchQueryBuilder;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.List;

public class CreditRuleInvalidAction extends StandardInvalidAction {
    private final CreditManager creditManager = SpringUtil.getContext().getBean(CreditManager.class);

    private String creditOccupiedRuleId;

    @Override
    protected void before(Arg arg) {
        super.before(arg);

        this.creditOccupiedRuleId = this.arg.getObjectDataId();
        SearchTemplateQuery query = SearchQueryBuilder.builder().eq(CreditRuleMatchRecordConst.F.CreditOccupiedRuleId.apiName, creditOccupiedRuleId).build(0, 2);
        List<IObjectData> creditRuleMatchRecordList = serviceFacade.findBySearchQuery(actionContext.getUser(), CreditRuleMatchRecordConst.API_NAME, query).getData();
        if (CollectionUtils.notEmpty(creditRuleMatchRecordList)) {
            throw new ValidateException(I18N.text(CAI18NKey.CREDIT_RULE_USED_NOT_INVALID, this.objectDataList.get(0).getName()));
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        creditManager.disablePluginInstanceByCreditRule(actionContext.getUser(),this.creditOccupiedRuleId,this.detailObjectData.get(CreditRuleDetailConst.API_NAME));
        return result;
    }
}
