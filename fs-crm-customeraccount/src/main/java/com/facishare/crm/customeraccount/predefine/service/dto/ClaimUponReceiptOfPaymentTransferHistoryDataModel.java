package com.facishare.crm.customeraccount.predefine.service.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

public class ClaimUponReceiptOfPaymentTransferHistoryDataModel {

    @Data
    public static class Arg {
        private List<String> tenantIds;
    }

    @AllArgsConstructor
    @Data
    public static class Result {
        private List<String> successTenantIds;
    }
}
