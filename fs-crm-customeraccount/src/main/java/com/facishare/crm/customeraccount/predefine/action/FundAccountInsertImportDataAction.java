package com.facishare.crm.customeraccount.predefine.action;

import com.facishare.crm.customeraccount.constants.FundAccountConstants;
import com.facishare.crm.customeraccount.enums.AccessModuleEnum;
import com.facishare.crm.customeraccount.enums.FundAccountAccountTypeEnum;
import com.facishare.crm.customeraccount.enums.FundAccountTypeEnum;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportDataAction;
import com.facishare.paas.metadata.api.IObjectData;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class FundAccountInsertImportDataAction extends StandardInsertImportDataAction {

    @Override
    protected void customInit(List<ImportData> dataList) {
        super.customInit(dataList);
        CollectionUtils.nullToEmpty(dataList).forEach(x -> {
            IObjectData objectData = x.getData();
            String accessModule = objectData.get(FundAccountConstants.Field.AccessModule.apiName, String.class);
            String accountType = objectData.get(FundAccountConstants.Field.AccountType.apiName, String.class);
            if (StringUtils.isEmpty(accessModule)) {
                objectData.set(FundAccountConstants.Field.AccessModule.apiName, AccessModuleEnum.DEFAULT.value);
            }
            if (StringUtils.isEmpty(accountType)) {
                objectData.set(FundAccountConstants.Field.AccountType.apiName, FundAccountAccountTypeEnum.Amount.value);
            }
            objectData.set(FundAccountConstants.Field.Type.apiName, FundAccountTypeEnum.UserDefine.getValue());
        });
    }
}
