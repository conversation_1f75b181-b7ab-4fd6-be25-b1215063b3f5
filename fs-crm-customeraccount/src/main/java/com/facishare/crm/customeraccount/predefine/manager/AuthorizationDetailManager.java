package com.facishare.crm.customeraccount.predefine.manager;

import com.facishare.crm.customeraccount.constants.*;
import com.facishare.crm.customeraccount.enums.FAccountAuthAuthorizedTypeEnum;
import com.facishare.crm.customeraccount.predefine.service.dto.AccessOutcomeAuthModel;
import com.facishare.crmcommon.describebuilder.CurrencyFieldDescribeBuilder;
import com.facishare.crmcommon.manager.CommonObjDataManager;
import com.facishare.crmcommon.util.ObjectDataUtil;
import com.facishare.crmcommon.util.ObjectFieldUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;

import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.CurrencyFieldDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class AuthorizationDetailManager extends CommonManager {

    @Autowired
    private IObjectDescribeService objectDescribeService;
    @Autowired
    private CommonObjDataManager commonObjDataManager;


    public List<IObjectData> query(String tenantId, String authorizedType, String authorizedObjectApiName) {
        User admin = new User(tenantId, "-10000");
        return query(admin, authorizedType, authorizedObjectApiName);
    }

    public List<IObjectData> query(User user, String authorizedType, String authorizedObjectApiName) {
        List<IFilter> filterList = Lists.newArrayList();

        IFilter authorizedTypeFilter = new Filter();
        authorizedTypeFilter.setFieldName(FAccountAuthorizationConstants.Field.AuthorizedType.apiName);
        authorizedTypeFilter.setFieldValues(Lists.newArrayList(authorizedType));
        authorizedTypeFilter.setOperator(Operator.EQ);
        //  authorizedTypeFilter.setValueType(7);
        authorizedTypeFilter.setIsMasterField(true);
        filterList.add(authorizedTypeFilter);

        IFilter authorizedObjectFilter = new Filter();
        authorizedObjectFilter.setFieldName(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName);
        authorizedObjectFilter.setFieldValues(Lists.newArrayList(authorizedObjectApiName));
        authorizedObjectFilter.setOperator(Operator.IN);
        //  authorizedObjectFilter.setValueType(7);
        authorizedObjectFilter.setIsMasterField(true);
        filterList.add(authorizedObjectFilter);

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(filterList);
        query.setOffset(0);
        query.setLimit(200);

        return serviceFacade.findBySearchQuery(user, AuthorizationDetailConstant.API_NAME, query).getData();
    }

    public List<IObjectData> query(User user, List<String> fAccountAuthorizationIds) {
        if (CollectionUtils.empty(fAccountAuthorizationIds)) {
            return Lists.newArrayList();
        }
        List<IFilter> filterList = Lists.newArrayList();

        IFilter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(AuthorizationDetailConstant.Field.FAccountAuthorizationId.apiName);
        lifeStatusFilter.setFieldValues(fAccountAuthorizationIds);
        lifeStatusFilter.setOperator(Operator.IN);
        filterList.add(lifeStatusFilter);

        IFilter deleteStatusFilter = new Filter();
        deleteStatusFilter.setFieldName(ObjectData.IS_DELETED);
        deleteStatusFilter.setFieldValues(Lists.newArrayList("0"));
        deleteStatusFilter.setOperator(Operator.EQ);
        filterList.add(deleteStatusFilter);

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(filterList);
        query.setOffset(0);
        query.setLimit(1000);

        return serviceFacade.findBySearchQuery(user, AuthorizationDetailConstant.API_NAME, query).getData();
    }


    public List<IObjectData> query(User user, List<String> fAccountAuthorizationIds, List<String> authorizeAccountIds) {
        if (CollectionUtils.empty(fAccountAuthorizationIds) || CollectionUtils.empty(authorizeAccountIds)) {
            return Lists.newArrayList();
        }
        List<IFilter> filterList = Lists.newArrayList();

        IFilter fAccountAuthorizationIdFilter = new Filter();
        fAccountAuthorizationIdFilter.setFieldName(AuthorizationDetailConstant.Field.FAccountAuthorizationId.apiName);
        fAccountAuthorizationIdFilter.setFieldValues(fAccountAuthorizationIds);
        fAccountAuthorizationIdFilter.setOperator(Operator.IN);
        filterList.add(fAccountAuthorizationIdFilter);

        IFilter authorizeAccountIdFilter = new Filter();
        authorizeAccountIdFilter.setFieldName(AuthorizationDetailConstant.Field.AuthorizeAccountId.apiName);
        authorizeAccountIdFilter.setFieldValues(authorizeAccountIds);
        authorizeAccountIdFilter.setOperator(Operator.IN);
        filterList.add(authorizeAccountIdFilter);

        IFilter deleteStatusFilter = new Filter();
        deleteStatusFilter.setFieldName(ObjectData.IS_DELETED);
        deleteStatusFilter.setFieldValues(Lists.newArrayList("0"));
        deleteStatusFilter.setOperator(Operator.EQ);
        filterList.add(deleteStatusFilter);

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(filterList);
        query.setOffset(0);
        query.setLimit(1000);

        return serviceFacade.findBySearchQuery(user, AuthorizationDetailConstant.API_NAME, query).getData();
    }

    public List<IObjectData> getAuthorizationDetailDatas(String tenantId, String fAccountAuthorizationId, List<String> fieldApiNames) {
        User admin = new User(tenantId, "-10000");
        String objectApiName = AuthorizationDetailConstant.API_NAME;

        List<IFilter> filters = new ArrayList<>();

        IFilter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(AuthorizationDetailConstant.Field.FAccountAuthorizationId.apiName);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(fAccountAuthorizationId));
        lifeStatusFilter.setOperator(Operator.EQ);
        filters.add(lifeStatusFilter);

        IFilter deleteStatusFilter = new Filter();
        deleteStatusFilter.setFieldName(ObjectData.IS_DELETED);
        deleteStatusFilter.setFieldValues(Lists.newArrayList("0"));
        deleteStatusFilter.setOperator(Operator.EQ);
        filters.add(deleteStatusFilter);

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(filters);
        query.setOffset(0);
        query.setLimit(200);
        QueryResult<IObjectData> result = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(admin, objectApiName, query, fieldApiNames);
        return result.getData();
    }

    public List<String> getContainFundAccountIds(String tenantId, List<String> fAccountAuthorizationIds, List<String> authorizeAccountIds) {
        User admin = User.systemUser(tenantId);
        List<IObjectData> authDetails = query(admin, fAccountAuthorizationIds, authorizeAccountIds);
        if (CollectionUtils.empty(authDetails)) {
            return Lists.newArrayList();
        }

        return authDetails.stream().map(d -> d.get(AuthorizationDetailConstant.Field.AuthorizeAccountId.apiName, String.class)).collect(Collectors.toList());
    }

    public void createTradeAmountField(String tenantId, List<IObjectData> needCreateTradeAmountFieldAuthDetailDatas, IObjectDescribe authorizedObjectDescribe, String authorizedObjectApiName) {
        if (CollectionUtils.empty(needCreateTradeAmountFieldAuthDetailDatas)) {
            return;
        }

        //生成字段apiName
        List<String> tradeAmountFieldApiNames = ObjectFieldUtil.generateFieldApiName(needCreateTradeAmountFieldAuthDetailDatas.size(), authorizedObjectDescribe);
        Map<String, String> authDetailId2FieldLabelMap = getAuthDetailId2FieldLabelMap(tenantId, needCreateTradeAmountFieldAuthDetailDatas, authorizedObjectDescribe);


        //创建自定义字段
        List<IFieldDescribe> fieldDescribes = Lists.newArrayList();
        int i = 0;
        for (IObjectData authDetail : needCreateTradeAmountFieldAuthDetailDatas) {
            String tradeAmountFieldApiName = tradeAmountFieldApiNames.get(i);
            i++;

            String authDetailId = authDetail.getId();

            CurrencyFieldDescribe fieldDescribe = CurrencyFieldDescribeBuilder.builder()
                    .apiName(tradeAmountFieldApiName).label(authDetailId2FieldLabelMap.get(authDetailId))
                    .required(false).maxLength(14).length(12).decimalPlaces(2).roundMode(4).currencyUnit("￥").build(); // 本位币是美元的企业，页面上加自定义字段，currencyUnit也是￥
            fieldDescribe.setDefineType("custom");
            fieldDescribe.setStatus("new");
            fieldDescribe.setIsExtend(true);

            fieldDescribes.add(fieldDescribe);

            authDetail.set(AuthorizationDetailConstant.Field.TradeAmountFieldapiname.apiName, tradeAmountFieldApiName);
            authDetail.setDescribeApiName(AuthorizationDetailConstant.API_NAME);
            authDetail.setTenantId(tenantId);
        }
        try {
            objectDescribeService.addCustomFieldDescribe(authorizedObjectDescribe, fieldDescribes);
        } catch (MetadataServiceException e) {
            log.warn("createTradeAmountField, addCustomFieldDescribe error, tenantId[{}], describe[{}], fieldDescribe[{}]", tenantId, authorizedObjectDescribe, fieldDescribes);
            throw new ValidateException(I18N.text(CAI18NKey.CREATE_CUSTOM_FIELD_FAILED));
        }

        //更新AuthorizationDetailObj
        User admin = new User(tenantId, "-10000");
        List<String> updateFieldList = Lists.newArrayList(AuthorizationDetailConstant.Field.TradeAmountFieldapiname.apiName);
        serviceFacade.batchUpdateByFields(admin, needCreateTradeAmountFieldAuthDetailDatas, updateFieldList);
    }

    /**
     * objectApiName对象创建tradeAmountFieldApiNames 这些金额字段
     */
    public void createTradeAmountField(String tenantId, List<String> tradeAmountFieldApiNames, Map<String, String> fieldApiNames2FundAccountId, IObjectDescribe authorizedObjectDescribe) {
        if (CollectionUtils.empty(tradeAmountFieldApiNames)) {
            return;
        }

        //生成字段apiName
        List<String> authorizeAccountIds = Lists.newArrayList(fieldApiNames2FundAccountId.values());
        Map<String, String> fundAccountId2FieldLabelMap = getFundAccountId2FieldLabelMap(tenantId, authorizeAccountIds, authorizedObjectDescribe);

        //创建自定义字段
        List<IFieldDescribe> fieldDescribes = Lists.newArrayList();
        for (String tradeAmountFieldApiName : tradeAmountFieldApiNames) {
            String authorizeAccountId = fieldApiNames2FundAccountId.get(tradeAmountFieldApiName);
            CurrencyFieldDescribe fieldDescribe = CurrencyFieldDescribeBuilder.builder()
                    .apiName(tradeAmountFieldApiName).label(fundAccountId2FieldLabelMap.get(authorizeAccountId))
                    .required(false).maxLength(14).length(12).decimalPlaces(2).roundMode(4).currencyUnit("￥").build(); // 本位币是美元的企业，页面上加自定义字段，currencyUnit也是￥
            fieldDescribe.setDefineType("custom");
            fieldDescribe.setStatus("new");
            fieldDescribe.setIsExtend(true);

            fieldDescribes.add(fieldDescribe);
        }
        try {
            objectDescribeService.addCustomFieldDescribe(authorizedObjectDescribe, fieldDescribes);
        } catch (MetadataServiceException e) {
            log.warn("createTradeAmountField, addCustomFieldDescribe error, tenantId[{}], describe[{}], fieldDescribe[{}]", tenantId, authorizedObjectDescribe, fieldDescribes);
            throw new ValidateException(I18N.text(CAI18NKey.CREATE_CUSTOM_FIELD_FAILED));
        }
    }

    /**
     * 新增新的授权明细
     */
    public void addNewDetails(User user, String fAccountAuthorizationId, AccessOutcomeAuthModel.Arg arg) {
        String authorizedObjectApiName = arg.getObjectData().getAuthorizedObjectApiName();
        if (CollectionUtils.empty(arg.getDetails().get(authorizedObjectApiName))) {
            return;
        }

        List<AccessOutcomeAuthModel.DetailFields> detailFields = arg.getDetails().get(authorizedObjectApiName);
        if (CollectionUtils.empty(detailFields)) {
            return;
        }

        List<String> authorizeAccountIds = detailFields.stream().map(AccessOutcomeAuthModel.DetailFields::getAuthorizeAccountId).collect(Collectors.toList());

        addNewDetails(user, fAccountAuthorizationId, authorizedObjectApiName, authorizeAccountIds);
    }

    public void addNewDetails(User user, String fAccountAuthorizationId, String authorizedObjectApiName, List<String> newFundAccountIds) {
        if (CollectionUtils.empty(newFundAccountIds)) {
            return;
        }

        //查已有的从对象
        List<IObjectData> oldDetails = query(user.getTenantId(), FAccountAuthAuthorizedTypeEnum.Outcome.getValue(), authorizedObjectApiName);
        List<String> oldAuthorizeAccountIds = oldDetails.stream().map(d -> d.get(AuthorizationDetailConstant.Field.AuthorizeAccountId.apiName, String.class)).collect(Collectors.toList());

        //排除已有的
        List<String> needAddFundAccountIds = newFundAccountIds.stream().filter(d -> !oldAuthorizeAccountIds.contains(d)).collect(Collectors.toList());

        if (CollectionUtils.empty(needAddFundAccountIds)) {
            return;
        }
        //保存新数据
        List<IObjectData> newAuthorizationDetails = new ArrayList<>();
        for (String needAddFundAccountId : needAddFundAccountIds) {
            IObjectData authorizationDetail = ObjectDataUtil.getBaseObjectData(user, AuthorizationDetailConstant.API_NAME);
            authorizationDetail.set(AuthorizationDetailConstant.Field.FAccountAuthorizationId.apiName, fAccountAuthorizationId);
            authorizationDetail.set(AuthorizationDetailConstant.Field.AuthorizeAccountId.apiName, needAddFundAccountId);
            authorizationDetail.set("record_type", "default__c");
            newAuthorizationDetails.add(authorizationDetail);
        }

        serviceFacade.bulkSaveObjectData(newAuthorizationDetails, user);
    }

    /**
     * 交易金额字段的label
     */
    private Map<String, String> getAuthDetailId2FieldLabelMap(String tenantId, List<IObjectData> needCreateTradeAmountFieldAuthDetailDatas, IObjectDescribe authorizedObjectDescribe) {
        Map<String, String> result = new HashMap<>();

        //查询账户名称
        List<String> accountIds = needCreateTradeAmountFieldAuthDetailDatas.stream().map(d -> d.get(AuthorizationDetailConstant.Field.AuthorizeAccountId.apiName, String.class)).collect(Collectors.toList());
        if (CollectionUtils.empty(accountIds)) {
            return result;
        }
        List<IObjectData> accountDatas = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, accountIds, FundAccountConstants.API_NAME);
        Map<String, String> accountId2Name = accountDatas.stream().collect(Collectors.toMap(IObjectData::getId,
                d -> d.get(FundAccountConstants.Field.Name.apiName, String.class)));

        for (IObjectData authDetail : needCreateTradeAmountFieldAuthDetailDatas) {
            String authDetailId = authDetail.getId();
            String authorizeAccountId = authDetail.get(AuthorizationDetailConstant.Field.AuthorizeAccountId.apiName, String.class);
            String accountName = accountId2Name.get(authorizeAccountId);
            String fieldLabel = getFieldLabel(accountName, authorizedObjectDescribe);
            result.put(authDetailId, fieldLabel);
        }

        return result;
    }

    /**
     * 交易金额字段的label
     */
    private Map<String, String> getFundAccountId2FieldLabelMap(String tenantId, List<String> fundAccountIds, IObjectDescribe authorizedObjectDescribe) {
        Map<String, String> result = new HashMap<>();

        //查询账户名称
        if (CollectionUtils.empty(fundAccountIds)) {
            return result;
        }
        List<IObjectData> accountDatas = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, fundAccountIds, FundAccountConstants.API_NAME);
        Map<String, String> accountId2Name = accountDatas.stream().collect(Collectors.toMap(IObjectData::getId,
                d -> d.get(FundAccountConstants.Field.Name.apiName, String.class)));

        for (String fundAccountId : fundAccountIds) {
            String accountName = accountId2Name.get(fundAccountId);
            String fieldLabel = getFieldLabel(accountName, authorizedObjectDescribe);
            result.put(fundAccountId, fieldLabel);
        }

        return result;
    }

    private String getFieldLabel(String accountName, IObjectDescribe authorizedObjectDescribe) {
        String fieldLabel = I18N.text(CAI18NKey.AUTH_DETAIL_REDUCE_AMOUNT_LABEL, accountName);
        if (!ObjectFieldUtil.hasFieldLabel(authorizedObjectDescribe, fieldLabel)) {
            return fieldLabel;
        }
        int num = 10000;
        int i = 1;
        while (i < num) {
            String newFieldLabel = fieldLabel + i;
            if (!ObjectFieldUtil.hasFieldLabel(authorizedObjectDescribe, newFieldLabel)) {
                return newFieldLabel;
            }

            i++;
        }

        return fieldLabel + System.currentTimeMillis();
    }

    public Map<String, String> getAuthorizeAccountId2TradeAmountFieldApiName(List<IObjectData> authorizationDetailDatas) {
        Map<String, String> authorizeAccountId2TradeAmountFieldApiName = new HashMap<>();

        if (CollectionUtils.empty(authorizationDetailDatas)) {
            return authorizeAccountId2TradeAmountFieldApiName;
        }

        for (IObjectData a : authorizationDetailDatas) {
            String authorizeAccountId = a.get(AuthorizationDetailConstant.Field.AuthorizeAccountId.apiName, String.class);
            String tradeAmountFieldApiName = a.get(AuthorizationDetailConstant.Field.TradeAmountFieldapiname.apiName, String.class);
            if (!Strings.isNullOrEmpty(authorizeAccountId) && !Strings.isNullOrEmpty(tradeAmountFieldApiName)) {
                authorizeAccountId2TradeAmountFieldApiName.put(authorizeAccountId, tradeAmountFieldApiName);
            }
        }

        return authorizeAccountId2TradeAmountFieldApiName;
    }

    /**
     * sourceAuthDetailDatas 复制到 targetTenantId 的 targetAccountAuthDataId 的从对象上(已经有的账户排除掉）
     */
    public void copy(String targetTenantId, String targetAccountAuthDataId, List<IObjectData> sourceAuthDetailDatas, List<IObjectData> targetExistAuthDetailDatas, boolean useSourceId) {
        User admin = User.systemUser(targetTenantId);

        //已经存在的账户
        List<String> targetExistFundAccountIds = targetExistAuthDetailDatas == null ? Lists.newArrayList() : targetExistAuthDetailDatas.stream()
                .map(d -> d.get(AuthorizationDetailConstant.Field.AuthorizeAccountId.apiName, String.class)).collect(Collectors.toList());

        //保存新数据
        List<IObjectData> newAuthorizationDetails = new ArrayList<>();
        for (IObjectData sourceAuthDetailData : sourceAuthDetailDatas) {
            String sourceAuthorizeAccountId = sourceAuthDetailData.get(AuthorizationDetailConstant.Field.AuthorizeAccountId.apiName, String.class);
            if (targetExistFundAccountIds.contains(sourceAuthorizeAccountId)) {
                continue;
            }

            IObjectData authorizationDetail = ObjectDataUtil.getBaseObjectData(admin, AuthorizationDetailConstant.API_NAME);
            boolean idHasUse = commonObjDataManager.idHasUse(targetTenantId, AuthorizationDetailConstant.API_NAME, sourceAuthDetailData.getId());
            log.info("copy tenantId[{}], objectApiName[{}], dataId[{}], idHasUse[{}]", targetTenantId, AuthorizationDetailConstant.API_NAME, sourceAuthDetailData.getId(), idHasUse);
            if (useSourceId && !idHasUse) {
                authorizationDetail.set(SystemConstants.Field.Id.apiName, sourceAuthDetailData.getId());
            }
            authorizationDetail.set(AuthorizationDetailConstant.Field.Name.apiName, sourceAuthDetailData.getName());
            authorizationDetail.set(AuthorizationDetailConstant.Field.FAccountAuthorizationId.apiName, targetAccountAuthDataId);
            authorizationDetail.set(AuthorizationDetailConstant.Field.AuthorizeAccountId.apiName, sourceAuthorizeAccountId);
            authorizationDetail.set(AuthorizationDetailConstant.Field.IsDefaultEntryAccount.apiName, sourceAuthDetailData.get(AuthorizationDetailConstant.Field.IsDefaultEntryAccount.apiName));
            authorizationDetail.set(AuthorizationDetailConstant.Field.Status.apiName, sourceAuthDetailData.get(AuthorizationDetailConstant.Field.Status.apiName));
            authorizationDetail.set(AuthorizationDetailConstant.Field.Remark.apiName, sourceAuthDetailData.get(AuthorizationDetailConstant.Field.Remark.apiName));
            authorizationDetail.set("record_type", sourceAuthDetailData.getRecordType());
            newAuthorizationDetails.add(authorizationDetail);
        }
        if (CollectionUtils.empty(newAuthorizationDetails)) {
            return;
        }

        try {
            serviceFacade.bulkSaveObjectData(newAuthorizationDetails, admin);
        } catch (Exception e) {
            log.warn("copy serviceFacade.bulkSaveObjectData fail newAuthorizationDetails[{}], admin[{}]", newAuthorizationDetails, admin, e);
            throw e;
        }
    }
}
