package com.facishare.crm.customeraccount.predefine.service.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
public class PayType {

    @Data
    public static class GetPaymentRecordsArg {
        @SerializedName("object_api_name")
        @JsonProperty("object_api_name")
        private String objectApiName;

        @SerializedName("object_data_id")
        @JsonProperty("object_data_id")
        private String objectDataId;
    }

    @Data
    @AllArgsConstructor
    public static class GetPaymentRecordsResult {
        private List<PaymentRecord> paymentRecords;
    }

    @Data
    @AllArgsConstructor
    public static class PaymentRecord {
        private String payAmount;
        private String payStatementName;
        private Long createTime;
    }

    @Data
    @AllArgsConstructor
    public static class GetSalesOrderPayFieldResult {
        private String payFieldApiName;
    }

    @Data
    public static class GetPayParamArg {
        @SerializedName("object_api_name")
        @JsonProperty("object_api_name")
        private String objectApiName;

        @SerializedName("object_data_id")
        @JsonProperty("object_data_id")
        private String objectDataId;

        @SerializedName("pay_source")
        @JsonProperty("pay_source")
        private String paySource;

        @SerializedName("sign_type")
        @JsonProperty("sign_type")
        private String signType;
    }

    @Data
    @AllArgsConstructor
    public static class GetPayParamResult {
        @SerializedName("pay_param")
        @JsonProperty("pay_param")
        private Map<String, Object> payParam;
    }

    @Data
    public static class RepairArg {
        private List<String> tenantIds;
    }

    @Data
    @AllArgsConstructor
    public static class RepairResult {
        private Integer successTenantIdSize;
        private Integer noNeedRepairTenantIdSize;
        private List<String> successTenantIds;
        private List<String> noNeedRepairTenantIds;
    }
    @Data
    public static class RepairSalesOrderPayDirectlyArg {
        private List<String> tenantIds;
        //如果有值，只处理这些paymentCacheId
        private List<String> paymentCacheIds;
    }

    @Data
    @AllArgsConstructor
    public static class RepairSalesOrderPayDirectlyResult {
        private Integer successTenantIdSize;
        private Integer failTenantIdSize;
        private Integer noNeedRepairTenantIdSize;
        private Integer noOpenSalesOrderPayDirectlyTenantIdSize;

        private List<String> successTenantIds;
        private List<String> failTenantIds;
        private List<String> noNeedRepairTenantIds;
        private List<String> noOpenSalesOrderPayDirectlyTenantIds;
    }

    @Data
    @AllArgsConstructor
    public static class GetNeedRepairResult {
        private Integer noNeedRepairTenantIdSize;
        private Integer needRepairTenantIdSize;
        private Set<String> noNeedRepairTenantIds;
        private Set<String> needRepairTenantIds;
        private Map<String, List<String>> tenantId2PaymentCacheIds;
    }

    @Data
    public static class IsSalesOrderCanPayDirectlyArg {
        @SerializedName("object_data_id")
        @JsonProperty("object_data_id")
        private String objectDataId;

        @SerializedName("pay_source")
        @JsonProperty("pay_source")
        private String paySource;
    }

    @Data
    @AllArgsConstructor
    public static class IsSalesOrderCanPayDirectlyResult {
        private boolean canPayDirectly;
    }
}
