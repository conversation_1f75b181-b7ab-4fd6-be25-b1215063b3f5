package com.facishare.crm.customeraccount.predefine.domainplugin.editchanger;

import com.facishare.crm.customeraccount.predefine.handler.checkrule.AmountChanger;
import com.facishare.crm.customeraccount.predefine.handler.checkrule.AmountChangerResult;
import com.facishare.paas.appframework.core.exception.ValidateException;

public class InApprovalDirectReduceAmountChanger implements AmountChanger {
    @Override
    public AmountChangerResult frozenEqAndReduceGt() {
        //审批中禁止编辑直接扣减金额
        throw new ValidateException("");
    }

    @Override
    public AmountChangerResult frozenEqAndReduceLt() {
        //审批中禁止编辑直接扣减金额
        throw new ValidateException("");
    }

    @Override
    public AmountChangerResult frozenGtAndReduceEq() {
        throw new ValidateException(errorMsg);
    }

    @Override
    public AmountChangerResult frozenGtAndReduceGt() {
        throw new ValidateException(errorMsg);
    }

    @Override
    public AmountChangerResult frozenGtAndReduceLt() {
        throw new ValidateException(errorMsg);
    }

    @Override
    public AmountChangerResult frozenLtAndReduceEq() {
        throw new ValidateException(errorMsg);
    }

    @Override
    public AmountChangerResult frozenLtAndReduceGt() {
        throw new ValidateException(errorMsg);
    }

    @Override
    public AmountChangerResult frozenLtAndReduceLt() {
        throw new ValidateException(errorMsg);
    }
}
