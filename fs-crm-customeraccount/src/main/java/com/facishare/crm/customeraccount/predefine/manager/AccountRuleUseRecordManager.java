package com.facishare.crm.customeraccount.predefine.manager;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.customeraccount.constants.AccountCheckRuleConstants;
import com.facishare.crm.customeraccount.constants.AccountRuleUseRecordConstants;
import com.facishare.crm.customeraccount.constants.AccountTransactionFlowConst;
import com.facishare.crm.customeraccount.constants.CAI18NKey;
import com.facishare.crm.customeraccount.constants.SystemConstants;
import com.facishare.crm.customeraccount.constants.*;
import com.facishare.crm.customeraccount.enums.AccountCheckRuleStatusEnum;
import com.facishare.crm.customeraccount.enums.AccountCheckRuleTypeEnum;
import com.facishare.crm.customeraccount.enums.FAccountAuthAuthorizedTypeEnum;
import com.facishare.crm.customeraccount.enums.RuleStageEnum;
import com.facishare.crm.customeraccount.predefine.handler.RuleHandlerUtil;
import com.facishare.crm.customeraccount.predefine.service.dto.FieldMappingModel;
import com.facishare.crm.customeraccount.predefine.service.dto.ObjectMappingModel;
import com.facishare.crm.customeraccount.util.CustomerAccountLogUtil;
import com.facishare.crmcommon.util.ObjectDataUtil;
import com.facishare.crmcommon.util.SearchUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class AccountRuleUseRecordManager extends CommonManager {

    @Autowired
    private AccountCheckRuleManager accountCheckRuleManager;

    @Autowired
    private AuthorizationDetailManager authorizationDetailManager;

    public Optional<IObjectData> queryUseRecord(User user, String objectApiName, String objectDataId, String ruleType) {
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, AccountRuleUseRecordConstants.Field.CheckRecordObjectApiName.apiName, Lists.newArrayList(objectApiName));
        SearchUtil.fillFilterEq(filters, AccountRuleUseRecordConstants.Field.CheckRecordObjectDataId.apiName, Lists.newArrayList(objectDataId));

        List<IObjectData> checkRecordList = query(user, AccountRuleUseRecordConstants.API_NAME, filters, Lists.newArrayList());
        checkRecordList.removeIf(x -> {
            IObjectData checkRuleData = new ObjectData();
            String json = x.get(AccountRuleUseRecordConstants.Field.CheckRule.apiName, String.class);
            checkRuleData.fromJsonString(json);
            String ruleTypeTemp = checkRuleData.get(AccountCheckRuleConstants.Field.RuleType.apiName, String.class);
            return !(ruleType.equals(ruleTypeTemp));
        });
        if (CollectionUtils.isEmpty(checkRecordList)) {
            return Optional.empty();
        }

        String action = "Query_" + ruleType + "UseRecord";
        CustomerAccountLogUtil.QueryDataLog.builder().action(action).dataList(checkRecordList).objectApiName(objectApiName).objectDataId(objectDataId)
                .queryObjectApiName(AccountRuleUseRecordConstants.API_NAME).build().log(user);
        return Optional.of(checkRecordList.get(0));
    }

    public IObjectData queryUseRecordForComponentReduce(User user, String objectApiName, String objectDataId, boolean throwIfUseRecordNotExist) {
        Optional<IObjectData> useRecordOption = queryUseRecord(user, objectApiName, objectDataId, AccountCheckRuleTypeEnum.Component_Reduce.getValue());
        if (!useRecordOption.isPresent()) {
            if (throwIfUseRecordNotExist) {
                throw new ValidateException(I18N.text(CAI18NKey.ACCOUNT_CHECK_RULE_USE_RECORD_NOT_FOUND));
            }
            return null;
        }
        return useRecordOption.get();
    }

    public List<IObjectData> query(User user, String objectApiName, List<IFilter> filters, List<OrderBy> orderByList) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setFilters(filters);
        searchTemplateQuery.setOrders(orderByList);
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(1000);
        List<IObjectData> resultList = serviceFacade.findBySearchQuery(user, objectApiName, searchTemplateQuery).getData();
        CustomerAccountLogUtil.QueryDataLog.builder().action("Query" + objectApiName).filters(filters).dataList(resultList).queryObjectApiName(objectApiName).build().log(user);
        return resultList;
    }

    public List<IObjectData> queryByCheckRecordObjectDataIds(String tenantId, List<String> checkRecordObjectDataIds) {
        User admin = new User(tenantId, "-10000");

        String objectApiName = AccountRuleUseRecordConstants.API_NAME;

        List<IFilter> filters = new ArrayList<>();
        SearchUtil.fillFilterIn(filters, AccountRuleUseRecordConstants.Field.CheckRecordObjectDataId.apiName, checkRecordObjectDataIds);
        SearchUtil.fillFilterEq(filters, SystemConstants.Field.LifeStatus.apiName, SystemConstants.LifeStatus.Normal.value);
        SearchUtil.fillFilterEq(filters, ObjectData.IS_DELETED, Lists.newArrayList("0"));

        return ObjectDataUtil.getObjectDatasWithDeleted(admin, objectApiName, filters);
    }

    public List<IObjectData> queryByRuleType(String tenantId, String ruleType) {
        User admin = new User(tenantId, "-10000");

        String objectApiName = AccountRuleUseRecordConstants.API_NAME;

        List<IFilter> filters = new ArrayList<>();
        SearchUtil.fillFilterEq(filters, AccountRuleUseRecordConstants.Field.RuleType.apiName, ruleType);
        SearchUtil.fillFilterEq(filters, SystemConstants.Field.LifeStatus.apiName, SystemConstants.LifeStatus.Normal.value);
        SearchUtil.fillFilterEq(filters, ObjectData.IS_DELETED, Lists.newArrayList("0"));

        return ObjectDataUtil.getObjectDatasWithDeleted(admin, objectApiName, filters);
    }

    /**
     * @param checkRecordObjectApiNames 空时不作为查询条件
     */
    public List<IObjectData> query(String tenantId, String ruleType, List<String> checkRecordObjectApiNames, List<String> checkRecordObjectDataIds, int offset, int limit, Long startTime, Long endTime) {
        User admin = new User(tenantId, "-10000");

        String objectApiName = AccountRuleUseRecordConstants.API_NAME;

        List<IFilter> filters = new ArrayList<>();
        SearchUtil.fillFilterEq(filters, AccountRuleUseRecordConstants.Field.RuleType.apiName, ruleType);
        SearchUtil.fillFilterEq(filters, SystemConstants.Field.LifeStatus.apiName, SystemConstants.LifeStatus.Normal.value);
        SearchUtil.fillFilterEq(filters, ObjectData.IS_DELETED, Lists.newArrayList("0"));
        if (!CollectionUtils.isEmpty(checkRecordObjectApiNames)) {
            SearchUtil.fillFilterIn(filters, AccountRuleUseRecordConstants.Field.CheckRecordObjectApiName.apiName, checkRecordObjectApiNames);
        }
        if (!CollectionUtils.isEmpty(checkRecordObjectDataIds)) {
            SearchUtil.fillFilterIn(filters, AccountRuleUseRecordConstants.Field.CheckRecordObjectDataId.apiName, checkRecordObjectDataIds);
        }
        if (startTime != null ) {
            SearchUtil.fillFilterGTE(filters, SystemConstants.Field.CreateTime.apiName, startTime);
        }
        if (endTime != null) {
            SearchUtil.fillFilterLTE(filters, SystemConstants.Field.CreateTime.apiName, endTime);
        }

        List<OrderBy> orders =  new ArrayList<>();
        orders.add(new OrderBy(SystemConstants.Field.CreateTime.apiName, Boolean.TRUE));

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(filters);
        query.setOffset(offset);
        query.setLimit(limit);
        query.setOrders(orders);
        QueryResult<IObjectData> result = serviceFacade.findBySearchQuery(admin, objectApiName, query);
        return result.getData();
    }

    /**
     * 如果有老的，删掉老的，保存最新的
     */
    public void reSaveForComponentReduce(User user, String objectApiName, String objectDataId, IObjectData componentReduceAccountCheckRuleData, String action) {
        //查规则使用记录
        IObjectData useRecordForComponentReduce = queryUseRecordForComponentReduce(user, objectApiName, objectDataId, false);
        if (useRecordForComponentReduce != null) {
            log.info("reSaveForComponentReduce useRecordForComponentReduce != null, user[{}], objectApiName[{}], objectDataId[{}]", user, objectApiName, objectDataId);
            serviceFacade.bulkDeleteWithInternalDescribe(Lists.newArrayList(useRecordForComponentReduce), user);
        }

        saveForComponentReduce(user, objectApiName, objectDataId, componentReduceAccountCheckRuleData, action);
    }

    /**
     * 组件扣减，保存规则使用记录
     *
     * 参考 ：com.facishare.crm.customeraccount.predefine.handler.checkrule.ValidateReduceFrozenPreActionHandler#saveAccountRuleUseRecord(com.facishare.crm.customeraccount.predefine.handler.checkrule.ValidateReduceFrozenPreActionHandler.Arg, com.facishare.paas.metadata.api.IObjectData)
     */
    public void saveForComponentReduce(User user, String objectApiName, String authDataId, IObjectData componentReduceAccountCheckRuleData, String action) {
        IObjectData accountRuleUseRecordData = RuleHandlerUtil.getAccountCheckRecordData(user, objectApiName, authDataId, componentReduceAccountCheckRuleData, RuleStageEnum.DirectReduce);

        serviceFacade.saveObjectData(user, accountRuleUseRecordData);
        log.info("saveForComponentReduce user:{},dataId:{},result:{}", user, authDataId, accountRuleUseRecordData);
        CustomerAccountLogUtil.HandlerResultLog.builder().objectApiName(objectApiName)
                .objectDataId(authDataId).accountRuleUseRecordData(accountRuleUseRecordData).action(action).build().log(user);
    }

    /**
     * 870，组件扣减，补充规则使用记录
     */
    public void repairForComponentReduce(String tenantId, List<IObjectData> componentDeduceTransactionFlowDatas) {
        if (CollectionUtils.isEmpty(componentDeduceTransactionFlowDatas)) {
            return;
        }

        //所有的relateRecordObjectDataIds
        Set<String> relateRecordObjectDataIds = componentDeduceTransactionFlowDatas.stream().map(d -> d.get(AccountTransactionFlowConst.Field.RelateRecordObjectDataId.apiName, String.class)).collect(Collectors.toSet());

        //有规则使用记录的
        List<IObjectData> useRecords = queryByCheckRecordObjectDataIds(tenantId, Lists.newArrayList(relateRecordObjectDataIds));
        List<String> hasUseRecordCheckRecordObjectDataIds = useRecords.stream().map(d -> d.get(AccountRuleUseRecordConstants.Field.CheckRecordObjectDataId.apiName, String.class)).collect(Collectors.toList());

        //没规则使用记录，需要创建规则使用记录
        List<IObjectData> needCreateUseRecordTransactionFlowDatas = componentDeduceTransactionFlowDatas.stream()
                .filter(d -> !hasUseRecordCheckRecordObjectDataIds.contains(d.get(AccountTransactionFlowConst.Field.RelateRecordObjectDataId.apiName, String.class))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(needCreateUseRecordTransactionFlowDatas)) {
            log.info("repairForComponentReduce needCreateUseRecordTransactionFlowDatas is empty, tenantId[{}]", tenantId);
            return;
        }

        //查所有的'组件扣减'的校验规则
        List<IObjectData> allComponentReduceAccountCheckRuleDatas = accountCheckRuleManager.getAllAccountCheckRuleDatas(tenantId, true, null, AccountCheckRuleTypeEnum.Component_Reduce.getValue());
        if (CollectionUtils.isEmpty(allComponentReduceAccountCheckRuleDatas)) {
            log.warn("repairForComponentReduce allComponentReduceAccountCheckRuleDatas is empty, tenantId[{}]", tenantId);
        }
        Map<String, IObjectData> objectApiName2AccountCheckRuleData = allComponentReduceAccountCheckRuleDatas.stream().collect(Collectors.toMap(
                d -> d.get(AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName, String.class), p -> p));

        User admin = new User(tenantId, "-10000");
        //创建规则使用记录
        for (IObjectData transactionFlowData : needCreateUseRecordTransactionFlowDatas) {
            String relateRecordObjectApiName = transactionFlowData.get(AccountTransactionFlowConst.Field.RelateRecordObjectApiName.apiName, String.class);
            String relateRecordObjectDataId = transactionFlowData.get(AccountTransactionFlowConst.Field.RelateRecordObjectDataId.apiName, String.class);

            if (hasUseRecordCheckRecordObjectDataIds.contains(relateRecordObjectDataId)) {
                continue;
            }

            //找组件扣减的校验规则
            IObjectData componentReduceAccountCheckRuleData = objectApiName2AccountCheckRuleData.get(relateRecordObjectApiName);
            if (componentReduceAccountCheckRuleData == null) {
                log.warn("repairForComponentReduce componentReduceAccountCheckRuleData is null, tenantId[{}], transactionFlowData[{}]", tenantId, transactionFlowData);
            }

            //创建规则使用记录
            saveForComponentReduce(admin, relateRecordObjectApiName, relateRecordObjectDataId, componentReduceAccountCheckRuleData, "组件扣减-补刷规则使用记录");//ignoreI18n

            hasUseRecordCheckRecordObjectDataIds.add(relateRecordObjectDataId);
        }
    }

    /**
     * 870，前2次刷数据 bugfix，把缺'支出金额'的映射字段的，补上
     */
    public void repairForComponentReduce(String tenantId, boolean update) {
        List<IObjectData> allComponentReduceUseRecords = queryByRuleType(tenantId, AccountCheckRuleTypeEnum.Component_Reduce.getValue());
        //查询所有'组件扣减'的规则使用记录
        if (CollectionUtils.isEmpty(allComponentReduceUseRecords)) {
            return;
        }

        for (IObjectData record : allComponentReduceUseRecords) {
            String ruleType = record.get(AccountRuleUseRecordConstants.Field.RuleType.apiName, String.class);

            if (!Objects.equals(ruleType, AccountCheckRuleTypeEnum.Component_Reduce.getValue())) {
                continue;
            }

            updateExpenseAmountMappingField(tenantId, record, update);
        }
    }

    private void updateExpenseAmountMappingField(String tenantId, IObjectData useRecord, boolean update) {
        IObjectData componentReduceAccountCheckRuleData = accountCheckRuleManager.getComponentReduceAccountCheckRuleData(useRecord);

        String reduceRelatedObject = componentReduceAccountCheckRuleData.get(AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName, String.class);
        String name = componentReduceAccountCheckRuleData.getName();

        //查授权明细
        User admin = new User(tenantId, "-10000");
        List<IObjectData> authorizationDetailDatas = authorizationDetailManager.query(admin, FAccountAuthAuthorizedTypeEnum.Outcome.getValue(), reduceRelatedObject);
        if (CollectionUtils.isEmpty(authorizationDetailDatas)) {
            log.info("useRecordUpdateExpenseAmountMappingField authorizationDetailManager.query empty tenantId[{}], reduceRelatedObject[{}]", tenantId, reduceRelatedObject);
            return;
        }

        Map<String, String> authorizeAccountId2TradeAmountFieldApiName = authorizationDetailManager.getAuthorizeAccountId2TradeAmountFieldApiName(authorizationDetailDatas);

        List<ObjectMappingModel> objectMappings = RuleHandlerUtil.getObjectMapping(componentReduceAccountCheckRuleData, AccountCheckRuleConstants.Field.ReduceMapping.apiName);
        if (CollectionUtils.isEmpty(objectMappings)) {
            return;
        }

        boolean needUpdate = false;
        for (ObjectMappingModel objectMapping : objectMappings) {
            List<FieldMappingModel> fieldMappings = objectMapping.getFieldMappingList();
            if (CollectionUtils.isEmpty(fieldMappings)) {
                continue;
            }

            for (FieldMappingModel fieldMapping : fieldMappings) {
                if (!Objects.equals(fieldMapping.getTargetFieldApiName(), AccountTransactionFlowConst.Field.ExpenseAmount.apiName)) {
                    continue;
                }

                String sourceFieldApiName = fieldMapping.getSourceFieldApiName();
                if (!Strings.isNullOrEmpty(sourceFieldApiName)) {
                    continue;
                }

                String tradeAmountFieldApiName = authorizeAccountId2TradeAmountFieldApiName.get(objectMapping.getFundAccountId());
                if (!Strings.isNullOrEmpty(tradeAmountFieldApiName)) {
                    fieldMapping.setSourceFieldApiName(tradeAmountFieldApiName);
                    needUpdate = true;
                    log.info("useRecordUpdateExpenseAmountMappingField, needUpdate tenantId[{}], useRecordId[{}], useRecordName[{}]", tenantId, useRecord.getId(), name);
                } else {
                    log.info("useRecordUpdateExpenseAmountMappingField, sourceFieldApiName = null tenantId[{}], useRecordId[{}], useRecordName[{}]", tenantId, useRecord.getId(), name);
                }
            }
        }

        if (!update) {
            return;
        }

        if (!needUpdate) {
            return;
        }

        String reduceMappingJson = JSONObject.toJSONString(objectMappings);
        log.info("useRecordUpdateExpenseAmountMappingField tenantId[{}], name[{}], reduceMappingJson[{}]", tenantId, name, reduceMappingJson);
        componentReduceAccountCheckRuleData.set(AccountCheckRuleConstants.Field.ReduceMapping.apiName, reduceMappingJson);


        useRecord.set(AccountRuleUseRecordConstants.Field.CheckRule.apiName, componentReduceAccountCheckRuleData.toJsonString());
        serviceFacade.updateObjectData(admin, useRecord);
        log.info("useRecordUpdateExpenseAmountMappingField end, tenantId[{}], useRecordId[{}], useRecordName[{}]", tenantId, useRecord.getId(), useRecord.getName());
    }

    /**
     * 删除停用的组件扣减类型的规则使用记录
     */
    public void deleteOffUseRecord(String tenantId, boolean delete) {
        List<IObjectData> allComponentReduceUseRecords = queryByRuleType(tenantId, AccountCheckRuleTypeEnum.Component_Reduce.getValue());
        //查询所有'组件扣减'的规则使用记录
        if (CollectionUtils.isEmpty(allComponentReduceUseRecords)) {
            log.info("deleteOffUseRecord allComponentReduceUseRecords is empty tenantId[{}]", tenantId);
            return;
        }

        List<IObjectData> needDeleteUseRecords = Lists.newArrayList();
        for (IObjectData useRecord : allComponentReduceUseRecords) {
            String ruleType = useRecord.get(AccountRuleUseRecordConstants.Field.RuleType.apiName, String.class);

            if (!Objects.equals(ruleType, AccountCheckRuleTypeEnum.Component_Reduce.getValue())) {
                continue;
            }

            IObjectData componentReduceAccountCheckRuleData = accountCheckRuleManager.getComponentReduceAccountCheckRuleData(useRecord);
            String status = componentReduceAccountCheckRuleData.get(AccountCheckRuleConstants.Field.Status.apiName, String.class);
            if (Objects.equals(status, AccountCheckRuleStatusEnum.On.getValue())) {
                continue;
            }
            needDeleteUseRecords.add(useRecord);
        }

        if (CollectionUtils.isEmpty(needDeleteUseRecords)) {
            log.info("deleteOffUseRecord  needDeleteUseRecords is empty");
            return;
        }

        List<String> needDeleteUseRecordIds = needDeleteUseRecords.stream().map(IObjectData::getId).collect(Collectors.toList());
        log.info("deleteOffUseRecord needDelete size[{}], needDeleteUseRecordIds[{}]", needDeleteUseRecordIds.size(), needDeleteUseRecordIds);

        if (!delete) {
            return;
        }

        User admin = new User(tenantId, "-10000");
        serviceFacade.bulkDeleteWithInternalDescribe(needDeleteUseRecords, admin);
        log.info("deleteOffUseRecord finish size[{}], needDeleteUseRecordIds[{}]", needDeleteUseRecordIds.size(), needDeleteUseRecordIds);
    }
}
