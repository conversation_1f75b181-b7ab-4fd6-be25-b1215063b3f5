package com.facishare.crm.customeraccount.model;

import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.api.IdGenerator;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

public class DataAddModel {
    @Getter
    @Builder
    @ToString
    public static class Arg {
        private IObjectData addObjectData;
        private boolean skipTriggerFunction;

        public String getObjectApiName() {
            return addObjectData.getDescribeApiName();
        }

        public String getIdAndGenerateIfAbsent() {
            String id = addObjectData.getId();
            if (StringUtils.isEmpty(id)) {
                id = IdGenerator.get();
                addObjectData.setId(id);
            }
            return id;
        }
    }

    @Getter
    @AllArgsConstructor
    @ToString
    public static class Result {
        private IObjectData addObjectData;
        private boolean skipTriggerFunction;

        public static Result of(Arg arg) {
            return new Result(arg.addObjectData, arg.skipTriggerFunction);
        }

        public String getObjectApiName() {
            return addObjectData.getDescribeApiName();
        }
    }
}
