package com.facishare.crm.customeraccount.util;

import com.facishare.crm.consts.CreditFlowDetailConst;
import com.facishare.crm.consts.CreditRuleDetailConst;
import com.facishare.crm.consts.CreditRuleMatchRecordConst;
import com.facishare.crm.consts.CustomerCreditAuthConst;
import com.facishare.crm.customeraccount.constants.*;
import com.facishare.crm.customeraccount.enums.*;
import com.facishare.crm.customeraccount.predefine.service.dto.CreditCurAndPreObjectConfig;
import com.facishare.crm.customeraccount.predefine.service.dto.CreditObjectConfig;
import com.facishare.crm.openapi.Utils;
import com.facishare.crmcommon.util.ObjectDataUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.functions.utils.Maps;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;

@Slf4j
public class CreditUtil {

    public static boolean setByDetail(String tenantId, String objectApiName) {
        return StringUtils.isNotEmpty(getCreditObjectConfig(tenantId, objectApiName).orElseThrow(() -> new ValidateException("credit config error, " + objectApiName + " not support")).getDetailObjectApiName());
    }

    public static List<CreditObjectConfig> getTenantCreditObjectConfig(String tenantId) {
        if (ConfigCenter.creditObjectConfigMap.containsKey(tenantId)) {
            return ConfigCenter.creditObjectConfigMap.get(tenantId);
        } else {
            return ConfigCenter.creditObjectConfigMap.getOrDefault("default", Lists.newArrayList());
        }
    }

    public static Optional<CreditObjectConfig> getCreditObjectConfig(String tenantId, String objectApiName) {
        return getTenantCreditObjectConfig(tenantId).stream().filter(x -> x.getObjectApiName().equals(objectApiName)).findAny();
    }

    public static CreditCurAndPreObjectConfig getCreditCurAndPreConfig(String tenantId, String objectApiName, String preObjectApiName) {
        CreditObjectConfig creditObjectConfig = getTenantCreditObjectConfig(tenantId).stream().filter(x -> x.getObjectApiName().equals(objectApiName)).findFirst().orElseThrow(() -> new ValidateException("credit config error"));
        return creditObjectConfig.convert(preObjectApiName);
    }

    public static String getPreObjectApiName(String objectApiName, List<IObjectData> creditRuleDetailList) {
        int curSequence = 0;
        Map<Integer, String> ruleSequenceObjectMap = Maps.newHashMap();
        for (IObjectData creditRuleDetail : creditRuleDetailList) {
            String creditObject = creditRuleDetail.get(CreditRuleDetailConst.F.CreditObject.apiName, String.class);
            Integer ruleSequence = creditRuleDetail.get(CreditRuleDetailConst.F.RuleSequence.apiName, Integer.class);
            if (StringUtils.equals(objectApiName, creditObject)) {
                curSequence = ruleSequence;
            }
            ruleSequenceObjectMap.put(ruleSequence, creditObject);
        }
        return ruleSequenceObjectMap.get(curSequence - 1);
    }

    public static List<String> getTagFieldValues(IObjectData objectData, String fieldName) {
        List valueList = objectData.get(fieldName, List.class);
        if (Objects.isNull(valueList)) {
            return Lists.newArrayList();
        }
        Set<String> values = Sets.newHashSet();
        for (Object value : valueList) {
            values.add(value.toString());
        }
        return Lists.newArrayList(values);
    }

    public static Map<String, Map<String, Object>> buildCustomerAccountCreditOccupiedAmountUpdateFieldMap(BigDecimal creditOccupiedAmount, String customerAccountId) {
        Map<String, Map<String, Object>> customerAccountUpdateFieldMap = Maps.newHashMap();
        Map<String, Object> updateFieldMap = Maps.newHashMap();
        if (creditOccupiedAmount.compareTo(BigDecimal.ZERO) == 0) {
            return customerAccountUpdateFieldMap;
        }
        updateFieldMap.put(NewCustomerAccountConstants.Field.AccountBalance.apiName, creditOccupiedAmount.negate());
        updateFieldMap.put(NewCustomerAccountConstants.Field.CreditOccupiedAmount.apiName, creditOccupiedAmount);
        customerAccountUpdateFieldMap.put(customerAccountId, updateFieldMap);
        return customerAccountUpdateFieldMap;
    }

    public static Map<String, Map<String, Object>> buildCustomerAccountCreditQuotaUpdateFieldMap(BigDecimal creditQuota, String customerAccountId) {
        Map<String, Map<String, Object>> customerAccountUpdateFieldMap = Maps.newHashMap();
        Map<String, Object> updateFieldMap = Maps.newHashMap();
        updateFieldMap.put(NewCustomerAccountConstants.Field.CreditQuota.apiName, creditQuota);
        updateFieldMap.put(NewCustomerAccountConstants.Field.AccountBalance.apiName, creditQuota);
        customerAccountUpdateFieldMap.put(customerAccountId, updateFieldMap);
        return customerAccountUpdateFieldMap;
    }

    public static boolean creditAuthActive(IObjectData objectData) {
        Date startTime = objectData.get(CustomerCreditAuthConst.F.StartTime.apiName, Date.class);
        Date endTime = objectData.get(CustomerCreditAuthConst.F.EndTime.apiName, Date.class);
        return ObjectDataExt.of(objectData).isNormal() && CreditUtil.isCurrentTimeActive(startTime, endTime);
    }

    public static boolean creditAuthEffectiveInFuture(IObjectData objectData) {
        Date startTime = objectData.get(CustomerCreditAuthConst.F.StartTime.apiName, Date.class);
        Date now = new Date();
        return DateUtil.getDateBeginDate(startTime).after(now);
    }

    public static BigDecimal computeChangedCreditQuota(IObjectData dbObjectData, IObjectData objectData) {
        BigDecimal dbCreditQuota = dbObjectData.get(CustomerCreditAuthConst.F.CreditQuota.apiName, BigDecimal.class);
        BigDecimal newCreditQuota = objectData.get(CustomerCreditAuthConst.F.CreditQuota.apiName, BigDecimal.class);
        BigDecimal changedCreditQuota = BigDecimal.ZERO;
        if (CreditUtil.creditAuthActive(dbObjectData) && CreditUtil.creditAuthActive(objectData)) {
            changedCreditQuota = newCreditQuota.subtract(dbCreditQuota);
        } else if (!CreditUtil.creditAuthActive(dbObjectData) && CreditUtil.creditAuthActive(objectData)) {
            changedCreditQuota = newCreditQuota;
        } else if (CreditUtil.creditAuthActive(dbObjectData) && !CreditUtil.creditAuthActive(objectData)) {
            changedCreditQuota = dbCreditQuota.negate();
        }
        return changedCreditQuota;
    }

    public static boolean isCurrentTimeActive(Date start, Date end) {
        if (Objects.nonNull(start)) {
            start = DateUtil.getDateBeginDate(start);
        }
        if (Objects.nonNull(end)) {
            end = DateUtil.getTomorrowBeginDate(end);
        }
        if (Objects.isNull(start) && Objects.isNull(end)) {
            return true;
        }
        Date now = new Date();
        if (Objects.isNull(start)) {
            return !now.after(end);
        }
        if (Objects.isNull(end)) {
            return now.before(start);
        }
        return now.after(start) && now.before(end);
    }

    public static void checkCreditRule(User user, List<ObjectDataDocument> detailDataDocumentList, ServiceFacade serviceFacade) {
        if (CollectionUtils.empty(detailDataDocumentList)) {
            throw new ValidateException(CAI18NKey.CREDIT_RULE_AT_LEAST_ONE_DETAIL);
        }
        String tenantId = user.getTenantId();
        Set<String> creditObjects = Sets.newHashSet();
        int sequence = 0;

        boolean setByDetail = false;
        Map<Integer, IObjectData> sequenceDetailDataMap = com.google.common.collect.Maps.newHashMap();
        for (ObjectDataDocument objectDataDocument : detailDataDocumentList) {
            IObjectData detailData = ObjectDataExt.of(objectDataDocument);
            String creditObject = detailData.get(CreditRuleDetailConst.F.CreditObject.apiName, String.class);
            if (creditObjects.contains(creditObject)) {
                throw new ValidateException(CAI18NKey.CREDIT_RULE_DETAIL_MUST_UNIQUE);
            }
            creditObjects.add(creditObject);
            CreditCurAndPreObjectConfig creditCurAndPreObjectConfig = CreditUtil.getCreditCurAndPreConfig(tenantId, creditObject, null);
            setByDetail = creditCurAndPreObjectConfig.setByDetail();
            if (StringUtils.isNotEmpty(creditCurAndPreObjectConfig.getDetailObjectApiName())) {
                creditObjects.add(creditCurAndPreObjectConfig.getDetailObjectApiName());
            }
            ++sequence;
            detailData.set(CreditRuleDetailConst.F.RuleSequence.apiName, sequence);
            Boolean checkCreditLimit = detailData.get(CreditRuleDetailConst.F.CheckCreditLimit.apiName, Boolean.class);
            if (!BooleanUtils.isTrue(checkCreditLimit)) {
                detailData.set(CreditRuleDetailConst.F.CheckStrength.apiName, null);
            }
            sequenceDetailDataMap.put(sequence, detailData);
        }

        Map<String, IObjectDescribe> describeMap = serviceFacade.findObjects(tenantId, creditObjects);
        for (ObjectDataDocument objectDataDocument : detailDataDocumentList) {
            IObjectData detailData = ObjectDataExt.of(objectDataDocument);
            String creditObject = detailData.get(CreditRuleDetailConst.F.CreditObject.apiName, String.class);

            Integer ruleSequence = detailData.get(CreditRuleDetailConst.F.RuleSequence.apiName, Integer.class);
            if (ruleSequence > 1) {
                //非首节点
                IObjectData preData = sequenceDetailDataMap.get(ruleSequence - 1);
                String preCreditObject = preData.get(CreditRuleDetailConst.F.CreditObject.apiName, String.class);
                CreditCurAndPreObjectConfig creditCurAndPreObjectConfig = CreditUtil.getCreditCurAndPreConfig(tenantId, creditObject, preCreditObject);
                if (creditCurAndPreObjectConfig.setByDetail() != setByDetail) {
                    throw new ValidateException(I18N.text(CAI18NKey.CREDIT_RULE_DETAIL_MUST_CONFIG_BY_SAME_MODE));
                }
                String objectApiNameOfOccupiedAmountField = creditCurAndPreObjectConfig.getObjectApiNameOfOccupiedAmountField();
                IObjectDescribe objectDescribeOfOccupiedAmountFieldObject = describeMap.get(objectApiNameOfOccupiedAmountField);
                if (Objects.isNull(objectDescribeOfOccupiedAmountFieldObject)) {
                    throw new ValidateException(I18N.text(CAI18NKey.CREDIT_RULE_OBJECT_NOT_EXIST, objectApiNameOfOccupiedAmountField));
                }
                if (!objectDescribeOfOccupiedAmountFieldObject.containsField(creditCurAndPreObjectConfig.getLookUpPreObjectFieldApiName())) {
                    log.warn("current object:{} lookup preObject:{} field:{} not exist", objectDescribeOfOccupiedAmountFieldObject.getApiName(), creditCurAndPreObjectConfig.getPreDetailObjectApiName(), creditCurAndPreObjectConfig.getLookUpPreObjectFieldApiName());
                    throw new ValidateException(I18N.text(CAI18NKey.CREDIT_RULE_OBJECT_CONFIG_ERROR, objectDescribeOfOccupiedAmountFieldObject.getApiName()));
                }
                ObjectReferenceFieldDescribe lookupFieldDescribe = (ObjectReferenceFieldDescribe) objectDescribeOfOccupiedAmountFieldObject.getFieldDescribe(creditCurAndPreObjectConfig.getLookUpPreObjectFieldApiName());
                String targetObjectApiName = lookupFieldDescribe.getTargetApiName();
                if (!StringUtils.equals(targetObjectApiName, creditCurAndPreObjectConfig.getPreDetailObjectApiName())) {
                    log.warn("object:{} lookup field :{} config error ,targetObject not match:{}", objectApiNameOfOccupiedAmountField, creditCurAndPreObjectConfig.getLookUpPreObjectFieldApiName(), creditCurAndPreObjectConfig.getPreDetailObjectApiName());
                    throw new ValidateException(I18N.text(CAI18NKey.CREDIT_RULE_OBJECT_CONFIG_ERROR, creditCurAndPreObjectConfig.getLookUpPreObjectFieldApiName()));
                }
            } else {
                //首个节点
                CreditCurAndPreObjectConfig creditCurAndPreObjectConfig = CreditUtil.getCreditCurAndPreConfig(tenantId, creditObject, null);
                if (!describeMap.containsKey(creditCurAndPreObjectConfig.getObjectApiNameOfOccupiedAmountField())) {
                    throw new ValidateException(I18N.text(CAI18NKey.CREDIT_RULE_OBJECT_NOT_EXIST, creditCurAndPreObjectConfig.getObjectApiNameOfOccupiedAmountField()));
                }
                if (creditCurAndPreObjectConfig.setByDetail() != setByDetail) {
                    throw new ValidateException(I18N.text(CAI18NKey.CREDIT_RULE_DETAIL_MUST_CONFIG_BY_SAME_MODE));
                }
            }

        }
    }

    public static void checkValidityPeriod(IObjectData objectData, List<IObjectData> customerCreditAuthList) {
        String creditType = objectData.get(CustomerCreditAuthConst.F.CreditType.apiName, String.class);
        if (CreditTypeEnum.OfficialCredit.getValue().equals(creditType)) {
            return;
        }
        for (IObjectData customerCreditAuthData : customerCreditAuthList) {
            String dbCreditType = customerCreditAuthData.get(CustomerCreditAuthConst.F.CreditType.apiName, String.class);
            if (CreditTypeEnum.OfficialCredit.getValue().equals(dbCreditType)) {
                continue;
            }
            if (validityPeriodCross(objectData, customerCreditAuthData)) {
                throw new ValidateException(I18N.text(CAI18NKey.VALIDITY_PERIOD_CROSS_OVER, customerCreditAuthData.getName()));
            }
        }
    }

    public static boolean validityPeriodCross(IObjectData objectData, IObjectData customerCreditAuthData) {
        Date start = DateUtil.getDateBeginDate(objectData.get(CustomerCreditAuthConst.F.StartTime.apiName, Date.class));
        Date end = DateUtil.getTomorrowBeginDate(objectData.get(CustomerCreditAuthConst.F.EndTime.apiName, Date.class));
        Date dbStart = DateUtil.getDateBeginDate(customerCreditAuthData.get(CustomerCreditAuthConst.F.StartTime.apiName, Date.class));
        Date dbEnd = DateUtil.getTomorrowBeginDate(customerCreditAuthData.get(CustomerCreditAuthConst.F.EndTime.apiName, Date.class));

        if (!start.after(dbStart) && end.after(dbStart)) {
            return true;
        }
        return !start.before(dbStart) && start.before(dbEnd);
    }

    public static String checkPreNodeCreditRuleMatchRecordSameRule(List<IObjectData> preRuleMatchRecordList) {
        if (CollectionUtils.empty(preRuleMatchRecordList)) {
            return null;
        }
        if (preRuleMatchRecordList.stream().map(x -> x.get(CreditRuleMatchRecordConst.F.CreditOccupiedRuleId.apiName, String.class)).distinct().count() > 1) {
            throw new ValidateException(I18N.text(CAI18NKey.RELATE_DATE_MATCH_DIFF_CREDIT_RULE));
        }
        return preRuleMatchRecordList.stream().map(x -> x.get(CreditRuleMatchRecordConst.F.CreditOccupiedRuleId.apiName, String.class)).findFirst().orElse(null);
    }

    public static void checkCreditQuota(IObjectDescribe objectDescribe, IObjectData objectData) {
        BigDecimal newCreditQuota = objectData.get(CustomerCreditAuthConst.F.CreditQuota.apiName, BigDecimal.class);
        if (newCreditQuota.compareTo(BigDecimal.ZERO) < 0) {
            IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(CustomerCreditAuthConst.F.CreditQuota.apiName);
            throw new ValidateException(I18N.text(CAI18NKey.FIELD_MUST_GT_ZERO, fieldDescribe.getLabel()));
        }
    }

    public static boolean isNotLastNode(String objectApiName, List<IObjectData> creditOccupiedRuleDetailList) {
        Integer sequence = null;
        Integer maxSequence = Integer.MIN_VALUE;
        for (IObjectData creditRuleDetailData : creditOccupiedRuleDetailList) {
            Integer ruleSequence = creditRuleDetailData.get(CreditRuleDetailConst.F.RuleSequence.apiName, Integer.class);
            String creditObject = creditRuleDetailData.get(CreditRuleDetailConst.F.CreditObject.apiName, String.class);
            if (StringUtils.equals(objectApiName, creditObject)) {
                sequence = ruleSequence;
            }
            if (ruleSequence > maxSequence) {
                maxSequence = ruleSequence;
            }
        }
        return !maxSequence.equals(sequence);
    }

    public static CreditRuleNodeTypeEnum getNodeType(String objectApiName, List<IObjectData> creditOccupiedRuleDetailList) {
        int sequence = 0;
        int maxSequence = Integer.MIN_VALUE;
        for (IObjectData creditRuleDetailData : creditOccupiedRuleDetailList) {
            Integer ruleSequence = creditRuleDetailData.get(CreditRuleDetailConst.F.RuleSequence.apiName, Integer.class);
            String creditObject = creditRuleDetailData.get(CreditRuleDetailConst.F.CreditObject.apiName, String.class);
            if (StringUtils.equals(objectApiName, creditObject)) {
                sequence = ruleSequence;
            }
            if (ruleSequence > maxSequence) {
                maxSequence = ruleSequence;
            }
        }
        if (maxSequence == sequence) {
            return sequence == 1 ? CreditRuleNodeTypeEnum.FIRST_AND_LAST : CreditRuleNodeTypeEnum.LAST_AND_NOT_FIRST;
        } else {
            return sequence == 1 ? CreditRuleNodeTypeEnum.FIRST_AND_NOT_LAST : CreditRuleNodeTypeEnum.NOT_FIRST_AND_NOT_LAST;
        }
    }

    public static IObjectData findCreditDetailData(String objectApiName, List<IObjectData> creditDetailDataList) {
        return creditDetailDataList.stream().filter(x -> x.get(CreditRuleDetailConst.F.CreditObject.apiName, String.class).equals(objectApiName)).findFirst().orElse(null);
    }

    public static BigDecimal getNewCreditAmount(String tenantId, IObjectData creditRuleDetailData, IObjectData objectData, Map<String, List<ObjectDataDocument>> details) {
        String objectApiName = creditRuleDetailData.get(CreditRuleDetailConst.F.CreditObject.apiName, String.class);
        CreditCurAndPreObjectConfig creditCurAndPreObjectConfig = CreditUtil.getCreditCurAndPreConfig(tenantId, objectApiName, null);
        String creditOccupiedAmountField = creditRuleDetailData.get(CreditRuleDetailConst.F.OccupiedAmountField.apiName, String.class);
        BigDecimal newCreditAmount;
        if (creditCurAndPreObjectConfig.setByDetail()) {
            List<ObjectDataDocument> detailObjectDataList = Objects.isNull(details) ? Lists.newArrayList() : details.getOrDefault(creditCurAndPreObjectConfig.getDetailObjectApiName(), Lists.newArrayList());
            newCreditAmount = detailObjectDataList.stream().map(x -> ObjectDataExt.of(x).get(creditOccupiedAmountField, BigDecimal.class, BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
        } else {
            newCreditAmount = objectData.get(creditOccupiedAmountField, BigDecimal.class, BigDecimal.ZERO);
        }
        return newCreditAmount;
    }

    public static IObjectData generateCreditAccountTransactionFlow(User user, String currentObjectApiName, String currentObjectDataId, BigDecimal creditAmount, IObjectData customerAccountData) {
        String fundAccountId = customerAccountData.get(NewCustomerAccountConstants.Field.FundAccount.apiName, String.class);
        String customerId = customerAccountData.get(NewCustomerAccountConstants.Field.Customer.apiName, String.class);
        BigDecimal accountBalance = customerAccountData.get(NewCustomerAccountConstants.Field.AccountBalance.apiName, BigDecimal.class);
        IObjectData objectData = ObjectDataUtil.getBaseObjectData(user, AccountTransactionFlowConst.API_NAME);
        objectData.setId(IdGenerator.get());
        objectData.setRecordType(AccountTransactionFlowConst.RecordType.OutcomeRecordType.apiName);
        objectData.set(AccountTransactionFlowConst.Field.EntryStatus.apiName, EntryStatusEnum.AlreadyEntry.getValue());
        objectData.set(AccountTransactionFlowConst.Field.FundAccount.apiName, fundAccountId);
        objectData.set(AccountTransactionFlowConst.Field.Customer.apiName, customerId);
        objectData.set(AccountTransactionFlowConst.Field.ExpenseType.apiName, ExpenseTypeEnum.CreditOccupied.getValue());
        objectData.set(AccountTransactionFlowConst.Field.AccessModule.apiName, AccessModuleEnum.CREDIT.value);
        objectData.set(AccountTransactionFlowConst.Field.RelateRecordObjectApiName.apiName, currentObjectApiName);
        objectData.set(AccountTransactionFlowConst.Field.RelateRecordObjectDataId.apiName, currentObjectDataId);
        objectData.set(AccountTransactionFlowConst.Field.ExpenseAmount.apiName, creditAmount);
        objectData.set(AccountTransactionFlowConst.Field.CustomerAccount.apiName, customerAccountData.getId());
        objectData.set(AccountTransactionFlowConst.Field.TransactionDate.apiName, System.currentTimeMillis());
        objectData.set(AccountTransactionFlowConst.Field.AccountBalance.apiName, accountBalance);
        objectData.setId(IdGenerator.get());

        return objectData;
    }

    public static IObjectData generateTransactionFlowDataFromOtherWithAmount(User user, IObjectData transactionFlowData, BigDecimal newExpenseAmount) {
        IObjectData objectData = new ObjectData();
        List<String> ignoreFields = Lists.newArrayList("_id", SystemConstants.Field.CreateBy.apiName, SystemConstants.Field.LastModifiedBy.apiName, SystemConstants.Field.CreateTime.apiName, SystemConstants.Field.LastModifiedTime.apiName);

        ObjectDataExt.of(transactionFlowData).toMap().forEach((k, v) -> {
            if (!ignoreFields.contains(k)) {
                objectData.set(k, v);
            }
        });
        objectData.setCreatedBy(user.getUserIdOrOutUserIdIfOutUser());
        objectData.setLastModifiedBy(user.getUserIdOrOutUserIdIfOutUser());
        long now = System.currentTimeMillis();
        objectData.setCreateTime(now);
        objectData.setLastModifiedTime(now);
        objectData.set(AccountTransactionFlowConst.Field.ExpenseAmount.apiName, newExpenseAmount);
        return objectData;
    }

    public static IObjectData generateCreditFlowDetailData(User user, IObjectData masterData, IObjectData detailData, IObjectData customerAccountData, BigDecimal creditAmount, String creditRuleId) {
        String masterApiName = masterData.getDescribeApiName();
        String masterDataId = masterData.getId();
        String objectApiName = detailData.getDescribeApiName();
        String objectDataId = detailData.getId();
        String customerId = customerAccountData.get(NewCustomerAccountConstants.Field.Customer.apiName, String.class);
        String fundAccountId = customerAccountData.get(NewCustomerAccountConstants.Field.FundAccount.apiName, String.class);
        String customerAccountId = customerAccountData.getId();
        IObjectData objectData = ObjectDataUtil.getBaseObjectData(user, CreditFlowDetailConst.API_NAME);
        objectData.setId(IdGenerator.get());
        objectData.set(CreditFlowDetailConst.F.FundAccountId.apiName, fundAccountId);
        objectData.set(CreditFlowDetailConst.F.CustomerAccountId.apiName, customerAccountId);
        objectData.set(CreditFlowDetailConst.F.CreditAmount.apiName, creditAmount);
        objectData.set(CreditFlowDetailConst.F.LeftCreditAmount.apiName, creditAmount);
        objectData.set(CreditFlowDetailConst.F.CreditAuthObjectApiName.apiName, Utils.ACCOUNT_API_NAME);
        objectData.set(CreditFlowDetailConst.F.CreditAuthObjectDataId.apiName, customerId);
        objectData.set(CreditFlowDetailConst.F.RelateObjectApiName.apiName, objectApiName);
        objectData.set(CreditFlowDetailConst.F.RelateObjectDataId.apiName, objectDataId);
        objectData.set(CreditFlowDetailConst.F.RelateMasterApiName.apiName, masterApiName);
        objectData.set(CreditFlowDetailConst.F.RelateMasterDataId.apiName, masterDataId);
        objectData.set(CreditFlowDetailConst.F.CreditOccupiedRuleId.apiName, creditRuleId);
        return objectData;
    }

    public static IObjectData generateCreditFlowDetailData(User user, IObjectData masterData, IObjectData detailData, IObjectData customerAccountData, BigDecimal creditAmount, String creditRuleId, BigDecimal leftCreditAmount) {
        IObjectData objectData = generateCreditFlowDetailData(user, masterData, detailData, customerAccountData, creditAmount, creditRuleId);
        objectData.set(CreditFlowDetailConst.F.LeftCreditAmount.apiName, leftCreditAmount);
        return objectData;
    }

    public static IObjectData generateCreditRuleMatchRecord(User user, String creditOccupiedRuleId, List<IObjectData> preNodeMatchRecordList, String currentObjectApiName, String currentObjectDataId) {
        IObjectData objectData = ObjectDataUtil.getBaseObjectData(user, CreditRuleMatchRecordConst.API_NAME);
        objectData.setId(IdGenerator.get());
        objectData.set(CreditRuleMatchRecordConst.F.CurrentObjectApiName.apiName, currentObjectApiName);
        objectData.set(CreditRuleMatchRecordConst.F.CurrentObjectDataId.apiName, currentObjectDataId);
        if (CollectionUtils.notEmpty(preNodeMatchRecordList)) {
            Set<String> preDataIds = Sets.newHashSet();
            Set<String> sourceDataIds = Sets.newHashSet();
            for (IObjectData preNodeMatchRecordData : preNodeMatchRecordList) {
                String preObject = preNodeMatchRecordData.get(CreditRuleMatchRecordConst.F.CurrentObjectApiName.apiName, String.class);
                String preDataId = preNodeMatchRecordData.get(CreditRuleMatchRecordConst.F.CurrentObjectDataId.apiName, String.class);
                objectData.set(CreditRuleMatchRecordConst.F.PreObject.apiName, preObject);
                preDataIds.add(preDataId);

                String sourceObject = preNodeMatchRecordData.get(CreditRuleMatchRecordConst.F.SourceObject.apiName, String.class);
                objectData.set(CreditRuleMatchRecordConst.F.SourceObject.apiName, sourceObject);
                sourceDataIds.addAll(CreditUtil.getTagFieldValues(preNodeMatchRecordData, CreditRuleMatchRecordConst.F.SourceDataIds.apiName));
            }
            //编辑场景可能会导致后面节点的数据不准确
            objectData.set(CreditRuleMatchRecordConst.F.SourceDataIds.apiName, Lists.newArrayList(sourceDataIds));
            objectData.set(CreditRuleMatchRecordConst.F.PreDataIds.apiName, Lists.newArrayList(preDataIds));
        } else {
            objectData.set(CreditRuleMatchRecordConst.F.SourceObject.apiName, currentObjectApiName);
            objectData.set(CreditRuleMatchRecordConst.F.SourceDataIds.apiName, Lists.newArrayList(currentObjectDataId));
        }
        objectData.set(CreditRuleMatchRecordConst.F.CurrentObjectApiName.apiName, currentObjectApiName);
        objectData.set(CreditRuleMatchRecordConst.F.CurrentObjectDataId.apiName, currentObjectDataId);
        objectData.set(CreditRuleMatchRecordConst.F.CreditOccupiedRuleId.apiName, creditOccupiedRuleId);
        return objectData;
    }

    public static IObjectData generateCreditFundAccountData(User user) {
        IObjectData objectData = ObjectDataUtil.getBaseObjectData(user, FundAccountConstants.API_NAME);
        objectData.setRecordType("default__c");
//        objectData.setName("信用账户");
        ObjectDataUtil.setObjectDataLang(user.getTenantId(), objectData, FundAccountConstants.Field.Name.apiName, CAI18NKey.FUND_ACCOUNT_CREDIT_ACCOUNT_NAME);

        objectData.set(FundAccountConstants.Field.AccessModule.apiName, AccessModuleEnum.CREDIT.value);
        objectData.set(FundAccountConstants.Field.Type.apiName, FundAccountTypeEnum.Preset.getValue());
        objectData.set(FundAccountConstants.Field.AccountType.apiName, FundAccountAccountTypeEnum.Amount.value);
        objectData.set(FundAccountConstants.Field.Status.apiName, true);
        return objectData;
    }
}
