package com.facishare.crm.customeraccount.predefine.action;

import com.facishare.crm.customeraccount.constants.AccountFrozenRecordConstant;
import com.facishare.crm.customeraccount.constants.CAI18NKey;
import com.facishare.crm.customeraccount.enums.EntryStatusEnum;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardBulkDeleteAction;

public class AccountFrozenRecordBulkDeleteAction extends StandardBulkDeleteAction {
    @Override
    protected void before(Arg arg) {
        super.before(arg);
        CollectionUtils.nullToEmpty(this.dataList).forEach(data -> {
            String entryStatus = data.get(AccountFrozenRecordConstant.Field.EntryStatus.apiName, String.class);
            //防止函数调用
            if (EntryStatusEnum.AlreadyEntry.getValue().equals(entryStatus)) {
                throw new ValidateException(I18N.text(CAI18NKey.NOT_SUPPORT_OPERATION));
            }
        });
    }
}
