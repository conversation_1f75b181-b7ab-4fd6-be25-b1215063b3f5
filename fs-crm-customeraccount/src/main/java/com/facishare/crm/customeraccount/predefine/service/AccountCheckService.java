package com.facishare.crm.customeraccount.predefine.service;

import com.facishare.crm.customeraccount.predefine.service.dto.*;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;

/**
 * 账户授权
 */
@ServiceModule("account_check")
public interface AccountCheckService {

    /**
     * 870'组件扣减'校验规则，拉取字段映射
     */
    @ServiceMethod("get_account_field_mapping")
    GetAccountFieldMappingModel.Result getAccountFieldMapping(ServiceContext serviceContext, GetAccountFieldMappingModel.Arg arg);

    /**
     * 870'组件扣减'校验规则，保存字段映射
     */
    @ServiceMethod("save_account_field_mapping")
    SaveAccountFieldMappingModel.Result saveAccountFieldMapping(ServiceContext serviceContext, SaveAccountFieldMappingModel.Arg arg);


    /**
     * 870需要把数据刷到'支出授权'
     * 排查字段映射中，是否设置了不同的'客户'字段
     */
    @ServiceMethod("get_mapping_account_field")
    GetMappingAccountFieldModel.Result getMappingAccountField(ServiceContext serviceContext, GetMappingAccountFieldModel.Arg arg);

    @ServiceMethod("is_fund_account_can_delete")
    IsFundAccountCanDeleteModel.Result isFundAccountCanDelete(ServiceContext serviceContext, IsFundAccountCanDeleteModel.Arg arg);

    /**
     * 【直接扣减】的【校验规则】，升级为强校验
     */
    @ServiceMethod("upgrade_direct_reduce_force_check_amount")
    UpgradeDirectReduceForceCheckAmountModel.Result upgradeDirectReduceForceCheckAmount(ServiceContext serviceContext);

}