package com.facishare.crm.customeraccount.predefine.action;

import com.facishare.crmcommon.constants.SystemConstants;
import com.facishare.crm.customeraccount.predefine.manager.NewCustomerAccountManager;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class NewCustomerAccountBulkInvalidAction extends StandardBulkInvalidAction {
    private NewCustomerAccountManager newCustomerAccountManager = SpringUtil.getContext().getBean(NewCustomerAccountManager.class);


    @Override
    protected void before(Arg arg) {
        super.before(arg);
        if (CollectionUtils.isEmpty(objectDataList) || objectDescribeMap.isEmpty()) {
            return;
        }

        List<String> dataIds = objectDataList.stream().map(data -> data.get(SystemConstants.Field.Id.apiName, String.class)).collect(Collectors.toList());
        newCustomerAccountManager.checkInvalid(actionContext.getUser(), dataIds);
    }
}
