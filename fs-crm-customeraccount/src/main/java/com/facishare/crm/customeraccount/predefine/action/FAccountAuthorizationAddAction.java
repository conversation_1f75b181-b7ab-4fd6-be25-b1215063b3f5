package com.facishare.crm.customeraccount.predefine.action;

import com.facishare.crm.customeraccount.constants.CAI18NKey;
import com.facishare.crm.customeraccount.constants.FAccountAuthorizationConstants;
import com.facishare.crm.customeraccount.predefine.manager.FAccountAuthorizationManager;
import com.facishare.crm.customeraccount.predefine.manager.UnfreezeAuthDetailManager;
import com.facishare.crmcommon.util.PeerNameUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class FAccountAuthorizationAddAction extends StandardAddAction {
    private FAccountAuthorizationManager fAccountAuthorizationManager = SpringUtil.getContext().getBean(FAccountAuthorizationManager.class);
    private UnfreezeAuthDetailManager unfreezeAuthDetailManager = SpringUtil.getContext().getBean(UnfreezeAuthDetailManager.class);

    //是否是DDS过来的
    private boolean isFromSyncData;
    //对象已经有对应的【账户授权】
    private boolean fAccountAuthorizationDataExist = false;
    private IObjectData accountAuthData;
    /**
     *【账户授权】的what字段，建不了查重规则，调整AddAction
     * 对于DDS过来的
     * 1、如果参数的id已存在，返回成功（id一样，也是第2点的：对象已经有对应的账户授权）
     * 2、如果参数的id不存在，但是对象已经有对应的账户授权，返回错误信息加上已存在的【账户授权】（至少返回id和name、tenant_id、objectApiName等信息）
     */
    @Override
    protected void before(Arg arg) {
        super.before(arg);
        //初始化状态
        objectData.set(FAccountAuthorizationConstants.Field.Status.apiName, "un_init");

        fAccountAuthorizationManager.setDefaultValue(objectData);
        unfreezeAuthDetailManager.setDefaultValue(objectData, detailObjectData);

        //是否存在
        String authorizedObjectApiName = objectData.get(FAccountAuthorizationConstants.Field.AuthorizedObjectApiName.apiName, String.class);
        String authorizedType = objectData.get(FAccountAuthorizationConstants.Field.AuthorizedType.apiName, String.class);
        accountAuthData = fAccountAuthorizationManager.getFAccountAuthorizationData(actionContext.getUser(), authorizedObjectApiName, authorizedType);

        log.info("FAccountAuthorizationAddAction peerName[{}]", actionContext.getPeerName());
        boolean exist = accountAuthData != null;
        if (exist) {
            //x-fs-peer-name=fs-sync-data-all
            isFromSyncData = PeerNameUtil.isFromSyncData(actionContext.getPeerName());
            log.info("FAccountAuthorizationAddAction isFromSyncData[{}], peerName[{}]", isFromSyncData, actionContext.getPeerName());

            if (isFromSyncData) {
                //DDS过来的，如果对应已经有对应的【账户授权】，会返回现有的数据，不保存，这里不用校验
                fAccountAuthorizationDataExist = true;
                return;
            } else {
                throw new ValidateException(I18N.text(CAI18NKey.ACCOUNT_AUTH_EXIST, authorizedObjectApiName));
            }
        }

        fAccountAuthorizationManager.checkForAddAndEdit(actionContext.getUser(), objectData, detailObjectData);
    }

    @Override
    protected Result doAct(Arg arg) {
        if (isFromSyncData && fAccountAuthorizationDataExist) {
            //参考 com.facishare.paas.appframework.core.predef.action.AbstractStandardEditAction.findDbDetailDataMap
            List<IObjectDescribe> detailDescribes = Lists.newArrayList(detailDescribeMap.values()).stream()
                    .filter(x -> detailObjectData.containsKey(x.getApiName())).collect(Collectors.toList());
            Map<String, List<IObjectData>> dbDetailDataMap = serviceFacade.findDetailObjectDataList(detailDescribes, accountAuthData, actionContext.getUser());

            return Result.builder()
                    .objectData(ObjectDataDocument.of(accountAuthData))
                    .details(ObjectDataDocument.ofMap(dbDetailDataMap))
                    .isDuplicate(Boolean.TRUE)
                    .build();
        }

        return super.doAct(arg);
    }
}