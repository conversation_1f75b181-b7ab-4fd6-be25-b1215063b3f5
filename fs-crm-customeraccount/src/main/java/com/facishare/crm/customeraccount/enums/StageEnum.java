package com.facishare.crm.customeraccount.enums;

import java.util.Arrays;
import java.util.Optional;

public enum StageEnum {

    Pre("pre"),
    POST("post"),
    UnKnow("unKnow");

    public final String stage;

    StageEnum(String stage) {
        this.stage = stage;
    }

    public static StageEnum of(String stage) {
        return Arrays.stream(values()).filter(x -> x.stage.equals(stage)).findAny().orElse(UnKnow);
    }
}
