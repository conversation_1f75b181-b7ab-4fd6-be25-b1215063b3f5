package com.facishare.crm.customeraccount.model;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.ToString;

import java.util.List;
import java.util.Map;

@Data
@Builder
public class DataUpdateModel {
    @Data
    @Builder
    public static class Arg {
        /**
         * 更新前的数据，列表中都是同一个对象的数据
         */
        private List<IObjectData> dataListBeforeUpdate;
        /**
         * 更新后的数据（这里还没有更新到数据库，是指内存更新后的数据）
         */
        private List<IObjectData> dataListAfterUpdate;
        /**
         * 更新的字段apiName列表
         */
        private List<String> updateFieldList;
        private boolean needInvalidAfterUpdate;
        private boolean needTriggerInvalidFunction;

        public String getObjectApiName() {
            if (CollectionUtils.empty(dataListBeforeUpdate)) {
                return null;
            }
            return dataListBeforeUpdate.get(0).getDescribeApiName();
        }
    }

    @Getter
    @Builder
    @ToString
    public static class Result {
        /**
         * 同一对象的数据
         */
        private List<IObjectData> dataListBeforeUpdate;
        private List<IObjectData> dataListAfterUpdate;
        private Map<String, Map<String, Object>> dataColumnUpdateMap;
        private boolean needInvalidAfterUpdate;
        private boolean needTriggerInvalidFunction;

        public String getObjectApiName() {
            if (CollectionUtils.empty(dataListBeforeUpdate)) {
                return null;
            }
            return dataListBeforeUpdate.get(0).getDescribeApiName();
        }
    }
}
