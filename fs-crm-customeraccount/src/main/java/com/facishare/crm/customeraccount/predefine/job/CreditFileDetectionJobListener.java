package com.facishare.crm.customeraccount.predefine.job;

import org.quartz.JobBuilder;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.JobKey;
import org.quartz.JobListener;
import org.quartz.Trigger;
import org.quartz.TriggerBuilder;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Deprecated
public class CreditFileDetectionJobListener implements JobListener {
    @Override
    public String getName() {
        return "CreditFileDetectionJobListener";
    }

    @Override
    public void jobToBeExecuted(JobExecutionContext context) {
        log.info("jobToBeExecuted,{}", context.getJobDetail().getKey());
    }

    @Override
    public void jobExecutionVetoed(JobExecutionContext context) {
        log.warn("jobExecutionVetoed:{}", context.getJobDetail().getKey());
    }

    @Override
    public void jobWasExecuted(JobExecutionContext context, JobExecutionException jobException) {
        //信用生效失效任务执行完后，执行信用增量任务
        JobDetail creditFileIncrementJob = JobBuilder.newJob(CreditIncrementJob.class).withIdentity(new JobKey(CreditIncrementJob.class.getName(), "crm")).build();
        Trigger creditFileIncrementTrigger = TriggerBuilder.newTrigger().forJob(creditFileIncrementJob).startNow().build();
        scheduleJob(context, creditFileIncrementJob, creditFileIncrementTrigger);
    }

    private void scheduleJob(JobExecutionContext jobExecutionContext, JobDetail jobDetail, Trigger trigger) {
        try {
            jobExecutionContext.getScheduler().scheduleJob(jobDetail, trigger);
        } catch (Exception e) {
            log.warn("scheduleJob:{} error", jobDetail.getKey().getName(), e);
        }
    }
}
