package com.facishare.crm.customeraccount.predefine.job;

import com.facishare.crm.customeraccount.enums.JobTypeEnum;
import com.facishare.crm.customeraccount.predefine.manager.BillJobManager;
import com.facishare.crm.customeraccount.predefine.manager.CustomerAccountConfigManager;
import com.facishare.crm.customeraccount.predefine.service.dto.CustomerAccountType;
import com.facishare.crm.customeraccount.util.ConfigCenter;
import com.facishare.crm.customeraccount.util.DateUtil;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.quartz.Job;
import org.quartz.JobExecutionContext;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;

@Slf4j
public abstract class IncrementJob implements Job {
    @Override
    public void execute(JobExecutionContext jobExecutionContext) {
        JobTypeEnum jobTypeEnum = jobType();
        log.info("start {} incrementJob", jobTypeEnum);
        CustomerAccountConfigManager customerAccountConfigManager = SpringUtil.getContext().getBean(CustomerAccountConfigManager.class);
        BillJobManager billJobManager = SpringUtil.getContext().getBean(BillJobManager.class);
        Date yesterdayDate = DateUtil.getYesterdayDate(new Date());
        int limit = 100;
        int offset = 0;
        int size = 0;
        do {
            List<String> tenantIds = customerAccountConfigManager.list(CustomerAccountType.CustomerAccountEnableSwitchStatus.ENABLE.getValue(), offset, limit);
            if (CollectionUtils.isNotEmpty(tenantIds)) {
                size = tenantIds.size();
                for (String tenantId : tenantIds) {
                    if (exclude(tenantId, jobTypeEnum)) {
                        continue;
                    }
                    try {
                        doIncrementJob(tenantId, yesterdayDate, billJobManager);
                    } catch (Exception e) {
                        log.warn("doIncrementJob:{} error tenantId:{}", jobTypeEnum.getDes(), tenantId, e);
                    }
                }
            } else {
                size = 0;
            }
            offset += limit;
        } while (size == limit);
    }

    public abstract void doIncrementJob(String tenantId, Date yesterdayDate, BillJobManager billJobManager) throws MetadataServiceException;

    public abstract JobTypeEnum jobType();

    private boolean exclude(String tenantId, JobTypeEnum jobTypeEnum) {
        if (Objects.isNull(ConfigCenter.incrementJobConfig)) {
            return false;
        }
        Set<String> tenantIds = null;
        switch (jobTypeEnum) {
        case CREDIT:
            tenantIds = ConfigCenter.incrementJobConfig.getCredit();
            break;
        case CUSTOMER_ACCOUNT_BILL:
            tenantIds = ConfigCenter.incrementJobConfig.getCustomerAccountBill();
            break;
        case PREPAY:
            tenantIds = ConfigCenter.incrementJobConfig.getPrepay();
            break;
        case REBATE_INCOME:
            tenantIds = ConfigCenter.incrementJobConfig.getRebateIncome();
            break;
        case REBATE_OUTCOME:
            tenantIds = ConfigCenter.incrementJobConfig.getRebateOutcome();
            break;
        }
        if (CollectionUtils.isEmpty(tenantIds)) {
            return false;
        }
        return tenantIds.contains(tenantId);
    }
}
