package com.facishare.crm.customeraccount.predefine.service.dto;

import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.ToString;

/**
 * @author: dongzhb
 * @date: 2019/7/1
 * @Description: 客户账户资金信息
 */
@Data
@ToString
public class GetCustomerAccountModel {
    @Data
    @ToString
    public static class Result {
        Map<String, Object> objectData;
        List<CustomerAccountType.SettleType> settleTypeEnumList;
    }

    @Data
    @ToString
    public static class Arg {
        String customerId;
    }
}
