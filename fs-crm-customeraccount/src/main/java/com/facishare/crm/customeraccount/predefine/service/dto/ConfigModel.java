package com.facishare.crm.customeraccount.predefine.service.dto;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;
import java.util.Set;

public class ConfigModel {

    @Data
    public static class GetArg {
        private Set<String> keys;
        private Boolean isAllConfig = Boolean.FALSE;
    }

    @Data
    public static class GetResult {
        private List<ConfigData> values = Lists.newArrayList();
    }

    @Data
    public static class ConfigData {
        private String key;
        private String value;

        public ConfigData(String key, String value) {
            this.key = key;
            this.value = value;
        }
    }
}
