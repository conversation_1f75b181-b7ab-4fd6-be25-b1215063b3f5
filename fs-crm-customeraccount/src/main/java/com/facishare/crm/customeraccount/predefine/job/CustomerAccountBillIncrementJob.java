package com.facishare.crm.customeraccount.predefine.job;

import com.facishare.crm.customeraccount.predefine.manager.BillJobManager;
import com.facishare.crm.customeraccount.predefine.manager.CustomerAccountBillManager;
import com.facishare.crm.customeraccount.util.ConfigCenter;
import com.facishare.crm.customeraccount.util.DateUtil;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.quartz.Job;
import org.quartz.JobExecutionContext;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 客户账户流水增量任务：检查昨日流水，日账单是否一致，发送日报
 * 该任务必须在日账单生成之后，且需要昨天和前天的日账单数据对比
 */
@Slf4j
public class CustomerAccountBillIncrementJob implements Job {
    @Override
    public void execute(JobExecutionContext jobExecutionContext) {
        log.info("start CustomerAccountBillIncrementJob");
        BillJobManager billJobManager = SpringUtil.getContext().getBean(BillJobManager.class);
        CustomerAccountBillManager customerAccountBillManager = SpringUtil.getContext().getBean(CustomerAccountBillManager.class);
        Date yesterdayDate = DateUtil.getYesterdayDate(new Date());
        //获取昨天有流水记录的企业
        List<String> tenantIds = customerAccountBillManager.listTenantIdsByBillDate(yesterdayDate);
        if (CollectionUtils.isEmpty(tenantIds)) {
            log.warn("no bill data");
            return;
        }
        for (String tenantId : tenantIds) {
            if (exclude(tenantId)) {
                continue;
            }
            User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
            List<String> customerAccountIds = customerAccountBillManager.listCustomerAccountIdsByTenantIdAndBillDate(tenantId, yesterdayDate);
            for (String customerAccountId : customerAccountIds) {
                billJobManager.doCustomerAccountBillIncrementJob(user, customerAccountId, yesterdayDate);
            }
        }
    }

    private boolean exclude(String tenantId) {
        if (Objects.isNull(ConfigCenter.incrementJobConfig)) {
            return false;
        }
        Set<String> tenantIds = ConfigCenter.incrementJobConfig.getCustomerAccountBill();
        if (CollectionUtils.isEmpty(tenantIds)) {
            return false;
        }
        return tenantIds.contains(tenantId);
    }
}
