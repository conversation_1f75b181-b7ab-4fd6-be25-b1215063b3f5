package com.facishare.crm.customeraccount.tools;

import com.facishare.crm.customeraccount.constants.AccountFrozenRecordConstant;
import com.facishare.crm.customeraccount.constants.AccountTransactionFlowConst;
import com.facishare.crm.customeraccount.constants.NewCustomerAccountConstants;
import com.facishare.crm.customeraccount.constants.UnfreezeDetailConstant;
import com.facishare.crm.customeraccount.enums.ConfigKeyEnum;
import com.facishare.crm.customeraccount.enums.EntryStatusEnum;
import com.facishare.crmcommon.util.SearchUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class CancelEntryManager {
    private final Map<String, CancelEntryHandler> objectCancelEntryHandlerMap = Maps.newHashMap();
    private final ServiceFacade serviceFacade;

    @Autowired
    public CancelEntryManager(ServiceFacade serviceFacade) {
        this.serviceFacade = serviceFacade;
        objectCancelEntryHandlerMap.put(AccountTransactionFlowConst.API_NAME, new FlowCancelEntryHandler(serviceFacade));
        objectCancelEntryHandlerMap.put(AccountFrozenRecordConstant.API_NAME, new FrozenCancelEntryHandler(serviceFacade));
    }

    public Map<String, Map<String, Object>> cancelEntry(User user, String objectApiName, String dataId) {
        CancelEntryHandler cancelEntryHandler = objectCancelEntryHandlerMap.get(objectApiName);
        if (Objects.isNull(cancelEntryHandler)) {
            return Maps.newHashMap();
        }
        IObjectData objectData = serviceFacade.findObjectData(user, dataId, objectApiName);
        return cancelEntryHandler.cancelEntry(user, objectData);
    }

    private static class FrozenCancelEntryHandler extends CancelEntryHandler {
        public FrozenCancelEntryHandler(ServiceFacade serviceFacade) {
            super(serviceFacade);
        }

        @Override
        public Map<String, Map<String, Object>> cancelEntry(User user, IObjectData objectData) {
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, UnfreezeDetailConstant.Field.AccountFrozenRecordId.apiName, objectData.getId());
            searchTemplateQuery.setFilters(filters);
            List<IObjectData> unfreezeDetailList = serviceFacade.findBySearchQuery(user, UnfreezeDetailConstant.API_NAME, searchTemplateQuery).getData();
            if (CollectionUtils.notEmpty(unfreezeDetailList)) {
                throw new ValidateException("has unfreeze detail");
            }
            String customerAccountId = objectData.get(AccountFrozenRecordConstant.Field.CustomerAccountId.apiName, String.class);
            IObjectData customerAccountData = serviceFacade.findObjectData(user, customerAccountId, NewCustomerAccountConstants.API_NAME);
            BigDecimal frozenAmount = objectData.get(AccountFrozenRecordConstant.Field.FreezeAmount.apiName, BigDecimal.class, BigDecimal.ZERO);
            String entryStatus = objectData.get(AccountFrozenRecordConstant.Field.EntryStatus.apiName, String.class);
            if (EntryStatusEnum.Cancelled.getValue().equals(entryStatus)) {
                throw new ValidateException("already cancelled");
            }
            objectData.set(AccountFrozenRecordConstant.Field.EntryStatus.apiName, EntryStatusEnum.Cancelled.getValue());

            Map<String, Map<String, Object>> columnHandleMap = Maps.newHashMap();
            Map<String, Object> customerAccountFieldMap = Maps.newHashMap();
            List<String> fieldList = Lists.newArrayList(NewCustomerAccountConstants.Field.AvailableBalance.apiName, NewCustomerAccountConstants.Field.OccupiedAmount.apiName);
            customerAccountFieldMap.put(NewCustomerAccountConstants.Field.AvailableBalance.apiName, frozenAmount);
            customerAccountFieldMap.put(NewCustomerAccountConstants.Field.OccupiedAmount.apiName, frozenAmount.negate());
            columnHandleMap.put(customerAccountId, customerAccountFieldMap);

            log.info("customerAccountColumnMap:{}", columnHandleMap);
            executeInTransaction(() -> {
                serviceFacade.updateObjectData(user, objectData);
                serviceFacade.batchUpdateByFields(user, Lists.newArrayList(customerAccountData), fieldList, columnHandleMap);
            });
            return columnHandleMap;
        }
    }

    private static class FlowCancelEntryHandler extends CancelEntryHandler {
        public FlowCancelEntryHandler(ServiceFacade serviceFacade) {
            super(serviceFacade);
        }

        @Override
        public Map<String, Map<String, Object>> cancelEntry(User user, IObjectData objectData) {
            String entryStatus = objectData.get(AccountFrozenRecordConstant.Field.EntryStatus.apiName, String.class);
            if (EntryStatusEnum.Cancelled.getValue().equals(entryStatus)) {
                throw new ValidateException("already cancelled");
            }
            String customerAccountId = objectData.get(AccountTransactionFlowConst.Field.CustomerAccount.apiName, String.class);
            IObjectData customerAccountData = serviceFacade.findObjectData(user, customerAccountId, NewCustomerAccountConstants.API_NAME);
            String recordType = objectData.getRecordType();
            BigDecimal incrementAmount;
            if (AccountTransactionFlowConst.RecordType.IncomeRecordType.apiName.equals(recordType)) {
                incrementAmount = objectData.get(AccountTransactionFlowConst.Field.RevenueAmount.apiName, BigDecimal.class, BigDecimal.ZERO);
                incrementAmount = incrementAmount.negate();
            } else if (AccountTransactionFlowConst.RecordType.OutcomeRecordType.apiName.equals(recordType)) {
                incrementAmount = objectData.get(AccountTransactionFlowConst.Field.ExpenseAmount.apiName, BigDecimal.class, BigDecimal.ZERO);
            } else {
                return Maps.newHashMap();
            }
            String configValue = serviceFacade.findTenantConfig(user, ConfigKeyEnum.ACCOUNT_CHECK_RULE.key);
            boolean checkRuleEnable = ConfigKeyEnum.ACCOUNT_CHECK_RULE.enabled(configValue);

            Map<String, Map<String, Object>> columnHandleMap = Maps.newHashMap();
            Map<String, Object> customerAccountFieldMap = Maps.newHashMap();
            List<String> updateFields = Lists.newArrayList(NewCustomerAccountConstants.Field.AccountBalance.apiName);
            customerAccountFieldMap.put(NewCustomerAccountConstants.Field.AccountBalance.apiName, incrementAmount);
            List<IObjectData> unfreezeDetailDataList = Lists.newArrayList();
            if (checkRuleEnable) {
                SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
                List<IFilter> filters = Lists.newArrayList();
                SearchUtil.fillFilterEq(filters, UnfreezeDetailConstant.Field.AccountTransactionFlow.apiName, objectData.getId());
                searchTemplateQuery.setFilters(filters);
                List<IObjectData> unfreezeDetailList = serviceFacade.findBySearchQuery(user, UnfreezeDetailConstant.API_NAME, searchTemplateQuery).getData();
                if (CollectionUtils.size(unfreezeDetailList) > 1) {
                    log.warn("more than one unfreeze detail,flowData:{},unfreezeDetailList:{}", objectData, unfreezeDetailList);
                    throw new ValidateException(String.format("[%s]-[%s] has more than one unfreeze detail", objectData.getDescribeApiName(), objectData.getName()));
                }
                if (CollectionUtils.notEmpty(unfreezeDetailList)) {
                    //解冻生成的流水
                    IObjectData unfreezeData = unfreezeDetailList.get(0);
                    unfreezeData.set(UnfreezeDetailConstant.Field.EntryStatus.apiName, EntryStatusEnum.Cancelled.getValue());
                    unfreezeDetailDataList.add(unfreezeData);
                    customerAccountFieldMap.put(NewCustomerAccountConstants.Field.OccupiedAmount.apiName, incrementAmount);
                    updateFields.add(NewCustomerAccountConstants.Field.OccupiedAmount.apiName);
                } else {
                    //新建或者入账生成的流水
                    customerAccountFieldMap.put(NewCustomerAccountConstants.Field.AvailableBalance.apiName, incrementAmount);
                    updateFields.add(NewCustomerAccountConstants.Field.AvailableBalance.apiName);
                }
            }
            columnHandleMap.put(customerAccountId, customerAccountFieldMap);
            objectData.set(AccountTransactionFlowConst.Field.EntryStatus.apiName, EntryStatusEnum.Cancelled.getValue());

            log.info("customerAccountColumnMap:{}", columnHandleMap);
            executeInTransaction(() -> {
                serviceFacade.updateObjectData(user, objectData);
                if (CollectionUtils.notEmpty(unfreezeDetailDataList)) {
                    serviceFacade.updateObjectData(user, unfreezeDetailDataList.get(0));
                    serviceFacade.invalid(unfreezeDetailDataList.get(0), user);
                }
                serviceFacade.batchUpdateByFields(user, Lists.newArrayList(customerAccountData), updateFields, columnHandleMap);
                serviceFacade.invalid(objectData, user);
            });
            return columnHandleMap;
        }
    }

    private abstract static class CancelEntryHandler {
        protected ServiceFacade serviceFacade;

        public CancelEntryHandler(ServiceFacade serviceFacade) {
            this.serviceFacade = serviceFacade;
        }

        public abstract Map<String, Map<String, Object>> cancelEntry(User user, IObjectData objectData);

        public void executeInTransaction(Runnable runnable) {
            PlatformTransactionManager tm = (PlatformTransactionManager) SpringUtil.getContext().getBean("paasMetadataTransactionManager");
            TransactionTemplate template = new TransactionTemplate(tm);
            template.execute(transactionStatus -> {
                runnable.run();
                return null;
            });
        }
    }

}
