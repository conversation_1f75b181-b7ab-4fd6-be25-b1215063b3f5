package com.facishare.crm.customeraccount.predefine.service.impl;

import com.facishare.crmcommon.constants.SystemConstants.LifeStatus;
import com.facishare.crm.customeraccount.constants.CAI18NKey;
import com.facishare.crm.customeraccount.constants.CustomerAccountConstants;
import com.facishare.crm.customeraccount.constants.RebateIncomeDetailConstants;
import com.facishare.crm.customeraccount.constants.RebateOutcomeDetailConstants;
import com.facishare.crm.customeraccount.constants.RebateOutcomeDetailConstants.Field;
import com.facishare.crm.customeraccount.constants.SystemConstants;
import com.facishare.crm.customeraccount.predefine.manager.CustomerAccountManager;
import com.facishare.crm.customeraccount.predefine.manager.RebateIncomeDetailManager;
import com.facishare.crm.customeraccount.predefine.manager.RebateOutcomeDetailManager;
import com.facishare.crm.customeraccount.predefine.service.CommonService;
import com.facishare.crm.customeraccount.predefine.service.RebateOutcomeDetailService;
import com.facishare.crm.customeraccount.predefine.service.dto.CreateModel;
import com.facishare.crm.customeraccount.predefine.service.dto.CreateModel.Result;
import com.facishare.crm.customeraccount.predefine.service.dto.ListByIdModel;
import com.facishare.crm.customeraccount.predefine.service.dto.RebateIncomeModle;
import com.facishare.crm.customeraccount.util.ObjectDataUtil;
import com.facishare.crmcommon.util.SearchUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @IgnoreI18nFile
 */
@Slf4j
@Component
public class RebateOutcomeDetailServiceImpl extends CommonService implements RebateOutcomeDetailService {
    public static final String REBATE_AVAILABLE_ERROR = "账户返利可用余额不足";
    public static final String REBATE_INCOME_DETAIL_ERROR = "返利支出可用返利余额不足";
    public static final String CUSTOMER_ID_ERROR = "无关联客户";
    @Autowired
    private CustomerAccountManager customerAccountManager;
    @Autowired
    RebateIncomeDetailManager rebateIncomeDetailManager;
    @Autowired
    RebateOutcomeDetailManager rebateOutcomeDetailManager;

    @Override
    public ListByIdModel.Result listByRebateIncomeId(ListByIdModel.RebateOutcomeArg arg, ServiceContext serviceContext) {

        List<IFilter> list = new ArrayList<>();
        List<OrderBy> orderByList = new ArrayList<>();
        SearchUtil.fillFilterEq(list, RebateOutcomeDetailConstants.Field.RebateIncomeDetail.apiName, arg.getId());
        SearchUtil.fillOrderBy(orderByList, SystemConstants.Field.CreateTime.apiName, false);
        QueryResult<IObjectData> queryResult = customerAccountManager.searchQuery(serviceContext.getUser(), RebateOutcomeDetailConstants.API_NAME, list, orderByList, arg.getOffset(), arg.getLimit());
        List<IObjectData> objectDataList = queryResult.getData();
        List<IObjectData> orderPaymentList = null;
        if (CollectionUtils.isNotEmpty(objectDataList)) {
            if (objectDataList.get(0).get(RebateOutcomeDetailConstants.Field.OrderPayment.apiName, String.class) != null) {
                List<String> orderPaymentIds = objectDataList.stream().map(o -> ObjectDataUtil.getReferenceId(o, RebateOutcomeDetailConstants.Field.OrderPayment.apiName)).collect(Collectors.toList());
                list.clear();
                SearchUtil.fillFilterIn(list, "_id", orderPaymentIds);
                orderPaymentList = customerAccountManager.searchQuery(serviceContext.getUser(), "OrderPaymentObj", list, orderByList, 0, 100).getData();
            }
        }
        if (orderPaymentList != null) {
            for (IObjectData objectData : objectDataList) {
                String orderPaymentId = ObjectDataUtil.getReferenceId(objectData, RebateOutcomeDetailConstants.Field.OrderPayment.apiName);
                for (IObjectData orderPaymentData : orderPaymentList) {
                    String id = orderPaymentData.getId();
                    if (id.equals(orderPaymentId)) {
                        String name = orderPaymentData.getName();
                        objectData.set("payment_code", name);
                        continue;
                    }
                }
            }
        }
        ListByIdModel.Result result = new ListByIdModel.Result();
        result.setObjectDatas(ObjectDataDocument.ofList(queryResult.getData()));
        result.setTotalNumber(queryResult.getTotalNumber());
        result.setPageNumber(arg.getPageNumber());
        result.setPageSize(arg.getPageSize());
        return result;
    }

    @Override
    public CreateModel.Result createRebateOutcomeDetailByCustomFunction(User user, ObjectDataDocument objectData) {
        IObjectData iObjectData = objectData.toObjectData();
        log.info("createRebateOutcomeDetailByCustomFunction,objectData={}", objectData);
        String customerId = null;
        IObjectData rebateIncomeDetailObjectData = null;
        BigDecimal amount = iObjectData.get(Field.Amount.apiName, BigDecimal.class);
        String rebateIncomeDetailId = iObjectData.get(Field.RebateIncomeDetail.apiName, String.class);

        if (StringUtils.isBlank(iObjectData.get(Field.Customer.apiName, String.class)) && !"-1".equals(rebateIncomeDetailId)) {
            /**指定返利*/
            rebateIncomeDetailObjectData = serviceFacade.findObjectData(user, rebateIncomeDetailId, RebateIncomeDetailConstants.API_NAME);
            customerId = rebateIncomeDetailObjectData.get(RebateIncomeDetailConstants.Field.Customer.apiName, String.class);
        } else {
            customerId = iObjectData.get(Field.Customer.apiName, String.class);
        }

        if (customerId == null) {
            throw new ValidateException(I18N.text(CAI18NKey.REBATE_OUTCOME_DETAIL_ERROR_MESSAGE, CUSTOMER_ID_ERROR));
        }

        /**客户账户信息校验*/
        IObjectData customerAccountObj = customerAccountManager.getCustomerAccountByCustomerId(user, customerId);
        BigDecimal rebateAvailable = customerAccountObj.get(CustomerAccountConstants.Field.RebateAvailableBalance.apiName, BigDecimal.class);
        if (rebateAvailable.compareTo(amount) < 0) {
            throw new ValidateException(I18N.text(CAI18NKey.REBATE_OUTCOME_DETAIL_ERROR_MESSAGE, REBATE_AVAILABLE_ERROR));
        }

        iObjectData.set(SystemConstants.Field.LifeStatus.apiName, LifeStatus.Normal.value);
        iObjectData.setOwner(Lists.newArrayList(user.getUserId()));
        /**指定返利收入*/
        if (rebateIncomeDetailObjectData != null) {
            BigDecimal availableRebate = rebateIncomeDetailObjectData.get(RebateIncomeDetailConstants.Field.AvailableRebate.apiName, BigDecimal.class);
            if (availableRebate.compareTo(amount) < 0) {
                throw new ValidateException(I18N.text(CAI18NKey.REBATE_OUTCOME_DETAIL_ERROR_MESSAGE, REBATE_INCOME_DETAIL_ERROR));
            }
            iObjectData.setDescribeApiName(RebateOutcomeDetailConstants.API_NAME);
            iObjectData.set(RebateOutcomeDetailConstants.Field.TransactionTime.apiName, iObjectData.get(RebateOutcomeDetailConstants.Field.TransactionTime.apiName));
            iObjectData.set(RebateOutcomeDetailConstants.Field.RebateIncomeDetail.apiName, rebateIncomeDetailObjectData.getId());
            /**扣减返利余额*/
            iObjectData.set(RebateOutcomeDetailConstants.Field.Amount.apiName, amount);
            iObjectData.set(RebateOutcomeDetailConstants.Field.OrderPayment.apiName, iObjectData.get(RebateOutcomeDetailConstants.Field.OrderPayment.apiName));
            rebateOutcomeDetailManager.createRebateOutcomeAndUpdateBalance(user, iObjectData);
        } else {
            /**可用返利收入**/
            List<RebateIncomeModle.PayForOutcomeModel> incomeObjectDataListToPay = rebateIncomeDetailManager.obtainRebateIncomeToPayList(user, amount, customerId);
            //扣减返利收入和创建支出
            for (RebateIncomeModle.PayForOutcomeModel payForOutcome : incomeObjectDataListToPay) {
                iObjectData.setDescribeApiName(RebateOutcomeDetailConstants.API_NAME);
                iObjectData.set(RebateOutcomeDetailConstants.Field.TransactionTime.apiName, iObjectData.get(RebateOutcomeDetailConstants.Field.TransactionTime.apiName));
                iObjectData.set(RebateOutcomeDetailConstants.Field.RebateIncomeDetail.apiName, payForOutcome.getRebateIncomeObj().getId());
                iObjectData.set(RebateOutcomeDetailConstants.Field.Amount.apiName, payForOutcome.getPayAmount());
                iObjectData.set(RebateOutcomeDetailConstants.Field.OrderPayment.apiName, iObjectData.get(RebateOutcomeDetailConstants.Field.OrderPayment.apiName));
                rebateOutcomeDetailManager.createRebateOutcomeAndUpdateBalance(user, iObjectData);
            }
        }
        CreateModel.Result result = new Result();
        result.setObjectData(ObjectDataDocument.of(iObjectData));
        return result;
    }
}
