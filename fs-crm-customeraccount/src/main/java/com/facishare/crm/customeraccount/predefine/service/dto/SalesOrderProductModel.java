package com.facishare.crm.customeraccount.predefine.service.dto;

import com.facishare.paas.metadata.api.IObjectData;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
public class SalesOrderProductModel {
    @Data
    public static class SalesOrderProductArg {
        private String tenantId;
        private Long endTime;
        private Long startTime;
        private Integer limit;
    }

    @Data
    @Builder
    public static class SalesOrderProductResult {
        private List<IObjectData> dataList;
    }
}
