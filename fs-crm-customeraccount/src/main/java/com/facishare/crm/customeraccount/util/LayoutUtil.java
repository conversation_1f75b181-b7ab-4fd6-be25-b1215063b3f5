package com.facishare.crm.customeraccount.util;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.appframework.metadata.ComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.ui.layout.component.MultiTableComponent;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;
import java.util.Set;

@Slf4j
public class LayoutUtil {
    public static LayoutDocument removeFields(LayoutDocument layoutDocument, Set<String> fieldNamesToRemove) {
        if (Objects.isNull(layoutDocument)) {
            return null;
        }
        if (CollectionUtils.empty(fieldNamesToRemove)) {
            return layoutDocument;
        }
        LayoutExt layoutExt = LayoutExt.of(layoutDocument);
        layoutExt.getFormComponent().ifPresent(x -> {
            x.removeFields(fieldNamesToRemove);
        });
        return LayoutDocument.of(layoutExt);
    }

    public static LayoutDocument readOnly(LayoutDocument layoutDocument, Set<String> readOnlyFields) {
        if (Objects.isNull(layoutDocument)) {
            return null;
        }
        if (CollectionUtils.empty(readOnlyFields)) {
            return layoutDocument;
        }
        LayoutExt layoutExt = LayoutExt.of(layoutDocument);
        layoutExt.getFormComponent().ifPresent(x -> {
            x.setReadOnly(readOnlyFields, true);
        });
        return LayoutDocument.of(layoutExt);
    }

    public static LayoutDocument setReadOnly(LayoutDocument layoutDocument, Set<String> fieldApiNames, boolean readOnly) {
        LayoutExt layoutExt = LayoutExt.of(layoutDocument);
        layoutExt.getFormComponent().ifPresent(x -> {
            x.setReadOnly(fieldApiNames, readOnly);
        });
        return LayoutDocument.of(layoutExt);
    }

    public static LayoutDocument removeButtonsByAction(LayoutDocument layoutDocument, Set<String> buttonsToRemove) {
        if (Objects.isNull(layoutDocument)) {
            return layoutDocument;
        }
        if (CollectionUtils.empty(buttonsToRemove)) {
            return layoutDocument;
        }
        LayoutExt layoutExt = LayoutExt.of(layoutDocument);
        List<IButton> buttonList = layoutExt.getButtons();
        if (CollectionUtils.empty(buttonList)) {
            return layoutDocument;
        }
        buttonList.removeIf(x -> buttonsToRemove.contains(x.getAction()));
        layoutExt.setButtons(buttonList);
        return LayoutDocument.of(layoutExt);
    }


    public static ILayout removeChildComponentsAllButtonOfMultiTable(ILayout layout) {
        if (Objects.isNull(layout)) {
            return null;
        }
        LayoutExt layoutExt = LayoutExt.of(layout);
        try {
            layoutExt.getComponents().stream().filter(x -> "multi_table".equals(x.getType())).findFirst().ifPresent(x -> {
                try {
                    ((MultiTableComponent) x).getChildComponents().forEach(
                            childComponent -> childComponent.setButtons(Lists.newArrayList())
                    );
                } catch (MetadataServiceException e) {
                    log.warn("", e);
                }
            });
        } catch (MetadataServiceException e) {
            log.warn("", e);
        }
        return layoutExt.getLayout();
    }
}
