package com.facishare.crm.customeraccount.predefine.controller;

import com.facishare.crm.customeraccount.util.ObjectDataUtil;
import com.facishare.paas.appframework.core.predef.controller.StandardDetailController;
import com.facishare.paas.appframework.metadata.ObjectDataExt;

import lombok.extern.slf4j.Slf4j;

/**
 * Created by xujf on 2017/10/21.
 */
@Slf4j
public class CustomerAccountDetailController extends StandardDetailController {

    @Override
    public Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        ObjectDataUtil.setAvailableCredit(controllerContext.getUser(), ObjectDataExt.of(result.getData()));
        return result;
    }
}
