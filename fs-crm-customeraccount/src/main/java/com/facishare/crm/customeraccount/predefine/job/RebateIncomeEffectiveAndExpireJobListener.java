package com.facishare.crm.customeraccount.predefine.job;

import org.quartz.JobBuilder;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.JobKey;
import org.quartz.JobListener;
import org.quartz.Trigger;
import org.quartz.TriggerBuilder;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Deprecated
public class RebateIncomeEffectiveAndExpireJobListener implements JobListener {
    private int n;

    @Override
    public String getName() {
        return "RebateIncomeEffectiveAndExpireJobListener";
    }

    @Override
    public void jobToBeExecuted(JobExecutionContext context) {
        log.info("jobToBeExecuted,{}", context.getJobDetail().getKey());
    }

    @Override
    public void jobExecutionVetoed(JobExecutionContext context) {
        log.warn("jobExecutionVetoed:{}", context.getJobDetail().getKey());
    }

    @Override
    public void jobWasExecuted(JobExecutionContext context, JobExecutionException jobException) {
        //返利生效，失效任务做完后，执行返利收入增量任务
        synchronized (this) {
            n++;
            n = n % 2;
            log.info("jobWasExecuted job:{},n:{},thread:{}", context.getJobDetail().getKey(), n, Thread.currentThread().getName());
            if (n == 0) {
                //执行返利收入增量任务
                JobDetail rebateIncomeIncrementJob = JobBuilder.newJob(RebateIncomeIncrementJob.class).withIdentity(new JobKey(RebateIncomeIncrementJob.class.getName(), "crm")).build();
                Trigger rebateIncomeIncrementTrigger = TriggerBuilder.newTrigger().forJob(rebateIncomeIncrementJob).startNow().build();
                scheduleJob(context, rebateIncomeIncrementJob, rebateIncomeIncrementTrigger);
            }
        }
    }

    private void scheduleJob(JobExecutionContext jobExecutionContext, JobDetail jobDetail, Trigger trigger) {
        try {
            jobExecutionContext.getScheduler().scheduleJob(jobDetail, trigger);
        } catch (Exception e) {
            log.warn("scheduleJob:{} error", jobDetail.getKey().getName(), e);
        }
    }
}
