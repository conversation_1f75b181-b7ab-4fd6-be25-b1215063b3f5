package com.facishare.crm.customeraccount.predefine.service.dto;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonProperty;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.google.gson.annotations.SerializedName;

import lombok.Data;

public class UpdateCreditModel {
    @Data
    public static class Arg {
        @SerializedName("customer_id")
        @JsonProperty("customer_id")
        private String customerId;
        @SerializedName("credit_available_quota")
        @JsonProperty("credit_available_quota")
        private BigDecimal availableCredit;
        @SerializedName("used_credit_quota")
        @JsonProperty("used_credit_quota")
        private BigDecimal usedCredit;
    }

    @Data
    public static class Result {
        private ObjectDataDocument objectData;
    }
}
