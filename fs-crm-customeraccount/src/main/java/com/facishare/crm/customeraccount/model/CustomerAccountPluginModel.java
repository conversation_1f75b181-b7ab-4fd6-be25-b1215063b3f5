package com.facishare.crm.customeraccount.model;

import com.facishare.crm.customeraccount.constants.RebateConstants;
import com.facishare.crmcommon.util.DomainPluginDescribeExt;
import com.facishare.enterprise.common.util.JsonUtil;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.reflect.TypeUtils;

import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public class CustomerAccountPluginModel {
    //com.facishare.crm.sfa.predefine.service.rebatecoupon.model.RebateCouponUse.Arg
    @Builder
    @Data
    public static class Arg {
        /**
         * 当前用户
         */
        private User user;
        /**
         * 主对象ApiName
         */
        private String masterObjectApiName;
        /**
         * 请求ID，不同订单requestId不一样,当单状态下保持requestId不变
         */
        private String requestId;
        /**
         * 数据id
         */
        private String dataId;

        /**
         * 主对象数据
         */
        private ObjectDataDocument masterData;

        /**
         * 主对象数据
         */
        private ObjectDataDocument dbMasterData;

        /**
         * 从对象数据
         */
        private List<ObjectDataDocument> detailDataList;

        /**
         * 操作
         */
        private String action;

        /**
         * before,after
         * ActionDomainPlugin.BEFORE ……
         */
        private String method;

        /**
         * 插件
         */
        private DomainPluginDescribeExt dmExt;

        // 审批流回调相关参数 triggerType callbackData status
        /**
         * 审批流发起动作
         */
        private String triggerType;

        /**
         * 审批流回调数据
         */
        private Map<String, Object> callbackData;

        /**
         * 审批流状态
         */
        private String status;

        /**
         * 批量作废的数据
         */
        private List<ObjectDataDocument> objectDataList;

        private boolean doActComplete;

        private Map<String, String> contextData;
    }

    /**
     * 返利过期，需要红冲的数据
     */
    @Data
    public static class RebateChargeOffData {
        private String objectApiName;
        private String objectDataId;
        private String chargeOffObjectApiName;
        private BigDecimal amount;
    }

    /**
     * 返利过期，需要红冲的数据
     */
    public static List<RebateChargeOffData> getRebateChargeOffDatas(CustomerAccountPluginModel.Arg arg) {
        if (arg.getContextData() == null || !arg.getContextData().containsKey(RebateConstants.UN_USE_EXPIRE_DATA)) {
            return Lists.newArrayList();
        }

        String chargeOffDataJson = arg.getContextData().get(RebateConstants.UN_USE_EXPIRE_DATA);
        if (Strings.isNullOrEmpty(chargeOffDataJson)) {
            return Lists.newArrayList();
        }

        Type type = TypeUtils.parameterize(List.class, RebateChargeOffData.class);
        return JsonUtil.fromJson(chargeOffDataJson, type);
    }

    public enum ExpenseAmountTypeEnum {
        /**
         * 全部
         */
        All("all"),
        /**
         * 增加
         */
        Increase("increase"),
        /**
         * 减小
         */
        Decrease("decrease"),
        /**
         * 不变
         */
        NoChange("noChange"),
        ;
        private final String value;

        ExpenseAmountTypeEnum(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }
}
