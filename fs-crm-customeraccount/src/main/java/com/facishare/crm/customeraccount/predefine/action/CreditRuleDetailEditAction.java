package com.facishare.crm.customeraccount.predefine.action;

import com.facishare.crm.customeraccount.constants.CAI18NKey;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;

public class CreditRuleDetailEditAction extends StandardEditAction {
    @Override
    protected void before(Arg arg) {
        throw new ValidateException(I18N.text(CAI18NKey.CREDIT_RULE_DETAIL_NOT_SUPPORT_SEPARATE_HANDLE));
    }
}
