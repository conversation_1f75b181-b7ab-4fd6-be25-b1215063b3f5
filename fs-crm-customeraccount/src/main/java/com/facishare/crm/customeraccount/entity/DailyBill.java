package com.facishare.crm.customeraccount.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class DailyBill extends BaseEntity {
    private String customerAccountId;
    private Date billDate;
    private BigDecimal prepayBalance;
    private BigDecimal prepayLockedBalance;
    private BigDecimal rebateBalance;
    private BigDecimal rebateLockedBalance;
    private BigDecimal creditAvailableQuota;
    private String remark;

}
