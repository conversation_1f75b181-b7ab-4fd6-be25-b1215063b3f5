package com.facishare.crm.customeraccount.predefine.manager;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.customeraccount.constants.AccountCheckRuleConstants;
import com.facishare.crm.customeraccount.constants.CAI18NKey;
import com.facishare.crm.customeraccount.constants.Constants;
import com.facishare.crm.customeraccount.enums.AccountCheckRuleTypeEnum;
import com.facishare.crm.customeraccount.enums.BizActionEnum;
import com.facishare.crm.customeraccount.enums.ReduceTriggerActionEnum;
import com.facishare.crm.customeraccount.predefine.service.BizJsonUtil;
import com.facishare.crm.customeraccount.util.ServiceContextUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ButtonDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.ButtonService;
import com.facishare.paas.appframework.core.predef.service.dto.button.*;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefAction;
import com.facishare.paas.metadata.impl.UdefAction;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;


@Component
@Slf4j
public class CaButtonManager {
    @Autowired
    private ButtonService buttonService;
    @Autowired
    private AccountCheckRuleManager accountCheckRuleManager;

    private final Set<String> checkRuleBizKeys = Sets.newHashSet(Constants.PRE_VALIDATE_BIZ_KEY, Constants.POST_FROZEN_BIZ_KEY, Constants.POST_UNFREEZE_BIZ_KEY,
        Constants.PRE_EDIT_KEY, Constants.POST_EDIT_KEY, Constants.POST_INVALID_KEY);

    public void add(RequestContext requestContext, IObjectData objectData) {
        ServiceContext serviceContext = new ServiceContext(requestContext, null, null);
        String checkObjectApiName = objectData.get(AccountCheckRuleConstants.Field.CheckObject.apiName, String.class);
        String reduceRelatedObjectApiName = objectData.get(AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName, String.class);
        boolean checkAndReduceObjectEqual = Objects.equals(checkObjectApiName, reduceRelatedObjectApiName);
        //何时触发校验
        String ruleType = objectData.get(AccountCheckRuleConstants.Field.RuleType.apiName, String.class);
        if (Objects.equals(ruleType, AccountCheckRuleTypeEnum.Check_Reduce.getValue())) {
            String checkTriggerAction = objectData.get(AccountCheckRuleConstants.Field.CheckTriggerAction.apiName, String.class);
            //触发动作是按钮才加按钮action
            if (checkTriggerAction.equals(ReduceTriggerActionEnum.Button.getValue())) {
                String checkTriggerButtonApiName = objectData.get(AccountCheckRuleConstants.Field.CheckTriggerButton.apiName, String.class);
                addForCheck(serviceContext, checkObjectApiName, checkTriggerButtonApiName);
            }
            String editButton = AppFrameworkConfig.isAddEditUIActionGray(serviceContext.getTenantId(), checkObjectApiName) ? "Edit_Save_button_default" : "Edit_button_default";
            addForEditAndInvalid(serviceContext, checkObjectApiName, editButton, false);
            addForEditAndInvalid(serviceContext, checkObjectApiName, "Abolish_button_default", checkAndReduceObjectEqual);
        }

        //何时触发扣减规则
        String reduceTriggerAction = objectData.get(AccountCheckRuleConstants.Field.ReduceTriggerAction.apiName, String.class);
        if (Objects.equals(ReduceTriggerActionEnum.Button.getValue(), reduceTriggerAction)) {
            String buttonApiName = objectData.get(AccountCheckRuleConstants.Field.ReduceTriggerButton.apiName, String.class);
            addForReduce(serviceContext, reduceRelatedObjectApiName, buttonApiName);
        }

        //字段变更/按钮：都要写'编辑、作废'对应的action
        if (!checkAndReduceObjectEqual) {
            String editButton = AppFrameworkConfig.isAddEditUIActionGray(serviceContext.getTenantId(), reduceRelatedObjectApiName) ? "Edit_Save_button_default" : "Edit_button_default";
            addForEditAndInvalid(serviceContext, reduceRelatedObjectApiName, editButton, false);
            addForEditAndInvalid(serviceContext, reduceRelatedObjectApiName, "Abolish_button_default", true);
        }
    }

    private void addForCheck(ServiceContext serviceContext, String describeApiName, String buttonApiName) {
        if (Objects.equals(buttonApiName, "Add_button_default")) {
            buttonApiName = AppFrameworkConfig.isAddEditUIActionGray(serviceContext.getTenantId(), describeApiName) ? "Add_Save_button_default" : "Add_button_default";
        }
        List<ActionPojo> existActions = getActions(serviceContext, describeApiName, buttonApiName);

        List<IUdefAction> needAddActions = new ArrayList<>();
        if (Objects.equals(buttonApiName, "Add_button_default") || Objects.equals(buttonApiName, "Add_Save_button_default")) {
            addActions(existActions, needAddActions, describeApiName, "Rule-PreValidate", "pre", Constants.PRE_VALIDATE_BIZ_KEY);
        } else {
            addActions(existActions, needAddActions, describeApiName, "Rule-FrozenAndDirectReduce", "pre", Constants.PRE_VALIDATE_BIZ_KEY);
            addActions(existActions, needAddActions, describeApiName, "Rule-FrozenAndDirectReduce", "post", Constants.POST_FROZEN_BIZ_KEY);
        }
        add(serviceContext, describeApiName, buttonApiName, needAddActions);
    }

    private void addForReduce(ServiceContext serviceContext, String describeApiName, String buttonApiName) {
        if (Objects.equals(buttonApiName, "Add_button_default")) {
            buttonApiName = AppFrameworkConfig.isAddEditUIActionGray(serviceContext.getTenantId(), describeApiName) ? "Add_Save_button_default" : "Add_button_default";
        } else if (Objects.equals(buttonApiName, "Edit_button_default")) {
            buttonApiName = AppFrameworkConfig.isAddEditUIActionGray(serviceContext.getTenantId(), describeApiName) ? "Edit_Save_button_default" : "Edit_button_default";
        }
        List<ActionPojo> existActions = getActions(serviceContext, describeApiName, buttonApiName);
        List<IUdefAction> needAddActions = Lists.newArrayList();
        addActions(existActions, needAddActions, describeApiName, "Rule-UnfreezeAndDirectReduce", "post", Constants.POST_UNFREEZE_BIZ_KEY);
        add(serviceContext, describeApiName, buttonApiName, needAddActions);
    }

    //编辑（pre、post）作废（post)
    public void addForEditAndInvalid(ServiceContext serviceContext, String describeApiName, String buttonApiName, boolean needAddPreInvalidAction) {
        log.info("addForEditAndInvalid, serviceContext[{}], describeApiName[{}], buttonApiName[{}]", serviceContext, describeApiName, buttonApiName);
        List<ActionPojo> existActions = getActions(serviceContext, describeApiName, buttonApiName);

        List<IUdefAction> needAddActions = new ArrayList<>();
        if (Objects.equals(buttonApiName, "Edit_button_default") || Objects.equals(buttonApiName, "Edit_Save_button_default")) {
            addActions(existActions, needAddActions, describeApiName, "Rule-ProEdit", "pre", Constants.PRE_EDIT_KEY);
            addActions(existActions, needAddActions, describeApiName, "Rule-PostEdit", "post", Constants.POST_EDIT_KEY);
        } else if (Objects.equals(buttonApiName, "Abolish_button_default")) {
            if (needAddPreInvalidAction) {
                addActions(existActions, needAddActions, describeApiName, "Rule-PreInvalid", "pre", Constants.POST_INVALID_KEY);
            }
            addActions(existActions, needAddActions, describeApiName, "Rule-PostInvalid", "post", Constants.POST_INVALID_KEY);
        } else {
            return;
        }

        add(serviceContext, describeApiName, buttonApiName, needAddActions);
    }

    public void addButtonAction(ServiceContext serviceContext, String objectApiName, String buttonApiName, List<BizActionEnum> bizActionEnumList) {
        List<ActionPojo> existActions = getActions(serviceContext, objectApiName, buttonApiName);
        List<IUdefAction> needAddActions = new ArrayList<>();
        bizActionEnumList.forEach(bizActionEnum -> {
            if (!existAction(existActions, objectApiName, bizActionEnum.stage, bizActionEnum.bizKey)) {
                IUdefAction udefAction = getAction(objectApiName, bizActionEnum.actionLabel, bizActionEnum.stage, bizActionEnum.bizKey, bizActionEnum.remark);
                needAddActions.add(udefAction);
            }
        });
        add(serviceContext, objectApiName, buttonApiName, needAddActions);
    }

    private boolean existAction(List<ActionPojo> existActions, String describeApiName, String stage, String bizKey) {
        for (ActionPojo action : existActions) {
            if (Objects.equals(stage, action.getStage())
                    && Objects.equals(describeApiName, action.getDescribe_api_name())
                    && Objects.equals(bizKey, action.getBizKey())
                    && Objects.equals("custom_biz", action.getAction_type())) {
                return true;
            }
        }
        return false;
    }

    private void addActions(List<ActionPojo> existActions, List<IUdefAction> needAddActions, String describeApiName, String label, String stage, String bizKey) {
        //是否存在
        for (ActionPojo action : existActions) {
            if (Objects.equals(stage, action.getStage())
                    && Objects.equals(describeApiName, action.getDescribe_api_name())
                    && Objects.equals(bizKey, action.getBizKey())
                    && Objects.equals("custom_biz", action.getAction_type())) {
                return;
            }
        }

        needAddActions.add(getAction(describeApiName, label, stage, bizKey, null));
    }

    private IUdefAction getAction(String describeApiName, String label, String stage, String bizKey, String remark) {
        IUdefAction action = new UdefAction();

        action.setDescribeApiName(describeApiName);
        action.setActionType("custom_biz");
        action.setLabel(label);
        //  action.setRemark();
        //  action.setActionParamter();
        action.setStage(stage);
        action.set("biz_key", bizKey);
        action.setRemark(remark);

        return action;
    }

    public void update(RequestContext requestContext, IObjectData objectData, IObjectData dbObjectData) {
        ServiceContext serviceContext = new ServiceContext(requestContext, null, null);
        String ruleType = objectData.get(AccountCheckRuleConstants.Field.RuleType.apiName, String.class);
        //1.第一步校验规则变更
        if (Objects.equals(ruleType, AccountCheckRuleTypeEnum.Check_Reduce.getValue())) {
            String newCheckObject = objectData.get(AccountCheckRuleConstants.Field.CheckObject.apiName, String.class);
            String dbCheckObject = dbObjectData.get(AccountCheckRuleConstants.Field.CheckObject.apiName, String.class);
            String newCheckTriggerAction = objectData.get(AccountCheckRuleConstants.Field.CheckTriggerAction.apiName, String.class);
            String dbCheckTriggerAction = dbObjectData.get(AccountCheckRuleConstants.Field.CheckTriggerAction.apiName, String.class);
            String newCheckTriggerButton = objectData.get(AccountCheckRuleConstants.Field.CheckTriggerButton.apiName, String.class);
            String dbCheckTriggerButton = dbObjectData.get(AccountCheckRuleConstants.Field.CheckTriggerButton.apiName, String.class);

            //字段变更->按钮
            if (Objects.equals(dbCheckTriggerAction, ReduceTriggerActionEnum.FieldChange.getValue()) && Objects.equals(newCheckTriggerAction, ReduceTriggerActionEnum.Button.getValue())) {
                String buttonApiName = objectData.get(AccountCheckRuleConstants.Field.CheckTriggerButton.apiName, String.class);
                if (!Strings.isNullOrEmpty(buttonApiName)) {
                    addForCheck(serviceContext, newCheckObject, buttonApiName);
                }
            }
            //按钮->字段变更
            else if (Objects.equals(dbCheckTriggerAction, ReduceTriggerActionEnum.Button.getValue()) && Objects.equals(newCheckTriggerAction, ReduceTriggerActionEnum.FieldChange.getValue())) {
                deleteForCheck(serviceContext, objectData, dbCheckObject, dbCheckTriggerButton);
            }
            //老按钮->新按钮
            else if (!Objects.equals(newCheckTriggerButton, dbCheckTriggerButton) && Objects.equals(dbCheckTriggerAction, ReduceTriggerActionEnum.Button.getValue()) && Objects.equals(newCheckTriggerAction, ReduceTriggerActionEnum.Button.getValue())) {
                if (!Strings.isNullOrEmpty(dbCheckTriggerButton)) {
                    deleteForCheck(serviceContext, objectData, dbCheckObject, dbCheckTriggerButton);
                }
                if (!Strings.isNullOrEmpty(newCheckTriggerButton)) {
                    addForCheck(serviceContext, dbCheckObject, newCheckTriggerButton);
                }
            }
        }

        //2 何时触发扣减规则
        updateForReduce(serviceContext, objectData, dbObjectData);

        //3 第一步和第三步：'编辑、作废'对应的action   因为对象不变，不用做什么
    }

    private void updateForReduce(ServiceContext serviceContext, IObjectData objectData, IObjectData dbObjectData) {
        String newReduceTriggerAction = objectData.get(AccountCheckRuleConstants.Field.ReduceTriggerAction.apiName, String.class);
        String dbReduceTriggerAction = dbObjectData.get(AccountCheckRuleConstants.Field.ReduceTriggerAction.apiName, String.class);

        String newReduceRelatedObject = objectData.get(AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName, String.class);
        String dbReduceRelatedObject = dbObjectData.get(AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName, String.class);

        String newReduceTriggerButton = objectData.get(AccountCheckRuleConstants.Field.ReduceTriggerButton.apiName, String.class);
        String dbReduceTriggerButton = dbObjectData.get(AccountCheckRuleConstants.Field.ReduceTriggerButton.apiName, String.class);

        if (Objects.equals(newReduceRelatedObject, dbReduceRelatedObject)
                && Objects.equals(newReduceTriggerButton, dbReduceTriggerButton)
                && Objects.equals(newReduceTriggerAction, dbReduceTriggerAction)) {
            return;
        }

        //还是字段变更
        if (Objects.equals(newReduceTriggerAction, dbReduceTriggerAction) && Objects.equals(newReduceTriggerAction, ReduceTriggerActionEnum.FieldChange.getValue())) {
            return;
        }

        //还是按钮
        if (Objects.equals(newReduceTriggerAction, dbReduceTriggerAction) && Objects.equals(newReduceTriggerAction, ReduceTriggerActionEnum.Button.getValue())) {
            deleteForReduce(serviceContext, objectData, dbReduceRelatedObject, dbReduceTriggerButton);
            addForReduce(serviceContext, newReduceRelatedObject, newReduceTriggerButton);
        }

        //字段变更=>按钮
        if (Objects.equals(dbReduceTriggerAction, ReduceTriggerActionEnum.FieldChange.getValue()) && Objects.equals(newReduceTriggerAction, ReduceTriggerActionEnum.Button.getValue())) {
            addForReduce(serviceContext, newReduceRelatedObject, newReduceTriggerButton);
        }

        //按钮=>字段变更
        if (Objects.equals(dbReduceTriggerAction, ReduceTriggerActionEnum.Button.getValue()) && Objects.equals(newReduceTriggerAction, ReduceTriggerActionEnum.FieldChange.getValue())) {
            //可能删掉
            deleteForReduce(serviceContext, objectData, dbReduceRelatedObject, dbReduceTriggerButton);
        }
    }

    /**
     * 用不到才删
     */
    private void deleteForCheck(ServiceContext serviceContext, IObjectData objectData, String checkObject, String checkTriggerButton) {
        //是否有其他在使用
        String ignoreDataId = objectData.get("_id", String.class);
        boolean hasDataByCheckObject = accountCheckRuleManager.hasDataByCheckObject(serviceContext.getUser(), ignoreDataId, checkObject, checkTriggerButton);
        if (hasDataByCheckObject) {
            return;
        }

        if (Objects.equals(checkTriggerButton, "Add_button_default")) {
            checkTriggerButton = AppFrameworkConfig.isAddEditUIActionGray(serviceContext.getTenantId(), checkObject) ? "Add_Save_button_default" : "Add_button_default";
        }

        //查action
        List<ActionPojo> existActions = getActions(serviceContext, checkObject, checkTriggerButton);

        //要删掉的
        List<String> needDeleteActionIds = new ArrayList<>();
        if (Objects.equals(checkTriggerButton, "Add_button_default") || Objects.equals(checkTriggerButton, "Add_Save_button_default")) {
            addActionId(existActions, needDeleteActionIds, checkObject, "pre", Constants.PRE_VALIDATE_BIZ_KEY);
        } else {
            addActionId(existActions, needDeleteActionIds, checkObject, "pre", Constants.PRE_VALIDATE_BIZ_KEY);
            addActionId(existActions, needDeleteActionIds, checkObject, "post", Constants.POST_FROZEN_BIZ_KEY);
        }

        //删掉
        delete(serviceContext, checkObject, checkTriggerButton, needDeleteActionIds);
    }


    /**
     * 用不到才删
     */
    private void deleteForReduce(ServiceContext serviceContext, IObjectData objectData, String dbReduceRelatedObject, String dbReduceTriggerButton) {
        //是否有其他在使用
        String ignoreDataId = objectData.get("_id", String.class);
        boolean hasDataByReduceRelatedObject = accountCheckRuleManager.hasDataByReduceRelatedObject(serviceContext.getUser(), ignoreDataId, dbReduceRelatedObject, dbReduceTriggerButton);
        if (hasDataByReduceRelatedObject) {
            return;
        }

        //查action
        List<ActionPojo> existActions = getActions(serviceContext, dbReduceRelatedObject, dbReduceTriggerButton);

        //要删掉的
        List<String> needDeleteActionIds = new ArrayList<>();
        addActionId(existActions, needDeleteActionIds, dbReduceRelatedObject, "post", Constants.POST_UNFREEZE_BIZ_KEY);

        //删掉
        delete(serviceContext, dbReduceRelatedObject, dbReduceTriggerButton, needDeleteActionIds);
    }

    /**
     * 用不到才删
     */
    private void deleteForEditAndInvalid(ServiceContext serviceContext, IObjectData objectData, String objectApiName, String buttonApiName) {
        log.info("deleteForEditAndInvalid serviceContext[{}], objectData[{}], objectApiName[{}], buttonApiName[{}]", serviceContext, objectData, objectApiName, buttonApiName);
        if (!Objects.equals(buttonApiName, "Edit_button_default") && !Objects.equals(buttonApiName, "Edit_Save_button_default") && !Objects.equals(buttonApiName, "Abolish_button_default")) {
            return;
        }

        //是否有其他在使用
        String ignoreDataId = objectData.get("_id", String.class);
        boolean hasDataByCheckObject = accountCheckRuleManager.hasDataByCheckObject(serviceContext.getUser(), ignoreDataId, objectApiName, null);
        if (hasDataByCheckObject) {
            return;
        }
        boolean hasDataByReduceRelatedObject = accountCheckRuleManager.hasDataByReduceRelatedObject(serviceContext.getUser(), ignoreDataId, objectApiName, null);
        if (hasDataByReduceRelatedObject) {
            return;
        }

        //查action
        List<ActionPojo> existActions = getActions(serviceContext, objectApiName, buttonApiName);

        //要删掉的
        List<String> needDeleteActionIds = new ArrayList<>();
        if (Objects.equals(buttonApiName, "Edit_button_default") || Objects.equals(buttonApiName, "Edit_Save_button_default")) {
            addActionId(existActions, needDeleteActionIds, objectApiName, "pre", Constants.PRE_EDIT_KEY);
            addActionId(existActions, needDeleteActionIds, objectApiName, "post", Constants.POST_EDIT_KEY);
        } else if (Objects.equals(buttonApiName, "Abolish_button_default")) {
            addActionId(existActions, needDeleteActionIds, objectApiName, "post", Constants.POST_INVALID_KEY);
        }

        //删掉
        delete(serviceContext, objectApiName, buttonApiName, needDeleteActionIds);
    }

    private void addActionId(List<ActionPojo> existActions, List<String> needDeleteActionIds, String describeApiName, String stage, String bizKey) {
        for (ActionPojo action : existActions) {
            if (Objects.equals(stage, action.getStage())
                    && Objects.equals(describeApiName, action.getDescribe_api_name())
                    && Objects.equals(bizKey, action.getBizKey())
                    && Objects.equals("custom_biz", action.getAction_type())) {

                needDeleteActionIds.add(action.getId());
                return;
            }
        }
    }

    public void delete(RequestContext requestContext, IObjectData objectData) {
        log.info("CaRuleEngineManager.delete requestContext[{}], objectData[{}]", requestContext, objectData);
        ServiceContext serviceContext = new ServiceContext(requestContext, null, null);
        String checkObject = objectData.get(AccountCheckRuleConstants.Field.CheckObject.apiName, String.class);

        //1 何时触发校验
        String ruleType = objectData.get(AccountCheckRuleConstants.Field.RuleType.apiName, String.class);
        if (Objects.equals(ruleType, AccountCheckRuleTypeEnum.Check_Reduce.getValue())) {
            //是否改变了
            String checkTriggerAction = objectData.get(AccountCheckRuleConstants.Field.CheckTriggerAction.apiName, String.class);
            //如果没有其他用到，删掉
            if (Objects.equals(checkTriggerAction, ReduceTriggerActionEnum.Button.getValue())) {
                String buttonApiName = objectData.get(AccountCheckRuleConstants.Field.CheckTriggerButton.apiName, String.class);
                deleteForCheck(serviceContext, objectData, checkObject, buttonApiName);
            }
            //'编辑、作废'对应的action
            //before里面已经校验，所有冻结记录都已经解冻，这里可以删
            String editButton = AppFrameworkConfig.isAddEditUIActionGray(serviceContext.getTenantId(), checkObject) ? "Edit_Save_button_default" : "Edit_button_default";
            deleteForEditAndInvalid(serviceContext, objectData, checkObject, editButton);
            deleteForEditAndInvalid(serviceContext, objectData, checkObject, "Abolish_button_default");
        }

        //2 何时触发扣减规则
        String reduceTriggerAction = objectData.get(AccountCheckRuleConstants.Field.ReduceTriggerAction.apiName, String.class);
        String reduceRelatedObject = objectData.get(AccountCheckRuleConstants.Field.ReduceRelatedObject.apiName, String.class);
        String reduceTriggerButton = objectData.get(AccountCheckRuleConstants.Field.ReduceTriggerButton.apiName, String.class);
        if (Objects.equals(reduceTriggerAction, ReduceTriggerActionEnum.Button.getValue())) {
            //before里面已经校验，所有冻结记录都已经解冻，这里可以删
            deleteForReduce(serviceContext, objectData, reduceRelatedObject, reduceTriggerButton);
        }

        //'编辑、作废'对应的action
        if (!Objects.equals(checkObject, reduceRelatedObject)) {
            String editButton = AppFrameworkConfig.isAddEditUIActionGray(serviceContext.getTenantId(), reduceRelatedObject) ? "Edit_Save_button_default" : "Edit_button_default";
            //before里面已经校验，所有冻结记录都已经解冻，这里可以删
            deleteForEditAndInvalid(serviceContext, objectData, reduceRelatedObject, editButton);
            deleteForEditAndInvalid(serviceContext, objectData, reduceRelatedObject, "Abolish_button_default");
        }
    }

    /**
     * 当校验规则灰度 domainPlugin ，历史校验规则数据迁移时，需要删除绑定在触发按钮上的 action
     */
    public void deleteCheckRuleActions(String tenantId, String describeApiName, Set<String> buttonApiNames) {
        ServiceContext context = ServiceContextUtil.getInnerServiceContext(tenantId, "-10000");
        buttonApiNames.forEach(buttonApiName -> {
            List<ActionPojo> actions = getActions(context, describeApiName, buttonApiName);
            List<String> needDelActionIds = actions.stream()
                .filter(action -> checkRuleBizKeys.contains(action.getBizKey()))
                .map(ActionPojo::getId)
                .collect(Collectors.toList());
            delete(context, describeApiName, buttonApiName, needDelActionIds);
        });
    }

    //加入mt_udef_action、mt_udef_button
    private void add(ServiceContext context, String describeApiName, String buttonApiName, List<IUdefAction> needAddActions) {
        if (CollectionUtils.isEmpty(needAddActions)) {
            return;
        }
        List<ActionPojo> actionPojos = needAddActions.stream().map(ActionPojo::fromUDefAction).collect(Collectors.toList());
        UpdateButtonPostAction.Arg arg = UpdateButtonPostAction.Arg.builder()
                .describeApiName(describeApiName).buttonApiName(buttonApiName).udefAction(actionPojos).build();
        try {
            UpdateButtonPostAction.Result result = buttonService.updateButtonPostAction(arg, context);
            log.info("updateButtonPostAction, context[{}], arg[{}], result[{}]", context, arg, result);
        } catch (Exception e) {
            log.warn("updateButtonPostAction, context[{}], arg[{}]", context, arg, e);
            throw new ValidateException(e.getMessage());
        }
    }

    private void delete(ServiceContext context, String describeApiName, String buttonApiName, List<String> actionIds) {
        if (CollectionUtils.isEmpty(actionIds)) {
            return;
        }
        DeleteButtonPostAction.Arg arg = new DeleteButtonPostAction.Arg();
        arg.setDescribeApiName(describeApiName);
        arg.setButtonApiName(buttonApiName);
        arg.setActionIds(actionIds);
        try {
            DeleteButtonPostAction.Result result = buttonService.deleteButtonPostAction(arg, context);
            log.info("deleteButtonPostAction, context[{}], arg[{}], result[{}]", context, arg, result);
        } catch (Exception e) {
            log.warn("deleteButtonPostAction, context[{}], arg[{}]", context, arg, e);
            throw new ValidateException(e.getMessage());
        }
    }

    private List<ActionPojo> getActions(ServiceContext serviceContext, String describeApiName, String buttonApiName) {
        FindButtonInfo.Arg arg = new FindButtonInfo.Arg();
        arg.setDescribeApiName(describeApiName);
        arg.setButtonApiName(buttonApiName);
        try {
            FindButtonInfo.Result buttonInfo = buttonService.findButtonInfo(arg, serviceContext);
            log.info("findButtonInfo, context[{}], arg[{}], buttonInfo[{}]", serviceContext, arg, buttonInfo);
            return buttonInfo.getPost_actions();
        } catch (Exception e) {
            log.info("findButtonInfo, context[{}], arg[{}]", serviceContext, arg, e);
            throw new ValidateException(e.getMessage());
        }
    }

    public void createButton(ServiceContext serviceContext, String objectApiName, List<String> buttonApiNames) {
        if (CollectionUtils.isEmpty(buttonApiNames)) {
            return;
        }

        Map<String, CreateButton.Arg> buttonArgMap = Maps.newHashMap();
        buttonApiNames.forEach(buttonApiName -> {
            String buttonJson = BizJsonUtil.getJson(String.format("button/%s_%s_button.json", objectApiName, buttonApiName));
            CreateButton.Arg buttonArg = JsonUtil.fromJson(buttonJson, CreateButton.Arg.class);
            buttonArgMap.put(buttonApiName, buttonArg);
        });

        FindButtonList.Arg findButtonArg = new FindButtonList.Arg();
        findButtonArg.setExcludeUIButton(true);
        findButtonArg.setDescribeApiName(objectApiName);
        FindButtonList.Result findButtonResult = buttonService.findButtonList(findButtonArg, serviceContext);
        List<ButtonDocument> buttonDocuments = findButtonResult.getButtonList();
        Map<String, ButtonDocument> buttonDocumentMap = Objects.isNull(buttonDocuments) ? Maps.newHashMap() : buttonDocuments.stream().collect(Collectors.toMap(x -> String.valueOf(x.get("api_name")), Function.identity()));

        buttonArgMap.forEach((k, buttonArg) -> {
            if (!buttonDocumentMap.containsKey(k)) {
                try {
                    CreateButton.Result result = buttonService.create(buttonArg, serviceContext);

                    if (!result.isSuccess()) {
                        log.warn("buttonService.create arg[{}], serviceContext[{}], result[{}]", buttonArg, serviceContext, result);
                        throw new ValidateException(I18N.text(CAI18NKey.CREATE_BUTTON_FAIL));
                    } else {
                        log.info("buttonService.create arg[{}], serviceContext[{}], result[{}]", buttonArg, serviceContext, result);
                    }
                } catch (Exception e) {
                    log.warn("buttonService.create arg[{}], serviceContext[{}]", buttonArg, serviceContext, e);
                    throw new ValidateException(k + e.getMessage());
                }
            }
        });
    }

    public void deleteButton(ServiceContext serviceContext, String objectApiName, String buttonApiName) {
        FindButtonList.Arg findButtonArg = new FindButtonList.Arg();
        findButtonArg.setDescribeApiName(objectApiName);
        FindButtonList.Result findButtonResult = buttonService.findButtonList(findButtonArg, serviceContext);
        List<ButtonDocument> buttonDocuments = findButtonResult.getButtonList();
        if (CollectionUtils.isEmpty(buttonDocuments)) {
            return;
        }
        Map<String, ButtonDocument> buttonDocumentMap = Objects.isNull(buttonDocuments) ? Maps.newHashMap() : buttonDocuments.stream().collect(Collectors.toMap(x -> String.valueOf(x.get("api_name")), Function.identity()));
        if (!buttonDocumentMap.containsKey(buttonApiName)) {
            return;
        }

        DeleteButton.Arg arg = new DeleteButton.Arg();
        arg.setDescribeApiName(objectApiName);
        arg.setButtonApiName(buttonApiName);

        try {
            DeleteButton.Result result = buttonService.delete(arg, serviceContext);
            if (!result.isSuccess()) {
                log.warn("buttonService.delete arg[{}], serviceContext[{}], result[{}]", arg, serviceContext, result);
                throw new ValidateException(I18N.text(CAI18NKey.DELETE_BUTTON_FAIL));
            } else {
                log.info("buttonService.delete arg[{}], serviceContext[{}], result[{}]", arg, serviceContext, result);
            }
        } catch (Exception e) {
            log.warn("buttonService.create arg[{}], serviceContext[{}]", arg, serviceContext, e);
            throw new ValidateException(e.getMessage());
        }
    }

    /**
     * 按钮位置，增加'列表页单条操作'
     */
    public void usePagesAddList(ServiceContext serviceContext, String describeApiName, String buttonApiName) {
        FindButtonInfo.Arg arg = new FindButtonInfo.Arg();
        arg.setDescribeApiName(describeApiName);
        arg.setButtonApiName(buttonApiName);

        FindButtonInfo.Result buttonResult = null;
        try {
            buttonResult = buttonService.findButtonInfo(arg, serviceContext);
        } catch (Exception e) {
            log.warn("buttonService.findButtonInfo, arg[{}], context[{}]", arg, serviceContext, e);
            throw e;
        }
        ButtonDocument buttonDoc = buttonResult.getButton();
        List<String> usePages = (List<String>) buttonDoc.get("use_pages");
        if (usePages.contains("list")) {
            return;
        }

        usePages.add("list");
        buttonDoc.put("use_pages", usePages);

        UpdateButton.Arg updateArg = new UpdateButton.Arg();
        updateArg.setButton(JSONObject.toJSONString(buttonDoc));
        updateArg.setPost_actions(buttonResult.getPost_actions());
        updateArg.setRoles(buttonResult.getRoles());

        try {
            UpdateButton.Result updateResult = buttonService.update(updateArg, serviceContext);
        } catch (Exception e) {
            log.warn("buttonService.update, arg[{}], context[{}]", updateArg, serviceContext, e);
            throw e;
        }
    }
}