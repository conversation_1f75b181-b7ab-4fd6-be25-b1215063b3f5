package com.facishare.crm.customeraccount.tools;

import com.facishare.crm.customeraccount.constants.FundAccountConstants;
import com.facishare.crm.customeraccount.enums.AccessModuleEnum;
import com.facishare.crm.customeraccount.enums.FundAccountAccountTypeEnum;
import com.facishare.crm.customeraccount.enums.FundAccountTypeEnum;
import com.facishare.crm.customeraccount.predefine.manager.FundAccountConfigManager;
import com.facishare.crm.customeraccount.tools.model.GenerateDataLangSqlModel;
import com.facishare.crm.customeraccount.tools.model.LangObjectFieldModel;
import com.facishare.crm.customeraccount.tools.model.ObjectDataLangModel;
import com.facishare.crmcommon.util.SearchUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.Text;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * @IgnoreI18n
 */
@Slf4j
@Component
@ServiceModule("object_lang_curl")
public class ObjectDataLangSqlService {
    public static final String ID_COL = "id";
    public static final String TENANT_ID_COL = "tenant_id";
    public static final String DESCRIBE_API_NAME_COL = "describe_api_name";
    public static final String LAST_MODIFIED_TIME_COL = "last_modified_time";
    public static final String LANG_COL = "lang";
    public static final String IS_DELETED_COL = "is_deleted";
    public static final String SYS_MODIFIED_TIME_COL = "sys_modified_time";
    public static final String DATA_ID_COL = "data_id";

    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private FundAccountConfigManager fundAccountConfigManager;

    @ServiceMethod("enable_field_lang")
    public GenerateDataLangSqlModel.EnableLangResult enableObjectFieldLang(ServiceContext serviceContext, GenerateDataLangSqlModel.Arg arg) {
        GenerateDataLangSqlModel.EnableLangResult result = new GenerateDataLangSqlModel.EnableLangResult();
        List<LangObjectFieldModel> langObjectFieldModels = CollectionUtils.nullToEmpty(arg.getObjectModelList());
        String tenantId = serviceContext.getTenantId();
        langObjectFieldModels.forEach(objectFieldModel -> {
            String objectApiName = objectFieldModel.getObjectApiName();
            IObjectDescribe objectDescribe = serviceFacade.findObject(tenantId, objectApiName);
            Set<String> fieldApiNames = CollectionUtils.nullToEmpty(objectFieldModel.getFieldApiNames());
            List<IFieldDescribe> updateFieldList = Lists.newArrayList();
            for (String fieldApiName : fieldApiNames) {
                IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(fieldApiName);
                if (Objects.nonNull(fieldDescribe) && !BooleanUtils.isTrue(fieldDescribe.getEnableMultiLang())) {
                    fieldDescribe.setEnableMultiLang(true);
                    updateFieldList.add(fieldDescribe);
                }
            }
            if (CollectionUtils.notEmpty(updateFieldList)) {
                serviceFacade.updateFieldDescribe(objectDescribe, updateFieldList);
            }
        });
        presetDataLang(serviceContext.getRequestContext());
        return result;
    }

    @ServiceMethod("disable_field_lang")
    public GenerateDataLangSqlModel.DisableLangResult disableObjectFieldLang(ServiceContext serviceContext, GenerateDataLangSqlModel.Arg arg) {
        GenerateDataLangSqlModel.DisableLangResult result = new GenerateDataLangSqlModel.DisableLangResult();
        List<LangObjectFieldModel> langObjectFieldModels = CollectionUtils.nullToEmpty(arg.getObjectModelList());
        String tenantId = serviceContext.getTenantId();
        langObjectFieldModels.forEach(objectFieldModel -> {
            String objectApiName = objectFieldModel.getObjectApiName();
            IObjectDescribe objectDescribe = serviceFacade.findObject(tenantId, objectApiName);
            Set<String> fieldApiNames = CollectionUtils.nullToEmpty(objectFieldModel.getFieldApiNames());
            if (CollectionUtils.empty(fieldApiNames)) {
                objectDescribe.getFieldDescribes().forEach(x -> {
                    if (BooleanUtils.isTrue(x.getEnableMultiLang())) {
                        fieldApiNames.add(x.getApiName());
                    }
                });
            }
            List<IFieldDescribe> updateFieldList = Lists.newArrayList();
            for (String fieldApiName : fieldApiNames) {
                IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(fieldApiName);
                if (Objects.nonNull(fieldDescribe) && BooleanUtils.isTrue(fieldDescribe.getEnableMultiLang())) {
                    fieldDescribe.setEnableMultiLang(false);
                    updateFieldList.add(fieldDescribe);
                }
            }
            if (CollectionUtils.notEmpty(updateFieldList)) {
                serviceFacade.updateFieldDescribe(objectDescribe, updateFieldList);
            }
        });
        return result;
    }

    @ServiceMethod("update_data_field_lang")
    public ObjectDataLangModel.Result updateDataFieldLang(ServiceContext serviceContext, ObjectDataLangModel.Arg arg) {
        ObjectDataLangModel.Result result = new ObjectDataLangModel.Result();
        List<ObjectDataLangModel.FieldLang> fieldLangList = arg.getFieldLangList();
        if (CollectionUtils.empty(fieldLangList)) {
            return result;
        }
        User user = serviceContext.getUser();
        String tenantId = serviceContext.getTenantId();
        String objectApiName = arg.getObjectApiName();
        Set<String> dataIds = arg.getObjectDataIds();
        List<IObjectData> dataList = serviceFacade.findObjectDataByIds(tenantId, Lists.newArrayList(dataIds), objectApiName);

        for (IObjectData objectData : dataList) {
            boolean needUpdate = false;
            for (ObjectDataLangModel.FieldLang fieldLang : fieldLangList) {
                String fieldName = fieldLang.getFieldName();
                Map<String, String> langDataMap = fieldLang.getLangDataMap();
                if (Objects.isNull(langDataMap)) {
                    break;
                }
                Map<String, String> fieldLangDataMap = getFieldLangDataMap(objectData, fieldName);
                needUpdate = langDataMap.entrySet().stream().map(entrySet ->
                        putIfEmpty(fieldLangDataMap, Lang.of(entrySet.getKey()), entrySet.getValue())
                ).anyMatch(BooleanUtils::isTrue);

                objectData.set(getFieldLangKey(fieldName), fieldLangDataMap);
            }
            if (needUpdate) {
                serviceFacade.updateObjectData(user, objectData);
            }
        }
        return result;
    }

    private void presetDataLang(RequestContext requestContext) {
        User user = requestContext.getUser();
        boolean isCustomerAccountEnable = fundAccountConfigManager.isFundAccountEnable(requestContext.getTenantId());
        if (isCustomerAccountEnable) {
            SearchTemplateQuery fundQuery = new SearchTemplateQuery();
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, FundAccountConstants.Field.Type.apiName, FundAccountTypeEnum.Preset.getValue());
            SearchUtil.fillFilterEq(filters, FundAccountConstants.Field.AccountType.apiName, FundAccountAccountTypeEnum.Amount.value);
            SearchUtil.fillFilterIn(filters, FundAccountConstants.Field.AccessModule.apiName, Lists.newArrayList(AccessModuleEnum.DEFAULT.value, AccessModuleEnum.CREDIT.value));
            fundQuery.setFilters(filters);
            fundQuery.setOffset(0);
            fundQuery.setLimit(10);

            List<IObjectData> fundAccountList = serviceFacade.findBySearchQuery(user, FundAccountConstants.API_NAME, fundQuery).getData();
            for (IObjectData fundAccountData : fundAccountList) {
                String accessModule = fundAccountData.get(FundAccountConstants.Field.AccessModule.apiName, String.class);
                Map<String, String> nameLangMap = getFieldLangDataMap(fundAccountData, FundAccountConstants.Field.Name.apiName);
                boolean needUpdate = false;
                if (AccessModuleEnum.DEFAULT.value.equals(accessModule)) {
                    needUpdate = putIfEmpty(nameLangMap, Lang.zh_CN, "现金账户")
                            || putIfEmpty(nameLangMap, Lang.zh_TW, "現金帳戶")
                            || putIfEmpty(nameLangMap, Lang.en, "Cash Account");
                } else if (AccessModuleEnum.CREDIT.value.equals(accessModule)) {
                    needUpdate = putIfEmpty(nameLangMap, Lang.zh_CN, "信用账户")
                            || putIfEmpty(nameLangMap, Lang.zh_TW, "信用帳戶")
                            || putIfEmpty(nameLangMap, Lang.en, "Credit Account");
                }
                if (needUpdate) {
                    fundAccountData.set("name__lang", nameLangMap);
                    serviceFacade.updateObjectData(user, fundAccountData);
                }
            }
        }
    }

    private String getFieldLangKey(String fieldName) {
        return fieldName + "__lang";
    }

    private Map<String, String> getFieldLangDataMap(IObjectData objectData, String fieldName) {
        return (Map<String, String>) objectData.get(getFieldLangKey(fieldName), Map.class, Maps.newHashMap());
    }

    private boolean putIfEmpty(Map<String, String> langDataMap, Lang lang, String value) {
        String langData = langDataMap.get(lang.getValue());
        if (StringUtils.isEmpty(langData)) {
            langDataMap.put(lang.getValue(), value);
            return true;
        }
        return false;
    }

    @ServiceMethod("generate_sql")
    public GenerateDataLangSqlModel.Result generateObjectDataLangSql(ServiceContext serviceContext, GenerateDataLangSqlModel.Arg arg) {
        GenerateDataLangSqlModel.Result result = new GenerateDataLangSqlModel.Result();
        List<String> pgSqlList = Lists.newArrayList();
        List<String> chSqlList = Lists.newArrayList();

        String tenantId = serviceContext.getTenantId();
        arg.getObjectModelList().forEach(objectModel -> {
            String objectApiName = objectModel.getObjectApiName();
            Set<String> fieldApiNames = CollectionUtils.nullToEmpty(objectModel.getFieldApiNames());
            IObjectDescribe objectDescribe = serviceFacade.findObject(tenantId, objectApiName);
            pgSqlList.add(getObjectDataLangSql(objectDescribe, fieldApiNames));
            chSqlList.add(getObjectDataLangChSql(objectDescribe, fieldApiNames));
        });
        StringBuilder pgSb = new StringBuilder();
        pgSqlList.forEach(sql -> pgSb.append("--***************************************************--").append("\n").append(sql).append("\n\n"));
        StringBuilder chSb = new StringBuilder();
        chSqlList.forEach(sql -> chSb.append("\n").append(sql).append("\n\n"));
        log.info("pgSqlResult:\n{}", pgSb);
        log.info("chSqlResult:\n{}", chSb);
        result.setObjectPgSqlList(pgSqlList);
        result.setObjectChSqlList(chSqlList);
        return result;
    }

    public String getObjectDataLangSql(IObjectDescribe objectDescribe, Set<String> fieldApiNames) {
        StringBuilder stringBuilder = new StringBuilder();
        String dataLangTableName = getDataLangTableName(objectDescribe);
        stringBuilder.append("CREATE TABLE IF NOT EXISTS ").append(dataLangTableName).append("(").append("\n").append(getWithQuota(ID_COL)).append(" ").append("VARCHAR(64) NOT NULL,").append("\n").append(getWithQuota(TENANT_ID_COL)).append(" ").append("VARCHAR(16) NOT NULL,").append("\n").append(getWithQuota(DESCRIBE_API_NAME_COL)).append(" ").append("VARCHAR(200),").append("\n").append(getWithQuota(DATA_ID_COL)).append(" ").append("VARCHAR(64),").append("\n").append(getWithQuota(LAST_MODIFIED_TIME_COL)).append(" ").append("int8,").append("\n").append(getWithQuota(LANG_COL)).append(" ").append("VARCHAR(32),").append("\n").append(getWithQuota(IS_DELETED_COL)).append(" ").append("boolean,").append("\n").append(getWithQuota(SYS_MODIFIED_TIME_COL)).append(" ").append("int8,").append("\n");

        fieldApiNames.forEach(fieldApiName -> {
            IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(fieldApiName);
            if (fieldDescribe != null) {
                int length = fieldDescribe instanceof Text ? ((Text) fieldDescribe).getMaxLength() : 100;
                stringBuilder.append(getWithQuota(getDataLangFieldName(fieldApiName))).append(" ").append("VARCHAR(").append(length).append("),").append("\n");
            }
        });
        stringBuilder.append("CONSTRAINT").append(getWithQuota(dataLangTableName + "_pkey")).append(" PRIMARY KEY(");

        //primary key
        List<String> primaryKeyColumns = Lists.newArrayList(ID_COL, TENANT_ID_COL);
        for (int i = 0; i < primaryKeyColumns.size(); i++) {
            String column = primaryKeyColumns.get(i);
            stringBuilder.append(getWithQuota(column));
            if (i < primaryKeyColumns.size() - 1) {
                stringBuilder.append(",");
            }
        }
        stringBuilder.append("));\n\n");

        //index
        stringBuilder.append("CREATE INDEX CONCURRENTLY IF NOT EXISTS ").append(getWithQuota(String.format("i_%s_sys_md_stamp_ei_del_api_idx", dataLangTableName))).append(" ON ").append(getWithQuota(dataLangTableName)).append(" USING btree(").append(SYS_MODIFIED_TIME_COL).append(" DESC,").append(TENANT_ID_COL).append(",").append(IS_DELETED_COL).append(",").append(DESCRIBE_API_NAME_COL).append(");\n\n");

        stringBuilder.append("CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS ").append(getWithQuota(String.format("uk_%s_ei_describe_id_lang", dataLangTableName))).append(" ON ").append(getWithQuota(dataLangTableName)).append(" USING btree(").append(TENANT_ID_COL).append(",").append(DESCRIBE_API_NAME_COL).append(",").append(DATA_ID_COL).append(",").append(LANG_COL).append(") where is_deleted = false;\n\n");

        stringBuilder.append("CREATE INDEX CONCURRENTLY IF NOT EXISTS ").append(getWithQuota(String.format("i_%s_lang_id_ei_describe_idx", dataLangTableName))).append(" ON ").append(getWithQuota(dataLangTableName)).append(" USING btree(").append(DATA_ID_COL).append(",").append(TENANT_ID_COL).append(",").append(DESCRIBE_API_NAME_COL).append(");\n\n");

        //trigger
        stringBuilder.append("DROP TRIGGER IF EXISTS x_audit_changes ON ").append(dataLangTableName).append(";\n\n");
        stringBuilder.append("CREATE TRIGGER x_audit_changes AFTER INSERT OR DELETE OR UPDATE ON ").append(dataLangTableName).append(" FOR EACH ROW EXECUTE PROCEDURE f_change_detail('id','tenant_id','describe_api_name','data_id');").append("\n\n");

        stringBuilder.append("DROP TRIGGER IF EXISTS x_system_changes ON ").append(dataLangTableName).append(";\n\n");
        stringBuilder.append("CREATE TRIGGER x_system_changes BEFORE INSERT OR UPDATE ON ").append(dataLangTableName).append(" FOR EACH ROW EXECUTE PROCEDURE public.f_system_change();").append("\n\n");

        log.info("objectDataLang:\n{}", stringBuilder);
        return stringBuilder.toString();
    }

    public String getObjectDataLangChSql(IObjectDescribe objectDescribe, Set<String> fieldApiNames) {
        StringBuilder stringBuilder = new StringBuilder();
        String tableName = getDataLangTableName(objectDescribe);
        stringBuilder.append("CREATE TABLE IF NOT EXISTS ").append(tableName).append(" ON CLUSTER '{cluster}'(").append("\n").append(getWithBackQuote(ID_COL)).append(" String,").append("\n").append(getWithBackQuote(TENANT_ID_COL)).append(" String,").append("\n").append(getWithBackQuote(DESCRIBE_API_NAME_COL)).append(" String,").append("\n").append(getWithBackQuote(DATA_ID_COL)).append(" String,").append("\n").append(getWithBackQuote(LAST_MODIFIED_TIME_COL)).append(" Int64 NOT NULL,").append("\n").append(getWithBackQuote(LANG_COL)).append(" String,").append("\n").append(getWithBackQuote(IS_DELETED_COL)).append(" Int16,").append("\n").append(getWithBackQuote(SYS_MODIFIED_TIME_COL)).append(" Int64 NOT NULL DEFAULT last_modified_time * 1000,").append("\n");


        fieldApiNames.forEach(fieldApiName -> {
            IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(fieldApiName);
            if (Objects.nonNull(fieldDescribe)) {
                stringBuilder.append(getWithBackQuote(getDataLangFieldName(fieldApiName))).append(" String,").append("\n");
            }
        });

        stringBuilder.append(getWithBackQuote("bi_sys_flag")).append(" Int8,").append("\n").append(getWithBackQuote("bi_sys_batch_id")).append(" Int64,").append("\n").append(getWithBackQuote("bi_sys_is_deleted")).append(" UInt8,").append("\n").append(getWithBackQuote("bi_sys_version")).append(" DateTime DEFAULT now(),").append("\n").append(getWithBackQuote("bi_sys_ods_part")).append(" String DEFAULT 's')").append("\n").append("ENGINE = ReplicatedReplacingMergeTree('/clickhouse/tables/{shard}/{database}/{uuid}/', '{replica}', bi_sys_version, bi_sys_is_deleted)").append("\n").append("PARTITION BY bi_sys_ods_part").append("\n").append("ORDER BY ( tenant_id,bi_sys_flag,id)").append("\n").append("TTL bi_sys_version + INTERVAL 1 MONTH DELETE WHERE bi_sys_ods_part = 's' AND (bi_sys_flag = 0 OR is_deleted IN (-1, -2)),").append("\n").append("bi_sys_version + INTERVAL 1 WEEK DELETE WHERE bi_sys_ods_part = 'i';");
        return stringBuilder.toString();

    }

    private static String getWithBackQuote(String name) {
        if (!name.startsWith("`")) {
            return "`" + name + "`";
        }
        return name;
    }

    private static String getWithQuota(String name) {
        if (!name.startsWith("\"")) {
            name = "\"" + name + "\"";
        }
        return name;
    }

    private String getDataLangTableName(IObjectDescribe objectDescribe) {
        String storeTableName = objectDescribe.getStoreTableName();
        return storeTableName + "_lang";
    }

    private String getDataLangFieldName(String apiName) {
        return String.format("%s_l", apiName);
    }
}
