package com.facishare.crm.customeraccount.predefine.action;

import com.facishare.crm.customeraccount.constants.NewCustomerAccountConstants;
import com.facishare.crmcommon.util.DataUtil;
import com.facishare.paas.appframework.core.predef.action.StandardIncrementUpdateAction;
import com.google.common.collect.Lists;

public class NewCustomerAccountIncrementUpdateAction extends StandardIncrementUpdateAction {
    @Override
    protected void before(Arg arg) {
        super.before(arg);
        DataUtil.fieldEditCheck(this.objectDescribe, this.dbObjectData, this.objectData, Lists.newArrayList(NewCustomerAccountConstants.Field.Customer.apiName,
                NewCustomerAccountConstants.Field.AccountBalance.apiName, NewCustomerAccountConstants.Field.FundAccount.apiName,
                NewCustomerAccountConstants.Field.AvailableBalance.apiName, NewCustomerAccountConstants.Field.OccupiedAmount.apiName,
                NewCustomerAccountConstants.Field.Name.apiName));
    }
}
