package com.facishare.crm.customeraccount.constants;

import com.facishare.crm.customeraccount.enums.FAccountAuthAuthorizedTypeEnum;

/**
 * @IgnoreI18nFile
 */
public interface FAccountAuthorizationConstants {
    String API_NAME = "FAccountAuthorizationObj";

    String DISPLAY_NAME = "账户授权";
    String DEFAULT_LAYOUT_API_NAME = "FAccountAuthorizationObj_default_layout__c";
    String DEFAULT_LAYOUT_DISPLAY_NAME = "默认布局";
    String LIST_LAYOUT_API_NAME = "FAccountAuthorizationObj_list_layout__c";
    String LIST_LAYOUT_DISPLAY_NAME = "移动端默认列表页";
    String STORE_TABLE_NAME = "f_account_authorization";

    enum Field {
        Name("name", "规则名称"),
        /**
         * @see FAccountAuthAuthorizedTypeEnum
         */
        AuthorizedType("authorized_type", "授权类型"),

        /**
         * what字段
         */
        AuthorizedObject("authorized_object", "授权对象"),
        AuthorizedObjectApiName("authorized_object_api_name", "授权对象ApiName"),
     // AuthorizedObjectDataId("authorized_object_data_id", "授权对象数据Id");  //没用到

        EntryCustomerFieldApiName("entry_customer_fieldapiname", "客户字段apiname"),
        EntryAmountFieldApiName("entry_amount_fieldapiname", "入账金额字段apiname"),

        AutoEntryStatus("autoentry_status", "自动入账"),

        IsUnfreezeAuth("is_unfreeze_auth", "解冻授权"),
        FrozenActions("frozen_actions", "冻结动作"),
        ReduceTriggerActions("reduce_trigger_actions", "扣减触发动作"),

        Status("status", "初始化状态"),
        Remark("remark", "备注"),
        ;

        public final String apiName;
        public final String label;

        Field(String apiName, String label) {
            this.apiName = apiName;
            this.label = label;
        }
    }
}
