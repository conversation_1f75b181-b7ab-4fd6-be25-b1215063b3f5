package com.facishare.crm.customeraccount.predefine.action;

import com.facishare.crm.config.CrmBizConfigManager;
import com.facishare.crm.customeraccount.constants.AccountTransactionFlowConst;
import com.facishare.crm.customeraccount.constants.CAI18NKey;
import com.facishare.crm.customeraccount.constants.NewCustomerAccountConstants;
import com.facishare.crm.customeraccount.enums.ChargeOffTypeEnum;
import com.facishare.crm.customeraccount.enums.ConfigKeyEnum;
import com.facishare.crm.customeraccount.enums.EntryStatusEnum;
import com.facishare.crm.customeraccount.enums.RevenueTypeEnum;
import com.facishare.crm.customeraccount.model.DataUpdateAndAddModel;
import com.facishare.crm.customeraccount.predefine.handler.RuleHandlerUtil;
import com.facishare.crm.customeraccount.predefine.manager.AccountCheckManager;
import com.facishare.crm.customeraccount.predefine.manager.NewCustomerAccountManager;
import com.facishare.crm.customeraccount.predefine.service.dto.ChargeOffModel;
import com.facishare.crm.customeraccount.util.CustomerAccountLogUtil;
import com.facishare.crmcommon.util.ObjectDataUtil;
import com.facishare.crmcommon.util.SearchUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.FundAccountBaseService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.MapUtils;
import org.redisson.api.RLock;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

public class AccountTransactionFlowBulkChargeOffAction extends AbstractStandardAction<ChargeOffModel.BulkFlowArg, ChargeOffModel.Result> {
    private final AccountCheckManager accountCheckManager = SpringUtil.getContext().getBean(AccountCheckManager.class);
    private final NewCustomerAccountManager newCustomerAccountManager = SpringUtil.getContext().getBean(NewCustomerAccountManager.class);
    private final CrmBizConfigManager crmBizConfigManager = SpringUtil.getContext().getBean(CrmBizConfigManager.class);

    private DataUpdateAndAddModel.Arg dataUpdateAndAddArg;
    private RLock lock;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    @Override
    protected List<String> getDataPrivilegeIds(ChargeOffModel.BulkFlowArg bulkFlowArg) {
        return Lists.newArrayList();
    }

    @Override
    protected void before(ChargeOffModel.BulkFlowArg arg) {
        super.before(arg);
        String objectApiName = arg.getObjectApiName();
        Map<String, BigDecimal> dataChargeOffAmountMap = arg.getDataChargeOffAmountMap();
        String chargeOffType = arg.getChargeOffType();
        boolean checkRuleEnable = crmBizConfigManager.isConfigEqual(actionContext.getUser(), ConfigKeyEnum.ACCOUNT_CHECK_RULE.key, ConfigKeyEnum.ACCOUNT_CHECK_RULE.enabledValue);
        if (ChargeOffTypeEnum.INCOME_FLOW.type.equals(chargeOffType)) {
            lock = lock(objectApiName, dataChargeOffAmountMap.keySet());
            incomeChargeOff(objectApiName, dataChargeOffAmountMap, checkRuleEnable);
        }
    }

    @Override
    protected ChargeOffModel.Result doAct(ChargeOffModel.BulkFlowArg arg) {
        if (Objects.nonNull(this.dataUpdateAndAddArg)) {
            String dataIdsStr = String.join(",", MapUtils.emptyIfNull(arg.getDataChargeOffAmountMap()).keySet());
            Map<String, List<IObjectData>> addDataListMap = Maps.newHashMap();
            CollectionUtils.nullToEmpty(this.dataUpdateAndAddArg.getAddDataModelList()).forEach(x -> {
                addDataListMap.compute(x.getObjectApiName(), (k, v) -> {
                    if (Objects.isNull(v)) {
                        v = Lists.newArrayList();
                    }
                    v.add(x.getAddObjectData());
                    return v;
                });
            });
            CustomerAccountLogUtil.ChargeOffLog.builder().action("incomeFlowChargeOff").objectApiName(arg.getObjectApiName()).objectDataId(dataIdsStr)
                    .toAddDataListMap(addDataListMap).customerAccountColumnMap(this.dataUpdateAndAddArg.getCustomerAccountColumnUpdateMap()).build().log(actionContext.getUser());
            newCustomerAccountManager.execute(actionContext.getUser(), this.dataUpdateAndAddArg);
        }
        return new ChargeOffModel.Result();
    }

    @Override
    protected void finallyDo() {
        super.finallyDo();
        infraServiceFacade.unlock(lock);
    }

    private RLock lock(String objectApiName, Set<String> dataIds) {
        List<String> lockKeys = CollectionUtils.nullToEmpty(dataIds).stream().map(x ->
                String.format("EnterCancel_%s_%s_%s", actionContext.getTenantId(), objectApiName, x)).collect(Collectors.toList());
        RLock rLock = infraServiceFacade.tryMultiLock(5L, 15L * lockKeys.size(), TimeUnit.SECONDS, lockKeys.toArray(new String[0]));
        if (Objects.isNull(rLock)) {
            throw new ValidateException(I18N.text(CAI18NKey.DUPLICATE_ACTION));
        }
        return rLock;
    }

    protected void incomeChargeOff(String objectApiName, Map<String, BigDecimal> dataChargeOffAmountMap, boolean checkRuleEnable) {
        if (MapUtils.isEmpty(dataChargeOffAmountMap)) {
            return;
        }
        User user = actionContext.getUser();

        List<String> dataIds = Lists.newArrayList(dataChargeOffAmountMap.keySet());
        //入账收入流水红冲
        String revenueType = FundAccountBaseService.getRevenueType(objectApiName);
        List<IFilter> filterList = Lists.newArrayList();
        SearchUtil.fillFilterIn(filterList, AccountTransactionFlowConst.Field.RevenueType.apiName, Lists.newArrayList(revenueType, RevenueTypeEnum.ChargeOff.getValue()));
        SearchUtil.fillFilterEq(filterList, AccountTransactionFlowConst.Field.RelateRecordObjectApiName.apiName, objectApiName);
        SearchUtil.fillFilterIn(filterList, AccountTransactionFlowConst.Field.RelateRecordObjectDataId.apiName, dataIds);
        SearchUtil.fillFilterEq(filterList, AccountTransactionFlowConst.Field.EntryStatus.apiName, EntryStatusEnum.AlreadyEntry.getValue());
        SearchUtil.fillFilterEq(filterList, "record_type", AccountTransactionFlowConst.RecordType.IncomeRecordType.apiName);
        List<IObjectData> flowDataList = accountCheckManager.query(user, AccountTransactionFlowConst.API_NAME, filterList, Lists.newArrayList());

        Map<String, List<IObjectData>> dataIdFlowListMap = CollectionUtils.nullToEmpty(flowDataList).stream().collect(Collectors.groupingBy(x -> x.get(AccountTransactionFlowConst.Field.RelateRecordObjectDataId.apiName, String.class)));
        Map<String, Map<String, Object>> customerAccountColumnMap = Maps.newHashMap();

        this.dataUpdateAndAddArg = DataUpdateAndAddModel.Arg.create();
        dataChargeOffAmountMap.forEach((dataId, chargeOffAmount) -> {
            if (chargeOffAmount.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ValidateException(I18N.text(CAI18NKey.FIELD_MUST_GT_ZERO, I18N.text(CAI18NKey.CHARGE_OFF)));
            }
            List<IObjectData> dataFlowList = dataIdFlowListMap.get(dataId);
            if (CollectionUtils.empty(dataFlowList)) {
                throw new ValidateException(I18N.text(CAI18NKey.ENTER_FLOW_ERROR));
            }
            BigDecimal totalAmount = dataFlowList.stream().map(x -> x.get(AccountTransactionFlowConst.Field.RevenueAmount.apiName, BigDecimal.class, BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (chargeOffAmount.compareTo(totalAmount) > 0) {
                log.warn("totalRevenueAmount:{},toChargeOffAmount:{},objectApiName:{},objectDataId:{}", totalAmount, chargeOffAmount, objectApiName, dataId);
                throw new ValidateException(I18N.text(CAI18NKey.FLOW_NOT_ENOUGH_TO_CANCEL_CHARGE_OFF));
            }
            List<IObjectData> enterFlowList = dataFlowList.stream().filter(x -> {
                String dataRevenueType = x.get(AccountTransactionFlowConst.Field.RevenueType.apiName, String.class);
                return revenueType.equals(dataRevenueType);
            }).collect(Collectors.toList());

            if (CollectionUtils.size(enterFlowList) != 1) {
                throw new ValidateException(I18N.text(CAI18NKey.ENTER_FLOW_ERROR));
            }
            IObjectData enterFlowData = enterFlowList.get(0);

            IObjectData chargeOffData = ObjectDataUtil.getBaseObjectData(user, AccountTransactionFlowConst.API_NAME);
            Lists.newArrayList(AccountTransactionFlowConst.Field.Customer.apiName, AccountTransactionFlowConst.Field.FundAccount.apiName,
                            AccountTransactionFlowConst.Field.AccessModule.apiName, AccountTransactionFlowConst.Field.CustomerAccount.apiName)
                    .forEach(f -> chargeOffData.set(f, enterFlowData.get(f)));
            String customerAccountId = enterFlowData.get(AccountTransactionFlowConst.Field.CustomerAccount.apiName, String.class);

            chargeOffData.setRecordType(AccountTransactionFlowConst.RecordType.IncomeRecordType.apiName);
            chargeOffData.set(AccountTransactionFlowConst.Field.RevenueType.apiName, RevenueTypeEnum.ChargeOff.getValue());
            chargeOffData.set(AccountTransactionFlowConst.Field.RevenueAmount.apiName, chargeOffAmount.negate());
            chargeOffData.set(AccountTransactionFlowConst.Field.RelateRecordObjectApiName.apiName, objectApiName);
            chargeOffData.set(AccountTransactionFlowConst.Field.RelateRecordObjectDataId.apiName, dataId);
            chargeOffData.set(AccountTransactionFlowConst.Field.EntryStatus.apiName, EntryStatusEnum.AlreadyEntry.getValue());
            com.facishare.crm.customeraccount.util.ObjectDataUtil.setFlowFieldIfNeed(chargeOffData);

            Map<String, Object> columnMap = customerAccountColumnMap.computeIfAbsent(customerAccountId, k -> Maps.newHashMap());
            if (checkRuleEnable) {
                RuleHandlerUtil.computeCustomerAccount(columnMap, NewCustomerAccountConstants.Field.AvailableBalance.apiName, chargeOffAmount.negate());
            }
            RuleHandlerUtil.computeCustomerAccount(columnMap, NewCustomerAccountConstants.Field.AccountBalance.apiName, chargeOffAmount.negate());
            this.dataUpdateAndAddArg.customerAccountColumnUpdateMap(customerAccountColumnMap).appendAddData(chargeOffData, false);
        });
    }
}
