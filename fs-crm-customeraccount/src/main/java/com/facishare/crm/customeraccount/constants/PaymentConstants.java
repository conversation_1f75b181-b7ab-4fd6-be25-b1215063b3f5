package com.facishare.crm.customeraccount.constants;

/**
 * @IgnoreI18nFile
 */
public interface PaymentConstants {
    String API_NAME = "PaymentObj";

    enum Field {
        Customer("account_id", "客户名称"),

        PaymentAmount("payment_amount", "已用金额"),

        PaymentTerm("payment_term", "回款方式"),

        PaymentPurpose("purpose", "回款用途"),

        OrderIdText("order_id", "销售订单"),

        Amount("amount", "回款金额"),

        AvailableAmount("available_amount", "可用金额"),

        PayType("pay_type", "支付方式"),

        ChargeNo("charge_no", "支付流水号"),

        ThirdChargeNo("third_charge_no", "第三方流水号"),

        PaymentTime("payment_time", "回款时间"),

        SubmitTime("submit_time", "提交时间"),

        FinanceConfirmTime("finance_confirm_time", "财务确认时间"),

        MatchStatus("match_status", "核销状态"),

        MatchAmount("match_amount", "已核销金额"),

        EnterIntoAccount("enter_into_account", "是否入账"),

        FundAccount("fund_account_id", "入账账户"),

        PayStatementId("pay_statement_id", "支付流水", "target_related_list_payment_pay_statement_id", "回款"),

        OpeningBalance("opening_balance", "期初"),

        CollectionType("collection_type", "回款类型");

        public String apiName;
        public String label;
        public String targetRelatedListName;
        public String targetRelatedListLabel;

        Field(String apiName, String label) {
            this.apiName = apiName;
            this.label = label;
        }

        Field(String apiName, String label, String targetRelatedListName, String targetRelatedListLabel) {
            this.apiName = apiName;
            this.label = label;
            this.targetRelatedListName = targetRelatedListName;
            this.targetRelatedListLabel = targetRelatedListLabel;
        }
    }
}
