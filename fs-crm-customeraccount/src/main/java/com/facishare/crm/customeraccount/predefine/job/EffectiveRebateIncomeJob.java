package com.facishare.crm.customeraccount.predefine.job;

import com.facishare.crm.customeraccount.predefine.manager.CustomerAccountConfigManager;
import com.facishare.crm.customeraccount.predefine.manager.RebateIncomeDetailManager;
import com.facishare.crm.customeraccount.predefine.service.dto.CustomerAccountType;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import java.util.List;

@Slf4j
public class EffectiveRebateIncomeJob implements Job {

    /**
     * 1.从customerAccountConfig表中获取所有企业<br>
     * 2.遍历每个生效的收入明细，增加客户账户返利余额<br>
     *
     * @param context
     * @throws JobExecutionException
     */
    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        log.info("start EffectiveRebateIncomeJob");
        RebateIncomeDetailManager rebateIncomeDetailManager = SpringUtil.getContext().getBean(RebateIncomeDetailManager.class);
        CustomerAccountConfigManager customerAccountConfigManager = SpringUtil.getContext().getBean(CustomerAccountConfigManager.class);

        List<String> tenantIds = customerAccountConfigManager.list(CustomerAccountType.CustomerAccountEnableSwitchStatus.ENABLE.getValue());
        // 查询所有企业
        for (String tenantId : tenantIds) {
            try {
                int listSize = 0;
                int size = 100;
                int offset = 0;
                int successSize = 0;
                do {
                    List<IObjectData> enabledRebateList = rebateIncomeDetailManager.listNowDayEnableRebateIncomeDetails(tenantId, offset, size);
                    int result = rebateIncomeDetailManager.batchEnableRebateIncomeDetails(enabledRebateList);
                    listSize = enabledRebateList.size();
                    successSize = result + successSize;
                    offset = offset + listSize;
                } while (listSize == size);
                log.info("EffectiveRebateIncomeJob,tenantId={},totalSize={},successSize={}", tenantId, offset, successSize);
            } catch (Exception e) {
                log.warn(" EffectiveRebateIncomeJob error correct  customerAccount balance,for tenantId:{}", tenantId, e);
            }
        }
    }

}
