package com.facishare.crm.customeraccount.predefine.service.dto;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;
import java.util.Map;

public class QueryConfigModel {
    @Data
    public static class Arg {
        private List<String> tenantIds = Lists.newArrayList();
        private String key;
    }

    @Data
    public static class Result {
        private Map<String, List<String>> configMap;
    }
}
