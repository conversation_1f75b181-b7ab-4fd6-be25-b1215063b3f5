package com.facishare.crm.customeraccount.predefine.domainplugin.processor.credit;

import com.facishare.crm.customeraccount.constants.AccountTransactionFlowConst;
import com.facishare.crm.customeraccount.constants.CAI18NKey;
import com.facishare.crm.customeraccount.predefine.domainplugin.model.CreditRuleAddContextModel;
import com.facishare.crm.customeraccount.predefine.domainplugin.model.ObjectDataCreditRuleInfo;
import com.facishare.crm.customeraccount.predefine.manager.CreditManager;
import com.facishare.crm.customeraccount.predefine.manager.NewCustomerAccountManager;
import com.facishare.crm.customeraccount.predefine.service.dto.CreditCurAndPreObjectConfig;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.domain.DomainPlugin;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public abstract class CreditRuleByMasterProcessor<A extends DomainPlugin.Arg, R extends DomainPlugin.Result, C> extends CreditRuleProcessor<A, R, C> {
    protected CreditRuleByMasterProcessor(NewCustomerAccountManager newCustomerAccountManager, CreditManager creditManager, ServiceFacade serviceFacade) {
        super(newCustomerAccountManager, creditManager, serviceFacade);
    }

    @Override
    public CreditRuleAddContextModel firstNodeMatchRule(RequestContext requestContext, IObjectData objectData, Map<String, List<ObjectDataDocument>> details, ObjectDataCreditRuleInfo objectDataCreditRuleInfo, boolean skipValidateCredit) {
        User user = requestContext.getUser();
        CreditCurAndPreObjectConfig creditCurAndPreConfig = objectDataCreditRuleInfo.getCreditCurAndPreObjectConfig();

        String customerFieldApiName = creditCurAndPreConfig.getCustomerFieldApiName();
        String customerId = objectData.get(customerFieldApiName, String.class);

        IObjectData creditCustomerAccountData = newCustomerAccountManager.findCreditCustomerAccountData(requestContext, customerId);
        String occupiedAmountField = objectDataCreditRuleInfo.getOccupiedAmountField();
        BigDecimal curTotalCreditAmount = objectData.get(occupiedAmountField, BigDecimal.class, BigDecimal.ZERO);
        CreditRuleFirstNodeMatchResult creditRuleFirstNodeMatchResult = CreditRuleFirstNodeMatchResult.ofByFirstNodeMasterMode(user, objectData, creditCustomerAccountData, curTotalCreditAmount, objectDataCreditRuleInfo.getCurCreditOccupiedRuleDetailData());
        newCustomerAccountManager.executeCredit(user, creditRuleFirstNodeMatchResult.to(skipValidateCredit));
        return creditRuleFirstNodeMatchResult.toContextModel(objectData);
    }

    @Override
    public CreditRuleAddContextModel notFirstNodeMatchRule(RequestContext requestContext, IObjectData objectData, Map<String, List<ObjectDataDocument>> details, ObjectDataCreditRuleInfo objectDataCreditRuleInfo) {
        User user = requestContext.getUser();
        String preCreditObject = objectDataCreditRuleInfo.getPreCreditObjectApiName();

        CreditCurAndPreObjectConfig creditCurAndPreConfig = objectDataCreditRuleInfo.getCreditCurAndPreObjectConfig();
        String lookUpPreFieldName = creditCurAndPreConfig.getLookUpPreObjectFieldApiName();
        String customerFieldApiName = creditCurAndPreConfig.getCustomerFieldApiName();
        String customerId = objectData.get(customerFieldApiName, String.class);
        List<IObjectData> preRuleMatchRecordList;

        String occupiedAmountField = objectDataCreditRuleInfo.getOccupiedAmountField();
        Set<String> preNodeMasterDataIds = Sets.newHashSet();

        BigDecimal curTotalCreditAmount = objectData.get(occupiedAmountField, BigDecimal.class, BigDecimal.ZERO);
        String lookupPreDataId = objectData.get(lookUpPreFieldName, String.class);
        if (StringUtils.isEmpty(lookupPreDataId)) {
            return new CreditRuleAddContextModel(objectData.getDescribeApiName(), objectData.getId());
        }
        preNodeMasterDataIds.add(lookupPreDataId);
        //查找上一节点是否有匹配记录
        preRuleMatchRecordList = creditManager.findCreditRuleMatchRecordByData(user, preCreditObject, preNodeMasterDataIds);
        if (CollectionUtils.empty(preRuleMatchRecordList)) {
            return new CreditRuleAddContextModel(objectData.getDescribeApiName(), objectData.getId());
        }
        List<IObjectData> preNodeTransactionFlowDataList = creditManager.findCreditTransactionExpenseFlowByRelateObject(user, preCreditObject, preNodeMasterDataIds);
        Map<String, IObjectData> preObjectDataTransactionFlowDataMap = preNodeTransactionFlowDataList.stream().collect(Collectors.toMap(x -> x.get(AccountTransactionFlowConst.Field.RelateRecordObjectDataId.apiName, String.class), Function.identity()));
        IObjectData preObjectDataTransactionFlowData = preObjectDataTransactionFlowDataMap.get(lookupPreDataId);
        BigDecimal preNodeCreditAmount = Objects.isNull(preObjectDataTransactionFlowData) ? BigDecimal.ZERO : preObjectDataTransactionFlowData.get(AccountTransactionFlowConst.Field.ExpenseAmount.apiName, BigDecimal.class);
        if (curTotalCreditAmount.compareTo(preNodeCreditAmount) > 0) {
            log.warn("curTotalCreditAmount:{},preNodeCreditAmount:{}", curTotalCreditAmount, preNodeCreditAmount);
            throw new ValidateException(I18N.text(CAI18NKey.CREDIT_AMOUNT_NOT_GT_PRE_NODE));
        }
        Map<String, BigDecimal> preNodeTransactionFlowTransferAmountMap = Maps.newHashMap();
        if (curTotalCreditAmount.compareTo(BigDecimal.ZERO) > 0 && Objects.nonNull(preObjectDataTransactionFlowData)) {
            preNodeTransactionFlowTransferAmountMap.put(preObjectDataTransactionFlowData.getId(), preNodeCreditAmount.subtract(curTotalCreditAmount));
        }
        IObjectData creditCustomerAccountData = newCustomerAccountManager.findCreditCustomerAccountData(requestContext, customerId);
        CreditRuleNotFirstNodeMatchResult creditRuleMatchResult = new CreditRuleNotFirstNodeMatchResult(preCreditObject, preRuleMatchRecordList, creditCustomerAccountData, objectDataCreditRuleInfo.getCurCreditOccupiedRuleDetailData()
                , Maps.newHashMap(), preNodeTransactionFlowTransferAmountMap, null);
        return creditRuleMatchResult.to(objectData);
    }

}
