package com.facishare.crm.customeraccount.constants;

/**
 * @IgnoreI18nFile
 */
public interface AccountCheckRuleConstants {
    String API_NAME = "AccountCheckRuleObj";

    String DISPLAY_NAME = "账户校验规则";
    String DEFAULT_LAYOUT_API_NAME = "AccountCheckRuleObj_default_layout__c";
    String DEFAULT_LAYOUT_DISPLAY_NAME = "默认布局";
    String LIST_LAYOUT_API_NAME = "AccountCheckRuleObj_list_layout__c";
    String LIST_LAYOUT_DISPLAY_NAME = "移动端默认列表页";
    String STORE_TABLE_NAME = "account_check_rule";
    int ICON_INDEX = 18;//待定

    enum Field {
        Name("name", "规则名称"),

        CheckObject("check_object", "校验对象"),
        CheckTriggerAction("check_trigger_action", "触发动作"),//之前是存储新建、自定义按钮等动作，这个字段要改为用来存储 字段变更、完成动作
        CheckTriggerButton("check_trigger_button","触发按钮"),//新增字段用来存储上面原来的 新建、自定义按钮 动作
        TriggerCondition("trigger_condition", "触发条件"),
        TriggerConditionRuleCode("trigger_condition_rule_code", "触发条件ID"),

        CheckRule("check_rule", "校验规则"),
        CheckRuleRuleCode("check_rule_rule_code", "校验规则ID"),
        OccupiedMapping("occupied_mapping", "占用映射"),

        ReduceRelatedObject("reduce_related_object", "扣减关联对象"),
        ReduceTriggerAction("reduce_trigger_action", "扣减触发动作"),
        ReduceTriggerButton("reduce_trigger_button", "扣减触发按钮"),
        ReduceTriggerCondition("reduce_trigger_condition", "扣减触发条件"),
        ReduceTriggerConditionRuleCode("reduce_trigger_condition_rule_code", "扣减触发条件ID"),
        ReduceMapping("reduce_mapping", "支出映射"),

        Status("status", "状态"),
        Priority("priority", "优先级"),
        Specification("specification", "规则说明"),
        RuleType("rule_type", "规则类型"),

        ReconciliationRuleIdJson("reconciliation_rule_json","稽核规则Id json")
        ;

        public final String apiName;
        public final String label;

        Field(String apiName, String label) {
            this.apiName = apiName;
            this.label = label;
        }

    }
}
