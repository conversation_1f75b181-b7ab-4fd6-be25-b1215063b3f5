package com.facishare.crm.customeraccount.predefine.domainplugin.processor.credit;

import com.facishare.crm.customeraccount.util.CreditUtil;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.domain.DomainPlugin;
import com.facishare.paas.appframework.core.predef.domain.*;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class CreditRuleProcessorFactory {

    Map<Class<?>, CreditRuleProcessor<? extends DomainPlugin.Arg, ? extends DomainPlugin.Result, ?>> creditRuleByDetailProcessorMap = Maps.newHashMap();
    Map<Class<?>, CreditRuleProcessor<? extends DomainPlugin.Arg, ? extends DomainPlugin.Result, ?>> creditRuleByMasterProcessorMap = Maps.newHashMap();

    @Autowired
    public <A extends DomainPlugin.Arg, R extends DomainPlugin.Result, C> CreditRuleProcessorFactory(List<CreditRuleProcessor<A, R, C>> creditRuleProcessorList) {
        for (CreditRuleProcessor<A, R, C> creditRuleProcessor : creditRuleProcessorList) {
            if (creditRuleProcessor instanceof CreditRuleByDetailProcessor) {
                creditRuleByDetailProcessorMap.put(AopUtils.getTargetClass(creditRuleProcessor), creditRuleProcessor);
            } else if (creditRuleProcessor instanceof CreditRuleByMasterProcessor) {
                creditRuleByMasterProcessorMap.put(AopUtils.getTargetClass(creditRuleProcessor), creditRuleProcessor);
            }
        }
    }

    public <A extends DomainPlugin.Arg, R extends DomainPlugin.Result> R process(RequestContext requestContext, A arg, boolean preAct, R defaultResult) {
        Tuple<Class<?>, Class<?>> clazzKeyTuple = getCreditRuleProcessor(arg);
        if (Objects.isNull(clazzKeyTuple)) {
            return defaultResult;
        }
        Class<?> clazz = clazzKeyTuple.getKey();
        String objectApiName = arg.getObjectApiName();
        CreditRuleProcessor creditRuleProcessor;
        if (CreditUtil.setByDetail(requestContext.getTenantId(), objectApiName)) {
            creditRuleProcessor = creditRuleByDetailProcessorMap.get(clazz);
        } else {
            creditRuleProcessor = creditRuleByMasterProcessorMap.get(clazz);
        }
        if (Objects.isNull(creditRuleProcessor)) {
            return defaultResult;
        }
        if (preAct) {
            return (R) creditRuleProcessor.preAct(requestContext, arg);
        } else {
            creditRuleProcessor.finallyDo(requestContext, arg);
            return defaultResult;
        }
    }

    private <A extends DomainPlugin.Arg> Tuple<Class<?>, Class<?>> getCreditRuleProcessor(A arg) {
        if (arg instanceof AddActionDomainPlugin.Arg) {
            return Tuple.of(AddCreditRuleProcessor.class, null);
        } else if (arg instanceof EditActionDomainPlugin.Arg) {
            //finallyDo 应该取dbMasterData
            ObjectDataExt objectDataExt = ObjectDataExt.of(((EditActionDomainPlugin.Arg) arg).getDbMasterData());
            if (objectDataExt.isIneffective()) {
                return Tuple.of(EditIneffectiveCreditRuleProcessor.class, null);
            } else {
                return Tuple.of(EditCreditRuleProcessor.class, null);
            }
        } else if (arg instanceof IncrementUpdateActionDomainPlugin.Arg) {
            return Tuple.of(IncrementUpdateCreditRuleProcessor.class, null);
        } else if (arg instanceof InvalidActionDomainPlugin.Arg) {
            return Tuple.of(InvalidCreditRuleProcessor.class, null);
        } else if (arg instanceof BulkInvalidActionDomainPlugin.Arg) {
            return Tuple.of(BulkInvalidCreditRuleProcessor.class, null);
        } else if (arg instanceof FlowCompletedActionDomainPlugin.Arg) {
            String triggerType = ((FlowCompletedActionDomainPlugin.Arg) arg).getTriggerType();
            ApprovalFlowTriggerType approvalFlowTriggerType = ApprovalFlowTriggerType.getType(triggerType);
            switch (approvalFlowTriggerType) {
                case CREATE:
                    return Tuple.of(AddFlowCompletedCreditRuleProcessor.class, null);
                case UPDATE:
                    return Tuple.of(EditFlowCompletedCreditRuleProcessor.class, null);
                case INVALID:
                    return Tuple.of(InvalidFlowCompletedCreditRuleProcessor.class, null);
                default:
                    break;
            }
        }
        return null;
    }
}
