package com.facishare.crm.customeraccount.predefine.action;

import com.facishare.crm.consts.CustomerCreditAuthConst;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportTemplateAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.google.common.collect.Lists;

import java.util.List;

public class CustomerCreditAuthInsertImportTemplateAction extends StandardInsertImportTemplateAction {
    private static List<String> notSupportField = Lists.newArrayList(CustomerCreditAuthConst.F.FundAccountId.apiName,
            CustomerCreditAuthConst.F.CustomerAccountId.apiName);

    @Override
    protected void customHeader(List<IFieldDescribe> headerFieldList) {
        super.customHeader(headerFieldList);
        headerFieldList.removeIf(x -> notSupportField.contains(x.getApiName()));
    }
}
