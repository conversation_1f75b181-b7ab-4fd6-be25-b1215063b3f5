package com.facishare.crm.customeraccount.predefine.controller;

import com.facishare.crmcommon.util.SearchUtil;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;

import java.util.List;

public class ShoppingCartListController extends StandardListController {
    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery searchTemplateQuery = super.buildSearchTemplateQuery();
        List<IFilter> filters = searchTemplateQuery.getFilters();
        User user = controllerContext.getUser();
        SearchUtil.fillFilterEq(filters, "created_by", Lists.newArrayList(user.isOutUser() ? user.getOutUserId() : user.getUserId()));
        return searchTemplateQuery;
    }
}
