package com.facishare.crm.customeraccount.predefine.domainplugin.processor;

import com.facishare.crm.customeraccount.constants.*;
import com.facishare.crm.customeraccount.enums.AccountCheckRuleTypeEnum;
import com.facishare.crm.customeraccount.enums.DirectReduceForceCheckAmountEnum;
import com.facishare.crm.customeraccount.enums.ReduceTriggerActionEnum;
import com.facishare.crm.customeraccount.enums.StageEnum;
import com.facishare.crm.customeraccount.model.AccountRuleUseRecordsModel;
import com.facishare.crm.customeraccount.model.DataTriggerFunctionModel;
import com.facishare.crm.customeraccount.model.DataUpdateAndAddModel;
import com.facishare.crm.customeraccount.mq.producer.MQProducerManager;
import com.facishare.crm.customeraccount.predefine.domainplugin.BranchTransactionUtil;
import com.facishare.crm.customeraccount.predefine.domainplugin.event.CheckRuleEvent;
import com.facishare.crm.customeraccount.predefine.domainplugin.event.CheckRuleInvalidModel;
import com.facishare.crm.customeraccount.predefine.domainplugin.event.CheckRuleTagEnum;
import com.facishare.crm.customeraccount.predefine.domainplugin.model.*;
import com.facishare.crm.customeraccount.predefine.domainplugin.rulecompute.DirectReduceFlowCalculator;
import com.facishare.crm.customeraccount.predefine.domainplugin.rulecompute.UnfreezeCalculator;
import com.facishare.crm.customeraccount.predefine.handler.RuleHandlerUtil;
import com.facishare.crm.customeraccount.predefine.handler.checkrule.AdaptEditResult;
import com.facishare.crm.customeraccount.predefine.manager.AccountCheckManager;
import com.facishare.crm.customeraccount.predefine.manager.NewCustomerAccountManager;
import com.facishare.crm.customeraccount.util.CaGrayUtil;
import com.facishare.crm.customeraccount.util.CustomerAccountLogUtil;
import com.facishare.crm.customeraccount.util.ObjectDataUtil;
import com.facishare.crm.customeraccount.util.rule.RuleUtil;
import com.facishare.crm.openapi.Utils;
import com.facishare.marketing.common.contstant.RocketMqDelayLevelConstants;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.model.domain.DomainPlugin;
import com.facishare.paas.appframework.core.predef.action.StandardAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.appframework.metadata.cache.RedissonServiceImpl;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.transaction.common.context.GlobalTransactionContext;
import com.fxiaoke.transaction.core.util.JacksonUtil;
import com.fxiaoke.transaction.tcc.api.context.BranchTransactionalContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public abstract class AbstractCheckRuleProcessor<A extends DomainPlugin.Arg, R extends DomainPlugin.Result, C> extends CheckRuleProcessor {
    private final RedissonServiceImpl redissonService;

    protected AbstractCheckRuleProcessor(AccountCheckManager accountCheckManager, NewCustomerAccountManager newCustomerAccountManager, MQProducerManager mqProducerManager, RedissonServiceImpl redissonService) {
        super(accountCheckManager, newCustomerAccountManager, mqProducerManager);
        this.redissonService = redissonService;
    }

    protected final CheckRuleTuple<FrozenRuleMatchedModel> matchFrozenAndGetContext(RequestContext requestContext, IObjectDescribe objectDescribe, IObjectData objectData, Map<String, Object> dataMap, List<IObjectData> checkRuleList) {
        DataUpdateAndAddModel.Arg updateAddArg = DataUpdateAndAddModel.Arg.create();
        IObjectData frozenRuleData = getMatchedFrozenRule(requestContext.getUser(), objectDescribe, dataMap, checkRuleList);
        if (Objects.isNull(frozenRuleData)) {
            return CheckRuleTuple.ofNull();
        }
        FrozenResult frozenResult = matchFrozenRule(requestContext, objectData, frozenRuleData);
        IObjectData ruleUseRecordData = frozenResult.getAccountRuleUseRecordData();
        if (Objects.isNull(ruleUseRecordData)) {
            return CheckRuleTuple.ofNull();
        }
        FrozenRuleMatchedModel frozenRuleMatchedModel = FrozenRuleMatchedModel.of(ruleUseRecordData.getId(), frozenRuleData.getId(), frozenResult);
        updateAddArg.appendAddData(frozenResult.getAccountRuleUseRecordData(), true);
        updateAddArg.appendAddData(frozenResult.getToAddFrozenRecordList(), true);
        updateAddArg.mergeCustomerAccountData(frozenResult.getCustomerAccountDataList());
        updateAddArg.customerAccountColumnUpdateMap(frozenResult.getCustomerAccountUpdateFieldColumnMap());
        return CheckRuleTuple.of(updateAddArg, frozenRuleMatchedModel);
    }

    protected final CheckRuleTuple<CheckRuleAdaptModel> adaptFrozenRuleAndGetContext(RequestContext requestContext, RuleUseRecordModel ruleUseRecordModel, IObjectData objectData, boolean preStage, boolean disableEditAmountField) {
        if (Objects.isNull(objectData)) {
            return CheckRuleTuple.ofNull();
        }
        DataUpdateAndAddModel.Arg updateAddArg = DataUpdateAndAddModel.Arg.create();
        AdaptEditResult adaptEditResult = adaptFrozenRule(requestContext, ruleUseRecordModel, objectData, preStage, disableEditAmountField);
        boolean invalidFlowAfterCancel = needInvalidFlowAfterCancel(requestContext.getUser());
        adaptEditResult.mergeTo(updateAddArg, invalidFlowAfterCancel);
        CheckRuleAdaptModel checkRuleAdaptModel = CheckRuleAdaptModel.of(ruleUseRecordModel.getAccountRuleUseRecordData(), ruleUseRecordModel.getDataMatchedRuleStageEnum(), adaptEditResult.changed());
        return CheckRuleTuple.of(updateAddArg, checkRuleAdaptModel);
    }

    final protected boolean needInvalidFlowAfterCancel(User user) {
        return newCustomerAccountManager.checkRuleFlowNeedInvalid(user);
    }

    protected AllRuleUseRecordModel queryAllRuleUseRecord(RequestContext requestContext, String objectApiName, String objectDataId) {
        AccountRuleUseRecordsModel accountRuleUseRecordsModel = accountCheckManager.queryCheckRuleUseRecordMap(requestContext.getUser(), objectApiName, objectDataId);
        return accountRuleUseRecordsModel.toAllRuleUseRecordData();
    }

    protected Map<String, IObjectData> queryAccountRuleUseRecord(RequestContext requestContext, List<String> accountRuleUseRecordIds) {
        List<IObjectData> dataList = accountCheckManager.findObjectDataByIds(requestContext.getUser(), accountRuleUseRecordIds, AccountRuleUseRecordConstants.API_NAME);
        return dataList.stream().collect(Collectors.toMap(IObjectData::getId, Function.identity()));
    }

    public abstract R newResultInstance();

    public abstract CheckRulePluginContextKey getContextKeyEnum();

    public abstract ObjectDataDocument getObjectDataDocument(A arg);

    public abstract Class<C> getContextClass();

    protected String toJson(C contextModel) {
        return JacksonUtil.toJson(contextModel);
    }

    public C getContextModel(A arg) {
        if (GlobalTransactionContext.isBind()) {
            return BranchTransactionUtil.getContextData(BranchTransactionalContext.getCurrent(), getContextKeyEnum().key, getContextClass());
        }
        String contextString = arg.getContextData(getContextKeyEnum().key);
        return JacksonUtil.fromJson(contextString, getContextClass());
    }

    public final R preAct(RequestContext requestContext, A arg) {
        R result = newResultInstance();
        ObjectDataDocument objectDataDocument = getObjectDataDocument(arg);
        Set<String> lockKeys = Sets.newHashSet();
        if (Objects.nonNull(objectDataDocument)) {
            IObjectData objectData = objectDataDocument.toObjectData();
            User user = requestContext.getUser();
            lockKeys = accountCheckManager.getLockKeys(user, objectData);
        }
        RLock lock = newCustomerAccountManager.lockAndBlock(requestContext, lockKeys);
        long lockTime = System.currentTimeMillis();
        C context = null;
        try {
            context = doPreAct(requestContext, arg);
            setLockInfo(context, lockTime, lockKeys);
            Map<String, String> contextData = Maps.newHashMap();
            if (GlobalTransactionContext.isBind()) {
                BranchTransactionUtil.setContextData(getContextKeyEnum().key, context);
                contextData.put(getContextKeyEnum().key + "_transaction", "true");
            } else {
                contextData.put(getContextKeyEnum().key, toJson(context));
            }
            result.setContextData(contextData);
        } catch (Exception e) {
            redissonService.unlock(lock);
            throw e;
        } finally {
            if (Objects.isNull(context) && GlobalTransactionContext.isBind()) {
                LockContextModel mark = new LockContextModel();
                mark.setNoContext(true);
                BranchTransactionUtil.setContextData(getContextKeyEnum().key, mark);
            }
        }
        return result;
    }

    public abstract C doPreAct(RequestContext requestContext, A arg);

    public final Boolean confirm(BranchTransactionalContext branchContext, RequestContext requestContext, A arg) {
        boolean exception = false;
        C contextModel = null;
        try {
            log.info("confirm,branchContext:{}", branchContext);
            bindTransaction(branchContext);
            contextModel = getContextModel(arg);
            doConfirm(branchContext, requestContext, arg, contextModel);
            return Boolean.TRUE;
        } catch (Exception e) {
            exception = true;
            throw e;
        } finally {
            unbindTransaction();
            //处理释放锁逻辑
            if (!exception) {
                newCustomerAccountManager.unlock(contextModel);
            }
        }
    }

    private void bindTransaction(BranchTransactionalContext branchContext) {
        BranchTransactionalContext.setCurrent(branchContext);
    }

    private void unbindTransaction() {
        BranchTransactionalContext.setCurrent(null);
    }

    public void doConfirm(BranchTransactionalContext branchContext, RequestContext requestContext, A arg, C contextModel) {


    }

    public final Boolean cancel(BranchTransactionalContext branchContext, RequestContext requestContext, A arg) {
        C contextModel = null;
        boolean exception = false;
        try {
            log.info("cancel,branchContext:{}", branchContext);
            bindTransaction(branchContext);
            contextModel = getContextModel(arg);//可能存在contextModel为null的情况：1.preAct中异常；2.preAct执行超时时，第一次cancel没有context，第二次cancel时有context
            if (contextModel instanceof LockContextModel && BooleanUtils.isTrue(((LockContextModel) contextModel).getNoContext())) {
                return Boolean.TRUE;
            }
            doCancel(branchContext, requestContext, arg, contextModel);
            return Boolean.TRUE;
        } catch (Exception e) {
            exception = true;
            throw e;
        } finally {
            unbindTransaction();
            //处理释放锁逻辑
            if (!exception) {
                newCustomerAccountManager.unlock(contextModel);
            }
        }
    }

    public void doCancel(BranchTransactionalContext branchContext, RequestContext requestContext, A arg, C contextModel) {

    }

    public final void finallyDo(RequestContext requestContext, A arg) {
        try {
            Map<String, String> contextData = arg.getContextData();
            String transactionKey = getContextKeyEnum().key + "_transaction";
            if (Objects.isNull(contextData) || (!contextData.containsKey(getContextKeyEnum().key) && !contextData.containsKey(transactionKey))) {
                //context 为空 finallyDo不需要执行，直接返回
                User user = RequestContextManager.getContext().getUser();
                String objectApiName = arg.getObjectApiName();
                ObjectDataDocument objectDataDocument = getObjectDataDocument(arg);
                CustomerAccountLogUtil.reportContextError(user, objectApiName, Objects.nonNull(objectDataDocument) ? objectDataDocument.getId() : null, getContextKeyEnum().key);
                return;
            }
            if (contextData.containsKey(transactionKey)) {
                //事务模式，直接返回
                return;
            }
            doFinallyDo(requestContext, arg);
        } finally {
            unlock(arg, getContextKeyEnum());
        }
    }

    public abstract void doFinallyDo(RequestContext requestContext, A arg);

    //编辑Action不适宜此方法
    protected CheckRuleCommonContextModel doPreActByAction(RequestContext requestContext, IObjectData objectData, boolean needSetLifeStatusNormal, boolean needDoAdapt) {
        User user = requestContext.getUser();
        String objectApiName = objectData.getDescribeApiName();

        CheckRuleCommonContextModel contextModel = new CheckRuleCommonContextModel(objectApiName, objectData.getId());
        DataUpdateAndAddModel.Arg updateAddArg = DataUpdateAndAddModel.Arg.create();

        AllRuleUseRecordModel allRuleUseRecordModel = needDoAdapt ? queryAllRuleUseRecord(requestContext, objectApiName, objectData.getId()) : new AllRuleUseRecordModel();
        List<IObjectData> checkRuleList = allRuleUseRecordModel.getCheckRuleList(user, objectApiName, accountCheckManager);//accountCheckManager.queryAccountCheckRule(user, objectApiName);

        IObjectDescribe objectDescribe = accountCheckManager.findObject(requestContext.getTenantId(), objectApiName);
        Map<String, Object> dataMap = ObjectDataUtil.toDataMap(objectDescribe, objectData);
        //set normal
        if (needSetLifeStatusNormal) {
            ObjectDataUtil.setLifeStatus(dataMap, ObjectLifeStatus.NORMAL);
        }
        if (Objects.isNull(allRuleUseRecordModel.getFrozenRuleUseRecordModel())) {
            CheckRuleTuple<FrozenRuleMatchedModel> frozenMatchTuple = matchFrozenAndGetContext(requestContext, objectDescribe, objectData, dataMap, checkRuleList);
            updateAddArg.merge(frozenMatchTuple.getArg());
            contextModel.setFrozenRuleMatchedModel(frozenMatchTuple.getContext());
        } else if (needDoAdapt) {
            CheckRuleTuple<CheckRuleAdaptModel> frozenAdaptResult = adaptFrozenRuleAndGetContext(requestContext, allRuleUseRecordModel.getFrozenRuleUseRecordModel(), objectData, true, false);
            contextModel.setFrozenRuleAdaptModel(frozenAdaptResult.getContext());
            updateAddArg.merge(frozenAdaptResult.getArg());
        }

        if (Objects.isNull(allRuleUseRecordModel.getDirectReduceRuleUseRecordModel())) {
            CheckRuleTuple<CheckRuleOrModel<DirectReduceRuleMatchedModel, DirectReduceRuleToMatchModel>> directReduceMatchResult = matchDirectReduceGetContextAtPre(requestContext, objectDescribe, objectData, dataMap, checkRuleList);
            updateAddArg.merge(directReduceMatchResult.getArg());
            contextModel.setDirectReduceRuleMatchedModel(directReduceMatchResult.getContext().getFirst());
            contextModel.setDirectReduceRuleToMatchModel(directReduceMatchResult.getContext().getSecond());
        } else if (needDoAdapt) {
            CheckRuleTuple<CheckRuleAdaptModel> directReduceAdaptResult = adaptDirectReduceRuleAndGetContext(requestContext, allRuleUseRecordModel.getDirectReduceRuleUseRecordModel(), objectData, true, false);
            contextModel.setDirectReduceAdaptModel(directReduceAdaptResult.getContext());
            updateAddArg.merge(directReduceAdaptResult.getArg());
        }

        if (needDoAdapt) {
            CheckRuleTuple<List<CheckRuleAdaptModel>> unfreezeAdaptResult = adaptUnfreezeRuleAndGetContext(requestContext, allRuleUseRecordModel.getUnfreezeRuleUseRecordModelList(), objectData, true, false);
            contextModel.setUnfreezeAdaptModelList(unfreezeAdaptResult.getContext());
            updateAddArg.merge(unfreezeAdaptResult.getArg());
        }

        contextModel.setUnfreezeRuleTriggerTypeEnum(matchUnfreezeMark());
        if (CheckRulePluginContextKey.Add.equals(getContextKeyEnum())) {
            CheckRuleEvent<CheckRuleAddContextModel> checkRuleEvent = CheckRuleEvent.of(requestContext.getUser(), contextModel.toAddContext());
            if (!GlobalTransactionContext.isBind()) {
                //开启事务时，不需要发送MQ处理超时回滚问题
                mqProducerManager.sendCheckRuleDelayMQ(requestContext.getTenantId(), CheckRuleTagEnum.CheckRuleAdd.tag, checkRuleEvent, RocketMqDelayLevelConstants.TWO_MINUTE);
            }
        }
        newCustomerAccountManager.execute(user, updateAddArg);
        return contextModel;
    }

    protected void doActCompleteTrueByAction(RequestContext requestContext, IObjectData objectData, CheckRuleCommonContextModel contextModel, StandardAction standardAction) {
        User user = requestContext.getUser();
        DataTriggerFunctionModel dataTriggerFunctionModel = DataTriggerFunctionModel.create();
        DataUpdateAndAddModel.Arg updateAddArg = DataUpdateAndAddModel.Arg.create();

        ObjectLifeStatus objectLifeStatus = ObjectDataExt.of(objectData).getLifeStatus();
        Map<String, IObjectData> accountRuleUseRecordDataMap = queryAccountRuleUseRecord(requestContext, contextModel.fetchRuleUseRecordIds());

        FrozenRuleMatchedModel frozenRuleMatchedModel = contextModel.getFrozenRuleMatchedModel();
        if (Objects.nonNull(frozenRuleMatchedModel)) {
            IObjectData accountRuleUseRecordData = accountRuleUseRecordDataMap.get(frozenRuleMatchedModel.getAccountRuleUseRecordId());
            if (Objects.nonNull(accountRuleUseRecordData)) {
                CheckRuleTuple<DataTriggerFunctionModel> frozenTuple = frozenMatchedFinallyDo(requestContext, standardAction, objectData, accountRuleUseRecordData, frozenRuleMatchedModel);
                dataTriggerFunctionModel.merge(frozenTuple.getContext());
                updateAddArg.merge(frozenTuple.getArg());
            }
        }

        CheckRuleAdaptModel frozenAdaptModel = contextModel.getFrozenRuleAdaptModel();
        if (Objects.nonNull(frozenAdaptModel)) {
            IObjectData accountRuleUseRecordData = accountRuleUseRecordDataMap.get(frozenAdaptModel.getAccountRuleUseRecordId());
            RuleUseRecordModel ruleUseRecordModel = toRuleUseRecordModel(requestContext.getUser(), accountRuleUseRecordData, frozenAdaptModel.getDataMatchedRuleStage());
            CheckRuleTuple<CheckRuleAdaptModel> frozenAdaptTuple = adaptFrozenRuleAndGetContext(requestContext, ruleUseRecordModel, objectData, false, false);
            updateAddArg.merge(frozenAdaptTuple.getArg());
        }

        DirectReduceRuleMatchedModel directReduceRuleMatchedModel = contextModel.getDirectReduceRuleMatchedModel();
        if (Objects.nonNull(directReduceRuleMatchedModel)) {
            String directReduceRuleUseRecordId = directReduceRuleMatchedModel.getAccountRuleUseRecordId();
            IObjectData accountRuleUseRecordData = accountRuleUseRecordDataMap.get(directReduceRuleUseRecordId);
            if (Objects.nonNull(accountRuleUseRecordData)) {
                CheckRuleTuple<DataTriggerFunctionModel> directReduceTuple = directReduceMatchedFinallyDo(requestContext, standardAction, objectData, accountRuleUseRecordData, directReduceRuleMatchedModel);
                dataTriggerFunctionModel.merge(directReduceTuple.getContext());
                updateAddArg.merge(directReduceTuple.getArg());
            }
        }

        CheckRuleAdaptModel directReduceAdaptModel = contextModel.getDirectReduceAdaptModel();
        if (Objects.nonNull(directReduceAdaptModel)) {
            IObjectData accountRuleUseRecordData = accountRuleUseRecordDataMap.get(directReduceAdaptModel.getAccountRuleUseRecordId());
            RuleUseRecordModel ruleUseRecordModel = toRuleUseRecordModel(requestContext.getUser(), accountRuleUseRecordData, directReduceAdaptModel.getDataMatchedRuleStage());
            CheckRuleTuple<CheckRuleAdaptModel> directReduceAdaptTuple = adaptDirectReduceRuleAndGetContext(requestContext, ruleUseRecordModel, objectData, false, false);
            updateAddArg.merge(directReduceAdaptTuple.getArg());
        }

        List<CheckRuleAdaptModel> unfreezeAdaptList = contextModel.getUnfreezeAdaptModelList();
        if (CollectionUtils.notEmpty(unfreezeAdaptList)) {
            List<RuleUseRecordModel> unfrezeRuleUseRecordList = unfreezeAdaptList.stream().map(x -> {
                IObjectData accountRuleUseRecordData = accountRuleUseRecordDataMap.get(x.getAccountRuleUseRecordId());
                return toRuleUseRecordModel(requestContext.getUser(), accountRuleUseRecordData, x.getDataMatchedRuleStage());
            }).collect(Collectors.toList());
            CheckRuleTuple<List<CheckRuleAdaptModel>> unfreezeTuple = adaptUnfreezeRuleAndGetContext(requestContext, unfrezeRuleUseRecordList, objectData, false, false);
            updateAddArg.merge(unfreezeTuple.getArg());
        }

        newCustomerAccountManager.execute(user, updateAddArg);
        dataTriggerFunctionModel.triggerFunctionIgnoreException(user, StageEnum.POST, newCustomerAccountManager, true);

        if (objectLifeStatus == ObjectLifeStatus.NORMAL) {
            triggerDirectReduceIgnoreException(requestContext, objectData, contextModel.getDirectReduceRuleToMatchModel());
            triggerUnfreezeIgnoreException(requestContext, objectData, contextModel.getUnfreezeRuleTriggerTypeEnum());
        }
    }

    protected void doActCompleteFalseByAction(RequestContext requestContext, IObjectData dbObjectData, CheckRuleCommonContextModel contextModel) {
        DataUpdateAndAddModel.Arg updateAddArg = DataUpdateAndAddModel.Arg.create();
        Map<String, IObjectData> accountRuleUseRecordDataMap = queryAccountRuleUseRecord(requestContext, contextModel.fetchRuleUseRecordIds());

        FrozenRuleMatchedModel frozenRuleMatchedModel = contextModel.getFrozenRuleMatchedModel();
        CheckRuleAdaptModel frozenAdaptModel = contextModel.getFrozenRuleAdaptModel();
        boolean invalidFlowAfterCancel = needInvalidFlowAfterCancel(requestContext.getUser());
        if (Objects.nonNull(frozenRuleMatchedModel)) {
            IObjectData accountRuleUseRecordData = accountRuleUseRecordDataMap.get(frozenRuleMatchedModel.getAccountRuleUseRecordId());
            FrozenRollbackResult frozenRollbackResult = rollbackMatchedFrozen(requestContext, accountRuleUseRecordData, true);
            frozenRollbackResult.mergeTo(updateAddArg, invalidFlowAfterCancel, false);
        } else if (Objects.nonNull(frozenAdaptModel)) {
            IObjectData accountRuleUseRecordData = accountRuleUseRecordDataMap.get(frozenAdaptModel.getAccountRuleUseRecordId());
            RuleUseRecordModel ruleUseRecordModel = toRuleUseRecordModel(requestContext.getUser(), accountRuleUseRecordData, frozenAdaptModel.getDataMatchedRuleStage());
            CheckRuleTuple<CheckRuleAdaptModel> frozenAdaptTuple = adaptFrozenRuleAndGetContext(requestContext, ruleUseRecordModel, dbObjectData, false, false);
            updateAddArg.merge(frozenAdaptTuple.getArg());
        }

        DirectReduceRuleMatchedModel directReduceRuleMatchedModel = contextModel.getDirectReduceRuleMatchedModel();
        CheckRuleAdaptModel directReduceAdaptModel = contextModel.getDirectReduceAdaptModel();
        if (Objects.nonNull(directReduceRuleMatchedModel)) {
            IObjectData accountRuleUseRecordData = accountRuleUseRecordDataMap.get(directReduceRuleMatchedModel.getAccountRuleUseRecordId());
            DirectReduceRollbackResult directReduceRollbackResult = rollbackDirectReduce(requestContext, accountRuleUseRecordData, true);
            directReduceRollbackResult.mergeTo(updateAddArg, invalidFlowAfterCancel, false);
        } else if (Objects.nonNull(directReduceAdaptModel)) {
            IObjectData accountRuleUseRecordData = accountRuleUseRecordDataMap.get(directReduceAdaptModel.getAccountRuleUseRecordId());
            RuleUseRecordModel ruleUseRecordModel = toRuleUseRecordModel(requestContext.getUser(), accountRuleUseRecordData, directReduceAdaptModel.getDataMatchedRuleStage());
            CheckRuleTuple<CheckRuleAdaptModel> directReduceAdaptTuple = adaptDirectReduceRuleAndGetContext(requestContext, ruleUseRecordModel, dbObjectData, false, false);
            updateAddArg.merge(directReduceAdaptTuple.getArg());
        }
        newCustomerAccountManager.execute(requestContext.getUser(), updateAddArg);
    }

    private void setLockInfo(Object result, long lockTime, Set<String> lockKeys) {
        if (Objects.nonNull(result) && result instanceof LockContextModel && !CollectionUtils.empty(lockKeys)) {
            LockContextModel lockContextModel = (LockContextModel) result;
            lockContextModel.setLockTime(lockTime);
            lockContextModel.setLockKeys(lockKeys);
            lockContextModel.setPluginReqId(IdGenerator.get());
        }
    }

    private void unlock(A arg, CheckRulePluginContextKey contextKey) {
        String contextDataString = arg.getContextData(contextKey.key);
        if (StringUtils.isEmpty(contextDataString)) {
            return;
        }
        C contextModel = getContextModel(arg);
        newCustomerAccountManager.unlock(contextModel);
    }

    protected final CheckRuleTuple<DirectReduceRuleMatchedModel> matchDirectReduceAndGetContextAtPre(RequestContext requestContext, IObjectData objectData, IObjectData directReduceRule) {
        DirectReduceResult directReduceResult = matchDirectReduceRuleAtPre(requestContext, objectData, directReduceRule, getContextKeyEnum() == CheckRulePluginContextKey.Add);
        //在pre中匹配规则时，不触发后动作函数，在finallyDo中触发
        IObjectData directReduceRuleUseRecordData = directReduceResult.getToAddAccountRuleUseRecordData();
        if (Objects.isNull(directReduceRuleUseRecordData)) {
            return CheckRuleTuple.ofNull();
        }
        DirectReduceRuleMatchedModel directReduceRuleMatchedModel = DirectReduceRuleMatchedModel.of(directReduceRuleUseRecordData.getId(), directReduceRule.getId(), directReduceResult.getToAddAccountTransactionFlowDataList());
        DataUpdateAndAddModel.Arg updateAddArg = DataUpdateAndAddModel.Arg.create();
        updateAddArg.appendAddData(directReduceRuleUseRecordData, true);
        updateAddArg.appendAddData(directReduceResult.getToAddAccountTransactionFlowDataList(), true);
        updateAddArg.mergeCustomerAccountData(directReduceResult.getCustomerAccountDataList());
        updateAddArg.customerAccountColumnUpdateMap(directReduceResult.getCustomerAccountUpdateColumnMap());
        return CheckRuleTuple.of(updateAddArg, directReduceRuleMatchedModel);
    }

    protected CheckRuleTuple<CheckRuleOrModel<DirectReduceRuleMatchedModel, DirectReduceRuleToMatchModel>> matchDirectReduceGetContextAtPre(RequestContext requestContext, IObjectDescribe objectDescribe,
                                                                                                                                            IObjectData objectData, Map<String, Object> dataMap,
                                                                                                                                            List<IObjectData> checkRuleList) {
        IObjectData directReduceRule = getMatchedDirectReduceRule(requestContext.getUser(), objectDescribe, dataMap, checkRuleList);
        DirectReduceRuleMatchedModel directReduceRuleMatchedModel = null;
        DirectReduceRuleToMatchModel directReduceRuleToMatchModel = null;
        DataUpdateAndAddModel.Arg arg = null;
        if (Objects.nonNull(directReduceRule)) {
            if (directReduceNeedBlock(directReduceRule)) {
                CheckRuleTuple<DirectReduceRuleMatchedModel> directReduceTuple = matchDirectReduceAndGetContextAtPre(requestContext, objectData, directReduceRule);
                directReduceRuleMatchedModel = directReduceTuple.getContext();
                arg = directReduceTuple.getArg();
            } else {
                directReduceRuleToMatchModel = new DirectReduceRuleToMatchModel(directReduceRule.getId());
            }
        }
        CheckRuleOrModel<DirectReduceRuleMatchedModel, DirectReduceRuleToMatchModel> checkRuleOrModel = new CheckRuleOrModel<>(directReduceRuleMatchedModel, directReduceRuleToMatchModel);
        return CheckRuleTuple.of(arg, checkRuleOrModel);
    }

    protected final CheckRuleTuple<CheckRuleAdaptModel> adaptDirectReduceRuleAndGetContext(RequestContext requestContext, RuleUseRecordModel ruleUseRecordModel, IObjectData objectData, boolean preStage, boolean disableEditAmountField) {
        if (Objects.isNull(objectData)) {
            return CheckRuleTuple.ofNull();
        }
        AdaptEditResult adaptEditResult = adaptDirectReduceRule(requestContext, ruleUseRecordModel, objectData, preStage, disableEditAmountField);
        DataUpdateAndAddModel.Arg updateAddArg = DataUpdateAndAddModel.Arg.create();
        boolean invalidFlowAfterCancel = needInvalidFlowAfterCancel(requestContext.getUser());
        adaptEditResult.mergeTo(updateAddArg, invalidFlowAfterCancel);
        return CheckRuleTuple.of(updateAddArg, CheckRuleAdaptModel.of(ruleUseRecordModel.getAccountRuleUseRecordData(), ruleUseRecordModel.getDataMatchedRuleStageEnum(), adaptEditResult.changed()));
    }

    protected UnfreezeRuleTriggerTypeEnum matchUnfreezeMark() {
        return null;
    }

    protected final CheckRuleTuple<List<CheckRuleAdaptModel>> adaptUnfreezeRuleAndGetContext(RequestContext requestContext, List<RuleUseRecordModel> ruleUseRecordModelList, IObjectData objectData, boolean preStage, boolean disableEditAmountField) {
        DataUpdateAndAddModel.Arg updateAddArg = DataUpdateAndAddModel.Arg.create();
        if (Objects.isNull(objectData)) {
            return CheckRuleTuple.ofNull();
        }
        List<CheckRuleAdaptModel> unfreezeAdaptModelList = Lists.newArrayList();
        Map<String, AdaptEditResult> adaptEditResultMap = adaptUnfreezeRule(requestContext, ruleUseRecordModelList, objectData, preStage, disableEditAmountField);
        if (preStage) {
            ruleUseRecordModelList.forEach(ruleUseRecordData -> {
                AdaptEditResult adaptEditResult = adaptEditResultMap.get(ruleUseRecordData.getAccountRuleUseRecordData().getId());
                unfreezeAdaptModelList.add(CheckRuleAdaptModel.of(ruleUseRecordData.getAccountRuleUseRecordData(), ruleUseRecordData.getDataMatchedRuleStageEnum(), adaptEditResult.changed()));
            });
        } else {
            boolean invalidFlowAfterCancel = needInvalidFlowAfterCancel(requestContext.getUser());
            adaptEditResultMap.forEach((k, v) -> v.mergeTo(updateAddArg, invalidFlowAfterCancel));
        }
        return CheckRuleTuple.of(updateAddArg, unfreezeAdaptModelList);
    }


    protected IObjectData getMatchedFrozenRule(User user, IObjectDescribe objectDescribe, Map<String, Object> dataMap, List<IObjectData> checkRuleList) {
        List<IObjectData> frozenRuleByButtonList = AccountCheckManager.removeNotMatchedRuleByTypeAndButton(checkRuleList, AccountCheckRuleTypeEnum.Check_Reduce, ReduceTriggerActionEnum.Button, ObjectAction.CREATE.getButtonApiName());
        return accountCheckManager.getMatchedCheckReduceRule(user, objectDescribe, dataMap, frozenRuleByButtonList, false).orElseGet(() -> {
            List<IObjectData> frozenRuleByFieldChangeList = AccountCheckManager.removeNotMatchedRuleByTypeAndButton(checkRuleList, AccountCheckRuleTypeEnum.Check_Reduce, ReduceTriggerActionEnum.FieldChange, null);
            return accountCheckManager.getMatchedCheckReduceRule(user, objectDescribe, dataMap, frozenRuleByFieldChangeList, false).orElse(null);
        });
    }

    protected IObjectData getMatchedDirectReduceRule(User user, IObjectDescribe objectDescribe, Map<String, Object> dataMap, List<IObjectData> checkRuleList) {
        List<IObjectData> directReduceRuleByButtonList = AccountCheckManager.removeNotMatchedRuleByTypeAndButton(checkRuleList, AccountCheckRuleTypeEnum.Direct_Reduce, ReduceTriggerActionEnum.Button, ObjectAction.CREATE.getButtonApiName());
        return accountCheckManager.getMatchedDirectReduceRule(user, objectDescribe, dataMap, directReduceRuleByButtonList).orElseGet(() -> {
            List<IObjectData> directReduceRuleByFieldChangeList = AccountCheckManager.removeNotMatchedRuleByTypeAndButton(checkRuleList, AccountCheckRuleTypeEnum.Direct_Reduce, ReduceTriggerActionEnum.FieldChange, null);
            return accountCheckManager.getMatchedDirectReduceRule(user, objectDescribe, dataMap, directReduceRuleByFieldChangeList).orElse(null);
        });
    }

    protected boolean directReduceNeedBlock(IObjectData directReduceRule) {
        String tenantId = directReduceRule.getTenantId();

        //查ConfigKeyEnum.DIRECT_REDUCE_FORCE_CHECK_AMOUNT
        String status = accountCheckManager.getDirectReduceForceCheckAmountStatus(tenantId);
        log.info("directReduceNeedBlock tenantId[{}], status[{}]", tenantId, status);
        if (Objects.equals(status, DirectReduceForceCheckAmountEnum.Force.getValue())) {
            log.info("directReduceNeedBlock true tenantId[{}]", tenantId);
            return true;
        }

        return CaGrayUtil.allow(tenantId, CaGrayUtil.DIRECT_REDUCE_BLOCK_ACTION_EIS);
    }

    protected boolean needRollbackRule(IObjectData accountRuleUseRecordData, IObjectData objectData, StandardAction standardAction) {
        String lifeStatus = ObjectDataExt.of(objectData).getLifeStatusText();
        if (standardAction == StandardAction.IncrementUpdate && ObjectLifeStatus.IN_CHANGE.getCode().equals(lifeStatus)) {
            //增量更新不会触发审批流，因此生命状态不会变更；in_change状态的编辑（审批例外人），通过或者驳回数据都不会回滚，已经更新到了数据库中
            return false;
        }
        String json = accountRuleUseRecordData.get(AccountRuleUseRecordConstants.Field.CheckRule.apiName, String.class);
        IObjectData checkRuleData = new ObjectData();
        checkRuleData.fromJsonString(json);
        return RuleUtil.needRollback(checkRuleData, lifeStatus);
    }

    protected void triggerUnfreezeIgnoreException(RequestContext requestContext, IObjectData objectData, UnfreezeRuleTriggerTypeEnum unfreezeRuleTriggerTypeEnum) {
        if (Objects.isNull(unfreezeRuleTriggerTypeEnum)) {
            return;
        }
        try {
            DataUpdateAndAddModel.Arg unfreezeMatchArg = DataUpdateAndAddModel.Arg.create();
            List<IObjectData> unfreezeRuleUseRecordList = accountCheckManager.queryUnfreezeRuleUseRecord(requestContext.getUser(), objectData);

            if (UnfreezeRuleTriggerTypeEnum.AddThenFieldChangeTrigger.equals(unfreezeRuleTriggerTypeEnum)) {
                matchAndComputeUnfreeze(requestContext, objectData, ObjectAction.CREATE.getButtonApiName(), unfreezeRuleUseRecordList)
                        .mergeTo(unfreezeMatchArg);
            } else if (UnfreezeRuleTriggerTypeEnum.ConfirmReceiptThenFieldChangeTrigger.equals(unfreezeRuleTriggerTypeEnum)) {
                matchAndComputeUnfreeze(requestContext, objectData, ObjectAction.CONFIRM_RECEIPT.getButtonApiName(), unfreezeRuleUseRecordList)
                        .mergeTo(unfreezeMatchArg);
            }

            matchAndComputeUnfreeze(requestContext, objectData, ReduceTriggerActionEnum.FieldChange.getValue(), unfreezeRuleUseRecordList)
                    .mergeTo(unfreezeMatchArg);
            newCustomerAccountManager.execute(requestContext.getUser(), unfreezeMatchArg);
        } catch (Exception e) {
            log.warn("unfreezeMatch error,objectData:{},unfreezeRuleTriggerType:{}", objectData, unfreezeRuleTriggerTypeEnum, e);
            CustomerAccountLogUtil.sendAuditLog(requestContext.getUser(), "checkRulePluginUnfreeze", objectData.getDescribeApiName(), objectData.getId(), e.getMessage(), "unfreezeError");
        }
    }

    public UnfreezeResult matchAndComputeUnfreeze(RequestContext requestContext, IObjectData objectData, String buttonApiName, List<IObjectData> unfreezeRuleUseRecordList) {
        //查询冻结解冻的使用记录
        List<IObjectData> ruleUseRecordToUnfreezeList = accountCheckManager.queryCheckReduceRecordToUnfreeze(requestContext.getUser(), objectData, buttonApiName);
        ruleUseRecordToUnfreezeList = RuleHandlerUtil.removeMatchedUnfreezeRule(unfreezeRuleUseRecordList, ruleUseRecordToUnfreezeList);
        //根据条件过滤得到满足条件的使用记录
        List<IObjectData> matchedRuleUseRecordList = accountCheckManager.getMatchedFrozenRuleUseRecordToUnfreeze(requestContext.getUser(), ObjectDataExt.of(objectData).toMap(), ruleUseRecordToUnfreezeList);
        return UnfreezeCalculator.builder().accountCheckManager(accountCheckManager).newCustomerAccountManager(newCustomerAccountManager).requestContext(requestContext)
                .checkValidateStageRuleUseRecordDataList(matchedRuleUseRecordList).objectData(objectData).build().compute();
    }

    protected void triggerDirectReduceIgnoreException(RequestContext requestContext, IObjectData objectData, DirectReduceRuleToMatchModel directReduceRuleToMatchModel) {
        ObjectLifeStatus objectLifeStatus = ObjectDataExt.of(objectData).getLifeStatus();
        if (Objects.nonNull(directReduceRuleToMatchModel) && objectLifeStatus == ObjectLifeStatus.NORMAL) {
            String checkRuleId = directReduceRuleToMatchModel.getAccountCheckRuleId();
            try {
                IObjectData accountCheckRuleData = accountCheckManager.findObjectData(requestContext.getUser(), checkRuleId, AccountCheckRuleConstants.API_NAME).orElse(null);
                if (Objects.isNull(accountCheckRuleData)) {
                    return;
                }
                DirectReduceResult directReduceResult = DirectReduceFlowCalculator.builder().directReduceRuleData(accountCheckRuleData).objectData(objectData).accountCheckManager(accountCheckManager).newCustomerAccountManager(newCustomerAccountManager).requestContext(requestContext).preStage(false).build().compute();
                DataUpdateAndAddModel.Arg directReduceArg = DataUpdateAndAddModel.Arg.create();
                directReduceArg.customerAccountColumnUpdateMap(directReduceResult.getCustomerAccountUpdateColumnMap());
                directReduceArg.mergeCustomerAccountData(directReduceResult.getCustomerAccountDataList());
                directReduceArg.appendAddData(directReduceResult.getToAddAccountTransactionFlowDataList());
                directReduceArg.appendAddData(directReduceResult.getToAddAccountRuleUseRecordData(), true);
                newCustomerAccountManager.execute(requestContext.getUser(), directReduceArg);
            } catch (Exception e) {
                //finallyDo中匹配直接扣减
                log.warn("match directReduceRule error,objectData:{},checkRuleId:{}", objectData, checkRuleId, e);
                CustomerAccountLogUtil.sendAuditLog(requestContext.getUser(), "checkRulePluginDirectReduce", objectData.getDescribeApiName(), objectData.getId(), e.getMessage(), "directReduceError");
            }
        }
    }

    protected CheckRuleTuple<DataTriggerFunctionModel> frozenMatchedFinallyDo(RequestContext requestContext, StandardAction standardAction, IObjectData objectData, IObjectData accountRuleUseRecord, FrozenRuleMatchedModel frozenRuleMatchedModel) {
        DataTriggerFunctionModel dataTriggerFunctionModel = DataTriggerFunctionModel.create();
        DataUpdateAndAddModel.Arg updateAddArg = DataUpdateAndAddModel.Arg.create();
        if (needRollbackRule(accountRuleUseRecord, objectData, standardAction)) {
            boolean needInvalidFlowAfterCancel = needInvalidFlowAfterCancel(requestContext.getUser());
            FrozenRollbackResult frozenRollbackResult = rollbackMatchedFrozen(requestContext, accountRuleUseRecord, true);
            frozenRollbackResult.mergeTo(updateAddArg, needInvalidFlowAfterCancel, false);
        } else {
            List<String> frozenIds = frozenRuleMatchedModel.getFrozenList().stream().map(FrozenRuleMatchedModel.FrozenModel::getId).collect(Collectors.toList());
            List<IObjectData> frozenDataList = accountCheckManager.findObjectDataByIds(requestContext.getUser(), frozenIds, AccountFrozenRecordConstant.API_NAME);
            dataTriggerFunctionModel.appendData(ObjectAction.CREATE.getButtonApiName(), frozenDataList);
        }
        return CheckRuleTuple.of(updateAddArg, dataTriggerFunctionModel);
    }

    protected DataTriggerFunctionModel frozenMatchedBuildTriggerData(RequestContext requestContext, FrozenRuleMatchedModel frozenRuleMatchedModel) {
        DataTriggerFunctionModel dataTriggerFunctionModel = DataTriggerFunctionModel.create();
        List<String> frozenIds = frozenRuleMatchedModel.getFrozenList().stream().map(FrozenRuleMatchedModel.FrozenModel::getId).collect(Collectors.toList());
        List<IObjectData> frozenDataList = accountCheckManager.findObjectDataByIds(requestContext.getUser(), frozenIds, AccountFrozenRecordConstant.API_NAME);
        dataTriggerFunctionModel.appendData(ObjectAction.CREATE.getButtonApiName(), frozenDataList);
        return dataTriggerFunctionModel;
    }

    protected CheckRuleTuple<DataTriggerFunctionModel> directReduceMatchedFinallyDo(RequestContext requestContext, StandardAction standardAction, IObjectData objectData, IObjectData accountRuleUseRecord, DirectReduceRuleMatchedModel directReduceRuleMatchedModel) {
        DataTriggerFunctionModel dataTriggerFunctionModel = DataTriggerFunctionModel.create();
        DataUpdateAndAddModel.Arg updateAddArg = DataUpdateAndAddModel.Arg.create();
        if (needRollbackRule(accountRuleUseRecord, objectData, standardAction)) {
            DirectReduceRollbackResult directReduceRollbackResult = rollbackDirectReduce(requestContext, accountRuleUseRecord, true);
            boolean invalidFlowAfterCancel = needInvalidFlowAfterCancel(requestContext.getUser());
            directReduceRollbackResult.mergeTo(updateAddArg, invalidFlowAfterCancel, false);
        } else {
            List<String> flowIds = directReduceRuleMatchedModel.getFlowList().stream().map(DirectReduceRuleMatchedModel.FlowModel::getId).collect(Collectors.toList());
            List<IObjectData> flowDataList = accountCheckManager.findObjectDataByIds(requestContext.getUser(), flowIds, AccountTransactionFlowConst.API_NAME);
            if (StandardAction.Add == standardAction) {
                flowDataList.forEach(x -> {
                    String relateObjectApiName = x.get(AccountTransactionFlowConst.Field.RelateRecordObjectApiName.apiName, String.class);
                    String relateObjectDataId = x.get(AccountTransactionFlowConst.Field.RelateRecordObjectDataId.apiName, String.class);
                    if (Utils.SALES_ORDER_API_NAME.equals(relateObjectApiName)) {
                        String orderId = x.get(AccountTransactionFlowConst.Field.SalesOrder.apiName, String.class);
                        if (StringUtils.isEmpty(orderId)) {
                            IObjectData dataBeforeUpdate = ObjectDataExt.of(x).copy();
                            x.set(AccountTransactionFlowConst.Field.SalesOrder.apiName, relateObjectDataId);
                            updateAddArg.appendUpdateData(AccountTransactionFlowConst.Field.SalesOrder.apiName, dataBeforeUpdate, x, false);
                        }
                    } else if (Utils.CUSTOMER_PAYMENT_API_NAME.equals(relateObjectApiName)) {
                        String paymentId = x.get(AccountTransactionFlowConst.Field.Payment.apiName, String.class);
                        if (StringUtils.isEmpty(paymentId)) {
                            IObjectData dataBeforeUpdate = ObjectDataExt.of(x).copy();
                            x.set(AccountTransactionFlowConst.Field.Payment.apiName, relateObjectDataId);
                            updateAddArg.appendUpdateData(AccountTransactionFlowConst.Field.SalesOrder.apiName, dataBeforeUpdate, x, false);
                        }
                    }
                });
            }
            dataTriggerFunctionModel.appendData(ObjectAction.CREATE.getButtonApiName(), flowDataList);
        }
        return CheckRuleTuple.of(updateAddArg, dataTriggerFunctionModel);
    }

    protected DataTriggerFunctionModel directReduceMatchedBuildTriggerData(RequestContext requestContext, DirectReduceRuleMatchedModel directReduceRuleMatchedModel) {
        DataTriggerFunctionModel dataTriggerFunctionModel = DataTriggerFunctionModel.create();
        List<String> flowIds = directReduceRuleMatchedModel.getFlowList().stream().map(DirectReduceRuleMatchedModel.FlowModel::getId).collect(Collectors.toList());
        List<IObjectData> flowDataList = accountCheckManager.findObjectDataByIds(requestContext.getUser(), flowIds, AccountTransactionFlowConst.API_NAME);
        dataTriggerFunctionModel.appendData(ObjectAction.CREATE.getButtonApiName(), flowDataList);
        return dataTriggerFunctionModel;
    }

    protected void sendCheckRuleInvalidMQ(User user, CheckRuleInvalidModel checkRuleInvalidModel) {
        CheckRuleEvent<CheckRuleInvalidModel> checkRuleRetryEvent = CheckRuleEvent.of(user, checkRuleInvalidModel);
        mqProducerManager.sendCheckRuleDelayMQ(user.getTenantId(), CheckRuleTagEnum.CheckRuleInvalid.tag, checkRuleRetryEvent, RocketMqDelayLevelConstants.FIVE_SECOND);
    }

    protected void sendCheckRuleInvalidMQ(User user, List<CheckRuleInvalidModel> checkRuleInvalidModelList) {
        CheckRuleEvent<List<CheckRuleInvalidModel>> checkRuleRetryEvent = CheckRuleEvent.of(user, checkRuleInvalidModelList);
        mqProducerManager.sendCheckRuleDelayMQ(user.getTenantId(), CheckRuleTagEnum.CheckRuleBulkInvalid.tag, checkRuleRetryEvent, RocketMqDelayLevelConstants.FIVE_SECOND);
    }

    protected void fillDataByDbDataWhenIncrementUpdate(IObjectData objectData, IObjectData dbObjectData) {
        if (Objects.isNull(dbObjectData) || Objects.isNull(objectData)) {
            return;
        }
        ObjectDataExt.of(dbObjectData).toMap().forEach((k, v) -> {
            if (!objectData.containsField(k)) {
                objectData.set(k, v);
            }
        });
    }
}
