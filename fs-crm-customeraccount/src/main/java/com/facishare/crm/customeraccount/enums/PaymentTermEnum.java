package com.facishare.crm.customeraccount.enums;

public enum PaymentTermEnum {

    WeiXin("7", "微信"), Prepay("10000", "预存款"), Rebate("10001", "返利"), PrepayAndRebate("10002", "预存款与返利");

    private String type;
    private String des;

    PaymentTermEnum(String type, String des) {
        this.type = type;
        this.des = des;
    }

    public String getType() {
        return type;
    }

    public String getDes() {
        return des;
    }
}
