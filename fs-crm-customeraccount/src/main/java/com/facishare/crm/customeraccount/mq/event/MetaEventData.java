package com.facishare.crm.customeraccount.mq.event;

import lombok.Data;

import java.util.Map;

@Data
public class MetaEventData {
    /**
     * 对象名称
     */
    private String name;

    /**
     * 事件ID
     */
    private String eventId;

    private MetaEventContext context;

    /**
     * 数据ID
     */
    private String objectId;

    /**
     * 对象apiName
     */
    private String entityId;

    /**
     * 触发类型
     */
    private String triggerType;

    /**
     * 编辑字段前值
     */
    private Map<String, Object> beforeTriggerData;

    /**
     * 编辑字段后值
     */
    private Map<String, Object> afterTriggerData;
}