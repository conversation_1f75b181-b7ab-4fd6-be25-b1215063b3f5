package com.facishare.crm.customeraccount.predefine.job;

import com.facishare.crm.customeraccount.enums.JobTypeEnum;
import com.facishare.crm.customeraccount.predefine.manager.BillJobManager;
import com.facishare.paas.metadata.exception.MetadataServiceException;

import java.util.Date;

@Deprecated
public class PrepayIncrementJob extends IncrementJob {
    @Override
    public void doIncrementJob(String tenantId, Date yesterdayDate, BillJobManager billJobManager) throws MetadataServiceException {
        billJobManager.doPrepayIncrementJob(tenantId, yesterdayDate);
    }

    @Override
    public JobTypeEnum jobType() {
        return JobTypeEnum.PREPAY;
    }
}
