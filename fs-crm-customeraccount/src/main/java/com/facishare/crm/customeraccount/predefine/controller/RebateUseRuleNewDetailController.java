package com.facishare.crm.customeraccount.predefine.controller;

import com.facishare.crm.customeraccount.constants.RebateUseRuleConstants;
import com.facishare.crmcommon.manager.CustomerRangeManager;
import com.facishare.paas.appframework.core.predef.controller.StandardNewDetailController;
import com.facishare.paas.metadata.util.SpringUtil;

public class RebateUseRuleNewDetailController extends StandardNewDetailController {
    private CustomerRangeManager customerRangeManager;

    @Override
    protected void before(Arg arg) {
        customerRangeManager = SpringUtil.getContext().getBean(CustomerRangeManager.class);
        super.before(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        customerRangeManager.packData(controllerContext.getUser(), result.getData(), RebateUseRuleConstants.Field.CustomerRange.apiName);
        return result;
    }
}
