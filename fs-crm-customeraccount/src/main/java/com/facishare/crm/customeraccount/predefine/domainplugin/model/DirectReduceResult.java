package com.facishare.crm.customeraccount.predefine.domainplugin.model;

import com.facishare.paas.metadata.api.IObjectData;
import lombok.Builder;
import lombok.Getter;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Getter
@Builder
public class DirectReduceResult {
    private IObjectData toAddAccountRuleUseRecordData;
    private List<IObjectData> toAddAccountTransactionFlowDataList;
    private Map<String, Map<String, Object>> customerAccountUpdateColumnMap;
    private List<String> customerAccountUpdateFieldList;
    private List<IObjectData> customerAccountDataList;

    public String getCheckRuleUseRecordId() {
        return Objects.nonNull(toAddAccountRuleUseRecordData) ? toAddAccountRuleUseRecordData.getId() : null;
    }
}
