package com.facishare.crm.customeraccount.predefine.handler.checkrule;

import com.facishare.crm.customeraccount.constants.*;
import com.facishare.crm.customeraccount.predefine.handler.RuleHandlerUtil;
import com.facishare.crm.customeraccount.util.ObjectDataUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.IdUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
public class FrozenAndUnfreezeAmountChanger implements AmountChanger {
    private final User user;
    private final IObjectData customerAccountData;
    private final IObjectData oldFrozenData;
    private final List<IObjectData> unfreezeDataListOfFrozenData;
    private final IObjectData toFrozenData;
    private final IObjectData toFlowData;
    private final Map<String, IObjectData> flowDataMap;

    //temp variables
    private final String objectApiName;
    private final String objectDataId;
    private final String customerAccountId;
    private final BigDecimal toFrozenAmount;
    private final BigDecimal oldFrozenAmount;
    private final BigDecimal toUnfreezeAmount;
    private final BigDecimal oldUnfreezeAmount;
    private final BigDecimal changedFrozenAmount;
    private final BigDecimal changedUnfreezeAmount;

    //result
    private final List<IObjectData> toAddFrozenDataList = Lists.newArrayList();
    private final List<IObjectData> toAddUnfreezeDataList = Lists.newArrayList();
    private final List<IObjectData> toAddFlowDataList = Lists.newArrayList();
    private final List<IObjectData> toDeletedFrozenDataList = Lists.newArrayList();
    private final List<IObjectData> toDeletedUnfreezeDataList = Lists.newArrayList();
    private final List<IObjectData> toDeletedFlowDataList = Lists.newArrayList();
    private final Map<String, Object> customerAccountColumnMap = Maps.newHashMap();

    public FrozenAndUnfreezeAmountChanger(User user, IObjectData objectData, IObjectData customerAccountData, IObjectData oldFrozenData, List<IObjectData> unfreezeDataList, IObjectData toFrozenData, IObjectData toFlowData, Map<String, IObjectData> flowDataMap) {
        this.user = user;
        this.customerAccountData = customerAccountData;
        this.oldFrozenData = oldFrozenData;
        this.unfreezeDataListOfFrozenData = unfreezeDataList;
        this.toFrozenData = toFrozenData;
        this.toFlowData = toFlowData;
        this.flowDataMap = flowDataMap;

        objectApiName = objectData.getDescribeApiName();
        objectDataId = objectData.getId();
        customerAccountId = customerAccountData.getId();

        toFrozenAmount = Objects.isNull(toFrozenData) ? BigDecimal.ZERO : toFrozenData.get(AccountFrozenRecordConstant.Field.FreezeAmount.apiName, BigDecimal.class, BigDecimal.ZERO);
        oldFrozenAmount = Objects.isNull(oldFrozenData) ? BigDecimal.ZERO : oldFrozenData.get(AccountFrozenRecordConstant.Field.FreezeAmount.apiName, BigDecimal.class, BigDecimal.ZERO);

        toUnfreezeAmount = Objects.isNull(toFlowData) ? BigDecimal.ZERO : toFlowData.get(AccountTransactionFlowConst.Field.ExpenseAmount.apiName, BigDecimal.class, BigDecimal.ZERO);
        oldUnfreezeAmount = unfreezeDataListOfFrozenData.stream().filter(x -> {
            String unfreezeObjectApiName = x.get(UnfreezeDetailConstant.Field.UnfreezeObjectApiName.apiName, String.class);
            String unfreezeObjectDataId = x.get(UnfreezeDetailConstant.Field.UnfreezeObjectDataId.apiName, String.class);
            return StringUtils.equals(unfreezeObjectApiName, objectApiName) && StringUtils.equals(unfreezeObjectDataId, objectDataId);
        }).map(x -> x.get(UnfreezeDetailConstant.Field.UnfreezeAmount.apiName, BigDecimal.class, BigDecimal.ZERO)).findAny().orElse(BigDecimal.ZERO);

        changedFrozenAmount = toFrozenAmount.subtract(oldFrozenAmount);
        changedUnfreezeAmount = toUnfreezeAmount.subtract(oldUnfreezeAmount);
    }

    @Override
    public AmountChangerResult frozenEqAndReduceGt() {
        //oldFrozenData为null时，表示之前没有冻结，而此时解冻金额无论如何变化也不需要处理
        if (Objects.isNull(oldFrozenData)) {
            return generateResult();
        }
        BigDecimal totalUnfreezeAmountOfFrozenData = BigDecimal.ZERO;
        IObjectData curUnfreezeData = null;
        for (IObjectData unfreezeDataOfFrozenData : unfreezeDataListOfFrozenData) {
            String unfreezeObjectApiName = unfreezeDataOfFrozenData.get(UnfreezeDetailConstant.Field.UnfreezeObjectApiName.apiName, String.class);
            String unfreezeObjectDataId = unfreezeDataOfFrozenData.get(UnfreezeDetailConstant.Field.UnfreezeObjectDataId.apiName, String.class);
            if (StringUtils.equals(unfreezeObjectApiName, objectApiName) && StringUtils.equals(unfreezeObjectDataId, objectDataId)) {
                curUnfreezeData = unfreezeDataOfFrozenData;
            }
            BigDecimal unfreezeAmountOfFrozenData = unfreezeDataOfFrozenData.get(UnfreezeDetailConstant.Field.UnfreezeAmount.apiName, BigDecimal.class, BigDecimal.ZERO);
            totalUnfreezeAmountOfFrozenData = totalUnfreezeAmountOfFrozenData.add(unfreezeAmountOfFrozenData);
        }

        BigDecimal leftUnfreezeAmount = oldFrozenAmount.subtract(totalUnfreezeAmountOfFrozenData);
        if (leftUnfreezeAmount.compareTo(BigDecimal.ZERO) <= 0) {
            //无剩余待解冻金额，即使解冻金额变大，也不需要处理
            return generateResult();
        }

        BigDecimal newUnfreezeAmount = computeNewToUnfreezeAmount(totalUnfreezeAmountOfFrozenData);
        //newUnfreezeAmount 不可能 <=0
        if (newUnfreezeAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return generateResult();
        }
        toFlowData.set(AccountTransactionFlowConst.Field.ExpenseAmount.apiName, newUnfreezeAmount);
        toFlowData.set(AccountTransactionFlowConst.Field.CustomerAccount.apiName, customerAccountId);

        toAddFlowDataList.add(toFlowData);
        toAddUnfreezeDataList.add(RuleHandlerUtil.getUnfreezeDetailData(user, oldFrozenData.getId(), toFlowData, objectApiName, objectDataId));

        if (Objects.nonNull(curUnfreezeData)) {
            String flowId = curUnfreezeData.get(UnfreezeDetailConstant.Field.AccountTransactionFlow.apiName, String.class);
            IObjectData oldFlowData = flowDataMap.get(flowId);
            toDeletedFlowDataList.add(oldFlowData);
            toDeletedUnfreezeDataList.add(curUnfreezeData);
        }
        //客户账户余额变更
        computeCustomerAccountUpdate(changedFrozenAmount, newUnfreezeAmount.subtract(oldUnfreezeAmount));
        return generateResult();
    }

    @Override
    public AmountChangerResult frozenEqAndReduceLt() {
        if (toUnfreezeAmount.compareTo(BigDecimal.ZERO) < 0) {
            //解冻扣减金额不能小于0
            throw new ValidateException(I18N.text(CAI18NKey.FREEZE_OR_EXPENSE_AMOUNT_NOT_ZERO));
        }

        if (Objects.isNull(oldFrozenData)) {
            //如果没有冻结记录，则不用处理
            return generateResult();
        }

        IObjectData curUnfreezeData = getCurUnfreezeData().orElse(null);
        if (Objects.isNull(curUnfreezeData)) {
            //解冻金额变小场景下，curUnfreezeData如果为空，那么compare<0，走不到这里
            return generateResult();
        }

        toDeletedUnfreezeDataList.add(curUnfreezeData);
        String flowId = curUnfreezeData.get(UnfreezeDetailConstant.Field.AccountTransactionFlow.apiName, String.class);
        IObjectData oldFlowData = flowDataMap.get(flowId);
        toDeletedFlowDataList.add(oldFlowData);

        //toUnfreezeAmount == 0 时，新的流水与解冻不需要新建
        if (toUnfreezeAmount.compareTo(BigDecimal.ZERO) > 0) {
            toFlowData.set(AccountTransactionFlowConst.Field.CustomerAccount.apiName, customerAccountId);
            IObjectData toUnfreezeData = RuleHandlerUtil.getUnfreezeDetailData(user, oldFrozenData.getId(), toFlowData, objectApiName, objectDataId);
            toUnfreezeData.set(UnfreezeDetailConstant.Field.AccountTransactionFlow.apiName, toFlowData.getId());
            toAddFlowDataList.add(toFlowData);
            toAddUnfreezeDataList.add(toUnfreezeData);
        }

        //客户账户余额变更
        computeCustomerAccountUpdate(changedFrozenAmount, toUnfreezeAmount.subtract(oldUnfreezeAmount));
        return generateResult();
    }

    @Override
    public AmountChangerResult frozenGtAndReduceEq() {
        ObjectDataUtil.validateCustomerAccountBalance(customerAccountData, changedFrozenAmount);
        //冻结金额变大时，toFrozenData肯定不为null
        toFrozenData.set(AccountFrozenRecordConstant.Field.CustomerAccountId.apiName, customerAccountId);
        toFrozenData.setId(IdUtil.generateId());
        toAddFrozenDataList.add(toFrozenData);

        //oldFrozenData为null，则说明没有解冻明细；而解冻金额不变，说明变更后也没有解冻明细
        if (Objects.nonNull(oldFrozenData)) {
            toDeletedFrozenDataList.add(oldFrozenData);

            if (CollectionUtils.notEmpty(unfreezeDataListOfFrozenData)) {
                List<IObjectData> newUnfreezeDataList = unfreezeDataListOfFrozenData.stream().map(x -> {
                    IObjectData newUnfreezeData = ObjectDataExt.of(x).copy();
                    newUnfreezeData.setId(null);
                    newUnfreezeData.set(UnfreezeDetailConstant.Field.AccountFrozenRecordId.apiName, toFrozenData.getId());
                    return newUnfreezeData;
                }).collect(Collectors.toList());
                toDeletedUnfreezeDataList.addAll(unfreezeDataListOfFrozenData);
                toAddUnfreezeDataList.addAll(newUnfreezeDataList);
            }
            //由于解冻金额不变，支出流水与冻结记录/解冻明细无关联，所以支出流水不用变
        }
        computeCustomerAccountUpdate(changedFrozenAmount, changedUnfreezeAmount);
        return generateResult();
    }

    @Override
    public AmountChangerResult frozenGtAndReduceGt() {
        ObjectDataUtil.validateCustomerAccountBalance(customerAccountData, changedFrozenAmount);

        toFrozenData.setId(IdUtil.generateId());
        toFrozenData.set(AccountFrozenRecordConstant.Field.CustomerAccountId.apiName, customerAccountId);
        toAddFrozenDataList.add(toFrozenData);

        if (Objects.nonNull(oldFrozenData)) {
            toDeletedFrozenDataList.add(oldFrozenData);
            BigDecimal totalUnfreezeAmount = BigDecimal.ZERO;
            for (IObjectData unfreezeDataOfFrozenData : unfreezeDataListOfFrozenData) {
                BigDecimal unfreezeAmount = unfreezeDataOfFrozenData.get(UnfreezeDetailConstant.Field.UnfreezeAmount.apiName, BigDecimal.class, BigDecimal.ZERO);
                totalUnfreezeAmount = totalUnfreezeAmount.add(unfreezeAmount);
                toDeletedUnfreezeDataList.add(unfreezeDataOfFrozenData);
                if (isCurUnfreeData(unfreezeDataOfFrozenData)) {
                    String flowId = unfreezeDataOfFrozenData.get(UnfreezeDetailConstant.Field.AccountTransactionFlow.apiName, String.class);
                    IObjectData oldFlowData = flowDataMap.get(flowId);
                    toDeletedFlowDataList.add(oldFlowData);
                } else {
                    IObjectData newUnfreezeData = ObjectDataExt.of(unfreezeDataOfFrozenData).copy();
                    newUnfreezeData.setId(null);
                    newUnfreezeData.set(UnfreezeDetailConstant.Field.AccountFrozenRecordId.apiName, toFrozenData.getId());
                    toAddUnfreezeDataList.add(newUnfreezeData);
                    //流水金额不变，不需要重新生成
                }
            }

            BigDecimal newToUnfreezeAmount = computeNewToUnfreezeAmount(totalUnfreezeAmount);
            //newToUnfreezeAmount = 0 不存在
            if (newToUnfreezeAmount.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ValidateException(I18N.text(CAI18NKey.FREEZE_OR_EXPENSE_AMOUNT_NOT_ZERO));
            }

            toFlowData.set(AccountTransactionFlowConst.Field.CustomerAccount.apiName, customerAccountId);
            toFlowData.set(AccountTransactionFlowConst.Field.ExpenseAmount.apiName, newToUnfreezeAmount);
            toAddFlowDataList.add(toFlowData);
            IObjectData toUnfreezeData = RuleHandlerUtil.getUnfreezeDetailData(user, toFrozenData.getId(), toFlowData, objectApiName, objectDataId);
            toUnfreezeData.set(UnfreezeDetailConstant.Field.AccountTransactionFlow.apiName, toFlowData.getId());
            toAddUnfreezeDataList.add(toUnfreezeData);
            //客户账户余额
            computeCustomerAccountUpdate(changedFrozenAmount, newToUnfreezeAmount.subtract(oldUnfreezeAmount));
        } else {
            BigDecimal newUnfreezeAmount = toUnfreezeAmount;
            if (toUnfreezeAmount.compareTo(toFrozenAmount) > 0) {
                newUnfreezeAmount = toFrozenAmount;
            }
            toFlowData.set(AccountTransactionFlowConst.Field.ExpenseAmount.apiName, newUnfreezeAmount);
            toFlowData.set(AccountTransactionFlowConst.Field.CustomerAccount.apiName, customerAccountId);
            toAddFlowDataList.add(toFlowData);
            IObjectData toUnfreezeData = RuleHandlerUtil.getUnfreezeDetailData(user, toFrozenData.getId(), toFlowData, objectApiName, objectDataId);
            toAddUnfreezeDataList.add(toUnfreezeData);
            //客户账户余额变更
            computeCustomerAccountUpdate(changedFrozenAmount, newUnfreezeAmount.subtract(oldUnfreezeAmount));
        }
        return generateResult();
    }

    @Override
    public AmountChangerResult frozenGtAndReduceLt() {
        if (toUnfreezeAmount.compareTo(BigDecimal.ZERO) < 0) {
            throw new ValidateException(I18N.text(CAI18NKey.FREEZE_OR_EXPENSE_AMOUNT_NOT_ZERO));
        }
        ObjectDataUtil.validateCustomerAccountBalance(customerAccountData, changedFrozenAmount);

        toFrozenData.setId(IdUtil.generateId());
        toFrozenData.set(AccountFrozenRecordConstant.Field.CustomerAccountId.apiName, customerAccountId);
        toAddFrozenDataList.add(toFrozenData);

        if (Objects.nonNull(oldFrozenData)) {
            toDeletedFrozenDataList.add(oldFrozenData);

            for (IObjectData unfreezeDataOfFrozenData : unfreezeDataListOfFrozenData) {
                toDeletedUnfreezeDataList.add(unfreezeDataOfFrozenData);
                if (isCurUnfreeData(unfreezeDataOfFrozenData)) {
                    String flowId = unfreezeDataOfFrozenData.get(UnfreezeDetailConstant.Field.AccountTransactionFlow.apiName, String.class);
                    IObjectData oldFlowData = flowDataMap.get(flowId);
                    toDeletedFlowDataList.add(oldFlowData);
                } else {
                    IObjectData newUnfreezeData = ObjectDataExt.of(unfreezeDataOfFrozenData).copy();
                    newUnfreezeData.setId(null);
                    newUnfreezeData.set(UnfreezeDetailConstant.Field.AccountFrozenRecordId.apiName, toFrozenData.getId());
                    toAddUnfreezeDataList.add(newUnfreezeData);
                }
            }

            if (Objects.nonNull(toFlowData) && toUnfreezeAmount.compareTo(BigDecimal.ZERO) > 0) {
                toFlowData.set(AccountTransactionFlowConst.Field.CustomerAccount.apiName, customerAccountId);
                IObjectData toUnfreezeData = RuleHandlerUtil.getUnfreezeDetailData(user, toFrozenData.getId(), toFlowData, objectApiName, objectDataId);
                toUnfreezeData.set(UnfreezeDetailConstant.Field.AccountTransactionFlow.apiName, toFlowData.getId());
                toAddUnfreezeDataList.add(toUnfreezeData);
                toAddFlowDataList.add(toFlowData);
            }
        } else {
            //之前没有冻结，解冻金额不可能再变小了，不存在此场景
            log.warn("not exist scene");
        }
        computeCustomerAccountUpdate(changedFrozenAmount, changedUnfreezeAmount);
        return generateResult();
    }

    /**
     * 冻结金额变小，解冻金额不变（包括解冻金额为0的不变和不为0的不变）
     */
    @Override
    public AmountChangerResult frozenLtAndReduceEq() {
        if (toFrozenAmount.compareTo(BigDecimal.ZERO) < 0) {
            //冻结金额不能小于0
            throw new ValidateException(I18N.text(CAI18NKey.FREEZE_OR_EXPENSE_AMOUNT_NOT_ZERO));
        }
        toDeletedFrozenDataList.add(oldFrozenData);
        if (toFrozenAmount.compareTo(BigDecimal.ZERO) > 0) {
            toFrozenData.setId(IdUtil.generateId());
            toFrozenData.set(AccountFrozenRecordConstant.Field.CustomerAccountId.apiName, customerAccountId);
            toAddFrozenDataList.add(toFrozenData);
        }
        BigDecimal totalUnfreezeAmount = BigDecimal.ZERO;
        for (IObjectData unfreezeDataOfFrozenData : unfreezeDataListOfFrozenData) {
            BigDecimal unfreezeAmount = unfreezeDataOfFrozenData.get(UnfreezeDetailConstant.Field.UnfreezeAmount.apiName, BigDecimal.class, BigDecimal.ZERO);
            totalUnfreezeAmount = totalUnfreezeAmount.add(unfreezeAmount);
            toDeletedUnfreezeDataList.add(unfreezeDataOfFrozenData);
            //由于解冻金额不变，因此解冻金额直接重新生成即可；流水不需要变化
            if (Objects.nonNull(toFrozenData)) {
                IObjectData newUnfreezeData = ObjectDataExt.of(unfreezeDataOfFrozenData).copy();
                newUnfreezeData.setId(null);
                newUnfreezeData.set(UnfreezeDetailConstant.Field.AccountFrozenRecordId.apiName, toFrozenData.getId());
                toAddUnfreezeDataList.add(newUnfreezeData);
            }
        }
        //解冻不变时，冻结变小于已解冻金额不支持
        if (toFrozenAmount.compareTo(totalUnfreezeAmount) < 0) {
            throw new ValidateException(I18N.text(CAI18NKey.UNFREEZE_AMOUNT_GT_FROZEN_AMOUNT));
        }
        //冻结金额变小，解冻金额不变
        computeCustomerAccountUpdate(changedFrozenAmount, BigDecimal.ZERO);
        return generateResult();
    }

    @Override
    public AmountChangerResult frozenLtAndReduceGt() {
        if (toFrozenAmount.compareTo(BigDecimal.ZERO) < 0) {
            //冻结金额不能小于0
            throw new ValidateException(I18N.text(CAI18NKey.FREEZE_OR_EXPENSE_AMOUNT_NOT_ZERO));
        }

        if (Objects.isNull(oldFrozenData)) {
            //不存在此场景
            return generateResult();
        }
        toDeletedFrozenDataList.add(oldFrozenData);

        if (toFrozenAmount.compareTo(BigDecimal.ZERO) > 0) {
            toFrozenData.setId(IdUtil.generateId());
            toFrozenData.set(AccountFrozenRecordConstant.Field.CustomerAccountId.apiName, customerAccountId);
            toAddFrozenDataList.add(toFrozenData);
        }

        BigDecimal totalUnfreezeAmount = BigDecimal.ZERO;
        for (IObjectData unfreezeDataOfFrozenData : unfreezeDataListOfFrozenData) {
            BigDecimal unfreezeAmount = unfreezeDataOfFrozenData.get(UnfreezeDetailConstant.Field.UnfreezeAmount.apiName, BigDecimal.class, BigDecimal.ZERO);
            totalUnfreezeAmount = totalUnfreezeAmount.add(unfreezeAmount);
            toDeletedUnfreezeDataList.add(unfreezeDataOfFrozenData);
            if (isCurUnfreeData(unfreezeDataOfFrozenData)) {
                String flowId = unfreezeDataOfFrozenData.get(UnfreezeDetailConstant.Field.AccountTransactionFlow.apiName, String.class);
                IObjectData oldFlowData = flowDataMap.get(flowId);
                toDeletedFlowDataList.add(oldFlowData);
            } else if (Objects.nonNull(toFrozenData)) {
                IObjectData newUnfreezeData = ObjectDataExt.of(unfreezeDataOfFrozenData).copy();
                newUnfreezeData.setId(null);
                newUnfreezeData.set(UnfreezeDetailConstant.Field.AccountFrozenRecordId.apiName, toFrozenData.getId());
                toAddUnfreezeDataList.add(newUnfreezeData);
            }
        }
        BigDecimal newToUnfreezeAmount = computeNewToUnfreezeAmount(totalUnfreezeAmount);
        //toFrozenAmount==0时，不生成冻结，解冻也不需要生成了
        if (newToUnfreezeAmount.compareTo(BigDecimal.ZERO) > 0 && Objects.nonNull(toFrozenData)) {
            toFlowData.set(AccountTransactionFlowConst.Field.ExpenseAmount.apiName, newToUnfreezeAmount);
            toFlowData.set(AccountTransactionFlowConst.Field.CustomerAccount.apiName, customerAccountId);
            toAddFlowDataList.add(toFlowData);
            IObjectData toUnfreezeData = RuleHandlerUtil.getUnfreezeDetailData(user, toFrozenData.getId(), toFlowData, objectApiName, objectDataId);
            toUnfreezeData.set(UnfreezeDetailConstant.Field.AccountTransactionFlow.apiName, toFlowData.getId());
            toAddUnfreezeDataList.add(toUnfreezeData);
        }
        computeCustomerAccountUpdate(changedFrozenAmount, newToUnfreezeAmount.subtract(oldUnfreezeAmount));
        return generateResult();
    }

    @Override
    public AmountChangerResult frozenLtAndReduceLt() {
        if (Objects.isNull(oldFrozenData)) {
            //此场景不存在
            return generateResult();
        }

        if (toFrozenAmount.compareTo(BigDecimal.ZERO) < 0) {
            throw new ValidateException(I18N.text(CAI18NKey.FREEZE_OR_EXPENSE_AMOUNT_NOT_ZERO));
        }
        if (toUnfreezeAmount.compareTo(BigDecimal.ZERO) < 0) {
            throw new ValidateException(I18N.text(CAI18NKey.FREEZE_OR_EXPENSE_AMOUNT_NOT_ZERO));
        }
        //新的冻结金额为0时，只需要删除就的冻结记录
        toDeletedFrozenDataList.add(oldFrozenData);
        if (toFrozenAmount.compareTo(BigDecimal.ZERO) > 0) {
            toFrozenData.setId(IdUtil.generateId());
            toFrozenData.set(AccountFrozenRecordConstant.Field.CustomerAccountId.apiName, customerAccountId);
            toAddFrozenDataList.add(toFrozenData);
        }

        BigDecimal oldTotalUnfreezeAmount = BigDecimal.ZERO;
        for (IObjectData unfreezeDataOfFrozenData : unfreezeDataListOfFrozenData) {
            BigDecimal unfreezeAmount = unfreezeDataOfFrozenData.get(UnfreezeDetailConstant.Field.UnfreezeAmount.apiName, BigDecimal.class, BigDecimal.ZERO);
            oldTotalUnfreezeAmount = oldTotalUnfreezeAmount.add(unfreezeAmount);
            if (isCurUnfreeData(unfreezeDataOfFrozenData)) {
                String flowId = unfreezeDataOfFrozenData.get(UnfreezeDetailConstant.Field.AccountTransactionFlow.apiName, String.class);
                IObjectData flowData = flowDataMap.get(flowId);
                toDeletedFlowDataList.add(flowData);
            } else if (Objects.nonNull(toFrozenData)) {
                IObjectData newUnfreezeData = ObjectDataExt.of(unfreezeDataOfFrozenData).copy();
                newUnfreezeData.setId(null);
                newUnfreezeData.set(UnfreezeDetailConstant.Field.AccountFrozenRecordId.apiName, toFrozenData.getId());
                toAddUnfreezeDataList.add(newUnfreezeData);
            }
            toDeletedUnfreezeDataList.add(unfreezeDataOfFrozenData);
        }
        if (toFrozenAmount.compareTo(oldTotalUnfreezeAmount.subtract(oldUnfreezeAmount)) < 0) {
            //冻结金额不能小于解冻金额
            throw new ValidateException(I18N.text(CAI18NKey.FROZEN_AMOUNT_LT_UNFREEZE_AMOUNT));
        }

        BigDecimal newToUnfreezeAmount = computeNewToUnfreezeAmount(oldTotalUnfreezeAmount);
        if (newToUnfreezeAmount.compareTo(BigDecimal.ZERO) > 0 && Objects.nonNull(toFrozenData)) {
            //由于 toFrozenAmount > oldTotalUnfreezeAmount.subtract(oldUnfreezeAmount).add(toUnfreezeAmount)，
            //所以 toFrozenAmount - oldTotalUnfreezeAmount.subtract(oldUnfreezeAmount) > toUnfreezeAmount
            toFlowData.set(AccountTransactionFlowConst.Field.CustomerAccount.apiName, customerAccountId);
            toFlowData.set(AccountTransactionFlowConst.Field.ExpenseAmount.apiName, newToUnfreezeAmount);
            toAddUnfreezeDataList.add(RuleHandlerUtil.getUnfreezeDetailData(user, toFrozenData.getId(), toFlowData, objectApiName, objectDataId));
            toAddFlowDataList.add(toFlowData);
        }
        computeCustomerAccountUpdate(changedFrozenAmount, newToUnfreezeAmount.subtract(oldUnfreezeAmount));
        return generateResult();
    }

    private BigDecimal computeNewToUnfreezeAmount(BigDecimal oldTotalUnfreezeAmount) {
        //冻结金额不能小于已解冻金额
        if (toFrozenAmount.compareTo(oldTotalUnfreezeAmount.subtract(oldUnfreezeAmount)) < 0) {
            throw new ValidateException(I18N.text(CAI18NKey.FROZEN_AMOUNT_LT_UNFREEZE_AMOUNT));
        }
        BigDecimal newToUnfreezeAmount = toUnfreezeAmount;
        if (toFrozenAmount.compareTo(oldTotalUnfreezeAmount.subtract(oldUnfreezeAmount).add(toUnfreezeAmount)) < 0) {
            //冻结金额不能小于解冻金额
            newToUnfreezeAmount = toFrozenAmount.subtract(oldTotalUnfreezeAmount.subtract(oldUnfreezeAmount));
        }
        return newToUnfreezeAmount;
    }

    private AmountChangerResult generateResult() {
        AmountChangerResult result = new AmountChangerResult();
        result.setToAddFrozenDataList(filterAmountGtZero(toAddFrozenDataList, AccountFrozenRecordConstant.Field.FreezeAmount.apiName));
        result.setToAddFlowDataList(filterAmountGtZero(toAddFlowDataList, AccountTransactionFlowConst.Field.ExpenseAmount.apiName));
        result.setToAddUnfreezeDataList(filterAmountGtZero(toAddUnfreezeDataList, UnfreezeDetailConstant.Field.UnfreezeAmount.apiName));
        result.setToDeletedFrozenDataList(toDeletedFrozenDataList);
        result.setToDeletedUnfreezeDataList(toDeletedUnfreezeDataList);
        result.setToDeletedFlowDataList(toDeletedFlowDataList);
        result.setCustomerAccountColumnMap(customerAccountColumnMap);
        result.setCustomerAccountData(customerAccountData);
        result.setActualChanged(true);
        return result;
    }

    private List<IObjectData> filterAmountGtZero(List<IObjectData> dataList, String fieldApiName) {
        if (CollectionUtils.empty(dataList)) {
            return Lists.newArrayList();
        }
        return dataList.stream().filter(x -> {
            BigDecimal amount = x.get(fieldApiName, BigDecimal.class, BigDecimal.ZERO);
            return amount.compareTo(BigDecimal.ZERO) > 0;
        }).collect(Collectors.toList());
    }

    private boolean isCurUnfreeData(IObjectData unfreezeData) {
        String unfreezeObjectApiName = unfreezeData.get(UnfreezeDetailConstant.Field.UnfreezeObjectApiName.apiName, String.class);
        String unfreezeObjectDataId = unfreezeData.get(UnfreezeDetailConstant.Field.UnfreezeObjectDataId.apiName, String.class);
        return StringUtils.equals(unfreezeObjectApiName, objectApiName) && StringUtils.equals(unfreezeObjectDataId, objectDataId);
    }

    private Optional<IObjectData> getCurUnfreezeData() {
        IObjectData curUnfreezeData = null;
        for (IObjectData unfreezeDataOfFrozenData : unfreezeDataListOfFrozenData) {
            String unfreezeObjectApiName = unfreezeDataOfFrozenData.get(UnfreezeDetailConstant.Field.UnfreezeObjectApiName.apiName, String.class);
            String unfreezeObjectDataId = unfreezeDataOfFrozenData.get(UnfreezeDetailConstant.Field.UnfreezeObjectDataId.apiName, String.class);
            if (StringUtils.equals(unfreezeObjectApiName, objectApiName) && StringUtils.equals(unfreezeObjectDataId, objectDataId)) {
                curUnfreezeData = unfreezeDataOfFrozenData;
                break;
            }
        }
        return Optional.ofNullable(curUnfreezeData);
    }

    private void computeCustomerAccountUpdate(BigDecimal changedFrozenAmount, BigDecimal changedUnfreezeAmount) {
        RuleHandlerUtil.computeCustomerAccount(customerAccountColumnMap, NewCustomerAccountConstants.Field.OccupiedAmount.apiName, changedFrozenAmount.subtract(changedUnfreezeAmount));
        RuleHandlerUtil.computeCustomerAccount(customerAccountColumnMap, NewCustomerAccountConstants.Field.AvailableBalance.apiName, changedFrozenAmount.negate());
        RuleHandlerUtil.computeCustomerAccount(customerAccountColumnMap, NewCustomerAccountConstants.Field.AccountBalance.apiName, changedUnfreezeAmount.negate());
    }

}
