package com.facishare.crm.customeraccount.predefine.service;

import com.facishare.crm.customeraccount.constants.*;
import com.facishare.crm.customeraccount.enums.CreditTypeEnum;
import com.facishare.crm.customeraccount.predefine.manager.*;
import com.facishare.crm.customeraccount.predefine.service.dto.EmptyResult;
import com.facishare.crm.openapi.Utils;
import com.facishare.crmcommon.util.SearchUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@ServiceModule("customer_account_data_fix")
@Component
public class CustomerAccountDataFixService {
    @Autowired
    private ObjectDataServiceImpl objectDataService;
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private PrepayDetailManager prepayDetailManager;
    @Autowired
    private NewCustomerAccountManager newCustomerAccountManager;
    @Autowired
    private RebateIncomeDetailManager rebateIncomeDetailManager;
    @Autowired
    private CustomerAccountManager customerAccountManager;
    @Autowired
    private CreditFileManager creditFileManager;

    @ServiceMethod("prepay_standard_import_by_ids")
    public Set<String> fixPrepayByStandardImport(ServiceContext serviceContext, IdArg arg) {
        Set<String> failIds = Sets.newHashSet();
        String tenantId = arg.getTenantId();
        List<String> ids = arg.getIds();
        if (CollectionUtils.empty(ids) || StringUtils.isEmpty(tenantId)) {
            return failIds;
        }
        List<IObjectData> prepayDetailDataList = serviceFacade.findObjectDataByIds(tenantId, ids, Utils.PREPAY_DETAIL_API_NAME);
        if (CollectionUtils.empty(prepayDetailDataList)) {
            return failIds;
        }
        User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
        prepayDetailDataList.forEach(prepayDetailData -> {
            try {
                prepayDetailManager.updateBalance(user, prepayDetailData, ObjectLifeStatus.INEFFECTIVE.getCode());
            } catch (Exception e) {
                log.warn("fixPrepayByStandardImport updatePrepayBalance error,data:{}", prepayDetailData, e);
                failIds.add(prepayDetailData.getId());
            }
        });
        return failIds;
    }

    @ServiceMethod("rebate_income_fix_by_ids")
    public Set<String> fixRebateIncomeData(ServiceContext serviceContext, IdArg arg) {
        Set<String> failIds = Sets.newHashSet();
        String tenantId = arg.getTenantId();
        List<String> ids = arg.getIds();
        String oldLifeStatus = arg.getOldLifeStatus();
        List<IObjectData> rebateIncomeDetail = serviceFacade.findObjectDataByIds(tenantId, ids, Utils.REBATE_INCOME_DETAIL_API_NAME);
        User user = User.systemUser(tenantId);
        for (IObjectData incomeData : rebateIncomeDetail) {
            String newLifeStatus = ObjectDataExt.of(incomeData).getLifeStatusText();
            log.info("tenantId:{},incomeId:{},oldLifeStatus:{},newLifeStatus:{}", tenantId, incomeData.getId(), oldLifeStatus, newLifeStatus);
            try {
                rebateIncomeDetailManager.updateBalanceForLifeStatus(user, incomeData, oldLifeStatus, newLifeStatus);
            } catch (Exception e) {
                log.warn("incomeUpdateBalance error,tenantId:{},incomeId:{}", tenantId, incomeData.getId(), e);
                failIds.add(incomeData.getId());
            }
        }
        return failIds;
    }

    @ServiceMethod("credit_file_edit_fix")
    public Set<String> fixCreditFileEdit(ServiceContext serviceContext, FixCreditFileArg arg) {
        String tenantId = serviceContext.getTenantId();
        IObjectData oldObjectData = arg.getOldObjectData().toObjectData();
        String id = oldObjectData.getId();
        if (StringUtils.isEmpty(id)) {
            throw new ValidateException("_id is null");
        }
        User user = User.systemUser(tenantId);
        IObjectData objectData = serviceFacade.findObjectData(user, id, CreditFileConstants.API_NAME);
        if (arg.getNewObjectData()!=null){
            ObjectDataExt.of(arg.getNewObjectData()).toMap().forEach(objectData::set);
        }
        boolean active = creditFileManager.isCreditActive(objectData);
        if (!active) {
            throw new ValidateException("credit not active");
        }

        String customerId = objectData.get(CreditFileConstants.Field.Customer.apiName, String.class);
        oldObjectData.set(CreditFileConstants.Field.Customer.apiName, customerId);

        String creditType = objectData.get(CreditFileConstants.Field.CreditType.apiName, String.class);
        BigDecimal credit;
        if (CreditTypeEnum.TemporaryCredit.getValue().equals(creditType)) {
            credit = oldObjectData.get(CreditFileConstants.Field.TemporaryCreditLimit.apiName, BigDecimal.class);
        } else {
            credit = oldObjectData.get(CreditFileConstants.Field.CreditQuota.apiName, BigDecimal.class);
        }
        if (Objects.isNull(credit)) {
            throw new ValidateException("credit limit is null");
        }
        customerAccountManager.updateCreditQuotaByEditCredit(user, oldObjectData, objectData);
        return Sets.newHashSet();
    }

    @ServiceMethod("query_prepay_by_create_time")
    public Map<String, List<ObjectDataDocument>> queryImportPrepayByCreateTime(ServiceContext serviceContext, FixPrepayByImportArg arg) {
        List<String> tenantIds = arg.getTenantIds();
        Long start = arg.getStart();
        Long end = arg.getEnd();
        Map<String, List<ObjectDataDocument>> resultMap = Maps.newHashMap();
        if (CollectionUtils.empty(tenantIds) || Objects.isNull(start) || Objects.isNull(end)) {
            return resultMap;
        }
        String sqlFormat = "select id,name,amount,record_type,life_status,is_deleted from prepay_detail where tenant_id='%s' and create_time > %d and create_time < %d offset %d limit %d";
        long limit = 500, offset = arg.getOffset();
        for (String tenantId : tenantIds) {
            List<ObjectDataDocument> resultList = Lists.newArrayList();
            String sql = String.format(sqlFormat, tenantId, start, end, offset, limit);
            List<Map> dataList = null;
            try {
                dataList = objectDataService.findBySql(tenantId, sql);
            } catch (Exception e) {
                log.warn("", e);
            }
            if (CollectionUtils.empty(dataList)) {
                break;
            }
            resultList.addAll(ObjectDataDocument.ofList(dataList.stream().map(x -> {
                x.put("_id", x.get("id"));
                return new ObjectData(x);
            }).collect(Collectors.toList())));
            resultMap.put(tenantId, resultList);
        }
        return resultMap;
    }

    @ServiceMethod("account_transaction_flow_customer_account_id")
    public Map<String, Object> fillCustomerAccountId(ServiceContext serviceContext) {
        int limit = 200;
        int offset = 0;
        int size;
        do {
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            searchTemplateQuery.setOffset(offset);
            searchTemplateQuery.setLimit(limit);
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterIsNull(filters, AccountTransactionFlowConst.Field.CustomerAccount.apiName);
            searchTemplateQuery.setFilters(filters);
            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(serviceContext.getUser(), AccountTransactionFlowConst.API_NAME, searchTemplateQuery);
            List<IObjectData> objectDataList = queryResult.getData();
            if (CollectionUtils.empty(objectDataList)) {
                break;
            }
            objectDataList.forEach(x -> {
                String customerId = x.get(AccountTransactionFlowConst.Field.Customer.apiName, String.class);
                String fundAccountId = x.get(AccountTransactionFlowConst.Field.FundAccount.apiName, String.class);
                IObjectData newCustomerAccountData = newCustomerAccountManager.getOrCreateNewCustomerAccount(serviceContext.getRequestContext(), fundAccountId, customerId, x);
                x.set(AccountTransactionFlowConst.Field.CustomerAccount.apiName, newCustomerAccountData.getId());
                serviceFacade.updateObjectData(serviceContext.getUser(), x);
            });
            size = objectDataList.size();
        } while (size == limit);
        return Maps.newHashMap();
    }

    @ServiceMethod("fix_merge_new_customer_account")
    public EmptyResult fixMergeNewCustomerAccount(ServiceContext serviceContext, IdArg arg) {
        String tenantId = arg.getTenantId();
        List<String> customerIds = arg.getIds();
        if (StringUtils.isEmpty(tenantId)) {
            throw new ValidateException("parameter error");
        }

        String sqlFormat = "SELECT customer_id, fund_account_id, cnt FROM (" +
                "SELECT count(customer_id) as cnt, customer_id, fund_account_id " +
                "FROM new_customer_account " +
                "WHERE tenant_id = '%s' and life_status != 'invalid' " +
                "GROUP BY customer_id, fund_account_id ) t " +
                "WHERE t.cnt > 1 ORDER BY customer_id OFFSET %d LIMIT %d";

        int limit = 200;
        int offset = 0;
        int size = 0;
        do {
            String sql = String.format(sqlFormat, tenantId, offset, limit);
            log.info("fixMergeNewCustomerAccount offset={} sql={}", offset, sql);
            List<Map> dataList = null;
            try {
                dataList = objectDataService.findBySql(tenantId, sql);
            } catch (Exception e) {
                log.error("fixMergeNewCustomerAccount findBySql error:", e);
                break;
            }
            dataList.forEach(record -> {
                String customerId = (String) record.get("customer_id");
                String fundAccountId = (String) record.get("fund_account_id");
                Long count = (Long) record.get("cnt");

                if (CollectionUtils.empty(customerIds) || customerIds.contains(customerId)) {
                    log.info("fixMergeNewCustomerAccount start: offset={} customerId={} fundAccountId={} count={}", offset, customerId, fundAccountId, count);
                    mergeNewCustomerAccount(tenantId, customerId, fundAccountId);
                    log.info("fixMergeNewCustomerAccount end: offset={} customerId={} fundAccountId={} count={}", offset, customerId, fundAccountId, count);
                }
            });
            size = dataList.size();
        } while (size == limit);

        return new EmptyResult();
    }

    private void mergeNewCustomerAccount(String tenantId, String customerId, String fundAccountId) {
        User user = User.systemUser(tenantId);
        // 查询客户账户余额
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(100);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, NewCustomerAccountConstants.Field.Customer.apiName, customerId);
        SearchUtil.fillFilterEq(filters, NewCustomerAccountConstants.Field.FundAccount.apiName, fundAccountId);
        searchTemplateQuery.setFilters(filters);
        OrderBy orderBy = new OrderBy(SystemConstants.Field.CreateTime.apiName,true);
        List<OrderBy> orders = Lists.newArrayList(orderBy);
        searchTemplateQuery.setOrders(orders);

        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, NewCustomerAccountConstants.API_NAME, searchTemplateQuery);
        List<IObjectData> objectDataList = queryResult.getData();
        if (objectDataList.size() < 2) {
            log.info("fixMergeNewCustomerAccount error: customerId={} fundAccountId={} count={}", customerId, fundAccountId, objectDataList.size());
            return;
        }

        IObjectData toBeUpdatedData = objectDataList.get(0);
        String mainCustomerAccountId = toBeUpdatedData.getId();

        List<String> toBeInvalidCustomerAccountIds = objectDataList.stream().map(IObjectData::getId).filter(id -> !StringUtils.equals(id, mainCustomerAccountId)).collect(Collectors.toList());
        List<IObjectData> toBeInvalidData = objectDataList.stream().filter(data -> !StringUtils.equals(data.getId(), mainCustomerAccountId)).collect(Collectors.toList());

        BigDecimal diffAccountBalance = toBeInvalidData.stream().map(data -> data.get(NewCustomerAccountConstants.Field.AccountBalance.apiName, BigDecimal.class, BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal diffOccupiedAmount = toBeInvalidData.stream().map(data -> data.get(NewCustomerAccountConstants.Field.OccupiedAmount.apiName, BigDecimal.class, BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal diffAvailableBalance = toBeInvalidData.stream().map(data -> data.get(NewCustomerAccountConstants.Field.AvailableBalance.apiName, BigDecimal.class, BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);

        // 根据多余的客户账户余额 查找收支流水，并更新流水的客户账户余额字段
        batchUpdateTransactionFlows(user, mainCustomerAccountId, toBeInvalidCustomerAccountIds);
        batchUpdateFrozenRecords(user, mainCustomerAccountId, toBeInvalidCustomerAccountIds);

        // 作废多余的客户账户余额，并更新保留的客户账户余额相关金额字段
        serviceFacade.bulkInvalid(toBeInvalidData, user);
//        serviceFacade.bulkDelete(toBeInvalidData, user);
        log.info("fixMergeNewCustomerAccount bulkInvalidNewCustomerAccount: toBeInvalidCustomerAccountIds={} ", toBeInvalidCustomerAccountIds);

        if (diffAccountBalance.compareTo(BigDecimal.ZERO) >0 || diffOccupiedAmount.compareTo(BigDecimal.ZERO) > 0 || diffAvailableBalance.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal accountBalance = toBeUpdatedData.get(NewCustomerAccountConstants.Field.AccountBalance.apiName, BigDecimal.class);
            BigDecimal occupiedAmount = toBeUpdatedData.get(NewCustomerAccountConstants.Field.OccupiedAmount.apiName, BigDecimal.class);
            BigDecimal availableBalance = toBeUpdatedData.get(NewCustomerAccountConstants.Field.AvailableBalance.apiName, BigDecimal.class);

            toBeUpdatedData.set(NewCustomerAccountConstants.Field.AccountBalance.apiName, accountBalance.add(diffAccountBalance));
            toBeUpdatedData.set(NewCustomerAccountConstants.Field.OccupiedAmount.apiName, occupiedAmount.add(diffOccupiedAmount));
            toBeUpdatedData.set(NewCustomerAccountConstants.Field.AvailableBalance.apiName, availableBalance.add(diffAvailableBalance));
            serviceFacade.updateObjectData(user, toBeUpdatedData);
            log.info("fixMergeNewCustomerAccount updateMainNewCustomerAccount: mainCustomerAccountId={} diffAccountBalance={} diffOccupiedAmount={} diffAvailableBalance={}",
                    mainCustomerAccountId, diffAccountBalance, diffOccupiedAmount, diffAvailableBalance);
        }
    }

    private void batchUpdateTransactionFlows(User user, String mainCustomerAccountId, List<String> toBeInvalidCustomerAccountIds) {
        List<String> updateFieldList = Lists.newArrayList(AccountTransactionFlowConst.Field.CustomerAccount.apiName);
        int limit = 500;
        int offset = 0;
        int size = 0;
        do {
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            searchTemplateQuery.setOffset(offset);
            searchTemplateQuery.setLimit(limit);
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterIn(filters, AccountTransactionFlowConst.Field.CustomerAccount.apiName, toBeInvalidCustomerAccountIds);
            searchTemplateQuery.setFilters(filters);
            OrderBy orderByCreateTime = new OrderBy(SystemConstants.Field.CreateTime.apiName,true);
            OrderBy orderByCustomerAccount = new OrderBy(AccountTransactionFlowConst.Field.CustomerAccount.apiName,true);
            List<OrderBy> orders = Lists.newArrayList(orderByCustomerAccount, orderByCreateTime);
            searchTemplateQuery.setOrders(orders);

            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, AccountTransactionFlowConst.API_NAME, searchTemplateQuery);
            List<IObjectData> objectDataList = queryResult.getData();
            if (CollectionUtils.empty(objectDataList)) {
                log.info("fixMergeNewCustomerAccount batchUpdateTransactionFlows: offset={} not found", offset);
                return;
            }

            List<String> transactionFlowNames = Lists.newArrayList();
            for (IObjectData objectData : objectDataList) {
                objectData.set(AccountTransactionFlowConst.Field.CustomerAccount.apiName, mainCustomerAccountId);
                transactionFlowNames.add(objectData.getName());
            }

            serviceFacade.batchUpdateByFields(user, objectDataList, updateFieldList);
            log.info("fixMergeNewCustomerAccount batchUpdateTransactionFlows: offset={} transactionFlowNames={}", offset, transactionFlowNames);
            size = objectDataList.size();
        } while (size == limit);
    }

    private void batchUpdateFrozenRecords(User user, String mainCustomerAccountId, List<String> toBeInvalidCustomerAccountIds) {
        List<String> updateFieldList = Lists.newArrayList(AccountFrozenRecordConstant.Field.CustomerAccountId.apiName);
        int limit = 500;
        int offset = 0;
        int size = 0;
        do {
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            searchTemplateQuery.setOffset(offset);
            searchTemplateQuery.setLimit(limit);
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterIn(filters, AccountFrozenRecordConstant.Field.CustomerAccountId.apiName, toBeInvalidCustomerAccountIds);
            searchTemplateQuery.setFilters(filters);
            OrderBy orderByCreateTime = new OrderBy(SystemConstants.Field.CreateTime.apiName,true);
            OrderBy orderByCustomerAccount = new OrderBy(AccountFrozenRecordConstant.Field.CustomerAccountId.apiName,true);
            List<OrderBy> orders = Lists.newArrayList(orderByCustomerAccount, orderByCreateTime);
            searchTemplateQuery.setOrders(orders);

            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, AccountFrozenRecordConstant.API_NAME, searchTemplateQuery);
            List<IObjectData> objectDataList = queryResult.getData();
            if (CollectionUtils.empty(objectDataList)) {
                log.info("fixMergeNewCustomerAccount batchUpdateTransactionFlows: offset={} not found", offset);
                return;
            }

            List<String> frozenRecordNames = Lists.newArrayList();
            for (IObjectData objectData : objectDataList) {
                objectData.set(AccountFrozenRecordConstant.Field.CustomerAccountId.apiName, mainCustomerAccountId);
                frozenRecordNames.add(objectData.getName());
            }

            serviceFacade.batchUpdateByFields(user, objectDataList, updateFieldList);
            log.info("fixMergeNewCustomerAccount batchUpdateFrozenRecords: offset={} frozenRecordNames={}", offset, frozenRecordNames);
            size = objectDataList.size();
        } while (size == limit);
    }

    @Data
    public static class FixPrepayByImportArg {
        private List<String> tenantIds;
        private Long start;
        private Long end;
        private long offset = 0;
    }

    @Data
    public static class IdArg {
        private String tenantId;
        private List<String> ids;

        private String oldLifeStatus;
    }

    @Data
    public static class FixCreditFileArg {
        private ObjectDataDocument oldObjectData;
        private ObjectDataDocument newObjectData;
    }
}
