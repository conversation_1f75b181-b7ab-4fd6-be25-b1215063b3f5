package com.facishare.crm.customeraccount.predefine.domainplugin.model;

import com.facishare.crm.customeraccount.model.DataUpdateAndAddModel;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.Builder;
import lombok.Getter;

import java.util.List;
import java.util.Map;

@Builder
@Getter
public class UnfreezeResult {
    private List<IObjectData> toAddAccountRuleUseRecordDataList;
    private List<IObjectData> toAddAccountTransactionFlowDataList;
    private List<IObjectData> toAddUnfreezeDetailDataList;
    private Map<String, Map<String, Object>> customerAccountUpdateFieldColumnMap;
    private List<String> customerAccountUpdateFieldList;
    private List<IObjectData> customerAccountDataList;

    public void mergeTo(DataUpdateAndAddModel.Arg arg){
        arg.appendAddData(toAddAccountRuleUseRecordDataList,true);
        arg.appendAddData(toAddAccountTransactionFlowDataList,false);
        arg.appendAddData(toAddUnfreezeDetailDataList,true);
        arg.customerAccountColumnUpdateMap(customerAccountUpdateFieldColumnMap);
        arg.mergeCustomerAccountData(customerAccountDataList);
    }
}
