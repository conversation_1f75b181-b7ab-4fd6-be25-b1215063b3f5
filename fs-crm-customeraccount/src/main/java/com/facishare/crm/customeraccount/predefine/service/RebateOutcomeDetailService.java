package com.facishare.crm.customeraccount.predefine.service;

import com.facishare.crm.customeraccount.predefine.service.dto.CreateModel;
import com.facishare.crm.customeraccount.predefine.service.dto.ListByIdModel;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.jaxrs.annotation.InnerAPI;

@InnerAPI
@ServiceModule("rebate_outcome_detail")
@Deprecated
public interface RebateOutcomeDetailService {
    @ServiceMethod("list_by_rebate_income_id")
    ListByIdModel.Result listByRebateIncomeId(ListByIdModel.RebateOutcomeArg arg, ServiceContext serviceContext);

    CreateModel.Result createRebateOutcomeDetailByCustomFunction(User user, ObjectDataDocument objectData);
}
