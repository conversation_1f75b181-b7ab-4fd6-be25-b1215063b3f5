package com.facishare.crm.payment.predefine.domainplugin;

import com.facishare.crm.customeraccount.predefine.domainplugin.EmptyAddActionDomainPlugin;
import com.facishare.crm.customeraccount.predefine.manager.CaConfigManager;
import com.facishare.crm.payment.predefine.manager.PayStatementManager;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
//@DomainProvider(name = "pay_statement_Add")
@ServiceModule("pay_statement_add")
public class PayStatementAddActionDomainPlugin extends EmptyAddActionDomainPlugin {

    @Autowired
    private PayStatementManager payStatementManager;
    @Autowired
    private CaConfigManager caConfigManager;

    @ServiceMethod("before")
    public Result beforeService(ServiceContext context, Arg arg) {
        log.info("PayStatementAddActionDomainPlugin beforeService context[{}], arg[{}]", context, arg);
        String tenantId = context.getTenantId();

        //【订单直接支付开关】（如果这个开关关了，插件实例停用了，回款二维码收款也不管）
        boolean isSalesOrderPayDirectlyOpen = caConfigManager.isSalesOrderPayDirectlyOpen(tenantId);
        if (!isSalesOrderPayDirectlyOpen) {
            log.info("PayStatementAddActionDomainPlugin beforeService isSalesOrderPayDirectlyOpen[{}], context[{}], arg[{}]", isSalesOrderPayDirectlyOpen, context, arg);
            return new Result();
        }

        ObjectDataDocument objectDataToUpdate = payStatementManager.getNeedDataToUpdate(context.getTenantId(), arg);
        Result result = new Result();
        result.setObjectDataToUpdate(objectDataToUpdate);
        log.info("PayStatementAddActionDomainPlugin beforeService context[{}], arg[{}], result[{}]", context, arg, result);
        return result;
    }

    @ServiceMethod("after")
    public Result afterService(ServiceContext context, Arg arg) {
        log.info("PayStatementAddActionDomainPlugin afterService context[{}], arg[{}]", context, arg);

        payStatementManager.createPaymentAndUpdatePaymentCachePayStatement(context.getTenantId(), arg.getObjectData().toObjectData());

        return new Result();
    }

    @ServiceMethod("finally")
    public Result finallyService(ServiceContext context, Arg arg) {
        log.info("PayStatementAddActionDomainPlugin finallyService context[{}], arg[{}]", context, arg);
        payStatementManager.createPaymentAndUpdatePaymentCachePayStatement(context.getTenantId(), arg.getObjectData().toObjectData());
        return new Result();
    }
}