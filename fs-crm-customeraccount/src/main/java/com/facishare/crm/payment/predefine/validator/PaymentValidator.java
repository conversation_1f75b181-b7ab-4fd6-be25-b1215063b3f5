package com.facishare.crm.payment.predefine.validator;

import com.facishare.crm.customeraccount.constants.*;
import com.facishare.crm.customeraccount.enums.ConfigKeyEnum;
import com.facishare.crm.customeraccount.enums.PayTypeEnum;
import com.facishare.crm.customeraccount.predefine.manager.FAccountAuthorizationManager;
import com.facishare.crm.customeraccount.predefine.manager.NewCustomerAccountManager;
import com.facishare.crm.customeraccount.predefine.validator.Validator;
import com.facishare.crm.customeraccount.util.CaGrayUtil;
import com.facishare.crm.customeraccount.util.ObjectDataUtil;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.payment.enums.PaySourceEnum;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.Strings;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
public class PaymentValidator implements Validator {
    FAccountAuthorizationManager fAccountAuthorizationManager = SpringUtil.getContext().getBean(FAccountAuthorizationManager.class);

    NewCustomerAccountManager newCustomerAccountManager = SpringUtil.getContext().getBean(NewCustomerAccountManager.class);

    @Override
    public void validate(Context context, BaseObjectSaveAction.Arg arg, IObjectDescribe objectDescribe) {
        IObjectData objectData = arg.getObjectData().toObjectData();
        ObjectDataUtil.fillAmountIfNotPresent(arg);
        RequestContext requestContext = context.getRequestContext();
        User user = requestContext.getUser();
        boolean receivableEnable = context.isConfigTrue(ConfigKeyEnum.ACCOUNT_RECEIVABLE_CONFIG.key, ConfigKeyEnum.ACCOUNT_RECEIVABLE_CONFIG.enabledValue);
        String payType = objectData.get(PaymentConstants.Field.PayType.apiName, String.class);

        SelectOneFieldDescribe payTypeFieldDescribe = (SelectOneFieldDescribe) objectDescribe.getFieldDescribe(PaymentConstants.Field.PayType.apiName);
        if (Objects.nonNull(payTypeFieldDescribe) && !isPaymentPay(requestContext) && (PayTypeEnum.CodeCollection.value.equals(payType) || PayTypeEnum.OnlineCharge.value.equals(payType))) {
            //如果不是回款支付来源的校验，则禁止支付方式为在线支付/二维码收款的回款
            String optionLabel = payTypeFieldDescribe.getOption(payType).orElseThrow(() -> new ValidateException("pay_type param error")).getLabel();
            throw new ValidateException(I18N.text(PaymentI18NKey.PAY_TYPE_NOT_SUPPORT, optionLabel));
        } else if (StringUtils.isEmpty(payType)) {
            //防止payment_type设置默认值为二维码收款/线上支付
            objectData.set(PaymentConstants.Field.PayType.apiName, null);
        }
        Map<String, List<ObjectDataDocument>> details = arg.getDetails();
        List<ObjectDataDocument> orderPaymentDataList = details.getOrDefault(Utils.ORDER_PAYMENT_API_NAME, Lists.newArrayList());
        if (!ObjectDataUtil.isSupportRedPayment(objectData)) {
            ObjectDataUtil.validatePaymentAmount(user, objectData, orderPaymentDataList.stream().map(ObjectDataDocument::toObjectData).collect(Collectors.toList()));
        }
        if (receivableEnable && CollectionUtils.notEmpty(orderPaymentDataList)) {
            log.warn("receivableEnable orderPaymentDataList not empty tenantId:{}", user.getTenantId());
        }
        //校验红蓝回款对应正负金额
        if (ObjectDataUtil.isSupportRedPayment(objectData)) {
            ObjectDataUtil.checkPaymentCollectionType(objectData, ObjectDataDocument.ofDataList(orderPaymentDataList));
        }
        Set<String> orderIds = Sets.newHashSet();
        for (ObjectDataDocument orderPaymentData : orderPaymentDataList) {
            IObjectData orderPayment = ObjectDataExt.of(orderPaymentData);
            String orderId = orderPayment.get(OrderPaymentConstants.Field.SalesOrder.apiName, String.class, "");
            orderIds.add(orderId);
        }
        objectData.set(PaymentConstants.Field.OrderIdText.apiName, Joiner.on(",").join(orderIds));
        String paymentTerm = objectData.get(PaymentConstants.Field.PaymentTerm.apiName, String.class);
        SelectOneFieldDescribe paymentField = (SelectOneFieldDescribe) objectDescribe.getFieldDescribe(PaymentConstants.Field.PaymentTerm.apiName);
        boolean noneMatch = StringUtils.isNotEmpty(paymentTerm) && paymentField.getSelectOptions().stream().noneMatch(x -> !x.isNotUsable() && x.getValue().equals(paymentTerm));
        if (noneMatch) {
            throw new ValidateException(I18N.text(PaymentI18NKey.OPTION_NOT_USABLE_OR_NOT_EXIST, paymentField.getLabel()));
        }
        //如果有回款明细，则不能入账(汉和可以)
        boolean enterIntoAccount = objectData.get(PaymentConstants.Field.EnterIntoAccount.apiName, Boolean.class, Boolean.FALSE);
        boolean enablePaymentEnterAccount = context.isConfigTrue(ConfigKeyEnum.PAYMENT_ENTER_ACCOUNT_CONFIG_KEY.key, ConfigKeyEnum.PAYMENT_ENTER_ACCOUNT_CONFIG_KEY.enabledValue);
        boolean enablePaymentWithDetailEnterAccount = context.isConfigTrue(ConfigKeyEnum.PAYMENT_WITH_DETAIL_ENTER_ACCOUNT.key, ConfigKeyEnum.PAYMENT_WITH_DETAIL_ENTER_ACCOUNT.enabledValue);
        String accountId = objectData.get(PaymentConstants.Field.Customer.apiName, String.class, null);

        // 若开启回款入账功能，校验账户是否在账户授权明细中
        if (enablePaymentEnterAccount && enterIntoAccount) {
            //入账时，入账账户必填
            String fundAccountId = objectData.get(PaymentConstants.Field.FundAccount.apiName, String.class);
            if (StringUtils.isEmpty(fundAccountId)) {
                throw new ValidateException(I18N.text(CAI18NKey.FUND_ACCOUNT_REQUIRED));
            }
            if (CollectionUtils.notEmpty(orderPaymentDataList) && !enablePaymentWithDetailEnterAccount) {
                throw new ValidateException(I18N.text(PaymentI18NKey.PAYMENT_ENTER_INTO_ACCOUNT_CANNOT_ADD_DETAIL));
            }
            //在主对象选择【是否入账】为“是”时，提示“未关联任何客户，暂无法入账！”
            if (Strings.isNullOrEmpty(accountId)) {
                throw new ValidateException(I18N.text(PaymentI18NKey.PAYMENT_NO_SELECT_ACCOUNT_CANNOT_ENTER_INTO_ACCOUNT));
            }
            fAccountAuthorizationManager.checkFundAccountInFundAuthorizedDetail(context.getUser(), objectData.get(PaymentConstants.Field.FundAccount.apiName, String.class));
            IObjectData fAccountAuthorizationData = newCustomerAccountManager.getPaymentEnterAccountAuthData(requestContext);
            String enterAmountFieldName = fAccountAuthorizationData.get(FAccountAuthorizationConstants.Field.EntryAmountFieldApiName.apiName, String.class);
            BigDecimal revenueAmount = objectData.get(enterAmountFieldName, BigDecimal.class, BigDecimal.ZERO);
            if (revenueAmount.compareTo(BigDecimal.ZERO) == 0 || (revenueAmount.compareTo(BigDecimal.ZERO) < 0 && !CaGrayUtil.allow(PaymentConstants.API_NAME, user.getTenantId(), CaGrayUtil.ENABLE_PAYMENT_NEGATIVE_ENTER_ACCOUNT))) {
                throw new ValidateException(I18N.text(PaymentI18NKey.PAYMENT_AMOUNT_MUST_GT_ZERO));
            }
        }
        //如果未选择客户，从对象不可创建
        if (Strings.isNullOrEmpty(accountId) && CollectionUtils.notEmpty(orderPaymentDataList)) {
            throw new ValidateException(I18N.text(PaymentI18NKey.PAYMENT_NO_ACCOUNT_CANNOT_ADD_DETAIL));
        }
        if (!enterIntoAccount) {
            objectData.set(PaymentConstants.Field.FundAccount.apiName, null);
        }
    }

    private boolean isPaymentPay(RequestContext requestContext) {
        PaySourceEnum paySourceEnum = requestContext.getAttribute(PaymentCacheConst.PAYMENT_PAY_SOURCE);
        if (Objects.nonNull(paySourceEnum)) {
            //对于payment_pay/service中调用PaymentValidator
            return true;
        }
        String modelName = requestContext.getModelName();
        String peerName = requestContext.getPeerName();
        if ("FmcgSales".equalsIgnoreCase(modelName) || "DingHuoTong".equalsIgnoreCase(modelName) || "DingHuoTong".equalsIgnoreCase(peerName)) {
            return true;
        }
        return StringUtils.isNotEmpty(peerName) && peerName.startsWith("fs-sail-order");
    }
}
