package com.facishare.crm.payment.predefine.manager;

import com.facishare.crm.customeraccount.constants.*;
import com.facishare.crm.customeraccount.enums.ConfigKeyEnum;
import com.facishare.crm.customeraccount.enums.PayStatusEnum;
import com.facishare.crm.customeraccount.enums.PayTypeEnum;
import com.facishare.crm.customeraccount.enums.PaymentTermEnum;
import com.facishare.crm.customeraccount.mq.producer.MQProducerManager;
import com.facishare.crm.customeraccount.predefine.manager.CaConfigManager;
import com.facishare.crm.customeraccount.predefine.manager.CommonManager;
import com.facishare.crm.customeraccount.util.CaGrayUtil;
import com.facishare.crm.customeraccount.util.ConfigCenter;
import com.facishare.crm.customeraccount.util.ExecuteUtil;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.payment.enums.PaySourceEnum;
import com.facishare.crm.payment.predefine.service.handler.DefaultPaymentPayHandler;
import com.facishare.crmcommon.constants.CommonSalesOrderConstants;
import com.facishare.crmcommon.util.ObjectDataUtil;
import com.facishare.crmcommon.util.SearchUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class PaymentCacheManager extends CommonManager {

    @Autowired
    private PayManager payManager;
    @Autowired
    private DefaultPaymentPayHandler defaultPaymentPayHandler;
    @Autowired
    private CaConfigManager caConfigManager;
    @Autowired
    private ConfigService configService;
    @Autowired
    private MQProducerManager mqProducerManager;

    public List<IObjectData> getDataListOrderByCreateTimeDesc(String tenantId, String sourceObjectApiName, String sourceObjectDataId, List<String> payStatusList) {
        User admin = new User(tenantId, "-10000");

        List<IFilter> filterList = Lists.newArrayList();
        SearchUtil.fillFilterEq(filterList, PaymentCacheConst.Field.SourceObjectApiName.apiName, sourceObjectApiName);
        SearchUtil.fillFilterEq(filterList, PaymentCacheConst.Field.SourceObjectDataId.apiName, sourceObjectDataId);
        SearchUtil.fillFilterIn(filterList, PaymentCacheConst.Field.PayStatus.apiName, payStatusList);
        SearchUtil.fillFilterEq(filterList, SystemConstants.Field.LifeStatus.apiName, SystemConstants.LifeStatus.Normal.value);

        List<OrderBy> orders = Lists.newArrayList();
        orders.add(SearchUtil.order(SystemConstants.Field.CreateTime.apiName, false));

        QueryResult<IObjectData> queryResult = searchQuery(admin, PaymentCacheConst.OBJECT_API_NAME, filterList, orders, 0, 1000);
        return queryResult.getData();
    }

    public IObjectData addPaymentCache(RequestContext requestContext, IObjectData objectData, String customerId) {
        User user = requestContext.getUser();

        User systemUser = User.systemUser(requestContext.getTenantId());
        String configJson = configService.findTenantConfig(systemUser, ConfigKeyEnum.SALES_ORDER_PAY_DIRECTLY_CONFIG.key);
        Long amount = getSalesOrderPayDirectlyPayAmount(objectData, configJson);

        String sourceObjectApiName = objectData.getDescribeApiName();
        String sourceObjectDataId = objectData.getId();

        IObjectData paymentCacheData = defaultPaymentPayHandler.getPaymentCacheData(user);
        paymentCacheData.set(PaymentCacheConst.Field.PayType.apiName, PayTypeEnum.OnlineCharge.value);
        paymentCacheData.set(PaymentCacheConst.Field.PayStatus.apiName, PayStatusEnum.PAYING.value);
        paymentCacheData.set(PaymentCacheConst.Field.Amount.apiName, amount);
        paymentCacheData.set(PaymentCacheConst.Field.CustomerId.apiName, customerId);
        paymentCacheData.set(PaymentCacheConst.Field.JsonContent.apiName, "{}"); //必填
        if (Objects.equals(sourceObjectApiName, CommonSalesOrderConstants.API_NAME)) {
            paymentCacheData.set(PaymentCacheConst.Field.OrderIds.apiName, Lists.newArrayList(sourceObjectDataId));
        }

        paymentCacheData.set(PaymentCacheConst.Field.SourceObjectApiName.apiName, sourceObjectApiName);
        paymentCacheData.set(PaymentCacheConst.Field.SourceObjectDataId.apiName, sourceObjectDataId);

        String targetObjectApiName = caConfigManager.getSalesOrderPayDirectlyPayStatementTargetObjectApiNameByConfig(configJson);
        paymentCacheData.set(PaymentCacheConst.Field.TargetObjectApiName.apiName, targetObjectApiName);
        /**
         * TargetObjectDataId 和 PaymentId
         * 如果是订单支付：在PayStatementAddActionDomainPlugin的after()中, 生成回款后补充, 具体在：com.facishare.crm.newpayment.manager.predefine.PaymentCacheManager#updatePaymentId
         * 如果是回款支付：在支付回调fs-sail-order                      中, 生成回款后补充, 具体在com.facishare.sail.resource.PaymentPayCallBackResource#doCallBack
         */
        //  paymentCacheData.set(PaymentCacheConst.Field.TargetObjectDataId.apiName, "");

        return ExecuteUtil.execute(() -> {
            IObjectData result = serviceFacade.saveObjectData(user, paymentCacheData);
            if (CaGrayUtil.supportPaymentCallStrategy(requestContext.getTenantId())) {
                mqProducerManager.sendPaymentPayCancelMessage(user, result.getId());
            }
            return result;
        });
    }

    /**
     * 订单直接支付的金额
     */
    private Long getSalesOrderPayDirectlyPayAmount(IObjectData objectData, String salesOrderPayDirectlyConfigJson) {
        String payFieldApiName = caConfigManager.getSalesOrderPayDirectlyPayFieldApiNameByConfig(salesOrderPayDirectlyConfigJson);
        BigDecimal payAmount = objectData.get(payFieldApiName, BigDecimal.class);
        return payManager.toPayAmount(payAmount);
    }

    private Long getSalesOrderPayDirectlyPayAmount(String tenantId, IObjectData objectData) {
        User systemUser = User.systemUser(tenantId);
        String configJson = configService.findTenantConfig(systemUser, ConfigKeyEnum.SALES_ORDER_PAY_DIRECTLY_CONFIG.key);
        return getSalesOrderPayDirectlyPayAmount(objectData, configJson);
    }

    /**
     * 更新 PaymentCacheConst.Field.Amount.apiName
     *
     * 比如对于订单，Amount的值目前是从receivable_amount取的，这是一个计算字段，值可能变化
     */
    public void updateAmount(String tenantId, IObjectData paymentCache, IObjectData objectData) {
        Long newAmount = getSalesOrderPayDirectlyPayAmount(tenantId, objectData);
        Long oldAmount = paymentCache.get(PaymentCacheConst.Field.Amount.apiName, Long.class);

        if (Objects.equals(newAmount, oldAmount)) {
            log.info("updateAmount newAmount = oldAmount paymentCacheId[{}], oldAmount[{}], newAmount[{}]", paymentCache.getId(), oldAmount, newAmount);
            return;
        }

        User systemUser = new User(tenantId, "-10000");
        List<String> updateFieldList = Lists.newArrayList(PaymentCacheConst.Field.Amount.apiName);
        paymentCache.set(PaymentCacheConst.Field.Amount.apiName, newAmount);

        try {
            serviceFacade.batchUpdateByFields(systemUser, Lists.newArrayList(paymentCache), updateFieldList);
            log.info("updateAmount paymentCacheId[{}], oldAmount[{}], newAmount[{}]", paymentCache.getId(), oldAmount, newAmount);
        } catch (Exception e) {
            log.warn("updateAmount paymentCacheId[{}], oldAmount[{}], newAmount[{}]", paymentCache.getId(), oldAmount, newAmount, e);
            throw e;
        }
    }

    /**
     * 有了paymentId, 更新paymentCache的PaymentId
     * 如果支付的结果是生成回款，也更新TargetObjectDataId
     */
    public void updatePaymentId(String tenantId, IObjectData paymentCache, String paymentId) {
        String oldPaymentId = paymentCache.get(PaymentCacheConst.Field.PaymentId.apiName, String.class);
        String oldTargetObjectDataId = paymentCache.get(PaymentCacheConst.Field.TargetObjectDataId.apiName, String.class);
        if (Objects.equals(oldPaymentId, paymentId) && Objects.equals(oldTargetObjectDataId, paymentId)) {
            return;
        }

        User systemUser = new User(tenantId, "-10000");
        List<String> updateFieldList = Lists.newArrayList(PaymentCacheConst.Field.PaymentId.apiName);
        paymentCache.set(PaymentCacheConst.Field.PaymentId.apiName, paymentId);

        //目标对象是回款,才更新TargetObjectDataId
        String targetObjectApiName = caConfigManager.getSalesOrderPayDirectlyPayStatementTargetObjectApiName(tenantId);
        if (Objects.equals(targetObjectApiName, PaymentConstants.API_NAME)) {
            updateFieldList.add(PaymentCacheConst.Field.TargetObjectDataId.apiName);
            paymentCache.set(PaymentCacheConst.Field.TargetObjectDataId.apiName, paymentId);
        }

        try {
            serviceFacade.batchUpdateByFields(systemUser, Lists.newArrayList(paymentCache), updateFieldList);
            log.info("updatePaymentId paymentCacheId[{}], paymentId[{}]", paymentCache.getId(), paymentId);
        } catch (Exception e) {
            log.warn("updatePaymentId paymentCacheId[{}], paymentId[{}], paymentCache[{}]", paymentCache.getId(), paymentId, paymentCache, e);
            throw e;
        }
    }

    public void updatePayStatus(String tenantId, IObjectData paymentCache, String payStatus) {
        User systemUser = new User(tenantId, "-10000");
        List<String> updateFieldList = Lists.newArrayList(PaymentCacheConst.Field.PayStatus.apiName);
        paymentCache.set(PaymentCacheConst.Field.PayStatus.apiName, payStatus);
        serviceFacade.batchUpdateByFields(systemUser, Lists.newArrayList(paymentCache), updateFieldList);
    }

    /**
     * 查payment_cache
     * 1、已支付 pay_status = "1"
     * 2、source_object_api_name = "SalesOrderObj"
     * 3、target_object_data_id 没值
     */
    public List<IObjectData> getNeedRepairDatas(String tenantId) {
        User admin = new User(tenantId, "-10000");

        List<IFilter> filterList = Lists.newArrayList();
        SearchUtil.fillFilterIn(filterList, PaymentCacheConst.Field.PayStatus.apiName, PayStatusEnum.PAID.value);
        SearchUtil.fillFilterEq(filterList, PaymentCacheConst.Field.SourceObjectApiName.apiName, "SalesOrderObj");
        SearchUtil.fillFilterIsNull(filterList, PaymentCacheConst.Field.TargetObjectDataId.apiName);
//        SearchUtil.fillFilterEq(filterList, SystemConstants.Field.LifeStatus.apiName, SystemConstants.LifeStatus.Normal.value);

        QueryResult<IObjectData> queryResult = searchQuery(admin, PaymentCacheConst.OBJECT_API_NAME, filterList, null, 0, 1000);
        return queryResult.getData();
    }

    /**
     * 通过paymentCache创建【回款】
     * 用于修复数据
     */
    public IObjectData createPayment(String tenantId, String purpose, IObjectData paymentCache) {
        //String createBy = paymentCache.get(SystemConstants.Field.CreateBy.apiName, String.class);
        User user = new User(tenantId, "-10000");

        String ea = serviceFacade.getEAByEI(tenantId);

        //获取金额，参考 com.facishare.crm.newpayment.manager.predefine.PaymentCacheManager.addPaymentCache
        BigDecimal amount = paymentCache.get(PaymentCacheConst.Field.Amount.apiName, BigDecimal.class);
        BigDecimal payAmount = amount.divide(new BigDecimal(100));
        log.info("createPayment tenantId[{}], paymentCacheId[{}], payAmount[{}]", tenantId, paymentCache.getId(), payAmount);
        String paymentTerm = PaymentTermEnum.WeiXin.getType(); //目前只有微信
        IObjectData paymentData = getPaymentData(user, purpose, paymentTerm, paymentCache, payAmount);
        Map<String, List<ObjectDataDocument>> detailObjectData = getDetailObjectDatas(user, paymentCache, payAmount);

        //传app-id确保 triggerAction调用本地的PaymentAddAction
        RequestContext requestContext = RequestContext.builder().user(user).tenantId(tenantId).ea(ea).appId(ConfigCenter.dhtAppId).build();
        BaseObjectSaveAction.Arg arg = new BaseObjectSaveAction.Arg();
        arg.setObjectData(ObjectDataDocument.of(paymentData));
        arg.setDetails(detailObjectData);
        requestContext.setAttribute(PaymentCacheConst.PAYMENT_PAY_SOURCE, PaySourceEnum.WxMiniProgram); //不然会报错：com.facishare.paas.appframework.core.exception.ValidateException: 不支持新建支付方式为在线支付的回款
        try {
            BaseObjectSaveAction.Result paymentAddResult = serviceFacade.triggerAction(new ActionContext(requestContext, Utils.CUSTOMER_PAYMENT_API_NAME, ObjectAction.CREATE.getActionCode()), arg, BaseObjectSaveAction.Result.class);
            log.info("createPayment, tenantId[{}], paymentCache[{}], arg[{}], payment[{}]", tenantId, paymentCache, arg, paymentAddResult.getObjectData().toObjectData());
            return paymentAddResult.getObjectData().toObjectData();
        } catch (Exception e) {
            log.warn("createPayment failed, tenantId[{}], paymentCache[{}], arg[{}]", tenantId, paymentCache, arg, e);
            throw new ValidateException(I18N.text(CAI18NKey.CREATE_PAYMENT_FAIL, e.getMessage()));
        }
    }

    private IObjectData getPaymentData(User user, String purpose, String paymentTerm, IObjectData paymentCache, BigDecimal amount) {
        IObjectData objectData = ObjectDataUtil.getBaseObjectData(user, Utils.CUSTOMER_PAYMENT_API_NAME);

        //参考线上 /v1/object/payment_pay/service/get_pay_param 前端传过来的参数
        objectData.set(PaymentConstants.Field.Customer.apiName, paymentCache.get(PaymentCacheConst.Field.CustomerId.apiName, String.class));

        objectData.set(PaymentConstants.Field.PaymentAmount.apiName, amount);
        objectData.set(PaymentConstants.Field.PaymentTerm.apiName, paymentTerm);
        String sourceObjectApiName = paymentCache.get(PaymentCacheConst.Field.SourceObjectApiName.apiName, String.class);
        if (Objects.equals(sourceObjectApiName, CommonSalesOrderConstants.API_NAME)) {
            objectData.set(PaymentConstants.Field.OrderIdText.apiName, paymentCache.get(PaymentCacheConst.Field.SourceObjectDataId.apiName, String.class));
        }
        objectData.set(PaymentConstants.Field.Amount.apiName, amount);
        //AvailableAmount("available_amount", "可用金额"),  计算字段不赋值
        objectData.set(PaymentConstants.Field.PayType.apiName, PayTypeEnum.OnlineCharge.value);
        // ChargeNo("charge_no", "支付流水号"), 不用管
        objectData.set(PaymentConstants.Field.PaymentPurpose.apiName, purpose);
        objectData.set(PaymentConstants.Field.PaymentTime.apiName, paymentCache.getLastModifiedTime());  // TODO: 2024/4/10
        objectData.set(PaymentConstants.Field.SubmitTime.apiName, paymentCache.getLastModifiedTime());
        objectData.set(PaymentConstants.Field.EnterIntoAccount.apiName, false);
        //  objectData.set(PaymentConstants.Field.PayStatementId.apiName, payStatement.getId());
        objectData.set("record_type", "default__c");
        return objectData;
    }

    /**
     * 回款明细
     */
    private Map<String, List<ObjectDataDocument>> getDetailObjectDatas(User user, IObjectData paymentCache, BigDecimal amount) {
        Map<String, List<ObjectDataDocument>> detailObjectData = new HashMap<>();

        IObjectData orderPayment = ObjectDataUtil.getBaseObjectData(user, Utils.ORDER_PAYMENT_API_NAME);
        orderPayment.set(OrderPaymentConstants.Field.UsedDate.apiName, System.currentTimeMillis());

        String sourceObjectApiName = paymentCache.get(PaymentCacheConst.Field.SourceObjectApiName.apiName, String.class);
        if (Objects.equals(sourceObjectApiName, CommonSalesOrderConstants.API_NAME)) {
            orderPayment.set(OrderPaymentConstants.Field.SalesOrder.apiName, paymentCache.get(PaymentCacheConst.Field.SourceObjectDataId.apiName, String.class));
        }

        orderPayment.set(OrderPaymentConstants.Field.PaymentAmount.apiName, amount);
        orderPayment.set(OrderPaymentConstants.Field.Customer.apiName, paymentCache.get(PaymentCacheConst.Field.CustomerId.apiName, String.class));

        orderPayment.set("record_type", "default__c");

        detailObjectData.put(Utils.ORDER_PAYMENT_API_NAME, Lists.newArrayList(ObjectDataDocument.of(orderPayment)));
        return detailObjectData;
    }

}