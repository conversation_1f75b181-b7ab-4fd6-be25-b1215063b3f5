package com.facishare.crm.consts;

public interface CreditFlowDetailConst {
    String API_NAME = "CreditFlowDetailObj";

    enum F {
        /**
         * 例如客户
         */
        CreditAuthObjectApiName("credit_auth_object_api_name"),
        CreditAuthObjectDataId("credit_auth_object_data_id"),
        FundAccountId("fund_account_id"),
        CustomerAccountId("customer_account_id"),
        RelateMasterApiName("relate_master_api_name"),
        RelateMasterDataId("relate_master_data_id"),
        /**
         * 从对象
         */
        RelateObjectApiName("relate_object_api_name"),
        RelateObjectDataId("relate_object_data_id"),
        /**
         * 信用金额
         */
        CreditAmount("credit_amount"),
        /**
         * 流转后剩余金额，没有下个节点时，credit_amount = left_credit_amount
         */
        LeftCreditAmount("left_credit_amount"),
        CreditOccupiedRuleId("credit_occupied_rule_id"),

        PreMasterApiName("pre_master_api_name"),
        PreMasterDataId("pre_master_data_id"),
        PreObjectApiName("pre_object_api_name"),
        PreObjectDataId("pre_object_data_id"),
        ;

        public final String apiName;

        F(String apiName) {
            this.apiName = apiName;
        }
    }
}
