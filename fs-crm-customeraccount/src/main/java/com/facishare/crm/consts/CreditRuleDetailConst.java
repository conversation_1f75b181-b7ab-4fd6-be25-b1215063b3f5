package com.facishare.crm.consts;

public interface CreditRuleDetailConst {
    String API_NAME = "CreditOccupiedRuleDetailObj";
    String DEFAULT_DETAIL_LAYOUT_API_NAME = "CreditOccupiedRuleDetail_detail_layout__c";

    enum MatchType {
        FIRST,
        NOT_FIRST,
        NOT_MATCH
    }

    enum F {
        Name("name"),
        CreditOccupiedRuleId("credit_occupied_rule_id"),
        RuleSequence("rule_sequence"),
        CreditObject("credit_object"),
        /**
         * 条件是按主对象的字段设置条件
         */
        RuleCondition("rule_condition"),
        OccupiedObject("occupied_object"),
        /**
         * 金额字段可以是按主对象或者从对象设置；
         * 按从对象设置时，信用占用金额=sum(从对象.{occupied_amount_field})
         * 按主对象设置时，信用张勇金额=主对象.{occupied_amount_field}
         */
        OccupiedAmountField("occupied_amount_field"),
        /**
         * @see com.facishare.crm.customeraccount.enums.OccupiedTimingEnum
         */
        OccupiedTiming("occupied_timing"),
        /**
         * boolean类型
         */
        CheckCreditLimit("check_credit_limit"),
        /**
         * @see com.facishare.crm.customeraccount.enums.CheckStrengthEnum
         */
        CheckStrength("check_strength");

        public final String apiName;

        F(String apiName) {
            this.apiName = apiName;
        }
    }
}
