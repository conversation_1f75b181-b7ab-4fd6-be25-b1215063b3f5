package checkrule

import com.facishare.crm.customeraccount.interceptor.IgnoreLogInterceptor
import com.facishare.crm.customeraccount.interceptor.LogInterceptor
import com.facishare.paas.appframework.metadata.ObjectDataExt
import com.google.common.collect.Lists
import spock.lang.Specification

import java.lang.reflect.Method

class LogInterceptorTest extends Specification {


    @IgnoreLogInterceptor
    def "testListObjectDataResultLogInterceptor"() {
        given:
        LogInterceptor logInterceptor = new LogInterceptor(null, null)

        Object result = Lists.newArrayList()
        result.add(ObjectDataExt.of(dataMap).getObjectData())

        Method method = AnnotationTest.getMethod(methodName)
        IgnoreLogInterceptor ignoreLogInterceptor = method.getAnnotation(IgnoreLogInterceptor)
        when:
        Object logResult = logInterceptor.toLogResult(result, ignoreLogInterceptor)

        then:
        noExceptionThrown()
        println("result=" + result)
        println("logResult=" + logResult)

        where:
        methodName                    | dataMap
        "testNoAnnotation"            | ["_id": "id1", "order_id": "orderId1"]
        "testAnnotationNotLogNoValue" | ["_id": "id1", "order_id": "orderId1"]
        "testAnnotationLogNoValue"    | ["_id": "id1", "order_id": "orderId1"]
        "testAnnotationLogValue"      | ["_id": "id1", "freeze_amount": "orderId1"]
    }

    public static class AnnotationTest {

        public void testNoAnnotation() {}

        @IgnoreLogInterceptor
        public void testAnnotationNotLogNoValue() {}

        @IgnoreLogInterceptor(log = true)
        public void testAnnotationLogNoValue() {}

        @IgnoreLogInterceptor(log = true, value = ["AccountFrozenRecordObj"])
        public void testAnnotationLogValue() {

        }
    }
}
