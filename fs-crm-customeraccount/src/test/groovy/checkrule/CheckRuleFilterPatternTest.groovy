package checkrule

import com.facishare.crm.customeraccount.predefine.reconciliationsql.AbstractReconciliationSqlGenerator
import com.facishare.paas.appframework.common.util.JacksonUtils
import com.facishare.paas.appframework.core.model.ObjectDataDocument
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.predef.domain.EditActionDomainPlugin
import com.facishare.paas.metadata.api.IObjectData
import spock.lang.Specification

class CheckRuleFilterPatternTest extends Specification {

    def "test parse order"() {
        given:
        AbstractReconciliationSqlGenerator generator = new AbstractReconciliationSqlGenerator() {
            @Override
            void doGenerate(RequestContext requestContext, IObjectData dbCheckRuleData, IObjectData checkRuleData) {

            }
        }

        when:
        int maxOrder = generator.getMaxOrder(pattern)

        then:
        assert maxOrder == result

        where:
        pattern                                                     || result
        "((1) and (2))"                                             || 2
        "(((1) and (2)) or (3 and 4))"                              || 4
        "(((1 and 2) or (3 and 4)) and (((5) and 6) or (7 and 8)))" || 8

    }

    def "test json"() {
        given:
        EditActionDomainPlugin.Arg arg = new EditActionDomainPlugin.Arg()
        ObjectDataDocument dbData = ObjectDataDocument.of([
                "lock_rule"                   : "default_lock_rule",
                "field_s9lEn__c"              : "2024-12-31",
                "field_11fu3__c"              : null,
                "settle_type"                 : null,
                "discount"                    : 100,
                "order_time"                  : *************,
                "receivable_amount"           : "52800.00",
                "field_9S0mF__c"              : "广西运松农资有限公司",
                "field_o6D75__c"              : "40",
                "ship_to_id"                  : "63bd2c5c36298a00011eb939",
                "field_067Qb__c"              : "2",
                "field_3oAtr__c"              : null,
                "field_eCs0d__c__relation_ids": "659751db4d3b2600016d424e",
                "field_rXcYa__c"              : null,
                "field_LTT4G__c"              : "667a7f3ab134b40001e182ae",
                "life_status_before_invalid"  : null,
                "owner_department_id"         : "1139",
                "activity_id"                 : null,
                "searchAfterId"               : [
                        "*************",
                        "667cc03633e9900001d79649"
                ],
                "field_84C9q__c"              : [
                        "1139"
                ],
                "field_LYw7b__c"              : "11.28962",
                "field_32k6j__c"              : null,
                "field_wfpf1__c"              : "2.00",
                "new_opportunity_id"          : null,
                "quote_id"                    : null,
                "payment_amount"              : "0.00",
                "field_2gy27__c"              : null,
                "field_M27s2__c"              : "-15787.20",
                "field_ClIa0__c"              : null,
                "version"                     : "12",
                "delivery_comment"            : null,
                "field_eCs0d__c"              : "659751db4d3b2600016d424e",
                "field_wq1QP__c"              : "0",
                "price_book_id"               : null,
                "field_Z6wbE__c"              : null,
                "delivered_amount_sum"        : null,
                "tenant_id"                   : "757079",
                "field_rw2kB__c"              : null,
                "field_fsphx__c"              : false,
                "data_own_organization"       : [
                        "1005"
                ],
                "field_FX1nc__c"              : "2",
                "store_write_off_id"          : null,
                "field_89K1k__c"              : null,
                "origin_source"               : null,
                "invoice_amount"              : "0.00",
                "field_ow7r4__c"              : null,
                "returned_goods_amount"       : "0.00",
                "refund_amount"               : "0.00000",
                "field_75dgC__c"              : *************,
                "product_amount"              : "52800.00",
                "field_7yRpT__c"              : null,
                "field_aRtID__c"              : "29.00000",
                "last_modified_time"          : *************,
                "field_tj715__c"              : null,
                "life_status"                 : "under_review",
                "field_Lq0ws__c"              : "3",
                "is_user_define_work_flow"    : null,
                "field_Dl0xi__c"              : "29.00",
                "ship_to_tel"                 : "***********",
                "field_gz7ve__c"              : null,
                "work_flow_id"                : null,
                "field_4Gwzb__c"              : null,
                "out_tenant_id"               : null,
                "field_uWMl8__c"              : "2",
                "field_j2uE4__c"              : "667a89755540190001e66571",
                "account_id"                  : "63bd2c4bc33cdb0001aeead2",
                "account_id__relation_ids"    : "63bd2c4bc33cdb0001aeead2",
                "field_LTT4G__c__relation_ids": "667a7f3ab134b40001e182ae",
                "no_invoice_amount"           : "52800.00",
                "order_by"                    : null,
                "dynamic_amount"              : "0.00",
                "field_zOjm9__c"              : null,
                "field_j2uE4__c__relation_ids": "667a89755540190001e66571",
                "commision_info"              : null,
                "field_u53C5__c"              : "option1",
                "current_level"               : "0",
                "field_Gitqy__c"              : true,
                "field_HhmA6__c"              : false,
                "ship_to_id__relation_ids"    : "63bd2c5c36298a00011eb939",
                "logistics_status"            : "1",
                "field_j77ea__c"              : null,
                "field_q80MN__c"              : "0",
                "extend_obj_data_id"          : "667cc03733e9900001d79fb3",
                "ship_to_add"                 : "广西南宁市横县云表镇桥北国道路98号",
                "field_wx3oO__c"              : null,
                "order_amount"                : "52800.00",
                "owner_department"            : "新胜利贵港",
                "signature_attachment"        : null,
                "field_r4fIS__c"              : null,
                "field_20ncU__c"              : false,
                "plan_payment_amount"         : null,
                "lock_status"                 : "1",
                "package"                     : "CRM",
                "create_time"                 : *************,
                "field_Oy1RU__c"              : null,
                "submit_time"                 : 1719451702226,
                "resource"                    : "0",
                "field_a1S9b__c"              : null,
                "field_u2LEg__c"              : [
                        [
                                "ext"        : "pdf",
                                "path"       : "N_202406_27_25d526859323483583fdb31505e3391a",
                                "filename"   : "广西运松农资有限公司XSL-XSDD-********-0408.pdf",
                                "create_time": 1719452302083,
                                "size"       : 31945
                        ]
                ],
                "field_hw8r7__c"              : null,
                "field_6zlb8__c"              : null,
                "created_by"                  : [
                        "1024"
                ],
                "relevant_team"               : [
                        [
                                "teamMemberEmployee"      : [
                                        "1024"
                                ],
                                "teamMemberType"          : "0",
                                "teamMemberRole"          : "1",
                                "teamMemberPermissionType": "2",
                                "teamMemberDeptCascade"   : "0"
                        ]
                ],
                "field_evf2J__c"              : "0",
                "confirmed_receive_date"      : null,
                "delivery_date"               : null,
                "data_own_department"         : [
                        "1139"
                ],
                "field_525pW__c"              : "1",
                "field_pCV10__c"              : "oK88ha24y",
                "field_fjqen__c"              : false,
                "field_Qx6uR__c"              : true,
                "name"                        : "XSL-XSDD-********-0408",
                "activity_agreement_id"       : null,
                "faccount_amount"             : null,
                "bill_money_to_confirm"       : null,
                "field_58u1E__c"              : "XSL-SYF-********-0075TS-XS02-********-006",
                "_id"                         : "667cc03633e9900001d79649",
                "payment_money_to_confirm"    : null,
                "field_P1Xm3__c"              : null,
                "invoice_status"              : "3",
                "field_Nb3vJ__c"              : null,
                "field_5rizL__c"              : null,
                "promotion_id"                : null,
                "remark"                      : null,
                "field_qNxk3__c"              : null,
                "dealer_activity_cost_id"     : null,
                "lock_user"                   : [
                        "-10000"
                ],
                "field_3hH29__c"              : null,
                "is_deleted"                  : false,
                "shipping_warehouse_id"       : null,
                "field_b3wRh__c"              : "-15787.20",
                "receipt_type"                : null,
                "object_describe_api_name"    : "SalesOrderObj",
                "field_Gk2qE__c"              : "1",
                "out_owner"                   : null,
                "owner"                       : [
                        "1024"
                ],
                "field_L19qU__c"              : null,
                "field_6a4f2__c"              : "52800.00",
                "field_9QG3f__c"              : "双方已盖章合同上传",
                "field_4fp2U__c"              : "广西运松农资有限公司",
                "field_j751d__c"              : null,
                "order_mode"                  : null,
                "last_modified_by"            : [
                        "-10000"
                ],
                "field_s78y4__c"              : "2",
                "record_type"                 : "default__c",
                "field_ahz3k__c"              : null,
                "activity_closed_status"      : null,
                "field_1OBv2__c"              : null,
                "field_1Xu9m__c"              : null,
                "confirmed_delivery_date"     : null
        ])

        arg.setDbMasterData(dbData)

        ObjectDataDocument objectDataDocument = ObjectDataDocument.of([
                "record_type"                 : "default__c",
                "field_u53C5__c__o"           : "",
                "field_evf2J__c__o"           : "",
                "field_Gk2qE__c__o"           : "",
                "field_FX1nc__c__o"           : "",
                "field_Lq0ws__c__o"           : "",
                "object_describe_api_name"    : "SalesOrderObj",
                "object_describe_id"          : "6310205af7a9a40001055fcb",
                "field_u53C5__c"              : "option1",
                "account_id"                  : "63bd2c4bc33cdb0001aeead2",
                "order_time"                  : *************,
                "field_eCs0d__c"              : "659751db4d3b2600016d424e",
                "field_9QG3f__c"              : "双方已盖章合同上传",
                "field_LTT4G__c"              : "667a7f3ab134b40001e182ae",
                "field_ow7r4__c"              : null,
                "field_j2uE4__c"              : "667a89755540190001e66571",
                "field_s9lEn__c"              : "2024-12-31",
                "field_89K1k__c"              : "",
                "order_amount"                : "52800.00",
                "remark"                      : "最低定价审批TS-XS02-********-006、销售物料领用申请XSL-SYF-********-0075",
                "field_P1Xm3__c"              : "",
                "field_Oy1RU__c"              : [

                ],
                "field_84C9q__c"              : [
                        "1139"
                ],
                "field_tj715__c"              : "655ad87c37bdd00001e2e4a1",
                "field_evf2J__c"              : "0",
                "field_2gy27__c"              : "",
                "field_58u1E__c"              : "XSL-SYF-********-0075TS-XS02-********-006",
                "field_Gk2qE__c"              : "1",
                "field_FX1nc__c"              : "2",
                "field_Lq0ws__c"              : "3",
                "field_75dgC__c"              : *************,
                "ship_to_id"                  : "63bd2c5c36298a00011eb939",
                "ship_to_tel"                 : "***********",
                "ship_to_add"                 : "广西南宁市横县云表镇桥北国道路98号",
                "field_a1S9b__c"              : "",
                "version"                     : null,
                "_id"                         : "667cc03633e9900001d79649",
                "lock_rule"                   : "default_lock_rule",
                "discount"                    : 100,
                "field_9S0mF__c"              : "广西运松农资有限公司",
                "field_o6D75__c"              : "40",
                "field_067Qb__c"              : "2",
                "field_wfpf1__c"              : "2.00",
                "field_M27s2__c"              : "-15787.20",
                "field_wq1QP__c"              : "0",
                "field_fsphx__c"              : false,
                "data_own_organization"       : [
                        "1005"
                ],
                "life_status"                 : "under_review",
                "field_uWMl8__c"              : "2",
                "no_invoice_amount"           : "52800.00",
                "dynamic_amount"              : "0.00",
                "field_Gitqy__c"              : true,
                "field_HhmA6__c"              : false,
                "owner_department"            : "新胜利贵港",
                "field_20ncU__c"              : false,
                "lock_status"                 : "1",
                "resource"                    : "0",
                "field_u2LEg__c"              : [
                        [
                                "ext"        : "pdf",
                                "path"       : "N_202406_27_25d526859323483583fdb31505e3391a",
                                "filename"   : "广西运松农资有限公司XSL-XSDD-********-0408.pdf",
                                "create_time": 1719452302083,
                                "size"       : 31945
                        ]
                ],
                "data_own_department"         : [
                        "1139"
                ],
                "field_525pW__c"              : "1",
                "field_pCV10__c"              : "oK88ha24y",
                "field_Qx6uR__c"              : true,
                "field_fjqen__c"              : false,
                "lock_user"                   : [
                        "-10000"
                ],
                "field_6a4f2__c"              : "52800.00",
                "field_s78y4__c"              : "2",
                "requestId"                   : "c01f1cb5e5904e1181a524ded3e8e571",
                "tenant_id"                   : "757079",
                "field_11fu3__c"              : null,
                "settle_type"                 : null,
                "field_3oAtr__c"              : null,
                "field_eCs0d__c__relation_ids": "659751db4d3b2600016d424e",
                "field_rXcYa__c"              : null,
                "life_status_before_invalid"  : null,
                "owner_department_id"         : "1139",
                "activity_id"                 : null,
                "searchAfterId"               : [
                        "*************",
                        "667cc03633e9900001d79649"
                ],
                "field_32k6j__c"              : null,
                "new_opportunity_id"          : null,
                "quote_id"                    : null,
                "field_ClIa0__c"              : null,
                "price_book_id"               : null,
                "field_Z6wbE__c"              : null,
                "field_rw2kB__c"              : null,
                "store_write_off_id"          : null,
                "origin_source"               : null,
                "field_7yRpT__c"              : null,
                "last_modified_time"          : *************,
                "field_gz7ve__c"              : null,
                "work_flow_id"                : null,
                "field_4Gwzb__c"              : null,
                "out_tenant_id"               : null,
                "account_id__relation_ids"    : "63bd2c4bc33cdb0001aeead2",
                "field_LTT4G__c__relation_ids": "667a7f3ab134b40001e182ae",
                "order_by"                    : null,
                "field_zOjm9__c"              : null,
                "field_j2uE4__c__relation_ids": "667a89755540190001e66571",
                "commision_info"              : null,
                "current_level"               : "0",
                "ship_to_id__relation_ids"    : "63bd2c5c36298a00011eb939",
                "field_j77ea__c"              : null,
                "field_q80MN__c"              : "0",
                "extend_obj_data_id"          : "667cc03733e9900001d79fb3",
                "field_wx3oO__c"              : null,
                "signature_attachment"        : null,
                "field_r4fIS__c"              : null,
                "package"                     : "CRM",
                "create_time"                 : *************,
                "field_hw8r7__c"              : null,
                "field_6zlb8__c"              : null,
                "created_by"                  : [
                        "1024"
                ],
                "relevant_team"               : [
                        [
                                "teamMemberEmployee"      : [
                                        "1024"
                                ],
                                "teamMemberType"          : "0",
                                "teamMemberRole"          : "1",
                                "teamMemberPermissionType": "2",
                                "teamMemberDeptCascade"   : "0"
                        ]
                ],
                "delivery_date"               : null,
                "name"                        : "XSL-XSDD-********-0408",
                "activity_agreement_id"       : null,
                "faccount_amount"             : null,
                "invoice_status"              : "3",
                "field_Nb3vJ__c"              : null,
                "field_5rizL__c"              : null,
                "promotion_id"                : null,
                "field_qNxk3__c"              : null,
                "dealer_activity_cost_id"     : null,
                "field_3hH29__c"              : null,
                "is_deleted"                  : false,
                "shipping_warehouse_id"       : null,
                "receipt_type"                : null,
                "out_owner"                   : null,
                "owner"                       : [
                        "1024"
                ],
                "field_L19qU__c"              : null,
                "order_mode"                  : null,
                "last_modified_by"            : [
                        "-10000"
                ],
                "field_ahz3k__c"              : null,
                "field_1OBv2__c"              : null,
                "field_1Xu9m__c"              : null,
                "__temp_data_id__"            : "667cc03633e9900001d79649",
                "field_Dl0xi__c"              : "29.00",
                "field_LYw7b__c"              : "11.28962",
                "product_amount"              : "52800.00",
                "field_aRtID__c"              : "29.00000",
                "field_b3wRh__c"              : "-15787.20",
                "original_life_status"        : "under_review"
        ])
        arg.setObjectData(objectDataDocument)

        when:
        String json = JacksonUtils.toJson(arg)

        then:
        noExceptionThrown()
        println(json)


    }
}
