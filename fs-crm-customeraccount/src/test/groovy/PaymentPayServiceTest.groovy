
import com.facishare.crm.consts.CreditRuleMatchRecordConst
import com.facishare.crm.customeraccount.constants.AccountTransactionFlowConst
import com.facishare.crm.customeraccount.constants.NewCustomerAccountConstants
import com.facishare.crm.customeraccount.enums.ExpenseTypeEnum
import com.facishare.crm.customeraccount.enums.PayTypeEnum
import com.facishare.crm.payment.predefine.service.PaymentPayService
import com.facishare.crm.customeraccount.predefine.service.dto.GetPayParamModel
import com.facishare.crm.customeraccount.predefine.service.dto.QueryPayStatusModel
import com.facishare.crm.customeraccount.predefine.service.dto.QuerySimilarityPaymentCacheModel
import com.facishare.crm.customeraccount.predefine.service.dto.QueryValidISVModel
import com.facishare.crmcommon.util.SearchQueryBuilder
import com.facishare.crmcommon.util.SearchUtil
import com.facishare.paas.appframework.core.model.InfraServiceFacade
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.ServiceContext
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction
import com.facishare.paas.appframework.metadata.dto.SaveMasterAndDetailData
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.QueryResult
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.api.search.IFilter
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.search.Filter
import com.facishare.paas.metadata.impl.search.Operator
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.google.common.collect.Sets
import org.redisson.api.RLock
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

import java.util.concurrent.CountDownLatch

@ContextConfiguration(locations = "classpath*:spring-test/applicationContext.xml")
class PaymentPayServiceTest extends Specification {
    @Autowired
    private PaymentPayService paymentPayService
    @Autowired
    private ServiceFacade serviceFacade
    @Autowired
    private InfraServiceFacade infraServiceFacade

    static {
        System.setProperty("spring.profiles.active", "fstest")
    }

    def "testLockAndUnLock"() {
        given:
        String objectApiName = "PaymentObj"
        User user = User.systemUser("68867")

        when:
        RLock lock = infraServiceFacade.tryLock(user, objectApiName, "check_rule")
        long outLockTime = System.currentTimeMillis()

        CountDownLatch countDownLatch = new CountDownLatch(1)
        new Thread() {
            @Override
            void run() {
                try {
                    RLock innerLock = infraServiceFacade.tryLock(user, objectApiName, "check_rule")
                    println("thread=" + currentThread().getName() + " innerLock = " + innerLock)
                    infraServiceFacade.unlock(innerLock)
                } finally {
                    countDownLatch.countDown()
                }
            }
        }.start()

        countDownLatch.await()

        println("thread=" + Thread.currentThread().getName() + " outLock=" + lock)
        println("locked time = " + (System.currentTimeMillis() - outLockTime))
        infraServiceFacade.unlock(lock)

        then:
        noExceptionThrown()

    }

    def "testTagFieldQuery"() {
        given:
        User user = User.systemUser(tenantId)

        when:
        SearchTemplateQuery recordQuery = SearchQueryBuilder.builder().eq(CreditRuleMatchRecordConst.F.PreObject.apiName, objectApiName).hasAnyOf(CreditRuleMatchRecordConst.F.PreDataIds.apiName, dataIds).build();
        List<IObjectData> nextMatchRecordDataList = serviceFacade.findBySearchQuery(user, CreditRuleMatchRecordConst.API_NAME, recordQuery).getData();

        then:
        println(nextMatchRecordDataList.size())

        where:
        tenantId | objectApiName   | dataIds
        "90339"  | "SalesOrderObj" | ["65b1d13ef0df6e00075e5b1c"]
        "68867"  | "SalesOrderObj" | ["65a5ebef3a98a200011e593c"]
    }

    def "testSearchQuery"() {
        given:
        SearchTemplateQuery subSearchQuery = new SearchTemplateQuery()
        List<IFilter> subFilters = Lists.newArrayList()
        SearchUtil.fillFilterEq(subFilters, "order_id", Lists.newArrayList("654dde576e0ab40001178ac4", "6548d0677966260001f952f7"))
        subSearchQuery.setFilters(subFilters)

        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery()
        List<IFilter> filters = Lists.newArrayList()
        Filter filter = new Filter()
        filter.setFieldName("_id")
        filter.setOperator(Operator.IN)
        filter.setValueType(10)
        filter.setFieldValues(Lists.newArrayList(subSearchQuery.toJsonString(), "SalesOrderProductObj", "product_id"))
        filters.add(filter)
        searchTemplateQuery.setFilters(filters)

        when:
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(User.systemUser(tenantId), "ProductObj", searchTemplateQuery)
        List<IObjectData> dataList = queryResult.getData()

        then:
        dataList.forEach { data ->
            println(data.toJsonString())
        }

        where:
        tenantId = "68867"

    }

    def "testAgge"() {
        given:
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery()
        List<IFilter> filterList = Lists.newArrayList()
        SearchUtil.fillFilterIn(filterList, "order_id", orderIds as List<String>)
        SearchUtil.fillFilterEq(filterList, "is_deleted", Boolean.FALSE)
        searchTemplateQuery.setFilters(filterList)
        searchTemplateQuery.setLimit(orderIds.size())

        when:
        List<IObjectData> dataList = serviceFacade.aggregateFindBySearchQueryWithGroupFields(User.systemUser(tenantId), searchTemplateQuery, "SalesOrderProductObj", Lists.newArrayList("order_id"), "count", "id")

        then:
        noExceptionThrown()
        dataList.forEach { data -> println(data.toJsonString()) }

        where:
        tenantId | orderIds
        "68867"  | ['654dde576e0ab40001178ac4', '6548d0677966260001f952f7', '655321d977a58a0001c41305', '6553262077a58a0001c43fb3']
    }

    def "testUpdateSelectOneOtherOption"() {
        given:
        User user = User.systemUser(tenantId)
        when:

        String otherExtraFieldName = fieldApiName + "__o"
        IObjectData objectData = serviceFacade.findObjectData(user, dataId, objectApiName)
        String optionValue = objectData.get(fieldApiName, String.class)
        String optionValueExtra = objectData.get(otherExtraFieldName, String.class)

        IObjectData destObjectData = serviceFacade.findObjectData(user, destDataId, objectApiName)
        destObjectData.set(fieldApiName, optionValue)
        destObjectData.set(otherExtraFieldName, optionValueExtra)
//        serviceFacade.updateObjectData(user, destObjectData)
        serviceFacade.batchUpdateByFields(user, Lists.newArrayList(destObjectData), Lists.newArrayList(fieldApiName, otherExtraFieldName))

        IObjectData destResult = serviceFacade.findObjectData(user, destDataId, objectApiName)
        String resultOptionValue = destResult.get(fieldApiName, String.class)
        String resultOptionValueExtra = destResult.get(otherExtraFieldName, String.class)
        then:
        println("optionValue:" + optionValue)
        println("optionValueExtra:" + optionValueExtra)
        println("objectData:" + objectData)
        println("resultOptionValue:" + resultOptionValue)
        println("resultOptionValueExtra:" + resultOptionValueExtra)
        assert optionValue == resultOptionValue
        assert optionValueExtra == resultOptionValueExtra
        where:
        tenantId | objectApiName     | dataId                     | fieldApiName     | destDataId
        '83050'  | 'object_Z3P3W__c' | '62f0888d710b710001a753eb' | 'field_tgU6t__c' | '62f087040484bf0001167053'
    }

    def "testRequestId"() {
        given:
        User user = new User(tenantId, userId)
        IObjectData transactionData = new ObjectData()
        transactionData.setTenantId(tenantId)
        transactionData.setDescribeApiName(AccountTransactionFlowConst.API_NAME)
        transactionData.setRecordType(AccountTransactionFlowConst.RecordType.OutcomeRecordType.apiName)
        transactionData.set(AccountTransactionFlowConst.Field.ExpenseAmount.apiName, 1)
        transactionData.set(AccountTransactionFlowConst.Field.ExpenseType.apiName, ExpenseTypeEnum.ManualDeduct.getValue())
        transactionData.setOwner(Lists.newArrayList(userId))
        transactionData.set(AccountTransactionFlowConst.Field.Customer.apiName, customerId)
        transactionData.set(AccountTransactionFlowConst.Field.FundAccount.apiName, fundAccountId)
        Map<String, IObjectDescribe> describeMap = serviceFacade.findObjects(tenantId, Sets.newHashSet(NewCustomerAccountConstants.API_NAME, AccountTransactionFlowConst.API_NAME))

        when:
        SaveMasterAndDetailData.Arg arg = SaveMasterAndDetailData.Arg.builder().masterObjectData(transactionData).detailObjectData(Maps.newHashMap()).objectDescribes(describeMap).build()
        SaveMasterAndDetailData.Result result = serviceFacade.saveMasterAndDetailData(user, arg)
        then:
        println("result=" + result)
        where:
        tenantId | userId | customerId | fundAccountId
        "83050"  | "1000" | ""         | ""

    }

    def "testQuerySimilarityUnpaidRecord"() {
        given:
        User user = new User(tenantId, userId)
        RequestContext requestContext = RequestContext.builder().tenantId(tenantId).user(Optional.of(user)).build()
        ServiceContext serviceContext = new ServiceContext(requestContext, "payment_pay", "query_similarity_unpaid_record")
        QuerySimilarityPaymentCacheModel.Arg arg = new QuerySimilarityPaymentCacheModel.Arg()
        arg.setLimit(10)
        arg.setOffset(0)
        arg.setAmount(amount)
        arg.setCustomerId(customerId)
        arg.setOrderIds(orderIds as Set<String>)
        arg.setPayType(payType)
        when:
        QuerySimilarityPaymentCacheModel.Result result = paymentPayService.querySimilarityCacheList(arg, serviceContext)
        then:
        printf("QuerySimilarityUnpaidRecord customerId=%s,payType=%s,amount=%d,orderIds=%s,result=" + result, customerId, payType, amount, orderIds)
        where:
        tenantId | userId | customerId | payType                          | amount | orderIds
        "78437"  | "1000" | ""         | PayTypeEnum.OnlineCharge.value   | 100    | ["1", "2"]
        "78437"  | "1000" | ""         | PayTypeEnum.OnlineCharge.value   | 100    | []
        "78437"  | "1000" | ""         | PayTypeEnum.OnlineCharge.value   | null   | ["1", "2"]
        "78437"  | "1000" | ""         | PayTypeEnum.OnlineCharge.value   | 0      | ["1", "2"]
        "78437"  | "1000" | ""         | PayTypeEnum.CodeCollection.value | 100    | ["1", "2"]
        "78437"  | "1000" | ""         | PayTypeEnum.CodeCollection.value | 100    | []
        "78437"  | "1000" | ""         | PayTypeEnum.CodeCollection.value | null   | ["1", "2"]
        "78437"  | "1000" | ""         | PayTypeEnum.CodeCollection.value | 0      | ["1", "2"]
    }

    def "testGetPayPrams"() {
        given:
        GetPayParamModel.Arg arg = new GetPayParamModel.Arg()
        arg.setJsonContent(addArg as BaseObjectSaveAction.Arg)
        when:
        GetPayParamModel.Result result = paymentPayService.getPayParam(arg, getServiceContext(tenantId, userId))
        then:
        println("getPayParamResult=" + result)
        where:
        tenantId | userId | addArg
        "78437"  | "1000" | ["objectData": ["customer_id": "", "amount": 100, "pay_type": PayTypeEnum.OnlineCharge.value], "details": []]
    }

    def "testQueryValidISV"() {
        given:
        User user = new User(tenantId, userId)
        RequestContext requestContext = RequestContext.builder().tenantId(tenantId).user(Optional.of(user)).build()
        ServiceContext serviceContext = new ServiceContext(requestContext, "payment_pay", "query_valid_isv")
        when:
        QueryValidISVModel.Result result = paymentPayService.queryValidISV(serviceContext)
        then:
        println("queryValidISV=" + result)
        where:
        tenantId | userId
        "78437"  | "1000"
    }

    def "testQueryPayStatus"() {
        given:
        ServiceContext serviceContext = getServiceContext(tenantId, userId)
        when:
        QueryPayStatusModel.Arg arg = new QueryPayStatusModel.Arg()
        arg.setPayType(payType)
        arg.setPaymentCacheId(paymentCacheId)
        arg.setPaySource(paySource)
        QueryPayStatusModel.Result result = paymentPayService.queryPayStatus(serviceContext, arg)
        then:
        noExceptionThrown()
        println("queryPayStatusResult=" + result)
        where:
        tenantId | userId | payType | paymentCacheId             | paySource
        "78437"  | "1000" | "2"     | "624ff24ec4970100019a8790" | "0"
    }

    private ServiceContext getServiceContext(String tenantId, String userId) {
        User user = new User(tenantId, userId)
        RequestContext requestContext = RequestContext.builder().tenantId(tenantId).user(Optional.of(user)).build()
        return new ServiceContext(requestContext, "", "")
    }
}
