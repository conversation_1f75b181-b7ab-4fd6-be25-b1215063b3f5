package com.facishare.crm.manager;

import com.facishare.crm.customeraccount.constants.AccountTransactionFlowConst;
import com.facishare.crm.customeraccount.enums.ExpenseTypeEnum;
import com.facishare.crm.customeraccount.predefine.manager.CustomerAccountConfigManager;
import com.facishare.crm.customeraccount.predefine.service.dto.CustomerAccountType.CustomerAccountEnableSwitchStatus;
import com.facishare.crmcommon.util.SearchUtil;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;

/**
 * <AUTHOR>
 * Created on 2020/3/30.
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath*:spring-test/applicationContext.xml")
public class CustomerAccountConfigManagerTest {

    @Autowired
    private CustomerAccountConfigManager customerAccountConfigManager;
    @Autowired
    private ServiceFacade serviceFacade;

    static {
        System.setProperty("spring.profiles.active", "fstest");
    }

    @Test
    public void queryTest(){
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, "outcome_object_api_name", "SalesOrderObj");
        SearchUtil.fillFilterEq(filters, "outcome_object_data_id", "611ce93379d4be000126d379");
        SearchUtil.fillFilterEq(filters, AccountTransactionFlowConst.Field.ExpenseType.apiName, ExpenseTypeEnum.ValidateDeduct.getValue());

        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setFilters(filters);
        searchTemplateQuery.setOrders(Lists.newArrayList());
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(1000);
        User user = new User("81146","1000");
        List<IObjectData> dataList = serviceFacade.findBySearchQuery(user, AccountTransactionFlowConst.API_NAME, searchTemplateQuery).getData();
        System.out.println(dataList);
    }

    @Test
    public void getStatusTest() {
        String tenantId = "58743";
        CustomerAccountEnableSwitchStatus status = customerAccountConfigManager.getStatus(tenantId);
        log.info("result={}", status);
    }

    @Test
    public void listTest_success() {
        int customerAccountEnable = 3;
        List<String> tenantIds = Lists.newArrayList("7", "75133", "74327");

        List<String> result = customerAccountConfigManager.list(customerAccountEnable, tenantIds);
        log.info("result={}", result);
    }

    @Test
    public void listTest_failure() {
        int customerAccountEnable = 3;
        List<String> tenantIds = Lists.newArrayList();

        List<String> result = customerAccountConfigManager.list(customerAccountEnable, tenantIds);
        log.info("result={}", result);
    }

    @Test
    public void updateStatusTest_create() {
        User user =User.builder().userId("1000").tenantId("1").build();
        customerAccountConfigManager.updateStatus(user, CustomerAccountEnableSwitchStatus.OPENING_LATER);
    }

    @Test
    public void updateStatusTest_update() {
        User user =User.builder().userId("1000").tenantId("7").build();
        customerAccountConfigManager.updateStatus(user, CustomerAccountEnableSwitchStatus.OPENING_LATER);
    }

}
