package com.facishare.crm.groovy.checkrule

import com.alibaba.fastjson.JSON
import com.facishare.crm.customeraccount.constants.NewCustomerAccountConstants
import com.facishare.crm.customeraccount.predefine.domainplugin.model.FrozenResult
import com.facishare.crm.customeraccount.predefine.handler.RuleHandlerUtil
import com.facishare.crm.customeraccount.predefine.handler.checkrule.CustomerFundAccount
import com.facishare.crm.customeraccount.predefine.handler.checkrule.FrozenComputeBuilder
import com.facishare.crm.customeraccount.predefine.manager.AccountCheckManager
import com.facishare.crm.customeraccount.predefine.manager.NewCustomerAccountManager
import com.facishare.crm.customeraccount.predefine.service.dto.FieldMappingModel
import com.facishare.crm.customeraccount.predefine.service.dto.ObjectMappingModel
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.impl.ObjectData
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.mockito.invocation.InvocationOnMock
import org.mockito.stubbing.Answer
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
//import org.powermock.modules.junit4.PowerMockRunner
//import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik
import spock.lang.Specification

@PowerMockIgnore(["javax.xml.*", "org.xml.*"])
@PrepareForTest([RuleHandlerUtil.class])
//@RunWith(PowerMockRunner)
//@PowerMockRunnerDelegate(Sputnik)
//这行代码的作用是限制RuleHandlerUtil类里的静态代码块初始化。可以使用PowerMock禁止RuleHandlerUtil类在第一次调用时可能会加载一些本地资源配置
@SuppressStaticInitializationFor(["com.facishare.crm.customeraccount.predefine.handler.RuleHandlerUtil"])
class FrozenComputeBuilderTest extends Specification {

    void setup() {
        //然后在setup()方法里
//        PowerMockito.mockStatic(RuleHandlerUtil.class)
    }

    def "testStaticMock"() {
        given:
        IObjectData objectData = new ObjectData()
        objectData.setId("id1")

        IObjectData ruleData = new ObjectData()
        ruleData.setId("ruleId1")

        User user = User.systemUser("ei1")

        PowerMockito.mockStatic(RuleHandlerUtil.class) //对静态类进行Mock设置
        PowerMockito.when(RuleHandlerUtil.toAccountFrozenRecord(Mockito.any(User), Mockito.any(IObjectData), Mockito.any(IObjectData))).then { invocationOnMock ->
            Object[] args = invocationOnMock.getArguments()
            println("length=" + Objects.nonNull(args) ? args.length : -1)
            Map<CustomerFundAccount, IObjectData> frozenDataMap = Maps.newHashMap()

            frozenDataMap.put(CustomerFundAccount.of("customer", "fundAccount"), null)
            return frozenDataMap
        }

        when:
        Map<CustomerFundAccount, IObjectData> frozenDataMap = RuleHandlerUtil.toAccountFrozenRecord(user, objectData, ruleData)

        then:
        println("=========" + frozenDataMap)
    }

    def "testFrozenBuilderCalculator"() {
        "FrozenComputeBuilder返回的结果中customerAccountDataList 与 customerAccountColumnMap 一致，不多也不少"
        given:
        String tenantId = "81146"
        User user = User.systemUser(tenantId)
        AccountCheckManager accountCheckManager = Mock(AccountCheckManager.class)
        accountCheckManager.queryFrozenListByRuleUseRecordData(_) >> Lists.newArrayList()

        RequestContext requestContext = RequestContext.builder().tenantId(tenantId).user(user).build()
        IObjectData objectData = new ObjectData()
        objectData.setId(dataId)
        objectData.setTenantId(tenantId)
        objectData.setDescribeApiName("SalesOrderObj")
        extraDataMap.forEach { k, v -> objectData.set(k, v) }

        IObjectData accountCheckRuleData = new ObjectData()
        accountCheckRuleData.setId(ruleId)
        List<ObjectMappingModel> objectMappingModelList = Lists.newArrayList()
        Map<CustomerFundAccount, IObjectData> customerFundAccountIObjectDataMap = Maps.newHashMap()
        String customerId = objectData.get("account_id")
        fundAccount2FieldMappingMap.forEach { fundAccountId, fieldMapping ->
            ObjectMappingModel objectMappingModel = new ObjectMappingModel()
            objectMappingModel.setSourceObjectApiName("SalesOrderObj")
            objectMappingModel.setTargetObjectApiName("AccountFrozenRecordObj")
            objectMappingModel.setFundAccountId(fundAccountId)

            List<FieldMappingModel> fieldMappingModelList = Lists.newArrayList()
            fieldMapping.forEach { targetField, sourceField ->
                FieldMappingModel fieldMappingModel = new FieldMappingModel()
                fieldMappingModel.setSourceFieldApiName(sourceField)
                fieldMappingModel.setTargetFieldApiName(targetField)
                fieldMappingModelList.add(fieldMappingModel)
            }
            FieldMappingModel customerFieldMapping = new FieldMappingModel()
            customerFieldMapping.setSourceFieldApiName("account_id")
            customerFieldMapping.setTargetFieldApiName("account_id")
            fieldMappingModelList.add(customerFieldMapping)
            objectMappingModel.setFieldMappingList(fieldMappingModelList)
            objectMappingModelList.add(objectMappingModel)

            IObjectData customerAccountData = new ObjectData()
            customerAccountData.setId(fundAccountId + "-" + customerId)
            customerAccountData.set(NewCustomerAccountConstants.Field.FundAccount.apiName, fundAccountId)
            customerAccountData.set(NewCustomerAccountConstants.Field.Customer.apiName, customerId)
            customerAccountData.set(NewCustomerAccountConstants.Field.AccountBalance.apiName, BigDecimal.valueOf(10000L))
            customerAccountData.set(NewCustomerAccountConstants.Field.AvailableBalance.apiName, BigDecimal.valueOf(10000L))
            customerFundAccountIObjectDataMap.put(CustomerFundAccount.of(customerId, fundAccountId), customerAccountData)
        }
        accountCheckRuleData.set("occupied_mapping", JSON.toJSON(objectMappingModelList))

        NewCustomerAccountManager newCustomerAccountManager = Mock(NewCustomerAccountManager.class)
        newCustomerAccountManager.batchGetOrCreateNewCustomerAccount(_ as RequestContext, _ as List) >> customerFundAccountIObjectDataMap

        /*and:
        PowerMockito.when(RuleHandlerUtil.toAccountFrozenRecord(Mockito.any(User), Mockito.any(IObjectData), Mockito.any(IObjectData))).then { invocationOnMock ->
            Object[] args = invocationOnMock.getArguments()
            println("length=" + Objects.nonNull(args) ? args.length : -1)

            Map<CustomerFundAccount, IObjectData> frozenDataMap = Maps.newHashMap()

            return frozenDataMap
        }*/

        when:
        FrozenResult frozenResult = FrozenComputeBuilder.builder()
                .accountCheckManager(accountCheckManager)
                .newCustomerAccountManager(newCustomerAccountManager)
                .requestContext(requestContext)
                .objectData(objectData)
                .accountCheckRuleData(accountCheckRuleData)
                .build().compute()

        then:
        println("frozenResult=" + JSON.toJSON(frozenResult))
        assert !frozenResult.needSkip()
        assert frozenResult.customerAccountDataList.size() == frozenResult.customerAccountUpdateFieldColumnMap.size()
        frozenResult.customerAccountDataList.forEach { customerAccountData ->
            String id = customerAccountData.getId()
            assert frozenResult.customerAccountUpdateFieldColumnMap.get(id) != null
        }

        where: " || 区分输入和输出变量，左边输入变量，右边输出变量"
        dataId   | extraDataMap                                                               | ruleId    | fundAccount2FieldMappingMap
        "dataId" | ["order_amount": 100, "account_id": "customerId1"]                         | "ruleId1" | ["fa1": ["freeze_amount": "order_amount"], "fa2": ["freeze_amount": "order_amount__c"]]
        "dataId" | ["order_amount": 100, "order_amount__c": 100, "account_id": "customerId1"] | "ruleId1" | ["fa1": ["freeze_amount": "order_amount"], "fa2": ["freeze_amount": "order_amount__c"]]
    }
}
