package com.facishare.crm.util;

import java.io.IOException;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.atomic.LongAdder;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.customeraccount.predefine.action.AccountTransactionFlowAddAction;
import com.facishare.crm.customeraccount.predefine.service.impl.InitServiceImpl;
import com.facishare.crm.customeraccount.util.InitUtil;
import com.facishare.idempotent.Idempotent;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.*;
import com.facishare.paas.metadata.util.GetI18nKeyUtil;
import com.fxiaoke.api.IdGenerator;
import com.google.common.base.Joiner;
import com.google.common.collect.Sets;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.reflect.TypeUtils;
import org.junit.Test;

import com.facishare.crm.customeraccount.constants.RebateIncomeDetailConstants;
import com.facishare.crm.customeraccount.constants.RebateOutcomeDetailConstants;
import com.facishare.crm.customeraccount.predefine.manager.result.FuncResult;
import com.facishare.crm.customeraccount.predefine.service.dto.SfaCreateModel;
import com.facishare.crm.customeraccount.util.HttpUtil;
import com.facishare.crm.userdefobj.CrmActionEnum;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

public class HttpUtilTest {

    private static String crmManagerRole = "00000000000000000000000000000006";

    static {
        System.setProperty("spring.profiles.active", "fstest");
    }

    @Test
    public void jsonTest() {
        class Test {
            String key1;
            String key2;
        }
        String json = "{\"code\":\"0\",\"msg\":\"ok\",\"result\":{\"key1\":\"11\",\"key2\":\"22\"}}";
        FuncResult<Test> result = JsonUtil.fromJson(json, FuncResult.class);
        System.out.println(result);
    }

    @Test
    public void checkRuleTest() {
        String eiStr = "725682,724477,760686,719287,738521,759744,679101,753227,728783,753485,707988,760956,711306,747520,725435,646380,701884,747125,701643,604452,712368,710225,753239,736359,756682,758860,740415,760422,728339,722872,727080,680306,755748,768396,739038,751301,642987,701383,724364,701779,739206,752521,753459,736732,757079,739370,687164,740116,735050,759383,760770,728907,734240,743470,715445,725471,753442,735738,320052,756430,761201,703464,744571,759137,756523,727289,718294,761710,721586,721611,742880,755829,761285,738545,712077,730688,716313,738547,735957,756377,728120,747022,725136,747149,750428,750821,680515,714520,753138,756526,524492";
        Set<String> eis = Arrays.stream(eiStr.split(",")).collect(Collectors.toSet());
        Map<String, String> headers = Maps.newHashMap();
        headers.put("Content-Type", "application/x-www-form-urlencoded");
        headers.put("cookie", "JSESSIONID=E36D12071282ED188505F56482AD4A05");
        headers.put("referer", "https://oss.foneshare.cn/paas-console/metadata/sql/query");

        String url = "https://oss.foneshare.cn/paas-console/metadata/sql/query-result";
        Map<String, Object> ei2NumMap = Maps.newHashMap();
        eis.forEach(tenantId -> {
            String sql = "sql=select+count(1)+from+account_rule_use_record+where+tenant_id%3D'" + tenantId + "'+and+rule_stage+in(+'direct_reduce'%2C'check_validate')&module=CRM&enableNestloop=true&resourceType=postgresql&tenantId=" + tenantId;
            SqlResult sqlResult = null;
            try {
                sqlResult = HttpUtil.post(url, headers, sql, SqlResult.class);
                String info = sqlResult.getInfo();
                Type type = TypeUtils.parameterize(List.class, Map.class);
                List<Map> infoMap = JsonUtil.fromJson(info, type);
                String countStr = (infoMap.get(0).get("count").toString());
                Double count = Double.valueOf(countStr);
                if (count > 0) {
                    ei2NumMap.put(tenantId, count);
                }
                Thread.sleep(2000);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        System.out.println("ei2NumMap=" + ei2NumMap);
    }

    @Test
    public void orderRuleMatch() {
        String cookie = "JSESSIONID=A2981881B7740BF4EB79285416D47DA9";

        int offset = 0;
        int size = 0;
        int limit = 50;
        Set<String> matchedOrderIds = Sets.newHashSet();
        Set<String> unMatchedOrderIds = Sets.newHashSet();
        Set<String> finishOrderIds = Sets.newHashSet();
        boolean error = false;
        do {
            String sql = "sql=select+id%2Cname%2Corder_time%2Ccreate_time%2Crecord_type%2Cis_deleted%2Clife_status%2C+value43+as+field_irr56__c%2Cvalue58+as+field_1Nkra__c+from+biz_sales_order+where+tenant_id%3D'707988'++and+is_deleted+%3D+0+and+create_time+%3E+1691461346259+and+create_time%3C1694139746259+and+is_deleted+%3D+0+and+record_type+in('record_hV12A__c'%2C'record_b0eKr__c')+order+by+id+" + "limit+" + limit + "+offset+" + offset + "&module=CRM&enableNestloop=true&resourceType=postgresql&tenantId=707988";
            try {
                List<Map<String, Object>> dataList = queryBySqlUsePaasConsole(cookie, sql);
                size = CollectionUtils.size(dataList);
                offset += size;
                for (Map<String, Object> dataMap : dataList) {
                    IObjectData objectData = ObjectDataExt.of(dataMap);
                    String id = objectData.get("id", String.class);
                    boolean ruleMatch = hlyRuleMatch(objectData);
                    boolean orderFinish = orderFinish(objectData);
                    if (ruleMatch) {
                        matchedOrderIds.add(id);
                    } else if (orderFinish) {
                        finishOrderIds.add(id);
                    } else {
                        unMatchedOrderIds.add(id);
                    }
                }
            } catch (Exception e) {
                error = true;
                e.printStackTrace();
                System.out.println("sqlError=" + sql + ",offset=" + offset);
                break;
            }
        } while (size == limit);

        System.out.println("error=" + error + ",matchedOrderIds=" + matchedOrderIds);
        System.out.println("error=" + error + "unMatchedOrderIds=" + unMatchedOrderIds);
        System.out.println("error=" + error + "finishOrderIds=" + finishOrderIds);

        List<String> matchedOrderIdsOfNoRuleUseRecord = noRuleUseRecordDataOrderIds(cookie, matchedOrderIds);
        List<String> finishOrderIdsOfNoRuleUseRecord = noRuleUseRecordDataOrderIds(cookie, finishOrderIds);

        System.out.println("size=" + matchedOrderIdsOfNoRuleUseRecord.size() + ",matchedOrderIdsOfNoRuleUseRecord=" + matchedOrderIdsOfNoRuleUseRecord);
        System.out.println("size=" + finishOrderIdsOfNoRuleUseRecord.size() + ",finishOrderIdsOfNoRuleUseRecord=" + finishOrderIdsOfNoRuleUseRecord);


    }

    private List<String> noRuleUseRecordDataOrderIds(String cookie, Set<String> orderIds) {
        List<String> noUseRecordDataOrderIds = Lists.newArrayList();
        if (CollectionUtils.empty(orderIds)) {
            return noUseRecordDataOrderIds;
        }
        Lists.partition(Lists.newArrayList(orderIds), 50).forEach(subOrderIds -> {
            String idCondition = subOrderIds.stream().map(id -> "'" + id + "'").collect(Collectors.joining(","));
            String sql = "sql=select+id%2Ccheck_record_object_data_id%2Ccheck_record_object_api_name%2Crule_stage+from+account_rule_use_record+where+tenant_id%3D'707988'+++and+check_record_object_api_name+%3D+'SalesOrderObj'++and+check_record_object_data_id+" + "in(" + idCondition + ")&module=CRM&enableNestloop=true&resourceType=postgresql&tenantId=707988";
            long startTime = System.currentTimeMillis();
            List<Map<String, Object>> dataList = queryBySqlUsePaasConsole(cookie, sql);
            long endTime = System.currentTimeMillis();
            long cost = endTime - startTime;
            Map<String, IObjectData> orderId2RuleUseRecordDataMap = Maps.newHashMap();
            CollectionUtils.nullToEmpty(dataList).forEach(x -> {
                IObjectData objectData = ObjectDataExt.of(x);
                String orderId = objectData.get("check_record_object_data_id", String.class);
                orderId2RuleUseRecordDataMap.put(orderId, objectData);
            });
            subOrderIds.forEach(orderId -> {
                if (!orderId2RuleUseRecordDataMap.containsKey(orderId)) {
                    noUseRecordDataOrderIds.add(orderId);
                }
            });
            if (cost < 1000) {
                try {
                    Thread.sleep(1000 - cost);
                } catch (InterruptedException e) {

                }
            }
        });
        return noUseRecordDataOrderIds;
    }

    private boolean orderFinish(IObjectData objectData) {
        String recordType = objectData.getRecordType();

        if ("record_hV12A__c".equals(recordType)) {
            Set<String> irr56MatchSet = Sets.newHashSet("YfpZ9tNhr");
            String fieldIrr56 = objectData.get("field_irr56__c", String.class);
            return irr56MatchSet.contains(fieldIrr56);
        } else if ("record_b0eKr__c".equals(recordType)) {
            return false;
        }
        return false;
    }

    private boolean hlyRuleMatch(IObjectData objectData) {
        Long orderTime = objectData.get("order_time", Long.class);
        if (Objects.isNull(orderTime)) {
            return false;
        }
        String recordType = objectData.getRecordType();
        if ("record_hV12A__c".equals(recordType)) {
            Set<String> irr56MatchSet = Sets.newHashSet("c2ash1r4r", "1q29byJAj", "61O401vaS", "51Sec6dch");
            String fieldIrr56 = objectData.get("field_irr56__c", String.class);
            return irr56MatchSet.contains(fieldIrr56);
        } else if ("record_b0eKr__c".equals(recordType)) {
            Set<String> nkraMatchSet = Sets.newHashSet("I23Czihp1", "MPklq2v1I", "ioQj3o0K1");
            String field1Nkra = objectData.get("field_1Nkra__c", String.class);
            return nkraMatchSet.contains(field1Nkra);
        }
        return false;
    }

    private List<Map<String, Object>> queryBySqlUsePaasConsole(String cookie, String sql) {
        try {
            String url = "https://oss.foneshare.cn/paas-console/metadata/sql/query-result";
            Map<String, String> headers = Maps.newHashMap();
            headers.put("Content-Type", "application/x-www-form-urlencoded");
            headers.put("cookie", cookie);
            headers.put("referer", "https://oss.foneshare.cn/paas-console/metadata/sql/query");

            SqlResult sqlResult = HttpUtil.post(url, headers, sql, SqlResult.class);
            String info = sqlResult.getInfo();
            Type type = TypeUtils.parameterize(List.class, TypeUtils.parameterize(Map.class, String.class, Object.class));
            return JsonUtil.fromJson(info, type);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Data
    public static class SqlResult {
        private int code;
        private String info;
    }


    @Test
    public void testFormat() {
        String errorMsgformat = "customerAccount id:%s name:%s cannot invalid,because %s";
        String result = String.format(errorMsgformat, 123, "abc", "不想invalid");
        System.out.println("result===" + result);
    }

    @Test
    public void getFunctionPrivilegeTest() throws IOException {
        Map<String, String> headers = Maps.newHashMap();
        headers.put("x-fs-ei", "55732");
        headers.put("x-fs-userId", "1000");
        List<String> actionCodes = Arrays.stream(ObjectAction.values()).map(ObjectAction::getActionCode).collect(Collectors.toList());
        String url = "http://10.113.32.46:8003/metadata/crmrest/objectPrivilege/getObjectsFunctionPrivilege";//10.112.32.68:8004  10.113.32.46:8003
        Map<String, Object> body = Maps.newHashMap();
        body.put(RebateOutcomeDetailConstants.API_NAME, actionCodes);
        Map<String, Object> resultMap = HttpUtil.post(url, headers, body, Map.class);
        System.out.println(resultMap);
    }

    @Test
    public void addFunByFuncInfo() {
        Map<String, String> headers = Maps.newHashMap();
        headers.put("Content-Type", "application/json");
        headers.put("Accept", "application/json");
        String url = "http://10.113.32.46:8003/metadata/crmrest/updatefuncrest/addfuncbyfuncinfo";
        Map<String, Object> body = Maps.newHashMap();
        body.put("tenantIds", Lists.newArrayList("55732"));
        Map<String, Object> funcCode2DescMap = Maps.newHashMap();
        /*for (CrmActionEnum functionEnum : CrmActionEnum.values()) {
            if (!functionEnum.getActionCode().equals("List")) {
                funcCode2DescMap.put(PrepayDetailConstants.API_NAME + "||" + functionEnum.getActionCode(), functionEnum.getActionLabel());
            } else {
                funcCode2DescMap.put(PrepayDetailConstants.API_NAME, functionEnum.getActionLabel());
            }
        }*/
        funcCode2DescMap.put(RebateIncomeDetailConstants.API_NAME + "||" + "Unlock", "解锁");
        funcCode2DescMap.put(RebateIncomeDetailConstants.API_NAME + "||" + "Lock", "锁定");
        body.put("funcCode2DescMap", funcCode2DescMap);
        try {
            Boolean result = HttpUtil.post(url, headers, body, Boolean.class);
            System.out.println(result);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void initCrmMangerAllFunc() {
        Map<String, String> headers = Maps.newHashMap();
        headers.put("Content-Type", "application/json");
        headers.put("Accept", "application/json");

        String initurl = "http://10.113.32.46:8003/metadata/crmrest/v1/userDefinedObjectInit";
        Map<String, Object> body1 = Maps.newHashMap();
        body1.put("tenantId", "55732");
        body1.put("userId", 1000);
        body1.put("apiName", RebateIncomeDetailConstants.API_NAME);
        try {
            FuncResult<Boolean> result = HttpUtil.post(initurl, headers, body1, FuncResult.class);
            System.out.println(result);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void addFunc2Role() {
        Map<String, String> headers = Maps.newHashMap();
        headers.put("Content-Type", "application/json");
        headers.put("Accept", "application/json");
        String toRoleUlr = "http://10.113.32.46:8003/metadata/crmrest/commonprivilege/addfunc2role";
        Map<String, Object> body2 = Maps.newHashMap();
        body2.put("tenantId", "55732");
        body2.put("roleCode", crmManagerRole);
        List<String> funcCodes = Lists.newArrayList();
        for (CrmActionEnum functionEnum : CrmActionEnum.values()) {
        }
        funcCodes.add(RebateIncomeDetailConstants.API_NAME + "||" + "Abolish");
        funcCodes.add(RebateIncomeDetailConstants.API_NAME + "||" + "Unlock");
        funcCodes.add(RebateIncomeDetailConstants.API_NAME + "||" + "Lock");
        funcCodes.add(RebateIncomeDetailConstants.API_NAME + "||" + "ChangeOwner");
        body2.put("funcCodes", funcCodes);
        try {
            FuncResult<String> roleResult = HttpUtil.post(toRoleUlr, headers, body2, FuncResult.class);
            System.out.println(roleResult);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void flapMapTest() {
        List<Tuple<String, List<SfaCreateModel.Arg>>> relatedObjectList = Lists.newArrayList(Tuple.of("test", null));
        List<IObjectData> iObjectDataList = (List) relatedObjectList.stream().map((x) -> {
            return (List) x.getValue();
        }).flatMap((y) -> {
            return y.stream();
        }).collect(Collectors.toList());
        System.out.println(relatedObjectList);
    }

    @Test
    public void testPrintLayoutJson() {
        String tenantId = "-100";
        String userId = "-10000";
        String fundAccountDetailLayoutJson = InitUtil.generateFundAccountDetailLayout(tenantId, userId).toJsonString();
        String fundAccountListLayoutJson = InitServiceImpl.generateFundAccountListLayout(tenantId, userId).toJsonString();
        System.out.println("FundAccountObj layoutJson,detailLayout:" + fundAccountDetailLayoutJson);
        System.out.println("FundAccountObj layoutJson,listLayout:" + fundAccountListLayoutJson);


        String newCustomerAccountDetailLayoutJson = InitUtil.generateNewCustomerAccountConstantsDescribeDetailLayout(tenantId, userId).toJsonString();
        String newCustomerAccountListLayoutJson = InitServiceImpl.generateNewCustomerAccountConstantsListLayout(tenantId, userId).toJsonString();
        System.out.println("NewCustomerAccountObj layoutJson,detailLayout:" + newCustomerAccountDetailLayoutJson);
        System.out.println("NewCustomerAccountObj layoutJson,listLayout:" + newCustomerAccountListLayoutJson);

        String flowIncomeLayoutJson = InitUtil.generateAccountTransactionFlowIncomeLayout(tenantId, userId).toJsonString();
        String flowOutcomeLayoutJson = InitUtil.generateAccountTransactionFlowOutcomeLayout(tenantId, userId).toJsonString();
        String flowListLayoutJson = InitServiceImpl.generateAccountTransactionFlowListLayout(tenantId, userId).toJsonString();

        System.out.println("AccountTransactionFlowObj layoutJson,incomeLayout:" + flowIncomeLayoutJson);
        System.out.println("AccountTransactionFlowObj layoutJson,outcomeLayout:" + flowOutcomeLayoutJson);
        System.out.println("AccountTransactionFlowObj layoutJson,listLayout:" + flowListLayoutJson);
    }

    @Test
    public void eiTest() {
        String allNoDataEiStr = "730591,590092,590197,590198,590199,732675,730252,720209,727080,710532,590038,590274,590272,663458,663004,730688,728120,663483,663481,663476,719537,730191,590244,708025,701872,590245,662977,683672,683678,735899,590115,724865,683691,683689,590029,590141,683660,683654,590256,683662,683668,683666,712664,683669,711330,590208,590207,663503,728185,662524,719353,590104,590228,719261,590223,683695,590218,663513,663514,683711,734040,735372,683715,683714,725435,679471,683724,683728,704640,683700,683701,683702,730910,683630,683755,683634,734240,683734,670799,683740,683745,683743,683627,683628,683746,683625,683747,714520,683629,663031,665472,689519,715380,716581,719618,735050,683809,665483,665482,663059,683804,683807,683808,683805,683806,665386";
        String allNewPaymentNoDataEiStr = "589981,589988,589989,589998,590002,590003,590013,590019,590020,590022,590024,590026,590027,590028,590029,590035,590036,590037,590038,590039,590040,590041,590045,590049,590053,590054,590060,590066,590071,590072,590073,590074,590076,590079,590080,590082,590087,590088,590092,590095,590096,590097,590098,590100,590101,590102,590104,590106,590107,590113,590114,590115,590116,590117,590118,590119,590120,590121,590122,590128,590132,590133,590137,590139,590141,590143,590147,590148,590150,590151,590152,590154,590155,590156,590157,590158,590159,590160,590163,590164,590166,590169,590170,590172,590177,590178,590180,590181,590182,590183,590184,590185,590186,590187,590194,590195,590197,590198,590199,590203,590204,590205,590207,590208,590210,590211,590214,590218,590221,590222,590223,590224,590226,590228,590229,590231,590232,590233,590235,590236,590237,590240,590241,590242,590244,590245,590246,590248,590249,590250,590253,590254,590255,590256,590260,590261,590266,590267,590268,590269,590271,590272,590274,590275,662524,662949,662951,662952,662975,662977,662988,662994,662995,662997,663004,663045,663059,663060,663455,663457,663458,663459,663460,663461,663462,663463,663464,663465,663466,663467,663468,663469,663470,663471,663472,663473,663474,663475,663476,663477,663478,663479,663480,663481,663482,663483,663484,663485,663486,663487,663488,663489,663490,663491,663492,663493,663494,663495,663496,663497,683674,683672,683711,683678,683676,683714,683715,683712,683713,665482,665478,665479,683662,665485,683663,665483,683661,665484,683666,683667,683664,683665,683670,683668,683669,665477,665386,665471,683651,665472,683652,665387,665473,683653,683777,683654,683660,683657,683658,683650,687889,683641,683642,683643,683648,683646,683647,683639,683640,663515,663516,683751,683754,670800,683634,683755,683631,683752,683632,683753,670799,683637,683638,683635,683636,683630,683628,683749,683629,683702,683703,663505,683701,683706,663508,663509,683704,663506,683705,663507,683743,663512,683744,663513,683741,663510,683709,683742,663511,683626,683747,683627,683748,683745,663514,683625,683746,683740,683738,683691,683692,683695,663498,683693,683694,683732,663501,683700,683733,683697,663500,683737,683734,663503,683735,683729,683730,683728,683681,683685,683682,683721,683722,683689,683686,683687,683725,683726,683723,683690,683724,683718,683719,683716,683720,710062,705221,679471,663031,620438,710129,574187,662602,706298,714401,714861,708780,701872,457482,715315,715475,710532,715240,712017,1885,689519,712821,589994,641625,711330,642103,625848,714520,589995,714918,590259,590249,718001,719537,690238,719761,718397,659812,710532,680306,720268,715179,720577,713211,715380,720949,540053,590021,679489,679493,719353,712202,708376,720490,424441,701643,710938,719618,721611,590021,679489,679493,720209,715669,712202,708376,717364,719015,711995,720724,720426,720684,720836,723981,663052,724477,718294,620218,718007,719655,725717,722872,671952,725136,716581,725435,680515,726430,722155,726390,575159,724364,714776,727080,724865,723343,725682,708885,725323,726154,728273,688007,703464,707955,590206,679456,712077,730095,728120,729881,712368,730120,590125,721302,729737,726885,729175,725471,710225,708904,706407,730247,726282,720284,730129,730910,729499,730228,728783,590103,590258,720066,729066,716686,732675,730384,712664,730252,730688,708025,730255,730191,701383,734516,734040,734520,734240,735372,734377,730591,642987,735957,728907,719261,735899,735050,733825,722160,728185";
        Set<String> allNoDataEis = Arrays.stream(allNoDataEiStr.split(",")).collect(Collectors.toSet());
        Set<String> allNewPaymentNoDataEis = Arrays.stream(allNewPaymentNoDataEiStr.split(",")).collect(Collectors.toSet());

        allNoDataEis.removeIf(allNewPaymentNoDataEis::contains);
        System.out.println("oldPaymentNoDataEis=" + Joiner.on(",").join(allNoDataEis));
    }

    @Test
    public void paymentTest() throws IOException {
        //格式[1,2,3]
        List<String> eiPayment = getTenantIdsByFile("C:\\Users\\<USER>\\Desktop\\回款数据统计\\2021_12_17\\回款描述在企业库的企业.txt");
        System.out.println("eiPayment size=" + eiPayment.size());

        Set<String> eiPaymentDistinct = Sets.newHashSetWithExpectedSize(20000);
        eiPayment.forEach(x -> {
            if (eiPaymentDistinct.contains(x)) {
                System.out.println("eiPayment duplicate=" + x);
            } else {
                eiPaymentDistinct.add(x);
            }
        });
        //格式[1,2,3]
        List<String> eiOrderPayment = getTenantIdsByFile("C:\\Users\\<USER>\\Desktop\\回款数据统计\\2021_12_17\\回款明细描述在企业库的企业.txt");
        System.out.println("eiOrderPayment size=" + eiOrderPayment.size());

        Set<String> eiOrderPaymentDistinct = Sets.newHashSetWithExpectedSize(20000);
        eiOrderPayment.forEach(x -> {
            if (eiOrderPaymentDistinct.contains(x)) {
                System.out.println("eiOrderPayment duplicate=" + x);
            } else {
                eiOrderPaymentDistinct.add(x);
            }
        });

        //TODO 拉取最新配置
        Set<String> configedEis = getConfigedEis();
        //格式{"ei1":1,"ei2":2}
        Map<String, Integer> paymentNumMap = getPaymentNumMap("C:\\Users\\<USER>\\Desktop\\回款数据统计\\2021_12_17\\有回款数据的企业.txt");

        Set<String> paymentInSysAndOrderPaymentInTenant = Sets.newHashSet();
        Set<String> paymentInTenantAndOrderPaymentInSys = Sets.newHashSet();
        Set<String> paymentInTenantAndOrderPaymentInTenant = Sets.newHashSet();

        eiPaymentDistinct.forEach(x -> {
            if (eiOrderPaymentDistinct.contains(x)) {
                paymentInTenantAndOrderPaymentInTenant.add(x);
            } else {
                paymentInTenantAndOrderPaymentInSys.add(x);
            }
        });

        eiOrderPaymentDistinct.forEach(x -> {
            if (!eiPaymentDistinct.contains(x)) {
                paymentInSysAndOrderPaymentInTenant.add(x);
            }
        });

        Set<String> eiOfDescInTenantDb = Sets.newHashSet();
        eiOfDescInTenantDb.addAll(eiPaymentDistinct);
        eiOfDescInTenantDb.addAll(eiOrderPaymentDistinct);

        Set<String> caEnableEis = getCaEnabledEis();

        /*//开启了客户账户且描述在系统库的企业
        Map<String, Integer> ei2NumCaSysMap = getPaymentNumMap("C:\\Users\\<USER>\\Desktop\\回款数据统计\\开启了客户账户且回款描述在系统库的企业数量.txt");

        //回款描述在系统库且开启了客户账户，回款描述降级企业，然后放入黑名单
        Set<String> degradeEis = Sets.newHashSet();
        ei2NumCaSysMap.forEach((ei, num) -> {
            if (!configedEis.contains(ei)) {
                degradeEis.add(ei);
            }
            if (!caEnableEis.contains(ei)) {
                System.out.println("未开启客户账户企业" + ei);
            }
        });
        System.out.printf("回款描述在系统库且开启了客户账户，回款描述需要降级企业放入黑名单size=%d,企业：%s%n", degradeEis.size(), Joiner.on(",").join(degradeEis));*/


        Set<String> caWithPaymentInSysAndOrderPaymentInSys = Sets.newHashSet();
        caEnableEis.forEach(ei -> {
            if (!configedEis.contains(ei)) {
                if (!eiOfDescInTenantDb.contains(ei)) {
                    caWithPaymentInSysAndOrderPaymentInSys.add(ei);
                }

            }
        });
        System.out.println("开启了客户账户，回款与回款明细都在系统库的企业(回款与回款明细描述都需要降级)：size=" + caWithPaymentInSysAndOrderPaymentInSys.size() + ",:" + Joiner.on(",").join(caWithPaymentInSysAndOrderPaymentInSys));


        Set<String> paymentDegradeEis = Sets.newHashSet();
        paymentInSysAndOrderPaymentInTenant.forEach(ei -> {
            if (!configedEis.contains(ei)) {
                paymentDegradeEis.add(ei);
            }
        });
        System.out.println("回款描述在系统库，回款明细描述在企业库的企业（回款降级）,size=" + paymentDegradeEis.size() + ",:" + Joiner.on(",").join(paymentDegradeEis));


        Set<String> orderPaymentDegradeEis = Sets.newHashSet();
        paymentInTenantAndOrderPaymentInSys.forEach(ei -> {
            if (!configedEis.contains(ei)) {
                orderPaymentDegradeEis.add(ei);
            }
        });
        System.out.println("回款描述在企业库，回款明细描述在系统库的企业（回款明细降级）,size=" + orderPaymentDegradeEis.size() + ",:" + Joiner.on(",").join(orderPaymentDegradeEis));


        LongAdder totalAdder = new LongAdder();
        //回款描述在系统库 且 有数据的企业
        //需要刷数据企业，描述走系统库
        Set<String> paymentInSysWithData = Sets.newHashSet();
        paymentNumMap.forEach((ei, num) -> {
            if (num > 0 && !eiOfDescInTenantDb.contains(ei) && !configedEis.contains(ei) && !caEnableEis.contains(ei)) {
                paymentInSysWithData.add(ei);
                totalAdder.add(num);
            }
        });
        System.out.println("数据量：" + totalAdder);
        System.out.printf("回款与回款明细描述都在系统库且未开启客户账户，回款有数据且已过滤新回款企业size=%d,企业:%s%n", paymentInSysWithData.size(), Joiner.on(",").join(paymentInSysWithData));

        totalAdder.reset();

        //回款描述在企业库，且有数据的企业（过滤已灰度新回款的企业）
        //需要放入黑名单企业 - 可能存在一个在企业库 一个在系统库 需要描述降级到企业库中
        Set<String> eiWithDescribeInTenantAndHasData = Sets.newHashSetWithExpectedSize(5000);
        eiOfDescInTenantDb.forEach(ei -> {
            if (!configedEis.contains(ei)) {
                int num = paymentNumMap.getOrDefault(ei, 0);
                if (num > 0) {
                    eiWithDescribeInTenantAndHasData.add(ei);
                    totalAdder.add(num);
                }
            }
        });
        System.out.println("数据量:" + totalAdder);
        System.out.printf("回款描述在企业库，且有数据的企业（过滤已灰度新回款的企业）size=%d,企业：%s%n", eiWithDescribeInTenantAndHasData.size(), Joiner.on(",").join(eiWithDescribeInTenantAndHasData));
        totalAdder.reset();

        //回款描述在企业库，且无数据的企业（过滤已灰度新回款的企业）
        Set<String> eiWithDescribeInTenantAndNoData = Sets.newHashSetWithExpectedSize(5000);
        eiOfDescInTenantDb.forEach(ei -> {
            if (!configedEis.contains(ei)) {
                int num = paymentNumMap.getOrDefault(ei, 0);
                if (num <= 0) {
                    eiWithDescribeInTenantAndNoData.add(ei);
                }
            }
        });
        System.out.printf("回款描述在企业库，且无数据的企业（过滤已灰度新回款的企业）size=%d,企业：%s%n", eiWithDescribeInTenantAndNoData.size(), Joiner.on(",").join(eiWithDescribeInTenantAndNoData));

        Set<String> eiWithDescInTenantAndDataAndNoCaEnable = Sets.newHashSet();
        eiWithDescribeInTenantAndNoData.forEach(x -> {
            if (!caEnableEis.contains(x)) {
                eiWithDescInTenantAndDataAndNoCaEnable.add(x);
            }
        });
        System.out.printf("无数据且未开启客户账户1.0的企业,size=%d,企业：%s%n", eiWithDescInTenantAndDataAndNoCaEnable.size(), Joiner.on(",").join(eiWithDescInTenantAndDataAndNoCaEnable));
    }

    /**
     * 统计有回款数据且描述在企业的企业且最近一个月回款不活跃的企业ei
     */
    @Test
    public void paymentDescInTenantDbWithDataTest() throws IOException {
        List<String> eiPayment = getTenantIdsByFile("C:\\Users\\<USER>\\Desktop\\回款数据统计\\ei_payment_15674(回款描述在企业库的ei).txt");
        List<String> eiOrderPayment = getTenantIdsByFile("C:\\Users\\<USER>\\Desktop\\回款数据统计\\ei_order_payment_13174(回款明细描述在企业库的ei).txt");
//        List<String> eiLastMonthNotActiveWithPaymentData = getTenantIdsByFile("C:\\Users\\<USER>\\Desktop\\回款数据统计\\ei_8347_回款有数据_最近一个月回款不活跃的企业.txt");
        Map<String, Integer> paymentNumMap = getPaymentNumMap("C:\\Users\\<USER>\\Desktop\\回款数据统计\\ei_to_payment_num_map_8347_最近一个月回款不活跃的企业.txt");

        System.out.printf("最近一个月不活跃且有数据且回款/回款明细描述在企业库的企业\n%6s\t%6s\n", "ei", "数量");
        paymentNumMap.forEach((ei, num) -> {
            if (eiPayment.contains(ei) || eiOrderPayment.contains(ei)) {
                if (num < 100) {
                    System.out.printf("%6s\t%6d\n", ei, num);
                }
            }
        });

        System.out.printf("最近一个月不活跃且有数据且回款/回款明细描述在系统库的企业\n%6s\t%6s\n", "ei", "数量");
        paymentNumMap.forEach((ei, num) -> {
            if (!eiPayment.contains(ei) && !eiOrderPayment.contains(ei)) {
                System.out.printf("%6s\t%6d\n", ei, num);
            }
        });

    }

    @Test
    public void parseRecentNotActiveTenantTest() throws IOException {
        Map<String, Integer> recentNotActiveTenantId2NumMap = getPaymentNumMap("C:\\Users\\<USER>\\Desktop\\回款数据统计\\最近一个月回款不活跃的企业.txt");
        System.out.printf("最近一个月不活跃的企业：\n%8s\t%6s\n", "ei", "数量");
        recentNotActiveTenantId2NumMap.forEach((ei, num) -> {
            System.out.printf("%8s\t%6d\n", ei, num);
        });
    }

    @Test
    public void getRecentNotActiveAndNumLT1000Eis() throws IOException {
        //最近一个月不活跃的企业，且回款数据量小于1000的企业
        List<String> recentNotActiveAndNumLt1000Eis = getTenantIdsByFile("C:\\Users\\<USER>\\Desktop\\回款数据统计\\最近一个月回款不活跃且在企业库的企业.txt");

        //过滤已灰度新回款的企业
        String configedEiStr = "589981,589988,589989,589998,590002,590003,590013,590019,590020,590022,590024,590026,590027,590028,590029,590035,590036,590037,590038,590039,590040,590041,590045,590049,590053,590054,590060,590066,590071,590072,590073,590074,590076,590079,590080,590082,590087,590088,590092,590095,590096,590097,590098,590100,590101,590102,590104,590106,590107,590113,590114,590115,590116,590117,590118,590119,590120,590121,590122,590128,590132,590133,590137,590139,590141,590143,590147,590148,590150,590151,590152,590154,590155,590156,590157,590158,590159,590160,590163,590164,590166,590169,590170,590172,590177,590178,590180,590181,590182,590183,590184,590185,590186,590187,590194,590195,590197,590198,590199,590203,590204,590205,590207,590208,590210,590211,590214,590218,590221,590222,590223,590224,590226,590228,590229,590231,590232,590233,590235,590236,590237,590240,590241,590242,590244,590245,590246,590248,590249,590250,590253,590254,590255,590256,590260,590261,590266,590267,590268,590269,590271,590272,590274,590275,662524,662949,662951,662952,662975,662977,662988,662994,662995,662997,663004,663045,663059,663060,663455,663457,663458,663459,663460,663461,663462,663463,663464,663465,663466,663467,663468,663469,663470,663471,663472,663473,663474,663475,663476,663477,663478,663479,663480,663481,663482,663483,663484,663485,663486,663487,663488,663489,663490,663491,663492,663493,663494,663495,663496,663497,683674,683672,683711,683678,683676,683714,683715,683712,683713,665482,665478,665479,683662,665485,683663,665483,683661,665484,683666,683667,683664,683665,683670,683668,683669,665477,665386,665471,683651,665472,683652,665387,665473,683653,683777,683654,683660,683657,683658,683650,687889,683641,683642,683643,683648,683646,683647,683639,683640,663515,663516,683751,683754,670800,683634,683755,683631,683752,683632,683753,670799,683637,683638,683635,683636,683630,683628,683749,683629,683702,683703,663505,683701,683706,663508,663509,683704,663506,683705,663507,683743,663512,683744,663513,683741,663510,683709,683742,663511,683626,683747,683627,683748,683745,663514,683625,683746,683740,683738,683691,683692,683695,663498,683693,683694,683732,663501,683700,683733,683697,663500,683737,683734,663503,683735,683729,683730,683728,683681,683685,683682,683721,683722,683689,683686,683687,683725,683726,683723,683690,683724,683718,683719,683716,683720,710062,705221,679471,663031,620438,710129,574187,662602,706298,714401,714861,708780,701872,457482,715315,715475,710532,715240,712017,1885,689519,712821,589994,641625,711330,642103,625848,714520,589995,714918,590259,590249,718001,719537,690238,719761,718397,659812,710532,680306,720268,715179,720577,713211,715380,720949,540053,590021,679489,679493,719353,712202,708376,720490,424441,701643,710938,719618,721611,590021,679489,679493,720209,715669,712202,708376,717364,719015,711995,720724,720426,720684,720836,723981,663052,724477,718294,620218,718007,719655,725717,722872,671952,725136,716581,725435,680515,726430,722155,726390,575159,724364,714776,727080,724865,723343,725682,708885,725323,726154,728273,688007,703464,707955,590206,679456,712077,730095,728120,729881,712368,730120,590125,721302,729737,726885,729175,725471,710225,708904,706407,730247,726282,720284,730129,730910,729499,730228,728783,590103,590258,720066,729066,716686,732675,730384,712664,730252,730688,708025,730255,730191,701383,734516,734040,734520,734240,735372,734377,730591,642987,735957,728907,719261,735899,735050,733825,722160,728185,562735,561416,6,7,490589,550739,511164,562754,561427,549743,464293,703176,703177,703174,728477,703175,703172,703173,703170,703171,634905,464269,559122,703178,703179,464274,715167,716496,464270,703187,703188,703185,703186,703183,703184,703181,728468,728467,703189,703180,104743,452265,307401,716489,703198,703199,728496,703196,703197,703194,703195,703192,703193,297511,465575,559101,464251,703190,703191,728491,727151,727153,633607,464258,489537,549776,535139,547113,368408,452241,610975,12438,296292,60077,139413,85375,453565,427297,86690,478884,139417,104793,96019,97343,98674,464234,97340,382711,60086,86694,670585,96046,97375,683894,683895,37717,683892,683893,683898,683899,683896,683897,104758,452250,97362,98692,439285,13759,97394,439231,297592,609943,453551,382789,513778,609938,439255,537722,102155,610901,513769,478812,63985,705833,428532,704501,127384,683832,683833,683830,683831,48344,683836,683837,717826,683834,48341,683835,683838,683839,657541,657542,683840,657540,683843,683844,683841,683842,683847,683848,683845,683846,683849,403213,48325,103398,23026,86649,683810,632278,683811,683814,683815,139399,717845,683812,683813,683819,683816,683817,48315,703206,703207,703204,703205,703202,705865,703203,703200,703201,683821,729807,683822,683820,683825,439204,683826,683823,683824,683829,717833,683827,683828,98648,585490,209005,683880,403273,683872,683873,683870,683871,683876,683877,683874,683875,84006,138005,683878,683879,59057,683890,683891,596142,683883,683884,86659,683881,683882,610890,681224,59059,60048,683888,681226,683885,249952,683886,608573,683889,380058,154980,73366,98662,683850,683851,683854,683855,683852,224661,683853,683858,237966,237963,683856,142999,683857,669584,98655,683861,683862,683860,683865,380078,683866,683863,84017,683864,683869,683867,683868,703253,703254,703251,703252,703250,703259,98727,285674,47137,645446,512357,669412,645445,645444,645443,704625,62776,705951,105907,487074,500372,587908,587904,704616,717944,48462,512342,12,645451,13,538997,189517,475098,727296,153548,61456,703304,703301,402010,382791,729909,402009,587915,729908,587914,596062,178836,513668,645448,645447,717930,430993,476392,333816,441647,728616,610798,657453,574611,729938,84105,47111,715305,438003,3790,561313,537645,103288,559093,1132,729934,319526,705988,575957,620107,307545,247390,403395,703298,585375,703295,703296,403398,416629,499012,703294,703291,645488,703292,74795,596028,610777,703299,596027,715282,380187,142854,86783,50,585367,235404,476386,392185,97425,403389,610768,596014,331288,429950,596016,563918,610773,632188,283037,50801,727287,501718,235431,463039,153537,596047,705931,463035,596049,550641,490657,548334,105923,84142,536352,717909,13793,584071,634814,715291,369856,442924,86,549650,559043,452363,587992,727343,727344,560036,102001,727340,394833,703257,727345,573345,703258,501612,703255,703256,559046,438063,715241,464350,250965,105987,405922,584113,703264,51927,559153,728541,477671,703265,727210,703262,704593,703263,703260,703261,163068,703268,488308,703269,729875,703266,728547,489637,716561,560176,49814,703273,730891,560178,452380,285619,464323,331121,452379,47074,236670,560189,451042,670477,62600,394827,548482,716580,560195,51954,562772,703210,405853,703217,560117,729847,703218,703215,728518,703216,62613,704545,703214,703211,703212,318127,415381,87911,703208,703209,382881,404514,51968,537609,703220,703221,560121,102053,74601,512302,441659,610806,703228,703229,704559,728504,703226,703227,703224,703225,704554,703222,703223,439341,477628,703219,115356,405838,439343,190661,609823,703231,476197,703232,585430,727201,703230,104680,103352,609809,703239,560139,703237,703238,703235,525608,703236,729864,703233,681163,729866,703234,103348,177695,703242,704573,703243,703240,703241,728520,560146,703248,729859,703249,703246,393539,703247,703244,728525,729856,703245,117990,681164,490485,681166,23076,585437,585436,12505,703297,596023,96194,12505,513677,703293,681116,502970,404516,560192,271081,732405,736732,399568,459717,80515,422426,724907,423754,700933,67551,44891,17298,447720,31582,422435,433103,44887,43550,399544,700960,17260,614487,723600,79553,158579,386264,724920,709304,80531,31561,337294,651486,591252,592586,20897,80567,580596,66269,592577,410419,592576,54288,650150,435721,728495,92567,649186,229436,700918,216106,93883,688762,688763,146572,146566,700921,675454,592545,724990,711679,724991,721004,337239,724989,578238,708020,567595,735656,711683,734325,591205,591207,313283,735663,56828,733003,566277,735665,554254,708046,722351,710374,710373,196894,217499,734308,722341,593886,554245,337212,555567,709383,735604,325219,578203,723620,723615,712955,724943,709324,724942,56862,301263,664708,613194,709331,592520,708005,724967,724966,29280,593847,711649,67532,711646,724964,724956,56881,581877,723628,711652,735601,326520,567585,291435,581929,339936,471396,292773,267797,482047,556953,544983,470079,326620,593907,592602,291454,669558,580618,733054,733057,580629,542369,722373,482016,470028,458390,278498,735692,735693,735690,735691,735694,472682,735695,470026,722365,708076,482003,517390,554337,543694,734378,733009,722393,445092,721063,567605,497967,734343,734349,567616,721055,302609,721054,554322,735681,469035,735686,735689,735687,735688,733027,497954,460669,44820,91139,461994,520932,433057,650069,447687,421081,689999,520928,543613,399521,399522,448980,350257,386202,472617,436995,291499,556903,435691,434367,460617,448990,544918,135839,674089,651324,43695,409572,196713,68990,157109,434530,591196,204205,689916,81998,78346,723716,689918,689919,41004,92635,433217,44999,53052,446533,689925,93958,710400,675334,206890,591133,92659,591126,91325,91324,385072,30332,80672,689987,92647,255824,243815,592488,507822,324071,158431,230538,18681,690957,65073,734437,735768,241211,338691,721122,709473,709475,709477,709479,641918,710474,735771,733114,627626,733119,578132,722438,567480,733120,543498,735749,735747,81901,722476,721139,735752,325364,735759,723795,708178,722461,530172,578159,710480,710485,734434,735767,734436,735764,593713,709431,79617,734408,723733,81921,734410,326665,735743,325314,593737,326648,710447,735703,593733,593734,735709,279843,9957,711780,735711,664617,568783,735715,566126,735718,30390,555498,637016,543539,542204,721192,314765,325412,543514,254590,567535,543507,364923,733185,302731,708187,708186,554233,708188,555555,554224,708183,708182,708185,531587,708184,7359,471476,568807,708190,554219,708191,363619,542242,529287,734499,721180,6056,721186,733130,734461,734464,721177,721179,734470,469153,470130,520818,445157,471436,663267,66291,109428,388949,730983,388947,726000,727333,236772,702058,62701,285728,703388,597310,584009,471445,727364,703395,319598,506536,690862,343217,471412,472740,584033,74703,20967,687230,548365,549693,728631,729961,727300,728636,189547,702004,729966,575970,44901,19949,532838,619186,728625,110459,140283,362362,532840,345847,320565,459776,518559,333855,560019,728655,573320,86719,687264,61418,715341,246130,472706,472707,587969,434484,716678,32901,716675,434469,729973,728641,399633,572008,447787,729977,532825,86717,387666,675291,575999,23197,585318,645428,730953,73409,562692,538862,287973,626246,375353,710505,735809,464078,103608,156151,490355,536213,91403,726085,211097,591042,578098,711829,690816,440096,448807,250696,464084,67792,435524,410226,562515,144180,424874,464047,53161,681083,464049,235072,77131,448836,447504,587807,488024,626218,91425,616899,261317,77122,337056,275956,711850,711852,710520,92784,92782,412821,702081,91456,156107,592341,687200,592344,464031,77155,550544,102312,676552,549561,726044,727375,513593,260047,62900,92770,80793,580368,591039,592364,690829,464005,592366,103629,464012,507700,398021,453387,548210,477315,452057,711803,436804,727398,89141,711805,50939,711801,536238,43745,464015,439089,690834,690833,169429,548205,89146,734559,48622,721240,440012,610740,466651,555357,492933,735898,734568,556685,722560,723897,231517,580324,400,733240,681027,128976,103699,566035,734537,404387,49933,48602,610715,555342,440039,466625,610714,466623,220898,340623,96282,567370,452016,567373,567372,733210,627526,592316,734547,49922,557994,610705,440028,557990,681000,415095,439041,140664,735888,735885,568638,735847,467938,298669,653820,723864,554074,153918,54434,538814,78408,711885,735854,734521,41191,555391,594917,735857,711865,570961,708231,722523,668075,525519,96292,139620,382517,7407,609703,428351,41173,711899,593615,722552,721224,443982,582957,681036,114376,127677,735832,542074,567336,711887,734507,593603,626252,555378,500218,709589,416380,711895,567341,7428,581632,735844,538809,735842,621356,97512,544745,417639,684921,60234,175212,6105,716735,483139,74889,517113,61574,163227,382433,531435,703417,683600,684936,716722,471167,47236,704766,634697,733290,728728,281842,49894,458175,152597,568743,567411,703441,6145,431954,519791,660964,60269,213720,293811,47214,544788,96226,98884,721285,73582,332267,23249,542133,48538,353939,96215,585261,573279,370499,73590,704709,632063,84236,671670,496402,542108,405677,97571,704720,610641,35218,684940,555415,734584,609656,139593,734589,544755,98895,670318,431915,721296,532791,611954,461781,97562,98893,471107,472437,281872,84259,49840,85580,573229,77070,91372,574553,715486,472404,575888,434143,213776,548276,435475,89056,587883,726122,43721,726129,715472,715474,225763,664468,676487,676486,18789,458124,727480,110760,308582,307253,689757,30406,434163,108439,585223,31734,321562,109788,702191,726146,536272,43703,550553,574587,278267,308590,250711,322886,688491,703457,663196,86818,62858,544707,86826,3951,646603,715449,307235,715445,704793,728741,561217,563879,422123,703469,424785,728744,351361,716768,489281,647946,491599,703473,703474,703471,73526,703470,703479,703477,702147,262739,703478,728775,703475,575871,703476,702145,728777,715464,477279,676494,676492,435454,587840,676493,703484,702153,703482,703483,703480,703481,573218,703488,727433,703486,703487,506449,390948,388630,586528,307227,710624,563701,285436,525428,334924,65249,334920,273443,551711,501460,512105,551719,735931,66586,580293,710618,734607,374132,423652,709641,708312,665715,593592,436950,476179,104825,723960,708321,735909,721301,709654,78582,723954,423655,602144,575705,710635,538734,78574,447612,310941,488133,90205,538728,411696,104803,476158,89222,452165,711910,663111,592220,593550,579257,690724,663104,663102,549449,580232,476125,89255,592218,711904,709604,726161,92896,726163,439180,207995,42559,489440,500163,77269,102208,435605,17583,512179,17584,710604,41216,28237,464126,581586,476103,261472,663123,664453,594892,465473,232945,663118,464140,723903,503,274788,477447,720031,440138,683560,684891,683561,242308,684890,464104,466763,610611,683551,683552,720029,684889,684888,684887,719044,684886,683556,719043,732021,732022,732023,628713,732025,326478,325127,48730,610606,722682,683571,683572,464112,683570,683564,440131,683565,683562,684893,683563,683568,683569,684898,683566,734692,683567,733365,568573,732038,594867,67847,593531,555222,721383,80811,721389,683531,683532,683530,683535,439170,683536,683533,683534,582878,683539,368315,733330,12774,735994,683537,734663,609619,683538,543244,657295,657293,568584,733338,670232,543228,733337,404497,732008,555210,532596,719051,704810,532594,720043,683550,683542,556539,555208,683540,568591,683541,683546,273497,683547,54544,683544,581559,683545,581558,582889,683548,734674,733343,683549,734673,734676,452125,734675,734677,302496,582881,570838,611908,570832,721322,707016,683597,416473,683598,721317,683596,683599,439113,735973,719008,735979,733316,734647,711989,723971,555273,555275,722635,711993,711994,78515,735983,733321,734651,733322,139508,734614,569866,66558,67889,720010,683582,683583,683575,683576,582836,297481,683574,683579,556589,683577,683578,719021,381337,368359,543287,156096,641734,156099,734622,567217,734624,734627,496591,570866,734629,570865,683590,720000,683593,723997,683594,723996,683591,723995,683592,267618,723994,683586,683587,683584,78532,683585,683588,683589,78535,719010,734630,514734,710680,439124,453411,687083,58033,225800,703536,533971,517002,484598,417762,573194,728828,532632,314516,403106,46043,415103,326510,46041,684807,585188,544617,687094,404432,46038,570922,58044,35385,704876,59370,189753,279655,58049,505014,569941,393201,98988,609575,458290,151149,404419,415122,570939,96317,46017,405742,370593,727516,429773,381240,417799,84337,568633,60382,225849,97679,720074,555322,659895,659893,719087,60396,684847,84366,716811,609557,733399,58082,6289,721393,670213,704833,610526,573157,610523,279698,659887,683528,49982,683529,542002,684857,586466,58093,58092,47316,545976,610517,557960,97690,177733,139478,733378,585148,472542,720080,720081,267696,569912,719097,720085,97687,557949,732052,49961,404448,48630,83044,544641,545972,472552,83040,609526,89176,74911,651048,108344,472526,599741,30543,30549,548158,562462,434249,89163,42511,28227,663020,663018,574447,688339,587752,647800,89191,663053,460520,663051,472504,678992,346965,250823,651071,587784,536156,726265,726261,691955,488097,472516,690637,647809,663038,663039,690633,663037,458220,50982,561162,587774,322981,727541,562419,563749,473819,663075,545919,423561,448858,490397,663069,435551,98917,461844,72301,663067,726206,592297,34097,663065,663066,107065,545907,548183,362123,308691,436870,727561,518319,519647,726233,561107,726239,550491,386104,675046,562440,548177,728882,663082,562444,687035,536192,663085,726224,663086,663084,423594,586409,575757,259205,508985,586404,690673,586401,676385,675051,734716,480798,52051,173719,500014,311925,539946,580151,734720,539962,734727,477152,593461,592135,53390,708434,390769,76012,576900,1465,576902,92966,642973,299729,734733,707110,91670,593497,719105,77375,722747,683497,449922,710771,710770,707127,42691,539942,564947,424642,540913,707125,708456,707126,721410,722741,722739,539938,340450,92989,576929,726292,513367,390794,593434,664319,639364,479756,478425,549320,709724,726287,593422,652324,77383,42679,76055,689625,537339,91679,327475,106924,64099,40002,39015,594785,663013,540968,663009,663008,303491,722708,454478,710710,39002,594770,109180,524016,478417,501391,88059,581471,478414,721483,720156,720155,720158,720148,555115,582736,684765,733470,48861,173791,567112,80902,570763,555111,555114,720137,609501,683447,683448,581415,609505,733447,671430,131940,67962,707196,707197,611803,707198,557764,35537,684741,105608,642903,581425,683415,734780,721492,544434,720161,720160,703602,721493,48839,441113,721498,54662,314107,657168,658496,273137,733464,733463,657163,732135,732136,733467,733425,670165,733427,244643,92940,708462,298425,556496,721437,720104,719126,720106,173755,316738,384947,497588,628634,683491,404191,555151,467701,721424,722755,484271,732110,77307,496247,77309,710783,404187,404188,570739,556477,720134,556472,720133,707158,720136,720135,722796,52033,721457,720128,641618,399193,417462,734744,733416,106995,721452,707165,484245,683472,708499,708498,720125,722778,484252,89305,79989,233995,257949,155853,431702,97751,418727,728948,84447,609475,60475,532510,73799,472260,59499,31901,46152,609469,716963,608139,321396,569827,569829,570814,585073,104245,371571,703678,429400,611766,548098,60499,459263,84465,608128,702351,585089,570824,406711,569833,47466,96430,155867,97761,610428,550396,550397,35488,382211,645124,543215,58197,720198,720199,155819,497506,575686,158090,658441,703606,255379,321369,646464,320052,587683,10184,71179,447243,719193,531242,574372,704951,281657,382235,684733,684738,54603,716921,214837,609425,104285,585034,48768,585030,545856,131868,610400,733490,728928,533863,733492,684706,54615,544532,54614,586358,585045,353713,353719,47421,669107,71191,280302,282961,609409,84492,563663,537376,537378,461519,588967,564998,725048,689545,576973,309699,437891,153282,713068,727695,727696,90279,512068,335969,689555,688224,727697,727698,551671,727699,726368,280368,308377,548025,538685,377715,725062,538686,588988,726399,447235,713084,308349,411282,726384,462838,435238,561036,622493,726381,462830,462831,689534,691834,225537,100727,551645,474819,690510,524050,588982,100722,728991,588920,647700,689580,588922,564956,73742,519501,592188,588929,727664,727665,376406,576931,491364,322613,676274,154570,72420,425872,550383,714342,548063,489041,714341,59457,592181,251868,275814,714343,714346,714345,727683,96407,701053,726354,18952,478372,449851,97731,465059,449852,524087,576959,310616,690542,575629,100752,153260,17624,727674,726343,691871,701061,727675,725017,713033,575633,610473,84410,678805,710865,453291,678800,76141,721520,709875,389877,100697,719207,734844,580051,710858,308409,592010,678818,709886,207703,491551,77467,721506,89448,453278,286541,40168,540806,556398,351287,492872,52196,250541,491545,642868,719229,719227,100690,579089,555058,262524,173608,592035,721531,683386,722855,11618,719211,721527,721528,710884,603225,719217,400727,478552,540865,665530,579010,489207,90481,165183,678851,722808,666858,453243,725073,454572,708516,734803,479895,640242,579024,594632,734808,338244,593303,60603,76175,713098,399264,106803,399228,708525,678828,710849,666885,593334,691800,513261,467882,580014,501283,73915,454596,537206,85902,340539,412740,479868,302298,543019,611707,466527,558997,34346,719289,683315,34344,34343,733594,197641,429596,429591,441219,719273,314257,443874,719279,719277,683327,683324,732272,7724,629818,720291,720292,65429,467830,733570,274599,715700,302241,733574,34328,232756,733579,64102,594614,48951,546976,546975,720286,479828,546984,684638,732254,733584,733587,47614,569607,571922,670048,104222,556379,720234,657078,721557,719242,455826,657071,76106,732220,10335,471091,65450,65452,569616,555035,557694,683366,315537,89408,556359,555028,104228,657064,732233,657065,733564,595912,66798,569625,185605,497694,64138,720250,720252,719269,557679,628530,583922,732200,90419,628537,302201,495028,65460,158015,734869,105543,732208,732209,720244,720243,198924,721568,719254,557667,719253,472395,732210,734876,293734,155732,405534,702446,703777,610338,611667,97870,610340,703769,472364,715768,715762,713102,611658,702454,484345,611662,279418,83224,533732,714420,131781,646315,725100,105455,96569,519443,58292,727766,142431,472348,635666,105448,647636,586279,702472,484328,727751,688161,714439,446099,429534,610305,726422,533716,611642,661932,472351,304951,713118,714443,609329,544425,447371,727701,727702,104182,35591,727700,447378,472327,545763,588895,647676,587560,422089,703741,486965,471006,173584,472333,447390,714409,544401,462986,422095,703753,520437,280437,727716,714410,498931,587586,485619,574270,462996,727709,498929,22262,703759,660640,458021,732296,714404,213649,725165,39112,461631,538582,449997,308483,713181,689414,213658,641478,564881,725153,460319,726488,41429,538595,676124,365832,587511,393081,435343,587531,678745,462946,725189,575552,30760,85835,588854,666794,238973,710913,461622,575562,691717,266176,726451,73866,727784,563502,97832,97833,462924,691789,42721,29749,474902,688128,474906,690443,690442,1659,621024,563511,97827,622356,58241,29756,593386,248340,576827,714465,519401,678788,409077,58252,691768,448645,610365,690427,508755,689436,661904,308422,309755,97849,462912,97841,701187,422019,550234,714484,307105,690431,250639,16429,478233,376207,734955,733626,734959,214593,480557,720310,377536,707334,390517,568283,466254,719322,627104,734961,719327,710993,275508,642724,732305,708674,493850,76255,341569,526415,425700,238547,627115,734970,341565,39299,733607,733609,480536,720334,720329,719342,683259,412419,556266,109385,378875,708699,720316,708625,678724,292049,653423,480515,570553,710949,540746,570550,708620,539750,570551,442243,480510,11708,480518,431595,734922,725194,725193,481833,238593,710930,708636,734925,734928,570567,707301,388212,526457,455537,88278,569584,582540,41572,189487,709973,721621,708644,719309,710955,709988,538412,593206,538415,40237,654767,388242,388240,678719,481817,583895,583896,666747,390595,546887,533576,359705,720396,659576,732380,556207,556206,568202,684528,54876,195342,430204,595801,545548,595803,719394,684540,443510,685863,684537,684536,274247,53558,684539,684538,731066,157078,303246,569542,546867,568217,532222,594502,77531,53560,460095,583844,105844,733695,91822,731033,77539,105848,104517,472069,545529,219437,405226,570547,671209,154813,196667,142840,702505,582526,5203,732372,582522,731048,732339,104584,706041,405293,47737,684564,719366,718033,76225,684567,52258,498676,733672,732343,133073,103262,574090,586073,182008,706055,720340,720344,720336,720335,419878,35741,733681,733684,76218,133081,733686,545595,733685,718028,733645,586085,733649,502407,64259,684551,684550,720375,484018,684544,684543,76242,684542,684541,684548,357113,419888,684547,684546,684545,533588,684549,472022,732325,733657,732328,5256,64268,558883,77560,77561,684553,502402,684552,532269,718042,103235,583810,628424,734992,732332,732335,734996,731004,587475,622202,509981,103176,70034,447055,611547,472007,714559,713222,17843,97989,97982,688069,610207,546917,178719,610211,713214,713213,571905,459019,586165,103152,623510,726552,232481,432810,587490,70052,727889,463995,702588,574184,245755,611525,57084,551497,688028,245761,551490,727878,33060,420834,418526,34398,713237,534925,624820,564795,647546,611509,727823,395327,424375,731091,727815,731092,69081,100938,544311,546974,54833,53508,673842,646222,405202,405201,546957,448329,558934,83388,82050,83382,69098,587451,701210,688075,673816,533626,660534,474603,727837,491185,726506,435052,726507,701209,298291,731072,731073,588792,661869,128483,703880,562166,546934,65512,419804,519354,419808,395319,533637,533636,255126,731084,214615,142766,507375,450665,435049,690394,463939,678647,395367,689303,691628,61967,335741,424329,475915,39238,451942,394040,438981,680969,88197,708714,622276,708718,563439,424340,691618,491157,475928,27256,308135,491158,605701,100987,73968,463915,73965,539774,395346,61981,41533,691603,609297,609298,641378,588731,540752,111609,226622,538450,61998,424361,58345,435001,451951,475900,239938,564713,609270,610258,725249,664040,549167,551477,609275,85973,581289,701285,294755,691654,42837,72660,58364,509933,42832,609269,84638,30851,382074,249311,85961,691647,593297,689320,593299,713284,424303,648803,30844,16538,153484,191739,280149,508624,84653,237317,252931,525186,676022,556173,732415,47811,22518,732417,453052,363010,390630,570478,708787,733751,631969,99004,557495,22523,570483,225362,147556,732430,455698,733723,581150,582481,453073,375012,594468,707474,721785,465014,719466,719464,340340,40394,641310,480649,593124,707482,721774,502317,684477,733741,732410,733743,679939,678607,135524,733702,678605,678604,262357,678603,678602,594402,708743,428058,208833,526323,719401,583749,501020,583747,583748,583745,678611,583744,678610,719406,583740,552608,678619,678618,678617,678616,595725,678615,678614,678613,99049,26085,292181,583758,582427,583754,678620,680935,455694,135549,541933,87090,571789,571788,84818,340307,582437,720407,159502,481931,541922,375049,539625,526308,84809,389694,594414,569480,442366,60864,480608,388356,224090,553904,219522,64311,116403,571721,64317,533452,339302,206216,417366,76301,731171,520145,731178,431672,399053,65651,104433,702612,671109,671108,130722,671107,315337,507156,672440,545418,46543,45210,154706,661787,533426,727915,51024,467609,715927,727910,387078,702635,595700,702631,358525,727909,702627,53697,52367,660443,287650,147597,88328,731130,496139,399097,330915,104474,496131,556121,158244,731104,671112,671110,732438,587294,720491,706185,533476,405394,370203,720496,47837,359895,357230,416010,443622,459184,11881,89678,384849,733771,233887,88343,104438,684441,615007,245844,615009,684438,87003,544123,29945,612755,115044,103049,562066,30943,562065,104395,702690,562069,726640,104398,115027,701368,115028,725311,575382,612747,636737,383407,474793,69178,726671,587375,104361,724015,700049,485425,70171,282815,678591,507229,510864,474762,420946,587369,678595,459147,587367,420949,104377,725331,104375,724001,460130,485413,712020,725329,417313,178615,636762,713358,54949,712023,624701,532201,279233,448466,727936,715965,450773,564681,713301,474742,588633,589967,714618,268567,727932,727933,727934,448452,701321,727929,727925,100827,17939,21196,714623,545511,78911,78918,405312,519246,727967,103070,116395,715981,335882,297084,726628,731194,269895,142636,420907,714654,214722,713323,587344,546815,549091,534851,155942,727955,727957,34470,726621,574025,726616,549096,713316,589993,436495,679857,589936,39357,59760,691502,423123,691505,576610,474704,60755,450731,71413,589929,736073,382158,736078,551302,736079,679874,526374,152085,395463,707513,491270,708842,60779,280239,610166,492596,610164,611496,526346,610163,736043,736048,679842,667889,589960,680827,69116,587300,562007,424488,576650,540637,707520,41649,526355,610154,609165,450744,14049,553946,564671,537056,624785,480590,593158,478271,609152,83429,712052,610147,82103,84760,736026,309538,582499,736027,712055,736028,454297,725352,724021,726689,391930,480588,537066,58482,594476,636701,84752,689227,678585,712047,725382,678548,509841,538360,725387,424433,448401,83459,736000,551336,140059,712072,553997,177335,491229,481898,481899,339395,28644,713393,712063,593174,54901,17997,736017,380948,731204,671057,671055,482978,731208,225006,706244,643817,719564,470988,671061,584998,671060,281184,581000,605558,671062,733876,481633,671067,731217,479334,594314,482960,721871,720543,444696,352006,76493,719556,444686,470995,59935,643827,402801,581015,479325,732554,569387,101679,733886,582360,732518,671038,719581,720572,47918,22611,570373,720569,720568,568064,629540,719584,327023,100313,733853,671041,733855,671047,733854,482944,671048,60936,593005,60934,595664,503535,58622,282489,733860,365336,733862,671053,642517,672380,671052,671051,539511,540501,71600,481601,733823,481603,240680,556092,733827,392979,401526,225057,57302,733833,733839,595602,707543,526213,418380,237019,553804,641206,733842,329639,671078,707559,40480,571667,571665,720530,469932,736084,582319,736082,582317,583643,583642,671081,671080,418398,679806,706238,289901,528849,736092,249011,671090,736099,671093,100366,671092,667835,26182,21384,65768,686943,686944,686946,731290,275348,686940,662973,686941,686942,6746,77759,686947,686948,686949,44018,646029,406337,545306,560962,497144,701403,251392,702731,686960,686954,686955,418347,686957,33365,533338,558613,686951,686952,274005,686953,584921,686958,686959,90706,44006,674941,560974,701413,147053,726713,701411,519047,75101,701412,445970,701410,661667,34684,51145,547963,498459,731276,569311,418354,433974,433978,546613,726700,75113,222820,51150,686935,731282,406307,433981,686937,199930,431320,686938,686939,731247,672348,671018,106522,405055,32005,51162,686987,686988,557338,686983,686984,686985,106514,731253,672352,546693,731257,671023,90750,720581,75136,433914,558661,719598,720579,719597,719595,719594,684336,588480,87105,731264,89763,433945,47950,686970,462469,686971,463794,405033,686967,718298,533349,686961,686962,686963,686964,731231,445928,686969,732564,671003,390380,674996,686980,686981,671008,686982,559971,560947,686976,686977,686978,702706,686979,686972,686973,686974,686975,546666,671010,448142,70279,724114,725446,724111,575252,725442,611305,551283,714793,508415,451789,258843,690127,510722,53705,397710,106454,726766,41700,724105,700156,690134,677176,736116,424197,690101,450475,725450,587262,725453,726785,725455,274074,637968,678482,636646,678489,385728,143416,678485,563225,563224,674922,674929,463732,725403,674928,463733,725405,674925,725400,674927,674926,449429,575203,636656,564562,650943,674931,713408,674930,546712,714738,533421,65736,700115,689199,686911,726716,726717,33287,52420,714743,510754,589850,691480,546704,726750,463716,463712,577893,726751,689167,725422,677182,691478,713445,690143,436147,186549,45247,105147,713429,674918,725413,105140,674913,674915,726743,635358,450426,510775,16702,581096,708947,707616,156787,708948,609072,708940,526245,721911,593080,142134,1972,84841,610065,721915,610063,385795,736186,721917,323503,641118,71518,526242,719601,154113,480263,412115,481591,72860,102925,736199,707619,514239,154120,588506,414789,653126,83529,129141,425441,630491,721936,720604,721937,719628,736168,71559,708975,15492,481579,437415,706318,538215,166115,708972,105183,166113,576522,719610,610034,611365,736176,57256,736177,736174,719619,397780,721928,719616,309204,481566,677118,40420,678447,595699,132779,551242,712174,102959,654486,593041,82224,677125,641158,677126,102952,736145,493549,101623,736149,102955,526284,712179,480222,708916,300843,481550,481551,594353,700197,126911,611342,736155,568094,677137,736159,494868,679794,480211,395142,156765,581070,724177,82248,470887,551217,680744,736125,438721,680745,494858,736124,680747,581067,156751,707605,71599,564508,609003,611324,82234,552535,28762,680755,736136,526271,340010,680757,470893,736138,631719,731324,479442,733988,707693,99251,101544,732662,413601,705040,570246,595521,75292,402921,102887,478115,719679,88595,455455,479421,438917,720690,684231,466111,582239,352140,684227,582235,582234,730009,733971,731310,456794,733974,705069,731314,730000,87282,731318,705063,731319,533288,720683,660291,545250,251401,719693,583573,583572,102865,731321,545251,733985,582243,179388,707659,400316,377464,303153,733952,667707,733954,138804,570205,572863,705008,718301,720614,99281,365475,719639,569221,718305,732630,718303,595516,57441,705015,497290,721976,611298,194955,584852,431494,569232,328434,706359,595505,705020,378796,720642,720641,720633,720632,721963,719653,720634,582208,100248,642437,690093,64552,701513,726812,726808,686825,702834,726805,45470,535866,329727,456731,535881,44133,730065,648564,587085,729077,63237,406451,389469,329731,729074,690099,689080,571505,535852,725501,207321,730092,209984,405110,534517,199801,599087,87216,497244,76568,45450,731396,210965,535848,731399,63252,219314,497241,713504,456716,63257,701545,263458,673507,417158,444744,700206,701536,584824,701537,569204,498566,661538,136293,686817,44105,63264,45431,106401,106400,388199,418407,559888,418409,575054,76581,718395,731373,162509,731379,45422,672235,575060,575059,258930,731381,672240,731389,535895,547877,717082,461257,419769,729063,87257,232313,245603,730046,346246,207370,288774,730043,717072,534571,76594,717074,151871,732695,686859,498526,731367,295912,725562,700261,689025,726899,283929,689024,680685,713589,736224,713588,588451,713585,736229,713584,53824,736226,713587,713586,588455,106336,95683,726889,551152,677051,576466,575139,451898,450566,690011,712242,677056,736238,448280,725584,463879,677017,724254,246965,552472,551140,372547,613826,677026,677027,736204,15526,736209,333009,463881,373867,713592,713591,197117,713593,553788,491096,713599,736214,713596,713595,713598,678363,713597,546607,616088,589737,522634,713547,690045,690044,15503,616073,649855,321020,324998,535960,563117,424268,449541,64526,257691,448213,700232,81089,576427,491075,576429,713534,438893,384538,712201,563123,713559,19086,551181,478091,448247,491069,604082,308024,491064,52552,714898,564464,322311,563132,689051,44038,63212,462510,32053,64549,63219,534601,690038,713556,105017,14295,679618,478071,294664,39591,424210,82303,526124,82304,424204,565733,201209,706419,565738,514120,719710,611267,503479,577711,10704,383241,565740,106386,642353,684191,396556,684190,541730,166009,653008,736282,82327,57365,553713,736287,736284,720728,719748,565750,437551,491029,155363,70349,564421,736290,719731,82316,736291,577733,450502,736295,401533,719736,156681,491018,724279,279075,724276,595575,28892,312933,83676,678335,736269,68049,725593,96974,724263,39544,570289,481673,57393,725599,570293,595571,654352,736275,281377,83665,736274,736279,677014,680657,677011,251504,214573,68057,541772,724297,724296,565707,41854,514192,279056,539450,9163,511915,466031,736240,654389,83692,719703,69386,719702,130023,53809,679647,401587,655699,565717,442096,539462,736253,736252,736257,82353,736256,87392,647189,595419,533175,705158,34866,468433,584756,730109,545164,705167,533180,672155,239403,684140,558469,632936,720783,20228,74087,684143,684142,406176,620927,731429,717161,571465,596768,559787,594101,21524,569150,684104,468411,57527,57529,98056,445797,717155,557106,717151,717152,730119,582123,269285,731405,457737,294222,684172,46806,706440,584719,684168,419463,731412,486288,660197,684182,631642,684181,660196,21516,631648,545192,470716,305466,719759,402609,569109,729109,533199,353149,392716,504676,378453,630330,719775,719771,713619,713618,474275,730197,713615,62016,725601,486255,419429,713622,713621,713624,474265,713623,88642,50038,88640,713607,730187,713609,713604,713603,474283,713605,147282,726919,559702,713610,348598,90983,713636,713639,264433,713638,33591,726951,50051,418114,636478,418115,421752,197508,604042,726948,377167,713644,713643,713646,87334,713645,713640,64690,713642,714973,713629,674714,474264,714957,713626,713625,713628,618673,713627,701666,726943,299110,51397,87329,499532,713632,713635,173565,713631,713630,588248,705198,365195,104102,673432,449273,714903,558435,717181,208315,406141,75361,717188,717189,559759,534458,589583,406135,729152,449262,106773,717172,75376,662767,276465,463593,717175,86019,558418,717173,87348,106769,564293,421704,87343,730177,730175,4397,730173,463553,50093,499508,726906,726902,421712,276457,535768,713602,713601,87376,499505,535782,730165,406118,730160,701620,75393,589590,588260,730159,591974,735009,540386,725688,463534,488836,551041,463532,40618,551035,576338,452879,81173,105348,588331,588333,667582,105364,322034,272906,540378,511807,713691,713690,713693,736356,713695,563042,589653,300698,589678,463516,155635,723040,155636,538040,28953,723049,53966,588353,736327,526066,736325,131677,576373,449243,65955,539386,666273,575036,678240,575039,452889,53970,735001,104006,589672,735008,81183,735006,325974,713659,713658,725645,700347,65966,551088,104062,700342,577630,713660,15623,713666,713665,713668,691255,713667,714998,713662,713661,713664,437212,713663,700350,713648,713647,713649,700357,551076,534501,89922,713655,576309,713657,713656,736311,713651,713650,271615,159177,565660,713653,713652,511880,564339,75305,591957,726997,552390,481386,132958,726996,713680,713682,713681,680588,713688,88620,713689,414595,713684,15605,713683,118692,713686,28929,713669,64665,591944,301999,219076,247986,700376,713671,713670,493353,713677,713676,713679,713678,438551,565684,713674,493358,219087,632890,735095,594174,735093,526008,667503,720827,69441,542956,37044,625783,481350,443085,707860,705203,25077,244062,582189,438519,541611,296984,82447,719862,654220,493321,82444,439850,438520,541608,656868,656867,684082,594176,452809,654209,735082,735087,83765,452802,444382,724397,595453,269390,611112,552321,467021,82466,53902,68168,39661,539367,296932,401245,629293,470677,494622,569186,680532,553649,735068,100547,600456,566910,707832,642273,540320,439802,707837,566911,112507,341118,469664,470646,553630,569190,735032,403887,735034,101841,431095,583485,125818,582172,642282,723070,15695,268029,540312,566929,27670,595472,570190,25011,735041,706508,719817,719816,100520,101851,53934,481522,731567,731568,481525,481529,660043,729248,684011,468550,149724,674693,583301,481516,674696,662691,331881,505805,559673,720897,468566,584645,545036,545031,730214,731587,729267,481500,730251,571348,572679,685318,328231,705292,585984,731552,584650,395073,86192,729257,731558,546337,98179,559654,316259,83914,572683,98178,83915,268072,98174,443259,58982,558319,101772,547678,730237,100446,265863,731526,731528,558391,367893,684052,69649,632848,55002,685378,720867,366553,672077,56339,731531,49195,46910,731534,433873,597908,597902,69658,331847,573956,498397,685393,720860,98198,305580,98195,585933,719877,456504,731505,731506,498387,731508,71980,656837,685363,83972,193866,498370,706599,705266,731516,239588,731519,656828,706598,529824,473089,731520,473065,418209,99412,725721,74114,605270,76785,725715,210706,473056,605279,547604,106610,406215,473073,536974,635018,523664,588162,535635,691186,691162,43039,473042,473041,63488,725746,560619,86127,15809,522301,473035,588196,86122,473050,473053,287248,540199,535620,473052,561959,523642,485021,473046,536944,473047,473021,98125,488995,558315,687961,576141,576146,433808,577468,701708,486341,661323,635046,474344,86143,87475,509384,58916,730269,564164,701709,105308,44334,46995,475692,310091,730260,310095,99446,488989,591762,685307,701715,685304,548986,731588,589479,461019,536983,548966,577492,463678,725702,406240,591793,32347,564181,450395,508076,87481,306803,576174,534331,559603,473000,473001,473006,730279,588203,156823,14451,451669,576217,642169,81297,499603,565572,735137,736468,565573,540271,156816,539281,723132,463661,723136,712480,451656,679477,564259,724493,679437,184035,476942,680424,735112,679444,719900,735118,451684,667487,588218,156832,735120,680433,679457,679453,436059,385605,736457,724432,724430,27721,725761,725759,578836,691136,322112,666171,591804,591802,476939,475607,724418,724416,424018,438654,88710,173344,735100,39704,735106,735104,324762,735105,566876,64771,106586,700485,552273,336730,679481,539291,361682,258743,452978,591838,408819,679484,271716,565556,397613,64784,400090,105269,724444,481496,299268,88739,552256,712461,725769,452969,412068,679499,438672,452961,679497,438674,589510,731600,582061,413318,732933,403993,439948,68238,336708,71880,481481,611029,717300,569096,383015,452916,467187,612361,629127,720948,719969,582055,70549,300761,719964,705322,732941,705321,155562,155564,395017,731617,155568,731618,731619,481473,705325,706652,452908,570087,705323,167551,611017,570095,720933,68242,439940,731620,383009,625651,403974,732916,685283,685282,685281,595397,685288,570097,685286,611007,594073,529856,467165,594071,358082,719986,624314,582094,685291,270423,424004,481455,719971,717310,83883,439963,582020,679418,39794,540223,400044,540229,612318,265900,597993,594003,571370,82587,720904,719925,582017,679421,680409,68288,719908,707947,330598,402697,367816,100427,735182,571380,735181,554851,81241,541550,82573,541551,68299,197394,155558,719939,736480,570063,629188,630175,401346,439932,566800,667440,679409,415969,679405,539227,719928,735160,736495,655464,467130,403998,82593,456483,735166,100400,736498,735167,342555,728035,731689,728036,572539,729364,728032,270057,572532,559563,343519,716053,495807,673241,585843,731693,731698,156376,704077,597821,470519,662569,32432,283358,584526,730339,730337,5824,535571,139827,731666,140818,458846,573888,384092,32422,731670,18118,305233,5836,701808,731678,728043,572565,103838,716066,612299,229862,612296,612295,499378,612294,704091,368842,730359,730311,239210,434852,732978,729320,102562,265508,265504,585806,673284,69761,730304,368856,673282,305212,558261,731656,558262,102572,560528,45704,585815,546288,719997,728013,730333,728012,728015,728014,68458,729342,348394,266818,625571,728017,728016,656717,56474,730324,731630,103868,731631,434866,241520,572527,67138,68468,728006,49281,685247,446847,547598,730315,434815,62255,713858,701876,725845,98203,561806,381872,620779,499325,499328,62265,391211,636219,701880,509219,463396,379552,701878,649546,701898,725867,701893,98221,725863,552069,725857,662518,490971,307818,511553,576091,475351,700577,725852,724517,86235,606457,86236,662509,662507,381889,712542,323036,649569,462005,725801,43139,462000,311055,577353,99575,591650,277570,661204,438384,701824,380582,701823,86264,535561,730385,728063,730380,547535,565383,138533,548861,98264,576046,74294,565391,700515,712513,564067,86271,547508,547507,113269,577382,510257,511588,450028,675850,728083,451353,723260,553460,63502,539155,552131,554791,552124,630076,493186,26553,682984,589405,441975,40853,64844,481198,724584,40858,163461,554774,439662,52868,39850,540123,670989,481186,670986,88827,276254,451329,277589,264281,439699,682965,682966,735239,735236,439697,670992,670995,591748,670993,391158,670999,670998,27879,438352,80094,565486,264272,735248,735209,735208,401098,725883,680362,679382,724549,637502,88845,536911,735210,290548,679385,439631,325721,439615,500958,725872,724545,725874,511608,494463,540167,300417,724571,724575,291891,651828,62239,482478,552148,456191,387950,87535,452610,87531,107306,107305,439652,63570,290579,9457,723238,468188,360000,88856,540163,540165,39803,657946,705439,542729,612245,717429,83990,343597,471790,493105,583282,70689,158957,717409,731737,49269,444175,705448,657939,685198,468167,657941,258459,583279,470475,9487,730410,516457,106024,317292,705451,296749,542702,731705,372022,731706,9480,657965,80019,106020,657969,56391,595278,685165,657971,718773,632674,657973,81352,404935,456154,494415,384009,372016,157641,668610,657957,657958,530726,52807,541438,595218,416903,540101,92021,482410,553413,331581,469441,468110,735294,682940,471750,13291,374698,670974,404997,657976,597871,656652,329258,584567,584564,269141,433487,718700,583252,601527,541410,397322,572597,572596,64808,81395,642028,432188,67088,530759,583262,595229,198131,398664,705427,331547,134989,735283,85093,560431,431029,572414,407394,421699,701905,103727,470621,558107,57862,57863,433676,608928,730460,44531,535481,471966,295490,228633,470637,384175,612195,546124,56548,612192,418067,536781,498183,560452,407377,535453,725900,571100,443033,97093,534116,536778,608924,612180,731795,608922,458978,571118,573779,331622,716182,662474,294180,700608,443016,522155,56565,585756,271477,4635,700609,509196,18213,499491,499496,102442,729441,18211,266959,718797,145565,420328,674495,731770,132262,158892,705486,156211,156218,728104,81517,499479,103779,547495,534161,499474,560414,378324,560417,560418,657930,81551,546157,238018,418032,547484,103747,242975,458934,499460,157565,103760,157568,633967,633969,633963,633965,633966,45803,506958,685131,546140,633971,48078,717478,434932,434930,725960,637417,50391,725957,38700,87672,87670,589248,713967,407305,725952,379668,75695,591565,462185,102487,735301,381983,381989,510112,724653,590263,723325,700684,680268,476788,499423,450198,61063,87692,366392,591589,474143,712657,392640,99665,723310,723312,608991,589274,712661,590202,674400,406029,712602,524777,725922,534100,73063,499409,210506,725919,499404,451462,85055,589224,44576,425193,156299,728184,139734,289602,311184,98357,725911,462132,592851,579898,452784,547410,450160,45898,592886,488732,463436,724612,724610,152716,276331,724603,712632,548732,565277,589226,548719,44556,548717,68504,724602,488723,662420,524775,114487,608956,211860,608952,608953,422938,608955,85065,592944,591612,63622,260811,401174,92184,555999,552002,710071,542692,710077,681539,564005,670890,222528,591606,488700,223850,463420,312596,413172,735380,555989,577318,710067,80191,542683,734057,679234,52988,503072,392591,542668,592965,734019,541338,564015,722074,464722,576002,577326,27982,709097,735356,50337,589324,463402,577343,88939,361411,718805,734033,235856,565363,734039,438416,64994,464700,724675,63666,724674,481284,709030,734001,87630,289682,567953,553376,553374,481279,723329,359127,579935,61014,439744,710026,541392,735308,735306,735307,301868,73006,62357,724698,681552,681551,14639,258502,437115,437111,735319,439758,74346,539064,425131,165980,577301,579961,722026,682893,709060,494563,87641,437100,679255,438432,542601,403750,439704,595138,673081,427716,716217,555918,68487,110891,633868,55174,661084,79123,468288,81441,199397,92124,294207,110887,92128,439728,48048,295532,397409,546195,494534,80143,717560,439718,731832,258560,729534,704256,716220,469595,730518,730517,456283,730516,730513,706843,543972,483869,402467,731807,731808,718830,734081,36049,504377,80160,81494,706855,48016,731817,445587,80158,583111,92169,403773,706869,398775,469536,595121,572480,397456,595105,705546,332992,595103,495818,471862,398479,155299,138715,650355,546008,676985,560316,499170,206965,447967,97194,97190,361185,472925,585601,608821,612081,730587,435977,560321,728267,729593,730580,536699,560325,535369,572302,716289,271130,420014,345925,54003,700702,546006,458643,676967,397129,94945,712701,460925,535336,728296,458609,81628,31333,102739,524686,574977,273789,434686,459941,510063,499140,535342,547308,676973,472919,206998,3428,79306,584302,728281,458620,67330,101416,704266,731885,476495,451190,20657,534066,686350,704267,717586,3437,499125,600066,730546,730540,729555,704270,488468,562948,612029,586907,586906,586904,101475,101476,321933,716249,730533,674382,729585,68697,731865,80338,94980,463157,590192,488462,381610,574937,102778,499108,548694,559330,730563,499100,370956,354003,94970,728249,548682,574940,419017,79348,685009,79349,574944,102784,586922,308953,546028,601362,490768,574948,574949,382935,731884,735405,380309,710105,709111,592786,679185,124148,565170,680167,61169,735410,380303,580796,86462,565179,87791,711429,241344,735416,735417,490748,379304,680181,577169,679197,608892,85128,42071,541197,711434,113494,86454,87785,710104,38813,640955,591471,489749,723447,577178,489748,723446,680151,680150,680144,608885,336134,680146,680148,608881,711466,42062,710131,712794,608889,43388,162438,451160,490728,562926,680152,680156,680158,86477,589153,380311,511303,308911,589156,711450,711451,676946,591413,649328,712724,676940,535314,579769,296491,608863,637348,608868,565130,676950,567792,566460,439476,591401,393674,535323,712713,43365,463104,56621,277311,56624,29076,676960,85166,676961,536648,565146,510030,200849,711417,367391,676927,439494,580779,439493,662317,688901,30047,338748,29048,56630,637364,509047,512689,565151,711420,43345,42015,537963,676931,711403,107600,676930,98499,464410,663636,279955,97161,423902,537955,565168,351702,722173,592826,722171,706925,554553,722176,554547,631168,682743,682744,734164,439436,592829,543888,92298,393591,706931,75736,722162,734173,682755,51773,402127,554535,566550,710187,734178,631171,75747,266696,470292,705615,62434,580856,577203,86407,577208,61104,667128,452412,722182,441756,426149,705621,452409,577216,313537,682730,236820,403439,734156,567823,106247,554591,571087,553256,606168,640907,469280,735458,734127,106256,50480,711470,710140,554579,735460,37400,734133,680138,710143,13453,735464,236845,710145,106222,26739,541255,73128,106224,554572,62478,579823,86449,682767,735436,566520,62481,106232,62482,392288,74467,566526,222225,500737,682773,471589,709181,87769,682774,680115,734111,734110,735448,734117,728312,546096,715002,715001,718990,730623,731971,583040,106296,729637,728309,728308,258220,106295,572385,82897,716322,445258,81563,82895,18306,729663,106266,36181,731941,558053,731943,33908,731947,595035,571075,546075,48160,81595,716351,603992,546076,81590,495502,729656,728327,505549,728326,716344,458581,235576,457257,402104,156505,572334,573665,555830,554500,472847,584316,586975,718954,433254,345971,397112,657736,657737,657734,657735,657738,657739,657741,656412,586987,51733,305067,457234,585652,682717,644450,543835,543830,560379,157854,156525,729626,472829,186373,734180,717642,716318,730601,586993,433276,458535,75714,543824,63731,596314,318356,63732,572368,596316,706993,205745,729613,272520,716308,505595,37469,560391,716303,728397,535232,676868,712801,562857,559206,712808,3516,663548,700811,715085,574834,535228,727055,499285,561531,625268,560203,676875,81718,560209,3523,676881,419153,676882,4855,676880,730698,457433,727089,676845,42104,332726,458727,662240,295254,408462,79432,30120,535205,548532,537882,712811,499267,712814,562889,688841,688840,295265,573530,700843,727070,728354,730673,730672,674245,728352,562810,728357,715044,716376,716374,591381,715042,716370,703066,703067,703064,703065,703062,703063,675587,703061,381709,729671,319744,295202,511292,703068,703069,663575,102686,703075,731986,728377,703073,295217,577093,295218,703074,676888,703071,703072,476592,559228,727049,20755,589081,715064,687535,421436,499225,703070,731993,730687,731994,434744,590059,731998,731999,729692,728361,727030,729694,729693,488566,463289,487239,715057,633731,715053,421448,487242,92428,102668,101336,67494,434751,730676,156485,501857,463258,99896,724873,164963,98563,99893,640824,639846,38941,710231,734201,85256,537806,710236,113380,734207,734209,452592,537820,724863,501852,640839,422710,735542,61299,607447,565065,589011,56709,475244,85240,85242,608779,86571,409711,97259,724894,724893,724892,608763,723559,554367,679047,475217,589047,85274,590016,73299,724888,724885,73290,589030,735520,589033,86597,113350,85262,475228,325597,566338,676823,593959,252147,97271,489850,700854,278779,451224,692080,608743,439596,453880,675503,692069,712833,536534,676830,593940,579665,709204,524554,566351,649209,592647,592648,426298,723524,724854,579677,67409,151627,338868,680069,700877,277427,9715,490811,581982,640883,513898,56757,566368,577024,724841,97282,379499,326882,640897,37571,592706,441863,63866,277496,336311,734285,528140,438227,50555,734288,438221,567752,682628,671982,645693,48216,542434,517472,554421,554413,439548,379392,566436,235630,729700,481067,86527,554402,717724,554408,735599,668333,404895,717706,99825,543743,439561,631078,683947,717714,13597,734277,717711,495697,98529,325614,584281,722250,415536,325618,223666,50591,453805,709273,735570,37533,85212,734249,584295,465803,235640,723572,99842,596263,682674,26883,711599,542485,735548,735549,98547,97215,722270,555784,722274,722275,554445,710292,483691,710291,682646,453820,438206,735555,735559,722265,99862,74585,301639,183,579720,723599,710283,735560,710282,683984,710287,543795,710285,710284,513927,567742,710288,729764,560276,399845,559282,560279,416824,729760,517402,662184,729768,484996,457383,730744,19752,704470,572267,729754,106172,729756,715110,143117,730739,603884,585593,729785,675477,728450,596245,469346,472986,687456,305192,469341,458693,584258,645616,302921,375888,729775,66088,704492,729771,106157,703169,572288,703167,703168,373228,78057,259648,471660,730754,472996,716466,385209,586860,562893,432039,91055,561564,560235,561569,421385,705752,683910,631001,730706,586858,683913,683914,730703,103920,683911,683912,717743,561571,585540,561574,669600,260683,419070,717729,420063,20704,572221,102606,574884,444007,716406,272668,80395,585550,459976,269005,460969,156405,596205,573569,63841,671912,704448,705774,444046,716431,715100,730728,716437,458651,730722,432066,705784,75837,683908,48239,484924,586890,683909,729730,671920,416815,285940,703128,704458,728407,420082,728401,573574,703124,728403,717752,420079,472954,656312,472959,87800,683902,458660,730714,683903,730713,683900,716426,37589,683901,683906,683907,683904,683905,733469,650993,663025,736102,711681,712011,731696,731213,701884,727854,648162,736472,714667,731184,678424,730218,735266,732553,732433,731862,731027,708844,735229,730011,732915,720295,726037,732000,731791,734545,728046,731039,708176,615838,733622,728112,732858,679416,731929,654911,678853,728070,678850,730425,735577,735457,727099,733718,728066,725910,654228,725992,721787,734130,731267,726351,733767,728134,733766,704320,660533,701608,578856,724449,725936,735031,733012,734984,731113,733659,729610,663080,735719,730164,712062,719379,733266,731122,662520,706492,717371,719792,712705,731672,712937,712938,706688,709718,728237,706442,726291,717338,706228,708642,736085,730328,717127,712528,723421,723422,723653,722317,710580,734333,711677,735425,710101,724776,724775,723437,721491,723430,723428,731280,724519,712304,735846,723622,733683,706065,718297,708019,719137,724352,724581,724342,735029,716752,723033,736571,724792,714757,726512,736762,713444,725894,735684,721916,725276,718741,717417,708729,717202,707403,731715,726389,716582,731960,708901,715681,735299,726564,736158,734181,714371,718968,707694,731570,733996,709412,720688,711966,721533,733984,732859,709603,709602,725077,726194,720895,719424,720885,723916,723541,730068,721352,721357,735300,734212,735304,734213,735305,735302,734215,723551,724636,725725,709687,721794,721307,712842,712633,721335,726410,722079,735598,705746,736215,711365,709287,724658,725546,725544,713549,714403,724078,715112,729308,716474,715381,730761,726266,706437,721803,724031,705755,723170,729711,708801,735395,735396,736014";
        Set<String> configedEis = Arrays.stream(configedEiStr.split(",")).collect(Collectors.toSet());
        int configedNum = recentNotActiveAndNumLt1000Eis.size();

        recentNotActiveAndNumLt1000Eis.removeIf(configedEis::remove);
        configedNum = configedNum - recentNotActiveAndNumLt1000Eis.size();
        System.out.println("已灰度的企业数量：" + configedNum);

        System.out.println("recentNotActiveANdLt1000Eis=" + Joiner.on(",").join(recentNotActiveAndNumLt1000Eis));
        //过滤已开启客户账户的企业
        String caEnableEiStr = "259335,484603,66190,102069,17280,580594,578200,439230,2,85381,578284,392088,85393,242446,99934,593905,460651,633550,537782,268,639782,602420,307447,520952,681257,703289,708041,710329,730888,36491,452369,580413,470122,505226,584088,568824,387683,404676,601029,430978,597350,610811,625003,91239,640696,634765,587907,601056,610810,495463,687239,690958,683710,683717,683750,683731,683708,683736,53092,710412,60255,563831,62810,374081,77255,97597,326396,73524,587809,301068,603560,602138,592391,610680,101034,615465,537414,622664,633359,640541,574557,652576,675188,665887,683633,683698,683659,683699,683696,683671,683677,319305,683675,659998,709541,683688,708249,709653,716715,728705,48745,550492,6285,84356,580208,387408,586423,77273,48636,91496,564942,507693,60391,585154,505013,66691,610537,614158,617979,156062,614026,647826,652394,663026,663024,663054,663078,663049,663089,663063,663094,663103,663099,663061,663087,536154,663117,675000,663046,663028,690655,688301,663106,663105,663015,663077,663036,663064,663041,663095,720003,489068,543130,548099,84431,520517,576943,585041,11489,592125,77329,545681,592045,52020,594730,599683,472252,575632,538689,610432,474862,621116,609413,491387,593312,100688,614055,576948,640212,639317,642910,646399,645155,658438,664226,663010,663007,684708,683455,689520,701018,708496,709730,714382,719200,720101,720103,538408,42713,59572,545728,546922,574286,515739,148999,575501,574229,568313,583963,533733,532277,5176,64252,622304,624980,629857,586200,381023,545707,641450,104211,642794,634337,72589,641431,671223,545711,684639,702447,706013,707281,706089,6671,534946,574146,587455,589982,534803,593101,534800,76319,587278,595759,593289,589984,63057,589983,534920,75013,617607,617627,87068,85993,473323,534811,605641,390660,609229,635579,589979,641300,641317,634125,534924,589999,589996,688076,16593,680931,707411,725319,727890,731136,557322,577926,522688,584964,581049,77747,589990,456603,601928,51124,596927,589986,444621,583668,589991,71452,468668,616249,156746,57264,162688,601929,569397,563217,257575,662968,662978,662989,662990,662985,662962,662953,672301,662964,662996,662950,662982,662983,662966,662981,662958,706242,708902,708976,719538,424259,106348,75233,538199,405196,100276,581058,449523,76576,76550,594208,322299,587124,558557,617426,610008,625880,628072,635253,635249,594354,156770,647299,647234,209918,666311,673571,667670,673508,684137,691402,690069,705056,712277,719718,729111,729122,49015,565678,583482,131710,584655,529955,353127,220003,552337,342542,589673,595435,577634,547750,584715,148551,618502,618667,616007,504622,488857,616023,629317,534463,39681,631502,597973,632829,527399,644936,642277,389106,662772,655565,730127,731525,57787,565489,585854,573833,558231,443222,594092,594014,155535,597826,452911,597834,533020,629131,643418,643491,617203,486308,662658,679411,679429,679451,679422,679455,679480,679415,679403,679434,679498,679454,679465,679420,679402,679440,649619,679482,679472,679406,679463,679447,706641,707962,707988,679446,509238,70833,522213,477946,572526,342235,18173,156216,379662,45813,600225,608933,410833,552122,631304,632646,577419,597893,387919,539161,590234,590243,590262,590252,590273,590270,662523,679335,662514,679360,590251,590257,590265,662508,701885,701916,731764,731789,731799,78014,2100,452728,504384,562974,26640,580984,589313,600129,78005,78021,307668,631242,614689,636153,590153,590175,590225,590179,158833,590188,590189,590213,590127,590174,590216,590162,127746,590193,590165,590215,668599,700642,706814,590191,709063,731800,731801,731803,731804,731805,734095,102648,50468,75783,598973,596319,597644,615950,592739,597689,132555,619457,618198,590089,590086,590110,82898,590131,590077,590130,590126,590084,163771,590145,590135,590146,590149,590090,590109,670702,7089,330090,590111,680112,590083,590134,590091,590140,590112,590124,706906,706927,712769,717609,717617,717626,717627,731953,501956,125297,548579,63848,574826,99852,51857,499231,590011,254773,590034,590047,590051,590008,590012,590062,590067,42178,590005,590069,590055,590044,590014,590001,590010,590050,625215,631074,590043,590052,590061,590068,590015,505435,631077,655006,656399,676861,663499,692045,705792,692020,710218,709293,709224,722209";
        Set<String> caEnableEis = Arrays.stream(caEnableEiStr.split(",")).collect(Collectors.toSet());
        int caEnabledNum = recentNotActiveAndNumLt1000Eis.size();
        recentNotActiveAndNumLt1000Eis.removeIf(caEnableEis::contains);
        caEnabledNum = caEnabledNum - recentNotActiveAndNumLt1000Eis.size();
        System.out.println("已开启客户账户的数量：" + caEnabledNum);
        System.out.println("recentNotActiveANdLt1000Eis,caNotEnableEis=" + Joiner.on(",").join(recentNotActiveAndNumLt1000Eis));

        Map<String, Integer> paymentNumMap = getPaymentNumMap("C:\\Users\\<USER>\\Desktop\\回款数据统计\\ei_to_payment_num_map_8347_最近一个月回款不活跃的企业.txt");

        int limit = 2000;
        System.out.printf("最近一个月不活跃的企业且在企业库且回款数据<%d：\n%8s\t%6s\n", limit, "ei", "数量");
        recentNotActiveAndNumLt1000Eis.forEach(ei -> {
            int num = paymentNumMap.get(ei);
            if (num < limit) {
                System.out.printf("%8s\t%6d\n", ei, num);
            }
        });


    }

    @Test
    public void getToConfigEisInSysDb() {
        Map<String, Integer> ei2NumMap = JsonUtil.fromJson("{\"730355\":0,\"730595\":2,\"707338\":0,\"705154\":0,\"642727\":2,\"732540\":0,\"733874\":0,\"650585\":0,\"687894\":0,\"720536\":1,\"705172\":0,\"700720\":0,\"638019\":18,\"729382\":0,\"685691\":0,\"731444\":144,\"733621\":0,\"730792\":0,\"731647\":0,\"619911\":1,\"730788\":0,\"730786\":0,\"730785\":0,\"730784\":0,\"730782\":0,\"730781\":0,\"730780\":0,\"630315\":53,\"604891\":0,\"730778\":0,\"719516\":6,\"641689\":1,\"731867\":0,\"731626\":0,\"730569\":0,\"675449\":388,\"730799\":0,\"730796\":0,\"721238\":0,\"711684\":1,\"710336\":0,\"724985\":0,\"684775\":0,\"691067\":0,\"432897\":0,\"707195\":0,\"734544\":2,\"732369\":0,\"630930\":0,\"708293\":0,\"692197\":2,\"627540\":15,\"624095\":0,\"653812\":0,\"723852\":1,\"627554\":56,\"672326\":0,\"700514\":0,\"734741\":3,\"620942\":920,\"728955\":0,\"679591\":199,\"715877\":0,\"736318\":0,\"730809\":0,\"730808\":0,\"730803\":0,\"636403\":0,\"725881\":0,\"704943\":0,\"734360\":1,\"678291\":0,\"658433\":0,\"640912\":0,\"733049\":0,\"678273\":0,\"735675\":0,\"715620\":12,\"724566\":0,\"706907\":2,\"704584\":0,\"631564\":0,\"730628\":0,\"730864\":0,\"705687\":0,\"730857\":0,\"730856\":0,\"706546\":0,\"706783\":0,\"622250\":0,\"726147\":0,\"687165\":0,\"730828\":0,\"600220\":0,\"730823\":0,\"680767\":2922,\"730821\":0,\"730817\":0,\"730816\":0,\"730813\":0,\"730811\":0,\"730810\":0,\"704321\":4117,\"730850\":0,\"614600\":1,\"730842\":0,\"716312\":0,\"719822\":386,\"74860\":0,\"655583\":11,\"680510\":0,\"730835\":0,\"735048\":0,\"730833\":0,\"706126\":1,\"706128\":0,\"732657\":2,\"709637\":996,\"653742\":0,\"640420\":0,\"734842\":1,\"705287\":0,\"675546\":0,\"689952\":0,\"700822\":0,\"735943\":0,\"731551\":0,\"708574\":1241,\"707486\":3,\"678849\":23,\"650282\":1,\"728101\":0,\"706339\":0,\"732872\":8,\"711933\":0,\"626540\":3,\"727032\":0,\"707440\":4,\"731763\":1,\"736858\":2,\"724631\":0,\"731174\":0,\"720260\":1,\"733126\":0,\"720059\":2,\"709491\":0,\"712432\":0,\"291980\":0,\"733307\":0,\"708593\":0,\"604371\":0,\"708122\":2214,\"81704\":0,\"684421\":0,\"735710\":0,\"638100\":8,\"637832\":0,\"736439\":0,\"644135\":0,\"734274\":0,\"736450\":0,\"692231\":4,\"701551\":3,\"635202\":0,\"621669\":5,\"734006\":0,\"709043\":0,\"734013\":0,\"722039\":2,\"714651\":116,\"713322\":0,\"726620\":0,\"719092\":0,\"724438\":0,\"735563\":0,\"662181\":6,\"656982\":2,\"730747\":0,\"724060\":0,\"703382\":0,\"706411\":105,\"679634\":2,\"678748\":0,\"653254\":34,\"689879\":532,\"719987\":0,\"724082\":0,\"730760\":0,\"710919\":86,\"673077\":6,\"601441\":0,\"642134\":0,\"613410\":0,\"717740\":0,\"712054\":0,\"653275\":0,\"705526\":0,\"665269\":0,\"735158\":0,\"725372\":0,\"656798\":0}", TypeUtils.parameterize(Map.class, String.class, Integer.class));
        Set<String> caEnabledEis = getCaEnabledEis();
        Set<String> configedEis = getConfigedEis();

        Set<String> toConfigedEisWithCa = Sets.newHashSet();
        Set<String> toConfigedEisNoCa = Sets.newHashSet();
        ei2NumMap.forEach((ei, num) -> {
            if (!configedEis.contains(ei)) {
                if (caEnabledEis.contains(ei)) {
                    toConfigedEisWithCa.add(ei);
                } else {
                    toConfigedEisNoCa.add(ei);
                }
            }
        });
        System.out.println("描述在系统库的企业，且开启了客户账户:" + Joiner.on(",").join(toConfigedEisWithCa));
        System.out.println("描述在系统库的企业，且未开启客户账户:" + Joiner.on(",").join(toConfigedEisNoCa));
    }

    @Test
    public void getPaymentDescInSysAndCaEnableTest() {
        String paymentDescInSysAndCaEnableEiStr = "704067,537415,717938,723267,725567,720794,549400,716293,607970,602042,52299,637385,623405,607977,711046,600407,710978,590075,704512,613841,722169,708313,575256,509618,719797,683727,719559,710983,592849,705979,704523,552351,613944,506016,606980,650383,100556,679442,704772,710877,710512,723032,97647,723399,598812,721532,630416,719214,635557,717398,720436,634100,533830,706392,679458,705180,636880,607944,683707,708502,538665,705595,703734,705911,706320,590046,708621,719402,716253,711361,575568,574118,713303,712579,589980,712210,711363,717347,538310,561177,620695,708758,111026,618133,663100,590031,721279,533781,599646,721820,597112,715150,683645,725747,709282,601023,706467,709617,715979,706460,613353,722154,706100,704600,662955,503226,608683,607118,713204,605852,714779,717802,713320,719306,714772,713441,712233,645814,623822,589985,359819,709067,706472,705147,717113,716145,656035,608312,144265,705818,597370,712345,710108,704585,603528,585330,624347,706402,706403,706404,712767,725045,725965,708145,708387,704228,704225,704586,706400,35556,616717,102918,503360,403988,710476,716698,712998,726007,590230,28229,619425,704599,722323,583839,707180,546084,703815,501295,713170,608098,663017,708394,718989,627875,712088,662967,567244,706305,518510,645982,590142,725502,608085,663055,704127,708840,647480,662998,716234,608401,717561,628178,574217,719186,703825,381411,717689,711341,710257,590138,619762,724766,706673,80375,704806,103798,719734,619083,714904,725005,720470,663076,708221,705634,721558,705629,707371,718715,629847,665480,603717,717608,710659,616236,704557,488983,704556,84749,712283,662521,635179,713491,704308,626709,714588,679430,602512,706506,590108,701853,705538,705535,708487,602183,707151,527590,716677,679528,590219,718719,575081,615121,708011,599947,707044,704459,577384,719378,719257,711097,505113,566373,715697,647603";
        Set<String> paymentDescInSysAndCaEnableEis = Arrays.stream(paymentDescInSysAndCaEnableEiStr.split(",")).collect(Collectors.toSet());
        Set<String> configedEis = getConfigedEis();
        Set<String> result = paymentDescInSysAndCaEnableEis.stream().filter(x -> !configedEis.contains(x)).collect(Collectors.toSet());
        System.out.println(result.size() + ",回款描述在系统库且开启了客户账户1.0的企业，过滤已灰度新回款的企业：" + Joiner.on(",").join(result));
    }

    @Test
    public void getOnlyOneInSysDescEis() throws IOException {
        Set<String> onlyPaymentInDbEis = getOnlyPaymentDescInTenantDbEis();
        Set<String> onlyOrderPaymentInDbEis = getOnlyOrderPaymentDescInTenantDbEis();
        Set<String> caEnableEis = getCaEnabledEis();

        Set<String> newPaymentEis = getConfigedEis();

        Set<String> grayEis = Arrays.stream("663548,706993,708518,729737,671268,498044,703638,531457,661932,701842,677183,114433,719289,701368,90706,642332,432897,716811,486266,700963,690802,549114,655122,678917,580121,535822,556269,63559,631168,333774,719354,577031,710477,692231,57515,650282,700479,638129,76894,514090,404022,678764,647486,574270,539641,414981,641848,730569,730269,597993,711873,709236,730210,662183,726743,677030,597750,707060,710930,106647,99516,722557,711630,534492,592827,399581,700921,80282,730553,665269,574414,142346,528046,710696,711895,401683,620942,701468,587511,99576,725267,438705,629539,253385,665506,517517,720260,724631,724566,229901,721296,326137,691087,547535,386693,525174,466778,672326,68299,715620,725073,627554,574611,473048,392829,687165,671584,688641,539690,505539,491196,587124,19610,678818,477095,666415,564659,687318,239192,658433,627540,560857,441820,684574,325727,332841,535960,634765,432745,716312,733952,458227,536752,676525,536983,634607,734013,709060,252931,276718,405313,702406,673077,617328,689952,430464,716723,717740,724823,689520,685257,726147,715965,706907,727032,469878,585387,641666,554419,600066,423637,679591,719829,463708,424426,731363,184035,475673,497298,613194,10335,686099,124593,690958,438486,496242,724515,519610,687728,40652,462767,491263,621669,424210,515757,61981,399260,543799,705382,388212,679051,386202,703382,581070,452916,733941,568906,604371,159458,711682,709491,732023,646301,499502,731867,40394,715921,572155,716957,690638,725597,672999,708122,706126,546711,679936,666651,720328,726423,600220,679107,654429,342259,725914,726552,640912,533575,496636,631564,572683,72979,706339,261918,716065,311499,710247,713535,481430,304047,100727,534501,706128,469210,648359,708293,675829,724082,554550,297193,600456,717668,720536,60559,733176,661750,684144,717724,513163,601441,566589,719194,439932,636475,610890,732375,701134,339913,340307,649957,702733,450160,620896,419488,716360,652128,371758,732872,735710,636051,34932,700018,462910,214722,658832,731444,710126,728955,530907,706992,467021,706099,359702,91521,485219,704584,52645,728467,731418,726620,683137,729231,597393,642134,653742,678992,730965,364298,706671,531731,542111,704321,612081,491433,734741,36491,662181,701551,729866,358226,703622,402491,459790,724444,285803,100444,45863,720059,700822,691955,726447,548025,705172,734355,705526,423244,459256,589613,710145,113209,113086,686090,728550,510775,648791,257827,701963,640550,684714,729578,225392,432154,17979,725785,450395,595595,626540,710919,704765,736318,619911,720280,715877,716570,488308,449096,454285,731506,88679,280988,310186,705040,717771,730355,684693,544375,650585,635202,637677,653254,251393,367530,419840,613410,704943,396587,473018,636760,712349,733704,420949,710513,675449,559775,604515,708888,663958,735675,692069,700249,692197,729696,452743,735257,719987,734544,679953,524803,720590,603872,69081,638100,592341,662474,712635,538403,602847,664693,720482,637644,641158,709637,575990,58050,302731,678849,548640,522289,719731,706411,630315,735754,689879,75969,714651,709693,291980,566344,722039,705287,709716,719021,680767,541209,630930,551055,656982,399429,676834,501557,656798,678273,579792,452412,644135,735943,634306,708574,685350,706816,329639,655131,109199,731626,332210,435551,624095,687894,733004,497575,507925,710336,297351,713322,731763,674495,719327,619212,710732,616774,83520,712660,684807,627626,709308,53316,723852,471060,723401,300698,664708,636781,60475,652764,653374,701587,532384,725826,711995,715688,155562,673432,705439,714792,642727,712065,593519,724854,722422,449959,556359,622250,36522,340354,594650,102449,593286,679886,678848,672254,692045,654228,592300,690068,651486,723981,390630,709311,726671,724965,706546,614825,724985,730375,480123,106535,544874,506625,719092,424441,623474,629853,730823,724209,730785,713181,708595,674411,730808,335806,279655,730784,730817,387078,311055,733786,711893,717078,735549,686928,700720,711684,730833,486308,721238,734274,525783,461541,734520,735387,730788,735809,730828,582648,734808,640420,471544,732657,730816,82093,666426,731174,412314,708593,82697,730857,672077,148999,597908,730835,732107,732062,708176,730803,729382,657455,637725,730850,726640,730778,733264,733621,730780,734803,707195,565602,678748,377571,685691,537722,736259,682794,734521,614600,81704,734842,653812,730799,734360,655583,730811,680510,642273,727153,730864,653370,736439,733307,710351,706055,735048,706783,424155,576827,724060,453296,707196,616073,638019,78582,339413,730540,703180,724438,730786,691067,527965,707440,736450,730796,669584,567833,730842,570838,732540,641689,74860,709054,735563,730821,733874,730760,655475,730810,731647,508561,632477,385728,689534,725372,710317,637832,609506,684137,730809,718027,729869,730747,377536,636403,594071,711933,653275,712758,654380,730792,730813,416044,735899,735158,730856,586583,730781,723901,691756,730782,651789,684775,734006,719516,736858,605656,708912,722647,640528,197598,60391,671396,733049,533020,606004,570841,96821,627104,730595,703802,478866,728101,719242,603992,470184,633731,715475,635358,710129,641468,282928,580886,635672,560048,335125,730628,507822,513940,526387,587560,582300,715545,604891,709043,79201,732335,57649,650236,684121,690720,707171,675546,689115,716735,630491,682623,654209,615979,418285,719087,702766,723620,680357,703606,715362,638136,721283,488202,684421,678291,654313,715324,691478,667542,707338,705687,705858,719822,587309,707605,657930,705154,676261,707486,676823,717172,710062,679634,58341,731551,708988,71021,709267,725881,593289,712432,732369,712054,690427,561531,726282,686817,729730,726239,700514,719215,691871,496900,718773,138143,730703,733126,391412,491018,624498,294755,679544,319305,650400,722160,579701,41796,454402,711972,339383,490850,670552,701861,549102,704457".split(",")).collect(Collectors.toSet());

//        Map<String, Integer> paymentNumMap = getPaymentNumMap("C:\\Users\\<USER>\\Desktop\\回款数据统计\\ei_to_payment_num_map_9826(有回款数据的企业统计).txt");
        Set<String> paymentResultWithCaEnable = Sets.newHashSet();
        Set<String> paymentResultWithNoCa = Sets.newHashSet();

        Set<String> orderPaymentResultWithCaEnable = Sets.newHashSet();
        Set<String> orderPaymentResultWithNoCa = Sets.newHashSet();

        Set<String> allResult = Sets.newHashSet();

        Map<String, Integer> resultMap = Maps.newHashMap();
        grayEis.forEach(x -> {
            if (!newPaymentEis.contains(x)) {
                if (onlyPaymentInDbEis.contains(x)) {
                    if (caEnableEis.contains(x)) {
                        paymentResultWithCaEnable.add(x);
                    } else {
                        paymentResultWithNoCa.add(x);
                    }
//                    resultMap.put(x, paymentNumMap.get(x));
                    allResult.add(x);
                }
                if (onlyOrderPaymentInDbEis.contains(x)) {
                    if (caEnableEis.contains(x)) {
                        orderPaymentResultWithCaEnable.add(x);
                    } else {
                        orderPaymentResultWithNoCa.add(x);
                    }
                    allResult.add(x);
                }
            }
        });
        System.out.println(paymentResultWithCaEnable.size() + ",回款描述在企业库-明细在系统库且开启了客户账户的企业：" + Joiner.on(",").join(paymentResultWithCaEnable));
        System.out.println(paymentResultWithNoCa.size() + ",回款描述在企业库-明细在系统库且未开启客户账户的企业：" + Joiner.on(",").join(paymentResultWithNoCa));

        System.out.println(orderPaymentResultWithCaEnable.size() + ",回款明细描述在企业库-回款在系统库且开启了客户账户的企业：" + Joiner.on(",").join(orderPaymentResultWithCaEnable));
        System.out.println(orderPaymentResultWithNoCa.size() + ",回款明细描述在企业库-回款在系统库且未开启客户账户的企业：" + Joiner.on(",").join(orderPaymentResultWithNoCa));

        System.out.println("allResult=" + JSONObject.toJSONString(allResult));
        System.out.println();
    }

    @Test
    public void duplicateEiTest() {
        List<String> allEis = Arrays.stream("707579,706249,732537,17280,528815,706242,468668,720547,583668,672394,537782,672397,700950,652764,1,2,60920,719553,75170,715194,307447,581014,93814,366646,629539,327019,732514,455372,434439,548405,700963,724923,581021,569397,709308,709309,387594,354687,87167,727142,654513,578284,452276,733831,580594,679825,298836,687453,613122,572984,392985,664786,582306,584964,582300,708888,706221,464244,489552,610085,679804,488202,727159,507925,503575,736098,584982,719538,524492,646011,456603,672301,708021,648670,681257,51124,735652,478866,662978,237066,457944,702733,522688,251393,64448,723656,662966,559948,662964,662962,522696,685624,711676,77747,733004,662968,596927,670583,617592,162688,702744,299332,662999,662996,251350,522663,708041,85393,736963,47988,85381,433977,662990,523996,25731,444621,662985,662986,662983,662981,662982,662989,720590,709311,616249,683000,578200,647381,439230,685655,639768,723613,599142,684326,605599,732582,640742,545361,533382,685665,683001,637114,711630,731225,242446,710328,711656,710329,723641,557322,723640,310186,718299,639782,54216,724965,461147,723627,439248,721081,99934,724117,670529,714792,691450,593905,712126,686090,686099,729821,737416,670511,724125,612607,268,102069,737427,322246,668243,62695,702767,702760,472684,649957,546711,257575,596134,737403,237970,619212,601929,601928,424155,677183,662953,662950,528046,662958,662957,670552,324855,734355,656270,730872,41796,716570,385793,666415,17208,606802,159800,69212,654429,553842,680730,646813,728575,646832,421080,333774,721933,60891,66190,730888,602420,729894,708976,484603,392088,679724,563217,703289,460651,83520,736175,680710,548489,680717,433004,708902,156746,560118,57264,691413,680765,581049,708912,689115,610008,594354,658832,633550,581058,708906,737488,729869,520952,729865,386228,733095,510701,156770,585440,438703,438705,259335,553870,609000,691402,736137,458505,594208,690928,661596,731341,705056,405196,80663,601056,710412,584895,637041,672258,672254,625003,672256,640696,100204,601029,708988,652699,687318,271065,719641,209918,690963,583505,718319,718316,679701,489682,83703,637079,167349,497298,733929,83733,503466,676652,337383,623269,688641,53092,684282,664693,385097,690958,733941,477689,635249,36522,710477,686827,647234,222921,635253,475005,580442,329733,730099,722470,560857,722477,686802,559812,76566,100276,690069,690068,690067,735754,713509,610829,75233,556782,673508,417156,76550,639632,452369,368580,558557,717060,610810,610811,711764,405167,665913,684211,76576,673571,393520,723763,580413,639660,485219,322299,647299,560824,617426,717078,601081,731363,584088,589777,430978,705949,683710,683717,587124,588434,404676,538199,478917,597393,736235,382799,717931,621456,474556,733176,495463,712277,700290,724245,650801,677030,599112,657447,683708,722491,491091,683750,346303,701560,648526,59175,635216,424259,360583,597350,713535,734459,649865,33392,336967,644173,724209,700238,683731,691357,713567,683736,470122,544874,649875,568824,701587,662820,491055,700249,86796,106348,589753,654313,424212,687246,614302,726017,687239,214500,730982,585340,565737,514127,667622,396587,58683,469136,240819,719718,675276,91239,270674,720727,728682,518501,625880,666311,705105,449523,429928,458462,584023,730994,667670,562653,642374,182910,628072,505226,715324,725597,634765,666329,642379,641062,387683,575990,598623,730965,654380,646762,565710,36491,678316,729976,472713,595592,680632,678320,481404,729122,640550,592391,640541,563831,729111,684137,489361,723801,549511,530907,641883,186807,731462,595435,587809,723833,730127,534492,148551,559775,709541,684121,504622,464063,685447,629317,644936,606615,114301,591010,652576,353127,392734,469712,551873,529955,719762,584715,665887,226142,326396,624498,731418,689873,688541,632979,603560,412845,57562,354479,684152,341999,690823,685476,386069,644966,453388,705145,592353,486266,406197,82515,720757,374081,499570,101034,97597,440017,555363,683675,683677,710598,580329,389106,683688,301068,734578,403055,547714,616023,636475,683659,131710,701664,683671,701660,700328,495176,734552,554070,618667,486211,534463,616007,25944,582934,662772,90997,731495,362911,710553,641834,223937,486203,617328,558421,641848,195796,710541,731467,708249,711898,729187,501557,488857,683696,640528,356262,683698,683699,499502,538812,558400,547750,642294,704745,65927,514090,223990,712378,77910,625702,565699,393117,728705,716723,622664,723043,727405,633359,527399,659998,610680,60255,220003,425299,637725,552337,735003,39602,589673,586583,589613,721283,578953,683633,535822,566984,672999,658674,577634,633395,51333,713654,372222,551061,700364,716715,542111,565678,672970,645399,531457,733264,712346,603529,707857,62810,507719,735092,574557,719829,705208,434133,726156,702183,688432,634607,689760,83779,594192,735075,49015,39681,684088,480004,635958,688493,422107,680524,675188,735057,703462,583482,667542,40652,539360,204875,655565,142346,646619,728773,73524,706504,642277,334825,600440,621369,719809,675169,249770,655581,735044,716789,537414,618502,652412,580276,342542,730227,711948,91521,615465,685350,319305,304290,392829,638129,734610,477484,735902,632829,45613,239555,735905,481507,709653,597973,77255,687979,638136,638131,584655,711985,710634,631502,709661,616774,78575,489466,57654,711972,584665,100444,602138,731525,77273,439197,684049,663106,663105,690720,663103,685398,34932,684058,607801,238266,711936,730210,726196,424949,711925,625684,673389,631544,685375,443222,663117,663116,673390,48745,579205,708381,51487,671581,671584,67832,466778,701760,580208,720019,707060,580202,684870,734660,732001,735997,531263,734678,663958,565489,707011,722652,722647,662658,475673,710673,733317,617203,570841,395976,533020,701718,382635,156062,570859,734618,521009,561904,721343,639427,731593,174741,710696,486308,310071,614158,720003,722656,556579,638101,540265,723143,339821,573198,679461,684803,679465,647858,679463,679472,684811,505013,497891,71021,58050,658540,735110,679447,679446,106535,680428,715545,679440,679449,430748,637606,86998,51434,679455,507693,679454,475622,679451,723101,610537,556653,97672,657231,594092,658566,60391,683519,6285,617168,703504,725756,700479,724426,84356,269420,622505,683527,585154,700481,725785,679482,679480,732046,649619,684828,48636,532668,577539,438679,679498,638070,706644,91496,439946,57581,706641,663028,452911,663026,643418,675020,480146,371026,707988,675029,49148,663024,629131,690655,663015,586423,604733,461851,705339,663054,630125,729304,706663,663049,83896,675000,585101,69582,536154,557075,663046,632798,663044,663041,652394,663036,452923,50985,706605,679415,155535,656778,663078,663077,728875,614984,688374,715568,679422,679420,679429,679428,519610,687057,663063,663064,663061,735184,667414,679436,679434,680415,387408,647826,663099,663094,550492,663095,676370,574414,655475,679403,679402,81275,679406,663089,663087,643491,594014,707962,735162,679411,676387,678927,705393,597834,614026,558231,597831,716055,522289,66691,729357,734725,597826,615340,68403,710736,585854,411314,52076,57767,585880,564942,652301,678917,716065,685206,638021,478456,709716,367530,710707,615315,617979,57787,685257,400678,644705,573833,688301,729317,443798,678984,624248,709734,708403,663010,709730,592125,663007,326137,73814,572526,705382,663001,729337,616607,640379,66611,707171,629927,720140,701885,387173,675829,684778,701877,725826,522213,609506,53316,490986,454402,543130,619771,449094,102580,155910,725864,725866,707199,662514,707194,453108,430464,670116,594730,680398,509238,701899,684754,662508,642910,662505,674525,159458,734756,99576,18173,720109,637587,642922,565375,732107,614055,701842,371628,720101,720103,662523,52020,700504,497575,44450,734773,69703,722790,496242,683462,683455,701844,410833,691095,708496,701861,725806,639317,701858,77329,477961,691087,704983,630074,383533,735250,735257,647751,539161,701001,736595,7689,472252,84431,477946,520517,391146,701018,670987,548099,387919,736566,474881,438364,702354,610432,735242,679335,658438,97791,474862,386609,711248,684729,725870,725876,519574,63559,684731,297193,735224,732199,736516,99516,679362,599683,679360,609413,11489,684708,645155,736527,585041,704974,666077,598335,461541,637528,532549,680357,577419,703638,633171,509312,684714,67026,632646,423244,61718,690525,678895,731730,626876,725034,690537,664226,714382,538689,689520,630009,491387,713083,477095,540999,448555,330261,489068,342235,643344,516471,390712,715682,489054,597881,713029,715688,600225,726321,723294,576943,322627,576948,737006,644677,175483,737008,432154,542743,718733,706754,596555,614832,608158,575632,597893,236226,621116,676261,503170,70833,597715,617897,683398,642849,719200,721516,642840,304047,642845,45863,710855,90430,708550,717498,701916,103732,580042,608933,671396,731789,675752,641541,592045,721541,523471,665505,31219,731799,510187,616529,662469,640212,100688,731764,678848,678844,593312,708501,61917,674496,705487,708515,708518,156216,590273,560408,299804,730413,156244,678825,687782,45813,678829,687779,379662,730443,728122,728125,631304,652227,604515,99655,590243,680284,719288,545681,85010,712635,646380,617007,719270,590234,583956,633072,499438,479842,646399,615297,590262,370407,590265,462152,590270,78744,583963,733571,733575,590257,720280,711328,499420,594616,590251,590252,720285,719292,684630,127746,582648,683303,684639,736614,499418,590209,371758,684693,720235,725916,725914,327520,255419,566589,708595,725906,580886,725904,603289,479805,629857,642812,648139,590225,471060,720251,700642,683340,532384,721586,683336,568300,70805,636153,642824,535408,590215,589227,590216,590213,536752,590217,104211,557674,687704,707281,568313,722052,369354,624980,545711,702447,722056,727748,24017,634306,574286,83232,72589,706814,646301,545707,735384,734052,533733,710066,735387,622304,734059,703793,713119,727767,701134,658303,706816,113086,438487,586278,303612,5176,713108,634337,726423,580984,682851,710086,735365,197598,589313,583193,723346,533768,507485,734004,668599,519454,725998,353810,722003,684613,494584,381023,26640,735343,624924,722037,417501,709054,710054,541382,545728,452728,709063,658336,710043,710042,736652,735325,263921,689424,717543,627185,73836,600129,158833,652128,631208,713173,673091,263917,586200,238981,731824,731826,258550,41419,652165,579905,641498,717568,422060,691716,574229,678764,731800,731801,643224,731803,731804,731805,477181,725123,631242,737114,713146,727772,726447,59572,42713,597750,737123,734095,713138,714466,292464,424671,575501,155785,73899,462910,685091,691753,631274,657886,737101,449959,504384,434654,562974,148999,662338,663667,716298,472924,515739,561656,728263,559311,407023,706013,604452,642730,734974,454285,708684,598942,708685,81632,594592,720326,700713,604443,515757,700719,319640,734945,641431,626695,2100,662353,556269,480521,307668,642758,710941,590161,590162,737191,590165,78005,641450,729551,590153,526459,642791,538403,642794,730570,653445,534046,590189,614689,78021,590188,654782,590193,590191,721619,678716,590174,590175,737181,729578,590179,78014,721607,654771,716260,685007,538408,481819,639154,590127,590126,712769,577152,706089,590124,359702,364298,736744,671223,42079,647596,712758,700796,706099,590112,590110,590111,583834,712764,381627,590149,590140,590145,163771,590146,711461,732367,451167,732368,712778,570543,590130,590131,590134,590135,732375,710126,732337,615153,706042,720352,683234,718037,733675,712719,592739,671268,684574,660589,683244,719354,64252,590109,700765,723401,544256,532277,532274,683224,642716,608836,733664,546924,592827,546922,554550,655131,656468,385957,705602,734170,460041,609222,681423,706927,534946,609229,624844,701252,688030,701258,682722,689359,17824,735474,702594,592833,648863,541209,735480,735483,706949,534920,50468,534924,565245,473323,723466,595066,423038,330090,647557,106258,583082,722120,682794,635579,508682,268439,619457,75783,688076,679121,248839,534970,62487,62488,722144,668492,574146,680112,475947,736774,706906,736776,587455,16593,513163,705675,505539,730629,680965,564761,666651,517511,615979,718983,82898,549114,525164,404706,413691,653370,653374,652043,680940,610282,491148,731953,537126,559370,645753,267137,597689,652027,471544,7089,615950,331333,726578,717627,644427,717626,678694,80282,717609,727890,731936,285803,665362,469210,382078,717617,414981,132555,670702,725267,632477,85993,737214,340269,716313,603952,618198,596319,499066,458539,726581,593289,729615,706992,593286,597644,598973,472835,510931,718964,345941,727066,650236,590086,590084,391967,590089,683157,81732,590090,590091,733754,561535,721753,594431,590077,593101,590083,79408,102648,692099,525888,43437,492630,724803,719465,204453,683137,585503,44753,288979,388320,617627,676861,641300,605641,643966,733703,733704,590042,590043,590047,707411,590044,719400,617607,590050,548598,641317,539641,538314,654621,617603,590034,716360,680931,87068,625215,499231,667972,666642,667973,595759,570456,590065,590062,590068,590069,653329,590067,642666,548579,667967,479611,583762,729696,730680,390660,590051,590052,390669,590055,628380,589070,590061,663598,574826,405378,590005,590008,577031,648791,648793,709236,592665,590000,590001,684401,639847,722209,655091,736866,499206,710218,692022,416044,661750,599297,254773,627872,565062,734211,404022,218217,736836,647486,709250,476547,583720,640843,736844,490850,692020,42178,65670,590015,715924,590010,590014,590011,590012,736850,76319,710247,607429,735525,587278,6671,712844,391124,707494,75002,239174,637219,724823,731136,652848,676834,675500,671149,43486,709201,75013,387095,88319,88315,712841,536527,490819,731106,679081,550802,732442,516926,692045,733779,736803,63057,625288,720482,709224,723510,257827,655006,700022,725319,700018,682623,534811,497424,516148,726644,551389,715992,655000,17979,554419,509884,670624,199529,714691,326970,125297,501903,338942,631074,636760,631077,534800,534803,462767,589979,679019,414206,99852,62577,441820,709277,682661,281544,86548,735572,587309,702637,735575,734247,589982,589983,589984,634125,636781,589996,589999,501956,369172,522858,579701,709293,709295,735558,714653,689293,589986,589987,495658,656399,736896,589990,589991,589992,710289,705792,425781,662183,728437,691508,736066,736067,662178,716458,531707,178685,645611,717771,736076,663499,623474,564659,653247,526344,716476,653250,491263,678512,603872,80383,730765,715148,484966,382174,505435,686139,416862,563341,725364,75801,633659,678576,736029,598839,71452,468002,737365,404867,51857,539690,691525,319795,686174,703113,63848,526387,737332,526380,577926,644328,679886,730724,678558,704457,538372,496900,577932,712065,671929,510816,238768,716427,736018,716173,729233,735818,721532,318536,721735,619675,732143,736994,731589,723872,587379,735453,711363,725747,735227,715979,684826,713441,629257,706305,730766,703285,735399,718719,710746,704067,562971,562974,729122,17280,81609,706242,614026,597834,720794,709511,706485,558231,468668,148999,583668,716293,607970,592391,602042,52299,637385,515739,640541,712931,66691,607977,537782,557372,597826,735818,563831,729111,710978,652764,618859,557373,706013,558223,647194,684137,719797,2,604452,307447,581014,585854,719559,710983,627119,482710,482714,595435,700963,570131,587809,687610,600085,650383,569397,730127,604449,100556,711848,593008,710512,564942,641431,148551,2100,653632,559775,709541,570386,617745,504622,717398,586968,705180,307668,644936,629317,607944,538665,705595,704264,88040,709956,704260,114301,652576,591010,578284,590161,704029,590162,353127,617979,688771,538661,590165,708621,57787,529955,716253,584715,665887,590171,78005,326396,627389,475399,717348,644705,452276,717347,580594,706456,706457,706458,728465,706459,538678,111026,641450,391650,573833,590153,688301,318536,706454,721820,715150,584968,603560,582306,584964,730539,726280,154187,706467,412845,705374,642794,578063,706460,613353,663010,706461,590189,709730,592125,464244,465578,614689,354479,590188,78021,663007,663005,590193,590191,644966,646900,477319,719306,645814,605733,679804,622059,572526,711802,594539,663001,590174,590175,706472,505507,590179,570581,705147,717113,78014,584505,730318,639154,538408,719538,641228,548442,374081,710108,590127,730199,590126,456603,66611,712767,536842,712769,672301,101034,97597,662980,593631,440017,706089,590123,590124,679186,683675,35556,681257,51124,623808,683677,588052,735894,537922,732143,710598,662978,736507,671223,618692,712998,543126,725832,522688,701885,590112,590110,722323,590111,583839,662966,707180,662964,703815,662962,389106,522213,734570,683688,736994,77747,301068,736999,662967,735666,662968,543130,590149,619771,596927,262499,162688,616023,708043,709132,590142,725625,590140,590145,590146,163771,662998,662514,662996,608401,719186,708041,662993,703825,733451,710375,381411,683659,629700,736963,85393,430464,590138,85381,590136,131710,7872,619762,617585,662990,594730,590130,702514,590131,701664,590134,675804,724766,683671,590135,444621,662985,662983,662981,509238,662982,735883,495176,662508,662989,662505,642910,537917,708229,714904,618667,18173,708222,708221,576265,534463,616249,616007,578200,647381,439230,577589,605596,662772,707371,589341,539908,642922,718039,641834,736705,711869,708239,592739,614055,616236,720101,720103,662523,52020,662521,662522,635179,589358,242446,590105,64252,708249,736912,590109,511122,710329,590108,569506,701853,557322,708487,683462,488857,683696,683455,683698,707151,683699,628654,639782,569990,410833,571836,615121,708011,723872,719372,532277,708496,577384,525981,639317,719378,719377,606894,439248,77329,547750,73307,99934,546922,723267,726538,65927,634675,77910,711046,601975,593905,600407,647751,728705,657537,539161,704512,726521,722169,575256,614802,706927,472252,477946,84431,683609,534946,609229,520517,701250,622664,592849,701018,704523,633359,552351,548099,613944,527399,506016,682964,682722,659998,656003,610680,60255,387919,736328,587005,704772,588578,725692,701021,723032,220003,705623,670519,610432,635557,534929,634226,268,552337,533830,102069,534920,679335,636880,50468,589673,712386,565245,534924,473323,517168,62695,658438,517160,574118,613928,683633,474862,535822,734124,712579,735453,330090,710159,713421,620695,716919,566984,618135,618133,257575,62468,721279,599646,701441,658674,635579,577634,683645,703615,735227,712561,621775,585497,624800,620447,564330,585033,237970,601929,601928,71188,619457,722154,75783,688076,677183,662955,679360,599683,595087,714770,662953,609413,662950,734341,716715,713204,714779,717802,662958,11489,714772,713441,684708,662957,561076,645155,565678,585041,62488,724569,722149,574146,656035,608312,705818,680112,577419,712345,712346,706906,662945,627978,587455,704585,603528,603529,16593,725045,632646,630468,725289,632401,62810,704586,143222,716573,102918,574557,17208,503360,587867,606802,583022,646805,589802,517994,704599,573483,82898,546084,664226,714382,629257,502024,717419,718989,718745,600256,605942,585696,706305,646832,538689,421080,645982,689520,704127,704126,491387,628178,49015,716118,66190,730888,602420,717689,708976,484603,39681,731953,727475,703285,708738,620073,392088,563217,597689,705468,684088,575683,706553,703289,460651,736172,489068,342235,525141,619083,37250,7089,621152,541673,708902,156746,725005,516471,705638,635958,57264,727428,705635,615950,705634,664041,597881,705629,717627,680524,675188,680765,718715,581049,717626,587822,600225,583481,683809,727894,583482,717609,620294,603717,717607,727890,723051,717608,728507,704557,704799,609262,704556,610008,594354,632220,634400,595450,26364,576943,713491,682950,633550,704308,655565,576948,414742,737006,708906,683804,581058,717617,683807,669718,683808,683805,714588,737008,683806,73524,706506,132555,642277,687144,670702,520952,729625,573200,704564,594394,386228,85993,527590,601530,718734,156770,706995,618198,728763,679528,596319,718719,153489,679525,259335,593289,596317,61315,537163,597644,598973,547197,608158,597893,575632,711097,691402,505113,510931,505111,625718,715697,621116,625716,625956,537653,70833,64398,594208,537415,537414,617897,618502,590086,593114,590084,706123,590089,590090,549400,684244,719200,636177,684249,590091,716173,342542,544199,92857,459843,729233,605419,615465,590075,592011,319305,592493,572663,590077,708313,593101,701916,684257,304290,638129,721507,590083,620800,711951,102648,664628,608933,605667,632829,705056,615690,731789,671396,655700,592045,573522,405196,709653,597973,80663,523471,77255,683137,606980,574855,638136,584898,601056,584655,710412,641538,608909,710877,731799,705067,730242,625003,510187,732407,672256,631502,719690,598812,721532,630416,573774,712819,592037,617627,590099,719214,684233,720436,605882,706392,640696,640212,676861,603224,641300,604310,584665,605641,100688,100204,602138,731764,727264,727022,708502,606718,601029,727267,536366,731525,539631,590042,593312,590043,706320,590046,590047,707411,590044,77273,505858,737074,721712,719402,616759,663106,617607,209918,663105,590050,581327,663103,627267,629448,89012,641317,537464,534194,538310,708758,708518,156216,728583,651597,590273,663100,590031,390445,590030,590033,590034,633950,523066,589052,680931,601023,730413,87068,728376,730211,655509,625215,678825,709617,499231,595759,706580,590062,689720,577098,45813,706100,590068,590069,590067,83733,721735,503226,632631,379662,654421,676652,605852,548579,424949,556183,359819,390660,631304,53092,708772,590051,590052,634810,590055,627291,443222,716145,690958,633970,144265,663117,584869,79226,590061,663114,574826,477689,590007,710227,590004,715919,590005,48745,619675,712648,590008,590009,725965,708145,708387,707055,709238,590000,590243,590001,635246,635249,707050,725716,722209,713501,633061,671581,710476,545681,647236,710218,652803,647234,618331,730062,611938,646380,590230,617007,722440,619425,590234,723769,580208,723768,599296,635253,708394,254773,732273,581773,627875,567244,646399,736836,590262,725502,700685,722473,646152,722477,590265,647480,590270,581787,76566,715701,583963,100276,712676,690069,691157,715702,712678,546751,736844,711341,710257,692020,690084,590017,42178,590257,590015,710479,590251,590252,590010,703963,75233,591103,590014,556782,590011,673508,590012,76550,723548,127746,704806,701539,637211,58907,684639,731164,452369,103798,76319,731126,587278,648359,707019,565489,6671,590209,720470,558557,707495,684210,610810,610811,619637,721558,662658,731131,514724,731132,629847,731136,586199,710659,708110,617203,488983,75013,708597,533020,617208,582822,534107,76576,442746,580881,56508,705082,673571,156062,629857,490819,731589,591799,730055,590225,700642,592883,683573,580413,602183,683336,102499,636153,640402,609813,590215,322299,590216,590213,692045,486308,590219,712614,647299,63057,590217,575081,537844,104211,707044,614158,617426,709224,720004,720003,719257,707281,620829,620822,566373,568313,627215,605447,584088,717938,624980,545711,39994,725567,702447,430978,655006,574286,596075,725319,734283,683710,679468,588450,534811,624978,679465,623405,683717,644109,679463,39518,587124,425151,404676,72589,706814,542435,613841,545707,538199,705720,509618,679472,551389,709085,705719,533733,727729,505013,683727,644116,622304,704640,705979,705737,724014,687068,729948,607154,736682,682842,679447,495463,679446,735595,679442,712277,586278,679440,5176,593801,97647,723399,125297,634337,552456,631074,554879,580984,634100,631077,381242,564030,679458,736698,732098,534800,679455,507693,683708,679454,534803,587379,683707,589313,679451,646333,616086,589977,589979,583193,703734,99852,62577,705911,610537,720079,711361,683750,575568,594092,594094,712214,713303,589980,712210,711363,589982,589983,60391,589984,6285,561177,715705,668599,618254,703504,567718,533781,634125,597112,424259,725747,709282,84356,597350,381023,26640,713531,589996,585154,734459,649865,589997,589999,715979,501956,704600,634377,610762,597385,679482,610761,679480,726627,709293,683731,608683,607118,684826,713326,683736,710295,713320,679487,620796,470122,712233,683739,649619,623822,589985,646365,545728,589986,589987,48636,568824,709067,222104,711371,452728,679491,709063,597370,641906,656399,589990,106348,589991,679498,570906,585330,624347,566811,706402,706403,706404,705792,91496,704228,704225,706641,706400,594051,554822,713185,616717,663028,452911,687239,663026,709908,403988,643418,716698,600129,731612,707988,622395,158833,49148,726007,663024,28229,663021,704477,535086,629131,491034,501295,713170,608098,663017,690655,663499,663015,719718,91239,712088,586423,562470,586200,518510,675237,270674,663054,608085,663055,79392,708840,663050,716234,633641,663049,717561,579905,574217,503024,675000,730766,730765,587763,69582,536154,585361,640183,625880,663046,666311,663041,706673,449523,610149,505435,80375,652394,631232,663036,574229,642359,716229,587536,730512,719734,667670,731800,706844,705990,731801,731803,731804,679415,155535,731805,663078,726218,663076,663077,729964,628072,688374,505226,630397,631242,653278,679422,679420,665480,515019,679429,592298,59572,71452,663063,610128,663064,84749,663061,42713,712283,634765,529209,734095,679436,626709,679434,387408,679430,647826,602512,51857,700080,112863,705538,387683,705778,663099,705535,596201,666363,663094,663095,613634,550492,63848,575501,611452,735399,679403,730967,679402,644328,577926,81275,716677,587722,625603,584230,559296,36491,726220,624752,601637,679407,679406,595104,663089,599947,705306,704459,663087,610106,643491,29738,707961,707962,594014,715332,632360,707719,504384,679411,676387,647603".split(",")).collect(Collectors.toList());
        Set<String> eis = Sets.newHashSet();
        Set<String> duplicatEis = Sets.newHashSet();
        allEis.forEach(x -> {
            if (!eis.contains(x)) {
                eis.add(x);
            } else {
                duplicatEis.add(x);
            }
        });
        Set<String> caEnableEis = getCaEnabledEis();
        Set<String> duplicateEisWithNoCa = Sets.newHashSet();
        duplicatEis.forEach(x -> {
            if (!caEnableEis.contains(x)) {
                duplicateEisWithNoCa.add(x);
            }
        });
        System.out.println(duplicateEisWithNoCa);
    }

    private Set<String> getConfigedEis() {
        String configedEiStr1 = "729639,729638,589981,589988,589989,589998,590002,590003,590013,590019,590020,590022,590024,590026,590027,590028,590029,590035,590036,590037,590038,590039,590040,590041,590045,590049,590053,590054,590060,590066,590071,590072,590073,590074,590076,590079,590080,590082,590087,590088,590092,590095,590096,590097,590098,590100,590101,590102,590104,590106,590107,590113,590114,590115,590116,590117,590118,590119,590120,590121,590122,590128,590132,590133,590137,590139,590141,590143,590147,590148,590150,590151,590152,590154,590155,590156,590157,590158,590159,590160,590163,590164,590166,590169,590170,590172,590177,590178,590180,590181,590182,590183,590184,590185,590186,590187,590194,590195,590197,590198,590199,590203,590204,590205,590207,590208,590210,590211,590214,590218,590221,590222,590223,590224,590226,590228,590229,590231,590232,590233,590235,590236,590237,590240,590241,590242,590244,590245,590246,590248,590249,590250,590253,590254,590255,590256,590260,590261,590266,590267,590268,590269,590271,590272,590274,590275,662524,662949,662951,662952,662975,662977,662988,662994,662995,662997,663004,663045,663059,663060,663455,663457,663458,663459,663460,663461,663462,663463,663464,663465,663466,663467,663468,663469,663470,663471,663472,663473,663474,663475,663476,663477,663478,663479,663480,663481,663482,663483,663484,663485,663486,663487,663488,663489,663490,663491,663492,663493,663494,663495,663496,663497,683674,683672,683711,683678,683676,683714,683715,683712,683713,665482,665478,665479,683662,665485,683663,665483,683661,665484,683666,683667,683664,683665,683670,683668,683669,665477,665386,665471,683651,665472,683652,665387,665473,683653,683777,683654,683660,683657,683658,683650,687889,683641,683642,683643,683648,683646,683647,683639,683640,663515,663516,683751,683754,670800,683634,683755,683631,683752,683632,683753,670799,683637,683638,683635,683636,683630,683628,683749,683629,683702,683703,663505,683701,683706,663508,663509,683704,663506,683705,663507,683743,663512,683744,663513,683741,663510,683709,683742,663511,683626,683747,683627,683748,683745,663514,683625,683746,683740,683738,683691,683692,683695,663498,683693,683694,683732,663501,683700,683733,683697,663500,683737,683734,663503,683735,683729,683730,683728,683681,683685,683682,683721,683722,683689,683686,683687,683725,683726,683723,683690,683724,683718,683719,683716,683720,710062,705221,679471,663031,620438,710129,574187,662602,706298,714401,714861,708780,701872,457482,715315,715475,710532,715240,712017,1885,689519,712821,589994,641625,711330,642103,625848,714520,589995,714918,590259,590249,718001,719537,690238,719761,718397,659812,710532,680306,720268,715179,720577,713211,715380,720949,540053,590021,679489,679493,719353,712202,708376,720490,424441,701643,710938,719618,721611,590021,679489,679493,720209,715669,712202,708376,717364,719015,711995,720724,720426,720684,720836,723981,663052,724477,718294,620218,718007,719655,725717,722872,671952,725136,716581,725435,680515,726430,722155,726390,575159,724364,714776,727080,724865,723343,725682,708885,725323,726154,728273,688007,703464,707955,590206,679456,712077,730095,728120,729881,712368,730120,590125,721302,729737,726885,729175,725471,710225,708904,706407,730247,726282,720284,730129,730910,729499,730228,728783,590103,590258,720066,729066,716686,732675,730384,712664,730252,730688,708025,730255,730191,701383,734516,734040,734520,734240,735372,734377,730591,642987,735957,728907,719261,735899,735050,733825,722160,728185,562735,561416,6,7,490589,550739,511164,562754,561427,549743,464293,703176,703177,703174,728477,703175,703172,703173,703170,703171,634905,464269,559122,703178,703179,464274,715167,716496,464270,703187,703188,703185,703186,703183,703184,703181,728468,728467,703189,703180,104743,452265,307401,716489,703198,703199,728496,703196,703197,703194,703195,703192,703193,297511,465575,559101,464251,703190,703191,728491,727151,727153,633607,464258,489537,549776,535139,547113,368408,452241,610975,12438,296292,60077,139413,85375,453565,427297,86690,478884,139417,104793,96019,97343,98674,464234,97340,382711,60086,86694,670585,96046,97375,683894,683895,37717,683892,683893,683898,683899,683896,683897,104758,452250,97362,98692,439285,13759,97394,439231,297592,609943,453551,382789,513778,609938,439255,537722,102155,610901,513769,478812,63985,705833,428532,704501,127384,683832,683833,683830,683831,48344,683836,683837,717826,683834,48341,683835,683838,683839,657541,657542,683840,657540,683843,683844,683841,683842,683847,683848,683845,683846,683849,403213,48325,103398,23026,86649,683810,632278,683811,683814,683815,139399,717845,683812,683813,683819,683816,683817,48315,703206,703207,703204,703205,703202,705865,703203,703200,703201,683821,729807,683822,683820,683825,439204,683826,683823,683824,683829,717833,683827,683828,98648,585490,209005,683880,403273,683872,683873,683870,683871,683876,683877,683874,683875,84006,138005,683878,683879,59057,683890,683891,596142,683883,683884,86659,683881,683882,610890,681224,59059,60048,683888,681226,683885,249952,683886,608573,683889,380058,154980,73366,98662,683850,683851,683854,683855,683852,224661,683853,683858,237966,237963,683856,142999,683857,669584,98655,683861,683862,683860,683865,380078,683866,683863,84017,683864,683869,683867,683868,703253,703254,703251,703252,703250,703259,98727,285674,47137,645446,512357,669412,645445,645444,645443,704625,62776,705951,105907,487074,500372,587908,587904,704616,717944,48462,512342,12,645451,13,538997,189517,475098,727296,153548,61456,703304,703301,402010,382791,729909,402009,587915,729908,587914,596062,178836,513668,645448,645447,717930,430993,476392,333816,441647,728616,610798,657453,574611,729938,84105,47111,715305,438003,3790,561313,537645,103288,559093,1132,729934,319526,705988,575957,620107,307545,247390,403395,703298,585375,703295,703296,403398,416629,499012,703294,703291,645488,703292,74795,596028,610777,703299,596027,715282,380187,142854,86783,50,585367,235404,476386,392185,97425,403389,610768,596014,331288,429950,596016,563918,610773,632188,283037,50801,727287,501718,235431,463039,153537,596047,705931,463035,596049,550641,490657,548334,105923,84142,536352,717909,13793,584071,634814,715291,369856,442924,86,549650,559043,452363,587992,727343,727344,560036,102001,727340,394833,703257,727345,573345,703258,501612,703255,703256,559046,438063,715241,464350,250965,105987,405922,584113,703264,51927,559153,728541,477671,703265,727210,703262,704593,703263,703260,703261,163068,703268,488308,703269,729875,703266,728547,489637,716561,560176,49814,703273,730891,560178,452380,285619,464323,331121,452379,47074,236670,560189,451042,670477,62600,394827,548482,716580,560195,51954,562772,703210,405853,703217,560117,729847,703218,703215,728518,703216,62613,704545,703214,703211,703212,318127,415381,87911,703208,703209,382881,404514,51968,537609,703220,703221,560121,102053,74601,512302,441659,610806,703228,703229,704559,728504,703226,703227,703224,703225,704554,703222,703223,439341,477628,703219,115356,405838,439343,190661,609823,703231,476197,703232,585430,727201,703230,104680,103352,609809,703239,560139,703237,703238,703235,525608,703236,729864,703233,681163,729866,703234,103348,177695,703242,704573,703243,703240,703241,728520,560146,703248,729859,703249,703246,393539,703247,703244,728525,729856,703245,117990,681164,490485,681166,23076,585437,585436,12505,703297,596023,96194,12505,513677,703293,681116,502970,404516,560192,271081,732405,736732,399568,459717,80515,422426,724907,423754,700933,67551,44891,17298,447720,31582,422435,433103,44887,43550,399544,700960,17260,614487,723600,79553,158579,386264,724920,709304,80531,31561,337294,651486,591252,592586,20897,80567,580596,66269,592577,410419,592576,54288,650150,435721,728495,92567,649186,229436,700918,216106,93883,688762,688763,146572,146566,700921,675454,592545,724990,711679,724991,721004,337239,724989,578238,708020,567595,735656,711683,734325,591205,591207,313283,735663,56828,733003,566277,735665,554254,708046,722351,710374,710373,196894,217499,734308,722341,593886,554245,337212,555567,709383,735604,325219,578203,723620,723615,712955,724943,709324,724942,56862,301263,664708,613194,709331,592520,708005,724967,724966,29280,593847,711649,67532,711646,724964,724956,56881,581877,723628,711652,735601,326520,567585,291435,581929,339936,471396,292773,267797,482047,556953,544983,470079,326620,593907,592602,291454,669558,580618,733054,733057,580629,542369,722373,482016,470028,458390,278498,735692,735693,735690,735691,735694,472682,735695,470026,722365,708076,482003,517390,554337,543694,734378,733009,722393,445092,721063,567605,497967,734343,734349,567616,721055,302609,721054,554322,735681,469035,735686,735689,735687,735688,733027,497954,460669,44820,91139,461994,520932,433057,650069,447687,421081,689999,520928,543613,399521,399522,448980,350257,386202,472617,436995,291499,556903,435691,434367,460617,448990,544918,135839,674089,651324,43695,409572,196713,68990,157109,434530,591196,204205,689916,81998,78346,723716,689918,689919,41004,92635,433217,44999,53052,446533,689925,93958,710400,675334,206890,591133,92659,591126,91325,91324,385072,30332,80672,689987,92647,255824,243815,592488,507822,324071,158431,230538,18681,690957,65073,734437,735768,241211,338691,721122,709473,709475,709477,709479,641918,710474,735771,733114,627626,733119,578132,722438,567480,733120,543498,735749,735747,81901,722476,721139,735752,325364,735759,723795,708178,722461,530172,578159,710480,710485,734434,735767,734436,735764,593713,709431,79617,734408,723733,81921,734410,326665,735743,325314,593737,326648,710447,735703,593733,593734,735709,279843,9957,711780,735711,664617,568783,735715,566126,735718,30390,555498,637016,543539,542204,721192,314765,325412,543514,254590,567535,543507,364923,733185,302731,708187,708186,554233,708188,555555,554224,708183,708182,708185,531587,708184,7359,471476,568807,708190,554219,708191,363619,542242,529287,734499,721180,6056,721186,733130,734461,734464,721177,721179,734470,469153,470130,520818,445157,471436,663267,66291,109428,388949,730983,388947,726000,727333,236772,702058,62701,285728,703388,597310,584009,471445,727364,703395,319598,506536,690862,343217,471412,472740,584033,74703,20967,687230,548365,549693,728631,729961,727300,728636,189547,702004,729966,575970,44901,19949,532838,619186,728625,110459,140283,362362,532840,345847,320565,459776,518559,333855,560019,728655,573320,86719,687264,61418,715341,246130,472706,472707,587969,434484,716678,32901,716675,434469,729973,728641,399633,572008,447787,729977,532825,86717,387666,675291,575999,23197,585318,645428,730953,73409,562692,538862,287973,626246,375353,710505,735809,464078,103608,156151,490355,536213,91403,726085,211097,591042,578098,711829,690816,440096,448807,250696,464084,67792,435524,410226,562515,144180,424874,464047,53161,681083,464049,235072,77131,448836,447504,587807,488024,626218,91425,616899,261317,77122,337056,275956,711850,711852,710520,92784,92782,412821,702081,91456,156107,592341,687200,592344,464031,77155,550544,102312,676552,549561,726044,727375,513593,260047,62900,92770,80793,580368,591039,592364,690829,464005,592366,103629,464012,507700,398021,453387,548210,477315,452057,711803,436804,727398,89141,711805,50939,711801,536238,43745,464015,439089,690834,690833,169429,548205,89146,734559,48622,721240,440012,610740,466651,555357,492933,735898,734568,556685,722560,723897,231517,580324,400,733240,681027,128976,103699,566035,734537,404387,49933,48602,610715,555342,440039,466625,610714,466623,220898,340623,96282,567370,452016,567373,567372,733210,627526,592316,734547,49922,557994,610705,440028,557990,681000,415095,439041,140664,735888,735885,568638,735847,467938,298669,653820,723864,554074,153918,54434,538814,78408,711885,735854,734521,41191,555391,594917,735857,711865,570961,708231,722523,668075,525519,96292,139620,382517,7407,609703,428351,41173,711899,593615,722552,721224,443982,582957,681036,114376,127677,735832,542074,567336,711887,734507,593603,626252,555378,500218,709589,416380,711895,567341,7428,581632,735844,538809,735842,621356,97512,544745,417639,684921,60234,175212,6105,716735,483139,74889,517113,61574,163227,382433,531435,703417,683600,684936,716722,471167,47236,704766,634697,733290,728728,281842,49894,458175,152597,568743,567411,703441,6145,431954,519791,660964,60269,213720,293811,47214,544788,96226,98884,721285,73582,332267,23249,542133,48538,353939,96215,585261,573279,370499,73590,704709,632063,84236,671670,496402,542108,405677,97571,704720,610641,35218,684940,555415,734584,609656,139593,734589,544755,98895,670318,431915,721296,532791,611954,461781,97562,98893,471107,472437,281872,84259,49840,85580,573229,77070,91372,574553,715486,472404,575888,434143,213776,548276,435475,89056,587883,726122,43721,726129,715472,715474,225763,664468,676487,676486,18789,458124,727480,110760,308582,307253,689757,30406,434163,108439,585223,31734,321562,109788,702191,726146,536272,43703,550553,574587,278267,308590,250711,322886,688491,703457,663196,86818,62858,544707,86826,3951,646603,715449,307235,715445,704793,728741,561217,563879,422123,703469,424785,728744,351361,716768,489281,647946,491599,703473,703474,703471,73526,703470,703479,703477,702147,262739,703478,728775,703475,575871,703476,702145,728777,715464,477279,676494,676492,435454,587840,676493,703484,702153,703482,703483,703480,703481,573218,703488,727433,703486,703487,506449,390948,388630,586528,307227,710624,563701,285436,525428,334924,65249,334920,273443,551711,501460,512105,551719,735931,66586,580293,710618,734607,374132,423652,709641,708312,665715,593592,436950,476179,104825,723960,708321,735909,721301,709654,78582,723954,423655,602144,575705,710635,538734,78574,447612,310941,488133,90205,538728,411696,104803,476158,89222,452165,711910,663111,592220,593550,579257,690724,663104,663102,549449,580232,476125,89255,592218,711904,709604,726161,92896,726163,439180,207995,42559,489440,500163,77269,102208,435605,17583,512179,17584,710604,41216,28237,464126,581586,476103,261472,663123,664453,594892,465473,232945,663118,464140,723903,503,274788,477447,720031,440138,683560,684891,683561,242308,684890,464104,466763,610611,683551,683552,720029,684889,684888,684887,719044,684886,683556,719043,732021,732022,732023,628713,732025,326478,325127,48730,610606,722682,683571,683572,464112,683570,683564,440131,683565,683562,684893,683563,683568,683569,684898,683566,734692,683567,733365,568573,732038,594867,67847,593531,555222,721383,80811,721389,683531,683532,683530,683535,439170,683536,683533,683534,582878,683539,368315,733330,12774,735994,683537,734663,609619,683538,543244,657295,657293,568584,733338,670232,543228,733337,404497,732008,555210,532596,719051,704810,532594,720043,683550,683542,556539,555208,683540,568591,683541,683546,273497,683547,54544,683544,581559,683545,581558,582889,683548,734674,733343,683549,734673,734676,452125,734675,734677,302496,582881,570838,611908,570832,721322,707016,683597,416473,683598,721317,683596,683599,439113,735973,719008,735979,733316,734647,711989,723971,555273,555275,722635,711993,711994,78515,735983,733321,734651,733322,139508,734614,569866,66558,67889,720010,683582,683583,683575,683576,582836,297481,683574,683579,556589,683577,683578,719021,381337,368359,543287,156096,641734,156099,734622,567217,734624,734627,496591,570866,734629,570865,683590,720000,683593,723997,683594,723996,683591,723995,683592,267618,723994,683586,683587,683584,78532,683585,683588,683589,78535,719010,734630,514734,710680,439124,453411,687083,58033,225800,703536,533971,517002,484598,417762,573194,728828,532632,314516,403106,46043,415103,326510,46041,684807,585188,544617,687094,404432,46038,570922,58044,35385,704876,59370,189753,279655,58049,505014,569941,393201,98988,609575,458290,151149,404419,415122,570939,96317,46017,405742,370593,727516,429773,381240,417799,84337,568633,60382,225849,97679,720074,555322,659895,659893,719087,60396,684847,84366,716811,609557,733399,58082,6289,721393,670213,704833,610526,573157,610523,279698,659887,683528,49982,683529,542002,684857,586466,58093,58092,47316,545976,610517,557960,97690,177733,139478,733378,585148,472542,720080,720081,267696,569912,719097,720085,97687,557949,732052,49961,404448,48630,83044,544641,545972,472552,83040,609526,89176,74911,651048,108344,472526,599741,30543,30549,548158,562462,434249,89163,42511,28227,663020,663018,574447,688339,587752,647800,89191,663053,460520,663051,472504,678992,346965,250823,651071,587784,536156,726265,726261,691955,488097,472516,690637,647809,663038,663039,690633,663037,458220,50982,561162,587774,322981,727541,562419,563749,473819,663075,545919,423561,448858,490397,663069,435551,98917,461844,72301,663067,726206,592297,34097,663065,663066,107065,545907,548183,362123,308691,436870,727561,518319,519647,726233,561107,726239,550491,386104,675046,562440,548177,728882,663082,562444,687035,536192,663085,726224,663086,663084,423594,586409,575757,259205,508985,586404,690673,586401,676385,675051,734716,480798,52051,173719,500014,311925,539946,580151,734720,539962,734727,477152,593461,592135,53390,708434,390769,76012,576900,1465,576902,92966,642973,299729,734733,707110,91670,593497,719105,77375,722747,683497,449922,710771,710770,707127,42691,539942,564947,424642,540913,707125,708456,707126,721410,722741,722739,539938,340450,92989,576929,726292,513367,390794,593434,664319,639364,479756,478425,549320,709724,726287,593422,652324,77383,42679,76055,689625,537339,91679,327475,106924,64099,40002,39015,594785,663013,540968,663009,663008,303491,722708,454478,710710,39002,594770,109180,524016,478417,501391,88059,581471,478414,721483,720156,720155,720158,720148,555115,582736,684765,733470,48861,173791,567112,80902,570763,555111,555114,720137,609501,683447,683448,581415,609505,733447,671430,131940,67962,707196,707197,611803,707198,557764,35537,684741,105608,642903,581425,683415,734780,721492,544434,720161,720160,703602,721493,48839,441113,721498,54662,314107,657168,658496,273137,733464,733463,657163,732135,732136,733467,733425,670165,733427,244643,92940,708462,298425,556496,721437,720104,719126,720106,173755,316738,384947,497588,628634,683491,404191,555151,467701,721424,722755,484271,732110,77307,496247,77309,710783,404187,404188,570739,556477,720134,556472,720133,707158,720136,720135,722796,52033,721457,720128,641618,399193,417462,734744,733416,106995,721452,707165,484245,683472,708499,708498,720125,722778,484252,89305,79989,233995,257949,155853,431702,97751,418727,728948,84447,609475,60475,532510,73799,472260,59499,31901,46152,609469,716963,608139,321396,569827,569829,570814,585073,104245,371571,703678,429400,611766,548098,60499,459263,84465,608128,702351,585089,570824,406711,569833,47466,96430,155867,97761,610428,550396,550397,35488,382211,645124,543215,58197,720198,720199,155819,497506,575686,158090,658441,703606,255379,321369,646464,320052,587683,10184,71179,447243,719193,531242,574372,704951,281657,382235,684733,684738,54603,716921,214837,609425,104285,585034,48768,585030,545856,131868,610400,733490,728928,533863,733492,684706,54615,544532,54614,586358,585045,353713,353719,47421,669107,71191,280302,282961,609409,84492,563663,537376,537378,461519,588967,564998,725048,689545,576973,309699,437891,153282,713068,727695,727696,90279,512068,335969,689555,688224,727697,727698,551671,727699,726368,280368,308377,548025,538685,377715,725062,538686,588988,726399,447235,713084,308349,411282,726384,462838,435238,561036,622493,726381,462830,462831,689534,691834,225537,100727,551645,474819,690510,524050,588982,100722,728991,588920,647700,689580,588922,564956,73742,519501,592188,588929,727664,727665,376406,576931,491364,322613,676274,154570,72420,425872,550383,714342,548063,489041,714341,59457,592181,251868,275814,714343,714346,714345,727683,96407,701053,726354,18952,478372,449851,97731,465059,449852,524087,576959,310616,690542,575629,100752,153260,17624,727674,726343,691871,701061,727675,725017,713033,575633,610473,84410,678805,710865,453291,678800,76141,721520,709875,389877,100697,719207,734844,580051,710858,308409,592010,678818,709886,207703,491551,77467,721506,89448,453278,286541,40168,540806,556398,351287,492872,52196,250541,491545,642868,719229,719227,100690,579089,555058,262524,173608,592035,721531,683386,722855,11618,719211,721527,721528,710884,603225,719217,400727,478552,540865,665530,579010,489207,90481,165183,678851,722808,666858,453243,725073,454572,708516,734803,479895,640242,579024,594632,734808,338244,593303,60603,76175,713098,399264,106803,399228,708525,678828,710849,666885,593334,691800,513261,467882,580014,501283,73915,454596,537206,85902,340539,412740,479868,302298,543019,611707,466527,558997,34346,719289,683315,34344,34343,733594,197641,429596,429591,441219,719273,314257,443874,719279,719277,683327,683324,732272,7724,629818,720291,720292,65429,467830,733570,274599,715700,302241,733574,34328,232756,733579,64102,594614,48951,546976,546975,720286,479828,546984,684638,732254,733584,733587,47614,569607,571922,670048,104222,556379,720234,657078,721557,719242,455826,657071,76106,732220,10335,471091,65450,65452,569616,555035,557694,683366,315537,89408,556359,555028,104228,657064,732233,657065,733564,595912,66798,569625,185605,497694,64138,720250,720252,719269,557679,628530,583922,732200,90419,628537,302201,495028,65460,158015,734869,105543,732208,732209,720244,720243,198924,721568,719254,557667,719253,472395,732210,734876,293734,155732,405534,702446,703777,610338,611667,97870,610340,703769,472364,715768,715762,713102,611658,702454,484345,611662,279418,83224,533732,714420,131781,646315,725100,105455,96569,519443,58292,727766,142431,472348,635666,105448,647636,586279,702472,484328,727751,688161,714439,446099,429534,610305,726422,533716,611642,661932,472351,304951,713118,714443,609329,544425,447371,727701,727702,104182,35591,727700,447378,472327,545763,588895,647676,587560,422089,703741,486965,471006,173584,472333,447390,714409,544401,462986,422095,703753,520437,280437,727716,714410,498931,587586,485619,574270,462996,727709,498929,22262,703759,660640,458021,732296,714404,213649,725165,39112,461631,538582,449997,308483,713181,689414,213658,641478,564881,725153,460319,726488,41429,538595,676124,365832,587511,393081,435343,587531,678745,462946,725189,575552,30760,85835,588854,666794,238973,710913,461622,575562,691717,266176,726451,73866,727784,563502,97832,97833,462924,691789,42721,29749,474902,688128,474906,690443,690442,1659,621024,563511,97827,622356,58241,29756,593386,248340,576827,714465,519401,678788,409077,58252,691768,448645,610365,690427,508755,689436,661904,308422,309755,97849,462912,97841,701187,422019,550234,714484,307105,690431,250639,16429,478233,376207,734955,733626,734959,214593,480557,720310,377536,707334,390517,568283,466254,719322,627104,734961,719327,710993,275508,642724,732305,708674,493850,76255,341569,526415,425700,238547,627115,734970,341565,39299,733607,733609,480536,720334,720329,719342,683259,412419,556266,109385,378875,708699,720316,708625,678724,292049,653423,480515,570553,710949,540746,570550,708620,539750,570551,442243,480510,11708,480518,431595,734922,725194,725193,481833,238593,710930,708636,734925,734928,570567,707301,388212,526457,455537,88278,569584,582540,41572,189487,709973,721621,708644,719309,710955,709988,538412,593206,538415,40237,654767,388242,388240,678719,481817,583895,583896,666747,390595,546887,533576,359705,720396,659576,732380,556207,556206,568202,684528,54876,195342,430204,595801,545548,595803,719394,684540,443510,685863,684537,684536,274247,53558,684539,684538,731066,157078,303246,569542,546867,568217,532222,594502,77531,53560,460095,583844,105844,733695,91822,731033,77539,105848,104517,472069,545529,219437,405226,570547,671209,154813,196667,142840,702505,582526,5203,732372,582522,731048,732339,104584,706041,405293,47737,684564,719366,718033,76225,684567,52258,498676,733672,732343,133073,103262,574090,586073,182008,706055,720340,720344,720336,720335,419878,35741,733681,733684,76218,133081,733686,545595,733685,718028,733645,586085,733649,502407,64259,684551,684550,720375,484018,684544,684543,76242,684542,684541,684548,357113,419888,684547,684546,684545,533588,684549,472022,732325,733657,732328,5256,64268,558883,77560,77561,684553,502402,684552,532269,718042,103235,583810,628424,734992,732332,732335,734996,731004,587475,622202,509981,103176,70034,447055,611547,472007,714559,713222,17843,97989,97982,688069,610207,546917,178719,610211,713214,713213,571905,459019,586165,103152,623510,726552,232481,432810,587490,70052,727889,463995,702588,574184,245755,611525,57084,551497,688028,245761,551490,727878,33060,420834,418526,34398,713237,534925,624820,564795,647546,611509,727823,395327,424375,731091,727815,731092,69081,100938,544311,546974,54833,53508,673842,646222,405202,405201,546957,448329,558934,83388,82050,83382,69098,587451,701210,688075,673816,533626,660534,474603,727837,491185,726506,435052,726507,701209,298291,731072,731073,588792,661869,128483,703880,562166,546934,65512,419804,519354,419808,395319,533637,533636,255126,731084,214615,142766,507375,450665,435049,690394,463939,678647,395367,689303,691628,61967,335741,424329,475915,39238,451942,394040,438981,680969,88197,708714,622276,708718,563439,424340,691618,491157,475928,27256,308135,491158,605701,100987,73968,463915,73965,539774,395346,61981,41533,691603,609297,609298,641378,588731,540752,111609,226622,538450,61998,424361,58345,435001,451951,475900,239938,564713,609270,610258,725249,664040,549167,551477,609275,85973,581289,701285,294755,691654,42837,72660,58364,509933,42832,609269,84638,30851,382074,249311,85961,691647,593297,689320,593299,713284,424303,648803,30844,16538,153484,191739,280149,508624,84653,237317,252931,525186,676022,556173,732415,47811,22518,732417,453052,363010,390630,570478,708787,733751,631969,99004,557495,22523,570483,225362,147556,732430,455698,733723,581150,582481,453073,375012,594468,707474,721785,465014,719466,719464,340340,40394,641310,480649,593124,707482,721774,502317,684477,733741,732410,733743,679939,678607,135524,733702,678605,678604,262357,678603,678602,594402,708743,428058,208833,526323,719401,583749,501020,583747,583748,583745,678611,583744,678610,719406,583740,552608,678619,678618,678617,678616,595725,678615,678614,678613,99049,26085,292181,583758,582427,583754,678620,680935,455694,135549,541933,87090,571789,571788,84818,340307,582437,720407,159502,481931,541922,375049,539625,526308,84809,389694,594414,569480,442366,60864,480608,388356,224090,553904,219522,64311,116403,571721,64317,533452,339302,206216,417366,76301,731171,520145,731178,431672,399053,65651,104433,702612,671109,671108,130722,671107,315337,507156,672440,545418,46543,45210,154706,661787,533426,727915,51024,467609,715927,727910,387078,702635,595700,702631,358525,727909,702627,53697,52367,660443,287650,147597,88328,731130,496139,399097,330915,104474,496131,556121,158244,731104,671112,671110,732438,587294,720491,706185,533476,405394,370203,720496,47837,359895,357230,416010,443622,459184,11881,89678,384849,733771,233887,88343,104438,684441,615007,245844,615009,684438,87003,544123,29945,612755,115044,103049,562066,30943,562065,104395,702690,562069,726640,104398,115027,701368,115028,725311,575382,612747,636737,383407,474793,69178,726671,587375,104361,724015,700049,485425,70171,282815,678591,507229,510864,474762,420946,587369,678595,459147,587367,420949,104377,725331,104375,724001,460130,485413,712020,725329,417313,178615,636762,713358,54949,712023,624701,532201,279233,448466,727936,715965,450773,564681,713301,474742,588633,589967,714618,268567,727932,727933,727934,448452,701321,727929,727925,100827,17939,21196,714623,545511,78911,78918,405312,519246,727967,103070,116395,715981,335882,297084,726628,731194,269895,142636,420907,714654,214722,713323,587344,546815,549091,534851,155942,727955,727957,34470,726621,574025,726616,549096,713316,589993,436495,679857,589936,39357,59760,691502,423123,691505,576610,474704,60755,450731,71413,589929,736073,382158,736078,551302,736079,679874,526374,152085,395463,707513,491270,708842,60779,280239,610166,492596,610164,611496,526346,610163,736043,736048,679842,667889,589960,680827,69116,587300,562007,424488,576650,540637,707520,41649,526355,610154,609165,450744,14049,553946,564671,537056,624785,480590,593158,478271,609152,83429,712052,610147,82103,84760,736026,309538,582499,736027,712055,736028,454297,725352,724021,726689,391930,480588,537066,58482,594476,636701,84752,689227,678585,712047,725382,678548,509841,538360,725387,424433,448401,83459,736000,551336,140059,712072,553997,177335,491229,481898,481899,339395,28644,713393,712063,593174,54901,17997,736017,380948,731204,671057,671055,482978,731208,225006,706244,643817,719564,470988,671061,584998,671060,281184,581000,605558,671062,733876,481633,671067,731217,479334,594314,482960,721871,720543,444696,352006,76493,719556,444686,470995,59935,643827,402801,581015,479325,732554,569387,101679,733886,582360,732518,671038,719581,720572,47918,22611,570373,720569,720568,568064,629540,719584,327023,100313,733853,671041,733855,671047,733854,482944,671048,60936,593005,60934,595664,503535,58622,282489,733860,365336,733862,671053,642517,672380,671052,671051,539511,540501,71600,481601,733823,481603,240680,556092,733827,392979,401526,225057,57302,733833,733839,595602,707543,526213,418380,237019,553804,641206,733842,329639,671078,707559,40480,571667,571665,720530,469932,736084,582319,736082,582317,583643,583642,671081,671080,418398,679806,706238,289901,528849,736092,249011,671090,736099,671093,100366,671092,667835,26182,21384,65768,686943,686944,686946,731290,275348,686940,662973,686941,686942,6746,77759,686947,686948,686949,44018,646029,406337,545306,560962,497144,701403,251392,702731,686960,686954,686955,418347,686957,33365,533338,558613,686951,686952,274005,686953,584921,686958,686959,90706,44006,674941,560974,701413,147053,726713,701411,519047,75101,701412,445970,701410,661667,34684,51145,547963,498459,731276,569311,418354,433974,433978,546613,726700,75113,222820,51150,686935,731282,406307,433981,686937,199930,431320,686938,686939,731247,672348,671018,106522,405055,32005,51162,686987,686988,557338,686983,686984,686985,106514,731253,672352,546693,731257,671023,90750,720581,75136,433914,558661,719598,720579,719597,719595,719594,684336,588480,87105,731264,89763,433945,47950,686970,462469,686971,463794,405033,686967,718298,533349,686961,686962,686963,686964,731231,445928,686969,732564,671003,390380,674996,686980,686981,671008,686982,559971,560947,686976,686977,686978,702706,686979,686972,686973,686974,686975,546666,671010,448142,70279,724114,725446,724111,575252,725442,611305,551283,714793,508415,451789,258843,690127,510722,53705,397710,106454,726766,41700,724105,700156,690134,677176,736116,424197,690101,450475,725450,587262,725453,726785,725455,274074,637968,678482,636646,678489,385728,143416,678485,563225,563224,674922,674929,463732,725403,674928,463733,725405,674925,725400,674927,674926,449429,575203,636656,564562,650943,674931,713408,674930,546712,714738,533421,65736,700115,689199,686911,726716,726717,33287,52420,714743,510754,589850,691480,546704,726750,463716,463712,577893,726751,689167,725422,677182,691478,713445,690143,436147,186549,45247,105147,713429,674918,725413,105140,674913,674915,726743,635358,450426,510775,16702,581096,708947,707616,156787,708948,609072,708940,526245,721911,593080,142134,1972,84841,610065,721915,610063,385795,736186,721917,323503,641118,71518,526242,719601,154113,480263,412115,481591,72860,102925,736199,707619,514239,154120,588506,414789,653126,83529,129141,425441,630491,721936,720604,721937,719628,736168,71559,708975,15492,481579,437415,706318,538215,166115,708972,105183,166113,576522,719610,610034,611365,736176,57256,736177,736174,719619,397780,721928,719616,309204,481566,677118,40420,678447,595699,132779,551242,712174,102959,654486,593041,82224,677125,641158,677126,102952,736145,493549,101623,736149,102955,526284,712179,480222,708916,300843,481550,481551,594353,700197,126911,611342,736155,568094,677137,736159,494868,679794,480211,395142,156765,581070,724177,82248,470887,551217,680744,736125,438721,680745,494858,736124,680747,581067,156751,707605,71599,564508,609003,611324,82234,552535,28762,680755,736136,526271,340010,680757,470893,736138,631719,731324,479442,733988,707693,99251,101544,732662,413601,705040,570246,595521,75292,402921,102887,478115,719679,88595,455455,479421,438917,720690,684231,466111,582239,352140,684227,582235,582234,730009,733971,731310,456794,733974,705069,731314,730000,87282,731318,705063,731319,533288,720683,660291,545250,251401,719693,583573,583572,102865,731321,545251,733985,582243,179388,707659,400316,377464,303153,733952,667707,733954,138804,570205,572863,705008,718301,720614,99281,365475,719639,569221,718305,732630,718303,595516,57441,705015,497290,721976,611298,194955,584852,431494,569232,328434,706359,595505,705020,378796,720642,720641,720633,720632,721963,719653,720634,582208,100248,642437,690093,64552,701513,726812,726808,686825,702834,726805,45470,535866,329727,456731,535881,44133,730065,648564,587085,729077,63237,406451,389469,329731,729074,690099,689080,571505,535852,725501,207321,730092,209984,405110,534517,199801,599087,87216,497244,76568,45450,731396,210965,535848,731399,63252,219314,497241,713504,456716,63257,701545,263458,673507,417158,444744,700206,701536,584824,701537,569204,498566,661538,136293,686817,44105,63264,45431,106401,106400,388199,418407,559888,418409,575054,76581,718395,731373,162509,731379,45422,672235,575060,575059,258930,731381,672240,731389,535895,547877,717082,461257,419769,729063,87257,232313,245603,730046,346246,207370,288774,730043,717072,534571,76594,717074,151871,732695,686859,498526,731367,295912,725562,700261,689025,726899,283929,689024,680685,713589,736224,713588,588451,713585,736229,713584,53824,736226,713587,713586,588455,106336,95683,726889,551152,677051,576466,575139,451898,450566,690011,712242,677056,736238,448280,725584,463879,677017,724254,246965,552472,551140,372547,613826,677026,677027,736204,15526,736209,333009,463881,373867,713592,713591,197117,713593,553788,491096,713599,736214,713596,713595,713598,678363,713597,546607,616088,589737,522634,713547,690045,690044,15503,616073,649855,321020,324998,535960,563117,424268,449541,64526,257691,448213,700232,81089,576427,491075,576429,713534,438893,384538,712201,563123,713559,19086,551181,478091,448247,491069,604082,308024,491064,52552,714898,564464,322311,563132,689051,44038,63212,462510,32053,64549,63219,534601,690038,713556,105017,14295,679618,478071,294664,39591,424210,82303,526124,82304,424204,565733,201209,706419,565738,514120,719710,611267,503479,577711,10704,383241,565740,106386,642353,684191,396556,684190,541730,166009,653008,736282,82327,57365,553713,736287,736284,720728,719748,565750,437551,491029,155363,70349,564421,736290,719731,82316,736291,577733,450502,736295,401533,719736,156681,491018,724279,279075,724276,595575,28892,312933,83676,678335,736269,68049,725593,96974,724263,39544,570289,481673,57393,725599,570293,595571,654352,736275,281377,83665,736274,736279,677014,680657,677011,251504,214573,68057,541772,724297,724296,565707,41854,514192,279056,539450,9163,511915,466031,736240,654389,83692,719703,69386,719702,130023,53809,679647,401587,655699,565717,442096,539462,736253,736252,736257,82353,736256,87392,647189,595419,533175,705158,34866,468433,584756,730109,545164,705167,533180,672155,239403,684140,558469,632936,720783,20228,74087,684143,684142,406176,620927,731429,717161,571465,596768,559787,594101,21524,569150,684104,468411,57527,57529,98056,445797,717155,557106,717151,717152,730119,582123,269285,731405,457737,294222,684172,46806,706440,584719,684168,419463,731412,486288,660197,684182,631642,684181,660196,21516,631648,545192,470716,305466,719759,402609,569109,729109,533199,353149,392716,504676,378453,630330,719775,719771,713619,713618,474275,730197,713615,62016,725601,486255,419429,713622,713621,713624,474265,713623,88642,50038,88640,713607,730187,713609,713604,713603,474283,713605,147282,726919,559702,713610,348598,90983,713636,713639,264433,713638,33591,726951,50051,418114,636478,418115,421752,197508,604042,726948,377167,713644,713643,713646,87334,713645,713640,64690,713642,714973,713629,674714,474264,714957,713626,713625,713628,618673,713627,701666,726943,299110,51397,87329,499532,713632,713635,173565,713631,713630,588248,705198,365195,104102,673432,449273,714903,558435,717181,208315,406141,75361,717188,717189,559759,534458,589583,406135,729152,449262,106773,717172,75376,662767,276465,463593,717175,86019,558418,717173,87348,106769,564293,421704,87343,730177,730175,4397,730173,463553,50093,499508,726906,726902,421712,276457,535768,713602,713601,87376,499505,535782,730165,406118,730160,701620,75393,589590,588260,730159,591974,735009,540386,725688,463534,488836,551041,463532,40618,551035,576338,452879,81173,105348,588331,588333,667582,105364,322034,272906,540378,511807,713691,713690,713693,736356,713695,563042,589653,300698,589678,463516,155635,723040,155636,538040,28953,723049,53966,588353,736327,526066,736325,131677,576373,449243,65955,539386,666273,575036,678240,575039,452889,53970,735001,104006,589672,735008,81183,735006,325974,713659,713658,725645,700347,65966,551088,104062,700342,577630,713660,15623,713666,713665,713668,691255,713667,714998,713662,713661,713664,437212,713663,700350,713648,713647,713649,700357,551076,534501,89922,713655,576309,713657,713656,736311,713651,713650,271615,159177,565660,713653,713652,511880,564339,75305,591957,726997,552390,481386,132958,726996,713680,713682,713681,680588,713688,88620,713689,414595,713684,15605,713683,118692,713686,28929,713669,64665,591944,301999,219076,247986,700376,713671,713670,493353,713677,713676,713679,713678,438551,565684,713674,493358,219087,632890,735095,594174,735093,526008,667503,720827,69441,542956,37044,625783,481350,443085,707860,705203,25077,244062,582189,438519,541611,296984,82447,719862,654220,493321,82444,439850,438520,541608,656868,656867,684082,594176,452809,654209,735082,735087,83765,452802,444382,724397,595453,269390,611112,552321,467021,82466,53902,68168,39661,539367,296932,401245,629293,470677,494622,569186,680532,553649,735068,100547,600456,566910,707832,642273,540320,439802,707837,566911,112507,341118,469664,470646,553630,569190,735032,403887,735034,101841,431095,583485,125818,582172,642282,723070,15695,268029,540312,566929,27670,595472,570190,25011,735041,706508,719817,719816,100520,101851,53934,481522,731567,731568,481525,481529,660043,729248,684011,468550,149724,674693,583301,481516,674696,662691,331881,505805,559673,720897,468566,584645,545036,545031,730214,731587,729267,481500,730251,571348,572679,685318,328231,705292,585984,731552,584650,395073,86192,729257,731558,546337,98179,559654,316259,83914,572683,98178,83915,268072,98174,443259,58982,558319,101772,547678,730237,100446,265863,731526,731528,558391,367893,684052,69649,632848,55002,685378,720867,366553,672077,56339,731531,49195,46910,731534,433873,597908,597902,69658,331847,573956,498397,685393,720860,98198,305580,98195,585933,719877,456504,731505,731506,498387,731508,71980,656837,685363,83972,193866,498370,706599,705266,731516,239588,731519,656828,706598,529824,473089,731520,473065,418209,99412,725721,74114,605270,76785,725715,210706,473056,605279,547604,106610,406215,473073,536974,635018,523664,588162,535635,691186,691162,43039,473042,473041,63488,725746,560619,86127,15809,522301,473035,588196,86122,473050,473053,287248,540199,535620,473052,561959,523642,485021,473046,536944,473047,473021,98125,488995,558315,687961,576141,576146,433808,577468,701708,486341,661323,635046,474344,86143,87475,509384,58916,730269,564164,701709,105308,44334,46995,475692,310091,730260,310095,99446,488989,591762,685307,701715,685304,548986,731588,589479,461019,536983,548966,577492,463678,725702,406240,591793,32347,564181,450395,508076,87481,306803,576174,534331,559603,473000,473001,473006,730279,588203,156823,14451,451669,576217,642169,81297,499603,565572,735137,736468,565573,540271,156816,539281,723132,463661,723136,712480,451656,679477,564259,724493,679437,184035,476942,680424,735112,679444,719900,735118,451684,667487,588218,156832,735120,680433,679457,679453,436059,385605,736457,724432,724430,27721,725761,725759,578836,691136,322112,666171,591804,591802,476939,475607,724418,724416,424018,438654,88710,173344,735100,39704,735106,735104,324762,735105,566876,64771,106586,700485,552273,336730,679481,539291,361682,258743,452978,591838,408819,679484,271716,565556,397613,64784,400090,105269,724444,481496,299268,88739,552256,712461,725769,452969,412068,679499,438672,452961,679497,438674,589510,731600,582061,413318,732933,403993,439948,68238,336708,71880,481481,611029,717300,569096,383015,452916,467187,612361,629127,720948,719969,582055,70549,300761,719964,705322,732941,705321,155562,155564,395017,731617,155568,731618,731619,481473,705325,706652,452908,570087,705323,167551,611017,570095,720933,68242,439940,731620,383009,625651,403974,732916,685283,685282,685281,595397,685288,570097,685286,611007,594073,529856,467165,594071,358082,719986,624314,582094,685291,270423,424004,481455,719971,717310,83883,439963,582020,679418,39794,540223,400044,540229,612318,265900,597993,594003,571370,82587,720904,719925,582017,679421,680409,68288,719908,707947,330598,402697,367816,100427,735182,571380,735181,554851,81241,541550,82573,541551,68299,197394,155558,719939,736480,570063,629188,630175,401346,439932,566800,667440,679409,415969,679405,539227,719928,735160,736495,655464,467130,403998,82593,456483,735166,100400,736498,735167,342555,728035,731689,728036,572539,729364,728032,270057,572532,559563,343519,716053,495807,673241,585843,731693,731698,156376,704077,597821,470519,662569,32432,283358,584526,730339,730337,5824,535571,139827,731666,140818,458846,573888,384092,32422,731670,18118,305233,5836,701808,731678,728043,572565,103838,716066,612299,229862,612296,612295,499378,612294,704091,368842,730359,730311,239210,434852,732978,729320,102562,265508,265504,585806,673284,69761,730304,368856,673282,305212,558261,731656,558262,102572,560528,45704,585815,546288,719997,728013,730333,728012,728015,728014,68458,729342,348394,266818,625571,728017,728016,656717,56474,730324,731630,103868,731631,434866,241520,572527,67138,68468,728006,49281,685247,446847,547598,730315,434815,62255,713858,701876,725845,98203,561806,381872,620779,499325,499328,62265,391211,636219,701880,509219,463396,379552,701878,649546,701898,725867,701893,98221,725863,552069,725857,662518,490971,307818,511553,576091,475351,700577,725852,724517,86235,606457,86236,662509,662507,381889,712542,323036,649569,462005,725801,43139,462000,311055,577353,99575,591650,277570,661204,438384,701824,380582,701823,86264,535561,730385,728063,730380,547535,565383,138533,548861,98264,576046,74294,565391,700515,712513,564067,86271,547508,547507,113269,577382,510257,511588,450028,675850,728083,451353,723260,553460,63502,539155,552131,554791,552124,630076,493186,26553,682984,589405,441975,40853,64844,481198,724584,40858,163461,554774,439662,52868,39850,540123,670989,481186,670986,88827,276254,451329,277589,264281,439699,682965,682966,735239,735236,439697,670992,670995,591748,670993,391158,670999,670998,27879,438352,80094,565486,264272,735248,735209,735208,401098,725883,680362,679382,724549,637502,88845,536911,735210,290548,679385,439631,325721,439615,500958,725872,724545,725874,511608,494463,540167,300417,724571,724575,291891,651828,62239,482478,552148,456191,387950,87535,452610,87531,107306,107305,439652,63570,290579,9457,723238,468188,360000,88856,540163,540165,39803,657946,705439,542729,612245,717429,83990,343597,471790,493105,583282,70689,158957,717409,731737,49269,444175,705448,657939,685198,468167,657941,258459,583279,470475,9487,730410,516457,106024,317292,705451,296749,542702,731705,372022,731706,9480,657965,80019,106020,657969,56391,595278,685165,657971,718773,632674,657973,81352,404935,456154,494415,384009,372016,157641,668610,657957,657958,530726,52807,541438,595218,416903,540101,92021,482410,553413,331581,469441,468110,735294,682940,471750,13291,374698,670974,404997,657976,597871,656652,329258,584567,584564,269141,433487,718700,583252,601527,541410,397322,572597,572596,64808,81395,642028,432188,67088,530759,583262,595229,198131,398664,705427,331547,134989,735283,85093,560431,431029,572414,407394,421699,701905,103727,470621,558107,57862,57863,433676,608928,730460,44531,535481,471966,295490,228633,470637,384175,612195,546124,56548,612192,418067,536781,498183,560452,407377,535453,725900,571100,443033,97093,534116,536778,608924,612180,731795,608922,458978,571118,573779,331622,716182,662474,294180,700608,443016,522155,56565,585756,271477,4635,700609,509196,18213,499491,499496,102442,729441,18211,266959,718797,145565,420328,674495,731770,132262,158892,705486,156211,156218,728104,81517,499479,103779,547495,534161,499474,560414,378324,560417,560418,657930,81551,546157,238018,418032,547484,103747,242975,458934,499460,157565,103760,157568,633967,633969,633963,633965,633966,45803,506958,685131,546140,633971,48078,717478,434932,434930,725960,637417,50391,725957,38700,87672,87670,589248,713967,407305,725952,379668,75695,591565,462185,102487,735301,381983,381989,510112,724653,590263,723325,700684,680268,476788,499423,450198,61063,87692,366392,591589,474143,712657,392640,99665,723310,723312,608991,589274,712661,590202,674400,406029,712602,524777,725922,534100,73063,499409,210506,725919,499404,451462,85055,589224,44576,425193,156299,728184,139734,289602,311184,98357,725911,462132,592851,579898,452784,547410,450160,45898,592886,488732,463436,724612,724610,152716,276331,724603,712632,548732,565277,589226,548719,44556,548717,68504,724602,488723,662420,524775,114487,608956,211860,608952,608953,422938,608955,85065,592944,591612,63622,260811,401174,92184,555999,552002,710071,542692,710077,681539,564005,670890,222528,591606,488700,223850,463420,312596,413172,735380,555989,577318,710067,80191,542683,734057,679234,52988,503072,392591,542668,592965,734019,541338,564015,722074,464722,576002,577326,27982,709097,735356,50337,589324,463402,577343,88939,361411,718805,734033,235856,565363,734039,438416,64994,464700,724675,63666,724674,481284,709030,734001,87630,289682,567953,553376,553374,481279,723329,359127,579935,61014,439744,710026,541392,735308,735306,735307,301868,73006,62357,724698,681552,681551,14639,258502,437115,437111,735319,439758,74346,539064,425131,165980,577301,579961,722026,682893,709060,494563,87641,437100,679255,438432,542601,403750,439704,595138,673081,427716,716217,555918,68487,110891,633868,55174,661084,79123,468288,81441,199397,92124,294207,110887,92128,439728,48048,295532,397409,546195,494534,80143,717560,439718,731832,258560,729534,704256,716220,469595,730518,730517,456283,730516,730513,706843,543972,483869,402467,731807,731808,718830,734081,36049,504377,80160,81494,706855,48016,731817,445587,80158,583111,92169,403773,706869,398775,469536,595121,572480,397456,595105,705546,332992,595103,495818,471862,398479,155299,138715,650355,546008,676985,560316,499170,206965,447967,97194,97190,361185,472925,585601,608821,612081,730587,435977,560321,728267,729593,730580,536699,560325,535369,572302,716289,271130,420014,345925,54003,700702,546006,458643,676967,397129,94945,712701,460925,535336,728296,458609,81628,31333,102739,524686,574977,273789,434686,459941,510063,499140,535342,547308,676973,472919,206998,3428,79306,584302,728281,458620,67330,101416,704266,731885,476495,451190,20657,534066,686350,704267,717586,3437,499125,600066,730546,730540,729555,704270,488468,562948,612029,586907,586906,586904,101475,101476,321933,716249,730533,674382,729585,68697,731865,80338,94980,463157,590192,488462,381610,574937,102778,499108,548694,559330,730563,499100,370956,354003,94970,728249,548682,574940,419017,79348,685009,79349,574944,102784,586922,308953,546028,601362,490768,574948,574949,382935,731884,735405,380309,710105,709111,592786,679185,124148,565170,680167,61169,735410,380303,580796,86462,565179,87791,711429,241344,735416,735417,490748,379304,680181,577169,679197,608892,85128,42071,541197,711434,113494,86454,87785,710104,38813,640955,591471,489749,723447,577178,489748,723446,680151,680150,680144,608885,336134";
        String configedEiStr2 = "680146,680148,608881,711466,42062,710131,712794,608889,43388,162438,451160,490728,562926,680152,680156,680158,86477,589153,380311,511303,308911,589156,711450,711451,676946,591413,649328,712724,676940,535314,579769,296491,608863,637348,608868,565130,676950,567792,566460,439476,591401,393674,535323,712713,43365,463104,56621,277311,56624,29076,676960,85166,676961,536648,565146,510030,200849,711417,367391,676927,439494,580779,439493,662317,688901,30047,338748,29048,56630,637364,509047,512689,565151,711420,43345,42015,537963,676931,711403,107600,676930,98499,464410,663636,279955,97161,423902,537955,565168,351702,722173,592826,722171,706925,554553,722176,554547,631168,682743,682744,734164,439436,592829,543888,92298,393591,706931,75736,722162,734173,682755,51773,402127,554535,566550,710187,734178,631171,75747,266696,470292,705615,62434,580856,577203,86407,577208,61104,667128,452412,722182,441756,426149,705621,452409,577216,313537,682730,236820,403439,734156,567823,106247,554591,571087,553256,606168,640907,469280,735458,734127,106256,50480,711470,710140,554579,735460,37400,734133,680138,710143,13453,735464,236845,710145,106222,26739,541255,73128,106224,554572,62478,579823,86449,682767,735436,566520,62481,106232,62482,392288,74467,566526,222225,500737,682773,471589,709181,87769,682774,680115,734111,734110,735448,734117,728312,546096,715002,715001,718990,730623,731971,583040,106296,729637,728309,728308,258220,106295,572385,82897,716322,445258,81563,82895,18306,729663,106266,36181,731941,558053,731943,33908,731947,595035,571075,546075,48160,81595,716351,603992,546076,81590,495502,729656,728327,505549,728326,716344,458581,235576,457257,402104,156505,572334,573665,555830,554500,472847,584316,586975,718954,433254,345971,397112,657736,657737,657734,657735,657738,657739,657741,656412,586987,51733,305067,457234,585652,682717,644450,543835,543830,560379,157854,156525,729626,472829,186373,734180,717642,716318,730601,586993,433276,458535,75714,543824,63731,596314,318356,63732,572368,596316,706993,205745,729613,272520,716308,505595,37469,560391,716303,728397,535232,676868,712801,562857,559206,712808,3516,663548,700811,715085,574834,535228,727055,499285,561531,625268,560203,676875,81718,560209,3523,676881,419153,676882,4855,676880,730698,457433,727089,676845,42104,332726,458727,662240,295254,408462,79432,30120,535205,548532,537882,712811,499267,712814,562889,688841,688840,295265,573530,700843,727070,728354,730673,730672,674245,728352,562810,728357,715044,716376,716374,591381,715042,716370,703066,703067,703064,703065,703062,703063,675587,703061,381709,729671,319744,295202,511292,703068,703069,663575,102686,703075,731986,728377,703073,295217,577093,295218,703074,676888,703071,703072,476592,559228,727049,20755,589081,715064,687535,421436,499225,703070,731993,730687,731994,434744,590059,731998,731999,729692,728361,727030,729694,729693,488566,463289,487239,715057,633731,715053,421448,487242,92428,102668,101336,67494,434751,730676,156485,501857,463258,99896,724873,164963,98563,99893,640824,639846,38941,710231,734201,85256,537806,710236,113380,734207,734209,452592,537820,724863,501852,640839,422710,735542,61299,607447,565065,589011,56709,475244,85240,85242,608779,86571,409711,97259,724894,724893,724892,608763,723559,554367,679047,475217,589047,85274,590016,73299,724888,724885,73290,589030,735520,589033,86597,113350,85262,475228,325597,566338,676823,593959,252147,97271,489850,700854,278779,451224,692080,608743,439596,453880,675503,692069,712833,536534,676830,593940,579665,709204,524554,566351,649209,592647,592648,426298,723524,724854,579677,67409,151627,338868,680069,700877,277427,9715,490811,581982,640883,513898,56757,566368,577024,724841,97282,379499,326882,640897,37571,592706,441863,63866,277496,336311,734285,528140,438227,50555,734288,438221,567752,682628,671982,645693,48216,542434,517472,554421,554413,439548,379392,566436,235630,729700,481067,86527,554402,717724,554408,735599,668333,404895,717706,99825,543743,439561,631078,683947,717714,13597,734277,717711,495697,98529,325614,584281,722250,415536,325618,223666,50591,453805,709273,735570,37533,85212,734249,584295,465803,235640,723572,99842,596263,682674,26883,711599,542485,735548,735549,98547,97215,722270,555784,722274,722275,554445,710292,483691,710291,682646,453820,438206,735555,735559,722265,99862,74585,301639,183,579720,723599,710283,735560,710282,683984,710287,543795,710285,710284,513927,567742,710288,729764,560276,399845,559282,560279,416824,729760,517402,662184,729768,484996,457383,730744,19752,704470,572267,729754,106172,729756,715110,143117,730739,603884,585593,729785,675477,728450,596245,469346,472986,687456,305192,469341,458693,584258,645616,302921,375888,729775,66088,704492,729771,106157,703169,572288,703167,703168,373228,78057,259648,471660,730754,472996,716466,385209,586860,562893,432039,91055,561564,560235,561569,421385,705752,683910,631001,730706,586858,683913,683914,730703,103920,683911,683912,717743,561571,585540,561574,669600,260683,419070,717729,420063,20704,572221,102606,574884,444007,716406,272668,80395,585550,459976,269005,460969,156405,596205,573569,63841,671912,704448,705774,444046,716431,715100,730728,716437,458651,730722,432066,705784,75837,683908,48239,484924,586890,683909,729730,671920,416815,285940,703128,704458,728407,420082,728401,573574,703124,728403,717752,420079,472954,656312,472959,87800,683902,458660,730714,683903,730713,683900,716426,37589,683901,683906,683907,683904,683905,733469,650993,663025,736102,711681,712011,731696,731213,701884,727854,648162,736472,714667,731184,678424,730218,735266,732553,732433,731862,731027,708844,735229,730011,732915,720295,726037,732000,731791,734545,728046,731039,708176,615838,733622,728112,732858,679416,731929,654911,678853,728070,678850,730425,735577,735457,727099,733718,728066,725910,654228,725992,721787,734130,731267,726351,733767,728134,733766,704320,660533,701608,578856,724449,725936,735031,733012,734984,731113,733659,729610,663080,735719,730164,712062,719379,733266,731122,662520,706492,717371,719792,712705,731672,712937,712938,706688,709718,728237,706442,726291,717338,706228,708642,736085,730328,717127,712528,723421,723422,723653,722317,710580,734333,711677,735425,710101,724776,724775,723437,721491,723430,723428,731280,724519,712304,735846,723622,733683,706065,718297,708019,719137,724352,724581,724342,735029,716752,723033,736571,724792,714757,726512,736762,713444,725894,735684,721916,725276,718741,717417,708729,717202,707403,731715,726389,716582,731960,708901,715681,735299,726564,736158,734181,714371,718968,707694,731570,733996,709412,720688,711966,721533,733984,732859,709603,709602,725077,726194,720895,719424,720885,723916,723541,730068,721352,721357,735300,734212,735304,734213,735305,735302,734215,723551,724636,725725,709687,721794,721307,712842,712633,721335,726410,722079,735598,705746,736215,711365,709287,724658,725546,725544,713549,714403,724078,715112,729308,716474,715381,730761,726266,706437,721803,724031,705755,723170,729711,708801,735395,735396,736014,734550,587907,435746,92501,386253,80518,688723,550746,650104,68889,434407,67558,298880,272220,5,196846,44890,79529,93818,399581,17261,724931,81868,489594,422446,3672,80526,525783,712940,158523,674129,452285,477584,298822,43535,548471,716493,637182,729793,562701,626430,687455,286859,32874,488230,325290,32871,686117,184829,602473,104745,637197,66261,728497,626425,562713,488219,3699,728490,550768,646906,398267,464259,29217,649199,562729,464257,370717,626406,478898,592547,478872,591214,49711,97355,723661,554277,709356,242426,338578,711685,491847,710351,85376,606004,711682,454893,477552,13771,339892,439263,709361,13774,476216,735664,513707,195597,652718,709374,464206,338553,196899,42299,104772,554244,723678,721015,709381,609965,452243,711695,593837,592503,184897,103493,593835,525746,709312,700980,639761,709329,439219,711625,102177,266428,392108,724940,580526,439224,495390,513786,93810,724971,722314,56874,81833,80500,664731,581861,712997,415289,710317,709342,280705,439240,195533,581878,724959,454869,712984,368489,327851,79509,98606,670522,37696,59001,98600,47012,50678,325301,74646,48334,721078,61335,394689,313321,314654,382671,483380,470076,75969,103395,542306,266456,705858,61344,581949,429884,339913,85316,729817,254466,85314,729819,60011,439217,683818,470046,62682,644268,644269,544951,35001,59033,482023,579642,632286,656230,555604,140389,717834,177642,445071,543698,48309,379088,84005,710392,657598,470020,265197,86663,585489,543686,74687,482000,596135,380057,734374,237936,620202,351527,73367,416526,705810,501834,496636,581901,472668,639700,84024,513814,38968,403242,265158,73378,721056,668250,501823,37628,415214,728550,461996,472648,497947,662057,730868,674057,497942,650081,199040,247328,286937,506625,715239,50605,343100,461972,572155,658816,47071,506651,727231,461987,596117,417824,634858,597447,568906,333762,189698,535184,63926,47060,351580,584144,517328,671814,704548,705876,729843,460620,728514,532956,657512,364892,532958,74605,603714,210088,729837,62625,447644,716520,48373,727206,152398,74609,152395,460614,11091,729852,62642,573453,91183,248686,50666,563946,319533,422548,664634,525678,550623,423876,423877,640663,550627,398364,158452,676638,711706,157108,297666,361083,709403,709402,592497,689946,488397,397043,435889,625016,538963,710408,690908,476390,16050,81995,490692,423896,271002,336090,259371,562641,91303,625001,260348,53051,81980,272325,538967,512358,398353,30357,451073,674009,285631,285636,559005,523050,423839,715289,308841,536375,536376,535048,286975,563913,550655,689988,105954,689963,487004,578189,490653,368516,477694,308825,549653,524378,464380,715292,489663,272366,604994,104601,550638,435840,242545,96148,243873,266510,709476,554150,68917,438065,683799,325381,514924,105988,581764,580432,13888,13886,593749,242554,608532,607206,710463,735785,142947,733127,439389,592447,531497,435806,81903,230577,423815,195440,283067,476300,196774,36503,735756,103308,451046,291501,349311,271082,723797,683788,56960,580464,514937,710486,381525,102041,200703,393545,502987,500328,502990,442998,133191,567434,439356,477612,500315,466977,723732,296376,81914,466981,379232,723761,721104,414073,451008,581746,79635,710452,53008,9958,568786,103347,102030,25816,66325,392201,441679,625043,708131,502997,80606,722422,580423,722418,454987,452325,452322,609800,477600,538929,404686,596068,596078,142895,338728,470184,646767,414013,153541,290232,584090,705968,721199,74762,253296,19919,717935,454920,705971,405992,668101,593812,97416,28,646796,704647,703314,671717,532856,314732,657455,289265,567536,543509,24483,85421,439325,289276,628900,343286,326707,154843,531595,708189,72131,74792,6030,98761,531590,554229,457184,734485,734488,36449,668111,49746,72148,668114,289207,668115,97424,722483,154838,86773,628920,542223,154863,729902,302717,501711,140211,415324,607115,585387,628936,290216,96116,12462,428645,402035,596037,142881,555525,66299,374303,6073,715362,470101,210148,560042,345891,602307,560048,726009,484741,460795,664590,715352,80597,715351,331253,519806,320580,726030,689886,91273,689876,687216,690863,236799,343211,602304,496711,561399,234132,318274,445163,445165,689896,223481,331231,459790,471422,547033,91299,563981,410488,599933,332542,333880,532830,472727,272437,404639,532837,321883,559075,108155,460759,729957,35187,728621,460752,728624,446425,506547,273757,586623,471400,646751,374367,472709,687263,447796,472703,715340,716674,86724,23187,50774,562684,562683,373028,619164,646763,404612,319564,607187,285708,472714,86715,60102,60101,621479,514893,156148,711835,464069,652536,168125,690802,723812,53147,490350,501583,275989,92737,128900,477378,575808,156154,183776,156137,67794,65137,423530,53151,261339,464086,723802,54483,362046,79766,723806,299969,448809,490347,651225,262654,41135,681081,587808,375384,651230,708216,710513,91422,501589,448830,299982,77126,501593,156161,427088,676549,78487,91449,452039,452037,490311,424806,92773,675228,219850,464038,492960,464041,325052,325054,299925,452025,604897,580362,478679,424819,603548,676525,77170,711819,466664,338359,90140,439098,31777,727397,156114,42414,440068,663237,644078,48624,671699,709594,466647,592300,681020,49959,439032,681014,542055,454657,734565,326338,578011,441333,177908,722557,681023,641808,415072,568697,231510,327676,543372,592325,428392,592324,152639,303693,452017,479936,480923,181174,592312,259098,296085,440032,684994,54421,453339,102349,103679,8716,12666,302338,67742,297335,708224,681061,11307,382520,735851,734526,570967,614293,568645,466616,298677,80708,429676,313003,297351,195797,11319,723845,581617,480907,53113,711873,609707,301025,302319,542069,567329,127687,708242,653844,25920,403096,256845,13947,582952,217296,403080,721213,721214,102390,602290,681043,639559,400800,416381,708251,284067,165908,710564,711893,568717,302412,62896,370467,382443,703406,369469,519777,633347,371786,85520,471165,471169,231599,684938,61580,622665,164588,429648,394416,704765,703431,716750,496,382460,428308,370490,645337,267535,73574,255571,581718,443933,532742,505151,672984,483108,109750,37892,502903,556774,85579,49871,12596,585249,586578,544792,98876,96216,86898,485748,716708,645389,97575,139592,683612,529136,37876,628811,293841,37860,23233,225735,611962,609643,532789,683622,3909,36532,237710,472434,728794,728796,675136,291275,108462,448786,703497,364616,715482,236418,320251,561258,175291,620093,381189,690773,585215,237732,603522,459418,90069,29406,688433,332210,604841,320211,91387,604828,550556,647941,647942,544700,74831,687166,223149,377974,687164,59206,59209,86823,321526,563870,675185,74848,436748,702132,236451,74844,646620,727415,59210,635969,377967,490263,489283,715438,477283,646632,74852,448760,519761,702149,424791,375339,390935,506453,646625,375324,728762,375323,477296,599825,727438,608393,134748,86828,86837,715450,85503,645304,619040,66574,158686,709638,409339,423642,78561,423637,53262,78566,723938,502794,464194,551710,709647,436946,156016,235166,563713,734609,665716,448922,104821,274766,710621,735941,435647,735906,525406,247138,399429,526740,723956,723955,259119,337185,640446,65261,476165,182306,563735,399434,563737,709662,53297,722617,711974,726175,65287,513483,640471,90245,436911,207979,689739,439194,652464,490437,399497,476124,452158,580231,477458,90230,452154,412953,727493,663101,616754,549432,653783,308615,284121,476110,477442,525448,723920,220975,500196,723910,689712,551733,663124,549426,31891,663125,491748,551738,580256,477453,90257,298738,339790,676413,410313,28249,423631,663121,273450,723901,464144,663113,581592,526790,326489,30557,593519,531276,297497,707059,96385,67828,301172,36764,79818,439159,568562,478745,96371,464115,243647,555231,466775,721348,556557,466780,337110,544585,452102,453431,325117,720052,707079,684861,398180,439172,327786,544570,672895,720042,440149,720041,555211,720044,579247,531261,356354,531256,704804,53216,452122,284142,368341,67860,570830,722651,722654,669294,723985,357695,54553,627422,416479,196992,36725,67876,711988,709682,281938,722636,721303,53233,340716,439102,641724,671597,734615,719020,709693,339720,721345,439133,54572,581501,439139,156095,627447,734620,326410,267613,722660,720002,722665,708378,722666,722663,80848,502762,80850,92823,382654,67890,439123,73669,569927,293978,517006,61686,609590,96301,728830,429740,59361,417761,557937,253035,47375,266363,646540,585186,242398,404435,715508,728824,200513,518320,253053,633228,635896,368269,331091,495235,18824,429761,96325,48686,687069,58051,483245,545939,659874,609571,732085,46020,675085,621205,213832,518341,704899,609563,84333,266331,289034,404409,471267,556652,58077,97673,417719,610533,609556,684845,683513,315806,531347,732062,34024,609559,645254,472579,658551,315819,416401,561185,545979,237850,556630,530000,703512,58094,6294,225871,728807,684827,138143,459587,609537,320396,569916,95028,569913,333689,531341,278354,96352,573185,660886,280662,279678,573180,459598,236505,74914,458227,89170,598409,663032,663033,663030,290060,575763,663029,663027,472529,690642,274814,388704,350109,663023,689674,460550,446235,61607,248540,74917,461859,134651,74931,485827,422297,621291,663056,663057,598432,434283,306046,320331,562487,461865,663043,691956,90171,663042,278395,690638,545914,728871,727540,663071,675059,663079,663074,43812,663073,687038,488088,587703,690684,322975,31831,533928,663068,726207,574406,647825,675075,663092,518318,73646,663091,374121,608280,663098,608283,663097,223299,658501,60332,72308,73639,663081,17511,476084,61673,86957,508980,306012,434232,375118,539950,722724,593481,627341,639323,581487,666930,299715,666931,299718,88000,707105,480783,707108,710732,92975,537302,594798,708430,707102,480786,513307,491433,135460,666926,707116,479780,640330,389778,592172,412652,90332,436601,678912,709785,424637,388434,527965,641666,236149,413991,64063,88011,709717,221929,339462,109199,654999,570791,690603,326150,604665,567154,478427,638030,638031,491400,455785,428185,604649,287703,467796,165294,77389,248189,654994,327473,604654,551625,666968,580121,341763,262451,479740,64097,513388,109175,90381,315476,663006,663003,64092,64094,479735,326138,478403,111487,524008,663002,663000,467764,689607,689609,327458,286488,399168,384907,467731,720157,220633,633189,632,35553,83194,428141,372913,339413,581418,684773,721469,375197,633193,159493,375193,159496,733448,593419,570779,543114,555100,533795,67969,442462,707190,442458,704913,54658,683416,232635,184448,428166,672777,384918,217011,155904,316776,602097,629951,52006,708469,539911,430412,416108,708464,396939,220681,455705,417440,657195,287789,159449,80946,569743,733441,642935,158149,630969,472272,497572,357351,684784,104315,629985,399197,7656,92958,384969,158153,734746,404178,615361,514621,303418,722781,273181,684797,77324,484251,569762,105594,586383,703658,155859,597043,357257,533841,242031,647739,561094,646424,104276,71135,702332,608135,609466,635777,608136,609464,85762,46153,715636,716964,72476,461571,521845,382223,611770,384881,533828,645112,485538,570820,485532,520522,282928,332038,417431,333372,622405,497515,394234,486851,574361,423284,573024,659774,474868,657110,334692,556525,83155,267371,195025,96459,462895,254063,671428,719194,703622,97785,599645,230070,498825,84478,732193,100709,405438,266033,459256,507592,435268,405431,586360,462864,334664,332005,531207,556502,382267,254026,633165,266021,716957,545861,54619,609415,545849,544516,646484,462878,36772,200218,519593,684712,657126,435287,435286,54623,536044,561001,726372,292362,508808,549366,435235,459202,437892,524064,486804,435218,89285,563673,513395,512065,727691,678898,112719,652248,461523,449866,491399,491396,31984,100748,713051,30655,73723,128290,713086,549347,279367,551655,347931,307015,180881,435237,73731,691831,536032,549332,489062,238847,728992,564952,477062,155896,412547,424533,200267,726333,533804,59447,237540,477074,423205,30614,437842,714344,533816,97737,726353,338196,388405,575620,423215,43909,71119,477054,71113,702390,450829,59472,635752,251846,412582,709878,453296,477262,489248,390874,503878,592026,389875,564802,709882,721510,594674,424737,299840,400745,76134,722838,174911,78799,90425,492885,88110,39175,424751,285219,592046,593376,190152,665506,581388,642865,642863,641551,526506,719215,146004,683387,248244,61909,604558,604556,604557,41472,537213,678845,678843,617852,190131,491520,387259,326272,537207,642884,582655,604542,479875,641559,28490,652209,412715,207753,480865,76177,61926,654870,273235,582664,88159,399260,479861,678826,708529,219740,190116,710852,340545,479856,90499,480846,654885,480844,709862,594650,76197,526552,7701,285275,568317,480830,556328,78722,53427,326237,66740,479852,66744,441210,197656,467871,719274,34331,479846,10386,479848,479845,405597,36981,495076,480812,314217,197623,34322,158072,53442,545665,399272,244711,595941,719299,732251,546980,732217,732219,344094,148679,708580,570606,105561,734889,670057,708596,583918,719237,52144,147359,603284,629853,442508,733567,66794,472390,431884,104201,106864,569624,431887,615256,556356,76122,442533,719267,89438,496353,304861,382424,544375,88103,571954,64148,531065,707282,582603,582600,642830,256738,587597,472371,405535,634308,610339,484358,83230,714434,624975,434090,83223,498985,702462,105452,303602,106781,416208,72598,34286,612979,486993,702466,97891,676172,405507,676175,690461,647657,106797,646323,71275,430507,430509,612966,609324,634346,635672,599592,714447,472352,533717,447372,71286,460341,633017,556407,557733,486968,104196,460352,519453,101912,575578,633033,702406,214951,321474,544408,405553,586240,333456,533747,53400,485616,586254,645032,404215,645030,486942,34238,254157,587579,701196,424692,85815,498916,29784,411386,434024,434023,90396,410058,691731,61850,490191,689431,58204,690411,678785,100621,58209,665476,263904,709930,307130,225670,60533,266186,587526,513299,88097,486901,459307,73854,394388,725179,61875,334733,42734,60548,28441,474934,653483,101939,434032,587532,155771,689460,97838,211044,96503,113974,702488,353888,727787,60559,365868,507405,448617,412679,58242,61894,665488,665487,61898,308453,713139,155770,621018,701174,609370,73881,58254,489157,450952,477158,437992,477177,624994,726464,97845,563538,691756,727797,167763,611681,437981,515742,413716,339265,720312,605790,491206,734963,733637,40293,288836,556288,442271,627111,526413,364222,40291,706025,683261,109371,706026,654737,544293,666719,719343,720328,642744,605773,89598,734942,544295,654723,708695,287529,720320,720322,442289,493837,238568,709955,514453,708626,391889,526441,639126,604422,40253,480504,525119,339216,330871,104508,377571,525128,454204,456865,710963,493809,641464,481826,666764,388255,708641,526422,627164,526423,515793,678713,654769,58425,250238,513129,641468,641467,342833,472091,105871,432876,533575,533574,533573,169067,706080,703802,329860,615182,673889,305899,673883,467508,719393,65539,299563,558859,683204,582504,683209,78835,104547,472098,582501,82093,287593,329838,105851,104521,615177,105850,66879,570532,533550,406562,407890,442211,658268,533547,388289,684508,53569,304545,91826,304540,6540,432889,600907,600908,702510,388281,53571,718089,157092,498685,305880,658251,557580,104586,431508,574086,532284,684568,104579,498674,586074,405282,245747,718027,498664,91837,629739,105893,558895,598075,598073,358440,104570,718041,556223,487998,684554,629758,232460,244444,371317,373979,664089,71361,688053,701234,727865,82029,497300,497302,691691,487979,154759,550180,688064,726523,727849,648835,714544,435068,533618,691681,70056,510990,103156,727888,725227,419840,534919,357035,475974,475972,587489,269715,293406,45047,95348,691678,550163,689373,256417,725209,714562,534921,54821,546968,611508,518011,22399,634228,727820,474628,357057,702516,82061,244490,661848,267118,435019,487935,463989,231164,310486,449659,491199,491196,69093,506027,507358,624808,435022,546960,648879,661834,447030,522975,598103,450679,507385,474601,321102,65510,463965,727831,546939,491177,727829,491173,321118,323779,558919,370067,395364,463934,436316,666663,652016,652024,438984,437655,252900,413681,72626,255199,401683,491156,526499,310446,491153,382036,491150,491151,424338,382034,642699,679996,100982,411035,540764,480495,564779,563449,463914,623594,652038,189449,423021,513183,708720,424352,549102,58337,491141,436345,451962,680943,268486,725292,622252,563458,135631,58341,335703,525146,30870,60667,610272,475904,411051,240960,454183,564712,725245,264939,29869,451905,593283,83314,280178,425614,666699,238635,480462,691653,412314,636816,610249,124977,713250,442189,382076,610254,57037,280168,85960,414984,480453,588700,70015,509964,726597,58378,424304,507300,340265,340264,525174,97968,341590,725257,537161,665385,680993,424317,612891,493757,251603,478354,453054,99019,339383,437801,631952,489006,541912,593110,146221,707455,99013,425815,249343,326071,569494,249345,581124,275624,340363,603018,477018,732424,557490,47802,706139,706130,38090,504956,556163,60801,197817,605660,340354,733760,311817,719439,514311,158224,390610,706146,390614,264958,99030,225395,327380,568185,225392,493965,605656,453067,112896,557479,706153,707485,466358,628346,667915,455674,261021,595739,212099,87073,679936,708740,707412,39392,339339,416075,628351,58517,642649,455664,680938,375026,654622,541946,503678,526338,326020,400544,679953,274327,582422,478315,667971,87092,667974,41698,467634,514350,528965,480618,569477,581101,708777,453020,640023,330979,391991,720400,503693,251683,539618,582443,539616,455678,479622,453016,6617,479624,363091,481920,184578,534782,46566,468951,51001,481926,715921,104414,104413,647479,615063,65658,184586,388380,520153,534787,233810,731188,375071,481901,481902,521446,53680,51026,569435,91947,6661,646165,399037,702632,582408,297050,357202,533436,472198,91972,671136,631912,706169,420960,444931,684442,34544,660476,604388,731137,63037,207595,359876,528906,52383,721788,51055,304652,604379,158241,304654,544142,416003,544141,404067,46502,431642,105774,286345,369211,720498,35855,660497,534799,88344,405386,91985,104455,533487,219591,5370,460180,504940,706192,444946,498765,384839,257829,303302,498768,733786,87000,498760,725322,419939,486778,281508,690248,269873,69162,142656,69167,712014,104394,587362,71496,127011,282827,522847,635412,460162,700029,82136,498742,231236,510833,587376,346576,104360,418629,726676,712030,636756,497408,420948,724000,724004,4080,549057,534806,333252,714682,383429,713359,498725,713356,587378,268578,423169,70192,701307,435155,498713,647430,589969,369168,419906,498707,498708,508561,46470,498703,180907,685708,590983,689283,405313,534841,726636,701333,731190,419921,244592,77617,731199,661747,715982,715985,77619,715984,648788,405301,267218,47789,713306,691598,590977,64306,701341,447146,574026,17917,546820,549097,459114,541999,691506,59768,691509,59767,551313,501066,678533,680846,623489,725392,677208,15393,708835,725395,708838,642584,461402,400473,165366,491276,491274,655885,708845,292268,538328,394135,225424,448447,201454,540648,424474,575300,575302,282899,365608,423139,721818,366933,365602,678513,609160,424489,449760,610157,1887,491250,39325,72777,726694,165337,691545,27327,700065,378946,537054,82106,713386,712056,412438,725351,335806,689235,153348,41622,551350,713374,680894,424413,665236,712048,712049,112867,377639,58496,466278,610122,41608,712075,83455,712070,424426,525050,237449,16659,436420,378959,708812,423111,339399,481891,691516,712064,200187,326083,713391,177322,712066,596986,706241,721884,571684,631834,60901,303074,102994,327046,59926,582330,340003,481632,732547,630517,481636,405081,378610,630514,339019,99123,684379,493610,391619,683049,58605,87142,733843,241959,556034,683020,99153,58614,683018,683019,683017,544057,253937,454039,493601,380964,516845,289949,47904,99143,606850,544046,252617,281156,544048,560991,240687,481602,237037,265982,84918,142183,58634,630551,605515,706216,113688,111024,667820,732508,60966,721822,402848,683089,420695,618809,526215,277959,380971,642534,431366,707558,708889,392951,527506,331953,631895,40488,442020,683065,83606,101684,667844,303010,642548,584972,431354,304349,667840,667842,708891,59992,707563,378679,736091,57338,684398,654530,99182,402820,667832,647348,405017,377377,406341,686945,533328,88407,88405,662971,159008,405001,90710,532014,662965,635378,686956,662963,299324,274001,76410,52466,533335,277996,276669,545311,486479,334194,662992,731272,713401,686925,111062,658022,686928,186625,111049,616250,647377,662991,616259,445961,662987,533317,604274,361816,635392,47976,137665,556012,521394,686986,684329,287301,672353,497100,560920,487775,51171,77782,77789,731263,106525,499756,546684,290958,616220,475781,475780,617553,533354,372508,686965,686968,385815,546675,475772,373827,557311,533365,558644,207259,75150,558638,558637,87127,732572,156701,424179,700146,575251,373735,424175,636606,82268,714799,82264,232214,295804,105121,701483,4184,487732,666497,636615,424185,689156,726765,575266,575259,82255,625938,361754,390291,726790,677144,385736,600623,725452,463769,587260,463766,463767,246854,463760,724128,612602,724119,713478,508421,348858,678486,348857,587257,421928,406300,233595,702766,575210,558701,245583,412158,52412,690164,106497,463746,510760,46599,475727,713412,689163,726755,701455,726758,65749,725423,677184,437470,662959,662956,269512,408935,21297,701468,726745,701467,424164,712104,690155,662947,69207,96821,244288,401454,654438,383134,383131,577825,707609,311529,412113,611390,539566,340082,463708,707628,655754,481595,708951,493577,502264,610051,577838,383127,269566,653127,166125,526223,721935,271889,680706,437432,564548,707648,706317,679725,324816,41767,57253,9010,481573,58581,666426,526230,481567,623333,479246,38100,725480,677116,227749,594363,700188,481565,551233,82221,414739,481556,481559,156733,102970,295843,653139,581059,341369,239718,581072,541898,708924,481549,539572,609014,493521,83578,665148,526260,708932,678439,395158,270585,270584,613985,69270,649908,526272,732655,454140,137503,365430,720665,719685,582213,479439,365426,731334,276718,631728,706374,558589,75296,481752,717015,717011,684259,661597,414940,102885,101556,533270,316482,720691,315153,405198,595554,733970,136203,240759,389410,630413,58746,480409,717030,479428,546589,34717,252733,87286,352155,58757,654400,667705,667706,237144,293266,443440,168682,718302,719632,584849,718304,327116,672288,560898,595512,654416,388127,584859,631783,83731,717006,733938,595507,595502,721973,402948,582206,718320,719650,545290,630461,71749,587079,64550,617495,65888,702843,726809,89853,545201,88521,674857,673520,305779,432745,717095,661511,175847,89849,389468,361912,419795,690097,571508,456700,714848,388176,689088,417146,233605,76562,89879,485274,508279,87215,89872,690082,263451,690080,286159,729099,726820,174535,497233,686816,87200,346276,705075,534583,731372,684208,674897,99205,559875,618779,461291,418421,334280,673573,660229,305705,714807,137567,322280,717080,334268,558530,417108,685518,587048,731359,405146,729054,218026,618752,486544,432726,486542,461271,418441,373851,95699,724234,552490,725569,424295,726894,463893,725564,700266,724236,678371,577789,690009,68074,81054,649811,641094,245669,724224,358226,538197,726884,726886,197139,509614,81046,713577,16886,44092,540489,724253,587130,724252,724255,335680,653094,68097,589789,173172,677029,552462,680670,552454,32099,521309,616085,65848,186435,576412,577744,522640,576415,81095,451860,713527,563119,601817,65857,474516,637873,726838,691386,691385,308058,650854,647204,565788,689042,478096,523942,724217,347619,565791,713569,463849,424283,589747,726868,700258,449569,90809,535936,690031,69328,642332,480392,478076,438859,105070,60995,491041,412233,552408,577706,540412,39582,96936,540416,707742,503473,72979,706424,95637,706427,642352,354718,625892,402884,540406,279011,84988,720720,611257,450519,83653,239824,323603,528761,106398,705102,679607,642363,71671,424240,279002,451838,502169,526111,451839,630386,57378,83644,667635,371293,68023,156683,479365,324926,595578,57382,102837,27561,680643,481676,707700,339170,679669,414855,654347,666334,481671,481672,264807,725598,480345,82338,666328,280043,667658,691300,83662,678342,678341,679671,565700,624524,539457,595595,481662,129065,41849,267076,438843,678312,679642,539468,395275,438823,197162,340141,255098,481652,511905,81024,595590,327174,81022,526153,526154,380706,481402,729126,596748,241739,685464,468434,731450,569133,456453,86064,33536,58838,418178,433798,455100,46831,241748,596734,684144,685468,644916,57515,190061,329453,545155,58847,660185,570135,367749,717167,674796,468417,717157,632960,705182,730116,100567,329436,330400,632964,124593,469716,296880,226146,71837,624499,433752,379744,391412,706452,226128,379749,720736,98071,569105,433761,317415,705139,720772,503346,419488,685477,329401,611172,70541,69558,38391,557155,719773,486270,584745,70537,730196,32280,238160,421736,76664,559713,276439,450299,75338,289766,378455,726922,419433,635139,726915,636461,89961,509450,174870,419431,287112,264434,726954,700322,701651,726946,276412,635142,535729,44235,408731,474263,498211,726944,726945,390105,87324,89985,45560,276406,731489,62052,74039,98003,576262,462242,661447,87359,336592,88688,705191,731499,474225,488865,559753,576274,576273,50080,462253,50088,173501,88679,21592,589570,105438,499512,264494,499515,263161,588246,673411,207000,576282,288409,726911,463561,607993,731470,105404,475532,731473,450277,714916,662792,661460,534456,521138,263139,99342,98011,58822,74062,701617,559728,685400,534446,324582,607984,88693,105351,463537,155612,247957,425265,451546,601750,81170,104032,588314,700391,247962,235951,53955,736357,452866,452863,197494,601743,540380,13018,106662,539377,106666,155632,449255,637714,526069,509525,678234,76607,589669,463526,296912,464859,463523,463524,725695,575045,576376,275156,360200,52645,678245,680558,324663,564318,425224,637738,439870,173476,234679,439873,565655,348600,104074,245345,104079,65979,337950,726958,258654,76612,439861,712349,336601,185442,385535,439892,234697,451523,508232,414594,700370,130302,44157,185451,726987,62007,105382,551055,552385,88618,712340,509557,691247,711017,712342,565682,644870,625796,565602,455074,632888,706520,679502,582177,37041,440806,679503,294310,294311,481352,481355,719831,527344,467071,69450,398851,245356,625770,684073,717200,82449,655553,452813,439855,582198,539303,668852,341153,564307,566968,539307,397532,611125,438513,566971,415815,37013,569173,612445,679544,282353,130360,514047,540335,583480,481314,53905,155642,253808,514048,296930,612429,330476,469696,735065,402569,313915,431060,540340,37009,541655,540326,679519,268039,570175,469666,82486,431092,526028,53921,457681,343778,268042,53924,642285,467009,65911,402550,469667,65915,65907,331791,68180,655587,625719,679534,471980,431086,495930,432599,378509,560675,632809,69601,573985,469878,481520,331894,468546,705279,685340,620819,468555,494830,685334,83911,684009,34981,533049,58958,239520,729231,596614,379822,383096,98154,45628,98155,445897,684013,69625,100440,32300,101762,729261,5712,57649,282370,732889,482825,595305,227557,468535,57655,71947,731563,70633,238244,572618,731529,558390,597912,558397,685385,505857,632845,684048,57662,354584,55009,473099,238230,70641,469853,719873,317559,660067,251220,111153,530827,69666,67003,331839,625696,732838,56351,720891,706587,685358,521098,684028,717240,473079,112474,49175,533084,729212,705265,239584,366596,732849,684041,504546,380871,83960,516516,418285,473061,186755,573902,252592,263249,252588,51485,637664,473058,535625,87431,620896,473059,473075,473076,448095,63478,63479,99400,576197,347393,522329,277880,637677,701759,210719,305541,74138,389262,299214,524961,700436,712433,620878,651990,86123,548915,288578,712418,147185,50181,725731,420554,50189,701779,473048,473049,510373,535673,62175,288506,701710,75489,648350,461038,75481,462371,58912,419501,436085,475676,546336,347359,473018,475678,390285,98110,701716,717296,234708,436074,106647,564174,473022,620846,588121,450368,87461,606559,662648,390290,535651,462347,99479,463673,420506,421836,510382,461029,473010,576171,521019,74185,663995,290856,263261,660008,473004,290859,564236,679459,723145,424059,724474,463651,679460,576215,655495,576218,736462,712490,679469,81295,679467,679466,52737,679464,76705,679462,94598,449340,105244,400077,463668,27776,311499,463671,246760,736471,680458,438699,679479,679478,679474,221464,462301,372308,27748,488935,553562,576242,651901,412093,476946,540250,450355,105204,436065,679441,540234,679448,724488,220121,424089,142200,553542,258711,400048,39727,438659,413359,463617,397629,725767,463613,40704,449316,439991,552289,52775,712453,691130,396300,649612,649615,617167,449301,724425,464950,88717,52784,197333,475601,475600,300775,700482,106582,371010,62114,62115,299263,425364,679483,680460,438680,712471,637644,577524,476903,680469,44283,679488,679485,565558,75435,481494,62128,481495,724446,481499,651934,438677,309129,566894,625679,707975,479167,706646,373693,336706,82538,101707,282431,49152,38492,439939,396343,481479,705320,403980,479174,731616,582071,626991,427975,706651,426639,271771,505896,439944,467192,582066,49143,516574,38488,244145,482791,401310,685285,492113,383031,82565,414677,600363,503262,439970,414670,717325,705343,480123,268107,706671,398989,481452,612327,452926,631470,656753,493439,383028,719976,439907,594009,439901,679417,479122,571368,193804,570036,480116,467146,142211,493420,530895,396380,494756,456499,539245,601659,668752,156853,679427,516581,427930,540218,481430,540219,656771,569061,542880,679435,582028,679433,719911,455177,481426,541537,216990,594027,270472,540208,571384,168867,397690,439930,155540,481415,439913,643498,642165,414635,679404,38433,241922,256184,469794,735164,654133,720911,736496,735163,168858,679414,679413,503296,495808,114503,729369,585848,32441,444217,471838,584515,269079,731692,70702,457543,139816,432225,456214,498072,68400,573879,98279,332853,295366,57750,585858,284685,470510,730375,215225,731667,728058,19441,68416,597858,728052,704085,571223,45735,332841,499390,471813,55104,675880,499381,70744,471829,731684,585871,241533,498044,499374,625595,81409,706684,487394,612288,227232,380505,332814,499363,114538,113209,420218,421549,731661,717337,717336,730330,422891,561867,559586,101208,81435,335088,718693,487373,69782,183950,241521,368892,612253,381842,380510,498005,138558,499330,98208,577396,98200,3239,474023,450056,651833,62262,736506,138567,475372,552087,552086,488670,421505,437080,76894,649547,239277,512890,74258,449096,700564,552073,74255,552071,663843,662510,324392,475335,499301,421519,421518,87576,252205,662519,242897,366270,311080,561834,464696,560507,662503,662502,724515,548800,540088,606459,425061,728078,87590,451350,651884,239291,74272,15906,311048,437057,86262,324378,87594,651875,462019,651874,729396,391257,700510,701843,392589,701841,700507,701838,31141,87589,589331,588001,15911,87583,591679,240262,510278,649588,99599,649585,312364,391228,674507,312359,57729,565394,101289,564061,728094,509287,32469,675844,510262,425093,662550,522226,393899,701863,181370,700525,700526,139883,452684,539158,679339,539156,682982,452638,680321,38548,175453,541481,540133,463305,642061,682999,490900,591728,589412,591755,566797,374608,208134,425049,64859,313765,553435,92078,540118,75513,591741,576126,76830,679333,210456,441944,164767,552168,481172,13203,151442,439619,399931,108679,325727,540179,92095,74200,426320,40813,87502,711234,691025,439624,106007,495797,288244,107310,617036,619695,617038,724574,247735,426330,374649,468178,539170,86204,107308,566766,401060,439638,565438,481130,87529,636202,374664,529756,404955,682900,717425,403629,208191,685191,482441,706770,81327,403615,342259,398612,516454,626861,229901,685166,705456,503148,495748,656644,730402,716114,730400,38592,234496,295425,343570,221177,685175,284706,331593,504464,505792,439603,456165,233159,644675,539117,343560,79052,386693,539112,571241,420168,26593,655331,457460,283444,342222,68390,626836,602866,403666,401001,539120,332895,282108,503155,359087,283437,457477,471762,584581,420190,38564,706749,483721,246491,80066,385374,554720,38566,67081,76801,81392,457446,601513,602847,403644,162196,92032,330212,79069,614825,356959,433687,183889,202014,56535,97073,103721,547465,70826,704197,381903,573755,332971,97065,649492,103738,470632,662446,701915,687766,103731,509179,730457,730456,455014,420393,716193,81504,329323,320975,498179,304022,687752,536790,82832,523489,612175,156284,731765,434974,731766,67221,561725,226017,55240,422987,393948,445627,498158,101101,612160,673161,132265,499482,499484,406093,79201,79208,4654,48095,102449,730454,67245,730453,728133,238021,560411,215385,353026,505639,559466,406089,403808,318617,3338,381977,156254,731752,241644,731755,367680,624115,644620,366353,572404,393931,534179,573730,534176,81539,717476,717477,717474,717475,81540,717472,717473,227364,600153,499458,600158,433600,590247,511422,474152,617016,463496,637425,381993,725956,701987,499444,365025,475477,607699,711324,85014,407309,101149,61042,380667,590238,365043,617000,590239,449185,474162,277635,43274,103792,264314,500787,590264,724647,445604,700691,618311,650400,102472,137137,692247,241682,618315,114433,73055,463488,277610,608995,101134,307942,151432,475465,712660,499416,711331,679298,87681,391360,323154,307901,464787,462127,69804,701951,74396,407354,74394,579894,663746,353096,580876,499406,70800,674411,589208,592857,674412,701963,277674,663739,687728,378380,324467,590227,651789,463438,524755,335117,374803,590220,437194,335125,488740,102498,85078,451484,97046,524770,475424,541354,211881,190978,75600,516396,92187,92185,682861,631289,555997,709076,540031,210541,348526,452750,402491,463427,592934,275063,734050,711399,734058,452743,475402,325876,324548,75621,62316,734020,37313,452775,681515,438486,451442,402470,591624,452770,87606,438473,402469,451439,438479,591629,542661,452760,503094,63660,667278,99613,235891,287019,425102,580915,711360,724669,679260,709032,592907,8220,438406,108554,324519,722002,313877,258538,493253,723362,325838,722038,385414,637400,710056,61023,509201,680228,711385,39936,542696,107204,722020,73016,73011,524803,439765,39924,679256,710046,257244,670801,56493,685063,633854,79133,359153,56497,456295,601459,270207,595126,685078,82779,716208,645845,271534,398732,730531,731823,731825,728218,481221,481223,729548,471899,728202,343691,503016,717554,456285,542646,706841,432278,67189,79173,515022,572454,420297,301808,403790,717506,67182,494517,67184,560481,397478,515012,38696,92137,415701,731816,503037,531995,81484,93498,515046,643243,542621,329360,471859,503066,158815,355612,471865,682810,328022,272883,682811,76913,561640,499177,730592,499176,434659,547339,67302,662341,82936,662339,472921,546014,410695,458630,636051,728266,434645,68642,590196,534035,460950,445300,600094,663652,547337,433347,499153,560331,676965,572316,472901,661027,574972,369927,79317,585626,522026,586956,586952,433352,231942,70985,434665,676975,572329,66004,536677,597602,54020,728280,217648,548640,435944,730553,70995,92301,70998,590167,44624,447925,422623,410651,475157,716257,730544,476489,730543,518803,397199,591483,423970,510080,558025,80309,203030,490788,547379,321930,547378,230627,410668,381618,728251,573605,728250,614686,535370,559343,463162,357994,379300,68690,4787,475134,67362,523391,645815,499109,728244,728246,435959,102791,435956,181536,573613,534059,534058,489776,295101,459909,560309,590176,476476,419011,283130,100185,148119,680166,639966,355354,379322,627981,550902,476443,423917,382959,312190,524623,411948,525958,423924,513942,664906,253327,283197,608899,692133,554490,313491,411953,649303,73169,525928,554494,423935,711460,476429,700799,640962,490730,476422,279926,711463,710117,410632,435912,463126,554485,724761,577186,525938,411969,26822,724765,402196,476438,325477,679176,490720,566459,97157,464420,724710,500691,30066,664950,56618,70904,589100,85175,338766,522015,452426,326783,692195,692196,18391,579792,700764,489705,97171,651672,537944,452457,565156,565155,439499,711407,565159,650324,537965,648022,392327,98497,439483,636041,477720,393586,351704,504294,288030,415467,657790,679107,92293,723495,655122,580842,494265,706928,75729,74417,667135,301548,541219,580857,313525,86405,682726,580850,682728,342084,342081,403445,670758,392249,639907,577222,157809,314871,516283,280988,379295,656474,253385,735484,452406,565244,99735,248843,681450,571095,61126,682788,494232,37412,606169,655172,567833,63794,292974,724783,692109,722125,724786,682799,627950,199633,38738,290326,326802,292983,106225,330068,680102,50497,471570,735437,512726,428761,495537,606133,469274,106229,541254,61150,496866,495535,595017,596346,458591,343354,517517,731970,717668,717666,457269,495524,36191,632414,596335,633752,314808,730611,472894,48173,729662,534084,727002,534082,374443,210294,614640,614641,92241,716355,80268,472863,49495,731950,19610,92233,416735,48159,445281,330017,505555,305083,597690,387740,458583,716348,345985,234281,345981,669730,398438,373136,670725,421266,460862,729603,472843,531865,528224,80286,555832,273889,505580,603959,518883,598955,573676,614604,21907,386433,586989,584322,459857,246246,258235,156528,632469,573688,63722,332646,63724,704323,63719,583009,730606,197024,50413,718977,217731,458537,499064,706991,259570,717631,472832,631156,361176,50424,75708,727069,727063,457419,598802,562856,524579,143190,295230,663549,79412,471711,662216,590093,590094,471714,421459,535242,68761,67435,318455,499281,590078,32797,3524,688853,590081,574848,574849,434772,511285,499276,499277,700837,127914,574850,724802,2200,676850,499268,727083,586831,676852,524568,613268,420165,524571,446768,524578,79426,727071,590048,730671,675577,499253,499256,727028,662257,688887,422743,319729,229773,565092,254728,730667,283282,20784,216466,499243,393710,727017,499238,730656,156463,66140,92444,727045,239110,728374,650298,590064,590063,562839,79476,590070,336295,179118,81791,102657,80460,92432,675569,727036,536597,675566,633729,101341,44716,590057,728369,590058,590056,167124,549894,31409,550878,100060,98565,554397,667070,500529,463255,577037,463257,73261,680046,265413,499200,554385,200910,724866,680056,640837,278728,86573,61293,42186,98589,592686,381779,590018,464578,501869,709267,26941,722224,253423,337577,627891,380432,86594,324266,542382,85293,692078,676821,511223,391120,408449,724833,489848,500575,621709,565010,381797,452556,537847,566344,688806,325586,108814,68727,97264,724819,381783,513867,477863,452581,265443,239192,393764,81702,709213,723521,724846,438286,679082,438282,710210,326895,43465,325547,650202,625286,391114,465866,326884,75849,48226,200989,62530,722298,554431,252196,162267,337637,554428,338968,632371,734284,48220,414252,402250,566425,542439,403585,464518,87818,606076,380370,554418,439546,99838,669675,670625,705735,98501,26899,26891,655038,26893,452538,567770,301672,223638,13595,404898,565119,531782,501902,74542,705745,495694,211662,717712,529460,554470,387811,50594,683995,470382,416873,416870,627824,106133,723570,415548,261932,723579,276196,554461,363857,493010,655048,415554,679036,711595,513940,543799,98544,567729,483683,501952,643084,402286,610995,176,470368,640809,471693,496991,439531,151581,668375,342160,543786,98532,74586,722267,481014,261918,682652,554433,682656,554435,596283,247631,606017,621636,579719,494327,513924,496987,687480,416827,416825,91016,662185,662182,506757,633620,221053,78049,560285,597548,78032,246362,457393,51810,495633,626506,626504,727122,626502,317170,472989,472987,80385,385210,716470,221076,471652,470322,458698,222389,36296,704498,597566,633653,217806,48278,470334,433368,560236,518777,420054,470307,631000,48265,716413,457343,705761,517439,603835,671942,331449,518765,317169,705768,317166,705764,78076,472977,273990,48255,273996,48254,528111,586861,106187,669631,484934,517465,728417,633678,283308,103906,472942,103905,229832,531720,273983,472941,560260,705783,398548,106197,157729,469309,106196,499180,106194,716421,632361,531731,585557,103911,560272,711306,737175,673513,704980,74516,516354,707493,98047,704668,689661,552443,700932,424889,663784,714831,640301,565294,676154,478562,626557,727058,711703,662495,703667,707421,704478,683096,701719,713495,719433,597079,680255,718660,700945,581855,701636,712686,625816,469171,713499,735748,643242,707954,726558,705779,425166,702024,678790,717121,731192,735476,629866,675123,604766,460337,342120,727076,700135,446010,264943,720167,707685,706035,634218,680394,605244,660641,582884,678564,711771,711772,703297,596023,96194,12505,513677,703293,681116,502970,404516,560192,271081,715167,513769,63985,634905,731566,727860,710743,723381,705831,726895,735491,719202,660276,735374,665600,735493,735130,736465,736107,735932,735259,722041,723370,725799,727977,726400,725307,733086,734851,733051,736320,735235,736687,734267,676173,731312,712815,727199,735912,734949,725213,721888,729806,723827,735486,733740,733864,737070,711919,735451,735697,736306,718677,733953,734923,728580,650946,706332,717452,736671,734010,726718,735463,733048,736317,735228,736879,711811,722152,721976,636798,721977,717001,721732,737177,737056,715061,734460,736642,734586,717921,717126,729211,727278,728008,726737,726738,736097,650052,715179,735324,736777,735406,713617,723663,721482,665451,728316,706763,726803,735770,720707,735532,733593,737037,720708,735896,713620,733116,736502,704595,734206,726365,706416,723096,648203,707064,721232,721593,706410,713294,724858,736077,736991,658478,684535,736873,733241,736511,736871,710104,736512,733568,734657,709932,708047,707510,724533,726709,736280,716350,736044,734540,732366,736966,735998,732006,732007,735515,725291,735758,730084,655535,724520,721497,725730,658491,736971,672660,733465,736611,734515,735603,730152,727543,636924,736261,737112,735059,736148,737113,732222,735853,728067,620056,736829,728747,706054,737001,734097,667535,732559,725145,726598,723764,701605,730608,735272,733651,728091,732687,726340,710437,734748,729731,736924,667687,723871,706197,688000,715695,735286,719496,686852,719252,734871,734753,737108,736019,735602,735963,735721,714488,726575,487388,712037,53395,736173,235910,720190,327286,727067,729639,729638,726389,737183";
        String configedEiStr3 = "705278,732535,726535,707054,701873,642729,723811,662733,656463,719447,731699,729870,724067,704115,708797,685501,718980,717936,679358,734853,619011,716905,725865,724771,726796,701135,708963,684466,712272,705727,732240,712038,717843,730887,732523,711969,619368,720162,702752,671729,597296,684479,734792,734157,633370,717954,711970,675976,731128,596706,663870,626285,702089,684450,717341,637467,718670,634595,730826,634750,690960,651770,642125,716777,732864,679829,707549,711502,622351,651475,707780,723972,724027,723726,684459,684732,733164,627434,713134,682929,646634,440876,596325,724058,724974,726358,658069,734582,676411,678830,734069,730326,732202,597803,724323,635749,597923,736095,642717,663910,712060,655180,400808,716424,637371,718722,736048,552122,711658";
        String configedEiStr4 = "730355,706126,730595,707338,706128,732657,705154,709637,728955,653742,642727,640420,732540,733874,734842,705287,650585,675546,687894,689952,720536,679591,637832,700822,715877,735943,736318,736439,705172,644135,700720,730809,638019,730808,730803,729382,731551,685691,708574,707486,636403,734274,736450,731444,733621,678849,692231,730792,725881,731647,619911,704943,701551,635202,734360,678291,621669,730788,650282,730786,730785,734006,730784,728101,730782,730781,730780,706339,630315,658433,640912,604891,709043,730778,734013,719516,641689,732872,733049,731867,731626,711933,626540,722039,678273,735675,730569,715620,714651,713322,675449,727032,707440,726620,719092,724566,724438,706907,735563,730799,730796,731763,704584,736858,724631,662181,721238,631564,656982,730628,731174,730747,730864,711684,724060,710336,703382,720260,724985,705687,706411,684775,679634,730857,730856,691067,733126,432897,706546,678748,706783,720059,709491,653254,689879,707195,734544,719987,712432,732369,724082,730760,630930,710919,622250,291980,726147,708293,673077,601441,692197,733307,642134,613410,687165,717740,712054,653275,730828,627540,600220,730823,680767,730821,708593,624095,705526,653812,723852,604371,730817,730816,627554,730813,730811,730810,704321,730850,672326,708122,614600,81704,684421,700514,665269,735158,734741,730842,716312,719822,735710,725372,74860,655583,656798,680510,638100,730835,735048,730833,620942";


        List<String> configedEis = Arrays.stream(configedEiStr1.split(",")).collect(Collectors.toList());
        configedEis.addAll(Arrays.stream(configedEiStr2.split(",")).collect(Collectors.toList()));
        configedEis.addAll(Arrays.stream(configedEiStr3.split(",")).collect(Collectors.toSet()));
        configedEis.addAll(Arrays.stream(configedEiStr4.split(",")).collect(Collectors.toSet()));
        Set<String> result = Sets.newHashSetWithExpectedSize(15000);
        Set<String> duplicateEis = Sets.newHashSet();
        configedEis.forEach(x -> {
            if (result.contains(x)) {
                duplicateEis.add(x);
            } else {
                result.add(x);
            }
        });
        System.out.printf("配置重复企业,size=%d,企业：%s%n", duplicateEis.size(), Joiner.on(",").join(duplicateEis));
        return result;
    }

    private Set<String> getCaEnabledEis() {
        return Arrays.stream("737074,737008,737006,736999,736994,736963,736912,736844,736836,736705,736698,736682,736507,736328,736172,735894,735883,735818,735666,735595,735453,735399,735227,734570,734516,734459,734341,734283,734124,734095,733451,732407,732273,732143,732098,731953,731805,731804,731803,731801,731800,731799,731789,731764,731612,731589,731525,731282,731164,731136,731132,731131,731126,730967,730888,730766,730765,730539,730512,730413,730318,730242,730211,730199,730127,730120,730062,730055,729964,729948,729625,729233,729122,729111,728763,728705,728583,728507,728465,728376,727894,727890,727729,727475,727428,727267,727264,727022,726627,726538,726521,726430,726280,726220,726218,726007,725965,725832,725747,725716,725692,725625,725567,725502,725319,725289,725045,725005,724766,724569,724014,723872,723769,723768,723548,723399,723267,723051,723032,722477,722473,722440,722323,722209,722169,722154,722149,721820,721735,721712,721558,721532,721507,721279,720949,720794,720543,720470,720436,720103,720101,720079,720004,720003,719797,719761,719734,719718,719690,719594,719559,719538,719402,719378,719377,719372,719306,719257,719214,719200,719186,718989,718745,718734,718719,718715,718039,717938,717802,717689,717627,717626,717617,717609,717608,717607,717561,717419,717398,717348,717347,717113,716919,716715,716698,716677,716573,716293,716253,716234,716229,716173,716145,716118,715979,715919,715705,715702,715701,715697,715332,715167,715150,714904,714779,714772,714770,714588,714401,714382,713531,713501,713491,713441,713421,713393,713326,713320,713303,713211,713204,713185,713170,712998,712931,712819,712769,712767,712678,712676,712648,712614,712579,712561,712386,712346,712345,712283,712277,712233,712214,712210,712202,712088,711951,711869,711848,711802,711371,711363,711361,711341,711097,711046,710983,710978,710938,710877,710746,710659,710598,710512,710479,710476,710412,710375,710329,710295,710257,710227,710225,710218,710159,710108,709956,709908,709730,709653,709617,709541,709511,709293,709282,709238,709224,709132,709085,709067,709063,708976,708975,708906,708902,708840,708772,708758,708738,708621,708597,708518,708502,708496,708487,708394,708387,708313,708249,708239,708229,708222,708221,708145,708110,708043,708041,708011,707988,707962,707961,707719,707495,707411,707371,707281,707180,707151,707055,707050,707044,707019,706995,706927,706906,706844,706814,706673,706641,706580,706553,706506,706485,706472,706467,706461,706460,706459,706458,706457,706456,706454,706404,706403,706402,706400,706392,706320,706305,706242,706123,706100,706089,706013,705990,705979,705911,705818,705792,705778,705737,705720,705719,705638,705635,705634,705629,705623,705595,705538,705535,705468,705374,705306,705180,705147,705082,705067,705056,704806,704799,704772,704640,704600,704599,704586,704585,704564,704557,704556,704523,704512,704477,704459,704308,704264,704260,704228,704225,704127,704126,704067,704029,703963,703825,703815,703734,703615,703504,703289,703285,702514,702447,701916,701885,701853,701664,701539,701441,701250,701021,701018,700963,700685,700642,700080,692045,692020,691402,691157,690958,690655,690084,690069,689720,689520,688771,688374,688301,688076,687610,687239,687144,687068,684826,684708,684639,684257,684249,684244,684233,684210,684137,684088,683899,683898,683885,683881,683869,683854,683841,683826,683822,683820,683814,683809,683808,683807,683806,683805,683804,683750,683739,683736,683731,683727,683717,683710,683708,683707,683699,683698,683696,683688,683677,683675,683671,683659,683645,683633,683609,683573,683462,683455,683336,683137,682964,682950,682842,682722,681257,680931,680765,680524,680515,680112,679804,679528,679525,679498,679491,679487,679482,679480,679472,679468,679465,679463,679458,679455,679454,679451,679447,679446,679442,679440,679436,679434,679430,679429,679422,679420,679415,679411,679407,679406,679403,679402,679360,679335,679186,678825,677183,676861,676652,676387,675804,675237,675188,675000,673571,673508,672301,672256,671581,671396,671223,670702,670519,669718,668599,667670,666363,666311,665887,665480,664628,664226,664041,663499,663117,663114,663106,663105,663103,663100,663099,663095,663094,663089,663087,663078,663077,663076,663064,663063,663061,663055,663054,663050,663049,663046,663041,663036,663028,663026,663024,663021,663017,663015,663010,663007,663005,663001,662998,662996,662993,662990,662989,662985,662983,662982,662981,662980,662978,662968,662967,662966,662964,662962,662958,662957,662955,662953,662950,662945,662772,662658,662523,662522,662521,662520,662514,662508,662505,659998,658674,658438,657537,656399,656035,656003,655700,655565,655509,655006,654421,653632,653278,652803,652764,652576,652394,651597,650383,649865,649619,648359,647826,647751,647603,647480,647381,647299,647236,647234,647194,646900,646832,646805,646399,646380,646365,646333,646152,645982,645814,645155,644966,644936,644705,644328,644116,644109,643491,643418,642987,642922,642910,642794,642359,642277,642103,641906,641834,641625,641538,641450,641431,641317,641300,641228,641206,640696,640541,640402,640212,640183,639782,639317,639154,638136,638129,637385,637211,636880,636177,636153,635958,635579,635557,635253,635249,635246,635179,634810,634765,634675,634400,634377,634337,634226,634125,634100,633970,633950,633641,633550,633359,633061,632829,632646,632631,632401,632360,632278,632220,631502,631304,631242,631232,631077,631074,630468,630416,630397,629857,629847,629700,629448,629317,629257,629131,628654,628178,628072,627978,627875,627389,627291,627267,627215,627119,626709,625956,625880,625718,625716,625603,625215,625003,624980,624978,624800,624752,624347,623822,623808,623405,622664,622395,622304,622059,621775,621152,621116,620829,620822,620800,620796,620695,620447,620294,620218,620073,619771,619762,619675,619637,619457,619425,619083,618859,618692,618667,618502,618331,618254,618198,618135,618133,617979,617897,617745,617627,617607,617585,617426,617208,617203,617007,616759,616717,616249,616236,616086,616023,616007,615950,615690,615465,615121,614802,614689,614158,614055,614026,613944,613928,613841,613634,613353,611938,611452,610811,610810,610762,610761,610680,610537,610432,610149,610128,610106,610008,609813,609413,609262,609229,608933,608909,608683,608401,608312,608158,608098,608085,607977,607970,607944,607154,607118,606980,606894,606802,606718,605942,605882,605852,605733,605667,605641,605596,605447,605419,604452,604449,604310,603717,603560,603529,603528,603224,602512,602420,602183,602138,602042,601975,601929,601928,601637,601530,601056,601029,601023,600407,600256,600225,600129,600085,599947,599683,599646,599296,598973,598812,597973,597893,597881,597834,597826,597689,597644,597385,597370,597350,597112,596927,596319,596317,596201,596075,595759,595450,595435,595104,595087,594730,594539,594394,594354,594314,594208,594094,594092,594051,594014,593905,593801,593631,593312,593289,593114,593101,593008,592883,592849,592739,592493,592391,592298,592125,592045,592037,592011,591799,591103,591010,590273,590270,590265,590262,590259,590258,590257,590252,590251,590243,590234,590230,590225,590219,590217,590216,590215,590213,590209,590206,590193,590191,590189,590188,590179,590175,590174,590171,590165,590162,590161,590153,590149,590146,590145,590142,590140,590138,590136,590135,590134,590131,590130,590127,590126,590124,590123,590112,590111,590110,590109,590108,590105,590103,590099,590091,590090,590089,590086,590084,590083,590077,590075,590069,590068,590067,590062,590061,590055,590052,590051,590050,590047,590046,590044,590043,590042,590034,590033,590031,590030,590017,590015,590014,590012,590011,590010,590009,590008,590007,590005,590004,590001,590000,589999,589997,589996,589995,589994,589991,589990,589987,589986,589985,589984,589983,589982,589980,589979,589977,589802,589673,589358,589341,589313,589052,588578,588450,588052,587907,587867,587822,587809,587763,587722,587536,587455,587379,587278,587124,587005,586968,586423,586278,586200,586199,585854,585696,585497,585361,585330,585154,585041,585033,584968,584964,584898,584869,584715,584665,584655,584505,584230,584088,583963,583839,583668,583482,583481,583193,583022,582822,582306,581787,581773,581327,581058,581049,581014,580984,580881,580594,580413,580208,579905,578284,578200,578063,577926,577634,577589,577419,577384,577098,576948,576943,576265,575683,575632,575568,575501,575256,575081,574855,574826,574557,574286,574229,574217,574187,574146,574118,573833,573774,573522,573483,573200,572663,572526,571836,570906,570581,570386,570131,569990,569506,569397,568824,568313,567718,567244,566984,566811,566373,565678,565489,565245,564942,564330,564030,563831,563217,562974,562971,562470,561177,561076,559775,559296,558557,558231,558223,557373,557372,557322,556782,556183,554879,554822,552456,552351,552337,552122,551389,550492,549400,548579,548442,548099,547750,547197,546922,546751,546084,545728,545711,545707,545681,544199,543130,543126,542435,541673,540053,539908,539631,539161,538689,538678,538665,538661,538408,538310,538199,537922,537917,537844,537782,537653,537464,537415,537414,537163,536842,536366,536154,535822,535086,534946,534929,534924,534920,534811,534803,534800,534463,534194,534107,533830,533781,533733,533020,532277,529955,529209,527590,527399,525981,525141,523471,523066,522688,522213,520952,520517,518510,517994,517168,517160,516471,515739,515019,514724,511122,510931,510187,509618,509238,507693,506016,505858,505507,505435,505226,505113,505111,505013,504622,504384,503360,503226,503024,502024,501956,501295,499231,495463,495176,491387,491034,490819,489068,488983,488857,486308,484603,482714,482710,477946,477689,477319,475399,474862,473323,472252,470122,468668,465578,464244,460651,459843,456603,452911,452728,452369,452276,449523,444621,443222,442746,440017,439248,439230,430978,430464,425151,424949,424259,421080,414742,412845,410833,405196,404676,403988,392088,391650,390660,390445,389106,387919,387683,387408,386228,381411,381242,381023,379662,374081,359819,354479,353127,342542,342235,330090,326396,322299,319305,318536,307668,307447,304290,301068,282489,270674,262499,259335,257575,254773,242446,237970,222104,220003,209918,163771,162688,158833,156770,156746,156216,156062,155535,154187,153489,148999,148551,144265,143222,132555,131710,127746,125297,114301,112863,111026,106348,104211,103798,102918,102648,102499,102069,101034,100688,100556,100276,100204,99934,99852,97647,97597,92857,91496,91239,89012,88040,87068,85993,85393,85381,84749,84431,84356,83733,82898,81609,81275,80663,80375,79392,79226,78021,78014,78005,77910,77747,77329,77273,77255,76576,76566,76550,76319,75783,75233,75013,73524,73307,72589,71452,71188,70833,69582,66691,66611,66190,65927,64398,64252,63848,63057,62810,62695,62577,62488,62468,61315,60391,60255,59572,58907,57787,57264,56508,53092,52299,52020,51857,51124,50468,49148,49015,48745,48636,45813,42713,42178,39994,39681,39518,37250,36491,35556,29738,28229,26640,26364,18173,17280,17208,16593,11489,7872,7089,6671,6285,5176,2100,268,2".split(",")).collect(Collectors.toSet());
    }

    private Set<String> getOnlyPaymentDescInTenantDbEis() {
        return Arrays.stream("715194,703176,703177,703174,703175,703172,703173,703170,703171,634905,703178,703179,703187,703188,703185,703186,703183,703184,703181,729793,728468,703189,703180,716489,703198,703199,728496,703196,703197,703194,703195,703192,703193,703190,703191,728491,646906,727151,727153,633607,610975,609965,609943,609938,610901,670522,729817,644269,703206,703207,703204,703205,703202,703203,703200,703201,717834,668243,596135,596134,596142,610890,681224,681226,608573,620202,670552,669584,656270,730872,703253,703254,703251,703252,703250,728550,703259,703257,703258,703255,703256,730868,703264,703265,703262,704593,703263,703260,703261,703268,703269,703266,728547,716561,730891,658816,727231,597447,716580,703210,703217,671814,703218,703215,703216,729843,703214,703211,703212,703208,703209,703220,703221,703228,703229,728504,703226,703227,703224,703225,704554,703222,703223,716520,703219,703231,703232,727201,703230,727206,703239,729869,703237,703238,703235,703236,703233,729866,703234,703242,704573,703243,703240,729852,703241,703248,729859,703249,703246,703247,703244,728525,703245,703297,703298,703295,703296,703293,703294,703291,703292,703299,715282,715289,463039,634814,715291,715292,608532,670477,683788,610806,609823,609809,609800,645446,645445,645444,645443,596068,596078,645451,729909,729908,596062,717935,645448,645447,646796,704647,703314,657453,705988,657447,620107,645488,596028,610777,596027,380187,668111,668115,610768,596014,596016,610773,596023,632188,729902,596047,607115,596037,727343,727344,727345,730983,730982,726000,702058,703388,597310,727364,703395,729961,619186,728625,728621,728624,728655,715341,715340,716675,646762,729973,619164,728641,646763,729977,730953,681081,702081,671699,610740,681027,610715,610714,610705,681061,609707,609703,681036,681043,621356,684921,703406,716736,716735,703417,622665,703431,634697,645337,672999,704709,632063,671670,716708,645389,610641,684940,683612,609656,670318,611962,609643,683622,728794,728796,715482,726129,715472,715474,702183,634607,702191,726146,647941,646603,728741,727415,635969,703469,715438,647946,703473,703474,703471,703470,703479,703477,703478,728775,703475,703476,702145,728777,715464,646625,703484,702153,703482,703483,728762,703480,703481,608393,703488,727433,703486,703487,726161,726163,610611,610606,684898,609619,611908,669294,597079,671597,683579,703536,609590,728828,646540,633228,609575,659874,621205,727516,704899,609563,610533,657231,609556,684847,716811,609557,645254,610526,610523,622505,610517,703512,609537,609526,598409,599741,621291,726261,647809,728871,727540,727541,727561,608280,658501,726292,726287,467796,684765,609501,683448,633193,609505,671430,611803,684741,683415,704913,657168,658496,273137,670165,657195,684784,683472,704980,597043,609475,647739,702332,609466,635777,608136,609464,715636,609469,608139,703678,611766,611770,610428,622405,645124,659774,703606,703622,684733,716921,609425,610400,633165,716957,646484,669107,684712,657126,609409,684714,725048,727695,727696,727697,727698,727699,725062,726399,713083,726384,622493,726381,728991,647700,727664,726333,715682,726353,701053,726354,727674,702390,727675,635752,713033,610473,683386,683387,725073,611707,683315,683327,684638,670048,683340,634308,610339,702446,610338,611667,610340,715762,714434,611658,611662,714420,703793,646315,725100,612979,658303,647636,702472,647657,727751,714439,610305,726422,611642,661932,634346,599592,713118,609329,727701,727702,633017,727700,647676,703741,684613,714409,727709,660640,725165,713181,726488,238981,725189,725179,726451,727784,725123,621024,622356,726447,713139,714465,609370,610365,661904,726464,714484,307105,214593,683259,725194,725193,659576,684528,673889,673883,684540,684537,684536,684539,684538,684508,600907,702505,683234,684564,684568,684567,684551,684550,684544,684543,684542,684541,598075,684548,684547,598073,684546,684545,684549,683224,684554,684553,684552,611547,727865,713222,610207,726523,610211,609222,713213,623510,726552,702588,611525,725227,727878,714562,713237,624820,611509,611508,727823,634228,727820,727815,673842,646222,624808,701210,598103,660534,727837,726506,701209,703880,622276,609297,609298,622252,609270,610258,725249,609275,701285,636816,610249,713250,610254,609269,713284,648803,612891,684477,715921,647479,672440,661787,715924,715927,702632,702631,702627,660476,701368,612747,635412,726671,724015,700049,726676,636756,712037,724000,725331,712020,636760,636762,712023,715965,647430,701321,727929,405313,726636,727967,726628,715985,714654,713323,648788,713306,727957,726621,726616,736066,736067,725395,736079,610166,610164,611496,610163,736043,736048,609160,610154,609165,609152,700065,712052,610147,736026,736027,712056,736028,736029,724021,713374,712048,712047,725382,725387,610122,712075,736000,712072,712063,712066,736018,731204,706241,605558,721871,630514,719556,719553,732553,732554,629539,733886,732518,720569,720568,719584,733855,595664,606850,733862,642517,733823,733825,733827,654513,706216,733839,707543,721822,618809,733842,708888,707559,708889,631895,720530,708891,707563,667832,667835,731290,731272,616259,604274,731253,720581,720579,719595,731225,617553,718298,731231,732564,690127,666497,690101,677144,678482,678489,678485,691480,689163,689167,677182,691478,690143,707616,654438,721915,721917,654429,708951,707619,653127,721933,630491,721936,720604,721937,721935,680706,706317,679724,708972,719610,719619,721928,719616,595699,594363,654486,641158,653139,594353,679794,665148,708932,707605,678439,631719,733988,707693,720665,719685,631728,705040,706374,595521,720691,720690,595554,730009,733971,731310,733974,731314,705063,630413,720683,719693,719641,654400,303153,667707,733954,705008,720614,719639,718305,718303,595516,595512,717006,595505,705020,733938,595507,595502,721973,720641,720633,720632,721963,719653,720634,733941,690093,730065,729074,690099,690097,689080,730092,731396,705075,731372,731373,731379,717082,729063,618752,717078,689024,678371,690009,680685,641094,690011,677017,680670,677030,616085,616088,690045,616073,689051,690038,667622,706419,707742,719710,642353,720727,720728,642363,630386,719736,595578,595575,83676,678335,666334,595571,666328,642379,667658,691300,680657,678342,595595,719703,679647,655699,595592,680632,595590,595419,705158,596734,632936,720783,644916,620927,731462,596768,594101,717167,717157,705182,730119,730116,731405,632964,706440,731412,731418,631642,719759,729109,353149,630330,705145,486266,719775,719771,730197,730196,730187,604042,618673,705198,717188,717189,717173,235910,730177,730173,730160,607984,666260,667582,678234,691255,691247,644870,707857,632888,632890,594174,667503,719829,244062,719862,654220,717200,655553,668852,594176,654209,595453,707832,679519,642273,642282,642285,655581,595472,655587,719816,731567,731568,729248,705279,620819,730227,729231,596614,720897,730214,731587,729267,731552,732889,729257,731563,731526,731528,731529,597912,632848,632845,731534,597902,720860,719873,607801,731506,731508,656837,717240,729212,732849,731519,656828,605270,605279,620896,620846,731588,655495,642169,680458,680424,667487,680433,691130,680460,680469,731600,706644,706646,403993,717300,629127,719969,719964,705322,732941,705321,705320,731618,731619,706651,705325,706652,705323,582066,731620,732915,732916,595397,594071,717325,719986,705343,632798,706671,719971,717310,656753,594009,597993,594003,719925,719908,707947,667414,719911,719939,594027,655475,630175,643498,719928,655464,720911,728035,728036,705393,729364,728032,270057,597831,716053,731692,731698,730337,731666,704085,731670,731678,730359,729320,487388,729317,717337,719997,730330,729342,656717,730324,728006,730315,620779,680394,606457,606459,728078,730385,729396,728083,682982,630076,642061,682965,680362,679385,691025,619695,617038,705439,717429,731737,706770,730410,731705,731706,595278,718773,632674,730402,730400,668610,595218,331581,655331,682940,597871,644677,269141,642028,706754,595229,198131,705427,596555,608928,717498,730457,716193,608924,731795,608922,731765,718797,731770,705486,730454,730453,728133,657930,731755,644620,717476,717477,717474,717475,717472,717473,717478,692247,608995,608991,728185,608956,608952,608953,608955,452743,706816,681515,718805,682893,595138,633854,595126,631208,716208,645845,271534,731825,717568,717554,716220,730518,730517,730516,706843,731808,718830,717506,706855,731817,643242,643243,595121,595105,595103,657886,608821,728267,730580,716289,598942,597602,728281,730553,704267,730544,716257,730543,716249,730533,729585,728251,730570,728250,645815,728244,730563,728246,729578,728249,716260,679185,680166,680167,680181,608892,608899,692133,608885,608881,608889,679176,608863,608868,692195,692196,608836,706925,631168,657790,655122,705615,667128,682728,656474,681450,606168,606169,655172,692109,728312,595017,596346,717668,731971,717666,729638,729637,729639,729663,731941,727002,595035,731950,728326,669730,729603,718954,731936,656412,682717,644450,717642,730606,730601,729615,596314,596316,706991,706993,716308,716303,727066,728397,727069,727063,715085,727055,727083,727070,727071,728352,728357,716376,703066,703067,703064,703065,703062,703063,703061,703068,727017,703069,703075,728377,703073,703074,703071,728374,703072,715064,703070,731993,729696,731998,731999,729692,727030,730676,667070,607447,608779,608763,679047,607429,621709,608743,692069,680069,645693,606076,670625,670624,705735,655038,668333,717706,631078,717711,683995,596263,682674,655048,679036,643084,668375,596283,606017,729764,729768,645611,597548,729756,715110,728450,596245,716470,715148,729775,703169,703167,703168,730754,631001,730706,716413,669600,671942,717729,705764,669631,728417,596205,671912,633678,716431,715100,730728,716437,705784,729730,671920,703128,704458,728407,728401,703124,728403,730714,730713,724907,700932,700960,724923,709308,709304,712940,674129,651486,592586,626430,592577,686117,602473,626425,649186,700918,398267,688762,700921,724990,723661,724991,721004,709356,724989,735652,735656,711683,734325,735663,735665,709374,710373,734308,593886,721015,709381,709383,711695,593835,709311,723620,724943,724942,724940,637114,709331,722314,708005,724967,724966,712997,593847,711646,724964,723627,724956,724959,735601,593907,733054,733057,722373,722365,734374,734378,639700,734343,721055,721056,721054,735681,733027,674057,689999,674089,640663,711706,709403,689946,625016,710408,710400,674009,690963,689988,689963,688641,690957,604994,734437,721122,709473,709475,710474,733114,593749,733119,710463,733120,735749,592447,735747,721139,735756,735754,723795,723797,639632,734436,710486,593713,709431,734408,723733,734410,326665,735743,593737,735703,593733,593734,710452,664617,708131,625043,637016,721192,593812,708187,708186,708189,708188,708183,708182,708185,708184,708190,708191,628920,734499,721180,721186,733130,734461,734464,628936,721177,721179,687246,614302,602307,689886,687216,690863,602304,689896,687230,687263,687264,626246,710505,640550,735809,652536,723812,690802,690816,723806,708216,710520,676549,687200,676552,688541,676525,690834,690833,663237,734559,721240,734568,723897,733240,734537,733210,734550,734552,735888,735885,735847,653820,723864,735851,734526,594917,614293,711865,708231,722523,195797,723845,711899,593615,722552,735832,593603,626252,709589,639559,711895,721283,734584,628811,721296,733264,664468,690773,434133,688433,689757,604841,604828,688491,663196,688493,709638,723938,735931,708312,665716,665715,593592,710621,734610,735941,735906,735905,708321,721301,709654,723956,723955,723954,640446,616774,722617,711910,690724,652464,709604,653783,710604,663124,663125,604766,664453,594892,720031,707059,720029,719044,719043,732023,732025,721348,733365,594867,720052,707079,733330,735994,734663,735997,733338,720042,720041,720044,719051,720043,734674,734673,734676,734675,722651,722652,721322,707016,721317,735973,719008,735979,733316,734647,733317,711989,709682,722636,721303,722635,735983,733321,734614,719020,709693,720010,721343,639427,710696,734627,720000,723997,723996,734630,687083,687094,505014,687069,720074,719087,733399,720080,720081,720085,638070,651048,689661,675029,689674,688339,691955,691956,690633,687038,687035,639323,666931,707105,615340,710736,708430,734733,707116,640330,722747,710771,678912,707125,678917,708456,707126,641666,664319,639364,638030,638031,652324,666968,594785,663008,663006,594770,616607,689609,721483,720156,720158,720148,720137,721469,375197,733448,733447,707196,642903,707190,721492,720160,721498,602097,629951,733463,732135,732136,708469,733427,708462,708464,720104,719126,720106,720109,628634,732107,721424,732110,733441,720134,720133,707158,641618,707165,722781,720199,719194,733490,689545,678898,652248,689555,691831,689534,691834,690510,689580,676274,676261,678805,721520,734844,678818,709886,721541,642865,719229,719227,721531,719215,616529,721527,604558,604556,719217,665530,617852,642884,722808,604542,666858,652209,708516,640242,594632,654870,678828,678826,708529,666885,691800,654885,594650,709862,604515,719289,719273,719279,732272,629818,720291,720292,733570,594614,720286,720285,719292,719299,732251,733587,732217,732219,720235,720234,732220,708596,595912,615256,720250,719269,628530,629866,732200,642824,628537,732209,720244,720243,721568,719254,719253,732210,734876,688161,732296,689424,449997,689414,641478,690411,676124,678785,678745,666794,710913,691717,653483,689460,691789,688128,690443,690442,665488,593386,691768,691756,449959,376207,734959,707334,720312,605790,719322,327286,627104,734961,710993,642724,708674,642730,706025,720334,706026,719343,720329,642744,604443,605773,708695,706035,708699,709955,708626,710941,678724,653423,710949,639126,734922,710930,734925,734928,707301,641464,721621,721619,709988,654767,654769,641467,666747,732380,595801,595803,719394,719393,615177,594502,733695,731033,732367,732368,219437,732372,732375,731048,718037,718033,706055,720344,720336,718027,720335,733681,733684,733686,733685,733645,629739,720375,732325,642716,628424,734992,629758,732332,731004,691691,688069,688064,689359,688028,691678,731073,731084,678647,689303,652016,691628,680969,708714,708718,691618,605701,680943,593283,666699,691654,689320,340269,676022,732415,732417,631952,706139,732430,733723,594468,707474,721785,641310,707485,733741,732410,643966,679939,595739,733702,594402,628351,719406,642649,595725,617603,680935,667971,720407,708777,594414,731178,731188,595700,582408,707493,707494,631912,731130,604388,731137,721787,721788,731104,720491,706185,720498,706192,690248,731199,691598,691509,708838,642584,679874,707513,708845,667889,721818,678513,689235,594476,680894,689227,665236,678548,691516,678564,683049,672380,683065,736084,736082,736091,736092,646011,686943,686944,686945,686946,686940,686941,686942,686947,686948,686949,646029,702731,686960,686954,686955,635378,686956,686957,686951,686952,686953,686958,686959,701413,702744,726713,701411,701412,701410,661667,686925,726700,686935,635392,686937,686938,686939,672348,686987,686988,686983,686984,686985,686986,684329,672352,684336,686970,686971,686965,686967,686968,686961,686962,686963,686964,686969,674996,686980,686981,686982,686976,686977,686978,702706,686979,686972,686973,686974,686975,724114,700146,611305,636615,712126,726790,600623,725450,726785,725455,612602,637968,636646,724119,385728,725400,636656,649957,714738,700115,686911,726716,714743,713412,726750,726751,725422,725423,713445,701467,726743,609072,610065,610063,736199,610051,736173,610034,611365,736177,736174,736145,736149,712179,611342,736155,736159,724177,609014,736125,736124,613985,609003,611324,736138,276718,661597,684231,672258,660291,672288,611298,673513,702843,726812,686825,702834,714831,673520,661511,686802,713504,701545,726820,700206,701537,686816,674897,672235,684211,672240,673573,660229,714807,685518,686859,725562,700261,726899,700266,736224,736229,736226,649811,726889,726884,726886,713577,736238,724253,724252,724255,724254,613826,736209,736214,649855,726838,713534,647204,713559,726868,700249,713556,611267,684191,684190,625892,736282,611257,736284,724276,725593,724263,736275,736274,724297,724296,514192,736253,736252,672155,684144,685468,660185,684104,674796,685447,624499,624498,684182,684181,611172,725601,713603,726919,726915,701636,636461,700322,636478,726946,726948,714973,674714,714957,701666,726943,673432,362911,726906,714916,701620,540386,725688,625702,713691,713690,736356,713695,637714,723049,736327,736325,735008,735006,700347,700342,700350,700357,736311,712349,726997,726996,713689,713683,700370,726987,712340,713670,712342,625796,625770,684073,735075,611125,735082,612445,611112,735057,735068,600440,735044,660043,684011,685334,662691,687979,685318,685378,672077,685393,660067,625696,685358,684028,684041,725721,637664,700436,651990,701710,687961,661323,635046,701718,701719,685307,685304,662648,663995,712490,735137,723132,723136,724493,735118,724488,637606,735120,736457,724430,725761,725759,712453,724418,735100,735104,700482,725785,637644,712471,651934,725769,309129,336706,611029,612361,626991,611017,625651,685283,685282,685281,685288,685286,611007,685285,624314,685291,612327,612318,735182,735181,736480,736495,735164,735163,735166,736498,735167,673241,612299,612296,612295,612294,625595,685257,612288,673284,673282,624248,685247,701876,725845,651833,636219,701880,675829,649547,701898,701893,725863,725857,700577,712542,674525,649569,661204,701824,651875,651874,701843,700507,649588,674507,712513,675844,662550,701863,700526,723260,735266,735236,735242,735248,735209,735208,725883,724549,637502,735210,711248,725870,725872,724545,725874,724571,612245,685166,685175,735294,626836,602866,601527,735283,701905,663784,612195,612192,612180,662474,700608,662469,612175,674495,612160,687782,406089,624115,685131,600153,725960,725957,701987,711324,725952,735301,724653,723325,700684,700691,712657,723310,712661,712602,701951,725922,663746,725914,725904,663739,651789,712632,662420,722052,735380,735384,735387,734019,722074,735355,735356,735357,735365,734039,724675,711360,709030,722002,735308,735306,735307,723362,637400,735319,722020,709060,735325,710046,673081,601459,661084,734081,685091,650355,676985,662339,612081,600094,663652,676965,661027,676975,686350,600066,612029,614686,685009,601362,735405,709111,711429,735416,712764,664906,640955,649303,723447,723446,711460,700799,640962,712794,724765,710126,712724,637348,664950,700765,723401,662317,711407,650324,711403,648022,663636,710187,734178,722182,639907,640907,722125,711470,710140,735460,734133,735464,735437,722144,709181,734110,735448,734117,603992,603959,614604,712801,663548,663549,662216,700811,625268,662240,700837,724802,676852,613268,675577,674245,662257,626557,663575,675569,675566,724873,640824,710231,734201,710236,734207,724863,640839,735542,734211,724894,724893,724892,724885,722224,735520,710247,735525,676823,593959,676821,724833,652848,675503,712833,676830,593940,709204,724819,649209,239192,724854,709213,723521,700877,710210,650202,625288,625286,640897,734284,734288,735599,734277,709277,735570,734249,627824,723570,723572,276196,711599,735548,735549,722270,722274,722275,710292,710291,709295,735555,722265,710283,710282,710287,710285,710284,710289,710288,626506,626504,675477,603872".split(",")).collect(Collectors.toSet());
    }

    private Set<String> getOnlyOrderPaymentDescInTenantDbEis() {
        return Arrays.stream("307447,728495,646900,669558,632286,668250,632220,260348,308841,438065,430978,644109,657455,222104,727333,726085,727375,681020,684994,668075,633347,727405,656003,726156,620073,716789,715508,454402,703658,599683,621152,726321,624980,634337,646333,714544,634218,648879,624800,725292,646152,702635,660497,636737,634125,701333,706249,303074,618859,643827,629540,605515,667840,616220,257575,630468,655754,606718,708988,679701,654416,654421,706359,717080,679671,666363,654389,705167,629317,616023,720827,679503,705203,642277,618502,655509,605244,620822,679467,679466,679464,679479,679477,679444,679441,679449,679457,679481,620796,679484,679499,679497,732933,679421,679433,629188,679409,679414,680321,617036,597893,632631,631304,670801,633868,631232,731970,596335,598973,718964,598802,705745,621636,728437,705768,716421,632360,711682,639761,709324,711658,708076,650081,602420,711703,664628,601056,625003,625001,640696,601029,709477,735771,722418,641906,616899,722557,734521,708251,734589,723901,641734,640402,734629,722665,722666,638101,675085,707108,627389,678984,734773,628654,639317,709878,709882,641538,722855,640212,734803,719288,720280,603284,627215,665476,734955,654737,720328,654723,605733,629700,666663,641378,603018,719439,605667,605641,691545,653278,683020,683019,683017,662973,601929,601928,635358,611390,599087,713509,712242,662772,713693,600456,723070,685340,684048,701759,648350,723143,601659,601637,663843,614802,601530,601513,648162,725900,725919,600129,600085,711434,627978,614640,615950,651597,687535,724866,640843,722250".split(",")).collect(Collectors.toSet());
    }

    private Map<String, Integer> getPaymentNumMap(String filePath) throws IOException {
        Path tenantPaymentNumMapPath = Paths.get(filePath);
        String str = new String(Files.readAllBytes(tenantPaymentNumMapPath), StandardCharsets.UTF_8);
        String subStr = str.substring(1, str.length() - 1);

        Map<String, Integer> dataMap = Maps.newHashMapWithExpectedSize(10000);
        for (String s : subStr.split(",")) {
            String[] item = s.split(":");
            String ei = item[0].replaceAll("\"", "");
            String num = item[1];
            if (dataMap.containsKey(ei)) {
                System.out.println("eiPaymentData duplicate=" + ei + ",num=" + num + ",num2=" + dataMap.get(ei));
            } else {
                dataMap.put(ei, Integer.parseInt(num));
            }
        }
        return dataMap;
    }

    private List<String> getTenantIdsByFile(String filePath) throws IOException {
        Path orderPaymentPath = Paths.get(filePath);
        return JsonUtil.fromJson(new String(Files.readAllBytes(orderPaymentPath), StandardCharsets.UTF_8), TypeUtils.parameterize(List.class, String.class));
    }

    @Test
    public void generateI18NTest() {
        String accountCheckRuleJson = "{\"fields\":{\"name\":{\"describe_api_name\":\"ReconciliationPlanObj\",\"is_unique\":true,\"type\":\"text\",\"is_required\":true,\"is_extend\":false,\"is_single\":false,\"is_index\":true,\"is_active\":true,\"create_time\":null,\"label\":\"对账方案名称\",\"is_abstract\":null,\"field_num\":null,\"is_need_convert\":false,\"api_name\":\"name\",\"_id\":null,\"is_index_field\":false,\"max_length\":256,\"help_text\":\"\",\"status\":\"released\",\"define_type\":\"system\"},\"plan_no\":{\"describe_api_name\":\"ReconciliationPlanObj\",\"description\":\"\",\"is_unique\":false,\"type\":\"text\",\"max_length\":128,\"is_required\":false,\"is_extend\":false,\"is_single\":false,\"is_index\":true,\"is_active\":true,\"create_time\":null,\"label\":\"方案编号\",\"is_abstract\":null,\"field_num\":null,\"is_need_convert\":false,\"api_name\":\"plan_no\",\"_id\":null,\"help_text\":\"\",\"status\":\"released\",\"define_type\":\"package\"},\"reconciliation_cycle\":{\"describe_api_name\":\"ReconciliationPlanObj\",\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"default_value\":\"1\",\"label\":\"对账周期\",\"type\":\"select_one\",\"is_required\":false,\"api_name\":\"reconciliation_cycle\",\"options\":[{\"value\":\"1\",\"label\":\"每周\"},{\"value\":\"2\",\"label\":\"每月\"},{\"value\":\"3\",\"label\":\"每季\"}],\"define_type\":\"package\",\"is_single\":false,\"is_index_field\":false,\"help_text\":\"\",\"status\":\"released\"},\"biz_status\":{\"describe_api_name\":\"ReconciliationPlanObj\",\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"default_value\":\"1\",\"label\":\"业务状态\",\"type\":\"select_one\",\"is_required\":true,\"api_name\":\"biz_status\",\"options\":[{\"value\":\"1\",\"label\":\"启用\"},{\"value\":\"2\",\"label\":\"停用\"}],\"define_type\":\"package\",\"is_single\":false,\"is_index_field\":false,\"help_text\":\"\",\"status\":\"released\"},\"generation_method\":{\"describe_api_name\":\"ReconciliationPlanObj\",\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"default_value\":\"1\",\"label\":\"生成方式\",\"type\":\"select_one\",\"is_required\":true,\"api_name\":\"generation_method\",\"options\":[{\"value\":\"1\",\"label\":\"手动\"},{\"value\":\"2\",\"label\":\"自动\"}],\"define_type\":\"package\",\"is_single\":false,\"is_index_field\":false,\"help_text\":\"\",\"status\":\"released\"},\"display_style\":{\"describe_api_name\":\"ReconciliationPlanObj\",\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"auto_adapt_places\":false,\"description\":\"\",\"is_unique\":false,\"default_value\":\"1\",\"label\":\"展示样式\",\"type\":\"select_one\",\"is_required\":true,\"api_name\":\"display_style\",\"options\":[{\"value\":\"1\",\"label\":\"平铺式\"},{\"value\":\"2\",\"label\":\"交叉式\"}],\"define_type\":\"package\",\"is_single\":false,\"is_index_field\":false,\"help_text\":\"\",\"status\":\"released\"},\"reconciliation_data_source\":{\"describe_api_name\":\"ReconciliationPlanObj\",\"description\":\"\",\"is_unique\":false,\"type\":\"long_text\",\"max_length\":10000,\"expression_type\":\"json\",\"is_required\":true,\"is_extend\":false,\"is_single\":false,\"is_index\":true,\"is_active\":true,\"create_time\":null,\"label\":\"对账数据源\",\"is_abstract\":null,\"field_num\":null,\"is_need_convert\":false,\"api_name\":\"reconciliation_data_source\",\"_id\":null,\"help_text\":\"\",\"status\":\"released\",\"define_type\":\"package\"},\"remark\":{\"describe_api_name\":\"ReconciliationPlanObj\",\"default_is_expression\":false,\"description\":\"\",\"is_unique\":false,\"type\":\"long_text\",\"is_required\":false,\"is_extend\":false,\"is_single\":false,\"label_r\":null,\"is_index\":true,\"is_active\":true,\"create_time\":null,\"label\":\"备注\",\"is_abstract\":null,\"field_num\":null,\"action_on_target_delete\":\"set_null\",\"is_need_convert\":false,\"api_name\":\"remark\",\"_id\":null,\"max_length\":1000,\"help_text\":\"\",\"status\":\"released\",\"define_type\":\"package\"},\"_id\":{\"is_index\":false,\"is_active\":true,\"create_time\":1618899501704,\"pattern\":\"\",\"is_unique\":false,\"description\":\"_id\",\"label\":\"_id\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"_id\",\"define_type\":\"system\",\"max_length\":200,\"status\":\"released\"},\"tenant_id\":{\"is_index\":false,\"is_active\":true,\"create_time\":1618899501704,\"pattern\":\"\",\"is_unique\":false,\"description\":\"tenant_id\",\"label\":\"tenant_id\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":true,\"api_name\":\"tenant_id\",\"define_type\":\"system\",\"max_length\":200,\"status\":\"released\"},\"object_describe_api_name\":{\"is_index\":false,\"is_active\":true,\"create_time\":1618899501704,\"pattern\":\"\",\"is_unique\":false,\"description\":\"object_describe_api_name\",\"label\":\"object_describe_api_name\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":true,\"api_name\":\"object_describe_api_name\",\"define_type\":\"system\",\"max_length\":200,\"status\":\"released\"},\"created_by\":{\"type\":\"employee\",\"is_index\":true,\"is_need_convert\":true,\"is_required\":false,\"is_unique\":false,\"is_single\":true,\"api_name\":\"created_by\",\"status\":\"released\",\"label\":\"创建人\",\"is_active\":true,\"create_time\":1623140646331,\"help_text\":\"\",\"define_type\":\"system\"},\"last_modified_by\":{\"type\":\"employee\",\"is_index\":true,\"is_need_convert\":true,\"is_required\":false,\"is_unique\":false,\"is_single\":true,\"api_name\":\"last_modified_by\",\"status\":\"released\",\"is_active\":true,\"label\":\"最后修改人\",\"create_time\":1623140646331,\"help_text\":\"\",\"define_type\":\"system\"},\"create_time\":{\"type\":\"date_time\",\"is_index\":true,\"is_need_convert\":false,\"is_required\":false,\"is_unique\":false,\"time_zone\":\"\",\"date_format\":\"yyyy-MM-dd HH:mm:ss\",\"label\":\"创建时间\",\"api_name\":\"create_time\",\"description\":\"create_time\",\"status\":\"released\",\"create_time\":1623140646331,\"help_text\":\"\",\"define_type\":\"system\"},\"last_modified_time\":{\"type\":\"date_time\",\"is_index\":true,\"is_need_convert\":false,\"is_required\":false,\"is_unique\":false,\"time_zone\":\"\",\"date_format\":\"yyyy-MM-dd HH:mm:ss\",\"label\":\"最后修改时间\",\"api_name\":\"last_modified_time\",\"description\":\"last_modified_time\",\"status\":\"released\",\"create_time\":1623140646331,\"help_text\":\"\",\"define_type\":\"system\"},\"version\":{\"is_index\":false,\"create_time\":1618899501704,\"length\":8,\"is_unique\":false,\"description\":\"version\",\"label\":\"version\",\"type\":\"number\",\"decimal_places\":0,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"version\",\"define_type\":\"system\",\"round_mode\":4,\"status\":\"released\"},\"is_deleted\":{\"type\":\"true_or_false\",\"is_index\":false,\"is_need_convert\":false,\"is_required\":false,\"is_unique\":false,\"label\":\"is_deleted\",\"api_name\":\"is_deleted\",\"description\":\"is_deleted\",\"default_value\":false,\"status\":\"released\",\"index_name\":\"is_del\",\"create_time\":null,\"help_text\":\"\",\"define_type\":\"system\"}},\"actions\":{},\"index_version\":1,\"_id\":null,\"tenant_id\":\"-100\",\"is_udef\":false,\"api_name\":\"ReconciliationPlanObj\",\"created_by\":\"-10000\",\"last_modified_by\":\"-10000\",\"display_name\":\"对账方案\",\"package\":\"CRM\",\"record_type\":null,\"is_active\":true,\"icon_path\":null,\"version\":1,\"release_version\":\"6.4\",\"plural_name\":null,\"define_type\":\"internal\",\"is_deleted\":false,\"config\":{},\"last_modified_time\":null,\"create_time\":null,\"store_table_name\":\"reconciliation_plan\",\"module\":null,\"icon_index\":null,\"description\":\"\",\"visible_scope\":null}";
        generateLanguageInfo(accountCheckRuleJson);
    }

    @Test
    public void generateI18NKeyAndZhCNTest() {
        String json = "{\"fields\":{\"name\":{\"describe_api_name\":\"MerchantProductLinesObj\",\"is_index\":true,\"is_active\":true,\"create_time\":*************,\"prefix\":\"MP{yyyy}{mm}{dd}-\",\"is_unique\":true,\"description\":\"\",\"start_number\":1,\"serial_number\":4,\"default_value\":\"01\",\"label\":\"范围产品编码\",\"type\":\"auto_number\",\"condition\":\"NONE\",\"is_need_convert\":false,\"is_required\":true,\"api_name\":\"name\",\"define_type\":\"system\",\"postfix\":\"\",\"is_single\":false,\"is_extend\":false,\"is_index_field\":false,\"status\":\"released\"},\"merchant_product_range_id\":{\"is_create_when_master_create\":true,\"is_required_when_master_create\":true,\"describe_api_name\":\"MerchantProductLinesObj\",\"is_index\":true,\"is_active\":true,\"create_time\":*************,\"is_unique\":false,\"description\":\"\",\"label\":\"经营范围\",\"show_detail_button\":false,\"target_api_name\":\"MerchantProductRangeObj\",\"type\":\"master_detail\",\"target_related_list_name\":\"target_related_list_merchant_product_master_detail\",\"target_related_list_label\":\"经营范围产品\",\"action_on_target_delete\":\"set_null\",\"is_need_convert\":false,\"is_required\":true,\"api_name\":\"merchant_product_range_id\",\"define_type\":\"package\",\"is_single\":false,\"is_extend\":false,\"is_index_field\":true,\"status\":\"released\"},\"product_id\":{\"describe_api_name\":\"MerchantProductLinesObj\",\"is_index\":true,\"is_active\":true,\"create_time\":*************,\"is_unique\":false,\"description\":\"\",\"label\":\"产品\",\"target_api_name\":\"ProductObj\",\"type\":\"object_reference\",\"target_related_list_name\":\"target_related_list_merchant_product_product\",\"target_related_list_label\":\"经营产品\",\"action_on_target_delete\":\"set_null\",\"is_need_convert\":false,\"is_required\":true,\"api_name\":\"product_id\",\"define_type\":\"package\",\"is_single\":false,\"is_extend\":false,\"is_index_field\":true,\"status\":\"released\"},\"product_status\":{\"describe_api_name\":\"MerchantProductLinesObj\",\"is_index\":false,\"is_active\":true,\"create_time\":*************,\"is_encrypted\":false,\"auto_adapt_places\":false,\"quote_field_type\":\"select_one\",\"is_unique\":false,\"label\":\"产品状态\",\"type\":\"quote\",\"quote_field\":\"product_id__r.product_status\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"product_status\",\"define_type\":\"package\",\"is_single\":false,\"is_extend\":false,\"is_index_field\":false,\"status\":\"released\",\"description\":\"\"},\"record_type\":{\"describe_api_name\":\"MerchantProductLinesObj\",\"is_index\":true,\"is_active\":true,\"create_time\":*************,\"description\":\"record_type\",\"is_unique\":false,\"label\":\"业务类型\",\"type\":\"record_type\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"record_type\",\"options\":[{\"is_active\":true,\"label\":\"默认业务类型\",\"api_name\":\"default__c\"}],\"define_type\":\"package\",\"is_single\":false,\"is_extend\":false,\"is_index_field\":false,\"config\":{},\"status\":\"released\"},\"owner_department\":{\"describe_api_name\":\"MerchantProductLinesObj\",\"default_is_expression\":false,\"is_index\":true,\"is_active\":true,\"create_time\":*************,\"is_encrypted\":false,\"pattern\":\"\",\"is_unique\":false,\"label\":\"负责人所在部门\",\"type\":\"text\",\"default_to_zero\":false,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"owner_department\",\"define_type\":\"package\",\"is_single\":false,\"is_extend\":false,\"is_index_field\":false,\"max_length\":256,\"status\":\"released\"},\"data_own_department\":{\"is_index\":true,\"is_active\":true,\"create_time\":*************,\"is_unique\":false,\"label\":\"归属部门\",\"type\":\"department\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"data_own_department\",\"define_type\":\"package\",\"is_single\":true,\"status\":\"released\"},\"relevant_team\":{\"describe_api_name\":\"MerchantProductLinesObj\",\"embedded_fields\":{\"teamMemberEmployee\":{\"is_index\":true,\"is_need_convert\":true,\"is_required\":false,\"api_name\":\"teamMemberEmployee\",\"is_unique\":false,\"define_type\":\"package\",\"description\":\"成员员工\",\"label\":\"成员员工\",\"type\":\"employee\",\"is_single\":true,\"help_text\":\"成员员工\"},\"teamMemberRole\":{\"is_index\":true,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"teamMemberRole\",\"options\":[{\"value\":\"1\",\"label\":\"负责人\"},{\"value\":\"4\",\"label\":\"普通成员\"}],\"is_unique\":false,\"define_type\":\"package\",\"description\":\"成员角色\",\"label\":\"成员角色\",\"type\":\"select_one\",\"help_text\":\"成员角色\"},\"teamMemberPermissionType\":{\"is_index\":true,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"teamMemberPermissionType\",\"options\":[{\"value\":\"1\",\"label\":\"只读\"},{\"value\":\"2\",\"label\":\"读写\"}],\"is_unique\":false,\"define_type\":\"package\",\"description\":\"成员权限类型\",\"label\":\"成员权限类型\",\"type\":\"select_one\",\"help_text\":\"成员权限类型\"}},\"is_index\":true,\"is_active\":true,\"create_time\":*************,\"is_encrypted\":false,\"is_unique\":false,\"label\":\"相关团队\",\"type\":\"embedded_object_list\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"relevant_team\",\"define_type\":\"package\",\"is_single\":false,\"is_index_field\":false,\"help_text\":\"相关团队\",\"status\":\"new\"},\"owner\":{\"describe_api_name\":\"MerchantProductLinesObj\",\"is_index\":true,\"is_active\":true,\"create_time\":*************,\"is_encrypted\":false,\"is_unique\":false,\"label\":\"负责人\",\"type\":\"employee\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"owner\",\"define_type\":\"package\",\"is_index_field\":false,\"is_single\":true,\"is_extend\":false,\"status\":\"released\"},\"life_status\":{\"describe_api_name\":\"MerchantProductLinesObj\",\"is_index\":true,\"is_active\":true,\"create_time\":*************,\"is_encrypted\":false,\"description\":\"生命状态\",\"is_unique\":false,\"default_value\":\"normal\",\"label\":\"生命状态\",\"type\":\"select_one\",\"is_need_convert\":false,\"is_required\":true,\"api_name\":\"life_status\",\"options\":[{\"value\":\"ineffective\",\"label\":\"未生效\"},{\"value\":\"under_review\",\"label\":\"审核中\"},{\"value\":\"normal\",\"label\":\"正常\"},{\"value\":\"in_change\",\"label\":\"变更中\"},{\"value\":\"invalid\",\"label\":\"作废\"}],\"define_type\":\"package\",\"is_single\":false,\"is_index_field\":false,\"config\":{},\"status\":\"new\"},\"life_status_before_invalid\":{\"describe_api_name\":\"MerchantProductLinesObj\",\"is_index\":false,\"is_active\":true,\"create_time\":*************,\"is_encrypted\":false,\"pattern\":\"\",\"description\":\"作废前生命状态\",\"is_unique\":false,\"label\":\"作废前生命状态\",\"type\":\"text\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"life_status_before_invalid\",\"define_type\":\"package\",\"is_single\":false,\"is_index_field\":false,\"max_length\":256,\"status\":\"new\"},\"lock_status\":{\"describe_api_name\":\"MerchantProductLinesObj\",\"is_index\":true,\"is_active\":true,\"create_time\":*************,\"is_encrypted\":false,\"description\":\"锁定状态\",\"is_unique\":false,\"default_value\":\"0\",\"label\":\"锁定状态\",\"type\":\"select_one\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"lock_status\",\"options\":[{\"value\":\"0\",\"label\":\"未锁定\"},{\"value\":\"1\",\"label\":\"锁定\"}],\"define_type\":\"package\",\"is_single\":false,\"is_index_field\":false,\"config\":{},\"status\":\"new\"},\"lock_rule\":{\"describe_api_name\":\"MerchantProductLinesObj\",\"is_index\":false,\"is_active\":true,\"create_time\":*************,\"is_encrypted\":false,\"description\":\"锁定规则\",\"is_unique\":false,\"rules\":[],\"default_value\":\"default_lock_rule\",\"label\":\"锁定规则\",\"type\":\"lock_rule\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"lock_rule\",\"define_type\":\"package\",\"is_single\":false,\"is_index_field\":false,\"status\":\"new\"},\"lock_user\":{\"describe_api_name\":\"MerchantProductLinesObj\",\"is_index\":false,\"is_active\":true,\"create_time\":*************,\"is_encrypted\":false,\"description\":\"加锁人\",\"is_unique\":false,\"label\":\"加锁人\",\"type\":\"employee\",\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"lock_user\",\"define_type\":\"package\",\"is_index_field\":false,\"is_single\":true,\"status\":\"new\"},\"extend_obj_data_id\":{\"describe_api_name\":\"MerchantProductLinesObj\",\"default_is_expression\":false,\"is_index\":true,\"is_active\":true,\"create_time\":*************,\"is_encrypted\":false,\"pattern\":\"\",\"is_unique\":false,\"label\":\"extend_obj_data_id\",\"type\":\"text\",\"default_to_zero\":false,\"is_need_convert\":false,\"is_required\":false,\"api_name\":\"extend_obj_data_id\",\"define_type\":\"package\",\"is_single\":false,\"is_extend\":false,\"is_index_field\":false,\"max_length\":256,\"status\":\"released\"},\"is_deleted\":{\"type\":\"true_or_false\",\"is_index\":false,\"is_need_convert\":false,\"is_required\":false,\"is_unique\":false,\"label\":\"is_deleted\",\"api_name\":\"is_deleted\",\"description\":\"is_deleted\",\"default_value\":false,\"status\":\"released\",\"create_time\":*************,\"help_text\":\"\",\"define_type\":\"system\"}},\"actions\":{},\"index_version\":1,\"_id\":null,\"tenant_id\":\"-100\",\"is_udef\":false,\"api_name\":\"MerchantProductLinesObj\",\"created_by\":\"-10000\",\"last_modified_by\":\"-10000\",\"display_name\":\"商家产品明细\",\"package\":\"CRM\",\"record_type\":null,\"is_active\":true,\"icon_path\":null,\"version\":1,\"release_version\":\"6.4\",\"plural_name\":null,\"define_type\":\"package\",\"is_deleted\":false,\"config\":{},\"last_modified_time\":null,\"create_time\":*************,\"store_table_name\":\"merchant_product_lines\",\"module\":null,\"icon_index\":108,\"description\":\"\",\"visible_scope\":\"config\"}";
        generateI18nKeyAndZhCNTest(json);
    }

    public void generateI18nKeyAndZhCNTest(String describeJson) {
        IObjectDescribe objectDescribe = new ObjectDescribe();
        objectDescribe.fromJsonString(describeJson);
        String objectApiName = objectDescribe.getApiName();
        List<IFieldDescribe> fieldDescribeList = objectDescribe.getFieldDescribes();
        Set<String> ignoreFields = Sets.newHashSet("_id", "tenant_id", "create_time", "last_modified_time", "created_by", "last_modified_by", "data_own_department", "is_deleted", "life_status", "life_status_before_invalid", "lock_rule", "lock_status", "lock_user", "object_describe_api_name", "order_by", "out_owner", "out_tenant_id", "owner", "owner_department", "package", "version", "relevant_team", "extend_obj_data_id");
        String displayNameKey = GetI18nKeyUtil.getDescribeDisplayNameKey(objectApiName);

        Map<String, String> i18nKeyZhCNMap = Maps.newHashMap();
        i18nKeyZhCNMap.put(displayNameKey, objectDescribe.getDisplayName());

        for (IFieldDescribe fieldDescribe : fieldDescribeList) {
            String fieldApiName = fieldDescribe.getApiName();
            if (ignoreFields.contains(fieldApiName)) {
                continue;
            }

            String fieldKey = GetI18nKeyUtil.getFieldLabelKey(objectApiName, fieldDescribe.getApiName());
            i18nKeyZhCNMap.put(fieldKey, fieldDescribe.getLabel());

            if (fieldDescribe instanceof RecordTypeFieldDescribe) {
                List<IRecordTypeOption> recordTypeOptions = ((RecordTypeFieldDescribe) fieldDescribe).getRecordTypeOptions();
                recordTypeOptions.forEach(r -> {
                    String optionKey = GetI18nKeyUtil.getOptionNameKey(objectApiName, fieldDescribe.getApiName(), r.getApiName());
                    i18nKeyZhCNMap.put(optionKey, r.getLabel());
                });
            } else if (fieldDescribe instanceof SelectOneFieldDescribe) {
                List<ISelectOption> selectOptions = ((SelectOneFieldDescribe) fieldDescribe).getSelectOptions();
                selectOptions.forEach(s -> {
                    String optionKey = GetI18nKeyUtil.getOptionNameKey(objectApiName, fieldDescribe.getApiName(), s.getValue());
                    i18nKeyZhCNMap.put(optionKey, s.getLabel());
                });
            } else if (fieldDescribe instanceof BooleanFieldDescribe) {
                BooleanFieldDescribe booleanFieldDescribe = (BooleanFieldDescribe) fieldDescribe;
                booleanFieldDescribe.getSelectOptions().forEach(s -> {
                    String optionKey = GetI18nKeyUtil.getOptionNameKey(objectApiName, fieldDescribe.getApiName(), s.getValue());
                    i18nKeyZhCNMap.put(optionKey, s.getLabel());
                });
            }
        }

        i18nKeyZhCNMap.forEach((key, value) -> {
            System.out.printf("%s\t%s%n", key, value);
        });

    }


    private Map<String, List<String>> generateLanguageInfo(String describeJson) {
        Map<String, List<String>> key2LanguagesMap = new HashMap<>();
        IObjectDescribe objectDescribe = new ObjectDescribe();
        objectDescribe.fromJsonString(describeJson);
        String objectApiName = objectDescribe.getApiName();
        List<IFieldDescribe> fieldDescribeList = objectDescribe.getFieldDescribes();
        Set<String> ignoreFields = Sets.newHashSet("_id", "tenant_id", "create_time", "last_modified_time", "created_by", "last_modified_by", "data_own_department", "is_deleted", "life_status", "life_status_before_invalid", "lock_rule", "lock_status", "lock_user", "object_describe_api_name", "order_by", "out_owner", "out_tenant_id", "owner", "owner_department", "package", "version", "relevant_team", "extend_obj_data_id");
        String displayNameKey = GetI18nKeyUtil.getDescribeDisplayNameKey(objectApiName);
        add(key2LanguagesMap, displayNameKey, objectDescribe.getDisplayName());

        for (IFieldDescribe fieldDescribe : fieldDescribeList) {
            String fieldApiName = fieldDescribe.getApiName();
            if (ignoreFields.contains(fieldApiName)) {
                continue;
            }

            String fieldKey = GetI18nKeyUtil.getFieldLabelKey(objectApiName, fieldDescribe.getApiName());
            add(key2LanguagesMap, fieldKey, fieldDescribe.getLabel());
            if (fieldDescribe instanceof RecordTypeFieldDescribe) {
                List<IRecordTypeOption> recordTypeOptions = ((RecordTypeFieldDescribe) fieldDescribe).getRecordTypeOptions();
                recordTypeOptions.forEach(r -> {
                    String optionKey = GetI18nKeyUtil.getOptionNameKey(objectApiName, fieldDescribe.getApiName(), r.getApiName());
                    add(key2LanguagesMap, optionKey, r.getLabel());
                });
            } else if (fieldDescribe instanceof SelectOneFieldDescribe) {
                List<ISelectOption> selectOptions = ((SelectOneFieldDescribe) fieldDescribe).getSelectOptions();
                selectOptions.forEach(s -> {
                    String optionKey = GetI18nKeyUtil.getOptionNameKey(objectApiName, fieldDescribe.getApiName(), s.getValue());
                    add(key2LanguagesMap, optionKey, s.getLabel());
                });
            }
        }
        key2LanguagesMap.forEach((k, v) -> {
            System.out.printf("%s\t%s\t%s\t%s\t%s\t%s%n", k, v.get(0), v.get(1), v.get(2), v.get(3), v.get(4));
        });
        System.out.println();
        return key2LanguagesMap;
    }

    private void add(Map<String, List<String>> map, String key, String cn) {
        List<String> languages = map.computeIfAbsent(key, k -> Lists.newArrayList());
        Map<String, String> translateResult = translate(cn);
        try {
            Thread.sleep(1000 * 3);
        } catch (InterruptedException exception) {

        }

        languages.add(cn);
        languages.add(translateResult.getOrDefault("tw", cn));
        languages.add(translateResult.getOrDefault("en", cn));
        languages.add(translateResult.getOrDefault("jp", cn));
        languages.add(translateResult.getOrDefault("vivn", cn));
    }

    private Map<String, String> translate(String cn) {
        String url = "https://oss.firstshare.cn/i18n-console/code/translateByGoogle/";
        Map<String, String> headers = Maps.newHashMap();
        headers.put("content-type", "application/x-www-form-urlencoded; charset=UTF-8");
        headers.put("cookie", "JSESSIONID=B5DF1248D8E5AD3A3389A0EC09481181; experimentation_subject_id=Ijc1ODA1MDY0LThkMWYtNDA1Ny1hYjI0LTEzNmRjYjc4NjhmNiI%3D--7e59eb911ba7636c4ed5c625a2bd517de376d106; OUTFOX_SEARCH_USER_ID_NCOO=1873435421.2351437");
        Map<String, String> result = Maps.newHashMap();
        //tw,en,jp
        try {
            result = HttpUtil.post(url, headers, "zh=" + cn, TypeUtils.parameterize(Map.class, String.class, String.class));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    @Test
    public void testTranslate() {
        System.out.println(IdGenerator.get());
    }

    @Data
    public static class QueryResult {
        private int code;
        private String info;
    }

    @Data
    public static class FieldDescribeResult {
        @SerializedName("field_id")
        private String fieldId;
        @SerializedName("describe_api_name")
        private String describeApiName;
        @SerializedName("api_name")
        private String apiName;
        @SerializedName("status")
        private String status;
        @SerializedName("is_active")
        private Boolean isActive;
    }

    @Data
    public static class CountResult {
        private int count;
    }
}
