package com.facishare.crm.customeraccount;

import com.facishare.crm.common.BaseActionTest;
import com.facishare.crm.customeraccount.constants.CustomerAccountConstants;
import com.facishare.paas.appframework.core.predef.action.StandardAction;
import com.facishare.paas.appframework.core.predef.action.StandardExportAction;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 * Created on 2018/7/26.
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/applicationContext.xml")
public class ExportActionTest extends BaseActionTest {

    public ExportActionTest() {
        super(CustomerAccountConstants.API_NAME);
    }

    static {
        System.setProperty("spring.profiles.active", "ceshi113");
    }

    @Test
    public void testCustomerAccountExportAction() {
        apiName = CustomerAccountConstants.API_NAME;
        StandardExportAction.Arg arg = new StandardExportAction.Arg();
        arg.setObject_describe_api_name(apiName);
        arg.setSearch_template_id("5b55caf1830bdbba6975f5a3");
        arg.setSearch_query_info("{\"limit\":20,\"offset\":0,\"filters\":[]}");

        StandardExportAction.Result result = (StandardExportAction.Result) execute(StandardAction.Export.name(), arg);
        log.info("StandardExportAction.Result->result:{}", result);

        try {
            // 等待生成xls文件的子线程
            Thread.sleep(60000 * 2);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

}
