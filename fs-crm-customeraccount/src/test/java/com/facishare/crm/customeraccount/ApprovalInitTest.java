package com.facishare.crm.customeraccount;

import com.facishare.crm.customeraccount.constants.AccountTransactionFlowConst;
import com.facishare.crmcommon.rest.ApprovalInitProxy;
import com.facishare.crmcommon.rest.dto.ApprovalInitModel;
import com.facishare.crmcommon.rest.dto.ApprovalInstanceModel;
import com.facishare.crmcommon.rest.dto.GetCurInstanceStateModel;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Map;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath*:spring-test/applicationContext.xml")
public class ApprovalInitTest {
    @Autowired
    private ApprovalInitProxy approvalInitProxy;
    @Autowired
    private FunctionPrivilegeService functionPrivilegeService;

    static {
        System.setProperty("spring.profiles.active", "fstest");
    }

    @Test
    public void createFunctionTest(){
        User user = new User("81367","1000");
        functionPrivilegeService.createFuncCode(user, AccountTransactionFlowConst.API_NAME, "CancelEntry", "取消入账");
        System.out.println();
    }

    @Test
    public void initApprovalTest() {
        ApprovalInitModel.Arg arg = new ApprovalInitModel.Arg();
        arg.setEntityId("object_oxmr2__c");
        Map<String, String> headers = Maps.newHashMap();
        headers.put("x-user-id", "1000");
        headers.put("x-tenant-id", "55732");
        ApprovalInitModel.Result result = approvalInitProxy.init(arg, headers);
        System.out.println(result);
    }

    @Test
    public void approvalInstanceTest() {
        String dataId = "5bc44e7ba5083db72f597f53";
        String tenantId = "71590";
        String userId = "1000";
        GetCurInstanceStateModel.Arg arg = new GetCurInstanceStateModel.Arg();
        arg.setObjectIds(Lists.newArrayList(dataId));
        GetCurInstanceStateModel.Result result = approvalInitProxy.getCurInstanceStateByObjectIds(arg, getApprovalInitHeaders(tenantId, userId));
        System.out.println(result);
    }

    @Test
    public void getInstanceDetailTest() {
        String tenantId = "78437";
        String userId = "-10000";

        ApprovalInstanceModel.DetailArg arg = new ApprovalInstanceModel.DetailArg();
        arg.setInstanceId("5ff7ccda8502330001f6059c");
        ApprovalInstanceModel.DetailResult result = approvalInitProxy.getInstanceDetail(arg, getApprovalInitHeaders(tenantId, userId));
        log.info("result={}", result);
    }

    private Map<String, String> getApprovalInitHeaders(String tenantId, String fsUserId) {
        Map<String, String> headers = Maps.newHashMap();
        headers.put("x-user-id", fsUserId);
        headers.put("x-tenant-id", tenantId);
        return headers;
    }
}
