package com.facishare.crm.customeraccount;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.facishare.crm.common.BaseActionTest;
import com.facishare.crm.customeraccount.constants.CreditFileConstants;
import com.facishare.crm.customeraccount.constants.SystemConstants;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.collect.Lists;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath*:spring-test/applicationContext.xml")
public class CreditFileAddActionTest extends BaseActionTest {

    public CreditFileAddActionTest() {
        super(CreditFileConstants.API_NAME);
    }

    static {
        System.setProperty("spring.profiles.active", "ceshi113");
    }

    @Autowired
    protected ServiceFacade serviceFacade;

    @Test
    public void addAction() {
        IObjectData objectData = new ObjectData();
        objectData.set(CreditFileConstants.Field.Customer.apiName, "bb97239244614572b30498601b7475f1");
        objectData.set(CreditFileConstants.Field.CreditType.apiName, "1");
        objectData.set(CreditFileConstants.Field.CreditPeriod.apiName, 11);
        objectData.set(CreditFileConstants.Field.CreditQuota.apiName, 11);
        objectData.set(CreditFileConstants.Field.StartTime.apiName, 1532361600000l);
        objectData.set(CreditFileConstants.Field.EndTime.apiName, 1532448000000l);
        objectData.set(SystemConstants.Field.Owner.apiName, Lists.newArrayList("1000"));
        Object result = executeAdd(objectData);
        System.out.println("addAction=" + result);
    }

    //"{"dataList":[{"object_describe_id":"5b56c81143a3ed6360bf0bde","object_describe_api_name":"CreditFileObj","apiname":"CreditFileObj","_id":"5b57e40f830bdb49a38982d4","dataId":"5b57e40f830bdb49a38982d4","tenant_id":"58438"}]}"
    @Test
    public void bulkInvalid() {
        //        BulkInvalidModel.Arg arg = new BulkInvalidModel.Arg();
        //        BulkInvalidModel.InvalidArg invalidArg = new BulkInvalidModel.InvalidArg();
        //        invalidArg.setObjectDescribeId("5b56c81143a3ed6360bf0bde");
        //        invalidArg.setObjectDescribeApiName(CreditFileConstants.API_NAME);
        //        invalidArg.setId("5b57e40f830bdb49a38982d4");
        //        invalidArg.setLifeStatus("normal");
        //        arg.setDataList(Lists.newArrayList(invalidArg));
        //executeBulkInvalid(arg);
    }

}
