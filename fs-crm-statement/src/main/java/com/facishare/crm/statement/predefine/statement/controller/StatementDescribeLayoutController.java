package com.facishare.crm.statement.predefine.statement.controller;

import com.facishare.crm.statement.util.StatementUtil;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;

import java.util.Objects;

public class StatementDescribeLayoutController extends StandardDescribeLayoutController {

    @Override
    protected void before(StandardDescribeLayoutController.Arg arg) {
        super.before(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        Boolean includeLayout = arg.getInclude_layout();
        if (Objects.nonNull(includeLayout) && includeLayout) {
            StatementUtil.readonlyWhileEdit(arg, result);
            StatementUtil.notShowFieldWhileAdd(arg, result);
        }
        StatementUtil.detailReadonly(arg, result.getDetailObjectList());
        return result;
    }
}
