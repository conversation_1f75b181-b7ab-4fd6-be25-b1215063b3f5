package com.facishare.crm.statement.predefine.reconciliation.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

public class InitReconciliationModel {
    @Data
    public static class Arg {
        @SerializedName("reconciliation_plan_id")
        @JsonProperty("reconciliation_plan_id")
        @JSONField(name = "reconciliation_plan_id")
        private String reconciliationPlanId;
    }

    @Data
    public static class Result {
        @SerializedName("reconciliation_config_status")
        @JsonProperty("reconciliation_config_status")
        @JSONField(name = "reconciliation_config_status")
        private String reconciliationConfigStatus;
    }

}
