package com.facishare.crm.statement.predefine.reconciliation.action;

import com.facishare.crm.statement.constants.ReconciliationPlanConst;
import com.facishare.crm.statement.constants.StateI18NKey;
import com.facishare.crm.statement.enums.ReconciliationPlanInitStatusEnum;
import com.facishare.crm.statement.predefine.reconciliation.dto.ReconciliationDataSourceModel;
import com.facishare.crm.statement.predefine.reconciliation.dto.ReconciliationFieldModel;
import com.facishare.crm.statement.predefine.reconciliation.manager.ReconciliationManager;
import com.facishare.crm.statement.predefine.reconciliation.manager.ReconciliationObjectGenerator;
import com.facishare.crm.statement.predefine.reconciliation.manager.StatementObjectDescribeManager;
import com.facishare.crm.statement.util.ReconciliationUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RLock;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

@Slf4j
public class ReconciliationPlanEditAction extends StandardEditAction {
    private final ReconciliationManager reconciliationManager = SpringUtil.getContext().getBean(ReconciliationManager.class);
    private final StatementObjectDescribeManager statementObjectDescribeManager = SpringUtil.getContext().getBean(StatementObjectDescribeManager.class);
    private RLock lock;
    private List<ReconciliationDataSourceModel> newDataSourceModelList;
    private List<ReconciliationDataSourceModel> dbDataSourceList;

    /**
     * 校验内容：
     * 1. 检查数据源对象是否可用
     * 2. 检查数据源对象的客户字段是否被禁用
     * 3. 检查数据源对象的时间字段是否被禁用
     * 4. 检查原目标对象是否与主对象有查找关联关系
     * 5. 检查数据源个数是否超过5个
     * 6. 检查数据源是否变更，修改对账方案的状态
     */
    @Override
    protected void before(Arg arg) {
        super.before(arg);
        User user = actionContext.getUser();

        lock = reconciliationManager.lockInitReconciliationPlan(user, objectData.getId());
        if (Objects.isNull(lock)) {
            throw new ValidateException(I18N.text(StateI18NKey.RECONCILIATION_PLAN_INIT_NOT_EDIT));
        }
        // 校验打印模板的可用性
        reconciliationManager.validateTemplate(user, objectData);
        // 校验对账方案数据源
        ReconciliationObjectGenerator reconciliationObjectGenerator = ReconciliationObjectGenerator.builder().user(user).reconciliationPlanData(this.objectData).serviceFacade(serviceFacade).reconciliationManager(reconciliationManager).build();
        reconciliationObjectGenerator.validateAndFillObjectInfo();

        // 对账方案数据源发生变更，修改对账方案状态
        IObjectData dbReconciliationPlanData = serviceFacade.findObjectData(user, objectData.getId(), ReconciliationPlanConst.API_NAME);
        dbDataSourceList = ReconciliationUtil.getReconciliationDataSource(dbReconciliationPlanData);
        newDataSourceModelList = ReconciliationUtil.getReconciliationDataSource(this.objectData);
        boolean needUpdateInitStatus = needUpdateInitStatus(dbReconciliationPlanData);
        if (needUpdateInitStatus){
            // 将初始化状态修改为待重新初始化
            objectData.set(ReconciliationPlanConst.Field.InitStatus.apiName, ReconciliationPlanInitStatusEnum.RE_INITIALIZED.value);
        }
        log.info("ReconciliationPlan init status change detail. dataId: {}, from {} update to {}", objectData.getId(), dbReconciliationPlanData.get(ReconciliationPlanConst.Field.InitStatus.apiName, String.class), objectData.get(ReconciliationPlanConst.Field.InitStatus.apiName, String.class));
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);

        String initStatus = objectData.get(ReconciliationPlanConst.Field.InitStatus.apiName, String.class);
        // 包含了 待重新初始化-> 待重新初始化 和 已初始化 -> 待重新初始化 两种场景
        if (ReconciliationPlanInitStatusEnum.RE_INITIALIZED.value.equals(initStatus)){
            // 删掉的'对账明细'，对应的自定义对象删掉，仅限已初始化的对账方案
            deleteDetailObjectDescribe(newDataSourceModelList, dbDataSourceList);
        }

        return result;
    }

    /**
     * 删掉的'对账明细'，对应的自定义对象删掉
     * 因为从对象最多5个，很容易超，所以长会加了这个逻辑
     */
    private void deleteDetailObjectDescribe(List<ReconciliationDataSourceModel> newDataSourceModelList, List<ReconciliationDataSourceModel> dbDataSourceList) {
        if (CollectionUtils.isEmpty(dbDataSourceList)) {
            return;
        }

        List<String> newDestObjectApiNames = newDataSourceModelList.stream().map(ReconciliationDataSourceModel::getDestObjectApiName).collect(Collectors.toList());
        List<String> removedObjectApiNameList = dbDataSourceList.stream().map(ReconciliationDataSourceModel::getDestObjectApiName).filter(destObjectApiName -> !newDestObjectApiNames.contains(destObjectApiName)).collect(Collectors.toList());
        if (removedObjectApiNameList.isEmpty()) {
            return;
        }
        log.info("deleteDetailObjectDescribe tenantId[{}], removedObjectApiNames[{}]", actionContext.getTenantId(), removedObjectApiNameList);

        /**判断是否能够删除的条件
         * 1. 已初始化的对账方案里不包含该源对象
         * 2. 从对象不包含业务数据
         */
        List<String> needDeleteObjectApiNames = reconciliationManager.filterNeedDeleteObjectDescribe(actionContext.getUser(), removedObjectApiNameList);
        log.info("deleteDetailObjectDescribe tenantId[{}], needDeleteObjectApiNames[{}]", actionContext.getTenantId(), needDeleteObjectApiNames);
        statementObjectDescribeManager.disableAndDeleteCustomObjectDescribe(actionContext.getRequestContext(), needDeleteObjectApiNames);
    }

    private boolean needUpdateInitStatus(IObjectData dbReconciliationPlanData) {
        // 获取原来的initStatus
        String initStatus = dbReconciliationPlanData.get(ReconciliationPlanConst.Field.InitStatus.apiName, String.class);
        // 只有已初始化的对账方案才需要修改初始化状态为待重新初始化RE_INITIALIZED
        if (!ReconciliationPlanInitStatusEnum.INITIALIZED.value.equals(initStatus)) {
            return false;
        }

        AtomicBoolean dataSourceUpdated = new AtomicBoolean(false);
        // 比较数据源个数变化
        if (!Objects.equals(newDataSourceModelList.size(), dbDataSourceList.size())) {
            dataSourceUpdated.set(true);
        }
        // 比较数据源字段变化
        newDataSourceModelList.forEach(newSource -> {
            String srcObjectApiName = newSource.getSrcObjectApiName();
            Set<String> newSourceFieldSet = newSource.getReconciliationContent().stream().map(ReconciliationFieldModel::getSrcFieldName).collect(Collectors.toSet());
            Optional<ReconciliationDataSourceModel> sourceModelOptional = dbDataSourceList.stream().filter(dbSource -> dbSource.getSrcObjectApiName().equals(srcObjectApiName)).findFirst();
            if (sourceModelOptional.isPresent()) {
                Set<String> dbSourceFieldSet = sourceModelOptional.get().getReconciliationContent().stream().map(ReconciliationFieldModel::getSrcFieldName).collect(Collectors.toSet());
                // 新老数据源个数不同，或者字段不同都会导致初始化状态更新
                if (newSourceFieldSet.size() != dbSourceFieldSet.size() || newSourceFieldSet.addAll(dbSourceFieldSet)) {
                    dataSourceUpdated.set(true);
                }
            } else {
                dataSourceUpdated.set(true);
            }
        });
        return dataSourceUpdated.get();
    }

    @Override
    protected void finallyDo() {
        super.finallyDo();
        infraServiceFacade.unlock(lock);
    }
}
