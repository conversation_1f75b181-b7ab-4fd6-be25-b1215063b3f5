package com.facishare.crm.statement.predefine.reconciliation.manager;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.statement.constants.ReconciliationPlanConst;
import com.facishare.crm.statement.constants.StateI18NKey;
import com.facishare.crm.statement.constants.TransactionStatementConst;
import com.facishare.crm.statement.enums.BizConfigEnum;
import com.facishare.crm.statement.enums.ReconciliationConfigEnum;
import com.facishare.crm.statement.enums.ReconciliationPlanInitStatusEnum;
import com.facishare.crm.statement.predefine.reconciliation.dto.GetPrintTemplateModel;
import com.facishare.crm.statement.predefine.reconciliation.utils.BizJsonUtil;
import com.facishare.crm.statement.util.ReconciliationUtil;
import com.facishare.crm.statement.util.StatementConfig;
import com.facishare.crmcommon.constants.CommonConstants;
import com.facishare.crmcommon.constants.SystemConstants;
import com.facishare.crmcommon.rest.BpmRestApi;
import com.facishare.crmcommon.rest.TemplateApi;
import com.facishare.crmcommon.rest.dto.InitBpmDefinitionModel;
import com.facishare.crmcommon.rest.dto.PrintTemplateInitResult;
import com.facishare.crmcommon.rest.dto.PrintTemplateModel;
import com.facishare.crmcommon.rest.dto.TemplateServiceModel;
import com.facishare.crmcommon.util.FSRestHeaderUtil;
import com.facishare.crmcommon.util.ObjectDataUtil;
import com.facishare.crmcommon.util.SearchUtil;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NGetFileMetaData;
import com.facishare.fsi.proxy.service.NFileStorageService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.service.ButtonService;
import com.facishare.paas.appframework.core.predef.service.dto.button.CreateButton;
import com.facishare.paas.appframework.core.predef.service.dto.button.FindButtonInfo;
import com.facishare.paas.appframework.core.predef.service.dto.button.FindButtonList;
import com.facishare.paas.appframework.core.predef.service.dto.button.UpdateButton;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeProxy;
import com.facishare.paas.appframework.privilege.dto.AuthContext;
import com.facishare.paas.appframework.privilege.dto.FuncPermiss;
import com.facishare.paas.appframework.privilege.dto.UpdateRoleModifiedFuncPrivilege;
import com.facishare.paas.appframework.privilege.util.ActionCodeConvertUtil;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.util.HeaderUtils;
import com.facishare.rest.core.util.JsonUtil;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.common.data.Page;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.fxiaoke.enterpriserelation2.arg.*;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.result.ListDownstreamEmployeesByDownstreamOuterTenantIdsResult;
import com.fxiaoke.enterpriserelation2.service.AppFuncationPermissionService;
import com.fxiaoke.enterpriserelation2.service.EnterpriseRelationService;
import com.fxiaoke.enterpriserelation2.service.FxiaokeAccountService;
import com.fxiaoke.enterpriserelation2.service.PublicEmployeeService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ReconciliationManager {
    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private InfraServiceFacade infraServiceFacade;
    @Resource
    private TemplateApi templateApi;
    @Resource(name = "enterpriseRelationServiceFactoryBean")
    private EnterpriseRelationService enterpriseRelationService;
    @Resource
    private PublicEmployeeService publicEmployeeService;
    @Resource
    private FxiaokeAccountService fxiaokeAccountService;
    @Autowired
    private NFileStorageService nFileStorageService;
    @Autowired
    private BpmRestApi bpmRestApi;
    @Autowired
    private AppFuncationPermissionService appFuncationPermissionService;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private ButtonService buttonService;
    @Autowired
    private FunctionPrivilegeProxy functionPrivilegeProxy;
    @Autowired
    private PrintTemplateManager printTemplateManager;
    @Autowired
    private MetadataControllerService metadataControllerService;

    public boolean hasInit(String tenantId) {
        User user = User.systemUser(tenantId);
        Map<String, String> configMap = serviceFacade.queryTenantConfigs(user, Lists.newArrayList(BizConfigEnum.Reconciliation.key));
        String reconciliationConfigValue = configMap.getOrDefault(BizConfigEnum.Reconciliation.key, ReconciliationConfigEnum.UN_OPENED.value);
        return Objects.equals(reconciliationConfigValue, ReconciliationConfigEnum.INITIALIZED.value);
    }

    public List<IObjectData> queryReconciliationData(User user) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setSearchSource("db");
        List<IFilter> filters = Lists.newArrayList();
        searchTemplateQuery.setLimit(200);
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setFilters(filters);
        return serviceFacade.findBySearchQuery(user, ReconciliationPlanConst.API_NAME, searchTemplateQuery).getData();
    }

    public void initBpm(User user) {
        InitBpmDefinitionModel.Arg arg = new InitBpmDefinitionModel.Arg();
        arg.setSourceWorkflowId(StatementConfig.TRANSACTION_STATEMENT_SOURCE_WORKFLOW_ID);
        Map<String, String> headers = HeaderUtils.getHeaderMap(Integer.parseInt(user.getTenantId()), Integer.parseInt(user.getUserId()));
        try {
            InitBpmDefinitionModel.Result result = bpmRestApi.initBpmDefinition(arg, headers);
            if (!result.success()) {
                log.warn("init transaction statement bpm error,user:{},arg:{},result:{}", user, arg, result);
            }
        } catch (Exception e) {
            log.warn("init transaction statement bpm error,user:{},arg:{}", user, arg, e);
        }
    }

    public String initPrintTemplate(User user, String objectApiName) {
        // 判断是否已存在模板
        List<TemplateServiceModel.Template> templateList = printTemplateManager.queryPrintTemplateList(user, objectApiName);
        if (CollectionUtils.notEmpty(templateList)){
            return templateList.get(0).getTemplateId();
        }
        Map<String, Object> pathParams = Maps.newHashMap();
        Map<String, Object> queryParams = Maps.newHashMap();
        Map<String, String> headers = FSRestHeaderUtil.getHeader(user);

        pathParams.put("tenantId", user.getTenantId());
        queryParams.put("initDescribeApiNames", objectApiName);

        PrintTemplateInitResult result = templateApi.init(pathParams, queryParams, headers);
        if (!result.success()) {
            log.warn("init print template error,user:{},objectApiName:{},result:{}", user, objectApiName, result);
            throw new ValidateException(result.getMsg());
        }
        List<TemplateServiceModel.Template> defaultTemplateList = printTemplateManager.queryPrintTemplateList(user, objectApiName);
        return defaultTemplateList.get(0).getTemplateId();
    }

    public List<IObjectData> queryReconciliationList(User user, String reconciliationPlanId, String initStatus, String bizStatus) {
        List<IFilter> reconciliationPlanFilterList = new ArrayList<>();
        if (StringUtils.isNotEmpty(reconciliationPlanId)){
            SearchUtil.fillFilterEq(reconciliationPlanFilterList, SystemConstants.Field.Id.apiName, reconciliationPlanId);
        }
        if (StringUtils.isNotEmpty(initStatus)){
            SearchUtil.fillFilterEq(reconciliationPlanFilterList, ReconciliationPlanConst.Field.InitStatus.apiName, initStatus);
        }
        if (StringUtils.isNotEmpty(bizStatus)){
            SearchUtil.fillFilterEq(reconciliationPlanFilterList, ReconciliationPlanConst.Field.BizStatus.apiName, bizStatus);
        }
        SearchTemplateQuery reconciliationPlanQuery = new SearchTemplateQuery();
        reconciliationPlanQuery.setOffset(0);
        reconciliationPlanQuery.setLimit(1000);
        reconciliationPlanQuery.setFilters(reconciliationPlanFilterList);
        reconciliationPlanQuery.setWheres(Lists.newArrayList());
        return serviceFacade.findBySearchQuery(user, ReconciliationPlanConst.API_NAME, reconciliationPlanQuery).getData();
    }

    public void addAppRoleObject(String tenantId, String appId, Set<String> objectApiNames, Set<String> outRoleIds) {
        String ea = serviceFacade.getEAByEI(tenantId);
        HeaderObj headerObj = HeaderObj.newInstance(Integer.parseInt(tenantId));
        InitObjectsPermissionsAndLayoutArg arg = new InitObjectsPermissionsAndLayoutArg();
        arg.setAppId(appId);
        arg.setRoleIds(Lists.newArrayList(outRoleIds));
        arg.setUpstreamEas(Lists.newArrayList(ea));
        List<InitObjectsPermissionsAndLayoutArg.InitObjectInfo> objectInfos = Lists.newArrayList();
        objectApiNames.forEach(objectApiName -> {
            InitObjectsPermissionsAndLayoutArg.InitObjectInfo initObjectInfo = new InitObjectsPermissionsAndLayoutArg.InitObjectInfo();
            initObjectInfo.setObjectApiName(objectApiName);
            List<String> permissions = Lists.newArrayList(objectApiName, objectApiName + "||View");
            if (TransactionStatementConst.API_NAME.equals(objectApiName)) {
                permissions.add(objectApiName + "||" + ObjectAction.CONFIRM_RECONCILIATION.getActionCode());
            }
            initObjectInfo.setPermissions(permissions);
            objectInfos.add(initObjectInfo);
        });
        arg.setObjectInfos(objectInfos);
        try {
            RestResult<Void> result = appFuncationPermissionService.batchAddAppRolesByEa(headerObj, arg);
            if (!result.isSuccess()) {
                log.warn("addAppRoleObject error,tenantId:{},ea:{},arg:{},result:{}", tenantId, ea, arg, result);
            }
        } catch (Exception e) {
            log.warn("addAppRoleObject error,tenantId:{},ea:{},arg:{}", tenantId, ea, arg, e);
        }
    }

    public GetPrintTemplateModel getPrintTemplatePdfPath(User user, String transactionStatementId, String reconciliationPlanId) {
        Map<String, String> headers = FSRestHeaderUtil.getHeader(user);
        // 获取打印模板ID并校验其有效性
        String printTemplateId = getPrintTemplateId(user, reconciliationPlanId);

        PrintTemplateModel.Arg arg = new PrintTemplateModel.Arg();
        arg.setObjectAPIName(TransactionStatementConst.API_NAME);
        arg.setObjectId(transactionStatementId);
        arg.setOrientation("Landscape");
        arg.setTemplateId(printTemplateId);

        PrintTemplateModel.Result result = templateApi.print(arg, headers);
        if (result.success()) {
            return new GetPrintTemplateModel(result.getData().getName(), result.getData().getPath());
        }
        log.warn("renderPdf error,user:{},dataId:{},result:{}", user, transactionStatementId, result);
        throw new ValidateException(result.getMessage());
    }

    public long getNFileSize(String tenantId, String nPath) {
        String ea = serviceFacade.getEAByEI(tenantId);
        NGetFileMetaData.Arg arg = new NGetFileMetaData.Arg();
        arg.setFileName(nPath);
        arg.setEa(ea);
        NGetFileMetaData.Result result = nFileStorageService.nGetFileMetaData(arg, ea);
        return result.getSize();
    }

    public RLock lockInitReconciliationPlan(User user, String reconciliationPlanId) {
        String lockKey = String.format("ReconciliationPlanInit_%s_%s", user.getTenantId(), reconciliationPlanId);
        return infraServiceFacade.tryLock(3, 5, TimeUnit.SECONDS, lockKey);
    }

    public RLock getReconciliationActionLock(User user, String dataId) {
        String lockKey = String.format("TransactionStatementAction_%s_%s", user.getTenantId(), dataId);
        return infraServiceFacade.tryLock(3, 10, TimeUnit.SECONDS, lockKey);
    }

    public List<Long> getOutersByCustomer(User user, String customerId) {
        String tenantId = user.getTenantId();
        String ea = serviceFacade.getEAByEI(tenantId);
        HeaderObj headerObj = HeaderObj.newInstance(Integer.parseInt(user.getTenantId()));

        GetOuterTenantIdByEaArg getOuterTenantIdByEaArg = new GetOuterTenantIdByEaArg();
        getOuterTenantIdByEaArg.setEa(ea);
        RestResult<Long> getOuterTenantIdResult = fxiaokeAccountService.getOuterTenantIdByEa(headerObj, getOuterTenantIdByEaArg);
        if (!getOuterTenantIdResult.isSuccess()) {
            throw new ValidateException(getOuterTenantIdResult.getErrMsg());
        }
        Long upstreamOuterTenantId = getOuterTenantIdResult.getData();

        GetDownstreamOuterTenantIdByMappObjectIdArg arg = new GetDownstreamOuterTenantIdByMappObjectIdArg();
        arg.setMapperObjectId(customerId);
        arg.setUpstreamOuterTenantId(upstreamOuterTenantId);
        RestResult<Long> downstreamOuterTenantIdResult = enterpriseRelationService.getDownstreamOuterTenantIdByMappObjectId(headerObj, arg);
        if (!downstreamOuterTenantIdResult.isSuccess()) {
            throw new ValidateException(downstreamOuterTenantIdResult.getErrMsg());
        }
        Long downstreamOuterTenantId = downstreamOuterTenantIdResult.getData();

        ListPublicEmployeeOuterUidsArg listPublicEmployeeOuterUidsArg = new ListPublicEmployeeOuterUidsArg();
        listPublicEmployeeOuterUidsArg.setUpstreamTenantId(Integer.parseInt(user.getTenantId()));
        listPublicEmployeeOuterUidsArg.setDestOuterTenantId(downstreamOuterTenantId);
        listPublicEmployeeOuterUidsArg.setSourceOuterTenantId(upstreamOuterTenantId);

        ListDownstreamEmployeesByDownstreamOuterTenantIdsArg listDownstreamEmployeesByDownstreamOuterTenantIdsArg = new ListDownstreamEmployeesByDownstreamOuterTenantIdsArg();
        listDownstreamEmployeesByDownstreamOuterTenantIdsArg.setUpstreamEa(ea);
        listDownstreamEmployeesByDownstreamOuterTenantIdsArg.setDownstreamOuterTenantIds(Lists.newArrayList(downstreamOuterTenantId));
        listDownstreamEmployeesByDownstreamOuterTenantIdsArg.setOffset(0);
        listDownstreamEmployeesByDownstreamOuterTenantIdsArg.setLimit(10);
        RestResult<List<ListDownstreamEmployeesByDownstreamOuterTenantIdsResult>> downstreamIdsResult = publicEmployeeService.listDownstreamEmployeesByDownstreamOuterTenantIds(headerObj, listDownstreamEmployeesByDownstreamOuterTenantIdsArg);
        if (!downstreamIdsResult.isSuccess()) {
            throw new ValidateException(downstreamIdsResult.getErrMsg());
        }
        return CollectionUtils.nullToEmpty(downstreamIdsResult.getData()).stream().map(ListDownstreamEmployeesByDownstreamOuterTenantIdsResult::getOuterUid).collect(Collectors.toList());
    }

    public IObjectData getReconciliationPlan(User user) {
        GetEnterpriseDataArg getEnterpriseDataArg = new GetEnterpriseDataArg();
        getEnterpriseDataArg.setEnterpriseId(Integer.parseInt(user.getTenantId()));
        GetEnterpriseDataResult getEnterpriseDataResult = enterpriseEditionService.getEnterpriseData(getEnterpriseDataArg);
        int industry = getEnterpriseDataResult.getEnterpriseData().getIndustry();
        IObjectData objectData = StatementConfig.getReconciliationPlan(industry).orElseGet(() -> {
            String json = ReconciliationUtil.readFileToString("reconciliation_plan/default_reconciliation_plan.json");
            IObjectData defaultObjectData = new ObjectData();
            defaultObjectData.fromJsonString(json);
            return defaultObjectData;
        });
        // 设置多语专表数据，仅处理已开通多语环境的企业
        ObjectDataUtil.setObjectDataLang(user.getTenantId(), objectData, ReconciliationPlanConst.Field.Name.apiName, StateI18NKey.RECONCILIATION_PLAN_MULTI_LANG_NAME);
        objectData.setTenantId(user.getTenantId());
        objectData.setDescribeApiName(ReconciliationPlanConst.API_NAME);
        objectData.setCreatedBy(user.getUserId());
        objectData.setCreateTime(System.currentTimeMillis());
        objectData.setOwner(Lists.newArrayList(user.getUserId()));
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        objectDataExt.setDefaultTeamMember();
        objectDataExt.setDefaultOutOwner2TeamMember();
        return ReconciliationObjectGenerator.builder().user(user).reconciliationPlanData(objectData).serviceFacade(serviceFacade).reconciliationManager(this).build().validateAndFillObjectInfo().getReconciliationPlanData();
    }

    public void initReconciliationPlanInitButton(ServiceContext serviceContext) {
        String objectApiName = ReconciliationPlanConst.API_NAME;
        String buttonApiName = ObjectAction.Init.getButtonApiName();
        // 创建按钮
        createButton(serviceContext, objectApiName, buttonApiName, false);
        List<String> funcCodes = Lists.newArrayList(ActionCodeConvertUtil.convert2FuncCode(ReconciliationPlanConst.API_NAME, ObjectAction.Init.getActionCode()));
        // 给角色分配按钮权限
        assignFunctionToRole(serviceContext.getUser(), getRoleCodes(), funcCodes);
    }

    public void initConfigPrintTemplateButton(ServiceContext serviceContext) {
        String objectApiName = ReconciliationPlanConst.API_NAME;
        String buttonApiName = ObjectAction.CONFIG_PRINT_TEMPLATE.getButtonApiName();
        // 创建按钮
        createButton(serviceContext, objectApiName, buttonApiName, true);
        List<String> funcCodes = Lists.newArrayList(ActionCodeConvertUtil.convert2FuncCode(ReconciliationPlanConst.API_NAME, ObjectAction.CONFIG_PRINT_TEMPLATE.getActionCode()));
        // 给角色分配按钮权限
        assignFunctionToRole(serviceContext.getUser(), getRoleCodes(), funcCodes);
    }

    public void initConfigBPMButton(ServiceContext serviceContext) {
        String objectApiName = ReconciliationPlanConst.API_NAME;
        String buttonApiName = ObjectAction.CONFIG_BPM.getButtonApiName();
        // 创建按钮
        createButton(serviceContext, objectApiName, buttonApiName, true);
        List<String> funcCodes = Lists.newArrayList(ActionCodeConvertUtil.convert2FuncCode(ReconciliationPlanConst.API_NAME, ObjectAction.CONFIG_BPM.getActionCode()));
        // 给角色分配按钮权限
        assignFunctionToRole(serviceContext.getUser(), getRoleCodes(), funcCodes);
    }

    /**
     * 按钮位置，增加'列表页单条操作'
     */
    public void usePagesAddList(ServiceContext serviceContext, String describeApiName, String buttonApiName) {
        FindButtonInfo.Arg arg = new FindButtonInfo.Arg();
        arg.setDescribeApiName(describeApiName);
        arg.setButtonApiName(buttonApiName);

        FindButtonInfo.Result buttonResult;
        try {
            buttonResult = buttonService.findButtonInfo(arg, serviceContext);
        } catch (Exception e) {
            log.warn("buttonService.findButtonInfo, arg[{}], context[{}]", arg, serviceContext, e);
            throw e;
        }
        ButtonDocument buttonDoc = buttonResult.getButton();
        List<String> usePages = (List<String>) buttonDoc.get("use_pages");
        if (usePages.contains("list")) {
            return;
        }

        usePages.add("list");
        buttonDoc.put("use_pages", usePages);

        UpdateButton.Arg updateArg = new UpdateButton.Arg();
        updateArg.setButton(JSONObject.toJSONString(buttonDoc));
        updateArg.setPost_actions(buttonResult.getPost_actions());
        updateArg.setRoles(getRoleCodes());

        try {
            buttonService.update(updateArg, serviceContext);
        } catch (Exception e) {
            log.warn("buttonService.update, arg[{}], context[{}]", updateArg, serviceContext, e);
            throw e;
        }
    }

    private void createButton(ServiceContext serviceContext, String objectApiName, String buttonApiName, boolean isUIButton){
        String buttonJson = BizJsonUtil.getJson(String.format("button/%s_%s_button.json", objectApiName, buttonApiName));
        CreateButton.Arg buttonArg = JsonUtil.fromJson(buttonJson, CreateButton.Arg.class);
        FindButtonList.Arg findButtonArg = new FindButtonList.Arg();
        if (!isUIButton){
            findButtonArg.setExcludeRedirectType(true);
        }
        findButtonArg.setDescribeApiName(objectApiName);
        FindButtonList.Result findButtonResult = buttonService.findButtonList(findButtonArg, serviceContext);
        List<ButtonDocument> buttonDocuments = findButtonResult.getButtonList();
        Map<String, ButtonDocument> buttonDocumentMap = Objects.isNull(buttonDocuments) ? Maps.newHashMap() : buttonDocuments.stream().collect(Collectors.toMap(x -> String.valueOf(x.get("api_name")), Function.identity()));

        if (!buttonDocumentMap.containsKey(buttonApiName)) {
            try {
                CreateButton.Result result = buttonService.create(buttonArg, serviceContext);
                if (!result.isSuccess()) {
                    log.warn("buttonService.create arg[{}], serviceContext[{}], result[{}]", buttonArg, serviceContext, result);
                    throw new ValidateException(I18N.text(StateI18NKey.CREATE_BUTTON_FAIL));
                } else {
                    log.info("buttonService.create arg[{}], serviceContext[{}], result[{}]", buttonArg, serviceContext, result);
                }
            } catch (Exception e) {
                log.warn("buttonService.create arg[{}], serviceContext[{}]", buttonArg, serviceContext, e);
                throw new ValidateException(buttonApiName + e.getMessage());
            }
        }
    }

    public void assignFunctionToRole(User user, List<String> roleCodes, List<String> functionCodes) {
        if (CollectionUtils.empty(roleCodes) || CollectionUtils.empty(functionCodes)){
            return;
        }
        String tenantId = user.getTenantId();
        AuthContext authContext = AuthContext.builder().tenantId(tenantId).userId(user.getUserId()).appId("CRM").build();
        for (String roleCode : roleCodes){
            FuncPermiss.RoleFuncPermissArg roleFuncPermissArg = new FuncPermiss.RoleFuncPermissArg(authContext, roleCode);
            Map<String, String> headers = FunctionPrivilegeProxy.HeaderUtil.buildHeaders(tenantId);
            FuncPermiss.Result funcPermissionResult = functionPrivilegeProxy.roleFuncPermiss(roleFuncPermissArg, headers);
            if (Objects.isNull(funcPermissionResult.getErrCode()) || funcPermissionResult.getErrCode() != 0) {
                log.warn("roleFuncPermission fail. header: {}, roleFuncPermissArg:{}, functionCodes: {}, result:{}", headers, roleFuncPermissArg, functionCodes, funcPermissionResult);
                continue;
            }
            List<FuncPermiss.FunctionPojo> functionPojoList = funcPermissionResult.getResult();
            List<String> toAddFunctionCodes = Lists.newArrayList();
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(functionPojoList)) {
                Set<String> existFunctionCodes = functionPojoList.stream().map(FuncPermiss.FunctionPojo::getFuncCode).collect(Collectors.toSet());
                functionCodes.forEach(x -> {
                    if (!existFunctionCodes.contains(x)) {
                        toAddFunctionCodes.add(x);
                    }
                });
            }
            log.info("Add functionCode [{}] for role [{}]. tenantId: {}", toAddFunctionCodes, roleCode, user.getTenantId());
            if (toAddFunctionCodes.isEmpty()) {
                continue;
            }
            UpdateRoleModifiedFuncPrivilege.Arg updateRoleFuncArg = new UpdateRoleModifiedFuncPrivilege.Arg(authContext, roleCode, toAddFunctionCodes, Lists.newArrayList(), null);
            UpdateRoleModifiedFuncPrivilege.Result updateRoleFuncResult = functionPrivilegeProxy.updateRoleModifiedFuncPrivilege(updateRoleFuncArg, headers);
            log.info("updateRoleModifiedFuncPrivilege result:{}", updateRoleFuncResult);
        }
    }

    public void validateTemplate(User user, IObjectData objectData){
        String printTemplateId = objectData.get(ReconciliationPlanConst.Field.PrintTemplateId.apiName, String.class);
        String describeApiName = TransactionStatementConst.API_NAME;
        // 打印模板必填
        if (StringUtils.isEmpty(printTemplateId)){
            throw new ValidateException(I18N.text(StateI18NKey.PRINT_TEMPLATE_ID_EMPTY));
        }
        // 打印模板必须存在
        if (!printTemplateManager.existPrintTemplate(user, describeApiName, printTemplateId)) {
            throw new ValidateException(I18N.text(StateI18NKey.PRINT_TEMPLATE_ID_NOT_EXIST));
        }
    }

    public List<String> filterNeedDeleteObjectDescribe(User user, List<String> removedObjectApiNameList){
        List<String> needDeleteObjectApiNames = new ArrayList<>();
        // 获取所有已初始化的对账方案数据源中的源对象
        List<IObjectData> reconciliationPlanList = queryReconciliationList(user, null, ReconciliationPlanInitStatusEnum.INITIALIZED.value, null);
        List<String> destObjectApiNameList = new ArrayList<>();
        reconciliationPlanList.forEach(x -> {
            List<Map> reconciliationDataSourceModelList = (List<Map>) x.get(ReconciliationPlanConst.Field.ReconciliationDataSource.apiName);
            List<String> destObjectApiName = reconciliationDataSourceModelList.stream().map(source -> source.get("dest_object_api_name").toString()).collect(Collectors.toList());
            destObjectApiNameList.addAll(destObjectApiName);
        });
        // 已初始化的对账方案数据源中存在的源对象不会被删除对应的目标自定义对象
        List<String> uniqueSrcApiNameList = removedObjectApiNameList.stream().filter(apiName -> !destObjectApiNameList.contains(apiName)).collect(Collectors.toList());
        // 查询对应的目标自定义对象是否有数据
        for (String destApiName : uniqueSrcApiNameList){
            User systemUser = User.systemUser(user.getTenantId());
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            List<IFilter> filterList = Lists.newArrayList();
            searchTemplateQuery.setFilters(filterList);
            int offset = 0;
            int limit = 1000;
            searchTemplateQuery.setOffset(offset);
            searchTemplateQuery.setLimit(limit);
            IObjectDescribe destObjectDescribe = serviceFacade.findObject(user.getTenantId(), destApiName);
            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryWithDeleted(systemUser, destObjectDescribe, searchTemplateQuery);
            if (queryResult.getData().isEmpty()){
                needDeleteObjectApiNames.add(destApiName);
            }
        }
        return needDeleteObjectApiNames;
    }

    private List<String> getRoleCodes(){
        List<String> roleCodes = new ArrayList<>();
        // CRM管理员
        roleCodes.add(CommonConstants.CRM_MANAGER_ROLE);
        // 订货管理员
        roleCodes.add("00000000000000000000000000000033");
        return roleCodes;
    }

    private String getPrintTemplateId(User user, String reconciliationPlanId) {
        List<IObjectData> reconciliationPlanList = queryReconciliationList(user, reconciliationPlanId, null, null);
        if (CollectionUtils.empty(reconciliationPlanList)) {
            throw new ValidateException(I18N.text(StateI18NKey.PRINT_TEMPLATE_ID_EMPTY));
        }
        // 获取对账单绑定的对账方案里设置的打印模板ID
        String templateId = reconciliationPlanList.get(0).get(ReconciliationPlanConst.Field.PrintTemplateId.apiName, String.class);
        // 校验打印模板是否存在
        boolean existTemplate = printTemplateManager.existPrintTemplate(user, TransactionStatementConst.API_NAME, templateId);
        if (existTemplate) {
            return templateId;
        } else {
            throw new ValidateException(I18N.text(StateI18NKey.PRINT_TEMPLATE_ID_NOT_EXIST));
        }
    }
}
