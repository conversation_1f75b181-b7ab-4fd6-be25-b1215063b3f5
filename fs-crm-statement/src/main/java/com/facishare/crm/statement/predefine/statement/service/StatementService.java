package com.facishare.crm.statement.predefine.statement.service;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.facishare.crmcommon.describebuilder.SelectOneFieldDescribeBuilder;
import com.facishare.crmcommon.describebuilder.SelectOptionBuilder;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.statement.constants.ConfigConstants;
import com.facishare.crm.statement.constants.StateI18NKey;
import com.facishare.crm.statement.constants.StatementConstants;
import com.facishare.crm.statement.constants.StatementDetailConstants;
import com.facishare.crm.statement.enums.DeliveryNoteStatusEnum;
import com.facishare.crm.statement.enums.StatementCrmNoticeEnum;
import com.facishare.crm.statement.enums.StatementDetailTypeEnum;
import com.facishare.crm.statement.enums.StatementEnableStatusEnum;
import com.facishare.crm.statement.enums.StatementReceivableByEnum;
import com.facishare.crm.statement.predefine.statement.manager.StatementInitManager;
import com.facishare.crm.statement.predefine.statement.service.dto.AddSignAttachFieldModel;
import com.facishare.crm.statement.predefine.statement.service.dto.EnableStatementModel;
import com.facishare.crm.statement.predefine.statement.service.dto.GetStatementConfigModel;
import com.facishare.crm.statement.predefine.statement.service.dto.SetStatementConfigModel;
import com.facishare.crm.statement.util.StatementConfig;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.ui.layout.component.SimpleComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;

/**
 * IgnoreI18nFile
 */
@Slf4j
@Service
@ServiceModule("statement")
public class StatementService {
    @Autowired
    private StatementInitManager statementInitManager;
    @Autowired
    private ConfigService configService;
    @Autowired
    private IObjectDescribeService objectDescribeService;
    @Autowired
    private ServiceFacade serviceFacade;

    private static final String STATEMENT_VERSION_KEY = "statement_app";

    @ServiceMethod("enable_statement")
    public EnableStatementModel.Result enableStatement(ServiceContext serviceContext) {
        User user = serviceContext.getUser();
        Set<String> moduleList = serviceFacade.getModule(user.getTenantId());
        if (CollectionUtils.isEmpty(moduleList) || !moduleList.contains(STATEMENT_VERSION_KEY)) {
            throw new ValidateException(I18N.text(StateI18NKey.STATEMENT_LICENSE_VALIDATE));
        }
        EnableStatementModel.Result result = new EnableStatementModel.Result();
        if (!StatementConfig.isStatementGrayTenant(user.getTenantId())) {
            throw new ValidateException("暂不支持该功能，请联系纷享客服！");
        }
        String configValue = configService.findTenantConfig(user, ConfigConstants.STATEMENT_ENABLE_KEY);
        if (StatementEnableStatusEnum.OPENED.value.equals(configValue)) {
            return result;
        }

        statementInitManager.init(user);
        updateTenantConfig(user, ConfigConstants.STATEMENT_ENABLE_KEY, StatementEnableStatusEnum.OPENED.value);
        //sendMq(serviceContext.getUser());
        return result;
    }

    @ServiceMethod("get_statement_config")
    public GetStatementConfigModel.Result getStatementConfig(ServiceContext serviceContext) {
        GetStatementConfigModel.Result result = new GetStatementConfigModel.Result();
        User user = serviceContext.getUser();
        String statementStatus = configService.findTenantConfig(user, ConfigConstants.STATEMENT_ENABLE_KEY);
        String statementReceivableBy = configService.findTenantConfig(user, ConfigConstants.STATEMENT_RECEIVABLE_BY_KEY);
        String statementNeedCrmNotice = configService.findTenantConfig(user, ConfigConstants.STATEMENT_CRM_NOTICE_KEY);
        String deliveryNoteStatus = configService.findTenantConfig(user, ConfigConstants.DELIVERY_NOTE_ENABLE_KEY);
        result.setStatementOpened(StatementEnableStatusEnum.OPENED.value.equals(statementStatus));
        result.setNeedCrmNotice(statementNeedCrmNotice == null || StatementCrmNoticeEnum.YES.name().equalsIgnoreCase(statementNeedCrmNotice));
        boolean deliveryOpened = DeliveryNoteStatusEnum.OPENED.getValue().equals(deliveryNoteStatus);
        if (StringUtils.isEmpty(statementReceivableBy) || !deliveryOpened) {
            statementReceivableBy = Utils.SALES_ORDER_API_NAME;
        }
        result.setReceivableBy(statementReceivableBy);
        result.setDeliveryNoteOpened(deliveryOpened);
        return result;
    }

    @ServiceMethod("update_statement_config")
    public SetStatementConfigModel.Result setStatementConfig(ServiceContext serviceContext, SetStatementConfigModel.Arg arg) {
        User user = serviceContext.getUser();
        boolean exist = Arrays.stream(StatementReceivableByEnum.values()).anyMatch(statementReceivableByEnum -> statementReceivableByEnum.value.equals(arg.getReceivableBy()));
        if (!exist && StringUtils.isNotEmpty(arg.getReceivableBy())) {
            throw new ValidateException(I18N.text(StateI18NKey.RECEIVED_BY_PARAMS_ERROR));
        }
        String deliveryNoteStatus = configService.findTenantConfig(user, ConfigConstants.DELIVERY_NOTE_ENABLE_KEY);
        if (!DeliveryNoteStatusEnum.OPENED.getValue().equals(deliveryNoteStatus) && StatementReceivableByEnum.DELIVERY_NOTE.value.equals(arg.getReceivableBy())) {
            throw new ValidateException(I18N.text(StateI18NKey.NOT_RECEIVED_BY_DELIVERY_NOTE));
        }
        if (StringUtils.isNotEmpty(arg.getReceivableBy())) {
            updateTenantConfig(user, ConfigConstants.STATEMENT_RECEIVABLE_BY_KEY, arg.getReceivableBy());
        }
        if (Objects.nonNull(arg.getNeedCrmNotice())) {
            updateTenantConfig(user, ConfigConstants.STATEMENT_CRM_NOTICE_KEY, arg.getNeedCrmNotice() ? StatementCrmNoticeEnum.YES.name() : StatementCrmNoticeEnum.NO.name());
        }
        return new SetStatementConfigModel.Result();
    }

    //开启对账单签章功能时 增加电子签章合同字段
    @ServiceMethod("add_statement_sign_attach_field")
    public AddSignAttachFieldModel.Result addSignAttachField(ServiceContext serviceContext) {
        String configValue = configService.findTenantConfig(serviceContext.getUser(), ConfigConstants.STATEMENT_ENABLE_KEY);
        if (!StatementEnableStatusEnum.OPENED.value.equals(configValue)) {
            throw new ValidateException(I18N.text(StateI18NKey.STATEMENT_NOT_OPEN));
        }
        statementInitManager.addSignAttachFieldWhenSignOpen(serviceContext.getUser());
        return new AddSignAttachFieldModel.Result();
    }

    @ServiceMethod("update_field_describe")
    public String updateFieldDescribe(ServiceContext serviceContext) {
        List<ISelectOption> typeSelectOptions = Arrays.stream(StatementDetailTypeEnum.values()).map(typeEnum -> SelectOptionBuilder.builder().value(typeEnum.value).label(typeEnum.label).build()).collect(Collectors.toList());
        SelectOneFieldDescribe typeSelectOneFieldDescribe = SelectOneFieldDescribeBuilder.builder().apiName(StatementDetailConstants.Field.Type.apiName).label(StatementDetailConstants.Field.Type.label).required(true).selectOptions(typeSelectOptions).build();
        try {
            IObjectDescribe objectDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(serviceContext.getTenantId(), StatementDetailConstants.API_NAME);
            objectDescribeService.updateFieldDescribe(objectDescribe, Lists.newArrayList(typeSelectOneFieldDescribe));
        } catch (MetadataServiceException e) {
            log.warn("update_field_describe", e);
            return e.getMessage();
        }
        return "ok";
    }

    @ServiceMethod("init_top_info")
    public List<String> initTopInfo(ServiceContext serviceContext, List<String> tenantIds) {
        List<String> failTenantIds = Lists.newArrayList();
        if (CollectionUtils.isEmpty(tenantIds)) {
            return failTenantIds;
        }
        for (String tenantId : tenantIds) {
            User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID);
            try {
                ILayout layout = serviceFacade.findLayoutByApiName(user, StatementConstants.DEFAULT_LAYOUT_API_NAME, StatementConstants.API_NAME);
                if (Objects.isNull(layout)) {
                    continue;
                }
                SimpleComponent newTopInfo = StatementConstants.getStatementTopInfo();
                if (Objects.nonNull(newTopInfo)) {
                    layout.setTopInfo(newTopInfo);
                    serviceFacade.updateLayout(user, layout);
                }
            } catch (Exception e) {
                log.warn("init statement top info error,tenantId:{}", tenantId, e);
                failTenantIds.add(tenantId);
            }
        }
        return failTenantIds;
    }

    private void updateTenantConfig(User user, String key, String value) {
        String configValue = configService.findTenantConfig(user, key);
        if (Objects.isNull(configValue)) {
            configService.createTenantConfig(user, key, value, ConfigValueType.STRING);
        } else {
            configService.updateTenantConfig(user, key, value, ConfigValueType.STRING);
        }
    }

}
