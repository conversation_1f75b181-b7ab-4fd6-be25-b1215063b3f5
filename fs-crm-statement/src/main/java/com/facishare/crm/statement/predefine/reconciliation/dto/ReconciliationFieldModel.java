package com.facishare.crm.statement.predefine.reconciliation.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

@Data
public class ReconciliationFieldModel {
    @SerializedName("src_field_name")
    @JsonProperty("src_field_name")
    @JSONField(name = "src_field_name")
    private String srcFieldName;
    @SerializedName("dest_field_name")
    @JsonProperty("dest_field_name")
    @JSONField(name = "dest_field_name")
    private String destFieldName;

    public ReconciliationFieldModel(String srcFieldName, String destFieldName) {
        this.srcFieldName = srcFieldName;
        this.destFieldName = destFieldName;
    }
}
