package com.facishare.crm.statement.predefine.statement.action;

import com.facishare.crm.statement.predefine.statement.manager.StatementManager;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.predef.action.StandardFlowStartCallbackAction;
import com.facishare.paas.metadata.util.SpringUtil;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class StatementFlowCallBackAction extends StandardFlowStartCallbackAction {
    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        StatementManager statementManager = SpringUtil.getContext().getBean(StatementManager.class);
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        parallelTask.submit(() -> statementManager.updateStatementDetail(actionContext.getUser(), this.objectData));
        try {
            parallelTask.run();
        } catch (Exception e) {
            log.warn("StatementFlowCallBackAction update detail error", e);
        }
        return result;
    }
}
