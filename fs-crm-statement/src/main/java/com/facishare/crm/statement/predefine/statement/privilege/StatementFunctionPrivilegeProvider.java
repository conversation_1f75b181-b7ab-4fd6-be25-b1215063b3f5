package com.facishare.crm.statement.predefine.statement.privilege;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;

import com.facishare.crmcommon.constants.CommonConstants;
import com.facishare.crm.statement.constants.StatementConstants;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.privilege.model.DefaultFunctionPrivilegeProvider;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

@Component
public class StatementFunctionPrivilegeProvider extends DefaultFunctionPrivilegeProvider {
    private final static List<String> SUPPORT_ACTIONS = Collections.unmodifiableList(Lists.newArrayList(ObjectAction.VIEW_LIST.getActionCode(), ObjectAction.VIEW_DETAIL.getActionCode(), ObjectAction.CREATE.getActionCode(), ObjectAction.UPDATE.getActionCode(), ObjectAction.RELATE.getActionCode(), ObjectAction.INVALID.getActionCode(), ObjectAction.RECOVER.getActionCode(), ObjectAction.DELETE.getActionCode(), ObjectAction.CHANGE_OWNER.getActionCode(), ObjectAction.BATCH_EXPORT.getActionCode(),
            ObjectAction.EDIT_TEAM_MEMBER.getActionCode(), ObjectAction.START_BPM.getActionCode(), ObjectAction.VIEW_ENTIRE_BPM.getActionCode(), ObjectAction.STOP_BPM.getActionCode(), ObjectAction.CHANGE_BPM_APPROVER.getActionCode(), ObjectAction.PRINT.getActionCode(), ObjectAction.LOCK.getActionCode(), ObjectAction.UNLOCK.getActionCode(), ObjectAction.MODIFYLOG_RECOVER.getActionCode()));

    @Override
    public String getApiName() {
        return StatementConstants.API_NAME;
    }

    @Override
    public List<String> getSupportedActionCodes() {
        return SUPPORT_ACTIONS;
    }

    @Override
    public Map<String, List<String>> getCustomInitRoleActionCodes() {
        return getRoleActions();
    }

    public static Map<String, List<String>> getRoleActions() {
        Map<String, List<String>> roleActionMap = Maps.newHashMap();
        roleActionMap.put(CommonConstants.ORDER_FINANCE_ROLE, SUPPORT_ACTIONS);
        roleActionMap.put(CommonConstants.PAYMENT_FINANCE_ROLE, SUPPORT_ACTIONS);
        List<String> viewActionList = Collections.unmodifiableList(Lists.newArrayList(ObjectAction.VIEW_DETAIL.getActionCode(), ObjectAction.VIEW_LIST.getActionCode()));
        roleActionMap.put(CommonConstants.BILL_FINANCE_ROLE, viewActionList);
        roleActionMap.put(CommonConstants.REFUND_FINANCE_ROLE, viewActionList);
        return Collections.unmodifiableMap(roleActionMap);
    }
}
