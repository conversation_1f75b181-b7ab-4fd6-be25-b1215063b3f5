package com.facishare.crm.statement.predefine.reconciliation.service;

import com.facishare.crm.statement.constants.TransactionStatementConst;
import com.facishare.crm.statement.predefine.reconciliation.dto.RefreshTransactionStatementModel;
import com.facishare.crm.statement.predefine.reconciliation.dto.TransactionStatementHasDataResult;
import com.facishare.crm.statement.predefine.reconciliation.manager.TransactionStatementManager;
import com.facishare.crmcommon.manager.CommonObjDataManager;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.rest.core.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;

@Slf4j
@ServiceModule("transaction_statement")
@Component
public class TransactionStatementService {
    @Autowired
    private CommonObjDataManager commonObjDataManager;
    @Autowired
    private TransactionStatementManager transactionStatementManager;

    /**
     * TransactionStatementObj是否有数据（包含回收站中作废的数据）
     */
    @ServiceMethod("has_data")
    public TransactionStatementHasDataResult hasData(ServiceContext serviceContext) {
        TransactionStatementHasDataResult result = new TransactionStatementHasDataResult();
        boolean hasDataIncludeInvalid = commonObjDataManager.hasDataIncludeInvalid(serviceContext.getTenantId(), TransactionStatementConst.API_NAME);
        result.setHasData(hasDataIncludeInvalid);
        return result;
    }


    /**
     * 1. 修改TenantConfig状态
     * 2. 给对账方案对象新属性赋值（初始化状态，打印模板，预制对象新增的系统字段）
     * 3. 给交易对账单对象赋值对账方案字段
     * arg：
     * {
     *     "tenantIds": ["88130"]
     * }
     */
    @ServiceMethod("refreshData")
    public RefreshTransactionStatementModel.Result refreshData(ServiceContext serviceContext, RefreshTransactionStatementModel.Arg arg) {
        RefreshTransactionStatementModel.Result result = new RefreshTransactionStatementModel.Result();
        List<String> tenantIds = arg.getTenantIds();
        log.info("RefreshTransactionStatementData start. tenantId: {}", tenantIds);
        if (CollectionUtils.empty(tenantIds)) {
            result.setMessage("未传入tenantIds"); //ignoreI18n
            return result;
        }
        List<String> successTenantIds = new ArrayList<>();
        List<String> failTenantIds = new ArrayList<>();
        List<String> errorTenantIds = new ArrayList<>();
        for (String tenantId : tenantIds) {
            User user = new User(tenantId, "-10000");
            try {
                boolean isSuccess = transactionStatementManager.refreshData(user);
                if (isSuccess){
                    successTenantIds.add(tenantId);
                } else {
                    failTenantIds.add(tenantId);
                }
            } catch (Exception e) {
                log.warn("RefreshTransactionStatementData error. tenantId: {}", tenantId, e);
                errorTenantIds.add(tenantId);
            }
        }
        log.info("RefreshTransactionStatementData end. success tenantIds: {}, fail tenantIds: {}, error tenantIds: {}", successTenantIds, failTenantIds, errorTenantIds);
        return result;
    }
}
