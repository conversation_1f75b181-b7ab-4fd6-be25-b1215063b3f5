package com.facishare.crm.statement.predefine.reconciliation.button;

import com.facishare.crm.statement.constants.TransactionStatementConst;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.metadata.button.SpecialButtonProvider;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class TransactionStatementButtonProvider implements SpecialButtonProvider {
    @Override
    public String getApiName() {
        return TransactionStatementConst.API_NAME;
    }

    @Override
    public List<IButton> getSpecialButtons() {
        return Lists.newArrayList(buildButton(ObjectAction.INITIATE_RECONCILIATION), buildButton(ObjectAction.CONFIRM_RECONCILIATION));
    }

    private IButton buildButton(ObjectAction objectAction) {
        IButton button = new Button();
        button.setAction(objectAction.getActionCode());
        button.setLabel(objectAction.getActionLabel());
        button.set("isActive", true);
        button.setName(objectAction.getButtonApiName());
        button.setActionType(IButton.ACTION_TYPE_DEFAULT);
        return button;
    }
}
