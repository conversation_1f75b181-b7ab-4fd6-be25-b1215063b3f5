package com.facishare.crm.statement.predefine.reconciliation.controller;

import com.facishare.crm.statement.util.ReconciliationUtil;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;

import java.util.List;
import java.util.Map;
import java.util.Objects;

public class TransactionStatementListController extends StandardListController {
    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery searchTemplateQuery = super.buildSearchTemplateQuery();
        return ReconciliationUtil.fillOuterFilterIfNeed(controllerContext.getUser(), searchTemplateQuery);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        ButtonInfo buttonInfo = result.getButtonInfo();
        if (!controllerContext.getUser().isOutUser() && Objects.nonNull(buttonInfo)) {
            Map<String, List<String>> buttonMap = buttonInfo.getButtonMap();
            if (Objects.nonNull(buttonMap)) {
                buttonMap.forEach((k, v) -> {
                    if (Objects.nonNull(v)) {
                        v.removeIf(x -> ObjectAction.CONFIRM_RECONCILIATION.getButtonApiName().equals(x));
                    }
                });
            }
        }
        return result;
    }
}
