package com.facishare.crm.statement.predefine.reconciliation.controller;

import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;

public class ReconciliationPlanListController extends StandardListController {
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery query = super.buildSearchTemplateQuery();
        if (Objects.nonNull(query)) {
            query.setPermissionType(0);
        }
        return query;
    }
}
