package com.facishare.crm.statement.service;

import com.facishare.crm.notify.model.RemindRecord;
import com.facishare.crm.notify.model.Service.AddRemindRecordArg;
import com.facishare.crm.notify.model.Service.AddRemindRecordResult;
import com.facishare.crm.notify.service.RemindRecordService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Map;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath*:spring-test/remind-test.xml")
public class RemindServiceTest {
    @Autowired
    private RemindRecordService remindRecordService;

    static {
        System.setProperty("spring.profiles.active", "fstest");
    }

    @Test
    public void remind() {
        //JsonMerge
        Map<String, String> headers = Maps.newHashMap();
        headers.put("x-fs-ei", "68867");
        headers.put("x-tenant-id", "68867");
        headers.put("x-fs-userInfo", "1000");
        headers.put("x-user-id", "1000");
        AddRemindRecordArg arg = new AddRemindRecordArg();
        RemindRecord remindRecord = new RemindRecord();
        remindRecord.setEi(68867);
//        remindRecord.setSenderId(1000);
        remindRecord.setType(209);
        remindRecord.setAppId("CRM");
        remindRecord.setTitle("对账通知");
        remindRecord.setReceiverIDs(Lists.newArrayList(1000));
        remindRecord.setFullContent("对账通知test");
        remindRecord.setSourceId(System.currentTimeMillis()+"");

        remindRecord.setUrlType(0);

        Map<String, String> urlParameter = Maps.newHashMap();
        urlParameter.put("objectApiName", "TransactionStatementObj");
        urlParameter.put("objectId", "63e0ee654899600001142705");
        remindRecord.setUrlParameter(urlParameter);


        arg.setRemindRecord(remindRecord);
        AddRemindRecordResult result = remindRecordService.addRemindRecord(headers, arg);
        System.out.println(result);
    }
}
