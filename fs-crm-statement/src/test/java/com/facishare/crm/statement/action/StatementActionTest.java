package com.facishare.crm.statement.action;

import java.util.Date;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.statement.predefine.statement.action.StatementAddAction;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.facishare.crmcommon.constants.SystemConstants;
import com.facishare.crm.statement.base.BaseActionTest;
import com.facishare.crm.statement.constants.StatementConstants;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.collect.Lists;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath*:spring-test/statement-test.xml")
public class StatementActionTest extends BaseActionTest {

    static {
        System.setProperty("spring.profiles.active", "fstest");
    }

    public StatementActionTest() {
        super(StatementConstants.API_NAME);
    }

    @Test
    public void addTest() {
        IObjectData statementData = new ObjectData();
        statementData.set(StatementConstants.Field.Customer.apiName, "b0082d32ebb54584a7efd8ca133a28b6");
        statementData.set(StatementConstants.Field.StartTime.apiName, new Date().getTime());
        statementData.set(StatementConstants.Field.EndTime.apiName, new Date().getTime());
        statementData.set(StatementConstants.Field.OpeningBalance.apiName, 100);
        statementData.set(SystemConstants.Field.Owner.apiName, Lists.newArrayList("1000"));
        Object result = executeAdd(statementData);
        System.out.print(result);
    }

    @Test
    public void bulkInvalidTest() {
        StandardBulkInvalidAction.Arg arg = new StandardBulkInvalidAction.Arg();
        arg.setJson("{\"dataList\":[{\"object_describe_id\":\"5b7542dc9adf7485dc2a220c\",\"object_describe_api_name\":\"StatementObj\",\"_id\":\"5b96515dbab09ca178e8e5b0\",\"tenant_id\":\"58438\"}]}");
        Object result = executeBulkInvalid(arg);
        System.out.print(result);
    }

    @Test
    public void queryTest() {
        String customerId = "73bb86c9d8674cb9bccaf9ae36ae833a";
        StatementAddAction addAction = new StatementAddAction();
        addAction.setServiceFacade(serviceFacade);
        long startTime = 1557765844000L;
        long endTime = 1557978190050L;
        try {
            addAction.createDetailData(user, Utils.SALES_ORDER_API_NAME, "order_amount", addAction.getSearchTemplateQuery(tenantId, Utils.SALES_ORDER_API_NAME, customerId, startTime, endTime));
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            addAction.createDetailData(user, Utils.RETURN_GOODS_INVOICE_API_NAME, "returned_goods_inv_amount", addAction.getSearchTemplateQuery(tenantId, Utils.RETURN_GOODS_INVOICE_API_NAME, customerId, startTime, endTime));
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            startTime = 1555173844000L;
            addAction.createDetailData(user, Utils.REFUND_API_NAME, "refunded_amount", addAction.getSearchTemplateQuery(tenantId, Utils.REFUND_API_NAME, customerId, startTime, endTime));
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println();


    }
}
