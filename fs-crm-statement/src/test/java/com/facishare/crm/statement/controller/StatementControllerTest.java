package com.facishare.crm.statement.controller;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.facishare.crmcommon.constants.SystemConstants;
import com.facishare.crm.statement.base.BaseControllerTest;
import com.facishare.crm.statement.constants.StatementConstants;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.appframework.core.predef.controller.StandardDetailController;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath*:spring-test/statement-test.xml")
public class StatementControllerTest extends BaseControllerTest {

    static {
        System.setProperty("spring.profiles.active", "ceshi113");
    }

    public StatementControllerTest() {
        super(StatementConstants.API_NAME);
    }

    @Test
    public void detailTest() {
        StandardDetailController.Arg arg = new StandardDetailController.Arg();
        arg.setObjectDataId("5b96515dbab09ca178e8e5b0");
        arg.setObjectDescribeApiName(apiName);
        StandardDetailController.Result result = executeDetail(arg);
        System.out.println(result);
    }

    @Test
    public void describeLayoutTest() {
        StandardDescribeLayoutController.Arg arg = new StandardDescribeLayoutController.Arg();
        arg.setApiname(apiName);
        arg.setInclude_detail_describe(false);
        arg.setInclude_layout(true);
        arg.setInclude_ref_describe(false);
        arg.setLayout_type(SystemConstants.LayoutType.Add.layoutType);
        arg.setRecordType_apiName("default__c");
        Object result = execute("DescribeLayout", arg);
        System.out.print(result);
    }

    @Test
    public void bulkInvalidTest() {

    }
}
