package com.facishare.crm.statement.service;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.facishare.crm.statement.base.BaseServiceTest;
import com.facishare.crm.statement.predefine.statement.manager.StatementInitManager;
import com.facishare.crm.statement.predefine.statement.service.dto.SetStatementConfigModel;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath*:spring-test/statement-test.xml")
public class StatementServiceTest extends BaseServiceTest {
    @Autowired
    private StatementInitManager statementInitManager;
    static {
        System.setProperty("spring.profiles.active", "fstest");
    }

    @Test
    public void addDownFieldTest() {
        boolean flag = statementInitManager.addFieldAndOptionWhileStatementAppOpened(user);
        System.out.println(flag);
    }

    @Test
    public void getStatementConfigTest() {
        Object result = invoke(newServiceContext("statement", "get_statement_config"), "");
        Assert.assertNotNull(result);
    }

    @Test
    public void enableStatementTest() {
        Object result = invoke(newServiceContext("statement", "enable_statement"), "");
        System.out.print(result);
    }

    @Test
    public void updateStatementConfigTest() {
        SetStatementConfigModel.Arg arg = new SetStatementConfigModel.Arg();
        arg.setNeedCrmNotice(false);
        Object result = invoke(newServiceContext("statement", "update_statement_config"), arg);
        System.out.print(result);
    }

    @Test
    public void addStatementSignAttachFieldTest() {
        Object result = invoke(newServiceContext("statement", "add_statement_sign_attach_field"), "");
        System.out.print(result);
    }

    @Test
    public void updateFieldDescribeTest() {

    }
}
