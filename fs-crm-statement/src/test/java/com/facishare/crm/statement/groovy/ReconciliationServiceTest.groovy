package com.facishare.crm.statement.groovy

import com.facishare.crm.statement.util.StatementUtil
import spock.lang.Specification

import java.text.SimpleDateFormat

class ReconciliationServiceTest extends Specification {

    def "test"() {
        given:
        Calendar calendar = Calendar.getInstance();
        calendar.set(2024, month - 1, day)
        Date date = calendar.getTime()

        when:
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss:SSS")
        Long startDate = StatementUtil.getDateStartTime(date)
        Long endDate = StatementUtil.getDateEndTime(date)
        String startDateStr = format.format(startDate)
        String endDateStr = format.format(endDate)

        then:
        println("======startDate          || endDate       ")
        println(startDateStr + "         " + endDateStr)

        where:
        month | day
        7     | 31


    }


}
