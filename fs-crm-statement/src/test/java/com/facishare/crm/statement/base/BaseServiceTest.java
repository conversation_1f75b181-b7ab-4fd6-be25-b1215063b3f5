package com.facishare.crm.statement.base;

import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.core.model.SerializerManager;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceDispatcher;
import com.facishare.paas.appframework.core.model.ServiceFacade;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class BaseServiceTest extends BaseTest {
    @Autowired
    private ServiceDispatcher serviceDispatcher;
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private SerializerManager serializerManager;

    public ServiceContext newServiceContext(String serviceName, String serviceMethod) {
        return new ServiceContext(requestContext, serviceName, serviceMethod);
    }

    public <T> Object invoke(ServiceContext serviceContext, T arg) {
        String payload = serializerManager.getSerializer(requestContext.getContentType()).encode(arg);
        Object result = this.serviceDispatcher.service(serviceContext, payload);
        log.debug("Service result:{}", JSON.toJSONString(result));
        return result;
    }

}
