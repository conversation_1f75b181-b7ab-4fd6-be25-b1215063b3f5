<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:c="http://www.springframework.org/schema/c"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-4.1.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <import resource="classpath:spring/metadata.xml"/>
    <import resource="classpath:spring/common.xml"/>
    <import resource="classpath:spring/dubbo.xml"/>
    <import resource="classpath:spring/log.xml"/>
    <import resource="classpath:spring/flow.xml"/>
    <import resource="classpath:spring/privilege.xml"/>
    <import resource="classpath:spring/restdriver.xml"/>
    <import resource="classpath:spring/licence.xml"/>
    <import resource="classpath:spring/fsi.xml"/>
    <import resource="classpath:spring/payment.xml"/>
    <import resource="classpath:spring/config.xml"/>
    <import resource="classpath:spring/statement-spring.xml"/>
    <!--<import resource="classpath:/META-INF/transfer-context.xml"/>-->
    <import resource="classpath:spring/common.xml"/>
    <!--<import resource="classpath:spring/fs-fsi-proxy-service.xml"/>-->
    <import resource="classpath:spring/function-service.xml"/>
    <import resource="classpath:spring/core-dubbo.xml"/>

    <context:component-scan base-package="com.facishare.paas.appframework,com.facishare.crm"/>
    <context:annotation-config/>

    <bean id="objectResource" class="com.facishare.paas.appframework.resource.ObjectResource"/>
    <bean id="objectInnerAPIResource" class="com.facishare.paas.appframework.resource.ObjectInnerAPIResource"/>
    <bean id="objectRestAPIResource" class="com.facishare.paas.appframework.resource.ObjectRestAPIResource"/>

    <bean id="autoConf"
          class="com.github.autoconf.spring.reloadable.ReloadablePropertySourcesPlaceholderConfigurer"
          p:fileEncoding="UTF-8"
          p:ignoreResourceNotFound="true"
          p:ignoreUnresolvablePlaceholders="false"
          p:location="classpath:application.properties"
          p:configName="dubbo-common,fs-paas-metadata-mongo,fs-paas-appframework-rest,fs-crm-java-config
            ,fs-crm-printconfig,fs-metadata,fs-crm-icon-path,fs-crm-java-detailpage-layout-setting"/>

    <bean class="com.github.autoconf.spring.reloadable.ReloadablePropertyPostProcessor"
          c:placeholderConfigurer-ref="autoConf"/>

    <bean class="com.facishare.fcp.service.FcpServiceBeanPostProcessor"
          id="fcpServiceBeanPostProcessor"/>

    <bean class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.rest.CrmRestApi">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.rest.ApprovalInitProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.rest.TemplateApi">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.rest.SendCrmMessageProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <!--privilege temp-->
    <import resource="classpath:privilege-temp.xml"/>
</beans>
