package com.facishare.crm.payment.action;

import com.google.common.collect.Lists;

import com.facishare.crm.payment.constant.CustomerPaymentObj;
import com.facishare.crm.payment.utils.SOPaymentI18NKeyUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportDataAction;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CustomerPaymentInsertImportDataAction extends StandardInsertImportDataAction {

  @Override
  protected void customValidate(List<ImportData> dataList) {
    super.customValidate(dataList);
    List<ImportError> errorList = Lists.newArrayList();
    log.debug("CustomerPaymentInsertImportDataAction customValidate dataList: {}", dataList);
    ArrayList< String > options = Lists.newArrayList(CustomerPaymentObj.PAYMENT_METHOD_DEPOSIT,
        CustomerPaymentObj.PAYMENT_METHOD_REBATE, CustomerPaymentObj.PAYMENT_METHOD_DNR);
    dataList.forEach(data -> {
      String method = (String) data.getData().get(CustomerPaymentObj.FIELD_PAYMENT_METHOD);
      if (StringUtils.isNotBlank(method) && options.contains(method)){
        errorList.add(new ImportError(data.getRowNo(), I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_PAYMENTTERMERROR)));
      }
    });
    mergeErrorList(errorList);
  }
}
