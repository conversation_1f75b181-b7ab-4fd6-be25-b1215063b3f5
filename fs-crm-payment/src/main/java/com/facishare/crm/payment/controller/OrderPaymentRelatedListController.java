package com.facishare.crm.payment.controller;

import com.facishare.crm.payment.service.OrderPaymentService;
import com.facishare.crm.payment.utils.JsonObjectUtils;
import com.facishare.crm.payment.utils.JsonPaths;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.List;


public class OrderPaymentRelatedListController extends StandardRelatedListController {

  @Override
  protected Result doService(Arg arg){
    Result result = super.doService(arg);
    OrderPaymentService orderPaymentService =
        SpringUtil.getContext().getBean(OrderPaymentService.class);
    List< ObjectDataDocument > list =
        orderPaymentService.parseOrderPaymentCost(result.getDataList(), controllerContext);
    result.setDataList(list);
    return result;
  }

}
