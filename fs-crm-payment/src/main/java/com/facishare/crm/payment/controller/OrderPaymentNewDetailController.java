package com.facishare.crm.payment.controller;

import com.facishare.crm.payment.constant.CustomerPaymentObj;
import com.facishare.crm.payment.utils.JsonObjectUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardNewDetailController;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.google.common.collect.Sets;

import java.util.HashSet;
import java.util.List;

import static com.facishare.crm.payment.utils.JsonPaths.NEW_DETAIL_LAYOUT_FORM;

/**
 * <AUTHOR>
 * @date 2019-08-27 15:08
 * @instruction
 */
public class OrderPaymentNewDetailController extends StandardNewDetailController {
    @Override
    protected Result doService(Arg arg) {
        Result result = super.doService(arg);

        HashSet<String> needRemoveButtonActions = Sets.newHashSet("Abolish", "Lock", "Edit", "Clone");
        List<IButton> buttons = result.getLayout().toLayout().getButtons();
        buttons.removeIf(button->needRemoveButtonActions.contains(button.getAction()));
        result.getLayout().toLayout().setButtons(buttons);
        result = modifyResult(result);
        return result;
    }

    /**
     * 隐藏当前审批人字段
     */
    private Result modifyResult(Result result) {
        result = JsonObjectUtils.remove(result, Result.class,
                NEW_DETAIL_LAYOUT_FORM + "[?(@.field_name=='"
                        + CustomerPaymentObj.FIELD_APPROVE_EMPLOYEE_ID + "')]");
        return result;
    }
}
