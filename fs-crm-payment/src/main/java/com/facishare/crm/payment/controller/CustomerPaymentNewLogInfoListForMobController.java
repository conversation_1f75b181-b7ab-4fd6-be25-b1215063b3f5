package com.facishare.crm.payment.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.payment.constant.CustomerPaymentObj;
import com.facishare.paas.appframework.core.predef.controller.StandardNewLogInfoListForMobController;
import com.facishare.paas.appframework.core.predef.service.dto.log.LogRecord;
import com.facishare.paas.appframework.log.dto.LogInfo;
import com.facishare.paas.appframework.log.dto.ModifyRecord;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.INameCache;
import com.google.common.collect.Lists;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2019-09-27
 * @instruction
 */
public class CustomerPaymentNewLogInfoListForMobController extends StandardNewLogInfoListForMobController {
    @Override
    protected LogRecord modifyRecordToLogRecord(ModifyRecord record) {
        LogRecord logRecord = super.modifyRecordToLogRecord(record);
        List<LogInfo.DiffObjectData> objectDataList = logRecord.getObjectData();
        if (CollectionUtils.isEmpty(objectDataList)) return logRecord;

        objectDataList.forEach(o -> {
            String fieldApiName = o.getFieldApiName();
            Map<String, Object> apiNameToValue = o.getValue();
            Map<String, Object> apiNameToOldValue = o.getOldValue();
            if (CustomerPaymentObj.FIELD_ORDER_ID.equals(fieldApiName)) {
                fillOrderName(fieldApiName, apiNameToOldValue);
                fillOrderName(fieldApiName, apiNameToValue);
            }

        });
        logRecord.setObjectData(objectDataList);
        return logRecord;
    }

    private void fillOrderName(String fieldApiName, Map<String, Object> apiNameToValue) {
        apiNameToValue.computeIfPresent(fieldApiName, (k, v) -> {
            List<INameCache> recordName = serviceFacade.findRecordName(ActionContextExt.of(controllerContext.getUser()).getContext(), Utils.SALES_ORDER_API_NAME, Lists.newArrayList(v.toString().split(",")));
            Map<String, String> idToName = recordName.stream().collect(Collectors.toMap(INameCache::getId, INameCache::getName));
            return Lists.newArrayList(v.toString().split(",")).stream().map(oo -> idToName.getOrDefault(oo, "")).collect(Collectors.joining(","));
        });
    }
}
