package com.facishare.crm.payment.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.payment.constant.CustomerPaymentObj;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardSnapShotForMobController;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.INameCache;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2019-09-27
 * @instruction
 */
public class CustomerPaymentSnapShotForMobController extends StandardSnapShotForMobController {
    @Override
    protected void fillFieldInfo(User user, IObjectDescribe objectDescribe, Map<String, Object> objData) {
        super.fillFieldInfo(user, objectDescribe, objData);
        objData.computeIfPresent(CustomerPaymentObj.FIELD_ORDER_ID,(k, v)->{
            List<INameCache> recordName = serviceFacade.findRecordName(ActionContextExt.of(controllerContext.getUser()).getContext(), Utils.SALES_ORDER_API_NAME, Lists.newArrayList(v.toString().split(",")));
            Map<String, String> idToName = recordName.stream().collect(Collectors.toMap(INameCache::getId, INameCache::getName));
            return Lists.newArrayList(v.toString().split(",")).stream().map(oo -> idToName.getOrDefault(oo, "")).collect(Collectors.joining(","));
        });
    }
}
