package com.facishare.crm.payment.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.payment.PaymentObject;
import com.facishare.crm.payment.constant.CrmPackageObjectConstants;
import com.facishare.crm.payment.constant.CustomerPaymentObj;
import com.facishare.crm.payment.constant.OrderPaymentObj;
import com.facishare.crm.payment.proxy.CustomerAccountForPaymentProxy;
import com.facishare.crm.payment.proxy.model.*;
import com.facishare.crm.payment.service.customeraccount.constans.PrepayDetailConstants;
import com.facishare.crm.payment.service.customeraccount.constans.RebateOutcomeDetailConstants;
import com.facishare.crm.payment.service.dto.Args;
import com.facishare.crm.payment.service.dto.PaymentDhtMq;
import com.facishare.crm.payment.utils.CustomerAccountUtil;
import com.facishare.crm.payment.utils.FieldUtils;
import com.facishare.crm.payment.utils.SOPaymentI18NKeyUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.mq.RocketMQMessageSender;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.action.StandardBulkDeleteAction;
import com.facishare.paas.appframework.core.predef.action.StandardBulkRecoverAction;
import com.facishare.paas.appframework.core.predef.action.StandardFlowCompletedAction;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.appframework.metadata.util.SFAHeaderUtil;
import com.facishare.paas.common.util.UdobjConstants;
import com.facishare.paas.metadata.api.INameCache;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.IObjectDataProxyService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.QuoteFieldDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.SqlEscaper;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.fxiaoke.rocketmq.producer.DefaultTopicMessage;
import com.fxiaoke.transfer.utils.ConverterUtil;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.ws.rs.POST;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.payment.constant.CustomerPaymentObj.*;
import static com.facishare.crm.payment.constant.OrderPaymentObj.FIELD_PAYMENT_ID;

@ServiceModule("customer_payment")
@Component
@Slf4j
public class CustomerPaymentService {

    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private IObjectDataProxyService proxyService;
    @Autowired
    private ObjectDataServiceImpl objectDataService;

    @Autowired
    private CustomerAccountForPaymentProxy customerAccountForPaymentProxy = SpringUtil.getContext().getBean(CustomerAccountForPaymentProxy.class);

    @ReloadableProperty("openPayQrCodeUrl")
    private String openPayQrCodeUrl = "https://www.fxiaoke.com/open/fe-pay/pay-qrcode-apply/index.html?busiNo=%s&busiCode=1009&orderName=%s&amount=%.2f&payerEnterpriseName=客户&alone=1&apiName=PaymentObj";

    private PaymentPlanService paymentPlanService =
            SpringUtil.getContext().getBean(PaymentPlanService.class);

    public String generateOpenPayQrCodeUrl(User user, String customerPaymentId) {
        IObjectData customerPayment = serviceFacade
                .findObjectData(user, customerPaymentId, PaymentObject.CUSTOMER_PAYMENT.getApiName());
        if (customerPayment == null) {
            return "";
        }
        return String
                .format(openPayQrCodeUrl, customerPayment.getId(), customerPayment.getName(),
                        ConverterUtil
                                .convert2Double(customerPayment.get(CustomerPaymentObj.FIELD_PAYMENT_AMOUNT)) * 100);
    }

    public ObjectDataDocument fillWithDetails(RequestContext context, String describeApiName,
                                              ObjectDataDocument data) {
        List<IObjectDescribe> detailDescribeList = serviceFacade
                .findDetailDescribes(context.getTenantId(), describeApiName);
        Map<String, List<IObjectData>> details = serviceFacade
                .findDetailObjectDataList(detailDescribeList, data.toObjectData(), context.getUser());
        data.put("details", details);
        return data;
    }


    private List<IObjectData> findOrderPayments(User user, String customerPaymentId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        Filter filter = new Filter();
        filter.setFieldValues(Lists.newArrayList(customerPaymentId));
        filter.setOperator(Operator.EQ);
        filter.setFieldName(OrderPaymentObj.FIELD_PAYMENT_ID);
        query.setFilters(Lists.newArrayList(filter));
        query.setPermissionType(0);
        return findOrderPayments(user, query);
    }

    private List<IObjectData> findOrderPayments(User user, SearchTemplateQuery query) {
        IObjectDescribe orderPaymentDescribe = serviceFacade
                .findObject(user.getTenantId(), PaymentObject.ORDER_PAYMENT.getApiName());
        int totalCount;
        int offset = 0;
        List<IObjectData> data = new ArrayList<>();
        do {
            query.setLimit(500);
            query.setOffset(offset);
            QueryResult<IObjectData> queryResult = serviceFacade
                    .findBySearchQueryWithDeleted(user, orderPaymentDescribe, query);
            totalCount = queryResult.getTotalNumber();
            offset += 500;
            data.addAll(queryResult.getData());
        } while (offset < totalCount);
        return data;
    }

    public List<IObjectData> findOrderPaymentIsDeletedList(User user, String customerPaymentId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        Filter filter = new Filter();
        filter.setFieldValues(Lists.newArrayList(customerPaymentId));
        filter.setOperator(Operator.EQ);
        filter.setFieldName(OrderPaymentObj.FIELD_PAYMENT_ID);

        Filter delFilter = new Filter();
        delFilter.setFieldValues(Lists.newArrayList("-1"));
        delFilter.setOperator(Operator.EQ);
        delFilter.setFieldName(CrmPackageObjectConstants.FIELD_IS_DELETED);

        query.setFilters(Lists.newArrayList(filter, delFilter));
        query.setPermissionType(0);
        return findOrderPayments(user, query);
    }

    public List<Map> queryOrderPaymentList(ServiceContext context, String customerPaymentId) {
        log.debug("queryOrderPaymentList customerPaymentId:" + customerPaymentId);
        List<Map> dataList = Lists.newArrayList();
        if (StringUtils.isBlank(customerPaymentId)) {
            return dataList;
        }

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.addFilters(Lists.newArrayList(
                FieldUtils.buildFilter(OrderPaymentObj.FIELD_PAYMENT_ID, Lists.newArrayList(customerPaymentId), Operator.IN, 0),
                FieldUtils.buildFilter(OrderPaymentObj.FIELD_IS_DELETED, Lists.newArrayList("0"), Operator.IN, 0))
        );


        query.setLimit(100);
        query.setOffset(0);
        query.setPermissionType(0);
        IObjectDescribe orderPaymentDescribe = serviceFacade
                .findObject(context.getTenantId(), PaymentObject.ORDER_PAYMENT.getApiName());
        while (true) {
            QueryResult<IObjectData> queryResult = serviceFacade
                    .findBySearchQueryWithDeleted(context.getUser(), orderPaymentDescribe, query);
            List<IObjectData> list = queryResult.getData();
            if (CollectionUtils.isEmpty(list)) {
                break;
            }
            dataList
                    .addAll(list.stream().map(x -> ObjectDataExt.of(x).toMap()).collect(Collectors.toList()));
            query.setOffset(query.getOffset() + query.getLimit());
        }
        return dataList;
    }

    public List<Map> queryOrderPaymentListForDelete(ServiceContext context, String customerPaymentId) {
        log.debug("queryOrderPaymentList customerPaymentId:" + customerPaymentId);
        List<Map> dataList = Lists.newArrayList();
        if (StringUtils.isBlank(customerPaymentId)) {
            return dataList;
        }

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.addFilters(Lists.newArrayList(
                FieldUtils.buildFilter(OrderPaymentObj.FIELD_PAYMENT_ID, Lists.newArrayList(customerPaymentId), Operator.IN, 0),
                FieldUtils.buildFilter(OrderPaymentObj.FIELD_IS_DELETED, Lists.newArrayList("0", "1"), Operator.IN, 0))
        );

        query.setLimit(100);
        query.setOffset(0);
        query.setPermissionType(0);
        IObjectDescribe orderPaymentDescribe = serviceFacade
                .findObject(context.getTenantId(), PaymentObject.ORDER_PAYMENT.getApiName());
        while (true) {
            QueryResult<IObjectData> queryResult = serviceFacade
                    .findBySearchQueryWithDeleted(context.getUser(), orderPaymentDescribe, query);
            List<IObjectData> list = queryResult.getData();
            if (CollectionUtils.isEmpty(list)) {
                break;
            }
            dataList
                    .addAll(list.stream().map(x -> ObjectDataExt.of(x).toMap()).collect(Collectors.toList()));
            query.setOffset(query.getOffset() + query.getLimit());
        }
        return dataList;
    }

    public BaseObjectSaveAction.Arg modifyArg(ActionContext action, BaseObjectSaveAction.Arg arg) {
        ObjectDataDocument objectData = arg.getObjectData();
        if (objectData == null) {
            throw new ValidateException(I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_PAYMENTDATANOTNULL));
        }
        Map<String, List<ObjectDataDocument>> details = arg.getDetails();
        if (details == null) {
            throw new ValidateException(I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_ORDERNOTNULL));
        }
        IObjectDescribe describe = serviceFacade
                .findObject(action.getTenantId(), action.getObjectApiName());
        if (describe == null) {
            throw new ValidateException(I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_NOTFINDOBJ));
        }
        if (arg.getObjectData().get(CrmPackageObjectConstants.FIELD_DESCRIBE_ID) == null) {
            objectData.put(CrmPackageObjectConstants.FIELD_DESCRIBE_ID, describe.getId());
            objectData.put(CrmPackageObjectConstants.FIELD_DESCRIBE_API_NAME, describe.getApiName());
            arg.setObjectData(objectData);
        }

        Set<String> set = Sets.newHashSet();
        List<ObjectDataDocument> orderPaymentDocuments = details
                .getOrDefault(PaymentObject.ORDER_PAYMENT.getApiName(), Lists.newArrayList());
        for (ObjectDataDocument od : orderPaymentDocuments) {
            String orderId = (String) od.getOrDefault(OrderPaymentObj.FIELD_ORDER_ID, "");
            if (StringUtils.isNotBlank(orderId)) {
                set.add(orderId);
            }
            if (od.get(CrmPackageObjectConstants.FIELD_DESCRIBE_ID) == null) {
                IObjectDescribe detailObject = serviceFacade
                        .findObject(action.getTenantId(), PaymentObject.ORDER_PAYMENT.getApiName());
                if (detailObject == null) {
                    throw new ValidateException(I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_ORDERPAYMENTNOTEXIST));
                }
                od.put(CrmPackageObjectConstants.FIELD_DESCRIBE_ID, detailObject.getId());
                od.put(CrmPackageObjectConstants.FIELD_DESCRIBE_API_NAME, detailObject.getApiName());
            }
            if (Objects.nonNull(objectData.get(CustomerPaymentObj.FIELD_ACCOUNT_ID))) {
                od.put(OrderPaymentObj.FIELD_ACCOUNT_ID, objectData.get(CustomerPaymentObj.FIELD_ACCOUNT_ID));
            }
            copyOrderPaymentCustomFieldData(describe, objectData, od);
        }

        if (CollectionUtils.isNotEmpty(set)) {
            Joiner joiner = Joiner.on(",");
            String orderContext = joiner.join(set);
            objectData.put(CustomerPaymentObj.FIELD_ORDER_ID, orderContext);
        }
        log.debug("modifyArg->return arg:{}", arg);
        return arg;
    }

    private void copyOrderPaymentCustomFieldData(IObjectDescribe masterDescribe,
                                                 ObjectDataDocument masterData, ObjectDataDocument detailData) {
        List<IFieldDescribe> fields = ObjectDescribeExt.of(masterDescribe).getFieldDescribesSilently()
                .stream()
                .filter(f -> "custom".equals(f.getDefineType()) && Boolean.TRUE.equals(f.isActive()))
                .collect(Collectors.toList());
        for (IFieldDescribe field : fields) {
            Object value = masterData.get(field.getApiName());
            if (null != value) {
                detailData.putIfAbsent(field.getApiName(), value);
            }
        }
    }

    public void checkCustomerAccountInfo(ActionContext action, BaseObjectSaveAction.Arg arg, ObjectAction objectAction) {
        String customerId = (String) arg.getObjectData().get(CustomerPaymentObj.FIELD_ACCOUNT_ID);
        if (StringUtils.isBlank(customerId)) {
            return;
        }
        String option = (String) arg.getObjectData().get(CustomerPaymentObj.FIELD_PAYMENT_METHOD);
        if (StringUtils.isBlank(option)) {
            return;
        }
        ArrayList<String> options = Lists.newArrayList(CustomerPaymentObj.PAYMENT_METHOD_DEPOSIT,
                CustomerPaymentObj.PAYMENT_METHOD_REBATE, CustomerPaymentObj.PAYMENT_METHOD_DNR);
        if (!options.contains(option)) {
            return;
        }

        boolean enable = CustomerAccountUtil.isCustomerAccountEnable(customerAccountForPaymentProxy, action.getUser());
        BigDecimal prepaySum = new BigDecimal(0);
        BigDecimal rebateSum = new BigDecimal(0);
        Map<String, BigDecimal> orderIdRebateAmountMap = Maps.newHashMap();
        if (enable) {
            //统计返利总额，预存款总额
            for (Map.Entry<String, List<ObjectDataDocument>> entry : arg.getDetails().entrySet()) {
                if (PaymentObject.ORDER_PAYMENT.getApiName().equals(entry.getKey())) {
                    if (CollectionUtils.isEmpty(entry.getValue())) {
                        throw new ValidateException(I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_ORDERNOTNULL));
                    }
                    for (ObjectDataDocument od : entry.getValue()) {
                        Object prepay = od.get(OrderPaymentObj.PREPAY);
                        Object rebate = od.get(OrderPaymentObj.REBATE_OUTCOME);
                        Object paymentAmount = od.get(OrderPaymentObj.FIELD_PAYMENT_AMOUNT);
                        if (option.equals(CustomerPaymentObj.PAYMENT_METHOD_DEPOSIT)) {
                            if (paymentAmount == null || StringUtils.isBlank(paymentAmount.toString())) {
                                throw new ValidateException(I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_PREPAYDETAILNOTNULL));
                            }
                            prepay = paymentAmount.toString();
                        }
                        if (option.equals(CustomerPaymentObj.PAYMENT_METHOD_REBATE)) {
                            if (paymentAmount == null || StringUtils.isBlank(paymentAmount.toString())) {
                                throw new ValidateException(I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_REBATEINCOMEDETAILNOTNULL));
                            }
                            rebate = paymentAmount.toString();
                        }
                        if (option.equals(CustomerPaymentObj.PAYMENT_METHOD_DNR)) {
                            if (rebate == null || StringUtils.isBlank(rebate.toString())) {
                                throw new ValidateException(I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_REBATEINCOMEDETAILNOTNULL));
                            }
                            if (prepay == null || StringUtils.isBlank(prepay.toString())) {
                                throw new ValidateException(I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_PREPAYDETAILNOTNULL));
                            }
                        }
                        if (null != prepay && StringUtils.isNotBlank(prepay.toString())) {
                            prepaySum = prepaySum.add(new BigDecimal(prepay.toString()));
                        }
                        if (null != rebate && StringUtils.isNotBlank(rebate.toString())) {
                            rebateSum = rebateSum.add(new BigDecimal(rebate.toString()));
                        }
                    }
                }
            }

            //统计订单返利总额
            for (Map.Entry<String, List<ObjectDataDocument>> entry : arg.getDetails().entrySet()) {
                if (PaymentObject.ORDER_PAYMENT.getApiName().equals(entry.getKey())) {
                    for (ObjectDataDocument od : entry.getValue()) {
                        Object rebate = od.get(OrderPaymentObj.REBATE_OUTCOME);
                        Object paymentAmount = od.get(OrderPaymentObj.FIELD_PAYMENT_AMOUNT);
                        if (option.equals(CustomerPaymentObj.PAYMENT_METHOD_REBATE)) {
                            rebate = paymentAmount.toString();
                        }
                        Object orderId = od.get(OrderPaymentObj.FIELD_ORDER_ID);
                        if (orderId == null || StringUtils.isBlank(orderId.toString())) {
                            throw new ValidateException(I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_SALESIDNOTNULL));
                        }
                        if (null != rebate && StringUtils.isNotBlank(rebate.toString())) {
                            if (orderIdRebateAmountMap.containsKey(orderId.toString())) {
                                orderIdRebateAmountMap.put(orderId.toString(),
                                        orderIdRebateAmountMap.get(orderId.toString()).add(new BigDecimal(rebate.toString())));
                            } else {
                                orderIdRebateAmountMap.put(orderId.toString(), new BigDecimal(rebate.toString()));
                            }
                        }
                    }
                }
            }
        }


        BalanceEnoughModel.Arg paymentArg = BalanceEnoughModel.Arg.builder().customerId(customerId).prepayToPay(prepaySum.doubleValue()).rebateToPay(rebateSum.doubleValue()).build();
        log.debug("customerAccountService->isBalanceEnough Arg:{}", paymentArg);
        BalanceEnoughModel.Result balanceEnoughRst = customerAccountForPaymentProxy.balanceEnough(paymentArg, SFAHeaderUtil.getHeaders(action.getUser()));
        if (balanceEnoughRst.getResult() == null) {
            throw new ValidateException(balanceEnoughRst.getErrMessage());
        }
        log.debug("customerAccountService->isBalanceEnough result:{}", balanceEnoughRst);
        if (!balanceEnoughRst.getResult().isPrepayEnough()) {
            throw new ValidateException(I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_PREPAYDETAILMONEYSHORTAGE));
        }
        if (!balanceEnoughRst.getResult().isRebateEnough()) {
            throw new ValidateException(I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_REBATEINCOMEDETAILMONEYSHORTAGE));
        }

        //校验返利
        if (orderIdRebateAmountMap.isEmpty()) {
            return;
        }

        String lifeStatus = arg.getObjectData().getOrDefault(CustomerPaymentObj.FIELD_LIFE_STATUS, ObjectLifeStatus.INEFFECTIVE.getCode()).toString();
        if (objectAction == ObjectAction.CREATE ||
                (objectAction == ObjectAction.UPDATE && lifeStatus.equals(ObjectLifeStatus.INEFFECTIVE.getCode()))) {

            ValidateRebateUseRuleModel.Arg validateArg = ValidateRebateUseRuleModel.Arg.builder()
                    .customerId(customerId).orderIdRebateAmountMap(orderIdRebateAmountMap).build();
            log.debug("customerAccountService->RebateUseRuleValidate Arg:{}", validateArg);

            ValidateRebateUseRuleModel.Result validateResult = customerAccountForPaymentProxy.validateRebateUseRule(validateArg, SFAHeaderUtil.getHeaders(action.getUser()));
            log.debug("customerAccountService->RebateUseRuleValidate result:{}", validateResult);
            if (validateResult.getResult() == null) {
                throw new ValidateException(validateResult.getErrMessage());
            }
            validateResult.getResult().getOrderIdValidateResultMap().forEach((k, v) -> {
                if (!v.getCanUseRebate()) {
                    IObjectData objectData = serviceFacade.findObjectData(action.getUser(), k, "SalesOrderObj");
                    throw new ValidateException(I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_MAXIMUMAMOUNTOFAVAILABLEREBATE, objectData.getName(), v.getMaxRebateAmountToUse()));
                }
            });
        }
    }

    public void checkCustomerAccountInfoForUpdate(ActionContext actionContext, BaseObjectSaveAction.Arg arg, ObjectAction objectAction,
                                                  List<IObjectData> orderPaymentToAdd, List<IObjectData> orderPaymentToUpdate,
                                                  List<IObjectData> orderPaymentToDelete, List<IObjectData> dbOldDataList) {
        String customerId = (String) arg.getObjectData().get(CustomerPaymentObj.FIELD_ACCOUNT_ID);
        if (StringUtils.isBlank(customerId)) {
            return;
        }
        String option = (String) arg.getObjectData().get(CustomerPaymentObj.FIELD_PAYMENT_METHOD);
        if (StringUtils.isBlank(option)) {
            return;
        }
        ArrayList<String> options = Lists.newArrayList(CustomerPaymentObj.PAYMENT_METHOD_DEPOSIT,
                CustomerPaymentObj.PAYMENT_METHOD_REBATE, CustomerPaymentObj.PAYMENT_METHOD_DNR);
        if (!options.contains(option)) {
            return;
        }
        boolean enable = CustomerAccountUtil.isCustomerAccountEnable(customerAccountForPaymentProxy, actionContext.getUser());
        if (!enable) {
            return;
        }
        BigDecimal prepaySum = new BigDecimal(0);
        BigDecimal rebateSum = new BigDecimal(0);
        Map<String, BigDecimal> orderIdRebateAmountMap = Maps.newHashMap();
        if (orderPaymentToAdd != null) {
            for (IObjectData x : orderPaymentToAdd) {
                Object prepay = x.get(OrderPaymentObj.PREPAY);
                Object rebate = x.get(OrderPaymentObj.REBATE_OUTCOME);
                Object paymentAmount = x.get(OrderPaymentObj.FIELD_PAYMENT_AMOUNT);
                if (option.equals(PAYMENT_METHOD_DEPOSIT)) {
                    if (paymentAmount == null || StringUtils.isBlank(paymentAmount.toString())) {
                        throw new ValidateException(I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_PREPAYDETAILNOTNULL));
                    }
                    prepay = paymentAmount.toString();
                }
                if (option.equals(PAYMENT_METHOD_REBATE)) {
                    if (paymentAmount == null || StringUtils.isBlank(paymentAmount.toString())) {
                        throw new ValidateException(I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_REBATEINCOMEDETAILNOTNULL));
                    }
                    rebate = paymentAmount.toString();
                }
                if (option.equals(PAYMENT_METHOD_DNR)) {
                    if (rebate == null || StringUtils.isBlank(rebate.toString())) {
                        throw new ValidateException(I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_REBATEINCOMEDETAILNOTNULL));
                    }
                    if (prepay == null || StringUtils.isBlank(prepay.toString())) {
                        throw new ValidateException(I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_PREPAYDETAILNOTNULL));
                    }
                }
                if (option.equals(CustomerPaymentObj.PAYMENT_METHOD_REBATE)) {
                    rebate = paymentAmount.toString();
                }
                Object orderId = x.get(OrderPaymentObj.FIELD_ORDER_ID);
                if (orderId == null || StringUtils.isBlank(orderId.toString())) {
                    throw new ValidateException(I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_SALESIDNOTNULL));
                }
                if (null != rebate && StringUtils.isNotBlank(rebate.toString())) {
                    if (orderIdRebateAmountMap.containsKey(orderId.toString())) {
                        orderIdRebateAmountMap.put(orderId.toString(),
                                orderIdRebateAmountMap.get(orderId.toString()).add(new BigDecimal(rebate.toString())));
                    } else {
                        orderIdRebateAmountMap.put(orderId.toString(), new BigDecimal(rebate.toString()));
                    }
                }
                if (null != prepay && StringUtils.isNotBlank(prepay.toString())) {
                    prepaySum = prepaySum.add(new BigDecimal(prepay.toString()));
                }
                if (null != rebate && StringUtils.isNotBlank(rebate.toString())) {
                    rebateSum = rebateSum.add(new BigDecimal(rebate.toString()));
                }
            }
        }
        if (orderPaymentToDelete != null) {
            for (IObjectData x : orderPaymentToDelete) {
                Object prepay = x.get(OrderPaymentObj.PREPAY);
                Object rebate = x.get(OrderPaymentObj.REBATE_OUTCOME);
                Object paymentAmount = x.get(OrderPaymentObj.FIELD_PAYMENT_AMOUNT);
                if (option.equals(PAYMENT_METHOD_DEPOSIT)) {
                    if (paymentAmount == null || StringUtils.isBlank(paymentAmount.toString())) {
                        throw new ValidateException(I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_PREPAYDETAILNOTNULL));
                    }
                    prepay = paymentAmount.toString();
                }
                if (option.equals(PAYMENT_METHOD_REBATE)) {
                    if (paymentAmount == null || StringUtils.isBlank(paymentAmount.toString())) {
                        throw new ValidateException(I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_REBATEINCOMEDETAILNOTNULL));
                    }
                    rebate = paymentAmount.toString();
                }
                if (option.equals(PAYMENT_METHOD_DNR)) {
                    if (rebate == null || StringUtils.isBlank(rebate.toString())) {
                        throw new ValidateException(I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_REBATEINCOMEDETAILNOTNULL));
                    }
                    if (prepay == null || StringUtils.isBlank(prepay.toString())) {
                        throw new ValidateException(I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_PREPAYDETAILNOTNULL));
                    }
                }
                if (option.equals(CustomerPaymentObj.PAYMENT_METHOD_REBATE)) {
                    rebate = paymentAmount.toString();
                }
                Object orderId = x.get(OrderPaymentObj.FIELD_ORDER_ID);
                if (orderId == null || StringUtils.isBlank(orderId.toString())) {
                    throw new ValidateException(I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_SALESIDNOTNULL));
                }
                if (null != rebate && StringUtils.isNotBlank(rebate.toString())) {
                    if (orderIdRebateAmountMap.containsKey(orderId.toString())) {
                        orderIdRebateAmountMap.put(orderId.toString(),
                                orderIdRebateAmountMap.get(orderId.toString()).subtract(new BigDecimal(rebate.toString())));
                    } else {
                        orderIdRebateAmountMap.put(orderId.toString(), new BigDecimal(rebate.toString()));
                    }
                }
                if (null != prepay && StringUtils.isNotBlank(prepay.toString())) {
                    prepaySum = prepaySum.subtract(new BigDecimal(prepay.toString()));
                }
                if (null != rebate && StringUtils.isNotBlank(rebate.toString())) {
                    rebateSum = rebateSum.subtract(new BigDecimal(rebate.toString()));
                }
            }
        }
        if (orderPaymentToUpdate != null) {
            for (IObjectData newPaymentData : orderPaymentToUpdate) {
                Optional<IObjectData> dbOldData = dbOldDataList.stream().filter(o -> o.getId().equals(newPaymentData.getId())).findFirst();
                if (dbOldData.isPresent()) {
                    Object newPrepay = newPaymentData.get(OrderPaymentObj.PREPAY);
                    Object newRebate = newPaymentData.get(OrderPaymentObj.REBATE_OUTCOME);
                    Object newPaymentAmount = newPaymentData.get(OrderPaymentObj.FIELD_PAYMENT_AMOUNT);

                    Object oldPrePay = dbOldData.get().get(OrderPaymentObj.PREPAY);
                    Object oldRebate = dbOldData.get().get(OrderPaymentObj.REBATE_OUTCOME);
                    Object oldPaymentAmount = dbOldData.get().get(OrderPaymentObj.FIELD_PAYMENT_AMOUNT);

                    if (option.equals(PAYMENT_METHOD_DEPOSIT)) {
                        if (newPaymentAmount == null || StringUtils.isBlank(newPaymentAmount.toString())) {
                            throw new ValidateException(I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_PREPAYDETAILNOTNULL));
                        }
                        newPrepay = newPaymentAmount.toString();
                        oldPrePay = oldPaymentAmount.toString();
                    }
                    if (option.equals(PAYMENT_METHOD_REBATE)) {
                        if (newPaymentAmount == null || StringUtils.isBlank(newPaymentAmount.toString())) {
                            throw new ValidateException(I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_REBATEINCOMEDETAILNOTNULL));
                        }
                        newRebate = newPaymentAmount.toString();
                        oldRebate = oldPaymentAmount.toString();
                    }
                    if (option.equals(PAYMENT_METHOD_DNR)) {
                        if (newRebate == null || StringUtils.isBlank(newRebate.toString())) {
                            throw new ValidateException(I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_REBATEINCOMEDETAILNOTNULL));
                        }
                        if (newRebate == null || StringUtils.isBlank(newRebate.toString())) {
                            throw new ValidateException(I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_PREPAYDETAILNOTNULL));
                        }
                    }
                    if (option.equals(CustomerPaymentObj.PAYMENT_METHOD_REBATE)) {
                        newRebate = newPaymentAmount.toString();
                        oldRebate = oldPaymentAmount.toString();
                    }
                    Object orderId = newPaymentData.get(OrderPaymentObj.FIELD_ORDER_ID);
                    if (orderId == null || StringUtils.isBlank(orderId.toString())) {
                        throw new ValidateException(I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_SALESIDNOTNULL));
                    }
                    if (null != newRebate && StringUtils.isNotBlank(newRebate.toString())) {
                        if (orderIdRebateAmountMap.containsKey(orderId.toString())) {
                            orderIdRebateAmountMap.put(orderId.toString(),
                                    orderIdRebateAmountMap.get(orderId.toString()).add(new BigDecimal(newRebate.toString()))
                                            .subtract(new BigDecimal(oldRebate.toString())));
                        } else {
                            orderIdRebateAmountMap.put(orderId.toString(), new BigDecimal(newRebate.toString()));
                        }
                    }
                    if (null != newPrepay && StringUtils.isNotBlank(newPrepay.toString())) {
                        prepaySum = prepaySum.add(new BigDecimal(newPrepay.toString()).subtract(new BigDecimal(oldPrePay.toString())));
                    }
                    if (null != newRebate && StringUtils.isNotBlank(newRebate.toString())) {
                        rebateSum = rebateSum.add(new BigDecimal(newRebate.toString()).subtract(new BigDecimal(oldRebate.toString())));
                    }
                }
            }
        }


        BalanceEnoughModel.Arg paymentArg = BalanceEnoughModel.Arg.builder().customerId(customerId).prepayToPay(prepaySum.doubleValue()).rebateToPay(rebateSum.doubleValue()).build();
        log.debug("customerAccountService->isBalanceEnough Arg:{}", paymentArg);
        BalanceEnoughModel.Result balanceEnoughRst = customerAccountForPaymentProxy.balanceEnough(paymentArg, SFAHeaderUtil.getHeaders(actionContext.getUser()));
        if (balanceEnoughRst.getResult() == null) {
            throw new ValidateException(balanceEnoughRst.getErrMessage());
        }
        log.debug("customerAccountService->isBalanceEnough result:{}", balanceEnoughRst);
        if (!balanceEnoughRst.getResult().isPrepayEnough()) {
            throw new ValidateException(I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_PREPAYDETAILMONEYSHORTAGE));
        }
        if (!balanceEnoughRst.getResult().isRebateEnough()) {
            throw new ValidateException(I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_REBATEINCOMEDETAILMONEYSHORTAGE));
        }

        //校验返利
        if (orderIdRebateAmountMap.isEmpty()) {
            return;
        }

        String lifeStatus = arg.getObjectData().getOrDefault(CustomerPaymentObj.FIELD_LIFE_STATUS, ObjectLifeStatus.INEFFECTIVE.getCode()).toString();
        if (objectAction == ObjectAction.CREATE ||
                (objectAction == ObjectAction.UPDATE && lifeStatus.equals(ObjectLifeStatus.INEFFECTIVE.getCode()))) {

            ValidateRebateUseRuleModel.Arg validateArg = ValidateRebateUseRuleModel.Arg.builder()
                    .customerId(customerId).orderIdRebateAmountMap(orderIdRebateAmountMap).build();
            log.debug("customerAccountService->RebateUseRuleValidate Arg:{}", validateArg);

            ValidateRebateUseRuleModel.Result validateResult = customerAccountForPaymentProxy.validateRebateUseRule(validateArg, SFAHeaderUtil.getHeaders(actionContext.getUser()));
            log.debug("customerAccountService->RebateUseRuleValidate result:{}", validateResult);
            if (validateResult.getResult() == null) {
                throw new ValidateException(validateResult.getErrMessage());
            }
            validateResult.getResult().getOrderIdValidateResultMap().forEach((k, v) -> {
                if (!v.getCanUseRebate()) {
                    IObjectData objectData = serviceFacade.findObjectData(actionContext.getUser(), k, "SalesOrderObj");
                    throw new ValidateException(I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_MAXIMUMAMOUNTOFAVAILABLEREBATE, objectData.getName(), v.getMaxRebateAmountToUse()));
                }
            });
        }
    }


    public BulkDeleteModel.Arg buildBulkDeleteSyncAccountInfoArg(ActionContext context,
                                                                 StandardBulkDeleteAction.Arg arg) {
        Map<String, List<String>> map = Maps.newHashMap();
        List<IObjectData> data = serviceFacade
                .findObjectDataByIdsIncludeDeleted(context.getUser(), arg.getIdList(),
                        arg.getDescribeApiName());
        for (IObjectData x : data) {
            String cpId = (String) x.get(OrderPaymentObj.FIELD_PAYMENT_ID);
            if (StringUtils.isNotEmpty(cpId)) {
                List<String> orderIds = map.get(cpId);
                if (CollectionUtils.isEmpty(orderIds)) {
                    orderIds = Lists.newArrayList();
                }
                String id = (String) x.get(OrderPaymentObj.FIELD_ID);
                orderIds.add(id);
                map.put(cpId, orderIds);
            }
        }
        BulkDeleteModel.Arg deleteArg = BulkDeleteModel.Arg.builder().build();
        deleteArg.setOrderPaymentMap(map);
        return deleteArg;
    }


    private ObjectDataDocument getPrepayData(IObjectData orderData, IObjectData paymentData) {
        ObjectDataDocument prepayData = new ObjectDataDocument();
        prepayData.put(CrmPackageObjectConstants.FIELD_RECORD_TYPE,
                PrepayDetailConstants.RecordType.OutcomeRecordType.apiName);
        prepayData.put(PrepayDetailConstants.Field.Customer.apiName,
                orderData.get(OrderPaymentObj.FIELD_ACCOUNT_ID).toString());
        prepayData.put(PrepayDetailConstants.Field.OutcomeType.apiName, "1");
        prepayData.put(PrepayDetailConstants.Field.TransactionTime.apiName,
                Long.valueOf(paymentData.get(CustomerPaymentObj.FIELD_PAYMENT_TIME).toString()));
        prepayData.put(PrepayDetailConstants.Field.Payment.apiName,
                paymentData.get(CustomerPaymentObj.FIELD_ID).toString());
        prepayData.put(CrmPackageObjectConstants.FIELD_LIFE_STATUS,
                paymentData.get(CustomerPaymentObj.FIELD_LIFE_STATUS).toString());
        return prepayData;
    }

    private ObjectDataDocument getRebateOutcomeData(IObjectData orderData, IObjectData paymentData) {
        ObjectDataDocument rebateData = new ObjectDataDocument();
        rebateData.put(RebateOutcomeDetailConstants.Field.Customer.apiName,
                orderData.get(OrderPaymentObj.FIELD_ACCOUNT_ID).toString());
        rebateData.put(RebateOutcomeDetailConstants.Field.TransactionTime.apiName,
                Long.valueOf(paymentData.get(CustomerPaymentObj.FIELD_PAYMENT_TIME).toString()));
        rebateData.put(RebateOutcomeDetailConstants.Field.Payment.apiName,
                paymentData.get(CustomerPaymentObj.FIELD_ID).toString());
        rebateData.put(CrmPackageObjectConstants.FIELD_LIFE_STATUS,
                paymentData.get(CustomerPaymentObj.FIELD_LIFE_STATUS).toString());
        return rebateData;
    }

    @POST
    @ServiceMethod("calculate_customers_payment_money")
    public Map<String, BigDecimal> calculateCustomersPaymentMoney(ServiceContext context,
                                                                  List<String> ids) {
        log.debug("calculateCustomersPaymentMoney ids:{}", ids);
        Map<String, BigDecimal> map = Maps.newHashMap();
        if (CollectionUtils.isEmpty(ids)) {
            return map;
        }
        ids.forEach(x -> map.put(x, BigDecimal.ZERO));

        // TODO 该字段是计算字段,能否通过 sql 一次性 sum 出来结果,年后修改

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.addFilters(Lists.newArrayList(
                FieldUtils.buildFilter(CustomerPaymentObj.FIELD_ACCOUNT_ID, ids, Operator.IN, 0),
                FieldUtils.buildFilter(CustomerPaymentObj.FIELD_LIFE_STATUS,
                        Lists.newArrayList("normal", "in_change"), Operator.IN, 0)));
        query.setLimit(100);
        query.setOffset(0);
        query.setPermissionType(0);
        while (true) {
            QueryResult<IObjectData> queryResult = serviceFacade
                    .findBySearchQuery(context.getUser(), PaymentObject.CUSTOMER_PAYMENT.getApiName(), query);
            List<IObjectData> list = queryResult.getData();
            if (CollectionUtils.isEmpty(list)) {
                break;
            }
            list.forEach(x -> {
                Object o = x.get(CustomerPaymentObj.FIELD_PAYMENT_AMOUNT);
                if (o == null) {
                    return;
                }
                BigDecimal amount = BigDecimal.valueOf(Double.valueOf(o.toString()));
                String accountId = x.get(CustomerPaymentObj.FIELD_ACCOUNT_ID, String.class);
                map.put(accountId, map.getOrDefault(accountId, BigDecimal.ZERO).add(amount));
            });
            query.setOffset(query.getOffset() + query.getLimit());
        }
        return map;
    }

    @POST
    @ServiceMethod("get_order_rebate_money")
    public Map<String, BigDecimal> getOrderRebateMoney(ServiceContext context,
                                                       List<String> orderIds) {
        //获取订单下所有符合条件的回款明细
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        filters.add(FieldUtils.buildFilter(OrderPaymentObj.FIELD_ORDER_ID, orderIds, Operator.IN, 0));
        filters.add(
                FieldUtils.buildFilter(OrderPaymentObj.FIELD_PAYMENT_METHOD, Lists.newArrayList(
                        CustomerPaymentObj.PAYMENT_METHOD_DNR,
                        CustomerPaymentObj.PAYMENT_METHOD_REBATE
                ), Operator.IN, 0));
        filters.add(
                FieldUtils.buildFilter(OrderPaymentObj.FIELD_LIFE_STATUS, Lists.newArrayList(
                        UdobjConstants.LIFE_STATUS_VALUE_UNDER_REVIEW,
                        UdobjConstants.LIFE_STATUS_VALUE_NORMAL,
                        UdobjConstants.LIFE_STATUS_VALUE_IN_CHANGE
                ), Operator.IN, 0));
        query.addFilters(filters);
        query.setPermissionType(0);
        query.setLimit(100);
        query.setOffset(0);

        IActionContext newContext = new com.facishare.paas.metadata.api.action.ActionContext();
        newContext.setDoCalculate(false);
        newContext.setEnterpriseId(context.getTenantId());
        newContext.setUserId(context.getUser().getUserId());

        Map<String, String> orderPaymentIdOrderIdMap = Maps.newHashMap(); //回款明细id  订单id map关系
        Map<String, BigDecimal> orderPaymentIdRebateMap = Maps.newHashMap(); //回款明细id 返利金额
        Set<String> orderPaymentIdDnrSet = Sets.newHashSet();  //回款明细id

        while (true) {
            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(newContext, PaymentObject.ORDER_PAYMENT.getApiName(), query);
            List<IObjectData> list = queryResult.getData();
            if (CollectionUtils.isEmpty(list)) {
                break;
            }
            for (IObjectData objectData : list) {
                Object method = objectData.get(OrderPaymentObj.FIELD_PAYMENT_METHOD);
                Object amountObj = objectData.get(CustomerPaymentObj.FIELD_PAYMENT_AMOUNT);
                if (amountObj != null) {
                    BigDecimal amount = BigDecimal.valueOf(Double.valueOf(amountObj.toString()));
                    String orderId = objectData.get(CustomerPaymentObj.FIELD_ORDER_ID, String.class);
                    String id = objectData.get(CustomerPaymentObj.FIELD_ID, String.class);
                    orderPaymentIdOrderIdMap.put(id, orderId);
                    //返利
                    if (method != null && method.toString().equals(CustomerPaymentObj.PAYMENT_METHOD_REBATE)) {
                        if (orderPaymentIdRebateMap.containsKey(id)) {
                            orderPaymentIdRebateMap.put(id, orderPaymentIdRebateMap.get(id).add(amount));
                        } else {
                            orderPaymentIdRebateMap.put(id, amount);
                        }
                    }
                    //预存款+返利
                    if (method != null && method.toString().equals(CustomerPaymentObj.PAYMENT_METHOD_DNR)) {
                        orderPaymentIdDnrSet.add(id);
                    }
                }
            }
            query.setOffset(query.getOffset() + query.getLimit());
        }

        //获取预存款+返利中的返利金额

        BatchGetRebateAmountByOrderPaymentIdsModel.Arg arg = BatchGetRebateAmountByOrderPaymentIdsModel.Arg.builder().build();
        arg.setOrderPaymentIds(Lists.newArrayList(orderPaymentIdDnrSet));
        BatchGetRebateAmountByOrderPaymentIdsModel.Result batchResult =
                customerAccountForPaymentProxy.batchGetRebateAmountByOrderPaymentIds(arg, SFAHeaderUtil.getHeaders(context.getUser()));
        if (batchResult != null && batchResult.getResult().getOrderPaymentIdRebateAmountMap() != null) {
            orderPaymentIdRebateMap.putAll(batchResult.getResult().getOrderPaymentIdRebateAmountMap());
        }

        Map<String, BigDecimal> result = Maps.newHashMap();
        orderPaymentIdRebateMap.forEach((k, v) -> {
            if (orderPaymentIdOrderIdMap.containsKey(k)) {
                String orderId = orderPaymentIdOrderIdMap.get(k);
                if (result.containsKey(orderId)) {
                    result.put(orderId, v.add(result.get(orderId)));
                } else {
                    result.put(orderId, v);
                }
            }
        });

        return result;
    }


    @POST
    @ServiceMethod("merge_customer_payment")
    @Transactional
    public boolean mergeCustomerPayment(Args.MergeCustomerPayment arg, ServiceContext context) {
        log.debug("mergeCustomerPayment arg:{}", arg);
        String sourceIds = "";
        for (String x : arg.getSourceCustomerId()) {
            sourceIds += String.format("'%s',", SqlEscaper.pg_escape(x));
        }
        sourceIds = sourceIds.substring(0, sourceIds.length() - 1);
        String cSql = "UPDATE payment_customer SET account_id = '" + SqlEscaper.pg_escape(arg.getTargetCustomerId())
                + "' WHERE account_id IN (" + sourceIds + ")";
        String oSql = "UPDATE payment_order SET account_id = '" + SqlEscaper.pg_escape(arg.getTargetCustomerId())
                + "' WHERE account_id IN (" + sourceIds + ")";
        String pSql = "UPDATE payment_plan SET account_id = '" + SqlEscaper.pg_escape(arg.getTargetCustomerId())
                + "' WHERE account_id IN (" + sourceIds + ")";
        try {
            objectDataService
                    .findBySql(cSql, context.getTenantId(), PaymentObject.CUSTOMER_PAYMENT.getApiName());
            objectDataService
                    .findBySql(oSql, context.getTenantId(), PaymentObject.ORDER_PAYMENT.getApiName());
            objectDataService
                    .findBySql(pSql, context.getTenantId(), PaymentObject.PAYMENT_PLAN.getApiName());
        } catch (MetadataServiceException e) {
            log.debug("mergeCustomerPayment fail:" + e.toString());
            return false;
        }
        return true;
    }


    public void updateOrderPayment(IObjectData objectData, User user) {
        if (objectData == null) {
            return;
        }
        List<IObjectData> detailObjectDataList = serviceFacade
                .findDetailIncludeInvalidObjectDataListIgnoreFormula(objectData, user);
        if (CollectionUtils.isEmpty(detailObjectDataList)) {
            log.info(String.format("%s detailObjectDataList is null", objectData.getId()));
            return;
        }
        //更新回款明细lookup的所有回款计划的状态
        Set<String> planIds = Sets.newHashSet();
        detailObjectDataList.forEach(x -> {
            if (x.get(OrderPaymentObj.FIELD_PAYMENT_PLAN_ID) != null) {
                planIds.add(x.get(OrderPaymentObj.FIELD_PAYMENT_PLAN_ID).toString());
            }
        });
        List<IObjectData> playObjectDataList = serviceFacade.findObjectDataByIds(user.getTenantId(), new ArrayList<>(planIds), PaymentObject.PAYMENT_PLAN.getApiName());
        log.info("updatePaymentPlanStatus %s", objectData.getId());
        paymentPlanService.updatePaymentPlanStatus(playObjectDataList, user);
    }

    public List<ObjectDataDocument> parseOrderNames(User user, List<ObjectDataDocument> documents) {
        log.info("CustomerPaymentService parseOrderNames start,tenantId {}", user.getTenantId());
        try {
            StringBuilder ids = new StringBuilder();
            documents.forEach(d -> ids.append(ConverterUtil.convert2String(d.get("order_id"))).append(","));
            List<String> idList = Arrays.stream(ids.toString().split(",")).distinct()
                    .collect(Collectors.toList());
            Map<String, String> orderNames = new HashMap<>();
            List<INameCache> nameCaches = proxyService
                    .findRecordName(ActionContextExt.of(user).getContext(), "SalesOrderObj", idList);
            nameCaches.forEach(n -> orderNames.put(n.getId(),
                    org.apache.commons.lang.StringUtils.isNotBlank(n.getName()) ? n.getName()
                            : n.getId()));
            documents.forEach(d -> {
                String orderId = ConverterUtil.convert2String(d.get("order_id"));
                if (org.apache.commons.lang.StringUtils.isNotBlank(orderId)) {
                    Set<String> orderIds = Sets.newHashSet();
                    orderIds.addAll(Arrays.asList(orderId.split(",")));
                    String updatedOrderId = orderIds.stream()
                            .map(o -> orderNames.getOrDefault(o, o)).collect(Collectors.joining(","));
                    d.put("order_id", updatedOrderId);
                }
                d.put("order_data_id", orderId);
            });
            log.info("CustomerPaymentService parseOrderNames end,tenantId {}", user.getTenantId());
            return documents;
        } catch (MetadataServiceException ex) {
            log.error("CustomerPaymentExportAction parseOrderNames error,tenantId {}", user.getTenantId(), ex);
            return documents;
        }
    }

    public List<ObjectDataDocument> parseDateTime(User user, IObjectDescribe objectDescribe, List<ObjectDataDocument> documents) {
        log.info("CustomerPaymentService parseDateTime start,tanantId {},apiName {}", user.getTenantId(), objectDescribe.getApiName());
        Map<String, IFieldDescribe> fieldDescribeMap = ObjectDescribeExt.of(objectDescribe).getFieldDescribeMap();
        for (ObjectDataDocument dataDocument : documents) {
            dataDocument.forEach((f, v) -> {
                IFieldDescribe fieldDescribe = fieldDescribeMap.get(f);
                if (fieldDescribe != null) {
                    if (fieldDescribe.getType().equals("date") || fieldDescribe.getType().equals("date_time") || fieldDescribe.getType().equals("time")) {
                        if (v != null && v.toString().equals("946656000000")) {
                            dataDocument.put(f, null);
                        }
                    }
                    if (fieldDescribe.getType().equals("quote")) {
                        QuoteFieldDescribe describe = (QuoteFieldDescribe) fieldDescribe;
                        if (describe.getQuoteFieldType().equals("date") || describe.getQuoteFieldType().equals("date_time") || describe.getQuoteFieldType().equals("time")) {
                            if (v != null && v.toString().equals("946656000000")) {
                                dataDocument.put(f, null);
                            }
                        }
                    }
                }
            });
        }
        log.info("CustomerPaymentService parseDateTime end,tanantId {},apiName {}", user.getTenantId(), objectDescribe.getApiName());
        return documents;
    }

    public void deletePaymentByEditPayment(ActionContext actionContext, IObjectData data) {

        String method = (String) data.get(CustomerPaymentObj.FIELD_PAYMENT_METHOD);
        ArrayList<String> options = Lists.newArrayList(CustomerPaymentObj.PAYMENT_METHOD_DEPOSIT,
                CustomerPaymentObj.PAYMENT_METHOD_REBATE, CustomerPaymentObj.PAYMENT_METHOD_DNR);
        if (!options.contains(method)) {
            return;
        }

        List<IObjectData> deletedList = findOrderPaymentIsDeletedList(actionContext.getUser(),
                data.getId());
    }

    public void sendDHTMq(User user, String status, String paymentId) {
        List<IObjectData> orderPayments = findOrderPayments(user, paymentId);
        if (orderPayments.isEmpty()) {
            return;
        }
        AutoConfMQProducer sender = SpringUtil.getContext().getBean("paymentDHTMQSender", AutoConfMQProducer.class);
        PaymentDhtMq dhtMq = PaymentDhtMq
                .builder()
                .status(status).tenantId(user.getTenantId())
                .paymentOrderIds(orderPayments.stream().map(IObjectData::getId).collect(Collectors.toList()))
                .build();
        try {
            DefaultTopicMessage defaultTopicMessage = new DefaultTopicMessage(JSON.toJSONBytes(dhtMq));
            sender.send(defaultTopicMessage);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    @Transactional
    public void addSyncAccountInfo(RequestContext requestContext, IObjectData objectData, IObjectData dbObjectData) {

        Object paymentTerm = Optional.ofNullable(objectData.get(FIELD_PAYMENT_METHOD)).orElse(dbObjectData.get(FIELD_PAYMENT_METHOD));
        //如果是预存款、返利、预存款+返利，调用深研的接口

        UpdateByCustomFunctionModel.Arg updateArg = UpdateByCustomFunctionModel.Arg.builder().build();
        updateArg.setPaymentId(dbObjectData.get(FIELD_PAYMENT_ID).toString());
        updateArg.setLifeStatus(Optional.ofNullable(objectData.get(FIELD_LIFE_STATUS)).orElse(dbObjectData.get(FIELD_LIFE_STATUS)).toString());
        if (paymentTerm != null && paymentTerm.toString().equals(PAYMENT_METHOD_DEPOSIT)) {

            ObjectDataDocument prepayData = getPrepayOrRebateData(OrderPaymentObj.PREPAY, dbObjectData, objectData);
            updateArg.setPrepayDetailData(prepayData);
        }
        if (paymentTerm != null && paymentTerm.toString().equals(PAYMENT_METHOD_REBATE)) {

            ObjectDataDocument prepayData = getPrepayOrRebateData(OrderPaymentObj.REBATE_OUTCOME, dbObjectData, objectData);
            updateArg.setRebateOutcomeDetailData(prepayData);
        }
        if (paymentTerm != null && paymentTerm.toString().equals(PAYMENT_METHOD_DNR)) {
            updateArg.setPrepayDetailData(getPrepayOrRebateData(OrderPaymentObj.PREPAY, dbObjectData, objectData));
            updateArg.setRebateOutcomeDetailData(getPrepayOrRebateData(OrderPaymentObj.REBATE_OUTCOME, dbObjectData, objectData));
        }
        if (MapUtils.isNotEmpty(updateArg.getPrepayDetailData()) || MapUtils.isNotEmpty(updateArg.getRebateOutcomeDetailData())) {
            customerAccountForPaymentProxy.updateByCustomFunction(updateArg, SFAHeaderUtil.getHeaders(requestContext.getUser()));
        }
        Map<String, Object> fieldMap = ObjectDataExt.of(objectData).toMap();
        serviceFacade.updateWithMap(requestContext.getUser(), objectData, fieldMap);
    }

    @NotNull
    private ObjectDataDocument getPrepayOrRebateData(String amountApiName, IObjectData dbObjectData, IObjectData objectData) {
        ObjectDataDocument data = new ObjectDataDocument();
        data.put("order_payment_id", dbObjectData.getId());
        data.put("amount", Optional.ofNullable(objectData.get(amountApiName)).orElse(dbObjectData.get(amountApiName)));
        return data;
    }
}
