package com.facishare.crm.payment.action;

import com.facishare.crm.payment.proxy.CustomerAccountForPaymentProxy;
import com.facishare.crm.payment.proxy.model.RecoverValidateModel;
import com.facishare.crm.payment.service.CustomerPaymentService;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardBulkRecoverAction;
import com.facishare.paas.appframework.metadata.FilterExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.util.SFAHeaderUtil;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.DELETE_STATUS;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.A;
import org.elasticsearch.common.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class CustomerPaymentBulkRecoverAction extends StandardBulkRecoverAction {

    private CustomerPaymentService customerPaymentService =
            SpringUtil.getContext().getBean(CustomerPaymentService.class);


    CustomerAccountForPaymentProxy customerAccountForPaymentProxy = SpringUtil.getContext().getBean(CustomerAccountForPaymentProxy.class);

    @Override
    protected void before(StandardBulkRecoverAction.Arg arg) {
        super.before(arg);
        RecoverValidateModel.Result result = customerAccountForPaymentProxy.checkRevertPayment(
                RecoverValidateModel.Arg.builder().paymentOrderPaymentIdMap(getOrderPaymentDataList(dataList)).build(),
                SFAHeaderUtil.getHeaders(actionContext.getUser())
        );
        if (!result.getErrCode().equals(0)) {
            throw new ValidateException(result.getErrMessage());
        }
        if (result.getResult() != null && !result.getResult().getValidateResult()) {
            throw new ValidateException(result.getResult().getMessage());
        }
    }

    @Override
    @Transactional
    public Result doAct(Arg arg) {
        Result result = super.doAct(arg);
        log.debug("CustomerPaymentBulkRecoverAction doAct arg:{} result: {}", arg, result);
        //customerPaymentService.bulkRecoverSyncAccountInfo(actionContext, arg, result);

        if (result.getSuccess()) {
            for (String id : arg.getIdList()) {
                IObjectData data = serviceFacade.findObjectData(actionContext.getUser(), id, objectDescribe);
                customerPaymentService.updateOrderPayment(data, actionContext.getUser());
            }
        }
        return result;
    }

    private Map<String, List<String>> getOrderPaymentDataList(List<IObjectData> masterDataList) {
        Map<String, List<String>> orderPaymentIdMap = Maps.newHashMap();
        IObjectDescribe orderPaymentDescribe = serviceFacade.findObject(actionContext.getTenantId(),"OrderPaymentObj");
        List<IFilter> filters = Lists.newArrayList(new IFilter[]{FilterExt.of(Operator.EQ, "is_deleted", Lists.newArrayList(new String[]{String.valueOf(DELETE_STATUS.INVALID.getValue()), String.valueOf(DELETE_STATUS.NORMAL.getValue())})).getFilter()});
        List<String> masterDataIds = masterDataList.stream().map((x) -> {
            return x.getId();
        }).collect(Collectors.toList());
        try {
            QueryResult<IObjectData> detailDataResult = this.serviceFacade.findDetailObjectDataBatchWithPageIncludeInvalid(this.actionContext.getUser(), this.objectDescribe.getApiName(), masterDataIds, orderPaymentDescribe, 1, 1000, filters);
            int totalPage = SearchTemplateQueryExt.calculateTotalPage(detailDataResult.getTotalNumber(), 1000);

            for(int pageNum = 1; pageNum <= totalPage; ++pageNum) {
                if (pageNum > 1) {
                    detailDataResult = this.serviceFacade.findDetailObjectDataBatchWithPageIncludeInvalid(this.actionContext.getUser(), this.objectDescribe.getApiName(), masterDataIds, orderPaymentDescribe, pageNum, 1000, filters);
                }
                List<IObjectData> dataList = (List) CollectionUtils.nullToEmpty(detailDataResult.getData()).stream().filter((data) -> {
                    return ObjectDataExt.of(data).isInvalid();
                }).collect(Collectors.toList());
                for (IObjectData x : dataList) {
                    String paymentId = x.get("payment_id", String.class);
                    if (Strings.isNullOrEmpty(paymentId)) {
                        continue;
                    }
                    if(orderPaymentIdMap.get(paymentId) != null){
                        List<String> orderPaymentIds = orderPaymentIdMap.get(paymentId);
                        orderPaymentIds.add(x.getId());
                        orderPaymentIdMap.put(paymentId, orderPaymentIds);
                    }else{
                        orderPaymentIdMap.put(paymentId, Lists.newArrayList(x.getId()));
                    }
                }
            }
        } catch (MetaDataBusinessException var9) {
            log.warn("getOrderPaymentDataList error,user:{},masterApiName:{},masterDataId:{}", new Object[]{this.actionContext.getUser(), this.objectDescribe.getApiName(), masterDataIds, var9});
        } catch (Exception var10) {
            log.error("getOrderPaymentDataList error,user:{},masterApiName:{},masterDataId:{}", new Object[]{this.actionContext.getUser(), this.objectDescribe.getApiName(), masterDataIds, var10});
        }
        return orderPaymentIdMap;
    }
}

