package com.facishare.crm.payment.controller;

import com.facishare.crm.payment.constant.PaymentPlanObj;
import com.facishare.crm.payment.service.PaymentPlanService;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.util.SpringUtil;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.List;

import static com.facishare.crm.payment.utils.PaymentPlanUtils.getPaymentPlanStatus;

/**
 * <AUTHOR>
 * @date 2018/10/29 16:52
 */
public class PaymentPlanRelatedListController extends StandardRelatedListController {
    private PaymentPlanService paymentPlanService =
            SpringUtil.getContext().getBean(PaymentPlanService.class);
    @Override
    protected Result doService(Arg arg) {
        Result result = super.doService(arg);
        return result;
    }
}
