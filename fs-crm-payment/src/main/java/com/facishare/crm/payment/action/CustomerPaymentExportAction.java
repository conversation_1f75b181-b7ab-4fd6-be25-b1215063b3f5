package com.facishare.crm.payment.action;

import com.facishare.crm.payment.PaymentObject;
import com.facishare.crm.payment.service.CustomerPaymentService;
import com.facishare.crm.payment.utils.SOPaymentI18NKeyUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardExportAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.DecimalFormat;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class CustomerPaymentExportAction extends StandardExportAction {

    private static final Logger LOGGER = LoggerFactory.getLogger(CustomerPaymentExportAction.class);

    private IObjectDescribe orderPaymentDescribe;

    private CustomerPaymentService service = SpringUtil.getContext()
            .getBean(CustomerPaymentService.class);

    private List<String> REMOVE_FIELDS = Lists.newArrayList("approve_employee_id", "approved_employee_id");

    @Override
    protected Result doAct(Arg arg) {
        LOGGER.debug("CustomerPayment export arg: {}", arg);
        return super.doAct(arg);
    }

    @Override
    protected int validateThrottle() {
        orderPaymentDescribe = serviceFacade
                .findObject(actionContext.getTenantId(), PaymentObject.ORDER_PAYMENT.getApiName());
        int count = super.validateThrottle();
        searchQuery.setLimit(1);
        QueryResult<IObjectData> orderPaymentData = findObjectByQuery(actionContext.getUser(),
                orderPaymentDescribe, searchQuery);
        int orderPaymentCount = orderPaymentData.getTotalNumber();
        if (count + orderPaymentCount > getExportRowsThrottle()) {
            throw new ValidateException(I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_EXPORT_EXCEED_THROTTLE,
                    new DecimalFormat(",###").format(getExportRowsThrottle())));
        }
        return count + orderPaymentCount;
    }

    @Override
    protected void initDescribeMapToExport() {
        super.initDescribeMapToExport();
        describeMap.put(orderPaymentDescribe.getApiName(), orderPaymentDescribe);
    }

    @Override
    protected Map<String, List<IObjectData>> getRelatedDataMap(QueryResult<IObjectData> queryResult) {
        Map<String, List<IObjectData>> dataMap = Maps.newHashMap();
        List<IObjectData> orderPaymentList = serviceFacade.findDetailObjectDataList(actionContext.getUser(), orderPaymentDescribe, queryResult.getData());
        dataMap.put(orderPaymentDescribe.getApiName(), orderPaymentList);

        return dataMap;
    }

    @Override
    protected void fillDataList(User user, IObjectDescribe describe, List<IFieldDescribe> fields, List<IObjectData> dataList) {
        if (objectDescribe.getApiName().equals(describe.getApiName())) {
            parseOrderNames(dataList);
        }
        parseDateTime(describe, dataList);
        super.fillDataList(user, describe, fields, dataList);
    }

    private void parseOrderNames(List<IObjectData> data) {
        service.parseOrderNames(actionContext.getUser(), data.stream().map(
                ObjectDataDocument::of).collect(Collectors.toList()));
    }

    private void parseDateTime(IObjectDescribe objectDescribe, List<IObjectData> data) {
        service.parseDateTime(actionContext.getUser(), objectDescribe, data.stream().map(
                ObjectDataDocument::of).collect(Collectors.toList()));
    }

    protected List<IFieldDescribe> findFields(String describeApiName, String recordType) {
        log.debug("CustomerPaymentExportAction findFields start, tenantId {},describeApiName {} ,recordType {}", actionContext.getTenantId(), describeApiName, recordType);
        List<IFieldDescribe> fields = super.findFields(describeApiName, recordType);
        fields.removeIf(k -> REMOVE_FIELDS.contains(k.getApiName()));
        log.debug("CustomerPaymentExportAction findFields ,tenantId {},describeApiName {} ,recordType {},fields{}", actionContext.getTenantId(), describeApiName, recordType, fields);
        return fields;
    }

}
