package com.facishare.crm.payment.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.payment.PaymentObject;
import com.facishare.crm.payment.constant.CustomerPaymentObj;
import com.facishare.crm.payment.constant.OrderPaymentObj;
import com.facishare.crm.payment.service.CustomerPaymentService;
import com.facishare.crm.payment.utils.SOPaymentI18NKeyUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.common.util.ParallelUtils.ParallelTask;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.appframework.flow.ApprovalFlowStartResult;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.Strings;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.facishare.crm.payment.constant.PaymentGrayConf.skipSetPaymentOrderLifeStatus;


@Slf4j
public class CustomerPaymentAddAction extends StandardAddAction {

  private CustomerPaymentService customerPaymentService =
          SpringUtil.getContext().getBean(CustomerPaymentService.class);

  private Map<String, List<JSONObject>> details;

  @Override
  protected void before(Arg arg) {
    validateDuplicateDetails(arg);
    log.debug("CustomerPaymentAddAction before arg:{}", arg);
    arg = customerPaymentService.modifyArg(actionContext, arg);
    arg.getObjectData().put("submit_time", System.currentTimeMillis());
    details = (Map<String, List<JSONObject>>) JSON.parse(JSON.toJSONString(arg.getDetails()));
    super.before(arg);
    customerPaymentService.checkCustomerAccountInfo(actionContext, arg,ObjectAction.CREATE);
  }

  private void validateDuplicateDetails(Arg arg) {
    if (null == arg) {
      throw new ValidateException(I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_PARAMETERERROR));
    }
    if (null == arg.getDetails() || arg.getDetails().isEmpty()) {
      throw new ValidateException(I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_ORDERPAYMENTNOTNULL));
    }
    List<ObjectDataDocument> details =
            arg.getDetails().get(PaymentObject.ORDER_PAYMENT.getApiName());
    if (CollectionUtils.isEmpty(details)) {
      throw new ValidateException(I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_ORDERPAYMENTNOTNULL));
    }
    List<String> keys = new ArrayList<>();
    details.forEach(d -> {
      if(ObjectUtils.isEmpty(d.get(OrderPaymentObj.FIELD_PAYMENT_AMOUNT))){
        throw new ValidateException(I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_NOTFINDORDERPAYYMENTMONEY));
      }
      BigDecimal paymentAmount =
              new BigDecimal(d.get(OrderPaymentObj.FIELD_PAYMENT_AMOUNT).toString());
      if (paymentAmount.doubleValue() < 0) {
        throw new ValidateException(I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_NOTFINDORDERPAYYMENTMONEYGT0));
      }
      String orderId = String
              .valueOf(d.getOrDefault(OrderPaymentObj.FIELD_ORDER_ID, OrderPaymentObj.FIELD_ORDER_ID));
      String planId = String.valueOf(d.getOrDefault(OrderPaymentObj.FIELD_PAYMENT_PLAN_ID, ""));
      planId = planId == "null" ? "" : planId;
      //7.0.5 新需求 回款计划id 为空时候不校验
      if(!Strings.isNullOrEmpty(planId)){
        String key = orderId + planId;
        if (keys.contains(key)) {
          throw new ValidateException(I18N.text(SOPaymentI18NKeyUtils.SO_PAYMENT_SALESIDNPAYMENTPLANID));
        }
        keys.add(key);
      }
    });
  }

  @Override
  protected Result doAct(Arg arg) {
    Result result = super.doAct(arg);
    log.debug("CustomerPaymentAddAction doAct arg:{} result: {}", arg, result);
    return result;
  }

  @Override
  protected Result after(Arg arg, Result result) {
    result = super.after(arg, result);

    if (!startApprovalFlowResult.containsKey(objectData.getId())
            || startApprovalFlowResult.get(objectData.getId()) == ApprovalFlowStartResult.APPROVAL_NOT_EXIST) {
      customerPaymentService.sendDHTMq(actionContext.getUser(), "no_flow", objectData.getId());
    }

    ParallelTask task = ParallelUtils.createParallelTask();
    task.submit(() -> {
      log.info("start updateOrderPayment %s", objectData.getId());
      customerPaymentService.updateOrderPayment(objectData, actionContext.getUser());
      log.info("end updateOrderPayment %s", objectData.getId());
    });
    try {
      task.run();
    } catch (Exception ex) {
      log.error(ex.getMessage(), ex);
    }
    return result;
  }
}

