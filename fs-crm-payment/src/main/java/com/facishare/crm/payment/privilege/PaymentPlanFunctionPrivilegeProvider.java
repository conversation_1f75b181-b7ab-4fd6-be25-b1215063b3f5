package com.facishare.crm.payment.privilege;

import com.facishare.crm.payment.PaymentObject;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.privilege.model.DefaultFunctionPrivilegeProvider;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component
public class PaymentPlanFunctionPrivilegeProvider extends DefaultFunctionPrivilegeProvider {
  private final static List<String> supportActionCodes = Lists.newArrayList(
          ObjectAction.VIEW_LIST.getActionCode(),
          ObjectAction.VIEW_DETAIL.getActionCode(),
          ObjectAction.CREATE.getActionCode(),
          ObjectAction.UPDATE.getActionCode(),
          ObjectAction.INVALID.getActionCode(),
          ObjectAction.RECOVER.getActionCode(),
          ObjectAction.DELETE.getActionCode(),
          ObjectAction.BATCH_IMPORT.getActionCode(),
          ObjectAction.BATCH_EXPORT.getActionCode(),
          ObjectAction.CHANGE_OWNER.getActionCode(),
          ObjectAction.EDIT_TEAM_MEMBER.getActionCode(),
          ObjectAction.BULK_RELATE.getActionCode(),
          ObjectAction.START_BPM.getActionCode(),
          ObjectAction.VIEW_ENTIRE_BPM.getActionCode(),
          ObjectAction.STOP_BPM.getActionCode(),
          ObjectAction.CHANGE_BPM_APPROVER.getActionCode(),
          ObjectAction.PRINT.getActionCode(),
          ObjectAction.INTELLIGENTFORM.getActionCode(),
          ObjectAction.LOCK.getActionCode(),
          ObjectAction.UNLOCK.getActionCode(),
          ObjectAction.MODIFYLOG_RECOVER.getActionCode()
  );

  private final static List<String> crmObserveActionCodes = Lists.newArrayList(
          ObjectAction.VIEW_LIST.getActionCode(),
          ObjectAction.VIEW_DETAIL.getActionCode()
  );

  private final static List<String> salesActionCodes = Lists.newArrayList(
          ObjectAction.VIEW_LIST.getActionCode(),
          ObjectAction.VIEW_DETAIL.getActionCode(),
          ObjectAction.CREATE.getActionCode(),
          ObjectAction.UPDATE.getActionCode(),
          ObjectAction.INVALID.getActionCode(),
          ObjectAction.BATCH_IMPORT.getActionCode(),
          ObjectAction.CHANGE_OWNER.getActionCode(),
          ObjectAction.EDIT_TEAM_MEMBER.getActionCode()
  );

  private final static List<String> paymentActionCodes = Lists.newArrayList(
          ObjectAction.VIEW_LIST.getActionCode(),
          ObjectAction.VIEW_DETAIL.getActionCode(),
          ObjectAction.CREATE.getActionCode(),
          ObjectAction.UPDATE.getActionCode(),
          ObjectAction.INVALID.getActionCode(),
          ObjectAction.BATCH_IMPORT.getActionCode(),
          ObjectAction.BATCH_EXPORT.getActionCode(),
          ObjectAction.CHANGE_OWNER.getActionCode(),
          ObjectAction.EDIT_TEAM_MEMBER.getActionCode()
  );

  private final static List<String> InvoiceActionCodes = Lists.newArrayList(
          ObjectAction.VIEW_LIST.getActionCode(),
          ObjectAction.VIEW_DETAIL.getActionCode()
  );

  @Override
  public String getApiName() {
    return PaymentObject.PAYMENT_PLAN.getApiName();
  }

  @Override
  public List<String> getSupportedActionCodes() {
    return Collections.unmodifiableList(supportActionCodes);
  }

  @Override
  public Map<String, List<String>> getCustomInitRoleActionCodes() {
    Map<String, List<String>> actionCodeMap = Maps.newHashMap();
    actionCodeMap.put("00000000000000000000000000000002", Collections.unmodifiableList(paymentActionCodes));
    actionCodeMap.put("00000000000000000000000000000009", Collections.unmodifiableList(crmObserveActionCodes));
    actionCodeMap.put("00000000000000000000000000000015", Collections.unmodifiableList(salesActionCodes));
    actionCodeMap.put("00000000000000000000000000000005", Collections.unmodifiableList(InvoiceActionCodes));
    return Collections.unmodifiableMap(actionCodeMap);
  }
}
