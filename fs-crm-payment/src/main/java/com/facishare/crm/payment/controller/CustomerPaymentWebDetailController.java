package com.facishare.crm.payment.controller;

import com.facishare.crm.payment.constant.CustomerPaymentObj;
import com.facishare.crm.payment.constant.OrderPaymentObj;
import com.facishare.crm.payment.service.CustomerPaymentService;
import com.facishare.crm.payment.utils.CustomerPaymentUtils;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.impl.ui.layout.component.ComponentFactory;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/6/19 10:37 上午
 * @illustration
 */
public class CustomerPaymentWebDetailController extends StandardWebDetailController {

    private CustomerPaymentService customerPaymentService = SpringUtil.getContext()
            .getBean(CustomerPaymentService.class);

    @Override
    protected Result doService(Arg arg) {
        Result newResult = super.doService(arg);
        newResult = setOrderNameToData(newResult);
        ILayout newLayout = newResult.getLayout().toLayout();
        newLayout = addPaymentRecordRelatedList(newLayout);
        newLayout = modifyResult(newLayout);
        newResult.setLayout(LayoutDocument.of(newLayout));
        return newResult;
    }


    /**
     * 添加企业收款明细页签
     *
     * @param layout
     * @return
     */
    private ILayout addPaymentRecordRelatedList(ILayout layout){
        Map<String, Object> stringObjectMap = CustomerPaymentUtils.builderPaymentRecordRelatedMap();
        try {
            IComponent multiComponent = ComponentFactory.newInstance(stringObjectMap);
            // 放在最后一个
            WebDetailLayout.of(layout).addComponents(Lists.newArrayList(multiComponent));
            return layout;
        } catch (Exception e) {
            log.error("CustomerPaymentWebDetailController addComponents error", e);
        }
        return layout;
    }


    /**
     * 过滤当前审批人字段
     * 只做字段屏蔽，从对象的按钮屏蔽
     * 走DetailListHeader
     *
     * @param layout
     * @return
     */
    private ILayout modifyResult(ILayout layout) {
        WebDetailLayout.of(layout).removeFields(Lists.newArrayList(CustomerPaymentObj.FIELD_APPROVE_EMPLOYEE_ID));
        return layout;
    }


    /**
     * 添加订单的信息到回款中
     *
     * @param result
     * @return
     */
    private Result setOrderNameToData(Result result){
        ObjectDataDocument data = result.getData();
        if (data.containsKey(CustomerPaymentObj.FIELD_ID)) {
            List<Map> orders = customerPaymentService.queryOrderPaymentList(CustomerPaymentUtils.builderServiceContext(controllerContext.getRequestContext()),
                    data.get(CustomerPaymentObj.FIELD_ID).toString());
            Set<String> orderIds = orders.stream().map(item -> item.getOrDefault(OrderPaymentObj.FIELD_ORDER_ID, "").toString()).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(orderIds)) {
                Joiner joiner = Joiner.on(",");
                String order_id = joiner.join(orderIds);
                data.put(CustomerPaymentObj.FIELD_ORDER_ID, order_id);
            }
        }
        result.setData(customerPaymentService.parseOrderNames(controllerContext.getUser(),
                Lists.newArrayList(data)).get(0));
        return result;
    }

}
