package com.facishare.crm.payment.controller;

import com.facishare.crm.payment.constant.CustomerPaymentObj;
import com.facishare.crm.payment.utils.JsonObjectUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;

import java.util.Objects;

import static com.facishare.crm.payment.utils.JsonPaths.DESCRIBE_DETAIL_LAYOUT_FORM_BASE_FIELDS;

public class PaymentPlanListHeaderController extends StandardListHeaderController {
    @Override
    protected Result doService(Arg arg) {
        Result result = super.doService(arg);
        result = modifyResult(result);
        return result;
    }

    /**
     * 隐藏当前审批人字段
     */
    private Result modifyResult(Result result) {
        result = JsonObjectUtils.remove(result, Result.class,
                DESCRIBE_DETAIL_LAYOUT_FORM_BASE_FIELDS + "[?(@.field_name=='"
                        + CustomerPaymentObj.FIELD_APPROVE_EMPLOYEE_ID + "')]");
        if (Objects.nonNull(result.getFieldList())) {
            result = JsonObjectUtils.remove(result, Result.class, "$.fieldList[?(@.approve_employee_id)]");
        }
        return result;
    }
}
