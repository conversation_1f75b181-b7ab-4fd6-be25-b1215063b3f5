package com.facishare.crm.payment.controller;

import com.facishare.crm.payment.service.OrderPaymentService;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardDetailListController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.impl.ui.layout.component.MultiTableComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-08-27 14:40
 * @instruction
 */
public class OrderPaymentDetailListController extends StandardDetailListController {

    @Override
    protected Result doService(Arg arg) {
        Result result = super.doService(arg);
        OrderPaymentService orderPaymentService =
                SpringUtil.getContext().getBean(OrderPaymentService.class);
        List<ObjectDataDocument> list =
                orderPaymentService.parseOrderPaymentCost(result.getDataList(), controllerContext);
        result.setDataList(list);
        return result;
    }
    /**
     * 删除所有业务类型下的按钮
     */
    @Override
    protected ILayout findLayout() {
        ILayout layout = super.findLayout();
        LayoutExt layoutExt = LayoutExt.of(layout);
        try {
            layoutExt.getComponents().stream().filter(o -> "multi_table".equals(o.getType())).findFirst().ifPresent(
                    component -> {
                        try {
                            ((MultiTableComponent) component).getChildComponents().forEach(
                                    childComponent -> childComponent.setButtons(Lists.newArrayList())
                            );
                        }catch (Exception e){
                            log.error("SalesOrderProductDetailListController tenantId:{}", controllerContext.getTenantId(), e);
                        }

                    }
            );
        } catch (Exception e) {
            log.error("SalesOrderProductDetailListController tenantId:{}", controllerContext.getTenantId(), e);
        }
        layoutExt.setButtons(Lists.newArrayList());
        return layoutExt.getLayout();
    }


}
