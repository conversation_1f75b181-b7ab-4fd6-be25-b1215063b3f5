package com.facishare.crm.payment.constant;

import com.github.autoconf.ConfigFactory;

/**
 * <AUTHOR> 2020-01-02
 * @instruction
 */
public class PaymentGrayConf {
    public static boolean skipSetPaymentOrderLifeStatus;

    static {
        ConfigFactory.getInstance().getConfig("fs-crm-java-console", config -> {
            skipSetPaymentOrderLifeStatus = config.getBool("skipSetPaymentOrderLifeStatus", Boolean.TRUE);
        });
    }
}
