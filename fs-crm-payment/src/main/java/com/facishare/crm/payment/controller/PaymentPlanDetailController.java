package com.facishare.crm.payment.controller;

import com.facishare.crm.payment.constant.PaymentPlanObj;
import com.facishare.crm.payment.service.OrderPaymentService;
import com.facishare.crm.payment.service.PaymentPlanService;
import com.facishare.crm.payment.utils.FieldUtils;
import com.facishare.crm.payment.utils.JsonObjectUtils;
import com.facishare.crm.payment.utils.JsonPaths;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardDetailController;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import org.springframework.beans.factory.annotation.Autowired;

import static com.facishare.crm.payment.utils.PaymentPlanUtils.getPaymentPlanStatus;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public class PaymentPlanDetailController extends StandardDetailController {

  private PaymentPlanService paymentPlanService =
          SpringUtil.getContext().getBean(PaymentPlanService.class);

  @Override
  public Result doService(Arg arg) {
    Result result = super.doService(arg);

    //layout 回款计划状态 实际回款金额
    String paymentStatusPath =
        JsonPaths.DESCRIBE_LAYOUT_DETAIL_FORM_BASE_FIELDS + "[?(@.field_name=='"
            + PaymentPlanObj.FIELD_PLAN_PAYMENT_STATUS + "')]";
    if (JsonObjectUtils.get(result, Map.class, paymentStatusPath) == null) {
      result = JsonObjectUtils.append(result, PaymentPlanDetailController.Result.class,
          JsonPaths.DESCRIBE_LAYOUT_DETAIL_FORM_BASE_FIELDS, FieldUtils
              .buildLayoutField(PaymentPlanObj.FIELD_PLAN_PAYMENT_STATUS, false, false,
                  "select_one"));
    }

    String realPaymentAmountPath =
        JsonPaths.DESCRIBE_LAYOUT_DETAIL_FORM_BASE_FIELDS + "[?(@.field_name=='"
            + PaymentPlanObj.FIELD_ACTUAL_PAYMENT_AMOUNT + "')]";
    if (JsonObjectUtils.get(result, Map.class, realPaymentAmountPath) == null) {
      result = JsonObjectUtils.append(result, PaymentPlanDetailController.Result.class,
          JsonPaths.DESCRIBE_LAYOUT_DETAIL_FORM_BASE_FIELDS, FieldUtils
              .buildLayoutField(PaymentPlanObj.FIELD_ACTUAL_PAYMENT_AMOUNT, false, true, "number"));
    }

    return result;
  }

}
