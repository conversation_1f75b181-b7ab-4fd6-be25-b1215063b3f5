package com.facishare.crm.payment.action;

import com.facishare.crm.payment.PaymentObject;
import com.facishare.crm.payment.service.CustomerPaymentService;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.predef.action.StandardFlowCompletedAction;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;

import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CustomerPaymentFlowCompletedAction extends StandardFlowCompletedAction {
    private CustomerPaymentService customerPaymentService =
            SpringUtil.getContext().getBean(CustomerPaymentService.class);

    private List<IObjectData> orderPaymentObjects;

    @Override
    protected void before(Arg arg) {
        log.info("CustomerPaymentFlowCompletedAction before arg: {}", arg);
        super.before(arg);
        if (Objects.equals(arg.getTriggerType(), ApprovalFlowTriggerType.INVALID.getTriggerTypeCode())) {
            IObjectDescribe orderPaymentDescribe =
                    serviceFacade.findObject(arg.getTenantId(), PaymentObject.ORDER_PAYMENT.getApiName());
            IObjectData masterObject = serviceFacade.findObjectData(actionContext.getUser(), arg.getDataId(),
                    PaymentObject.CUSTOMER_PAYMENT.getApiName());
            orderPaymentObjects =
                    serviceFacade.findDetailObjectDataList(orderPaymentDescribe, masterObject, actionContext.getUser());
        }
    }

    @Override
    @Transactional
    protected Result doAct(Arg arg) {
        Result result = super.doAct(arg);
        log.info("CustomerPaymentFlowCompletedAction doAct arg: {}, result: {}", arg, result);
        return result;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        boolean success = result.getSuccess();
        log.info("CustomerPaymentFlowCompletedAction after info tenantId:{},paymentId:{}", getActionContext().getTenantId(), arg.getDataId());
        ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
        task.submit(() -> {
            try {
                IObjectData data = serviceFacade.findObjectDataIncludeDeleted(actionContext.getUser(), arg.getDataId(), arg.getDescribeApiName());
                customerPaymentService.updateOrderPayment(data, actionContext.getUser());
                customerPaymentService.deletePaymentByEditPayment(actionContext, data);

                if (Objects.equals(arg.getTriggerType(), ApprovalFlowTriggerType.CREATE.getTriggerTypeCode()) && success) {
                    if (Objects.equals(arg.getStatus(), "reject") || Objects.equals(arg.getStatus(), "pass")) {
                        customerPaymentService.sendDHTMq(actionContext.getUser(), arg.getStatus(), arg.getDataId());
                    }
                }
            } catch (Exception e) {
                log.error("CustomerPaymentFlowCompletedAction after error tenantId:{},paymentId:{}", getActionContext().getTenantId(), arg.getDataId(), e);
            }

        });
        task.run();
        return result;
    }
}

