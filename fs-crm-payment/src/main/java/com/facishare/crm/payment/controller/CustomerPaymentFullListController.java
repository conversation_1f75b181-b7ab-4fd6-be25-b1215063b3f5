package com.facishare.crm.payment.controller;

import com.facishare.crm.payment.service.CustomerPaymentService;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.metadata.api.DELETE_STATUS;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;

public class CustomerPaymentFullListController extends CustomerPaymentListController {


    @Override
    protected QueryResult<IObjectData> findData(SearchTemplateQuery query) {
        IFilter filter = new Filter();
        filter.setFieldName(IObjectData.IS_DELETED);
        filter.setOperator(Operator.IN);
        filter.setFieldValues(Lists.newArrayList(String.valueOf(DELETE_STATUS.NORMAL.getValue()), String.valueOf(DELETE_STATUS.INVALID.getValue())));
        query.addFilters(Lists.newArrayList(filter));
        return serviceFacade.findBySearchQueryWithDeleted(controllerContext.getUser(), objectDescribe, query);
    }
}
