package com.facishare.crm.payment.action;

import com.facishare.crm.payment.constant.PaymentPlanObj;
import com.facishare.paas.appframework.core.predef.action.BaseImportTemplateAction;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportTemplateAction;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.google.common.collect.Lists;

import java.util.List;

public class PaymentPlanInsertImportTemplateAction extends
        StandardInsertImportTemplateAction {

    private List<String> REMOVE_FIELDS = Lists.newArrayList(
            PaymentPlanObj.FIELD_ACTUAL_PAYMENT_AMOUNT,
            PaymentPlanObj.FIELD_PLAN_PAYMENT_STATUS,
            "extend_obj_data_id", "approve_employee_id"
    );

    @Override
    protected void customHeader(List<IFieldDescribe> headerFieldList) {
        headerFieldList.removeIf(f -> REMOVE_FIELDS.contains(f.getApiName()));
        headerFieldList.stream().filter(f -> "plan_payment_method".equals(f.getApiName()) && "select_one".equals(f.getType())).forEach(k -> {
            SelectOneFieldDescribe selectOneField = (SelectOneFieldDescribe) k;
            List<ISelectOption> selectOptions = selectOneField.getSelectOptions();
            selectOptions.removeIf(r ->
                    "10000".equals(r.getValue()) ||
                            "10001".equals(r.getValue()) ||
                            "10002".equals(r.getValue())
            );
            selectOneField.setSelectOptions(selectOptions);
        });
    }
}
