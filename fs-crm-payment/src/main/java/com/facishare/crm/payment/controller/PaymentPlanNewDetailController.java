package com.facishare.crm.payment.controller;

import com.facishare.crm.payment.constant.PaymentPlanObj;
import com.facishare.crm.payment.service.PaymentPlanService;
import com.facishare.crm.payment.utils.FieldUtils;
import com.facishare.crm.payment.utils.JsonObjectUtils;
import com.facishare.crm.payment.utils.JsonPaths;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardNewDetailController;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.Map;

import static com.facishare.crm.payment.utils.JsonPaths.NEW_DETAIL_LAYOUT_FORM;

/**
 * <AUTHOR>
 * @date 2019-08-27 14:51
 * @instruction
 */
public class PaymentPlanNewDetailController extends StandardNewDetailController {

    private PaymentPlanService paymentPlanService =
            SpringUtil.getContext().getBean(PaymentPlanService.class);

    @Override
    public Result doService(Arg arg) {
        Result result = super.doService(arg);

        //layout 回款计划状态 实际回款金额
        String paymentStatusPath =NEW_DETAIL_LAYOUT_FORM+"[?(@.field_name=='"
                        + PaymentPlanObj.FIELD_PLAN_PAYMENT_STATUS + "')]";
        if (JsonObjectUtils.get(result, Map.class, paymentStatusPath) == null) {
            result = JsonObjectUtils.append(result, PaymentPlanDetailController.Result.class,
                    NEW_DETAIL_LAYOUT_FORM, FieldUtils
                            .buildLayoutField(PaymentPlanObj.FIELD_PLAN_PAYMENT_STATUS, false, false,
                                    "select_one"));
        }

        String realPaymentAmountPath =
                NEW_DETAIL_LAYOUT_FORM + "[?(@.field_name=='"
                        + PaymentPlanObj.FIELD_ACTUAL_PAYMENT_AMOUNT + "')]";
        if (JsonObjectUtils.get(result, Map.class, realPaymentAmountPath) == null) {
            result = JsonObjectUtils.append(result, PaymentPlanDetailController.Result.class,
                    NEW_DETAIL_LAYOUT_FORM, FieldUtils
                            .buildLayoutField(PaymentPlanObj.FIELD_ACTUAL_PAYMENT_AMOUNT, false, true, "number"));
        }


        return result;
    }
}
