package com.facishare.crm.payment.controller;

import com.facishare.crm.payment.constant.CustomerPaymentObj;
import com.facishare.crm.payment.utils.OrderPaymentUtils;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;

/**
 * <AUTHOR>
 * @date 2020/6/19 11:43 上午
 * @illustration
 */
public class OrderPaymentWebDetailController extends StandardWebDetailController {


    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        ILayout layout = newResult.getLayout().toLayout();
        layout = modifyResult(layout);
        layout = removeButton(layout);
        newResult.setLayout(LayoutDocument.of(layout));

        return newResult;
    }

    private ILayout removeButton(ILayout layout){
        WebDetailLayout.of(layout).removeButtonsByActionCode(OrderPaymentUtils.REMOVE_BUTTON_BY_ACTIONS);
        return layout;
    }

    private ILayout modifyResult(ILayout layout) {
        WebDetailLayout.of(layout).removeFields(Lists.newArrayList(CustomerPaymentObj.FIELD_APPROVE_EMPLOYEE_ID));
        return layout;
    }

}
