package com.facishare.crm.payment.action;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.payment.constant.CustomerPaymentObj;
import com.facishare.crm.payment.constant.OrderPaymentObj;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportDataAction;
import com.facishare.paas.metadata.api.IObjectData;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class OrderPaymentInsertImportDataAction extends StandardInsertImportDataAction {

    @Override
    protected void customAfterImport(List<IObjectData> actualList) {
        log.debug("OrderPaymentInsertImportDataAction customAfterImport actualList:{}", actualList);
        super.customAfterImport(actualList);
        Map<String, List<String>> customerPaymentIdWithOrderIds = Maps.newHashMap();
        actualList.forEach(x -> {
            String paymentId = (String) x.get(OrderPaymentObj.FIELD_PAYMENT_ID);
            String orderId = (String) x.get(OrderPaymentObj.FIELD_ORDER_ID);
            if (StringUtils.isNotEmpty(paymentId)) {
                customerPaymentIdWithOrderIds.computeIfAbsent(paymentId, k -> Lists.newArrayList()).add(orderId);
            }
        });
        String tenantId = this.getActionContext().getTenantId();
        List<IObjectData> customerPaymentList = serviceFacade.findObjectDataByIdsIgnoreFormula(tenantId,
                Lists.newArrayList(customerPaymentIdWithOrderIds.keySet()), Utils.CUSTOMER_PAYMENT_API_NAME);
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        customerPaymentList.forEach(customerPaymentObjectData -> {
            if (customerPaymentObjectData != null) {
                parallelTask.submit(() -> {
                    String paymentId = customerPaymentObjectData.getId();
                    List<String> orderIds = customerPaymentIdWithOrderIds.get(paymentId);
                    if (CollectionUtils.isNotEmpty(orderIds)) {
                        Set<String> oldOrderIds = getOrderList(customerPaymentObjectData);
                        oldOrderIds.addAll(orderIds);
                        Map paramMap = Maps.newHashMap();
                        paramMap.put(CustomerPaymentObj.FIELD_ORDER_ID, Joiner.on(",").join(oldOrderIds));
                        //update by quzf,父类有批量插入异步操作，所以此处更新时不关注版本号，防止和父类同时保存主数据版本号不一致报错
                        serviceFacade.updateWithMap(actionContext.getUser(), customerPaymentObjectData, paramMap);
                    }
                });
            }
        });
        try {
            parallelTask.run();
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }


    private Set<String> getOrderList(IObjectData data) {
        List<IObjectData> dataList =
                serviceFacade.findDetailObjectDataListIgnoreFormula(data, actionContext.getUser());
        Set<String> orderIdList = Sets.newHashSet();
        if (CollectionUtils.isNotEmpty(dataList)) {
            dataList.forEach(x -> {
                orderIdList.add((String) x.get(OrderPaymentObj.FIELD_ORDER_ID));
            });
        }
        return orderIdList;
    }
}
