{"components": [{"api_name": "table_component", "ref_object_api_name": "UnitInfoObj", "include_fields": [{"api_name": "name", "render_type": "text", "field_name": "name"}, {"api_name": "life_status", "render_type": "select_one", "is_show_field_name": false, "field_name": "life_status"}], "type": "table"}], "buttons": [], "package": "CRM", "ref_object_api_name": "UnitInfoObj", "layout_type": "list", "display_name": "单位列表布局", "is_default": false, "version": 1, "agent_type": "agent_type_mobile", "is_deleted": false, "api_name": "layout_UnitInfoObj_mobile", "is_show_fieldname": true}