{"components": [{"include_fields": [{"is_readonly": false, "is_required": true, "render_type": "object_reference", "field_name": "up_product_id"}, {"is_readonly": false, "is_required": true, "render_type": "true_or_false", "field_name": "constraint_type"}, {"is_readonly": false, "is_required": true, "render_type": "object_reference", "field_name": "down_product_id"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "up_product_path"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "down_product_path"}, {"is_readonly": false, "is_required": true, "render_type": "master_detail", "field_name": "product_constraint_id"}], "api_name": "table_component", "ref_object_api_name": "ProductConstraintLinesObj", "type": "table"}], "package": "CRM", "ref_object_api_name": "ProductConstraintLinesObj", "layout_type": "list", "display_name": "产品约束明细默认列表布局", "is_default": false, "version": "1", "agent_type": "agent_type_mobile", "is_deleted": false, "api_name": "layout_ProductConstraintLinesObj_mobile", "is_show_fieldname": true}