{"buttons": [], "components": [{"field_section": [{"form_fields": [{"is_readonly": false, "is_required": true, "render_type": "object_reference", "field_name": "account_id"}, {"is_readonly": false, "is_required": true, "render_type": "text", "field_name": "sales_process_name"}, {"is_readonly": false, "is_required": true, "render_type": "text", "field_name": "name"}, {"is_readonly": false, "is_required": true, "render_type": "date", "field_name": "expected_deal_closed_date"}, {"is_readonly": false, "is_required": true, "render_type": "currency", "field_name": "expected_deal_amount"}, {"is_readonly": false, "is_required": true, "render_type": "text", "field_name": "oppo_stage_id"}, {"is_readonly": false, "is_required": false, "render_type": "select_one", "field_name": "lost_reason"}, {"is_readonly": false, "is_required": true, "render_type": "employee", "field_name": "owner"}, {"is_readonly": false, "is_required": false, "render_type": "date_time", "field_name": "sales_stg_changed_time"}, {"is_readonly": false, "is_required": false, "render_type": "long_text", "field_name": "remark"}], "api_name": "base_field_section__c", "tab_index": "ltr", "column": 2, "header": "基本信息", "is_show": true}, {"form_fields": [{"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "owner_department"}, {"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "created_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "create_time"}, {"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "last_modified_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "last_modified_time"}, {"is_readonly": false, "is_required": false, "render_type": "date_time", "field_name": "last_followed_time"}, {"is_readonly": false, "is_required": false, "render_type": "select_one", "field_name": "status"}], "api_name": "system_form_field_generate_by_UDObjectServer__c", "tab_index": "ltr", "column": 2, "header": "系统信息", "is_show": true}], "buttons": [], "api_name": "form_component", "is_hidden": false, "header": "详细信息", "type": "form", "order": 2}], "is_deleted": false, "version": 2, "layout_description": "PRM代理商销售布局-商机", "api_name": "default_crm_OpportunityObj_prm_downstream_layout", "default_component": "form_component", "config": {"edit": 0, "remove": 0}, "display_name": "PRM代理商销售布局-商机", "is_default": true, "layout_type": "detail", "package": "CRM", "ref_object_api_name": "OpportunityObj"}