{"buttons": [{"action_type": "default", "api_name": "Edit_button_default", "action": "Edit", "label": "编辑"}, {"action_type": "default", "api_name": "SaleRecord_button_default", "action": "SaleRecord", "label": "销售记录"}, {"action_type": "default", "api_name": "Dial_button_default", "action": "<PERSON><PERSON>", "label": "打电话"}, {"action_type": "default", "api_name": "ChangeOwner_button_default", "action": "ChangeOwner", "label": "更换负责人"}, {"action_type": "default", "api_name": "StartBPM_button_default", "action": "StartBPM", "label": "发起流程"}, {"action_type": "default", "api_name": "Abolish_button_default", "action": "Abolish", "label": "作废"}, {"action_type": "default", "api_name": "Lock_button_default", "action": "Lock", "label": "锁定"}, {"action_type": "default", "api_name": "Unlock_button_default", "action": "Unlock", "label": "解锁"}, {"action_type": "default", "api_name": "Clone_button_default", "action": "<PERSON><PERSON>", "label": "复制"}, {"action_type": "default", "api_name": "SendMail_button_default", "action": "SendMail", "label": "发邮件"}, {"action_type": "default", "api_name": "Discuss_button_default", "action": "Discuss", "label": "转发"}, {"action_type": "default", "api_name": "Remind_button_default", "action": "Remind", "label": "提醒"}, {"action_type": "default", "api_name": "Schedule_button_default", "action": "Schedule", "label": "日程"}, {"action_type": "default", "api_name": "Print_button_default", "action": "Print", "label": "打印"}], "components": [{"field_section": [{"form_fields": [{"is_readonly": false, "is_required": true, "render_type": "text", "field_name": "name"}, {"is_readonly": false, "is_required": true, "render_type": "currency", "field_name": "standard_price"}, {"is_readonly": false, "is_required": true, "render_type": "select_one", "field_name": "unit"}, {"field_name": "record_type", "render_type": "record_type", "is_required": true, "is_readonly": false}, {"is_readonly": false, "is_required": true, "render_type": "select_one", "field_name": "category"}, {"is_readonly": false, "is_required": false, "render_type": "select_one", "field_name": "product_line"}, {"is_readonly": false, "is_required": false, "render_type": "image", "field_name": "picture"}, {"is_readonly": false, "is_required": true, "render_type": "true_or_false", "field_name": "is_spec"}, {"is_readonly": false, "is_required": true, "render_type": "employee", "field_name": "owner"}, {"is_readonly": false, "is_required": false, "render_type": "long_text", "field_name": "remark"}, {"is_readonly": true, "is_required": false, "render_type": "department", "field_name": "data_own_department"}], "api_name": "base_field_section__c", "tab_index": "ltr", "column": 2, "header": "基本信息", "is_show": true}, {"form_fields": [{"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "created_by"}, {"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "last_modified_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "create_time"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "last_modified_time"}], "api_name": "sysinfo_section__c", "tab_index": "ltr", "column": 2, "header": "系统信息", "is_show": true}], "buttons": [], "api_name": "form_component", "is_hidden": false, "header": "详细信息", "type": "form", "order": 2}, {"buttons": [], "api_name": "sale_log", "is_hidden": false, "header": "摘要", "type": "related_record", "order": 1}, {"buttons": [], "api_name": "relevant_team_component", "is_hidden": false, "header": "相关团队", "is_show_avatar": true, "type": "user_list", "order": 3}, {"relationType": 2, "buttons": [], "api_name": "payment_recordrelated_list_generate_by_UDObjectServer__c", "related_list_name": "payment_record_LIST", "ref_object_api_name": "payment_record", "is_hidden": false, "header": "收款记录", "type": "relatedlist", "order": 4}, {"buttons": [], "api_name": "BPM_related_list", "related_list_name": "", "ref_object_api_name": "BPM", "is_hidden": false, "header": "流程列表", "type": "relatedlist", "order": 5}, {"buttons": [], "api_name": "Approval_related_list", "related_list_name": "", "ref_object_api_name": "Approval", "is_hidden": false, "header": "审批流程", "type": "relatedlist", "order": 6}, {"buttons": [], "api_name": "operation_log", "is_hidden": false, "header": "修改记录", "type": "related_record", "fields": {"operation_time": {"render_type": "date_time", "field_name": "operation_time"}, "message": {"render_type": "text", "field_name": "log_msg"}, "user": {"render_type": "employee", "field_name": "user_id"}}, "order": 7}, {"relationType": 2, "buttons": [], "api_name": "ProductObj_spu_id_related_list", "related_list_name": "spu_sku_list", "ref_object_api_name": "ProductObj", "is_hidden": false, "header": "产品", "type": "relatedlist", "order": 8, "field_api_name": "spu_id"}], "last_modified_time": 1543200070881, "package": "CRM", "create_time": 1540979534877, "ref_object_api_name": "SPUObj", "layout_type": "detail", "last_modified_by": "1000", "display_name": "默认布局", "is_default": true, "version": 24, "is_deleted": false, "api_name": "SPUObj_layout_generate_by_UDObjectServer__c", "layout_description": "商品默认布局", "config": {"edit": 1, "remove": 0}, "default_component": "ProductObj_spu_id_related_list", "top_info": {"field_section": [{"form_fields": [{"render_type": "employee", "field_name": "owner"}, {"render_type": "text", "field_name": "owner_department"}, {"render_type": "date_time", "field_name": "last_modified_time"}, {"render_type": "record_type", "field_name": "record_type"}], "api_name": "detail"}], "buttons": [], "api_name": "top_info", "is_hidden": false, "header": "顶部信息", "type": "simple", "order": 0}}