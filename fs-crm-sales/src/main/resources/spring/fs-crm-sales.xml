<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo" xmlns:p="http://www.springframework.org/schema/p"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
       http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">


    <import resource="classpath*:/dubborestouterapi/fs-wechat-dubbo-rest-outer-api-client.xml"/>

    <import resource="classpath*:spring/ei-ea-converter.xml"/>

    <import resource="classpath*:/META-INF/spring/pod-api-client.xml"/>


    <bean id="newopportunityEnginePullMQSender"
          class="com.facishare.paas.appframework.common.mq.RocketMQMessageSender"
          p:configName="newopportunity-enginepull-mq"></bean>

    <bean id="locationCalculationMessageSender" class="com.facishare.paas.appframework.common.mq.RocketMQMessageSender"
          p:configName="fs-crm-mq-location-calculation"  init-method="init"> </bean>

    <bean id="accountPathMessageSender" class="com.facishare.paas.appframework.common.mq.RocketMQMessageSender"
          p:configName="fs-crm-mq-account-path"  init-method="init"> </bean>

    <bean id="addProMqMessageSender" class="com.facishare.paas.appframework.common.mq.RocketMQMessageSender"
          p:configName="fs-crm-add-product-pricebook"  init-method="init"> </bean>
    <bean id="addDataCleanMqMessageSender" class="com.facishare.paas.appframework.common.mq.RocketMQMessageSender"
          p:configName="fs-crm-mq-data-clean-task"  init-method="init"> </bean>
    <bean id="initModuleCtrlMQSender"
          class="com.facishare.paas.appframework.common.mq.RocketMQMessageSender"
          p:configName="crm-module-ctrl-mq"></bean>
    <!--<import resource="spring-muti-thread.xml"/>-->


    <bean id="outHttpClientSupport" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean">
        <property name="configName" value="fs-crm-out-http-support"/>
    </bean>
    <aop:aspectj-autoproxy expose-proxy="true"/>
</beans>
