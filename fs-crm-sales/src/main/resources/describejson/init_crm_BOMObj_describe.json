{"fields": {"name": {"is_index": true, "is_active": true, "prefix": "{yyyy}{mm}{dd}", "is_unique": true, "default_value": "", "serial_number": 6, "start_number": 0, "label": "产品选配明细编号", "type": "auto_number", "is_required": false, "api_name": "name", "define_type": "system", "postfix": "", "help_text": "", "status": "released", "is_extend": false}, "_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "_id", "api_name": "_id", "description": "_id", "status": "released", "is_extend": false}, "product_id": {"describe_api_name": "BOMObj", "is_index": true, "is_active": true, "is_unique": false, "label": "产品名称", "target_api_name": "ProductObj", "type": "object_reference", "target_related_list_name": "target_related_list_product_id", "is_abstract": null, "target_related_list_label": "关联该产品的子产品", "action_on_target_delete": "cascade_delete", "is_required": true, "wheres": [{"connector": "OR", "filters": [{"value_type": 0, "operator": "EQ", "field_name": "life_status", "field_values": ["normal"]}, {"value_type": 0, "operator": "EQ", "field_name": "product_status", "field_values": ["1"]}]}], "api_name": "product_id", "define_type": "package", "is_index_field": false, "is_single": false, "config": {"add": 0, "edit": 1, "enable": 0, "display": 1, "remove": 0, "attrs": {"wheres": 0, "default_value": 1, "label": 0, "target_api_name": 0, "target_related_list_name": 0, "help_text": 1, "is_required": 0}}, "help_text": "", "status": "released"}, "parent_bom_id": {"is_index": true, "is_active": true, "pattern": "", "description": "父BOMid", "is_unique": false, "label": "父BOMid", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "parent_bom_id", "define_type": "package", "is_index_field": false, "is_single": false, "max_length": 200, "config": {"add": 0, "edit": 0, "enable": 0, "display": 0, "remove": 0, "attrs": {"api_name": 0, "is_unique": 0, "default_value": 0, "label": 0, "help_text": 0}}, "status": "released"}, "root_id": {"is_index": true, "is_active": true, "pattern": "", "description": "根BOMid", "is_unique": false, "label": "根BOMid", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "root_id", "define_type": "package", "is_index_field": false, "is_single": false, "max_length": 200, "config": {"add": 0, "edit": 0, "enable": 0, "display": 0, "remove": 0, "attrs": {"api_name": 0, "is_unique": 0, "default_value": 0, "label": 0, "help_text": 0}}, "status": "released"}, "bom_path": {"is_index": true, "is_active": true, "pattern": "", "description": "BOM路径", "is_unique": false, "label": "BOM路径", "type": "tree_path", "is_need_convert": false, "is_required": false, "api_name": "bom_path", "define_type": "package", "is_index_field": false, "is_single": false, "max_length": 500, "config": {"add": 0, "edit": 0, "enable": 0, "display": 0, "remove": 0, "attrs": {"api_name": 0, "is_unique": 0, "default_value": 0, "label": 0, "help_text": 0}}, "status": "released"}, "adjust_price": {"describe_api_name": "BOMObj", "default_is_expression": false, "is_unique": false, "type": "currency", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "max_length": 14, "is_index": true, "is_active": true, "length": 12, "default_value": "0", "label": "标准选配价格", "currency_unit": "￥", "extend_info": {"show_positive_sign": true}, "is_abstract": null, "api_name": "adjust_price", "config": {"add": 0, "edit": 1, "enable": 0, "display": 1, "remove": 0, "attrs": {"api_name": 0, "is_unique": 0, "default_value": 1, "label": 0, "help_text": 1, "max_length": 1, "decimal_places": 1, "is_required": 0}}, "round_mode": 4, "help_text": "", "status": "released"}, "amount": {"describe_api_name": "BOMObj", "default_is_expression": false, "is_unique": false, "type": "number", "decimal_places": 0, "default_to_zero": true, "is_required": true, "define_type": "package", "is_single": false, "max_length": 14, "is_index": true, "is_active": true, "length": 14, "default_value": "1", "label": "数量", "is_abstract": null, "api_name": "amount", "is_index_field": false, "config": {"add": 0, "edit": 1, "enable": 0, "display": 1, "remove": 0, "attrs": {"api_name": 0, "is_unique": 0, "default_value": 1, "label": 0, "help_text": 1, "max_length": 1, "decimal_places": 0, "is_required": 0}}, "round_mode": 4, "help_text": "", "status": "released"}, "is_required": {"describe_api_name": "BOMObj", "is_index": true, "is_active": true, "is_unique": false, "default_value": false, "label": "必选", "type": "true_or_false", "is_abstract": null, "is_required": true, "api_name": "is_required", "options": [{"label": "是", "value": true, "config": {"edit": 0, "enable": 0, "remove": 0}}, {"label": "否", "value": false, "config": {"edit": 0, "enable": 0, "remove": 0}}], "define_type": "package", "is_index_field": false, "is_single": false, "config": {"add": 0, "edit": 1, "enable": 0, "display": 1, "remove": 0, "attrs": {"api_name": 0, "options": 0, "is_unique": 0, "default_value": 1, "label": 0, "is_required": 0, "help_text": 1}}, "help_text": "", "status": "released"}, "selected_by_default": {"describe_api_name": "BOMObj", "is_index": true, "is_active": true, "is_unique": false, "default_value": false, "label": "默认选中", "type": "true_or_false", "is_abstract": null, "is_required": true, "api_name": "selected_by_default", "options": [{"label": "是", "value": true, "config": {"edit": 0, "enable": 0, "remove": 0}}, {"label": "否", "value": false, "config": {"edit": 0, "enable": 0, "remove": 0}}], "define_type": "package", "is_index_field": false, "is_single": false, "config": {"add": 0, "edit": 1, "enable": 0, "display": 1, "remove": 0, "attrs": {"api_name": 0, "options": 0, "is_unique": 0, "default_value": 1, "label": 0, "is_required": 0, "help_text": 1}}, "help_text": "", "status": "released"}, "price_editable": {"describe_api_name": "BOMObj", "is_index": true, "is_active": true, "is_unique": false, "default_value": false, "label": "价格可编辑", "type": "true_or_false", "is_abstract": null, "is_required": true, "api_name": "price_editable", "options": [{"label": "是", "value": true, "config": {"edit": 0, "enable": 0, "remove": 0}}, {"label": "否", "value": false, "config": {"edit": 0, "enable": 0, "remove": 0}}], "define_type": "package", "is_index_field": false, "is_single": false, "config": {"add": 0, "edit": 1, "enable": 0, "display": 1, "remove": 0, "attrs": {"api_name": 0, "options": 0, "is_unique": 0, "default_value": 1, "label": 0, "is_required": 0, "help_text": 1}}, "help_text": "", "status": "released"}, "amount_editable": {"describe_api_name": "BOMObj", "is_index": true, "is_active": true, "is_unique": false, "default_value": true, "label": "数量可编辑", "type": "true_or_false", "is_abstract": null, "is_required": true, "api_name": "amount_editable", "options": [{"label": "是", "value": true, "config": {"edit": 0, "enable": 0, "remove": 0}}, {"label": "否", "value": false, "config": {"edit": 0, "enable": 0, "remove": 0}}], "define_type": "package", "is_index_field": false, "is_single": false, "config": {"add": 0, "edit": 1, "enable": 0, "display": 1, "remove": 0, "attrs": {"api_name": 0, "options": 0, "is_unique": 0, "default_value": 1, "label": 0, "is_required": 0, "help_text": 1}}, "help_text": "", "status": "released"}, "min_amount": {"describe_api_name": "BOMObj", "default_is_expression": false, "is_unique": false, "type": "number", "decimal_places": 0, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "max_length": 14, "is_index": true, "is_active": true, "length": 14, "default_value": "", "label": "最小数量", "is_abstract": null, "api_name": "min_amount", "is_index_field": false, "config": {"add": 0, "edit": 1, "enable": 0, "display": 1, "remove": 0, "attrs": {"api_name": 0, "is_unique": 0, "default_value": 1, "label": 0, "help_text": 1, "max_length": 1, "decimal_places": 0, "is_required": 0}}, "round_mode": 4, "help_text": "", "status": "released"}, "max_amount": {"describe_api_name": "BOMObj", "default_is_expression": false, "is_unique": false, "type": "number", "decimal_places": 0, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "max_length": 14, "is_index": true, "is_active": true, "length": 14, "default_value": "", "label": "最大数量", "is_abstract": null, "api_name": "max_amount", "is_index_field": false, "config": {"add": 0, "edit": 1, "enable": 0, "display": 1, "remove": 0, "attrs": {"api_name": 0, "is_unique": 0, "default_value": 1, "label": 0, "help_text": 1, "max_length": 1, "is_required": 0, "decimal_places": 0}}, "round_mode": 4, "help_text": "", "status": "released"}, "increment": {"describe_api_name": "BOMObj", "default_is_expression": false, "is_unique": false, "type": "number", "decimal_places": 0, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "max_length": 14, "is_index": true, "is_active": true, "length": 14, "default_value": "1", "label": "增加数量幅度", "is_abstract": null, "api_name": "increment", "is_index_field": false, "config": {"add": 0, "edit": 1, "enable": 0, "display": 1, "remove": 0, "attrs": {"api_name": 0, "is_unique": 0, "default_value": 1, "label": 0, "help_text": 1, "max_length": 1, "is_required": 0, "decimal_places": 0}}, "round_mode": 4, "help_text": "", "status": "released"}, "enabled_status": {"describe_api_name": "BOMObj", "is_index": true, "is_active": true, "is_unique": false, "default_value": true, "label": "启用状态", "type": "true_or_false", "is_abstract": null, "is_required": true, "api_name": "enabled_status", "options": [{"label": "是", "value": true, "config": {"edit": 0, "enable": 0, "remove": 0}}, {"label": "否", "value": false, "config": {"edit": 0, "enable": 0, "remove": 0}}], "define_type": "package", "is_index_field": false, "is_single": false, "config": {"add": 0, "edit": 1, "enable": 0, "display": 1, "remove": 0, "attrs": {"api_name": 1, "options": 0, "is_unique": 0, "default_value": 0, "label": 0, "is_required": 0, "help_text": 1}}, "help_text": "", "status": "released"}, "remark": {"expression_type": "long_text", "default_is_expression": false, "is_index": true, "is_active": true, "pattern": "", "is_unique": false, "default_value": "", "label": "备注", "type": "long_text", "default_to_zero": false, "is_required": false, "api_name": "remark", "define_type": "package", "help_text": "", "max_length": 2000, "status": "released", "is_extend": false, "config": {"add": 0, "edit": 1, "enable": 0, "display": 1, "remove": 0, "attrs": {"api_name": 0, "is_unique": 0, "default_value": 1, "label": 0, "is_required": 0, "help_text": 1}}}, "order_field": {"describe_api_name": "BOMObj", "is_unique": false, "type": "number", "decimal_places": 0, "default_to_zero": true, "is_required": false, "define_type": "package", "max_length": 14, "is_index": true, "is_active": true, "length": 14, "default_value": "", "label": "序号", "api_name": "order_field", "config": {"add": 0, "edit": 1, "enable": 0, "display": 1, "remove": 0, "attrs": {"api_name": 0, "is_unique": 0, "label": 0, "help_text": 1, "max_length": 1, "decimal_places": 0, "is_required": 0}}, "round_mode": 4, "help_text": "", "status": "released"}, "product_group_id": {"describe_api_name": "BOMObj", "default_is_expression": false, "is_index": true, "is_active": true, "is_unique": false, "label": "产品分组", "target_api_name": "ProductGroupObj", "type": "object_reference", "target_related_list_name": "target_related_list_product_group_id", "is_abstract": null, "target_related_list_label": "产品选配明细", "action_on_target_delete": "cascade_delete", "is_required": false, "wheres": [], "api_name": "product_group_id", "define_type": "package", "is_index_field": true, "is_single": false, "config": {"add": 0, "edit": 0, "enable": 0, "display": 1, "remove": 0, "attrs": {"target_related_list_label": 0, "wheres": 0, "api_name": 0, "is_unique": 0, "default_value": 0, "label": 0, "target_api_name": 0, "target_related_list_name": 0, "help_text": 0}}, "help_text": "", "status": "released"}, "product_status": {"is_index": false, "is_active": true, "quote_field_type": "select_one", "is_unique": false, "label": "上下架", "type": "quote", "quote_field": "product_id__r.product_status", "is_required": false, "api_name": "product_status", "define_type": "package", "help_text": "", "status": "released", "is_extend": false}, "product_life_status": {"is_index": false, "is_active": true, "quote_field_type": "select_one", "is_unique": false, "label": "产品生命状态", "type": "quote", "quote_field": "product_id__r.life_status", "is_required": false, "api_name": "product_life_status", "define_type": "package", "help_text": "", "status": "released", "is_extend": false}, "created_by": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "创建人", "api_name": "created_by", "description": "created_by", "status": "released", "is_extend": false}, "last_modified_by": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "最后修改人", "api_name": "last_modified_by", "description": "last_modified_by", "status": "released", "is_extend": false}, "package": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "package", "api_name": "package", "description": "package", "status": "released", "is_extend": false}, "tenant_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "tenant_id", "api_name": "tenant_id", "description": "tenant_id", "status": "released", "is_extend": false}, "object_describe_api_name": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "object_describe_api_name", "api_name": "object_describe_api_name", "description": "object_describe_api_name", "status": "released", "is_extend": false}, "object_describe_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "object_describe_id", "api_name": "object_describe_id", "description": "object_describe_id", "status": "released", "is_extend": false}, "version": {"type": "number", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "round_mode": 4, "length": 8, "decimal_places": 0, "label": "version", "api_name": "version", "description": "version", "status": "released", "is_extend": false}, "create_time": {"type": "date_time", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "创建时间", "api_name": "create_time", "description": "create_time", "status": "released", "is_extend": false}, "last_modified_time": {"type": "date_time", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "最后修改时间", "api_name": "last_modified_time", "description": "last_modified_time", "status": "released", "is_extend": false}, "is_deleted": {"type": "true_or_false", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "label": "is_deleted", "api_name": "is_deleted", "description": "is_deleted", "default_value": false, "status": "released", "is_extend": false}, "out_tenant_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "外部企业", "api_name": "out_tenant_id", "description": "out_tenant_id", "status": "released", "config": {"display": 0}}, "owner": {"describe_api_name": "BOMObj", "is_index": true, "is_active": true, "is_unique": false, "label": "负责人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "owner", "define_type": "package", "is_index_field": false, "is_single": true, "config": {"add": 0, "edit": 0, "enable": 0, "display": 1, "remove": 0, "attrs": {"label": 0}}, "help_text": "", "status": "new"}, "owner_department": {"describe_api_name": "BOMObj", "default_is_expression": false, "pattern": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "is_single": true, "max_length": 100, "is_index": true, "is_active": true, "default_value": "", "label": "负责人主属部门", "is_need_convert": false, "api_name": "owner_department", "is_index_field": false, "config": {"add": 0, "edit": 0, "enable": 0, "display": 1, "remove": 0, "attrs": {"label": 0}}, "help_text": "", "status": "new"}, "life_status": {"describe_api_name": "BOMObj", "is_index": true, "is_active": true, "description": "", "is_unique": false, "default_value": "normal", "label": "生命状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "life_status", "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "is_index_field": false, "is_single": false, "config": {"add": 0, "edit": 0, "enable": 0, "display": 1, "remove": 0, "attrs": {"is_required": 0, "options": 0, "default_value": 0, "label": 0, "help_text": 0}}, "help_text": "", "status": "new"}, "life_status_before_invalid": {"describe_api_name": "BOMObj", "is_index": false, "is_active": true, "pattern": "", "description": "作废前生命状态", "is_unique": false, "label": "作废前生命状态", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "life_status_before_invalid", "define_type": "package", "is_index_field": false, "is_single": false, "max_length": 256, "status": "new"}, "lock_rule": {"describe_api_name": "BOMObj", "is_index": false, "is_active": true, "description": "锁定规则", "is_unique": false, "default_value": "default_lock_rule", "rules": [], "label": "锁定规则", "type": "lock_rule", "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "is_index_field": false, "is_single": false, "status": "new"}, "lock_status": {"describe_api_name": "BOMObj", "is_index": true, "is_active": true, "description": "锁定状态", "is_unique": false, "default_value": "0", "label": "锁定状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "lock_status", "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "is_index_field": false, "is_single": false, "config": {}, "status": "new"}, "lock_user": {"describe_api_name": "BOMObj", "is_index": false, "is_active": true, "description": "加锁人", "is_unique": false, "label": "加锁人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "lock_user", "define_type": "package", "is_index_field": false, "is_single": true, "status": "new"}, "out_owner": {"type": "employee", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "is_single": true, "api_name": "out_owner", "status": "released", "label": "外部负责人", "config": {"display": 1}}, "relevant_team": {"describe_api_name": "BOMObj", "embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": true, "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": false, "is_active": true, "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "is_index_field": false, "is_single": false, "help_text": "相关团队", "status": "new"}, "record_type": {"describe_api_name": "BOMObj", "is_index": true, "is_active": true, "description": "record_type", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "is_index_field": false, "is_single": false, "config": {"add": 0, "edit": 0, "enable": 0, "display": 1, "remove": 0, "attrs": {"label": 0}}, "index_name": "record_type", "help_text": "", "status": "released"}, "extend_obj_data_id": {"default_is_expression": false, "is_index": false, "is_active": true, "pattern": "", "description": "extend_obj_data_id", "default_value": "", "type": "text", "label": "extend_obj_data_id", "default_to_zero": false, "is_required": false, "api_name": "extend_obj_data_id", "define_type": "system", "help_text": "", "max_length": 100, "status": "released", "is_extend": false}}, "validate_rules": {}, "triggers": {}, "actions": {}, "index_version": 1, "api_name": "BOMObj", "display_name": "产品选配明细", "package": "CRM", "define_type": "package", "is_active": true, "icon_index": 0, "store_table_name": "biz_bom", "is_deleted": false, "config": {"record_type": {"add": 0, "assign": 0}, "layout": {"add": 1, "assign": 1}}}