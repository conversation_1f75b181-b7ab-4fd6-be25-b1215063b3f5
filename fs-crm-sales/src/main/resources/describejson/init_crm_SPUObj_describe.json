{"fields": {"name": {"is_index": true, "is_active": true, "is_unique": true, "default_value": "", "label": "商品名称", "type": "text", "is_required": true, "api_name": "name", "define_type": "package", "status": "released", "is_extend": false, "config": {"edit": 1, "enable": 0, "display": 1, "remove": 0}}, "standard_price": {"type": "currency", "define_type": "package", "is_index": true, "is_active": true, "round_mode": 4, "length": 14, "is_need_convert": false, "decimal_places": 2, "label": "标准价格", "api_name": "standard_price", "is_unique": false, "description": "标准价格", "status": "released", "is_required": true, "is_extend": false, "config": {"edit": 1, "enable": 0, "display": 1, "remove": 0, "attrs": {"is_required": 0, "api_name": 0, "is_unique": 0, "default_value": 0, "label": 0, "help_text": 0, "max_length": 1, "decimal_places": 1}}}, "extend_obj_data_id": {"describe_api_name": "SPUObj", "default_is_expression": false, "is_index": false, "is_active": true, "pattern": "", "description": "extend_obj_data_id", "is_unique": false, "default_value": "", "label": "extend_obj_data_id", "type": "text", "field_num": null, "default_to_zero": false, "is_required": false, "api_name": "extend_obj_data_id", "define_type": "system", "is_extend": false, "is_index_field": false, "is_single": false, "help_text": "", "max_length": 100, "status": "released"}, "unit": {"is_index": true, "is_active": true, "description": "单位", "is_unique": false, "label": "单位", "type": "select_one", "field_num": null, "is_need_convert": false, "is_required": true, "api_name": "unit", "options": [{"label": "个", "value": "1", "config": {"edit": 1, "remove": 1, "enable": 1}}, {"label": "块", "value": "2", "config": {"edit": 1, "remove": 1, "enable": 1}}, {"label": "只", "value": "3", "config": {"edit": 1, "remove": 1, "enable": 1}}, {"label": "把", "value": "4", "config": {"edit": 1, "remove": 1, "enable": 1}}, {"label": "枚", "value": "5", "config": {"edit": 1, "remove": 1, "enable": 1}}, {"label": "条", "value": "6", "config": {"edit": 1, "remove": 1, "enable": 1}}, {"label": "瓶", "value": "7", "config": {"edit": 1, "remove": 1, "enable": 1}}, {"label": "盒", "value": "8", "config": {"edit": 1, "remove": 1, "enable": 1}}, {"label": "套", "value": "9", "config": {"edit": 1, "remove": 1, "enable": 1}}, {"label": "箱", "value": "10", "config": {"edit": 1, "remove": 1, "enable": 1}}, {"label": "米", "value": "11", "config": {"edit": 1, "remove": 1, "enable": 1}}, {"label": "千克", "value": "12", "config": {"edit": 1, "remove": 1, "enable": 1}}, {"label": "吨", "value": "13", "config": {"edit": 1, "remove": 1, "enable": 1}}], "define_type": "package", "is_index_field": false, "is_single": false, "config": {"add": 1, "edit": 1, "enable": 0, "display": 1, "remove": 1, "attrs": {"options": 1}}, "status": "released"}, "category": {"type": "select_one", "define_type": "package", "is_index": true, "is_need_convert": false, "api_name": "category", "expression_type": "tree", "is_required": true, "status": "released", "label": "分类", "is_unique": false, "description": "分类", "is_extend": false, "config": {"edit": 1, "enable": 0, "display": 1, "remove": 0, "attrs": {"is_required": 0}}, "options": [{"label": "办公用品", "value": "1"}, {"label": "数码产品", "value": "2"}, {"label": "服务", "value": "3"}, {"label": "家用电器", "value": "4"}, {"label": "家居用品", "value": "5"}]}, "picture": {"type": "image", "define_type": "package", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "file_amount_limit": 1, "file_size_limit": 10485760, "support_file_types": [], "api_name": "picture", "status": "released", "label": "图片", "description": "图片", "config": {"add": 0, "edit": 1, "enable": 1, "display": 1, "remove": 0, "attrs": {"file_amount_limit": 1, "api_name": 0, "label": 1, "is_watermark": 1, "help_text": 1}}}, "product_line": {"type": "select_one", "define_type": "package", "is_index": true, "is_need_convert": false, "pattern": "", "api_name": "product_line", "is_required": false, "status": "released", "label": "产品线", "is_unique": false, "description": "产品线", "is_extend": false, "config": {"edit": 1, "add": 1, "enable": 1, "display": 1, "remove": 0, "attrs": {"options": 1}}, "options": [{"label": "产品线1", "value": "1", "config": {"edit": 1, "remove": 1, "enable": 1}}, {"label": "产品线2", "value": "2", "config": {"edit": 1, "remove": 1, "enable": 1}}, {"label": "产品线3", "value": "3", "config": {"edit": 1, "remove": 1, "enable": 1}}]}, "remark": {"type": "long_text", "define_type": "package", "is_index": true, "is_need_convert": false, "max_length": 2000, "pattern": "", "api_name": "remark", "is_required": false, "status": "released", "label": "备注", "is_unique": false, "description": "备注", "is_extend": false, "config": {"edit": 1, "enable": 1, "display": 1, "remove": 1}}, "is_spec": {"type": "true_or_false", "define_type": "package", "is_index": true, "is_need_convert": false, "pattern": "", "api_name": "is_spec", "is_required": true, "status": "released", "label": "是否有规格", "is_unique": false, "description": "是否有规格", "is_extend": false, "default_value": "0", "config": {"edit": 1, "enable": 0, "display": 1, "remove": 0, "attrs": {"is_required": 0}}, "options": [{"label": "否", "value": false, "config": {"edit": 1, "remove": 0, "enable": 0}}, {"label": "是", "value": true, "config": {"edit": 1, "remove": 0, "enable": 0}}]}, "order_field": {"type": "number", "define_type": "package", "is_index": true, "is_need_convert": false, "api_name": "order_field", "label": "排序", "is_unique": false, "is_required": false, "description": "排序", "max_length": 100}, "batch_sn": {"type": "select_one", "define_type": "package", "is_index": true, "is_active": false, "is_need_convert": false, "api_name": "batch_sn", "is_required": true, "status": "released", "label": "批次与序列号管理", "is_unique": false, "default_value": "1", "description": "批次与序列号管理", "is_extend": false, "config": {"edit": 1, "enable": 0, "display": 1, "remove": 0, "attrs": {"is_required": 0}}, "options": [{"label": "不开启", "value": "1"}, {"label": "开启批次管理", "value": "2"}, {"label": "开启序列号管理", "value": "3"}]}, "created_by": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "创建人", "api_name": "created_by", "description": "created_by", "resource_bundle_key": "SpecificationValueObj.created_by", "status": "released", "is_extend": false}, "last_modified_by": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "最后修改人", "api_name": "last_modified_by", "description": "last_modified_by", "resource_bundle_key": "SpecificationValueObj.last_modified_by", "status": "released", "is_extend": false}, "package": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "package", "api_name": "package", "description": "package", "resource_bundle_key": "SpecificationValueObj.package", "status": "released", "is_extend": false}, "tenant_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "tenant_id", "api_name": "tenant_id", "description": "tenant_id", "resource_bundle_key": "SpecificationValueObj.tenant_id", "status": "released", "is_extend": false}, "object_describe_api_name": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "object_describe_api_name", "api_name": "object_describe_api_name", "description": "object_describe_api_name", "resource_bundle_key": "SpecificationValueObj.object_describe_api_name", "status": "released", "is_extend": false}, "object_describe_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "object_describe_id", "api_name": "object_describe_id", "description": "object_describe_id", "resource_bundle_key": "SpecificationValueObj.object_describe_id", "status": "released", "is_extend": false}, "version": {"type": "number", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "round_mode": 4, "length": 8, "decimal_places": 0, "label": "version", "api_name": "version", "description": "version", "resource_bundle_key": "SpecificationValueObj.version", "status": "released", "is_extend": false}, "create_time": {"type": "date_time", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "创建时间", "api_name": "create_time", "description": "create_time", "resource_bundle_key": "SpecificationValueObj.create_time", "status": "released", "is_extend": false}, "last_modified_time": {"type": "date_time", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "最后修改时间", "api_name": "last_modified_time", "description": "last_modified_time", "resource_bundle_key": "SpecificationValueObj.last_modified_time", "status": "released", "is_extend": false}, "is_deleted": {"type": "true_or_false", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "label": "is_deleted", "api_name": "is_deleted", "description": "is_deleted", "default_value": false, "status": "released", "is_extend": false}, "out_tenant_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "is_extend": false, "pattern": "", "label": "外部企业", "api_name": "out_tenant_id", "description": "out_tenant_id", "status": "released", "config": {"display": 0}}, "owner": {"describe_api_name": "SPUObj", "is_index": true, "is_active": true, "is_unique": false, "is_extend": false, "label": "负责人", "type": "employee", "is_need_convert": false, "is_required": true, "api_name": "owner", "define_type": "package", "is_index_field": false, "is_single": true, "config": {"add": 0, "edit": 0, "enable": 0, "display": 1, "remove": 0, "attrs": {"label": 0}}, "help_text": "", "status": "new"}, "owner_department": {"describe_api_name": "SPUObj", "default_is_expression": false, "pattern": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "is_single": true, "is_extend": false, "index_name": "string_4", "max_length": 100, "is_index": false, "is_active": true, "default_value": "", "label": "负责人主属部门", "is_need_convert": false, "api_name": "owner_department", "is_index_field": false, "config": {"add": 0, "edit": 0, "enable": 0, "display": 1, "remove": 0, "attrs": {"label": 0}}, "help_text": "", "status": "new"}, "life_status": {"describe_api_name": "SPUObj", "is_index": true, "is_active": true, "description": "", "is_unique": false, "default_value": "normal", "label": "生命状态", "type": "select_one", "is_need_convert": false, "is_extend": false, "is_required": false, "api_name": "life_status", "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "is_index_field": false, "is_single": false, "config": {"add": 0, "edit": 0, "enable": 0, "display": 1, "remove": 0, "attrs": {"is_required": 0, "options": 0, "default_value": 0, "label": 0, "help_text": 0}}, "help_text": "", "status": "new"}, "life_status_before_invalid": {"describe_api_name": "SPUObj", "is_index": false, "is_active": true, "pattern": "", "description": "作废前生命状态", "is_unique": false, "label": "作废前生命状态", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "life_status_before_invalid", "define_type": "package", "is_index_field": false, "is_extend": false, "is_single": false, "max_length": 256, "status": "new"}, "lock_rule": {"describe_api_name": "SPUObj", "is_index": false, "is_active": true, "description": "锁定规则", "is_unique": false, "default_value": "default_lock_rule", "rules": [], "label": "锁定规则", "type": "lock_rule", "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "is_extend": false, "define_type": "package", "is_index_field": false, "is_single": false, "status": "new"}, "lock_status": {"describe_api_name": "SPUObj", "is_index": true, "is_active": true, "description": "锁定状态", "is_unique": false, "default_value": "0", "label": "锁定状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "lock_status", "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "is_index_field": false, "is_single": false, "is_extend": false, "config": {}, "status": "new"}, "lock_user": {"describe_api_name": "SPUObj", "is_index": false, "is_active": true, "description": "加锁人", "is_unique": false, "label": "加锁人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "lock_user", "define_type": "package", "is_extend": false, "is_index_field": false, "is_single": true, "status": "new"}, "out_owner": {"type": "employee", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "is_single": true, "api_name": "out_owner", "is_extend": false, "status": "released", "label": "外部负责人", "config": {"display": 1}}, "relevant_team": {"describe_api_name": "SPUObj", "embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": true, "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": false, "is_active": true, "is_extend": false, "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "is_index_field": false, "is_single": false, "help_text": "相关团队", "status": "new"}, "record_type": {"describe_api_name": "SPUObj", "is_index": true, "is_active": true, "is_extend": false, "description": "record_type", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": true, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "is_index_field": false, "is_single": false, "config": {"add": 0, "edit": 0, "enable": 0, "display": 1, "remove": 0, "attrs": {"label": 0}}, "index_name": "record_type", "help_text": "", "status": "released"}}, "actions": {"ChangeOwner": {"action_class": "ChangeOwnerAction", "source_type": "java_spring", "action_code": "ChangeOwner"}, "Lock": {"action_class": "LockAction", "source_type": "java_spring", "action_code": "Lock", "label": "加锁"}, "Unlock": {"action_class": "UnlockAction", "source_type": "java_spring", "action_code": "Unlock", "label": "解锁"}}, "validate_rules": {}, "triggers": {}, "index_version": 1, "package": "CRM", "api_name": "SPUObj", "display_name": "商品", "is_deleted": false, "store_table_name": "stand_prod_unit", "define_type": "package"}