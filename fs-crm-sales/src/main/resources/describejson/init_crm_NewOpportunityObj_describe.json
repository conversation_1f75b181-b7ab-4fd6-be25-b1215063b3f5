{"fields": {"owner": {"is_index": true, "is_active": true, "is_unique": false, "label": "负责人", "type": "employee", "is_need_convert": false, "is_required": true, "api_name": "owner", "define_type": "package", "is_index_field": false, "is_single": true, "config": {"add": 0, "edit": 0, "enable": 0, "display": 1, "remove": 0, "attrs": {}}, "help_text": "", "status": "released"}, "lock_rule": {"is_index": false, "is_active": true, "description": "锁定规则", "is_unique": false, "default_value": "default_lock_rule", "rules": [], "label": "锁定规则", "type": "lock_rule", "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "is_index_field": false, "is_single": false, "status": "released"}, "lock_status": {"is_index": true, "is_active": true, "description": "锁定状态", "is_unique": false, "default_value": "0", "label": "锁定状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "lock_status", "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "is_index_field": false, "is_single": false, "config": {}, "status": "released"}, "life_status": {"is_index": true, "is_active": true, "description": "生命状态", "is_unique": false, "default_value": "normal", "label": "生命状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "life_status", "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "is_single": false, "status": "released"}, "life_status_before_invalid": {"is_index": false, "is_active": true, "pattern": "", "description": "作废前生命状态", "is_unique": false, "label": "作废前生命状态", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "life_status_before_invalid", "define_type": "package", "is_index_field": false, "is_single": false, "max_length": 256, "status": "released"}, "owner_department": {"default_is_expression": false, "is_index": true, "is_active": true, "pattern": "", "is_unique": false, "default_value": "", "label": "负责人主属部门", "type": "text", "default_to_zero": false, "is_need_convert": false, "is_required": false, "api_name": "owner_department", "define_type": "package", "is_single": true, "help_text": "", "max_length": 100, "status": "released"}, "lock_user": {"is_index": false, "is_active": true, "description": "加锁人", "is_unique": false, "label": "加锁人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "lock_user", "define_type": "package", "is_single": true, "status": "released"}, "record_type": {"is_index": true, "is_active": true, "description": "record_type", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": true, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "is_single": false, "status": "released"}, "name": {"default_is_expression": false, "is_index": true, "is_active": true, "pattern": "", "is_unique": true, "default_value": "", "label": "商机名称", "type": "text", "default_to_zero": false, "is_required": true, "api_name": "name", "define_type": "package", "is_index_field": false, "is_single": false, "help_text": "", "max_length": 100, "status": "released"}, "leads_id": {"type": "object_reference", "define_type": "package", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": true, "api_name": "leads_id", "label": "主线索", "description": "主线索", "status": "released", "target_api_name": "LeadsObj", "action_on_target_delete": "cascade_delete", "target_related_list_label": "商机2.0名称", "target_related_list_name": "leads_new_opportunity_list", "is_active": true, "wheres": [{"filters": [{"field_name": "biz_status", "field_values": ["transformed"], "operator": "EQ"}]}], "config": {"display": 1, "edit": 0, "attrs": {"default_value": 1, "wheres": 0, "help_text": 1, "is_required": false}}}, "account_id": {"is_index": true, "is_active": true, "is_unique": false, "label": "客户名称", "target_api_name": "AccountObj", "type": "object_reference", "target_related_list_name": "account_new_opportunity_obj_list", "target_related_list_label": "商机2.0", "action_on_target_delete": "set_null", "is_required": true, "wheres": [], "api_name": "account_id", "define_type": "package", "is_index_field": true, "is_single": false, "help_text": "", "config": {"add": 0, "edit": 1, "enable": 0, "display": 1, "remove": 0, "attrs": {"is_required": 0, "target_related_list_label": 1, "wheres": 1, "api_name": 0, "is_unique": 0, "default_value": 1, "label": 0, "target_api_name": 0, "target_related_list_name": 1, "help_text": 1}}, "status": "released"}, "amount": {"describe_api_name": "NewOpportunityObj", "default_is_expression": true, "is_unique": false, "type": "currency", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "package", "is_extend": false, "is_single": false, "index_name": "double_1", "max_length": 14, "is_index": true, "is_active": true, "create_time": *************, "length": 12, "default_value": "$opp_discount$*$opp_lines_sum$", "label": "商机金额", "currency_unit": "￥", "is_abstract": null, "field_num": null, "api_name": "amount", "_id": "5bffcdce319d19c28cfd7179", "is_index_field": false, "config": {"add": 0, "edit": 1, "enable": 0, "display": 1, "remove": 0, "attrs": {"api_name": 0, "formula": 1, "label": 0, "help_text": 1, "default_value": 1, "max_length": 1, "decimal_places": 1}}, "round_mode": 4, "help_text": "", "status": "released"}, "probability_amount": {"describe_api_name": "NewOpportunityObj", "expression_type": "js", "return_type": "currency", "description": "预测金额", "is_unique": false, "type": "formula", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "max_length": 14, "is_index": true, "expression": "$amount$*$probability$", "is_active": true, "create_time": 1554885370183, "label": "预测金额", "is_abstract": null, "field_num": null, "is_need_convert": false, "api_name": "probability_amount", "is_index_field": false, "config": {"add": 0, "edit": 1, "enable": 1, "display": 1, "remove": 0, "attrs": {"api_name": 0, "formula": 1, "is_unique": 0, "default_value": 1, "label": 1, "help_text": 1, "max_length": 1, "decimal_places": 1}}, "round_mode": 4, "help_text": "商机金额*赢率", "status": "released"}, "cost_time": {"describe_api_name": "NewOpportunityObj", "expression_type": "js", "return_type": "number", "description": "结单周期", "is_unique": false, "type": "formula", "decimal_places": 0, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "max_length": 14, "is_index": true, "expression": "IF(OR($sales_status$=='赢单',$sales_status$=='输单',$sales_status$=='无效'), $stg_changed_time$-$create_time$, 0)*3600000", "is_active": true, "create_time": 1554885370183, "label": "结单周期", "is_abstract": null, "field_num": null, "is_need_convert": false, "api_name": "cost_time", "is_index_field": false, "config": {"display": 0}, "round_mode": 4, "status": "released"}, "lost_reason": {"is_index": true, "is_active": true, "description": "输单原因", "is_unique": false, "default_value": null, "label": "输单原因", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "lost_reason", "options": [{"not_usable": false, "label": "客户不想买了", "value": "0", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"not_usable": false, "label": "未满足客户需求", "value": "1", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"not_usable": false, "label": "被竞争对手抢单", "value": "2", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"not_usable": true, "label": "其他", "value": "other", "config": {"edit": 1, "enable": 1, "remove": 1}}], "define_type": "package", "is_index_field": false, "is_single": false, "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "options": 1, "is_unique": 1, "default_value": 1, "label": 1, "help_text": 1}}, "status": "released"}, "sales_process_id": {"is_index": true, "is_active": true, "is_unique": false, "default_value": "", "label": "销售流程", "type": "select_one", "is_required": true, "api_name": "sales_process_id", "define_type": "package", "is_index_field": false, "is_single": false, "config": {"add": 0, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "options": 1, "is_unique": 1, "is_required": 0, "default_value": 0, "label": 1, "help_text": 1}}, "help_text": "", "status": "released"}, "close_date": {"is_index": true, "is_active": true, "is_unique": false, "default_is_expression": true, "label": "结单日期", "time_zone": "GMT+8", "type": "date", "is_required": true, "api_name": "close_date", "define_type": "package", "date_format": "yyyy-MM-dd", "default_value": "$currentDate__g$+DAYS(7)", "is_index_field": false, "is_single": false, "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "default_value": 1, "is_unique": 1, "label": 1, "help_text": 1}}, "help_text": "", "status": "released"}, "probability": {"default_is_expression": false, "is_index": true, "is_active": true, "is_unique": false, "default_value": "", "label": "赢率", "type": "percentile", "default_to_zero": true, "is_required": false, "api_name": "probability", "define_type": "package", "is_index_field": false, "is_single": false, "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "is_unique": 1, "default_value": 1, "label": 1, "help_text": 1}}, "help_text": "", "status": "released"}, "stg_changed_time": {"is_index": true, "is_active": true, "is_unique": false, "default_value": "", "label": "阶段变更时间", "time_zone": "GMT+8", "type": "date_time", "is_required": false, "api_name": "stg_changed_time", "define_type": "package", "date_format": "yyyy-MM-dd HH:mm:ss", "is_index_field": false, "is_single": false, "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "is_unique": 1, "label": 1, "help_text": 1}}, "help_text": "", "status": "released"}, "opp_lines_sum": {"return_type": "number", "is_index": true, "is_active": true, "count_type": "sum", "is_unique": false, "count_field_api_name": "total_amount", "label": "产品合计", "type": "count", "decimal_places": 2, "sub_object_describe_apiname": "NewOpportunityLinesObj", "is_required": false, "api_name": "opp_lines_sum", "define_type": "package", "count_field_type": "currency", "is_single": false, "help_text": "", "status": "released", "config": {"edit": 1, "enable": 1, "attrs": {"decimal_places": 1, "default_value": 1, "help_text": 1, "label": 0, "max_length": 1}}}, "opp_discount": {"expression_type": "percentile", "default_is_expression": true, "is_index": true, "is_active": true, "is_unique": false, "default_value": "1", "label": "整单折扣", "type": "percentile", "default_to_zero": true, "is_required": false, "api_name": "opp_discount", "define_type": "package", "is_single": false, "help_text": "", "status": "released", "config": {"edit": 1, "enable": 1, "attrs": {"decimal_places": 1, "default_value": 1, "help_text": 1, "label": 0, "max_length": 1}}}, "price_book_id": {"is_index": true, "is_active": false, "is_unique": false, "label": "价目表", "target_api_name": "PriceBookObj", "type": "object_reference", "target_related_list_name": "price_book_new_opportunity_list", "target_related_list_label": "商机2.0", "action_on_target_delete": "set_null", "is_required": false, "wheres": [], "api_name": "price_book_id", "define_type": "package", "is_index_field": true, "is_single": false, "config": {"edit": 1, "display": 0}, "help_text": "", "status": "released"}, "sales_stage": {"is_index": true, "is_active": true, "is_unique": false, "default_value": "", "label": "商机阶段", "type": "select_one", "is_required": true, "api_name": "sales_stage", "options": [{"not_usable": false, "label": "验证客户", "value": "1", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"not_usable": false, "label": "需求确定", "value": "2", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"not_usable": false, "label": "方案/报价", "value": "3", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"not_usable": false, "label": "谈判审核", "value": "4", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"not_usable": false, "label": "赢单", "value": "5", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"not_usable": false, "label": "输单", "value": "6", "config": {"edit": 1, "enable": 1, "remove": 1}}, {"not_usable": false, "label": "无效", "value": "7", "config": {"edit": 1, "enable": 1, "remove": 1}}], "define_type": "package", "is_index_field": false, "is_single": false, "config": {"add": 1, "edit": 1, "enable": 1, "display": 1, "remove": 1, "attrs": {"api_name": 1, "options": 1, "is_unique": 1, "default_value": 0, "label": 0, "is_required": 0, "help_text": 1}}, "help_text": "", "status": "released", "extend_info": {"1": {"probability": "10", "sales_status": "1"}, "2": {"probability": "30", "sales_status": "1"}, "3": {"probability": "60", "sales_status": "1"}, "4": {"probability": "80", "sales_status": "1"}, "5": {"probability": "100", "sales_status": "2"}, "6": {"probability": "0", "sales_status": "4"}, "7": {"probability": "0", "sales_status": "3"}}}, "sales_status": {"api_name": "sales_status", "define_type": "package", "description": "阶段状态", "index": false, "is_index": true, "is_need_convert": false, "is_required": false, "label": "阶段状态", "options": [{"label": "进行中", "resource_bundle_key": "", "value": "1"}, {"label": "赢单", "resource_bundle_key": "", "value": "2"}, {"label": "无效", "resource_bundle_key": "", "value": "3"}, {"label": "输单", "resource_bundle_key": "", "value": "4"}], "status": "released", "type": "select_one", "unique": false, "config": {"add": 0, "edit": 1, "enable": 0, "display": 1, "remove": 0, "attrs": {"is_required": 0, "api_name": 1, "options": 1, "is_unique": 1, "default_value": 0, "drag": 0, "label": 0, "help_text": 1}}}, "relevant_team": {"embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": true, "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "is_index_field": false, "is_single": false, "help_text": "相关团队", "status": "released"}, "created_by": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": true, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "创建人", "api_name": "created_by", "description": "created_by", "resource_bundle_key": "NewOpportunityObj.created_by", "status": "released"}, "last_modified_by": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": true, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "最后修改人", "api_name": "last_modified_by", "description": "last_modified_by", "resource_bundle_key": "NewOpportunityObj.last_modified_by", "status": "released"}, "package": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "package", "api_name": "package", "description": "package", "status": "released"}, "tenant_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "tenant_id", "api_name": "tenant_id", "description": "tenant_id", "status": "released"}, "object_describe_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "object_describe_id", "api_name": "object_describe_id", "description": "object_describe_id", "status": "released"}, "object_describe_api_name": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "object_describe_api_name", "api_name": "object_describe_api_name", "description": "object_describe_api_name", "status": "released"}, "version": {"type": "number", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "round_mode": 4, "length": 8, "decimal_places": 0, "label": "version", "api_name": "version", "description": "version", "status": "released"}, "create_time": {"type": "date_time", "define_type": "system", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "创建时间", "api_name": "create_time", "description": "create_time", "status": "released"}, "last_modified_time": {"type": "date_time", "define_type": "system", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "最后修改时间", "api_name": "last_modified_time", "description": "last_modified_time", "status": "released"}, "is_deleted": {"type": "true_or_false", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "label": "is_deleted", "api_name": "is_deleted", "description": "is_deleted", "default_value": false, "status": "released"}, "extend_obj_data_id": {"default_is_expression": false, "is_index": false, "is_active": true, "pattern": "", "description": "extend_obj_data_id", "default_value": "", "type": "text", "label": "extend_obj_data_id", "default_to_zero": false, "is_required": false, "api_name": "extend_obj_data_id", "define_type": "system", "help_text": "", "is_unique": false, "max_length": 100, "status": "released", "is_extend": false}, "partner_id": {"action_on_target_delete": "cascade_delete", "api_name": "partner_id", "create_time": 1527147613853, "define_type": "package", "describe_api_name": "NewOpportunityObj", "description": "合作伙伴", "field_num": null, "index_name": "string_1", "is_active": false, "is_index": true, "is_index_field": true, "is_need_convert": false, "is_required": false, "is_single": false, "is_unique": false, "label": "合作伙伴", "status": "new", "target_api_name": "PartnerObj", "target_related_list_label": "商机2.0", "target_related_list_name": "partner_newopportunity_list", "type": "object_reference", "config": {"display": 1}}, "out_resources": {"api_name": "out_resources", "config": {"display": 1}, "create_time": 1527147613868, "define_type": "package", "describe_api_name": "NewOpportunityObj", "description": "外部来源", "field_num": null, "is_active": false, "is_index": true, "is_index_field": false, "is_required": false, "is_single": false, "is_unique": false, "label": "外部来源", "options": [{"config": {}, "label": "PRM", "not_usable": false, "value": "partner"}], "status": "new", "type": "select_one"}, "last_followed_time": {"api_name": "last_followed_time", "create_time": 1527147613883, "date_format": "yyyy-MM-dd HH:mm:ss", "define_type": "package", "describe_api_name": "NewOpportunityObj", "description": "最后跟进时间", "field_num": null, "is_active": true, "is_index": true, "is_index_field": false, "is_need_convert": false, "is_required": false, "is_single": false, "is_unique": false, "label": "最后跟进时间", "status": "released", "time_zone": "GMT+8", "type": "date_time", "config": {"display": 1, "edit": 0, "enable": 0, "remove": 0, "attrs": {"default_value": 1, "wheres": 1, "help_text": 1, "is_required": 0}}}}, "actions": {"AddTeamMember": {"action_class": "AddTeamMemberCustomAction", "action_code": "AddTeamMember", "source_type": "java_spring", "label": "添加团队成员"}, "DeleteTeamMember": {"action_class": "DeleteTeamMemberCustomAction", "action_code": "DeleteTeamMember", "source_type": "java_spring", "label": "删除团队成员"}, "Unlock": {"action_class": "UnlockCustomAction", "action_code": "Unlock", "source_type": "java_spring", "label": "解锁"}, "EditTeamMember": {"action_class": "EditTeamMemberCustomAction", "action_code": "EditTeamMember", "source_type": "java_spring", "label": "编辑团队成员"}, "ChangeOwner": {"action_class": "ChangeOwnerCustomAction", "action_code": "ChangeOwner", "source_type": "java_spring", "label": "更换负责人"}, "Lock": {"action_class": "LockCustomAction", "action_code": "Lock", "source_type": "java_spring", "label": "锁定"}}, "index_version": 1, "api_name": "NewOpportunityObj", "display_name": "商机2.0", "package": "CRM", "define_type": "package", "is_active": true, "version": 1, "is_deleted": false, "revision": 1, "icon_index": 12, "store_table_name": "new_opportunity"}