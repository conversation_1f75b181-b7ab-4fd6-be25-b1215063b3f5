{"fields": {"owner": {"describe_api_name": "MatchNoteDetailObj", "auto_adapt_places": false, "description": "", "is_unique": false, "type": "employee", "is_required": true, "define_type": "package", "is_single": true, "is_extend": false, "index_name": "owner", "is_index": true, "is_active": true, "create_time": 1641546975106, "is_encrypted": false, "label": "负责人", "is_need_convert": false, "api_name": "owner", "_id": "61d804dfd3571d0001ce12db", "is_index_field": false, "help_text": "", "status": "released"}, "ar_detail_id": {"describe_api_name": "MatchNoteDetailObj", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "is_required": false, "wheres": [], "define_type": "package", "input_mode": "", "is_single": false, "is_extend": false, "index_name": "s_2", "is_index": true, "is_active": true, "create_time": *************, "is_encrypted": false, "default_value": "", "label": "应收单明细", "target_api_name": "AccountsReceivableDetailObj", "target_related_list_name": "target_related_list_match_note_detail_ar_detail", "target_related_list_label": "核销单明细", "action_on_target_delete": "set_null", "is_need_convert": false, "related_wheres": [], "api_name": "ar_detail_id", "_id": "6500087e322d500001c1ea16", "is_index_field": true, "help_text": "", "status": "released"}, "credit_detail_object": {"describe_api_name": "MatchNoteDetailObj", "auto_adapt_places": false, "description": "收付款对象明细", "is_unique": false, "group_type": "what", "type": "group", "is_required": false, "define_type": "package", "is_single": false, "index_name": "s_13", "is_index": true, "is_active": true, "id_field": "credit_detail_data_id", "create_time": *************, "is_encrypted": false, "label": "收付款对象明细", "api_name": "credit_detail_object", "_id": "6502ec8d322d500001c233a5", "is_index_field": false, "fields": {"id_field": "credit_detail_data_id", "api_name_field": "credit_detail_api_name"}, "help_text": "", "status": "released", "api_name_field": "credit_detail_api_name"}, "credit_api_name": {"describe_api_name": "MatchNoteDetailObj", "auto_adapt_places": false, "pattern": "", "description": "收付款对象", "is_unique": false, "type": "text", "used_in": "component", "is_required": false, "define_type": "package", "is_single": false, "index_name": "t_8", "max_length": 100, "is_index": true, "is_active": true, "create_time": *************, "is_encrypted": false, "label": "收付款对象", "is_need_convert": false, "api_name": "credit_api_name", "_id": "6502ec8d322d500001c233a6", "is_index_field": false, "status": "released"}, "debit_object": {"describe_api_name": "MatchNoteDetailObj", "auto_adapt_places": false, "description": "核销对象", "is_unique": false, "group_type": "what", "type": "group", "is_required": false, "define_type": "package", "is_single": false, "index_name": "s_8", "is_index": true, "is_active": true, "id_field": "debit_data_id", "create_time": *************, "is_encrypted": false, "label": "核销对象", "api_name": "debit_object", "_id": "6502ec8d322d500001c233a7", "is_index_field": false, "fields": {"id_field": "debit_data_id", "api_name_field": "debit_api_name"}, "help_text": "", "status": "released", "api_name_field": "debit_api_name"}, "credit_detail_data_id": {"describe_api_name": "MatchNoteDetailObj", "auto_adapt_places": false, "pattern": "", "description": "id", "is_unique": false, "type": "text", "used_in": "component", "is_required": false, "define_type": "package", "is_single": false, "index_name": "t_9", "max_length": 100, "is_index": true, "is_active": true, "create_time": *************, "is_encrypted": false, "label": "收付款对象明细单号", "is_need_convert": false, "api_name": "credit_detail_data_id", "_id": "6502ec8d322d500001c233a8", "is_index_field": false, "status": "released"}, "debit_api_name": {"describe_api_name": "MatchNoteDetailObj", "auto_adapt_places": false, "pattern": "", "description": "核销对象", "is_unique": false, "type": "text", "used_in": "component", "is_required": false, "define_type": "package", "is_single": false, "index_name": "t_3", "max_length": 100, "is_index": true, "is_active": true, "create_time": *************, "is_encrypted": false, "label": "核销对象", "is_need_convert": false, "api_name": "debit_api_name", "_id": "6502ec8d322d500001c233a9", "is_index_field": false, "status": "released"}, "debit_detail_object": {"describe_api_name": "MatchNoteDetailObj", "auto_adapt_places": false, "description": "核销对象明细", "is_unique": false, "group_type": "what", "type": "group", "is_required": false, "define_type": "package", "is_single": false, "index_name": "s_9", "is_index": true, "is_active": true, "id_field": "debit_detail_data_id", "create_time": *************, "is_encrypted": false, "label": "核销对象明细", "api_name": "debit_detail_object", "_id": "6502ec8d322d500001c233aa", "is_index_field": false, "fields": {"id_field": "debit_detail_data_id", "api_name_field": "debit_detail_api_name"}, "help_text": "", "status": "released", "api_name_field": "debit_detail_api_name"}, "debit_detail_api_name": {"describe_api_name": "MatchNoteDetailObj", "auto_adapt_places": false, "pattern": "", "description": "核销对象明细", "is_unique": false, "type": "text", "used_in": "component", "is_required": false, "define_type": "package", "is_single": false, "index_name": "t_4", "max_length": 100, "is_index": true, "is_active": true, "create_time": *************, "is_encrypted": false, "label": "核销对象明细", "is_need_convert": false, "api_name": "debit_detail_api_name", "_id": "6502ec8d322d500001c233ab", "is_index_field": false, "status": "released"}, "credit_object": {"describe_api_name": "MatchNoteDetailObj", "auto_adapt_places": false, "description": "收付款对象", "is_unique": false, "group_type": "what", "type": "group", "is_required": false, "define_type": "package", "is_single": false, "index_name": "s_14", "is_index": true, "is_active": true, "id_field": "credit_data_id", "create_time": *************, "is_encrypted": false, "label": "收付款对象", "api_name": "credit_object", "_id": "6502ec8d322d500001c233ac", "is_index_field": false, "fields": {"id_field": "credit_data_id", "api_name_field": "credit_api_name"}, "help_text": "", "status": "released", "api_name_field": "credit_api_name"}, "debit_data_id": {"describe_api_name": "MatchNoteDetailObj", "auto_adapt_places": false, "pattern": "", "description": "id", "is_unique": false, "type": "text", "used_in": "component", "is_required": false, "define_type": "package", "is_single": false, "index_name": "t_5", "max_length": 100, "is_index": true, "is_active": true, "create_time": *************, "is_encrypted": false, "label": "核销对象单号", "is_need_convert": false, "api_name": "debit_data_id", "_id": "6502ec8d322d500001c233ad", "is_index_field": false, "status": "released"}, "credit_data_id": {"describe_api_name": "MatchNoteDetailObj", "auto_adapt_places": false, "pattern": "", "description": "id", "is_unique": false, "type": "text", "used_in": "component", "is_required": false, "define_type": "package", "is_single": false, "index_name": "t_10", "max_length": 100, "is_index": true, "is_active": true, "create_time": *************, "is_encrypted": false, "label": "收付款对象单号", "is_need_convert": false, "api_name": "credit_data_id", "_id": "6502ec8d322d500001c233ae", "is_index_field": false, "status": "released"}, "debit_detail_data_id": {"describe_api_name": "MatchNoteDetailObj", "auto_adapt_places": false, "pattern": "", "description": "id", "is_unique": false, "type": "text", "used_in": "component", "is_required": false, "define_type": "package", "is_single": false, "index_name": "t_6", "max_length": 100, "is_index": true, "is_active": true, "create_time": *************, "is_encrypted": false, "label": "核销对象明细单号", "is_need_convert": false, "api_name": "debit_detail_data_id", "_id": "6502ec8d322d500001c233af", "is_index_field": false, "status": "released"}, "credit_detail_api_name": {"describe_api_name": "MatchNoteDetailObj", "auto_adapt_places": false, "pattern": "", "description": "收付款对象明细", "is_unique": false, "type": "text", "used_in": "component", "is_required": false, "define_type": "package", "is_single": false, "index_name": "t_11", "max_length": 100, "is_index": true, "is_active": true, "create_time": *************, "is_encrypted": false, "label": "收付款对象明细", "is_need_convert": false, "api_name": "credit_detail_api_name", "_id": "6502ec8d322d500001c233b0", "is_index_field": false, "status": "released"}, "lock_rule": {"describe_api_name": "MatchNoteDetailObj", "auto_adapt_places": false, "description": "锁定规则", "is_unique": false, "rules": [], "type": "lock_rule", "is_required": false, "define_type": "package", "is_single": false, "is_index": false, "is_active": true, "create_time": 1641546975106, "is_encrypted": false, "default_value": "default_lock_rule", "label": "锁定规则", "is_need_convert": false, "api_name": "lock_rule", "_id": "61d804dfd3571d0001ce12dc", "is_index_field": false, "help_text": "", "status": "new"}, "lock_status": {"describe_api_name": "MatchNoteDetailObj", "auto_adapt_places": false, "description": "锁定状态", "is_unique": false, "type": "select_one", "is_required": false, "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "option_id": "f8f6e3b07ca18b590c9a3cf86e014576", "is_single": false, "is_index": true, "is_active": true, "create_time": *************, "is_encrypted": false, "default_value": "0", "label": "锁定状态", "is_need_convert": false, "api_name": "lock_status", "_id": "61d804dfd3571d0001ce12dd", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "ar_detail_amount": {"expression_type": "js", "return_type": "currency", "describe_api_name": "MatchNoteDetailObj", "auto_adapt_places": false, "description": "", "is_unique": false, "type": "formula", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "is_extend": false, "index_name": "d_1", "is_index": true, "is_active": true, "expression": "$ar_detail_id__r.price_tax_amount$", "create_time": *************, "is_encrypted": false, "label": "价税合计", "is_need_convert": false, "api_name": "ar_detail_amount", "_id": "61d804dfd3571d0001ce12de", "is_index_field": false, "help_text": "", "status": "released"}, "ar_id": {"describe_api_name": "MatchNoteDetailObj", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "is_required": false, "wheres": [], "define_type": "package", "input_mode": "", "is_single": false, "is_extend": false, "index_name": "s_1", "is_index": true, "is_active": true, "create_time": *************, "is_encrypted": false, "label": "应收单", "target_api_name": "AccountsReceivableNoteObj", "target_related_list_name": "target_related_list_match_note_detail_ar", "target_related_list_label": "核销单明细", "action_on_target_delete": "set_null", "is_need_convert": false, "api_name": "ar_id", "_id": "61d804dfd3571d0001ce12df", "is_index_field": true, "help_text": "", "status": "released"}, "life_status": {"describe_api_name": "MatchNoteDetailObj", "auto_adapt_places": false, "description": "生命状态", "is_unique": false, "type": "select_one", "is_required": true, "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "option_id": "18de2c4eae8cf7a180ffb0f175010e6f", "is_single": false, "is_index": true, "is_active": true, "create_time": *************, "is_encrypted": false, "default_value": "normal", "label": "生命状态", "is_need_convert": false, "api_name": "life_status", "_id": "61d804dfd3571d0001ce12e0", "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "no_settled_amount": {"expression_type": "js", "return_type": "currency", "describe_api_name": "MatchNoteDetailObj", "auto_adapt_places": false, "description": "", "is_unique": false, "type": "formula", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "is_extend": false, "index_name": "d_2", "is_index": true, "is_active": true, "expression": "$ar_detail_id__r.no_settled_amount$", "create_time": *************, "is_encrypted": false, "label": "待结算金额", "is_need_convert": false, "api_name": "no_settled_amount", "_id": "61d804dfd3571d0001ce12e1", "is_index_field": false, "help_text": "", "status": "released"}, "lock_user": {"describe_api_name": "MatchNoteDetailObj", "auto_adapt_places": false, "description": "加锁人", "is_unique": false, "type": "employee", "is_required": false, "define_type": "package", "is_single": true, "is_index": false, "is_active": true, "create_time": *************, "is_encrypted": false, "label": "加锁人", "is_need_convert": false, "api_name": "lock_user", "_id": "61d804dfd3571d0001ce12e2", "is_index_field": false, "help_text": "", "status": "new"}, "relevant_team": {"describe_api_name": "MatchNoteDetailObj", "auto_adapt_places": false, "description": "", "is_unique": false, "type": "embedded_object_list", "is_required": false, "define_type": "package", "is_single": false, "embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": true, "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "create_time": 1641546975343, "is_encrypted": false, "label": "相关团队", "is_need_convert": false, "api_name": "relevant_team", "_id": "61d804dfd3571d0001ce12e3", "is_index_field": false, "help_text": "相关团队", "status": "new"}, "record_type": {"describe_api_name": "MatchNoteDetailObj", "auto_adapt_places": false, "description": "record_type", "is_unique": false, "type": "record_type", "is_required": false, "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "option_id": "504eae3cf17c3d7223be4a3e0e893ecb", "is_single": false, "is_index": true, "is_active": true, "create_time": *************, "is_encrypted": false, "label": "业务类型", "is_need_convert": false, "api_name": "record_type", "_id": "61d804dfd3571d0001ce12e4", "is_index_field": false, "config": {}, "help_text": "", "status": "released"}, "matchnote_id": {"describe_api_name": "MatchNoteDetailObj", "auto_adapt_places": false, "description": "", "is_unique": false, "type": "master_detail", "is_required": true, "define_type": "package", "is_single": false, "is_extend": false, "index_name": "s_4", "is_index": true, "is_active": true, "create_time": *************, "is_encrypted": false, "label": "核销单编号", "target_api_name": "MatchNoteObj", "show_detail_button": false, "target_related_list_name": "target_related_list_match_note_detail_match_note", "target_related_list_label": "核销单明细", "is_need_convert": false, "api_name": "matchnote_id", "is_create_when_master_create": true, "_id": "61d804dfd3571d0001ce12e5", "is_index_field": true, "is_required_when_master_create": false, "help_text": "", "status": "released"}, "extend_obj_data_id": {"describe_api_name": "MatchNoteDetailObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "is_extend": false, "index_name": "t_1", "max_length": 256, "is_index": true, "is_active": true, "create_time": *************, "is_encrypted": false, "default_value": "", "label": "extend_obj_data_id", "is_need_convert": false, "api_name": "extend_obj_data_id", "_id": "61d804dfd3571d0001ce12e6", "is_index_field": false, "help_text": "", "status": "released"}, "life_status_before_invalid": {"describe_api_name": "MatchNoteDetailObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "description": "作废前生命状态", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "max_length": 256, "is_index": false, "is_active": true, "create_time": *************, "is_encrypted": false, "default_value": "", "label": "作废前生命状态", "is_need_convert": false, "api_name": "life_status_before_invalid", "_id": "61d804dfd3571d0001ce12e7", "is_index_field": false, "help_text": "", "status": "new"}, "product_id": {"describe_api_name": "MatchNoteDetailObj", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "is_required": false, "wheres": [], "define_type": "package", "input_mode": "", "is_single": false, "is_extend": false, "index_name": "s_3", "is_index": true, "is_active": true, "create_time": *************, "is_encrypted": false, "label": "产品", "target_api_name": "ProductObj", "target_related_list_name": "target_related_list_match_note_detail_product", "target_related_list_label": "核销单明细", "action_on_target_delete": "set_null", "is_need_convert": false, "api_name": "product_id", "_id": "61d804dfd3571d0001ce12e9", "is_index_field": true, "help_text": "", "status": "released"}, "name": {"describe_api_name": "MatchNoteDetailObj", "prefix": "MD{yyyy}{mm}{dd}", "auto_adapt_places": false, "description": "", "is_unique": true, "start_number": 1, "type": "auto_number", "is_required": true, "define_type": "system", "postfix": "", "is_single": false, "is_extend": false, "index_name": "name", "is_index": true, "is_active": true, "auto_number_type": "normal", "create_time": *************, "is_encrypted": false, "serial_number": 3, "default_value": "01", "label": "核销明细编号", "condition": "NONE", "is_need_convert": false, "api_name": "name", "func_api_name": "", "_id": "61d804dfd3571d0001ce12ea", "is_index_field": false, "help_text": "", "status": "released"}, "owner_department": {"describe_api_name": "MatchNoteDetailObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "is_extend": false, "index_name": "owner_dept", "max_length": 256, "is_index": true, "is_active": true, "create_time": *************, "is_encrypted": false, "default_value": "", "label": "负责人所在部门", "is_need_convert": false, "api_name": "owner_department", "_id": "61d804dfd3571d0001ce12eb", "is_index_field": false, "help_text": "", "status": "released"}, "this_match_amount": {"describe_api_name": "MatchNoteDetailObj", "default_is_expression": false, "auto_adapt_places": false, "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "currency", "decimal_places": 2, "default_to_zero": true, "is_required": true, "define_type": "package", "is_single": false, "is_extend": false, "index_name": "d_3", "max_length": 14, "is_index": true, "is_active": true, "create_time": 1641546975373, "is_encrypted": false, "length": 12, "default_value": "", "label": "本次核销金额", "currency_unit": "￥", "is_need_convert": false, "api_name": "this_match_amount", "_id": "61d804dfd3571d0001ce12ec", "is_index_field": false, "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "released"}, "_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "_id", "is_active": true, "api_name": "_id", "description": "_id", "status": "released", "index_name": "_id", "create_time": 1640777235512}, "tenant_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "tenant_id", "is_active": true, "api_name": "tenant_id", "description": "tenant_id", "status": "released", "create_time": 1640777235512, "index_name": "ei"}, "object_describe_api_name": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": true, "is_unique": false, "max_length": 200, "pattern": "", "label": "object_describe_api_name", "is_active": true, "api_name": "object_describe_api_name", "description": "object_describe_api_name", "status": "released", "index_name": "api_name", "create_time": 1640777235512}, "version": {"type": "number", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "round_mode": 4, "length": 8, "decimal_places": 0, "label": "version", "api_name": "version", "description": "version", "status": "released", "index_name": "version", "create_time": 1640777235512}, "created_by": {"type": "employee", "define_type": "system", "is_index": true, "is_need_convert": true, "is_required": false, "is_unique": false, "is_single": true, "api_name": "created_by", "status": "released", "label": "创建人", "is_active": true, "index_name": "crt_by", "create_time": 1640777235512}, "last_modified_by": {"type": "employee", "define_type": "system", "is_index": true, "is_need_convert": true, "is_required": false, "is_unique": false, "is_single": true, "api_name": "last_modified_by", "status": "released", "is_active": true, "index_name": "md_by", "label": "最后修改人", "create_time": 1640777235512}, "package": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "package", "is_active": true, "api_name": "package", "description": "package", "status": "released", "create_time": 1640777235512, "index_name": "pkg"}, "create_time": {"type": "date_time", "define_type": "system", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "创建时间", "api_name": "create_time", "description": "create_time", "status": "released", "index_name": "crt_time", "create_time": 1640777235512}, "last_modified_time": {"type": "date_time", "define_type": "system", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "time_zone": "", "date_format": "yyyy-MM-dd HH:mm:ss", "label": "最后修改时间", "api_name": "last_modified_time", "description": "last_modified_time", "status": "released", "index_name": "md_time", "create_time": 1640777235512}, "is_deleted": {"type": "true_or_false", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "label": "is_deleted", "api_name": "is_deleted", "description": "is_deleted", "default_value": false, "status": "released", "index_name": "is_del", "create_time": 1640777235512}, "out_tenant_id": {"type": "text", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "max_length": 200, "pattern": "", "label": "外部企业", "is_active": true, "api_name": "out_tenant_id", "description": "out_tenant_id", "status": "released", "index_name": "o_ei", "create_time": 1640777235512, "config": {"display": 0}}, "out_owner": {"type": "employee", "define_type": "system", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "is_single": true, "api_name": "out_owner", "index_name": "o_owner", "status": "released", "label": "外部负责人", "is_active": true, "config": {"display": 1}, "create_time": 1640777235512}, "data_own_department": {"type": "department", "define_type": "package", "is_index": true, "is_need_convert": false, "is_required": false, "is_unique": false, "is_single": true, "api_name": "data_own_department", "status": "released", "label": "归属部门", "is_active": true, "index_name": "data_owner_dept_id", "create_time": 1640777235512}, "order_by": {"type": "number", "define_type": "system", "is_index": false, "is_need_convert": false, "is_required": false, "is_unique": false, "round_mode": 4, "length": 8, "decimal_places": 0, "label": "order_by", "api_name": "order_by", "description": "order_by", "status": "released", "index_name": "l_by", "create_time": 1640777235512}}, "actions": {}, "index_version": 1, "_id": "61cc46135e781a000104393e", "tenant_id": "-100", "api_name": "MatchNoteDetailObj", "created_by": "-10000", "last_modified_by": "-1000", "display_name": "核销单明细", "package": "CRM", "is_active": true, "version": 8, "release_version": "6.4", "define_type": "package", "is_deleted": false, "last_modified_time": 1641546975106, "create_time": 1640777235512, "store_table_name": "match_note_detail", "icon_index": 18, "description": "", "visible_scope": "config", "short_name": "mnd"}