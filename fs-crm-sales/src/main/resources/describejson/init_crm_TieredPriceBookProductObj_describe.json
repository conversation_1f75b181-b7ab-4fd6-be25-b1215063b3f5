{"fields": {"name": {"is_index": true, "is_active": true, "prefix": "", "is_unique": true, "default_value": "", "serial_number": 10, "start_number": 1, "label": "编号", "type": "auto_number", "is_required": false, "api_name": "name", "define_type": "package", "postfix": "", "help_text": "", "status": "released", "is_extend": false}, "tiered_price_book_id": {"is_index": true, "is_active": true, "is_unique": false, "label": "阶梯价目表", "target_api_name": "TieredPriceBookObj", "type": "master_detail", "target_related_list_name": "tiered_price_book_product_master_id", "target_related_list_label": "阶梯价目表适用产品", "is_required": true, "api_name": "tiered_price_book_id", "define_type": "package", "is_create_when_master_create": true, "is_required_when_master_create": true, "help_text": "", "status": "released", "is_extend": false, "config": {"edit": 1, "attrs": {"label": 1, "help_text": 1}}}, "product_id": {"is_index": true, "is_active": true, "is_unique": false, "label": "产品名称", "target_api_name": "ProductObj", "type": "object_reference", "target_related_list_name": "tiered_price_book_product_product_id_", "target_related_list_label": "阶梯价目表适用产品", "action_on_target_delete": "set_null", "is_required": true, "wheres": "[{\"connector\":\"OR\",\"filters\":[{\"value_type\":0,\"operator\":\"EQ\",\"field_name\":\"life_status\",\"field_values\":[\"normal\"]},{\"value_type\":0,\"operator\":\"EQ\",\"field_name\":\"product_status\",\"field_values\":[\"1\"]},{\"value_type\":0,\"operator\":\"EQ\",\"field_name\":\"is_saleable\",\"field_values\":[true]}]}]", "api_name": "product_id", "define_type": "package", "help_text": "", "status": "released", "is_extend": false, "config": {"edit": 1, "attrs": {"label": 1, "help_text": 1, "wheres": 1}}}, "product_life_status": {"is_index": false, "is_active": true, "quote_field_type": "select_one", "is_unique": false, "label": "产品对象生命状态", "type": "quote", "is_abstract": null, "quote_field": "product_id__r.life_status", "field_num": null, "is_required": false, "api_name": "product_life_status", "define_type": "package", "is_extend": false, "is_index_field": false, "is_single": false, "config": {"enable": 0, "display": 1, "remove": 0, "attrs": {"is_index": 0, "api_name": 0, "quote_field_type": 0, "label": 0, "help_text": 0, "quote_field": 0}}, "status": "released"}, "product_status": {"is_index": false, "is_active": true, "quote_field_type": "select_one", "is_unique": false, "label": "上下架", "type": "quote", "is_abstract": null, "quote_field": "product_id__r.product_status", "is_required": false, "api_name": "product_status", "define_type": "package", "is_extend": false, "is_index_field": false, "is_single": false, "config": {"add": 0, "edit": 0, "enable": 0, "display": 1, "remove": 0, "attrs": {"is_index": 0, "api_name": 0, "quote_field_type": 0, "label": 0, "help_text": 0, "quote_field": 0}}, "help_text": "", "status": "released"}, "is_package": {"is_index": false, "is_active": true, "quote_field_type": "true_or_false", "description": "", "is_unique": false, "label": "是否产品组合", "type": "quote", "is_abstract": null, "quote_field": "product_id__r.is_package", "field_num": null, "is_required": false, "api_name": "is_package", "define_type": "package", "is_index_field": false, "is_single": false, "config": {"add": 0, "edit": 0, "enable": 0, "display": 1, "remove": 0, "attrs": {"is_index": 0, "api_name": 0, "quote_field_type": 0, "label": 0, "help_text": 0, "quote_field": 0}}, "help_text": "", "status": "released"}, "is_active": {"is_index": true, "is_active": true, "is_unique": false, "default_value": true, "label": "启用状态", "type": "true_or_false", "is_abstract": null, "is_required": true, "api_name": "is_active", "options": [{"label": "是", "value": true, "config": {"edit": 0, "enable": 0, "remove": 0}}, {"label": "否", "value": false, "config": {"edit": 0, "enable": 0, "remove": 0}}], "define_type": "package", "is_index_field": false, "is_single": false, "config": {"add": 0, "edit": 1, "enable": 0, "display": 1, "remove": 0, "attrs": {"api_name": 0, "options": 0, "is_unique": 0, "default_value": 0, "label": 1, "is_required": 0, "help_text": 1}}, "help_text": "", "status": "released"}, "active_time_start": {"is_index": true, "is_active": true, "is_required": false, "api_name": "active_time_start", "is_unique": false, "define_type": "package", "label": "生效时间（含）", "help_text": "", "type": "date_time", "time_zone": "GMT+8", "date_format": "yyyy-MM-dd HH:mm", "config": {"edit": 1, "attrs": {"label": 1, "help_text": 1}}}, "active_time_end": {"is_index": true, "is_active": true, "is_required": false, "api_name": "active_time_end", "is_unique": false, "define_type": "package", "label": "失效时间（含）", "help_text": "", "type": "date_time", "time_zone": "GMT+8", "date_format": "yyyy-MM-dd HH:mm", "config": {"edit": 1, "attrs": {"label": 1, "help_text": 1}}}, "order_field": {"default_is_expression": false, "pattern": "", "description": "排序", "is_unique": false, "type": "number", "decimal_places": 0, "default_to_zero": false, "is_required": false, "define_type": "package", "is_single": false, "max_length": 20, "is_index": true, "is_active": true, "length": 18, "label": "排序", "is_abstract": null, "field_num": null, "is_need_convert": false, "api_name": "order_field", "is_index_field": false, "config": {"add": 0, "edit": 1, "enable": 0, "display": 1, "remove": 1, "attrs": {"api_name": 0, "is_unique": 0, "default_value": 1, "label": 1, "help_text": 1, "decimal_places": 1}}, "round_mode": 4, "help_text": "", "status": "released"}, "relevant_team": {"embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": true, "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": false, "is_active": true, "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "is_index_field": false, "is_single": false, "help_text": "相关团队", "status": "new"}, "owner": {"is_index": true, "is_active": true, "is_unique": false, "label": "负责人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "owner", "define_type": "package", "is_index_field": false, "is_single": true, "config": {"add": 0, "edit": 0, "enable": 0, "display": 1, "remove": 0, "attrs": {"label": 0}}, "help_text": "", "status": "new"}, "lock_status": {"is_index": true, "is_active": true, "description": "锁定状态", "is_unique": false, "default_value": "0", "label": "锁定状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "lock_status", "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "is_index_field": false, "is_single": false, "config": {}, "status": "new"}, "life_status": {"is_index": true, "is_active": true, "description": "", "is_unique": false, "default_value": "normal", "label": "生命状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "life_status", "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "is_index_field": false, "is_single": false, "config": {"add": 0, "edit": 0, "enable": 0, "display": 1, "remove": 0, "attrs": {"is_required": 0, "options": 0, "default_value": 0, "label": 0, "help_text": 0}}, "help_text": "", "status": "new"}, "record_type": {"is_index": true, "is_active": true, "description": "record_type", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "is_index_field": false, "is_single": false, "config": {"add": 0, "edit": 0, "enable": 0, "display": 1, "remove": 0, "attrs": {"label": 0}}, "help_text": "", "status": "released"}, "extend_obj_data_id": {"default_is_expression": false, "is_index": false, "is_active": true, "pattern": "", "description": "extend_obj_data_id", "default_value": "", "type": "text", "label": "extend_obj_data_id", "default_to_zero": false, "is_required": false, "api_name": "extend_obj_data_id", "define_type": "package", "help_text": "", "max_length": 100, "status": "released", "is_extend": false}, "lock_user": {"is_index": false, "is_active": true, "description": "加锁人", "is_unique": false, "label": "加锁人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "lock_user", "define_type": "package", "is_index_field": false, "is_single": true, "status": "new"}, "lock_rule": {"is_index": false, "is_active": true, "description": "锁定规则", "is_unique": false, "default_value": "default_lock_rule", "rules": [], "label": "锁定规则", "type": "lock_rule", "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "is_index_field": false, "is_single": false, "status": "new"}, "life_status_before_invalid": {"is_index": false, "is_active": true, "pattern": "", "description": "作废前生命状态", "is_unique": false, "label": "作废前生命状态", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "life_status_before_invalid", "define_type": "package", "is_index_field": false, "is_single": false, "max_length": 256, "status": "new"}, "owner_department": {"default_is_expression": false, "pattern": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "is_single": true, "max_length": 100, "is_index": true, "is_active": true, "default_value": "", "label": "负责人主属部门", "is_need_convert": false, "api_name": "owner_department", "is_index_field": false, "config": {"add": 0, "edit": 0, "enable": 0, "display": 1, "remove": 0, "attrs": {"label": 0}}, "help_text": "", "status": "new"}}, "index_version": 1, "api_name": "TieredPriceBookProductObj", "display_name": "阶梯价目表适用产品", "package": "CRM", "define_type": "package", "is_active": true, "store_table_name": "tiered_price_book_product", "is_deleted": false, "config": {"record_type": {"add": 0, "assign": 0}}}