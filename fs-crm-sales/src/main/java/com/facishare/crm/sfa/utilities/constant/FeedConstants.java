package com.facishare.crm.sfa.utilities.constant;

/**
 * @IgnoreI18nFile
 */
public interface FeedConstants {

    enum FeedExtDataType {
        <PERSON>oQin("考勤", 1), CrmCustomer("客户", 2), CrmContact("联系人", 3), CrmFeedTag("Feed标签", 4)
        ,CrmVisit("拜访", 5), CrmSalesClue("线索", 6), CrmProduct("产品", 7), CrmPayment("回款", 8)
        ,CrmRefund("退款", 9), CrmSaleAction("销售流程", 10), CrmOpportunity("机会", 11), CrmBill("开票", 12)
        ,CrmTrade("成交", 13), CrmCustomerOrder("订单", 14), CrmReturnOrder("退货单", 15), CrmVisitActistudyon("拜访动作", 16)
        ,CrmInventoryAction("盘点动作", 17), CrmContract("合同", 18), CrmSalesCluePool("线索池", 19), CrmHighSeas("公海", 20)
        ,CrmCompetitor("竞争对手", 21), CrmMarketingEvent("市场活动", 22), CrmInventory("盘点", 23)
        ,UserDefinedObject("自定义对象", 24), CrmTradeProduct("订单产品", 28);
        // 成员变量
        private String lable;
        private int value;

        // 构造方法
        private FeedExtDataType(String lable, int value) {
            this.lable = lable;
            this.value = value;
        }
        public int getValue()
        {
            return value;
        }
    }

    enum FeedExtPermissionType {
        GetCrmCustomerEventList("查看销售记录列表权限", 1), GetCrmCustomerServiceList("查看服务记录列表权限", 2)
        , GetCrmCustomerFeedList("查看所有记录列表权限", 3), AddCrmCustomerEvent("添加销售记录权限", 4)
        ,AddCrmCustomerService("添加服务记录权限", 5), AddJobCommonFeed("在工作 下添加 分享、日志、审批、任务 等", 6)
        , GetJobCommonFeed("在工作 下获取 分享、日志、审批、任务 等", 7);
        // 成员变量
        private String lable;
        private int value;

        // 构造方法
        private FeedExtPermissionType(String lable, int value) {
            this.lable = lable;
            this.value = value;
        }
        public int getValue()
        {
            return value;
        }
    }
}
