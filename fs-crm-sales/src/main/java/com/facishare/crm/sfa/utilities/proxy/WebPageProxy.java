package com.facishare.crm.sfa.utilities.proxy;

import com.facishare.crm.sfa.utilities.proxy.model.FindSystemMenuByApiNames;
import com.facishare.crm.sfa.utilities.proxy.model.MenuAddItem;
import com.facishare.crm.sfa.utilities.proxy.model.MenuAddItems;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

/**
 * Created by zhangyu on 2020/6/10
 */
@RestResource(value = "WebPage", desc = "自定义服务提供的接口", contentType = "application/json")
public interface WebPageProxy {

    @POST(value = "/initMenu/menuAddItem", desc = "追加一个菜单项")
    MenuAddItem.MenuInitAddObjectResult menuAddItem(@HeaderMap Map<String, String> headers, @Body MenuAddItem.MenuInitAddObjectArg arg);

    @POST(value = "/initMenu/menuAddObjects", desc = "追加菜单项到指定的位置")
    MenuAddItems.MenuInitAddObjectsResult menuAddObjects(@HeaderMap Map<String, String> headers, @Body MenuAddItems.MenuInitAddObjectsArg arg);

    @POST(value = "/initMenu/findSystemMenuByApiNames", desc = "查找系统级菜单")
    FindSystemMenuByApiNames.FindSystemMenuByApiNamesResult findSystemMenuByApiNames(@HeaderMap Map<String, String> headers, @Body FindSystemMenuByApiNames.FindSystemMenuDataByApiNamesArg arg);

}
