package com.facishare.crm.sfa.utilities.util;

import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;

import java.util.Arrays;
import java.util.List;

public class SupportCheckInsSpuSkuUtils {
    private static List<String> spuFields;
    private static volatile List<String> pagingSpuFields;
    private static volatile List<String> pagingSkuFields;

    static {
        ConfigFactory.getConfig("support-check-ins-spu-sku", config -> {
            String configStr = config.get("spuFields");

            if (configStr != null) {
                String[] fields = configStr.split(",");

                spuFields = Arrays.asList(fields);

            } else {
                spuFields = Lists.newArrayList();
            }
        });
    }

    static {
        ConfigFactory.getConfig("so-support-check-ins-spu-sku", config -> {
            String configStr = config.get("spuFields");

            if (configStr != null) {
                String[] fields = configStr.trim().split(",");

                pagingSpuFields = Arrays.asList(fields);

            } else {
                pagingSpuFields = Lists.newArrayList();
            }

            String configStr1 = config.get("skuFields");

            if (configStr1 != null) {
                String[] fields = configStr1.trim().split(",");

                pagingSkuFields = Arrays.asList(fields);

            } else {
                pagingSkuFields = Lists.newArrayList();
            }
        });
    }

    public static List<String> getSpuFields() {
        return spuFields;
    }

    public static List<String> pagingSpuFields() {
        return pagingSpuFields;
    }

    public static List<String> pagingSkuFields() {
        return pagingSkuFields;
    }
}
