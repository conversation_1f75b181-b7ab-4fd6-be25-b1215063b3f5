package com.facishare.crm.sfa.predefine.service;

import com.facishare.crm.sfa.predefine.service.model.ConfigManagementServiceModel;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;

@ServiceModule("config_service")
@Service
@Slf4j
public class ConfigManagementService {
    @Autowired
    private ConfigService configService;

    @ServiceMethod("find_tenant_config")
    public ConfigManagementServiceModel.Result findTenantConfig(ConfigManagementServiceModel.FindConfigArg arg, ServiceContext context) {
        String errorMsg = "";
        String tenantConfig = "";
        try {
            tenantConfig = configService.findTenantConfig(User.builder().tenantId(arg.getTenantId()).userId("-10000").build(), arg.getKey());
        } catch (Exception e) {
            log.error("ConfigManagementService findTenantConfig arg:{},{}", arg, e);
            errorMsg = e.getMessage();
        }
        return ConfigManagementServiceModel.Result.builder().data(tenantConfig).isSuccess(Boolean.TRUE).errorMsg(errorMsg).build();
    }

    @ServiceMethod("update_tenant_config")
    public ConfigManagementServiceModel.Result updateTenantConfig(ConfigManagementServiceModel.UpdateTenantConfigArg arg, ServiceContext context) {
        String errorMsg = "";
        try {
            ConfigValueType type = null;
            switch (arg.getType()) {
                case "String":
                    type = ConfigValueType.STRING;
                    break;
                case "Json":
                    type = ConfigValueType.JSON;
                    break;
            }
            configService.updateTenantConfig(User.builder().tenantId(arg.getTenantId()).userId("-10000").build(),
                    arg.getKey(),
                    arg.getValue(),
                    type);
        } catch (Exception e) {
            log.error("ConfigManagementService updateTenantConfig arg:{}", arg);
            errorMsg = e.getMessage();
        }
        return ConfigManagementServiceModel.Result.builder().isSuccess(Boolean.TRUE).errorMsg(errorMsg).build();
    }

    @ServiceMethod("delete_tenant_config")
    public ConfigManagementServiceModel.Result deleteTenantConfig(ConfigManagementServiceModel.FindConfigArg arg, ServiceContext context) {
        String errorMsg = "";
        try {
            configService.deleteTenantConfig(User.builder().tenantId(arg.getTenantId()).userId("-10000").build(), arg.getKey());
        } catch (Exception e) {
            log.error("ConfigManagementService deleteTenantConfig arg:{}", arg);
            errorMsg = e.getMessage();
        }
        return ConfigManagementServiceModel.Result.builder().isSuccess(Boolean.TRUE).errorMsg(errorMsg).build();
    }

}
