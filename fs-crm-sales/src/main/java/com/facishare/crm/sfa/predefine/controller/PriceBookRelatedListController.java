package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.MetaDataFindServiceExt;
import com.facishare.crm.sfa.utilities.util.AvailableRangeUtils;
import com.google.common.collect.Lists;

import com.facishare.crm.sfa.predefine.service.PriceBookService;
import com.facishare.crm.sfa.utilities.constant.PriceBookConstants;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Component
public class PriceBookRelatedListController extends StandardRelatedListController {
    protected PriceBookService priceBookService = (PriceBookService) SpringUtil.getContext().getBean("priceBookService");
    private MetaDataFindServiceExt metaDataFindServiceExt = SpringUtil.getContext().getBean(MetaDataFindServiceExt.class);
    private final AvailableRangeUtils availableRangeUtils = SpringUtil.getContext().getBean(AvailableRangeUtils.class);

    protected String accountId = null;
    protected String partnerId = null;
    protected Boolean isAccountSelect = false;

    @Override
    protected void doFunPrivilegeCheck() {
        initAccountId();
        //有客户id时，不需要校验功能权限
        if (StringUtils.isBlank(accountId)) {
            super.doFunPrivilegeCheck();
        }
    }

    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery query = super.buildSearchTemplateQuery();
        availableRangeUtils.buildRangeSearchQueryForPriceBook(controllerContext.getUser(), query, arg.getRelatedListName(), accountId, partnerId);
        return query;
    }

    @Override
    protected Result doService(StandardRelatedListController.Arg arg) {
        List<ILayout> layouts = this.findMobileLayouts();
        SearchTemplateQuery query = this.buildSearchTemplateQuery();
        QueryResult<IObjectData> queryResult = this.getQueryResult(query);
        return this.buildResult(layouts, query, queryResult);
    }

    @Override
    protected QueryResult<IObjectData> findData(SearchTemplateQuery query) {
        QueryResult<IObjectData> queryResult;
        if (StringUtils.isNotBlank(accountId)) {
            queryResult = priceBookService.findPriceBookByAccountId(getControllerContext().getUser(), query, accountId, partnerId, true);
        } else {
            query.setLimit(2000);
            queryResult = metaDataFindServiceExt.findBySearchQuery(getControllerContext().getUser(), PriceBookConstants.API_NAME, query);
        }
        return queryResult;
    }

    protected void initAccountId() {
        if (arg.getObjectData() != null && arg.getObjectData().containsKey("account_id")) {
            accountId = Objects.nonNull(arg.getObjectData().get("account_id")) ? arg.getObjectData().get("account_id").toString() : null;
            partnerId = Optional.ofNullable(arg.getObjectData().get("partner_id")).orElse("").toString();
            isAccountSelect = true;
        }
    }

    @Override
    protected void modifyQueryByRefFieldName(SearchTemplateQuery query) {
        if (isAccountSelect) {
            //如果是从客户选择查看
            return;
        }
        super.modifyQueryByRefFieldName(query);
    }

    @Override
    protected List<ILayout> findMobileLayouts() {
        List<ILayout> mobileLayouts = super.findMobileLayouts();
        for (ILayout layout : mobileLayouts) {
            layout.set("buttons", Lists.newArrayList());
        }
        return mobileLayouts;
    }
}
