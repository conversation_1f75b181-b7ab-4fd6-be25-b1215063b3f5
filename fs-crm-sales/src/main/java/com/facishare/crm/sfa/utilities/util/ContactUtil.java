package com.facishare.crm.sfa.utilities.util;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.model.ContactSearchResult;
import com.facishare.crm.sfa.predefine.service.push.ContactSessionSandwichService;
import com.facishare.crm.sfa.utilities.constant.ContactConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.dto.QueryPhoneNumberInformation;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseImportAction;
import com.facishare.paas.appframework.core.predef.action.BaseImportDataAction;
import com.facishare.paas.appframework.core.predef.controller.BaseListController;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.TeamMember;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @IgnoreI18nFile
 */
@Slf4j
public class ContactUtil {
    private static final ServiceFacade SERVICE_FACADE = SpringUtil.getContext().getBean(ServiceFacadeImpl.class);

    private static final ContactSessionSandwichService contactSessionSandwichService = SpringUtil.getContext()
            .getBean(ContactSessionSandwichService.class);

    private static FunctionPrivilegeService functionPrivilegeService = SpringUtil.getContext()
            .getBean("functionPrivilegeService", FunctionPrivilegeService.class);

    /**
     * 移动端列表增加扫名片和从通讯录导入按钮
     * 扫名片按钮权限利用新建权限
     *
     * @param result
     */
    public static void addButton(User user, BaseListController.Result result) {
        Map<String, Boolean> actionFunMap = functionPrivilegeService.funPrivilegeCheck(user,
                SFAPreDefineObject.Contact.getApiName(),
                Lists.newArrayList(ObjectAction.CREATE.getActionCode(), ObjectAction.IMPORT_FROM_ADDRESS_BOOK.getActionCode()));
        if (result == null || CollectionUtils.empty(result.getListLayouts())) {
            return;
        }
        for (LayoutDocument layoutDocument : result.getListLayouts()) {
            ILayout layout = new Layout((layoutDocument));
            if (actionFunMap.get(ObjectAction.CREATE.getActionCode())) {
                layout.addButtons(ObjectAction.SCAN_CARD.createButton());
            }
            if (actionFunMap.get(ObjectAction.IMPORT_FROM_ADDRESS_BOOK.getActionCode())) {
                layout.addButtons(ObjectAction.IMPORT_FROM_ADDRESS_BOOK.createButton());
            }
        }
    }

    //拼接生日
    public static void concatenateBirthDay(List<IObjectData> objectDataList) {
        if (CollectionUtils.notEmpty(objectDataList)) {
            for (IObjectData objectData : objectDataList) {
                String year = objectData.get("year_of_birth", String.class);
                String month = objectData.get("month_of_birth", String.class);
                String day = objectData.get("day_of_birth", String.class);

                if (StringUtils.isEmpty(year)) {
                    year = "0";
                }
                if (StringUtils.isEmpty(month)) {
                    month = "0";
                }
                if (StringUtils.isEmpty(day)) {
                    day = "0";
                }

                //处理生日字段 生日字段可能存在 0000-00-00/0000-12-17/1991-10-00//1991-00-00这四种状态,需要特殊处理
                String birthDay = String.format("%04d-%02d-%02d", Integer.valueOf(year),
                        Integer.valueOf(month), Integer.valueOf(day));
                if ("0000-00-00".equals(birthDay)) {
                    objectData.set("date_of_birth", "");
                } else if (birthDay.startsWith("0000-")) {
                    objectData.set("date_of_birth", birthDay.replaceAll("0000-", ""));
                } else {
                    objectData.set("date_of_birth", birthDay.replaceAll("-00", ""));
                }
           /*     objectData.set("date_of_birth", String.format("%04d-%02d-%02d", Integer.valueOf(year),
                        Integer.valueOf(month), Integer.valueOf(day)));*/
            }
        }
    }

    public static void setTelMobileFieldProperty(User user, String apiName, FormComponent formComponent) {
        Set<String> invisibeList = functionPrivilegeService.getUnauthorizedFields(user, apiName);

        if (invisibeList.contains("tel")) {
            PreDefLayoutUtil.removeSomeFields(formComponent, Sets.newHashSet("tel1", "tel2", "tel3", "tel4", "tel5"));
        }
        if (invisibeList.contains("mobile")) {
            PreDefLayoutUtil.removeSomeFields(formComponent, Sets.newHashSet("mobile1", "mobile2", "mobile3", "mobile4", "mobile5"));
        }

        Set<String> readonlyFieldsList = functionPrivilegeService.getReadonlyFields(user, apiName);

        if (readonlyFieldsList.contains("tel")) {
            PreDefLayoutUtil.setFormComponentFieldsReadOnly(formComponent, Lists.newArrayList("tel1", "tel2", "tel3", "tel4", "tel5"));
        }
        if (readonlyFieldsList.contains("mobile")) {
            PreDefLayoutUtil.setFormComponentFieldsReadOnly(formComponent, Lists.newArrayList("mobile1", "mobile2", "mobile3", "mobile4", "mobile5"));
        }
    }


    public static void handlePredefinePhoneField(List<IObjectData> objectDataList) {
        if (CollectionUtils.notEmpty(objectDataList)) {
            List<String> predefinePhoneFields = Lists.newArrayList("mobile1", "mobile2", "mobile3", "mobile4",
                    "mobile5", "tel1", "tel2", "tel3", "tel4", "tel5");

            for (IObjectData objectData : objectDataList) {
                List<String> hasValueFields = Lists.newArrayList();
                for (String field : predefinePhoneFields) {
                    if (!ObjectDataExt.isValueEmpty(objectData.get(field)) ||
                            !ObjectDataExt.isValueEmpty(objectData.get(field + "__s"))) {
                        hasValueFields.add(field);
                    }
                }
                objectData.set("contact_mobile_fields", hasValueFields);
            }
        }
    }

    /**
     * 补充联系人电话、手机归属地等信息
     *
     * @param objectDataList
     */
    public static void handleMobileInfosField(List<IObjectData> objectDataList) {
        if (CollectionUtils.notEmpty(objectDataList)) {
            List<String> predefinePhoneFields = Lists.newArrayList("mobile1", "mobile2", "mobile3", "mobile4",
                    "mobile5", "tel1", "tel2", "tel3", "tel4", "tel5");
            for (IObjectData objectData : objectDataList) {
                List<ContactSearchResult.mobile_infos> mobileInfosList = Lists.newArrayList();
                for (String field : predefinePhoneFields) {
                    if (!ObjectDataExt.isValueEmpty(objectData.get(field))) {
                        Object temp = objectData.get(field + "__p");
                        if (!ObjectDataExt.isValueEmpty(temp)) {
                            QueryPhoneNumberInformation.Result result = (QueryPhoneNumberInformation.Result) temp;
                            mobileInfosList.add(ContactSearchResult.mobile_infos.builder().mobile(result.getMobile()).mobilePath(result.getMobilePath()).build());
                        }
                    }
                }
                objectData.set("mobile_infos", mobileInfosList);
            }
        }
    }

    /**
     * 更新联系人外部负责人
     *
     * @param user
     * @param contactObjectData
     */
    public static void updateContactOutInfo(User user, List<IObjectData> contactObjectData) {
        if (CollectionUtils.empty(contactObjectData)) {
            log.warn("ContactUtil->updateContactOutInfo  info:{}", "联系人信息为空");
            return;
        }
        //不确定contactObjectData是否有相关团队数据，重新获取一下
        List<String> contactIds = contactObjectData.stream().map(item -> item.getId()).collect(Collectors.toList());
        List<IObjectData> contactList = SERVICE_FACADE.findObjectDataByIds(user.getTenantId(), contactIds,
                SFAPreDefineObject.Contact.getApiName());
        if (CollectionUtils.empty(contactList)) {
            return;
        }
        List<String> accountIDList = contactList.stream()
                .filter(m -> m.get(ContactConstants.Field.ACCOUNTID) != null && !Strings.isNullOrEmpty(m.get(ContactConstants.Field.ACCOUNTID).toString()))
                .map(it -> String.valueOf(it.get(ContactConstants.Field.ACCOUNTID)))
                .distinct()
                .collect(Collectors.toList());
        List<IObjectData> accountList = SERVICE_FACADE.findObjectDataByIds(user.getTenantId(), accountIDList, SFAPreDefineObject.Account.getApiName());
        List<IObjectData> contactUpdateList = Lists.newArrayList();
        for (IObjectData objectData : contactList) {
            Optional<IObjectData> accountCurrentOptional = accountList.stream().filter(item -> String.valueOf(item.getId()).equals(AccountUtil.getStringValue(objectData, ContactConstants.Field.ACCOUNTID, ""))).findFirst();
            if (accountCurrentOptional.isPresent() && accountCurrentOptional.get().getOutOwner().size() > 0 && !Strings.isNullOrEmpty(accountCurrentOptional.get().getOutTenantId())) {
                objectData.setOutTenantId(accountCurrentOptional.get().getOutTenantId());
                objectData.setOutOwner(accountCurrentOptional.get().getOutOwner());
                updateOwnerToTeamMember(objectData, accountCurrentOptional.get().getOutOwner().get(0).toString(),
                        accountCurrentOptional.get().getOutTenantId());
            } else {
                objectData.setOutTenantId(null);
                objectData.setOutOwner(null);
                updateOwnerToTeamMember(objectData, null, null);
            }
            contactUpdateList.add(objectData);
        }
        if (CollectionUtils.notEmpty(contactUpdateList)) {
            List<String> updateField = Lists.newArrayList(ObjectDataExt.OUTER_OWNER, ObjectDataExt.OUTER_TENANT,
                    ObjectDataExt.RELEVANT_TEAM);
            SERVICE_FACADE.batchUpdateByFields(user, contactUpdateList, updateField);
        }
    }

    /**
     * 联系人补全部分字段
     *
     * @param objectData
     */
    public static void handleContactFields(ObjectDataDocument objectData) {
        handleRemoveSpecialField(objectData);
/*        //特殊处理10个电话手机字段
        handdlePhone(objectData.toObjectData());*/
        //补全生日字段 生日字段可能存在 ""/12-17/1991-10//1991这三种状态,需要特殊处理
        handleInvalidBirthDayField(objectData);
        handleNameOrderField(objectData);
    }

    public static void handlePhoneFields(ObjectDataDocument objectData) {
        //特殊处理10个电话手机字段
        handdlePhone(objectData.toObjectData());
    }

    /**
     * 生日特殊处理，将生日字段拆分成年月日三个字段
     *
     * @param objectData
     */
    public static void handleInvalidBirthDayField(ObjectDataDocument objectData) {
        try {
            String dateOfBirth = objectData.toObjectData().get(ContactConstants.Field.DATEOFBIRTH, String.class);
            String birthDay = "";
            if (org.springframework.util.StringUtils.isEmpty(dateOfBirth)) {
                birthDay = "0000-00-00";
            } else if (dateOfBirth.length() == 5) {
                birthDay = "0000-" + dateOfBirth;
            } else if (dateOfBirth.length() == 7) {
                birthDay = dateOfBirth + "-00";
            } else if (dateOfBirth.length() == 4) {
                birthDay = dateOfBirth + "-00-00";
            } else {
                birthDay = dateOfBirth;
            }
            String[] arrBirthDay = birthDay.split("-");
            if (birthDay.length() != 10 || arrBirthDay.length != 3) {
                throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_CONTACT_ERRORDATAOFBIRTH));
            }
            objectData.put(ContactConstants.Field.YEAROFBIRTH, Integer.parseInt(arrBirthDay[0]));
            objectData.put(ContactConstants.Field.MONTHOFBIRTH, Integer.parseInt(arrBirthDay[1]));
            objectData.put(ContactConstants.Field.DAYOFBIRTH, Integer.parseInt(arrBirthDay[2]));
        } catch (Exception e) {
            log.error("handleInvalidBirthDayField error", e);
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_CONTACT_ERRORDATAOFBIRTH));
        }
    }

    /**
     * 生日特殊处理，将年月日合成生日字段
     *
     * @param objectData
     */
    public static void handleInvalidBirthField(ObjectDataDocument objectData) {
        String dateOfBirth = objectData.toObjectData().get(ContactConstants.Field.DATEOFBIRTH, String.class);
        String yearOfBirth = objectData.toObjectData().get(ContactConstants.Field.YEAROFBIRTH, String.class);
        String monthOfBirth = objectData.toObjectData().get(ContactConstants.Field.MONTHOFBIRTH, String.class);
        String dayOfBirth = objectData.toObjectData().get(ContactConstants.Field.DAYOFBIRTH, String.class);
        if (org.springframework.util.StringUtils.isEmpty(dateOfBirth)) {
            if (org.springframework.util.StringUtils.isEmpty(yearOfBirth)) {
                yearOfBirth = "0000";
            }
            if (org.springframework.util.StringUtils.isEmpty(monthOfBirth)) {
                monthOfBirth = "00";
            }
            if (org.springframework.util.StringUtils.isEmpty(dayOfBirth)) {
                dayOfBirth = "00";
            }
            dateOfBirth = yearOfBirth + "-" + monthOfBirth + "-" + dayOfBirth;
            objectData.put(ContactConstants.Field.DATEOFBIRTH, dateOfBirth);
        }
    }

    /**
     * 处理联系人nameorder字段
     *
     * @param objectData
     */
    public static void handleNameOrderField(ObjectDataDocument objectData) {
        String name = objectData.toObjectData().get(ContactConstants.Field.NAME, String.class);
        if (!org.springframework.util.StringUtils.isEmpty(name)) {
            objectData.put("name_order", Chinese2PinyinUtils.getGBKOrder(name));
        }
    }

    /**
     * 处理联系人负责人变更时间
     *
     * @param objectData
     */
    public static void handleOwnerChangedTimeField(ObjectDataDocument objectData) {
        if (CollectionUtils.notEmpty(objectData.toObjectData().getOwner())) {
            objectData.put(ContactConstants.Field.OWNERCHANGEDTIME, System.currentTimeMillis());
        }
    }

    /**
     * 移除部分自处理字段
     *
     * @param objectData
     */
    public static void handleRemoveSpecialField(ObjectDataDocument objectData) {
        if (!objectData.isEmpty()) {
            objectData.remove(ContactConstants.Field.YEAROFBIRTH);
            objectData.remove(ContactConstants.Field.MONTHOFBIRTH);
            objectData.remove(ContactConstants.Field.DAYOFBIRTH);
            objectData.remove(ContactConstants.Field.NAMEORDER);
            objectData.remove(ContactConstants.Field.OWNERCHANGEDTIME);
        }
    }

    /**
     * 导入模板中需要去除的字段
     */
    public static List<String> getImportTemplateRemoveFields() {
        return Lists.newArrayList(
                "opportunity_id", "leads_id", "contact_status", "tel", "mobile", "name_order", "owner_changed_time", "out_resources", "lock_status", "life_status", "owner_department", "enterprise_wechat_user_id"
                , "phone_number_attribution_country", "phone_number_attribution_location", "phone_number_attribution_address", "phone_number_attribution_city",
                "phone_number_attribution_province", "phone_number_attribution_district"
        );
    }

    /**
     * 联系人模板部分字段特殊排序
     *
     * @param validFieldList
     * @return
     */
    @NotNull
    public static List<IFieldDescribe> sortFieldDescribes(List<IFieldDescribe> validFieldList) {
        if (CollectionUtils.empty(validFieldList)) {
            return validFieldList;
        }
        Iterator<IFieldDescribe> iterator = validFieldList.iterator();
        List<IFieldDescribe> resultList = Lists.newLinkedList();
        List<String> fieldNameList = Lists.newArrayList("tel1", "tel2", "tel3", "tel4", "tel5",
                "mobile1", "mobile2", "mobile3", "mobile4", "mobile5", "year_of_birth", "month_of_birth", "day_of_birth");
        List<IFieldDescribe> needSortList = Lists.newLinkedList();
        while (iterator.hasNext()) {
            IFieldDescribe next = iterator.next();
            if (fieldNameList.contains(next.getApiName())) {
                needSortList.add(next);
            } else {
                resultList.add(next);
            }
        }
        if (CollectionUtils.notEmpty(needSortList)) {
            //按照字段label排序
            needSortList.sort(Comparator.comparing(IFieldDescribe::getLabel));
            resultList.addAll((resultList.size() > 1 ? 1 : 0), needSortList);
        }
        return resultList;
    }

    /**
     * 联系人导入特殊校验
     *
     * @param dataList
     * @return
     */
    @NotNull
    public static List<BaseImportAction.ImportError> importCustomValidate(List<BaseImportDataAction.ImportData> dataList) {
        List<BaseImportAction.ImportError> errorList = Lists.newArrayList();
        for (BaseImportDataAction.ImportData x : dataList) {
            String yearOfBirth = x.getData().get(ContactConstants.Field.YEAROFBIRTH, String.class);
            String monthOfBirth = x.getData().get(ContactConstants.Field.MONTHOFBIRTH, String.class);
            String dayOfBirth = x.getData().get(ContactConstants.Field.DAYOFBIRTH, String.class);
            String msg = validateData(yearOfBirth, monthOfBirth, dayOfBirth);
            if (!Strings.isNullOrEmpty(msg)) {
                errorList.add(new BaseImportAction.ImportError(x.getRowNo(), msg));
            }
        }
        List<BaseImportDataAction.ImportData> validateDataList = dataList.stream().filter(d -> !Strings.isNullOrEmpty(d.getData().get(ContactConstants.Field.ACCOUNTID, String.class)) &&
                "default_contact_partner__c".equals(d.getData().getRecordType()))
                .collect(Collectors.toList());
        if (CollectionUtils.notEmpty(validateDataList)) {
            for (BaseImportDataAction.ImportData errorImportData : validateDataList) {
                errorList.add(new BaseImportAction.ImportError(errorImportData.getRowNo(), I18N.text(SFAI18NKeyUtil.SFA_CONTACT_IMPORTNOACCOUNT, I18N.text("PartnerObj.attribute.self.display_name"), I18N.text("AccountObj.attribute.self.display_name"))));
            }
        }

        validateDataList = dataList.stream().filter(d -> !Strings.isNullOrEmpty(d.getData().get("owned_partner_id", String.class)) &&
                !"default_contact_partner__c".equals(d.getData().getRecordType()))
                .collect(Collectors.toList());
        if (validateDataList != null && validateDataList.size() > 0) {
            for (BaseImportDataAction.ImportData errorImportData : validateDataList) {
                errorList.add(new BaseImportAction.ImportError(errorImportData.getRowNo(), I18N.text(SFAI18NKeyUtil.SFA_CONTACT_IMPORTNOPARTNER, I18N.text("PartnerObj.attribute.self.display_name"), I18N.text("PartnerObj.attribute.self.display_name"))));
            }
        }
        return errorList;
    }

    /**
     * 联系人导入部分字段特殊赋值
     *
     * @param validList
     */
    public static void handleContactImportFields(List<IObjectData> validList) {
        if (CollectionUtils.empty(validList)) {
            return;
        }
        validList.forEach(x -> {
            handdlePhone(x);
            ContactUtil.handleInvalidBirthField(ObjectDataDocument.of(x));
            ContactUtil.handleNameOrderField(ObjectDataDocument.of(x));
            //处理联系人负责人变更时间
            ContactUtil.handleOwnerChangedTimeField(ObjectDataDocument.of(x));
        });
    }

    /**
     * 特殊处理电话，如果电话1-5或者手机1-5有值，则将电话1-5和手机1-5 组装成tel、mobile,否则反过来拆分tel、mobile到电话电话1-5和手机1-5
     *
     * @param objectData
     */
    public static void handdlePhone(IObjectData objectData) {
        Boolean isNeedSplit = true;
        for (int i = 1; i <= 5; i++) {
            if (!Strings.isNullOrEmpty(objectData.get("tel" + i, String.class))) {
                isNeedSplit = false;
            }
            if (!Strings.isNullOrEmpty(objectData.get("mobile" + i, String.class))) {
                isNeedSplit = false;
            }
            if (!isNeedSplit) {
                break;
            }
        }
        if (!isNeedSplit) {
            PhoneUtil.dealPhone(ObjectDataDocument.of(objectData));
        } else {
            PhoneUtil.splitPhoneNumber(ObjectDataDocument.of(objectData));
        }
    }

    /**
     *
     * @param objectData
     * @param key:mobile,tel
     * @return
     */
    public static Boolean existMobile(ObjectDataDocument objectData,String key){

        if (objectData.containsKey(key)) {
            return true;
        }
        for (int i = 1; i <= 5; i++) {
            if (objectData.containsKey(key+i)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查是否是闰年
     *
     * @param year
     * @return
     */
    public static boolean isLeapYear(int year) {
        if (year % 4 == 0 && year % 100 != 0 || year % 400 == 0) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 简单校验年月日
     *
     * @param year
     * @param month
     * @param day
     * @return
     */
    public static String validateData(String year, String month, String day) {
        String msg = "";
        // 年
        String yearMatch = "[0-9]{4}";
        // 月
        String monthMatch = "[0-9]||0[0-9]||1[0-2]";
        // 天
        String dayMatch = "[0-9]||[0-2][0-9]||3[01]";

        //region 最基本的检查格式 begin
        if (!Strings.isNullOrEmpty(year) && !year.equals("0") && !year.matches(yearMatch)) {
            //生日年数据不对
            msg = I18N.text(SFAI18NKeyUtil.SFA_CONTACT_YEAROFBIRTHEX);
        }
        if (!Strings.isNullOrEmpty(month) && !month.equals("0") && !month.matches(monthMatch)) {
            //生日月数据不对
            msg = I18N.text(SFAI18NKeyUtil.SFA_CONTACT_MONTHOFBIRTHEX);
        }
        if (!Strings.isNullOrEmpty(day) && !day.equals("0") && !day.matches(dayMatch)) {
            //生日日数据不对
            msg = I18N.text(SFAI18NKeyUtil.SFA_CONTACT_DAYOFBIRTHEX);
        }
        // endregion
        return msg;
    }

    /**
     * 联系人手机电话权限特殊处理（数据）
     * */
    public static void hideTelAndMobile(List<ObjectDataDocument> datas, String apiName) {
        for (ObjectDataDocument data : datas) {
            Integer num = 1;
            while (num <= 5) {
                if (data.containsKey(apiName + num.toString())) {
                    data.put(apiName + num.toString() + "__s", datas.remove(apiName + num.toString()));
                    data.put(apiName + num.toString() + "__s", "*****");
                }
                num++;
            }
        }
    }

    /**
     * 联系人手机电话权限特殊处理（布局）
     * */
    public static void deleteLayout(LayoutDocument layoutDescribe, String apiName) {
        List<Map<String, Object>> documents = (List) layoutDescribe.get("components");
        for (Map<String, Object> document : documents) {
            if (document.get("header").equals("详细信息")) {
                List<Map<String, Object>> childDocuments = (List) document.get("child_components");
                if (childDocuments != null && childDocuments.size() != 0) {
                    List<Map<String, Object>> fieldSections = (List) childDocuments.get(0).get("field_section");
                    if (fieldSections != null && fieldSections.size() != 0) {
                        for (Map<String, Object> fieldSection : fieldSections) {
                            if (fieldSection.get("header").equals("联系方式")) {
                                List<Map<String, Object>> formFields = (List) fieldSection.get("form_fields");
                                if (formFields != null && formFields.size() != 0) {
                                    Integer num = 1;
                                    while (num <= 5) {
                                        String s = apiName + num;
                                        formFields.removeIf(x -> x.get("field_name").equals(s));
                                        num++;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            break;
        }
    }

    private static void updateOwnerToTeamMember(IObjectData objectData, String outOwner, String outTenantId) {
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        List<TeamMember> teamMembers = objectDataExt.getTeamMembers();
        //从原相关团队中获取原外部负责人
        Optional<TeamMember> oldOwnerTeamMemberOpt =
                teamMembers.stream().filter(f -> f.getRole() == TeamMember.Role.OWNER
                        && f.isOutMember()).findFirst();
        if (oldOwnerTeamMemberOpt.isPresent()) {
            //移除原外部负责人
            teamMembers.remove(oldOwnerTeamMemberOpt.get());
        }
        //从原相关团队中获取新外部负责人,如果能找到,也移除,避免在后面被二次添加
        teamMembers = teamMembers.stream()
                .filter(it -> !it.getEmployee().equals(outOwner)).collect(Collectors.toList());

        //添加新外部负责人
        if (!Strings.isNullOrEmpty(outOwner)) {
            teamMembers.add(new TeamMember(outOwner, TeamMember.Role.OWNER, TeamMember.Permission.READANDWRITE, outTenantId));
        }

        objectDataExt.setTeamMembers(teamMembers);
    }

    public static void recordOwnerChangeHistory(User user, List<IObjectData> objectDataList) {
        try {
            ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
            task.submit(() -> {
                saveOwnerChangeHistory(user, objectDataList);
            });
            task.run();
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    public static void saveOwnerChangeHistory(User user, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        String tableName = "biz_contact_owner_history";
        List<Map<String, Object>> insertMap = Lists.newArrayList();
        objectDataList.forEach(m -> {
            if (CollectionUtils.notEmpty(m.getOwner())) {
                Map<String, Object> insertData = Maps.newHashMap();
                insertData.put("id", SERVICE_FACADE.generateId());
                insertData.put("tenant_id", user.getTenantId());
                insertData.put("contact_id", m.getId());
                insertData.put("owner", m.getOwner().get(0));
                insertData.put("object_describe_api_name", "ContactObj");
                insertData.put("created_by", user.getUserId());
                insertData.put("create_time", System.currentTimeMillis());
                insertData.put("last_modified_by", user.getUserId());
                insertData.put("last_modified_time", System.currentTimeMillis());
                insertData.put("is_deleted", 0);
                insertMap.add(insertData);
            }
        });
        CommonSqlUtil.insertDataBySql(user, tableName, insertMap);
        Set<String> owners = objectDataList.stream()
                .filter(m -> CollectionUtils.notEmpty(m.getOwner()))
                .map(n -> n.getOwner()).flatMap(Collection::stream).collect(Collectors.toSet());
        //通过企信服务发送联系人变动通知
        contactSessionSandwichService.push(user.getTenantId(), Lists.newArrayList(owners));
    }
}
