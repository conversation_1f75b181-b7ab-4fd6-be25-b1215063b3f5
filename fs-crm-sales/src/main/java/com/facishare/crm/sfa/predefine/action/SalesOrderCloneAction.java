package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.constant.SalesOrderConstants;
import com.facishare.crm.sfa.utilities.validator.SalesOrderValidator;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardCloneAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.CurrencyFieldDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2019/8/16 15:38
 */
public class SalesOrderCloneAction extends StandardCloneAction {

    String DEFAULT_VALUE_PRODUCT_PRICE = "$product_id__r.price$";
    String DEFAULT_VALUE_PRICE_BOOK_PRODUCT_PRICE = "$price_book_product_id__r.pricebook_sellingprice$";


    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);

//        IObjectDescribe salesOrderProductDescribe = serviceFacade.findObject(actionContext.getTenantId(), Utils.SALES_ORDER_PRODUCT_API_NAME);
//        CurrencyFieldDescribe productPrice = (CurrencyFieldDescribe)salesOrderProductDescribe.getFieldDescribe("product_price");
//        Integer decimalPlaces = productPrice.getDecimalPlaces();


        IObjectDescribe salesOrderProductDescribe = serviceFacade.findObject(actionContext.getTenantId(), Utils.SALES_ORDER_PRODUCT_API_NAME);
        String productPriceDefaultValue = salesOrderProductDescribe.getFieldDescribe("product_price").getDefaultValue().toString();
        // 上面2个公式，走特殊逻辑
        if(Objects.equals(productPriceDefaultValue, DEFAULT_VALUE_PRICE_BOOK_PRODUCT_PRICE) || Objects.equals(productPriceDefaultValue, DEFAULT_VALUE_PRODUCT_PRICE)){
            Map<String, List<ObjectDataDocument>> newDetails = SalesOrderValidator.getProductNewPrice(newResult.getDetails(), actionContext.getTenantId());
            newResult.setDetails(newDetails);
        }


        ObjectDataDocument objectData = result.getObjectData();
        SalesOrderConstants.salesOrderResetNullToField.forEach(o->{
            objectData.put(o, null);
        });
        objectData.put("invoice_amount","0");

        Map<String, List<ObjectDataDocument>> detailDataMap = result.getDetails();
        if (detailDataMap.containsKey(Utils.SALES_ORDER_PRODUCT_API_NAME)) {
            List<ObjectDataDocument> salesOrderProductList = detailDataMap.getOrDefault(Utils.SALES_ORDER_PRODUCT_API_NAME, Lists.newArrayList());
            salesOrderProductList.forEach(x -> {
                for (String fieldApiName : SalesOrderConstants.salesOrderProductResetNullToField) {
                    x.put(fieldApiName, null);
                }
                for (String fieldApiName : SalesOrderConstants.salesOrderProductResetNullToZeroField) {
                    x.put(fieldApiName, "0");
                }
            });
        }
        IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(SalesOrderConstants.SalesOrderField.PRICE_BOOK_ID.getApiName());
        if (fieldDescribe != null && !Boolean.TRUE.equals(fieldDescribe.isActive())) {
            objectData.put(SalesOrderConstants.SalesOrderField.PRICE_BOOK_ID.getApiName(), null);
        }
        newResult.setObjectData(objectData);
        newResult.setDetails(detailDataMap);
        return newResult;
    }
}
