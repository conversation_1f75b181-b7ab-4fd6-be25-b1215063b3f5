package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.service.AvailableRangeService;
import com.facishare.crm.sfa.utilities.constant.AvailableConstants;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

public class AvailablePriceBookAddAction extends StandardAddAction {
    private AvailableRangeService availableRangeService = SpringUtil.getContext().getBean(AvailableRangeService.class);

    @Override
    protected Result after(Arg arg, Result result) {
        Result tmpResult = super.after(arg, result);
        String rangeId = objectData.get(AvailableConstants.PriceBookField.AVAILABLE_RANGE_ID, String.class);
        if (!Strings.isNullOrEmpty(rangeId)) {
            availableRangeService.resetCalculateStatus(actionContext.getUser(), Lists.newArrayList(rangeId));
        }
        return tmpResult;
    }
}
