package com.facishare.crm.sfa.predefine.service.real.invoice;

import com.facishare.crm.sfa.predefine.service.task.InvoiceDataTransferTaskService;
import com.facishare.crm.sfa.predefine.service.task.SalesOrderProductDataTransferTaskService;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
public class InvoiceLinesTransferService {
    private static final int LIMIT_COUNT = 3000;

    private static final String QUERY_TENANT_INVOICE_COUNT_SQL = "select count(*) as count from biz_invoice_application where tenant_id = '%s' and is_deleted <> -1";
    private static final String QUERY_TENANT_SALES_ORDER_PRODUCT_SQL = "select count(*) as count  from biz_sales_order_product where tenant_id = '%s' and is_deleted != '-1';";

    @Autowired
    private ObjectDataServiceImpl objectDataService;

    @Autowired
    private InvoiceDataTransferTaskService invoiceDataTransferTaskService;

    @Autowired
    private SalesOrderProductDataTransferTaskService salesOrderProductDataTransferTaskService;

    /**
     * 按照企业迁移,返回是否成功,如果失败,返回迁移失败的 开票申请id列表
     * 迁移是这个企业全量迁移,遇到失败的不停止
     *
     * @param tenantId
     * @return 0属于 无需等待 1属于 大企业
     */
    public int invoiceLinesTransfer(String tenantId,String userId) {
        int result = 0;
        try {
            List<Map> countRst =  objectDataService.findBySql(tenantId,String.format(QUERY_TENANT_INVOICE_COUNT_SQL,tenantId));
            if(CollectionUtils.notEmpty(countRst)){
                Integer invoiceCount =Integer.valueOf(countRst.get(0).get("count").toString());
                Calendar cal = Calendar.getInstance();
                cal.setTime(new Date(System.currentTimeMillis()));
                Random sleepMills = new Random(cal.getTimeInMillis());
                cal.add(Calendar.MILLISECOND,sleepMills.nextInt(999));
                if(invoiceCount > LIMIT_COUNT){
                    result = 1;
                }
                invoiceDataTransferTaskService.createOrUpdateTask(tenantId,userId,tenantId,System.currentTimeMillis(),result,cal.getTime());
            }
        } catch (MetadataServiceException e) {
            log.error(e.getMessage());
        }
        return result;
    }

    public String getInvoiceTransferBiz(String tenantId,String userId){
        List<Map> countRst = null;
        String bizRst = "invoice_data_transfer_0";
        try {
            countRst = objectDataService.findBySql(tenantId,String.format(QUERY_TENANT_INVOICE_COUNT_SQL,tenantId));
        } catch (MetadataServiceException e) {
            log.error(e.getMessage(),e);
        }
        if(CollectionUtils.notEmpty(countRst)){
            Integer invoiceCount =Integer.valueOf(countRst.get(0).get("count").toString());
            Calendar cal = Calendar.getInstance();
            cal.setTime(new Date(System.currentTimeMillis() + 300000));
            Random sleepMills = new Random(cal.getTimeInMillis());
            cal.add(Calendar.MILLISECOND,sleepMills.nextInt(999));
            if(invoiceCount > LIMIT_COUNT){
                bizRst = "invoice_data_transfer_1";
            }
        }
        return bizRst;
    }


    /**
     * 开票申请模式2切3的时候，全部在晚上进行迁移
     * 不区分大小企业
     *
     * @param tenantId
     * @param userId
     * @return
     */
    public int invoiceSalesOrderProductTransfer(String tenantId,String userId){
        int result = 0;
        try {
            List<Map> salesOrderProductCount = objectDataService.findBySql(tenantId, String.format(QUERY_TENANT_SALES_ORDER_PRODUCT_SQL, tenantId));
            if(CollectionUtils.notEmpty(salesOrderProductCount)){
                Integer orderProductCount =Integer.valueOf(salesOrderProductCount.get(0).get("count").toString());
                Calendar cal = Calendar.getInstance();
                cal.setTime(new Date());
                Random sleepMills = new Random(cal.getTimeInMillis());
                cal.add(Calendar.MILLISECOND,sleepMills.nextInt(999));
                if(orderProductCount > LIMIT_COUNT){
                    result = 1;
                }
                salesOrderProductDataTransferTaskService.createOrUpdateTask(tenantId,userId,tenantId,result,System.currentTimeMillis(),cal.getTime());
            }
        } catch (MetadataServiceException e) {
            log.error("invoiceSalesOrderProductTransfer fail tenantId->{}, userId->{} ", tenantId, userId, e.getMessage());
        }
        return result;
    }

    public String getInvoiceOrderProductTransferBiz(String tenantId,String userId){
        List<Map> countRst = null;
        String bizRst = "sales_order_product_data_transfer__0";
        try {
            countRst = objectDataService.findBySql(tenantId, String.format(QUERY_TENANT_SALES_ORDER_PRODUCT_SQL, tenantId));
        } catch (MetadataServiceException e) {
            log.error(e.getMessage(),e);
        }
        if(CollectionUtils.notEmpty(countRst)){
            Integer invoiceCount =Integer.valueOf(countRst.get(0).get("count").toString());
            Calendar cal = Calendar.getInstance();
            cal.setTime(new Date(System.currentTimeMillis() + 300000));
            Random sleepMills = new Random(cal.getTimeInMillis());
            cal.add(Calendar.MILLISECOND,sleepMills.nextInt(999));
            if(invoiceCount > LIMIT_COUNT){
                bizRst = "sales_order_product_data_transfer__1";
            }
        }
        return bizRst;
    }

}
