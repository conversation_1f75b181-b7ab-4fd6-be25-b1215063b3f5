package com.facishare.crm.sfa.predefine.service.real.salesorder.factory;

import com.facishare.crm.sfa.predefine.service.real.salesorder.AbsType;

/**
 * <AUTHOR>
 * @date 2020/7/13 5:27 下午
 * @illustration
 */
public class SalesOrderProducer {

    public static AbsSalesOrder getFactory(String choice){
        if(choice.equalsIgnoreCase(AbsType.PRICE.getType())){
            return new SalesOrderProductPrice();
        }
        return null;
    }

}
