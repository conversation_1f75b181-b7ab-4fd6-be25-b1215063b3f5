package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crmcommon.constants.CommonProductConstants;
import com.facishare.crmcommon.constants.SystemConstants;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.AvailableRangeService;
import com.facishare.crm.sfa.predefine.service.AvailableRangeServiceImpl;
import com.facishare.crm.sfa.predefine.service.BomService;
import com.facishare.crm.sfa.predefine.service.BomServiceImpl;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.cpq.BomCoreService;
import com.facishare.crm.sfa.utilities.constant.BomConstants;
import com.facishare.crm.sfa.utilities.constant.PriceBookConstants;
import com.facishare.crm.sfa.utilities.constant.QuoteConstants;
import com.facishare.crm.sfa.utilities.enums.EnumUtil;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.sfa.utilities.util.Price.RealPriceService;
import com.facishare.crm.sfa.utilities.util.SoCommonUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.controller.BaseListController;
import com.facishare.paas.appframework.core.predef.controller.StandardTreeRelatedListV1Controller;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.INameCache;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.impl.ui.layout.FieldSection;
import com.facishare.paas.metadata.impl.ui.layout.FormField;
import com.facishare.paas.metadata.impl.ui.layout.component.TableComponent;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.ui.layout.ITableColumn;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.utilities.util.SOI18NKeyUtils.SO_SUB_FIELD_MODIFIED_ADJUST_PRICE;

/**
 * <AUTHOR>
 * @date 2019/9/2 10:55
 */
public class BomTreeRelatedListV1Controller extends StandardTreeRelatedListV1Controller {


    private List<ObjectDataDocument> globalRootBomList = Lists.newArrayList();

    private List<String> bomIds = Lists.newArrayList();
    private List<String> bomIdToGroup = Lists.newArrayList();

    private BomService bomService = SpringUtil.getContext().getBean(BomServiceImpl.class);
    private BomCoreService bomCoreService = SpringUtil.getContext().getBean(BomCoreService.class);

    private AvailableRangeService availableRangeService = SpringUtil.getContext().getBean(AvailableRangeServiceImpl.class);
    private static final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);

    @Override
    protected void init() {
        super.init();
    }

    @Override
    protected SearchTemplateQuery buildTreeSearchTemplateQuery(ObjectDescribeExt objectDescribeExt, String searchQueryInfo) {

        bomIds = Lists.newArrayList(getBomIds(searchQueryInfo));
        SearchTemplateQuery query = serviceFacade.getSearchTemplateQuery(controllerContext.getUser(), objectDescribe,
                getSearchTemplateId(), searchQueryInfo, true);
        query.setLimit(1000);
        query.setNeedReturnCountNum(false);
        query.setPermissionType(0);
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, "life_status", "normal");
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, "is_deleted", "0");
        SearchTemplateQueryExt.of(query).addFilter(Operator.IN, "_id", bomIds);
        return query;
    }

    @Override
    protected ILayout getChildDetailLayout() {
        ILayout childLayout = super.getChildDetailLayout();

        if (!RequestUtil.isMobileRequest()) {
            return childLayout;
        }
        LayoutExt bomLayout = LayoutExt.of(childLayout);
        FieldSection fieldSection = bomLayout.getFormComponent().get().getFieldSection().get();
        List<IFormField> fields = fieldSection.getFields();
        fields.stream().filter(o -> !o.getFieldName().endsWith("__c") && !("amount".equals(o.getFieldName()) || "adjust_price".equals(o.getFieldName()))).forEach(o -> o.setReadOnly(true));

        IFormField modifiedAdjustPriceFormField = new FormField();
        modifiedAdjustPriceFormField.setReadOnly(false);
        modifiedAdjustPriceFormField.setRequired(false);
        modifiedAdjustPriceFormField.setRenderType("currency");
        modifiedAdjustPriceFormField.setFieldName("modified_adjust_price");
        int index = -1;
        for (int i = 0; i < fields.size(); i++) {
            IFormField field = fields.get(i);
            if ("adjust_price".equals(field.getFieldName())) {
                index = i + 1;
            }
        }
        if (index != -1) {
            fields.add(index, modifiedAdjustPriceFormField);
            fieldSection.setFields(fields);
            try {
                List<IComponent> components = bomLayout.getComponents();
                components.removeIf(o -> "relevant_team_component".equals(o.getName()));
                childLayout.setComponents(components);
            } catch (MetadataServiceException e) {

            }
        }
        return childLayout;
    }

    @Override
    protected List<ILayout> getDetailLayout() {
        return Lists.newArrayList(this.getChildDetailLayout());
    }

    @Override
    protected List<ILayout> findMobileLayouts(ObjectDescribeExt describeExt) {
        List<ILayout> mobileLayouts = super.findMobileLayouts(describeExt);
        if (!Utils.BOM_API_NAME.equals(describeExt.getApiName())) {
            return mobileLayouts;
        }

        if (!RequestUtil.isMobileRequest()) {
            return mobileLayouts;
        }
        ILayout iLayout = mobileLayouts.get(0);
        LayoutExt bomLayout = LayoutExt.of(iLayout);
        TableComponent tableComponent = bomLayout.getTableComponent().get();
        List<ITableColumn> fields = tableComponent.getIncludeFields();
        fields.removeIf(field -> Objects.equals(field.getName(), "amount"));
        tableComponent.setIncludeFields(fields);
        try {
            iLayout.setComponents(bomLayout.getComponents());
        } catch (MetadataServiceException e) {
            log.error("bomTreeRelatedListController findMobileLayouts tenantId:{}", controllerContext.getTenantId(), e);
        }
        // 移动端不下发调整价格，不下发数量，设计如此系列
//        ITableColumn modifiedAdjustPriceITableColumn = new TableColumn();
//        modifiedAdjustPriceITableColumn.setName("modified_adjust_price");
//        modifiedAdjustPriceITableColumn.setRenderType("currency");
//        modifiedAdjustPriceITableColumn.setLabelName(I18N.text(SO_SUB_FIELD_MODIFIED_ADJUST_PRICE));
//        modifiedAdjustPriceITableColumn.set("api_name", "modified_adjust_price");
//        int index = -1;
//        for (int i = 0; i < fields.size(); i++) {
//            ITableColumn field = fields.get(i);
//            if ("adjust_price".equals(field.getName())) {
//                index = i + 1;
//            }
//        }
//        if (index != -1) {
//            fields.add(index, modifiedAdjustPriceITableColumn);
//            tableComponent.setIncludeFields(fields);
//            try {
//                iLayout.setComponents(bomLayout.getComponents());
//            } catch (MetadataServiceException e) {
//                log.error("bomTreeRelatedListController findMobileLayouts tenantId:{}", controllerContext.getTenantId(), e);
//            }
//        }
        mobileLayouts = Lists.newArrayList(iLayout);

        return mobileLayouts;
    }

    @Override
    protected Result buildResult(List<ILayout> mobileLayouts, List<ILayout> detailLayouts, List<IObjectData> queryChildList) {
        Result result = super.buildResult(mobileLayouts, detailLayouts, queryChildList);
        if (!RequestUtil.isMobileRequest()) {
            return result;
        }
        if (arg.getIncludeDesc()) {
            ObjectDescribeDocument objectDescribeDocument = result.getObjectDescribes().stream().filter(o -> SFAPreDefineObject.Bom.getApiName().equals(o.toObjectDescribe().getApiName())).findFirst().get();
            IFieldDescribe adjustPrice = objectDescribeDocument.toObjectDescribe().getFieldDescribe("adjust_price");
            IFieldDescribe modifiedAdjustPrice = deepCopyList(adjustPrice);
            modifiedAdjustPrice.setApiName("modified_adjust_price");
            modifiedAdjustPrice.setId("123");
            modifiedAdjustPrice.setLabel(I18N.text(SO_SUB_FIELD_MODIFIED_ADJUST_PRICE));
            objectDescribeDocument.toObjectDescribe().addFieldDescribe(modifiedAdjustPrice);
        }
        return result;
    }

    @Override
    protected BaseListController.Result after(Arg arg, BaseListController.Result result) {
        Result rst = (Result) super.after(arg, result);
        //TODO: 判断findFrist null, 这段逻辑提前到DoAction（getBomPriceByAccountIdAndProId）
        List<ObjectDataDocument> bomList = rst.getDataMapList().stream().filter(o -> Utils.BOM_API_NAME.equals(o.getDescribeApiName())).findFirst().get().getDataList();
        IObjectDescribe productObjDesc = serviceFacade.findObject(controllerContext.getTenantId(), Utils.PRODUCT_API_NAME);
        IFieldDescribe fieldDescribe = productObjDesc.getFieldDescribe(CommonProductConstants.Field.Unit.apiName);
        List<Map> optionsList = fieldDescribe.get("options", List.class);
        // 给Bom数据补充相关信息
        fillData(bomList, optionsList, Utils.PRODUCT_API_NAME, BomConstants.FIELD_PRODUCT_ID);
        fillProductGroup(bomList, rst);
        fillRootProduct(rst);
        // 佳能灰度控制，下发priceBookProduct__ro
        if (GrayUtil.isGrayBomPriceBookProduct(controllerContext.getTenantId())) {
            fillPriceBookProduct(bomList, arg.getPriceBookId(), rst);
        } else {
            // 只下发根节点的价目表数据
            fillPriceBookProduct(arg.getPriceBookId(), rst);
            getBomPriceByAccountIdAndProId(bomList);
            fill2EditConfig(bomList);
        }
        return rst;
    }

    /**
     * 除了 Bis001（佳能）外的企业走该逻辑
     *
     * @param bomList
     */
    private void getBomPriceByAccountIdAndProId(List<ObjectDataDocument> bomList) {
        if (StringUtils.isBlank(arg.getAccountId())) {
            return;
        }
        List<ObjectDataDocument> newBomList = bomList.stream().filter(x -> MapUtils.getIntValue(x, BomConstants.FIELD_PRICE_MODE, 0) == EnumUtil.PriceMode.PRICE_BOOK.getValue()).collect(Collectors.toList());
        if (CollectionUtils.empty(newBomList)) {
            return;
        }
        RealPriceService.Arg param = new RealPriceService.Arg();
        param.setAccountId(arg.getAccountId());
        param.setProductIdList(newBomList.stream().map(x -> MapUtils.getString(x, BomConstants.FIELD_PRODUCT_ID)).collect(Collectors.toList()));
        RealPriceService.Result realPrice = availableRangeService.getRealPrice(new ServiceContext(controllerContext.getRequestContext(), "", ""), param);
        Map<String, ObjectDataDocument> rstMap = realPrice.getRst();
        rstMap.forEach((k, v) -> {
            bomList.stream().filter(x -> MapUtils.getIntValue(x, BomConstants.FIELD_PRICE_MODE, 0) == EnumUtil.PriceMode.PRICE_BOOK.getValue()
                    && k.equals(MapUtils.getString(x, BomConstants.FIELD_PRODUCT_ID))).forEach(x -> {
                x.put(BomConstants.FIELD_ADJUST_PRICE, v.get(PriceBookConstants.ProductField.PRICEBOOKSELLINGPRICE.getApiName()));
                x.put(QuoteConstants.QuoteField.PRICEBOOKID.getApiName(), v.get(PriceBookConstants.ProductField.PRICEBOOKID.getApiName()));
                x.put("price_book_product_id", v.getId());
                x.put("price_book_product_id__r", v.get("name"));
                x.put(QuoteConstants.QuoteField.PRICEBOOKNAME.getApiName(), v.get(PriceBookConstants.ProductField.PRICEBOOKID.getApiName().concat(SystemConstants.AliasApiName.R.value)));
            });
        });
    }

    private void fill2EditConfig(List<ObjectDataDocument> bomList) {
        List<ObjectDataDocument> argBomList = arg.getBomList();
        if (CollectionUtils.empty(argBomList)) {
            return;
        }
        Map<String, String> bomIdToProductPriceId = Maps.newHashMap();
        List<Map<String, String>> keyAndValue = Lists.newArrayList();

        argBomList.forEach(doc -> {
            IObjectData data = doc.toObjectData();
            String bomId = data.get("bom_id", String.class);
            String productId = data.get("product_id", String.class);
            String priceBookId = data.get("price_book_id", String.class);
            Map<String, String> map = Maps.newHashMap();
            if (StringUtils.isNotEmpty(productId)
                    && StringUtils.isNotEmpty(priceBookId)
                    && StringUtils.isNotEmpty(bomId)) {
                map.put("product_id", productId);
                map.put("pricebook_id", priceBookId);
                keyAndValue.add(map);

                bomIdToProductPriceId.put(bomId, productId.concat(priceBookId));
            }
        });

        SearchTemplateQuery searchTemplateQuery = SoCommonUtils.buildWhereOrQuery(keyAndValue);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(controllerContext.getUser(), Utils.PRICE_BOOK_PRODUCT_API_NAME, searchTemplateQuery);
        if (CollectionUtils.empty(queryResult.getData())) {
            return;
        }

        List<IObjectData> priceBookProductList = queryResult.getData();
        Map<String, String> productPriceIdToSellingPrice = Maps.newHashMap();
        priceBookProductList.forEach(data -> {
            String productId = data.get("product_id", String.class);
            String priceBookId = data.get("pricebook_id", String.class);
            String price = data.get("pricebook_sellingprice", String.class);
            productPriceIdToSellingPrice.put(productId.concat(priceBookId), price);
        });

        bomList.forEach(data -> {
            String bomId = data.getId();
            String productPriceId = bomIdToProductPriceId.get(bomId);
            if(StringUtils.isNotEmpty(productPriceId)&&MapUtils.getIntValue(data, BomConstants.FIELD_PRICE_MODE, 0) == EnumUtil.PriceMode.PRICE_BOOK.getValue()){
                data.put(BomConstants.FIELD_ADJUST_PRICE, productPriceIdToSellingPrice.get(productPriceId));
            }
        });
    }


    private String getOptionLabel(List<Map> optionsList, String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        if (CollectionUtils.empty(optionsList)) {
            return null;
        }
        for (Map option : optionsList) {
            if (String.valueOf(option.get("value")).equals(key)) {
                return option.get("label").toString();
            }
        }
        return null;
    }

    /**
     * 给BOM数据 补充产品信息
     *
     * @param bomList
     * @param optionsList
     * @param referenceDescribeApiName
     * @param referenceFieldApiName
     */
    private void fillData(List<ObjectDataDocument> bomList, List<Map> optionsList, String referenceDescribeApiName, String referenceFieldApiName) {

        // 获取产品ID
        List<String> productIds = bomList.stream().filter(x -> x.get(referenceFieldApiName) != null).map(o -> o.get(referenceFieldApiName).toString()).distinct().collect(Collectors.toList());
        if (CollectionUtils.empty(productIds)) {
            return;
        }

        // 产品数据
        List<IObjectData> referenceDataList = serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), productIds, referenceDescribeApiName);
        for (IObjectData data : referenceDataList) {
            Object utilVal = data.get(CommonProductConstants.Field.Unit.apiName);
            if (utilVal != null) {
                data.set(CommonProductConstants.Field.Unit.apiName + "__r"
                        , getOptionLabel(optionsList, utilVal.toString()));
            }
        }

        Map<String, IObjectData> referenceDataMap = referenceDataList.stream().collect(Collectors.toMap(o -> o.getId(), o -> o));

        for (ObjectDataDocument bom : bomList) {
            IObjectData referenceData = referenceDataMap.get(bom.get(referenceFieldApiName).toString());
            if (referenceData != null) {
                bom.put(referenceFieldApiName + "__ro", ObjectDataDocument.of(referenceData));
                bom.put("bom_id__r", SoCommonUtils.getValue(bom, "name", ""));
            }
        }
    }

    /**
     * 补充 rootBom 根节点的产品数据
     *
     * @param rst
     */
    private void fillRootProduct(Result rst) {
        List<String> rootProductIds = globalRootBomList.stream()
                .map(data -> data.get(BomConstants.FIELD_PRODUCT_ID).toString()).collect(Collectors.toList());

        List<IObjectData> rootProductData = serviceFacade.findObjectDataByIdsIgnoreFormula(controllerContext.getTenantId(), rootProductIds, Utils.PRODUCT_API_NAME);

        for (IObjectData data : rootProductData) {
            Optional<ObjectDataDocument> bom = globalRootBomList.stream().filter(x -> Objects.equals(data.getId(), x.get(BomConstants.FIELD_PRODUCT_ID).toString())).findFirst();
            if (bom.isPresent()) {
                data.set("bom_id", bom.get().getId());
            }
        }
        ObjectData rootProduct = ObjectData.builder().dataList(ObjectDataDocument.ofList(rootProductData)).describeApiName(Utils.PRODUCT_API_NAME).build();
        rst.getDataMapList().add((rootProduct));
    }

    /**
     * 补充bom 分组信息
     *
     * @param bomList
     * @param rst
     */
    private void fillProductGroup(List<ObjectDataDocument> bomList, Result rst) {

        Set<String> ids = bomList.stream().map(ObjectDataDocument::getId).collect(Collectors.toSet());
        ids.addAll(globalRootBomList.stream().map(data -> data.getId()).collect(Collectors.toList()));
        if (CollectionUtils.empty(ids)) {
            if (CollectionUtils.notEmpty(bomIdToGroup)) {
                ids.addAll(bomIdToGroup);
            } else {
                return;
            }
        }

        // 查询分组信息
        SearchTemplateQuery searchQuery = bomService.getNewTemplateQuery();
        SearchTemplateQueryExt.of(searchQuery).addFilter(Operator.IN, BomConstants.FIELD_PARENT_BOM_ID, Lists.newArrayList(ids));
        QueryResult<IObjectData> groupList = serviceFacade.findBySearchQuery(controllerContext.getUser(), Utils.PRODUCT_GROUP_API_NAME, searchQuery);
        if (CollectionUtils.empty(groupList.getData())) {
            return;
        }

        List<ObjectDataDocument> parentBomDataList = Lists.newArrayList();
        parentBomDataList.addAll(bomList);
        parentBomDataList.addAll(globalRootBomList);


        List<String> productIds = Lists.newArrayList();
        Map<String, String> bomIdToProductId = Maps.newHashMap();
        Map<String, String> bomIdToBomName = Maps.newHashMap();
        parentBomDataList.forEach(data -> {
            String productId = data.get(BomConstants.FIELD_PRODUCT_ID).toString();
            productIds.add(productId);
            bomIdToProductId.put(data.getId(), productId);
            bomIdToBomName.put(data.getId(), data.get("name") == null ? "" : data.get("name").toString());
        });


        Map<String, String> referenceDataMap = serviceFacade.findObjectIdByName(controllerContext.getUser(), Utils.PRODUCT_API_NAME, productIds);
        groupList.getData().forEach(group -> {
            String productName = referenceDataMap.get(bomIdToProductId.get(group.get(BomConstants.FIELD_PARENT_BOM_ID, String.class)));
            group.set(BomConstants.FIELD_PARENT_PRODUCT_ID + "__r", productName);
            group.set(BomConstants.FIELD_PARENT_BOM_ID + "__r", bomIdToBomName.get(group.get(BomConstants.FIELD_PARENT_BOM_ID, String.class)));
        });

        ObjectData groupData = ObjectData.builder().dataList(ObjectDataDocument.ofList(groupList.getData())).describeApiName(Utils.PRODUCT_GROUP_API_NAME).build();
        rst.getDataMapList().add((groupData));
    }

    /**
     * 除了 Bis001（佳能）外的企业走该逻辑
     *
     * @param priceBookId
     * @param after
     */
    private void fillPriceBookProduct(String priceBookId, Result after) {
        if (StringUtils.isBlank(priceBookId)) {
            return;
        }
        List<String> productIds = globalRootBomList.stream().map(data -> data.get(BomConstants.FIELD_PRODUCT_ID).toString()).collect(Collectors.toList());
        List<IObjectData> priceBookProductList = getPriceBookProduct(priceBookId, productIds);
        if (CollectionUtils.empty(priceBookProductList)) {
            return;
        }
        fillRootBomPriceBookProduct(after, priceBookProductList);
    }


    /**
     * bis001（佳能） 走该逻辑
     *
     * @param bomList
     * @param priceBookId
     * @param after
     */
    private void fillPriceBookProduct(List<ObjectDataDocument> bomList, String priceBookId, Result after) {
        if (StringUtils.isBlank(priceBookId)) {
            return;
        }

        List<ObjectDataDocument> allBomDataList = Lists.newArrayList();
        allBomDataList.addAll(bomList);
        allBomDataList.addAll(globalRootBomList);
        List<String> productIds = allBomDataList.stream().map(data -> data.get(BomConstants.FIELD_PRODUCT_ID).toString()).collect(Collectors.toList());

        List<IObjectData> priceBookProductList = getPriceBookProduct(priceBookId, productIds);
        if (CollectionUtils.empty(priceBookProductList)) {
            return;
        }

        fillRootBomPriceBookProduct(after, priceBookProductList);
        fillSubBomBisOO1PriceBookProduct(bomList, priceBookProductList, after);
    }


    private void fillSubBomBisOO1PriceBookProduct(List<ObjectDataDocument> bomList, List<IObjectData> priceBookProductList, Result after) {
        Map<String, ObjectDataDocument> priceBookProductMap = priceBookProductList.stream()
                .collect(Collectors.toMap(o -> o.get(PriceBookConstants.ProductField.PRODUCTID.getApiName(), String.class),
                        o -> ObjectDataDocument.of(o)));
        bomList.forEach(oo -> oo.put("price_book_product__ro", priceBookProductMap.get(oo.get("product_id"))));
    }

    private void fillRootBomPriceBookProduct(Result after, List<IObjectData> priceBookProductList) {
        //只给根节点添加价目表明细数据
        List<IObjectData> priceBookDataList = priceBookProductList.stream()
                .filter(x -> arg.getRootProductIds().contains(x.get(PriceBookConstants.ProductField.PRODUCTID.getApiName(), String.class))).collect(Collectors.toList());

        ObjectData rootProd = ObjectData.builder().dataList(ObjectDataDocument.ofList(priceBookDataList)).describeApiName(Utils.PRICE_BOOK_PRODUCT_API_NAME).build();
        after.getDataMapList().add((rootProd));
    }


    private List<IObjectData> getPriceBookProduct(String priceBookId, List<String> productIds) {
        List<IObjectData> priceBookProductList = bomService.getPriceBookProductList(controllerContext.getUser(), priceBookId, productIds);
        List<INameCache> recordName = serviceFacade.findRecordName(ActionContextExt.of(controllerContext.getUser()).getContext(),
                Utils.PRICE_BOOK_API_NAME, Lists.newArrayList(priceBookId));
        priceBookProductList.forEach(data->data.set("pricebook_id__r", recordName.get(0).getName()));
        return priceBookProductList;
    }

    /**
     * 根据RootProductId 查询 根 BomId 数据
     *
     * @param searchQueryInfo
     * @return
     */
    private Set<String> getBomIds(String searchQueryInfo) {
        SearchTemplateQuery query = serviceFacade.getSearchTemplateQuery(controllerContext.getUser(), objectDescribe, getSearchTemplateId(), searchQueryInfo, true);
        QueryResult<IObjectData> rootBomList = bomCoreService.findBomByRootProductId(controllerContext.getUser(), query, Lists.newArrayList(arg.getRootProductIds()));

        if (CollectionUtils.empty(rootBomList.getData())) {
            hasData = Boolean.FALSE;
            return Sets.newHashSet();
        }

        List<String> rootBomIdList = Lists.newArrayList();
        for (IObjectData data : rootBomList.getData()) {
            globalRootBomList.add(ObjectDataDocument.of(data));
            rootBomIdList.add(data.getId());
        }

        List<IObjectData> rootBomInfo = bomCoreService.findBomByRootId(controllerContext.getUser(), rootBomIdList);
        List<IObjectData> tmpList = rootBomInfo.stream()
                .filter(x -> !rootBomIdList.contains(x.getId()))
                .collect(Collectors.toList());
        if (CollectionUtils.notEmpty(tmpList)) {
            return tmpList.stream().map(x -> x.getId()).collect(Collectors.toSet());
        }


        hasData = Boolean.FALSE;
        // 处理空分组的问题
        bomIdToGroup.addAll(tmpList.stream()
                .map(x -> x.get(BomConstants.FIELD_PARENT_BOM_ID).toString())
                .collect(Collectors.toList()));
        return Sets.newHashSet();
    }


    private <T> T deepCopyList(T src) {
        T dest = null;
        try {
            ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
            ObjectOutputStream out = new ObjectOutputStream(byteOut);
            out.writeObject(src);
            ByteArrayInputStream byteIn = new ByteArrayInputStream(byteOut.toByteArray());
            ObjectInputStream in = new ObjectInputStream(byteIn);
            dest = (T) in.readObject();
        } catch (IOException | ClassNotFoundException ignored) {

        }
        return dest;
    }

}
