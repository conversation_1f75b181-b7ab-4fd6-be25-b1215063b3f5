package com.facishare.crm.sfa.predefine.action;


import com.facishare.crm.sfa.utilities.util.ContractUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

/**
 * Create by baoxinxue
 */
@Slf4j
public class ContractFlowCompletedAction extends SFAFlowCompletedAction {

    @Override
    protected void doCreateAction() {
        log.info("ContractFlowCompletedAction doCreateAction,id={}", this.data.getId());
        //更新合同confirm_time字段
        ContractUtil.updateContractConfirmTime(actionContext.getUser(), Lists.newArrayList(this.data));
        super.doCreateAction();
    }

}
