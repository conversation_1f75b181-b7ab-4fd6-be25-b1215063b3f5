package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;

public class LeadsPoolListController extends SFAListController {
    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery query = super.buildSearchTemplateQuery();
        query.setDataRightsParameter(null);
        return query;
    }
}
