package com.facishare.crm.sfa.predefine.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.exception.SFABusinessException;
import com.facishare.crm.sfa.predefine.exception.SFAErrorCode;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.crm.sfa.utilities.util.LeadsUtils;
import com.facishare.crmcommon.util.SearchUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAction;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class LeadsMarkMQLAction extends AbstractStandardAction<LeadsMarkMQLAction.Arg, LeadsMarkMQLAction.Result> {
    private static final IObjectDataService objectDataService = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.MarkMQL.getActionCode());
    }

    @Override
    protected List<String> getDataPrivilegeIds(LeadsMarkMQLAction.Arg arg) {
        return Lists.newArrayList(arg.getObjectDataId());
    }

    @Override
    protected void before(LeadsMarkMQLAction.Arg arg) {
        super.before(arg);
    }

    @Override
    protected LeadsMarkMQLAction.Result doAct(LeadsMarkMQLAction.Arg arg) {
        LeadsMarkMQLAction.Result result = new LeadsMarkMQLAction.Result();
        if (CollectionUtils.isEmpty(dataList)) return result;
        dataList.stream().forEach(d -> {
            d.set("leads_stage", "MQL");
            d.set("leads_stage_changed_time", System.currentTimeMillis());
        });
        LeadsUtils.updateObjectDataByFields(actionContext.getUser(), dataList, Lists.newArrayList("leads_stage", " leads_stage_changed_time"));
        return result;
    }

    @Override
    protected LeadsMarkMQLAction.Result after(LeadsMarkMQLAction.Arg arg, LeadsMarkMQLAction.Result result) {
        result = super.after(arg, result);
        if(CollectionUtils.isNotEmpty(dataList)) {
            IObjectData objectData = dataList.get(0);
            String msg= String.format("%s：%s", I18N.text("LeadsObj.attribute.self.display_name"),objectData.getName());
            serviceFacade.logCustomMessageOnly(actionContext.getUser(), EventType.MODIFY, ActionType.MarkMQL, objectDescribe, objectData,
                    msg);
        }
        addFlowRecord();
        return result;
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.MarkMQL.getButtonApiName();
    }

    @Override
    protected IObjectData getPreObjectData() {
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIdsIncludeDeleted(actionContext.getUser(), Lists.newArrayList(arg.getObjectDataId()), actionContext.getObjectApiName());
        if (objectDataList != null && objectDataList.size() != 0) {
            return objectDataList.get(0);
        }
        return null;
    }

    @Override
    protected IObjectData getPostObjectData() {
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIdsIncludeDeleted(actionContext.getUser(), Lists.newArrayList(arg.getObjectDataId()), actionContext.getObjectApiName());
        if (objectDataList != null && objectDataList.size() != 0) {
            return objectDataList.get(0);
        }
        return null;
    }

    private void addFlowRecord() {
        List<IObjectData> oldFlowRecordDataList = Lists.newArrayList();
        for (IObjectData leadsData : dataList) {
            String oldOwnerId = LeadsUtils.getOwner(leadsData);
            if(StringUtils.isBlank(oldOwnerId)) {
                continue;
            }
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            searchTemplateQuery.setLimit(1);
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, "leads_owner", oldOwnerId);
            SearchUtil.fillFilterEq(filters, "leads_id", leadsData.getId());
            searchTemplateQuery.setFilters(filters);
            List<OrderBy> orderByList = Lists.newArrayList();
            orderByList.add(new OrderBy("last_modified_time", false));
            searchTemplateQuery.setOrders(orderByList);
            searchTemplateQuery.setNeedReturnCountNum(false);
            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(actionContext.getUser(), SFAPreDefineObject.LeadsFlowRecord.getApiName(), searchTemplateQuery);
            if(queryResult != null && org.apache.commons.collections.CollectionUtils.isNotEmpty(queryResult.getData())) {
                IObjectData oldFlowRecordData = queryResult.getData().get(0);
                oldFlowRecordData.set("leads_stage", "MQL");
                oldFlowRecordData.set("leads_stage_changed_time", leadsData.get("leads_stage_changed_time"));
                oldFlowRecordData.set("last_modified_by", Lists.newArrayList(actionContext.getUser().getUserId()));
                oldFlowRecordData.set("last_modified_time", System.currentTimeMillis());
                oldFlowRecordDataList.add(oldFlowRecordData);
            }
        }
        if(CollectionUtils.isNotEmpty(oldFlowRecordDataList)) {
            List<String> updateFieldList = Lists.newArrayList("leads_stage", "leads_stage_changed_time", "last_modified_by",
                    "last_modified_time");
            try {
//            serviceFacade.batchUpdateByFields(user, objectDataList, updateFieldList);
                objectDataService.batchUpdateWithField(oldFlowRecordDataList, updateFieldList, AccountUtil.getDefaultActionContext(actionContext.getUser(), ""));
            } catch (MetadataServiceException metadataError) {
                log.info("addFlowRecord warn", metadataError);
                throw new SFABusinessException(metadataError.getMessage(), SFAErrorCode.ACCOUNT_COMMON_ERROR);
            } catch (Exception e) {
                log.error("addFlowRecord error", e);
                throw new SFABusinessException(SFAErrorCode.ACCOUNT_COMMON_ERROR);
            }
        }
    }

    @Data
    @NoArgsConstructor
    static class Arg {
        @JsonProperty("object_data_id")
        @JSONField(name="object_data_id")
        String objectDataId;
    }

    @Data
    @Builder
    @NoArgsConstructor
    public static class Result {
    }
}
