package com.facishare.crm.sfa.predefine.bizvalidator.validator.newopportunitylines;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.bizvalidator.Validator;
import com.facishare.crm.sfa.predefine.bizvalidator.ValidatorContext;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.NewOppportunityConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.List;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_OPPORTUNITY_ID_NOT_EXISTS;
import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_PRODUCT_ALREADY_EXIST_THIS_OPPORTUNITY;

/**
 * 商机2.0明细 - 产品是否存在校验器
 * <AUTHOR>
 */
public class ProductIsRepeatedInNewOpportunityValidator implements Validator {
    ServiceFacade serviceFacade = SpringUtil.getContext().getBean(ServiceFacade.class);

    @Override
    public void validate(ValidatorContext context) {
        IObjectData objectData = context.getObjectData();
        Object productId = objectData.get(NewOppportunityConstants.NewOpportunityLinesField.PRODUCTID.getApiName());
        //产品Id不传时不校验
        if (productId == null) {
            return;
        }

        Object newOpportunityId = objectData.get(NewOppportunityConstants.NewOpportunityField.NEWOPPORTUNITYID.getApiName());
        if (newOpportunityId == null) {
            throw new ValidateException(I18N.text(SFA_OPPORTUNITY_ID_NOT_EXISTS));
        }

        Object recordType = objectData.get(IFieldType.RECORD_TYPE);
        if (recordType == null) {
            throw new ValidateException(I18N.text("paas.metadata.data.record_type_not_exist"));
        }

        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(1);
        searchQuery.setOffset(0);
        List filters = Lists.newLinkedList();
        SearchUtil.fillFilterEq(filters, NewOppportunityConstants.NewOpportunityField.NEWOPPORTUNITYID.getApiName(), newOpportunityId.toString());
        SearchUtil.fillFilterEq(filters, NewOppportunityConstants.NewOpportunityLinesField.PRODUCTID.getApiName(), productId.toString());
        SearchUtil.fillFilterEq(filters, IFieldType.RECORD_TYPE, recordType.toString());
        searchQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(context.getUser(),
                SFAPreDefineObject.NewOpportunityLines.getApiName(), searchQuery);
        if (!queryResult.getData().isEmpty()) {
            IObjectData newOpportunityLine = queryResult.getData().get(0);
            if (newOpportunityLine != null && !newOpportunityLine.getId().equals(objectData.getId())) {
                throw new ValidateException(I18N.text(SFA_PRODUCT_ALREADY_EXIST_THIS_OPPORTUNITY));
            }
        }
    }
}
