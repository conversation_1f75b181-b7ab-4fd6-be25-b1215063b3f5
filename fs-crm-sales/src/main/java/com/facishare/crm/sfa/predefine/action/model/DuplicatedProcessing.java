package com.facishare.crm.sfa.predefine.action.model;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DuplicatedProcessing
{
    private Boolean needConfirmCollect;

    private String noBlockMessage;

    private boolean collectConfirmed;

    private String collectedToLeadsId;

    private Map<String, List<String>> needMarkObjectIdList;

    private int refreshVersion;

    private String duplicatedProcessingId;
}