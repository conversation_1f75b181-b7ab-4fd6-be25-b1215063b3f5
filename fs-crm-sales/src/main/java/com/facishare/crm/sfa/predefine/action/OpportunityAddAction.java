package com.facishare.crm.sfa.predefine.action;

import com.facishare.common.TimeUtil;
import com.facishare.crmcommon.constants.SystemConstants;
import com.facishare.crm.sfa.predefine.service.OpportunityService;
import com.facishare.crm.sfa.utilities.constant.OpportunityConstants;
import com.facishare.crm.sfa.utilities.proxy.SaleActionProxy;
import com.facishare.crm.sfa.utilities.proxy.model.SaleActionRestModel;
import com.facishare.crm.sfa.utilities.proxy.model.SaleActionSelectModel;
import com.facishare.crm.sfa.utilities.util.CommonUtil;
import com.facishare.crm.sfa.utilities.util.JsonUtil;
import com.facishare.crm.sfa.utilities.util.LogUtil;
import com.facishare.crm.sfa.utilities.util.OpportunityUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.appframework.metadata.util.SFAHeaderUtil;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.facishare.rest.core.exception.RestProxyInvokeException;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.*;

/**
 * Created by liux on 2017/7/24.
 */
@Slf4j
public class OpportunityAddAction extends StandardAddAction {
    private final SaleActionProxy saleActionProxy = SpringUtil.getContext().getBean(SaleActionProxy.class);
    protected OpportunityService opportunityService = SpringUtil.getContext().getBean(OpportunityService.class);
    boolean isFromLeadsTransfer = false;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        if(isFromLeadsTransfer) {
            return Lists.newArrayList("TransferAdd");
        } else {
            return super.getFuncPrivilegeCodes();
        }
    }

    @Override
    protected void before(Arg arg) {
        log.info("OpportunityAddAction>before()>arg={}" + JsonUtil.toJsonWithNullValues(arg));
        isFromLeadsTransfer = (boolean) arg.getObjectData().getOrDefault("is_from_leads_transfer", false);

        super.before(arg);
        initDefaultValue(arg);

        //判断流程是否适用员工部门
        SaleActionSelectModel.Arg argSaleActionSelectModel = new SaleActionSelectModel.Arg();
        argSaleActionSelectModel.setCurrentEmployeeID(Integer.parseInt(actionContext.getUser().getUserId()));
        argSaleActionSelectModel.setPageSize(10000);
        argSaleActionSelectModel.setPageTime(0L);
        argSaleActionSelectModel.setType(2);//取适用我所在部门的流程

        String salesProcessId = objectData.get("sales_process_id", String.class);
        if (Strings.isNullOrEmpty(salesProcessId)) {
            salesProcessId = objectData.get("sales_process_name", String.class);
        }

        if (StringUtil.isNullOrEmpty(salesProcessId)) {
            throw new ValidateException(I18N.text(SFA_SALES_PROCESS_CANNOT_BE_EMPTY));
        }
        try {
            if (!StringUtil.isNullOrEmpty(salesProcessId)) {
                //根据流程id找第一个售前阶段，取对应的赢率回写
                List<Map> saleActionStageList = CommonUtil.getSFATableSimpleEntityListByQueryId(actionContext.getUser(), "sale_action_stage", "sale_action_id", salesProcessId);
                List<Map> preSaleActionStages = saleActionStageList.stream()
                        .filter(r -> r.get("type").toString().equals(OpportunityConstants.SaleActionType.PRE_SALE.getValue()))
                        .collect(Collectors.toList());
                if (preSaleActionStages != null && !preSaleActionStages.isEmpty()) {
                    //Optional<Map> preSaleActionStage=preSaleActionStages.stream().sorted(Comparator.comparing(Map::get)).findFirst();
                    Collections.sort(preSaleActionStages, new Comparator<Map>() {
                        @Override
                        public int compare(Map o1, Map o2) {
                            return new Integer(o1.get("stage_order").toString()).compareTo(new Integer(o2.get("stage_order").toString()));
                        }
                    });
                    Optional<Map> preSaleActionStage = preSaleActionStages.stream().findFirst();
                    if (preSaleActionStage.isPresent()) {
                        Integer probability = Integer.valueOf(preSaleActionStage.get().get("weight").toString());
                        arg.getObjectData().toObjectData().set("probability", probability);
                    }
                }

                //判断流程是否使用所在部门
                log.info("argSaleActionSelectModel>arg={}" + JsonUtil.toJsonWithNullValues(argSaleActionSelectModel));
                SaleActionSelectModel.Result response = saleActionProxy.getSelectSaleActionList(SFAHeaderUtil.getHeaders(actionContext.getUser()), argSaleActionSelectModel);
                List<SaleActionSelectModel.SaleActionInfo> saleActionInfoList = response.getValue();
                if (saleActionInfoList != null) {
                    boolean matchSaleActionFlag = false;
                    for (SaleActionSelectModel.SaleActionInfo saleActionInfo : saleActionInfoList) {
                        if (saleActionInfo.getSaleActionID().equals(salesProcessId)) {
                            matchSaleActionFlag = true;
                            break;
                        }
                    }
                    if (!matchSaleActionFlag) {
                        log.warn("流程不适用所在部门 warn,arg:{}",
                                new Object[]{com.facishare.rest.core.util.JsonUtil.toJsonWithNull(argSaleActionSelectModel)});
                        throw new ValidateException(I18N.text(SFA_PROCESS_DOESNOT_APPLY_DEPARTMENT));
                    }
                }
            } else {
                throw new ValidateException(I18N.text(SFA_SALES_PROCESS_DOES_NOT_EXIST));
            }
        } catch (RestProxyInvokeException e) {
            log.error("RestProxyInvokeException error ", argSaleActionSelectModel);
            throw new RestProxyInvokeException(e);
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        //商机关联销售流程
        String opportunityID = result.getObjectData().getId();
        //String saleActionID = arg.getObjectData().get("sales_process_name").toString();
        String saleActionID = objectData.get("sales_process_id", String.class);
        if (Strings.isNullOrEmpty(saleActionID)) {
            saleActionID = objectData.get("sales_process_name", String.class);
        }
        if (!StringUtil.isNullOrEmpty(saleActionID)) {
            //商机关联销售流程
            SaleActionRestModel.Arg argSaleAction = new SaleActionRestModel.Arg();
            argSaleAction.setSaleActionID(saleActionID);
            argSaleAction.setOpportunityIds(Arrays.asList(opportunityID));

            try {
                log.info("addOpportunitySaleAction>arg={}" + JsonUtil.toJsonWithNullValues(argSaleAction));
                SaleActionRestModel.Result response = saleActionProxy.addOpportunitySaleAction(SFAHeaderUtil.getHeaders(actionContext.getUser()), argSaleAction);
                if (!response.isValue()) {
                    log.error("addOpportunitySaleAction error,headers:{},arg:{},result:{}",
                            new Object[]{SFAHeaderUtil.getHeaders(actionContext.getUser()), com.facishare.rest.core.util.JsonUtil.toJsonWithNull(argSaleAction), com.facishare.rest.core.util.JsonUtil.toJsonWithNull(response)});
                    throw new ValidateException(response.getMessage());
                }
            } catch (RestProxyInvokeException e) {
                log.error("RestProxyInvokeException error ", argSaleAction);
                throw new RestProxyInvokeException(e);
            }
            opportunityService.bindOpportunitySaleAction(actionContext.getUser(), saleActionID, result.getObjectData().toObjectData());
        }
        long expectedDealTime = objectData.get("expected_deal_closed_date", Long.class);
        OpportunityUtil.addOrModifyOpportunityTimeOutTask(actionContext.getTenantId(),actionContext.getUser(),opportunityID,expectedDealTime,true);

        super.after(arg, result);
        return result;
    }

    private void initDefaultValue(Arg arg) {
        //填充商机状态
        arg.getObjectData().toObjectData().set("biz_status", OpportunityConstants.OpportunityBizStatus.RUNNING.getValue());
        arg.getObjectData().toObjectData().set("life_status", SystemConstants.LifeStatus.Ineffective.value);
        arg.getObjectData().toObjectData().set("last_followed_time", TimeUtil.now());
    }

    @Override
    protected void recordLog() {
        List<IObjectData> allObjectData = this.getAllObjectDataCopy();
        LogUtil.recordAddSpecailLog(actionContext.getUser(), allObjectData, objectData, objectDescribe);
        this.stopWatch.lap("recordLog");
    }
}
