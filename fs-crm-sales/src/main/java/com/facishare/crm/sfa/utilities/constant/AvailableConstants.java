package com.facishare.crm.sfa.utilities.constant;

public interface AvailableConstants {

    class AvailableRangeField {
        public static final String ORG_IDS = "org_ids";
        public static final String PARTNER_IDS = "partner_ids";
        public static final String PRIORITY = "priority";
        public static final String CALCULATE_STATUS = "calculate_status";
        public static final String ACCOUNT_RANGE = "account_range";
        public static final String PARTNER_RANGE = "partner_range";
        public static final String PRODUCT_RANGE = "product_range";
        public static final String ORG_RANGE = "org_range";
    }

    class AccountResultField {
        public static final String ACCOUNT_ID = "account_id";
        public static final String AVAILABLE_RANGE_ID = "available_range_id";
    }

    class ProductResultField {
        public static final String AVAILABLE_RANGE_ID = "available_range_id";
        public static final String PRODUCT_ID = "product_id";
    }

    class PriceBookField {
        public static final String AVAILABLE_RANGE_ID = "available_range_id";
        public static final String PRICE_BOOK_ID = "price_book_id";

    }

    class PublicConstants {
        public static final String RANGE_ALL = "ALL";
        public static final String RANGE_NONE = "NONE";
    }

    enum CalculateStatus {
        CALCULATING("0"),
        EFFECTIVE("1");

        private String status;

        CalculateStatus(String status) {
            this.status = status;
        }
        public String getStatus() {
            return status;
        }
    }

    enum AvailableRangePriority {
        FIXED(1),
        CONDITION(2),
        ALL(3);

        private int priority;

        AvailableRangePriority(int priority) {
            this.priority = priority;
        }
        public int getPriority() {
            return priority;
        }
    }
}
