package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crmcommon.manager.MultiUnitManager;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.ModuleCtrlConfigService;
import com.facishare.crm.sfa.predefine.service.modulectrl.IModuleInitService;
import com.facishare.crm.sfa.predefine.service.real.MultiUnitService;
import com.facishare.crm.sfa.predefine.service.real.invoice.InvoiceService;
import com.facishare.crm.sfa.utilities.constant.InvoiceApplicationConstants;
import com.facishare.crm.sfa.utilities.constant.SalesOrderConstants;
import com.facishare.crm.sfa.utilities.util.*;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.ISearchTemplateQuery;
import com.facishare.paas.metadata.api.search.ISpecifiedTableParameter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.impl.search.SpecifiedTableParameter;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * Created by luxin on 2018/1/26.
 * <AUTHOR>
 * @IgnoreI18nFile
 */
@Component
public class SalesOrderProductRelatedListController extends SFARelatedListController {
    protected StopWatch orderProductRelateListStopWatch = StopWatch.create("order_product_relate_list");
    private static final ModuleCtrlConfigService moduleCtrlConfigService = SpringUtil.getContext().getBean(ModuleCtrlConfigService.class);
    private InvoiceService invoiceService = SpringUtil.getContext().getBean(InvoiceService.class);
    private MultiUnitManager multiUnitManager = SpringUtil.getContext().getBean(MultiUnitManager.class);
    private MultiUnitService multiUnitService = SpringUtil.getContext().getBean(MultiUnitService.class);

    private Boolean fromInvoiceApplication = false;
    private boolean invoiceIsAllowedOverflow = false;
    private boolean newInvoice = false;
    private boolean isInvoiceMode3 = false;

    private long time = System.currentTimeMillis();



    @Override
    protected QueryResult<IObjectData> getQueryResult(SearchTemplateQuery query) {
        orderProductRelateListStopWatch.lap("SalesOrderProductRelatedListController getQueryResult  begin");
        QueryResult<IObjectData> queryResult = super.getQueryResult(query);
        orderProductRelateListStopWatch.lap("SalesOrderProductRelatedListController getQueryResult  end");
        handleProductInfo(queryResult.getData());
        return queryResult;
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        openStatusInit();
    }

    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery searchTemplateQuery = super.buildSearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        if (fromInvoiceApplication) {
            // 不可以超额开票 + 新开票 + 代开票金额>0
            if (!invoiceIsAllowedOverflow && newInvoice && isInvoiceMode3) {
                IFilter filter1 = new Filter();
                filter1.setFieldName("no_invoice_amount");
                filter1.setFieldValues(Lists.newArrayList("0"));
                filter1.setOperator(Operator.GT);
                filters.add(filter1);

                IFilter invoiceStatusFilter = new Filter();
                invoiceStatusFilter.setFieldName("invoice_status");
                invoiceStatusFilter.setFieldValues(Lists.newArrayList("2", "3"));
                invoiceStatusFilter.setOperator(Operator.IN);
                filters.add(invoiceStatusFilter);

                searchTemplateQuery.setSpecifiedTableParameter(getISpecifiedTableParameter());

            }
        }
        searchTemplateQuery.setFilters(filters);
        return searchTemplateQuery;
    }

    private void handleProductInfo(List<IObjectData> objectDataList) {
        orderProductRelateListStopWatch.lap("SalesOrderProductRelatedListController handleProductInfo  begin");
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }

        List<String> productIds = Lists.newArrayList();
        Map<String, String> idMap = Maps.newHashMap();

        for (IObjectData objectData : objectDataList) {
            String productId = objectData.get("product_id", String.class);
            if (!productIds.contains(productId)) {
                productIds.add(productId);
            }
            idMap.put(objectData.getId(), productId);
            objectData.set("product_name", objectData.get("product_id__r"));
        }

        List<IObjectData> products = serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), productIds, Utils.PRODUCT_API_NAME);

        if (CollectionUtils.empty(products)) {
            return;
        }

        for (IObjectData objectData : objectDataList) {
            Map<String, Object> map = Maps.newHashMap();
            String productId = objectData.get("product_id", String.class);
            Optional<IObjectData> product = products.stream().filter(p -> p.getId().equals(productId)).findFirst();
            if (product.isPresent()) {
                map.put("name", product.get().getName());
                map.put("product_status", product.get().get("product_status"));
                map.put("price", product.get().get("price"));
                map.put("unit", objectData.get("unit"));

                objectData.set("product_id__ro", map);
            }
        }
        if (multiUnitManager.isOpenMultiUnit(controllerContext.getTenantId())) {
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            List<IFilter> filters = Lists.newArrayList();
            IFilter filter = new Filter();
            filter.setFieldName("product_id");
            filter.setFieldValues(productIds);
            filter.setOperator(Operator.IN);
            filters.add(filter);
            searchTemplateQuery.setFilters(filters);
            searchTemplateQuery.setOffset(0);
            searchTemplateQuery.setLimit(500);
            searchTemplateQuery.setWheres(Lists.newArrayList());
            //0不走权限  1走权限
            searchTemplateQuery.setPermissionType(0);
            QueryResult<IObjectData> multiUnitRelateds = serviceFacade.findBySearchQuery(controllerContext.getUser(),
                    Utils.MULTI_UNIT_RELATED_API_NAME,
                    searchTemplateQuery);
            if (multiUnitRelateds == null || multiUnitRelateds.getData().isEmpty()) {
                return;
            }

            for (IObjectData objectData : objectDataList) {
                String productId = objectData.get("product_id", String.class);
                String actualUnit = objectData.get("actual_unit", String.class);
                String quantity=objectData.get("quantity", String.class);
                Optional<IObjectData> multiUnitRelated = multiUnitRelateds.getData().stream()
                        .filter(p -> p.get("product_id").equals(productId)&&p.get("unit_id").equals(actualUnit)).findFirst();
                if (multiUnitRelated.isPresent()) {
                    Object p=multiUnitRelated.get().get("places_decimal");
                    if (p != null) {
                        Integer placesDecimal = Integer.parseInt(p.toString());
                        Integer decimal = multiUnitService.getSalesOrderProductQuantityDecimalPlaces(objectDescribe);
                        if (placesDecimal < decimal) {
                            Double q = Double.valueOf(quantity);
                            objectData.set("quantity", String.format("%." + placesDecimal + "f", q));
                        }
                    }
                }
            }
        }
        orderProductRelateListStopWatch.lap("SalesOrderProductRelatedListController handleProductInfo  end");
    }

    @Override
    protected Result after(Arg arg, Result result) {
        orderProductRelateListStopWatch.logSlow(100);
        Result newResult = super.after(arg, result);
        newResult = SoRelatedListUtils.removeButtons(newResult, SoRelatedListUtils.xxProductFilterButtonInfo);
        if(newResult.getDataList().size() < 1
                && Objects.equals(arg.getTargetObjectApiName(), Utils.SALES_ORDER_API_NAME)){
            SoCommonUtils.sendAuditLog("订单产品数量为0，请注意！！！", time, result.getDataList().size(), arg.getTargetObjectDataId(), arg.toString(),
                    new ActionContext(controllerContext.getRequestContext(), Utils.SALES_ORDER_PRODUCT_API_NAME, "SalesOrderProductRelatedList"));
        }
        return newResult;
    }

    @Override
    protected ObjectDescribeDocument buildDescribeExt(ISearchTemplateQuery query) {
        ObjectDescribeDocument objectDescribeDocument = super.buildDescribeExt(query);
        return customFieldIsNotCloneable(objectDescribeDocument);
    }

    private ObjectDescribeDocument customFieldIsNotCloneable(ObjectDescribeDocument objectDescribeDocument) {
        // 特殊处理不能复制的字段
        IObjectDescribe objectDescribe1 = objectDescribeDocument.toObjectDescribe();
        List<IFieldDescribe> fieldDescribes = Lists.newArrayList();
        SalesOrderConstants.salesOrderProductResetNullToField.forEach(o -> {
            IFieldDescribe fieldDescribe = this.objectDescribe.getFieldDescribe(o);
            if (fieldDescribe != null) {
                fieldDescribe.set("is_cloneabled", false);
                fieldDescribes.add(fieldDescribe);
            }
        });
        objectDescribe1.setFieldDescribes(fieldDescribes);
        return ObjectDescribeDocument.of(objectDescribe1);
    }

    private void getSpecialData() {
        ObjectDataDocument objectData = arg.getObjectData();
        if(objectData == null){
            return;
        }
        if (objectData.containsKey("from_invoice_application")) {
            Boolean flag = (Boolean) objectData.get("from_invoice_application");
            fromInvoiceApplication = flag;
        }
    }

    private ISpecifiedTableParameter getISpecifiedTableParameter(){
        ISpecifiedTableParameter specifiedTableParameter = new SpecifiedTableParameter();
        ISpecifiedTableParameter.JoinCondition joinCondition = ISpecifiedTableParameter.JoinCondition.builder().mainTableColumn("id").joinTableColumn("orderProductId").build();
        ISpecifiedTableParameter.JoinCondition joinCondition1 = ISpecifiedTableParameter.JoinCondition.builder().mainTableColumn("tenant_id").joinTableColumn("tenantId").build();
        specifiedTableParameter.setJoinConditions(Lists.newArrayList(joinCondition, joinCondition1));
        specifiedTableParameter.setJoinPattern("left");
        specifiedTableParameter.setTableName("(select order_product_id as orderProductId, tenant_id as tenantId , sum(invoiced_amount :: numeric) as sumAmount\n" +
                "                  from invoice_line\n" +
                "                  where tenant_id = '"+controllerContext.getTenantId()+"'\n" +
                "                    and invoice_line.life_status = 'under_review' and is_deleted = '0'\n" +
                "                  group by order_product_id, tenant_id) as newTable\n" +
                "");
        specifiedTableParameter.setTableNameAlias("newTable");
        specifiedTableParameter.setWhereConditions(Lists.newArrayList("SalesOrderProductObj.no_invoice_amount :: numeric > COALESCE(sumAmount, 0)"));
        return specifiedTableParameter;
    }

    private void openStatusInit(){
        invoiceIsAllowedOverflow = moduleCtrlConfigService.isOpen("invoice_is_allowed_overflow", controllerContext.getUser());
        newInvoice = moduleCtrlConfigService.isOpen(IModuleInitService.MODULE_NEW_INVOICE, controllerContext.getUser());
        InvoiceApplicationConstants.InvoiceApplicationMode invoiceMode = invoiceService.getInvoiceMode(controllerContext.getTenantId());
        if(invoiceMode.equals(InvoiceApplicationConstants.InvoiceApplicationMode.SALES_ORDER_PRODUCT)){
            isInvoiceMode3 = true;
        }
        if (newInvoice) {
            getSpecialData();
        }
    }

}