package com.facishare.crm.sfa.predefine.service;

import com.facishare.crm.sfa.predefine.service.model.CheckProductBomModel;
import com.facishare.crm.sfa.predefine.service.model.GetAllParentProdListModel;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;

import java.util.List;

public interface BomService {
    GetAllParentProdListModel.Result getAllParentProdList(ServiceContext context, GetAllParentProdListModel.Arg arg);

    CheckProductBomModel.Result checkBom(ServiceContext serviceContext, CheckProductBomModel.Arg arg);

    /**
     * 校验产品包bom是否适配的阶梯价目表
     * 直接在明细存在就校验住，不区分是否匹配到的了
     *
     * @param serviceContext
     * @param arg
     * @return
     */
    CheckProductBomModel.CheckTieredPriceProductResult checkTieredPriceProductBom(ServiceContext serviceContext, CheckProductBomModel.CheckTieredPriceProductArg arg);


    List<IObjectData> getPriceBookProductList(User user, String priceBookId, List<String> productIds);

    SearchTemplateQuery getNewTemplateQuery();

    List<ObjectDataDocument> getProductGroupByIds(ServiceContext context, List<String> ids);

    /**
     * 校验产品BOM的数量信息
     *
     * @param objectDataDocument
     */
    void checkSubProducts(ObjectDataDocument objectDataDocument);

}
