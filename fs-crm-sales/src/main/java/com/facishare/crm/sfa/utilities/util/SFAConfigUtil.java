package com.facishare.crm.sfa.utilities.util;

import com.facishare.crmcommon.enums.ConfigType;
import com.facishare.crm.openapi.Utils;
import com.facishare.crmcommon.rest.SettingRestApi;
import com.facishare.crmcommon.rest.dto.SetConfigModel;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.Strings;

import java.util.*;

/**
 * Created by renlb on 2019/1/17.
 */
public class SFAConfigUtil {
    private static ConfigService configService = SpringUtil.getContext().getBean(ConfigService.class);
//    private static final ObjectDataServiceImpl objectDataService = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);

    public static void setConfigValue(String tenantId, String userId, String key, String value) {
        SettingRestApi restApi = SpringUtil.getContext().getBean(SettingRestApi.class);
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("x-fs-ei", tenantId);
        headerMap.put("x-fs-userInfo", User.SUPPER_ADMIN_USER_ID);
        restApi.setConfigValue(SetConfigModel.Arg.builder().key(key).value(value).build(), headerMap);
    }

    public static String getConfigValue(String tenantId, String configKey, String userId) {
        User user = new User(tenantId, userId, "", "");
        String queryRst = configService.findTenantConfig(user, configKey);
        if (com.google.common.base.Strings.isNullOrEmpty(queryRst)) {
            return getDefaultConfigValue(configKey);
        } else {
            return queryRst;
        }
    }

    public static Map<String, String> getConfigValues(String tenantId, String userId, String[] configKeys) {
        List<String> keys = Lists.newArrayList(configKeys);
        User user = new User(tenantId, userId, "", "");
        Map<String, String> queryRst = configService.queryTenantConfigs(user, keys);
        keys.forEach(x -> {
            if (!queryRst.containsKey(x)) {
                queryRst.put(x, ConfigType.getConfigType(x).getDefaultValue());
            }
        });
        return queryRst;

    }

    public static boolean mustHaveDetail(String tenantId) {
        boolean needDetail = false;
        String configValue = getConfigValue(tenantId, "16", User.SUPPER_ADMIN_USER_ID);
        if (Strings.isNullOrEmpty(configValue)) {
            configValue = "1,0,0";
        }
        String[] strArray = configValue.split(",", 3);

        if (strArray.length > 0) {
            if (Objects.equals(strArray[0], "1")) {
                needDetail = true;
            }

            if (strArray.length > 2) {
                if (Objects.equals(strArray[2], "1")) {
                    needDetail = true;
                }
            }
        }

        return needDetail;
    }

    public static boolean isSoftWorkflow(String tenantId, String apiName) {
        String configKey;
        if (Objects.equals(apiName, Utils.SALES_ORDER_API_NAME)) {
            configKey = "21";
        } else if (Objects.equals(apiName, Utils.RETURN_GOODS_INVOICE_API_NAME)) {
            configKey = "22";
        } else {
            return false;
        }
        String configValue = getConfigValue(tenantId, configKey, User.SUPPER_ADMIN_USER_ID);
        return !Objects.equals(configValue, "0");
    }

//    public static boolean isPriceBookEnabled(String tenantId) {
//        String configValue = getConfigValue(tenantId, "28", User.SUPPER_ADMIN_USER_ID);
//        return Objects.equals(configValue, "1");
//    }

    public static boolean isNewOpportunityEnabled(String tenantId) {
        String configValue = getConfigValue(tenantId, "config_newopportunity_open", User.SUPPER_ADMIN_USER_ID);
        return Objects.equals(configValue, "open");
    }

    public static boolean isDetailRepeatable(String tenantId) {
        String configValue = getConfigValue(tenantId, "43", User.SUPPER_ADMIN_USER_ID);
        return Objects.equals(configValue, "1");
    }

    public static boolean isCustomerAccountEnabled(String tenantId) {
        String configValue = getConfigValue(tenantId, "29", User.SUPPER_ADMIN_USER_ID);
        return Objects.equals(configValue, "1");
    }

    public static boolean isDeliveryNoteEnabled(String tenantId) {
        String configValue = getConfigValue(tenantId, "33", User.SUPPER_ADMIN_USER_ID);
        return Objects.equals(configValue, "1");
    }

    public static boolean isStockEnabled(String tenantId) {
        String configValue = getConfigValue(tenantId, "30", User.SUPPER_ADMIN_USER_ID);
        return Objects.equals(configValue, "1");
    }

    public static boolean isPromotionEnabled(String tenantId) {
        String configValue = getConfigValue(tenantId, "31", User.SUPPER_ADMIN_USER_ID);
        return Objects.equals(configValue, "1");
    }

    public static boolean isAllowNoRightSeeCompleteCustomerName(String tenantId, String userId) {
        //String configValue = getConfigValue(tenantId, "7", User.SUPPER_ADMIN_USER_ID);
        String configValue = getConfigValue(tenantId, "7", userId);
        return Objects.equals(configValue, "1");
    }

    public static boolean isPartnerOpen(String tenantId) {
        String configValue = getConfigValue(tenantId, "config_partner_open", User.SUPPER_ADMIN_USER_ID);
        return Objects.equals(configValue, "open");
    }

    public static boolean isMultiUnitOpen(String tenantId) {
        String configValue = getConfigValue(tenantId, "multiple_unit", User.SUPPER_ADMIN_USER_ID);
        return Objects.equals(configValue, "1");
    }

    public static boolean isCPQ(String tenantId) {
        String configValue = getConfigValue(tenantId, "cpq",User.SUPPER_ADMIN_USER_ID);
        return Objects.equals(configValue, "1");
    }

    public static boolean isTieredPrice(String tenantId){
        String configValue = getConfigValue(tenantId, "cpq_tieredprice",User.SUPPER_ADMIN_USER_ID);
        return Objects.equals(configValue, "1");
    }

    public static String getDefaultConfigValue(String key) {
        if (ConfigType.getConfigType(key).equals(ConfigType.None)) {
            return "";
        } else {
            return ConfigType.getConfigType(key).getDefaultValue();
        }
    }

    public static boolean isSpuOpen(String tenantId) {
        String configValue = getConfigValue(tenantId, ConfigType.SPU.getKey(), User.SUPPER_ADMIN_USER_ID);
        return "1".equals(configValue);
    }

    public static boolean isAvailableRangeEnabled(String tenantId) {
        String configValue = getConfigValue(tenantId, ConfigType.AVAILABLE_RANGE.getKey(), User.SUPPER_ADMIN_USER_ID);
        return "1".equals(configValue);
    }

    public static boolean isCPQOpen(String tenantId) {
        return StringUtils.equals("1", getConfigValue(tenantId, ConfigType.CPQ.getKey(), User.SUPPER_ADMIN_USER_ID)) ? true : false;
    }
}
