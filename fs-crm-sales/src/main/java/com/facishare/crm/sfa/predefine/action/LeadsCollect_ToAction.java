package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.LeadsDuplicatedProcessingService;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @IgnoreI18nFile
 */
public class LeadsCollect_ToAction extends AbstractStandardAction<LeadsCollect_ToAction.Arg, LeadsCollect_ToAction.Result> {

    LeadsDuplicatedProcessingService processingService = SpringUtil.getContext().getBean(LeadsDuplicatedProcessingService.class);
    List<IObjectData> leadsDataList; // 被归集的线索列表
    IObjectData collectedToLeads ; //归集到的线索
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.COLLECT_TO.getActionCode());
    }

    @Override
    protected List<String> getDataPrivilegeIds(LeadsCollect_ToAction.Arg arg) {
        return arg.getLeadsIdList();
    }

    @Override
    protected void before(LeadsCollect_ToAction.Arg arg) {
        super.before(arg);
        List<String> leadsIdList = arg.getLeadsIdList();
        if (CollectionUtils.empty(leadsIdList)) {
            throw new ValidateException("参数错误");
        }
        String collectedLeadsId = arg.getCollectedLeadsId();
        if (StringUtils.isEmpty(collectedLeadsId)) {
            throw new ValidateException("参数错误");
        }

    }

    @Override
    protected LeadsCollect_ToAction.Result doAct(LeadsCollect_ToAction.Arg arg) {
        List<String> leadsIdList = arg.getLeadsIdList();
        String collectedLeadsId = arg.getCollectedLeadsId();
        List<String> list = Lists.newArrayList();
        list.addAll(leadsIdList);
        list.add(collectedLeadsId);
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), list, SFAPreDefineObject.Leads.getApiName());
        leadsDataList = objectDataList.stream().filter(d -> !collectedLeadsId.equals(d.getId())).collect(Collectors.toList());
        Optional<IObjectData> optional = objectDataList.stream().filter(d -> collectedLeadsId.equals(d.getId())).findFirst();
        if (optional.isPresent()) {
            collectedToLeads = optional.get();
        }
        if (collectedToLeads != null && CollectionUtils.notEmpty(leadsDataList)) {
            processingService.collect(actionContext.getUser(), leadsDataList, collectedToLeads,false);
        }
        return new LeadsCollect_ToAction.Result();
    }

    @Override
    protected  LeadsCollect_ToAction.Result after(LeadsCollect_ToAction.Arg arg,LeadsCollect_ToAction.Result result)
    {
        result = super.after(arg,result);


        return result;
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.COLLECT_TO.getButtonApiName();
    }

    @Override
    protected IObjectData getPreObjectData() {
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIdsIncludeDeleted(actionContext.getUser(), Lists.newArrayList(arg.getCollectedLeadsId()), actionContext.getObjectApiName());
        if (objectDataList != null && objectDataList.size() != 0) {
            return objectDataList.get(0);
        }
        return null;
    }

    @Override
    protected IObjectData getPostObjectData() {
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIdsIncludeDeleted(actionContext.getUser(), Lists.newArrayList(arg.getCollectedLeadsId()), actionContext.getObjectApiName());
        if (objectDataList != null && objectDataList.size() != 0) {
            return objectDataList.get(0);
        }
        return null;
    }

    @Data
    @NoArgsConstructor
    static class Arg {
        List<String> leadsIdList;
        String collectedLeadsId;
    }

    @Data
    @Builder
    @NoArgsConstructor
    public static class Result {
    }
}
