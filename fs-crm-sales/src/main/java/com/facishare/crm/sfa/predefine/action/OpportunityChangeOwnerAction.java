package com.facishare.crm.sfa.predefine.action;

import com.facishare.paas.appframework.core.predef.action.StandardChangeOwnerAction;
import lombok.extern.slf4j.Slf4j;

/**
 * 合同更换负责人 class
 *
 * <AUTHOR>
 * @date 2019/1/14
 */
@Slf4j
public class OpportunityChangeOwnerAction extends StandardChangeOwnerAction {
    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        //todo 日志记录
        //todo 汇聚发Queue
        return result;
    }
}
