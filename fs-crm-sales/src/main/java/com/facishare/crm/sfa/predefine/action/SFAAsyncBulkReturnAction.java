package com.facishare.crm.sfa.predefine.action;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAsyncBulkAction;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by yuanjl on 2018/7/18.
 */
public class SFAAsyncBulkReturnAction extends AbstractStandardAsyncBulkAction<BaseSFAReturnAction.Arg, BaseSFAReturnAction.Arg> {

    @Override
    protected String getDataIdByParam(BaseSFAReturnAction.Arg param) {
        return param.getObjectIDs().get(0);
    }

    @Override
    protected List<BaseSFAReturnAction.Arg> getButtonParams() {
        return arg.getObjectIDs().stream()
                .map(data -> {
                    BaseSFAReturnAction.Arg returnArg = new BaseSFAReturnAction.Arg();
                    returnArg.setObjectIDs(Lists.newArrayList(data));
                    returnArg.setObjectPoolId(arg.getObjectPoolId());
                    returnArg.setSkipTriggerApprovalFlow(arg.isSkipTriggerApprovalFlow());
                    returnArg.setSkipFunctionCheck(arg.isSkipFunctionCheck());
                    returnArg.setOperationType(arg.getOperationType());
                    returnArg.setBackReason(arg.getBackReason());
                    returnArg.setBackReason__o(arg.getBackReason__o());
                    returnArg.setArgs(arg.getArgs());
                    return returnArg;
                })
                .collect(Collectors.toList());
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.RETURN.getButtonApiName();
    }

    @Override
    protected String getActionCode() {
        return ObjectAction.RETURN.getActionCode();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.RETURN.getActionCode());
    }
}