package com.facishare.crm.sfa.predefine.service.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


public interface PromotionsRestModel {
    @Data
    @Builder
    class PromotionRestEntity {
        @JSONField(name = "promotion")
        @JsonProperty("promotion")
        private ObjectDataDocument promotion;
        @JSONField(name = "promotionProducts")
        @JsonProperty("promotionProducts")
        private List<ObjectDataDocument> promotionProducts;
        @JSONField(name = "promotionRules")
        @JsonProperty("promotionRules")
        private List<ObjectDataDocument> promotionRules;
        @JSONField(name = "promotionGifts")
        @JsonProperty("promotionGifts")
        private List<ObjectDataDocument> promotionGifts;
    }
}
