package com.facishare.crm.sfa.predefine.service;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.model.*;
import com.facishare.crm.sfa.utilities.proxy.PromotionProxy;
import com.facishare.crm.sfa.utilities.proxy.model.*;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.sfa.utilities.util.PromotionUtil;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.util.SFAHeaderUtil;
import com.facishare.paas.metadata.api.INameCache;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@ServiceModule("promotion_sfa")
@Service
@Slf4j
public class PromotionService {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private PromotionProxy promotionProxy;

    /**
     * 封装促销接口给终端
     */
    @ServiceMethod("get_promotions_by_accountId")
    public GetPromotionsModel.Result getPromotionsByAccountId(GetPromotionsModel.Arg arg, ServiceContext context) {
        Map<String, String> headers = SFAHeaderUtil.getHeaders(context.getTenantId(),
                context.getUser().getUserId());
        String accountId = arg.getAccountId();
        GetPromotionByAccountIdModel.Arg proxyArg = GetPromotionByAccountIdModel.Arg.builder().customerId(accountId).build();
        GetPromotionByAccountIdModel.Result response = promotionProxy.getPromotionsByAccountId(proxyArg, headers);
        List<PromotionsRestModel.PromotionRestEntity> promotions = new ArrayList<>();
        if (response != null&&response.getResult()!=null) {
            SortPromotionList(response.getResult().getPromotions());
            HandGiftName(context.getUser(), response.getResult().getPromotions());
            promotions = PromotionUtil.convertPromotionEntityList(response.getResult().getPromotions());
        }
        GetPromotionsModel.Result result = GetPromotionsModel.Result.builder().promotions(promotions).build();
        return result;
    }

    @ServiceMethod("get_promotions_by_sales_order_product_ids")
    public GetPromotionsBySalesOrderProductIdsRest.Result getPromotionsBySalesOrderProductIds(GetPromotionsBySalesOrderProductIdsRest.Arg arg, ServiceContext context) {
        Map<String, String> headers = SFAHeaderUtil.getHeaders(context.getTenantId(),
                context.getUser().getUserId());
        String accountId = arg.getAccountId();
        List<String> productIds = arg.getProduct_ids();

        GetPromotionByAccountIdAndProductIdsModel.Arg proxyArg1 = GetPromotionByAccountIdAndProductIdsModel.Arg.builder()
                .customerId(accountId)
                .productIds(productIds)
                .build();
        GetPromotionByAccountIdAndProductIdsModel.Result response = promotionProxy.getPromotionsByAccountIdAndProductIds(proxyArg1, headers);
        List<GetPromotionsBySalesOrderProductIdsRest.DetailResult> promotions = new ArrayList<>();
        for (String productId : productIds) {
            List<PromotionsRestModel.PromotionRestEntity> promotionEntityList = new ArrayList<>();

            if (response != null && response.getResult() != null && response.getResult().getPromotions() != null) {
                for (GetPromotionByAccountIdAndProductIdsModel.DetailResult map : response.getResult().getPromotions()) {
                    if (map.getProductId().equals(productId)) {
                        SortPromotionList(map.getPromotions());
                        HandGiftName(context.getUser(), map.getPromotions());
                        promotionEntityList = PromotionUtil.convertPromotionEntityList(map.getPromotions());
                        break;
                    }
                }
            }

            promotions.add(GetPromotionsBySalesOrderProductIdsRest.DetailResult.builder()
                    .product_id(productId)
                    .promotions(promotionEntityList)
                    .build());
        }

        GetPromotionsBySalesOrderProductIdsRest.Result result = GetPromotionsBySalesOrderProductIdsRest.Result.builder()
                .promotions(promotions)
                .build();
        return result;
    }

    /**
     * 封装订单提交促销校验给终端
     */
    @ServiceMethod("check_promotions_for_sales_order")
    public ValidatePromotionsForSalesOrder.Result checkPromotionsForSalesOrder(ValidatePromotionsForSalesOrder.Arg arg, ServiceContext context) {
        ValidatePromotionsForSalesOrder.Result result = checkPromotionsForSalesOrder(context.getTenantId(), context.getUser()
                , arg.getObjectData(), arg.getDetails());
        return result;
    }
    @ServiceMethod("is_promotion_open")
    public IsPromotionOpenModel.Result isPromotionOpen(ServiceContext context) {
        if (GrayUtil.isGrayOrderPromotion(context.getTenantId())) {
            if (PromotionUtil.getIsPromotionEnable(context.getUser(), context.getClientInfo())) {
                return IsPromotionOpenModel.Result.builder().enable(true).build();
            }
        }
        return IsPromotionOpenModel.Result.builder().enable(false).build();
    }
    public ValidatePromotionsForSalesOrder.Result checkPromotionsForSalesOrder(String tenantId,User user,ObjectDataDocument objectData, List<ObjectDataDocument> details) {
        ValidatePromotionsForSalesOrder.Result result = null;
        Map<String, String> headers = SFAHeaderUtil.getHeaders(tenantId, user.getUserId());
        ValidatePromotionsForSalesOrderModel.Arg proxyArg = ValidatePromotionsForSalesOrderModel.Arg.builder()
                .objectData(objectData)
                .details(details)
                .build();
        ValidatePromotionsForSalesOrderModel.Result response = promotionProxy.validatePromotionsForSalesOrder(proxyArg, headers);
        log.warn("validatePromotionsForSalesOrder,arg:{},result:{}", new Object[]{JsonUtil.toJsonWithNull(proxyArg), JsonUtil.toJsonWithNull(response)});
        List<ValidatePromotionsForSalesOrder.PromotionProductQuota> productQuotas = new ArrayList<>();
        List<String> invalidPromotionIds= new ArrayList<>();
        List<PromotionsRestModel.PromotionRestEntity> variablePromotions = new ArrayList<>();
        if (response != null && response.getResult() != null) {
            if (response.getResult().getProductQuotas() != null) {
                for (ValidatePromotionsForSalesOrderModel.PromotionProductQuota data : response.getResult().getProductQuotas()) {
                    ValidatePromotionsForSalesOrder.PromotionProductQuota ppq = ValidatePromotionsForSalesOrder.PromotionProductQuota.builder()
                            .availableAmount(data.getAvailableAmount())
                            .availableQuantity(data.getAvailableQuantity())
                            .condition(data.getCondition())
                            .productId(data.getProductId())
                            .promotionId(data.getPromotionId())
                            .build();
                    productQuotas.add(ppq);
                }
            }
            if (response.getResult().getVariablePromotionIds() != null) {
                GetPromotionByIdsModel.Arg proxyArg1 = GetPromotionByIdsModel.Arg.builder()
                        .ids(response.getResult().getVariablePromotionIds())
                        .deleteInvalidFlag(true)
                        .build();
                GetPromotionByIdsModel.Result responsePromotions = promotionProxy.getPromotionByIds(proxyArg1, headers);
                if (responsePromotions != null && responsePromotions.getResult() != null) {
                    HandGiftName(user, responsePromotions.getResult());
                    variablePromotions = PromotionUtil.convertPromotionEntityList(responsePromotions.getResult());
                }
            }
            if (response.getResult().getInvalidPromotionIds() != null) {
                invalidPromotionIds = response.getResult().getInvalidPromotionIds();
            }
        } else {
            throw new ValidateException(response.getErrMessage());
        }
        result = ValidatePromotionsForSalesOrder.Result.builder()
                .status(response.getResult().getStatus())
                .invalidPromotionIds(invalidPromotionIds)
                .variablePromotions(variablePromotions)
                .productQuotas(productQuotas)
                .build();
        return result;
    }

    public List<GetPromotionProductsByAccountIdModel.WrapProductPromotion> getProductPromotionList(String tenantId,User user,String accountId) {
        //叠加促销信息
        List<GetPromotionProductsByAccountIdModel.WrapProductPromotion> productPromotionList = null;
        Map<String, String> headers = SFAHeaderUtil.getHeaders(tenantId,user.getUserId());
        GetPromotionProductsByAccountIdModel.Arg proxyArg = GetPromotionProductsByAccountIdModel.Arg.builder().customerId(accountId).build();
        GetPromotionProductsByAccountIdModel.Result result = promotionProxy.getPromotionProductsByAccountId(proxyArg, headers);
        productPromotionList = result.getResult().getPromotions();
        return productPromotionList;
    }

    public void handPromotionQuery(List<GetPromotionProductsByAccountIdModel.WrapProductPromotion> productPromotionList,SearchTemplateQuery query,String fieldName) {
        //叠加促销信息
        if (productPromotionList != null) {
            List<String> productIds = new ArrayList<>();
            productPromotionList.forEach(o -> {
                String productId = o.getProductId();
                if (!productIds.contains(productId)) {
                    productIds.add(productId);
                }
            });
            IFilter filter = new Filter();
            filter.setFieldName(fieldName);
            filter.setOperator(Operator.IN);
            filter.setFieldValues(productIds);
            query.setFilters(Lists.newArrayList(filter));
        }
    }
    /**
     * 根据产品id叠加数据促销的信息
     */
    public void fillPromotionInfo(User user,List<ObjectDataDocument> resultList, List<GetPromotionProductsByAccountIdModel.WrapProductPromotion> productPromotionList) {
        for (ObjectDataDocument data : resultList) {
            String productId = "";
            if (data.get("object_describe_api_name").toString().equals(Utils.PRICE_BOOK_PRODUCT_API_NAME)) {
                productId = data.get("product_id").toString();
            } else if (data.get("object_describe_api_name").toString().equals(Utils.PRODUCT_API_NAME)) {
                productId = data.getId();
            } else {
                continue;
            }
            String finalProductId = productId;
            Optional<GetPromotionProductsByAccountIdModel.WrapProductPromotion> promotionData = productPromotionList.stream().filter(r -> r.getProductId().equals(finalProductId)).findFirst();
            if (!promotionData.isPresent()) {
                continue;
            }
            SortPromotionList(promotionData.get().getPromotions());
            HandGiftName(user, promotionData.get().getPromotions());
            data.put("promotion", promotionData.get().getPromotions());
        }
    }
    private void HandGiftName(User user,List<PromotionModel.PromotionEntity> promotionList)
    {
        if (promotionList != null && !promotionList.isEmpty()) {
            List<String> giftIds = new ArrayList<>();
            for (PromotionModel.PromotionEntity promotion : promotionList) {
                /*if (promotion.getPromotionGifts()!=null) {
                    for (ObjectDataDocument gift : promotion.getPromotionGifts()) {
                        if (gift != null && gift.get("gift_product_id") != null) {
                            String giftId = gift.get("gift_product_id").toString();
                            if (!giftIds.contains(giftId) && !giftId.isEmpty()) {
                                giftIds.add(giftId);
                            }
                        }
                    }
                }*/
                if (promotion.getPromotionProducts()!=null) {
                    for (ObjectDataDocument product : promotion.getPromotionProducts()) {
                        if (product != null && product.get("gift_product_id") != null) {
                            String giftId = product.get("gift_product_id").toString();
                            if (!giftIds.contains(giftId) && !giftId.isEmpty()) {
                                giftIds.add(giftId);
                            }
                        }
                    }
                }
            }
            if (giftIds != null && !giftIds.isEmpty()) {
                //List<IObjectData> productList = serviceFacade.findObjectDataByIds(tenantId, giftIds, Utils.PRODUCT_API_NAME);
                List<INameCache> productNameCache = serviceFacade.findRecordName(ActionContextExt.of(user).getContext(), Utils.PRODUCT_API_NAME, giftIds);
                if (productNameCache == null) {
                    return;
                }
                for (PromotionModel.PromotionEntity promotion : promotionList) {
                    if (promotion != null) {
                        //HandGiftNameInner(promotion.getPromotionGifts(), productNameCache);
                        HandGiftNameInner(promotion.getPromotionProducts(), productNameCache);
                    }
                }
            }
        }
    }
    private void HandGiftNameInner(List<ObjectDataDocument> giftList,List<INameCache> productNameCache)
    {
        if (giftList != null) {
            for (ObjectDataDocument gift : giftList) {
                if (gift != null && gift.get("gift_product_id") != null) {
                    String giftId = gift.get("gift_product_id").toString();
                    for (INameCache product : productNameCache) {
                        if (product != null && product.getId().equals(giftId)) {
                            gift.toObjectData().set("gift_product_id__r", product.getName());
                        }
                    }
                }
            }
        }
    }
    public void SortPromotionList(List<PromotionModel.PromotionEntity> promotionList)
    {
        if (promotionList != null && !promotionList.isEmpty()) {
            Collections.sort(promotionList, new Comparator<PromotionModel.PromotionEntity>() {
                @Override
                public int compare(PromotionModel.PromotionEntity o1, PromotionModel.PromotionEntity o2) {
                    return o2.getPromotion().get("start_time").toString().compareTo(o1.getPromotion().get("start_time").toString());
                }
            });
        }
    }
}
