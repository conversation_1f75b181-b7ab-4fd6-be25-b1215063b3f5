package com.facishare.crm.sfa.utilities.util;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.ServiceFacadeImpl;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;

@Slf4j
public class ContractUtil {

    private static final ServiceFacade SERVICE_FACADE = SpringUtil.getContext().getBean(ServiceFacadeImpl.class);

    /**
     * 合同部分字段特殊赋值
     *
     * @param validList
     */
    public static void handleDefaultValueFields(List<IObjectData> validList) {
        if (CollectionUtils.empty(validList)) {
            return;
        }
        validList.forEach(x -> {
            ContractUtil.handleConfirmTimeField(ObjectDataDocument.of(x));
        });
    }

    /**
     * 处理合同confirm_time字段
     *
     * @param objectData
     */
    public static void handleConfirmTimeField(ObjectDataDocument objectData) {
        if (!objectData.isEmpty()) {
            objectData.put("confirm_time", System.currentTimeMillis());
        }
    }

    /**
     * 更新合同confirm_time字段
     *
     * @param user
     * @param objectData
     */
    public static void updateContractConfirmTime(User user, List<IObjectData> objectData) {
        if (CollectionUtils.empty(objectData)) {
            log.warn("ContractUtil->updateContractConfirmTime  info:{}", "合同信息为空");
            return;
        }

        objectData.forEach(x -> {
            ContractUtil.handleConfirmTimeField(ObjectDataDocument.of(x));
        });
        if (CollectionUtils.notEmpty(objectData)) {
            List<String> updateField = Arrays.asList("confirm_time");
            SERVICE_FACADE.batchUpdateByFields(user, objectData, updateField);
        }
    }
}
