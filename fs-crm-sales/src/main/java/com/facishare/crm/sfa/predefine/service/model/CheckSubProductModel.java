package com.facishare.crm.sfa.predefine.service.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

public interface CheckSubProductModel {
    @Data
    class Arg {
        //根据xx明细的主对象聚合
        private List<DataToValidate> dataArrayToValidate;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class Result {
        //入参的id,错误的信息
        private Map<String,String> result;
    }

    @Data
    class DataToValidate{
        private String masterId; //xx明细主对象id
        private String looupProductId; //xx明细lookup产品的id
        private Double subProductMaxAmount;//子产品明细的最大数量
        private Double subProductMinAmount;//子产品明细的最小数量
        private Double subProductInitialAmount;//子产品明细的初始数量
        private Double increment;//子产品明细数量的增加幅度
        private Double subProductInitialPrice;//子产品明细的初始化价格
        private Double productPackagePrice; //产品组合的价格
        private Double detailAmount;//xx明细的数量
        private Double detailPrice;//xx明细的价格
        private Boolean productIsPackage;//子产品明细lookup的产品是否是产品组合
        private String subProductMasterId;//子产品明细的主对象的id
    }
}
