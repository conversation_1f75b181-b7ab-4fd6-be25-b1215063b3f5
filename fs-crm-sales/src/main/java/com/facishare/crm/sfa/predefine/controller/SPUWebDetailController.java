package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.real.MultiUnitService;
import com.facishare.crm.sfa.predefine.service.real.MultiUnitServiceImpl;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/6/22 2:11 下午
 * @illustration
 */
public class SPUWebDetailController extends SFAWebDetailController {

    private final MultiUnitService multiUnitService = SpringUtil.getContext().getBean(MultiUnitServiceImpl.class);

    List<String> filterButtonForMobile = Lists.newArrayList(ObjectAction.CREATE.getActionCode(), ObjectAction.UPDATE.getActionCode(), ObjectAction.CLONE.getActionCode(), ObjectAction.INVALID.getActionCode(), ObjectAction.CHANGE_OWNER.getActionCode());


    @Override
    protected void before(Arg arg) {
        multiUnitService.validateMobileClientVersion(controllerContext.getRequestContext());
        super.before(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        ILayout layout = newResult.getLayout().toLayout();
        // 多单位页签
        layout = handleMultiUnitRelatedComponent(layout);

        // 移除新建编辑按钮
        WebDetailLayout.of(layout).removeButtonsByActionCode(Lists.newArrayList(ObjectAction.CREATE.getActionCode(), ObjectAction.UPDATE.getActionCode(), ObjectAction.CLONE.getActionCode()));
        removeButtons4MobileOrH5(layout, filterButtonForMobile);

        // 是web请求且是有规格的数据才下发是否有'规格值' 新建的权限
        if (RequestUtil.isWebRequest() && Objects.equals(Boolean.TRUE, data.get("is_spec", Boolean.class))) {
            ObjectDataDocument data = result.getData();
            boolean hasSpecValueCreatePrivilege = serviceFacade.funPrivilegeCheck(controllerContext.getUser(), Utils.SPECIFICATION_VALUE_API_NAME, ObjectAction.CREATE.getActionCode());
            data.put("hasSpecValueCreatePrivilege", hasSpecValueCreatePrivilege);

        }
        newResult.setLayout(LayoutDocument.of(layout));
        return newResult;
    }
}
