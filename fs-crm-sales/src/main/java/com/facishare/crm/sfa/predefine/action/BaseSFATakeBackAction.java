package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.model.SFAObjectPoolCommon;
import com.facishare.crm.sfa.predefine.enums.ActionCodeEnum;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * Created by yuanjl on 2019/1/4
 */
@Slf4j
public abstract class BaseSFATakeBackAction extends BaseObjectPoolAction<SFAObjectPoolCommon.Arg, SFAObjectPoolCommon.Result> {
    @Override
    protected List<String> getDataPrivilegeIds(SFAObjectPoolCommon.Arg arg) {
        return arg.getObjectIDs();
    }

    @Override
    protected ObjectAction getObjectAction() {
        return ObjectAction.TAKE_BACK;
    }

    @Override
    protected String getUserId(SFAObjectPoolCommon.Arg arg) {
        return actionContext.getUser().getUserId();
    }

    @Override
    protected String getObjectPoolId(SFAObjectPoolCommon.Arg arg) {
        return arg.getObjectPoolId();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList("TakeBack");
    }

    protected List<String> failedList;

    @Override
    protected void doFunPrivilegeCheck() {
        if (arg.isSkipFunctionCheck()) {
            return;
        }
        super.doFunPrivilegeCheck();
    }

    @Override
    protected void doDataPrivilegeCheck() {
        if (arg.isSkipFunctionCheck()) {
            return;
        }
        super.doDataPrivilegeCheck();
    }

    @Override
    protected boolean needTriggerApprovalFlow() {
        return false;
    }

    @Override
    protected void before(SFAObjectPoolCommon.Arg arg) {
        super.before(arg);
    }

    @Override
    protected SFAObjectPoolCommon.Result doAct(SFAObjectPoolCommon.Arg arg) {
        SFAObjectPoolCommon.Result result = objectPoolService.takeBack(actionContext.getObjectApiName(), actionContext.getUser(), arg.getObjectPoolId(), arg.getObjectIDs(),
                actionContext.getEventId(), isPrmOpen(actionContext.getTenantId()));

        return result;
    }

    @Override
    protected SFAObjectPoolCommon.Result after(SFAObjectPoolCommon.Arg arg, SFAObjectPoolCommon.Result result) {
        super.after(arg, result);
        addLog();

        // 发送重算到期时间的task
        if (CollectionUtils.isNotEmpty(arg.getObjectIDs())) {
            for (String s : arg.getObjectIDs()) {
                recalculateTaskService.delete(actionContext.getTenantId(), s, actionContext.getObjectApiName());
                recalculateTaskService.send(actionContext.getTenantId(), s, arg.getObjectPoolId(), actionContext.getObjectApiName(), ActionCodeEnum.RETURN);
            }
        }
        return result;
    }

    protected void addLog() {
        logAsync(dataList, EventType.MODIFY, ActionType.TakeBack);
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.TAKE_BACK.getButtonApiName();
    }

    @Override
    protected IObjectData getPreObjectData() {
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIdsIncludeDeleted(actionContext.getUser(), arg.getObjectIDs(), actionContext.getObjectApiName());
        if (objectDataList != null && objectDataList.size() != 0) {
            return objectDataList.get(0);
        }
        return null;
    }

    @Override
    protected IObjectData getPostObjectData() {
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIdsIncludeDeleted(actionContext.getUser(), arg.getObjectIDs(), actionContext.getObjectApiName());
        if (objectDataList != null && objectDataList.size() != 0) {
            return objectDataList.get(0);
        }
        return null;
    }
}