package com.facishare.crm.sfa.predefine.action.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import lombok.Data;

@Data
public class LeadsSaveArg extends BaseObjectSaveAction.Arg {
    @JSO<PERSON>ield(
            name = "M100"
    )
    DuplicatedProcessing duplicatedProcessing;
    @JSONField(
            name = "M101"
    )
    boolean skipCheckCleanOwner;
}


