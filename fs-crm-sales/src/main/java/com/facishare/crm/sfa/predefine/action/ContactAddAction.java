package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.LeadsObjTransferService;
import com.facishare.crm.sfa.predefine.service.push.ContactSessionSandwichService;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.crm.sfa.utilities.util.ContactUtil;
import com.facishare.crm.sfa.utilities.util.LogUtil;
import com.facishare.enterprise.common.model.paas.ObjectAction;
import com.facishare.idempotent.Idempotent;
import com.facishare.idempotent.Serializer;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2018/3/19 16:09
 */
@Slf4j
@Idempotent(serializer = Serializer.Type.java)
public class ContactAddAction extends StandardAddAction {
    ContactSessionSandwichService contactSessionSandwichService = SpringUtil.getContext()
            .getBean(ContactSessionSandwichService.class);
    LeadsObjTransferService leadsObjTransferService = SpringUtil.getContext().getBean(LeadsObjTransferService.class);

    boolean isFromLeadsTransfer = false;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        if(isFromLeadsTransfer) {
            return Lists.newArrayList("TransferAdd");
        } else {
            return super.getFuncPrivilegeCodes();
        }
    }

    @Override
    protected void before(Arg arg) {
        ObjectDataDocument objectData = arg.getObjectData();
        isFromLeadsTransfer = (boolean) objectData.getOrDefault("is_from_leads_transfer", false);
        //处理生日，nameorder，电话
        ContactUtil.handleContactFields(objectData);
        ContactUtil.handlePhoneFields(objectData);
        //处理联系人负责人变更时间
        ContactUtil.handleOwnerChangedTimeField(objectData);
        //获取手机归属地字段
        AccountUtil.getPhoneNumberInfo(arg.getObjectData(), "mobile1");
        super.before(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        //发送消息
        contactSessionSandwichService.push(actionContext.getTenantId(), result.getObjectData().toObjectData().getOwner());
        //更新 out_tenant_id，out_owner
        ContactUtil.updateContactOutInfo(actionContext.getUser(), Lists.newArrayList(result.getObjectData().toObjectData()));
        if (result.getObjectData().get("leads_id") != null && !isFromLeadsTransfer) {
            String leadsId = result.getObjectData().get("leads_id").toString();
            String contactId = result.getObjectData().getId();
            if (StringUtils.isNotEmpty(leadsId)) {
                //新建的一定是主线索
                leadsObjTransferService.saveLeadsTransferLog(actionContext.getUser(), SFAPreDefineObject.Contact.getApiName(), leadsId, contactId, true);
            }
        }
        return super.after(arg, result);
    }

    @Override
    protected void recordLog() {
        List<IObjectData> allObjectData = this.getAllObjectDataCopy();
        LogUtil.recordAddSpecailLog(actionContext.getUser(), allObjectData, objectData, objectDescribe);
        this.stopWatch.lap("recordLog");
    }
}
