package com.facishare.crm.sfa.predefine.service.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface FindAccountAddrList {
    @Data
    class Arg {
        @JSONField(name = "M1")
        @JsonProperty("searchQueryInfo")
        @SerializedName("searchQueryInfo")
        private String searchQueryInfo;

        @JSONField(name = "M2")
        @JsonProperty("searchTemplateId")
        @SerializedName("searchTemplateId")
        private String searchTemplateId;

        @JSONField(name = "M3")
        @JsonProperty("includeLayout")
        @SerializedName("includeLayout")
        private boolean includeLayout = false;
    }

    @Data
    @Builder
    class Result {
        @JSONField(name = "M1")
        private List<ObjectDataDocument> dataList;

        @JSONField(name = "M2")
        private Integer offset;

        @JSONField(name = "M3")
        private Integer limit;

        @JSONField(name = "M4")
        private Integer total;

        @JSONField(name = "M7")
        private ObjectDescribeDocument objectDescribe;

        @JSONField(name = "M6")
        private List<LayoutDocument> listLayouts;
    }
}
