package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.real.ProductService;
import com.facishare.paas.metadata.util.SpringUtil;

/**
 * <AUTHOR>
 * @date 2018/12/13 10:04
 */
public class SpecificationFullDetailController extends SpecificationDetailController {

    private final ProductService productService = SpringUtil.getContext().getBean(ProductService.class);

    @Override
    protected void before(Arg arg) {
        arg.setFromRecycleBin(true);
        super.before(arg);
    }

    @Override
    protected Result doService(Arg arg) {
        Result r = super.doService(arg);
        if (null != r && null != r.getData()) {
            r.setData(productService.fillWithDetails(controllerContext.getRequestContext(),
                    controllerContext.getObjectApiName(), r.getData()));
        }
        return r;
    }
}
