package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.constant.SalesOrderConstants;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/7/30 10:59 上午
 * @illustration
 * 添加历史订单产品时，会调用list接口，查子数据，导致复制历史产品清空的数据value，被覆盖了。
 * 该方法处理子数据的 remove value操作，只针对 添加历史订单产品选包的场景
 */
public class SalesOrderProductCpqListController extends SalesOrderProductListController {

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult =  super.after(arg, result);
        removeValue(newResult);
        return newResult;
    }

    private void removeValue(Result result){
        List<ObjectDataDocument> dataList = result.getDataList();
        dataList.forEach(o->{
            o.forEach((k,v)->{
                if(k.endsWith("__c")){
                    o.put(k, null);
                }
            });
            SalesOrderConstants.salesOrderProductResetNullToField.forEach(x->{
                o.put(x, null);
            });
        });
    }


}
