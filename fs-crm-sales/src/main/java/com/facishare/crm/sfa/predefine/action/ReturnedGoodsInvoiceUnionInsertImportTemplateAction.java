package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.service.ModuleCtrlConfigService;
import com.facishare.crm.sfa.predefine.service.model.ConfigCtrlModule;
import com.facishare.crm.sfa.predefine.service.modulectrl.IModuleInitService;
import com.facishare.crm.sfa.utilities.util.ReturnedGoodsInvoiceUtil;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardUnionInsertImportTemplateAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Objects;

import java.util.List;

/**
 * Created by renlb on 2019/5/11.
 */
public class ReturnedGoodsInvoiceUnionInsertImportTemplateAction extends StandardUnionInsertImportTemplateAction {

    private final ModuleCtrlConfigService moduleCtrlConfigService = SpringUtil.getContext().getBean(ModuleCtrlConfigService.class);

    @Override
    protected void customMasterHeader(List<IFieldDescribe> headerFieldList) {
        super.customMasterHeader(headerFieldList);
        ReturnedGoodsInvoiceUtil.removeUnSupportedMasterFields(headerFieldList, actionContext.getTenantId());
    }

    @Override
    protected void customDetailHeader(List<IFieldDescribe> headerFieldList) {
        super.customDetailHeader(headerFieldList);
        ReturnedGoodsInvoiceUtil.removeUnSupportedDetailFields(headerFieldList);
        // 开启了cqp
        if (moduleCtrlConfigService.openStatus(IModuleInitService.MODULE_CPQ,actionContext.getUser(),actionContext)) {
            headerFieldList.removeIf(field->ReturnedGoodsInvoiceUtil.parentKeys.contains(field.getApiName()));
        }
    }



}
