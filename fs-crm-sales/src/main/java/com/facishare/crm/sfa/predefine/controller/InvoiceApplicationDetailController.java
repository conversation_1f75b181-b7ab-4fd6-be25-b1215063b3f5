package com.facishare.crm.sfa.predefine.controller;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.predefine.service.real.invoice.InvoiceService;
import com.facishare.crm.sfa.utilities.constant.InvoiceApplicationConstants;
import com.facishare.paas.appframework.common.service.dto.UserInfo;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

@Slf4j
public class InvoiceApplicationDetailController extends SFADetailController {
    protected ObjectDataServiceImpl objectDataService = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);
    private InvoiceService invoiceService = SpringUtil.getContext().getBean(InvoiceService.class);

    // todo 销售订单和订单产品 和 产品字段都修改成只读

    @Override
    protected Result after(Arg arg, Result result) {
        Result rst = super.after(arg, result);
        if (result.getData() != null) {
            IObjectData dataRst = result.getData().toObjectData();
            if (dataRst.get("finance_employee_id") != null) {
                ArrayList<String> financeEmpId = (ArrayList<String>) dataRst.get("finance_employee_id");
                try {
                    String picAddr = getUserPicAddr(controllerContext.getTenantId(), financeEmpId.get(0));
                    String financeEmpId__r = dataRst.get("finance_employee_id__r") == null ? "" :
                            dataRst.get("finance_employee_id__r").toString();
                    if (financeEmpId__r != null && !financeEmpId__r.isEmpty()) {
                        UserInfo userInfo = JSONObject.parseObject(financeEmpId__r, UserInfo.class);
                        userInfo.setPicAddr(picAddr);
                        data.set("finance_employee_Id__r", userInfo);
                        rst.setData(ObjectDataDocument.of(data));
                    }
                } catch (MetadataServiceException e) {
                    log.error("finance_employee_id 对应user不存在,tenantId:{},dataId:{}", controllerContext.getTenantId(), arg.getObjectDataId(), e);
                }
            }
        }
        if (result.getLayout() == null) {
            return result;
        }
        ILayout layout = new Layout(result.getLayout());
        addSoftWorkflowButtons(controllerContext.getUser(), data, layout);

        // 销售订单和开票申请多对多的处理
        if (invoiceService.isNewInvoiceOpen(controllerContext.getUser())) {
            invoiceService.removeCloneButtonOnOpenInvoiceLine(controllerContext.getUser(), layout);
            addInvoiceAppComponent(layout);
        }

        return rst;
    }


    private void addInvoiceAppComponent(ILayout layout) {
        Optional<IComponent> componentOp = LayoutExt.of(layout).getComponentByApiName("relatedObject");
        if (componentOp.isPresent()) {
            IComponent component = componentOp.get();
            List childComponents = component.get("child_components", List.class);

            childComponents.add(InvoiceApplicationConstants.SALES_ORDER_INFO);
            component.set("child_components", childComponents);
        }
    }

    //todo 抽取方法
    private String getUserPicAddr(String tenantId, String userId) throws MetadataServiceException {
        List<Map> sqlRst = objectDataService.findBySql(controllerContext.getTenantId(),
                String.format("select pic_addr from org_user where tenant_id = \'%s\' and user_id = \'%s\'", tenantId, userId));
        if (sqlRst == null || sqlRst.size() == 0) {
            return "";
        } else {
            return sqlRst.get(0).get("pic_addr").toString();
        }

    }
}
