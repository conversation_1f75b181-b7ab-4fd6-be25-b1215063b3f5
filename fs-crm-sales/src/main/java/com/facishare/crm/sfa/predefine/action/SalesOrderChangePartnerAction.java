package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.service.PriceBookCommonService;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.crm.sfa.utilities.util.SOI18NKeyUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardChangePartnerAction;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class SalesOrderChangePartnerAction extends StandardChangePartnerAction {
//    PriceBookCommonService priceBookCommonService = SpringUtil.getContext().getBean(PriceBookCommonService.class);

//    @Override
//    protected void before(Arg arg) {
//        super.before(arg);
//        validatePriceBook();
//    }

//    private void validatePriceBook() {
//        if (bizConfigThreadLocalCacheService.isPriceBookEnabled(actionContext.getTenantId())) {
//            User user = this.getActionContext().getUser();
//            objectDataList.forEach(objectData -> {
//                String priceBookId = objectData.get("price_book_id", String.class);
//                if (StringUtils.isNotEmpty(priceBookId)) {
//                    Boolean flag = priceBookCommonService.validatePriceBook(user, priceBookId, objectData.get("account_id", String.class),
//                            partnerObjData.getId(), true);
//                    if (!flag) {
//                        throw new ValidateException(I18N.text(SOI18NKeyUtils.SO_PARTNER_NOTAPPLICABLEPARTNER, objectData.getName()));
//                    }
//                }
//            });
//        }
//    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        this.serviceFacade.sendActionMq(this.actionContext.getUser(), this.objectDataList, ObjectAction.CHANGE_PARTNER);
        return result;
    }
}
