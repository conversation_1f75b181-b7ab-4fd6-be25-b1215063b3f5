package com.facishare.crm.sfa.predefine.action.listener;

import com.facishare.crm.sfa.utilities.proxy.SalesOrderBizProxy;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.SalesOrderInterceptorModel;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.salesorderparam.BulkRecoverAfterModel;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.salesorderparam.BulkRecoverBeforeModel;
import com.facishare.crm.sfa.utilities.util.SoCommonUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.metadata.util.SpringUtil;
import org.springframework.stereotype.Component;

/**
 * Created by renlb on 2019/3/14.
 */
@Component
public class SalesOrderBulkRecoverActionListener extends StandardBulkRecoverActionListener {

    private static final SalesOrderBizProxy salesOrderBizProxy = SpringUtil.getContext().getBean(SalesOrderBizProxy.class);

    @Override
    protected void callBeforeInterceptor(BulkRecoverBeforeModel.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext("SalesOrderInterceptor",
                "BulkRecoverBefore");
//        salesOrderInterceptorService.bulkRecoverBefore(context, arg);
        SalesOrderInterceptorModel.BulkRecoverBeforeResult bulkRecoverBeforeResult = salesOrderBizProxy.bulkRecoverBefore(arg, SoCommonUtils.getCrmHeader(context.getTenantId(), context.getUser()));
        if(!bulkRecoverBeforeResult.isSuccess()){
            throw new ValidateException(bulkRecoverBeforeResult.getMessage());
        }
    }

    @Override
    protected void callAfterInterceptor(BulkRecoverAfterModel.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext("SalesOrderInterceptor",
                "BulkRecoverAfter");
//        salesOrderInterceptorService.bulkRecoverAfter(context, arg);
        SalesOrderInterceptorModel.BulkRecoverAfterResult bulkRecoverAfterResult = salesOrderBizProxy.bulkRecoverAfter(arg, SoCommonUtils.getCrmHeader(context.getTenantId(), context.getUser()));
        if(!bulkRecoverAfterResult.isSuccess()){
            throw new ValidateException(bulkRecoverAfterResult.getMessage());
        }
    }
}
