package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.metadata.ui.layout.IButton;

import java.util.List;

/**
 * <AUTHOR> 2019-11-11
 * @instruction
 */
public class TieredPriceBookListHeaderController extends StandardListHeaderController {
    @Override
    protected List<IButton> getButtons() {
        List<IButton> buttons = super.getButtons();
        if (RequestUtil.isMobileOrH5Request())
            buttons.removeIf(o -> "Add".equals(o.getAction()));
        return buttons;
    }
}
