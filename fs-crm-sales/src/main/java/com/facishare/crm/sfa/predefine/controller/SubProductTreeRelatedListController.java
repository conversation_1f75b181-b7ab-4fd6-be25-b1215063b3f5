//package com.facishare.crm.sfa.predefine.controller;
//
//import com.facishare.crm.openapi.Utils;
//import com.facishare.crm.sfa.predefine.service.CPQService;
//import com.facishare.crm.sfa.predefine.service.real.TransformData4ViewService;
//import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
//import com.facishare.crm.sfa.utilities.constant.PriceBookConstants;
//import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
//import com.facishare.paas.I18N;
//import com.facishare.paas.appframework.common.util.CollectionUtils;
//import com.facishare.paas.appframework.core.model.ObjectDataDocument;
//import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
//import com.facishare.paas.appframework.core.predef.controller.BaseListController;
//import com.facishare.paas.appframework.core.predef.controller.StandardTreeRelatedListController;
//import com.facishare.paas.appframework.core.util.RequestUtil;
//import com.facishare.paas.appframework.metadata.LayoutExt;
//import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
//import com.facishare.paas.metadata.api.IObjectData;
//import com.facishare.paas.metadata.api.QueryResult;
//import com.facishare.paas.metadata.api.describe.IFieldDescribe;
//import com.facishare.paas.metadata.api.describe.IObjectDescribe;
//import com.facishare.paas.metadata.api.search.IFilter;
//import com.facishare.paas.metadata.exception.MetadataServiceException;
//import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
//import com.facishare.paas.metadata.impl.ui.layout.FieldSection;
//import com.facishare.paas.metadata.impl.ui.layout.FormField;
//import com.facishare.paas.metadata.impl.ui.layout.TableColumn;
//import com.facishare.paas.metadata.impl.ui.layout.component.TableComponent;
//import com.facishare.paas.metadata.ui.layout.IComponent;
//import com.facishare.paas.metadata.ui.layout.IFormField;
//import com.facishare.paas.metadata.ui.layout.ILayout;
//import com.facishare.paas.metadata.ui.layout.ITableColumn;
//import com.facishare.paas.metadata.util.SpringUtil;
//import com.google.common.collect.Lists;
//import org.apache.commons.lang3.StringUtils;
//
//import java.io.*;
//import java.util.List;
//import java.util.Map;
//import java.util.Optional;
//import java.util.stream.Collectors;
//
//import static com.facishare.crm.sfa.utilities.util.SOI18NKeyUtils.SO_SUB_FIELD_MODIFIED_ADJUST_PRICE;
//
//public class SubProductTreeRelatedListController extends StandardTreeRelatedListController {
//
//    private TransformData4ViewService transformData4ViewService = SpringUtil.getContext().getBean(TransformData4ViewService.class);
//
//    @Override
//    protected SearchTemplateQuery buildSearchTemplateQuery(ObjectDescribeExt objectDescribeExt, String relatedListName, String searchQueryInfo) {
//        SearchTemplateQuery searchTemplateQuery = super.buildSearchTemplateQuery(objectDescribeExt, relatedListName, searchQueryInfo);
//        if (SearchTemplateQuery.fromJsonString(arg.getTargetChildSearchQueryInfo()).getPermissionType() == 0 && bizConfigThreadLocalCacheService.isPriceBookEnabled(controllerContext.getTenantId()))
//            searchTemplateQuery.setPermissionType(0);
//        return searchTemplateQuery;
//    }
//
//    @Override
//    protected ILayout getChildLayout() {
//        ILayout childLayout = super.getChildLayout();
//        if (RequestUtil.isMobileRequest()) {
//            LayoutExt subProductListLayout = LayoutExt.of(childLayout);
//            FieldSection fieldSection = subProductListLayout.getFormComponent().get().getFieldSection().get();
//            List<IFormField> fields = fieldSection.getFields();
//            fields.stream().filter(o -> !o.getFieldName().endsWith("__c") && !("amount".equals(o.getFieldName()) || "adjust_price".equals(o.getFieldName()))).forEach(o -> o.setReadOnly(true));
//
//            IFormField modifiedAdjustPriceFormField = new FormField();
//            modifiedAdjustPriceFormField.setReadOnly(false);
//            modifiedAdjustPriceFormField.setRequired(false);
//            modifiedAdjustPriceFormField.setRenderType("currency");
//            modifiedAdjustPriceFormField.setFieldName("modified_adjust_price");
//            int index = -1;
//            for (int i = 0; i < fields.size(); i++) {
//                IFormField field = fields.get(i);
//                if ("adjust_price".equals(field.getFieldName())) {
//                    index = i + 1;
//                }
//            }
//            if (index != -1) {
//                fields.add(index, modifiedAdjustPriceFormField);
//                fieldSection.setFields(fields);
//                try {
//                    List<IComponent> components = subProductListLayout.getComponents();
//                    components.removeIf(o -> "relevant_team_component".equals(o.getName()));
//                    childLayout.setComponents(components);
//                } catch (MetadataServiceException e) {
//
//                }
//            }
//        }
//        return childLayout;
//    }
//
//    @Override
//    protected List<ILayout> getDetailLayout() {
//        return Lists.newArrayList(this.getChildLayout());
//    }
//
//    @Override
//    protected List<ILayout> findMobileLayouts() {
//        List<ILayout> mobileLayouts = super.findMobileLayouts();
//        if (!Utils.SUBPRODUCT_API_NAME.equals(objectDescribe.getApiName())) {
//            return mobileLayouts;
//        }
//
//        if (RequestUtil.isMobileRequest()) {
//            ILayout iLayout = mobileLayouts.get(0);
//            LayoutExt subProductListLayout = LayoutExt.of(iLayout);
//            TableComponent tableComponent = subProductListLayout.getTableComponent().get();
//            List<ITableColumn> fields = tableComponent.getIncludeFields();
//            ITableColumn modifiedAdjustPriceITableColumn = new TableColumn();
//            modifiedAdjustPriceITableColumn.setName("modified_adjust_price");
//            modifiedAdjustPriceITableColumn.setRenderType("currency");
//            modifiedAdjustPriceITableColumn.setLabelName(I18N.text(SO_SUB_FIELD_MODIFIED_ADJUST_PRICE));
//            modifiedAdjustPriceITableColumn.set("api_name", "modified_adjust_price");
//            int index = -1;
//            for (int i = 0; i < fields.size(); i++) {
//                ITableColumn field = fields.get(i);
//                if ("adjust_price".equals(field.getName())) {
//                    index = i + 1;
//                }
//            }
//            if (index != -1) {
//                fields.add(index, modifiedAdjustPriceITableColumn);
//                tableComponent.setIncludeFields(fields);
//                try {
//                    iLayout.setComponents(subProductListLayout.getComponents());
//                } catch (MetadataServiceException e) {
//                    log.error("SubProductTreeRelatedListController findMobileLayouts tenantId:{}", controllerContext.getTenantId(), e);
//                }
//            }
//            mobileLayouts = Lists.newArrayList(iLayout);
//        }
//        return mobileLayouts;
//    }
//
//
//    @Override
//    protected Result buildResult(List<ILayout> childLayouts, List<ILayout> parentLayouts, QueryResult<IObjectData> queryChildResult, QueryResult<IObjectData> queryParentResult) {
//        Result result = super.buildResult(childLayouts, parentLayouts, queryChildResult, queryParentResult);
//        if (RequestUtil.isMobileRequest()) {
//            ObjectDescribeDocument objectDescribeDocument = result.getObjectDescribes().stream().filter(o -> Utils.SUBPRODUCT_API_NAME.equals(o.toObjectDescribe().getApiName())).findFirst().get();
//            IFieldDescribe adjustPrice = objectDescribeDocument.toObjectDescribe().getFieldDescribe("adjust_price");
//            IFieldDescribe modifiedAdjustPrice = deepCopyList(adjustPrice);
//            modifiedAdjustPrice.setApiName("modified_adjust_price");
//            modifiedAdjustPrice.setId("123");
//            modifiedAdjustPrice.setLabel(I18N.text(SO_SUB_FIELD_MODIFIED_ADJUST_PRICE));
//            objectDescribeDocument.toObjectDescribe().addFieldDescribe(modifiedAdjustPrice);
//        }
//        return result;
//    }
//
//    @Override
//    protected BaseListController.Result after(Arg arg, BaseListController.Result result) {
//        Result after = (Result) super.after(arg, result);
//        fillProductPkg(arg, after);
//        fillProduct(after);
//        if (CPQService.grayCPQTenantIds.contains(controllerContext.getTenantId())){
//            fillPriceBookProduct(arg, after);
//        }
//        return after;
//    }
//
//    private void fillProduct(Result after) {
//        ObjectData objectData = after.getDataMapList().stream().filter(o -> Utils.SUBPRODUCT_API_NAME.equals(o.getDescribeApiName())).findFirst().get();
//        if (CollectionUtils.notEmpty(objectData.getDataList())) {
//            List<String> skuIds = objectData.getDataList().stream().map(o -> o.get("lookup_product_id").toString()).collect(Collectors.toList());
//            List<IObjectData> objectDataByIds = serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), skuIds, Utils.PRODUCT_API_NAME);
//            IObjectDescribe skuDescribe = serviceFacade.findObject(controllerContext.getTenantId(), Utils.PRODUCT_API_NAME);
//            transformData4ViewService.batchTransformDataForView(controllerContext.getUser(), skuDescribe, objectDataByIds);
//            Map<String, IObjectData> skuIdToData = objectDataByIds.stream().collect(Collectors.toMap(o -> o.getId(), o -> o));
//            for (ObjectDataDocument subProduct : objectData.getDataList()) {
//                IObjectData lookupData = skuIdToData.get(subProduct.get("lookup_product_id").toString());
//                subProduct.put("lookup_product_id__ro", ObjectDataDocument.of(lookupData));
//            }
//        }
//
//    }
//
//    private void fillProductPkg(Arg arg, Result after) {
//        List<IObjectData> objectDataByIds = serviceFacade.findObjectDataByIds(getControllerContext().getTenantId(), Lists.newArrayList(arg.getSourceDataId()), Utils.PRODUCT_API_NAME);
//        ObjectData productPkg = ObjectData.builder().dataList(ObjectDataDocument.ofList(objectDataByIds)).describeApiName(Utils.PRODUCT_API_NAME).build();
//        after.getDataMapList().add((productPkg));
//    }
//
//    private <T> T deepCopyList(T src) {
//        T dest = null;
//        try {
//            ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
//            ObjectOutputStream out = new ObjectOutputStream(byteOut);
//            out.writeObject(src);
//            ByteArrayInputStream byteIn = new ByteArrayInputStream(byteOut.toByteArray());
//            ObjectInputStream in = new ObjectInputStream(byteIn);
//            dest = (T) in.readObject();
//        } catch (IOException | ClassNotFoundException ignored) {
//
//        }
//        return dest;
//    }
//
//    /**
//     * 给产品包和子产品明细，添加价目表明细的信息
//     */
//    private void fillPriceBookProduct(Arg arg, Result after) {
//        if (StringUtils.isBlank(arg.getPriceBookId())) return;
//        List<Object> skuIds = Lists.newArrayList(arg.getSourceDataId());
//        Optional<List<ObjectDataDocument>> subProductList = after.getDataMapList().stream().filter(o -> Utils.SUBPRODUCT_API_NAME.equals(o.getDescribeApiName())).map(ObjectData::getDataList).findFirst();
//        subProductList.ifPresent(o->{
//            o.forEach(oo->{
//                skuIds.add(oo.get("lookup_product_id"));
//            });
//        });
//        skuIds.remove(null);
//        if (CollectionUtils.empty(skuIds))return;
//        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
//        searchQuery.setLimit(1000);
//        List<IFilter> filters = searchQuery.getFilters();
//        SearchUtil.fillFilterIn(filters, "product_id", skuIds);
//        SearchUtil.fillFilterEq(filters, "is_deleted", 0);
//        SearchUtil.fillFilterEq(filters, "life_status", "normal");
//        SearchUtil.fillFilterEq(filters, "pricebook_id", arg.getPriceBookId());
//        QueryResult<IObjectData> result = serviceFacade.findBySearchQuery(controllerContext.getUser(), Utils.PRICE_BOOK_PRODUCT_API_NAME, searchQuery);
//        if (CollectionUtils.empty(result.getData())) return;
//        Map<String, ObjectDataDocument> skuIdToPriceBookProduct = result.getData().stream().collect(Collectors.toMap(o -> o.get(PriceBookConstants.ProductField.PRODUCTID.getApiName(),String.class), o -> ObjectDataDocument.of(o)));
//
//        //添加产品包对应的价目表明细
//        ObjectData productPkg = ObjectData.builder().dataList(Lists.newArrayList(ObjectDataDocument.of( skuIdToPriceBookProduct.get(arg.getSourceDataId()))))
//                .describeApiName(Utils.PRICE_BOOK_PRODUCT_API_NAME).build();
//        after.getDataMapList().add((productPkg));
//
//        subProductList.ifPresent(o->{
//            o.forEach(oo->oo.put("price_book_product__ro",skuIdToPriceBookProduct.get(oo.get("lookup_product_id"))));
//        });
//    }
//}
