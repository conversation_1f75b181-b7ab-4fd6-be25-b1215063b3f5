package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.model.MultiUnitData;
import com.facishare.crm.sfa.predefine.service.real.MultiUnitService;
import com.facishare.crm.sfa.predefine.service.real.MultiUnitServiceImpl;
import com.facishare.crm.sfa.predefine.service.real.ProductService;
import com.facishare.crm.sfa.predefine.service.real.SpuSkuService;
import com.facishare.crm.sfa.predefine.service.real.SpuSkuServiceImpl;
import com.facishare.crm.sfa.utilities.constant.ProductConstants;
import com.facishare.crm.sfa.utilities.proxy.StockProxy;
import com.facishare.crm.sfa.utilities.proxy.model.CheckIsAllowModifySpuBatchSNRestModel;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.crm.sfa.utilities.util.StringRegexEscapeUtils;
import com.facishare.crm.sfa.utilities.validator.ProductValidator;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ObjectLockStatus;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.license.LicenseServiceImpl;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.appframework.metadata.util.SFAHeaderUtil;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.SelectOne;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

import static com.facishare.crm.sfa.utilities.constant.SpuSkuConstants.SKU_RELATE_SPU_ID;

/**
 * Created by luxin on 2018/11/12.
 * @IgnoreI18nFile
 */
public class SPUEditAction extends StandardEditAction {

    private final SpuSkuService skuService = SpringUtil.getContext().getBean(SpuSkuServiceImpl.class);
    private final ProductService productService = SpringUtil.getContext().getBean(ProductService.class);
    private final MultiUnitService multiUnitService = SpringUtil.getContext().getBean(MultiUnitServiceImpl.class);
    private LicenseService licenseService=SpringUtil.getContext().getBean(LicenseServiceImpl.class);
    private final StockProxy stockProxy = SpringUtil.getContext().getBean(StockProxy.class);

    /**
     * 新增的sku数据
     */
    private final List<IObjectData> addSkuDataList = Lists.newArrayList();
    /**
     * 作废sku数据
     */
    private final List<IObjectData> invalidSkuDataList = Lists.newArrayList();
    /**
     * 被编辑的sku数据
     */
    private final List<IObjectData> editedSkuDataList = Lists.newArrayList();
    private List<MultiUnitData.MultiUnitItem> multiUnitData;

    @Override
    protected void before(Arg arg) {
        if (!SFAConfigUtil.isSpuOpen(actionContext.getTenantId())) {
            throw new ValidateException("商品对象已关闭，不允许更新商品数据");
        }
        if (!Objects.equals(actionContext.getPeerName(), "OpenAPI-V2.0")) {
            IObjectDescribe spuDescribe = serviceFacade.findObject(actionContext.getTenantId(), Utils.SPU_API_NAME);
            productService.modifyArg(spuDescribe, arg.getObjectData());
        }
        multiUnitData = multiUnitService.preprocessMultiUnit(arg.getObjectData());
        Set<String> module = licenseService.getModule(actionContext.getTenantId());
        if (multiUnitData != null &&module.contains("kx_peculiarity")&&multiUnitData.size() > 3) {
            throw new ValidateException("开启快销企业多单位数量超过上限3个");
        }
        if (multiUnitData != null && multiUnitData.size() > 20) {
            throw new ValidateException("多单位数量超过上限20个");
        }

        super.before(arg);
    }

    @Override
    protected void validate() {
        super.validate();

        // 开启了多单位,校验多单位数据
        if (isMultiUnit()) {
            multiUnitService.checkEditSpuMultiUnit(objectData, multiUnitData, (SelectOne) objectDescribe.getFieldDescribe("unit"));
        }
        batchSNFieldValidate();
    }

    @Override
    protected void startCreateWorkFlow() {
        // do nothing
    }

    @Override
    protected Result doAct(Arg arg) {
        if (!Objects.equals(actionContext.getPeerName(), "OpenAPI-V2.0")) {
            parseSkuData();
        } else {
            // TODO: 2018/12/25 针对OpenAPI做适配，获取公共字段：单位，产品分类，产品线字段，同步更新SPU下的sku
            String spuId = arg.getObjectData().getId();
            Object category = arg.getObjectData().get("category");
            Object unit = arg.getObjectData().get("unit");
            Object productLine = arg.getObjectData().get("product_line");
            Object batchSN = arg.getObjectData().get("batch_sn");
            IFilter filter = new Filter();
            filter.setFieldName(SKU_RELATE_SPU_ID);
            filter.setFieldValues(Lists.newArrayList(spuId));
            filter.setOperator(Operator.EQ);
            List<IObjectData> skuDataList = serviceFacade.findDataWithWhere(actionContext.getUser(), Utils.PRODUCT_API_NAME, Lists.newArrayList(filter), Lists.newArrayList(), 0, 0);
            String spuNewName = arg.getObjectData().get("name").toString();
            IObjectData spuDataFromDB = serviceFacade.findObjectData(actionContext.getUser(), spuId, Utils.SPU_API_NAME);
            boolean isSpec = Objects.equals(Boolean.TRUE, spuDataFromDB.get("is_spec"));
            String spuNameFromDB = spuDataFromDB.getName();
            for (IObjectData skuData : skuDataList) {
                String skuOldName = skuData.getName();
                String skuNewName;
                if (isSpec) {
                    skuNewName = skuOldName.replaceFirst(StringRegexEscapeUtils.escape(spuNameFromDB), spuNewName);
                } else {
                    skuNewName = spuNewName;
                }
                skuData.setName(skuNewName);
                if (!Objects.isNull(category) && StringUtils.isNotEmpty(category.toString())) {
                    skuData.set("category", category);
                }
                if (!Objects.isNull(unit) && StringUtils.isNotEmpty(unit.toString())) {
                    skuData.set("unit", unit);
                }
                if (!Objects.isNull(productLine) && StringUtils.isNotEmpty(productLine.toString())) {
                    skuData.set("product_line", productLine);
                }
                if (!Objects.isNull(batchSN) && StringUtils.isNotEmpty(batchSN.toString())) {
                    skuData.set("batch_sn", batchSN);
                }
            }
            serviceFacade.batchUpdate(skuDataList, actionContext.getUser());
        }
        return super.doAct(arg);
    }


    @Override
    protected boolean needTriggerApprovalFlow() {
        return false;
    }

    @Override
    /**
     * 商品产品保存走service里面的逻辑,不用父类的保存方法
     */
    protected void doUpdateData() {
        if (Objects.equals(actionContext.getPeerName(), "OpenAPI-V2.0")) {
            super.batchUpdateObjectData(Lists.newArrayList(objectData));
            return;
        }
        if (objectData == null) {
            return;
        }

        processSkuData();

        if (isMultiUnit()) {
            skuService.updateMultiUnitSpuAndHandleSku(objectData, actionContext.getUser(), addSkuDataList, invalidSkuDataList, editedSkuDataList, multiUnitData);
        } else {
            skuService.updateSpuAndHandleSku(objectData, actionContext.getUser(), addSkuDataList, invalidSkuDataList, editedSkuDataList);
        }
        updatedDataList.add(objectData);
    }


    @Override
    protected Result after(Arg arg, Result result) {
        updatedDataList.stream().findFirst().ifPresent(o -> o.set("sku", ""));
        return super.after(arg, result);
    }

    @Override
    protected void recordLog() {
        super.recordLog();
    }

    /**
     * 校验深圳的是否能编辑batch_sn字段,如果不能编辑抛出校验失败异常
     */
    private void batchSNFieldValidate() {
        IFieldDescribe batchField = objectDescribe.getFieldDescribe("batch_sn");
        if (batchField != null && Boolean.TRUE.equals(batchField.isActive())) {
            IObjectData beforeUpdateSpuData = serviceFacade.findObjectData(actionContext.getTenantId(), this.objectData.getId(), objectDescribe);


            CheckIsAllowModifySpuBatchSNRestModel.Arg snValidateArg = CheckIsAllowModifySpuBatchSNRestModel.Arg.builder()
                    .newValue(objectData.get("batch_sn", String.class))
                    .oldValue(beforeUpdateSpuData.get("batch_sn", String.class))
                    .spuId(objectData.getId())
                    .tenantId(actionContext.getTenantId())
                    .build();


            CheckIsAllowModifySpuBatchSNRestModel.Result snValidateResult = stockProxy.checkIsAllowModifySpuBatchSN(snValidateArg,
                    SFAHeaderUtil.getHeaders(actionContext.getUser()));

            if (!snValidateResult.getData().getResult()) {
                throw new ValidateException("批次与序列号管理字段校验失败,失败原因: " + snValidateResult.getData().getErrorMsg());
            }
        }
    }

    /**
     * 处理 sku 新增的数据
     */
    private void processSkuData() {
        IObjectDescribe skuDesc = serviceFacade.findObject(actionContext.getTenantId(), Utils.PRODUCT_API_NAME);
        if (CollectionUtils.notEmpty(addSkuDataList)) {
            addSkuDataList.forEach(o -> {
                o.set("spu_id", objectData.getId());
                o.set("category", objectData.get("category"));
                o.set("product_line", objectData.get("product_line"));
                o.set("unit", objectData.get("unit"));
                o.set("batch_sn", objectData.get("batch_sn"));
                setDefaultRecordType(o, skuDesc);
                modifyObjectDataBeforeCreate(o, skuDesc);
            });
        }
    }


    /**
     * 从spu数据中解析出sku数据
     */
    private void parseSkuData() {
        Optional<List<Map<String, Object>>> tmpSkuListOptional = Optional.of(objectData.get("sku", List.class));
        IObjectDescribe skuDescribe = serviceFacade.findObject(actionContext.getTenantId(), Utils.PRODUCT_API_NAME);

        tmpSkuListOptional.orElseThrow(() -> new ValidateException(I18N.text("product.sku_data_is_null")));

        boolean isSpec = Objects.equals(objectData.get("is_spec"), true);
        //新增的规格产品
        List<IObjectData> toAddSkuList = Lists.newArrayList();
        //更换规格值或者新增规格的历史产品
        List<IObjectData> toEditSkuList = Lists.newArrayList();
        tmpSkuListOptional.get().forEach(o -> {
            Object statusFlag = o.get("status_flag");

            if (isMultiUnit()) {
                o.put("is_multiple_unit", true);
            }

            if (statusFlag == null) {
                throw new RuntimeException("status flag is null");
            }

            if (Objects.isNull(o.get("object_describe_id"))) {
                o.put(ProductConstants.DescribleField.FIELD_DESCRIBE_ID.getApiName(), skuDescribe.getId());
                o.put(ProductConstants.DescribleField.FIELD_DESCRIBE_API_NAME.getApiName(), skuDescribe.getApiName());
            }

            // 更新要编辑和要作废的产品
            if (statusFlag.equals(ProductConstants.SkuEditStatus.EDIT.getStatus()) ||
                    (statusFlag.equals(ProductConstants.SkuEditStatus.INVALID.getStatus())) ||
                    (statusFlag.equals(ProductConstants.SkuEditStatus.SKU_EDIT_STATUS.getStatus())) ||
                    statusFlag.equals(ProductConstants.SkuEditStatus.ADD_SPEC.getStatus())) {
                // ------全部数据更新 start
                ObjectDataExt dataExt = ObjectDataExt.of(Maps.newHashMap(ObjectDataExt.of(o).toMap()));
                dataExt.remove(IObjectData.VERSION);
                dataExt.remove(ObjectLockStatus.LOCK_STATUS_API_NAME);
                dataExt.remove(ObjectLifeStatus.LIFE_STATUS_API_NAME);
                dataExt.remove(ObjectLifeStatus.LIFE_STATUS_BEFORE_INVALID_API_NAME);

                dataExt.set("name", objectData.getName());
                dataExt.set("category", objectData.get("category"));
                dataExt.set("product_line", objectData.get("product_line"));
                dataExt.set("unit", objectData.get("unit"));
                dataExt.set("batch_sn", objectData.get("batch_sn"));

                if (!(Objects.equals(objectData.get("is_spec"), true))) {
                    dataExt.set("price", objectData.get("standard_price"));
                }
                editedSkuDataList.add(dataExt.getObjectData());
                // ------全部数据更新 end
                if (isSpec && (statusFlag.equals(ProductConstants.SkuEditStatus.SKU_EDIT_STATUS.getStatus()) ||
                        statusFlag.equals(ProductConstants.SkuEditStatus.ADD_SPEC.getStatus()))) {
                    toEditSkuList.add(dataExt.getObjectData());
                }
            }

            if (statusFlag.equals(ProductConstants.SkuEditStatus.ADD.getStatus())) {
                o.put(ObjectLifeStatus.LIFE_STATUS_API_NAME, ObjectLifeStatus.NORMAL.getCode());
                o.put("owner", objectData.getOwner());
                IObjectData needAddSkuData = ObjectDataExt.of(o).getObjectData();
                modifyObjectDataBeforeCreate(needAddSkuData, skuDescribe);
                addSkuDataList.add(needAddSkuData);
                if (isSpec) {
                    toAddSkuList.add(needAddSkuData);
                }
            } else if (statusFlag.equals(ProductConstants.SkuEditStatus.INVALID.getStatus())) {
                invalidPrivilegeCheck();
                if (Objects.equals(o.get("is_deleted"), false)) {
                    invalidSkuDataList.add(ObjectDataExt.of(o).getObjectData());
                }
            }
        });
        if (CollectionUtils.notEmpty(toAddSkuList) || CollectionUtils.notEmpty(toEditSkuList)) {
            ProductValidator.validateSpecStatusActive(actionContext.getTenantId(), toAddSkuList, toEditSkuList);
        }
    }

    // 如果有作废产品数据需要校验作废权限
    private void invalidPrivilegeCheck() {
        serviceFacade.doFunPrivilegeCheck(actionContext.getUser(), Utils.PRODUCT_API_NAME, Lists.newArrayList(ObjectAction.INVALID.getActionCode()));
    }


    private boolean isMultiUnit() {
        return multiUnitData != null;
    }

}
