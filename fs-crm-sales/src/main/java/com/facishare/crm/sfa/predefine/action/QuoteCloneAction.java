package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.cpq.BomCoreService;
import com.facishare.crm.sfa.utilities.constant.BomConstants;
import com.facishare.crm.sfa.utilities.constant.QuoteConstants;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.crm.sfa.utilities.validator.QuoteValidator;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardCloneAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class QuoteCloneAction extends StandardCloneAction {private static final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);
    private final BomCoreService bomCoreService = SpringUtil.getContext().getBean(BomCoreService.class);
    @Override
    public void filterOrResetFieldValue(IObjectDescribe objectDescribe, IObjectData objectData, List<IObjectDescribe> detailDescribes, Map<String, List<IObjectData>> detailDataMap) {
        super.filterOrResetFieldValue(objectDescribe, objectData, detailDescribes, detailDataMap);
        if (!bizConfigThreadLocalCacheService.isPriceBookEnabled(actionContext.getTenantId())) {
            objectData.set(QuoteConstants.QuoteField.PRICEBOOKID.getApiName(), null);
        }
    }
}
