package com.facishare.crm.sfa.predefine.service.real.salesorder;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/10/14 14:08
 */
public interface SalesOrderHistoryProductService {


    /**
     * 复制历史订单产品，不支持wheres条件的接口。
     * 原始接口。进行灰度功能
     * @param query  查询条件只支持filter，不支持wheres
     * @param tenantId  企业ID
     * @return 处理后的query
     */
    SearchTemplateQuery dealQueryFilters(SearchTemplateQuery query, String tenantId, String userId);

    /**
     * 复制历史订单产品
     * @param query 查询条件，支持filter和wheres条件。 wheres条件最后会被清空
     * @param tenantId 企业ID
     * @param user  用户
     * @return 处理后的query
     */
    SearchTemplateQuery getSearchTemplateQuery(SearchTemplateQuery query, String tenantId, User user);

}
