package com.facishare.crm.sfa.predefine.initscene;

import com.facishare.crmcommon.enums.SearchTemplateEnum;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.constant.OpportunityConstants;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.initscene.DefaultSceneProvider;
import com.facishare.paas.metadata.api.SearchTemplateCode;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.Operator;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class OpportunityInitSceneProvider extends DefaultSceneProvider {
    @Override
    public String getApiName() {
        return SFAPreDefineObject.Opportunity.getApiName();
    }

    @Override
    public List<ISearchTemplate> getDefaultSearchTemplateList(User user, String apiName, String extendAttribute) {
        try {
            List<ISearchTemplate> searchTemplates = Lists.newArrayList();
            searchTemplates.addAll(this.searchTemplateService.findByObjectDescribeAPINameAndCode(
                    user.getTenantId(), apiName,
                    Sets.newHashSet(new SearchTemplateCode[]{SearchTemplateCode.ALL})));
            searchTemplates.addAll(this.searchTemplateService.findByObjectDescribeAPINameAndCode(
                    user.getTenantId(), apiName,
                    Sets.newHashSet(new SearchTemplateCode[]{SearchTemplateCode.IN_CHARGE})));
            searchTemplates.add(searchTemplateService.getSearchTemplate(user.getTenantId(), apiName,
                    SearchTemplateEnum.FOLLOW_UP.getLabel(), false, Lists.newArrayList(),
                    SearchTemplateEnum.FOLLOW_UP.getApiName()));
            searchTemplates.add(searchTemplateService.getSearchTemplate(user.getTenantId(), apiName,
                    SearchTemplateEnum.SERVICE.getLabel(), false, Lists.newArrayList(),
                    SearchTemplateEnum.SERVICE.getApiName()));
            searchTemplates.addAll(this.searchTemplateService.findByObjectDescribeAPINameAndCode(
                    user.getTenantId(), apiName,
                    Sets.newHashSet(new SearchTemplateCode[]{SearchTemplateCode.INVOLVED})));
            searchTemplates.addAll(this.searchTemplateService.findByObjectDescribeAPINameAndCode(
                    user.getTenantId(), apiName,
                    Sets.newHashSet(new SearchTemplateCode[]{SearchTemplateCode.SUB_IN_CHARGE})));
            searchTemplates.add(searchTemplateService.getSearchTemplate(user.getTenantId(), apiName,
                    SearchTemplateEnum.WIN.getLabel(), false,
                    Lists.newArrayList(searchTemplateService.getFilterByPara(Operator.EQ, OpportunityConstants.Field.STATUS.getApiName(),
                            Lists.newArrayList(OpportunityConstants.OpportunityStatus.WIN.getValue()),false)),
                    SearchTemplateEnum.WIN.getApiName()));
            searchTemplates.add(searchTemplateService.getSearchTemplate(user.getTenantId(), apiName,
                    SearchTemplateEnum.LOST.getLabel(), false,
                    Lists.newArrayList(searchTemplateService.getFilterByPara(Operator.EQ, OpportunityConstants.Field.STATUS.getApiName(),
                            Lists.newArrayList(OpportunityConstants.OpportunityStatus.LOSE.getValue()),false)),
                    SearchTemplateEnum.LOST.getApiName()));
            searchTemplates.add(searchTemplateService.getSearchTemplate(user.getTenantId(), apiName,
                    SearchTemplateEnum.SUB_FOLLOW_UP.getLabel(), false, Lists.newArrayList(),
                    SearchTemplateEnum.SUB_FOLLOW_UP.getApiName()));
            searchTemplates.add(searchTemplateService.getSearchTemplate(user.getTenantId(), apiName,
                    SearchTemplateEnum.SUB_SERVICE.getLabel(), false, Lists.newArrayList(),
                    SearchTemplateEnum.SUB_SERVICE.getApiName()));
            searchTemplates.addAll(this.searchTemplateService.findByObjectDescribeAPINameAndCode(
                    user.getTenantId(), apiName,
                    Sets.newHashSet(new SearchTemplateCode[]{SearchTemplateCode.SUB_INVOLVED})));
            searchTemplates.addAll(this.searchTemplateService.findByObjectDescribeAPINameAndCode(
                    user.getTenantId(), apiName,
                    Sets.newHashSet(new SearchTemplateCode[]{SearchTemplateCode.SHARED})));
            searchTemplates.addAll(this.searchTemplateService.findByObjectDescribeAPINameAndCode(
                    user.getTenantId(), apiName,
                    Sets.newHashSet(new SearchTemplateCode[]{SearchTemplateCode.IN_CHARGE_DEPT})));

            searchTemplates.forEach(x-> {
                if(x.getApiName().equals(SearchTemplateEnum.IN_CHARGE.getApiName())) {
                    x.setIsDefault(true);
                }else{
                    x.setIsDefault(false);
                }});

            return searchTemplates;
        } catch (MetadataServiceException e) {
            log.error("Error in init DefaultSearchTemplate, ei:{}, apiName:{}", user.getTenantId(), apiName);
            return Lists.newArrayList();
        }
    }
}
