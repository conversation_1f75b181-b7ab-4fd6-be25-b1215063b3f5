package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.action.listener.ReturnedGoodsInvoiceBulkRecoverActionListener;
import com.facishare.paas.appframework.core.model.ActionListener;
import com.facishare.paas.appframework.core.predef.action.StandardBulkRecoverAction;

import java.util.List;

/**
 * Created by renlb on 2019/3/14.
 */
public class ReturnedGoodsInvoiceBulkRecoverAction extends StandardBulkRecoverAction {

    @Override
    public List<Class<? extends ActionListener<Arg, Result>>> getActionListenerClassList() {
        List<Class<? extends ActionListener<Arg, Result>>> classList = super.getActionListenerClassList();
        classList.add(ReturnedGoodsInvoiceBulkRecoverActionListener.class);
        return classList;
    }
}
