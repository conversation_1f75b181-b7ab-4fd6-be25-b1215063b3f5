package com.facishare.crm.sfa.predefine.importobject;

import com.facishare.paas.appframework.metadata.importobject.DefaultObjectImportProvider;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.springframework.stereotype.Component;

@Component
public class PartnerObjectImportProvider extends DefaultObjectImportProvider {
    @Override
    protected boolean getIsBackFillIndustrialAndCommercialInfo(IObjectDescribe objectDescribe) {
        return true;
    }

    @Override
    protected boolean getIsVerifyEnterprise(IObjectDescribe objectDescribe) {
        return true;
    }
}
