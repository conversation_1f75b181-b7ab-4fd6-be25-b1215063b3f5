package com.facishare.crm.sfa.predefine.service.model.Duplicate;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

public interface DuplicateCheckByName {
    @Data
    class Arg {
        @JsonProperty("api_name")
        private String apiName;
        @JsonProperty("object_data")
        private ObjectDataDocument objectData;
    }

    @Data
    @Builder
    class Result {
        Boolean duplicated;
    }
}