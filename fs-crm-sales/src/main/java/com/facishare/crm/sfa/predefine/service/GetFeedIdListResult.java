package com.facishare.crm.sfa.predefine.service;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

public interface GetFeedIdListResult {
    @Data
    class Arg implements Serializable {
        @JSONField(
                name = "M1"
        )
        String search_query_info;
        @JSONField(
                name = "M2"
        )
        String search_template_id;
        @JSONField(
                name = "M3"
        )
        Integer pageSize = 3;//分页大小
        @JSONField(
                name = "M4"
        )
        Integer sinceID = 4;//当前页最小FeedID
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        private List<Integer> feedIdList = Lists.newArrayList();
        private boolean hasMore;
        public static Result EmptyResult(){
            return new Result().builder().feedIdList(Lists.newArrayList()).hasMore(false).build();
        }
    }

}
