package com.facishare.crm.sfa.predefine.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.sfa.predefine.enums.ActionCodeEnum;
import com.facishare.crm.sfa.predefine.exception.SFABusinessException;
import com.facishare.crm.sfa.predefine.exception.SFAErrorCode;
import com.facishare.crm.sfa.predefine.service.task.RecalculateTaskService;
import com.facishare.crm.sfa.utilities.constant.AccountConstants;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_SUCESSFUL;

/**
 * Created by renlb on 2018/7/18.
 * @IgnoreI18nFile
 */
public class AccountChangeDealStatusAction extends PreDefineAction<AccountChangeDealStatusAction.Arg, AccountChangeDealStatusAction.Result> {
    private static final IObjectDataService objectDataService = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);
    private static final RecalculateTaskService recalculateTaskService = SpringUtil.getContext().getBean(RecalculateTaskService.class);
    private Integer originalDealStatus = 1;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.UPDATE_DEAL_STATUS.getActionCode());
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Lists.newArrayList(arg.getAccountId());
    }

    @Override
    protected Result doAct(Arg arg) {
        if (originalDealStatus.equals(arg.getDealStatus())) {
            return AccountChangeDealStatusAction.Result.builder().errorCode("0").message(I18N.text(SFA_SUCESSFUL)).build();
        }

        if (CollectionUtils.notEmpty(dataList)) {
            for (IObjectData objectData : dataList) {
                objectData.set(AccountConstants.Field.DEAL_STATUS, arg.getDealStatus());
                if (AccountConstants.DealStatus.UN_DEAL.getValue().equals(String.valueOf(arg.getDealStatus()))) {
                    objectData.set("last_deal_closed_time", null);
                } else if (AccountConstants.DealStatus.MULTI_DEAL.getValue().equals(String.valueOf(arg.getDealStatus()))) {
                    objectData.set("last_deal_closed_time", System.currentTimeMillis());
                } else {
                    Long dealTime = AccountUtil.getLongValue(objectData, AccountConstants.Field.LAST_DEAL_TIME, System.currentTimeMillis());
                    objectData.set("last_deal_closed_time", dealTime);
                }
            }
        }
        List<String> updateFieldList = Lists.newArrayList("last_deal_closed_time", AccountConstants.Field.DEAL_STATUS);

        try {
            objectDataService.batchUpdateWithField(dataList, updateFieldList, AccountUtil.getDefaultActionContext(actionContext.getUser()));
            // 发送重算到期时间的task
            for (IObjectData objectData : dataList) {
                if (!originalDealStatus.equals(arg.getDealStatus())) {
                    recalculateTaskService.send(objectData.getTenantId(), objectData.getId(), "AccountObj", ActionCodeEnum.CHANGE_DEAL);
                }
            }
        } catch (Exception e) {
            throw new SFABusinessException(SFAErrorCode.ACCOUNT_COMMON_ERROR);
        }
        return AccountChangeDealStatusAction.Result.builder().errorCode("0").message(I18N.text(SFA_SUCESSFUL)).build();
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        IObjectData objectData = dataList.get(0);
        originalDealStatus = AccountUtil.getIntegerValue(objectData, AccountConstants.Field.DEAL_STATUS, 1);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        if (originalDealStatus.equals(arg.getDealStatus())) {
            return result;
        }
        String dealStatusText = "已成交";
        if (String.valueOf(arg.getDealStatus()).equals(AccountConstants.DealStatus.UN_DEAL.getValue())) {
            dealStatusText = "未成交";
        } else if (String.valueOf(arg.getDealStatus()).equals(AccountConstants.DealStatus.MULTI_DEAL.getValue())) {
            dealStatusText = "多次成交";
        }
        String logContent = String.format("成交状态，被更改为 %s 状态", dealStatusText);
        serviceFacade.logWithCustomMessage(actionContext.getUser(), EventType.MODIFY, ActionType.CHANGEDEALSTATUS, objectDescribe, dataList, logContent);
        return result;
    }

    @Data
    public static class Arg {
        @JSONField(name = "M1")
        @JsonProperty("account_id")
        private String accountId;

        @JSONField(name = "M2")
        @JsonProperty("deal_status")
        private int dealStatus;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private String errorDetail;
        private String errorCode;
        private String message;

        public boolean isSuccess() {
            if ("0".equals(errorCode)) {
                return true;
            }
            return false;
        }
    }
}
