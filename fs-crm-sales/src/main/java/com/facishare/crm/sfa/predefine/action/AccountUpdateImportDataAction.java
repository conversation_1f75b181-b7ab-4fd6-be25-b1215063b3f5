package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.AccountPathSynchronizer;
import com.facishare.crm.sfa.predefine.service.AccountPoolServiceImpl;
import com.facishare.crm.sfa.utilities.constant.AccountAddrConstants;
import com.facishare.crm.sfa.utilities.constant.AccountConstants;
import com.facishare.crm.sfa.utilities.util.*;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.BaseImportAction;
import com.facishare.paas.appframework.core.predef.action.StandardUpdateImportDataAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.dispatcher.ObjectDataProxy;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @IgnoreI18nFile
 */
@Slf4j
public class AccountUpdateImportDataAction extends StandardUpdateImportDataAction {
    private static final ObjectDataProxy objectDataProxy = SpringUtil.getContext().getBean(ObjectDataProxy.class);
    private List<IObjectData> dbBeforeImportDataList = Lists.newArrayList();
    private Boolean isNeedChangeAccountPath = false;
    private static final AccountPoolServiceImpl accountPoolService = SpringUtil.getContext().getBean(AccountPoolServiceImpl.class);

    @Override
    protected void customInit(List<ImportData> dataList) {
        super.customInit(dataList);
        List<String> objectDataIds = dataList.stream().filter(x -> StringUtils.isNotBlank(x.getData().getId())).map(x -> x.getData().getId()).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(objectDataIds)) {
            dbBeforeImportDataList = serviceFacade.findObjectDataByIdsIncludeDeleted(this.actionContext.getUser(), objectDataIds, this.actionContext.getObjectApiName());
        }
    }

    @Override
    protected void getPhoneNumberInfo(List<ImportData> dataList) {
        for (ImportData data : dataList) {
            if (!Objects.isNull(data.getData().get("tel"))) {
                String phoneNum = data.getData().get("tel").toString();
                PhoneUtil.Result result = PhoneUtil.getPhoneNumberInfo(phoneNum);
                if (!org.springframework.util.StringUtils.isEmpty(result)) {
                    data.getData().set("phone_number_attribution_country", result.getCountry());
                    data.getData().set("phone_number_attribution_province", result.getProvince());
                    data.getData().set("phone_number_attribution_city", result.getCity());
                } else {
                    data.getData().set("phone_number_attribution_country", null);
                    data.getData().set("phone_number_attribution_province", null);
                    data.getData().set("phone_number_attribution_city", null);
                }
            } else {
                data.getData().set("phone_number_attribution_country", null);
                data.getData().set("phone_number_attribution_province", null);
                data.getData().set("phone_number_attribution_city", null);
            }
        }
    }

    @Override
    protected void findDescribe() {
        objectDescribe = serviceFacade.findObject(actionContext.getTenantId(), actionContext.getObjectApiName());
        AccountUtil.setImportFields(actionContext.getTenantId(), arg.getObjectCode(), objectDescribe.getFieldDescribes(), true);
    }

    @Override
    protected void customValidate(List<ImportData> dataList) {
        super.customValidate(dataList);
        List<ImportError> errorList = AccountUtil.importCustomValidate(dataList, actionContext.getUser(), true, objectDescribe, arg.getIsEmptyValueToUpdate());

        mergeErrorList(errorList);

        //校验是否改变，是否成环
        Map<Integer, ObjectDataExt> dataInStore = getDataInStore();
        if (!CollectionUtils.isEmpty(dataList) && !dataInStore.isEmpty() &&
                ObjectDataDocument.of(dataList.get(0).getData()).containsKey(AccountConstants.Field.FIELD_PARENT_ACCOUNT_ID)) {
            List<IObjectData> needCheckDataList = Lists.newArrayList();
            dataList.forEach(m -> {
                String parentAccountId = m.getData().get(AccountConstants.Field.FIELD_PARENT_ACCOUNT_ID, String.class);
                ObjectDataExt objectData = dataInStore.get(m.getRowNo());
                if (!Objects.isNull(objectData)) {
                    String oldParentAccountId = objectData.get(AccountConstants.Field.FIELD_PARENT_ACCOUNT_ID, String.class);
                    if (!Objects.equals(parentAccountId, oldParentAccountId)) {
                        m.getData().set("row_no", m.getRowNo());
                        needCheckDataList.add(m.getData());
                    }
                }
            });
            if (!CollectionUtils.isEmpty(needCheckDataList)) {
                isNeedChangeAccountPath = true;
                try {
                    List<ImportError> errorHoopList = AccountPathUtil.checkIsHoopForImport(actionContext.getUser(),
                            "editImport", needCheckDataList);
                    if (!CollectionUtils.isEmpty(errorHoopList)) {
                        mergeErrorList(errorHoopList);
                    }
                } catch (Exception ex) {
                    log.warn("客户层级校验失败，", ex);
                    List<ImportError> errorHoopList = Lists.newArrayList();
                    needCheckDataList.forEach(m -> {
                        Integer rowNo = m.get("row_no", Integer.class);
                        if (rowNo == null || rowNo == 0) {
                            return;
                        }
                        errorHoopList.add(new BaseImportAction.ImportError(rowNo, I18N.text(SFAI18NKeyUtil.SFA_ACCOUNT_PARENTISNOTEXIST)));
                    });
                    mergeErrorList(errorHoopList);
                }
            }
        }

    }

    @Override
    protected void customDefaultValue(List<IObjectData> validList) {
        super.customDefaultValue(validList);
        AccountAddrUtil.bulkHandleLocationField(validList);

        validList.forEach(x -> {
            x.set("pin_yin", Chinese2PinyinUtils.getPinyinString(x.getName()));
            int completed_field_quantity = AccountUtil.calculateObjectHasValueCount(objectDescribe, x);
            x.set("completed_field_quantity", completed_field_quantity);
            if (AccountUtil.isUserDefineDealSetting(actionContext.getTenantId())) {
                String dealStatus = AccountUtil.getStringValue(x, AccountConstants.Field.DEAL_STATUS, "");
                if (arg.getIsEmptyValueToUpdate() && StringUtils.isEmpty(dealStatus)) {
                    Optional<IObjectData> objectDataOptional = dbBeforeImportDataList.stream()
                            .filter(d -> x.getId().equals(d.getId())).findFirst();
                    if (objectDataOptional.isPresent()) {
                        IObjectData objectData = objectDataOptional.get();
                        String dbDealStatus = AccountUtil.getStringValue(objectData, AccountConstants.Field.DEAL_STATUS, AccountConstants.DealStatus.UN_DEAL.getValue());
                        if (!dbDealStatus.equals(AccountConstants.DealStatus.UN_DEAL.getValue())) {
                            Long dbDealTime = AccountUtil.getLongValue(objectData, AccountConstants.Field.LAST_DEAL_TIME, System.currentTimeMillis());
                            x.set(AccountConstants.Field.LAST_DEAL_TIME, dbDealTime);
                        }
                        x.set(AccountConstants.Field.DEAL_STATUS, dbDealStatus);
                    }
                }
                if (StringUtils.isNotEmpty(dealStatus)) {
                    if (dealStatus.equals(AccountConstants.DealStatus.UN_DEAL.getValue())) {
                        x.set(AccountConstants.Field.LAST_DEAL_TIME, null);
                    } else {
                        Long dealTime = AccountUtil.getLongValue(x, AccountConstants.Field.LAST_DEAL_TIME, System.currentTimeMillis());
                        x.set(AccountConstants.Field.LAST_DEAL_TIME, dealTime);
                    }
                }
            }
        });
    }

    @Override
    protected void customAfterImport(List<IObjectData> actualList) {
        if (!CollectionUtils.isEmpty(actualList) && ObjectDataDocument.of(actualList.get(0)).containsKey(AccountConstants.Field.FIELD_ACCOUNT_PATH)) {
            actualList.forEach(m -> {
                ObjectDataDocument.of(m).remove(AccountConstants.Field.FIELD_ACCOUNT_PATH);
            });
        }
        super.customAfterImport(actualList);
        if (isNeedChangeAccountPath) {
            AccountPathSynchronizer.builder()
                    .user(actionContext.getUser())
                    .objectDataList(actualList)
                    .build()
                    .asyncDealData();
        }

        updateClaimTime(actualList);
        boolean sendLocationMq = true;
        List<IObjectData> manualUpdateLocationDataList = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(arg.getRows())) {
            ObjectDataDocument rowData = arg.getRows().get(0);
            if (rowData.containsKey("经度")
                    && rowData.containsKey("纬度")
                    && (rowData.containsKey("定位（必填）") || rowData.containsKey("定位"))) {
                sendLocationMq = false;
                dataList.forEach(x -> {
                    int rowNo = x.getRowNo();
                    Optional<ObjectDataDocument> importObjectDataOptional = arg.getRows().stream()
                            .filter(d -> AccountUtil.getIntegerValue(d, "rowNo", 0) == rowNo).findFirst();
                    if (importObjectDataOptional.isPresent()) {
                        ObjectDataDocument importObjectData = importObjectDataOptional.get();
                        String location = "";
                        if (rowData.containsKey("定位（必填）")) {
                            location = AccountUtil.getStringValue(importObjectData, "定位（必填）", "");
                        } else if (rowData.containsKey("定位")) {
                            location = AccountUtil.getStringValue(importObjectData, "定位", "");
                        }
                        String longitude = AccountUtil.getStringValue(importObjectData, "经度", "");
                        String latitude = AccountUtil.getStringValue(importObjectData, "纬度", "");
                        String dataId = x.getData().getId();
                        Optional<IObjectData> objectDataOptional = actualList.stream().filter(d -> dataId.equals(d.getId())).findFirst();
                        if (objectDataOptional.isPresent()) {
                            IObjectData actualObjectData = objectDataOptional.get();
                            if (StringUtils.isNotBlank(location) && !location.endsWith("#%$")) {
                                String[] tempLocation = location.split("\\#\\%\\$");
                                if (tempLocation != null && tempLocation.length > 1) {
                                    location = tempLocation[tempLocation.length - 1];
                                }
                            }
                            if (StringUtils.isNotBlank(location)
                                    && StringUtils.isNotBlank(longitude) && !"0".equals(longitude)
                                    && StringUtils.isNotBlank(latitude) && !"0".equals(latitude)) {
                                location = longitude + "#%$" + latitude + "#%$" + location;
                                actualObjectData.set(AccountAddrConstants.Field.LOCATION.getApiName(), location);
                                manualUpdateLocationDataList.add(actualObjectData);
                            }
                        }
                    }
                });
            }
        }

        //批量修改客户主地址
        AccountAddrUtil.bulkUpdateAccountAddrForMain(actionContext.getUser(), actualList);
        boolean isGrayDisableGeoCalculate = AccountUtil.isGrayDisableGeoCalculate(actionContext.getTenantId());
        if (sendLocationMq && !isGrayDisableGeoCalculate) {
            AccountAddrUtil.sendLocationMq(actionContext.getUser(), SFAPreDefineObject.Account.getApiName(), actualList);
        } else {
            if (!CollectionUtils.isEmpty(manualUpdateLocationDataList)) {
                List<String> updateFieldList = Lists.newArrayList();
                updateFieldList.add(AccountAddrConstants.Field.LOCATION.getApiName());
                IActionContext context = AccountUtil.getDefaultActionContext(actionContext.getUser());
                try {
                    objectDataProxy.batchUpdateIgnoreOther(manualUpdateLocationDataList, updateFieldList, context);
                } catch (Exception e) {
                    log.error("account manual update location error", e);
                }
                List<String> updatedIds = manualUpdateLocationDataList.stream().map(x -> x.getId()).collect(Collectors.toList());
                actualList.removeIf(x -> updatedIds.contains(x.getId()));
                if (!CollectionUtils.isEmpty(actualList) && !isGrayDisableGeoCalculate) {
                    AccountAddrUtil.sendLocationMq(actionContext.getUser(), SFAPreDefineObject.Account.getApiName(), actualList);
                }
            } else {
                if(!isGrayDisableGeoCalculate) {
                    AccountAddrUtil.sendLocationMq(actionContext.getUser(), SFAPreDefineObject.Account.getApiName(), actualList);
                }
            }
        }

        List<String> poolIds = AccountUtil.getPoolIds(actualList);
        poolIds.addAll(AccountUtil.getPoolIds(dbBeforeImportDataList));
        if (!CollectionUtils.isEmpty(poolIds)) {
            poolIds = poolIds.stream().distinct().collect(Collectors.toList());
            accountPoolService.updatePoolCustomerCount(actionContext.getUser(), poolIds);
        }
//        IndustryEnterInfoUtil.updateIndustryEnterInfo(actionContext.getUser(), actionContext.getObjectApiName(),
//                arg.getIsVerifyEnterprise(), arg.getIsBackFillIndustrialAndCommercialInfo(),
//                arg.getIsBackFillOverwriteOldValue(), actualList);
    }

    private void updateClaimTime(List<IObjectData> actualList) {
        List<String> objectDataIds = actualList.stream().map(x -> x.getId()).collect(Collectors.toList());
        List<IObjectData> dbAfterImportDataList = serviceFacade.findObjectDataByIdsIncludeDeleted(this.actionContext.getUser(), objectDataIds, this.actionContext.getObjectApiName());
        List<IObjectData> toBeUpdateDataList = Lists.newArrayList();
        for (IObjectData objectData : dbAfterImportDataList) {
            Optional<IObjectData> objectDataOptional = dbBeforeImportDataList.stream().filter(x -> objectData.getId().equals(x.getId())).findFirst();
            if (objectDataOptional.isPresent()) {
                IObjectData tempData = objectDataOptional.get();
                String beforePoolId = AccountUtil.getPoolId(tempData);
                String afterPoolId = AccountUtil.getPoolId(objectData);
                if (!afterPoolId.equals(beforePoolId)) {
                    if (StringUtils.isNotBlank(afterPoolId)) {
                        objectData.set("claimed_time", System.currentTimeMillis());
                    }
                    toBeUpdateDataList.add(objectData);
                }
            }
        }
        if (!CollectionUtils.isEmpty(toBeUpdateDataList)) {
            List<String> updateFieldList = Lists.newArrayList("claimed_time", "owner_modified_time");
            IActionContext context = AccountUtil.getDefaultActionContext(actionContext.getUser());
            try {
                objectDataProxy.batchUpdateIgnoreOther(toBeUpdateDataList, updateFieldList, context);
            } catch (Exception e) {
                log.error("account update claimed_time,owner_modified_time error", e);
            }
        }
    }
}
