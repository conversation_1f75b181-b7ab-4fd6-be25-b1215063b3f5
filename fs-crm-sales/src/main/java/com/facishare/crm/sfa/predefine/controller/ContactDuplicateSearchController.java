package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.util.ContactUtil;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.service.dto.duplicatedSearch.GetResult;

public class ContactDuplicateSearchController extends SFADuplicateSearchController {
    @Override
    protected GetResult.Result doService(GetResult.Arg arg) {
        ObjectDataDocument dataDocument= arg.getObjectData();
        if(dataDocument!=null)
        {
            ContactUtil.handleContactFields(dataDocument);
            ContactUtil.handlePhoneFields(dataDocument);
        }
        GetResult.Result result = super.doService(arg);
        return result;
    }

}
