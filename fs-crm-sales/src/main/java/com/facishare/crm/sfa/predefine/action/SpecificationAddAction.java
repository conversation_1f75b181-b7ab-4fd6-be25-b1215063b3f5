package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.openapi.Utils;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.real.ProductService;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.validator.SpecificationValidator;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;

import com.google.common.collect.Lists;
import org.springframework.util.CollectionUtils;

import java.util.List;

import static com.facishare.crm.sfa.utilities.util.SOI18NKeyUtils.SO_CHECK_SPEC_SAME_NAME;

/**
 * <AUTHOR>
 * @date 2018/11/21 19:10
 */
public class SpecificationAddAction extends StandardAddAction {
    private ServiceFacade serviceFacade = SpringUtil.getContext().getBean(ServiceFacade.class);

    private ProductService productService = SpringUtil.getContext().getBean(ProductService.class);

    @Override
    protected void before(Arg arg) {
        //兼容传参object_describe_id和object_describe_api_name为空的情况
        IObjectDescribe speDescribe = serviceFacade
                .findObject(actionContext.getTenantId(), Utils.SPECIFICATION_API_NAME);
        IObjectDescribe valueDescribe = serviceFacade
                .findObject(actionContext.getTenantId(), Utils.SPECIFICATION_VALUE_API_NAME);
        productService.modifySpeAndValueArg(speDescribe, valueDescribe, arg, actionContext);
        super.before(arg);
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        List<IFilter> filters = searchQuery.getFilters();
        SearchUtil.fillFilterEq(filters, "name", this.objectData.getName());
        QueryResult<IObjectData> result = serviceFacade.findBySearchQuery(getActionContext().getUser(), Utils.SPECIFICATION_API_NAME, searchQuery);
        if (!CollectionUtils.isEmpty(result.getData())) {
            throw new ValidateException(I18N.text(SO_CHECK_SPEC_SAME_NAME));
        }
        SpecificationValidator.validateSpecificationValueRepeatable(
                detailObjectData.getOrDefault(SFAPreDefineObject.SpecificationValue.getApiName(), Lists.newArrayList()));
    }
}
