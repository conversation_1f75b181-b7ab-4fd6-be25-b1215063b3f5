package com.facishare.crm.sfa.predefine.action;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.constant.AccountAddrConstants;
import com.facishare.crm.sfa.utilities.constant.AccountFinInfoConstants;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.crm.sfa.utilities.util.SOI18NKeyUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 地址作废 class
 *
 * <AUTHOR>
 * @date 2019/2/23
 */
@Slf4j
public class AccountFinInfoBulkInvalidAction extends StandardBulkInvalidAction {

    @Override
    protected void before(Arg arg) {
        if (StringUtils.isEmpty(this.arg.getJson())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_ACCOUNTADDR_INVALIDDATANOTNULL));
        }
        JSONObject jsonObject;
        try {
            jsonObject = JSON.parseObject(this.arg.getJson());
        } catch (JSONException e) {
            log.error(e.getMessage(), e);
            throw new ValidateException(I18N.text(SOI18NKeyUtils.SO_PRICEBOOK_INVALIDDATAANALYSISERROR));
        }
        JSONArray jsonArray = jsonObject.getJSONArray("dataList");
        if (null == jsonArray) {
            throw new ValidateException(I18N.text(SOI18NKeyUtils.SO_PRICEBOOK_INVALIDISNULL));
        }
        List<String> idList = Lists.newArrayList();
        for (int i = 0; i < jsonArray.size(); ++i) {
            idList.add(jsonArray.getJSONObject(i).get("_id").toString());
        }
        List<IObjectData> accountFinInfoList = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), idList, SFAPreDefineObject.AccountFinInfo.getApiName());
        if (CollectionUtils.empty(accountFinInfoList)) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_ACCOUNTADDR_CONTACTNOTNULL, I18N.text("AccountFinInfoObj.attribute.self.display_name")));
        }
        Optional<IObjectData> accountFinInfoDefaultOptional = accountFinInfoList.stream().filter(item -> item.get(AccountFinInfoConstants.Field.IS_DEFAULT.getApiName(), Boolean.class)).findFirst();
        if (accountFinInfoDefaultOptional.isPresent()) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_ACCOUNTFININFO_DEFAULTNOTALLOWINVALID));
        }
        List<String> accountIdList = accountFinInfoList.stream()
                .map(it -> String.valueOf(it.get(AccountAddrConstants.Field.ACCOUNT_ID.getApiName())))
                .distinct()
                .collect(Collectors.toList());
        List<IObjectData> accountList = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), accountIdList, SFAPreDefineObject.Account.getApiName());
        if (CollectionUtils.empty(accountList) || accountList.size() < accountIdList.size()) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_ACCOUNTADDR_ACOUNTNOTNULL, I18N.text("AccountObj.attribute.self.display_name")));
        }
        super.before(arg);
    }
}
