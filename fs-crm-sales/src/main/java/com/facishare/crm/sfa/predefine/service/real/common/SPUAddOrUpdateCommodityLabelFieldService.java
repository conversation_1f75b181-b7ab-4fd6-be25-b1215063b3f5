package com.facishare.crm.sfa.predefine.service.real.common;

import com.facishare.crm.openapi.Utils;
import org.springframework.stereotype.Component;

@Component
public class SPUAddOrUpdateCommodityLabelFieldService extends AbstractObjectAddOrUpdatePredefinedFieldService {
    @Override
    public String getObjectApiName() {
        return Utils.SPU_API_NAME;
    }

    @Override
    public String getFieldApiName() {
        return "commodity_label";
    }

    @Override
    public String getFieldDescribeJson() {
        return "{\n" +
                "        \"describe_api_name\": \"SPUObj\",\n" +
                "        \"is_index\": true,\n" +
                "        \"is_active\": true,\n" +
                "        \"description\": \"商品标签\",\n" +
                "        \"is_unique\": false,\n" +
                "        \"label\": \"商品标签\",\n" +
                "        \"type\": \"select_many\",\n" +
                "        \"is_need_convert\": false,\n" +
                "        \"is_required\": false,\n" +
                "        \"api_name\": \"commodity_label\",\n" +
                "        \"define_type\": \"package\",\n" +
                "        \"is_index_field\": true,\n" +
                "        \"is_single\": false,\n" +
                "        \"options\": [\n" +
                "            {\n" +
                "                \"not_usable\": false,\n" +
                "                \"label\": \"新品\",\n" +
                "                \"value\": \"option1\",\n" +
                "                \"config\": {\n" +
                "                    \"edit\": 0,\n" +
                "                    \"enable\": 0,\n" +
                "                    \"remove\": 0\n" +
                "                }\n" +
                "            },\n" +
                "            {\n" +
                "                \"not_usable\": false,\n" +
                "                \"label\": \"热销\",\n" +
                "                \"value\": \"option2\",\n" +
                "                \"config\": {\n" +
                "                    \"edit\": 0,\n" +
                "                    \"enable\": 0,\n" +
                "                    \"remove\": 0\n" +
                "                }\n" +
                "            },\n" +
                "            {\n" +
                "                \"not_usable\": false,\n" +
                "                \"label\": \"清仓\",\n" +
                "                \"value\": \"option3\",\n" +
                "                \"config\": {\n" +
                "                    \"edit\": 0,\n" +
                "                    \"enable\": 0,\n" +
                "                    \"remove\": 0\n" +
                "                }\n" +
                "            },\n" +
                "            {\n" +
                "                \"not_usable\": false,\n" +
                "                \"label\": \"推荐\",\n" +
                "                \"value\": \"option4\",\n" +
                "                \"config\": {\n" +
                "                    \"edit\": 0,\n" +
                "                    \"enable\": 0,\n" +
                "                    \"remove\": 0\n" +
                "                }\n" +
                "            },\n" +
                "            {\n" +
                "                \"not_usable\": false,\n" +
                "                \"label\": \"重要\",\n" +
                "                \"value\": \"option5\",\n" +
                "                \"config\": {\n" +
                "                    \"edit\": 0,\n" +
                "                    \"enable\": 0,\n" +
                "                    \"remove\": 0\n" +
                "                }\n" +
                "            },\n" +
                "            {\n" +
                "                \"not_usable\": false,\n" +
                "                \"label\": \"其他\",\n" +
                "                \"value\": \"option6\",\n" +
                "                \"config\": {\n" +
                "                    \"edit\": 0,\n" +
                "                    \"enable\": 0,\n" +
                "                    \"remove\": 0\n" +
                "                }\n" +
                "            }\n" +
                "        ],\n" +
                "        \"config\": {\n" +
                "            \"edit\": 1,\n" +
                "            \"add\": 0,\n" +
                "            \"enable\": 1,\n" +
                "            \"help_text\": 1,\n" +
                "            \"default_value\": 1,\n" +
                "            \"attrs\": {\n" +
                "                \"api_name\": 1,\n" +
                "                \"default_value\": 1,\n" +
                "                \"options\": 0,\n" +
                "                \"help_text\": 1,\n" +
                "                \"is_readonly\": 1,\n" +
                "                \"is_required\": 1,\n" +
                "                \"label\": 1\n" +
                "            }\n" +
                "        },\n" +
                "        \"status\": \"released\"\n" +
                "    }";
    }
}
