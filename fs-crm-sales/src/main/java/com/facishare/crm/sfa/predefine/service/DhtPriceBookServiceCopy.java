package com.facishare.crm.sfa.predefine.service;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.model.*;
import com.facishare.crm.sfa.predefine.service.real.TransformData4ViewService;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.ProductConstants;
import com.facishare.crm.sfa.utilities.constant.SalesOrderConstants;
import com.facishare.crm.sfa.utilities.util.ConcatenateSqlUtils;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.APPException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.ProductCategoryService;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.INameCache;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.predefine.service.modulectrl.IModuleInitService.MODULE_CPQ;
import static com.facishare.crm.sfa.utilities.util.ConcatenateSqlUtils.*;

@ServiceModule("dht_pricebook_copy")
@Component
@Slf4j
public class DhtPriceBookServiceCopy {

    @Autowired
    private ServiceFacade serviceFacade;

    @Autowired
    private ObjectDataServiceImpl objectDataService;

    @Autowired
    private ModuleCtrlConfigService moduleCtrlConfigService;

    @Autowired
    private ProductCategoryService productCategoryService;
    @Autowired
    private TransformData4ViewService transformData4ViewService;

    @Autowired
    private MetaDataFindServiceExt metaDataFindServiceExt;
    @Autowired
    private DhtPriceBookService dhtPriceBookService;
    @Autowired
    private BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;
    @Autowired
    private InfraServiceFacade infraServiceFacade;

    //根据spu的创建时间和价目表id查询spu
    @ServiceMethod("get_new_products")
    public GetProductByCreateTime.Result getNewProductsOldMethod(GetProductByCreateTime.Arg arg, ServiceContext context) {
        GetProductByCreateTime.Result newProductsPrivateMethod = getNewProductsPrivateMethod(arg, context);
//        fillMultipleUnitInfo(context, newProductsPrivateMethod.getDataList(),"spu_id");
        return newProductsPrivateMethod;
    }

    @ServiceMethod("product_list")
    public ProductListModule.Result productList(ProductListModule.Arg arg, ServiceContext context) {
        String dataSql = "";
        Set<String> productIds = null;
        if (bizConfigThreadLocalCacheService.isAvailableRangeEnabled(context.getTenantId())) {
            if (Strings.isEmpty(arg.getAccountId())) {
                return new ProductListModule.Result();
            }
            Tuple<Boolean, Set<String>> availableTuple = dhtPriceBookService.matchAvailableProduct(context,
                    arg.getAccountId(), arg.getPriceBookId(), arg.getAvailableRangeId());
            if (!availableTuple.getKey()) {
                return new ProductListModule.Result();
            }
            productIds = availableTuple.getValue();
        } else {
            if (Strings.isNotBlank(arg.getPriceBookId()) && !priceBookAvailable(arg.getPriceBookId(), context.getUser())) {
                log.warn("DhtPriceBookService productList priceBook is not available arg:{}", arg);
                return new ProductListModule.Result();
            }
            if (Strings.isNotBlank(arg.getPriceBookId())) {
                dataSql = ConcatenateSqlUtils.getSkuByPriceBookId(arg, context);
            }
        }
        if (StringUtils.isBlank(arg.getSalesOrderProSearchQueryInfo())) {
            SearchTemplateQuery skuSearchTemplateQuery = (SearchTemplateQuery) SearchTemplateQuery.fromJsonString(arg.getProductSearchQueryInfo());
            if (StringUtils.isNotBlank(dataSql)) {
                SearchUtil.fillFilterInBySql(skuSearchTemplateQuery.getFilters(), "_id", SearchUtil.FIELD_VALUE_TYPE_SQL, dataSql);
            }
            if (CollectionUtils.isNotEmpty(productIds)) {
                SearchUtil.fillFilterIn(skuSearchTemplateQuery.getFilters(), IObjectData.ID, productIds);
            }
            return handleCategory(context, skuSearchTemplateQuery);
        }

        log.info("getSPUByFilters dataSql sql:{} ", dataSql);
        SearchTemplateQuery salesOrderProSearchTemplateQuery = null;
        SearchTemplateQuery skuSearchTemplateQuery;
        try {
            if (StringUtils.isNotBlank(arg.getSalesOrderProSearchQueryInfo())) {
                salesOrderProSearchTemplateQuery = (SearchTemplateQuery) SearchTemplateQuery.fromJsonString(arg.getSalesOrderProSearchQueryInfo());
                salesOrderProSearchTemplateQuery.setOffset(0);
                salesOrderProSearchTemplateQuery.setLimit(0);
            }
            skuSearchTemplateQuery = (SearchTemplateQuery) SearchTemplateQuery.fromJsonString(arg.getProductSearchQueryInfo());
        } catch (Exception e) {
            log.error("get_product_list searchTemplateQuery format error.tenantId:{} arg:{}", context.getTenantId(), arg, e);
            throw new ValidateException("SearchTemplateQuery format error");
        }
        if (StringUtils.isNotBlank(dataSql) && StringUtils.isNotBlank(arg.getSalesOrderProSearchQueryInfo())) {
            SearchUtil.fillFilterInBySql(salesOrderProSearchTemplateQuery.getFilters(), SalesOrderConstants.SalesOrderProductField.PRODUCT_ID.getApiName(), SearchUtil.FIELD_VALUE_TYPE_SQL, dataSql);
        }
        if (StringUtils.isNotBlank(arg.getSalesOrderProSearchQueryInfo())) {
            QueryResult<IObjectData> salesOrderProResult = metaDataFindServiceExt.findBySearchQuery(context.getUser(), Utils.SALES_ORDER_PRODUCT_API_NAME, salesOrderProSearchTemplateQuery);
            List<String> skuIds = salesOrderProResult.getData().stream().map(o -> o.get(SalesOrderConstants.SalesOrderProductField.PRODUCT_ID.getApiName()).toString()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(skuIds)) {
                return new ProductListModule.Result(Lists.newArrayList(), 0);
            }
            if (CollectionUtils.isNotEmpty(productIds)) {
                skuIds.retainAll(productIds);
            }
            if (CollectionUtils.isEmpty(skuIds)) {
                return new ProductListModule.Result(Lists.newArrayList(), 0);
            }
            SearchUtil.fillFilterIn(skuSearchTemplateQuery.getFilters(), IObjectData.ID, skuIds);
        }
        return handleCategory(context, skuSearchTemplateQuery);
    }

    @NotNull
    private ProductListModule.Result handleCategory(ServiceContext context, SearchTemplateQuery skuSearchTemplateQuery) {
        // 处理query里面的filters
        productCategoryService.handleCategoryFilters(context.getTenantId(), context.getUser().getUserId(), skuSearchTemplateQuery.getFilters());
        // 处理wheres里面的分类filters
        skuSearchTemplateQuery.getWheres().stream()
                .filter(k -> CollectionUtils.isNotEmpty(k.getFilters()))
                .forEach(k -> productCategoryService.handleCategoryFilters(context.getTenantId(), context.getUser().getUserId(), k.getFilters()));
        SearchUtil.fillFilterEq(skuSearchTemplateQuery.getFilters(), "product_status", ProductConstants.Status.ON.getStatus());
        if (moduleCtrlConfigService.isOpen(MODULE_CPQ, context.getUser())) {
            SearchUtil.fillFilterEq(skuSearchTemplateQuery.getFilters(), "is_saleable", Boolean.TRUE);
        }
        QueryResult<IObjectData> skuData = serviceFacade.findBySearchQuery(context.getUser(), Utils.PRODUCT_API_NAME, skuSearchTemplateQuery);

        return new ProductListModule.Result(ObjectDataDocument.ofList(skuData.getData()), skuData.getTotalNumber());
    }


    @ServiceMethod("get_new_spus")
    @Deprecated
    public GetProductByCreateTime.Result getNewProducts(GetProductByCreateTime.Arg arg, ServiceContext context) {
        return getNewProductsPrivateMethod(arg, context);
    }

    @NotNull
    private GetProductByCreateTime.Result getNewProductsPrivateMethod(GetProductByCreateTime.Arg arg, ServiceContext context) {
        if (Strings.isNotBlank(arg.getPriceBookId()) && !priceBookAvailable(arg.getPriceBookId(), context.getUser())) {
            log.warn("DhtPriceBookService getNewProducts priceBook is not available arg:{}", arg);
            return new GetProductByCreateTime.Result();
        }
        String dataSql;
        String countSql;
        if (Strings.isNotBlank(arg.getPriceBookId())) {
            dataSql = ConcatenateSqlUtils.getSqlByTime(arg, context, false);
            countSql = ConcatenateSqlUtils.getSqlByTime(arg, context, true);
        } else {
            dataSql = ConcatenateSqlUtils.getSqlByTimePriceBookIdNotExist(arg, context, false);
            countSql = ConcatenateSqlUtils.getSqlByTimePriceBookIdNotExist(arg, context, true);
        }
        QueryResult<IObjectData> dataResult;
        QueryResult<IObjectData> countResult;
        try {
            log.info("getNewProducts dataSql findBySql sql:{} ", dataSql);
            log.info("getNewProducts countSql findBySql sql:{} ", countSql);
            dataResult = checkCPQFindBySql(dataSql, context.getTenantId(), Utils.SPU_API_NAME);
            countResult = checkCPQFindBySql(countSql, context.getTenantId(), Utils.SPU_API_NAME);
        } catch (MetadataServiceException e) {
            log.error("getNewProducts dataSql findBySql error. sql:{} ", dataSql, e);
            log.error("getNewProducts countSql findBySql error. sql:{} ", countSql, e);
            throw new APPException("system error.");
        }
        Integer total = countResult.getData().get(0).get("count", Integer.class);
        Integer totalPage = total % arg.getLimit() == 0 ? total / arg.getLimit() : total / arg.getLimit() + 1;
        List<IObjectData> result = sortDataList(context, dataResult);
        return new GetProductByCreateTime.Result(ObjectDataDocument.ofList(result), total, totalPage);
    }


    @ServiceMethod("batch_get_product")
    @Deprecated
    public BatchGetProductModel.Result batchGetProduct(BatchGetProductModel.Arg arg, ServiceContext context) {
        log.info("arg {},context {}", arg, context);
        if (!priceBookAvailable(arg.getPriceBookId(), context.getUser())) {
            log.warn("DhtPriceBookService batchGetProduct priceBook is not available arg:{}", arg);
            return new BatchGetProductModel.Result();
        }
        String dataSql = ConcatenateSqlUtils.getBatchGetProductSql(arg, context.getTenantId());
        QueryResult<IObjectData> dataResult;
        try {
            log.info("batchGetProduct metadata findBySql sql:{} ", dataSql);
            dataResult = objectDataService.findBySql(dataSql, context.getTenantId(), Utils.PRODUCT_API_NAME);
        } catch (MetadataServiceException e) {
            log.error("batchGetProduct metadata findBySql error. sql:{} ", dataSql, e);
            throw new APPException("system error!");
        }
        List<IObjectData> skuDataList = findDataByIds(context, dataResult);
        return new BatchGetProductModel.Result(ObjectDataDocument.ofList(skuDataList));
    }

    private List<IObjectData> findDataByIds(ServiceContext context, QueryResult<IObjectData> dataResult) {
        List<IObjectData> skuDataList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(dataResult.getData())) {
            skuDataList = serviceFacade.findObjectDataByIds(context.getTenantId(),
                    dataResult.getData().stream().map(o -> o.get("id").toString()).collect(Collectors.toList()), Utils.PRODUCT_API_NAME);
            fullProductDataList(dataResult.getData(), skuDataList);
        }
        return skuDataList;
    }

    @ServiceMethod("get_allpro_by_spuid_pricebookid")
    @Deprecated
    public SameGroupProductModel.Result getAllSkuInSpu(SameGroupProductModel.Arg arg, ServiceContext context) {
        log.info("arg {},context {}", arg, context);
        if (!priceBookAvailable(arg.getPriceBookId(), context.getUser())) {
            log.warn("DhtPriceBookService batchGetProduct priceBook is not available arg:{}", arg);
            return new SameGroupProductModel.Result();
        }
        String dataSql = ConcatenateSqlUtils.getSqlByPriceBookIdAndSkuId(context.getTenantId(), arg);
        QueryResult<IObjectData> dataResult;
        try {
            log.info("batchGetProduct findBySql sql:{} ", dataSql);
            dataResult = checkCPQFindBySql(dataSql, context.getTenantId(), Utils.PRODUCT_API_NAME);
        } catch (MetadataServiceException e) {
            log.error("batchGetProduct metadata findBySql error. sql:{} ", dataSql);
            throw new APPException("system error!");
        }
        List<IObjectData> skuDataList = findDataByIds(context, dataResult);
        return new SameGroupProductModel.Result(ObjectDataDocument.ofList(skuDataList));
    }

    @ServiceMethod("get_allpro_by_spuids_pricebookid")
    @Deprecated
    public SameGroupProductModel.Result getAllSkuInSpus(SameGroupProductModel.Arg arg, ServiceContext context) {
        log.info("arg {},context {}", arg, context);
        if (!priceBookAvailable(arg.getPriceBookId(), context.getUser())) {
            log.warn("DhtPriceBookService getAllSkuInSpus priceBook is not available arg:{}", arg);
            return new SameGroupProductModel.Result();
        }
        String dataSql = ConcatenateSqlUtils.getSqlByPriceBookIdAndSkuIds(context.getTenantId(), arg);
        QueryResult<IObjectData> dataResult;
        try {
            log.info("getAllSkuInSpus findBySql sql:{} ", dataSql);
            dataResult = checkCPQFindBySql(dataSql, context.getTenantId(), Utils.PRODUCT_API_NAME);
        } catch (MetadataServiceException e) {
            log.error("getAllSkuInSpus metadata findBySql error. sql:{} ", dataSql);
            throw new APPException("system error!");
        }
        List<IObjectData> skuDataList = findDataByIds(context, dataResult);
        return new SameGroupProductModel.Result(ObjectDataDocument.ofList(skuDataList));
    }


    @ServiceMethod("get_allpro_by_skuid_pricebookid")
    public SameGroupProductModel.DhtResult getAllSku(SameGroupProductModel.DhtArg arg, ServiceContext context) {
        if (!priceBookAvailable(arg.getPriceBookId(), context.getUser())) {
            log.warn("DhtPriceBookService getAllSku priceBook is not available arg:{}", arg);
            return new SameGroupProductModel.DhtResult();
        }
        String dataSql = ConcatenateSqlUtils.getSqlByPriceBookIdAndProductId(context.getTenantId(), arg);
        QueryResult<IObjectData> dataResult;
        try {
            log.info("batchGetProduct findBySql sql:{} ", dataSql);
            dataResult = checkCPQFindBySql(dataSql, context.getTenantId(), Utils.PRODUCT_API_NAME);
        } catch (MetadataServiceException e) {
            log.error("batchGetProduct metadata findBySql error. sql:{} ", dataSql);
            throw new APPException("system error!");
        }
        List<IObjectData> skuDataList = findDataByIds(context, dataResult);
        return new SameGroupProductModel.DhtResult(ObjectDataDocument.ofList(skuDataList));
    }


    @ServiceMethod("get_allpro_by_skuids_pricebookid")
    public SameGroupProductModel.DhtResult getAllSkus(SameGroupProductModel.DhtArg arg, ServiceContext context) {
        if (!priceBookAvailable(arg.getPriceBookId(), context.getUser())) {
            log.warn("DhtPriceBookService getAllSkus priceBook is not available arg:{}", arg);
            return new SameGroupProductModel.DhtResult();
        }
        String dataSql = ConcatenateSqlUtils.getSqlByPriceBookIdAndProductIds(context.getTenantId(), arg);
        QueryResult<IObjectData> dataResult;
        try {
            log.info("getAllSkuInSpus findBySql sql:{} ", dataSql);
            dataResult = checkCPQFindBySql(dataSql, context.getTenantId(), Utils.PRODUCT_API_NAME);
        } catch (MetadataServiceException e) {
            log.error("getAllSkuInSpus metadata findBySql error. sql:{} ", dataSql);
            throw new APPException("system error!");
        }
        List<IObjectData> skuDataList = findDataByIds(context, dataResult);
        return new SameGroupProductModel.DhtResult(ObjectDataDocument.ofList(skuDataList));
    }



    @ServiceMethod("get_spu_by_filters")
    @Deprecated
    public PagingGetProductsModel.Result getSPUByFilters(PagingGetProductsModel.Arg arg, ServiceContext context) {
        if (Strings.isNotBlank(arg.getPriceBookId()) && !priceBookAvailable(arg.getPriceBookId(), context.getUser())) {
            log.warn("DhtPriceBookService getNewProducts priceBook is not available arg:{}", arg);
            return new PagingGetProductsModel.Result();
        }
        if (CollectionUtils.isNotEmpty(arg.getOrderList())) {
            IObjectDescribe object = serviceFacade.findObject(context.getTenantId(), Utils.SPU_API_NAME);
            Map<String, IFieldDescribe> fieldDescribeMap = object.getFieldDescribeMap();
            arg.getOrderList().removeIf(o -> !fieldDescribeMap.containsKey(o.getFieldName()) || "custom".equals(fieldDescribeMap.get(o.getFieldName()).getType()));
        }
        String dataSql;
        String countSql;
        Set<String> categorySet = null;
        if (Strings.isNotBlank(arg.getCategory())) {
            categorySet = productCategoryService.getCategoryChildrenCategoryCodesContainSelf(context.getTenantId(), context.getUser().getUserId(), arg.getCategory());
        }
        if (Strings.isNotBlank(arg.getPriceBookId())) {
            dataSql = ConcatenateSqlUtils.getSpuByPriceBookId(categorySet, arg, context, false);
            countSql = ConcatenateSqlUtils.getSpuByPriceBookId(categorySet, arg, context, true);
        } else {
            dataSql = ConcatenateSqlUtils.getSpuByFilters(categorySet, arg, context, false);
            countSql = ConcatenateSqlUtils.getSpuByFilters(categorySet, arg, context, true);
        }
        QueryResult<IObjectData> spuResult;
        QueryResult<IObjectData> countResult;
        try {
            log.info("getSPUByFilters dataSql sql:{} ", dataSql);
            log.info("getSPUByFilters countSql sql:{} ", countSql);
            spuResult = checkCPQFindBySql(dataSql, context.getTenantId(), Utils.SPU_API_NAME);
            countResult = checkCPQFindBySql(countSql, context.getTenantId(), Utils.SPU_API_NAME);
        } catch (MetadataServiceException e) {
            log.error("getSPUByFilters dataSql error. sql:{} ", dataSql, e);
            log.error("getSPUByFilters countSql error. sql:{} ", countSql, e);
            throw new APPException("system error!");
        }
        List<IObjectData> spuDataList = sortDataList(context, spuResult);
        return new PagingGetProductsModel.Result(ObjectDataDocument.ofList(spuDataList), countResult.getData().get(0).get("count", Integer.class));
    }

    @ServiceMethod("spu_list")
    @Deprecated
    public SPUListModule.Result spuList(SPUListModule.Arg arg, ServiceContext context) {
        if (Strings.isNotBlank(arg.getPriceBookId()) && !priceBookAvailable(arg.getPriceBookId(), context.getUser())) {
            log.warn("DhtPriceBookService getNewProducts priceBook is not available arg:{}", arg);
            return new SPUListModule.Result();
        }
        String dataSql;
        if (Strings.isNotBlank(arg.getPriceBookId())) {
            dataSql = ConcatenateSqlUtils.getSpuByPriceBookIdTODO(arg, context);
        } else {
            dataSql = ConcatenateSqlUtils.getSpuByFiltersTODO(arg, context);
        }
        log.info("getSPUByFilters dataSql sql:{} ", dataSql);
        dataSql = handleSqlForCPQ(dataSql, context.getUser());
        SearchTemplateQuery searchTemplateQuery;
        try {
            searchTemplateQuery = (SearchTemplateQuery) SearchTemplateQuery.fromJsonString(arg.getSearchQueryInfo());

        } catch (Exception e) {
            log.error("get_spu_by_filters searchTemplateQuery format error.tenantId:{} arg:{}", context.getTenantId(), arg, e);
            throw new ValidateException("SearchTemplateQuery format error");
        }
        // 处理query里面的filters
        productCategoryService.handleCategoryFilters(context.getTenantId(), context.getUser().getUserId(), searchTemplateQuery.getFilters());
        // 处理wheres里面的分类filters
        searchTemplateQuery.getWheres().stream()
                .filter(k -> CollectionUtils.isNotEmpty(k.getFilters()))
                .forEach(k -> productCategoryService.handleCategoryFilters(context.getTenantId(), context.getUser().getUserId(), k.getFilters()));

        List<IFilter> filters = searchTemplateQuery.getFilters();
        SearchUtil.fillFilterInBySql(filters, IObjectData.ID, SearchUtil.FIELD_VALUE_TYPE_SQL, dataSql);

        QueryResult<IObjectData> bySearchQuery = serviceFacade.findBySearchQuery(context.getUser(), Utils.SPU_API_NAME, searchTemplateQuery);

        List<IObjectData> spuDataList = sortDataList(context, bySearchQuery);
        return new SPUListModule.Result(ObjectDataDocument.ofList(spuDataList), bySearchQuery.getTotalNumber());
    }

    @NotNull
    private Set<String> getSkuData(ServiceContext context, List<String> spuIds) {
        Set<String> spuIdsByIsPackage = Sets.newHashSet();
        try {
            List<Map> spuIdsByIsPackageResult = objectDataService.findBySql(context.getTenantId(), getIsPackageSPUIdsSql(context.getTenantId(), spuIds));
            spuIdsByIsPackage.addAll(spuIdsByIsPackageResult.stream().map(o -> o.get("spu_id").toString()).collect(Collectors.toSet()));
        } catch (MetadataServiceException e) {
            log.error("DhtPriceBookServiceCopy getIsPackageSPUIdsSql,tenantId:{},spuIds:{}", context.getTenantId(), spuIds);
        }
        return spuIdsByIsPackage;
    }


    @ServiceMethod("get_floor_price_products")
    public FloorPriceProductModel.Result getFloorPriceProducts(FloorPriceProductModel.Arg arg, ServiceContext context) {
        String dataSql;
        FloorPriceProductModel.Result result = new FloorPriceProductModel.Result();
        boolean isAvailableRangeEnabled = bizConfigThreadLocalCacheService.isAvailableRangeEnabled(context.getTenantId());
        if (isAvailableRangeEnabled) {
            if (Strings.isEmpty(arg.getAccountId())) {
                return result;
            }
            Tuple<Boolean, Set<String>> availableTuple = dhtPriceBookService.matchAvailableProduct(context,
                    arg.getAccountId(), arg.getPriceBookId(), arg.getAvailableRangeId());
            if (!availableTuple.getKey()) {
                return result;
            }
            if (Strings.isEmpty(arg.getPriceBookId())) {
                dataSql = ConcatenateSqlUtils.getFloorskusSql(context.getTenantId(), arg.getSpuIds(),
                        availableTuple.getValue());
            } else {
                dataSql = ConcatenateSqlUtils.getFloorPriceProductsSql(context.getTenantId(), arg.getSpuIds(),
                        arg.getPriceBookId(), availableTuple.getValue());
            }
        } else {
            if (Strings.isNotBlank(arg.getPriceBookId()) && !priceBookAvailable(arg.getPriceBookId(), context.getUser())) {
                log.warn("DhtPriceBookService getFloorPriceProducts priceBook is not available arg:{}", arg);
                return result;
            }
            if (Strings.isNotBlank(arg.getPriceBookId())) {
                dataSql = ConcatenateSqlUtils.getFloorPriceProductsSql(context.getTenantId(), arg.getSpuIds(),
                        arg.getPriceBookId(), null);
            } else {
                dataSql = ConcatenateSqlUtils.getFloorskusSql(context.getTenantId(), arg.getSpuIds(), null);
            }
        }
        QueryResult<IObjectData> dataResult;
        try {
            log.info("getFloorPriceProducts findBySql sql:{} ", dataSql);
            dataResult = checkCPQFindBySql(dataSql, context.getTenantId(), Utils.PRODUCT_API_NAME);
        } catch (MetadataServiceException e) {
            log.error("getFloorPriceProducts findBySql error. sql:{" + dataSql + "}", e);
            throw new APPException("system error.");
        }
        List<IObjectData> skuDataList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(dataResult.getData())) {
            skuDataList = serviceFacade.findObjectDataByIds(context.getTenantId(),
                    dataResult.getData().stream().map(o -> o.get("id").toString()).collect(Collectors.toList()), Utils.PRODUCT_API_NAME);
            if (arg.getPriceBookId() != null) {
                fullProductDataList(dataResult.getData(), skuDataList, arg.getPriceBookId());
            }
            if (isAvailableRangeEnabled && Strings.isEmpty(arg.getPriceBookId())
                    && bizConfigThreadLocalCacheService.isPriceBookEnabled(context.getTenantId())) {
                dhtPriceBookService.fillPriceBookInfo(context, skuDataList, arg.getAccountId());
            }
        }
        result.setDataList(ObjectDataDocument.ofList(skuDataList));
        return result;
    }


    @ServiceMethod("getProductPkgDataByIds")
    public ProductPkgModule.Result getProductPkgDataByIds(ProductPkgModule.Arg arg, ServiceContext context) {
        String subProductCatalogObj = "SubProductCatalogObj";
        Map<String, IObjectDescribe> describeMap = serviceFacade.findObjects(context.getTenantId(), Lists.newArrayList(Utils.SUBPRODUCT_API_NAME, subProductCatalogObj));

        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(500);
        List<IFilter> filters = searchQuery.getFilters();
        SearchUtil.fillFilterIn(filters, "master_product_id", arg.getProductPkgIds());
        SearchUtil.fillFilterEq(filters, "is_deleted", 0);

        QueryResult<IObjectData> subProductList = serviceFacade.findBySearchQuery(context.getUser(), describeMap.get(Utils.SUBPRODUCT_API_NAME), Utils.SUBPRODUCT_API_NAME, searchQuery);

        QueryResult<IObjectData> subProductCatalogList = serviceFacade.findBySearchQuery(context.getUser(), describeMap.get(subProductCatalogObj), subProductCatalogObj, searchQuery);

        List<IObjectData> productPackageList = serviceFacade.findObjectDataByIds(context.getTenantId(), arg.getProductPkgIds(), Utils.PRODUCT_API_NAME);

        if (CollectionUtils.isNotEmpty(subProductList.getData())) {
            fillData(context, Utils.SUBPRODUCT_API_NAME, describeMap, subProductList);
            fillProduct(subProductList.getData(), context.getUser());
        }
        if (CollectionUtils.isNotEmpty(subProductCatalogList.getData())) {
            fillData(context, subProductCatalogObj, describeMap, subProductCatalogList);
        }

        return fillResult(productPackageList, subProductList, subProductCatalogList, arg.getIsNeedPkgData());

    }

    @ServiceMethod("getHasPackageProductSpuIds")
    public CheckHasPackageProductSPUModel.Result checkHasPackageProductSpuIds(ServiceContext context, CheckHasPackageProductSPUModel.Arg arg) {
        if (CollectionUtils.isEmpty(arg.getSpuIds())) {
            return CheckHasPackageProductSPUModel.Result.builder().hasPackageProductSpuIds(Lists.newArrayList()).build();
        }
        String sql = checkHasPackageProductSpuIdsSql(context.getTenantId(), arg.getSpuIds());
        List<Map> result = Lists.newArrayList();
        try {
            result = checkCPQFindBySql(sql, context.getTenantId());
        } catch (MetadataServiceException e) {
            log.error("checkHasPackageProductSpuIds context:{} arg:{}", context, arg, e);
        }
        return CheckHasPackageProductSPUModel.Result.builder().hasPackageProductSpuIds(result.stream().map(o -> o.get("spu_id").toString()).collect(Collectors.toList())).build();

    }

    @ServiceMethod("getMultiUnitBySpuIdsOrSkuIds")
    public MultiUnitModule.Result getMultiUnitBySpuIdsOrSkuIds(ServiceContext context, MultiUnitModule.Arg arg) {
        if (arg.isEmpty()) {
            log.warn("getMultiUnitBySpuIdsOrSkuIds arg is empty:arg:{}", arg);
            return new MultiUnitModule.Result();
        }
        String sql = getMultiUnitSqlBySpuIdsOrSkuIds(context.getTenantId(), arg);
        log.info("getMultiUnitBySpuIdsOrSkuIds tenantId:{} sql:{}", context.getTenantId(), sql);
        QueryResult<IObjectData> result = new QueryResult<>();
        try {
            result = objectDataService.findBySql(sql, context.getTenantId(), Utils.MULTI_UNIT_RELATED_API_NAME);
        } catch (MetadataServiceException e) {
            log.error("getMultiUnitBySpuIdsOrSkuIds:tenantId:{},arg:{}", context.getTenantId(), arg, e);
        }
        if (CollectionUtils.isNotEmpty(result.getData())) {
            List<String> unitIds = result.getData().stream().map(o -> o.get("unit_id").toString()).distinct().collect(Collectors.toList());
            List<INameCache> unitNames = serviceFacade.findRecordName(ActionContextExt.of(context.getUser()).getContext(), Utils.UNIT_INFO_API_NAME, unitIds);
            Map<String, String> idToName = unitNames.stream().collect(Collectors.toMap(INameCache::getId, INameCache::getName));
            result.getData().forEach(o -> o.set("unit_id__r", idToName.get(o.get("unit_id").toString())));
        }

        return MultiUnitModule.Result.builder().result(ObjectDataDocument.ofList(result.getData())).build();
    }


    private ProductPkgModule.Result fillResult(List<IObjectData> productPackageList, QueryResult<IObjectData> subProductList, QueryResult<IObjectData> subProductCatalogList, Boolean isNeedPkgData) {
        ProductPkgModule.Result result = new ProductPkgModule.Result();
        List<ProductPkgModule.PkgObjectData> pkgObjectDataList = Lists.newArrayList();
        Map<Object, List<IObjectData>> subProductMap = subProductList.getData().stream().collect(Collectors.groupingBy(o -> o.get("master_product_id")));
        Map<Object, List<IObjectData>> subProductCatalogMap = subProductCatalogList.getData().stream().collect(Collectors.groupingBy(o -> o.get("master_product_id")));


        for (IObjectData productPkgObjectData : productPackageList) {
            ProductPkgModule.PkgObjectData pkgObjectData = new ProductPkgModule.PkgObjectData();
            pkgObjectData.setProductPkgId(productPkgObjectData.getId());
            ProductPkgModule.ObjectData productPkgData = new ProductPkgModule.ObjectData(Utils.PRODUCT_API_NAME, null, ObjectDataDocument.ofList(Lists.newArrayList(productPkgObjectData)));
            ProductPkgModule.ObjectData subProductData = new ProductPkgModule.ObjectData(Utils.SUBPRODUCT_API_NAME, false, ObjectDataDocument.ofList(subProductMap.get(productPkgObjectData.getId())));
            ProductPkgModule.ObjectData subProductCatalogData = new ProductPkgModule.ObjectData("SubProductCatalogObj", true, ObjectDataDocument.ofList(subProductCatalogMap.get(productPkgObjectData.getId())));
            if (isNeedPkgData) {
                pkgObjectData.setDataList(Lists.newArrayList(productPkgData, subProductData, subProductCatalogData));
            } else {
                pkgObjectData.setDataList(Lists.newArrayList(subProductData, subProductCatalogData));
            }
            pkgObjectDataList.add(pkgObjectData);
        }
        result.setResult(pkgObjectDataList);
        return result;
    }

    private void fillProduct(List<IObjectData> subProductList, User user) {

        List<String> skuIds = subProductList.stream().map(o -> o.get("lookup_product_id").toString()).collect(Collectors.toList());
        List<IObjectData> lookupProductDataList = serviceFacade.findObjectDataByIds(user.getTenantId(), skuIds, Utils.PRODUCT_API_NAME);
        IObjectDescribe skuDescribe = serviceFacade.findObject(user.getTenantId(), Utils.PRODUCT_API_NAME);

        transformData4ViewService.batchTransformDataForView(user, skuDescribe, lookupProductDataList);
        Map<String, IObjectData> skuIdToData = lookupProductDataList.stream().collect(Collectors.toMap(o -> o.getId(), o -> o));
        for (IObjectData subProduct : subProductList) {
            IObjectData lookupData = skuIdToData.get(subProduct.get("lookup_product_id").toString());
            subProduct.set("lookup_product_id__ro", ObjectDataDocument.of(lookupData));
        }

    }

    private void fillData(ServiceContext context, String describeApiName, Map<String, IObjectDescribe> describeMap, QueryResult<IObjectData> dataQueryResult) {
        try {
            ParallelUtils.createParallelTask()
                    .submit(() -> infraServiceFacade.fillQuoteFieldValue(context.getUser(), dataQueryResult.getData(), describeMap.get(describeApiName), false))
                    .submit(() -> serviceFacade.fillObjectDataWithRefObject(describeMap.get(describeApiName), dataQueryResult.getData(), context.getUser()))
                    .await(15, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            log.error("findSalesOrderProductSubProducts parallelTask error: tenantId:{}", context.getTenantId(), e);
        }
    }

    private void fullProductDataList(List<IObjectData> dataList, List<IObjectData> skuDataList) {
        fullProductDataList(dataList, skuDataList, null);
    }

    private void fullProductDataList(List<IObjectData> dataList, List<IObjectData> skuDataList, String priceBookId) {
        Map<String, IObjectData> idToData = dataList.stream().collect(Collectors.toMap(o -> o.get("id").toString(), o -> o));
        skuDataList.forEach(o -> {
            o.set("pricebookprod_code", idToData.get(o.getId()).get("pricebookprod_code"));
            o.set("pricebook_price", idToData.get(o.getId()).get("pricebook_price"));
            o.set("pricebook_product_id", idToData.get(o.getId()).get("pricebook_product_id"));
            o.set("pricebook_product_discount", idToData.get(o.getId()).get("pricebook_product_discount"));
            o.set("pricebook_id", priceBookId);
        });
    }


    public Boolean priceBookAvailable(String priceBookId, User user) {
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(1);
        List<IFilter> filters = searchQuery.getFilters();
        SearchUtil.fillFilterEq(filters, "_id", priceBookId);
        SearchUtil.fillFilterEq(filters, "is_deleted", 0);
        SearchUtil.fillFilterEq(filters, "life_status", "normal");
        SearchUtil.fillFilterEq(filters, "active_status", "1");
        QueryResult<IObjectData> result = serviceFacade.findBySearchQuery(user, "PriceBookObj", searchQuery);
        if (result.getData().isEmpty()) {
            return Boolean.FALSE;
        }
        Long startDate = result.getData().get(0).get("start_date", Long.class);
        Long endDate = result.getData().get(0).get("end_date", Long.class);
        if (startDate != null && new Date().before(new Date(startDate))) {
            return Boolean.FALSE;
        }
        //价目表到期时间库里存的是凌晨，应该按照23：59：59 来判断
        if (endDate != null && new Date().after(new Date(endDate + 86399999))) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }


    private String handleSqlForCPQ(String sql, User user) {
        if (moduleCtrlConfigService.isOpen(MODULE_CPQ, user)) {
            return sql.replaceAll("bp.product_status='1'", "bp.product_status = '1'\n\tand bp.is_saleable='true'");
        }
        return sql;
    }

    private QueryResult<IObjectData> checkCPQFindBySql(String querySql, String tenantId, String objectDescribeApiName) throws MetadataServiceException {
        String sql = handleSqlForCPQ(querySql, new User(tenantId, "-10000"));
        log.info("DhtPriceBookServiceCopy handleSqlForCPQ:tenantId:{},sql:{}", tenantId, sql);
        return objectDataService.findBySql(sql, tenantId, objectDescribeApiName);
    }

    private List<Map> checkCPQFindBySql(String querySql, String tenantId) throws MetadataServiceException {
        String sql = handleSqlForCPQ(querySql, new User(tenantId, "-10000"));
        log.info("DhtPriceBookServiceCopy handleSqlForCPQ:tenantId:{},sql:{}", tenantId, sql);
        return objectDataService.findBySql(tenantId, sql);
    }


    @NotNull
    private List<IObjectData> sortDataList(ServiceContext context, QueryResult<IObjectData> dataResult) {
        List<IObjectData> result = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(dataResult.getData())) {
            List<String> spuIds = dataResult.getData().stream().map(o -> Optional.ofNullable(o.get("id")).orElse(o.getId()).toString()).collect(Collectors.toList());
            Set<String> spuIdsByIsPackage = getSkuData(context, spuIds);

            result = serviceFacade.findObjectDataByIds(context.getTenantId(),
                    spuIds, Utils.SPU_API_NAME);
            Map<String, IObjectData> spuDataMap = result.stream().collect(Collectors.toMap(DBRecord::getId, o -> o));
            result = dataResult.getData().stream().map(o -> {
                IObjectData data = spuDataMap.get(Optional.ofNullable(o.get("id")).orElse(o.getId()).toString());
                data.set("is_has_package_product", spuIdsByIsPackage.contains(data.get("_id").toString()));
                return data;
            }).collect(Collectors.toList());
        }
        return result;
    }

}
