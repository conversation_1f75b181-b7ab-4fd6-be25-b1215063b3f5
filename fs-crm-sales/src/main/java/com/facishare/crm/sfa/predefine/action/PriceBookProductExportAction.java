package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.service.UnitService;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardExportAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-08-01 10:54
 * @instruction
 */
public class PriceBookProductExportAction extends StandardExportAction {
    private UnitService unitService = SpringUtil.getContext().getBean(UnitService.class);

    @Override
    protected void fillDataList(User user, IObjectDescribe describe, List<IFieldDescribe> fields, List<IObjectData> dataList) {
        super.fillDataList(user, describe, fields, dataList);
        unitService.fillMultipleUnitForPriceBookProduct(user, ObjectDataDocument.ofList(dataList));
    }
}
