package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.paas.appframework.core.predef.service.dto.duplicatedSearch.GetResult;
import com.facishare.paas.metadata.api.data.IDuplicatedSearch;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Optional;

public class LeadsDuplicateSearchController extends SFADuplicateSearchController {
    @Override
    protected GetResult.Result doService(GetResult.Arg arg) {
        GetResult.Result result = super.doService(arg);
        List<GetResult.RelatedSearchInfo> relatedSearchInfos = result.getRelatedSearchInfos();
        if (CollectionUtils.isNotEmpty(relatedSearchInfos)) {
            Optional<GetResult.RelatedSearchInfo> op = relatedSearchInfos.stream().filter(r -> r.getObjectDescribe() != null &&
                    r.getObjectDescribe().getOrDefault("api_name", "").equals(SFAPreDefineObject.Account.getApiName())).findFirst();
            if (op.isPresent()) {
                GetResult.RelatedSearchInfo relatedSearchInfo = op.get();
                if (relatedSearchInfo.getTotal() > 0) {
                    IDuplicatedSearch duplicatedSearchRule = serviceFacade.
                            findDuplicatedSearchByApiNameAndType(controllerContext.getTenantId(), SFAPreDefineObject.Leads.getApiName(), IDuplicatedSearch.Type.NEW, false);
                    IDuplicatedSearch.RulesDef rulesDef = duplicatedSearchRule.getUseableRules();
                    Optional<IDuplicatedSearch.RelatedDescribe> describeOptional =
                            rulesDef.getRelatedDescribes().stream().filter(r -> SFAPreDefineObject.Account.getApiName().equals(r.getDescribeApiName())).findFirst();
                    if (!(describeOptional.isPresent() && describeOptional.get().getAllowCreateWhenDuplicated())) {
                        relatedSearchInfo.setMatchType(IDuplicatedSearch.Policy.PRECISE);
                        result.setMatchType(IDuplicatedSearch.Policy.PRECISE);
                        result.setKeepSave(false);
                    }
                }
            }
        }
        return result;
    }
}
