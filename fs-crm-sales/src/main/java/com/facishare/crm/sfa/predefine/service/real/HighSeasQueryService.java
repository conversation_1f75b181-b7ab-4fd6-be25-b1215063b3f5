package com.facishare.crm.sfa.predefine.service.real;

import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/1/15
 */
public interface HighSeasQueryService {

    /**
     * @param tenantId
     * @param highSeasIds
     * @return
     */
    QueryResult<IObjectData> getHighSeasPermissionList(String tenantId, List<String> highSeasIds);


    /**
     * 根据employeeid获取公海id
     *
     * @param dataIds
     * @param type 1 员工   2 部门
     * @return
     */
    QueryResult<IObjectData> getHighSeasIdByDataIds(String tenantId, List<String> dataIds,Integer type);

}
