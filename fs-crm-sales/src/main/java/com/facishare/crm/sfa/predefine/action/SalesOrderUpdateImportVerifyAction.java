package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.utilities.util.SalesOrderUtil;
import com.facishare.crm.sfa.utilities.util.imports.ImportSoUtil;
import com.facishare.paas.appframework.core.predef.action.StandardUpdateImportVerifyAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;

import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2019/10/31 20:05
 * @instruction
 */
@Slf4j
public class SalesOrderUpdateImportVerifyAction extends StandardUpdateImportVerifyAction {

    @Override
    protected List<IFieldDescribe> getValidImportFields() {
        List<IFieldDescribe> validImportFields = super.getValidImportFields();
        validImportFields.removeIf(o-> ImportSoUtil.SALES_ORDER_UPDATE_IMPORT_FILTER_FIELD.contains(o.getApiName()));
        validImportFields.removeIf(o-> ImportSoUtil.SALES_ORDER_FILTER_DELIVERY_FIELD.contains(o.getApiName()));
        SalesOrderUtil.removeNewInvoiceFields(validImportFields);
        return validImportFields;
    }

}
