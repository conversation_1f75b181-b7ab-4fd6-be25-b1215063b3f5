package com.facishare.crm.sfa.model.Enum;


public enum FeedExtDataTypeEnum {
    KAO_QIN(1, "考勤"),
    CRM_CUSTOMER(2, "CRM 客户"),
    CRM_CONTACT(3, "CRM 联系人"),
    CRM_FEED_TAG(4, "CRM Feed标签"),
    CRM_VISIT(5, "CRM 拜访"),
    CRM_SALES_CLUE(6, "线索"),
    CRM_PRODUCT(7, "产品"),
    CRM_PAYMENT(8, "回款"),
    CRM_REFUND(9, "退款"),
    CRM_SALE_ACTION(10, "销售流程"),
    CRM_OPPORTUNITY(11, "机会"),
    CRM_BILL(12, "开票"),
    CRM_TRADE(13, "成交"),
    CRM_CUSTOMER_ORDER(14, "订单"),
    CRM_RETURN_ORDER(15, "退货单"),
    CRM_VISIT_ACTISTUDYON(16, "拜访动作"),
    CRM_INVENTORY_ACTION(17, "盘点动作"),
    CRM_CONTRACT(18, "合同"),
    CRM_SALES_CLUE_POOL(19, "线索池"),
    CRM_HIGH_SEAS(20, "公海"),
    CRM_COMPETITOR(21, "竞争对手"),
    CRM_MARKETING_EVENT(22, "市场活动"),
    CRM_INVENTORY(23, "盘点"),
    USER_DEFINED_OBJECT(24, "自定义对象"),
    CRM_TRADE_PRODUCT(28, "订单产品");


    private int code;
    private String text;

    FeedExtDataTypeEnum(int code, String text) {
        this.code = code;
        this.text = text;
    }

    public int getCode() {
        return code;
    }
}
