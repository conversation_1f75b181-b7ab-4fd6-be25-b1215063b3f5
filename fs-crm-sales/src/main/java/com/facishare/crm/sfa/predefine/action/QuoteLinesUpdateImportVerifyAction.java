package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.utilities.constant.QuoteConstants;
import com.facishare.paas.appframework.core.predef.action.StandardUpdateImportVerifyAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.google.common.collect.Lists;

import java.util.List;

public class QuoteLinesUpdateImportVerifyAction extends StandardUpdateImportVerifyAction {
    private List<String> removeFields = Lists.newArrayList(ObjectDataExt.EXTEND_OBJ_DATA_ID,
            QuoteConstants.QuoteLinesField.PARENT_PROD_PACKAGE_ID.getApiName(),
            QuoteConstants.QuoteLinesField.PARENT_PROD_PKG.getApiName(),
            QuoteConstants.QuoteLinesField.PROD_PKG.getApiName(),
            QuoteConstants.QuoteLinesField.SUB_PRODUCT_ID.getApiName(),
            QuoteConstants.QuoteLinesField.PRICEBOOKPRODUCTID.getApiName(),
            QuoteConstants.QuoteLinesField.ROOT_PROD_PKG.getApiName()
            , QuoteConstants.QuoteLinesField.BOM_ID.getApiName());

    @Override
    protected List<IFieldDescribe> getValidImportFields() {
        List<IFieldDescribe> fields = super.getValidImportFields();
        fields.removeIf(f -> removeFields.contains(f.getApiName()));
        return fields;
    }
}
