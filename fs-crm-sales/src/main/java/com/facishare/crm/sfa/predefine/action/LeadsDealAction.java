package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.model.Enum.LeadsBizStatusEnum;
import com.facishare.crm.sfa.model.Enum.LeadsStatusEnum;
import com.facishare.crm.sfa.model.Enum.SessionBOCItemKeys;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.exception.SFABusinessException;
import com.facishare.crm.sfa.predefine.exception.SFAErrorCode;
import com.facishare.crm.sfa.predefine.service.ObjectPoolService;
import com.facishare.crm.sfa.predefine.service.QiXinTodoService;
import com.facishare.crm.sfa.predefine.service.SFALogService;
import com.facishare.crm.sfa.predefine.service.model.SFALogModels;
import com.facishare.crm.sfa.predefine.service.task.LeadsOverTimeTaskService;
import com.facishare.crm.sfa.utilities.constant.LeadsConstants;
import com.facishare.crm.sfa.utilities.proxy.model.FeedsModel;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.crm.sfa.utilities.util.LeadsUtils;
import com.facishare.crmcommon.util.SearchUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.BaseObjectApprovalAction;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.flow.ApprovalFlowStartResult;
import com.facishare.paas.appframework.flow.mq.WorkflowProducer;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.IParamForm;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ParamForm;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import joptsimple.internal.Strings;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.*;

@Slf4j
public class LeadsDealAction extends BaseObjectApprovalAction<LeadsDealAction.Arg, LeadsDealAction.Result> {
    private LeadsOverTimeTaskService leadsOverTimeTaskService = SpringUtil.getContext().getBean(LeadsOverTimeTaskService.class);
    private QiXinTodoService qiXinTodoService = SpringUtil.getContext().getBean(QiXinTodoService.class);
    private ObjectPoolService objectPoolService = SpringUtil.getContext().getBean(ObjectPoolService.class);
    private SFALogService sfaLogService = SpringUtil.getContext().getBean(SFALogService.class);
    private static final IObjectDataService objectDataService = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);
    private List<String> systemButtonParamFormApiNameList = Lists.newArrayList("form_completed_result", "form_send_event", "form_close_reason", "form_close_reason__o");

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        if (arg.getOperationType() == 1) {
            return Lists.newArrayList(ObjectAction.FOLLOW_UP.getActionCode());
        }
        if (arg.getOperationType() == 2) {
            return Lists.newArrayList(ObjectAction.CLOSE.getActionCode());
        }
        return Lists.newArrayList(ObjectAction.CLOSE.getActionCode());
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Lists.newArrayList(arg.getLeadsId());
    }

    @Override
    protected void doFunPrivilegeCheck() {
    }

    @Override
    protected void before(Arg arg) {
        if(StringUtils.isBlank(arg.getLeadsId()) && CollectionUtils.notEmpty(arg.getDataIds())) {
            arg.setLeadsId(arg.getDataIds().get(0));
        }
        initButton();
        if(RequestUtil.isMobileRequest() && RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_710) && udefButton != null) {
            List<Map> paramForm = udefButton.getParamForm();
            if (!CollectionUtils.empty(paramForm)) {
                List<IParamForm> paramFormList = ParamForm.fromList(paramForm);
                List<IParamForm> udefParamFormList = paramFormList.stream().filter(x -> !systemButtonParamFormApiNameList.contains(x.getApiName())).collect(Collectors.toList());
                if(CollectionUtils.notEmpty(udefParamFormList)) {
                    throw new SFABusinessException(SFAErrorCode.CLIENT_UPGRADE_PROMPT);
                }
            }
        }
        if(arg.getArgs() != null) {
            IObjectData argObjectData = arg.getArgs().toObjectData();
            arg.setDealResult(AccountUtil.getStringValue(argObjectData, "form_completed_result", ""));
            arg.setSendSaleEvent(AccountUtil.getBooleanValue(argObjectData, "form_send_event", false));
            arg.setCloseReason(AccountUtil.getStringValue(argObjectData, "form_close_reason", ""));
            arg.setCloseReason__o(AccountUtil.getStringValue(argObjectData, "form_close_reason__o", ""));
            arg.setOperationType(1);
        } else {
            ObjectDataDocument objectDataDocument = new ObjectDataDocument();
            objectDataDocument.put("form_completed_result", arg.getDealResult());
            objectDataDocument.put("form_send_event", arg.isSendSaleEvent());
            objectDataDocument.put("form_close_reason", arg.getCloseReason());
            objectDataDocument.put("form_close_reason__o", arg.getCloseReason__o());
            arg.setArgs(objectDataDocument);
        }
        super.before(arg);
        IObjectData leadsData;
        if (!CollectionUtils.notEmpty(dataList)) {
            leadsData = serviceFacade.findObjectData(actionContext.getTenantId(), arg.getLeadsId(), objectDescribe);
        } else {
            leadsData = dataList.get(0);
        }
        if (leadsData == null) {
            throw new ValidateException(String.format(I18N.text(SFA_HAS_BEEN_DELETED), I18N.text("LeadsObj.attribute.self.display_name")));
        }
        if (!(leadsData.get("biz_status", String.class).equals(LeadsBizStatusEnum.UN_PROCESSED.getCode()) ||
                leadsData.get("biz_status", String.class).equals(LeadsBizStatusEnum.PROCESSED.getCode()) ||
                leadsData.get("biz_status", String.class).equals(LeadsBizStatusEnum.CLOSED.getCode()))) {
            throw new ValidateException(String.format(I18N.text(SFA_OBJECT_CANT_DO_THIS_JOB),
                    I18N.text("LeadsObj.attribute.self.display_name")));
        }
        switch (arg.getOperationType()) {
            case 1:
                if (StringUtils.isEmpty(arg.dealResult)) {
                    throw new ValidateException(I18N.text(SFA_PROCESS_RST_CANT_BE_NULL));
                }
                break;
            case 2:
                IFieldDescribe field = objectDescribe.getFieldDescribe("close_reason");
                if (field != null && field.isActive() && StringUtils.isEmpty(arg.closeReason)) {
                    throw new ValidateException(I18N.text(SFA_INVALID_REASON_CANT_BE_EMPTY));
                }
                break;
            default:
                throw new ValidateException(I18N.text(SFA_CONFIG_PARAMETER_ERROR));
        }
    }

    @Override
    protected Result doAct(Arg arg) {
        Result result = Result.builder().build();
        LeadsStatusEnum status = LeadsStatusEnum.DEALED;
        LeadsBizStatusEnum bizStatus = LeadsBizStatusEnum.PROCESSED;
        if (arg.getOperationType() == 2) {
            status = LeadsStatusEnum.CLOSED;
            bizStatus = LeadsBizStatusEnum.CLOSED;
        }
        String closeReason = arg.getCloseReason();
        if (closeReason == null) {
            closeReason = "";
        } else if ("other".equals(closeReason) && !Strings.isNullOrEmpty(arg.getCloseReason__o())) {
            closeReason += arg.getCloseReason__o();
        }
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), Lists.newArrayList(arg.getLeadsId()), SFAPreDefineObject.Leads.getApiName());
        if (CollectionUtils.notEmpty(objectDataList)) {

            List<ObjectDataDocument> objectDataDocumentList = ObjectDataDocument.ofList(objectDataList);

            for (ObjectDataDocument objectData : objectDataDocumentList) {
                objectData.put("leads_status", status.getCode());
                objectData.put("biz_status", bizStatus.getCode());
                objectData.put("completed_result", arg.getDealResult());
                objectData.put("last_modified_by", Lists.newArrayList(actionContext.getUser().getUserId()));
                objectData.put("last_modified_time", System.currentTimeMillis());
                objectData.put("is_overtime", false);
                if (bizStatus == LeadsBizStatusEnum.CLOSED) {
                    objectData.put("close_reason", closeReason);
                }
            }
            objectDataList = objectDataDocumentList.stream().map((x) -> x.toObjectData()).collect(Collectors.toList());
        }
        List<String> updateFieldList = Lists.newArrayList("leads_status", "biz_status", "completed_result", "last_modified_by",
                "last_modified_time", "is_overtime");
        if (bizStatus == LeadsBizStatusEnum.CLOSED) {
            updateFieldList.add("close_reason");
        }

        try {
            serviceFacade.batchUpdateByFields(actionContext.getUser(), objectDataList, updateFieldList);
        } catch (Exception e) {
            log.error("leads deal error {}", actionContext.getTenantId(), e);
            throw new SFABusinessException(SFAErrorCode.LEADS_COMMON_ERROR);
        }

        return result;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result.setSuccessList(Lists.newArrayList(arg.getLeadsId()));
        result.setFailedList(Lists.newArrayList());
        result.setErrorList(Lists.newArrayList());
        leadsOverTimeTaskService.deleteTask(actionContext.getTenantId(), arg.getLeadsId());

        // 新增加线索回收计算到期时间
//        recalculateTaskService.send(actionContext.getTenantId(), arg.getLeadsId(), "LeadsObj", ActionCodeEnum.DEAL);
        String leadsId = arg.getLeadsId();
        dataList = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), Lists.newArrayList(leadsId), SFAPreDefineObject.Leads.getApiName());
        addFlowRecord();
        Optional<IObjectData> optionalData = dataList.stream().filter(d -> leadsId.equals(d.getId())).findFirst();
        if (optionalData.isPresent()) {
            IObjectData objectData = optionalData.get();
            if (arg.getOperationType() == 1) {
                // 发送【跟进中】的计算跟进时间的消息
                this.serviceFacade.sendActionMq(this.actionContext.getUser(), Lists.newArrayList(objectData), ObjectAction.FOLLOW_UP);
                String msg = String.format(I18N.text(SFA_LEADS_FLLOWING_UP), I18N.text("LeadsObj.attribute.self.display_name"), objectData.getName());
                serviceFacade.logCustomMessageOnly(actionContext.getUser(), EventType.MODIFY, ActionType.Handle, objectDescribe, objectData,
                        msg);
            }
            if (arg.getOperationType() == 2) {
                // 发送【无效】的计算跟进时间的消息
                this.serviceFacade.sendActionMq(this.actionContext.getUser(), Lists.newArrayList(objectData), ObjectAction.CLOSE);
                String closeReasonCode = objectData.get("close_reason", String.class);
                String closeReason = "--";
                if (!StringUtils.isEmpty(closeReasonCode)) {
                    SelectOneFieldDescribe fieldDescribe = (SelectOneFieldDescribe) objectDescribe.getFieldDescribe("close_reason");
                    if (fieldDescribe.getOption(closeReasonCode).isPresent()) {
                        closeReason = fieldDescribe.getOption(closeReasonCode).get().getLabel();
                        if ("other".equals(closeReasonCode) && !Strings.isNullOrEmpty(objectData.get("close_reason__o", String.class))) {
                            closeReason += ":" + objectData.get("close_reason__o", String.class);
                        }
                    }
                }
                String msg = String.format(I18N.text(SFA_INVALID_REASON_MSG), I18N.text("LeadsObj.attribute.self.display_name"), objectData.getName(), closeReason);
                serviceFacade.logCustomMessageOnly(actionContext.getUser(), EventType.MODIFY, ActionType.Handle, objectDescribe, objectData,
                        msg);
            }
            String leadsPoolId = objectData.get(LeadsConstants.Field.LEADS_POOL_ID.getApiName(), String.class);
            IObjectData leadsPool = objectPoolService.getObjectPoolById(SFAPreDefineObject.Leads.getApiName(), actionContext.getTenantId()
                    , leadsPoolId);
            if (leadsPool != null) {
                String msg = String.format("%s %s，%s %s", I18N.text("LeadsObj.attribute.self.display_name"), objectData.getName(),
                        I18N.text("LeadsPoolObj.attribute.self.display_name"), leadsPool.getName());

                SFALogModels.SFALogEntity logEntity = sfaLogService.buildLogEntity(leadsPool,
                        msg,
                        false);
                List<SFALogModels.SFALogTextMessage> textMessageList = Lists.newArrayList();
                sfaLogService.appendNameLinkLogTextMessage(actionContext.getObjectApiName(), objectData, textMessageList);
                msg = String.format("，%s %s", I18N.text("LeadsPoolObj.attribute.self.display_name"), leadsPool.getName());
                sfaLogService.appendLogTextMessage(actionContext.getObjectApiName(), objectData,
                        SFALogModels.LogLinkType.NO_LINK, msg, textMessageList);
                logEntity.setLogTextMessageList(textMessageList);
                sfaLogService.addLog(actionContext.getUser(), logEntity, "SalesCluePoolLog",
                        SFALogModels.LogOperationType.DEAL);
            }
            String employeeId = actionContext.getUser().isOutUser() ? actionContext.getUser().getOutUserId() : actionContext.getUser().getUserId();
            qiXinTodoService.dealTodo(actionContext.getTenantId(), actionContext.getUser(), SFAPreDefineObject.Leads.getApiName(),
                    SessionBOCItemKeys.TobeProcessedSalesClue, leadsId);
            LeadsUtils.insertCrmDealDataRelation(actionContext.getTenantId(), objectData.getId(), 2, employeeId);
        }
        //triggerWorkFlow();
        if(arg.isSendSaleEvent() && StringUtils.isNotBlank(arg.getDealResult())) {
            List<FeedsModel.FeedRelatedCrmObject> crmObjects = Lists.newArrayList();
            FeedsModel.FeedRelatedCrmObject crmObject = FeedsModel.FeedRelatedCrmObject.builder()
                    .apiName(SFAPreDefineObject.Leads.getApiName()).dataId(arg.getLeadsId()).build();
            crmObjects.add(crmObject);
            AccountUtil.PublishFeeds(actionContext.getUser(), arg.getDealResult(), 5, 1, crmObjects);
        }
        return super.after(arg, result);
    }

    protected void triggerWorkFlow() {
        this.startWorkFlow(Maps.newHashMap());
        this.stopWatch.lap("startCreateWorkFlow");
    }

    protected void startWorkFlow(Map<String, ApprovalFlowStartResult> startApprovalFlowResult) {
        //批量处理触发工作流以及处理过滤器异常的数据
        for (IObjectData resultObjectData : dataList) {
            infraServiceFacade.startWorkFlow(resultObjectData.getId()
                    , resultObjectData.getDescribeApiName(), WorkflowProducer.TRIGGER_UPDATE, actionContext
                            .getUser(), Maps.newHashMap(), actionContext.getEventId());
        }
    }

    private void addFlowRecord() {
        List<IObjectData> oldFlowRecordDataList = Lists.newArrayList();
        for (IObjectData leadsData : dataList) {
            String oldOwnerId = LeadsUtils.getOwner(leadsData);
            if(StringUtils.isBlank(oldOwnerId)) {
                continue;
            }
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            searchTemplateQuery.setLimit(1);
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, "leads_owner", oldOwnerId);
            SearchUtil.fillFilterEq(filters, "leads_id", leadsData.getId());
            searchTemplateQuery.setFilters(filters);
            List<OrderBy> orderByList = Lists.newArrayList();
            orderByList.add(new OrderBy("last_modified_time", false));
            searchTemplateQuery.setOrders(orderByList);
            searchTemplateQuery.setNeedReturnCountNum(false);
            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(actionContext.getUser(), SFAPreDefineObject.LeadsFlowRecord.getApiName(), searchTemplateQuery);
            if(queryResult != null && org.apache.commons.collections.CollectionUtils.isNotEmpty(queryResult.getData())) {
                IObjectData oldFlowRecordData = queryResult.getData().get(0);
                oldFlowRecordData.set("leads_status", leadsData.get("biz_status"));
                oldFlowRecordData.set("leads_status_changed_time", System.currentTimeMillis());
                oldFlowRecordData.set("last_modified_by", Lists.newArrayList(actionContext.getUser().getUserId()));
                oldFlowRecordData.set("last_modified_time", System.currentTimeMillis());
                oldFlowRecordDataList.add(oldFlowRecordData);
            }
        }
        if(CollectionUtils.notEmpty(oldFlowRecordDataList)) {
            List<String> updateFieldList = Lists.newArrayList("leads_status", "leads_status_changed_time", "last_modified_by",
                    "last_modified_time");
            try {
//            serviceFacade.batchUpdateByFields(user, objectDataList, updateFieldList);
                objectDataService.batchUpdateWithField(oldFlowRecordDataList, updateFieldList, AccountUtil.getDefaultActionContext(actionContext.getUser(), ""));
            } catch (MetadataServiceException metadataError) {
                log.info("addFlowRecord warn", metadataError);
                throw new SFABusinessException(metadataError.getMessage(), SFAErrorCode.ACCOUNT_COMMON_ERROR);
            } catch (Exception e) {
                log.error("addFlowRecord error", e);
                throw new SFABusinessException(SFAErrorCode.ACCOUNT_COMMON_ERROR);
            }
        }
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.FOLLOW_UP.getButtonApiName();
    }

    @Override
    protected IObjectData getPreObjectData() {
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIdsIncludeDeleted(actionContext.getUser(), Lists.newArrayList(arg.getLeadsId()), actionContext.getObjectApiName());
        if (objectDataList != null && objectDataList.size() != 0) {
            return objectDataList.get(0);
        }
        return null;
    }

    @Override
    protected IObjectData getPostObjectData() {
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIdsIncludeDeleted(actionContext.getUser(), Lists.newArrayList(arg.getLeadsId()), actionContext.getObjectApiName());
        if (objectDataList != null && objectDataList.size() != 0) {
            return objectDataList.get(0);
        }
        return null;
    }

    @Override
    protected boolean skipPreFunction() {
        return arg.isSkipPreAction() || super.skipPreFunction();
    }

    @Override
    protected boolean skipCheckButtonConditions() {
        return arg.isSkipButtonConditions() || super.skipCheckButtonConditions();
    }

    @Override
    protected Map<String, Object> getArgs() {
        if(arg.getArgs() != null) {
            return ObjectDataExt.toMap(arg.getArgs().toObjectData());
        }
        return Maps.newHashMap();
    }

    @Data
    @NoArgsConstructor
    static class Arg {
        private String leadsId;
        private String closeReason;
        private String closeReason__o;
        private String dealResult;
        private Integer operationType;
        private boolean sendSaleEvent;
        private boolean skipFunctionCheck;
        private boolean skipPreAction;
        private boolean skipButtonConditions;
        private List<String> dataIds;
        private ObjectDataDocument args;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private List<String> successList;
        private List<String> failedList;
        private List<String> errorList;
    }
}
