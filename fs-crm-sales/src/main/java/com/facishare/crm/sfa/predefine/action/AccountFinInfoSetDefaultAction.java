package com.facishare.crm.sfa.predefine.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.constant.AccountFinInfoConstants;
import com.facishare.crm.sfa.utilities.util.LogUtil;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.appframework.core.predef.action.StandardAction;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.metadata.api.IObjectData;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;


/**
 * Created by wangmy on 2019/1/3 10:23.
 */
public class AccountFinInfoSetDefaultAction extends PreDefineAction<AccountFinInfoSetDefaultAction.Arg, AccountFinInfoSetDefaultAction.Result> {
    protected List<IObjectData> accountFinInfoList;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardAction.Edit.getFunPrivilegeCodes();
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Lists.newArrayList(arg.getInvoiceId());
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        this.serviceFacade.checkActionByLockStatusAndLifeStatus(this.dataList, ObjectAction.UPDATE, this.actionContext.getUser(), this.objectDescribe.getApiName(), false);
    }

    @Override
    protected Result doAct(Arg arg) {
        if (Strings.isNullOrEmpty(arg.getInvoiceId()) || Strings.isNullOrEmpty(arg.getAccountId())) {
            throw new ValidateException(I18N.text(I18NKey.REQUEST_PARAM_IS_NULL));
        }
        IObjectData accountData = serviceFacade.findObjectData(actionContext.getUser(), arg.getAccountId(), SFAPreDefineObject.Account.getApiName());
        if (accountData == null) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_ACCOUNTADDR_ACOUNTNOTNULL, I18N.text("AccountObj.attribute.self.display_name")));
        }
        accountFinInfoList = serviceFacade.findDetailObjectDataList(serviceFacade.findObject(actionContext.getTenantId(), SFAPreDefineObject.AccountFinInfo.getApiName()), accountData, actionContext.getUser());
        if (CollectionUtils.empty(accountFinInfoList)) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_ACCOUNTFININFO_ACCOUNTFININFONOTNULL));
        }
        Optional<IObjectData> accountFinfoCurrentOptional = accountFinInfoList.stream().filter(item -> item.getId().equals(arg.getInvoiceId())).findFirst();
        if (!accountFinfoCurrentOptional.isPresent()) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_ACCOUNTFININFO_ACCOUNTFININFONOTNULL));
        }
        for (IObjectData objectData : accountFinInfoList) {
            if (objectData.getId().equals(arg.getInvoiceId())) {
                objectData.set(AccountFinInfoConstants.Field.IS_DEFAULT.getApiName(), true);
            } else {
                objectData.set(AccountFinInfoConstants.Field.IS_DEFAULT.getApiName(), false);
            }
        }
        serviceFacade.batchUpdateByFields(actionContext.getUser(), accountFinInfoList, Arrays.asList(AccountFinInfoConstants.Field.IS_DEFAULT.getApiName()));
        return Result.builder().build();
    }


    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        //添加日志
        LogUtil.recordLogs(actionContext.getUser(), accountFinInfoList, EventType.MODIFY, ActionType.SET_DEFAULT);
        return result;
    }


    @Data
    public static class Arg {
        @JSONField(name = "M1")
        @JsonProperty("invoice_id")
        private String invoiceId;

        @JSONField(name = "M2")
        @JsonProperty("account_id")
        private String accountId;
    }

    @Data
    @Builder
    @NoArgsConstructor
    public static class Result {
    }
}
