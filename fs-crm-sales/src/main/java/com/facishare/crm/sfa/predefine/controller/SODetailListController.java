package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.util.ButtonUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardDetailListController;
import com.facishare.paas.metadata.ui.layout.ILayout;

/**
 * <AUTHOR> 2019-11-28
 * @instruction
 */
public class SODetailListController extends StandardDetailListController {
    @Override
    protected ILayout findLayout() {
        return ButtonUtils.removeButtons(super.findLayout());
    }
}
