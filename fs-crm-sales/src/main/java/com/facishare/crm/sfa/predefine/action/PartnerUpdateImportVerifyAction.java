package com.facishare.crm.sfa.predefine.action;

import com.facishare.paas.appframework.core.predef.action.StandardUpdateImportVerifyAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.google.common.collect.Lists;

import java.util.List;

public class PartnerUpdateImportVerifyAction extends StandardUpdateImportVerifyAction {

    private List<String> removeFields = Lists.newArrayList(
            "is_er_enterprise"
    );

    @Override
    protected List<IFieldDescribe> getValidImportFields() {
        List<IFieldDescribe> fieldDescribes = super.getValidImportFields();
        fieldDescribes.removeIf(f -> removeFields.contains(f.getApiName()));
        return fieldDescribes;
    }
}
