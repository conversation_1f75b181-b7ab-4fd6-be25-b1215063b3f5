package com.facishare.crm.sfa.utilities.proxy;

import com.facishare.crm.sfa.utilities.proxy.model.*;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

@RestResource(value = "FS_CRM", desc = "库存服务", contentType = "application/json")
public interface PromotionProxy {
    @POST(value = "/promotion/service/list_by_customer_id", desc = "适用该客户的所有订单促销")
    GetPromotionByAccountIdModel.Result getPromotionsByAccountId(@Body GetPromotionByAccountIdModel.Arg arg, @HeaderMap Map<String, String> headers);

    @POST(value = "/promotion/service/list_by_product_ids", desc = "适用该客户下产品的所有促销")
    GetPromotionByAccountIdAndProductIdsModel.Result getPromotionsByAccountIdAndProductIds(@Body GetPromotionByAccountIdAndProductIdsModel.Arg arg, @HeaderMap Map<String, String> headers);

    @POST(value = "/promotion/service/get_by_id", desc = "获取促销详情")
    GetPromotionByIdModel.Result getPromotionById(@Body GetPromotionByIdModel.Arg arg, @HeaderMap Map<String, String> headers);

    @POST(value = "/promotion/service/is_promotion_enable", desc = "是否开启促销")
    IsPromotionEnableModel.Result isPomotionEnable(@HeaderMap Map<String, String> headers);

    @POST(value = "/promotion/service/list_promotion_products_by_customer_id", desc = "根据客户id查询（商品）促销产品列表")
    GetPromotionProductsByAccountIdModel.Result getPromotionProductsByAccountId(@Body GetPromotionProductsByAccountIdModel.Arg arg, @HeaderMap Map<String, String> headers);

    @POST(value = "/promotion/service/validate_promotions_for_sales_order", desc = "订单提交促销校验")
    ValidatePromotionsForSalesOrderModel.Result validatePromotionsForSalesOrder(@Body ValidatePromotionsForSalesOrderModel.Arg arg, @HeaderMap Map<String, String> headers);

    @POST(value = "/promotion/service/get_by_ids", desc = "批量查询促销详情（促销、促销产品和促销规则, 以及促销赠品）")
    GetPromotionByIdsModel.Result getPromotionByIds(@Body GetPromotionByIdsModel.Arg arg, @HeaderMap Map<String, String> headers);
}
