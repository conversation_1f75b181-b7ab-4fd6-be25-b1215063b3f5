package com.facishare.crm.sfa.utilities.dataconverter.converter;

import com.facishare.crm.sfa.utilities.dataconverter.FieldValueConvertContext;
import org.apache.commons.lang3.StringUtils;

public class PercentFieldValueConverter implements AbstractFieldValueConverter {
    @Override
    public String convert(FieldValueConvertContext context) {
        String fieldValue = String.valueOf(context.getFieldValue());

        return StringUtils.isNotBlank(fieldValue) ? fieldValue + "%" : "";
    }
}
