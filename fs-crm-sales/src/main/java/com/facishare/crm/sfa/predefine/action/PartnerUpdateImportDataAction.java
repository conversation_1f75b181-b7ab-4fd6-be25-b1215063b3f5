package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.utilities.util.*;
import com.facishare.paas.appframework.core.predef.action.StandardUpdateImportDataAction;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

@Slf4j
public class PartnerUpdateImportDataAction extends StandardUpdateImportDataAction {

    @Override
    protected void customAfterImport(List<IObjectData> actualList) {
        super.customAfterImport(actualList);
//        IndustryEnterInfoUtil.updateIndustryEnterInfo(actionContext.getUser(), actionContext.getObjectApiName(),
//                arg.getIsVerifyEnterprise(), arg.getIsBackFillIndustrialAndCommercialInfo(),
//                arg.getIsBackFillOverwriteOldValue(), actualList);
    }
}
