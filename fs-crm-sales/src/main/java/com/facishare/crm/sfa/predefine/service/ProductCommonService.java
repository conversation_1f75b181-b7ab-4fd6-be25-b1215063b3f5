package com.facishare.crm.sfa.predefine.service;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.real.MultiUnitService;
import com.facishare.crm.sfa.predefine.service.real.MultiUnitServiceImpl;
import com.facishare.crm.sfa.predefine.service.real.SpuSkuService;
import com.facishare.crm.sfa.predefine.service.real.TransformData4ViewService;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.proxy.StockProxy;
import com.facishare.crm.sfa.utilities.proxy.model.ProductRelateStockCheckModel;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.util.SFAHeaderUtil;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.predefine.service.modulectrl.IModuleInitService.MODULE_MULTIPLE_UNIT;
import static com.facishare.crm.sfa.predefine.service.modulectrl.IModuleInitService.MODULE_SPU;

@Service
@Slf4j
public class ProductCommonService {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private SpuSkuService spuSkuService;
    @Autowired
    private TransformData4ViewService transformData4ViewService;
    @Autowired
    private MultiUnitService multiUnitService;
    @Autowired
    private ModuleCtrlConfigService moduleCtrlConfigService;
    @Autowired
    private StockProxy stockProxy;

    /**
     * 特殊：给订单选择价目表产品时，填充产品数据
     * 给数据填充特殊产品数据
     */
    public void fillDataWithProduct(User user, List<IObjectData> data,boolean isRealLookUp) {
        List<String> productIdList = data.stream().map(k -> k.get("product_id", String.class)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(data) && CollectionUtils.isNotEmpty(productIdList)) {

            Map<String, IObjectDescribe> apiNameAndDescribeMapping = serviceFacade.findObjects(user.getTenantId(), Lists.newArrayList(Utils.PRODUCT_API_NAME, Utils.PRICE_BOOK_PRODUCT_API_NAME));

            List<IObjectData> productList = findProductList(user, productIdList);
            transformData4ViewService.batchTransformDataForView(user, apiNameAndDescribeMapping.get(Utils.PRODUCT_API_NAME), productList);

            Map<String, ObjectDataDocument> productMap = productList.stream().collect(Collectors.toMap(k -> k.getId(), k -> ObjectDataDocument.of(k)));
            if (!isRealLookUp) {
                spuSkuService.fillSpecAndValue4Skus(user.getTenantId(), productMap);
            }
            data.forEach(k -> {
                k.set("product_id__ro", productMap.get(k.get("product_id", String.class)));
            });
        }
    }


    private List<IObjectData> findProductList(User user, List<String> productIdList) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(productIdList.size());
        SearchUtil.fillFilterIn(searchTemplateQuery.getFilters(), IObjectData.ID, productIdList);
        searchTemplateQuery.setPermissionType(0);
        QueryResult<IObjectData> productResult = serviceFacade.findBySearchQuery(ActionContextExt.of(user).pgDbType().skipRelevantTeam().getContext(), Utils.PRODUCT_API_NAME, searchTemplateQuery);
        if (moduleCtrlConfigService.isOpen(MODULE_MULTIPLE_UNIT, user)) {
            multiUnitService.handleUnitAndPrice(productResult.getData(), user.getTenantId());
        }
        return productResult.getData();
    }

    public void checkProductStockRelation(User user, List<String> productIdList){
        ProductRelateStockCheckModel.Arg proxyArg = ProductRelateStockCheckModel.Arg.builder().productIds(productIdList).build();
        Map<String, String> headers = SFAHeaderUtil.getHeaders(user.getTenantId(),
                user.getUserId());
        ProductRelateStockCheckModel.Result proxyRst = stockProxy.checkProductRelateStock(proxyArg, headers);
        if(!proxyRst.getData().isResult()){
            throw new ValidateException(I18N.text("sfa.product.stock.has.relation"));
        }
    }
}
