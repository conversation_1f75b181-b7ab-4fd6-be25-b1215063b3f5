package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.constant.AccountAddrConstants;
import com.facishare.crm.sfa.utilities.util.AccountAddrUtil;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.crm.sfa.utilities.validator.AccountAddrValidator;
import com.facishare.idempotent.Idempotent;
import com.facishare.idempotent.Serializer;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created by wangmy on 2019/1/5 16:51.
 */
@Slf4j
@Idempotent(serializer = Serializer.Type.java)
public class AccountAddrAddAction extends StandardAddAction {
    protected IObjectData accountData;
    protected Boolean isMain = false;

    @Override
    protected void before(Arg arg) {
        AccountAddrValidator.beforeValidator(actionContext.getUser(), "Add", arg.getObjectData().toObjectData());
        accountData = serviceFacade.findObjectData(actionContext.getUser(), String.valueOf(arg.getObjectData().get(AccountAddrConstants.Field.ACCOUNT_ID.getApiName())), SFAPreDefineObject.Account.getApiName());
        if (accountData == null) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_ACCOUNTADDR_ACOUNTNOTNULL, I18N.text("AccountObj.attribute.self.display_name")));
        }
        List<IObjectData> accountAddList = serviceFacade.findDetailObjectDataList(serviceFacade.findObject(actionContext.getTenantId(), SFAPreDefineObject.AccountAddr.getApiName()), accountData, actionContext.getUser());
        //根据客户ID获取地址列表，如果没有则将主地址和默认收货地址置为true，否则为false
        if (CollectionUtils.empty(accountAddList)) {
            arg.getObjectData().put(AccountAddrConstants.Field.IS_DEFAULT_ADD.getApiName(), true);
            arg.getObjectData().put(AccountAddrConstants.Field.IS_SHIP_TO_ADD.getApiName(), true);
            isMain = true;
        } else {
            arg.getObjectData().put(AccountAddrConstants.Field.IS_DEFAULT_ADD.getApiName(), false);
            arg.getObjectData().put(AccountAddrConstants.Field.IS_SHIP_TO_ADD.getApiName(), false);
        }
        AccountAddrUtil.handleLocationField(arg.getObjectData().toObjectData());
        AccountAddrUtil.handleGeoPointField(arg.getObjectData().toObjectData());
        super.before(arg);
    }

    @Override
    @Transactional
    protected Result doAct(Arg arg) {
        Result result = super.doAct(arg);
        if (isMain) {
            //更新主地址时，更新biz_account表中的地址相关信息
            AccountAddrUtil.bulkUpdateAccountLocation(actionContext.getUser(), Lists.newArrayList(accountData), Lists.newArrayList(result.getObjectData().toObjectData()));
        }
        return result;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        return result;
    }


}
