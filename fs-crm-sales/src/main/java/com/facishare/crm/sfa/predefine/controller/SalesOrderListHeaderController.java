package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.util.SfaListHeaderUtil;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import java.util.List;
import java.util.Objects;


/**
 * Created by renlb on 2018/12/25.
 */
public class SalesOrderListHeaderController extends StandardListHeaderController {


    @Override
    protected List<IButton> getButtons() {
        List<IButton> buttons = super.getButtons();
        if(Objects.equals(arg.getTargetObjectApiName(), Utils.INVOICE_APPLICATION_API_NAME)){
            buttons.removeIf(button -> Objects.equals(button.getAction(), ObjectAction.CREATE.getActionCode()));
        }
        return buttons;
    }

}
