package com.facishare.crm.sfa.predefine.service.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-09-04 17:22
 * @instruction
 */
public interface CheckHasPackageProductSPUModel {
    @Data
    class Arg {
        @JSONField(name = "M1")
        private List<String> spuIds;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class Result {
        @JSONField(name = "M1")
        private List<String> hasPackageProductSpuIds;
    }


}
