package com.facishare.crm.sfa.predefine.service.model;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;
import java.util.List;

/**
 * 获取全部联系人给移动端缓存 class
 *
 * <AUTHOR>
 * @date 2019/2/23
 */
public interface ContactSearchResult {
    @Data
    class Arg {
        private Long lastModifiedTime;
        private Integer pageSize;
    }

    @Data
    @Builder
    class Result {
        private List<ObjectDataDocument> contactList= Lists.newArrayList();
        private List<String> deletedContactIDs  = Lists.newArrayList();
        private Long updateTime;
    }


    @Data
    @Builder
    class mobile_infos {
        private String mobile;
        private String mobilePath;
    }
}
