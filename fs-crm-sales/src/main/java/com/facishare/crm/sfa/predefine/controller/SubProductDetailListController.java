//package com.facishare.crm.sfa.predefine.controller;
//
//import com.facishare.paas.appframework.core.predef.controller.StandardDetailListController;
//import com.facishare.paas.metadata.ui.layout.ILayout;
//
//import static com.facishare.crm.sfa.utilities.util.ButtonUtils.removeButtons;
//
///**
// * <AUTHOR>
// * @date 2019-08-27 16:43
// * @instruction
// */
//public class SubProductDetailListController extends StandardDetailListController {
//    @Override
//    protected ILayout findLayout() {
//        return removeButtons(super.findLayout());
//    }
//}
