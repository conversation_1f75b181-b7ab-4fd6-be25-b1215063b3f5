package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.util.ContactUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;

public class ContactRelatedListController extends SFARelatedListController {

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        ContactUtil.addButton(controllerContext.getUser(), result);
        if (CollectionUtils.empty(result.getDataList())) {
            return result;
        }
        //列表需要下发手机号归属地信息
        serviceFacade.fillPhoneNumberInformation(objectDescribe, ObjectDataDocument.ofDataList(result.getDataList()));
        ContactUtil.handlePredefinePhoneField(ObjectDataDocument.ofDataList(result.getDataList()));
        ContactUtil.concatenateBirthDay(ObjectDataDocument.ofDataList(result.getDataList()));
        return result;
    }

}
