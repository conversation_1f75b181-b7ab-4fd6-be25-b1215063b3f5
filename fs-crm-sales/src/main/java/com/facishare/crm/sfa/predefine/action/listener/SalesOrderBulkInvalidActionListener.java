package com.facishare.crm.sfa.predefine.action.listener;


import com.facishare.crm.sfa.utilities.proxy.SalesOrderBizProxy;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.SalesOrderInterceptorModel;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.salesorderparam.BulkInvalidAfterModel;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.salesorderparam.BulkInvalidBeforeModel;
import com.facishare.crm.sfa.utilities.util.SoCommonUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.metadata.util.SpringUtil;
import org.springframework.stereotype.Component;

/**
 * Created by renlb on 2019/3/14.
 */
@Component
public class SalesOrderBulkInvalidActionListener extends StandardBulkInvalidActionListener {

    private static final SalesOrderBizProxy salesOrderBizProxy = SpringUtil.getContext().getBean(SalesOrderBizProxy.class);

    @Override
    protected void callBeforeInterceptor(BulkInvalidBeforeModel.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext("SalesOrderInterceptor",
                "BulkInvalidBefore");
//        salesOrderInterceptorService.bulkInvalidBefore(context, arg);
        SalesOrderInterceptorModel.BulkInvalidBeforeResult  bulkInvalidBeforeResult = salesOrderBizProxy.bulkInvalidBefore(arg, SoCommonUtils.getCrmHeader(context.getTenantId(), context.getUser()));
        if(!bulkInvalidBeforeResult.isSuccess()){
            throw new ValidateException(bulkInvalidBeforeResult.getMessage());
        }
    }

    @Override
    protected void callAfterInterceptor(BulkInvalidAfterModel.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext("SalesOrderInterceptor",
                "BulkInvalidAfter");
//        salesOrderInterceptorService.bulkInvalidAfter(context, arg);
        SalesOrderInterceptorModel.BulkInvalidAfterResult bulkInvalidAfterResult = salesOrderBizProxy.bulkInvalidAfter(arg, SoCommonUtils.getCrmHeader(context.getTenantId(), context.getUser()));
        if(!bulkInvalidAfterResult.isSuccess()){
            throw new ValidateException(bulkInvalidAfterResult.getMessage());
        }
    }
}
