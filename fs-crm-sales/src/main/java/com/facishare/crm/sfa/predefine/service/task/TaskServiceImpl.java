package com.facishare.crm.sfa.predefine.service.task;

import com.fxiaoke.paas.gnomon.api.NomonProducer;
import com.fxiaoke.paas.gnomon.api.entity.NomonDeleteMessage;
import com.fxiaoke.paas.gnomon.api.entity.NomonMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class TaskServiceImpl implements TaskService {
    @Autowired
    private NomonProducer nomonProducer;

    @Override
    public void createOrUpdateTask(String biz, String tenantId, String dataId, Date executeTime,String callArg) {
        send(biz, tenantId, dataId, executeTime, callArg, null);
    }

    @Override
    public void createOrUpdateTask(String biz, String tenantId, String dataId, Date executeTime, String callArg, Integer callQueueMod) {
        send(biz, tenantId, dataId, executeTime, callArg, callQueueMod);
    }

    private void send(String biz, String tenantId, String dataId, Date executeTime, String callArg, Integer callQueueMod) {
        NomonMessage message = NomonMessage
                .builder()
                .biz(biz)
                .tenantId(tenantId)
                .dataId(dataId)
                .executeTime(executeTime)
                .callArg(String.format(callArg, tenantId, dataId))
                .callQueueMod(callQueueMod)
                .build();
        nomonProducer.send(message);
    }

    @Override
    public void deleteTask(String biz, String tenantId, String dataId) {
        NomonDeleteMessage message = NomonDeleteMessage
                .builder()
                .biz(biz)
                .tenantId(tenantId)
                .dataId(dataId)
                .build();
        nomonProducer.send(message);
    }
}
