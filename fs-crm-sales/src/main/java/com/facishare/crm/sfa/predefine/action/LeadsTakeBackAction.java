package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.model.Enum.LeadsBizStatusEnum;
import com.facishare.crm.sfa.model.Enum.SessionBOCItemKeys;
import com.facishare.crm.sfa.model.SFAObjectPoolCommon;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.exception.SFABusinessException;
import com.facishare.crm.sfa.predefine.exception.SFAErrorCode;
import com.facishare.crm.sfa.predefine.service.LeadsPoolServiceImpl;
import com.facishare.crm.sfa.predefine.service.QiXinTodoService;
import com.facishare.crm.sfa.predefine.service.model.SFALogModels;
import com.facishare.crm.sfa.predefine.service.task.LeadsAllocateOverTimeTaskService;
import com.facishare.crm.sfa.predefine.service.task.LeadsOverTimeTaskService;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.crm.sfa.utilities.util.LeadsUtils;
import com.facishare.crmcommon.util.SearchUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.*;

/**
 * @IgnoreI18nFile
 */
@Slf4j
public class LeadsTakeBackAction extends BaseSFATakeBackAction {
    private LeadsOverTimeTaskService leadsOverTimeTaskService = SpringUtil.getContext().getBean(LeadsOverTimeTaskService.class);
    private LeadsAllocateOverTimeTaskService leadsAllocateOverTimeTaskService = SpringUtil.getContext().getBean(LeadsAllocateOverTimeTaskService.class);
    private static final QiXinTodoService qiXinTodoService = SpringUtil.getContext().getBean(QiXinTodoService.class);
    private LeadsPoolServiceImpl leadsPoolServiceImpl = SpringUtil.getContext().getBean(LeadsPoolServiceImpl.class);
    private static final IObjectDataService objectDataService = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);

    @Override
    protected void before(SFAObjectPoolCommon.Arg arg) {
        super.before(arg);
        if (CollectionUtils.isEmpty(dataList)) {
            dataList = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), arg.getObjectIDs(), SFAPreDefineObject.Leads.getApiName());
        }
        Map<String, LeadsBizStatusEnum> unOperateStatus = Maps.newHashMap();
        unOperateStatus.put(LeadsBizStatusEnum.UN_ASSIGNED.getCode(), LeadsBizStatusEnum.UN_ASSIGNED);
        unOperateStatus.put(LeadsBizStatusEnum.TRANSFORMED.getCode(), LeadsBizStatusEnum.TRANSFORMED);
        Map<String, LeadsBizStatusEnum> canOperateStatus = Maps.newHashMap();
        canOperateStatus.put(LeadsBizStatusEnum.UN_PROCESSED.getCode(), LeadsBizStatusEnum.UN_PROCESSED);
        canOperateStatus.put(LeadsBizStatusEnum.PROCESSED.getCode(), LeadsBizStatusEnum.PROCESSED);
        canOperateStatus.put(LeadsBizStatusEnum.CLOSED.getCode(), LeadsBizStatusEnum.CLOSED);
        List<String> tobeProcessIds = Lists.newArrayList();
        failedList = Lists.newArrayList();
        for (IObjectData objectData : dataList) {
            String leadsId = objectData.getId();
            String bizStatus = objectData.get("biz_status", String.class);
            String lifeStatus = objectData.get("life_status", String.class);
            if (canOperateStatus.containsKey(bizStatus) && !lifeStatus.equals(ObjectLifeStatus.INVALID.getCode())) {
                tobeProcessIds.add(leadsId);
            } else if (unOperateStatus.containsKey(bizStatus)) {
                String failedMsg = String.format("%s %s %s，%s", I18N.text("LeadsObj.attribute.self.display_name"),
                        objectData.getName(), unOperateStatus.get(bizStatus).getText(), I18N.text(SFA_UNABEL_OPERATE));
                failedList.add(failedMsg);
                continue;
            } else if (lifeStatus.equals(ObjectLifeStatus.INVALID.getCode()) || lifeStatus.equals(ObjectLifeStatus.IN_CHANGE.getCode())) {
                String failedMsg = String.format("%s %s %s，%s",
                        I18N.text("LeadsObj.attribute.self.display_name"),
                        objectData.getName(),
                        I18N.text(SFA_HAS_BEEN_INVALID),
                        I18N.text(SFA_UNABEL_OPERATE));
                failedList.add(failedMsg);
                continue;
            } else {

                String failedMsg = String.format(I18N.text(SFA_OBJECT_STATUS_UNPROCESSED),
                        I18N.text("LeadsObj.attribute.self.display_name"),
                        objectData.getId(), bizStatus);
                failedList.add(failedMsg);
                continue;
            }
        }
        arg.setObjectIDs(tobeProcessIds);
    }

    @Override
    protected void doFunPrivilegeCheck() {
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        String privilegeCode = "TakeBack";
        return Lists.newArrayList(privilegeCode);
    }

    @Override
    protected SFAObjectPoolCommon.Result doAct(SFAObjectPoolCommon.Arg arg) {
        SFAObjectPoolCommon.Result result = super.doAct(arg);
        return result;
    }

    @Override
    protected SFAObjectPoolCommon.Result after(SFAObjectPoolCommon.Arg arg, SFAObjectPoolCommon.Result result) {
        result.setSuccessList(arg.getObjectIDs());
        result.setFailedList(failedList);
        if (CollectionUtils.isEmpty(arg.getObjectIDs())) {
            return result;
        }
        super.after(arg, result);
        IObjectData leadsPool = objectPoolService.getObjectPoolById(SFAPreDefineObject.Leads.getApiName(), actionContext.getTenantId()
                , arg.getObjectPoolId());

        //创建线索超时提醒任务
        leadsOverTimeTaskService.deleteTask(actionContext.getTenantId(), arg.getObjectIDs());
        leadsAllocateOverTimeTaskService.createOrUpdateTask(actionContext.getTenantId(), arg.getObjectIDs(), leadsPool);
        addFlowRecord();
        if (!CollectionUtils.isEmpty(arg.getObjectIDs())) {
            for (String processedId : arg.getObjectIDs()) {
                Optional<IObjectData> optionalData = dataList.stream().filter(d -> processedId.equals(d.getId())).findFirst();
                if (!optionalData.isPresent()) {
                    continue;
                }
                IObjectData objectData = optionalData.get();
                String ownerName = "";
                if (objectData.getOwner().size() > 0) {
                    String ownerId = objectData.getOwner().get(0);
                    User owner = serviceFacade.getUser(actionContext.getTenantId(), ownerId);
                    if (owner != null) {
                        ownerName = owner.getUserName();
                    }
                }
                String company = objectData.get("company", String.class);
                if (Strings.isNullOrEmpty(company)) {
                    company = "";
                }
                String msg = String.format("%s %s %s，%s %s，%s %s",
                        I18N.text("LeadsObj.attribute.self.display_name"),
                        objectData.getName(),
                        company,
                        I18N.text(SFA_ORIGINAL_MANAGER),
                        ownerName,
                        I18N.text("LeadsPoolObj.attribute.self.display_name"),
                        leadsPool.getName());
                serviceFacade.logCustomMessageOnly(actionContext.getUser(), EventType.MODIFY, ActionType.TakeBack, objectDescribe, objectData,
                        msg);
                LeadsUtils.addPoolReturnLog(actionContext.getUser(), objectData, objectPoolData, SFALogModels.LogOperationType.TACK_BACK);
                //待办
                String leadsPoolId = arg.getObjectPoolId();
                if (!Strings.isNullOrEmpty(leadsPoolId)) {
                    Map<String, List<String>> inAndOutPoolAdminList = leadsPoolServiceImpl.getInAndOutPoolAdminById(actionContext.getUser(), leadsPoolId);
                    if (!inAndOutPoolAdminList.isEmpty()) {
                        qiXinTodoService.sendInAndOutTodo(actionContext.getTenantId(), SessionBOCItemKeys.TOBE_ASSIGNED_SALES_CLUE,
                                actionContext.getObjectApiName(), processedId, actionContext.getUser().getUserId(),
                                inAndOutPoolAdminList.containsKey("in") ? inAndOutPoolAdminList.get("in") : Lists.newArrayList(),
                                inAndOutPoolAdminList.containsKey("out") ? inAndOutPoolAdminList.get("out") : Lists.newArrayList());
                    }
                }
            }
        }
        return result;
    }

    @Override
    protected void logAsync(List<IObjectData> dataList, EventType eventType, ActionType actionType) {
    }

    @Override
    protected boolean needTriggerApprovalFlow() {
        return false;
    }

    private void addFlowRecord() {
        List<IObjectData> oldFlowRecordDataList = Lists.newArrayList();
        for (IObjectData leadsData : dataList) {
            String oldOwnerId = LeadsUtils.getOwner(leadsData);
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            searchTemplateQuery.setLimit(1);
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, "leads_owner", oldOwnerId);
            SearchUtil.fillFilterEq(filters, "leads_id", leadsData.getId());
            searchTemplateQuery.setFilters(filters);
            List<OrderBy> orderByList = Lists.newArrayList();
            orderByList.add(new OrderBy("last_modified_time", false));
            searchTemplateQuery.setOrders(orderByList);
            searchTemplateQuery.setNeedReturnCountNum(false);
            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(actionContext.getUser(), SFAPreDefineObject.LeadsFlowRecord.getApiName(), searchTemplateQuery);
            if(queryResult != null && org.apache.commons.collections.CollectionUtils.isNotEmpty(queryResult.getData())) {
                IObjectData oldFlowRecordData = queryResult.getData().get(0);
                oldFlowRecordData.set("leads_status", "returned");
                oldFlowRecordData.set("leads_status_changed_time", System.currentTimeMillis());
                oldFlowRecordData.set("leads_back_reason", "管理员收回");
                oldFlowRecordData.set("last_modified_by", Lists.newArrayList(actionContext.getUser().getUserId()));
                oldFlowRecordData.set("last_modified_time", System.currentTimeMillis());
                oldFlowRecordDataList.add(oldFlowRecordData);
            }
        }
        if(CollectionUtils.isNotEmpty(oldFlowRecordDataList)) {
            List<String> updateFieldList = Lists.newArrayList("leads_status", "leads_status_changed_time", "leads_back_reason", "last_modified_by",
                    "last_modified_time");
            try {
//            serviceFacade.batchUpdateByFields(user, objectDataList, updateFieldList);
                objectDataService.batchUpdateWithField(oldFlowRecordDataList, updateFieldList, AccountUtil.getDefaultActionContext(actionContext.getUser(), ""));
            } catch (MetadataServiceException metadataError) {
                log.info("addFlowRecord warn", metadataError);
                throw new SFABusinessException(metadataError.getMessage(), SFAErrorCode.ACCOUNT_COMMON_ERROR);
            } catch (Exception e) {
                log.error("addFlowRecord error", e);
                throw new SFABusinessException(SFAErrorCode.ACCOUNT_COMMON_ERROR);
            }
        }
    }
}
