package com.facishare.crm.sfa.utilities.util.imports;

/**
 * <AUTHOR>
 * @date 2019/7/16 15:04
 * @instruction
 */
public enum BuilderTypes {

    Text("com.facishare.crm.sfa.utilities.util.imports.TextFieldDescribeBuilderImpl"),
    SelectOne("com.facishare.crm.sfa.utilities.util.imports.TextFieldDescribeBuilderImpl"),
    Currency("com.facishare.crm.sfa.utilities.util.imports.CurrencyFieldDescribeBuilderImpl");


    String value = "";

    private BuilderTypes(String value) {
        this.value = value;
    }

    public String getvalue() {
        return this.value;
    }
}
