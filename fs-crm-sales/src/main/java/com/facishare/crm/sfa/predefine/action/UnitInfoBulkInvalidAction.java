package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.service.UnitService;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Joiner;

import java.util.List;

import static com.facishare.crm.sfa.utilities.util.SOI18NKeyUtils.SO_CHECK_INVALID_UNIT;

public class UnitInfoBulkInvalidAction extends StandardBulkInvalidAction {

    private UnitService unitService = SpringUtil.getContext().getBean(UnitService.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        checkReference(actionContext.getTenantId());

    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        unitService.synchronizeUnit(actionContext.getUser());
        return after;
    }

    private void checkReference(String tenantId){
        if(SFAConfigUtil.isSpuOpen(tenantId)){
            List<String> checkResult = unitService.checkReferenceSpu(actionContext.getUser(), getDataPrivilegeIds(arg));
            if (!checkResult.isEmpty()) {
                String fillData = Joiner.on(",").join(checkResult);
                throw new ValidateException(I18N.text(SO_CHECK_INVALID_UNIT, fillData));
            }
        }else{
            List<String> checkResult = unitService.checkReferenceSku(actionContext.getUser(), getDataPrivilegeIds(arg));
            if (!checkResult.isEmpty()) {
                String fillData = Joiner.on(",").join(checkResult);
                throw new ValidateException(I18N.text(SO_CHECK_INVALID_UNIT, fillData));
            }
        }
    }

}
