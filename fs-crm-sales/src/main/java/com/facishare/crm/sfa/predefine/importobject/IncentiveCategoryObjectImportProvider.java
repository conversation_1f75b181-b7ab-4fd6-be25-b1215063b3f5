package com.facishare.crm.sfa.predefine.importobject;

import com.facishare.paas.appframework.metadata.importobject.DefaultObjectImportProvider;
import com.facishare.paas.appframework.metadata.importobject.ImportType;
import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.springframework.stereotype.Component;


@Component
public class IncentiveCategoryObjectImportProvider extends DefaultObjectImportProvider {
    @Override
    public String getObjectCode() {
        return "IncentiveCategoryObj";
    }

    @Override
    protected ImportType getImportType(IObjectDescribe objectDescribe, IUniqueRule uniqueRule) {
        return ImportType.UNSUPPORT_UPDATE_IMPORT;
    }
}
