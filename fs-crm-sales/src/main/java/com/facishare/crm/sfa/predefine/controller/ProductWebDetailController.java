package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.cpq.BomCoreService;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2020/6/22 2:03 下午
 * @illustration
 */
public class ProductWebDetailController extends SFAWebDetailController {
    private final BomCoreService bomCoreService = SpringUtil.getContext().getBean(BomCoreService.class);

    List<String> filterButtonForMobile = Lists.newArrayList(ObjectAction.CREATE.getActionCode(),
            ObjectAction.UPDATE.getActionCode(),
            ObjectAction.CHANGE_OWNER.getActionCode());

    private boolean isSpuOpen = false;
    private boolean cpqOpen = false;

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        isSpuOpen = SFAConfigUtil.isSpuOpen(controllerContext.getTenantId());
        cpqOpen = SFAConfigUtil.isCPQOpen(controllerContext.getTenantId());

    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        if (newResult.getLayout() == null) {
            return newResult;
        }
        ILayout layout = new Layout(newResult.getLayout());
        // 设置配置产品组合按钮
        processSubProductButton(layout);
        // 处理产品规格字段只读
        removeProductSpecField(layout);
        // 移除新建编辑更换负责人
        removeButtons4MobileOrH5(layout, filterButtonForMobile);
        // 处理多单位页签
        handleMultiUnitRelatedComponent(layout);
        // 处理无规格数据按钮和相关团队按钮
        removeNoProdSpecButtons(layout);
        // 移除产品选配明细页签
        removeBomComponent(layout);

        return newResult;
    }

    private void removeBomComponent(ILayout layout) {
        WebDetailLayout.of(layout).removeComponents(Lists.newArrayList("BOMObj_parent_product_id_related_list"));
    }

    /**
     * 处理配置产品组合按钮下发场景
     *
     * @param layout
     */
    private void processSubProductButton(ILayout layout) {
        LayoutExt layoutExt = LayoutExt.of(layout);
        Optional<IComponent> headInfoComponentOp = layoutExt.getHeadInfoComponent();
        if (!headInfoComponentOp.isPresent()) {
            return;
        }

        IComponent headInfoComponent = headInfoComponentOp.get();
        List<IButton> buttons = headInfoComponent.getButtons();
        if (RequestUtil.isWebRequest()) {
            Optional<IButton> editButtonOptional = buttons.stream().filter(o -> Objects.equals(ObjectAction.UPDATE.getActionCode(), o.getAction())).findAny();
            Optional<IButton> configureProductButtonOptional = buttons.stream().filter(o -> Objects.equals(ObjectAction.CONFIGURE_PRODUCT.getActionCode(), o.getAction())).findAny();
            if (configureProductButtonOptional.isPresent()) {
                configureProductButtonOptional.get().setLabel(I18N.text(I18NKey.CONFIGURE_SBUPRODUCT_BUTTON));
            }

            // 没有编辑按钮,删除配置子产品按钮; 如果没有子产品的权限,不下发按钮
            if (!editButtonOptional.isPresent() || !bomCoreService.haveConfigBOMPrivilege(controllerContext.getUser())) {
                buttons.removeIf(b -> b.getAction().equals(ObjectAction.CONFIGURE_PRODUCT.getActionCode()));
            } else {
                if (!cpqOpen) {
                    buttons.removeIf(b -> b.getAction().equals(ObjectAction.CONFIGURE_PRODUCT.getActionCode()));
                } else if (!configureProductButtonOptional.isPresent()) {
                    IButton editButton = editButtonOptional.get();
                    IButton button = new Button();
                    button.setLabel(I18N.text(I18NKey.CONFIGURE_SBUPRODUCT_BUTTON));
                    button.setAction(ObjectAction.CONFIGURE_PRODUCT.getActionCode());
                    button.setName(editButton.getName());
                    button.setActionType(editButton.getActionType());
                    buttons.add(button);
                }
            }
        }
        WebDetailLayout.of(layout).addButtons(buttons);
    }

    /**
     * 处理规格字段只读
     *
     * @param layout
     */
    private void removeProductSpecField(ILayout layout) {
        LayoutExt layoutExt = LayoutExt.of(layout);
        FormComponent formComponent = (FormComponent) layoutExt.getFormComponent().get().getFormComponent();
        formComponent.getFieldSections().forEach(field -> {
            field.getFields().forEach(formField -> {
                if (Objects.equals(formField.getFieldName(), "product_spec")) {
                    formField.setReadOnly(false);
                }
            });
        });
    }



    /**
     * 无规格产品去掉更换负责人按钮
     *
     * @param layout
     */
    private void removeNoProdSpecButtons(ILayout layout) {
        if (isSpuOpen) {
            if (null == result.getData().get("spu_id")) {
                return;
            }
            WebDetailLayout.of(layout).removeButtonsByActionCode(Lists.newArrayList(ObjectAction.CHANGE_OWNER.getActionCode()));
            List<String> buttons = Lists.newArrayList("AddTeamMember", "EditTeamMember", "DeleteTeamMember");
            WebDetailLayout.of(layout).removeButtonsByActionCode(buttons, "AddTeamMember_button_default");
        }
    }
}
