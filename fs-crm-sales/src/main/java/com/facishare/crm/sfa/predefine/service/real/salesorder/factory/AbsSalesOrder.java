package com.facishare.crm.sfa.predefine.service.real.salesorder.factory;

import com.facishare.crm.sfa.predefine.service.real.salesorder.price.NewPriceForOrderProduct;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/7/13 5:33 下午
 * @illustration
 */
public abstract class AbsSalesOrder{


    /**
     * 获取计算价格的服务
     * @param tenantId
     * @param data
     * @return
     */
    public abstract void getPrice(String tenantId, List<ObjectDataDocument> data);


}
