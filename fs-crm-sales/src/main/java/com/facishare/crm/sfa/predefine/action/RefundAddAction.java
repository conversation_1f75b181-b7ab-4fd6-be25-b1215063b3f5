package com.facishare.crm.sfa.predefine.action;

import com.facishare.crmcommon.constants.SystemConstants;
import com.facishare.crm.sfa.utilities.constant.RefundConstants;
import com.facishare.crm.sfa.utilities.proxy.RefundProxy;
import com.facishare.crm.sfa.utilities.proxy.model.RefundCreateModel;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.crm.sfa.utilities.validator.RefundValidator;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.Map;
import java.util.Objects;

import static com.facishare.crm.sfa.predefine.service.transfer.CrmPackageObjectConstants.FIELD_LIFE_STATUS;

@Slf4j
public class RefundAddAction extends StandardAddAction {


    private RefundProxy refundProxy = SpringUtil.getContext().getBean(RefundProxy.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        this.setDefaultValue();
        RefundValidator.validateCreate(serviceFacade, actionContext.getTenantId(), arg.getObjectData().toObjectData());
    }
    protected void setDefaultValue(){
        this.objectData.set(RefundConstants.RefundField.SUBMIT_TIME.getApiName(),System.currentTimeMillis());
        this.objectData.set(RefundConstants.RefundField.BIZ_STATUS.getApiName(), RefundConstants.RefundBizStatus.UNREFUND.getValue());
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        //如果审批流为空时,新建的life_status就为normal
        if (result.getObjectData() != null) {
            IObjectData data = result.getObjectData().toObjectData();
            if (Objects.equals(data.get(FIELD_LIFE_STATUS), SystemConstants.LifeStatus.Normal.value)) {
                data.set(RefundConstants.RefundField.BIZ_STATUS.getApiName(), RefundConstants.RefundBizStatus.REFUNDED.getValue());
                serviceFacade.batchUpdateByFields(actionContext.getUser(), Arrays.asList(data),
                        Arrays.asList(RefundConstants.RefundField.BIZ_STATUS.getApiName()));
                result.setObjectData(ObjectDataDocument.of(data));
            }
            if (SFAConfigUtil.isCustomerAccountEnabled(actionContext.getTenantId())) {
                Map<String, String> header = Maps.newHashMap();
                header.put("x-fs-Employee-Id", actionContext.getUser().getUserId());
                header.put("x-fs-Enterprise-Id", actionContext.getUser().getTenantId());
                header.put("x-fs-ei", actionContext.getUser().getTenantId());
                header.put("x-fs-userInfo", actionContext.getUser().getUserId());
                if (data.get(RefundConstants.RefundField.REFUND_METHOD.getApiName()) != null) {
                    if (data.get(RefundConstants.RefundField.REFUND_METHOD.getApiName()).toString().equals(RefundConstants.RefundType.PrePay.getValue())) {
                        RefundCreateModel.Result rst = refundProxy.refundCreate(RefundCreateModel.Arg.builder()
                                        .prepayDetailData(RefundCreateModel.PrepayTransaction.builder()
                                                .amount(data.get(RefundConstants.RefundField.REFUNDED_AMOUNT.getApiName()).toString())
                                                .customer_id(data.get(RefundConstants.RefundField.ACCOUNT_ID.getApiName()).toString())
                                                .refund_id(data.getId())
                                                .life_status(data.get(FIELD_LIFE_STATUS).toString())
                                                .income_type("1")
                                                .transaction_time(System.currentTimeMillis())
                                                .build()).build()
                                , header);
                        if (!rst.IsSuccess()) {
                            log.warn(String.format("%s调用深研RefundCreatePrepay失败,msg:%s", data.getId(), rst.getErrMessage()));
                        }
                    } else if (data.get(RefundConstants.RefundField.REFUND_METHOD.getApiName()).toString().equals(RefundConstants.RefundType.Rebate.getValue())) {
                        RefundCreateModel.Result rst = refundProxy.refundCreate(
                                RefundCreateModel.Arg.builder().rebateIncomeDetailData(RefundCreateModel.RebateIncomeDetailData.builder()
                                        .amount(data.get(RefundConstants.RefundField.REFUNDED_AMOUNT.getApiName()).toString())
                                        .customer_id(data.get(RefundConstants.RefundField.ACCOUNT_ID.getApiName()).toString())
                                        .refund_id(data.getId())
                                        .income_type("1")
                                        .transaction_time(System.currentTimeMillis())
                                        .life_status(data.get(FIELD_LIFE_STATUS).toString())
                                        .build())
                                        .build(),
                                header);
                        if (!rst.IsSuccess()) {
                            log.warn(String.format("%s调用深研RefundCreateRebate失败,msg:%s", data.getId(), rst.getErrMessage()));
                        }
                    }
                }
            }
        }

        return result;
    }
}
