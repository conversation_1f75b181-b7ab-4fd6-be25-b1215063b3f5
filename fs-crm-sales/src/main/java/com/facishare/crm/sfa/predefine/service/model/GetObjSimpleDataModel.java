package com.facishare.crm.sfa.predefine.service.model;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * created by xiexd on 2021年3月1日
 */
public interface GetObjSimpleDataModel {
	
    @Data
    class Arg {
        @JsonProperty(value = "obj_api_name")
        @JSONField(name = "obj_api_name")
        private String objApiName;

        @JsonProperty(value = "object_id")
        @JSONField(name = "object_id")
        private String objectId;
        
        public boolean validParam() {
        	return StringUtils.isNotBlank(objApiName) && StringUtils.isNotBlank(objectId);
        }
    }

    @Data
    @AllArgsConstructor
    class Result {
        private ObjectDataDocument objectData;
    }
}
