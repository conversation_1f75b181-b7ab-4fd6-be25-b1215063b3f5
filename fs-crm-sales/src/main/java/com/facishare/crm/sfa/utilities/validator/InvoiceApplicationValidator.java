package com.facishare.crm.sfa.utilities.validator;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.constant.AccountConstants;
import com.facishare.crm.sfa.utilities.constant.InvoiceApplicationConstants;
import com.facishare.crm.sfa.utilities.constant.RefundConstants;
import com.facishare.crm.sfa.utilities.constant.SalesOrderConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.predef.action.BaseImportAction;
import com.facishare.paas.appframework.core.predef.action.BaseImportDataAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import javax.xml.ws.Service;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.List;

/**
 * Create by baoxinxue 2019/01/11
 */


public class InvoiceApplicationValidator {

    public static void validateSalesOrder(ServiceFacade serviceFacade,String tenantId,
                                                       IObjectData objectData){
        //检验订单信息
        SalesOrderValidator.validateSalesOrderBelongTo(serviceFacade,tenantId,objectData);
    }

    public static List<BaseImportAction.ImportError> validateImportData(ServiceFacade serviceFacade,String tenantId,
                                                                    List<BaseImportDataAction.ImportData> dataList){
        return validateOrderBelongToImportData(serviceFacade,tenantId,dataList);
    }

    public static List<BaseImportAction.ImportError> validateOrderBelongToImportData(ServiceFacade serviceFacade,String tenantId,
                                                                        List<BaseImportDataAction.ImportData> dataList){
        return SalesOrderValidator.validateSalesOrderBelongToForImport(serviceFacade,tenantId,dataList);
    }
}
