package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.utilities.util.TeamMemberUtil;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportTemplateAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.google.common.collect.Lists;

import java.util.List;

public class OpportunityInsertImportTemplateAction extends StandardInsertImportTemplateAction {
    private List<String> removeFields = Lists.newArrayList(
            "sales_stg_changed_time","status","leads_id","sales_process_id"
            ,"out_resources","last_followed_time"
            ,"biz_status","lost_reason","after_sale_stage_status","probability"
    );

    @Override
    protected void customHeader(List<IFieldDescribe> headerFieldList) {
        super.customHeader(headerFieldList);
        headerFieldList.removeIf(f -> removeFields.contains(f.getApiName()));
        TeamMemberUtil.addRelevantTeam(headerFieldList);
    }
}
