package com.facishare.crm.sfa.predefine.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.model.*;
import com.facishare.crm.sfa.utilities.proxy.WebPageProxy;
import com.facishare.crm.sfa.utilities.proxy.model.FindSystemMenuByApiNames;
import com.facishare.crm.sfa.utilities.proxy.model.MenuAddItem;
import com.facishare.crm.sfa.utilities.proxy.model.MenuAddItems;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.menu.MenuCommonService;
import com.facishare.paas.appframework.metadata.menu.MenuConstants;
import com.facishare.paas.appframework.metadata.menu.model.MenuItemConfigObject;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * crm菜单初始化类
 */
@ServiceModule("crm_menu_init")
@Service
@Slf4j
public class CrmMenuInitService {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private CrmMenuAdminService crmMenuAdminService;
    @Autowired
    private MenuCommonService menuCommonService;
    @Autowired
    private LicenseService licenseService;
    @Autowired
    private WebPageProxy webPageProxy;

    @ServiceMethod("init_system_menus")
    @Deprecated
    public InitSystemCrmArg.Result initSystemCrm(InitSystemCrmArg.Arg arg, ServiceContext context) {
        InitSystemCrmArg.Result result = InitSystemCrmArg.Result.builder().result(Maps.newHashMap()).build();
        return result;
    }

    @ServiceMethod("init_en_system_menus")
    @Deprecated
    public InitSystemCrmArg.Result initEnSystemCrm(InitSystemCrmArg.Arg arg, ServiceContext context) {
        InitSystemCrmArg.Result result = InitSystemCrmArg.Result.builder().result(Maps.newHashMap()).build();
        return result;
    }

    @ServiceMethod("menu_add_items")
    public CrmMenuInitAddObjects.Result menuAddObjects(CrmMenuInitAddObjects.Arg arg, ServiceContext context) {
        CrmMenuInitAddObjects.Result result = CrmMenuInitAddObjects.Result.builder().result(Maps.newHashMap()).build();
        List<String> tenantIds = Arrays.asList(arg.getTenantIds().split(","));
        for (String tenantId : tenantIds) {
            User user = new User(tenantId, "-10000");
            InitSystemCrmArg.ResultModel resultModel = new InitSystemCrmArg.ResultModel();
            try {
                List<String> apiNameList = Lists.newArrayList(arg.getApiNames().split(","));
                createMenuItem(user, apiNameList, arg.getAfterApiName());
                log.info("fs-crm:menuAddObjects:success,tenantId {}", tenantId);
                resultModel.setFlag(true);
            } catch (Exception e) {
                resultModel.setFlag(false);
                resultModel.setErrorMsg(e.getMessage());
                log.error("fs-crm:menuAddObjects:error,tenantId {} ", tenantId, e);
            }
            result.getResult().put(tenantId, resultModel);
        }
        return result;
    }

    @ServiceMethod("menu_add_item")
    public CrmMenuInitAddObject.Result menuAddItem(CrmMenuInitAddObject.Arg arg, ServiceContext context) {

        String tenantId = arg.getTenantId();
        String apiName = arg.getApiName();
        Integer order = arg.getOrder();

        CrmMenuInitAddObject.Result result = CrmMenuInitAddObject.Result.builder().result(true).build();
        try {
            MenuAddItem.MenuInitAddObjectArg menuInitAddObjectArg = new MenuAddItem.MenuInitAddObjectArg();
            {
                menuInitAddObjectArg.setTenantId(tenantId);
                menuInitAddObjectArg.setApiName(apiName);
                menuInitAddObjectArg.setOrder(order);

            }
            MenuAddItem.MenuInitAddObjectResult menuInitAddObjectResult = webPageProxy.menuAddItem(buildHeaderMap(String.valueOf(1)), menuInitAddObjectArg);
            result.setResult(menuInitAddObjectResult.isSuccess());
            log.info("fs-webpage-customer:menuAddItem:success,tenantId:{}, apiName:{}, order:{}", tenantId, apiName, order);
        } catch (Exception e) {
            log.error("menuAddItem error by tenantId:{}, apiName:{}, order:{}", tenantId, apiName, order);
        }

        return result;
    }


    @ServiceMethod("menu_add_item_group")
    @Deprecated
    public MenuAddItemGroup.Result menuAddItemGroup(MenuAddItemGroup.Arg arg, ServiceContext context) {
        MenuAddItemGroup.Result result = MenuAddItemGroup.Result.builder().result(true).build();
        String tenantIds = arg.getTenantIds();
        for (String tenantId : tenantIds.split(",")) {
            User user = new User(tenantId, "-10000");
            tenantId = tenantId.trim();
            IObjectData systemCrm = crmMenuAdminService.findDefaultCrmMenu(user);
            String menuId = systemCrm.getId();
            log.info("fs-crm:menuAddItemGroup:start,tenantId {}", tenantId);
            try {
                Map<String, Object> groupInfo = (Map) JSON.parseObject(arg.getConfig());
                List<IObjectData> menuItemList = buildObjectMenuItem(user, menuId);
                buildObjectMenuItemGroup(user, menuId, menuItemList);
                List<String> apiNameList = Lists.newArrayList();

                Map<String, String> apiNamesIndex = Maps.newHashMap();
                Map<String, String> groupNameMap = Maps.newHashMap();
                groupInfo.forEach((groupNameAndIndex, apiNames) -> {
                    if ("noGroup".equals(groupNameAndIndex)) {
                        List<String> apiNameAndIndex = JSON.parseArray(apiNames.toString(), String.class);
                        for (String nameAndIndex : apiNameAndIndex) {
                            String[] s = nameAndIndex.split(",");
                            apiNameList.add(s[0]);
                            apiNamesIndex.put(s[0], s[1]);
                        }
                    } else {
                        groupNameMap.put(groupNameAndIndex.split(",")[0], groupNameAndIndex.split(",")[1]);
                        apiNameList.addAll(Lists.newArrayList(apiNames.toString().split(",")));
                    }
                });
                menuItemList.removeIf(o ->
                        Objects.isNull(o.get(MenuConstants.MenuItemField.REFERENCEAPINAME.getApiName(), String.class))
                                ? !groupNameMap.keySet().contains(o.get(MenuConstants.MenuItemField.DISPLAYNAME.getApiName(), String.class))
                                : !apiNameList.contains(o.get(MenuConstants.MenuItemField.REFERENCEAPINAME.getApiName(), String.class))

                );
                for (IObjectData o : menuItemList) {
                    if (Objects.isNull(o.get(MenuConstants.MenuItemField.REFERENCEAPINAME.getApiName(), String.class))) {
                        o.set(MenuConstants.MenuItemField.NUMBER.getApiName(), groupNameMap.get(o.get(MenuConstants.MenuItemField.DISPLAYNAME.getApiName(), String.class)));
                    } else {
                        if ((apiNamesIndex.keySet().contains(o.get(MenuConstants.MenuItemField.REFERENCEAPINAME.getApiName(), String.class)))) {
                            o.set(MenuConstants.MenuItemField.NUMBER.getApiName(), apiNamesIndex.get(o.get(MenuConstants.MenuItemField.REFERENCEAPINAME.getApiName(), String.class)));
                        }
                    }

                }
                serviceFacade.bulkSaveObjectData(menuItemList, user);

                log.info("fs-crm:menuAddItemGroup:success,tenantId {}", tenantId);
            } catch (Exception e) {
                result.setResult(false);
                log.error("fs-crm:menuAddItemGroup:error,tenantId {} ", tenantId, e);
            }
        }
        return result;
    }

    @ServiceMethod("findMenuDataListByApiNames")
    public CrmMenuInitFindMenuDataList.CrmMenuInitFindMenuDataListResult findMenuDataListByApiNames(CrmMenuInitFindMenuDataList.CrmMenuInitFindMenuDataListArg arg,
                                                                                                    ServiceContext context) {

        CrmMenuInitFindMenuDataList.CrmMenuInitFindMenuDataListResult result = new CrmMenuInitFindMenuDataList.CrmMenuInitFindMenuDataListResult();

        try {
            FindSystemMenuByApiNames.FindSystemMenuDataByApiNamesArg findSystemMenuDataByApiNamesArg = new FindSystemMenuByApiNames.FindSystemMenuDataByApiNamesArg();
            findSystemMenuDataByApiNamesArg.setApiNames(arg.getApiNames());
            FindSystemMenuByApiNames.FindSystemMenuByApiNamesResult findSystemMenuByApiNamesResult = webPageProxy.findSystemMenuByApiNames(buildHeaderMap(context.getTenantId()), findSystemMenuDataByApiNamesArg);
            result.setMenuDataList(findSystemMenuByApiNamesResult.getMenuDataList());
        } catch (Exception e) {
            log.error("findSystemMenuByApiNames error by tenantId : {}, apiNames : {}", context.getTenantId(), arg.getApiNames(), e);
        }

        return result;
    }

    public CrmMenuInitFindMenuDataList.CrmMenuInitFindMenuDataListResult innerFindMenuDataListByApiNames(CrmMenuInitFindMenuDataList.CrmMenuInitFindMenuDataListArg arg,String tenantId) {

        CrmMenuInitFindMenuDataList.CrmMenuInitFindMenuDataListResult result = new CrmMenuInitFindMenuDataList.CrmMenuInitFindMenuDataListResult();

        try {
            FindSystemMenuByApiNames.FindSystemMenuDataByApiNamesArg findSystemMenuDataByApiNamesArg = new FindSystemMenuByApiNames.FindSystemMenuDataByApiNamesArg();
            findSystemMenuDataByApiNamesArg.setApiNames(arg.getApiNames());
            FindSystemMenuByApiNames.FindSystemMenuByApiNamesResult findSystemMenuByApiNamesResult = webPageProxy.findSystemMenuByApiNames(buildHeaderMap(tenantId), findSystemMenuDataByApiNamesArg);
            result.setMenuDataList(findSystemMenuByApiNamesResult.getMenuDataList());
        } catch (Exception e) {
            log.error("findSystemMenuByApiNames error by tenantId : {}, apiNames : {}", tenantId, arg.getApiNames(), e);
        }

        return result;
    }

    private void buildObjectMenuItemGroup(User user, String menuId, List<IObjectData> menuItemList) {
        List<String> versionAndPackages = licenseService.getVersionAndPackages(user.getTenantId());
        Set<String> versions = MenuConstants.menuItemsGroupDefaultConfig.keySet();
        String version = versionAndPackages.stream().filter(o -> versions.contains(o)).findFirst().orElse("default");
        Map<String, String> groupInfo = (Map) JSON.parseObject(MenuConstants.menuItemsGroupDefaultConfig.getString(version));
        Map<String, IObjectData> menuItemMap = menuItemList
                .stream()
                .collect(Collectors.toMap(o -> o.get(MenuConstants.MenuItemField.REFERENCEAPINAME.getApiName(), String.class), o -> o));
        List<String> needDeleteItems = Lists.newArrayList();
        List<IObjectData> needAddItems = Lists.newArrayList();
        IObjectDescribe menuItemDescribe = serviceFacade.findObject(user.getTenantId(), MenuConstants.MENU_ITEM_API_NAME);
        groupInfo.forEach((groupName, item) -> {
            ObjectData objectData = new ObjectData();
            objectData.set(MenuConstants.MenuItemField.DEFINETYPE.getApiName(), MenuConstants.MenuItemField.DEFINETYPE_STYSTEM.getApiName());
            objectData.set(MenuConstants.MenuItemField.MENUID.getApiName(), menuId);
            objectData.set(MenuConstants.MenuItemField.ITEMTYPE.getApiName(), MenuConstants.MenuItemField.ITEMTYPE_NOTOBJ.getApiName());
            objectData.setTenantId(user.getTenantId());
            objectData.setDescribeId(menuItemDescribe.getId());
            objectData.setDescribeApiName(menuItemDescribe.getApiName());
            objectData.set("display_name", groupName.split(",")[0]);
            objectData.set("number", groupName.split(",")[1]);
            objectData.set("type", "group");
            objectData.set("is_hidden", false);
            String id = serviceFacade.generateId();
            objectData.setId(id);
            needAddItems.add(objectData);
            if (Strings.isNotBlank(item)) {
                needDeleteItems.addAll(Lists.newArrayList(item.split(",")));
                for (String apiName : item.split(",")) {
                    IObjectData menuItem = menuItemMap.get(apiName);
                    menuItem.set("pid", id);
                    menuItem.set("is_hidden", false);
                    menuItem.set("type", "menu");
                    needAddItems.add(menuItem);
                }
            }
        });

        menuItemList.removeIf(o -> needDeleteItems.contains(o.get(MenuConstants.MenuItemField.REFERENCEAPINAME.getApiName())));
        menuItemList.forEach(o -> {
            o.set("is_hidden", false);
            o.set("type", "menu");
        });
        menuItemList.addAll(needAddItems);
        //未开启商品时移除商品、规格、规格值
        if (!SFAConfigUtil.isSpuOpen(user.getTenantId())) {
            List<String> spuItems = Lists.newArrayList(Utils.SPU_API_NAME, Utils.SPECIFICATION_API_NAME,
                    Utils.SPECIFICATION_VALUE_API_NAME);
            menuItemList.removeIf(item ->
                    spuItems.contains(item.get(MenuConstants.MenuItemField.REFERENCEAPINAME.getApiName(), String.class)));
        }
    }


    /**
     * 构建预设对象菜单项和自定义对象CRM菜单
     */
    private List<IObjectData> buildObjectMenuItem(User user, String menuId) {
        IObjectDescribe menuItemDescribe = serviceFacade.findObject(user.getTenantId(), MenuConstants.MENU_ITEM_API_NAME);
        //配置的预设对象菜单
        List<MenuItemConfigObject> configObjectList = MenuConstants.configMenuItemList;
        List<IObjectData> menuItemList = configObjectList.stream().map(menuItemConfigObject -> {
            IObjectData menuItem = menuCommonService.buildMenuItem(user, menuId, menuItemDescribe, menuItemConfigObject.getApiName());
            menuItem.set(MenuConstants.MenuItemField.NUMBER.getApiName(), menuItemConfigObject.getNumber());
            return menuItem;
        }).collect(Collectors.toList());
        //自定义对象菜单
        List<IObjectDescribe> customDescribeList = crmMenuAdminService.findUserCustomDescribe(user, false, false, true);
        //自定义对象默认排序号从10000开始
        AtomicInteger order = new AtomicInteger(10000);
        //此apinames是为了验证保证最后生成菜单的apiname不重复
        Set apiNames = Sets.newHashSet();
        List<IObjectData> customMenuItemList = customDescribeList.stream().filter(k -> !apiNames.contains(k.getApiName())).map(k -> {
            apiNames.add(k.getApiName());
            IObjectData menuItem = menuCommonService.buildMenuItem(user, menuId, menuItemDescribe, k.getApiName());
            menuItem.set(MenuConstants.MenuItemField.NUMBER.getApiName(), order.intValue());
            order.getAndAdd(5);
            return menuItem;
        }).collect(Collectors.toList());
        menuItemList.addAll(customMenuItemList);
        return menuItemList;
    }

    /**
     * 在预制CRM菜单下对象新增对象菜单项 apiName 要增加的对象apiName afterApiName 当前apiname要跟随指定的apiname后
     */
    @Transactional
    public void createMenuItem(User user, List<String> apiNameList, String afterApiName) {

        String tenantId = user.getTenantId();

        try {
            MenuAddItems.MenuInitAddObjectsArg arg = new MenuAddItems.MenuInitAddObjectsArg();
            arg.setTenantId(Integer.parseInt(tenantId));
            arg.setApiNames(apiNameList);
            arg.setAfterApiName(afterApiName);
            MenuAddItems.MenuInitAddObjectsResult result = webPageProxy.menuAddObjects(buildHeaderMap(user.getTenantId()), arg);
            log.info("fs-webpage-customer:createMenuItem:success,tenantId:{}, apiNames:{}, afterApiName:{}", tenantId, apiNameList, afterApiName);
        } catch (Exception e) {
            log.error("menuAddObjects error by tenantId:{}, apiNameList:{}, afterApiName: {}", tenantId, apiNameList, afterApiName, e);
        }

    }

    private Map<String, String> buildHeaderMap(String tenantId) {
        Map<String, String> headerMap = Maps.newHashMap();
        headerMap.put("X-fs-Enterprise-Id", tenantId);
        headerMap.put("X-fs-ei", tenantId);
        headerMap.put("X-fs-Employee-Id", "-10000");
        return headerMap;
    }
}
