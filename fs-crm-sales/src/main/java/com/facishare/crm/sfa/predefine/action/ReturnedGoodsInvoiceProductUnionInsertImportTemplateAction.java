package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.utilities.util.ReturnedGoodsInvoiceUtil;
import com.facishare.paas.appframework.core.predef.action.StandardUnionInsertImportTemplateAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;

import java.util.List;

/**
 * Created by renlb on 2019/5/11.
 */
public class ReturnedGoodsInvoiceProductUnionInsertImportTemplateAction extends StandardUnionInsertImportTemplateAction {

    @Override
    protected void customDetailHeader(List<IFieldDescribe> headerFieldList) {
        super.customDetailHeader(headerFieldList);
        ReturnedGoodsInvoiceUtil.removeUnSupportedDetailFields(headerFieldList);
    }
}
