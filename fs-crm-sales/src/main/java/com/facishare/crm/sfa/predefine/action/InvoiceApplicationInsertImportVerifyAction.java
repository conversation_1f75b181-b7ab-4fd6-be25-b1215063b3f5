package com.facishare.crm.sfa.predefine.action;

import com.facishare.paas.appframework.core.predef.action.StandardInsertImportVerifyAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.google.common.collect.Lists;

import java.util.List;

public class InvoiceApplicationInsertImportVerifyAction extends StandardInsertImportVerifyAction {
    private List<String> removeFields = Lists.newArrayList(
            "attach", "life_status","owner_department","biz_status","is_fixed_flow","status","name","submit_time"
    );
    @Override
    protected List<IFieldDescribe> getValidImportFields() {
        List<IFieldDescribe> fields = super.getValidImportFields();
        fields.removeIf(f -> removeFields.contains(f.getApiName()));
        return fields;
    }
}
