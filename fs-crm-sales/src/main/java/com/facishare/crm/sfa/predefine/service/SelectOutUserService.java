package com.facishare.crm.sfa.predefine.service;

import com.facishare.crm.sfa.predefine.service.model.OutUserSelectArg;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.result.EmployeeCardSimpleResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@ServiceModule(value = "select_out_user")
@Slf4j
@Service
public class SelectOutUserService {

    @Autowired
    private PartnerService partnerService;

    @ServiceMethod("list")
    public RestResult<List<EmployeeCardSimpleResult>> listAllOutUser(OutUserSelectArg arg, ServiceContext context) {
        return partnerService.listAllOutUser(Long.valueOf(arg.getDestEnterpriseAccount()), Integer.valueOf(context.getTenantId()), context.getUser().getUserId());


    }

}
