package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.real.MultiUnitService;
import com.facishare.crm.sfa.predefine.service.real.MultiUnitServiceImpl;
import com.facishare.crm.sfa.utilities.util.JsonObjectUtils;
import com.facishare.crm.sfa.utilities.util.LayoutUtils;
import com.facishare.crm.sfa.utilities.util.MultiUnitRelatedUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.SystemErrorCode;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardDetailController;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.component.GroupComponent;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Created by luxin on 2018/12/9.
 */
public class SPUDetailController extends StandardDetailController {
    private final MultiUnitService multiUnitService = SpringUtil.getContext().getBean(MultiUnitServiceImpl.class);


    @Override
    protected void before(Arg arg) {
        // 校验版本
        multiUnitService.validateMobileClientVersion(controllerContext.getRequestContext());
        super.before(arg);
    }

    @Override
    protected Result doService(Arg arg) {
        Result result = super.doService(arg);
        if (RequestUtil.isMobileRequest()) {
            result = JsonObjectUtils.remove(result, Result.class, "$.layout.components[?(@.api_name=='relatedObject')]" +
                    ".child_components[?(@.api_name=='relevant_team_component')].buttons");
        }
        return result;
    }

    @Override
    protected ILayout getLayout() {
        ILayout layout = super.getLayout();
        LayoutExt layoutExt = LayoutExt.of(layout);
        MultiUnitRelatedUtils.handleMultiUnitRelatedComponent(layoutExt, data);
        layoutExt.getFormComponent().ifPresent(o -> LayoutUtils.removeFormComponentAddAndEditButton4MobileOrH5(o));
        handleRelatedComponent(layoutExt.getRelatedComponent());
        LayoutUtils.removeButtons4MobileOrH5(
                layoutExt,
                Lists.newArrayList(ObjectAction.CREATE.getActionCode(),
                        ObjectAction.UPDATE.getActionCode(),
                        ObjectAction.CLONE.getActionCode(),
                        ObjectAction.INVALID.getActionCode(),
                        ObjectAction.CHANGE_OWNER.getActionCode())
        );
        //屏蔽掉商品的复制按钮
        LayoutUtils.removeButtons(layoutExt, Lists.newArrayList(ObjectAction.CLONE.getActionCode()));

        return layout;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        // 是web请求且是有规格的数据才下发是否有'规格值' 新建的权限
        if (RequestUtil.isWebRequest() && Objects.equals(Boolean.TRUE, data.get("is_spec", Boolean.class))) {
            ObjectDataDocument data = result.getData();
            boolean hasSpecValueCreatePrivilege = serviceFacade.funPrivilegeCheck(controllerContext.getUser(), Utils.SPECIFICATION_VALUE_API_NAME, ObjectAction.CREATE.getActionCode());
            data.put("hasSpecValueCreatePrivilege", hasSpecValueCreatePrivilege);

        }
        return super.after(arg, result);
    }

    /**
     * 处理相关的 component
     */
    private void handleRelatedComponent(Optional<GroupComponent> groupComponentOptional) {

        if (groupComponentOptional.isPresent()) {
            GroupComponent groupComponent = groupComponentOptional.get();
            handleProductRelatedComponent(groupComponent);
        }
    }

    /**
     * 处理商品下 产品页签下的新建编辑按钮
     */
    private void handleProductRelatedComponent(GroupComponent groupComponent) {
        try {
            List<IComponent> childComponents = groupComponent.getChildComponents();

            Optional<IComponent> productRelatedComponentOptional = childComponents.stream()
                    .filter(o -> Objects.equals("ProductObj_spu_id_related_list", o.getName()))
                    .findFirst();

            // 删除商品下相关页签下关联的产品的 新建 button
            productRelatedComponentOptional.ifPresent(o -> {
                List<IButton> buttons = o.getButtons();
                buttons.removeIf(b -> Objects.equals(ObjectAction.CREATE.getActionCode(), b.getAction())
                        || Objects.equals(ObjectAction.UPDATE.getActionCode(), b.getAction())
                );
                o.setButtons(buttons);
            });
        } catch (MetadataServiceException e) {
            throw new MetaDataException(SystemErrorCode.METADATA_COMPONENTS_ERROR, e);
        }
    }
}
