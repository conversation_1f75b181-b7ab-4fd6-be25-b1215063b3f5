package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.util.LayoutUtils;
import com.facishare.crm.sfa.utilities.util.VersionUtil;
import com.facishare.paas.appframework.core.predef.controller.StandardDetailController;
import com.facishare.paas.appframework.metadata.LayoutExt;

import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created by luxin on 2018/1/31.
 */
@Slf4j
@Component
public class ReturnedGoodsInvoiceProductDetailController extends StandardDetailController {
    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        if (result.getLayout() != null) {
            result.getLayout().toLayout().setButtons(Lists.newArrayList());
        }

        return result;
    }

    @Override
    protected ILayout getLayout() {
        ILayout layout = super.getLayout();
        if (layout == null) {
            return layout;
        }

        try {
            removeRelatedComponent(layout);
        } catch (MetadataServiceException e) {
        }
        LayoutUtils.removeDetailButtons(layout);
        return layout;
    }


    //退货单产品销售记录移除新建按钮
    private void removeRelatedComponent(ILayout layout) throws MetadataServiceException {
        log.info("org layout {}", layout);
        LayoutExt.of(layout).getRelatedComponent().ifPresent(x -> {
            try {
                List<IComponent> components = x.getChildComponents();
                for (IComponent component : components) {
                    if ("sale_log".equals(component.getName())) {
                        component.setButtons(Lists.newArrayList());
                    }
                }
            } catch (MetadataServiceException ignored) {
            }
        });
        log.info("res layout {}", layout);
    }
}
