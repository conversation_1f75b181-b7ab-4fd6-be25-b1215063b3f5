package com.facishare.crm.sfa.predefine.action.listener;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.proxy.ReturnedGoodsInvoiceProxy;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.ReturnedGoodsInvoiceInterceptorModel;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.returngoodsparam.ReturnedGoodsInvoiceAddFlowCompletedAfterModel;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.returngoodsparam.ReturnedGoodsInvoiceEditFlowCompletedAfterModel;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.returngoodsparam.ReturnedGoodsInvoiceInvalidFlowCompletedAfterModel;
import com.facishare.crm.sfa.utilities.util.SoCommonUtils;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.StandardFlowCompletedAction;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * Created by renlb on 2019/3/14.
 */
@Component
public class ReturnedGoodsInvoiceFlowCompletedActionListener implements ActionListener<StandardFlowCompletedAction.Arg, StandardFlowCompletedAction.Result> {

    private static final ReturnedGoodsInvoiceProxy returnedGoodsInvoiceProxy = SpringUtil.getContext().getBean(ReturnedGoodsInvoiceProxy.class);

    @Autowired
    ServiceFacade serviceFacade;

    @Override
    public void before(ActionContext actionContext, StandardFlowCompletedAction.Arg arg) {

    }

    @Override
    public void after(ActionContext actionContext, StandardFlowCompletedAction.Arg arg, StandardFlowCompletedAction.Result result) {
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIdsIncludeDeletedIgnoreFormula(
                actionContext.getUser(),
                Lists.newArrayList(arg.getDataId()),
                Utils.RETURN_GOODS_INVOICE_API_NAME);
        IObjectData objectData = null;
        if(CollectionUtils.notEmpty(objectDataList)) {
            objectData = objectDataList.get(0);
        }
        if(objectData != null) {
            Map<String, String> headers = SoCommonUtils.getCrmHeader(actionContext.getTenantId(), actionContext.getUser());
            ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
            if(ApprovalFlowTriggerType.CREATE.equals(arg.approvalFlowTriggerType())) {
                ReturnedGoodsInvoiceAddFlowCompletedAfterModel.Arg serviceArg = new ReturnedGoodsInvoiceAddFlowCompletedAfterModel.Arg();
                serviceArg.setDataId(objectDataExt.getId());
                serviceArg.setBeforeLifeStatus(ObjectLifeStatus.UNDER_REVIEW.getCode());
                serviceArg.setAfterLifeStatus(objectDataExt.getLifeStatus().getCode());
                ServiceContext context = ContextManager.buildServiceContext("ReturnedGoodsInvoiceInterceptor",
                        "AddFlowCompletedAfter");
//                returnedGoodsInvoiceInterceptorService.addFlowCompletedAfter(context, serviceArg);
                ReturnedGoodsInvoiceInterceptorModel.AddFlowCompletedAfterResult addFlowCompletedAfterResult = returnedGoodsInvoiceProxy.addFlowCompletedAfter(serviceArg, headers);
                if(!addFlowCompletedAfterResult.isSuccess()){
                    throw new ValidateException(addFlowCompletedAfterResult.getMessage());
                }
            } else if(ApprovalFlowTriggerType.UPDATE.equals(arg.approvalFlowTriggerType())) {
                ReturnedGoodsInvoiceEditFlowCompletedAfterModel.Arg serviceArg = new ReturnedGoodsInvoiceEditFlowCompletedAfterModel.Arg();
                serviceArg.setDataId(objectDataExt.getId());
                serviceArg.setBeforeLifeStatus(ObjectLifeStatus.IN_CHANGE.getCode());
                serviceArg.setAfterLifeStatus(objectDataExt.getLifeStatus().getCode());
                ServiceContext context = ContextManager.buildServiceContext("ReturnedGoodsInvoiceInterceptor",
                        "EditFlowCompletedAfter");
//                returnedGoodsInvoiceInterceptorService.editFlowCompletedAfter(context, serviceArg);
                ReturnedGoodsInvoiceInterceptorModel.EditFlowCompletedAfterResult editFlowCompletedAfterResult = returnedGoodsInvoiceProxy.editFlowCompletedAfter(serviceArg, headers);
                if(!editFlowCompletedAfterResult.isSuccess()){
                    throw new ValidateException(editFlowCompletedAfterResult.getMessage());
                }
            } else if(ApprovalFlowTriggerType.INVALID.equals(arg.approvalFlowTriggerType())) {
                ReturnedGoodsInvoiceInvalidFlowCompletedAfterModel.Arg serviceArg = new ReturnedGoodsInvoiceInvalidFlowCompletedAfterModel.Arg();
                serviceArg.setDataId(objectDataExt.getId());
                serviceArg.setBeforeLifeStatus(ObjectLifeStatus.IN_CHANGE.getCode());
                serviceArg.setAfterLifeStatus(objectDataExt.getLifeStatus().getCode());
                ServiceContext context = ContextManager.buildServiceContext("ReturnedGoodsInvoiceInterceptor",
                        "InvalidFlowCompletedAfter");
//                returnedGoodsInvoiceInterceptorService.invalidFlowCompletedAfter(context, serviceArg);
                ReturnedGoodsInvoiceInterceptorModel.InvalidFlowCompletedAfterResult invalidFlowCompletedAfterResult = returnedGoodsInvoiceProxy.invalidFlowCompletedAfter(serviceArg, headers);
                if(!invalidFlowCompletedAfterResult.isSuccess()){
                    throw new ValidateException(invalidFlowCompletedAfterResult.getMessage());
                }
            }
        }
    }
}
