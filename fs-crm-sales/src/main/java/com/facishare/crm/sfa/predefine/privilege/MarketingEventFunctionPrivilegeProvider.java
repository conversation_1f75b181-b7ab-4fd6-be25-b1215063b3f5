package com.facishare.crm.sfa.predefine.privilege;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.privilege.model.DefaultFunctionPrivilegeProvider;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component
public class MarketingEventFunctionPrivilegeProvider extends DefaultFunctionPrivilegeProvider {

    private final static List<String> SUPPORT_ACTION_CODES = Lists.newArrayList(
            ObjectAction.VIEW_DETAIL.getActionCode(),
            ObjectAction.VIEW_LIST.getActionCode(),
            ObjectAction.CREATE.getActionCode(),
            ObjectAction.UPDATE.getActionCode(),
            ObjectAction.DELETE.getActionCode(),
            ObjectAction.CHANGE_OWNER.getActionCode(),
            ObjectAction.INVALID.getActionCode(),
            ObjectAction.RECOVER.getActionCode(),
            ObjectAction.BATCH_IMPORT.getActionCode(),
            ObjectAction.BATCH_EXPORT.getActionCode(),
            ObjectAction.EDIT_TEAM_MEMBER.getActionCode(),
            ObjectAction.PRINT.getActionCode(),
            ObjectAction.VIEW_ENTIRE_BPM.getActionCode(),
            ObjectAction.START_BPM.getActionCode(),
            ObjectAction.STOP_BPM.getActionCode(),
            ObjectAction.CHANGE_BPM_APPROVER.getActionCode(),
            ObjectAction.LOCK.getActionCode(),
            ObjectAction.UNLOCK.getActionCode(),
            ObjectAction.UPLOAD_DELETE_ATTACH.getActionCode(),
            ObjectAction.VIEW_ATTACH.getActionCode(),
            ObjectAction.CHANGE_STATES.getActionCode(),
            ObjectAction.MODIFYLOG_RECOVER.getActionCode()

    );

    @Override
    public String getApiName() {
        return SFAPreDefineObject.MarketingEvent.getApiName();
    }


    @Override
    public List<String> getSupportedActionCodes() {
        return Collections.unmodifiableList(SUPPORT_ACTION_CODES);
    }

    @Override
    public Map<String, List<String>> getCustomInitRoleActionCodes() {
        Map<String, List<String>> actionCodeMap = Maps.newHashMap();
        actionCodeMap.put("00000000000000000000000000000015", Collections.unmodifiableList(Lists.newArrayList(ObjectAction.VIEW_LIST.getActionCode(), ObjectAction.VIEW_DETAIL.getActionCode(), ObjectAction.CREATE.getActionCode(), ObjectAction.UPDATE.getActionCode(), ObjectAction.CHANGE_OWNER.getActionCode(), ObjectAction.INVALID.getActionCode(), ObjectAction.BATCH_IMPORT.getActionCode(), ObjectAction.CHANGE_STATES.getActionCode(), ObjectAction.UPLOAD_DELETE_ATTACH.getActionCode(), ObjectAction.VIEW_ATTACH.getActionCode(), ObjectAction.EDIT_TEAM_MEMBER.getActionCode())));
        actionCodeMap.put("00000000000000000000000000000009", Collections.unmodifiableList(Lists.newArrayList(ObjectAction.VIEW_LIST.getActionCode(), ObjectAction.VIEW_DETAIL.getActionCode(), ObjectAction.VIEW_ATTACH.getActionCode())));
        actionCodeMap.put("00000000000000000000000000000017", Collections.unmodifiableList(Lists.newArrayList(ObjectAction.VIEW_LIST.getActionCode())));
        return Collections.unmodifiableMap(actionCodeMap);
    }
}
