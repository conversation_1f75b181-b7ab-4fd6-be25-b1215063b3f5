package com.facishare.crm.sfa.predefine.service;

import com.facishare.crm.sfa.predefine.service.real.scanbarcode.AbstractScanBarcodeService;
import com.facishare.crm.sfa.predefine.service.real.scanbarcode.ScanBarcodeServiceManager;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@ServiceModule("MUSBarcode")
@Component
public class MUSBarcodeService {
    @Autowired
    private ScanBarcodeServiceManager scanBarcodeServiceManager;
    @Autowired
    private ModuleCtrlConfigService moduleCtrlConfigService;


    @ServiceMethod("scanBarcode")
    public StandardRelatedListController.Result scanBarcode(ServiceContext serviceContext, StandardRelatedListController.Arg arg) {
        String apiName = arg.getObjectApiName();
        if (StringUtils.isBlank(apiName)) {
            throw new ValidateException("associated_object_describe_api_name is blank.");
        }

        boolean multipleUnitOpen = moduleCtrlConfigService.isOpen("multiple_unit", serviceContext.getUser());

        AbstractScanBarcodeService scanBarcodeService = scanBarcodeServiceManager.getScanBarcodeService(
                apiName,
                multipleUnitOpen,
                (String) arg.getObjectData().getOrDefault("object_describe_api_name", "undefined")
        );
        return scanBarcodeService.getScanBarcodeResult(arg, serviceContext.getRequestContext());
    }

}
