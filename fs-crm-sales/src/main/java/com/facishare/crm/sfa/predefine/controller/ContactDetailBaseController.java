package com.facishare.crm.sfa.predefine.controller;


import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.util.ContactUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.facishare.paas.appframework.core.model.RequestContext.CLIENT_INFO;

/**
 * Created by luohl on 2017/11/14.
 */
@Slf4j
public class ContactDetailBaseController extends SFADetailController {
    private FunctionPrivilegeService functionPrivilegeService = SpringUtil.getContext().getBean("functionPrivilegeService", FunctionPrivilegeService.class);


    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);

        ObjectDataDocument objectData = result.getData();
        //web处理是否决策人这个字段类型时，无法采用字符串方式
        Object primaryContact = objectData.get("primary_contact");
        if (primaryContact != null && primaryContact instanceof Integer) {
            objectData.put("primary_contact", String.valueOf(primaryContact));
            if (Objects.equals(primaryContact, 0)) {
                objectData.put("primary_contact", "");
            }
        }
        handleInvalidBirthDayField(result);
        handleInvalidCardField(newResult);

        if (newResult.getLayout() != null) {
            ILayout layout = new Layout(newResult.getLayout());
            specialLogicForLayout(layout);
        }
        return newResult;
    }


    protected void specialLogicForLayout(ILayout layout) {
        String clientInfo = getControllerContext().getRequestContext().getAttribute(CLIENT_INFO);

        LayoutExt.of(layout).getRelatedComponent().ifPresent(x -> {
            try {
                List<IComponent> childComponents = x.getChildComponents();

                //移除销售线索按钮
                removeLeadsObjButtons(childComponents);
                x.setChildComponents(childComponents);
            } catch (MetadataServiceException e) {
                log.error("getChildComponents error", e);
            }
        });
        removeMobileClientAndPcButtons(layout, clientInfo);

        LayoutExt layoutExt = LayoutExt.of(layout);
        layoutExt.getFormComponent().ifPresent(formComponentExt ->
                ContactUtil.setTelMobileFieldProperty(controllerContext.getUser(), Utils.OPPORTUNITY_API_NAME,
                        (FormComponent) formComponentExt.getFormComponent()));


    }

    private void handleInvalidBirthDayField(Result result) {
        //处理生日字段 生日字段可能存在 0000-00-00/0000-12-17/1991-10-00//1991-00-00这四种状态,需要特殊处理
        String birthDay = (String) result.getData().get("date_of_birth");
        if (StringUtils.isEmpty(birthDay) || "0000-00-00".equals(result.getData().get("date_of_birth"))) {
            result.getData().put("date_of_birth", "");
        } else if (birthDay.startsWith("0000-")) {
            result.getData().put("date_of_birth", birthDay.replaceAll("0000-", ""));
        } else {
            result.getData().put("date_of_birth", birthDay.replaceAll("-00", ""));
        }
    }


    private void handleInvalidCardField(Result newResult) {
        Object card = (newResult.getData()).get("card");
        if (card instanceof List) {
            List cards = (List) card;
            if (cards.isEmpty()) {
                newResult.getData().put("card", Lists.newArrayList());
            } else {
                cards.stream().findFirst().ifPresent(x -> {
                    if (x instanceof Map) {
                        Object path = ((Map) x).get("path");
                        if (path == null || "".equals(path)) {
                            newResult.getData().put("card", Lists.newArrayList());
                        }
                    } else {
                        newResult.getData().put("card", Lists.newArrayList());
                    }
                });
            }
        }
    }


    private void removeMobileClientAndPcButtons(ILayout layout, String clientInfo) {
        List<IButton> buttons = layout.getButtons();
        removeCommonButtons(buttons);

        //手机端需要做的特殊处理
        if (!Strings.isNullOrEmpty(clientInfo) && RequestUtil.isMobileOrH5Request()) {
            removePhoneButtons(buttons);
            List<IButton> remainButtons = Lists.newCopyOnWriteArrayList(buttons);
            buttons.clear();
            buttons.add(createButton(ObjectAction.SALE_RECORD));
            buttons.add(createButton(ObjectAction.DIAL));
            buttons.add(createButton(ObjectAction.SEND_MAIL));
            buttons.add(createButton(ObjectAction.DISCUSS));
            buttons.add(createButton(ObjectAction.SCHEDULE));
            buttons.add(createButton(ObjectAction.REMIND));
            buttons.add(createButton(ObjectAction.PRINT));
            buttons.add(createButton("SaveToPhone", I18N.text("paas.udobj.action.save_to_phone")));
            buttons.addAll(remainButtons);
        }
        layout.setButtons(buttons);
    }

    private void removeLeadsObjButtons(List<IComponent> childComponents) {
        childComponents.stream().forEach(childComponent -> {
            if ("LeadsObj".equals(childComponent.get("ref_object_api_name", String.class))) {
                List<IButton> childComponentButtons = childComponent.getButtons();
                childComponentButtons.removeIf(button -> ObjectAction.CREATE.getActionCode().equals(button.getAction())
                        || ObjectAction.BULK_RELATE.getActionCode().equals(button.getAction())
                        || ObjectAction.BULK_DISRELATE.getActionCode().equals(button.getAction()));
                childComponent.setButtons(childComponentButtons);
            }
        });
    }

    private void removeCommonButtons(List<IButton> buttons) {
        buttons.removeIf(k -> k.getAction().equals(ObjectAction.VIEW_DETAIL.getActionCode())
                || ObjectAction.CREATE.getActionCode().equals(k.getAction())
                || ObjectAction.ADD_EVENT.getActionCode().equals(k.getAction())
                || ObjectAction.BATCH_EXPORT.getActionCode().equals(k.getAction())
                || ObjectAction.BATCH_IMPORT.getActionCode().equals(k.getAction())
                || ObjectAction.VIEW_ENTIRE_BPM.getActionCode().equals(k.getAction())
                || ObjectAction.CHANGE_BPM_APPROVER.getActionCode().equals(k.getAction())
                || ObjectAction.VIEW_FEED_CARD.getActionCode().equals(k.getAction())
                || ObjectAction.SALE_RECORD.getActionCode().equals(k.getAction())
                || ObjectAction.DIAL.getActionCode().equals(k.getAction())
                || "SaveToPhone".equals(k.getAction())
                || "ModifyLog_Recover".equals(k.getAction())
                || "Share".equals(k.getAction())
                || "Merge".equals(k.getAction()));
    }

    private void removePhoneButtons(List<IButton> buttons) {
        buttons.removeIf(k -> ObjectAction.EDIT_TEAM_MEMBER.getActionCode().equals(k.getAction())
                || ObjectAction.SCHEDULE.getActionCode().equals(k.getAction())
                || ObjectAction.REMIND.getActionCode().equals(k.getAction())
                || ObjectAction.DISCUSS.getActionCode().equals(k.getAction())
                || ObjectAction.SEND_MAIL.getActionCode().equals(k.getAction())
                || ObjectAction.STOP_BPM.getActionCode().equals(k.getAction())
                || ObjectAction.PRINT.getActionCode().equals(k.getAction()));
    }

    private IButton createButton(ObjectAction action) {
        return createButton(action.getActionCode(), action.getActionLabel());
    }

    private IButton createButton(String actionCode, String actionLabel) {
        IButton button = new Button();
        button.setName(actionCode + "_button_" + IButton.ACTION_TYPE_DEFAULT);
        button.setAction(actionCode);
        button.setActionType(IButton.ACTION_TYPE_DEFAULT);
        button.setLabel(actionLabel);
        return button;
    }
}
