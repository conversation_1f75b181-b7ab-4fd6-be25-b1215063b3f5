package com.facishare.crm.sfa.predefine.action.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.salesorderparam.BulkInvalidAfterModel;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.salesorderparam.BulkInvalidBeforeModel;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Created by renlb on 2019/3/14.
 */
@Component
public class StandardBulkInvalidActionListener implements ActionListener<StandardBulkInvalidAction.Arg, StandardBulkInvalidAction.Result> {

    @Autowired
    ServiceFacade serviceFacade;

    @Override
    public void before(ActionContext actionContext, StandardBulkInvalidAction.Arg arg) {
        List<String> ids = getDataIds(arg);
        if(CollectionUtils.notEmpty(ids)) {
            List<IObjectData> objectDataList = serviceFacade.findObjectDataByIdsIncludeDeleted(actionContext.getUser(),
                    ids, actionContext.getObjectApiName());
            if(CollectionUtils.notEmpty(objectDataList)) {
                BulkInvalidBeforeModel.Arg serviceArg = new BulkInvalidBeforeModel.Arg();
                List<BulkInvalidBeforeModel.BulkObj> bulkObjs = Lists.newArrayList();
                JSONObject jsonObject = JSON.parseObject(arg.getJson());
                JSONArray jsonArray = jsonObject.getJSONArray("dataList");
                for(int i = 0; i < jsonArray.size(); ++i) {
                    String dataId = jsonArray.getJSONObject(i).getString("_id");
                    Optional<IObjectData> objectData = objectDataList.stream()
                            .filter(r -> r.getId().equals(dataId))
                            .findFirst();
                    if(objectData.isPresent()) {
                        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData.get());
                        BulkInvalidBeforeModel.BulkObj bulkObj = new BulkInvalidBeforeModel.BulkObj();
                        bulkObj.setDataId(objectDataExt.getId());
                        bulkObj.setNowLifeStatus(objectDataExt.getLifeStatus().getCode());
                        bulkObjs.add(bulkObj);
                        jsonArray.getJSONObject(i).put("original_life_status", objectDataExt.getLifeStatus().getCode());
                    }
                }
                arg.setJson(JSON.toJSONString(jsonObject));

                serviceArg.setBulkObjs(bulkObjs);
                callBeforeInterceptor(serviceArg);
            }
        }
    }

    @Override
    public void after(ActionContext actionContext, StandardBulkInvalidAction.Arg arg, StandardBulkInvalidAction.Result result) {
        if(CollectionUtils.notEmpty(result.getObjectDataList())) {
            JSONObject jsonObject = JSON.parseObject(arg.getJson());
            JSONArray jsonArray = jsonObject.getJSONArray("dataList");
            BulkInvalidAfterModel.Arg serviceArg = new BulkInvalidAfterModel.Arg();
            List<BulkInvalidAfterModel.BulkObj> bulkObjs = Lists.newArrayList();
            for(int i = 0; i < jsonArray.size(); ++i) {
                String dataId = jsonArray.getJSONObject(i).getString("_id");
                Optional<ObjectDataDocument> objectData = result.getObjectDataList().stream()
                        .filter(r -> r.getId().equals(dataId))
                        .findFirst();
                if(objectData.isPresent()) {
                    ObjectDataExt objectDataExt = ObjectDataExt.of(objectData.get().toObjectData());
                    BulkInvalidAfterModel.BulkObj bulkObj = new BulkInvalidAfterModel.BulkObj();
                    bulkObj.setDataId(objectDataExt.getId());
                    bulkObj.setBeforeLifeStatus(jsonArray.getJSONObject(i).getString("original_life_status"));
                    bulkObj.setAfterLifeStatus(objectDataExt.getLifeStatus().getCode());
                    bulkObjs.add(bulkObj);
                }
            }

            serviceArg.setBulkObjs(bulkObjs);

            callAfterInterceptor(serviceArg);
        }
    }

    protected void callBeforeInterceptor(BulkInvalidBeforeModel.Arg arg) {

    }

    protected void callAfterInterceptor(BulkInvalidAfterModel.Arg arg) {

    }

    private List<String> getDataIds(StandardBulkInvalidAction.Arg arg) {
        JSONObject jsonObject = JSON.parseObject(arg.getJson());
        JSONArray jsonArray = jsonObject.getJSONArray("dataList");
        return (List)jsonArray.stream().map((x) -> {
            return ((JSONObject)x).getString("_id");
        }).collect(Collectors.toList());
    }
}
