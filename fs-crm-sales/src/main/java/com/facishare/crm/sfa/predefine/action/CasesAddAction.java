package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.utilities.util.SalesConfigUtil;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;

import java.util.Map;

/**
 * @description:
 * @author: guom
 * @date: Created in 2021/3/11 16:04
 */
@Slf4j
public class CasesAddAction extends StandardAddAction {

    @Override
    protected void before(Arg arg) {
        super.before(arg);

        resetData(arg.getObjectData());

        log.debug("CasesAddAction before, arg={}", arg);
    }

    public void resetData(ObjectDataDocument objectDataDocument) {
        if (objectDataDocument == null) {
            return ;
        }
        Map<String, Object> casesResetData = SalesConfigUtil.getCasesResetData();
        if (MapUtils.isEmpty(casesResetData)) {
            return ;
        }
        try {
            casesResetData.forEach((k ,v) -> {
                if (objectDataDocument.containsKey(k)) {
                    objectDataDocument.put(k, v);
                }
            });
        } catch (Exception e) {
            log.warn("Fail to resetData, objectDataDocument={}, casesResetData={}",
                    objectDataDocument, casesResetData, e);
        }

    }
}
