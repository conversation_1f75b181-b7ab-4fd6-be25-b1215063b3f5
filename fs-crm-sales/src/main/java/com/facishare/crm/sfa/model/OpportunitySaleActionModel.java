package com.facishare.crm.sfa.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.elasticsearch.search.DocValueFormat;

import java.util.List;


@Data
@Builder
public class OpportunitySaleActionModel {
    private String opportunity_sale_action_id;
    private String sale_action_id;
    private String opportunity_id;
    private String pre_sale_stage_id;
    private String after_sale_stage_id;
    private Integer pre_status;
    private Integer after_status;
}
