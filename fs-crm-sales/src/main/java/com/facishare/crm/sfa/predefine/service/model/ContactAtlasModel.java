package com.facishare.crm.sfa.predefine.service.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 商机联系人图谱 class
 *
 * <AUTHOR>
 * @date 2019/9/26
 */
public interface ContactAtlasModel {
    @Data
    class Arg {
        @JSONField(name = "account_id")
        @JsonProperty("account_id")
        private String accountId;
    }

    @Data
    @Builder
    class Result {
        @JSONField(name = "account_id")
        @JsonProperty("account_id")
        private String accountId;
        @JSONField(name = "contact_card")
        @JsonProperty("contact_card")
        private List<ContactCard> contactCard;
        @JSONField(name = "card_relation")
        @JsonProperty("card_relation")
        private List<CardRelation> cardRelation;
        @JSONField(name = "list_layouts")
        @JsonProperty("list_layouts")
        private List<LayoutDocument> listLayouts;
        @JSONField(name = "object_describe")
        @JsonProperty("object_describe")
        private ObjectDescribeDocument objectDescribe;
    }

    @Data
    @Builder
    class CardRelation {
        @JSONField(name = "source")
        @JsonProperty("source")
        private String source;
        @JSONField(name = "target")
        @JsonProperty("target")
        private String target;
        @JSONField(name = "relation_type")
        @JsonProperty("relation_type")
        private String relationType;
    }

    @Data
    @Builder
    class ContactCard {
        @JSONField(name = "contact_id")
        @JsonProperty("contact_id")
        private String contactId;
        @JSONField(name = "card_id")
        @JsonProperty("card_id")
        private String cardId;
        @JSONField(name = "contact_info")
        @JsonProperty("contact_info")
        private ObjectDataDocument contactInfo;
        @JSONField(name = "show_contact")
        @JsonProperty("show_contact")
        private Boolean showContact;
        @JSONField(name = "order_by")
        @JsonProperty("order_by")
        private Integer orderBy;
    }

    @Data
    class SaveArg {
        @JSONField(name = "account_id")
        @JsonProperty("account_id")
        private String accountId;
        @JSONField(name = "contact_card")
        @JsonProperty("contact_card")
        private List<ContactCardModel> contactCard;
        @JSONField(name = "card_relation")
        @JsonProperty("card_relation")
        private List<CardRelationModel> cardRelation;
    }

    @Data
    class CardRelationModel {
        @JSONField(name = "source")
        @JsonProperty("source")
        private String source;
        @JSONField(name = "target")
        @JsonProperty("target")
        private String target;
        @JSONField(name = "relation_type")
        @JsonProperty("relation_type")
        private String relationType;
    }

    @Data
    class ContactCardModel {
        @JSONField(name = "contact_id")
        @JsonProperty("contact_id")
        private String contactId;
        @JSONField(name = "card_id")
        @JsonProperty("card_id")
        private String cardId;
    }

    @Data
    @Builder
    class SaveResult {
    }

    @Data
    class RelationTypeIsUsedArg {
        @JSONField(name = "relation_type")
        @JsonProperty("relation_type")
        private String relationType;
    }

    @Data
    @Builder
    class RelationTypeIsUsedResult {
        @JSONField(name = "has_used")
        @JsonProperty("has_used")
        Boolean hasUsed;
    }
}
