package com.facishare.crm.sfa.predefine.action;

import com.facishare.crmcommon.constants.SystemConstants;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.action.listener.SalesOrderBulkInvalidActionListener;
import com.facishare.crm.sfa.predefine.service.ModuleCtrlConfigService;
import com.facishare.crm.sfa.predefine.service.modulectrl.IModuleInitService;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ActionListener;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * Created by renlb on 2019/3/14.
 * @IgnoreI18nFile
 */
public class SalesOrderBulkInvalidAction extends StandardBulkInvalidAction {


    private final ModuleCtrlConfigService moduleCtrlConfigService = SpringUtil.getContext().getBean(ModuleCtrlConfigService.class);
    private boolean newInvoice = false;


    @Override
    public List<Class<? extends ActionListener<Arg, Result>>> getActionListenerClassList() {
        List<Class<? extends ActionListener<Arg, Result>>> classList = super.getActionListenerClassList();
        classList.add(SalesOrderBulkInvalidActionListener.class);
        return classList;
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        newInvoice = moduleCtrlConfigService.isOpen(IModuleInitService.MODULE_NEW_INVOICE, actionContext.getUser());
        if(newInvoice){
            dataList.forEach(objectData->{
                findNewInvoiceLineByOrderId(objectData.getId());
                String invoiceStatus = objectData.get("invoice_status", String.class);
                if(!Objects.equals("3",invoiceStatus)){
                    throw new ValidateException("订单已开票，请先作废开票");
                }

                BigDecimal invoicedAmount = objectData.get("invoice_amount") == null ? BigDecimal.ZERO
                        : objectData.get("invoice_amount", BigDecimal.class);
                if(invoicedAmount.compareTo(BigDecimal.ZERO)  > 0){
                    throw new ValidateException("订单已开票，请先作废开票");
                }
            });
        }
    }

    private void findNewInvoiceLineByOrderId(String orderId){
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setOffset(0);
        query.setLimit(10);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters,"order_id",orderId);
        SearchUtil.fillFilterEq(filters,"is_deleted","0");
        SearchUtil.fillFilterIn(filters,"life_status", Lists.newArrayList(
                SystemConstants.LifeStatus.UnderReview.value,
                SystemConstants.LifeStatus.Normal.value,
                SystemConstants.LifeStatus.InChange.value));
        query.setFilters(filters);
        QueryResult<IObjectData> newInvoiceLines = serviceFacade.findBySearchQuery(actionContext.getUser(), Utils.INVOICE_APPLICATION_LINES_API_NAME, query);
        if(CollectionUtils.notEmpty(newInvoiceLines.getData())){
            List<IObjectData> data = newInvoiceLines.getData();
            if(data.size()>0){
                throw new ValidateException("订单已开票，请先作废开票");
            }
        }
    }
}
