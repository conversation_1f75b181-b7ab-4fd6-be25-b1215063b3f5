package com.facishare.crm.sfa.utilities.util;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2019-09-03 15:21
 * @instruction
 */
public class DhtUtil {
    public static boolean isDhtRequest(String peerName){
        return "DingHuoTong".equals(peerName);
    }
    public static boolean isFromOpenApi(String  peerName) {
        return StringUtils.isNotEmpty(peerName) && peerName.toLowerCase().contains("openapi");
    }
}
