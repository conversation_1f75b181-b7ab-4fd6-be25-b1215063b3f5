package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.model.SFAObjectPoolCommon;
import com.facishare.crm.sfa.predefine.service.model.ObjectPoolPermission;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * Created by yuanj<PERSON> on 2019/1/4
 */
@Slf4j
public class SFAMoveAction extends BaseObjectPoolAction<SFAObjectPoolCommon.Arg, SFAObjectPoolCommon.Result> {
    protected List<String>  failedList;

    @Override
    protected List<String> getDataPrivilegeIds(SFAObjectPoolCommon.Arg arg) {
        return arg.getObjectIDs();
    }

    @Override
    protected ObjectAction getObjectAction() {
        return ObjectAction.MOVE;
    }

    @Override
    protected String getObjectPoolId(SFAObjectPoolCommon.Arg arg) {
        return arg.getObjectPoolId();
    }

    @Override
    protected String getUserId(SFAObjectPoolCommon.Arg arg) {
        return actionContext.getUser().getUserId();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList("Move");
    }

    @Override
    protected boolean needTriggerApprovalFlow() {
        return false;
    }

    @Override
    protected boolean needTriggerWorkFlow(){
        return false;
    }

    @Override
    protected void before(SFAObjectPoolCommon.Arg arg) {
        super.before(arg);
        boolean isOnlyMemberMove = AccountUtil.getBooleanValue(objectPoolData, "only_allow_member_move", false);
        if(isOnlyMemberMove && !isCrmAdmin(actionContext.getUser())) {
            if(objectPoolPermissions == null
                    || ObjectPoolPermission.ObjectPoolPermissions.NO_PERMISSION.getValue().equals(objectPoolPermissions.getValue())) {
                throw new ValidateException(I18N.text("sfa.pool.only_pool_member_can_move"));
            }
        }
    }

    @Override
    protected SFAObjectPoolCommon.Result doAct(SFAObjectPoolCommon.Arg arg) {
        SFAObjectPoolCommon.Result result = objectPoolService.move(actionContext.getObjectApiName(), actionContext.getUser(), arg.getObjectPoolId(), arg.getObjectIDs(),actionContext.getEventId());
        return  result;
    }

    @Override
    protected SFAObjectPoolCommon.Result after(SFAObjectPoolCommon.Arg arg, SFAObjectPoolCommon.Result result) {
        super.after(arg, result);
        try {
            ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
            task.submit(() -> {
                asyncAfter(arg, result);
            });
            task.run();
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
        return result;
    }

    protected void asyncAfter(SFAObjectPoolCommon.Arg arg, SFAObjectPoolCommon.Result result) {
        addLog();
    }

    protected  void addLog() {
        logAsync(dataList, EventType.MODIFY, ActionType.MOVE);
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.MOVE.getButtonApiName();
    }

    @Override
    protected IObjectData getPreObjectData() {
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIdsIncludeDeleted(actionContext.getUser(), arg.getObjectIDs(), actionContext.getObjectApiName());
        if (objectDataList != null && objectDataList.size() != 0) {
            return objectDataList.get(0);
        }
        return null;
    }

    @Override
    protected IObjectData getPostObjectData() {
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIdsIncludeDeleted(actionContext.getUser(), arg.getObjectIDs(), actionContext.getObjectApiName());
        if (objectDataList != null && objectDataList.size() != 0) {
            return objectDataList.get(0);
        }
        return null;
    }

}