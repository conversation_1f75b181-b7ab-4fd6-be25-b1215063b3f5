package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.constant.CPQConstraintConstants;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.INameCache;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by py on 2020/6/30.
 */
public class ProductConstraintLinesListController extends StandardListController {

    @Override
    protected QueryResult<IObjectData> getQueryResult(SearchTemplateQuery query) {
        QueryResult<IObjectData> queryResult = super.getQueryResult(query);
        getQueryResult(queryResult, CPQConstraintConstants.DOWN_PRODUCT_PATH,CPQConstraintConstants.UP_PRODUCT_PATH);
        return queryResult;
    }

    private void getQueryResult(QueryResult<IObjectData> queryResult, String... fields) {
        final Set<String> pathProductIds = Sets.newHashSet();
        Lists.newArrayList(fields).forEach(field->{
            queryResult.getData().stream().map(x->MapUtils.getString(ObjectDataExt.of(x).toMap(),field,"")).filter(x-> StringUtils.isNotBlank(x)).forEach(productPath -> {
                pathProductIds.addAll(Lists.newArrayList(productPath.split("\\.")));
            });
            if (CollectionUtils.isNotEmpty(pathProductIds)) {
                //翻译产品路径
                List<INameCache> recordName = serviceFacade.findRecordName(ActionContextExt.of(controllerContext.getUser()).getContext()
                        , CPQConstraintConstants.DESC_PRODUCT_CONSTRAINT_LINES_API_NAME, new ArrayList<>(pathProductIds));
                Map<String, String> productIdToName = recordName.stream().collect(Collectors.toMap(INameCache::getId, INameCache::getName));
                for (IObjectData data : queryResult.getData()) {
                    String path = MapUtils.getString(ObjectDataExt.of(data).toMap(), field, "");
                    if(StringUtils.isNotBlank(path)){
                        String[] productIds = path.split("\\.");
                        List<String> productNames = Lists.newArrayList(productIds).stream().map(productIdToName::get).collect(Collectors.toList());
                        data.set(field.concat("_name"), Joiner.on("->").join(productNames));
                    }
                }
            }
        });
    }
}
