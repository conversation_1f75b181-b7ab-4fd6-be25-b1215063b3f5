package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.ILayout;

import java.util.List;

/**
 * <AUTHOR> 2019-11-11
 * @instruction
 */
public class TieredPriceBookRelatedListController extends StandardRelatedListController {
    @Override
    protected List<IButton> getButtons(ILayout layout) {
        List<IButton> buttons = super.getButtons(layout);
        buttons.removeIf(o -> "Add".equals(o.getAction()));
        return buttons;
    }
}
