package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.bizvalidator.BizValidator;
import com.facishare.crm.sfa.predefine.bizvalidator.ValidatorContext;
import com.facishare.crm.sfa.predefine.bizvalidator.validator.PriceBookValidator;
import com.facishare.crm.sfa.predefine.bizvalidator.validator.ProductIsRepeatedValidator;
import com.facishare.crm.sfa.predefine.bizvalidator.validator.ProductRangeValidator;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.sfa.utilities.validator.QuoteValidator;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.metadata.util.SpringUtil;

public class QuoteEditAction extends StandardEditAction {
    private static final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        ValidatorContext validatorContext = ValidatorContext.builder()
                .action(ObjectAction.UPDATE)
                .user(actionContext.getUser())
                .describeApiName(objectDescribe.getApiName())
                .objectDescribes(objectDescribes)
                .dbMasterData(dbMasterData).objectData(objectData).detailObjectData(detailObjectData)
                .detailsToAdd(detailsToAdd).detailsToUpdate(detailsToUpdate).detailsToDelete(detailsToDelete)
                .build();
        BizValidator.build()
                .withContext(validatorContext)
//                .with(new MobileUnSupportValidator())
//                .when(RequestUtil.isMobileOrH5Request())
                .with(new ProductIsRepeatedValidator())
                .with(new PriceBookValidator())
                .when(bizConfigThreadLocalCacheService.isPriceBookEnabled(actionContext.getTenantId()))
                .with(new ProductRangeValidator())
                .when(!bizConfigThreadLocalCacheService.isPriceBookEnabled(actionContext.getTenantId()))
                .doValidate();

        if (bizConfigThreadLocalCacheService.isPriceBookEnabled(actionContext.getTenantId()) && !GrayUtil.isGrayPriceBookRefactor(actionContext.getTenantId())) {
            QuoteValidator.validateAccountPriceBook(actionContext, objectData, true);

//            QuoteValidator.validateProductInPriceBook(actionContext, objectData, detailObjectData);
        }
    }
}
