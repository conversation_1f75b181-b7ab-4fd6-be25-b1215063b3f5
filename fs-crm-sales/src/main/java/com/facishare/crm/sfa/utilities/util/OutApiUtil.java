package com.facishare.crm.sfa.utilities.util;

import com.facishare.converter.EIEAConverter;
import com.facishare.crmcommon.rest.dto.PrmEnterpriseModel;
import com.facishare.crm.sfa.predefine.service.model.ListOuterEnterprise;
import com.facishare.crm.sfa.utilities.proxy.InitObjectsPermissionsAndLayoutProxy;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.AppIdMapping;
import com.facishare.paas.appframework.prm.util.PrmConstant;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;

/**
 * Created by renlb on 2019/9/26.
 */
public class OutApiUtil {
    private static final EIEAConverter eieaConverter = SpringUtil.getContext().getBean(EIEAConverter.class);
    private static final InitObjectsPermissionsAndLayoutProxy initObjectsPermissionsAndLayoutProxy =
            SpringUtil.getContext().getBean(InitObjectsPermissionsAndLayoutProxy.class);
    public static ListOuterEnterprise.Result getDownstreamEnterprisesAndUsers(String tenantId) {
        PrmEnterpriseModel.GetAllDownStreamUsersArg arg = new PrmEnterpriseModel.GetAllDownStreamUsersArg();
        String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId));
        arg.setUpstreamEa(ea);
        arg.setLinkAppId(AppIdMapping.appIdMapping.get(PrmConstant.PRM_APP_ID));
        Map<String, String> header = Maps.newHashMap();
        header.put("Content-Type", "application/json");
        header.put("x-fs-ei", tenantId);
        header.put("x-eip-appid", "x_app_framework");
        List<ListOuterEnterprise.OuterEnterprise> enterprises = Lists.newArrayList();
        PrmEnterpriseModel.DownstreamEnterpriseAndEmployeeResult enterpriseInfos = initObjectsPermissionsAndLayoutProxy
                .getAllDownstreamEnterprisesAndEmployee(header, arg);
        if (enterpriseInfos != null && CollectionUtils.notEmpty(enterpriseInfos.getData())) {
            for (PrmEnterpriseModel.DownstreamEnterpriseAndEmployee enterprise : enterpriseInfos.getData()) {
                if (!Boolean.TRUE.equals(enterprise.getAuthorized())) {
                    continue;
                }
                List<ListOuterEnterprise.OuterEnterprise> outerEmployees = Lists.newArrayList();
                if (CollectionUtils.notEmpty(enterprise.getDownstreamEmployees())) {
                    for (PrmEnterpriseModel.DownstreamEmployee employee : enterprise.getDownstreamEmployees()) {
                        if (!Boolean.TRUE.equals(employee.getAuthorized())) {
                            continue;
                        }
                        outerEmployees.add(ListOuterEnterprise.OuterEnterprise.builder()
                                .id(employee.getOuterUid())
                                .name(employee.getName())
                                .build());
                    }
                }
                enterprises.add(ListOuterEnterprise.OuterEnterprise.builder()
                        .id(enterprise.getDownstreamOuterTenantId())
                        .name(enterprise.getName())
                        .children(outerEmployees)
                        .build());
            }
        }
        return ListOuterEnterprise.Result.builder().data(enterprises).build();
    }
}
