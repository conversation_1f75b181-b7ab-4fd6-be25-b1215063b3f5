package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.model.Enum.LeadsBizStatusEnum;
import com.facishare.crm.sfa.model.Enum.LeadsStatusEnum;
import com.facishare.crm.sfa.model.Enum.SessionBOCItemKeys;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.exception.SFABusinessException;
import com.facishare.crm.sfa.predefine.exception.SFAErrorCode;
import com.facishare.crm.sfa.predefine.service.ObjectPoolService;
import com.facishare.crm.sfa.predefine.service.QiXinTodoService;
import com.facishare.crm.sfa.predefine.service.SFALogService;
import com.facishare.crm.sfa.predefine.service.model.SFALogModels;
import com.facishare.crm.sfa.predefine.service.task.LeadsOverTimeTaskService;
import com.facishare.crm.sfa.utilities.proxy.model.FeedsModel;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.crm.sfa.utilities.util.LeadsUtils;
import com.facishare.crmcommon.util.SearchUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.BaseObjectApprovalAction;
import com.facishare.paas.appframework.flow.ApprovalFlowStartResult;
import com.facishare.paas.appframework.flow.mq.WorkflowProducer;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.*;

@Slf4j
public class LeadsFollowUpAction extends BaseObjectApprovalAction<LeadsFollowUpAction.Arg, LeadsFollowUpAction.Result> {
    private LeadsOverTimeTaskService leadsOverTimeTaskService = SpringUtil.getContext().getBean(LeadsOverTimeTaskService.class);
    private QiXinTodoService qiXinTodoService = SpringUtil.getContext().getBean(QiXinTodoService.class);
    private ObjectPoolService objectPoolService = SpringUtil.getContext().getBean(ObjectPoolService.class);
    private SFALogService sfaLogService = SpringUtil.getContext().getBean(SFALogService.class);
    private static final IObjectDataService objectDataService = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);
    private String dealResult = "";
    private boolean isSendSaleEvent = false;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.FOLLOW_UP.getActionCode());
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return arg.getDataIds();
    }

    @Override
    protected void doFunPrivilegeCheck() {
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        for(IObjectData  leadsData : dataList) {
            if (!(leadsData.get("biz_status", String.class).equals(LeadsBizStatusEnum.UN_PROCESSED.getCode()) ||
                    leadsData.get("biz_status", String.class).equals(LeadsBizStatusEnum.PROCESSED.getCode()) ||
                    leadsData.get("biz_status", String.class).equals(LeadsBizStatusEnum.CLOSED.getCode()))) {
                throw new ValidateException(String.format(I18N.text(SFA_OBJECT_CANT_DO_THIS_JOB),
                        I18N.text("LeadsObj.attribute.self.display_name")));
            }
        }
        IObjectData argObjectData = arg.getArgs().toObjectData();
        if (argObjectData != null) {
            dealResult = AccountUtil.getStringValue(argObjectData, "form_completed_result", "");
            isSendSaleEvent = AccountUtil.getBooleanValue(argObjectData, "form_send_event", false);
        }
    }

    @Override
    protected Result doAct(Arg arg) {
        Result result = Result.builder().build();
        LeadsStatusEnum status = LeadsStatusEnum.DEALED;
        LeadsBizStatusEnum bizStatus = LeadsBizStatusEnum.PROCESSED;
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), arg.getDataIds(), SFAPreDefineObject.Leads.getApiName());
        if (CollectionUtils.notEmpty(objectDataList)) {
            for (IObjectData objectData : objectDataList) {
                objectData.set("leads_status", status.getCode());
                objectData.set("biz_status", bizStatus.getCode());
                objectData.set("completed_result", dealResult);
                objectData.set("last_modified_by", Lists.newArrayList(actionContext.getUser().getUserId()));
                objectData.set("last_modified_time", System.currentTimeMillis());
                objectData.set("is_overtime", false);
            }
        }
        List<String> updateFieldList = Lists.newArrayList("leads_status", "biz_status", "completed_result", "last_modified_by",
                "last_modified_time", "is_overtime");

        try {
            serviceFacade.batchUpdateByFields(actionContext.getUser(), objectDataList, updateFieldList);
        } catch (Exception e) {
            log.error("leads followup error {}", actionContext.getTenantId(), e);
            throw new SFABusinessException(SFAErrorCode.LEADS_COMMON_ERROR);
        }

        return result;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result.setSuccessList(arg.getDataIds());
        result.setFailedList(Lists.newArrayList());
        result.setErrorList(Lists.newArrayList());
        dataList = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), arg.getDataIds(), SFAPreDefineObject.Leads.getApiName());
        addFlowRecord();
        for(String leadsId : arg.getDataIds()) {
            leadsOverTimeTaskService.deleteTask(actionContext.getTenantId(), leadsId);
            Optional<IObjectData> optionalData = dataList.stream().filter(d -> leadsId.equals(d.getId())).findFirst();
            if (optionalData.isPresent()) {
                IObjectData objectData = optionalData.get();
                this.serviceFacade.sendActionMq(this.actionContext.getUser(), Lists.newArrayList(objectData), ObjectAction.FOLLOW_UP);
                String msg = String.format(I18N.text(SFA_LEADS_FLLOWING_UP), I18N.text("LeadsObj.attribute.self.display_name"), objectData.getName());
                serviceFacade.logCustomMessageOnly(actionContext.getUser(), EventType.MODIFY, ActionType.Handle, objectDescribe, objectData,
                        msg);

                String leadsPoolId = LeadsUtils.getPoolId(objectData);
                IObjectData leadsPool = objectPoolService.getObjectPoolById(SFAPreDefineObject.Leads.getApiName(), actionContext.getTenantId()
                        , leadsPoolId);
                if (leadsPool != null) {
                    msg = String.format("%s %s，%s %s", I18N.text("LeadsObj.attribute.self.display_name"), objectData.getName(),
                            I18N.text("LeadsPoolObj.attribute.self.display_name"), leadsPool.getName());

                    SFALogModels.SFALogEntity logEntity = sfaLogService.buildLogEntity(leadsPool,
                            msg,
                            false);
                    List<SFALogModels.SFALogTextMessage> textMessageList = Lists.newArrayList();
                    sfaLogService.appendNameLinkLogTextMessage(actionContext.getObjectApiName(), objectData, textMessageList);
                    msg = String.format("，%s %s", I18N.text("LeadsPoolObj.attribute.self.display_name"), leadsPool.getName());
                    sfaLogService.appendLogTextMessage(actionContext.getObjectApiName(), objectData,
                            SFALogModels.LogLinkType.NO_LINK, msg, textMessageList);
                    logEntity.setLogTextMessageList(textMessageList);
                    sfaLogService.addLog(actionContext.getUser(), logEntity, "SalesCluePoolLog",
                            SFALogModels.LogOperationType.DEAL);
                }
                String employeeId = actionContext.getUser().isOutUser() ? actionContext.getUser().getOutUserId() : actionContext.getUser().getUserId();
                qiXinTodoService.dealTodo(actionContext.getTenantId(), actionContext.getUser(), SFAPreDefineObject.Leads.getApiName(),
                        SessionBOCItemKeys.TobeProcessedSalesClue, leadsId);
                LeadsUtils.insertCrmDealDataRelation(actionContext.getTenantId(), objectData.getId(), 2, employeeId);
            }

            if(isSendSaleEvent && StringUtils.isNotBlank(dealResult)) {
                List<FeedsModel.FeedRelatedCrmObject> crmObjects = Lists.newArrayList();
                FeedsModel.FeedRelatedCrmObject crmObject = FeedsModel.FeedRelatedCrmObject.builder()
                        .apiName(SFAPreDefineObject.Leads.getApiName()).dataId(leadsId).build();
                crmObjects.add(crmObject);
                AccountUtil.PublishFeeds(actionContext.getUser(), dealResult, 5, 1, crmObjects);
            }
        }

        return super.after(arg, result);
    }

    protected void triggerWorkFlow() {
        this.startWorkFlow(Maps.newHashMap());
        this.stopWatch.lap("startCreateWorkFlow");
    }

    protected void startWorkFlow(Map<String, ApprovalFlowStartResult> startApprovalFlowResult) {
        //批量处理触发工作流以及处理过滤器异常的数据
        for (IObjectData resultObjectData : dataList) {
            infraServiceFacade.startWorkFlow(resultObjectData.getId()
                    , resultObjectData.getDescribeApiName(), WorkflowProducer.TRIGGER_UPDATE, actionContext
                            .getUser(), Maps.newHashMap(), actionContext.getEventId());
        }
    }

    private void addFlowRecord() {
        List<IObjectData> oldFlowRecordDataList = Lists.newArrayList();
        for (IObjectData leadsData : dataList) {
            String oldOwnerId = LeadsUtils.getOwner(leadsData);
            if(StringUtils.isBlank(oldOwnerId)) {
                continue;
            }
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            searchTemplateQuery.setLimit(1);
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, "leads_owner", oldOwnerId);
            SearchUtil.fillFilterEq(filters, "leads_id", leadsData.getId());
            searchTemplateQuery.setFilters(filters);
            List<OrderBy> orderByList = Lists.newArrayList();
            orderByList.add(new OrderBy("last_modified_time", false));
            searchTemplateQuery.setOrders(orderByList);
            searchTemplateQuery.setNeedReturnCountNum(false);
            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(actionContext.getUser(), SFAPreDefineObject.LeadsFlowRecord.getApiName(), searchTemplateQuery);
            if(queryResult != null && org.apache.commons.collections.CollectionUtils.isNotEmpty(queryResult.getData())) {
                IObjectData oldFlowRecordData = queryResult.getData().get(0);
                oldFlowRecordData.set("leads_status", leadsData.get("biz_status"));
                oldFlowRecordData.set("leads_status_changed_time", System.currentTimeMillis());
                oldFlowRecordData.set("last_modified_by", Lists.newArrayList(actionContext.getUser().getUserId()));
                oldFlowRecordData.set("last_modified_time", System.currentTimeMillis());
                oldFlowRecordDataList.add(oldFlowRecordData);
            }
        }
        if(CollectionUtils.notEmpty(oldFlowRecordDataList)) {
            List<String> updateFieldList = Lists.newArrayList("leads_status", "leads_status_changed_time", "last_modified_by",
                    "last_modified_time");
            try {
//            serviceFacade.batchUpdateByFields(user, objectDataList, updateFieldList);
                objectDataService.batchUpdateWithField(oldFlowRecordDataList, updateFieldList, AccountUtil.getDefaultActionContext(actionContext.getUser(), ""));
            } catch (MetadataServiceException metadataError) {
                log.info("addFlowRecord warn", metadataError);
                throw new SFABusinessException(metadataError.getMessage(), SFAErrorCode.ACCOUNT_COMMON_ERROR);
            } catch (Exception e) {
                log.error("addFlowRecord error", e);
                throw new SFABusinessException(SFAErrorCode.ACCOUNT_COMMON_ERROR);
            }
        }
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.FOLLOW_UP.getButtonApiName();
    }

    @Override
    protected IObjectData getPreObjectData() {
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIdsIncludeDeleted(actionContext.getUser(), arg.getDataIds(), actionContext.getObjectApiName());
        if (objectDataList != null && objectDataList.size() != 0) {
            return objectDataList.get(0);
        }
        return null;
    }

    @Override
    protected IObjectData getPostObjectData() {
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIdsIncludeDeleted(actionContext.getUser(), arg.getDataIds(), actionContext.getObjectApiName());
        if (objectDataList != null && objectDataList.size() != 0) {
            return objectDataList.get(0);
        }
        return null;
    }

    @Override
    protected boolean skipPreFunction() {
        return arg.isSkipPreAction() || super.skipPreFunction();
    }

    @Override
    protected boolean skipCheckButtonConditions() {
        return arg.isSkipButtonConditions() || super.skipCheckButtonConditions();
    }

    @Override
    protected Map<String, Object> getArgs() {
        if(arg.getArgs() != null) {
            return ObjectDataExt.toMap(arg.getArgs().toObjectData());
        }
        return Maps.newHashMap();
    }

    @Data
    @NoArgsConstructor
    static class Arg {
        private boolean skipFunctionCheck;
        private boolean skipPreAction;
        private boolean skipButtonConditions;
        private List<String> dataIds;
        private ObjectDataDocument args;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private List<String> successList;
        private List<String> failedList;
        private List<String> errorList;
    }
}
