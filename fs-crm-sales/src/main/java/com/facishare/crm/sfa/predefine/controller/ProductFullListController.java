package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.real.ProductService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;

/**
 * <AUTHOR>
 * @date 2018/12/13 19:19
 */
public class ProductFullListController extends ProductListController {

    private final ProductService productService = SpringUtil.getContext().getBean(ProductService.class);

    @Override
    protected QueryResult<IObjectData> findData(SearchTemplateQuery query) {
        return productService.findData(query, controllerContext.getUser(), objectDescribe);
    }
}
