package com.facishare.crm.sfa.predefine.service.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * Created by luxin on 2018/11/29.
 */
public interface ProductFindDataModel {
    @Data
    class InvalidDataArg {
        @SerializedName("spu_id")
        @JsonProperty("spu_id")
        private String spuId;
    }

}
