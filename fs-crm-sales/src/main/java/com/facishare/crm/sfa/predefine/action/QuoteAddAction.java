package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.bizvalidator.BizValidator;
import com.facishare.crm.sfa.predefine.bizvalidator.ValidatorContext;
import com.facishare.crm.sfa.predefine.bizvalidator.validator.*;
import com.facishare.crm.sfa.predefine.bizvalidator.validator.quote.ProductInTieredPriceBookValidator;
import com.facishare.crm.sfa.predefine.service.PriceBookService;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.utilities.constant.QuoteConstants;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.sfa.utilities.validator.QuoteValidator;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class QuoteAddAction extends StandardAddAction {

    private final PriceBookService priceBookService = SpringUtil.getContext().getBean(PriceBookService.class);
    private static final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);

    @Override
    protected void init() {
        super.init();
        //价目表字段调整为非必填，不再补标准价目表
//        if (!bizConfigThreadLocalCacheService.isPriceBookEnabled(actionContext.getTenantId())) {
//            setStandardPriceBook();
//        }
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        ValidatorContext validatorContext = ValidatorContext.builder()
                .action(ObjectAction.CREATE)
                .user(actionContext.getUser())
                .describeApiName(objectDescribe.getApiName())
                .objectDescribes(objectDescribes)
                .objectData(objectData).detailObjectData(detailObjectData)
                .build();
        BizValidator.build()
                .withContext(validatorContext)
//                .with(new MobileUnSupportValidator())
//                .when(RequestUtil.isMobileOrH5Request())
                .with(new BomValidator())
                .with(new ProductIsRepeatedValidator())
                .with(new ProductInTieredPriceBookValidator())
                .with(new PriceBookValidator())
                .when(bizConfigThreadLocalCacheService.isPriceBookEnabled(actionContext.getTenantId()))
                .with(new ProductRangeValidator())
                .when(!bizConfigThreadLocalCacheService.isPriceBookEnabled(actionContext.getTenantId()))
                .doValidate();

        //todo 价目表全网后,去掉该代码
        if (bizConfigThreadLocalCacheService.isPriceBookEnabled(actionContext.getTenantId()) && !GrayUtil.isGrayPriceBookRefactor(actionContext.getTenantId())) {
            QuoteValidator.validateAccountPriceBook(actionContext, objectData, false);
//            QuoteValidator.validateProductInPriceBook(actionContext, objectData, detailObjectData);
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);

        //为兼容IOS BUG，未开启价目表时，清除价目表ID数据
        if (!bizConfigThreadLocalCacheService.isPriceBookEnabled(actionContext.getTenantId())) {
            result.getObjectData().put(QuoteConstants.QuoteField.PRICEBOOKID.getApiName(), null);
            result.getObjectData().put(QuoteConstants.QuoteField.PRICEBOOKNAME.getApiName(), null);
        }
        return result;
    }

    /**
     * 没有开启价目表时，报价单自动填充标准价目表。
     */
    private void setStandardPriceBook() {
        if (!bizConfigThreadLocalCacheService.isPriceBookEnabled(actionContext.getTenantId())) {

            IObjectData standardPriceBook = priceBookService.getStandardPriceBook(actionContext.getUser());
            if (standardPriceBook == null) {
                log.error("standardPriceBook no found. actionContext {}", actionContext);
                return;
            }
            objectData.set(QuoteConstants.QuoteField.PRICEBOOKID.getApiName(), standardPriceBook.getId());
            List<IObjectData> quoteLinesDatas = detailObjectData.get(SFAPreDefineObject.QuoteLines.getApiName());
            //价目表产品ID=产品ID+租户ID
            if (quoteLinesDatas != null) {
                quoteLinesDatas.forEach(quoteLinesData -> {
                    quoteLinesData.set(QuoteConstants.QuoteLinesField.PRICEBOOKPRODUCTID.getApiName(),
                            String.valueOf(quoteLinesData.get(QuoteConstants.QuoteLinesField.PRODUCTID.getApiName()))
                                    .concat(actionContext.getTenantId()));
                    quoteLinesData.set(QuoteConstants.QuoteLinesField.PRICE_BOOK_ID.getApiName(), standardPriceBook.getId());
                });
            }
        }
    }
}
