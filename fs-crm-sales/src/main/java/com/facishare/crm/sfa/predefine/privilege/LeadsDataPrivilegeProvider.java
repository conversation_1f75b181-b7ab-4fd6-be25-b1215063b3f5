package com.facishare.crm.sfa.predefine.privilege;

import com.facishare.crmcommon.constants.SystemConstants;
import com.facishare.crm.sfa.model.Enum.LeadsBizStatusEnum;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.model.ObjectPoolPermission;
import com.facishare.crm.sfa.utilities.constant.LeadsConstants;
import com.facishare.crm.sfa.utilities.util.LeadsUtils;
import com.facishare.crm.sfa.utilities.util.PrmUtils;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.Strings;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class LeadsDataPrivilegeProvider extends PoolDataPrivilegeProvider {
    @Override
    public String getApiName() {
        return SFAPreDefineObject.Leads.getApiName();
    }

    @Override
    public Map<String, Map<String, Permissions>> checkBusinessPrivilege(User user, Map<String, Permissions> dataPrivilegeMap,
                                                                        List<IObjectData> dataList,
                                                                        List<String> actionCodes) {
        Boolean isCrmAdmin = isCrmAdmin(user) || user.isSupperAdmin();
        Map<String, Map<String, Permissions>> resultMap = Maps.newHashMap();
        String leadsPoolIdFieldName = LeadsConstants.Field.LEADS_POOL_ID.getApiName();
        List<String> poolIds = dataList.stream().filter(p -> !StringUtils.isEmpty(p.get(leadsPoolIdFieldName, String.class))).
                map(l -> l.get(leadsPoolIdFieldName, String.class)).distinct().collect(Collectors.toList());
        List<IObjectData> pools = getObjectPoolByIds(user.getTenantId(), poolIds);
        Map<String, Boolean> isMemberSendFeedMap = Maps.newHashMap();
        Map<String, Boolean> isMemberViewFeedMap = Maps.newHashMap();
        Map<String, Boolean> isVisibleToMemberMap = Maps.newHashMap();
        Map<String, Boolean> isAllowMemberRelationMap = Maps.newHashMap();
        if (CollectionUtils.notEmpty(pools)) {
            for (IObjectData pool : pools) {
                Boolean isMemberSendFeed = pool.get("is_member_send_feed", Boolean.class);
                isMemberSendFeedMap.put(pool.getId(), isMemberSendFeed == null ? false : isMemberSendFeed);
                Boolean isMemberViewFeed = pool.get("is_member_view_feed", Boolean.class);
                isMemberViewFeedMap.put(pool.getId(), isMemberViewFeed == null ? false : isMemberViewFeed);
                Boolean isVisibleToMember = pool.get("is_visible_to_member", Boolean.class);
                isVisibleToMemberMap.put(pool.getId(), isVisibleToMember == null ? false : isVisibleToMember);
                Boolean isAllowMemberRelation = pool.get("allow_member_relation", Boolean.class);
                isAllowMemberRelationMap.put(pool.getId(), isAllowMemberRelation == null ? false : isAllowMemberRelation);
            }
        }
        Map<String, ObjectPoolPermission.ObjectPoolPermissions> poolPermissionsMap = getPoolPermissionsMap(user, poolIds);

        actionCodes.forEach(actionCode -> {
            Map<String, Permissions> businessPrivilegeMap = Maps.newHashMap();
            for (IObjectData objectData : dataList) {
                ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
                String poolId = objectData.get(leadsPoolIdFieldName, String.class);

                boolean isMemberSendFeed = false;
                if (isMemberSendFeedMap.containsKey(poolId)) {
                    isMemberSendFeed = isMemberSendFeedMap.get(poolId);
                }
                boolean isMemberViewFeed = false;
                if (isMemberViewFeedMap.containsKey(poolId)) {
                    isMemberViewFeed = isMemberViewFeedMap.get(poolId);
                }
                boolean isVisibleToMember = false;
                if (isVisibleToMemberMap.containsKey(poolId)) {
                    isVisibleToMember = isVisibleToMemberMap.get(poolId);
                }
                boolean isAllowMemberRelation = false;
                if (isAllowMemberRelationMap.containsKey(poolId)) {
                    isAllowMemberRelation = isAllowMemberRelationMap.get(poolId);
                }
                String bizStatus = objectData.get("biz_status", String.class);
                ObjectLifeStatus lifeStatus = objectDataExt.getLifeStatus();
                boolean isLocked = SystemConstants.LockStatus.Locked.value.equals(objectData.get("lock_status", String.class));
                ObjectPoolPermission.ObjectPoolPermissions objectPoolPermissions = poolPermissionsMap.get(poolId);
                Permissions commonPermissions = dataPrivilegeMap.getOrDefault(objectData.getId(), Permissions.NO_PERMISSION);
                Permissions permissions = Permissions.NO_PERMISSION;
                if (ObjectAction.SALE_RECORD.getActionCode().equals(actionCode)) {
                    if (isCrmAdmin || Strings.isNullOrEmpty(poolId) || objectData.getOwner().size() > 0 || objectPoolPermissions.isPoolAdmin() || !(objectPoolPermissions.isPoolMember() && !isMemberSendFeed)) {
                        permissions = Permissions.READ_WRITE;
                    }
                } else if (ObjectAction.VIEW_FEED_CARD.getActionCode().equals(actionCode)) {
                    if (isCrmAdmin || Strings.isNullOrEmpty(poolId) || objectData.getOwner().size() > 0 || objectPoolPermissions.isPoolAdmin() || !(objectPoolPermissions.isPoolMember() && !isMemberViewFeed)) {
                        permissions = Permissions.READ_WRITE;
                    }
                } else if (ObjectAction.DEAL.getActionCode().equals(actionCode)) {
                    if ((bizStatus.equals(LeadsBizStatusEnum.UN_PROCESSED.getCode()) || bizStatus.equals(LeadsBizStatusEnum.PROCESSED.getCode())
                            || bizStatus.equals(LeadsBizStatusEnum.CLOSED.getCode())) && lifeStatus.equals(ObjectLifeStatus.NORMAL)){
                        if (isCrmAdmin) {
                            permissions = Permissions.READ_WRITE;
                        } else {
                            permissions = commonPermissions;
                        }
                    }
                } else if (ObjectAction.VIEW_DETAIL.getActionCode().equals(actionCode)) {
                    if (isCrmAdmin) {
                        permissions = Permissions.READ_WRITE;
                    } else if (lifeStatus.equals(ObjectLifeStatus.INVALID)) {
                        permissions = Permissions.NO_PERMISSION;
                    } else{
                        permissions = commonPermissions;
                        if (!Strings.isNullOrEmpty(poolId)) {
                            if (lifeStatus == null) {
                                log.error("lifeStatus is null");
                            }
                            if (objectPoolPermissions == null) {
                                log.error("objectPoolPermissions is null");
                            }
                            if (objectPoolPermissions.isPoolAdmin()) {
                                permissions = Permissions.READ_WRITE;
                            } else {
                                if (bizStatus.equals(LeadsBizStatusEnum.UN_ASSIGNED.getCode()) && isVisibleToMember
                                        && objectPoolPermissions.isPoolMember()) {
                                    permissions = Permissions.READ_WRITE;
                                }
                            }
                        }
                    }
                } else if (ObjectAction.UPDATE.getActionCode().equals(actionCode) ||
                        ObjectAction.LOCK.getActionCode().equals(actionCode) ||
                        ObjectAction.UNLOCK.getActionCode().equals(actionCode)) {
                    if (!lifeStatus.equals(ObjectLifeStatus.INVALID) && !lifeStatus.equals(ObjectLifeStatus.IN_CHANGE)) {
                        if (isCrmAdmin) {
                            permissions = Permissions.READ_WRITE;
                        } else {
                            permissions = commonPermissions;
                        }
                    }
                    if (!Strings.isNullOrEmpty(poolId)) {
                        if (bizStatus.equals(LeadsBizStatusEnum.UN_ASSIGNED.getCode()) && objectPoolPermissions.isPoolAdmin()) {
                            permissions = Permissions.READ_WRITE;
                        }
                    }
                } else if (ObjectAction.ALLOCATE.getActionCode().equals(actionCode) ||
                        ObjectAction.MOVE.getActionCode().equals(actionCode)) {
                    if (!Strings.isNullOrEmpty(poolId)) {
                        if ((isCrmAdmin || objectPoolPermissions.isPoolAdmin()) && lifeStatus.equals(ObjectLifeStatus.NORMAL) && !isLocked &&
                                (bizStatus.equals(LeadsBizStatusEnum.UN_ASSIGNED.getCode()) || bizStatus.equals(LeadsBizStatusEnum.UN_PROCESSED.getCode())
                                        || bizStatus.equals(LeadsBizStatusEnum.PROCESSED.getCode()) || bizStatus.equals(LeadsBizStatusEnum.CLOSED.getCode()))) {
                            permissions = Permissions.READ_WRITE;
                        }
                    }
                } else if (ObjectAction.TAKE_BACK.getActionCode().equals(actionCode)) {
                    if (!Strings.isNullOrEmpty(poolId)) {
                        if ((isCrmAdmin || objectPoolPermissions.isPoolAdmin()) && lifeStatus.equals(ObjectLifeStatus.NORMAL) && !isLocked &&
                                (bizStatus.equals(LeadsBizStatusEnum.UN_PROCESSED.getCode())
                                        || bizStatus.equals(LeadsBizStatusEnum.PROCESSED.getCode()) || bizStatus.equals(LeadsBizStatusEnum.CLOSED.getCode()))) {
                            permissions = Permissions.READ_WRITE;
                        }
                    }
                } else if (ObjectAction.CHOOSE.getActionCode().equals(actionCode)) {
                    if (!Strings.isNullOrEmpty(poolId)) {
                        if (objectPoolPermissions.isPoolMember() && lifeStatus.equals(ObjectLifeStatus.NORMAL) && !isLocked &&
                                bizStatus.equals(LeadsBizStatusEnum.UN_ASSIGNED.getCode()) && isVisibleToMember) {
                            permissions = Permissions.READ_WRITE;
                        }
                    }
                } else if (ObjectAction.RETURN.getActionCode().equals(actionCode)) {
                    if ((bizStatus.equals(LeadsBizStatusEnum.UN_PROCESSED.getCode())
                            || bizStatus.equals(LeadsBizStatusEnum.PROCESSED.getCode()) || bizStatus.equals(LeadsBizStatusEnum.CLOSED.getCode())) && lifeStatus.equals(ObjectLifeStatus.NORMAL) && !isLocked) {
                        if (isCrmAdmin) {
                            permissions = Permissions.READ_WRITE;
                        } else {
                            permissions = commonPermissions;
                        }
                    }
                } else if (ObjectAction.CLOSE.getActionCode().equals(actionCode)) {
                    if ((bizStatus.equals(LeadsBizStatusEnum.UN_PROCESSED.getCode())
                            || bizStatus.equals(LeadsBizStatusEnum.PROCESSED.getCode())) && lifeStatus.equals(ObjectLifeStatus.NORMAL)) {
                        if (isCrmAdmin) {
                            permissions = Permissions.READ_WRITE;
                        } else {
                            permissions = commonPermissions;
                        }
                    }
                } else if (ObjectAction.FOLLOW_UP.getActionCode().equals(actionCode)) {
                    if ((bizStatus.equals(LeadsBizStatusEnum.UN_PROCESSED.getCode())
                            || bizStatus.equals(LeadsBizStatusEnum.PROCESSED.getCode()) ||
                            bizStatus.equals(LeadsBizStatusEnum.CLOSED.getCode())) && lifeStatus.equals(ObjectLifeStatus.NORMAL)) {
                        if (isCrmAdmin) {
                            permissions = Permissions.READ_WRITE;
                        } else {
                            permissions = commonPermissions;
                        }
                    }
                } else if (ObjectAction.CHANGE_OWNER.getActionCode().equals(actionCode)) {
                    if (bizStatus.equals(LeadsBizStatusEnum.UN_PROCESSED.getCode())
                            || bizStatus.equals(LeadsBizStatusEnum.PROCESSED.getCode()) || bizStatus.equals(LeadsBizStatusEnum.CLOSED.getCode())
                            || lifeStatus.equals(ObjectLifeStatus.INEFFECTIVE)) {
                        if (isCrmAdmin) {
                            permissions = Permissions.READ_WRITE;
                        } else {
                            permissions = commonPermissions;
                        }
                    }
                } else if (ObjectAction.EDIT_TEAM_MEMBER.getActionCode().equals(actionCode)) {
                    if (!lifeStatus.equals(ObjectLifeStatus.INEFFECTIVE)) {
                        if (isCrmAdmin) {
                            permissions = Permissions.READ_WRITE;
                        } else {
                            permissions = commonPermissions;
                        }
                    }
                    if (bizStatus.equals(LeadsBizStatusEnum.UN_ASSIGNED.getCode())) {
                        if (isCrmAdmin) {
                            permissions = Permissions.READ_WRITE;
                        }
                        if (isAllowMemberRelation) {
                            permissions = Permissions.READ_WRITE;
                        }
                        if (!Strings.isNullOrEmpty(poolId)) {
                            if (objectPoolPermissions.isPoolAdmin()) {
                                permissions = Permissions.READ_WRITE;
                            }
                        }
                    }
                } else if (ObjectAction.PRINT.getActionCode().equals(actionCode)) {
                    if (isCrmAdmin) {
                        permissions = Permissions.READ_WRITE;
                    } else {
                        permissions = commonPermissions;
                    }
                } else if (ObjectAction.MERGE.getActionCode().equals(actionCode)) {
                    if (isCrmAdmin) {
                        permissions = Permissions.READ_WRITE;
                    }
                    if (objectData.getOwner().contains(user.getUserId()) && Strings.isNullOrEmpty(poolId)) {
                        permissions = commonPermissions;
                    }
                } else if (ObjectAction.VIEW_LIST.getActionCode().equals(actionCode) || ObjectAction.CREATE.getActionCode().equals(actionCode)) {
                    permissions = commonPermissions;
                } else if (ObjectAction.START_BPM.getActionCode().equals(actionCode)) {
                    if (!lifeStatus.equals(ObjectLifeStatus.INVALID) && !lifeStatus.equals(ObjectLifeStatus.UNDER_REVIEW)
                            && !lifeStatus.equals(ObjectLifeStatus.INEFFECTIVE)) {
                        permissions = commonPermissions;
                    }
                } else if (ObjectAction.INVALID.getActionCode().equals(actionCode)) {
                    if (bizStatus.equals(LeadsBizStatusEnum.UN_PROCESSED.getCode())
                            || bizStatus.equals(LeadsBizStatusEnum.PROCESSED.getCode()) || bizStatus.equals(LeadsBizStatusEnum.CLOSED.getCode())
                            || (bizStatus.equals(LeadsBizStatusEnum.TRANSFORMED.getCode()) && !isLocked)
                            || lifeStatus.equals(ObjectLifeStatus.INEFFECTIVE) || lifeStatus.equals(ObjectLifeStatus.UNDER_REVIEW)) {
                        if (isCrmAdmin) {
                            permissions = Permissions.READ_WRITE;
                        } else {
                            permissions = commonPermissions;
                        }
                    }
                    if (isCrmAdmin && bizStatus.equals(LeadsBizStatusEnum.UN_ASSIGNED.getCode())) {
                        permissions = Permissions.READ_WRITE;
                    }
                    if (!Strings.isNullOrEmpty(poolId) && objectPoolPermissions.isPoolAdmin() && bizStatus.equals(LeadsBizStatusEnum.UN_ASSIGNED.getCode())) {
                        permissions = Permissions.READ_WRITE;
                    }
                } else if (ObjectAction.RECOVER.getActionCode().equals(actionCode)) {
                    if (isCrmAdmin && lifeStatus.equals(ObjectLifeStatus.INVALID)) {
                        permissions = Permissions.READ_WRITE;
                    }
                } else if (ObjectAction.DELETE.getActionCode().equals(actionCode)) {
                    if (lifeStatus.equals(ObjectLifeStatus.INVALID)) {
                        if (isCrmAdmin) {
                            permissions = Permissions.READ_WRITE;
                        } else {
                            permissions = commonPermissions;
                        }
                    }
                } else if (ObjectAction.VIEW_ENTIRE_BPM.getActionCode().equals(actionCode)) {
                    permissions = commonPermissions;
                } else if (ObjectAction.CHANGE_BPM_APPROVER.getActionCode().equals(actionCode)) {
                    permissions = commonPermissions;
                } else if (ObjectAction.STOP_BPM.getActionCode().equals(actionCode)) {
                    permissions = commonPermissions;
                } else if (ObjectAction.COLLECT_TO.getActionCode().equals(actionCode)) {
                    if(LeadsUtils.isGrayLeadsDuplicated(user.getTenantId())) {
                        if (!ObjectLifeStatus.INVALID.equals(lifeStatus) && !ObjectLifeStatus.INEFFECTIVE.equals(lifeStatus)) {
                            permissions = commonPermissions;
                        }
                    }else {
                        permissions = Permissions.NO_PERMISSION;
                    }
                } else if (ObjectAction.MarkMQL.getActionCode().equals(actionCode)) {
                    if (!ObjectLifeStatus.INVALID.equals(lifeStatus) &&
                            !ObjectLifeStatus.INEFFECTIVE.equals(lifeStatus) && !LeadsBizStatusEnum.TRANSFORMED.getCode().equals(bizStatus) &&!isLocked ) {
                        permissions = commonPermissions;
                    }
                }
                else if(ObjectAction.TRANSFER.getActionCode().equals(actionCode))
                {
                    if (!ObjectLifeStatus.INVALID.equals(lifeStatus) &&
                            !ObjectLifeStatus.INEFFECTIVE.equals(lifeStatus) && !LeadsBizStatusEnum.TRANSFORMED.getCode().equals(bizStatus) && !LeadsBizStatusEnum.UN_ASSIGNED.getCode().equals(bizStatus) &&!isLocked ) {
                        permissions = commonPermissions;
                    }
                } else if (ObjectAction.TRANSFER_PARTNER.getActionCode().equals(actionCode)){
                    if (!ObjectLifeStatus.INVALID.equals(lifeStatus) &&
                            !ObjectLifeStatus.INEFFECTIVE.equals(lifeStatus) && !LeadsBizStatusEnum.TRANSFORMED.getCode().equals(bizStatus) && !LeadsBizStatusEnum.UN_ASSIGNED.getCode().equals(bizStatus) &&!isLocked ) {
                        permissions = commonPermissions;
                    }
                    if(!PrmUtils.isOpenPartner(user)){
                        permissions = Permissions.NO_PERMISSION;
                    }
                } else if (ObjectAction.EXTEND_EXPIRETIME.getActionCode().equals(actionCode)){
                    if (isLocked || !isAllowExtend(objectData)
                            || RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_700)){
                        permissions = Permissions.NO_PERMISSION;
                    }else {
                        permissions = commonPermissions;
                    }
                } else {
                    permissions = commonPermissions;
                }
                businessPrivilegeMap.put(objectData.getId(), permissions);
            }
            resultMap.put(actionCode, businessPrivilegeMap);
        });

        return resultMap;
    }
}