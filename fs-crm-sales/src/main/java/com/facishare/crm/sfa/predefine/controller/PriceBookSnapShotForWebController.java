package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.PriceBookCommonService;
import com.facishare.crm.sfa.predefine.service.PriceBookCommonServiceImpl;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardSnapShotForWebController;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.UseScopeFieldDescribe;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.Map;

/**
 * <AUTHOR> 2019-09-25
 * @instruction
 */
public class PriceBookSnapShotForWebController extends StandardSnapShotForWebController {
    private PriceBookCommonService priceBookCommonService = SpringUtil.getContext().getBean(PriceBookCommonServiceImpl.class);

    @Override
    protected void fillFieldInfo(User user, IObjectDescribe objectDescribe, Map<String, Object> objData) {
        super.fillFieldInfo(user, objectDescribe, objData);
        IObjectDescribe describe = serviceFacade.findObject(controllerContext.getTenantId(), Utils.PRICE_BOOK_API_NAME);
        describe.getFieldDescribes().stream().filter(k -> IFieldType.UseScope.equals(k.getType()))
                .forEach(k -> objData.put(k.getApiName(), priceBookCommonService.resolveUseScopeField(objData.get(k.getApiName()), ((UseScopeFieldDescribe) k).getTargetApiName())));
    }
}
