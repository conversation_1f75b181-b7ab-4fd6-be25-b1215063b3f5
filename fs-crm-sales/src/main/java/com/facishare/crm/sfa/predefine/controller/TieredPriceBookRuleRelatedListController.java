package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.constant.TieredPriceConstants;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;

import java.util.Comparator;

import static com.facishare.crm.sfa.predefine.service.cpq.CPQTieredPriceServiceImpl.parseDouble;

/**
 * <AUTHOR> 2019-11-15
 * @instruction
 */
public class TieredPriceBookRuleRelatedListController extends StandardRelatedListController {
    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery searchTemplateQuery = super.buildSearchTemplateQuery();
        if (SearchTemplateQuery.fromJsonString(arg.getSearchQueryInfo()).getPermissionType() == 0)
            searchTemplateQuery.setPermissionType(0);
        return searchTemplateQuery;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        after.getDataList().sort(Comparator.comparing(o -> parseDouble(o.get(TieredPriceConstants.TieredPriceRule.StartCount.getFieldName())), Comparator.nullsFirst(Double::compareTo)));
        return after;
    }
}
