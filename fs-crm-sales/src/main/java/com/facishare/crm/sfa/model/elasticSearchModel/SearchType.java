package com.facishare.crm.sfa.model.elasticSearchModel;

public enum SearchType {
    /**
     * 字符串关键字
     */
    keyword("kw"),
    /**
     * 中文分词
     */
    ik("ik"),
    /**
     * 标准分词
     */
    std("std"),
    /**
     * 空格分词
     */
    whitespace("ws"),

    longType("long"),

    doubleType("double"),

    booleanType("boolean"),

    none("none");

    private String prefix;

    SearchType(String prefix) {
        this.prefix = prefix;
    }

    public String prefix() {
        return this.prefix;
    }
}
