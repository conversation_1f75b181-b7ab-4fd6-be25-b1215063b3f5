package com.facishare.crm.sfa.utilities.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crmcommon.constants.SystemConstants;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.model.ObjectLimitRuleModel;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.exception.SFABusinessException;
import com.facishare.crm.sfa.predefine.exception.SFAErrorCode;
import com.facishare.crm.sfa.predefine.service.AccountPoolServiceImpl;
import com.facishare.crm.sfa.predefine.service.TeamMemberService;
import com.facishare.crm.sfa.predefine.service.model.ObjectPoolPermission;
import com.facishare.crm.sfa.predefine.service.push.ContactSessionSandwichService;
import com.facishare.crm.sfa.predefine.service.real.SFARecyclingService;
import com.facishare.crm.sfa.predefine.service.real.SFARecyclingServiceImpl;
import com.facishare.crm.sfa.utilities.constant.AccountConstants;
import com.facishare.crm.sfa.utilities.proxy.CompanyLyricalProxy;
import com.facishare.crm.sfa.utilities.proxy.FeedsProxy;
import com.facishare.crm.sfa.utilities.proxy.model.FeedsModel;
import com.facishare.crmcommon.util.CommonSqlUtils;
import com.facishare.crmcommon.util.SearchUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.CRMNotificationServiceImpl;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByUserIds;
import com.facishare.paas.appframework.common.service.model.CRMNotification;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.APPException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.ServiceFacadeImpl;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseImportAction;
import com.facishare.paas.appframework.core.predef.action.BaseImportDataAction;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.TeamMember;
import com.facishare.paas.appframework.metadata.dto.sfa.CustomerLimit;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.ICommonSqlService;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.CountFieldDescribe;
import com.facishare.paas.metadata.impl.search.*;
import com.facishare.paas.metadata.service.impl.CommonSqlServiceImpl;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.service.impl.ObjectDescribeServiceImpl;
import com.facishare.paas.metadata.support.GDSHandler;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.*;

/**
 * Created by renlb on 2018/11/19.
 * @IgnoreI18nFile
 */
@Slf4j
public class AccountUtil {
    private static final ServiceFacade SERVICE_FACADE = SpringUtil.getContext().getBean(ServiceFacadeImpl.class);
    private static final IObjectDataService objectDataService = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);
    private static final AccountPoolServiceImpl ACCOUNT_POOL_SERVICE_IMPL = SpringUtil.getContext().getBean(AccountPoolServiceImpl.class);
    private static final TeamMemberService teamMemberService = SpringUtil.getContext().getBean(TeamMemberService.class);
    private static final IObjectDescribeService objectDescribeService = SpringUtil.getContext().getBean(ObjectDescribeServiceImpl.class);
    private static final CRMNotificationServiceImpl crmNotificationService = SpringUtil.getContext().getBean("crmNotificationService", CRMNotificationServiceImpl.class);
    private static final ContactSessionSandwichService contactSessionSandwichService = SpringUtil.getContext().getBean(ContactSessionSandwichService.class);
    private static final SFARecyclingService sfaRecyclingService = SpringUtil.getContext().getBean(SFARecyclingServiceImpl.class);
    private static final FsGrayReleaseBiz gray = FsGrayRelease.getInstance("sfa");
    private static final CompanyLyricalProxy companyLyricalProxy = SpringUtil.getContext().getBean(CompanyLyricalProxy.class);
    private static final ICommonSqlService commonSqlService = SpringUtil.getContext().getBean(CommonSqlServiceImpl.class);
    private static final Long OneDayTimeStamp = 24 * 60 * 60 * 1000L;
    private static final FeedsProxy feedsProxy = SpringUtil.getContext().getBean(FeedsProxy.class);
    private static GDSHandler gdsHandler = SpringUtil.getContext().getBean(GDSHandler.class);

    public static void handleRemainingTime(List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }

        Long defaultValue = 946656000000L;
        Long currentTimeMillis = System.currentTimeMillis();
        Long base = 24 * 3600 * 1000L;
        Long baseHour = 3600 * 1000L;

        for (IObjectData objectData : objectDataList) {
            String value = objectData.get("expire_time", String.class);
            if (Strings.isNullOrEmpty(value) || "0".equals(value)) {
                objectData.set("remaining_time", "");
            } else {
                Long expireTimeLongValue = Long.parseLong(value);
                if (expireTimeLongValue > defaultValue) {
                    if (Utils.LEADS_API_NAME.equals(objectData.getDescribeApiName())) {
                        String remainTime = String.valueOf((expireTimeLongValue - currentTimeMillis) / baseHour);
                        objectData.set("remaining_time", remainTime);
                    } else {
                        String remainTime = String.valueOf((expireTimeLongValue - currentTimeMillis) / base);
                        objectData.set("remaining_time", remainTime);
                    }
                    objectData.set("detail_remaining_time", longTime2String(expireTimeLongValue - System.currentTimeMillis()));
                } else {
                    objectData.set("remaining_time", "");
                }
            }
        }
    }

    public static void handleRemainingTimeDesc(IObjectDescribe describe, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        handleRemainingTime(objectDataList);
        for (IFieldDescribe fieldDescribe : describe.getFieldDescribes()) {
            if ("remaining_time".equals(fieldDescribe.getApiName())) {
                fieldDescribe.set("data_help_text", objectDataList.get(0).get("detail_remaining_time", String.class));
            }
        }
    }

    private static String longTime2String(Long time) {
        String timeStr = "0秒";
        String format;
        time = time / 1000;
        Object[] array;
        Integer days = (int) (time / (60 * 60 * 24));
        Integer hours = (int) (time / (60 * 60) - days * 24);
        if (days > 0) {
            format = "%1$,d天%2$,d时";
            array = new Object[]{days, hours};
        } else if (hours > 0) {
            format = "%1$,d时";
            array = new Object[]{hours};
        } else {
            return null;
        }
        timeStr = String.format(format, array);
        return timeStr;
    }

    public static String getOwner(ObjectDataDocument dataDocument) {
        IObjectData objectData = dataDocument.toObjectData();
        return getOwner(objectData);
    }

    public static String getOwner(IObjectData objectData) {
        List<String> ownerList = objectData.getOwner();
        String owner = "";
        if (CollectionUtils.notEmpty(ownerList)) {
            owner = ownerList.get(0);
        }
        return owner;
    }

    public static String getOutOwner(ObjectDataDocument dataDocument) {
        IObjectData objectData = dataDocument.toObjectData();
        return getOutOwner(objectData);
    }

    public static String getOutOwner(IObjectData objectData) {
        List ownerList = objectData.getOutOwner();
        String owner = "";
        if (CollectionUtils.notEmpty(ownerList)) {
            owner = String.valueOf(ownerList.get(0));
        }
        return owner;
    }

    public static String getEmployee(ObjectDataDocument dataDocument, String employeeField) {
        List<String> employeeList = (List<String>) dataDocument.getOrDefault(employeeField, Lists.newArrayList());
        String result = "";
        if (CollectionUtils.notEmpty(employeeList)) {
            result = employeeList.get(0);
        }
        return result;
    }

    public static String getEmployee(IObjectData objectData, String employeeField) {
        List<String> employeeList = (List<String>) objectData.get(employeeField);
        String result = "";
        if (CollectionUtils.notEmpty(employeeList)) {
            result = employeeList.get(0);
        }
        return result;
    }

    public static void checkFillingChecker(String tenantId, String fillingCheckerId) {
        if (StringUtils.isEmpty(fillingCheckerId)) {
            return;
        }
        String configValue = SFAConfigUtil.getConfigValue(tenantId, "3", User.SUPPER_ADMIN_USER_ID);
        if ("1".equals(configValue) && Integer.valueOf(fillingCheckerId) <= 0) {
            throw new SFABusinessException(SFAErrorCode.ACCOUNT_FILLING_CHECKER_ERROR);
        }
    }

    public static void checkAccountLimit(User user, Integer employeeId, String highSeasId) {
        CustomerLimit.Result rstResult = getCustomerLimit(user, employeeId);
        if (rstResult == null) {
            return;
        }
        if (StringUtils.isNotEmpty(highSeasId) && !rstResult.getValue().getIncludeHighSeasCustomer()) {
            return;
        }
        Integer totalCount = getOwnerCustomerCount(user.getTenantId(), employeeId.toString(), rstResult.getValue().getIncludeHighSeasCustomer(), rstResult.getValue().getIncludeDealCustomer());
        if (totalCount >= rstResult.getValue().getLimitNum()) {
            throw new SFABusinessException(SFAErrorCode.ACCOUNT_LIMIT_ERROR);
        }
    }

    public static void checkAccountLimit(User user, String owner, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList) || StringUtils.isEmpty(owner)) {
            return;
        }
        List<String> poolIds = getPoolIds(objectDataList);
        if (CollectionUtils.notEmpty(poolIds)) {
            poolIds.forEach(x -> {
                List<IObjectData> tempDataList = objectDataList.stream().filter(data -> x.equals(getPoolId(data)))
                        .collect(Collectors.toList());
                if (CollectionUtils.notEmpty(tempDataList)) {
                    checkPoolAccountLimit(user, owner, x, tempDataList);
                }
            });
        }
        CustomerLimit.Result rstResult = getCustomerLimit(user, Integer.valueOf(owner));
        if (rstResult == null) {
            return;
        }
        List<IObjectData> dataList = Lists.newArrayList();
        if (rstResult.getValue().getIncludeHighSeasCustomer()) {
            dataList.addAll(objectDataList);
        } else {
            dataList.addAll(objectDataList.stream().filter(x -> StringUtils.isEmpty(getPoolId(x))).collect(Collectors.toList()));
        }

        if (CollectionUtils.empty(dataList)) {
            return;
        }
        List<IObjectData> unDealAccountList = getUnDealAccountList(dataList);

        if (!rstResult.getValue().getIncludeDealCustomer() && CollectionUtils.empty(unDealAccountList)) {
            return;
        }
        Integer totalCount = getOwnerCustomerCount(user.getTenantId(), owner, rstResult.getValue().getIncludeHighSeasCustomer(), rstResult.getValue().getIncludeDealCustomer());
        if (totalCount > rstResult.getValue().getLimitNum()) {
            throw new SFABusinessException(SFAErrorCode.ACCOUNT_LIMIT_ERROR);
        }
        if (rstResult.getValue().getIncludeDealCustomer()) {
            if (totalCount + dataList.size() > rstResult.getValue().getLimitNum()) {
                throw new SFABusinessException(SFAErrorCode.ACCOUNT_LIMIT_ERROR);
            }
        } else {
            if (CollectionUtils.notEmpty(unDealAccountList)) {
                if (totalCount + unDealAccountList.size() > rstResult.getValue().getLimitNum()) {
                    throw new SFABusinessException(SFAErrorCode.ACCOUNT_LIMIT_ERROR);
                }
            }
        }
    }

    private static List<IObjectData> getUnDealAccountList(List<IObjectData> dataList) {
        List<IObjectData> unDealAccountList = Lists.newArrayList();
        if (CollectionUtils.empty(dataList)) {
            return unDealAccountList;
        }
        unDealAccountList = dataList.stream()
                .filter(x -> AccountConstants.DealStatus.UN_DEAL.getValue().equals(getStringValue(x, AccountConstants.Field.DEAL_STATUS, AccountConstants.DealStatus.UN_DEAL.getValue())))
                .collect(Collectors.toList());
        if (CollectionUtils.empty(unDealAccountList)) {
            unDealAccountList = Lists.newArrayList();
        }
        return unDealAccountList;
    }

    @NotNull
    public static List<String> getPoolIds(List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return Lists.newArrayList();
        }
        return objectDataList.stream().filter(x -> StringUtils.isNotEmpty(getStringValue(x, AccountConstants.Field.HIGH_SEAS_ID, "")))
                .map(x -> getPoolId(x)).distinct().collect(Collectors.toList());
    }

    public static String getPoolId(IObjectData objectData) {
        return getStringValue(objectData, AccountConstants.Field.HIGH_SEAS_ID, "");
    }

    public static void checkPoolAccountLimit(User user, String owner, String poolId, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList) || StringUtils.isEmpty(owner)) {
            return;
        }
        IObjectData poolData = ACCOUNT_POOL_SERVICE_IMPL.getObjectPoolById(user.getTenantId(), poolId);
        if (poolData == null || CollectionUtils.empty(objectDataList)) {
            return;
        }
        List<IObjectData> unDealAccountList = objectDataList.stream()
                .filter(x -> getStringValue(x, AccountConstants.Field.DEAL_STATUS, AccountConstants.DealStatus.UN_DEAL.getValue()).equals(AccountConstants.DealStatus.UN_DEAL.getValue()))
                .collect(Collectors.toList());

        boolean includeDealCustomer = getBooleanValue(poolData, "is_claim_limit_include_dealed_customers", false);

        if (!includeDealCustomer && CollectionUtils.empty(unDealAccountList)) {
            return;
        }
        Integer claimLimitNum = getIntegerValue(poolData, "claim_limit_num", 0);

        Integer totalCount = getOwnerCustomerCount(user.getTenantId(), owner, poolId, includeDealCustomer);
        if (totalCount > claimLimitNum) {
            throw new SFABusinessException(SFAErrorCode.ACCOUNT_LIMIT_ERROR);
        }
        if (includeDealCustomer) {
            if (totalCount + objectDataList.size() > claimLimitNum) {
                throw new SFABusinessException(SFAErrorCode.ACCOUNT_LIMIT_ERROR);
            }
        } else {
            if (CollectionUtils.notEmpty(unDealAccountList)) {
                if (totalCount + unDealAccountList.size() > claimLimitNum) {
                    throw new SFABusinessException(SFAErrorCode.ACCOUNT_LIMIT_ERROR);
                }
            }
        }
    }

    public static void checkAccountLimit(User user, String owner, String poolId, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList) || StringUtils.isEmpty(owner)) {
            return;
        }
        checkPoolAccountLimit(user, owner, poolId, objectDataList);
        CustomerLimit.Result rstResult = getCustomerLimit(user, Integer.valueOf(owner));
        if (rstResult != null && rstResult.getValue().getIncludeHighSeasCustomer()) {
            checkAccountLimit(user, owner, objectDataList);
            return;
        }
    }

    public static void checkAccountLimit(User user, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        List<String> ownerList = objectDataList.stream().filter(x -> hasOwner(x))
                .map(x -> getOwner(x)).distinct().collect(Collectors.toList());
        if (CollectionUtils.notEmpty(ownerList)) {
            ownerList.forEach(owner -> {
                List<IObjectData> tempDataList = objectDataList.stream().filter(x -> getOwner(x).equals(owner)).collect(Collectors.toList());
                if (CollectionUtils.notEmpty(tempDataList)) {
                    checkAccountLimit(user, owner, tempDataList);
                }
            });
        }
    }

    public static void checkAccountLimitForRemove(User user, String owner, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList) || StringUtils.isEmpty(owner)) {
            return;
        }
        CustomerLimit.Result rstResult = getCustomerLimit(user, Integer.valueOf(owner));
        if (rstResult == null) {
            return;
        }
        List<IObjectData> allAccountList = objectDataList.stream().filter(x -> !owner.equals(AccountUtil.getOwner(x)))
                .collect(Collectors.toList());
        if (CollectionUtils.empty(allAccountList)) {
            return;
        }
        List<IObjectData> unDealAccountList = getUnDealAccountList(allAccountList);

        Integer totalCount = getOwnerCustomerCount(user.getTenantId(), owner, rstResult.getValue().getIncludeHighSeasCustomer(), rstResult.getValue().getIncludeDealCustomer());
        if (totalCount > rstResult.getValue().getLimitNum()) {
            throw new SFABusinessException(SFAErrorCode.ACCOUNT_LIMIT_ERROR);
        }
        if (rstResult.getValue().getIncludeDealCustomer()) {
            if (totalCount + allAccountList.size() > rstResult.getValue().getLimitNum()) {
                throw new SFABusinessException(SFAErrorCode.ACCOUNT_LIMIT_ERROR);
            }
        } else {
            if (CollectionUtils.notEmpty(unDealAccountList)) {
                if (totalCount + unDealAccountList.size() > rstResult.getValue().getLimitNum()) {
                    throw new SFABusinessException(SFAErrorCode.ACCOUNT_LIMIT_ERROR);
                }
            }
        }
    }

    @Nullable
    public static CustomerLimit.Result getCustomerLimit(User user, Integer employeeId) {
        CustomerLimit.Result rstResult = null;
        ObjectLimitRuleModel.AccountLimitRule limitRule = ObjectLimitUtil.getAccountLimitRuleByEmployeeId(user, String.valueOf(employeeId));
        if (limitRule != null) {
            CustomerLimit.CustomerLimitResult limitResult = new CustomerLimit.CustomerLimitResult();
            limitResult.setLimitNum(limitRule.getLimitNumber());
            limitResult.setIncludeHighSeasCustomer(limitRule.getIncludeHighSeasCustomer());
            limitResult.setIncludeDealCustomer(limitRule.getIncludeDealCustomer());
            rstResult = new CustomerLimit.Result();
            rstResult.setValue(limitResult);
            rstResult.setMessage("");
            rstResult.setSuccess(true);
            rstResult.setErrorCode(0);
        }
        if (rstResult == null || rstResult.getValue().getLimitNum() <= 0) {
            return null;
        }
        return rstResult;
    }

    public static Integer getOwnerCustomerCount(String tenantId, String employeeId, Boolean includeHighSeasCustomer, Boolean includeDealCustomer) {
        Integer totalCount = 0;
        Count countFieldDescribe = getCountField();
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, "tenant_id", tenantId);
        SearchUtil.fillFilterEq(filters, "owner", employeeId);
        SearchUtil.fillFilterEq(filters, "is_deleted", 0);
        if (includeHighSeasCustomer != null && !includeHighSeasCustomer) {
            SearchUtil.fillFilterIsNull(filters, "high_seas_id");
        }
        if (includeDealCustomer != null && !includeDealCustomer) {
            SearchUtil.fillFilterEq(filters, "deal_status", "1");
        }
        query.setFilters(filters);
        Object objResult = SERVICE_FACADE.getCountValue(tenantId, countFieldDescribe, query);
        if (objResult != null && !Strings.isNullOrEmpty(objResult.toString())) {
            totalCount = Integer.valueOf(objResult.toString());
        }
        return totalCount;
    }

    public static Integer getOwnerCustomerCount(String tenantId, String employeeId, String poolId, Boolean includeDealCustomer) {
        Integer totalCount = 0;
        Count countFieldDescribe = getCountField();
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, "owner", employeeId);
        SearchUtil.fillFilterEq(filters, "high_seas_id", poolId);
        if (!includeDealCustomer) {
            SearchUtil.fillFilterEq(filters, "deal_status", "1");
        }
        query.setFilters(filters);
        Object objResult = SERVICE_FACADE.getCountValue(tenantId, countFieldDescribe, query);
        if (objResult != null && !Strings.isNullOrEmpty(objResult.toString())) {
            totalCount = Integer.valueOf(objResult.toString());
        }
        return totalCount;
    }

    private static Integer getTotalCount(String tenantId, String querySql) {
        Integer totalCount = 0;
        try {
            List<Map> queryResult = objectDataService.findBySql(tenantId, querySql);
            if (CollectionUtils.notEmpty(queryResult)) {
                Map countResult = queryResult.get(0);
                Iterator<String> iterator = countResult.keySet().iterator();
                String firstKey = iterator.next();
                totalCount = Integer.valueOf(countResult.get(firstKey).toString());
            }
        } catch (Exception e) {
            throw new SFABusinessException(SFAErrorCode.ACCOUNT_COMMON_ERROR);
        }
        return totalCount;
    }

    public static String buildSqlInString(List<String> ids) {
        if (CollectionUtils.empty(ids)) {
            return "''";
        }
        ids = ids.stream().distinct().collect(Collectors.toList());
        StringBuilder builder = new StringBuilder();
        for (String id : ids) {
            builder.append("'" + id + "',");
        }
        String idString = builder.toString();
        idString = idString.substring(0, idString.length() - 1);
        return idString;
    }

    public static Map<String, Integer> getPoolCustomerCount(String tenantId, List<String> poolIds) {
        if(isGrayPoolGroupBy(tenantId)) {
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, "tenant_id", tenantId);
            SearchUtil.fillFilterIn(filters, AccountConstants.Field.HIGH_SEAS_ID, poolIds);
            SearchUtil.fillFilterEq(filters, "is_deleted", 0);
            Map<String, Integer> result = CommonSqlUtils.getGroupByResult(tenantId, SFAPreDefineObject.Account.getApiName(), AccountConstants.Field.HIGH_SEAS_ID, filters);
            return result;
        } else {
            String idString = AccountUtil.buildSqlInString(poolIds);
            String querySql = "SELECT high_seas_id, COUNT(1) AS totalcount FROM biz_account WHERE tenant_id='" + tenantId +
                    "' AND is_deleted=0 AND high_seas_id = any(array[" + idString + "]) GROUP BY tenant_id, high_seas_id ";

            return getTotalMap(tenantId, querySql);
        }
    }

    public static Map<String, Integer> getPoolUnDeletedCustomerCount(String tenantId, List<String> poolIds) {
        if(isGrayPoolGroupBy(tenantId)){
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, SystemConstants.Field.TennantID.apiName, tenantId);
            SearchUtil.fillFilterIn(filters, AccountConstants.Field.HIGH_SEAS_ID, poolIds);
            SearchUtil.fillFilterGTE(filters, "is_deleted", 0);
            Map<String, Integer> result = CommonSqlUtils.getGroupByResult(tenantId, SFAPreDefineObject.Account.getApiName(), AccountConstants.Field.HIGH_SEAS_ID, filters);
            return result;
        } else {
            String idString = AccountUtil.buildSqlInString(poolIds);
            String querySql = "SELECT high_seas_id, COUNT(1) AS totalcount FROM biz_account WHERE tenant_id='" + tenantId +
                    "' AND is_deleted>=0 AND high_seas_id = any(array[" + idString + "]) GROUP BY tenant_id, high_seas_id";

            return getTotalMap(tenantId, querySql);
        }
    }

    private static Map<String, Integer> getTotalMap(String tenantId, String querySql) {
        Map<String, Integer> result = Maps.newHashMap();
        try {
            List<Map> queryResult = objectDataService.findBySql(tenantId, querySql);
            if (CollectionUtils.notEmpty(queryResult)) {
                for (Map data : queryResult) {
                    String id = data.get("high_seas_id").toString();
                    Integer totalCount = Integer.valueOf(data.get("totalcount").toString());
                    result.put(id, totalCount);
                }
            }
        } catch (Exception e) {
            throw new SFABusinessException(SFAErrorCode.ACCOUNT_COMMON_ERROR);
        }
        return result;
    }

    public static Map<String, Integer> getPoolUnAllocatedCount(String tenantId, List<String> poolIds) {
        if(isGrayPoolGroupBy(tenantId)) {
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, "tenant_id", tenantId);
            SearchUtil.fillFilterIn(filters, AccountConstants.Field.HIGH_SEAS_ID, poolIds);
            SearchUtil.fillFilterEq(filters, "is_deleted", 0);
            SearchUtil.fillFilterEq(filters, "biz_status", AccountConstants.AccountBizStatus.UN_ALLOCATED.getValue());
            Map<String, Integer> result = CommonSqlUtils.getGroupByResult(tenantId, SFAPreDefineObject.Account.getApiName(), AccountConstants.Field.HIGH_SEAS_ID, filters);
            return result;
        } else {
            String idString = AccountUtil.buildSqlInString(poolIds);
            String querySql = "SELECT high_seas_id, COUNT(1) AS totalcount FROM biz_account WHERE tenant_id='" + tenantId +
                    "' AND is_deleted=0 AND high_seas_id = any(array[" + idString + "]) AND biz_status='" +
                    AccountConstants.AccountBizStatus.UN_ALLOCATED.getValue() + "' " +
                    "GROUP BY tenant_id, high_seas_id";

            return getTotalMap(tenantId, querySql);
        }
    }

    public static void updateContactOwner(User user, List<String> accountIds, String owner, String dataOwnerDpt, Boolean cleanTeamMember) {
        if (CollectionUtils.empty(accountIds)) {
            return;
        }
        int offset = 0;
        int limit = 200;
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, "account_id", accountIds);
        searchTemplateQuery.setFilters(filters);
        List<com.facishare.paas.metadata.impl.search.OrderBy> orderByList = Lists.newArrayList();
        com.facishare.paas.metadata.impl.search.OrderBy orderBy = new com.facishare.paas.metadata.impl.search.OrderBy();
        orderBy.setFieldName("create_time");
        orderBy.setIsAsc(true);
        orderByList.add(orderBy);
        searchTemplateQuery.setOrders(orderByList);
        searchTemplateQuery.setLimit(limit);
        searchTemplateQuery.setOffset(offset);
        int executeCount = 0;
        while (true) {
            ++executeCount;
            QueryResult<IObjectData> queryResult = SERVICE_FACADE.findBySearchQuery(user,
                    SFAPreDefineObject.Contact.getApiName(), searchTemplateQuery);
            if (CollectionUtils.empty(queryResult.getData())) {
                if (!Strings.isNullOrEmpty(owner)) {
                    contactSessionSandwichService.push(user.getTenantId(), owner);
                }
                break;
            }
            List<IObjectData> objectDataList = queryResult.getData();
            List<IObjectData> oldDataList = Lists.newArrayList();
            for (IObjectData objectData : objectDataList) {
                oldDataList.add(ObjectDataExt.of(objectData).copy());
                if (StringUtils.isEmpty(owner)) {
                    objectData.set("owner", Lists.newArrayList());
                } else {
                    objectData.set("owner", Lists.newArrayList(owner));
                }
                objectData.set("owner_changed_time", System.currentTimeMillis());
                objectData.set("data_own_department", Lists.newArrayList(dataOwnerDpt));
                objectData.setLastModifiedBy(user.getUserId());
                objectData.setLastModifiedTime(System.currentTimeMillis());
            }
            List<String> updateFieldList = Lists.newArrayList("owner", "owner_changed_time", "data_own_department",
                    "last_modified_by", "last_modified_time");
            try {
                objectDataService.batchUpdateWithField(objectDataList, updateFieldList, getDefaultActionContext(user));
                IObjectDescribe contactDescribe = SERVICE_FACADE.findObject(user.getTenantId(),
                        SFAPreDefineObject.Contact.getApiName());
                if (cleanTeamMember != null && cleanTeamMember) {
                    teamMemberService.removeObjectAllTeamMemberExceptOwner(user, objectDataList);
                }

                if (StringUtils.isEmpty(owner)) {
                    teamMemberService.removeObjectOwner(user, objectDataList);
                } else {
                    teamMemberService.changeOwner(user, owner, objectDataList);
                }

                ContactUtil.recordOwnerChangeHistory(user, oldDataList);

                SERVICE_FACADE.logByActionType(user, EventType.MODIFY, ActionType.ChangeOwner, oldDataList, objectDataList, contactDescribe);

                offset += limit;
                searchTemplateQuery.setOffset(offset);
            } catch (Exception e) {
                if (!Strings.isNullOrEmpty(owner)) {
                    contactSessionSandwichService.push(user.getTenantId(), owner);
                }
                log.error("updateContactOwner {}", e);
                throw new SFABusinessException(SFAErrorCode.ACCOUNT_COMMON_ERROR);
            }

            if (executeCount > 10000) {
                log.error("update updateContactOwner, user:{}, accountIds:{}, owner:{}, cleanTeamMember:{}", user,
                        accountIds, owner, cleanTeamMember);
                break;
            }
        }
    }

    public static ActionContext getDefaultActionContext(User user) {
        return getDefaultActionContextByApiName(user, SFAPreDefineObject.Account.getApiName());
    }

    public static ActionContext getDefaultActionContextByApiName(User user, String apiName) {
        IObjectDescribe objectDescribe = null;
        try {
            objectDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(user.getTenantId(), apiName);
        } catch (Exception e) {

        }
        ActionContext actionContext = new ActionContext();
        actionContext.setEnterpriseId(user.getTenantId());
        actionContext.setUserId(user.getUserId());
        actionContext.setDbType("pg");
        actionContext.setAllowUpdateInvalid(true);
        actionContext.put("not_validate", true);
        actionContext.setPrivilegeCheck(false);
        actionContext.setObjectDescribe(objectDescribe);
        return actionContext;
    }

    public static ActionContext getDefaultActionContext(User user, String eventId) {
        ActionContext actionContext = getDefaultActionContext(user);
        if (StringUtils.isNotBlank(eventId)) {
            actionContext.put("eventId", eventId);
        }
        return actionContext;
    }

    public static void checkAccountClaimTime(User user, String ownerId, String poolId, List<String> objectIds) {
        if (CollectionUtils.empty(objectIds) || StringUtils.isEmpty(poolId) || StringUtils.isEmpty(ownerId)) {
            return;
        }
        IObjectData poolData = ACCOUNT_POOL_SERVICE_IMPL.getObjectPoolById(user.getTenantId(), poolId);
        Integer claimIntervalDays = getIntegerValue(poolData, "claim_interval_days", 0);
        if (claimIntervalDays <= 0) {
            return;
        }
        boolean canClaim = Objects.equals(true, checkCustomerClaimTime(user, ownerId, poolId, objectIds, claimIntervalDays));
        if (!canClaim) {
            throw new ValidateException(String.format(I18N.text(SFA_CANT_RECEIVE_SAME_IN_DAYS),
                    claimIntervalDays, I18N.text("AccountObj.attribute.self.display_name")));
        }
    }

    public static boolean hasOwner(IObjectData objectData) {
        List<String> ownerList = objectData.getOwner();
        if (CollectionUtils.empty(ownerList)) {
            return false;
        }
        String owner = ownerList.get(0);
        if (StringUtils.isEmpty(owner) || "0".equals(owner)) {
            return false;
        }
        return true;
    }

    public static String getUserMainDepartId(String tenantId, String userId) {
        if (StringUtils.isEmpty(userId)) {
            return "";
        }
        Map<String, QueryDeptInfoByUserIds.MainDeptInfo> mainDeptInfoMap = SERVICE_FACADE.getMainDeptInfo(tenantId, userId, Lists.newArrayList(userId));
        if (mainDeptInfoMap.containsKey(userId)) {
            return mainDeptInfoMap.get(userId).getDeptId();
        }
        return "";
    }

    public static String getUserMainDepartName(String tenantId, String userId) {
        if (StringUtils.isEmpty(userId)) {
            return null;
        }
        Map<String, QueryDeptInfoByUserIds.MainDeptInfo> mainDeptInfoMap = SERVICE_FACADE.getMainDeptInfo(tenantId, userId, Lists.newArrayList(userId));
        if (mainDeptInfoMap.containsKey(userId)) {
            return mainDeptInfoMap.get(userId).getDeptName();
        }
        return null;
    }

    public static Map<String, String> getUserMainDepartIdMap(String tenantId, List<String> userIds) {
        Map<String, String> result = Maps.newHashMap();
        if (CollectionUtils.empty(userIds)) {
            return result;
        }
        Map<String, QueryDeptInfoByUserIds.MainDeptInfo> mainDeptInfoMap = SERVICE_FACADE.getMainDeptInfo(tenantId, User.SUPPER_ADMIN_USER_ID, userIds);
        if (CollectionUtils.notEmpty(mainDeptInfoMap)) {
            for (Map.Entry<String, QueryDeptInfoByUserIds.MainDeptInfo> data : mainDeptInfoMap.entrySet()) {
                result.put(data.getKey(), data.getValue().getDeptId());
            }
        }
        return result;
    }

    public static List<String> getUserDepartIds(String tenantId, String userId) {
        //List<QueryDeptInfoByUserId.DeptInfo> deptInfos = SERVICE_FACADE.getDeptInfoByUserId(tenantId, userId, userId);
        List<String> departList = SERVICE_FACADE.queryAllSuperDeptByUserId(tenantId, userId, userId);
//        List<String> deptIds = Lists.newArrayList();
//        if (CollectionUtils.notEmpty(deptInfos)) {
//            deptIds.addAll(deptInfos.stream().map(x -> x.getDeptId()).collect(Collectors.toList()));
//        }
        return departList;
    }

    public static Map<String, String> getUserName(String tenantId, List<String> userIds) {
        Map<String, String> result = SERVICE_FACADE.getUserNameMapByIds(tenantId, User.SUPPER_ADMIN_USER_ID, userIds);
        return result;
    }

    public static String getStringValue(IObjectData objectData, String key, String defaultValue) {
        if (objectData == null || StringUtils.isEmpty(key)) {
            return defaultValue;
        }
        Object tempValue = objectData.get(key);
        if (tempValue != null) {
            return tempValue.toString();
        }
        return defaultValue;
    }

    public static String getStringValue(ObjectDataDocument dataDocument, String key, String defaultValue) {
        IObjectData objectData = dataDocument.toObjectData();
        return getStringValue(objectData, key, defaultValue);
    }

    public static Integer getIntegerValue(IObjectData objectData, String key, Integer defaultValue) {
        if (objectData == null || StringUtils.isEmpty(key)) {
            return defaultValue;
        }
        Object tempValue = objectData.get(key);
        if (tempValue != null) {
            try {
                return Integer.valueOf(tempValue.toString());
            } catch (Exception e) {
                return defaultValue;
            }
        }
        return defaultValue;
    }

    public static Integer getIntegerValue(ObjectDataDocument dataDocument, String key, Integer defaultValue) {
        IObjectData objectData = dataDocument.toObjectData();
        return getIntegerValue(objectData, key, defaultValue);
    }

    public static boolean getBooleanValue(IObjectData objectData, String key, boolean defaultValue) {
        if (objectData == null || StringUtils.isEmpty(key)) {
            return defaultValue;
        }
        Object tempValue = objectData.get(key);
        if (tempValue != null) {
            try {
                Boolean result = Boolean.valueOf(tempValue.toString());
                if (result == null) {
                    return defaultValue;
                }
                return result ? true : false;
            } catch (Exception e) {
                return defaultValue;
            }
        }
        return defaultValue;
    }

    public static boolean getBooleanValue(ObjectDataDocument dataDocument, String key, boolean defaultValue) {
        IObjectData objectData = dataDocument.toObjectData();
        return getBooleanValue(objectData, key, defaultValue);
    }

    public static Long getLongValue(IObjectData objectData, String key, Long defaultValue) {
        if (objectData == null || StringUtils.isEmpty(key)) {
            return defaultValue;
        }
        Object tempValue = objectData.get(key);
        if (tempValue != null) {
            try {
                return Long.valueOf(tempValue.toString());
            } catch (Exception e) {
                return defaultValue;
            }
        }
        return defaultValue;
    }

    public static List<String> getListValue(ObjectDataDocument dataDocument, String key, List<String> defaultValue) {
        IObjectData objectData = dataDocument.toObjectData();
        return getListValue(objectData, key, defaultValue);
    }

    public static List<String> getListValue(IObjectData objectData, String key, List<String> defaultValue) {
        Object value = objectData.get(key);
        if (null == value) {
            return Lists.newArrayList();
        } else {
            String str;
            if (value instanceof String) {
                str = (String) value;
            } else {
                str = JSON.toJSONString(value);
            }

            return (List) JSONObject.parseObject(str, List.class);
        }
    }

    public static List<TeamMember> getTeamMember(IObjectData objectData) {
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        List<TeamMember> teamMembers = objectDataExt.getTeamMembers();
        return teamMembers;
    }

    public static void sendCRMNotification(User user, String remindContent, Integer remindRecordType, String title
            , String dataId, String content2Id, List<String> receiverIds) {
        receiverIds.removeIf(x -> user.getUserId().equals(x));
        receiverIds = receiverIds.stream().distinct().collect(Collectors.toList());
        if (CollectionUtils.empty(receiverIds)) {
            return;
        }

        List<Integer> realReceiverIds = Lists.newArrayList();
        for (String receiverId : receiverIds) {
            if (StringUtils.isBlank(receiverId)) {
                continue;
            }
            try {
                Integer realId = Integer.valueOf(receiverId);
                realReceiverIds.add(realId);
            } catch (Exception e) {
                log.error("accountutil sendCRMNotification error", e);
            }
        }
        if (StringUtils.isEmpty(content2Id)) {
            content2Id = user.getUserId();
        } else {
            try {
                Integer realId = Integer.valueOf(content2Id);
            } catch (Exception e) {
                content2Id = user.getUserId();
            }
        }
        CRMNotification crmNotification = CRMNotification.builder().sender(user.getUserId())
                .remindRecordType(remindRecordType).title(title).content(remindContent).dataId(dataId)
                .content2Id(content2Id)
                .receiverIds(realReceiverIds.stream().collect(Collectors.toSet()))
                .objectApiName(Utils.ACCOUNT_API_NAME)
                .build();
//        if (StringUtils.isNotBlank(dataId)) {
//            crmNotification.setFixContent2ID(String.format("{\"dataId\":\"%s\",\"type\":\"%s\"}", dataId, Utils.ACCOUNT_API_NAME));
//        }
        crmNotificationService.sendCRMNotification(user, crmNotification);
    }

    public static void sendCRMNotification(User user, String remindContent, Integer remindRecordType, String title
            , String dataId, String content2Id, List<String> receiverIds, Boolean remove, String apiName) {
        if (remove) {
            receiverIds.removeIf(x -> user.getUserId().equals(x));
        }
        receiverIds = receiverIds.stream().distinct().collect(Collectors.toList());
        if (CollectionUtils.empty(receiverIds)) {
            return;
        }

        List<Integer> realReceiverIds = Lists.newArrayList();
        for (String receiverId : receiverIds) {
            if (StringUtils.isBlank(receiverId)) {
                continue;
            }
            try {
                Integer realId = Integer.valueOf(receiverId);
                realReceiverIds.add(realId);
            } catch (Exception e) {
                log.error("accountutil sendCRMNotification error", e);
            }
        }
        if (StringUtils.isEmpty(content2Id)) {
            content2Id = user.getUserId();
        } else {
            try {
                Integer realId = Integer.valueOf(content2Id);
            } catch (Exception e) {
                content2Id = user.getUserId();
            }
        }
        CRMNotification crmNotification = CRMNotification.builder().sender(user.getUserId())
                .remindRecordType(remindRecordType).title(title).content(remindContent).dataId(dataId)
                .content2Id(content2Id)
                .receiverIds(realReceiverIds.stream().collect(Collectors.toSet()))
                .objectApiName(apiName)
                .build();

//        if (StringUtils.isNotBlank(dataId)) {
//            crmNotification.setFixContent2ID(String.format("{\"dataId\":\"%s\",\"type\":\"%s\"}", dataId, apiName));
//        }
        crmNotificationService.sendCRMNotification(user, crmNotification);
    }

    public static List<BaseImportAction.ImportError> importCustomValidate(List<BaseImportDataAction.ImportData> dataList, User user, boolean isUpdateImport, IObjectDescribe objectDescribe, Boolean isEmptyValueToUpdate) {
        List<BaseImportAction.ImportError> errorList = Lists.newArrayList();
        if (CollectionUtils.empty(dataList)) {
            return errorList;
        }
        if (!AccountUtil.isUserDefineDealSetting(user.getTenantId())) {
            BaseImportDataAction.ImportData data = dataList.get(0);
            if (data.containsField(AccountConstants.Field.DEAL_STATUS)) {
                dataList.forEach(x -> {
                    errorList.add(new BaseImportAction.ImportError(x.getRowNo(), "模板错误，系统预置成交规则不允许手动更新成交状态"));
                });
            }
        }

        List<String> highSeasNames = dataList.stream()
                .filter(x -> StringUtils.isNotEmpty(AccountUtil.getStringValue(x.getData(), "high_seas_name", "")))
                .map(x -> AccountUtil.getStringValue(x.getData(), "high_seas_name", ""))
                .distinct().collect(Collectors.toList());

        Map<String, String> highSeasNameMap = Maps.newHashMap();
        if (CollectionUtils.notEmpty(highSeasNames)) {
            highSeasNameMap = SERVICE_FACADE.findObjectIdByName(user, Utils.HIGHSEAS_API_NAME, highSeasNames);
        }
        List<String> notExistHighSeas = Lists.newArrayList();
        for (String highSeasName : highSeasNames) {
            if (!highSeasNameMap.containsKey(highSeasName)) {
                notExistHighSeas.add(highSeasName);
            }
        }
        if (CollectionUtils.notEmpty(notExistHighSeas)) {
            dataList.forEach(x -> {
                if (notExistHighSeas.contains(AccountUtil.getStringValue(x.getData(), "high_seas_name", ""))) {
                    errorList.add(new BaseImportAction.ImportError(x.getRowNo(), String.format(I18N.text(SFA_NOT_EXISTS),
                            I18N.text("HighSeasObj.attribute.self.display_name"))));
                }
            });
        }

        if (!isUpdateImport) {
            List<String> ownerList = dataList.stream()
                    .filter(x -> AccountUtil.hasOwner(x.getData())).map(x -> AccountUtil.getOwner(x.getData())).distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.notEmpty(ownerList)) {
                ownerList.forEach(x -> {
                    List<IObjectData> tempDataList = dataList.stream()
                            .filter(d -> AccountUtil.getOwner(d.getData()).equals(x)).map(d -> d.getData())
                            .collect(Collectors.toList());
                    if (!ObjectLimitUtil.isGrayAccountLimit(user.getTenantId())) {
                        try {
                            AccountUtil.checkAccountLimit(user, x, tempDataList);
                        } catch (Exception e) {
                            List<BaseImportDataAction.ImportData> errorDataList = dataList.stream()
                                    .filter(d -> AccountUtil.getOwner(d.getData()).equals(x))
                                    .collect(Collectors.toList());
                            if (CollectionUtils.notEmpty(errorDataList)) {
                                errorDataList.forEach(d -> errorList.add(new BaseImportAction.ImportError(d.getRowNo(),
                                        String.format(I18N.text(SFA_REACH_LIMIT_OBJ),
                                                I18N.text("AccountObj.attribute.self.display_name")))));
                            }
                        }
                    } else {
                        tempDataList.forEach(data -> data.set(SystemConstants.Field.Id.apiName, SERVICE_FACADE.generateId()));
                        List<IObjectData> checkLimitDataList = ObjectDataExt.copyList(tempDataList);
                        checkLimitDataList.forEach(data -> {
                            data.set(SystemConstants.Field.LifeStatus.apiName, SystemConstants.LifeStatus.Normal.value);
                            data.set(AccountConstants.Field.BIZ_STATUS, AccountConstants.AccountBizStatus.ALLOCATED.getValue());
                            data.set("last_follow_time", System.currentTimeMillis());
                            data.set("owner_modified_time", System.currentTimeMillis());
                            data.set("owner_department", AccountUtil.getUserMainDepartName(user.getTenantId(), x));
                            data.set("created_by", Lists.newArrayList(user.getUserId()));
                            data.set("create_time", System.currentTimeMillis());
                            List<String> ownDepartment = AccountUtil.getListValue(data, SystemConstants.Field.DataOwnDepartment.apiName, Lists.newArrayList());
                            if (CollectionUtils.empty(ownDepartment)) {
                                data.set("data_own_department", Lists.newArrayList(AccountUtil.getUserMainDepartId(user.getTenantId(), x)));
                            }
                        });
                        ObjectLimitUtil.CheckLimitResult checkLimitResult = ObjectLimitUtil.checkObjectLimit(user, SFAPreDefineObject.Account.getApiName(), x, checkLimitDataList, objectDescribe);
                        if (CollectionUtils.notEmpty(checkLimitResult.getFailureIds())) {
                            dataList.forEach(data -> {
                                if (checkLimitResult.getFailureIds().contains(data.getData().getId())) {
                                    errorList.add(new BaseImportAction.ImportError(data.getRowNo(), String.format(I18N.text(SFA_REACH_LIMIT_OBJ),
                                            I18N.text("AccountObj.attribute.self.display_name"))));
                                }
                            });
                        }
                    }
                });
            }
        } else {
            if (ObjectLimitUtil.isGrayAccountLimit(user.getTenantId())) {
                List<String> objectDataIds = dataList.stream().map(x -> x.getData().getId()).collect(Collectors.toList());
                List<IObjectData> objectDataList = SERVICE_FACADE.findObjectDataByIdsIncludeDeleted(user, objectDataIds, SFAPreDefineObject.Account.getApiName());
                List<String> ownerList = objectDataList.stream()
                        .filter(x -> AccountUtil.hasOwner(x)).map(x -> AccountUtil.getOwner(x)).distinct()
                        .collect(Collectors.toList());
                if (CollectionUtils.notEmpty(ownerList)) {
                    ownerList.forEach(x -> {
                        List<IObjectData> tempDataList = objectDataList.stream()
                                .filter(d -> AccountUtil.getOwner(d).equals(x))
                                .collect(Collectors.toList());
                        List<IObjectData> oldDataList = ObjectDataExt.copyList(tempDataList);
                        List<IObjectData> checkLimitDataList = ObjectDataExt.copyList(tempDataList);
                        checkLimitDataList.forEach(data -> {
                            data.set(SystemConstants.Field.LifeStatus.apiName, SystemConstants.LifeStatus.Normal.value);
                            data.set(AccountConstants.Field.BIZ_STATUS, AccountConstants.AccountBizStatus.ALLOCATED.getValue());
                            data.setLastModifiedBy(user.getUserId());
                            data.setLastModifiedTime(System.currentTimeMillis());
                            List<IObjectData> updateDataList = dataList.stream().filter(d -> data.getId().equals(d.getData().getId()))
                                    .map(d -> d.getData()).collect(Collectors.toList());
                            if (CollectionUtils.notEmpty(updateDataList)) {
                                IObjectData updateData = updateDataList.get(0);
                                ObjectDataDocument dataDocument = ObjectDataDocument.of(updateData);
                                if (isEmptyValueToUpdate == null || !isEmptyValueToUpdate) {
                                    dataDocument.entrySet().removeIf((next) -> {
                                        String value = ObjectDataExt.of(updateData).getStringValueInImport(next.getKey());
                                        return Strings.isNullOrEmpty(value);
                                    });
                                }
                                dataDocument.entrySet().forEach((next) -> {
                                    data.set(next.getKey(), next.getValue());
                                });
                            }
                        });
                        ObjectLimitUtil.CheckLimitResult checkLimitResult = ObjectLimitUtil.checkObjectLimitForEdit(user, SFAPreDefineObject.Account.getApiName(), x, oldDataList, checkLimitDataList, objectDescribe);
                        if (CollectionUtils.notEmpty(checkLimitResult.getFailureIds())) {
                            dataList.forEach(data -> {
                                if (checkLimitResult.getFailureIds().contains(data.getData().getId())) {
                                    errorList.add(new BaseImportAction.ImportError(data.getRowNo(), String.format(I18N.text(SFA_REACH_LIMIT_OBJ),
                                            I18N.text("AccountObj.attribute.self.display_name"))));
                                }
                            });
                        }
                    });
                }
            }
        }

        //checkAccountNo(user.getTenantId(), dataList, errorList);
        checkOnlyAllowMemberMove(user, dataList, errorList, Utils.HIGHSEAS_API_NAME);

        return errorList;
    }

    public static void importCustomDefaultValue(List<IObjectData> validList, String tenantId, IObjectDescribe objectDescribe) {
        validList.forEach(x -> {
            x.setTenantId(tenantId);
            x.setDescribeApiName(objectDescribe.getApiName());
            x.setDescribeId(objectDescribe.getId());
            x.set("pin_yin", Chinese2PinyinUtils.getPinyinString(x.getName()));
            x.set("life_status", "normal");
            x.set("lock_status", "0");
            x.set("owner_modified_time", System.currentTimeMillis());
            x.set("last_followed_time", System.currentTimeMillis());
            int completed_field_quantity = AccountUtil.calculateObjectHasValueCount(objectDescribe, x);
            x.set("completed_field_quantity", completed_field_quantity);

            String owner = x.get(AccountConstants.Field.OWNER, String.class);
            if (StringUtils.isNotEmpty(owner)) {
                x.set(AccountConstants.Field.BIZ_STATUS, AccountConstants.AccountBizStatus.ALLOCATED.getValue());
                x.set(AccountConstants.Field.TRANSFER_COUNT, 1);
                x.set("claimed_time", System.currentTimeMillis());
            } else {
                x.set(AccountConstants.Field.BIZ_STATUS, AccountConstants.AccountBizStatus.UN_ALLOCATED.getValue());
                x.set(AccountConstants.Field.TRANSFER_COUNT, 0);
            }
            if (AccountUtil.isUserDefineDealSetting(tenantId)) {
                String dealStatus = getStringValue(x, AccountConstants.Field.DEAL_STATUS, AccountConstants.DealStatus.UN_DEAL.getValue());
                if (dealStatus.equals(AccountConstants.DealStatus.UN_DEAL.getValue())) {
                    x.set(AccountConstants.Field.LAST_DEAL_TIME, null);
                } else {
                    Long dealTime = getLongValue(x, AccountConstants.Field.LAST_DEAL_TIME, System.currentTimeMillis());
                    x.set(AccountConstants.Field.LAST_DEAL_TIME, dealTime);
                }
            } else {
                x.set(AccountConstants.Field.DEAL_STATUS, AccountConstants.DealStatus.UN_DEAL.getValue());
                x.set(AccountConstants.Field.LAST_DEAL_TIME, null);
            }
        });
    }

    public static List<String> getImportTemplateRemoveFields(String tenantId) {
        List<String> removeFields = Lists.newArrayList(
                "lock_status", "life_status", "owner_department", "leads_id", "remaining_time",
                "owner_modified_time", "claimed_time", "last_deal_closed_amount", "returned_time", "transfer_count",
                "account_status", "biz_status", "high_seas_name", "last_followed_time", "recycled_reason",
                "expire_time", "out_resources", "total_refund_amount", "filling_checker_id", "pin_yin", "extend_obj_data_id",
                "completed_field_quantity", "remind_days", "is_er_enterprise", "biz_reg_name", "phone_number_attribution_country",
                "phone_number_attribution_location", "phone_number_attribution_address", "phone_number_attribution_city",
                "phone_number_attribution_province", "phone_number_attribution_district", "account_path"
        );
        if (!isUserDefineDealSetting(tenantId)) {
            removeFields.add("deal_status");
            removeFields.add("last_deal_closed_time");
        }
        return removeFields;
    }

    public static void handleIsRemindRecycling(List<IObjectData> objectDataList) {
        try {
            if (CollectionUtils.notEmpty(objectDataList)) {
                sfaRecyclingService.getRecyclingRule("AccountObj", objectDataList);
            }
        } catch (Exception e) {
            log.error("handleIsRemindRecycling error", e);
        }
    }

    public static void calculateCompletionRate(IObjectDescribe objectDescribe, List<IObjectData> objectDataList) {
        if (objectDescribe == null || CollectionUtils.empty(objectDataList)) {
            return;
        }
        List<IFieldDescribe> activeFieldList = objectDescribe.getFieldDescribes().stream().filter(x -> x.isActive())
                .collect(Collectors.toList());
        int fieldTotalCount = activeFieldList.size();
        for (IObjectData objectData : objectDataList) {
            int hasValueFieldCount = getIntegerValue(objectData, "completed_field_quantity", 0);
            double completeRate = ((double) hasValueFieldCount) / fieldTotalCount * 100;
            NumberFormat nf = NumberFormat.getNumberInstance();
            nf.setMaximumFractionDigits(2);
            String completionRate = nf.format(completeRate) + "%";
            objectData.set("completion_rate", completionRate);
        }
    }

    public static int calculateObjectHasValueCount(IObjectDescribe objectDescribe, IObjectData objectData) {
        int result = 0;
        for (IFieldDescribe fieldDescribe : objectDescribe.getFieldDescribes()) {
            Object fieldValue = objectData.get(fieldDescribe.getApiName());
            if (fieldValue != null) {
                if (fieldValue instanceof List) {
                    if (CollectionUtils.notEmpty((List) fieldValue)) {
                        ++result;
                    }
                } else if (fieldValue instanceof String) {
                    if (StringUtils.isNotEmpty((String) fieldValue)) {
                        ++result;
                    }
                } else {
                    ++result;
                }
            }
        }
        return result;
    }

    public static void setImportFields(String tenantId, String objectCode, List<IFieldDescribe> fieldDescribes, boolean isUpdateImport) {
        log.info("setImportFields tenantId:{},objectCode:{},fieldDescribes{},isUpdateImport:{} ", tenantId, objectCode, fieldDescribes, isUpdateImport);
        Optional<IFieldDescribe> poolDescribeOptional = fieldDescribes.stream()
                .filter(h -> AccountConstants.Field.HIGH_SEAS_ID.equals(h.getApiName())).findFirst();
        Optional<IFieldDescribe> ownerDescribeOptional = fieldDescribes.stream()
                .filter(h -> AccountConstants.Field.OWNER.equals(h.getApiName())).findFirst();
        Optional<IFieldDescribe> dealStatusDescribeOptional = fieldDescribes.stream()
                .filter(h -> AccountConstants.Field.DEAL_STATUS.equals(h.getApiName())).findFirst();

        if (Utils.HIGHSEAS_API_NAME.equals(objectCode) || "highseas".equals(objectCode)) {
            if (poolDescribeOptional.isPresent()) {
                poolDescribeOptional.get().setRequired(true);
            }
            if (ownerDescribeOptional.isPresent()) {
                ownerDescribeOptional.get().setRequired(false);
            }
        } else {
            if (poolDescribeOptional.isPresent()) {
                poolDescribeOptional.get().setRequired(false);
            }
            if (ownerDescribeOptional.isPresent()) {
                ownerDescribeOptional.get().setRequired(true);
            }
        }

        if (AccountUtil.isUserDefineDealSetting(tenantId)) {
            if (isUpdateImport) {
                if (dealStatusDescribeOptional.isPresent()) {
                    dealStatusDescribeOptional.get().setRequired(false);
                }
            } else {
                if (dealStatusDescribeOptional.isPresent()) {
                    dealStatusDescribeOptional.get().setRequired(true);
                }
            }
        }
    }

    public static String getRecyclingReason(User user, ObjectAction action, String owner) {
        String recyclingReason = "";
        switch (action) {
            case ALLOCATE:
                if (StringUtils.isNotEmpty(owner)) {
                    recyclingReason = "管理员（上级）收回";
                }
                break;
            case TAKE_BACK:
                recyclingReason = "管理员（上级）收回";
                break;
            case RETURN:
            case CHANGE_OWNER:
                if (user.getUserId().equals(owner)) {
                    recyclingReason = "销售人员退回";
                } else {
                    recyclingReason = "管理员（上级）收回";
                }
                break;
            default:
                recyclingReason = "未成交/未跟进收回";
                break;
        }
        return recyclingReason;
    }

    public static List<IObjectData> fillOldData(List<IObjectData> objectDataList, List<IObjectData> oldDataList) {
        return ObjectDataDocument.fillOldData(objectDataList, oldDataList);
    }

    public static boolean isOpenAccountFillingCheck(String tenantId) {
        String configValue = SFAConfigUtil.getConfigValue(tenantId, "3", User.SUPPER_ADMIN_USER_ID);
        return "1".equals(configValue);
    }

    public static boolean isUserDefineDealSetting(String tenantId) {
        String sql = "SELECT * FROM object_follow_deal_setting"
                + String.format(" WHERE tenant_id = '%s'", tenantId)
                + " AND is_user_define_setting = 't' AND setting_type='2' AND is_deleted>=0 LIMIT 1";
        try {
            List<Map> queryResult = objectDataService.findBySql(tenantId, sql);
            if (CollectionUtils.notEmpty(queryResult)) {
                return true;
            }
            return false;
        } catch (MetadataServiceException e) {
            throw new APPException("getConfigValueFromPg 元数据异常", e);
        }
    }

    public static void getOwnerFilter(List<IFilter> filters, User user) {
        if (user.isOutUser()) {
            SearchUtil.fillFilterEq(filters, "out_owner", Lists.newArrayList(user.getOutUserId()));
        } else {
            SearchUtil.fillFilterEq(filters, "owner", Lists.newArrayList(user.getUserId()));
        }
    }

    public static void checkAccountLimit(User user, String owner, String poolId, List<IObjectData> objectDataList,
                                         ObjectPoolPermission.ObjectPoolMemberType objectPoolMemberType,
                                         Long outerTenantId, Long outerOwnerId) {
        if (CollectionUtils.empty(objectDataList) || StringUtils.isEmpty(owner)) {
            return;
        }
        checkPoolAccountLimit(user, owner, poolId, objectDataList, objectPoolMemberType, outerTenantId, outerOwnerId);
        CustomerLimit.Result rstResult = null;
        if (objectPoolMemberType != ObjectPoolPermission.ObjectPoolMemberType.OUTER_ENTERPRISE &&
                objectPoolMemberType != ObjectPoolPermission.ObjectPoolMemberType.OUTER_EMPLOYEE) {
            rstResult = getCustomerLimit(user, Integer.valueOf(owner));
        }
        if (rstResult != null && rstResult.getValue().getIncludeHighSeasCustomer()) {
            checkAccountLimit(user, owner, objectDataList);
            return;
        }
    }

    public static void checkPoolAccountLimit(User user, String owner, String poolId, List<IObjectData> objectDataList,
                                             ObjectPoolPermission.ObjectPoolMemberType objectPoolMemberType,
                                             Long outerTenantId, Long outerOwnerId) {
        if (CollectionUtils.empty(objectDataList) || StringUtils.isEmpty(owner)) {
            return;
        }
        IObjectData poolData = ACCOUNT_POOL_SERVICE_IMPL.getObjectPoolById(user.getTenantId(), poolId);
        if (poolData == null) {
            return;
        }
        List<IObjectData> unDealAccountList = objectDataList.stream()
                .filter(x -> getStringValue(x, AccountConstants.Field.DEAL_STATUS, AccountConstants.DealStatus.UN_DEAL.getValue()).equals(AccountConstants.DealStatus.UN_DEAL.getValue()))
                .collect(Collectors.toList());

        boolean includeDealCustomer = getBooleanValue(poolData, "is_claim_limit_include_dealed_customers", false);

        if (!includeDealCustomer && CollectionUtils.empty(unDealAccountList)) {
            return;
        }
        Integer claimLimitNum = getIntegerValue(poolData, "claim_limit_num", 0);
        String limitType = getStringValue(poolData, "limit_type", "");

        Integer totalCount = getOwnerCustomerCount(user.getTenantId(), owner, poolId, includeDealCustomer, limitType,
                objectPoolMemberType, outerTenantId, outerOwnerId);
        if (totalCount > claimLimitNum) {
            throw new SFABusinessException(SFAErrorCode.ACCOUNT_LIMIT_ERROR);
        }
        if (includeDealCustomer) {
            if (totalCount + objectDataList.size() > claimLimitNum) {
                throw new SFABusinessException(SFAErrorCode.ACCOUNT_LIMIT_ERROR);
            }
        } else {
            if (CollectionUtils.notEmpty(unDealAccountList)) {
                if (totalCount + unDealAccountList.size() > claimLimitNum) {
                    throw new SFABusinessException(SFAErrorCode.ACCOUNT_LIMIT_ERROR);
                }
            }
        }
    }

    public static Integer getOwnerCustomerCount(String tenantId, String employeeId, String poolId, Boolean includeDealCustomer,
                                                String limitType, ObjectPoolPermission.ObjectPoolMemberType objectPoolMemberType,
                                                Long outerTenantId, Long outerOwnerId) {
        Integer totalCount = 0;
        Count countFieldDescribe = getCountField();
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, "high_seas_id", poolId);
        if (objectPoolMemberType == ObjectPoolPermission.ObjectPoolMemberType.OUTER_EMPLOYEE ||
                objectPoolMemberType == ObjectPoolPermission.ObjectPoolMemberType.OUTER_ENTERPRISE) {
            SearchUtil.fillFilterEq(filters, "out_tenant_id", String.valueOf(outerTenantId));
            if (!ObjectPoolPermission.ObjectPoolLimitType.ENTERPRISE.getValue().equals(limitType)) {
                SearchUtil.fillFilterEq(filters, "out_owner", String.valueOf(outerOwnerId));
            }
        } else {
            SearchUtil.fillFilterEq(filters, "owner", employeeId);
        }
        if (!includeDealCustomer) {
            SearchUtil.fillFilterEq(filters, "deal_status", "1");
        }
        query.setFilters(filters);
        Object objResult = SERVICE_FACADE.getCountValue(tenantId, countFieldDescribe, query);
        if (objResult != null && !Strings.isNullOrEmpty(objResult.toString())) {
            totalCount = Integer.valueOf(objResult.toString());
        }
        return totalCount;
    }

    public static Count getCountField() {
        Count countFieldDescribe = new CountFieldDescribe();
        countFieldDescribe.setApiName(Utils.ACCOUNT_API_NAME);
        countFieldDescribe.setFieldApiName("totalcount");
        countFieldDescribe.setSubObjectDescribeApiName(Utils.ACCOUNT_API_NAME);
        countFieldDescribe.setCountFieldApiName("id");
        countFieldDescribe.setCountType(Count.TYPE_COUNT);
        countFieldDescribe.setReturnType("number");
        countFieldDescribe.setDecimalPlaces(0);
        return countFieldDescribe;
    }

    public static void checkOuterAccountLimit(User user, String owner, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList) || StringUtils.isEmpty(owner)) {
            return;
        }
        List<String> poolIds = getPoolIds(objectDataList);
        if (CollectionUtils.notEmpty(poolIds)) {
            poolIds.forEach(x -> {
                List<IObjectData> tempDataList = objectDataList.stream().filter(data ->
                        x.equals(data.get(AccountConstants.Field.HIGH_SEAS_ID, String.class)))
                        .collect(Collectors.toList());
                if (CollectionUtils.notEmpty(tempDataList)) {
                    checkPoolAccountLimit(user, owner, x, tempDataList, ObjectPoolPermission.ObjectPoolMemberType.OUTER_EMPLOYEE,
                            Long.valueOf(tempDataList.get(0).getOutTenantId()), Long.valueOf(owner));
                }
            });
        }
    }

    public static void handleSearchQuery(SearchTemplateQuery query, User user) {
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, "life_status", SystemConstants.LifeStatus.UnderReview.value);
        SearchUtil.fillFilterEq(filters, AccountConstants.Field.FILLING_CHECKER_ID, user.getUserId());
        if (!AccountUtil.isOpenAccountFillingCheck(user.getTenantId())) {
            IFilter filter = new Filter();
            filter.setFieldName("name");
            filter.setFieldValues(Lists.newArrayList());
            filter.setOperator(Operator.IS);
            filters.add(filter);
        }
        query.setFilters(filters);
        query.setPermissionType(0);
    }

    public static boolean isShowCompanyLyricalAll(String tenantId, String companyName) {
        return  false;
//        if (!gray.isAllow("company_lyrical_enable", tenantId)) {
//            return false;
//        }
//        try {
//            if (Strings.isNullOrEmpty(companyName)) {
//                return false;
//            }
//            Map<String, String> pathParams = Maps.newHashMap();
//            pathParams.put("company_name", URLEncoder.encode(companyName, "UTF-8"));
//            CompanyLyricalModel.GetCompanyLyricalAllResult rstResult = companyLyricalProxy.getCompanyLyricalAll(pathParams);
//            if (rstResult != null && rstResult.getResult() != null) {
//                return rstResult.getResult() > 0;
//            }
//        } catch (Exception e) {
//            log.warn("getCompanyLyricalAll error param:{}", companyName, e);
//        }
//        return false;
    }

    public static void updateConfirmTime(String tenantId, String objectApiName, List<String> objectIds) {
        List<IObjectData> objectDataList = Lists.newArrayList();
        objectIds.forEach(objectId -> {
            IObjectData objectData = new ObjectData();
            objectData.setTenantId(tenantId);
            objectData.setDescribeApiName(objectApiName);
            objectData.setId(objectData.getId());
            objectData.set("confirm_time", System.currentTimeMillis());
            objectDataList.add(objectData);
        });
        List<String> toUpdateFields = Lists.newArrayList("confirm_time");
        User user = new User(tenantId, User.SUPPER_ADMIN_USER_ID, "", "");
        SERVICE_FACADE.batchUpdateByFields(user, objectDataList, toUpdateFields);
    }

    public static boolean isGrayFeatureEnable(String tenantId) {
        if (!gray.isAllow("account_feature_enable", tenantId)) {
            return false;
        }
        return true;
    }

    public static boolean isGrayLocationEnable(String tenantId) {
        if (!gray.isAllow("account_location_enable", tenantId)) {
            return false;
        }
        return true;
    }

    public static boolean isGrayDisableGeoCalculate(String tenantId) {
        if (!gray.isAllow("account_geo_calculate_disable", tenantId)) {
            return false;
        }
        return true;
    }

    public static boolean isGrayPoolGroupBy(String tenantId) {
        if (!gray.isAllow("pool_group_by_enable", tenantId)) {
            return false;
        }
        return true;
    }

    public static Boolean checkCustomerClaimTime(User user, String employeeId, String poolId, List<String> objectIds, Integer claimIntervalDays) {
        List<Map> resultMap = selectHighSeasClaimLogBySql(user, employeeId, poolId, objectIds, claimIntervalDays);
        return CollectionUtils.empty(resultMap);
    }

    private static List<Map> selectHighSeasClaimLogBySql(User user, String employeeId, String poolId, List<String> objectIds, Integer claimIntervalDays) {
        List<Map> resultMap = Lists.newArrayList();
        if (CollectionUtils.empty(objectIds)) {
            return resultMap;
        }
        List<WhereParam> whereParamsList = Lists.newArrayList();
        CommonSqlUtils.addWhereParam(whereParamsList, "tenant_id", CommonSqlOperator.EQ, Lists.newArrayList(user.getTenantId()));
        CommonSqlUtils.addWhereParam(whereParamsList, "api_name", CommonSqlOperator.EQ, Lists.newArrayList("AccountObj"));
        CommonSqlUtils.addWhereParam(whereParamsList, "employee_id", CommonSqlOperator.EQ, Lists.newArrayList(employeeId));
        CommonSqlUtils.addWhereParam(whereParamsList, "pool_id", CommonSqlOperator.EQ, Lists.newArrayList(poolId));
        CommonSqlUtils.addWhereParam(whereParamsList, "operation_time", CommonSqlOperator.GT, Lists.newArrayList(System.currentTimeMillis() - (claimIntervalDays * OneDayTimeStamp)));
        List<Map<String, Object>> tmpColumnMap = Lists.newArrayList();
        objectIds.forEach(m -> {
            Map<String, Object> columnMap = Maps.newHashMap();
            columnMap.put("object_id", m);
            tmpColumnMap.add(columnMap);
        });
        IActionContext actionContext = CommonSqlUtil.buildContext(user);
        try {
            resultMap = commonSqlService.selectJoinOther("biz_data_claim_log", tmpColumnMap, whereParamsList, actionContext);
        } catch (MetadataServiceException e) {
            log.error("selectHighSeasClaimLogBySql error ", e);
        }
        return resultMap;
    }

    public static List<IObjectData> getAllObjectPools(String tenantId, String userId, String apiName) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        User user = new User(tenantId, userId);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, "is_deleted", 0);
        searchTemplateQuery.setFilters(filters);
        searchTemplateQuery.setLimit(1000);
        searchTemplateQuery.setOrders(Lists.newArrayList(new OrderBy("create_time", true)));
        QueryResult<IObjectData> queryResult = SERVICE_FACADE.findBySearchQuery(user,
                apiName, searchTemplateQuery);
        return queryResult.getData();
    }

    /**
     * 只允许本公海/线索池成员转移客户到该公海/线索池
     *
     * @param poolData
     * @return
     */
    public static Boolean getAllowMemberMove(IObjectData poolData) {
        return getBooleanValue(poolData, "only_allow_member_move", false);
    }

    /**
     * 只允许本公海/线索池成员退回客户到该公海/线索池
     *
     * @param poolData
     * @return
     */
    public static Boolean getAllowMemberReturn(IObjectData poolData) {
        return getBooleanValue(poolData, "only_allow_member_return", false);
    }

    /**
     * 根据成员获取公海/线索池id
     *
     * @param tenantId
     * @param outTenantId 外部企业id
     * @param deptIds     成员所属部门id
     * @param userId      成员或者外部成员
     * @param poolApiName 公海/线索池APIName
     * @return
     */
    public static List<String> getPoolIdsByPermission(String tenantId, String outTenantId, List<String> deptIds,
                                                      String userId, String poolApiName) {
        String queryString = "SELECT pool_id FROM biz_pool_permission WHERE tenant_id='"
                + tenantId + "' and object_api_name='" + poolApiName + "' and is_deleted=0  ";

        queryString += "AND ((data_id='" + userId + "' AND type in ('" +
                ObjectPoolPermission.ObjectPoolMemberType.EMPLOYEE.getValue() + "','" +
                ObjectPoolPermission.ObjectPoolMemberType.OUTER_EMPLOYEE.getValue() + "')) ";
        if (!Strings.isNullOrEmpty(outTenantId)) {
            queryString += " OR (type='" + ObjectPoolPermission.ObjectPoolMemberType.OUTER_ENTERPRISE.getValue() + "' AND data_id='"
                    + outTenantId + "')) ";
        } else {
            if (CollectionUtils.empty(deptIds)) {
                queryString += ") ";
            } else {
                String idString;
                if (Utils.LEADS_POOL_API_NAME.equalsIgnoreCase(poolApiName)) {
                    idString = LeadsUtils.getListQuerySql(deptIds);
                } else {
                    idString = buildSqlInString(deptIds);
                }
                queryString += " OR (type='" + ObjectPoolPermission.ObjectPoolMemberType.CIRCLE.getValue() + "' AND data_id in ("
                        + idString + "))) ";
            }
        }
        List<Map> queryResult = null;
        try {
            queryResult = objectDataService.findBySql(tenantId, queryString);
        } catch (MetadataServiceException e) {
            log.error("account getPoolIdsByPermission error: {}", queryString, e);
            throw new SFABusinessException(SFAErrorCode.ACCOUNT_COMMON_ERROR);
        }
        if (CollectionUtils.empty(queryResult)) {
            return Lists.newArrayList();
        }
        final List<String> poolIds = queryResult.stream().map(x -> x.get("pool_id").toString()).distinct().collect(Collectors.toList());
        if (CollectionUtils.empty(poolIds)) {
            return Lists.newArrayList();
        }
        return poolIds;
    }

    /**
     * 过滤配置了 only_allow_member_move,only_allow_member_return 为true 并且 非此用户所在 的公海/线索池
     *
     * @param objectDataList 全部公海/线索池
     * @param poolIds        此用户所在的公海/线索池
     */
    public static void filterOnlyAllowMemberReturn(List<IObjectData> objectDataList, List<String> poolIds, String action) {
        if ("move".equals(action)) {
            objectDataList.removeIf(x -> !poolIds.contains(x.getId()) && AccountUtil.getAllowMemberMove(x));
        }
        if ("return".equals(action)) {
            objectDataList.removeIf(x -> !poolIds.contains(x.getId()) && AccountUtil.getAllowMemberReturn(x));
        }
    }

    /**
     * @param user
     * @param objectDataList
     * @param errorList
     */
    public static void checkOnlyAllowMemberMove(User user, List<BaseImportDataAction.ImportData> objectDataList,
                                                List<BaseImportAction.ImportError> errorList, String poolApiNName) {
        if (LeadsUtils.isCrmAdmin(user)) {
            return;
        }
        String apiName = "high_seas_id";
        String i18Ntext = I18N.text(SFA_HIGH_SEAS_ONLY_ALLOW_MOVE);
        if (Utils.LEADS_POOL_API_NAME.equals(poolApiNName)) {
            apiName = "leads_pool_id";
            i18Ntext = I18N.text(SFA_LEADS_POOL_ONLY_ALLOW_MOVE);
        }
        final String poolIdApiName = apiName;

        String userId = user.isOutUser() ? user.getOutUserId() : user.getUserId();
        String outTenantId = user.getOutTenantId();
        List<String> highSeasIds = objectDataList.stream()
                .filter(x -> StringUtils.isNotEmpty(AccountUtil.getStringValue(x.getData(), poolIdApiName, "")))
                .map(x -> AccountUtil.getStringValue(x.getData(), poolIdApiName, ""))
                .distinct().collect(Collectors.toList());
        if (CollectionUtils.empty(highSeasIds)) {
            return;
        }
        List<String> deptIds = getUserDepartIds(user.getTenantId(), userId);
        List<String> poolIds = getPoolIdsByPermission(user.getTenantId(), outTenantId, deptIds, userId, poolApiNName);

        List<String> notMemberIdList = new ArrayList<>();
        for (String highSeasId : highSeasIds) {
            if (!poolIds.contains(highSeasId)) {
                notMemberIdList.addAll(highSeasIds);
            }
        }

        List<String> errorIdList = new ArrayList<>();
        List<IObjectData> results = SERVICE_FACADE.findObjectDataByIds(user.getTenantId(), notMemberIdList, poolApiNName);
        for (IObjectData result : results) {
            if (getAllowMemberMove(result)) {
                errorIdList.add(result.getId());
            }
        }
        if (CollectionUtils.empty(errorIdList)) {
            return;
        }
        for (BaseImportDataAction.ImportData data : objectDataList) {
            String highSeasId = AccountUtil.getStringValue(data.getData(), poolIdApiName, null);
            if (errorIdList.contains(highSeasId)) {
                errorList.add(new BaseImportAction.ImportError(data.getRowNo(), i18Ntext));
            }
        }

    }

    /**
     * 补充手机归属地赋值
     */
    public static void getPhoneNumberInfo(ObjectDataDocument objectDataDocument, String fieldName) {
        if (!org.springframework.util.StringUtils.isEmpty(objectDataDocument.get(fieldName))) {
            String phoneNum = objectDataDocument.get(fieldName).toString();
            PhoneUtil.Result result = PhoneUtil.getPhoneNumberInfo(phoneNum);
            if (!Objects.isNull(result)) {
                objectDataDocument.put("phone_number_attribution_country", result.getCountry());
                objectDataDocument.put("phone_number_attribution_province", result.getProvince());
                objectDataDocument.put("phone_number_attribution_city", result.getCity());
            } else {
                objectDataDocument.put("phone_number_attribution_country", null);
                objectDataDocument.put("phone_number_attribution_province", null);
                objectDataDocument.put("phone_number_attribution_city", null);
            }
        } else if (objectDataDocument.containsKey(fieldName)) {
            objectDataDocument.put("phone_number_attribution_country", null);
            objectDataDocument.put("phone_number_attribution_province", null);
            objectDataDocument.put("phone_number_attribution_city", null);
        }
    }

    /**
     * 补充手机归属地赋值,审批流回调使用
     */
    public static void getPhoneNumberInfoForFlow(Map<String, Object> callbackData, String fieldName) {
        //判断电话是否修改
        if (CollectionUtils.empty(callbackData) || !callbackData.containsKey(fieldName)) {
            return;
        }
        if (!org.springframework.util.StringUtils.isEmpty(callbackData.get(fieldName))) {
            String phoneNum = callbackData.get(fieldName).toString();
            PhoneUtil.Result result = PhoneUtil.getPhoneNumberInfo(phoneNum);
            if (!Objects.isNull(result)) {
                callbackData.put("phone_number_attribution_country", result.getCountry());
                callbackData.put("phone_number_attribution_province", result.getProvince());
                callbackData.put("phone_number_attribution_city", result.getCity());
            } else {
                callbackData.put("phone_number_attribution_country", null);
                callbackData.put("phone_number_attribution_province", null);
                callbackData.put("phone_number_attribution_city", null);
            }
        } else {
            callbackData.put("phone_number_attribution_country", null);
            callbackData.put("phone_number_attribution_province", null);
            callbackData.put("phone_number_attribution_city", null);
        }
    }

    public static void PublishFeeds(User user, String content, Integer feedType, Integer source, List<FeedsModel.FeedRelatedCrmObject> crmObjects) {
        try {
            FeedsModel.PublishFeedArg arg = FeedsModel.PublishFeedArg.builder()
                    .content(content).currentEmployeeId(user.getUserIdInt())
                    .enterpriseId(user.getTenantIdInt()).feedType(feedType)
                    .source(source).crmObjects(crmObjects)
                    .enterpriseAccount(gdsHandler.getEAByEI(user.getTenantId()))
                    .build();
            feedsProxy.publishFeed(arg);
        } catch (Exception e) {
            log.error("PublishFeeds error", e);
            throw new SFABusinessException(SFAErrorCode.ACCOUNT_COMMON_ERROR);
        }
    }

    public static String getNameCode(){
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        String result = formatter.format(new Date(System.currentTimeMillis()));
        result = result.replace(":", "");
        result = result.replace(".", "");
        result = result.replace(" ", "-");
        Random random = new Random();
        int lastNumber = random.nextInt(100);
        DecimalFormat decimalFormat = new DecimalFormat("###");
        result = String.format("%s%s", result, decimalFormat.format(lastNumber));
        return result;
    }
}
