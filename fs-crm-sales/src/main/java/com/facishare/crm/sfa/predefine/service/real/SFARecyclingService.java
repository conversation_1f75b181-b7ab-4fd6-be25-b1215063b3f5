package com.facishare.crm.sfa.predefine.service.real;

import com.facishare.crm.sfa.utilities.proxy.model.SFARecyclingProxyModel;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;
import java.util.Map;

public interface SFARecyclingService {

    Map<String, Map> getRecyclingRule(SFARecyclingProxyModel.Arg arg);

    void getRecyclingRule(String apiName, List<IObjectData> objectDataList);
}
