package com.facishare.crm.sfa.predefine.controller;


import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.util.SoRelatedListUtils;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardDetailListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.facishare.crm.sfa.utilities.util.ButtonUtils.removeButtons;

/**
 * <AUTHOR>
 * @date 2019-08-26 15:49
 * @instruction
 */
public class SalesOrderProductDetailListController extends StandardDetailListController {


    @Override
    protected QueryResult<IObjectData> getQueryResult(SearchTemplateQuery query) {
        QueryResult<IObjectData> queryResult = super.getQueryResult(query);
        handleProductInfo(queryResult.getData());
        return queryResult;
    }


    private void handleProductInfo(List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }

        List<String> productIds = Lists.newArrayList();
        Map<String, String> idMap = new HashMap<>();

        for (IObjectData objectData : objectDataList) {
            String productId = objectData.get("product_id", String.class);
            if (!productIds.contains(productId)) {
                productIds.add(productId);
            }
            idMap.put(objectData.getId(), productId);
            objectData.set("product_name", objectData.get("product_id__r"));
        }

        List<IObjectData> products = serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), productIds, Utils.PRODUCT_API_NAME);

        if (CollectionUtils.empty(products)) {
            return;
        }

        for (IObjectData objectData : objectDataList) {
            Map<String, Object> map = new HashMap<>();
            String productId = objectData.get("product_id", String.class);
            Optional<IObjectData> product = products.stream().filter(p -> p.getId().equals(productId)).findFirst();
            if (product.isPresent()) {
                map.put("name", product.get().getName());
                map.put("product_status", product.get().get("product_status"));
                map.put("price", product.get().get("price"));
                map.put("unit", objectData.get("unit"));

                objectData.set("product_id__ro", map);
            }
        }
    }

    /**
     * 删除所有业务类型下的按钮
     */
    @Override
    protected ILayout findLayout() {
        return removeButtons(super.findLayout());
    }

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        return SoRelatedListUtils.removeButtons(result, SoRelatedListUtils.xxProductFilterButtonInfo);
    }
}
