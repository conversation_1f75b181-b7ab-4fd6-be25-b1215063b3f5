package com.facishare.crm.sfa.predefine.service.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 修改客户路径 class
 *
 * <AUTHOR>
 * @date 2020/4/24
 */
public interface ChangeAccountPathMessage {
    @Data
    @Builder
    class Arg {
        @JSONField(name = "object_ids")
        @JsonProperty("object_ids")
        List<String> objectIds;
        @JSONField(name = "tenant_id")
        @JsonProperty("tenant_id")
        private String tenantId;
    }

}
