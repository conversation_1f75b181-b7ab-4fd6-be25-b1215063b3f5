package com.facishare.crm.sfa.predefine.service.real;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.proxy.SFARecyclingProxy;
import com.facishare.crm.sfa.utilities.proxy.model.SFARecyclingProxyModel;
import com.facishare.crm.sfa.utilities.util.CommonUtil;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.common.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-04-26 18:22
 */
@Slf4j
@Service
public class SFARecyclingServiceImpl implements SFARecyclingService {


    @Autowired
    private SFARecyclingProxy sfaRecyclingProxy;


    @Autowired
    private IObjectDataService objectDataPgService;

    private static final long dayTime = 86400000L;
    private static final long hourTime = 3600000L;


    @Override
    public Map<String, Map> getRecyclingRule(SFARecyclingProxyModel.Arg arg) {

        Map<String, String> headers = Maps.newHashMap();
        headers.put("Content-Type", "application/json");
        headers.put("X-fs-Enterprise-Id", arg.getTenantId());

        SFARecyclingProxyModel.Result recyclingRule = null;
        try {
            recyclingRule = sfaRecyclingProxy.getRecyclingRule(arg, headers);
        } catch (Exception e) {
            log.warn("getRecyclingRule error:{}", e.getMessage());
        }
        if (recyclingRule == null) {
            return null;
        }
        Map<String, String> value = recyclingRule.getValue();

        if (CollectionUtils.isEmpty(value.values())) {
            return null;
        }
        String sql = "select recycling_rule_id,remind_days from biz_recycling_remind_rule where" +
                String.format(" tenant_id = '%s' and is_deleted=0 ", arg.getTenantId()) +
                String.format(" and recycling_rule_id in ('%s')", String.join("','", value.values()));

        List<Map> recyclingRemindRule = Lists.newArrayList();
        try {
            recyclingRemindRule = objectDataPgService.findBySql(arg.getTenantId(), sql);
        } catch (MetadataServiceException e) {

        }

        Map<String, Map> map = new HashMap<>();


        for (Map.Entry<String, String> s : value.entrySet()) {
            for (Map map1 : recyclingRemindRule) {
                if (StringUtils.isNotBlank(s.getValue())) {
                    if (s.getValue().equals(map1.get("recycling_rule_id"))) {
                        map.put(s.getKey(), map1);
                        break;
                    }
                } else {
                    break;
                }
            }
        }
        return map;
    }


    @Override
    public void getRecyclingRule(String apiName, List<IObjectData> objectDataList) {

        List<String> accountIdList = Lists.newArrayList();

        calculateLocalRemindDasy(objectDataList, accountIdList);
//       线索不需要调接口计算是否回收
        if (Utils.LEADS_API_NAME.equals(apiName)) {
            return;
        }

        if (CollectionUtils.isEmpty(accountIdList)) {
            return;
        }

        SFARecyclingProxyModel.Arg arg = SFARecyclingProxyModel.Arg.builder().apiName(apiName)
                .tenantId(objectDataList.get(0).getTenantId()).objectIds(accountIdList).build();

        Map<String, Map> recyclingRule = null;
        try {
            recyclingRule = getRecyclingRule(arg);
        } catch (Exception e) {
            log.warn("recyclingRule service is not available");
            return;
        }
        if (recyclingRule == null) {
            return;
        }

        boolean isRemindRecycling = true;
        for (IObjectData objectData : objectDataList) {
            if (recyclingRule.get(objectData.getId()) == null) {
                continue;
            }
            if (recyclingRule.get(objectData.getId()).get("remind_days") == null) {
                continue;
            }
            if (objectData.get("expire_time") == null || StringUtils.isEmpty(objectData.get("expire_time").toString())) {
                isRemindRecycling = false;
            } else {
                long millis = (long) objectData.get("expire_time") - System.currentTimeMillis();
                long remindDays = Long.valueOf(recyclingRule.get(objectData.getId()).get("remind_days").toString()).longValue() * dayTime;
                if (millis < remindDays) {
                    isRemindRecycling = true;
                } else {
                    isRemindRecycling = false;
                }
            }
            objectData.set("is_remind_recycling", isRemindRecycling);
        }
    }

    private void calculateLocalRemindDasy(List<IObjectData> objectDataList, List<String> accountIdList) {
        boolean isRemindRecycling = false;
        for (IObjectData objectData : objectDataList) {
            isRemindRecycling = false;
            if (!Strings.isNullOrEmpty(objectData.getId())) {
                if (objectData.get("expire_time") == null || StringUtils.isBlank(objectData.get("expire_time", String.class))) {
                    continue;
                }
                if (objectData.get("remind_days") == null) {
                    accountIdList.add(objectData.getId());
                } else {
                    Long remindDays;
                    if (Utils.LEADS_API_NAME.equals(objectData.getDescribeApiName())) {
                        remindDays = objectData.get("remind_days", Long.class) * hourTime;
                    } else {
                        remindDays = objectData.get("remind_days", Long.class) * dayTime;
                    }

                    long millis = (long) objectData.get("expire_time") - System.currentTimeMillis();
                    if (millis < remindDays) {
                        isRemindRecycling = true;
                    } else {
                        isRemindRecycling = false;
                    }
                }
            }
            objectData.set("is_remind_recycling", isRemindRecycling);
        }
    }
}
