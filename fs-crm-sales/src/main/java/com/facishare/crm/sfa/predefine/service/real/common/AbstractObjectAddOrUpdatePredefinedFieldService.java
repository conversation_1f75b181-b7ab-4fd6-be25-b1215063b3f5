package com.facishare.crm.sfa.predefine.service.real.common;


import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.model.ServiceResult;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

@Slf4j
public abstract class AbstractObjectAddOrUpdatePredefinedFieldService {
    @Autowired
    IObjectDescribeService objectDescribeService;


    public abstract String getObjectApiName();

    public abstract String getFieldApiName();

    public abstract String getFieldDescribeJson();

    private IFieldDescribe getFieldDescribe() {
        Map describeMap = JSON.parseObject(getFieldDescribeJson(), Map.class);
        IFieldDescribe describe = FieldDescribeExt.of(describeMap).getFieldDescribe();
        return describe;
    }


    public ServiceResult updateOrInsertField(User user) {
        try {
            IObjectDescribe objectDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(user.getTenantId(),
                    getObjectApiName());
            if (objectDescribe != null) {
                objectDescribe.removeFieldDescribe(getFieldApiName());
                objectDescribe.addFieldDescribe(getFieldDescribe());
                objectDescribeService.update(objectDescribe);
            }
//            objectDescribeService.updateOrInsertFieldsForOnline(user.getTenantId(), getObjectApiName(), Lists.newArrayList(getFieldDescribe()));
            return ServiceResult.ofSuccess();
        } catch (MetadataServiceException e) {
            log.warn("updateOrInsertField faild. user {},objectApiName {},fieldApiName {}", user, getObjectApiName(), getFieldApiName(), e);
            return ServiceResult.ofFailed(e.getMessage());
        }
    }
}
