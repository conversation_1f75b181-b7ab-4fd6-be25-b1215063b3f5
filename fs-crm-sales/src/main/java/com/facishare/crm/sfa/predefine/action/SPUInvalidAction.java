package com.facishare.crm.sfa.predefine.action;

import com.google.common.collect.Lists;

import com.facishare.crm.sfa.predefine.service.real.SpuSkuService;
import com.facishare.crm.sfa.predefine.service.real.SpuSkuServiceImpl;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.util.SpringUtil;

/**
 * Created by luxin on 2018/12/3.
 */
public class SPUInvalidAction extends StandardInvalidAction {

    private SpuSkuService spuSkuService = SpringUtil.getContext().getBean(SpuSkuServiceImpl.class);

    @Override
    protected Result doAct(Arg arg) {

        Boolean existInvalidAnySkuUnderSpu = spuSkuService.isExistSkuUnderAnySpuByIsDeleteStatusNotWithDataPermiss(actionContext.getUser(), Lists.newArrayList(arg.getObjectDataId()), Lists.newArrayList("0"));

        if (existInvalidAnySkuUnderSpu) {
            // TODO 18NKey
            throw new ValidateException(I18N.text("so.spu.invalid.check"));
        }
        return super.doAct(arg);
    }
}
