package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.service.UnitService;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.appframework.metadata.ProductCategoryService;
import com.facishare.paas.appframework.metadata.ProductCategoryServiceImpl;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

public class PriceBookProductListController extends StandardListController {
    private ProductCategoryService productService = SpringUtil.getContext().getBean(ProductCategoryServiceImpl.class);
    private final UnitService unitService = SpringUtil.getContext().getBean(UnitService.class);

    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery searchTemplateQuery = super.buildSearchTemplateQuery();
        // 处理query里面的filters
        productService.handleCategoryFilters(controllerContext.getTenantId(), controllerContext.getUser().getUserId(), searchTemplateQuery.getFilters());

        // 处理wheres里面的分类filters
        searchTemplateQuery.getWheres().stream()
                .filter(k -> CollectionUtils.isNotEmpty(k.getFilters()))
                .forEach(k -> productService.handleCategoryFilters(controllerContext.getTenantId(), controllerContext.getUser().getUserId(), k.getFilters()));
        return searchTemplateQuery;
    }

    @Override
    protected List<ILayout> findMobileLayouts() {
        List<ILayout> mobileLayouts = super.findMobileLayouts();
        for (ILayout layout : mobileLayouts) {
            layout.set("buttons", Lists.newArrayList());
        }
        return mobileLayouts;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        unitService.fillMultipleUnit(controllerContext.getUser(), result.getDataList());
        return after;
    }
}
