package com.facishare.crm.sfa.predefine.action.model;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.core.predef.action.BaseImportAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.github.autoconf.ConfigFactory;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/1/5 11:26
 * @instruction
 */
@Data
public class ImportDataSpuAndSku {

    private Integer spuRowNo;
    private IObjectData spuData;
    private List<IObjectData> skuDatas;
    private Map<String, List<String>> spuAndSpecMaps;
    private List<BaseImportAction.ImportError> imporError;
    private IObjectDescribe spuDescribe;
    private IObjectDescribe producetDescribe;

}
