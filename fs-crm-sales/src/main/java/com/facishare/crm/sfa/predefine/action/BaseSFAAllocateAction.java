package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.openapi.Utils;
import com.facishare.crmcommon.rest.dto.PrmEnterpriseModel;
import com.facishare.crm.sfa.model.SFAObjectPoolCommon;
import com.facishare.crm.sfa.predefine.service.model.ObjectPoolPermission;
import com.facishare.crmcommon.util.PRMRestResult;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.common.util.AppIdMapping;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.flow.ApprovalFlowStartResult;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.prm.util.PrmConstant;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_ONLY_ALLOCATE_POOL_MEMBER;
import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_TRADEPRODUCTBUSINESS_2077_1;

/**
 * Created by yuanjl on 2018/8/8.
 */
public abstract class BaseSFAAllocateAction extends BaseObjectPoolAction<BaseSFAAllocateAction.Arg, SFAObjectPoolCommon.Result> {
    protected List<IObjectData> objectDataList;
    protected ObjectPoolPermission.ObjectPoolMemberType objectPoolMemberType;

    @Override
    protected List<String> getDataPrivilegeIds(BaseSFAAllocateAction.Arg arg) {
        return arg.getObjectIDs();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList("Allocate");
    }

    @Override
    protected ObjectAction getObjectAction() {
        return ObjectAction.ALLOCATE;
    }

    @Override
    protected String getObjectPoolId(BaseSFAAllocateAction.Arg arg) {
        return arg.getObjectPoolId();
    }

    @Override
    protected String getUserId(BaseSFAAllocateAction.Arg arg) {
        return arg.getOwnerId();
    }

    @Override
    protected void before(BaseSFAAllocateAction.Arg arg) {
        dealOwnerId(arg);
        super.before(arg);
        objectDataList = this.serviceFacade.findObjectDataByIdsIncludeDeleted(actionContext.getUser(), arg.getObjectIDs(), actionContext.getObjectApiName());
        if (objectDataList.stream().anyMatch(x -> "-1".equals(x.get("is_deleted")))) {
            throw new ValidateException(I18N.text(I18NKey.action_invalid));
        }
        if (!objectPoolPermissions.equals(ObjectPoolPermission.ObjectPoolPermissions.POOL_MEMBER)
                && !objectPoolPermissions.equals(ObjectPoolPermission.ObjectPoolPermissions.POOL_ALL)) {
            throw new ValidateException(I18N.text(SFA_ONLY_ALLOCATE_POOL_MEMBER));
        }
        if (arg.getOwnerId().isEmpty() || Integer.parseInt(arg.getOwnerId()) < 0) {
            throw new ValidateException(I18N.text(SFA_TRADEPRODUCTBUSINESS_2077_1));
        }
    }

    @Override
    protected SFAObjectPoolCommon.Result doAct(BaseSFAAllocateAction.Arg arg) {
        if (needTriggerApprovalFlow()) {
            if (dataList.size() > 1) {
                this.startApprovalFlowAsynchronous(this.dataList, ApprovalFlowTriggerType.CHANGE_OWNER, approvalFlowTriggerMap(), approvalFlowTriggerMap());
                return SFAObjectPoolCommon.Result.builder().build();
            }

            Map<String, ApprovalFlowStartResult> resultMap = this.startApprovalFlow(this.dataList, ApprovalFlowTriggerType.CHANGE_OWNER.getId(), approvalFlowTriggerMap(), approvalFlowTriggerMap());
            if (!ApprovalFlowStartResult.getNeedTriggerChangeOwnerAfterActionEnum().contains(resultMap.getOrDefault((this.dataList.get(0)).getId(), ApprovalFlowStartResult.APPROVAL_NOT_EXIST))) {
                return SFAObjectPoolCommon.Result.builder().build();
            }
        }
        SFAObjectPoolCommon.Result result = objectPoolService.allocate(actionContext.getObjectApiName(),
                actionContext.getUser(), arg.getObjectPoolId(), arg.getObjectIDs(), arg.getOwnerId(),
                actionContext.getEventId(), arg.getOuterTenantId(), arg.getOuterOwnerId(), arg.getPartnerId());
        return result;
    }

    @Override
    protected SFAObjectPoolCommon.Result after(BaseSFAAllocateAction.Arg arg, SFAObjectPoolCommon.Result result) {
        super.after(arg, result);
        addLog();
        return result;
    }

    protected void addLog() {
        logAsync(dataList, EventType.MODIFY, ActionType.ALLOCATE);
    }

    @Override
    protected boolean needTriggerApprovalFlow() {
        return false;
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.ALLOCATE.getButtonApiName();
    }

    @Override
    protected IObjectData getPreObjectData() {
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIdsIncludeDeleted(actionContext.getUser(), arg.getObjectIDs(), actionContext.getObjectApiName());
        if (objectDataList != null && objectDataList.size() != 0) {
            return objectDataList.get(0);
        }
        return null;
    }

    @Override
    protected IObjectData getPostObjectData() {
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIdsIncludeDeleted(actionContext.getUser(), arg.getObjectIDs(), actionContext.getObjectApiName());
        if (objectDataList != null && objectDataList.size() != 0) {
            return objectDataList.get(0);
        }
        return null;
    }

    protected String getOwnerName() {
        if (objectPoolMemberType == ObjectPoolPermission.ObjectPoolMemberType.OUTER_ENTERPRISE) {
            PrmEnterpriseModel.GetAllDownstreamEnterprisesArg getAllDownstreamEnterprisesArg = new PrmEnterpriseModel.GetAllDownstreamEnterprisesArg();
            String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(actionContext.getTenantId()));
            getAllDownstreamEnterprisesArg.setUpstreamEa(ea);
            PrmEnterpriseModel.GetAllDownstreamEnterprisesResult downstreamEnterprises = initObjectsPermissionsAndLayoutProxy
                    .getAllDownstreamEnterprises(getHeader(), getAllDownstreamEnterprisesArg);
            if (downstreamEnterprises != null && CollectionUtils.isNotEmpty(downstreamEnterprises.getData())) {
                Optional<PrmEnterpriseModel.OuterTenantSimpleInfo> outerEnterprise = downstreamEnterprises.getData()
                        .stream().filter(enterprise -> enterprise.getOuterTenantId().equals(arg.getOuterTenantId()))
                        .findFirst();
                if (outerEnterprise.isPresent()) {
                    return outerEnterprise.get().getEnterpriseName();
                }
            }
            return "--";
        } else if (objectPoolMemberType == ObjectPoolPermission.ObjectPoolMemberType.OUTER_EMPLOYEE) {
            PrmEnterpriseModel.GetDownStreamUsersArg getDownStreamUsersArg = new PrmEnterpriseModel.GetDownStreamUsersArg();
            String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(actionContext.getTenantId()));
            getDownStreamUsersArg.setUpstreamEa(ea);
            getDownStreamUsersArg.setLinkAppId(AppIdMapping.appIdMapping.get(PrmConstant.PRM_APP_ID));
            getDownStreamUsersArg.setDownstreamOuterTenantIds(Lists.newArrayList(arg.getOuterTenantId()));
            PrmEnterpriseModel.DownstreamEmployeeResult downstreamUsers = initObjectsPermissionsAndLayoutProxy
                    .getDownstreamEmployee(getHeader(), getDownStreamUsersArg);
            if (downstreamUsers != null && CollectionUtils.isNotEmpty(downstreamUsers.getData())) {
                if (CollectionUtils.isNotEmpty(downstreamUsers.getData().get(0).getEmployeeVos())) {
                    Optional<PrmEnterpriseModel.DownstreamUser> downstreamUser = downstreamUsers.getData().get(0)
                            .getEmployeeVos().stream().filter(user -> user.getOuterUid().equals(arg.getOuterOwnerId()))
                            .findFirst();
                    if (downstreamUser.isPresent()) {
                        return downstreamUser.get().getName();
                    }
                }
            }
            return "--";
        } else {
            User user = serviceFacade.getUser(actionContext.getTenantId(), arg.getOwnerId());
            return user != null ? user.getUserName() : "--";
        }
    }

    private void dealOwnerId(BaseSFAAllocateAction.Arg arg) {
        if (isPrmOpen(actionContext.getTenantId())) {
            objectPoolMemberType = objectPoolService.getPoolMemberType(actionContext.getObjectApiName(),
                    actionContext.getTenantId(), arg.getOwnerId(), arg.getObjectPoolId());
            String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(actionContext.getTenantId()));
            if (objectPoolMemberType == null) {
                return;
            } else if (objectPoolMemberType == ObjectPoolPermission.ObjectPoolMemberType.OUTER_EMPLOYEE) {
                arg.setOuterOwnerId(Long.parseLong(arg.getOwnerId()));
                PrmEnterpriseModel.GetAllDownStreamUsersArg getAllDownStreamUsersArg = new PrmEnterpriseModel.GetAllDownStreamUsersArg();
                getAllDownStreamUsersArg.setUpstreamEa(ea);
                getAllDownStreamUsersArg.setLinkAppId(AppIdMapping.appIdMapping.get(PrmConstant.PRM_APP_ID));
                PrmEnterpriseModel.AllDownstreamEmployeeResult downstreamEmployees = initObjectsPermissionsAndLayoutProxy
                        .getAllDownstreamEmployee(getHeader(), getAllDownStreamUsersArg);
                if (downstreamEmployees != null && CollectionUtils.isNotEmpty(downstreamEmployees.getData())) {
                    Optional<PrmEnterpriseModel.DownstreamLinkEmployee> outerEmployee = downstreamEmployees.getData().stream()
                            .filter(employee -> employee.getDownstreamOuterUid()
                                    .equals(Long.parseLong(arg.getOwnerId())))
                            .findFirst();
                    if (outerEmployee.isPresent()) {
                        arg.setOuterTenantId(outerEmployee.get().getDownstreamOuterTenantId());
                    }
                }
            } else if (objectPoolMemberType == ObjectPoolPermission.ObjectPoolMemberType.OUTER_ENTERPRISE) {
                arg.setOuterTenantId(Long.parseLong(arg.getOwnerId()));
            }
            if (arg.getOuterTenantId() != null && arg.getOuterTenantId() > 0) {
                PrmEnterpriseModel.GetMapperObjectIdArg getMapperObjectIdArg = new PrmEnterpriseModel.GetMapperObjectIdArg();
                getMapperObjectIdArg.setUpstreamEa(ea);
                getMapperObjectIdArg.setDownstreamOuterTenantId(arg.getOuterTenantId());
                getMapperObjectIdArg.setObjectApiName(Utils.PARTNER_API_NAME);
                PRMRestResult<String> restResult = initObjectsPermissionsAndLayoutProxy.getMapperObjectId(getHeader(), getMapperObjectIdArg);
                if (restResult != null && !Strings.isNullOrEmpty(restResult.getData())) {
                    arg.setPartnerId(restResult.getData());
                    IObjectData dbObjectData = serviceFacade.findObjectData(
                            new User(actionContext.getTenantId(), User.SUPPER_ADMIN_USER_ID), restResult.getData(),
                            Utils.PARTNER_API_NAME);
                    if (dbObjectData != null && CollectionUtils.isNotEmpty(dbObjectData.getOwner())) {
                        arg.setOwnerId(dbObjectData.getOwner().get(0));
                    }
                }
                if (arg.getOuterOwnerId() == null || arg.getOuterOwnerId() == 0) {
                    PrmEnterpriseModel.GetOutTenantIDArg getOutTenantIDArg = new PrmEnterpriseModel.GetOutTenantIDArg();
                    getOutTenantIDArg.setEa(ea);
                    PrmEnterpriseModel.GetOuterTenantIdByEaResult upstreamOuterTenantId = initObjectsPermissionsAndLayoutProxy
                            .getOuterTenantIdByEa(getHeader(), getOutTenantIDArg);
                    if (upstreamOuterTenantId != null) {
                        PrmEnterpriseModel.GetOutUidArg getOutUidArg = new PrmEnterpriseModel.GetOutUidArg();
                        getOutUidArg.setUpstreamOuterTenantId(upstreamOuterTenantId.getData());
                        getOutUidArg.setDownstreamOuterTenantId(arg.getOuterTenantId());
                        PrmEnterpriseModel.GetMainOuterOwnerResult userIdResult = initObjectsPermissionsAndLayoutProxy.getDownstreamRelationOwnerOuterUid(
                                getHeader(), getOutUidArg);
                        if (userIdResult != null && !Strings.isNullOrEmpty(userIdResult.getData())) {
                            arg.setOuterOwnerId(Long.valueOf(userIdResult.getData()));
                        }
                    }
                }
                objectPoolPermissions = ObjectPoolPermission.ObjectPoolPermissions.POOL_MEMBER;
            }
        } else {
            arg.setOuterTenantId(null);
            arg.setOuterOwnerId(null);
            arg.setPartnerId(null);
        }
    }

    private Map<String, String> getHeader() {
        Map<String, String> header = Maps.newHashMap();
        header.put("Content-Type", "application/json");
        header.put("x-eip-appid", "x_app_framework");
        return header;
    }

    @Data
    @NoArgsConstructor
    public static class Arg extends SFAObjectPoolCommon.Arg {
        private String ownerId;
        /**
         * 1. 新增 2. 退回 3.转移
         */
        private Integer allocateOperationSource;
        /**
         * 是否自动分配
         */
        private Integer isAutoAllocate;

        private Long outerTenantId;
        private Long outerOwnerId;
        private String partnerId;
    }
}