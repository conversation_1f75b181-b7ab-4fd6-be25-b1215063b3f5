package com.facishare.crm.sfa.utilities.proxy.model;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface GetPromotionProductsByAccountIdModel {
    @Data
    @Builder
    class Arg {
        private String customerId;
    }
    @Data
    class Result{
        String errCode;
        String errMessage;
        ResultData result;
    }

    @Data
    class ResultData{
        private List<WrapProductPromotion> promotions;
    }

    @Data
    class WrapProductPromotion {
        private String productId;
        private List<PromotionModel.PromotionEntity> promotions;
    }
}
