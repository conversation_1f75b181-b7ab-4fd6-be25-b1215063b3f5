package com.facishare.crm.sfa.predefine.bizvalidator.validator;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.constant.QuoteConstants;
import com.facishare.crm.sfa.utilities.constant.SalesOrderConstants;

import java.util.HashMap;
import java.util.Map;

/**
 * 业务校验器
 *
 * <AUTHOR>
 */
public class BaseValidator {
    protected static final Map<String, String> MASTER_DETAIL_API_NAME = new HashMap<String, String>() {
        {
            put(Utils.SALES_ORDER_API_NAME, Utils.SALES_ORDER_PRODUCT_API_NAME);
            put(Utils.QUOTE_API_NAME, Utils.QUOTE_LINES_API_NAME);
            put(Utils.NEW_OPPORTUNITY_API_NAME, Utils.NEW_OPPORTUNITY_LINES_API_NAME);
        }
    };

    protected static final Map<String, String> DETAIL_MASTER_API_NAME = new HashMap<String, String>() {
        {
            put(Utils.SALES_ORDER_PRODUCT_API_NAME, Utils.SALES_ORDER_API_NAME);
            put(Utils.QUOTE_LINES_API_NAME, Utils.QUOTE_API_NAME);
            put(Utils.NEW_OPPORTUNITY_LINES_API_NAME, Utils.NEW_OPPORTUNITY_API_NAME);
        }
    };

    protected static final Map<String, String> DETAIL_MASTER_ID = new HashMap<String, String>() {
        {
            put(Utils.QUOTE_LINES_API_NAME, QuoteConstants.QuoteField.QUOTEID.getApiName());
            put(Utils.NEW_OPPORTUNITY_LINES_API_NAME, QuoteConstants.QuoteField.NEWOPPORTUNITYID.getApiName());
        }
    };
}
