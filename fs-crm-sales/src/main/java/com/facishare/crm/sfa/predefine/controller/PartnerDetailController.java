package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardDetailController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;

import java.util.List;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class PartnerDetailController extends StandardDetailController {

    @Override
    protected ILayout getLayout() {
        ILayout layout = super.getLayout();
        if (layout == null) {
            return null;
        }
        removePartnerRelatedBtn(layout);
        return layout;
    }

    /**
     * 如果是预制联系人页签，则移除客户名称字段
     */
    private void removePartnerRelatedBtn(ILayout layout) {
        LayoutExt layoutExt = LayoutExt.of(layout);
        layoutExt.getRelatedComponent().ifPresent(relatedComponent -> {
            try {
                List<IComponent> childComponents = relatedComponent.getChildComponents();
                childComponents.stream().forEach(childComponent -> {
                    List<IButton> btnList = childComponent.getButtons();
                    //如果是预制联系人页签，则移除 关联解除关联 客户名称字段
                    if ("partner_contact_list".equals(childComponent.get("related_list_name", String.class))) {
                        List<Map> includeFields = childComponent.get("include_fields", List.class);
                        includeFields.removeIf(k -> "account_id".equals(k.get("field_name")));
                        childComponent.set("include_fields", includeFields);
                    }
                    //只有客户，商机，销售线索保留保留关联，解除关联功能
                    String relatedName = childComponent.get("related_list_name", String.class);
                    if (!"partner_leads_list".equals(relatedName) && !"partner_account_list".equals(relatedName) && !"partner_opportunity_list".equals(relatedName)) {
                        btnList.removeIf(btn -> "BulkRelate".equals(btn.getAction()) || "BulkDisRelate".equals(btn.getAction()));
                    }
                    childComponent.setButtons(btnList);
                });
                childComponents.removeIf(iComponent -> "ActiveRecordObj_partner_id_related_list".equals(iComponent.getName()));
                relatedComponent.setChildComponents(childComponents);
            } catch (MetadataServiceException e) {
                log.warn("PartnerDetailController getChildComponents error", e);
            }
        });
    }


    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        if (newResult != null && newResult.getData() != null) {
            IObjectData objectData = newResult.getData().toObjectData();
            boolean isShowCompanyLyricalAll = AccountUtil.isShowCompanyLyricalAll(controllerContext.getTenantId(), objectData.getName());
            objectData.set("isShowCompanyLyricalAll", isShowCompanyLyricalAll);
            newResult.setData(ObjectDataDocument.of(objectData));
        }
        return newResult;
    }
}
