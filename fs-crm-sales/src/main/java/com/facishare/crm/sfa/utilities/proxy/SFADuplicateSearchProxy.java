package com.facishare.crm.sfa.utilities.proxy;

import com.facishare.crm.sfa.utilities.proxy.model.DuplicateSearchData;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/02/15.
 */
@RestResource(value = "CRM_SFA", desc = "CRM Rest API Call", contentType = "application/json")
public interface SFADuplicateSearchProxy {
    @POST(value = "/crm/duplicatesearch/query", desc = "查重")
    DuplicateSearchData.Result getDuplicateSearchData(@HeaderMap Map<String, String> headers, @Body DuplicateSearchData.Arg arg);
}