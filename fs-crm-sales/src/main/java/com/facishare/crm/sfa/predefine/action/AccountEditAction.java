package com.facishare.crm.sfa.predefine.action;

import com.facishare.crmcommon.constants.SystemConstants;
import com.facishare.crm.sfa.predefine.service.AccountPathSynchronizer;
import com.facishare.crm.sfa.utilities.constant.AccountConstants;
import com.facishare.crm.sfa.utilities.util.*;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppIdMapping;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.appframework.prm.util.PrmConstant;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.Strings;

import java.util.List;
import java.util.Objects;
import java.util.Set;

import static com.facishare.crm.common.exception.CRMErrorCode.FS_CRM_DEFOBJ_CHECKED_CAN_NOT_LOOKUP_MYSELF;
import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_CANNOT_CHANGE_RECORDTYPE;
import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_REACH_LIMIT_OBJ;

/**
 * 客户编辑操作
 * <p>
 * Created by liyiguang on 2017/7/13.
 */
@Slf4j
public class AccountEditAction extends StandardEditAction {
    private static List<String> editExceptFields = Lists.newArrayList(SystemConstants.Field.Owner.apiName
            , AccountConstants.Field.EXPIRE_TIME, AccountConstants.Field.LAST_FOLLOWED_TIME
            , AccountConstants.Field.OWNER_MODIFIED_TIME, AccountConstants.Field.REMIND_DAYS, "leads_id"
            , AccountConstants.Field.DEAL_STATUS, AccountConstants.Field.LAST_DEAL_TIME, AccountConstants.Field.FIELD_ACCOUNT_PATH);
    private Boolean isNeedChangeAccountPath = false;

    @Override
    protected void before(Arg arg) {
        log.info("AccountEditAction>before()arg=" + JsonUtil.toJsonWithNullValues(arg));
        super.before(arg);
        //获取手机归属地字段
        AccountUtil.getPhoneNumberInfo(arg.getObjectData(), AccountConstants.Field.TEL);
        ObjectDataDocument dataDocument = arg.getObjectData();
//        String accountNo = AccountUtil.getStringValue(dataDocument, "account_no", "");
//        if (StringUtils.isNotEmpty(accountNo)) {
//            AccountUtil.checkAccountNo(actionContext.getTenantId(), accountNo, dataDocument.getId());
//        }
        String name = AccountUtil.getStringValue(dataDocument, "name", "");
        String pinyinString = Chinese2PinyinUtils.getPinyinString(name);
        dataDocument.put("pin_yin", pinyinString);

        Object objectValue = dataDocument.get(AccountConstants.Field.RECORD_TYPE);
        if (objectValue != null) {
            if (!objectData.getRecordType().equals(objectValue.toString())) {
                throw new ValidateException(I18N.text(SFA_CANNOT_CHANGE_RECORDTYPE));
            }
        }

        if (dataDocument.containsKey(AccountConstants.Field.FIELD_PARENT_ACCOUNT_ID)) {
            String parentAccountId = dataDocument.toObjectData().get(AccountConstants.Field.FIELD_PARENT_ACCOUNT_ID, String.class);
            String oldParentAccountId = CollectionUtils.empty(dataList) ? "" : dataList.get(0).get(AccountConstants.Field.FIELD_PARENT_ACCOUNT_ID, String.class);
            if (Objects.equals(parentAccountId, dataDocument.toObjectData().getId())) {
                throw new ValidateException(FS_CRM_DEFOBJ_CHECKED_CAN_NOT_LOOKUP_MYSELF.getMessage());
            }
            if (!Objects.equals(parentAccountId, oldParentAccountId)) {
                if (!Strings.isNullOrEmpty(parentAccountId)) {
                    AccountPathUtil.checkIsHoop(actionContext.getUser(), Lists.newArrayList(dataDocument.toObjectData()));
                }
                isNeedChangeAccountPath = true;
            }
        }

        int completed_field_quantity = AccountUtil.calculateObjectHasValueCount(objectDescribe, dataDocument.toObjectData());
        dataDocument.put("completed_field_quantity", completed_field_quantity);
        if (ObjectLimitUtil.isGrayAccountLimit(actionContext.getTenantId())) {
            List<IObjectData> oldDataList = ObjectDataExt.copyList(dataList);
            List<IObjectData> checkLimitDataList = ObjectDataExt.copyList(Lists.newArrayList(dataDocument.toObjectData()));
            checkLimitDataList.forEach(x -> {
                x.set(SystemConstants.Field.LifeStatus.apiName, ObjectLifeStatus.NORMAL.getCode());
                x.setLastModifiedBy(actionContext.getUser().getUserId());
                x.setLastModifiedTime(System.currentTimeMillis());
            });
            if (AppIdMapping.appIdMapping.get(PrmConstant.PRM_APP_ID).equals(actionContext.getAppId())) {
                String outOwner = AccountUtil.getOutOwner(dbMasterData);
                String outTenantId = AccountUtil.getStringValue(dbMasterData, SystemConstants.Field.OutTenantId.apiName, "");
                if (StringUtils.isNotBlank(outOwner) && StringUtils.isNotBlank(outTenantId)) {
                    ObjectLimitUtil.CheckLimitResult checkOutLimitResult = ObjectLimitUtil.checkOutUserObjectLimitForEdit(actionContext.getUser(), actionContext.getObjectApiName(), outTenantId, outOwner, oldDataList, checkLimitDataList, objectDescribe, true);
                    if (CollectionUtils.notEmpty(checkOutLimitResult.getFailureIds())) {
                        throw new ValidateException(String.format(I18N.text(SFA_REACH_LIMIT_OBJ),
                                I18N.text("AccountObj.attribute.self.display_name")));
                    }
                }
            }

            String owner = AccountUtil.getOwner(dbMasterData);
            if (StringUtils.isNotBlank(owner)) {
                ObjectLimitUtil.CheckLimitResult checkLimitResult = ObjectLimitUtil.checkObjectLimitForEdit(actionContext.getUser(), actionContext.getObjectApiName(), owner, oldDataList, checkLimitDataList, objectDescribe);
                if (CollectionUtils.notEmpty(checkLimitResult.getFailureIds())) {
                    throw new ValidateException(String.format(I18N.text(SFA_REACH_LIMIT_OBJ),
                            I18N.text("AccountObj.attribute.self.display_name")));
                }
            }
        }
        for (String field : editExceptFields) {
            if (dataDocument.containsKey(field)) {
                dataDocument.remove(field);
            }
        }
    }

    @Override
    protected Set<String> getIgnoreFieldsForApproval() {
        Set<String> result = super.getIgnoreFieldsForApproval();
        result.add(AccountConstants.Field.EXPIRE_TIME);
        result.add(AccountConstants.Field.REMIND_DAYS);
        result.add(AccountConstants.Field.LAST_FOLLOWED_TIME);
        result.add(AccountConstants.Field.LAST_DEAL_TIME);
        return result;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        //成功触发审批流不需要执行后续操作
        if (isApprovalFlowStartSuccess(this.objectData.getId())) {
            AccountPathUtil.dealAccountPath(actionContext.getUser(), Sets.newHashSet(this.objectData.getId()));
            return result;
        }
        result = super.after(arg, result);
        if (isNeedChangeAccountPath) {
            AccountPathSynchronizer.builder()
                    .user(actionContext.getUser())
                    .objectDataList(Lists.newArrayList(result.getObjectData().toObjectData()))
                    .build()
                    .asyncDealData();
        }

        //修改客户时，同时修改客户地址中的主地址
        AccountAddrUtil.updateAccountAddrForMain(actionContext.getUser(), result.getObjectData().toObjectData());

        return result;
    }
}
