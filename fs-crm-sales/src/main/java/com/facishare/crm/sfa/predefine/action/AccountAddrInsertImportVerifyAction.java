package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.utilities.util.AccountAddrUtil;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportVerifyAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;

import java.util.List;

public class AccountAddrInsertImportVerifyAction extends StandardInsertImportVerifyAction {

    @Override
    protected List<IFieldDescribe> getValidImportFields() {
        List<IFieldDescribe> fieldDescribes = super.getValidImportFields();
        fieldDescribes.removeIf(f -> AccountAddrUtil.getImportTemplateRemoveFields().contains(f.getApiName()));
        return fieldDescribes;
    }

}
