package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.ObjectPoolService;
import com.facishare.crm.sfa.utilities.util.AccountAddrUtil;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.crm.sfa.utilities.util.LayoutUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardDetailController;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.license.dto.GetVersion;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.MetaDataService;
import com.facishare.paas.appframework.metadata.MetaDataServiceImpl;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.facishare.crm.userdefobj.DefObjConstants.invisibleReferenceMap;

@Slf4j
public class AccountWebDetailController extends SFAWebDetailController {
    private static final String ACCOUNT_ADDR_MD_GROUP_COMPONENT = Utils.ACCOUNT_ADDR_API_NAME + "_md_group_component";
    private static final String ACCOUNT_FIN_INFO_MD_GROUP_COMPONENT = Utils.ACCOUNT_FIN_INFO_API_NAME + "_md_group_component";
    private final static MetaDataService metaDataService = SpringUtil.getContext().getBean(MetaDataServiceImpl.class);

    @Override
    protected boolean defaultEnableQixinGroup() {
        return Boolean.TRUE;
    }

    private void specialLogicForLayout(ILayout layout, Result result) {
        try {
            //todo 考虑合并
            Map<String, Map<String, Boolean>> objApiNameAndActionCodePrivilegeMapping = functionPrivilegeService.batchFunPrivilegeCheck(controllerContext.getUser(),
                    Lists.newArrayList(Utils.ACCOUNT_API_NAME),
                    Lists.newArrayList(ObjectAction.VIEW_ATTACH.getActionCode(), ObjectAction.UPLOAD_DELETE_ATTACH.getActionCode()));
            List<IComponent> componentList = layout.getComponents();
            if (!componentList.isEmpty()) {
                Optional<IComponent> attachComponent = layout.getComponents().stream().filter(x -> ATTACH_COMPONENT.equals(x.getName())).findFirst();
                if (attachComponent.isPresent()) {
                    if (objApiNameAndActionCodePrivilegeMapping.get(Utils.ACCOUNT_API_NAME).get(ObjectAction.VIEW_ATTACH.getActionCode())) {
                        if (result != null && result.getData() != null && !result.getData().toObjectData().isDeleted()) {
                            if (objApiNameAndActionCodePrivilegeMapping.get(Utils.ACCOUNT_API_NAME).get(ObjectAction.UPLOAD_DELETE_ATTACH.getActionCode())) {
                                List<IButton> buttons = getButtons();
                                WebDetailLayout.of(layout).addButtons(buttons, ATTACH_COMPONENT);
                            }
                        }
                    } else {
                        WebDetailLayout.of(layout).removeComponents(Lists.newArrayList(ATTACH_COMPONENT));
                    }
                }
                if (result != null && result.getData() != null && !result.getData().toObjectData().isDeleted()) {
                    Optional<IComponent> componentAccountAddr = componentList.stream().filter(s -> s.getName().equals(ACCOUNT_ADDR_MD_GROUP_COMPONENT)).findFirst();
                    if (componentAccountAddr.isPresent() && result.getData() != null) {
                        WebDetailLayout.of(layout).addButtons(AccountAddrUtil.getButtons(controllerContext.getUser(), Utils.ACCOUNT_ADDR_API_NAME, result.getData()), ACCOUNT_ADDR_MD_GROUP_COMPONENT);
                    }
                    Optional<IComponent> componentAccountFinInfo = componentList.stream().filter(s -> s.getName().equals(ACCOUNT_FIN_INFO_MD_GROUP_COMPONENT)).findFirst();
                    if (componentAccountFinInfo.isPresent()) {
                        WebDetailLayout.of(layout).addButtons(AccountAddrUtil.getButtons(controllerContext.getUser(), Utils.ACCOUNT_FIN_INFO_API_NAME, result.getData()), ACCOUNT_FIN_INFO_MD_GROUP_COMPONENT);
                    }
                }
            }
        } catch (MetadataServiceException ex) {
            log.warn("AccountWebDetailController->ChangeComponentOrder  error", ex);
        }

        //移动端移除ViewFeedCard按钮
        if (RequestUtil.isMobileRequest()) {
            List<IButton> buttons = layout.getButtons();
            buttons.removeIf(x -> x.getAction().equals(ObjectAction.VIEW_FEED_CARD.getActionCode()));
            layout.setButtons(buttons);
            LayoutExt layoutExt = LayoutExt.of(layout);
            Optional<IComponent> headInfoComponentOp = layoutExt.getHeadInfoComponent();
            if (headInfoComponentOp.isPresent()) {
                IComponent headInfoComponent = headInfoComponentOp.get();
                List<IButton> headInfoButtons = headInfoComponent.getButtons();
                headInfoButtons.removeIf(x -> x.getAction().equals(ObjectAction.VIEW_FEED_CARD.getActionCode()));
                WebDetailLayout.of(layout).setButtons(headInfoButtons, "head_info");
            }
        }
        List<String> invisibleReference = invisibleReferenceMap.get(arg.getObjectDescribeApiName());
        List<String> componentApiNameList = Lists.newArrayList();
        invisibleReference.forEach(m -> componentApiNameList.add(m + "_account_id_related_list"));
        componentApiNameList.add("LeadsObj_account_id_related_list");
        WebDetailLayout.of(layout).removeComponents(componentApiNameList);
    }

    @Override
    protected StandardDetailController.Result after(StandardDetailController.Arg arg, StandardDetailController.Result result) {
        this.stopWatch.lap("after start");
        StandardDetailController.Result newResult = super.after(arg, result);
        IObjectData objectData = newResult.getData().toObjectData();
        AccountUtil.handleRemainingTimeDesc(describe, Lists.newArrayList(objectData));
        this.stopWatch.lap("handleRemainingTimeDesc end");
        AccountUtil.handleIsRemindRecycling(Lists.newArrayList(objectData));
        this.stopWatch.lap("handleIsRemindRecycling end");
        AccountUtil.calculateCompletionRate(describe, Lists.newArrayList(objectData));
        this.stopWatch.lap("calculateCompletionRate end");
        boolean isShowCompanyLyricalAll = AccountUtil.isShowCompanyLyricalAll(controllerContext.getTenantId(), objectData.getName());
        this.stopWatch.lap("isShowCompanyLyricalAll end");
        objectData.set("isShowCompanyLyricalAll", isShowCompanyLyricalAll);
        newResult.setData(ObjectDataDocument.of(objectData));
        if (newResult.getLayout() == null) {
            return newResult;
        }
        ILayout layout = new Layout(newResult.getLayout());

        specialLogicForLayout(layout, newResult);
        this.stopWatch.lap("specialLogicForLayout end");

        String owner = AccountUtil.getOwner(objectData);
        if (StringUtils.isEmpty(owner)) {
            handleRelatedListByHighSeaSetting(layout);
            this.stopWatch.lap("handleRelatedListByHighSeaSetting end");
        }

        GetVersion.VersionInfo versionInfo = serviceFacade.getVersionInfo(controllerContext.getTenantId());
        if (versionInfo != null) {
            LayoutUtils.handleRelatedCasesObj(versionInfo.getCurrentVersion(), "CasesObj_account_id_related_list", layout);
            this.stopWatch.lap("handleRelatedCasesObj end");

        }
        this.stopWatch.lap("after end");
        return newResult;
    }

    private FunctionPrivilegeService functionPrivilegeService = SpringUtil.getContext().getBean("functionPrivilegeService", FunctionPrivilegeService.class);

    private List<IButton> getButtons() {
        Map<String, Map<String, Permissions>> privilege = metaDataService.checkDataPrivilege(
                controllerContext.getUser(), Lists.newArrayList(data), ObjectDescribeExt.of(describe),
                Lists.newArrayList(ObjectAction.UPLOAD_DELETE_ATTACH.getActionCode()));

        List<IButton> buttons = Lists.newArrayList();
        if (privilege.containsKey(ObjectAction.UPLOAD_DELETE_ATTACH.getActionCode())) {
            Map<String, Permissions> permissions = privilege.get(ObjectAction.UPLOAD_DELETE_ATTACH.getActionCode());
            if (permissions.containsKey(arg.getObjectDataId())
                    && permissions.get(arg.getObjectDataId()).equals(Permissions.READ_WRITE)) {
                IButton addButton = new Button();
                addButton.setAction("Add");
                addButton.setActionType("default");
                addButton.setLabel(I18N.text(I18NKey.action_upload));
                addButton.setName("AccountAttObj_Add_button_default");
                buttons.add(addButton);
                IButton deleteButton = new Button();
                deleteButton.setAction("Delete");
                deleteButton.setActionType("default");
                deleteButton.setLabel(I18N.text(I18NKey.action_delete));
                deleteButton.setName("AccountAttObj_Delete_button_default");
                buttons.add(deleteButton);
            }
        }
        return buttons;
    }

    /**
     * 根据公海的设置动态隐藏相关列表对象及隐藏字段
     */
    protected void handleRelatedListByHighSeaSetting(ILayout layout) {
        //移除外部企业字段
        SFADetailController.CheckNeedShowRelatedObjsResult checkNeedShowRelatedObjsResult = checkNeedShowRelatedObjs();
        if (!checkNeedShowRelatedObjsResult.isAllowMemberRelation()) {
            List<String> removeComponentNameList = Lists.newArrayList("CustomerAccountObj_md_group_component",
                    "AccountFinInfoObj_md_group_component", "AccountAddrObj_md_group_component", "operation_log",
                    "relevant_team_component", "account_hierarchy_component", "contact_relation_component");
            List<String> removeComponentTypeList = Lists.newArrayList("relatedlist", "multi_table", "group");
            if (checkNeedShowRelatedObjsResult.isAllowMemberViewFeed()) {
                try {
                    List<IComponent> ch_comps = layout.getComponents();
                    List<String> removeComponentList = ch_comps.stream().filter(g -> !"sale_log".equals(g.getName()) &&
                            (removeComponentTypeList.contains(g.getType()) || removeComponentNameList.contains(g.getName())))
                            .map(g -> g.getName()).collect(Collectors.toList());
                    WebDetailLayout.of(layout).removeComponents(removeComponentList);
                } catch (MetadataServiceException e) {
                    log.error("getComponents error", e);
                }
            } else {
                try {
                    List<IComponent> ch_comps = layout.getComponents();
                    List<String> removeComponentList = ch_comps.stream().filter(g -> "sale_log".equals(g.getName()) ||
                            removeComponentTypeList.contains(g.getType()) || removeComponentNameList.contains(g.getName()))
                            .map(g -> g.getName()).collect(Collectors.toList());
                    WebDetailLayout.of(layout).removeComponents(removeComponentList);
                } catch (MetadataServiceException e) {
                    log.error("getComponents error", e);
                }
            }
        }

        if (!checkNeedShowRelatedObjsResult.isAllowMemberViewFeed()) {
            WebDetailLayout.of(layout).removeComponents(Lists.newArrayList("sale_log"));
        }

        if (!checkNeedShowRelatedObjsResult.isAllowMemberSendFeed()) {
            List<IButton> layoutButtonsbuttons = layout.getButtons();
            layoutButtonsbuttons.removeIf(x -> x.getAction().equals(ObjectAction.ADD_EVENT.getActionCode()));
            layout.setButtons(layoutButtonsbuttons);
            IComponent component = getComponent(layout, "related_record", "sale_log");
            if (component != null) {
                List<IButton> buttons = component.getButtons();
                buttons.removeIf(b -> ObjectAction.ADD_EVENT.getActionCode().equals(b.getAction()));
                component.setButtons(buttons);
            }
        }

        //隐藏详细信息和顶部信息中的字段
        handleHideFieldsBySetting(layout, checkNeedShowRelatedObjsResult.getNeedHideFields());
    }
}
