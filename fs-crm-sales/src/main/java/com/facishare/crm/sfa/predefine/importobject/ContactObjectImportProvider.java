package com.facishare.crm.sfa.predefine.importobject;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.metadata.UniqueRuleExt;
import com.facishare.paas.appframework.metadata.importobject.DefaultObjectImportProvider;
import com.facishare.paas.appframework.metadata.importobject.ImportType;
import com.facishare.paas.appframework.metadata.importobject.MatchingType;
import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
public class ContactObjectImportProvider extends DefaultObjectImportProvider {
    public ContactObjectImportProvider() {
    }

    @Override
    public String getObjectCode() {
        return SFAPreDefineObject.Contact.getApiName();
    }

    @Override
    protected boolean getOpenWorkFlow(IObjectDescribe objectDescribe) {
        return true;
    }

    @Override
    protected ImportType getImportType(IObjectDescribe objectDescribe, IUniqueRule uniqueRule) {
        return ImportType.DEFAULT;
    }

    @Override
    protected Boolean getIsEnableUniqueRule(IUniqueRule uniqueRule) {
        return UniqueRuleExt.isEffectiveWhenImport(uniqueRule);
    }

    @Override
    protected List<MatchingType> getMatchingTypesByUpdateWithUniqueRule(IObjectDescribe objectDescribe, IUniqueRule uniqueRule) {
        if ((Objects.isNull(uniqueRule) || !UniqueRuleExt.of(uniqueRule).isEffectiveWhenImport()) &&
                AppFrameworkConfig.isUpdateImportByIdGrayTenant(getObjectCode(), RequestContextManager.getContext().getTenantId())) {
            return Lists.newArrayList(MatchingType.ID);
        }
        return Lists.newArrayList(MatchingType.UNIQUE_RULE);
    }
}
