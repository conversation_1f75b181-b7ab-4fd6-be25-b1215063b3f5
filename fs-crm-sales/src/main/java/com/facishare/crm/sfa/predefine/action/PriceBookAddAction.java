package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.AvailableRangeService;
import com.facishare.crm.sfa.utilities.constant.PriceBookConstants;
import com.facishare.crm.sfa.utilities.validator.PriceBookValidator;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.INameCache;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.utilities.util.DhtUtil.isFromOpenApi;
import static com.facishare.crm.sfa.utilities.util.SOI18NKeyUtils.SO_PRICEBOOKPRODUCT_PRODUT_ID_REPEAT;

public class PriceBookAddAction extends StandardAddAction {
    private static final AvailableRangeService availableRangeService = SpringUtil.getContext().getBean(AvailableRangeService.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        PriceBookValidator.validatePriceBookExpireTime(objectDescribe, Lists.newArrayList(objectData));
        if (isFromOpenApi(actionContext.getPeerName())) {
            validateProducts();
        }
        //清空优先级
        objectData.set(PriceBookConstants.Field.PRIORITY.getApiName(), null);
    }

    @Override
    protected void doSaveData() {
        super.doSaveData();
        if (actionContext.isFromOpenAPI()) {
            availableRangeService.sync2AvailableRange(actionContext.getUser(), Lists.newArrayList(objectData));
        }
    }

    private void validateProducts() {
        List<IObjectData> priceBookProductList = detailObjectData.getOrDefault(PriceBookConstants.API_NAME_PRODUCT, Lists.newArrayList());
        Map<String, Long> collect = priceBookProductList.stream().map(o -> o.get(PriceBookConstants.ProductField.PRODUCTID.getApiName(), String.class)).collect(Collectors.groupingBy(o -> o, Collectors.counting()));

        if (!collect.isEmpty()) {
            List<String> productIds = Lists.newArrayList();
            for (Map.Entry<String, Long> entry : collect.entrySet()) {
                if (entry.getValue() > 1) {
                    productIds.add(entry.getKey());
                }
            }
            List<INameCache> recordName = serviceFacade.findRecordName(ActionContextExt.of(actionContext.getUser()).getContext(), Utils.PRODUCT_API_NAME, productIds);
            String productNames = recordName.stream().map(INameCache::getName).collect(Collectors.joining(","));
            throw new ValidateException(I18N.text(SO_PRICEBOOKPRODUCT_PRODUT_ID_REPEAT, productNames));
        }
    }
}
