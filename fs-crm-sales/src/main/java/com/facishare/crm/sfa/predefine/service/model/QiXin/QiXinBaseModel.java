package com.facishare.crm.sfa.predefine.service.model.QiXin;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

public interface QiXinBaseModel {
    @Data
    class Arg implements Serializable {
        private String uuid;

        private String ei;
    }

    @Data
    class Result {
        String code;
        String message;

    }

    @Builder
    @Data
    class QiXinTodoItemInfo{
        String sourceId;
        String senderId;
        String bizType;
        String todoType;
        Integer actionRange;
        List<Integer> receiverIds;
        Boolean hasTodoData;
        String objectApiName;
        String objectId;
        boolean useObjGenTodoData;
        QiXinTodoDataInfo todoData;
        Long createTime;
        List<Integer> outEmployeeIds;
        String appId;
    }


    @Data
    class QiXinRecoveryTodoItemInfo{
        String sourceId;
        String senderId;
        String bizType;
        String todoType;
        Integer actionRange;
        List<String> receiverIds;
        QiXinTodoDataInfo todoData;
    }

    @Builder
    @Data
    class QiXinTodoDataInfo{
        String title;
        String content;
        List<QiXinTodoDataElement> elements;
    }

    @Builder
    @Data
    class QiXinTodoDataElement{
        String key;
        String value;
    }
}
