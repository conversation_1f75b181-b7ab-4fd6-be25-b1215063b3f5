package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.service.ProductCommonService;
import com.facishare.crm.sfa.predefine.service.cpq.BomCoreService;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.Map;
import java.util.Objects;

/**
 * @IgnoreI18nFile
 */
public class ProductInvalidAction extends StandardInvalidAction {

    private BomCoreService bomCoreService = SpringUtil.getContext().getBean(BomCoreService.class);
    private ProductCommonService productCommonService = SpringUtil.getContext().getBean(ProductCommonService.class);
    private ThreadPoolTaskExecutor executor = (ThreadPoolTaskExecutor) SpringUtil.getContext().getBean("taskExecutor");

    private boolean isPackageInvalid = false;

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        String productId = arg.getObjectDataId();
        productCommonService.checkProductStockRelation(actionContext.getUser(), Lists.newArrayList(productId));

        if(SFAConfigUtil.isCPQ(actionContext.getTenantId())){
            /**
             * 3个场景
             *  1. 删除字节点，校验bom没有该产品的引用，就可以删除
             *  2. 根节点，有空分组，但是没有自节点，提示不允许删除
             *  3. 根节点，没有分组和子节点，可以删除
             */
            IObjectData productData = objectDataList.stream().findFirst().get();
            Boolean isPackage = productData.get("is_package", Boolean.class);

            if(Objects.equals(Boolean.TRUE, isPackage)){
                Map<String, Long> bomElement = bomCoreService.findBomElement(actionContext.getUser(), Lists.newArrayList(arg.getObjectDataId()));
                if (bomElement.values().stream().anyMatch(x -> x > 0L)) {
                    throw new ValidateException("产品包下包含子节点，不允许作废");
                }


                Map<String, Long> nullGroup = bomCoreService.findNullGroup(actionContext.getUser(), Lists.newArrayList(arg.getObjectDataId()));
                if (nullGroup.values().stream().anyMatch(x -> x > 0L)) {
                    throw new ValidateException("产品包下包含分组，不允许作废");
                }

                isPackageInvalid = true;
            }else{
                Map<String, Long> middleNodeMap = bomCoreService.findMiddleNodeCount(actionContext.getUser(), Lists.newArrayList(arg.getObjectDataId()));
                if (middleNodeMap.values().stream().anyMatch(x -> x > 0L)) {
                    throw new ValidateException("产品被产品选配明细引用，不允许作废");
                }
            }
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        if(isPackageInvalid){
            // 是产品组合，要把对应的bom也删除掉。
            executor.execute(() -> {
                bomCoreService.bulkDeletedRootBomElement(actionContext.getUser(), Lists.newArrayList(arg.getObjectDataId()));
                bomCoreService.updateProductIsPackage(actionContext.getUser(), Lists.newArrayList(arg.getObjectDataId()), false);
            });
        }
        return newResult;
    }
}
