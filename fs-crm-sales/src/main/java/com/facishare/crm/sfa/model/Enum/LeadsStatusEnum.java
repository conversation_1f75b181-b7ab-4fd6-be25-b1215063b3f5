package com.facishare.crm.sfa.model.Enum;

public enum LeadsStatusEnum {

    UN_ALLOCATE(1, "未分配"),
    UN_DEAL(2, "待处理"),
    TO_CUSTOMER(3,"已转换"),
    DEALED(4,"跟进中"),
    CLOSED(5,"无效"),
    INEFFECTIVE(6,"未生效"),
    AUDITING(7,"审核中"),
    CHANGING(8,"变更中"),
    ABOLISHED(99,"已作废");

    private int code;
    private String text;

    LeadsStatusEnum(int code, String text) {
        this.code = code;
        this.text = text;
    }
    public int getCode() {
        return code;
    }
    public String getValue()
    {
        return String.valueOf(code);
    }
    public String getText()
    {
        return text;
    }
}
