package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.util.SfaListHeaderUtil;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import java.util.List;


/**
 * Created by renlb on 2018/12/25.
 */
public class SalesOrderProductListHeaderController extends StandardListHeaderController {

    protected FormComponent formComponent;
    private static final List<String> FILTER_BUTTON = Lists.newArrayList("Import_button_default", "IntelligentForm_button_default", "ExportFile_button_default");
    private static final List<String> FILTER_FIELD = Lists.newArrayList("parent_prod_pkg_key", "prod_pkg_key","bom_id");

    @Override
    protected Result after(Arg arg, Result result) {
        Result res = super.after(arg, result);
        ILayout layout = new Layout(res.getLayout());
        SfaListHeaderUtil.specialLogicForLayout(layout,SfaListHeaderUtil.SALES_ORDER_PRODUCT_WHITE_LIST_BUTTONS);
        SfaListHeaderUtil.specialButtons(res.getButtons(),SfaListHeaderUtil.SALES_ORDER_PRODUCT_WHITE_LIST_BUTTONS);
        SfaListHeaderUtil.specialLayout(res.getLayout(), Sets.newHashSet(SfaListHeaderUtil.SALES_ORDER_PRODUCT_QUOTE_LINES_FILTER_FIELD));
        SfaListHeaderUtil.specialTemplates(res.getTemplates(), SfaListHeaderUtil.SALES_ORDER_PRODUCT_QUOTE_LINES_FILTER_FIELD);
        SfaListHeaderUtil.specialVisibleFields(res.getVisibleFields(),SfaListHeaderUtil.SALES_ORDER_PRODUCT_QUOTE_LINES_FILTER_FIELD);
        ObjectDescribeDocument objectDescribeDocument = SfaListHeaderUtil.specialDescribe(res.getObjectDescribe(), SfaListHeaderUtil.SALES_ORDER_PRODUCT_QUOTE_LINES_FILTER_FIELD);
        res.setObjectDescribe(objectDescribeDocument);
        return res;
    }
}
