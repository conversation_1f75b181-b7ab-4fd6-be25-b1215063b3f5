//package com.facishare.crm.sfa.utilities.util.Price;
//
//import com.facishare.crm.sfa.utilities.constant.ProductConstants;
//import com.facishare.paas.appframework.core.model.User;
//import com.facishare.paas.metadata.api.DBRecord;
//import com.facishare.paas.metadata.api.IObjectData;
//import com.google.common.collect.Lists;
//import lombok.extern.slf4j.Slf4j;
//
//import java.util.*;
//import java.util.stream.Collectors;
//
//import static java.lang.Float.parseFloat;
//
///**
// * <AUTHOR>
// * 不开可售范围,不开价目表
// */
//@Slf4j
//public class RealPrice3 extends BaseRealPrice {
//
//    public RealPrice3(User user) {
//        super(user);
//    }
//
//    @Override
//    public Set<String> checkProductRange(Arg arg) {
//        fillStandardProductList(arg);
//        return standardProductList.stream().map(DBRecord::getId).collect(Collectors.toSet());
//    }
//
//    @Override
//    public Float getPrice(Arg arg, String productId) {
//        Optional<IObjectData> standardProduct = standardProductList.stream().filter(x -> x.getId().equals(productId)).findFirst();
//        return standardProduct.map(x -> Float.parseFloat(x.get(ProductConstants.Field.PRICE.getApiName()).toString())).orElse(null);
//    }
//}
