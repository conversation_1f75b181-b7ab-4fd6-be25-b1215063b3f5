package com.facishare.crm.sfa.predefine.service;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.service.ObjectDesignerService;
import com.facishare.paas.appframework.core.predef.service.dto.objectDescribe.FindDescribeAndNewLayout;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.LayoutLogicService;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.TabSection;
import com.facishare.paas.metadata.impl.ui.layout.component.ChartComponent;
import com.facishare.paas.metadata.impl.ui.layout.component.TabsComponent;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @IgnoreI18nFile
 */
@ServiceModule("marketing_event")
@Component
@Slf4j
public class MarketingEventService {
    @Autowired
    private ObjectDesignerService objectDesignerService;
    @Autowired
    private LayoutLogicService layoutLogicService;
    @Autowired
    private ServiceFacade serviceFacade;

    @ServiceMethod("refreshLayout")
    public void refreshLayout(ServiceContext context) {
        IObjectDescribe objectDescribe = serviceFacade.findObject(context.getTenantId(), SFAPreDefineObject.MarketingEvent.getApiName());
        List<ILayout> detailLayoutList = layoutLogicService.getDetailLayouts(context.getTenantId(), objectDescribe);
        if(CollectionUtils.empty(detailLayoutList)) {
            return;
        }
        Map<String, String> chartMap = Maps.newHashMap();
        chartMap.put("BI_5ea167bae3caad0001c02cd0", "按市场活动统计线索转化情况");
        chartMap.put("BI_5ea1691be3caad0001c02cfe", "按市场活动查看ROI");
        chartMap.put("BI_5ea16c71e3caad0001c02da7", " 按来源统计线索转化情况");
        String chartComponentApiName = "container_default_MarketingEventObj_BI_Sys_Default_All_Result_Report";
        for (ILayout layout : detailLayoutList){
            FindDescribeAndNewLayout.Arg arg = new FindDescribeAndNewLayout.Arg();
            arg.setDescribeApiName(SFAPreDefineObject.MarketingEvent.getApiName());
            arg.setIncludeLayout(true);
            arg.setLayoutApiName(layout.getName());
            FindDescribeAndNewLayout.Result newDetailLayoutResult = objectDesignerService.findDescribeAndNewLayout(arg, context);
            ILayout newDetailLayout = LayoutExt.of((LayoutDocument) newDetailLayoutResult.getLayout()).getLayout();
            try{
                List<IComponent> components = newDetailLayout.getComponents();
                Optional<TabsComponent> tabsComponentOp = components.stream().filter(x -> "tabs".equals(x.getType())).map(x-> (TabsComponent)x).findFirst();
                if(!tabsComponentOp.isPresent()){
                    continue;
                }
                TabsComponent tabsComponent = tabsComponentOp.get();
                List<TabSection> tabSections = tabsComponent.getTabs();
                Optional<TabSection> chartTabOp = tabSections.stream().filter(x-> chartComponentApiName.equals(x.getApiName())).findFirst();
                if(chartTabOp.isPresent()){
                    continue;
                }

                TabSection chartTab = new TabSection();
                chartTab.setApiName(chartComponentApiName);
                chartTab.setHeader("市场活动结果数据");
                List<IComponent> addedChartComponents = Lists.newArrayList();
                chartMap.keySet().forEach(k -> addedChartComponents.add(getChartComponent("chart_component_" + k, k, chartMap.get(k))));
                components.addAll(addedChartComponents);
                List<List<String>> tabsComponents = tabsComponent.getComponents();
                tabsComponents.add(addedChartComponents.stream().map(x -> x.getName()).collect(Collectors.toList()));
                tabsComponent.addTabSection(chartTab);
                newDetailLayout.setComponents(components);
                layoutLogicService.updateLayout(context.getUser(), newDetailLayout);
            }catch (Exception e){
                log.error("refreshLayout error", e);
            }
        }
    }

    private IComponent getChartComponent(String name, String chartId, String header) {
        ChartComponent chartComponent = new ChartComponent();
        chartComponent.setName(name);
        chartComponent.setCardID(chartId);
        chartComponent.setHeader(header);
        chartComponent.setTitle(header);
        chartComponent.setType("chart");
        chartComponent.setDefineType("general");
        chartComponent.set("limit", 10);
        Map<String, Object> styleMap = Maps.newHashMap();
        styleMap.put("height", "300px");
        chartComponent.setStyle(styleMap);
        return chartComponent;
    }
}
