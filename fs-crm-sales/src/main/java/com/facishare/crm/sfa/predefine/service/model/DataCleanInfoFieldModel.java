package com.facishare.crm.sfa.predefine.service.model;

import com.facishare.paas.appframework.core.model.ObjectFieldDescribeDocument;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

public interface DataCleanInfoFieldModel {
    @Data
    public class Arg{
        String objectApiName;
        String tenantID;
    }
    @Data
    public class Result{
        Map<String, ObjectFieldDescribeDocument> duplicateSearchFields;
    }
}
