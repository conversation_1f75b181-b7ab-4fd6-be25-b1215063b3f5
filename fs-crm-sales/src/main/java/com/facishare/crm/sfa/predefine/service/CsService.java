package com.facishare.crm.sfa.predefine.service;

import com.facishare.crm.sfa.predefine.service.model.CsModel;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019-04-18 12:27
 */
@ServiceModule("cs")
@Component
@Slf4j
public class CsService {


    @Autowired
    private ObjectDataServiceImpl objectDataService;


    @ServiceMethod("getCustomer")
    public CsModel.Result getCustomer(CsModel.Arg arg, ServiceContext context) {
        String tenantId = context.getTenantId();
        try {
            String queryAccountSql = "SELECT * FROM biz_account WHERE "
                    + String.format("tenant_id = '%s' ", tenantId)
                    + String.format("and value6 = '%s' ", arg.getEa())
                    + String.format("and is_deleted = 0 ");
            List<Map> accountResult = objectDataService.findBySql(tenantId, queryAccountSql);

            if (CollectionUtils.notEmpty(accountResult)) {
                return CsModel.Result.builder().value(accountResult.get(0)).build();
            }
        } catch (Exception e) {
            log.error("getCustomer error:{}", e.getMessage());
        }
        return CsModel.Result.builder().build();
    }
}
