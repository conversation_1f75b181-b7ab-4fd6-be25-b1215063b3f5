package com.facishare.crm.sfa.predefine.controller;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.predefine.service.real.invoice.InvoiceService;
import com.facishare.paas.appframework.common.service.dto.UserInfo;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import com.facishare.paas.appframework.privilege.model.DataPrivilegeProvider;
import com.facishare.paas.appframework.privilege.model.DataPrivilegeProviderManager;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.component.ComponentFactory;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/6/22 10:48 上午
 * @illustration
 */
public class InvoiceApplicationWebDetailController extends SFAWebDetailController {

    protected ObjectDataServiceImpl objectDataService = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);
    private InvoiceService invoiceService = SpringUtil.getContext().getBean(InvoiceService.class);

    static String MULTI_INVOICE_SALES_ORDER_JSON = "{\n" +
            "    \"buttons\": [],\n" +
            "    \"api_name\": \"invoice_app_sales_order_multi_related_list\",\n" +
            "    \"is_hidden\": false,\n" +
            "    \"header\": \"销售订单\",\n" +
            "    \"type\": \"relatedlist\",\n" +
            "    \"ref_object_api_name\": \"SalesOrderObj\",\n" +
            "    \"related_list_name\": \"invoice_app_sales_order_multi_related_list\",\n" +
            "    \"order\": 999\n" +
            "}";


    @Override
    protected Result after(Arg arg, Result result) {
        Result rst = super.after(arg, result);
        super.setFinanceEmployeeIdInfo(rst);
        if (result.getLayout() == null) {
            return result;
        }
        ILayout layout = new Layout(result.getLayout());
        // 销售订单和开票申请多对多的处理
        if (invoiceService.isNewInvoiceOpen(controllerContext.getUser())) {
            WebDetailLayout.of(layout).removeButtonsByActionCode(Lists.newArrayList(ObjectAction.CLONE.getActionCode()));
            addInvoiceAppLayoutStructure(layout);
        }
        addSoftWorkflowButtons(controllerContext.getUser(), data, layout);
        return rst;
    }

    /**
     * 添加订单和开票的多对多页签
     *
     * @param layout
     * @return
     */
    public ILayout addInvoiceAppLayoutStructure(ILayout layout) {
        try {
            IComponent multiComponent = ComponentFactory.newInstance(MULTI_INVOICE_SALES_ORDER_JSON);
            // 放在最后一个
            WebDetailLayout.of(layout).addComponents(Lists.newArrayList(multiComponent),9999);
            return layout;
        } catch (Exception e) {
            log.error("InvoiceApplicationWebDetailController  addComponents error tenantId->{}, userId->{}, dataId->{}",
                    controllerContext.getTenantId(), controllerContext.getUser().getUserId(), arg.getObjectDataId(), e);
        }
        return layout;
    }
}
