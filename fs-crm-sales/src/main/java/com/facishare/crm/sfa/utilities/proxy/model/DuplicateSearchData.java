package com.facishare.crm.sfa.utilities.proxy.model;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

public interface DuplicateSearchData {
    @Data
    @Builder
    class Arg {
        @JSONField(name = "ObjectType")
        public Integer objectType ;
        @JSONField(name = "Data")
        public JSONObject data;
    }
    @Data
    class SearchValue
    {
        @JSONField(name = "DuplicateMode")
        public Integer duplicateMode;
        @JSONField(name = "Objects")
        public List<Map<String,String>> objects;
    }
    @Data
    class Result {
        boolean success;
        String message;
        int errorCode;
        JsonObject  value;
    }
}