package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.model.SFAObjectPoolCommon;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by yuanjl on 2018/7/18.
 */
@Slf4j
public class SFAPoolFlowCompletedAction extends SFAFlowCompletedAction {

    @Override
    protected void doOtherAction() {
        log.warn("extend_log SFAPoolFlowCompletedAction doOtherAction {}",arg);
        if (((Arg)this.arg).approvalFlowTriggerType() == ApprovalFlowTriggerType.CHOOSE) {
            doChooseAction();
        }
        if (((Arg)this.arg).approvalFlowTriggerType() == ApprovalFlowTriggerType.RETURN) {
            doReturnAction();
        }

        if (((Arg)this.arg).approvalFlowTriggerType() == ApprovalFlowTriggerType.EXTEND_EXPIRETIME) {
            doExtendAction();
        }
    }

    private void doChooseAction() {
        Map<String, Object> callbackData = parseCallbackData();
        List<String> dataIds = new ArrayList<>();
        dataIds.add(arg.getDataId());
        SFAObjectPoolCommon.Arg arg =  new SFAObjectPoolCommon.Arg();
        if (callbackData.get("objectPoolId") != null) {
            String objectPoolId=callbackData.get("objectPoolId").toString();
            arg.setObjectPoolId(objectPoolId);
        }

        arg.setObjectIDs(dataIds);
        arg.setSkipTriggerApprovalFlow(true);
        arg.setSkipFunctionCheck(true);
        arg.setSkipPreAction(true);
        arg.setSkipButtonConditions(true);
        ActionContext detailActionContext = new ActionContext(this.actionContext.getRequestContext(), this.objectDescribe.getApiName(), "Choose");
        this.serviceFacade.triggerAction(detailActionContext, arg, SFAObjectPoolCommon.Result.class);
    }

    private void doReturnAction() {
        Map<String, Object> callbackData = parseCallbackData();
        List<String> dataIds = new ArrayList<>();
        dataIds.add(arg.getDataId());
        BaseSFAReturnAction.Arg arg =  new BaseSFAReturnAction.Arg();

        if(callbackData.containsKey("form_back_reason") || callbackData.containsKey("form_back_reason__o")) {
            ObjectDataDocument data = ObjectDataDocument.of(callbackData);
            arg.setObjectPoolId(data.get("objectPoolId").toString());
            arg.setOperationType(Integer.parseInt(data.get("operationType").toString()));
            arg.setArgs(data);
        } else {
            arg.setObjectPoolId(callbackData.get("objectPoolId").toString());
            if (callbackData.get("backReason") != null) {
                arg.setBackReason(callbackData.get("backReason").toString());
            }
            if (callbackData.get("backReason__o") != null) {
                arg.setBackReason__o(callbackData.get("backReason__o").toString());
            }
            arg.setOperationType(Integer.parseInt(callbackData.get("operationType").toString()));
        }
        arg.setObjectIDs(dataIds);
        arg.setSkipTriggerApprovalFlow(true);
        arg.setSkipFunctionCheck(true);
        arg.setSkipPreAction(true);
        arg.setSkipButtonConditions(true);
        ActionContext detailActionContext = new ActionContext(this.actionContext.getRequestContext(), this.objectDescribe.getApiName(), "Return");
        this.serviceFacade.triggerAction(detailActionContext, arg, SFAObjectPoolCommon.Result.class);
    }

    private void doExtendAction() {
        Map<String, Object> callbackData = parseCallbackData();
        List<String> dataIds = new ArrayList<>();
        dataIds.add(arg.getDataId());
        BaseSFAExtendAction.Arg arg =  new BaseSFAExtendAction.Arg();
        if(callbackData.get("extendDays") != null) {
            arg.setExtendDays(Double.valueOf(callbackData.get("extendDays").toString()));
        }
        if(callbackData.get("extendReason") != null) {
            arg.setExtendReason(callbackData.get("extendReason").toString());
        }
        arg.setObjectIDs(dataIds);
        arg.setSkipTriggerApprovalFlow(true);
        arg.setSkipFunctionCheck(true);
        arg.setSkipPreAction(true);
        arg.setSkipButtonConditions(true);
        arg.setPass(this.arg.isPass());
        log.warn("extend_log SFAPoolFlowCompletedAction doExtendAction:{}",arg);
        ActionContext detailActionContext = new ActionContext(this.actionContext.getRequestContext(), this.objectDescribe.getApiName(), "Extend");
        this.serviceFacade.triggerAction(detailActionContext, arg, SFAObjectPoolCommon.Result.class);
    }

}
