package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.exception.SOBusinessException;
import com.facishare.crm.sfa.utilities.constant.PriceBookConstants;
import com.facishare.crm.sfa.utilities.validator.PriceBookValidator;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.INameCache;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.predefine.exception.SOErrorCode.SO_PRICEBOOK_PRICEBOOKPRODUCT_NOR_UPDATE_PRODUCT_ID__ERROR;
import static com.facishare.crm.sfa.utilities.util.ConcatenateSqlUtils.getPriceBookProductProductIdsByPriceBookId;
import static com.facishare.crm.sfa.utilities.util.DhtUtil.isFromOpenApi;
import static com.facishare.crm.sfa.utilities.util.SOI18NKeyUtils.SO_PRICEBOOKPRODUCT_PRODUT_ID_REPEAT;

@Slf4j
public class PriceBookEditAction extends StandardEditAction {
    private ObjectDataServiceImpl objectDataService = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        PriceBookValidator.validatePriceBookExpireTime(objectDescribe , Lists.newArrayList(objectData));
        if (isFromOpenApi(actionContext.getPeerName())) {
            validateProducts();
        }
        //回填原优先级
        objectData.set(PriceBookConstants.Field.PRIORITY.getApiName(), dbMasterData.get(PriceBookConstants.Field.PRIORITY.getApiName()));
    }

    private void validateProducts() {
        String productId = PriceBookConstants.ProductField.PRODUCTID.getApiName();
        try {
            List<Map> result = objectDataService.findBySql(actionContext.getTenantId(), getPriceBookProductProductIdsByPriceBookId(actionContext.getTenantId(), objectData.getId()));
            Map<String, String> detailIdToProductId = result.stream().collect(Collectors.toMap(o -> o.get("id").toString(), o -> Optional.ofNullable((String) o.get("product_id")).orElse("")));

            List<String> errorAddProductIds = detailsToAdd.stream().map(o -> o.get(productId, String.class)).filter(detailIdToProductId::containsValue).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(errorAddProductIds)) {
                throw new ValidateException(I18N.text(SO_PRICEBOOKPRODUCT_PRODUT_ID_REPEAT, getProductNameByIds(errorAddProductIds)));
            }
            if (CollectionUtils.isNotEmpty(detailsToUpdate)) {
                List<String> updateErrorProductIds = Lists.newArrayList();
                for (IObjectData data : detailsToUpdate) {
                    if (!detailIdToProductId.get(data.getId()).equals(data.get(productId, String.class)))
                        updateErrorProductIds.add(data.get(productId, String.class));
                }
                if (CollectionUtils.isNotEmpty(updateErrorProductIds))
                    throw new SOBusinessException(SO_PRICEBOOK_PRICEBOOKPRODUCT_NOR_UPDATE_PRODUCT_ID__ERROR);
            }

            checkRepeatProductIds(detailsToAdd);
            checkRepeatProductIds(detailsToUpdate);


        } catch (MetadataServiceException e) {
            log.error("PriceBookEditAction validateProducts:tenantId:{},id:{}", actionContext.getTenantId(), objectData.getId());
        }
    }

    private void checkRepeatProductIds(List<IObjectData> detailDatas) {
        Map<String, Long> collect = detailDatas.stream().map(o -> o.get(PriceBookConstants.ProductField.PRODUCTID.getApiName(), String.class)).collect(Collectors.groupingBy(o -> o, Collectors.counting()));
        if (!collect.isEmpty()) {
            List<String> errorProductIds = Lists.newArrayList();
            for (Map.Entry<String, Long> entry : collect.entrySet()) {
                if (entry.getValue() > 1) {
                    errorProductIds.add(entry.getKey());
                }
            }
            if (CollectionUtils.isNotEmpty(errorProductIds))
                throw new ValidateException(I18N.text(SO_PRICEBOOKPRODUCT_PRODUT_ID_REPEAT, getProductNameByIds(errorProductIds)));
        }
    }

    private String getProductNameByIds(List<String> productIds){
        List<INameCache> recordName = serviceFacade.findRecordName(ActionContextExt.of(actionContext.getUser()).getContext(), Utils.PRODUCT_API_NAME, productIds);
        return recordName.stream().map(INameCache::getName).collect(Collectors.joining(","));
    }
}
