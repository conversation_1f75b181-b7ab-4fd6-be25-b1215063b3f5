package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.model.SFAObjectPoolCommon;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAsyncBulkAction;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by yuanjl on 2018/7/18.
 */
public class SFAAsyncBulkChooseAction extends AbstractStandardAsyncBulkAction<SFAObjectPoolCommon.Arg, SFAObjectPoolCommon.Arg> {

    @Override
    protected String getDataIdByParam(SFAObjectPoolCommon.Arg param) {
        return param.getObjectIDs().get(0);
    }

    @Override
    protected List<SFAObjectPoolCommon.Arg> getButtonParams() {

        return arg.getObjectIDs().stream()
                .map(data -> {
                    SFAObjectPoolCommon.Arg chooseArg = new SFAObjectPoolCommon.Arg ();
                    chooseArg.setObjectIDs(Lists.newArrayList(data));
                    if (!arg.getObjectPoolId().contains("*")) {
                        chooseArg.setObjectPoolId(arg.getObjectPoolId());
                    }else {
                        if (needInvalidData()) {
                            dataList = serviceFacade.findObjectDataByIdsIncludeDeleted(actionContext.getUser(),
                                    arg.getObjectIDs(),
                                    actionContext.getObjectApiName());
                        } else {
                            dataList = serviceFacade.findObjectDataByIds(actionContext.getTenantId(),
                                    arg.getObjectIDs(),
                                    actionContext.getObjectApiName());
                        }
                        if(dataList!=null && dataList.size()!=0) {
                            Object leadsPoolId = dataList.get(0).get("leads_pool_id");
                            if(leadsPoolId != null) {
                                chooseArg.setObjectPoolId(leadsPoolId.toString());
                            }
                        }
                    }
                    chooseArg.setSkipTriggerApprovalFlow(arg.isSkipTriggerApprovalFlow());
                    chooseArg.setSkipFunctionCheck(arg.isSkipFunctionCheck());
                    return  chooseArg;
                })
                .collect(Collectors.toList());
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.CHOOSE.getButtonApiName();
    }

    @Override
    protected String getActionCode() {
        return ObjectAction.CHOOSE.getActionCode();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.CHOOSE.getActionCode());
    }
}