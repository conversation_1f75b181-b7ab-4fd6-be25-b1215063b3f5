package com.facishare.crm.sfa.predefine.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.sfa.model.SFAObjectPoolCommon;
import com.facishare.crm.sfa.predefine.service.model.SFALogModels;
import com.facishare.crm.sfa.utilities.constant.AccountConstants;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.*;

/**
 * Created by yuanjl on 2018/7/18.
 * @IgnoreI18nFile
 */
@Slf4j
public class AccountTakeBackAction extends BaseSFATakeBackAction {

    @Override
    protected void before(SFAObjectPoolCommon.Arg arg) {
        super.before(arg);
        if (dataList.stream().anyMatch(x -> "-1".equals(x.get("is_deleted")))) {
            throw new ValidateException(I18N.text(I18NKey.action_delete));
        }
        if (dataList.stream().anyMatch(x -> ObjectLifeStatus.INVALID.getCode().equals(AccountUtil.getStringValue(x, "life_status", "")))) {
            throw new ValidateException(I18N.text(I18NKey.action_invalid));
        }
        if (dataList.stream().anyMatch(x -> ObjectLifeStatus.INEFFECTIVE.getCode().equals(AccountUtil.getStringValue(x, "life_status", "")))) {
            throw new ValidateException(String.format(I18N.text(SFA_FORBID_TRANSFERRING_EFFECTIVE), I18N.text("HighSeasObj.attribute.self.display_name"), I18N.text("AccountObj.attribute.self.display_name")));
        }
        if (dataList.stream().anyMatch(x -> ObjectLifeStatus.UNDER_REVIEW.getCode().equals(AccountUtil.getStringValue(x, "life_status", "")))) {
            throw new ValidateException(String.format(I18N.text(SFA_FORBID_REPORTING_TRANSFERRING_EFFECTIVE), I18N.text("AccountObj.attribute.self.display_name"), I18N.text("HighSeasObj.attribute.self.display_name")));
        }
    }

    @Override
    protected SFAObjectPoolCommon.Result after(SFAObjectPoolCommon.Arg arg, SFAObjectPoolCommon.Result result) {
        super.after(arg, result);
        try {
            ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
            task.submit(() -> {
                sendActionMq();
                sendCrmNotification();
            });
            task.run();
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
        return result;
    }

    @Override
    protected void addLog() {
        List<String> oldOwnerIds = dataList.stream().filter(x -> AccountUtil.hasOwner(x))
                .map(x -> AccountUtil.getOwner(x)).collect(Collectors.toList());

        Map<String, String> oldOwnerUserNameMap = serviceFacade.getUserNameMapByIds(actionContext.getTenantId(),
                User.SUPPER_ADMIN_USER_ID, oldOwnerIds);

        for (IObjectData objectData : dataList) {
            String oldOwnerId = AccountUtil.getOwner(objectData);
            String oldOwnerName = "";
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(oldOwnerId)) {
                if (oldOwnerUserNameMap.containsKey(oldOwnerId)) {
                    oldOwnerName = oldOwnerUserNameMap.get(oldOwnerId);
                }
            }
            if (org.apache.commons.lang3.StringUtils.isEmpty(oldOwnerName)) {
                oldOwnerName = "--";
            }
            String messageContent = "客户 " + objectData.getName() + " ， 原负责人 " + oldOwnerName + "， 公海 " + objectPoolData.getName();

            SFALogModels.SFALogEntity logEntity = sfaLogService.buildLogEntity(objectPoolData,
                    messageContent,
                    false);
            List<SFALogModels.SFALogTextMessage> textMessageList = Lists.newArrayList();
            sfaLogService.appendNameLinkLogTextMessage(actionContext.getObjectApiName(), objectData, textMessageList);

            messageContent = " ， 原负责人 " + oldOwnerName + "， 公海 " + objectPoolData.getName();
            sfaLogService.appendLogTextMessage(actionContext.getObjectApiName(), objectData,
                    SFALogModels.LogLinkType.NO_LINK, messageContent, textMessageList);

            logEntity.setLogTextMessageList(textMessageList);
            sfaLogService.addLog(actionContext.getUser(), logEntity, "HighSeasLog",
                    SFALogModels.LogOperationType.TACK_BACK);

            messageContent = " ， 原负责人 " + oldOwnerName + "， 公海 " + objectPoolData.getName();
            serviceFacade.logWithCustomMessage(actionContext.getUser(), EventType.MODIFY, ActionType.TakeBack, objectDescribe, Lists.newArrayList(objectData), messageContent);
        }
    }

    @Override
    protected void sendActionMq(List<IObjectData> objectDataList, ObjectAction objectAction) {
    }

    private void sendActionMq() {
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIdsIncludeDeleted(actionContext.getUser(), arg.getObjectIDs(), actionContext.getObjectApiName());
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
//        serviceFacade.sendActionMq(this.actionContext.getUser(), AccountUtil.fillOldData(objectDataList, dataList)
//                ,ObjectAction.TAKE_BACK);

        ReturnHighSeasActionContent actionContent = ReturnHighSeasActionContent.builder()
                .accountIds(arg.getObjectIDs()).backReason("")
                .backReasonDescription("").build();
        if (objectDataList.size() > 1) {
            sfaOpenApiMqService.sendOpenApiMq(actionContext.getUser(), ObjectAction.TAKE_BACK.getActionCode(),
                    "HighSeasObj", arg.getObjectPoolId(), actionContent);
        } else {
            IObjectData objectData = objectDataList.get(0);
            actionContent.setCustomerName(objectData.getName());
            AccountConstants.AccountBizStatus bizStatus = AccountConstants.AccountBizStatus.of(
                    AccountUtil.getStringValue(objectData, AccountConstants.Field.BIZ_STATUS, AccountConstants.AccountBizStatus.UN_ALLOCATED.getValue()));
            actionContent.setAccountStatus(bizStatus.getValue());
            actionContent.setAccountStatusDescription(bizStatus.getLabel());
            actionContent.setHouseNo(AccountUtil.getStringValue(objectData, AccountConstants.Field.ADDRESS, ""));

            sfaOpenApiMqService.sendOpenApiMq(actionContext.getUser(), ObjectAction.TAKE_BACK.getActionCode(),
                    "HighSeasObj", arg.getObjectPoolId(), actionContent);
        }
        AccountUtil.fillOldData(objectDataList, dataList);
        this.serviceFacade.sendActionMq(this.actionContext.getUser(), objectDataList, getObjectAction());
    }

    private void sendCrmNotification() {
        for (IObjectData objectData : dataList) {
            List<String> receiverIds = AccountUtil.getTeamMember(objectData).stream().map(x -> x.getEmployee()).distinct().collect(Collectors.toList());
            receiverIds.removeIf(x -> actionContext.getUser().getUserId().equals(x));
            if (CollectionUtils.empty(receiverIds)) {
                continue;
            }

            String remindContent = String.format("%s，%s：%s，", objectData.getName(), I18N.text(SFA_NEW_DIRECTOR), I18N.text("无"));
            AccountUtil.sendCRMNotification(actionContext.getUser(),
                    remindContent,
                    6, I18N.text(SFA_TITLE_TAKE_BACK_ACCOUNT), objectData.getId(), "", Lists.newArrayList(receiverIds));
        }
    }

    @Data
    @Builder
    static class ReturnActionContent {
        @JSONField(name = "CustomerName")
        String name;
        @JSONField(name = "CustomerID")
        String accountId;
        @JSONField(name = "TrustInfo")
        AccountAllocateAction.TrustChangeAdminEvent trustChangeAdminEvent;
    }

    @Data
    @Builder
    static class ReturnHighSeasActionContent {
        @JSONField(name = "Name")
        String poolName;
        @JSONField(name = "CustomerName")
        String customerName;
        @JSONField(name = "CustomerStatus")
        String accountStatus;
        @JSONField(name = "CustomerStatusDescription")
        String accountStatusDescription;
        @JSONField(name = "HouseNo")
        String houseNo;
        @JSONField(name = "CustomerIds")
        List<String> accountIds;
        @JSONField(name = "BackReason")
        String backReason;
        @JSONField(name = "BackReasonDescription")
        String backReasonDescription;
    }
}