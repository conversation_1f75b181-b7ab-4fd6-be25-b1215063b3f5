package com.facishare.crm.sfa.utilities.util;

import com.facishare.crm.sfa.predefine.service.model.PromotionsRestModel;
import com.facishare.crm.sfa.utilities.proxy.PromotionProxy;
import com.facishare.crm.sfa.utilities.proxy.model.PromotionModel;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
public class PromotionUtil {
    private static final PromotionProxy promotionProxy = SpringUtil.getContext().getBean(PromotionProxy.class);
    public static boolean getIsPromotionEnable(User user,String clientInfo) {
        if (Objects.nonNull(clientInfo)
                &&
                (clientInfo.startsWith(RequestContext.IOS_CLIENT_INFO_PREFIX)
                        || clientInfo.startsWith(RequestContext.Android_CLIENT_INFO_PREFIX)
                ))
        {
            if (!isOpenPromotionForOrder(user)) {
                return isOpenPromotion(user);
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    public static PromotionsRestModel.PromotionRestEntity convertPromotionEntity(PromotionModel.PromotionEntity entity) {
        return PromotionsRestModel.PromotionRestEntity.builder()
                .promotion(entity.getPromotion())
                .promotionGifts(entity.getPromotionGifts())
                .promotionProducts(entity.getPromotionProducts())
                .promotionRules(entity.getPromotionRules())
                .build();
    }
    public static List<PromotionsRestModel.PromotionRestEntity> convertPromotionEntityList(List<PromotionModel.PromotionEntity> entityList) {
        List<PromotionsRestModel.PromotionRestEntity> promotions = new ArrayList<>();
        if (entityList != null) {
            for (PromotionModel.PromotionEntity entity : entityList) {
                promotions.add(PromotionsRestModel.PromotionRestEntity.builder()
                        .promotion(entity.getPromotion())
                        .promotionGifts(entity.getPromotionGifts())
                        .promotionProducts(entity.getPromotionProducts())
                        .promotionRules(entity.getPromotionRules())
                        .build());
            }
        }
        return promotions;
    }

    public static boolean isOpenPromotion(User user) {
        String configValue = SFAConfigUtil.getConfigValue(user.getTenantId(), "promotion_status",
                user.getUserId());
        if ("2".equals(configValue)) {
            return true;
        }
        return false;
    }
    public static boolean isOpenPromotionForOrder(User user) {
        String configValue = SFAConfigUtil.getConfigValue(user.getTenantId(), "promotion_mobile_h5",
                user.getUserId());
        if ("1".equals(configValue)) {
            return true;
        }
        return false;
    }
}
