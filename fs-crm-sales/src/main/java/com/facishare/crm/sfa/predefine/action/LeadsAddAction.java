package com.facishare.crm.sfa.predefine.action;

import com.facishare.crmcommon.constants.SystemConstants;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.model.Enum.LeadsBizStatusEnum;
import com.facishare.crm.sfa.model.Enum.SessionBOCItemKeys;
import com.facishare.crm.sfa.model.ObjectLimitRuleModel;
import com.facishare.crm.sfa.predefine.action.model.LeadsSaveArg;
import com.facishare.crm.sfa.predefine.action.model.LeadsSaveResult;
import com.facishare.crm.sfa.predefine.enums.ActionCodeEnum;
import com.facishare.crm.sfa.predefine.exception.SFABusinessException;
import com.facishare.crm.sfa.predefine.exception.SFAErrorCode;
import com.facishare.crm.sfa.predefine.service.LeadsPoolServiceImpl;
import com.facishare.crm.sfa.predefine.service.QiXinTodoService;
import com.facishare.crm.sfa.predefine.service.SFALogService;
import com.facishare.crm.sfa.predefine.service.model.Duplicate.LeadsDuplicatedProcessing;
import com.facishare.crm.sfa.predefine.service.model.ObjectPoolPermission;
import com.facishare.crm.sfa.predefine.service.model.SFALogModels;
import com.facishare.crm.sfa.predefine.service.task.LeadsAllocateOverTimeTaskService;
import com.facishare.crm.sfa.predefine.service.task.LeadsAllocateTaskService;
import com.facishare.crm.sfa.predefine.service.task.LeadsOverTimeTaskService;
import com.facishare.crm.sfa.predefine.service.task.RecalculateTaskService;
import com.facishare.crm.sfa.utilities.util.*;
import com.facishare.crmcommon.util.CommonSqlUtils;
import com.facishare.idempotent.Idempotent;
import com.facishare.idempotent.Serializer;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.AcceptableValidateException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.common.util.AppIdMapping;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAddAction;
import com.facishare.paas.appframework.metadata.AutoNumberLogicService;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.appframework.metadata.TeamMember;
import com.facishare.paas.appframework.prm.util.PrmConstant;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.service.ICommonSqlService;
import com.facishare.paas.metadata.service.impl.CommonSqlServiceImpl;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import java.util.List;
import java.util.Map;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;


import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.*;

/**
 * Created by yuanjl on 2018/3/21.
 */
@Slf4j
@Idempotent(serializer = Serializer.Type.java)
public class LeadsAddAction extends AbstractStandardAddAction<LeadsSaveArg> {
    LeadsPoolServiceImpl leadsPoolServiceImpl = SpringUtil.getContext().getBean(LeadsPoolServiceImpl.class);
    LeadsOverTimeTaskService leadsOverTimeTaskService = SpringUtil.getContext().getBean(LeadsOverTimeTaskService.class);
    LeadsAllocateOverTimeTaskService leadsAllocateOverTimeTaskService = SpringUtil.getContext().getBean(LeadsAllocateOverTimeTaskService.class);
    LeadsAllocateTaskService leadsAllocateTaskService = SpringUtil.getContext().getBean(LeadsAllocateTaskService.class);
    private RecalculateTaskService recalculateTaskService = SpringUtil.getContext().getBean(RecalculateTaskService.class);
    private QiXinTodoService qiXinTodoService = SpringUtil.getContext().getBean(QiXinTodoService.class);
    private SFALogService sfaLogService = SpringUtil.getContext().getBean(SFALogService.class);
    private String overRuleNotifyUserId;
    private String overRulePoolName;
    private boolean overRuleNotify = false;
    private LeadsDuplicatedProcessing.ProcessSingleResult processSingleResult;
    private static ICommonSqlService commonSqlService = SpringUtil.getContext().getBean(CommonSqlServiceImpl.class);
    private static AutoNumberLogicService autoNumberLogicService = SpringUtil.getContext().getBean(AutoNumberLogicService.class);
    private Map<String, Boolean> cleanOwnerResult;

    @Override
    protected boolean needTriggerApprovalFlow() {
        String leadsPoolId = objectData.get("leads_pool_id", String.class);
        if (!LeadsUtils.isPoolIdEmpty(leadsPoolId)) {
            return false;
        }
        return super.needTriggerApprovalFlow();
    }

    @Override
    protected void setDefaultSystemInfo(IObjectData objectData) {
        super.setDefaultSystemInfo(objectData);
        String leadsPoolId = objectData.get("leads_pool_id", String.class);
        if (!LeadsUtils.isPoolIdEmpty(leadsPoolId)) {
            ObjectDataExt.of(objectData).setLifeStatus(ObjectLifeStatus.NORMAL);
        }
    }

    @Override
    protected void before(LeadsSaveArg arg) {
        log.info("LeadsAddAction>before()arg=" + JsonUtil.toJsonWithNullValues(arg));
        log.info("LeadsAddAction>before()context=" + JsonUtil.toJsonWithNullValues(actionContext));

        super.before(arg);
        fillDefaultValue(arg);
        //获取手机归属地字段
        AccountUtil.getPhoneNumberInfo(arg.getObjectData(),"mobile");
        String leadsPoolId = objectData.get("leads_pool_id", String.class);
        String owner = AccountUtil.getOwner(objectData);
        IObjectData poolData = leadsPoolServiceImpl.getObjectPoolById(actionContext.getTenantId(), leadsPoolId);
        objectData.set("life_status", ObjectLifeStatus.NORMAL.getCode());
        objectData.set("leads_stage_changed_time", System.currentTimeMillis());
        objectData.set("last_follow_time", System.currentTimeMillis());
        String userId = actionContext.getUser().getUserId();
        objectData.set("last_follower", Lists.newArrayList(userId));
        if (AppIdMapping.appIdMapping.get(PrmConstant.PRM_APP_ID).equals(actionContext.getAppId())
                && !Strings.isNullOrEmpty(leadsPoolId)) {
            objectData.setOutOwner(Lists.newArrayList());
            objectData.set("partner_id", null);
            objectData.setOwner(Lists.newArrayList());
            objectData.setOutTenantId(null);
        }
        objectData.set("created_by", Lists.newArrayList(actionContext.getUser().getUserId()));
        objectData.set("create_time", System.currentTimeMillis());

        if (poolData != null) {
            if(StringUtils.isBlank(owner)) {
                objectData.set("biz_status", LeadsBizStatusEnum.UN_ASSIGNED.getCode());
            } else {
                if(RequestUtil.isMobileRequest() && RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_720)) {
                    throw new SFABusinessException(SFAErrorCode.CLIENT_UPGRADE_PROMPT);                
                }
                List<IObjectData> checkCleanOwnerDataList = ObjectDataExt.copyList(Lists.newArrayList(objectData));
                cleanOwnerResult = PoolOwnerRuleUtil.cleanOwner(actionContext.getUser(), Utils.LEADS_API_NAME, leadsPoolId, checkCleanOwnerDataList, objectDescribe);
                if(!actionContext.isFromSmartForm() && !actionContext.isFromOpenAPI() && !arg.isSkipCheckCleanOwner() && cleanOwnerResult.containsKey(objectData.getId()) && Boolean.TRUE.equals(cleanOwnerResult.get(objectData.getId()))) {
                    throw new AcceptableValidateException(buildCleanOwnerValidateResult());
                }
                if(cleanOwnerResult.containsKey(objectData.getId()) && Boolean.TRUE.equals(cleanOwnerResult.get(objectData.getId()))) {
                    owner = "";
                    objectData.set("biz_status", LeadsBizStatusEnum.UN_ASSIGNED.getCode());
                    objectData.set("owner", Lists.newArrayList());
                } else {
                    try {
                        LeadsUtils.checkPoolLeadsLimit(actionContext.getUser(), owner, leadsPoolId, Lists.newArrayList(objectData), ObjectPoolPermission.ObjectPoolMemberType.EMPLOYEE, 0L, 0L);
                    }catch (Exception e){
                        throw new ValidateException(String.format(I18N.text(SFA_REACH_POOL_LIMIT_OBJ),
                                I18N.text("LeadsObj.attribute.self.display_name")));
                    }
                    objectData.set("biz_status", LeadsBizStatusEnum.UN_PROCESSED.getCode());
                }
            }
        } else {
            if(StringUtils.isBlank(owner)) {
                owner = actionContext.getUser().getUserId();
                objectData.set("owner", Lists.newArrayList(actionContext.getUser().getUserId()));
            }
            objectData.set("leads_pool_id", null);
            objectData.set("biz_status", LeadsBizStatusEnum.UN_PROCESSED.getCode());
        }
        if (!StringUtils.isBlank(owner)) {
            objectData.set("owner_change_time", System.currentTimeMillis());
            objectData.set("owner_department", AccountUtil.getUserMainDepartName(actionContext.getTenantId(), owner));
        }
        if (StringUtils.isNotBlank(owner) && ObjectLimitUtil.isGrayLeadsLimit(actionContext.getTenantId()) && !actionContext.isFromOpenAPI()) {
            List<IObjectData> checkLimitDataList = ObjectDataExt.copyList(Lists.newArrayList(objectData));
            log.info("LeadsAddAction>before>checkobjectlimit>objectdata=" + JsonUtil.toJsonWithNullValues(objectData));

            if (AppIdMapping.appIdMapping.get(PrmConstant.PRM_APP_ID).equals(actionContext.getAppId())) {
                String outOwner = AccountUtil.getOutOwner(objectData);
                String outTenantId = AccountUtil.getStringValue(objectData, SystemConstants.Field.OutTenantId.apiName, "");
                log.info("LeadsAddAction>before>checkoutuserobjectlimit>outowner" + outOwner + ">outtenantid>" + outTenantId);

                ObjectLimitUtil.CheckLimitResult checkOutLimitResult = ObjectLimitUtil.checkOutUserObjectLimit(actionContext.getUser(), actionContext.getObjectApiName(), outTenantId, outOwner, checkLimitDataList, objectDescribe, true);
                if (CollectionUtils.isNotEmpty(checkOutLimitResult.getFailureIds())) {
                    throw new ValidateException(String.format(I18N.text(SFA_REACH_LIMIT_OBJ),
                            I18N.text("LeadsObj.attribute.self.display_name")));
                }
            }
            ObjectLimitUtil.CheckLimitResult checkLimitResult = ObjectLimitUtil.checkObjectLimit(actionContext.getUser(), actionContext.getObjectApiName(), actionContext.getUser().getUserId(), checkLimitDataList, objectDescribe);
            if (CollectionUtils.isNotEmpty(checkLimitResult.getFailureIds())) {
                if (actionContext.isFromSmartForm()
                        || ObjectLimitUtil.isFromOverRuleApp(objectData)) {
                    ObjectLimitRuleModel.ObjectLimitOverRule overRule = ObjectLimitUtil.getObjectLimitOverRule(actionContext.getTenantId(), actionContext.getObjectApiName());
                    if (overRule == null || StringUtils.isBlank(overRule.getObjectPoolId())) {
                        throw new ValidateException(String.format(I18N.text(SFA_OBJECT_LIMIT_OVER_RULE_ERROR),
                                I18N.text("LeadsObj.attribute.self.display_name")));
                    }
                    poolData = leadsPoolServiceImpl.getObjectPoolById(actionContext.getTenantId(), overRule.getObjectPoolId());
                    if (poolData == null) {
                        throw new ValidateException(String.format(I18N.text(SFA_OBJECT_LIMIT_OVER_RULE_ERROR),
                                I18N.text("LeadsObj.attribute.self.display_name")));
                    }
                    overRuleNotifyUserId = AccountUtil.getOwner(objectData);
                    overRuleNotify = true;
                    overRulePoolName = poolData.getName();
                    objectData.set("biz_status", LeadsBizStatusEnum.UN_ASSIGNED.getCode());
                    objectData.setOwner(Lists.newArrayList());
                    objectData.set("leads_pool_id", overRule.getObjectPoolId());
                } else {
                    throw new ValidateException(String.format(I18N.text(SFA_REACH_LIMIT_OBJ),
                            I18N.text("LeadsObj.attribute.self.display_name")));
                }
            }
        }

        LeadsDuplicatedProcessing.TriggerAction triggerAction = DuplicatedProcessingUtils.getTriggerAction(this.actionContext,
                LeadsDuplicatedProcessing.TriggerAction.ADD);
        objectData.set("last_modified_time", System.currentTimeMillis()); //查重处理规则可能会用到
        this.processSingleResult = DuplicatedProcessingUtils.processDuplicatedBefore(actionContext.getUser(), arg, triggerAction, objectData);
        if (this.processSingleResult.isNeedConfirm()) {
            throw new AcceptableValidateException(buildValidateResult());
        }
        if (objectData.get("IsAddCreatorToTeamMember") != null && objectData.get("IsAddCreatorToTeamMember", Boolean.class)
                && !userId.equals(owner)) {
            TeamMember addTeamMember = new TeamMember(userId, TeamMember.Role.NORMAL_STAFF, TeamMember.Permission.READONLY);
            ObjectDataExt leadsDataExt = ObjectDataExt.of(objectData);
            List<TeamMember> teamMembers = leadsDataExt.getTeamMembers();
            teamMembers.add(addTeamMember);
            leadsDataExt.setTeamMembers(teamMembers);
        }
    }

    protected void fillDefaultValue(LeadsSaveArg arg) {
    }

    @Override
    protected Result after(LeadsSaveArg arg, Result result) {
        super.after(arg, result);
        try {
            ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
            parallelTask.submit(() ->
                    DuplicatedProcessingUtils.processDuplicatedAfter(actionContext.getUser(),
                            objectData, null, processSingleResult));
            parallelTask.run();
        } catch (Exception ex) {
            log.error("parallel execute error:" + ex.getMessage(), ex);
        }

        String leadsPoolId = objectData.get("leads_pool_id", String.class);
        boolean isNewNotifyAdmin = false;
        if (!LeadsUtils.isPoolIdEmpty(leadsPoolId)) {
            IObjectData leadsPool = leadsPoolServiceImpl.getObjectPoolById(actionContext.getTenantId(), leadsPoolId);
            if (leadsPool != null) {
                isNewNotifyAdmin = AccountUtil.getBooleanValue(leadsPool, "is_new_to_notify_admin", false);
                String owner = AccountUtil.getOwner(objectData);
                //创建自动分配任务
                if(StringUtils.isBlank(owner)) {
                    leadsAllocateTaskService.createOrUpdateTask(actionContext.getTenantId(), objectData.getId(), objectData.get("leads_pool_id").toString(), 1);
                    //创建线索分配超时提醒任务
                    leadsAllocateOverTimeTaskService.createOrUpdateTask(actionContext.getTenantId(), objectData.getId(), leadsPool);
                } else {
                    //创建线索超时提醒任务
                    leadsOverTimeTaskService.createOrUpdateTask(actionContext.getTenantId(), objectData.getId(), leadsPool);
                }
                LeadsUtils.updateLeadsPoolsCount(actionContext.getUser(), leadsPoolId);
                addPoolLog(leadsPool, objectData);
            }
        }
        // 发送重算到期时间的task
        recalculateTaskService.send(actionContext.getTenantId(), result.getObjectData().getId(), "LeadsObj", ActionCodeEnum.ADD);
        if (overRuleNotify) {
            if (!User.SUPPER_ADMIN_USER_ID.equals(overRuleNotifyUserId)) {
                String remindContent = String.format(I18N.text(SFA_OBJECT_LIMIT_OVER_RULE_NOTICE),
                        I18N.text("LeadsObj.attribute.self.display_name"), overRulePoolName);
                LeadsUtils.sendCRMNotification(actionContext.getUser(), remindContent, 92, "", objectData.getId(), "", Lists.newArrayList(overRuleNotifyUserId));
            }
        }
        String bizStatus = objectData.get("biz_status", String.class);
        if (isNewNotifyAdmin && bizStatus.equals(LeadsBizStatusEnum.UN_ASSIGNED.getCode())) {
            if (!Strings.isNullOrEmpty(leadsPoolId)) {
                Map<String, List<String>> inAndOutPoolAdminList = leadsPoolServiceImpl.getInAndOutPoolAdminById(actionContext.getUser(), leadsPoolId);
                if (!inAndOutPoolAdminList.isEmpty()) {
                    qiXinTodoService.sendInAndOutTodo(actionContext.getTenantId(), SessionBOCItemKeys.TOBE_ASSIGNED_SALES_CLUE,
                            actionContext.getObjectApiName(), objectData.getId(), actionContext.getUser().getUserId(),
                            inAndOutPoolAdminList.containsKey("in") ? inAndOutPoolAdminList.get("in") : Lists.newArrayList(),
                            inAndOutPoolAdminList.containsKey("out") ? inAndOutPoolAdminList.get("out") : Lists.newArrayList());
                }
            }
        }
        if(!isApprovalFlowStartSuccessOrAsynchronous(objectData.getId()) || isApprovalNotExist()) {
            String owner = AccountUtil.getOwner(objectData);
            if(StringUtils.isNotBlank(owner)) {
                addFlowRecord();
            }
        }

        return result;
    }

    private void addFlowRecord(){
        try {
            String owner = AccountUtil.getOwner(objectData);
            String owner_department = LeadsUtils.getUserMainDepartId(actionContext.getTenantId(), owner);
            IObjectDescribe flowRecordObjDescribe = serviceFacade.findObject(actionContext.getTenantId(), SFAPreDefineObject.LeadsFlowRecord.getApiName());
            IFieldDescribe nameFiled = flowRecordObjDescribe.getFieldDescribe("name");
            List<IObjectData> flowRecordDataList = Lists.newArrayList();
            IObjectData flowRecordObjectData = new ObjectData();
            flowRecordObjectData.set("tenant_id", actionContext.getTenantId());
            flowRecordObjectData.set("record_type", "default__c");
            flowRecordObjectData.set("_id", serviceFacade.generateId());
            flowRecordObjectData.set("leads_id", objectData.getId());
            flowRecordObjectData.set("leads_owner", Lists.newArrayList(owner));
            flowRecordObjectData.set("leads_owner_department", Lists.newArrayList(owner_department));
            flowRecordObjectData.set("flow_type", "add");
            flowRecordObjectData.set("flow_time", System.currentTimeMillis());
            flowRecordObjectData.set("leads_status", objectData.get("biz_status", String.class));
            flowRecordObjectData.set("leads_status_changed_time", System.currentTimeMillis());
            flowRecordObjectData.set("leads_stage", objectData.get("leads_stage"));
            flowRecordObjectData.set("leads_stage_changed_time", objectData.get("leads_stage_changed_time"));
            flowRecordObjectData.set("leads_back_reason", null);
            flowRecordObjectData.set("account_id", null);
            flowRecordObjectData.set("contact_id", null);
            flowRecordObjectData.set("new_opportunity_id", null);
            flowRecordObjectData.set("opportunity_id", null);
            flowRecordObjectData.set("owner", Lists.newArrayList(actionContext.getUser().getUserId()));
            flowRecordObjectData.set("life_status", "normal");
            flowRecordObjectData.set("lock_status", "0");
            flowRecordObjectData.set("package", "CRM");
            flowRecordObjectData.set("object_describe_id", flowRecordObjDescribe != null ? flowRecordObjDescribe.getId() : null);
            flowRecordObjectData.set("object_describe_api_name", SFAPreDefineObject.LeadsFlowRecord.getApiName());
            flowRecordObjectData.set("is_deleted", false);
            if(nameFiled != null && "text".equals(nameFiled.getType())) {
                flowRecordObjectData.set("name", AccountUtil.getNameCode());
            }
            flowRecordDataList.add(flowRecordObjectData);
            if(nameFiled == null || !"text".equals(nameFiled.getType())) {
                autoNumberLogicService.calculateAutoNumberValue(flowRecordObjDescribe, flowRecordDataList);
            }
//        flowRecordDataList = serviceFacade.bulkSaveObjectData(flowRecordDataList, actionContext.getUser());

            List<Map<String, Object>> dataList = Lists.newArrayList();
            Map<String, Object> dataMap = Maps.newHashMap();
            dataMap.put("tenant_id", actionContext.getTenantId());
            dataMap.put("record_type", "default__c");
            dataMap.put("id", serviceFacade.generateId());
            dataMap.put("name", flowRecordObjectData.get("name"));
            dataMap.put("leads_id", objectData.getId());
            dataMap.put("leads_owner", owner);
            dataMap.put("leads_owner_department", owner_department);
            dataMap.put("flow_type", "add");
            dataMap.put("flow_time", System.currentTimeMillis());
            dataMap.put("leads_status", objectData.get("biz_status", String.class));
            dataMap.put("leads_status_changed_time", System.currentTimeMillis());
            dataMap.put("leads_stage", objectData.get("leads_stage"));
            dataMap.put("leads_stage_changed_time", objectData.get("leads_stage_changed_time"));
            dataMap.put("leads_back_reason", null);
            dataMap.put("account_id", null);
            dataMap.put("contact_id", null);
            dataMap.put("new_opportunity_id", null);
            dataMap.put("opportunity_id", null);
            dataMap.put("owner", actionContext.getUser().getUserId());
            dataMap.put("life_status", "normal");
            dataMap.put("lock_status", "0");
            dataMap.put("package", "CRM");
            dataMap.put("object_describe_id", flowRecordObjDescribe != null ? flowRecordObjDescribe.getId() : null);
            dataMap.put("object_describe_api_name", SFAPreDefineObject.LeadsFlowRecord.getApiName());
            dataMap.put("is_deleted", 0);
            dataMap.put("data_own_department", owner_department);
            dataMap.put("version", 1);
            dataMap.put("created_by", actionContext.getUser().getUserId());
            dataMap.put("create_time", System.currentTimeMillis());
            dataMap.put("last_modified_by", actionContext.getUser().getUserId());
            dataMap.put("last_modified_time", System.currentTimeMillis());
            dataList.add(dataMap);

            com.facishare.paas.metadata.api.action.ActionContext context = CommonSqlUtils.convert2ActionContext(actionContext);
            commonSqlService.insert("biz_leads_flow_record", dataList, context);
        } catch (Exception e) {
            log.error("add flowRecord error", e);
            throw new SFABusinessException(SFAErrorCode.ACCOUNT_COMMON_ERROR);
        }
    }

    private void addPoolLog(IObjectData pool, IObjectData objectData) {
        if (pool == null) return;
        SFALogModels.SFALogEntity logEntity = sfaLogService.buildLogEntity(pool,
                String.format("%s ", I18N.text("LeadsObj.attribute.self.display_name")) + objectData.getName(),
                false);
        List<SFALogModels.SFALogTextMessage> textMessageList = Lists.newArrayList();
        sfaLogService.appendNameLinkLogTextMessage(actionContext.getObjectApiName(), objectData, textMessageList);

        logEntity.setLogTextMessageList(textMessageList);
        sfaLogService.addLog(actionContext.getUser(), logEntity, "SalesCluePoolLog",
                SFALogModels.LogOperationType.ADD);
    }

    @Override
    protected LeadsSaveResult buildValidateResult() {
        Result result = super.buildValidateResult();
        LeadsSaveResult saveResult = DuplicatedProcessingUtils.buildResult(result, processSingleResult);
        return saveResult;
    }

    protected LeadsSaveResult buildCleanOwnerValidateResult() {
        Result result = super.buildValidateResult();
        LeadsSaveResult saveResult = new LeadsSaveResult();
        saveResult.setObjectData(result.getObjectData());
        saveResult.setDetails(result.getDetails());
        saveResult.setFuncValidateMessage(result.getFuncValidateMessage());
        saveResult.setIsDuplicate(result.getIsDuplicate());
        saveResult.setValidationRuleMessage(result.getValidationRuleMessage());
        saveResult.setCleanOwner(true);
        return saveResult;
    }
}
