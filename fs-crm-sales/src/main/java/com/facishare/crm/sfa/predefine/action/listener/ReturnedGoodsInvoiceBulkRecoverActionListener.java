package com.facishare.crm.sfa.predefine.action.listener;

import com.facishare.crm.sfa.utilities.proxy.ReturnedGoodsInvoiceProxy;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.ReturnedGoodsInvoiceInterceptorModel;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.salesorderparam.BulkRecoverAfterModel;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.salesorderparam.BulkRecoverBeforeModel;
import com.facishare.crm.sfa.utilities.util.SoCommonUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.metadata.util.SpringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * Created by renlb on 2019/3/14.
 */
@Component
public class ReturnedGoodsInvoiceBulkRecoverActionListener extends StandardBulkRecoverActionListener {

    private static final ReturnedGoodsInvoiceProxy returnedGoodsInvoiceProxy = SpringUtil.getContext().getBean(ReturnedGoodsInvoiceProxy.class);

    @Override
    protected void callBeforeInterceptor(BulkRecoverBeforeModel.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext("ReturnedGoodsInvoiceInterceptor",
                "BulkRecoverBefore");
//        returnedGoodsInvoiceInterceptorService.bulkRecoverBefore(context, arg);
        ReturnedGoodsInvoiceInterceptorModel.BulkRecoverBeforeResult bulkRecoverBeforeResult = returnedGoodsInvoiceProxy.bulkRecoverBefore(arg, SoCommonUtils.getCrmHeader(context.getTenantId(), context.getUser()));
        if(!bulkRecoverBeforeResult.isSuccess()){
            throw new ValidateException(bulkRecoverBeforeResult.getMessage());
        }
    }

    @Override
    protected void callAfterInterceptor(BulkRecoverAfterModel.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext("ReturnedGoodsInvoiceInterceptor",
                "BulkRecoverAfter");
//        returnedGoodsInvoiceInterceptorService.bulkRecoverAfter(context, arg);
        ReturnedGoodsInvoiceInterceptorModel.BulkRecoverAfterResult bulkRecoverAfterResult = returnedGoodsInvoiceProxy.bulkRecoverAfter(arg, SoCommonUtils.getCrmHeader(context.getTenantId(), context.getUser()));
        if(!bulkRecoverAfterResult.isSuccess()){
            throw new ValidateException(bulkRecoverAfterResult.getMessage());
        }
    }
}
