package com.facishare.crm.sfa.predefine.action.listener;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.ModuleCtrlConfigService;
import com.facishare.crm.sfa.predefine.service.modulectrl.IModuleInitService;
import com.facishare.crm.sfa.utilities.proxy.SalesOrderBizProxy;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.SalesOrderInterceptorModel;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.salesorderparam.SalesOrderEditBeforeModel;
import com.facishare.crm.sfa.utilities.util.SalesOrderUtil;
import com.facishare.crm.sfa.utilities.util.SoCommonUtils;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by renlb on 2019/3/13.
 */
@Component
public class SalesOrderEditActionListener implements ActionListener<BaseObjectSaveAction.Arg,BaseObjectSaveAction.Result> {
    private static final SalesOrderBizProxy salesOrderBizProxy = SpringUtil.getContext().getBean(SalesOrderBizProxy.class);

    @Autowired
    ServiceFacade serviceFacade;
    @Autowired
    ModuleCtrlConfigService moduleCtrlConfigService;

    @Override
    public void before(ActionContext actionContext, BaseObjectSaveAction.Arg arg) {
        boolean isMultipleUnit = false;
        if(moduleCtrlConfigService.openStatus(IModuleInitService.MODULE_MULTIPLE_UNIT,actionContext.getUser(),actionContext)){
            isMultipleUnit = true;
        }
        ServiceContext context = ContextManager.buildServiceContext("SalesOrderInterceptor",
                "EditBefore");
        IObjectData objectData = arg.getObjectData().toObjectData();
        IObjectData dbData = serviceFacade.findObjectData(actionContext.getUser(),
                objectData.getId(), Utils.SALES_ORDER_API_NAME);
        if(dbData != null) {
            SalesOrderEditBeforeModel.Arg serviceArg = new SalesOrderEditBeforeModel.Arg();
            ObjectDataExt objectDataExt = ObjectDataExt.of(dbData);
            ObjectLifeStatus originalLifeStatus = objectDataExt.getLifeStatus();
            serviceArg.setNowLifeStatus(originalLifeStatus.getCode());
            arg.getObjectData().put("original_life_status", originalLifeStatus.getCode());
            SalesOrderEditBeforeModel.SalesOrderVo salesOrderVo = new SalesOrderEditBeforeModel.SalesOrderVo();
            salesOrderVo.setCustomerId(dbData.get("account_id", String.class));
            salesOrderVo.setTradeId(objectData.getId());
            String warehouseId = arg.getObjectData().containsKey("shipping_warehouse_id")
                    ? objectData.get("shipping_warehouse_id", String.class)
                    : dbData.get("shipping_warehouse_id", String.class);
            String orderMode = arg.getObjectData().containsKey("order_mode")
                    ? objectData.get("order_mode", String.class)
                    : dbData.get("order_mode", String.class);

            salesOrderVo.setOrderMode(orderMode);
            salesOrderVo.setWarehouseId(warehouseId);
            if (CollectionUtils.notEmpty(arg.getDetails()) && arg.getDetails().get(Utils.SALES_ORDER_PRODUCT_API_NAME) != null) {
                List<ObjectDataDocument> details = arg.getDetails().getOrDefault(Utils.SALES_ORDER_PRODUCT_API_NAME, Lists.newArrayList());
                salesOrderVo.setSalesOrderProductVos(SalesOrderUtil.handleSalesOrderProductVos(details,isMultipleUnit));
            }
            serviceArg.setSalesOrderVo(salesOrderVo);
            SalesOrderInterceptorModel.EditBeforeResult interceptorResult = salesOrderBizProxy.editBefore(serviceArg, SoCommonUtils.getCrmHeader(context.getTenantId(), context.getUser()));
            if(interceptorResult.isSuccess()){
                if(!Strings.isNullOrEmpty(interceptorResult.getData().getWarehouseId())) {
                    arg.getObjectData().put("shipping_warehouse_id", interceptorResult.getData().getWarehouseId());
                }
            }else{
                throw new ValidateException(interceptorResult.getMessage());
            }
        }
    }

    @Override
    public void after(ActionContext actionContext, BaseObjectSaveAction.Arg arg, BaseObjectSaveAction.Result result) {
//        ServiceContext context = ContextManager.buildServiceContext("SalesOrderInterceptor",
//                "EditAfter");
//        SalesOrderEditAfterModel.Arg serviceArg = new SalesOrderEditAfterModel.Arg();
//        IObjectData objectData = result.getObjectData().toObjectData();
//        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
//        List<IObjectData> objectDataList = queryDetailData(actionContext.getTenantId(),
//                Lists.newArrayList(objectData.getId()));
//        serviceArg.setDataId(objectData.getId());
//        serviceArg.setBeforeLifeStatus(arg.getObjectData().toObjectData().get("original_life_status", String.class));
//        serviceArg.setAfterLifeStatus(objectDataExt.getLifeStatus().getCode());
//        SalesOrderEditAfterModel.SalesOrderVo salesOrderVo = new SalesOrderEditAfterModel.SalesOrderVo();
//        salesOrderVo.setCustomerId(objectData.get("account_id", String.class));
//        salesOrderVo.setTradeId(objectData.getId());
//        salesOrderVo.setWarehouseId(objectData.get("shipping_warehouse_id", String.class));
//        List<SalesOrderProductVo> salesOrderProductVoList = Lists.newArrayList();
//        if(CollectionUtils.notEmpty(objectDataList)) {
//            for(IObjectData data : objectDataList) {
//                SalesOrderProductVo salesOrderProductVo = handleSalesOrderProductVo(data);
//                if(salesOrderProductVo != null) {
//                    salesOrderProductVoList.add(salesOrderProductVo);
//                }
//            }
//            salesOrderVo.setSalesOrderProductVos(salesOrderProductVoList);
//        }
//        serviceArg.setSalesOrderVo(salesOrderVo);
//        salesOrderInterceptorService.editAfter(context, serviceArg);
    }
}
