package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.model.Enum.LeadsBizStatusEnum;
import com.facishare.crm.sfa.model.Enum.SessionBOCItemKeys;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.exception.SFABusinessException;
import com.facishare.crm.sfa.predefine.exception.SFAErrorCode;
import com.facishare.crm.sfa.predefine.service.QiXinTodoService;
import com.facishare.crm.sfa.predefine.service.task.LeadsAllocateOverTimeTaskService;
import com.facishare.crm.sfa.predefine.service.task.LeadsOverTimeTaskService;
import com.facishare.crm.sfa.utilities.constant.LeadsConstants;
import com.facishare.crm.sfa.utilities.util.LeadsUtils;
import com.facishare.crmcommon.util.SearchUtil;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.predef.action.StandardBulkDeleteAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class LeadsBulkDeleteAction extends StandardBulkDeleteAction {
    private static final QiXinTodoService qiXinTodoService = SpringUtil.getContext().getBean(QiXinTodoService.class);
    private static final IObjectDataService objectDataService = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);
    LeadsOverTimeTaskService leadsOverTimeTaskService = SpringUtil.getContext().getBean(LeadsOverTimeTaskService.class);
    LeadsAllocateOverTimeTaskService leadsAllocateOverTimeTaskService = SpringUtil.getContext().getBean(LeadsAllocateOverTimeTaskService.class);

    @Override
    protected Result after(Arg arg, Result result) {
        leadsOverTimeTaskService.deleteTask(actionContext.getTenantId(), arg.getIdList());
        //删除线索分配超时提醒任务
        leadsAllocateOverTimeTaskService.deleteTask(actionContext.getTenantId(), arg.getIdList());

        List<String> leadsPoolIds = dataList.stream()
                .filter(l -> !StringUtils.isEmpty(l.get("leads_pool_id", String.class)))
                .map(l -> l.get("leads_pool_id", String.class)).distinct()
                .collect(Collectors.toList());
        LeadsUtils.updateLeadsPoolsCount(actionContext.getUser(), leadsPoolIds);
        addFlowRecord();
        try {
            ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
            task.submit(() -> deleteTodo(arg));
            task.run();
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
        return super.after(arg, result);
    }

    private void deleteTodo(Arg arg) {
        if(dataList != null && dataList.size() >0){
            List<IObjectData> unAssignedDatas = dataList.stream().filter(x->
                    x.get(LeadsConstants.Field.BIZ_STATUS.getApiName()).toString().equals(LeadsBizStatusEnum.UN_ASSIGNED.getCode())).collect(Collectors.toList());
            if(unAssignedDatas != null && unAssignedDatas.size()>0){
                unAssignedDatas.forEach(r->qiXinTodoService.deleteTodo(actionContext.getTenantId(), SFAPreDefineObject.Leads.getApiName(),
                        SessionBOCItemKeys.TOBE_ASSIGNED_SALES_CLUE, r.getId()));
            }
            List<IObjectData> unProcessedDatas = dataList.stream().filter(x->
                    x.get(LeadsConstants.Field.BIZ_STATUS.getApiName()).toString().equals(LeadsBizStatusEnum.UN_PROCESSED.getCode())).collect(Collectors.toList());
            if(unProcessedDatas != null && unProcessedDatas.size()>0){
                unAssignedDatas.forEach(r->qiXinTodoService.deleteTodo(actionContext.getTenantId(), SFAPreDefineObject.Leads.getApiName(),
                        SessionBOCItemKeys.TobeProcessedSalesClue, r.getId()));
            }
        }
    }

    private void addFlowRecord() {
        List<IObjectData> oldFlowRecordDataList = Lists.newArrayList();
        for (IObjectData leadsData : dataList) {
            String oldOwnerId = LeadsUtils.getOwner(leadsData);
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            searchTemplateQuery.setLimit(1);
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, "leads_owner", oldOwnerId);
            SearchUtil.fillFilterEq(filters, "leads_id", leadsData.getId());
            searchTemplateQuery.setFilters(filters);
            List<OrderBy> orderByList = Lists.newArrayList();
            orderByList.add(new OrderBy("last_modified_time", false));
            searchTemplateQuery.setOrders(orderByList);
            searchTemplateQuery.setNeedReturnCountNum(false);
            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(actionContext.getUser(), SFAPreDefineObject.LeadsFlowRecord.getApiName(), searchTemplateQuery);
            if(queryResult != null && org.apache.commons.collections.CollectionUtils.isNotEmpty(queryResult.getData())) {
                IObjectData oldFlowRecordData = queryResult.getData().get(0);
                oldFlowRecordDataList.add(oldFlowRecordData);
            }
        }
        if(CollectionUtils.isNotEmpty(oldFlowRecordDataList)) {
            try {
                serviceFacade.bulkInvalidAndDeleteWithSuperPrivilege(oldFlowRecordDataList, actionContext.getUser());
            } catch (Exception e) {
                log.error("addFlowRecord error", e);
                throw new SFABusinessException(SFAErrorCode.ACCOUNT_COMMON_ERROR);
            }
        }
    }
}
