//package com.facishare.crm.sfa.predefine.controller;
//
//import com.facishare.crm.sfa.utilities.util.JsonObjectUtils;
//import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
//
//public class SubProductDescribeLayoutController extends StandardDescribeLayoutController {
//    @Override
//    protected Result doService(Arg arg) {
//        Result result = super.doService(arg);
//        result = JsonObjectUtils.update(result, Result.class, "$.layout.components[?(@.api_name=='form_component')]" +
//                ".field_section[?(@.api_name=='base_field_section__c')].form_fields[?(@.field_name=='lookup_prod_catalog_id')].is_readonly", true);
//
//        return result;
//    }
//}
