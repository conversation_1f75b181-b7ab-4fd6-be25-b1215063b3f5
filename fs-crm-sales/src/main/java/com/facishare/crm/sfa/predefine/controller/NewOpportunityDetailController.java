package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.util.LeadsUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardDetailController;
import com.facishare.paas.appframework.metadata.ButtonExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.api.describe.IObjectCluster;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.ILayout;

import java.util.Objects;

public class NewOpportunityDetailController extends StandardDetailController {

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        if (newResult.getLayout() == null) {
            return newResult;
        }
        ILayout layout = new Layout(newResult.getLayout());

        IObjectCluster cluster = infraServiceFacade.find(controllerContext.getUser(), arg.getObjectDescribeApiName());
        if (Objects.isNull(cluster) || cluster.getIsActive()) {
            LayoutExt.of(layout).addTopInfoComponentButton(ButtonExt.generateQinxinGroupButton());
        }
        LeadsUtils.handleLeadsRelatedComponents(layout,"LeadsObj_new_opportunity_id_related_list");
        return newResult;
    }
    @Override
    protected boolean defaultEnableQixinGroup() {
        return Boolean.TRUE;
    }
}
