package com.facishare.crm.sfa.predefine.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.predefine.service.model.CrmCommonlyUsedMenuItemModel;
import com.facishare.crm.sfa.predefine.service.model.CrmMenuListArg;
import com.facishare.crm.sfa.predefine.version.VersionService;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.util.SOI18NKeyUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.menu.MenuConstants;
import com.facishare.paas.appframework.metadata.menu.MenuConstants665;
import com.facishare.paas.appframework.metadata.menu.model.MenuItemConfigObject;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.predefine.service.CrmMenuAdminService.getSpecialDisPlayName;

@ServiceModule("common_used_menu_item")
@Component
@Slf4j
public class CommonUsedMenuItemService {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private CrmMenuAdminService crmMenuAdminService;
    @Autowired
    private FunctionPrivilegeService functionPrivilegeService;
    @Autowired
    private CrmMenuActionService crmMenuActionService;
    @Autowired
    private CrmMenuService crmMenuService;
    @Autowired
    VersionService versionService;
    @Autowired
    ConfigService configService;

    private final String ADD = "Add";
    private final String LIST = "List";
    private FsGrayReleaseBiz menuGray = FsGrayRelease.getInstance("menu");

    @ServiceMethod("insert")
    public CrmCommonlyUsedMenuItemModel.Result insert(CrmCommonlyUsedMenuItemModel.Arg arg, ServiceContext context) {
        if (arg.getMenuItems().size() < 3) {
            throw new ValidateException(I18N.text(SOI18NKeyUtils.SO_MENU_ITEM_COMMONUSEDGT3));
        }
        IObjectDescribe commonlyUsedMenuItemDescribe = serviceFacade.findObject(context.getTenantId(), MenuConstants.COMMONLY_USED_MENU_ITEM_API_NAME);
        Set<String> referenceApinameSet = Sets.newHashSet();
        List<IObjectData> objectList = arg.getMenuItems().stream()
                .filter(o -> {
                    if (referenceApinameSet.contains(o.getReferenceApiname())) {
                        return false;
                    } else {
                        referenceApinameSet.add(o.getReferenceApiname());
                        return true;
                    }
                })
                .map(menuItem -> {
                    IObjectData objectData = new ObjectData();
                    objectData.setDescribeApiName(MenuConstants.COMMONLY_USED_MENU_ITEM_API_NAME);
                    objectData.setDescribeId(commonlyUsedMenuItemDescribe.getId());
                    objectData.set(MenuConstants.CommonlyUsedMenuItemField.USER.getApiName(), context.getUser().getUserId());
                    objectData.set(MenuConstants.CommonlyUsedMenuItemField.DISPLAYNAME.getApiName(), menuItem.getDisplayName());
                    objectData.set(MenuConstants.CommonlyUsedMenuItemField.ICON_PATH.getApiName(), menuItem.getIconPathHome());
                    objectData.set(MenuConstants.CommonlyUsedMenuItemField.ORDER_BY.getApiName(), menuItem.getNumber());
                    objectData.setTenantId(context.getUser().getTenantId());
                    objectData.set(MenuConstants.CommonlyUsedMenuItemField.REFERENCEAPINAME.getApiName(), menuItem.getReferenceApiname());
                    return objectData;
                }).collect(Collectors.toList());
        SearchTemplateQuery workbenchDeleteQuery = new SearchTemplateQuery();
        SearchUtil.fillFilterEq(workbenchDeleteQuery.getFilters(), MenuConstants.CommonlyUsedMenuItemField.TENANTID.getApiName(), context.getTenantId());
        SearchUtil.fillFilterEq(workbenchDeleteQuery.getFilters(), MenuConstants.CommonlyUsedMenuItemField.USER.getApiName(), context.getUser().getUserId());
        crmMenuActionService.deleteBySearchTemplate(context.getUser(), MenuConstants.COMMONLY_USED_MENU_ITEM_API_NAME, workbenchDeleteQuery);
        serviceFacade.bulkSaveObjectData(objectList, context.getUser());
        return CrmCommonlyUsedMenuItemModel.Result.builder().result(true).build();
    }

    @ServiceMethod("query")
    public CrmCommonlyUsedMenuItemModel.Result query(ServiceContext context) {
        Boolean isDefault = false;
        User user = context.getUser();
        Map<String, MenuItemConfigObject> configMenuItemMap = crmMenuAdminService.getConfigMenuItemMapByTenantId(context.getUser().getTenantId());

        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(1000);
        List<IFilter> filters = searchQuery.getFilters();
        SearchUtil.fillFilterEq(filters, MenuConstants.CommonlyUsedMenuItemField.TENANTID.getApiName(), context.getTenantId());
        SearchUtil.fillFilterEq(filters, MenuConstants.CommonlyUsedMenuItemField.USER.getApiName(), user.getUserId());
        QueryResult<IObjectData> commonlyUsedMenuItemResult = serviceFacade.findBySearchQuery(user, MenuConstants.COMMONLY_USED_MENU_ITEM_API_NAME, searchQuery);

        //获取所有的菜单项
        List<IObjectData> supportIObjectData = crmMenuService.findAllMenuItemApiName(user);
//        SearchTemplateQuery searchRoleQuery = new SearchTemplateQuery();
//        searchRoleQuery.setLimit(1000);
//        //用户的角色
//        List<String> roleIdList = serviceFacade.getUserRole(user);
//        if (CollectionUtils.isEmpty(roleIdList)) {
//            log.warn("findUserMenuList user role not exist,tenantId {},userId {}", user.getTenantId(), user.getUserId());
//            throw new ValidateException(I18N.text(SOI18NKeyUtils.SO_MENU_ROLENOTEXIST));
//        }
//        List<IFilter> roleFilters = searchRoleQuery.getFilters();
//        SearchUtil.fillFilterIn(roleFilters, MenuConstants.RoleSourceField.ROLE_ID.getApiName(), roleIdList);
//        //查询菜单资源类型的
//        SearchUtil.fillFilterEq(roleFilters, MenuConstants.RoleSourceField.SOURCETYPE.getApiName(), MenuConstants.RoleSourceField.SOURCETYPE_MENU.getApiName());
//        QueryResult<IObjectData> roleSourceList = serviceFacade.findBySearchQuery(user, MenuConstants.ROLE_SOURCE_API_NAME, searchRoleQuery);
//        List<String> menuIdsList = roleSourceList.getData().stream()
//                .map(k -> k.get(MenuConstants.RoleSourceField.SOURCEID.getApiName(), String.class)).collect(Collectors.toList());
//        supportIObjectData = supportIObjectData.stream()
//                .filter(x->menuIdsList.contains(x.get(MenuConstants.MenuItemField.MENUID.getApiName(),String.class))).collect(Collectors.toList());
        Set<String> supportApiNameList = supportIObjectData.stream()
                .map(k -> k.get(MenuConstants.MenuItemField.REFERENCEAPINAME.getApiName(), String.class)).collect(Collectors.toSet());
        supportApiNameList.remove(null);

        //根据版本过滤到不支持的对象
        versionService.filterSupportObj(user.getTenantId(), supportApiNameList);

        Set<String> commonApiNames = Sets.newHashSet();
        Map<String, IObjectDescribe> describeMap;
        List<CrmCommonlyUsedMenuItemModel.MenuItem> menuItemList = Lists.newArrayList();
        if (commonlyUsedMenuItemResult.getData().isEmpty()) {
            isDefault = true;
            commonApiNames.addAll(Sets.newHashSet("CrmRemind", "DataBoard", "LeadsObj", "AccountObj", "ContactObj", "OpportunityObj", "CrmInfo"));
            describeMap = crmMenuAdminService.findDescribeListByApiNamesWithoutFields(user, new ArrayList(commonApiNames), ObjectAction.VIEW_LIST.getActionCode());
            Map<String, Map<String, Boolean>> needCheckPrivilegeDescribe = batchFunPrivilegeCheck(user, Lists.newArrayList("LeadsObj","AccountObj","ContactObj","OpportunityObj"),
                    Lists.newArrayList(ObjectAction.VIEW_LIST.getActionCode(), ObjectAction.CREATE.getActionCode()));
            menuItemList.addAll(defaultCommonlyUsedMenuItem(describeMap, supportApiNameList,needCheckPrivilegeDescribe,context));
        } else {
            commonlyUsedMenuItemResult.getData().forEach(o -> commonApiNames.add(o.get(MenuConstants.CommonlyUsedMenuItemField.REFERENCEAPINAME.getApiName(), String.class)));
            //附近客户依赖客户权限，目标完成情况依赖目标值
            commonApiNames.add("AccountObj");
            commonApiNames.addAll(MenuConstants.privilegeActionListDepends.keySet());
            describeMap = crmMenuAdminService.findDescribeListByApiNamesWithoutFields(user, new ArrayList(commonApiNames), ObjectAction.VIEW_LIST.getActionCode());
            Set<String> needCheckPrivilegeApiNames = Sets.newHashSet();
            commonApiNames.stream().forEach(apiName -> {
                MenuItemConfigObject configObject = configMenuItemMap.get(apiName);
                if (configObject != null) {
                    if (configObject.getValidatePrivilege()) {
                        needCheckPrivilegeApiNames.add(apiName);
                    }
                } else {
                    //自定义对象
                    needCheckPrivilegeApiNames.add(apiName);
                }
            });
            Map<String, Map<String, Boolean>> needCheckPrivilegeDescribe = batchFunPrivilegeCheck(user, new ArrayList<>(needCheckPrivilegeApiNames),
                    Lists.newArrayList(ObjectAction.VIEW_LIST.getActionCode(), ObjectAction.CREATE.getActionCode()));

            Boolean isShowServiceManager = crmMenuService.isShowServiceManager(user);
            Boolean isShowReport = crmMenuService.isShowReport(user);
            Map<String, String> defaultMap;
            if (RequestUtil.isMobileRequestBeforeVersion("670000")) {
                defaultMap = JSON.parseArray(MenuConstants665.specialConfigMenuItem, CrmMenuListArg.MenuItem.class).stream()
                        .filter(x -> !menuGray.isAllow("SaleRecord_rule", context.getUser().getTenantId()))
                        .collect(Collectors.toMap(CrmMenuListArg.MenuItem::getReferenceApiname, CrmMenuListArg.MenuItem::getIconPathHome));
            } else {
                defaultMap = JSON.parseArray(MenuConstants.specialConfigMenuItem, CrmMenuListArg.MenuItem.class).stream()
                        .filter(x -> !menuGray.isAllow("SaleRecord_rule", context.getUser().getTenantId()))
                        .collect(Collectors.toMap(CrmMenuListArg.MenuItem::getReferenceApiname, CrmMenuListArg.MenuItem::getIconPathHome));
            }
            Map<String, Map<String, String>> iconPaths = crmMenuAdminService.getIconPaths(context);
            commonlyUsedMenuItemResult.getData().stream()
                    .filter(k -> {
                        //服务管理特殊处理
                        return !"CrmServiceManager".equals(k.get(MenuConstants.CommonlyUsedMenuItemField.REFERENCEAPINAME.getApiName(), String.class)) || isShowServiceManager;
                    })
                    //报表特殊处理
                    .filter(k -> !"Report".equals(k.get(MenuConstants.CommonlyUsedMenuItemField.REFERENCEAPINAME.getApiName(), String.class)) || isShowReport)
                    //过滤版本，Crm提醒不过滤
                    .filter(k -> checkVersion(supportApiNameList, k.get(MenuConstants.CommonlyUsedMenuItemField.REFERENCEAPINAME.getApiName(), String.class)))
                    //验证describe
                    .filter(o -> validateDescribe(o, configMenuItemMap,
                            describeMap.getOrDefault(o.get(MenuConstants.CommonlyUsedMenuItemField.REFERENCEAPINAME.getApiName(), String.class), new ObjectDescribe())))
                    //验证功能权限
                    .filter(o -> validatePrivilege(o, configMenuItemMap, needCheckPrivilegeDescribe))
                    .forEach(o -> {
                        String referenceApiName = o.get(MenuConstants.CommonlyUsedMenuItemField.REFERENCEAPINAME.getApiName(), String.class);
                        MenuItemConfigObject menuItemConfigObject = configMenuItemMap.getOrDefault(referenceApiName,
                                new MenuItemConfigObject(true, true, MenuConstants.DEVICE_TYPE_ALL));
                        Map<String, Boolean> checkPrivilege = needCheckPrivilegeDescribe.get(referenceApiName);
                        IObjectDescribe objectDescribe = describeMap.getOrDefault(referenceApiName, new ObjectDescribe());
                        CrmCommonlyUsedMenuItemModel.MenuItem menuItem = CrmCommonlyUsedMenuItemModel.MenuItem.builder()
                                .displayName(getSpecialDisPlayName(referenceApiName, objectDescribe.getDisplayName(),
                                        o.get(MenuConstants.CommonlyUsedMenuItemField.DISPLAYNAME.getApiName(), String.class)))
                                .referenceApiname(referenceApiName)
                                .iconPathHome(String.valueOf(Optional.ofNullable(emptyElse(menuItemConfigObject.getIconPathHome(),
                                        objectDescribe.getIconPath()))
                                        .orElse(defaultMap.get(referenceApiName))))
                                .number(o.get(MenuConstants.CommonlyUsedMenuItemField.ORDER_BY.getApiName(), Integer.class))
                                .privilegeAction(fillPrivilege(checkPrivilege, referenceApiName,objectDescribe)).build();
                        if (Objects.nonNull(menuItemConfigObject.getMobileConfig())||Objects.equals(referenceApiName,"CrmToDo")) {
                            fillMobileConfig(menuItem, menuItemConfigObject.getMobileConfig(),context);
                        }
                        menuItem = convertIconPath(menuItem, objectDescribe, iconPaths);
                        if (ObjectUtils.notEqual(referenceApiName, "SaleRecord") || RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_650) || !menuGray.isAllow("SaleRecord_rule", context.getUser().getTenantId())) {
                            menuItemList.add(menuItem);
                        }
                    });
        }

        //其他信息
        Map<String, Object> homePermissions = crmMenuAdminService.getHomePermissions(context, StopWatch.create("commonUsedMenuItemService"));

        //增加是否开启合作夥伴
        String configPartnerIsOpen = configService.findTenantConfig(context.getUser(), "config_partner_open");
        if (!StringUtils.isBlank(configPartnerIsOpen) && "open".equals(configPartnerIsOpen)) {
            homePermissions.put("IsOpenPartner", true);
        } else {
            homePermissions.put("IsOpenPartner", false);
        }
        Set<String> referenceApinameSet = Sets.newHashSet();
        menuItemList.removeIf(o -> {
            if (!referenceApinameSet.contains(o.getReferenceApiname())) {
                referenceApinameSet.add(o.getReferenceApiname());
                return Boolean.FALSE;
            } else {
                return Boolean.TRUE;
            }
        });
        return CrmCommonlyUsedMenuItemModel.Result.builder().result(true).isDefault(isDefault).menuItems(menuItemList).configinfo(homePermissions).build();
    }

    private boolean checkVersion(Set<String> supportApiNameList, String apiName) {
        return (!StringUtils.equals(apiName, "CrmInfo") && MenuConstants.apiNamesNoCheckVersion.contains(apiName))
                || supportApiNameList.contains(apiName);
    }

    private CrmCommonlyUsedMenuItemModel.MenuItem convertIconPath(CrmCommonlyUsedMenuItemModel.MenuItem menuItem, IObjectDescribe objectDescribe, Map<String, Map<String, String>> iconPaths) {
        if (ObjectUtils.isNotEmpty(objectDescribe)
                && ObjectUtils.isNotEmpty(objectDescribe.getDefineType())
                && objectDescribe.getDefineType().equals(IObjectDescribe.DEFINE_TYPE_CUSTOM)) {//自定义对象
            Integer index = objectDescribe.getIconIndex();
            if (ObjectUtils.isEmpty(index)) {
                return menuItem;
            }
            Map<String, String> iconPath = iconPaths.get(String.valueOf(index));
            menuItem.setIconPathHome(MapUtils.getString(iconPath, "iconHomePath", ""));
        } else {
            Map<String, String> iconPath = iconPaths.get(menuItem.getReferenceApiname());
            if (MapUtils.isNotEmpty(iconPath)) {
                menuItem.setIconPathHome(MapUtils.getString(iconPath, "iconHomePath", ""));
            }
        }
        return menuItem;
    }

    private List<CrmCommonlyUsedMenuItemModel.MenuItem> defaultCommonlyUsedMenuItem(Map<String, IObjectDescribe> describeMap, Set<String> supportApiNameList, Map<String, Map<String, Boolean>> needCheckPrivilegeDescribe, ServiceContext context) {
        String defaultCommonUsedMenuItem;
        if (RequestUtil.isMobileRequestBeforeVersion("670000")) {
            defaultCommonUsedMenuItem = MenuConstants665.defaultCommonUsedMenuItem;
        } else {
            defaultCommonUsedMenuItem = MenuConstants.defaultCommonUsedMenuItem;

        }
        List<CrmCommonlyUsedMenuItemModel.MenuItem> defaultMenuItemList = JSON.parseArray(defaultCommonUsedMenuItem, CrmCommonlyUsedMenuItemModel.MenuItem.class);
//        defaultMenuItemList.removeIf(o -> MenuConstants.defaultNeedCheckPrivilege.contains(o.getReferenceApiname())
//                && (!describeMap.containsKey(o.getReferenceApiname()) || !supportApiNameList.contains(o.getReferenceApiname())));
        defaultMenuItemList.removeIf(o -> !checkVersion(supportApiNameList,o.getReferenceApiname()));
        defaultMenuItemList.forEach(o -> {
            MenuItemConfigObject menuItemConfigObject = MenuConstants.configMenuItemMap.getOrDefault(o.getReferenceApiname(),
                    new MenuItemConfigObject(true, true, MenuConstants.DEVICE_TYPE_ALL));
            if (Objects.nonNull(menuItemConfigObject.getMobileConfig())) {
                fillMobileConfig(o, menuItemConfigObject.getMobileConfig(), context);
            }
            Map<String, Boolean> checkPrivilege = needCheckPrivilegeDescribe.get(o.getReferenceApiname());
            o.setPrivilegeAction(fillPrivilege(checkPrivilege, o.getReferenceApiname(), describeMap.getOrDefault(o.getReferenceApiname(), new ObjectDescribe())));
            String specialDisPlayName = getSpecialDisPlayName(o.getReferenceApiname());

            if (describeMap.get(o.getReferenceApiname()) != null) {
                o.setDisplayName(describeMap.get(o.getReferenceApiname()).getDisplayName());
            } else if (Strings.isNotBlank(specialDisPlayName)) {
                o.setDisplayName(specialDisPlayName);

            }
        });
        return defaultMenuItemList;
    }

    private Object emptyElse(Object var1, Object var2) {
        if (var1 instanceof String) {
            return StringUtils.isNotBlank(var1.toString()) ? var1 : var2;
        } else {
            return Objects.nonNull(var1) ? var1 : var2;
        }
    }

    private Map<String, Map<String, Boolean>> batchFunPrivilegeCheck(User user, List apiNames, List actionCodes) {
        Map<String, Map<String, Boolean>> map = functionPrivilegeService.batchFunPrivilegeCheck(user, apiNames, actionCodes);
        JSONObject privilegeActionListDepends = MenuConstants.privilegeActionListDepends;
        privilegeActionListDepends.keySet().forEach(key -> {
            Optional.ofNullable(map.get(key)).filter(k -> k.get(ObjectAction.VIEW_LIST.getActionCode())).ifPresent(m -> {
                JSONArray needDependsApiNames = privilegeActionListDepends.getJSONArray(key);
                Optional.ofNullable(needDependsApiNames).get().forEach(needDependsApiName -> {
                    Map<String, Boolean> newHashMap = Maps.newHashMap();
                    newHashMap.put(ObjectAction.VIEW_LIST.getActionCode(), true);
                    map.put(needDependsApiName.toString(), newHashMap);
                });
            });
        });
        return map;
    }

    private List<String> fillPrivilege(Map<String, Boolean> privillege, String apiName, IObjectDescribe objectDescribe) {
        List<String> result = Lists.newArrayList();
        if (privillege == null || privillege.isEmpty()) {
            result.add(LIST);
            return result;
        }
        if (Objects.nonNull(privillege.get(LIST)) && privillege.get(LIST)) {
            result.add(LIST);
        }
        if (Objects.nonNull(privillege.get(ADD)) && privillege.get(ADD)) {
            String quickAddExcludeApiConfig = MenuConstants.quickAddExcludeDevice.getOrDefault(apiName, MenuConstants.DEVICE_TYPE_ALL);
            if (MenuConstants.DEVICE_TYPE_ALL.equals(quickAddExcludeApiConfig) || "mobile".equals(quickAddExcludeApiConfig)) {
                if (ObjectUtils.isNotEmpty(objectDescribe) && ObjectDescribeExt.of(objectDescribe).isHideButton()) {

                } else {
                    result.add(ADD);
                }
            }
        }
        if (result.isEmpty()) {
            result.add(LIST);
        }
        return result;
    }

    private void fillMobileConfig(CrmCommonlyUsedMenuItemModel.MenuItem menuItem, MenuItemConfigObject.MobileConfig config, ServiceContext context) {
        CrmMenuListArg.MobileConfig mobileConfig = new CrmMenuListArg.MobileConfig();
        if (Objects.equals(menuItem.getReferenceApiname(), "CrmToDo")) {
            CrmMenuListArg.MenuItem mobileSpecialMenuItem = crmMenuService.getMobileSpecialMenuItemByApiName(context, "CrmToDo");
            if (ObjectUtils.isNotEmpty(mobileSpecialMenuItem)) {
                mobileConfig.setAddAction(mobileSpecialMenuItem.getMobileConfig().getAddAction());
                mobileConfig.setListAction(mobileSpecialMenuItem.getMobileConfig().getListAction());
            }
            menuItem.setMobileConfig(mobileConfig);
        }else {
            mobileConfig.setAddAction(config.getMobileAddAction());
            mobileConfig.setListAction(config.getMobileListAction());
        }
        menuItem.setMobileConfig(mobileConfig);
    }

    private Boolean validateDescribe(IObjectData iObjectData, Map<String, MenuItemConfigObject> configMenuItemMap, IObjectDescribe objectDescribe) {
        //如果配置项相应的对象为空，是自定义对象
        MenuItemConfigObject menuItemConfigObject = configMenuItemMap.getOrDefault(iObjectData.get(MenuConstants.CommonlyUsedMenuItemField.REFERENCEAPINAME.getApiName(), String.class),
                new MenuItemConfigObject(true, true, MenuConstants.DEVICE_TYPE_ALL));
        //预制的crm对象，并且配置中不存在该对象时，则过滤
        if (IObjectDescribe.DEFINE_TYPE_PACKAGE.equals(objectDescribe.getDefineType()) && StringUtils.isEmpty(menuItemConfigObject.getApiName())) {
            return false;
        }
        //自定义对象默认校验describe
        Boolean needValidateDescribe = menuItemConfigObject == null ? true : menuItemConfigObject.getValidateDescribe();
        if (MenuConstants.apiNamesNoCheckVersion.contains(iObjectData.get(MenuConstants.CommonlyUsedMenuItemField.REFERENCEAPINAME.getApiName(), String.class))) {
            return true;
        }
        //验证describe
        if (needValidateDescribe && (objectDescribe.get(IObjectDescribe.IS_ACTIVE, Boolean.class) == null || !objectDescribe.isActive())) {
            return false;
        }
        return true;
    }

    private Boolean validatePrivilege(IObjectData iObjectData, Map<String, MenuItemConfigObject> configMenuItemMap, Map<String, Map<String, Boolean>> needCheckPrivilegeDescribe) {
        String apiName = iObjectData.get(MenuConstants.CommonlyUsedMenuItemField.REFERENCEAPINAME.getApiName(), String.class);
        MenuItemConfigObject menuItemConfigObject = configMenuItemMap.get(apiName);
        //自定义对象默认校验权限
        Boolean needValidatePrivilege = menuItemConfigObject == null ? true : menuItemConfigObject.getValidatePrivilege();
        if (MenuConstants.apiNamesNoCheckVersion.contains(apiName) && !"NearByCustomer".equals(apiName)) {
            needValidatePrivilege = Boolean.FALSE;
        }
        //判断权限
        if (needValidatePrivilege && !needCheckPrivilegeDescribe.get("NearByCustomer".equals(apiName) ? "AccountObj" : apiName)
                .get(ObjectAction.VIEW_LIST.getActionCode())) {
            return false;
        }
        return true;
    }

}
