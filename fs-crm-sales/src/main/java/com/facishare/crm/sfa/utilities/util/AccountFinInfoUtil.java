package com.facishare.crm.sfa.utilities.util;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.constant.AccountFinInfoConstants;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.ServiceFacadeImpl;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * 客户财务信息公共类 class
 *
 * <AUTHOR>
 * @date 2019/5/16
 */
@Slf4j
public class AccountFinInfoUtil {
    private static final ServiceFacade SERVICE_FACADE = SpringUtil.getContext().getBean(ServiceFacadeImpl.class);

    /**
     * 财务信息没有默认找第一条作为默认
     *
     * @param validList
     * @param user
     */
    public static void importCustomDefaultValue(List<IObjectData> validList, User user) {
        List<String> accountIds = validList.stream().filter(m -> !Strings.isNullOrEmpty(m.get(AccountFinInfoConstants.Field.ACCOUNT_ID.getApiName(), String.class)))
                .map(n -> n.get(AccountFinInfoConstants.Field.ACCOUNT_ID.getApiName(), String.class))
                .distinct()
                .collect(Collectors.toList());
        QueryResult<IObjectData> queryResult = getObjectDataList(user, accountIds);
        if (queryResult != null && queryResult.getTotalNumber() > 0 && !CollectionUtils.isEmpty(queryResult.getData())) {
            List<IObjectData> accountFinInfoList = queryResult.getData();
            for (String accountId : accountIds) {
                //region 默认财务信息校验
                Optional<IObjectData> accountFinInfoDefault = accountFinInfoList.stream()
                        .filter(m -> accountId.equals(m.get(AccountFinInfoConstants.Field.ACCOUNT_ID.getApiName(), String.class))
                                && m.get(AccountFinInfoConstants.Field.IS_DEFAULT.getApiName(), Boolean.class)).findFirst();
                if (!accountFinInfoDefault.isPresent()) {
                    Optional<IObjectData> accountFinInfoDefaultForExcel = validList.stream()
                            .filter(m -> accountId.equals(m.get(AccountFinInfoConstants.Field.ACCOUNT_ID.getApiName(), String.class))).findFirst();
                    if (accountFinInfoDefaultForExcel.isPresent()) {
                        accountFinInfoDefaultForExcel.get().set(AccountFinInfoConstants.Field.IS_DEFAULT.getApiName(), true);
                    }
                }
            }
        }
    }

    public static QueryResult<IObjectData> getObjectDataList(User user, List<String> accountIds) {
        QueryResult<IObjectData> queryResult = null;
        if (!CollectionUtils.isEmpty(accountIds)) {
            SearchTemplateQueryExt ext = SearchTemplateQueryExt.of(new SearchTemplateQuery());
            ext.addFilter(Operator.IN, AccountFinInfoConstants.Field.ACCOUNT_ID.getApiName(), accountIds);
            queryResult = SERVICE_FACADE.findBySearchQuery(user, SFAPreDefineObject.AccountFinInfo.getApiName(), (SearchTemplateQuery) ext.getQuery());
        }
        return queryResult;
    }


    public static List<String> getImportTemplateRemoveFields() {
        return Lists.newArrayList(
                "is_default", "lock_status", "life_status", "owner_department"
        );
    }
}
