package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.utilities.constant.NewOppportunityConstants;
import com.facishare.crm.sfa.utilities.util.PriceBookUtil;
import com.facishare.crm.sfa.utilities.validator.QuoteImportValidator;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportDataAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.List;

public class NewOpportunityLinesInsertImportDataAction extends StandardInsertImportDataAction {
    protected static final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);

    @Override
    protected void customDefaultValue(List<IObjectData> validList) {
        super.customDefaultValue(validList);
        //价目表字段调整为非必填，不再补标准价目表
//        boolean priceBookEnabled = bizConfigThreadLocalCacheService.isPriceBookEnabled(actionContext.getTenantId());
//        PriceBookUtil.detailImportDefaultValue(actionContext.getTenantId(), validList, priceBookEnabled);
    }

    @Override
    protected void customValidate(List<ImportData> dataList) {
        super.customValidate(dataList);
        List<ImportError> errorList = Lists.newArrayList();
        QuoteImportValidator.customValidate(objectDescribe
                , SFAPreDefineObject.NewOpportunity.getApiName()
                , NewOppportunityConstants.NewOpportunityLinesField.NEW_OPPORTUNITY_ID.getApiName()
                , actionContext, errorList, dataList, Boolean.TRUE
        );
        mergeErrorList(errorList);
    }
}
