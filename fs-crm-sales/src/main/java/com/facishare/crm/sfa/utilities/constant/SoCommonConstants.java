package com.facishare.crm.sfa.utilities.constant;

/**
 * <AUTHOR>
 * @date 2019/9/2 10:55
 */
public interface SoCommonConstants {

    /**
     * 请求来源  订货通
     */
    String REQUEST_RESOURCE_DINGHUOTONG = "DingHuoTong";


    /**
     * 审计日志
     */
    String SALES_ORDER_VIP_AUDIT_LOG = "sales_vip_audit_log";


    /**
     * 自由流发送代办
     */
    String SALES_ORDER_SEND_TODO = "sales_order_send_todo";

    /**
     * 新开票灰度字段禁用企业
     * 需要加审批
     */
    String SALES_ORDER_NEW_INVOICE = "sales_order_new_invoice";


    /**
     * 导入的子产品分组
     * 如果是灰度bom的企业，下发新的分组导入
     */
    String PRODUCT_GROUP_IMPORT = "product_group_import";


    /**
     * bom v3 版本 灰度的Key
     */
    String BOM_V3 = "bom_v3";



}
