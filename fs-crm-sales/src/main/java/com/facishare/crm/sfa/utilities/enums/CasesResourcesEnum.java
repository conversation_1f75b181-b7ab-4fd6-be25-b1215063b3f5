package com.facishare.crm.sfa.utilities.enums;

import java.util.Optional;

/**
 * 工单对象来源字段枚举
 * created by xiexd on 2019年5月10日
 */
public enum CasesResourcesEnum {

	TELEPHONE("1", "电话"),
    WECHAT("2", "微信"),
    EMAIL("3", "Email"),
    WEB("4", "网页端"),
    FORUM("5", "论坛"),
    OTHER("6", "其他"),
    PREVENTIVE_MAINTENANCE_DETAIL("7", "维保计划")
    ;

    private String code;
    private String description;
    
    public static Optional<CasesResourcesEnum> findByIndex(String code) {
    	CasesResourcesEnum[] statuses = CasesResourcesEnum.values();
        for (CasesResourcesEnum status : statuses) {
            if (status.getCode().equals(code)) {
                return Optional.of(status);
            }
        }
        return Optional.empty();
    }
    
    CasesResourcesEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return "CasesSettleStatusEnum{" +
                "code=" + code +
                ", description='" + description + '\'' +
                '}';
    }
}
