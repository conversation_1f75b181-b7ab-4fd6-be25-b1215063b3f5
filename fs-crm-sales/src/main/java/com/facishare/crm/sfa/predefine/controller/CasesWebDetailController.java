//package com.facishare.crm.sfa.predefine.controller;
//
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//
//import org.apache.commons.lang3.StringUtils;
//
//import com.facishare.crm.feesettlement.constants.CasesConstant;
//import com.facishare.crm.feesettlement.predefine.manager.CasesDeviceManager;
//import com.facishare.crm.feesettlement.predefine.manager.CasesManager;
//import com.facishare.crm.sfa.utilities.util.CaseConfigUtils;
//import com.facishare.crm.supplier.util.HttpClientUtil;
//import com.facishare.paas.I18N;
//import com.facishare.paas.appframework.common.util.CollectionUtils;
//import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
//import com.facishare.paas.appframework.metadata.LayoutExt;
//import com.facishare.paas.appframework.metadata.layout.LayoutAgentType;
//import com.facishare.paas.appframework.metadata.layout.LayoutStructure;
//import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
//import com.facishare.paas.metadata.impl.ui.layout.component.RelatedObjectList;
//import com.facishare.paas.metadata.ui.layout.IComponent;
//import com.facishare.paas.metadata.ui.layout.ILayout;
//import com.facishare.paas.metadata.util.SpringUtil;
//import com.google.common.collect.Lists;
//import com.google.gson.Gson;
//
///**
// * 新版详情页,工单需要根据是否配置模板判断是否有工单信息总览tab
// * Created by dingc on 2020/4/16.
// */
//public class CasesWebDetailController extends StandardWebDetailController {
//
//	private CasesDeviceManager casesDeviceManager = SpringUtil.getContext().getBean(CasesDeviceManager.class);
//	private CasesManager casesManager = SpringUtil.getContext().getBean(CasesManager.class);
//
//    @Override
//    protected Result after(Arg arg, Result result) {
//
//        // 如果打开了工单信息总览, 那么在Component中添加
//        addTemplateComponentIfNeed(arg, result);
//
//        // 处理更新工单设备地址按钮
//        casesDeviceManager.handleUpdateCasesDeviceAddressButtonForWebDetailIfNeed(controllerContext.getUser(), result);
//
//        // 处理【领取工单】按钮
//        casesManager.handleReceiveCasesButtonForWebDetailIfNeed(controllerContext.getUser(), result);
//
//        // 下游详情页隐藏工单相关信息展示组件
//        hiddenCasesRelatedInfoComponentForDownstream(result);
//
//        return super.after(arg, result);
//    }
//
//	@Override
//    protected ILayout getLayout() {
//        ILayout layout = super.getLayout();
//
//        try {
//
//            // 工单开启多设备，隐藏设备字段
//            casesDeviceManager.handleMultipleCasesDeviceForDetailLayoutIfNeed(
//            		controllerContext.getUser().getTenantId(), layout);
//        } catch (Exception e) {
//
//            log.warn("getComponents error.", e);
//        }
//        return layout;
//    }
//
//    /**
//     * 添加tab(工单信息总览),自定义的web页面
//     * @param arg
//     * @param result
//     */
//    @SuppressWarnings({ "rawtypes", "unchecked" })
//	private void addTemplateComponentIfNeed(Arg arg, Result result) {
//        try {
//
//        	// 终端详情页不展示工单信息总览
//        	if(LayoutAgentType.MOBILE.getCode().equals(arg.getLayoutAgentType())) {
//        		return;
//        	}
//
//            //根据数据的业务类型,和企业号,去服务通查询是否配置了模板
//            String recordType = (String)result.getData().get("record_type");
//            String tenantId = controllerContext.getUser().getTenantId();
//            String url = CaseConfigUtils.getQueryHasTemplateUrl();
//            Map<String, Object> params = new HashMap<>();
//            Map<String, Object> innerparam = new HashMap<>();
//            innerparam.put("recordType", recordType);
//            innerparam.put("tenantId", tenantId);
//            params.put("arg1", innerparam);
//
//            String paramsJson = new Gson().toJson(params);
//			String queryResult = HttpClientUtil.httpPostJsonRequest(url, new HashMap(), paramsJson);
//            log.info("query hasTemplate, arg: {}, result: {}", paramsJson, queryResult);
//
//            Map<String, Object> resultMap = new Gson().fromJson(queryResult, Map.class);
//
//
//            boolean hasTemplate = false;
//            if (resultMap.containsKey("data")) {
//                hasTemplate = (Boolean)resultMap.get("data");
//            }
//
//            if (hasTemplate) {
//
//            	if(null == result || null == result.getLayout()) {
//            		return ;
//            	}
//                ILayout layout = result.getLayout().toLayout();
//                RelatedObjectList component = new RelatedObjectList();
//                component.setName("workorderinfos");
//                component.setHeader(I18N.text("cases.all.info.view"));
//                component.setType(IComponent.TYPE_FORM);
//                component.setOrder(2);
//                component.setIsHidden(false);
//                layout.addComponent(component);
//
//                addComponent2Structure(layout, Lists.newArrayList("workorderinfos"));
//            }
//
//
//        } catch (Exception e) {
//            log.warn("Fail to addIFrameComponentIfNeed, ", e);
//        }
//    }
//
//    protected void addComponent2Structure(ILayout layout, List<String> componentApiNameList) {
//        if (layout == null || CollectionUtils.empty(componentApiNameList)) {
//            return;
//        }
//        try {
//            List<IComponent> componentList = layout.getComponents().stream()
//                    .filter(x -> componentApiNameList.contains(x.getName())).collect(Collectors.toList());
//            if (CollectionUtils.notEmpty(componentList)) {
//                LayoutExt layoutExt = LayoutExt.of(layout);
//                LayoutStructure.addComponentsToLeftStructure(layoutExt, componentList);
//            }
//        } catch (Exception e) {
//            log.warn("addComponent2Structure component error", e);
//        }
//    }
//
//    private void hiddenCasesRelatedInfoComponentForDownstream(Result result) {
//
//    	try {
//
//    		// 下游详情页隐藏工单相关信息展示组件
//    		String outTenantId = controllerContext.getUser().getOutTenantId();
//    		String outUserId = controllerContext.getUser().getOutUserId();
//        	if(StringUtils.isBlank(outTenantId) || StringUtils.isBlank(outUserId)) {
//        		return;
//        	}
//
//    		WebDetailLayout webDetailLayout = WebDetailLayout.of(result.getLayout().toLayout());
//
//    		// 隐藏工单相关信息展示组件
//    		webDetailLayout.removeComponents(Lists.newArrayList(CasesConstant.ESERVICE_CASES_RELATED_INFO_COMPONENT));
//
//		} catch (Exception e) {
//			log.warn("failed to hiddenCasesRelatedInfoComponent, result={}", result, e);
//		}
//    }
//}
