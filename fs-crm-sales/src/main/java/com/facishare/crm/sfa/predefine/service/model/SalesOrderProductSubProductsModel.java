package com.facishare.crm.sfa.predefine.service.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface SalesOrderProductSubProductsModel {
    @Data
    class Arg {
        private List<String> orderProductIds;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class Result {
        private List<ObjectData> dataMapList;

    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class ObjectData {
        private String orderProductId;
        private List<ObjectDataDocument> dataList;

    }
}
