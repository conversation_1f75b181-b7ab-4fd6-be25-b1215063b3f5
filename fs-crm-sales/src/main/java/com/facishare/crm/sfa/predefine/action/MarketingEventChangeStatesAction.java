package com.facishare.crm.sfa.predefine.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * @IgnoreI18nFile
 */
@Slf4j
public class MarketingEventChangeStatesAction extends PreDefineAction<MarketingEventChangeStatesAction.Arg, MarketingEventChangeStatesAction.Result> {
    private String oriStatus = "";

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected List<String> getDataPrivilegeIds(MarketingEventChangeStatesAction.Arg arg) {
        return Lists.newArrayList(arg.getMarketingEventID());
    }

    @Override
    protected void before(MarketingEventChangeStatesAction.Arg arg) {
        super.before(arg);
    }

    @Override
    protected MarketingEventChangeStatesAction.Result doAct(MarketingEventChangeStatesAction.Arg arg) {
        if (Strings.isNullOrEmpty(arg.getBiz_status()) || Strings.isNullOrEmpty(arg.getMarketingEventID())) {
            throw new ValidateException(I18N.text(I18NKey.REQUEST_PARAM_IS_NULL));
        }
        IObjectData marketingEventData = serviceFacade.findObjectData(actionContext.getTenantId(), String.valueOf(arg.getMarketingEventID()), serviceFacade.findObject(actionContext.getTenantId(), SFAPreDefineObject.MarketingEvent.getApiName()));
        if (marketingEventData == null) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_ACCOUNTADDR_ACOUNTNOTNULL, I18N.text("AccountObj.attribute.self.display_name")));
        }
        oriStatus = AccountUtil.getStringValue(marketingEventData, "biz_status", "");
        marketingEventData.set("biz_status",arg.getBiz_status());
        IObjectData result  =  serviceFacade.updateObjectData(actionContext.getUser(),marketingEventData);
        return MarketingEventChangeStatesAction.Result.builder().errorCode("0").value(true).build();
    }

    @Override
    protected MarketingEventChangeStatesAction.Result after(MarketingEventChangeStatesAction.Arg arg, MarketingEventChangeStatesAction.Result result){
        IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe("biz_status");
        if(fieldDescribe != null && fieldDescribe instanceof SelectOneFieldDescribe){
            SelectOneFieldDescribe selectOneFieldDescribe = (SelectOneFieldDescribe)fieldDescribe;
            String oriStatusLabel = oriStatus;
            String statusLabel = "";
            if(selectOneFieldDescribe.getOption(oriStatus).isPresent()){
                oriStatusLabel = selectOneFieldDescribe.getOption(oriStatus).get().getLabel();
            }
            if(selectOneFieldDescribe.getOption(arg.getBiz_status()).isPresent()){
                statusLabel = selectOneFieldDescribe.getOption(arg.getBiz_status()).get().getLabel();
            }
            String logContent = String.format("状态，原状态: %s ,被更改为: %s 状态", oriStatusLabel, statusLabel);
            serviceFacade.logWithCustomMessage(actionContext.getUser(), EventType.MODIFY, ActionType.MODIFY, objectDescribe, dataList, logContent);
        }
        return super.after(arg, result);
    }

    @Data
    public static class Arg {
        @JSONField(name = "M1")
        @JsonProperty("biz_status")
        private String biz_status;

        @JSONField(name = "M2")
        @JsonProperty("marketingEvent_id")
        private String marketingEventID;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private String errorDetail;
        private String errorCode;
        private String message;
        private Boolean value;

        public boolean isSuccess() {
            return "0".equals(errorCode) && value;
        }
    }
}