package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.constant.SpuSkuConstants;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardBulkRecoverAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by luxin on 2018/12/7.
 * @IgnoreI18nFile
 */
public class ProductBulkRecoverAction extends StandardBulkRecoverAction {
    // TODO 将这几个查询整合成一个方法
    private ObjectDataServiceImpl objectDataService = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);
    private static final int MAX_BATCH_SIZE = 1000;

    String findInvalidSpuSql = "select p.name,p.id as _id\n" +
            "from stand_prod_unit as p,\n" +
            "     biz_product as k\n" +
            "where p.id = k.spu_id\n" +
            "  and k.id in('%s')\n" +
            "  and p.tenant_id = '%s'\n" +
            "  and k.tenant_id = '%s'\n" +
            "  and p.is_deleted = 1;";


    @Override
    // TODO opt
    protected void before(Arg arg) {
        boolean isSpuOpen = SFAConfigUtil.isSpuOpen(actionContext.getTenantId());
        if (isSpuOpen) {
            if (CollectionUtils.notEmpty(arg.getIdList())) {
                try {
                    Joiner joiner1 = Joiner.on("','");
                    String sql = String.format(findInvalidSpuSql, joiner1.join(arg.getIdList()), actionContext.getTenantId(), actionContext.getTenantId());

                    List<Map> invalidSpuInfoList = objectDataService.findBySql(actionContext.getTenantId(), sql);
                    if (CollectionUtils.notEmpty(invalidSpuInfoList)) {
                        List<String> invalidSpuNames = invalidSpuInfoList.stream().map(o -> o.get("name").toString()).distinct().collect(Collectors.toList());
                        Joiner joiner = Joiner.on(",");
                        // TODO
                        throw new ValidateException("恢复产品前,必须先恢复下列商品:\n" + joiner.join(invalidSpuNames));
                    }
                } catch (MetadataServiceException e) {
                    // TODO
                    throw new RuntimeException(e);
                }
            }
        }
        super.before(arg);
        if (isSpuOpen) {
            //校验规格数量
            validateSpecCount();
        }
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return arg.getIdList();
    }

    private void validateSpecCount() {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        //获取关联商品
        Map<String, String> skuSpuIdMap = Maps.newHashMap();
        Set<String> spuIds = Sets.newHashSet();
        for (IObjectData objectData : dataList) {
            skuSpuIdMap.put(objectData.getId(), objectData.get("spu_id", String.class));
            spuIds.add(objectData.get("spu_id", String.class));
        }
        List<IObjectData> spuDataList = serviceFacade.findObjectDataByIds(actionContext.getTenantId(),
                Lists.newArrayList(spuIds), Utils.SPU_API_NAME);
        if (CollectionUtils.empty(spuDataList)) {
            return;
        }
        //过滤多规格商品
        List<String> hasSpecSpuIds = spuDataList.stream()
                .filter(data -> Objects.equals(data.get("is_spec"), true))
                .map(data -> data.getId()).collect(Collectors.toList());
        if (CollectionUtils.empty(hasSpecSpuIds)) {
            return;
        }
        Map<String, String> toValidateSkuSpuMap = Maps.newHashMap();
        skuSpuIdMap.forEach((k, v) -> {
            if (hasSpecSpuIds.contains(v)) {
                toValidateSkuSpuMap.put(k, v);
            }
        });
        validateSpecBySkuSpuMap(toValidateSkuSpuMap);
    }

    private void validateSpecBySkuSpuMap(Map<String, String> skuSpuMap) {
        List<String> invalidSkuIds = Lists.newArrayList(skuSpuMap.keySet());
        List<IObjectData> skuList = getSkuListBySpuIds(skuSpuMap.values());
        //多规格商品无SKU
        if (CollectionUtils.empty(skuList)) {
            return;
        }
        Set<String> allSkuIds = Sets.newHashSet();
        skuList.forEach(sku -> allSkuIds.add(sku.getId()));
        List<IObjectData> relatedSpecList = getRelatedSpecListBySpuSkuIds(skuSpuMap.values(), allSkuIds);
        //SPU已用规格数量
        Map<String, Integer> currentSpuSpecCountMap = Maps.newHashMap();
        Map<String, Set<String>> spuSpecsMap = Maps.newHashMap();
        //待恢复产品已用规格数量
        Map<String, Integer> invalidSkuSpecCountMap = Maps.newHashMap();
        for (IObjectData objectData : relatedSpecList) {
            String spuId = objectData.get("spu_id", String.class);
            String skuId = objectData.get("sku_id", String.class);
            String specId = objectData.get("spec_id", String.class);
            if (spuSpecsMap.containsKey(spuId)) {
                Set<String> specs = spuSpecsMap.get(spuId);
                specs.add(specId);
            } else {
                spuSpecsMap.put(spuId, Sets.newHashSet(specId));
            }
            if (invalidSkuIds.contains(skuId)) {
                if (invalidSkuSpecCountMap.containsKey(skuId)) {
                    Integer cnt = invalidSkuSpecCountMap.get(skuId);
                    cnt++;
                    invalidSkuSpecCountMap.put(skuId, cnt);
                } else {
                    invalidSkuSpecCountMap.put(skuId, 1);
                }
            }
        }
        spuSpecsMap.forEach((k,v) -> currentSpuSpecCountMap.put(k, v.size()));
        for (Map.Entry<String, Integer> entry : invalidSkuSpecCountMap.entrySet()) {
            String spuId = skuSpuMap.getOrDefault(entry.getKey(), "");
            Integer currentUsedCnt = currentSpuSpecCountMap.getOrDefault(spuId, 0);
            if (currentUsedCnt > entry.getValue()) {
                throw new ValidateException("商品已新增规格，无法恢复原作废产品");
            }
        }
    }

    private List<IObjectData> getRelatedSpecListBySpuSkuIds(Collection<String> spuIds, Set<String> skuIds) {
        SearchTemplateQuery query = buildRelatedSpecSearchQuery(spuIds, skuIds);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(actionContext.getUser(),
                SpuSkuConstants.SPUSKUSPECVALUERELATEOBJ, query);
        if (queryResult != null && CollectionUtils.notEmpty(queryResult.getData())) {
            return queryResult.getData();
        }
        return Lists.newArrayList();
    }

    private SearchTemplateQuery buildRelatedSpecSearchQuery(Collection<String> spuIds, Set<String> skuIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        IFilter filter = new Filter();
        filter.setFieldName("spu_id");
        filter.setFieldValues(Lists.newArrayList(spuIds));
        filter.setOperator(Operator.IN);
        filters.add(filter);
        IFilter skuFilter = new Filter();
        skuFilter.setFieldName("sku_id");
        skuFilter.setFieldValues(Lists.newArrayList(skuIds));
        skuFilter.setOperator(Operator.IN);
        filters.add(skuFilter);
        query.setFilters(filters);
        query.setPermissionType(0);
        query.setLimit(3000);
        query.setNeedReturnCountNum(Boolean.FALSE);
        query.setNeedReturnQuote(Boolean.FALSE);
        return query;
    }

    private List<IObjectData> getSkuListBySpuIds(Collection<String> spuIds) {
        SearchTemplateQuery query = buildSkuSearchQuery(spuIds);
        List<IObjectData> objectDataList = Lists.newArrayList();
        int offset = 0;
        int loopCnt = 0;
        while (loopCnt < 10) {
            query.setOffset(offset);
            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(actionContext.getUser(),
                    Utils.PRODUCT_API_NAME, query);
            if (queryResult != null && CollectionUtils.notEmpty(queryResult.getData())) {
                objectDataList.addAll(queryResult.getData());
            }
            if (queryResult == null || CollectionUtils.empty(queryResult.getData()) || queryResult.getData().size() < MAX_BATCH_SIZE) {
                break;
            }
            offset += MAX_BATCH_SIZE;
            loopCnt++;
        }
        return objectDataList;
    }

    private SearchTemplateQuery buildSkuSearchQuery(Collection<String> spuIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        IFilter filter = new Filter();
        filter.setFieldName("spu_id");
        filter.setFieldValues(Lists.newArrayList(spuIds));
        filter.setOperator(Operator.IN);
        filters.add(filter);
        IFilter isDeletedFilter = new Filter();
        isDeletedFilter.setFieldValues(Lists.newArrayList("0","1"));
        isDeletedFilter.setOperator(Operator.IN);
        isDeletedFilter.setFieldName("is_deleted");
        filters.add(isDeletedFilter);
        query.setFilters(filters);
        query.setPermissionType(0);
        query.setLimit(MAX_BATCH_SIZE);
        query.setNeedReturnCountNum(Boolean.FALSE);
        query.setNeedReturnQuote(Boolean.FALSE);
        return query;
    }
}
