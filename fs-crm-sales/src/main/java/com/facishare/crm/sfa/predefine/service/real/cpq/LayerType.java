package com.facishare.crm.sfa.predefine.service.real.cpq;

import com.facishare.paas.appframework.core.exception.ValidateException;

import java.util.Objects;

public class LayerType {
    private String priceBookTieredType; //2-分层定价,4-分层折扣
    private String rulePriceType;//1-单价，2-打包价

    private LayerType(String priceBookTieredType, String rulePriceType) {
        this.priceBookTieredType = priceBookTieredType;
        this.rulePriceType = rulePriceType;
    }

    /**
     * 分层定价,规则为单价
     */
    public static final LayerType UNIT_PRICING = new LayerType("1", "1");

    /**
     * 分层折扣,规则为单价
     */
    public static final LayerType DISCOUNT_PRICING = new LayerType("2", "1");

    /**
     * 分层定价,规则为一口价
     */
    public static final LayerType PACKAGE_PRICING = new LayerType("1", "2");


    public static LayerType getInstance(String priceBookTieredType, String rulePriceType) {
        if (Objects.equals(priceBookTieredType, "1") && Objects.equals(rulePriceType, "1")) {
            return UNIT_PRICING;
        }
        if (Objects.equals(priceBookTieredType, "2") && Objects.equals(rulePriceType, "1")) {
            return DISCOUNT_PRICING;
        }
        if (Objects.equals(priceBookTieredType, "1") && Objects.equals(rulePriceType, "2")) {
            return PACKAGE_PRICING;
        }

        throw new ValidateException(String.format("阶梯价目表或是规则类型不正确,priceBookTieredType: %s,rulePriceType: %s", priceBookTieredType, rulePriceType));
    }

}
