package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.bizvalidator.BizValidator;
import com.facishare.crm.sfa.predefine.bizvalidator.ValidatorContext;
import com.facishare.crm.sfa.predefine.bizvalidator.validator.*;
import com.facishare.crm.sfa.predefine.bizvalidator.validator.newopportunity.*;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.utilities.proxy.NewOpportunityProxy;
import com.facishare.crm.sfa.utilities.proxy.model.NewOpportunityTriggerModel;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.sfa.utilities.util.NewOpportunityUtil;
import com.facishare.crm.sfa.utilities.validator.NewOpportunityValidator;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.facishare.rest.core.exception.RestProxyInvokeException;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
public class NewOpportunityEditAction extends StandardEditAction {

    private final NewOpportunityProxy newOpportunityProxy = SpringUtil.getContext().getBean(NewOpportunityProxy.class);
    private static final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);

    private void setStageData(){
        Map map = NewOpportunityValidator.salesStageToProbability((String) arg.getObjectData().get("sales_stage"), objectDescribe);
        if (map != null && map.size() > 0) {
            objectData.set("sales_status", map.get("sales_status"));
        }
    }

    @Override
    protected void init() {
        super.init();
        setStageData();
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        ValidatorContext validatorContext = ValidatorContext.builder()
                .action(ObjectAction.UPDATE)
                .user(actionContext.getUser())
                .describeApiName(objectDescribe.getApiName())
                .objectDescribes(objectDescribes)
                .dbMasterData(dbMasterData).objectData(objectData).detailObjectData(detailObjectData)
                .detailsToAdd(detailsToAdd).detailsToUpdate(detailsToUpdate).detailsToDelete(detailsToDelete)
                .build();
        BizValidator.build()
                .withContext(validatorContext)
                .with(new ProductIsRepeatedValidator())
                .with(new PriceBookValidator())
                .when(bizConfigThreadLocalCacheService.isPriceBookEnabled(actionContext.getTenantId()))
                .with(new ProductRangeValidator())
                .when(!bizConfigThreadLocalCacheService.isPriceBookEnabled(actionContext.getTenantId()))
                .with(new AvailableWorkflowValidator())
                .doValidate();


        //todo 价目表全网后,去掉该代码
        if (bizConfigThreadLocalCacheService.isPriceBookEnabled(actionContext.getTenantId()) && !GrayUtil.isGrayPriceBookRefactor(actionContext.getTenantId())) {
            NewOpportunityValidator.validateAccountPriceBook(getActionContext(), objectData, true);
        }
    }

    @Override
    protected void modifyObjectDataByDbData(IObjectData newData, IObjectData dbData) {

        super.modifyObjectDataByDbData(newData, dbData);
        if (dbData.get("sales_process_id") != null && newData.get("sales_process_id") != null) {
            if (dbData.get("sales_process_id").equals(newData.get("sales_process_id"))) {
                if (dbData.get("sales_stage") != null && newData.get("sales_stage") != null) {
                    if (!dbData.get("sales_stage").equals(newData.get("sales_stage"))) {
                        NewOpportunityUtil.fillModelByChangeSalesStage(actionContext, newData, dbData);
                    }
                }
            }
        }
        //销售流程变更
        if (dbData.get("sales_process_id") != null && arg.getObjectData().get("sales_process_id") != null) {
            if (!dbData.get("sales_process_id").equals(arg.getObjectData().get("sales_process_id"))) {
                newData.set("stg_changed_time", String.valueOf(System.currentTimeMillis()));
                Map map = NewOpportunityValidator.salesStageToProbability((String) arg.getObjectData().get("sales_stage"), objectDescribe);
                if (!CollectionUtils.empty(map)) {
                    newData.set("sales_status", map.get("sales_status"));
                }
            }
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        //判断是是否更改了销售流程

        if (!"in_change".equals(this.objectData.get("life_status"))) {
            if (!dbMasterData.get("sales_process_id").equals(arg.getObjectData().get("sales_process_id"))) {
                Map<String, String> headers = Maps.newHashMap();
                headers.put("Content-Type", "application/json");
                headers.put("x-tenant-id", getActionContext().getTenantId());
                headers.put("x-user-id", getActionContext().getUser().getUserId());

                NewOpportunityTriggerModel.Arg instanceChange = new NewOpportunityTriggerModel.Arg();
                instanceChange.setEntityId(SFAPreDefineObject.NewOpportunity.getApiName());
                instanceChange.setObjectId(String.valueOf(arg.getObjectData().get("_id")));
                instanceChange.setSourceWorkflowId(String.valueOf(arg.getObjectData().get("sales_process_id")));
                try {
                    log.warn("NewOpportunityinstanceChange,instanceChange{}", instanceChange);
                    NewOpportunityTriggerModel.Result instanceChangeResult = newOpportunityProxy.instanceChange(instanceChange, headers);
                    log.warn("NewOpportunityTriggerModel.Result {}", instanceChangeResult.getData());
                    if (instanceChangeResult.getCode() > 0) {
                        log.warn("NewOpportunityTriggerModel.Result {}", instanceChangeResult.getCode());
                        throw new ValidateException(instanceChangeResult.getMessage());
                    }
                } catch (RestProxyInvokeException e) {
                    log.error("RestProxyInvokeException instanceChange {}", instanceChange);
                    throw new RestProxyInvokeException(e);
                }
            }
        }
        return result;
    }
}
