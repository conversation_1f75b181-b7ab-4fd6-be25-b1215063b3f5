package com.facishare.crm.sfa.predefine.importobject;

import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.metadata.importobject.DefaultObjectImportProvider;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.springframework.stereotype.Component;

@Component
public class TimeSheetObjImportProvider extends DefaultObjectImportProvider {

    @Override
    public String getObjectCode() {
        return "TimeSheetObj";
    }

    @Override
    protected boolean getOpenWorkFlow(IObjectDescribe objectDescribe) {
        if (GrayUtil.isProjManageImportWorkflow(RequestContextManager.getContext().getTenantId())) {
            return true;
        }
        return super.getOpenWorkFlow(objectDescribe);
    }

}
