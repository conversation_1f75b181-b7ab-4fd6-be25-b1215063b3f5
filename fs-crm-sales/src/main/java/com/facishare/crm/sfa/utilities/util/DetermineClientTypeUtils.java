package com.facishare.crm.sfa.utilities.util;

import com.facishare.paas.appframework.core.model.RequestContext;

/**
 * 判断客户端类型的工具类
 * Created by luxin on 2018/12/10.
 */
public class DetermineClientTypeUtils {

    public static boolean isMobile(String clientInfo) {
        return isAndroid(clientInfo) || isIOS(clientInfo);
    }

    /**
     * 不是移动端就当成是 web 端,这个逻辑将来可能改动
     *
     * @param clientInfo
     * @return
     */
    public static boolean isWeb(String clientInfo) {
        return !isMobile(clientInfo);
    }

    public static boolean isIOS(String clientInfo) {
        return clientInfo != null
                && clientInfo.toLowerCase().contains(RequestContext.IOS_CLIENT_INFO_PREFIX.toLowerCase());
    }

    public static boolean isAndroid(String clientInfo) {
        return clientInfo != null
                && clientInfo.toLowerCase().contains(RequestContext.Android_CLIENT_INFO_PREFIX.toLowerCase());
    }


    private DetermineClientTypeUtils() {
    }
}
