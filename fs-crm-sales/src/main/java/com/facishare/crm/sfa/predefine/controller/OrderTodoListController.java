package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.model.Enum.DealDataTypeEnum;
import com.facishare.crm.sfa.utilities.constant.SalesOrderConstants;
import com.facishare.crm.sfa.utilities.util.SalesOrderUtil;
import com.facishare.crm.sfa.utilities.validator.SalesOrderValidator;
import com.facishare.crmcommon.util.SearchUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by renlb on 2019/3/25.
 */
public class OrderTodoListController extends SFATodoListController {

    protected StopWatch orderTodoListStopWatch = StopWatch.create("Order_TodoList_Controller");
    protected final ServiceFacade serviceFacade = SpringUtil.getContext().getBean(ServiceFacade.class);
    private boolean hasDeliveryRole = false;
    private boolean hasOrderManagerRole = false;
    private boolean hasFinanceRole = false;


    @Override
    protected void buildUnProcessedQuery(SearchTemplateQuery query) {
        if (arg.getSessionBOCItemKey() == DealDataTypeEnum.TOBE_DELIVERY_CUSTOMER_ORDER.getSessionKey()) {
            checkRole();
            orderTodoListStopWatch.lap("OrderTodoListController buildUnProcessedQuery getDeliveryFilters  begin");
            List<IFilter> filters = SalesOrderUtil.getDeliveryFilters(controllerContext.getUser(), hasDeliveryRole);
            query.setFilters(filters);
            orderTodoListStopWatch.lap("OrderTodoListController buildUnProcessedQuery getDeliveryFilters  end");
        } else {
            List<String> tobeConfirmIds = Lists.newArrayList();
            if (Objects.equals(arg.getObjectDescribeApiName(), Utils.SALES_ORDER_API_NAME)) {
                log.warn("OrderTodoListController buildUnProcessedQuery gray white tenant_id->{}, userId->{}",
                        controllerContext.getTenantId(), controllerContext.getUser().getUserId());
                checkRole();
                Set<String> ids = SalesOrderValidator.isApprover(controllerContext, hasOrderManagerRole, hasFinanceRole);
                tobeConfirmIds.addAll(ids);
                query.setPermissionType(0);
            } else {
                List<IObjectData> dataList = SalesOrderUtil.getToConfirmData(controllerContext.getUser(), objectDescribe.getApiName());
                if (CollectionUtils.notEmpty(dataList)) {
                    tobeConfirmIds = dataList.stream().map(d -> d.getId()).collect(Collectors.toList());
                }
            }

            List<IFilter> filters = Lists.newArrayList();
            IFilter filter = new Filter();
            filter.setFieldName("life_status");
            List<String> fieldValues = Lists.newArrayList();
            fieldValues.add(ObjectLifeStatus.UNDER_REVIEW.getCode());
            filter.setFieldValues(fieldValues);
            filter.setOperator(Operator.EQ);
            filters.add(filter);

            if (tobeConfirmIds.isEmpty()) {
                tobeConfirmIds.add("");
            }
            SearchUtil.fillFilterIn(filters, IObjectData.ID, new ArrayList<>(tobeConfirmIds));
            query.setFilters(filters);
        }
    }

    private void checkRole() {
        List<String> roleCodes = serviceFacade.getUserRole(controllerContext.getUser());
        if (CollectionUtils.notEmpty(roleCodes) && roleCodes.contains(SalesOrderConstants.GOODS_SENDING_PERSON_ROLE)) {
            hasDeliveryRole = true;
        }
        // 订单管理员
        if (CollectionUtils.notEmpty(roleCodes) && roleCodes.contains(SalesOrderConstants.ORDER_MANAGER_ROLE)) {
            hasOrderManagerRole = true;
        }
        // 订单财务
        if (CollectionUtils.notEmpty(roleCodes) && roleCodes.contains(SalesOrderConstants.ORDER_FINANCE_ROLE)) {
            hasFinanceRole = true;
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        orderTodoListStopWatch.logSlow(100);
        return super.after(arg, result);
    }
}
