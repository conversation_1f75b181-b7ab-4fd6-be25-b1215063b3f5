package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.service.ModuleCtrlConfigService;
import com.facishare.crm.sfa.predefine.service.modulectrl.IModuleInitService;
import com.facishare.crm.sfa.utilities.util.SalesOrderUtil;
import com.facishare.crm.sfa.utilities.util.imports.ImportSoUtil;
import com.facishare.paas.appframework.core.predef.action.StandardUnionInsertImportTemplateAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.List;

public class SalesOrderProductUnionInsertImportTemplateAction extends StandardUnionInsertImportTemplateAction {

    private final ModuleCtrlConfigService moduleCtrlConfigService = SpringUtil.getContext().getBean(ModuleCtrlConfigService.class);

    @Override
    protected void  customDetailHeader (List<IFieldDescribe> masterFieldList) {
        super.customDetailHeader(masterFieldList);
        SalesOrderUtil.removeUnSupportedDetailFields(masterFieldList, actionContext.getTenantId());

        // 开启cpq 移除部分字段
        if (moduleCtrlConfigService.openStatus(IModuleInitService.MODULE_CPQ, actionContext.getUser(), actionContext)) {
            ImportSoUtil.removeFields(masterFieldList, ImportSoUtil.SALES_ORDER_PRODUCT_OPEN_MULTI_UNIT_FILTER_FIELDS);
        }

        // 开启大小单位 移除部分字段
        if (moduleCtrlConfigService.openStatus(IModuleInitService.MODULE_MULTIPLE_UNIT, actionContext.getUser(), actionContext)) {
            ImportSoUtil.removeFields(masterFieldList, ImportSoUtil.SALES_ORDER_PRODUCT_FILTER_FIELDS);
        }
    }
}
