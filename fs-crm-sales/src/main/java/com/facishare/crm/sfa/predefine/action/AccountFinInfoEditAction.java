package com.facishare.crm.sfa.predefine.action;

import com.facishare.paas.appframework.core.predef.action.StandardEditAction;

public class AccountFinInfoEditAction extends StandardEditAction {

    @Override
    protected void before(Arg arg) {
        if (arg.getObjectData().containsKey("is_default")) {
            arg.getObjectData().remove("is_default");
        }
        super.before(arg);
    }
}
