package com.facishare.crm.sfa.utilities.constant;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * Created by luxin on 2018/11/17.
 * @IgnoreI18nFile
 */
public interface SpuSkuConstants {

    /**
     * 多单位关联关系导入，处理过滤字段
     */
    static List<String> multiUnitfilterFields = Lists.newArrayList("owner", "record_type", "data_own_department", "name", "is_enable");
    /**
     * 多单位关联关系，排序excel表头
     */
    static List<String> multiUnitsortFields = Lists.newArrayList("spu_id", "product_id", "is_base", "unit_id", "barcode", "conversion_ratio", "price", "is_pricing", "is_enable");

    String API_NAME = "api_name";
    String PRODUCTOBJ = "ProductObj";
    String SPUOBJ = "SPUObj";
    String SPUSKUSPECVALUERELATEOBJ = "SpuSkuSpecValueRelateObj";
    String SPECIFICATIONOBJ = "SpecificationObj";
    String SPECIFICATIONVALUEOBJ = "SpecificationValueObj";
    String CUSTOM_SPU_SPEC_FIELD = "specification";
    String CUSTOM_SKU_SPEC_VALUE_FIELD = "specificationvalue";
    String SKU_SPEC_FIELD  = "product_spec";
    String LIFE_STATUS = "life_status";
    String LIFE_STATUS_NORMAL = "normal";
    String SPU_ID = "_id";
    String SKU_ID = "_id";
    String FIELD_LABEL = "label";
    String SKU_RELATE_SPU_ID = "spu_id";
    String IS_DELETED = "is_deleted";
    String SPEC_ID = "specification_id";


    String SPU_SPECIFICATION_PROPERTY = "商品规格属性";
    String SKU_SPECIFICATION_VALUE_PROPERTY = "产品规格属性";
    String CUSTOM_SKU_PREFIX_WITH_KEY = "产品_";
    String SPEC_SPECVALUE_AMOUNT_NOT_EQ = "%s , 规格和规格值数量不匹配！";
    String SPEC_VALUE_IS_INVALID = "%s , 该规格值不可用！";
    String SEPC_NOT_EXIST = "%s , 该规格不可用！";
    String SKU_NAME_IS_EXIST = "%s , 该产品已存在！";
    String SKU_NAME_IS_EXIST_WITH_NO_SPEC_SPU = "%s , 同一个无规格商品下，只能出现一个产品！";
    String SKU_NAME_IS_EXIST_WITH_SPEC_SPU = "%s , 同一个有规格商品下，不能出现规格值相同的产品！";
    String SPEC_COUNT_IS_NOT_SURPASS_FOUR = "%s , 规格不能填写超过15个！";
    String SKU_IS_NOT_EXIST = "%s , 该产品不存在！";
    String SPU_IS_NOT_EXIST = "%s , 该商品不存在！";
    String SPU_IS_SPEC_NOT_CHANGE = "%s , 商品的是否有规格不允许修改！";
    String SPU_NUMBER = "商品系统编号";
    String SKU_NUMBER = "产品系统编号";
    String SKU_PREFIX_SKU_NUMBER = "产品_产品系统编号";
    String SKU_FILL_IN_THE_SPEC = "%s , 当前商品为有规格商品，请填写产品规格值！";
    String SKU_FILL_NOT_MATCH_THE_SPEC = "%s , 规格规格值不匹配！";
    String SKU_SPEC_IS_NOT_NULL="%s , 有规格商品，规格属性不能为空！";
    String SPU_NOT_IS_SPEC_NO_VALUE="%s , 无规格商品，商品规格属性请不要填写！";
    String SPU_NOT_IS_SPEC_SKU_NO_VALUE="%s , 无规格商品，产品规格属性请不要填写！";
    String MUST_FILL_IN = "（必填）";
    String SPU_SPECIFICATION_EXAMPLE = "颜色；存储大小；";
    String SKU_SPECIFICATION_EXAMPLE = "银色；128G；";
    String PRODUCT_NUMBER_IS_MUST_FILL_IN = "%s , 产品系统编号为必填！";
    String SPU_NUMBER_IS_MUST_FILL_IN = "%s , 商品系统编号为必填！";
    String SPU_OTHER_DATA_HAVA_ERROR="%s ，同一个商品下其他数据有错误导致该条失败！";
    String SPU_NAME_NOT_NULL ="商品名称是必填项，请填写商品名称！";
    String UNDER_SPU_SKU_ERROR ="同一商品下商品或是其他产品数据有问题,导致商品下的全部产品导入不成功.";
    String IS_SPEC_NOT_NULL = "是否有规格不能为空";

    /**
     * 商品上的单位字段
     */
    String MULTIUNITRELEATED_SPU_UNIT = "unit";
    /**
     * 通过spuId查询产品
     */
    String MULTIUNITRELEATED_FIND_SKU_BY_SPU_ID = "select * from biz_product where tenant_id='%s' and spu_id in ('%s') and is_deleted = 0;";

    String MULTIUNITRELEATED_FIND_SKU_BY_SKU_ID = "select * from biz_product where tenant_id='%s' and id in ('%s') and is_deleted = 0;";

    /**
     * 是否是产品组合
     */
    String MULTIUNITRELEATED_IS_PACKAGE = "is_package";


    /**
     * 商品ID
     */
    String MULTIUNITRELEATED_SPU_ID = "spu_id";

    /**
     * 产品ID
     */
    String MULTIUNITRELEATED_PRODUCT_ID = "product_id";

    /**
     * 商品名称
     */
    String MULTIUNITRELEATED_SPU_NAME = "spu_name";

    /**
     * 产品名称
     */
    String MULTIUNITRELEATED_SKU_NAME = "sku_name";

    /**
     * 单位ID
     */
    String MULTIUNITRELEATED_UNIT_ID = "unit_id";

    /**
     * 转换比
     */
    String MULTIUNITRELEATED_CONVERSION_RATION = "conversion_ratio";

    /**
     * 价格，多单位导入的价格是虚拟字段
     */
    String MULTIUNITRELEATED_PRICE= "price";

    /**
     * 基准单位
     */
    String MULTIUNITRELEATED_IS_BASE = "is_base";

    /**
     * 定价单位
     */
    String MULTIUNITRELEATED_IS_PRICING = "is_pricing";

    /**
     * ID
     */
    String MULTIUNITRELEATED_ID = "id";

    /**
     * 条形码
     */
    String MULTIUNITRELEATED_BARCODE = "barcode";

    /**
     * 商品价格
     */
    String MULTIUNITRELEATED_STANDARD_PRICE = "standard_price";

    /**
     * 多单位
     */
    String MULTIUNITRELEATED_IS_MULTIPLE_UNIT = "is_multiple_unit";


    /**
     * 多规格
     */
    String MULTIUNITRELEATED_IS_SPEC = "is_spec";

    /**
     * 限制位数
     */
    Integer ASTRICT_NUMBER = 20;

    /**
     * 限制转化比小数位数
     */
    Integer ASTRICT_CONVER_RATIO_DECIMAL = 4;

    /**
     * 基准单位转换比为1
     */
    String CONVER_RATIO_IS_ONE = "1.0000";

    /**
     * 限制价格小数位数
     */
    Integer ASTRICT_PRICE_DECIMAL = 8;

    /**
     * 包含属性
     */
    String CONTAINS_PATTERN = "\\.";

    /**
     * 处理截取
     */
    String CONTAINS_PATTERN_DOT = ".";

    /**
     * 多单位虚拟字段的apiname
     */
    String MULTI_UNIT_RELATED_PRICE = "price";

    /**
     * 虚拟key，用来标识每行的数据。
     */
    String VRITUAL_KEY = "virtualKey";


    /**
     * 单位ID
     */
    String UNIT_ID = "unit_id";

}
