package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.bizvalidator.BizValidator;
import com.facishare.crm.sfa.predefine.bizvalidator.ValidatorContext;
import com.facishare.crm.sfa.predefine.bizvalidator.validator.ProductInPriceBookValidator;
import com.facishare.crm.sfa.predefine.bizvalidator.validator.quote.ProductInTieredPriceBookValidator;
import com.facishare.crm.sfa.predefine.bizvalidator.validator.quotelines.ProductIsRepeatedInQuoteValidator;
import com.facishare.crm.sfa.predefine.service.PriceBookCommonService;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.crm.sfa.utilities.validator.QuoteValidator;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.metadata.util.SpringUtil;

public class QuoteLinesEditAction extends StandardEditAction {

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        ValidatorContext validatorContext = ValidatorContext.builder()
                .action(ObjectAction.UPDATE)
                .user(actionContext.getUser())
                .describeApiName(objectDescribe.getApiName())
                .objectDescribes(objectDescribes)
                .objectData(objectData).detailObjectData(detailObjectData)
                .build();
        BizValidator.build()
                .withContext(validatorContext)
                .with(new ProductIsRepeatedInQuoteValidator())
                .with(new ProductInPriceBookValidator())
                .putAttribute2Context("enableCheckParentProdPkgKey", true)
                .doValidate();
    }
}
