package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.exception.SOBusinessException;
import com.facishare.crm.sfa.predefine.service.ModuleCtrlConfigService;
import com.facishare.crm.sfa.predefine.service.modulectrl.IModuleInitService;
import com.facishare.crm.sfa.utilities.constant.PriceBookConstants;
import com.facishare.crm.sfa.utilities.util.SOI18NKeyUtils;
import com.facishare.crm.sfa.utilities.validator.SalesOrderValidator;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import static com.facishare.crm.sfa.predefine.exception.SOErrorCode.SO_PRICEBOOK_PRICEBOOKPRODUCT_NOR_EXIST_ERROR;
import static com.facishare.crm.sfa.predefine.exception.SOErrorCode.SO_PRICEBOOK_PRICEBOOKPRODUCT_NOR_UPDATE_PRODUCT_ID__ERROR;

@Slf4j
public class SalesOrderProductEditAction extends StandardEditAction {
    private static final ModuleCtrlConfigService moduleCtrlConfigService = SpringUtil.getContext().getBean(ModuleCtrlConfigService.class);

    boolean isMultipleUnit = false;

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        isMultipleUnit = moduleCtrlConfigService.isOpen(IModuleInitService.MODULE_MULTIPLE_UNIT, actionContext.getUser());
        SalesOrderValidator.validateQuantityDecimal(actionContext, objectData,isMultipleUnit,objectDescribe);
    }
}
