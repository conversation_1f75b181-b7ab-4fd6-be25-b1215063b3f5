package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.model.MultiUnitData;
import com.facishare.crm.sfa.predefine.service.real.*;
import com.facishare.crm.sfa.utilities.constant.ProductConstants;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.crm.sfa.utilities.validator.ProductValidator;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.appframework.flow.mq.WorkflowProducer;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.license.LicenseServiceImpl;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.appframework.metadata.dto.SaveMasterAndDetailData;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.SelectOne;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.api.IdGenerator;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * Created by luxin on 2018/10/29.
 * 实现了spu和sku及关联表的存储
 * @IgnoreI18nFile
 */
// TODO 定价单位到价目表产品上的同步
@Slf4j
public class SPUAddAction extends StandardAddAction {
    private final SpuSkuService spuSkuService = SpringUtil.getContext().getBean(SpuSkuServiceImpl.class);
    private static final ConfigService configService = SpringUtil.getContext().getBean(ConfigService.class);
    private final List<IObjectData> skuList = Lists.newArrayList();
    private final ProductService productService = SpringUtil.getContext().getBean(ProductService.class);
    private final MultiUnitService multiUnitService = SpringUtil.getContext().getBean(MultiUnitServiceImpl.class);
    private List<MultiUnitData.MultiUnitItem> multiUnitData;
    private LicenseService licenseService=SpringUtil.getContext().getBean(LicenseServiceImpl.class);


    @Override
    protected void before(Arg arg) {
        if (!SFAConfigUtil.isSpuOpen(actionContext.getTenantId())) {
            throw new ValidateException("商品对象已关闭，不允许新增商品数据");
        }
        IObjectDescribe spuDescribe = serviceFacade
                .findObject(actionContext.getTenantId(), Utils.SPU_API_NAME);

        productService.modifyArg(spuDescribe, arg.getObjectData());

        multiUnitData = multiUnitService.preprocessMultiUnit(arg.getObjectData());
        if (multiUnitData != null && multiUnitData.size() > 20) {
            throw new ValidateException("多单位数量超过上限20个");
        }
        Set<String> module = licenseService.getModule(actionContext.getTenantId());
        if (multiUnitData != null &&module.contains("kx_peculiarity")&&multiUnitData.size() > 3) {
            throw new ValidateException("开启快销企业多单位数量超过上限3个");
        }
        super.before(arg);
        parseSkuData();
        if (Objects.equals(objectData.get("is_spec"), true)) {
            ProductValidator.validateSpecStatusActive(actionContext.getTenantId(), skuList);
        }
    }

    @Override
    protected void validate() {
        if (multiUnitData != null) {
            multiUnitService.checkMultiUnitAndGetUnitInfo(actionContext.getTenantId(), multiUnitData, (SelectOne) objectDescribe.getFieldDescribe("unit"));
        }
        super.validate();
    }


    @Override
    protected void doSaveData() {
        SaveMasterAndDetailData.Arg saveArg = SaveMasterAndDetailData.Arg.builder()
                .masterObjectData(objectData)
                .detailObjectData(detailObjectData)
                .objectDescribes(objectDescribes)
                .build();

        objectData.set(ObjectLifeStatus.LIFE_STATUS_API_NAME, ObjectLifeStatus.NORMAL.getCode());

        String spuId = objectData.getId();
        if (StringUtils.isBlank(spuId)) {
            // TODO threw ex
        }

        // 对sku数据列表做处理
        IObjectDescribe skuDesc = serviceFacade.findObject(actionContext.getTenantId(), Utils.PRODUCT_API_NAME);
        skuList.forEach(o -> {
            setDefaultRecordType(o, skuDesc);
            modifyObjectDataBeforeCreate(o, skuDesc);

            if (StringUtils.isEmpty((o.getId()))) {
                o.setId(IdGenerator.get());
            }
        });

        SaveMasterAndDetailData.Result result;
        // 保存spu及sku及关联关系表
        if (multiUnitData == null) {
            result = spuSkuService.saveSpuAndSkuRelation(actionContext.getUser(), skuList, saveArg);
        } else {
            result = spuSkuService.saveMultiUnitSpuAndSku(actionContext.getUser(), skuList, multiUnitData, saveArg);
        }

        objectData = result.getMasterObjectData();
        detailObjectData = result.getDetailObjectData();
    }

    @Override
    protected void triggerWorkFlow() {
        if (!needTriggerWorkFlow()) {
            return;
        }

        //只有当审批流不存在时,才触发工作流
        if (!isApprovalNotExist()) {
            return;
        }

        //批量处理触发工作流以及处理过滤器异常的数据
        for (IObjectData data : skuList) {
            infraServiceFacade.startWorkFlow(data.getId(), data.getDescribeApiName(), WorkflowProducer.TRIGGER_START,
                    actionContext.getUser(), Maps.newHashMap(), actionContext.getEventId());
        }
    }

    @Override
    protected void recordLog() {
        super.recordLog();

        IObjectDescribe productDescribe = serviceFacade.findObject(actionContext.getTenantId(), Utils.PRODUCT_API_NAME);
        HashMap<String, IObjectDescribe> objectObjectHashMap = Maps.newHashMap();
        objectObjectHashMap.put(productDescribe.getApiName(), productDescribe);
        serviceFacade.log(actionContext.getUser(), EventType.ADD, ActionType.Add, objectObjectHashMap, skuList);
    }

    /**
     * 从spu数据中解析出sku数据
     */
    private void parseSkuData() {
        IObjectDescribe skuDescribe = serviceFacade.findObject(actionContext.getTenantId(), Utils.PRODUCT_API_NAME);
        Optional<List<Map<String, Object>>> tmpSkuListOptional = Optional.ofNullable(objectData.get("sku", List.class));

        tmpSkuListOptional.orElseThrow(() -> new ValidateException(I18N.text("product.sku_data_is_null")));

        if (tmpSkuListOptional.get().size() > 300) {
            throw new ValidateException(I18N.text("product.sku_data_more_then_limit"));
        }

        tmpSkuListOptional.get().forEach(o -> {
            if (Objects.isNull(o.get("object_describe_id"))) {
                o.put(ProductConstants.DescribleField.FIELD_DESCRIBE_ID.getApiName(), skuDescribe.getId());
                o.put(ProductConstants.DescribleField.FIELD_DESCRIBE_API_NAME.getApiName(), skuDescribe.getApiName());
            }
            skuList.add(ObjectDataExt.of(o).getObjectData());
        });
    }
}
