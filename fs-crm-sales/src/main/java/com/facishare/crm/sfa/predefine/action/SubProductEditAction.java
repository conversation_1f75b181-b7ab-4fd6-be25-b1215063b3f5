//package com.facishare.crm.sfa.predefine.action;
//
//import com.facishare.crm.sfa.predefine.service.CPQService;
//import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
//import com.facishare.paas.metadata.util.SpringUtil;
//
//public class SubProductEditAction extends StandardEditAction {
//    private final CPQService cpqService = SpringUtil.getContext().getBean(CPQService.class);
//
//    @Override
//    protected void validate() {
//        super.validate();
//        cpqService.checkSubProducts(arg.getObjectData());
//    }
//}
