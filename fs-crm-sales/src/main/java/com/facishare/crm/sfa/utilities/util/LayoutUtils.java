package com.facishare.crm.sfa.utilities.util;

import com.facishare.crmcommon.describebuilder.FormFieldBuilder;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.ServiceFacadeImpl;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.license.util.LicenseConstants;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.layout.TableComponentRender;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.FormField;
import com.facishare.paas.metadata.impl.ui.layout.component.ComponentFactory;
import com.facishare.paas.metadata.impl.ui.layout.component.GroupComponent;
import com.facishare.paas.metadata.ui.layout.*;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;

import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * Created by luxin on 2018/7/23.
 */
@Slf4j
public class LayoutUtils {

    public static final Map<String, Map<String, Object>> PRODUCT_NEED_MODIFY_FIELD_INFO;
    public static final Map<String, Map<String, Object>> PRODUCT_EDIT_NEED_MODIFY_FIELD_INFO;
    private static final ServiceFacade SERVICE_FACADE = SpringUtil.getContext().getBean(ServiceFacadeImpl.class);
    private static final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);

    static {

        Map<String, Object> tmpPropertyNameValueMap = Maps.newHashMap();
        tmpPropertyNameValueMap.put("is_readonly", true);
        Map<String, Object> propertyNameValueMap = Collections.unmodifiableMap(tmpPropertyNameValueMap);
        Map<String, Map<String, Object>> tmpProductNeedModifyFieldInfo = Maps.newHashMap();
        tmpProductNeedModifyFieldInfo.put("name", propertyNameValueMap);
        tmpProductNeedModifyFieldInfo.put("product_spec", propertyNameValueMap);
        tmpProductNeedModifyFieldInfo.put("unit", propertyNameValueMap);
        tmpProductNeedModifyFieldInfo.put("category", propertyNameValueMap);
        tmpProductNeedModifyFieldInfo.put("product_line", propertyNameValueMap);
        tmpProductNeedModifyFieldInfo.put("batch_sn", propertyNameValueMap);

        PRODUCT_NEED_MODIFY_FIELD_INFO = Collections.unmodifiableMap(tmpProductNeedModifyFieldInfo);
    }

    static {
        Map<String, Object> tmpPropertyNameValueMap = Maps.newHashMap();
        tmpPropertyNameValueMap.put("is_readonly", true);
        Map<String, Object> propertyNameValueMap = Collections.unmodifiableMap(tmpPropertyNameValueMap);
        Map<String, Map<String, Object>> tmpProductNeedModifyFieldInfo = Maps.newHashMap();
        tmpProductNeedModifyFieldInfo.put("name", propertyNameValueMap);
        tmpProductNeedModifyFieldInfo.put("product_spec", propertyNameValueMap);
        tmpProductNeedModifyFieldInfo.put("unit", propertyNameValueMap);
        tmpProductNeedModifyFieldInfo.put("spu_id", propertyNameValueMap);
        tmpProductNeedModifyFieldInfo.put("category", propertyNameValueMap);
        tmpProductNeedModifyFieldInfo.put("product_line", propertyNameValueMap);
        tmpProductNeedModifyFieldInfo.put("batch_sn", propertyNameValueMap);
        PRODUCT_EDIT_NEED_MODIFY_FIELD_INFO = Collections.unmodifiableMap(tmpProductNeedModifyFieldInfo);
    }


    private LayoutUtils() {
    }

    /**
     * A版客户/联系人 详情页删除工单这个关联对象
     *
     * @param layout 布局
     */
    public static void handleRelatedCasesObj(String version, String componentApiName, ILayout layout) {
        if (LicenseConstants.Versions.VERSION_BASIC.equals(version)) {
            List<IComponent> tmpComponents;
            try {
                tmpComponents = layout.getComponents();
            } catch (MetadataServiceException e) {
                log.warn("getComponents error.", e);
                return;
            }
            List<IComponent> components = Lists.newArrayListWithCapacity(tmpComponents.size());
            tmpComponents.forEach(o -> {
                if (Objects.equals("relatedObject", o.getName())) {
                    ArrayList<Document> childComponents = o.get("child_components", ArrayList.class);
                    childComponents.removeIf(document -> Objects.equals(componentApiName, document.get("api_name")));
                }
                components.add(o);
            });
            layout.setComponents(components);
        }
    }

    /**
     * 移除移动端的 formComponent 新建和编辑按钮
     *
     * @param formComponentExt 详情信息
     */
    public static void removeFormComponentAddAndEditButton4MobileOrH5(FormComponentExt formComponentExt) {

        if (RequestUtil.isMobileOrH5Request()) {
            removeFormComponentButtons(formComponentExt, Lists.newArrayList(ObjectAction.CREATE.getActionCode(), ObjectAction.UPDATE.getActionCode()));
        }
    }

    /**
     * 去掉 formComponent 里面目标的按钮
     *
     * @param actionCodes 要删除的按钮的 actionCode
     */
    public static void removeFormComponentButtons(FormComponentExt formComponentExt, List<String> actionCodes) {

        List<IButton> buttons = formComponentExt.getButtons();
        // 移动端去掉新建按钮
        buttons.removeIf(b -> actionCodes.contains(b.getAction()));
        formComponentExt.setButtons(buttons);
    }

    public static void removeButtons4MobileOrH5(LayoutExt layoutExt, List<String> actionCodes) {
        if (RequestUtil.isMobileOrH5Request()) {
            List<IButton> buttons = layoutExt.getButtons().stream().filter(o -> !actionCodes.contains(o.getAction())).collect(Collectors.toList());
            layoutExt.setButtons(buttons);
        }
    }

    public static void removeButtons(LayoutExt layoutExt, List<String> actionCodes) {
        List<IButton> buttons = layoutExt.getButtons().stream().filter(o -> !actionCodes.contains(o.getAction())).collect(Collectors.toList());
        layoutExt.setButtons(buttons);
    }

    /**
     * 删除系统信息
     *
     * @param formComponentExt 详情布局部分
     */
    public static void removeFormComponentSystemSection(FormComponentExt formComponentExt) {
        List<IFieldSection> fieldSections = formComponentExt.getFieldSections().stream()
                .filter(o -> !"sysinfo_section__c".equals(o.getName())
                        && !"system_form_field_generate_by_UDObjectServer__c".equals(o.getName()))
                .collect(Collectors.toList());
        formComponentExt.setFieldSections(fieldSections);
    }

    /**
     * 更改 formComponent 目标字段的字段属性
     *
     * @param formComponentExt                 详情布局部分
     * @param fieldApiNameAndPropertiesInfoMap 要更新的字段及属性信息
     */
    public static void modifyFormComponentFieldsProperty(FormComponentExt formComponentExt, Map<String, Map<String, Object>> fieldApiNameAndPropertiesInfoMap) {
        Optional<IFieldSection> baseFieldSection = formComponentExt.getBaseFieldSection();
        baseFieldSection.ifPresent(o -> {
            List<IFormField> formFields = o.getFields();

            Set<String> needModifyFieldApiNames = fieldApiNameAndPropertiesInfoMap.keySet();
            formFields.forEach(f -> {
                if (needModifyFieldApiNames.contains(f.getFieldName())) {
                    fieldApiNameAndPropertiesInfoMap.get(f.getFieldName()).forEach(f::set);
                }
            });
        });
    }

    public static void removeFormComponentFields(FormComponentExt formComponentExt, Set<String> fieldApiNames) {
        List<IFieldSection> fieldSections = formComponentExt.getFieldSections();
        fieldSections.forEach(f -> {
            List<IFormField> formFields = f.getFields();
            Predicate<IFormField> fieldPredicate = x -> fieldApiNames.contains(x.getFieldName());
            formFields.removeIf(fieldPredicate);
            f.setFields(formFields);
        });
    }

    public static void removeFormComponentSpec4Product(FormComponentExt formComponentExt) {
        List<IFieldSection> fieldSections = formComponentExt.getFieldSections();
        fieldSections.forEach(f -> {
            List<IFormField> formFields = f.getFields();
            List<IFormField> specFields = formFields.stream()
                    .filter(field -> "product_spec".equals(field.getFieldName()))
                    .collect(Collectors.toList());
            if (CollectionUtils.notEmpty(specFields) && specFields.size() > 1) {
                formFields.removeIf(o -> Objects.equals(o.getFieldName(), "product_spec")
                        && Objects.equals(o.get("is_readonly"), false));
            }
            f.setFields(formFields);
        });
    }

    public static void removeProductSpecField(LayoutExt layoutExt, String parentComponentApiName) {
        Optional<IComponent> detailInfo = layoutExt.getComponentByApiName(parentComponentApiName);

        if (detailInfo.isPresent() && detailInfo.get() instanceof GroupComponent) {
            try {
                Optional<IComponent> formComponent = ((GroupComponent) detailInfo.get()).getChildComponents().stream()
                        .filter(o -> Objects.equals(o.getName(), "form_component")).findFirst();
                if (formComponent.isPresent() && formComponent.get() instanceof IFormComponent) {
                    LayoutUtils.removeFormComponentSpec4Product(FormComponentExt.of((IFormComponent) formComponent.get()));
                }
            } catch (MetadataServiceException e) {
                throw new MetaDataBusinessException(e);
            }
        }
    }


    public static void removeFormComponentFields4Product(FormComponentExt formComponentExt, boolean isSpuOpen) {
        Set<String> fieldApiNames = Sets.newHashSet("on_shelves_time", "off_shelves_time", "is_package");
        if (isSpuOpen) {
            fieldApiNames.addAll(Lists.newArrayList("owner", "product_spec"));
        }
        removeFormComponentFields(formComponentExt, fieldApiNames);
    }

    public static void removeListLayoutButtons4MobileOrH5(List<LayoutDocument> listLayouts, List<String> actionCodes) {
        if (CollectionUtils.notEmpty(listLayouts)) {
            if (RequestUtil.isMobileOrH5Request()) {
                Optional<LayoutDocument> listLayoutOptional = listLayouts.stream().findFirst();

                if (listLayoutOptional.isPresent()) {
                    ILayout layout = listLayoutOptional.get().toLayout();
                    LayoutUtils.removeButtons(LayoutExt.of(layout), actionCodes);

                    LayoutExt.of(layout);
                }
            }
        }
    }

    public static void removeListLayoutButtons(List<LayoutDocument> listLayouts, List<String> actionCodes) {
        Optional<LayoutDocument> listLayoutOptional = listLayouts.stream().findFirst();

        if (listLayoutOptional.isPresent()) {
            ILayout layout = listLayoutOptional.get().toLayout();
            LayoutUtils.removeButtons(LayoutExt.of(layout), actionCodes);

            LayoutExt.of(layout);
        }
    }

    /**
     * 移除移动端 list layout 更新类的 buttons
     *
     * @param listLayouts
     */
    public static void removeListLayoutUpdateTypeButtons4MobileOrH5(List<LayoutDocument> listLayouts) {
        if (CollectionUtils.notEmpty(listLayouts)) {
            if (RequestUtil.isMobileOrH5Request()) {
                removeListLayoutButtons4MobileOrH5(
                        listLayouts,
                        Lists.newArrayList(
                                ObjectAction.CREATE.getActionCode(),
                                ObjectAction.UPDATE.getActionCode(),
                                ObjectAction.INVALID.getActionCode(),
                                ObjectAction.CLONE.getActionCode(),
                                ObjectAction.CHANGE_OWNER.getActionCode()
                        )
                );
            }

            if (RequestUtil.isCepRequest()) {
                removeListLayoutButtons(
                        listLayouts,
                        Lists.newArrayList(ObjectAction.DUPLICATECHECK.getActionCode())
                );
            }
        }
    }

    public static void removeDetailButtons(ILayout layout) {
        List<IButton> buttons = layout.getButtons();
        List<String> removeActionList = Lists.newArrayList(
                ObjectAction.INVALID.getActionCode(),
                ObjectAction.LOCK.getActionCode(),
                ObjectAction.UNLOCK.getActionCode(),
                ObjectAction.CONFIRM.getActionCode(),
                ObjectAction.REJECT.getActionCode(),
                ObjectAction.UPDATE.getActionCode(),
                ObjectAction.CLONE.getActionCode(),
                ObjectAction.DELETE.getActionCode(),
                ObjectAction.CREATE.getActionCode(),
                ObjectAction.PRINT.getActionCode());
        List<IButton> toRemoveButtons = buttons.stream().filter(x -> removeActionList.contains(x.getAction()))
                .collect(Collectors.toList());
        buttons.removeAll(toRemoveButtons);
        layout.setButtons(buttons);
    }

    /**
     * 增加component
     *
     * @param layoutExt
     * @param relatedComponentJson
     */
    public static void addComponentByComponentJsonStr(LayoutExt layoutExt, String relatedComponentJson) {
        try {
            List<IComponent> components = layoutExt.getComponentsSilently();

            IComponent component = ComponentFactory.newInstance(relatedComponentJson);
            components.add(component);
            layoutExt.setComponents(components);
        } catch (MetadataServiceException e) {
            throw new MetaDataBusinessException(e);
        }
    }


    //region For New Detail

    /**
     * A版客户/联系人 详情页删除工单这个关联对象
     *
     * @param layout 布局
     */
    public static void handleRelatedCasesObjForNewDetail(String version, String componentApiName, ILayout layout) {
        if (LicenseConstants.Versions.VERSION_BASIC.equals(version)) {
            LayoutExt.of(layout).getComponentByApiName("middle_component").ifPresent(m -> {
                GroupComponentExt groupComponent = GroupComponentExt.of((IGroupComponent) m);
                try {
                    List<IComponent> ch_component = groupComponent.getChildComponents();
                    ch_component.removeIf(document -> Objects.equals(componentApiName, document.get("api_name")));
                    groupComponent.setChildComponents(ch_component);
                } catch (MetadataServiceException e) {
                    log.warn("getComponents error.", e);
                    return;
                }
            });

        }
    }
    //endregion

    public static void addNewDetailComponentByComponentJsonStr(LayoutExt layoutExt, String relatedComponentJson) {
        layoutExt.getComponentByApiName("middle_component").ifPresent(
                o -> {
                    try {
                        ((GroupComponent) o).addComponent(ComponentFactory.newInstance(relatedComponentJson));
                    } catch (MetadataServiceException e) {
                        log.error("addNewDetailComponentByComponentJsonStr tenantId:{}", layoutExt.getTenantId());
                    }
                }
        );

    }

    public static void addNewDetailComponentByComponent(LayoutExt layoutExt, IComponent component) {
        if (null != component) {
            layoutExt.getComponentByApiName("middle_component").ifPresent(
                    o -> ((GroupComponent) o).addComponent(component)
            );
        }
    }


    public static void processMultiUnitField(ILayout layout, String tenantId) {
        List<IComponent> components = Lists.newArrayList();
        for (IComponent o : LayoutExt.of(layout).getComponentsSilently()) {
            List fields = ComponentExt.of(o).get("include_fields", List.class);
            if (!bizConfigThreadLocalCacheService.isPriceBookEnabled(tenantId)) {
                fields.removeIf(x -> "is_pricing".equals(((Map) x).get("api_name")));
            }

            FormField priceField = FormFieldBuilder.builder().required(false).fieldName("price").renderType("currency").build();
            priceField.set("label", "价格");
            fields.add(priceField.getContainerDocument());

            o.set("include_fields", fields);
            components.add(o);
        }
        layout.setComponents(components);
    }

    public static ILayout addEditForMastDetail(ILayout layout, String componentsApiName) {
        if (layout.get("components") != null) {
            try {
                List<IComponent> components = layout.getComponents();
                components.forEach(component -> {
                    if (component.get("api_name").equals(componentsApiName)) {
                        if (component.get("child_components") != null) {
                            List<Map<String, Object>> chComponents = (List<Map<String, Object>>) component.get("child_components");
                            chComponents.forEach(ch -> {
                                List<IButton> buttons = (List<IButton>) ch.get("buttons");
                                buttons.add(ObjectAction.UPDATE.createButton());
                                ch.put("buttons", buttons);
                            });
                        }
                    }
                });
            } catch (MetadataServiceException e) {
                log.error("获取layout components出错");
            }
        }
        return layout;
    }

    public static List<ILayout> findMobileLayouts(User user, ObjectDescribeExt describeExt) {
        List<ILayout> listLayouts = SERVICE_FACADE.findMobileListLayout(user, describeExt, false);
        listLayouts.forEach(m -> doRender(user, m, describeExt));
        return listLayouts;
    }

    public static void doRender(User user, ILayout layout, ObjectDescribeExt objectDescribe) {
        getComponentRender(user, layout, objectDescribe).render();
    }

    public static TableComponentRender getComponentRender(User user, ILayout layout, ObjectDescribeExt objectDescribe) {
        return TableComponentRender.builder()
                .functionPrivilegeService(SERVICE_FACADE)
                .user(user)
                .describeExt(objectDescribe)
                .tableComponentExt(TableComponentExt.of(LayoutExt.of(layout).getTableComponent().get()))
                .build();
    }
}
