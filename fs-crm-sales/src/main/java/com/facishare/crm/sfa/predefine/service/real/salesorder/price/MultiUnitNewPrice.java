package com.facishare.crm.sfa.predefine.service.real.salesorder.price;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.model.MutipleUnitInfo;
import com.facishare.crm.sfa.utilities.constant.ProductConstants;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Builder;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/7/13 2:13 下午
 * @illustration
 */
public class MultiUnitNewPrice extends AbsNewPriceForOrderProduct {


    @Builder
    public MultiUnitNewPrice(String tenantId, List<ObjectDataDocument> data){
        super(tenantId, data);
    }


    @Override
    public void getNewPrice() {
        super.getNewPrice();

        List<MutipleUnitInfo.CalculateParam> calcPriceByUnitParam = Lists.newArrayList();
        Map<String, Double> multiUnitPrice = Maps.newHashMap();
        Map<String, String> priceBookProductPrice = Maps.newHashMap();
        Map<String, String> productPrice = Maps.newHashMap();

        details.forEach(data->{
            IObjectData objectData = data.toObjectData();
            String productId = objectData.get("product_id", String.class);
            String quantity = objectData.get("quantity", String.class);
            IObjectData productData = productIdToData.get(productId);
            String productStatus = productData.get("product_status", String.class);
            String lifeStatus = productData.get("life_status", String.class);
            String priceBookProductId = objectData.get("price_book_product_id", String.class);
            Boolean isMultipleUnit = productData.get("is_multiple_unit", Boolean.class);
            if(!Objects.equals(ProductConstants.Status.OFF.getStatus(), productStatus)
                    && !Objects.equals(lifeStatus, "invalid")){
                if (isMultipleUnit) {
                    String actualUnit = objectData.get("actual_unit", String.class);
                    String otherUnit = objectData.get("other_unit", String.class);
                    MutipleUnitInfo.CalculateParam param = new MutipleUnitInfo.CalculateParam();
                    param.setProductId(productId);
                    param.setCount(Double.valueOf(quantity));
                    param.setUnitId(actualUnit);
                    param.setOtherUnitId(otherUnit);
                    param.setPriceBookProductId(priceBookProductId);
                    calcPriceByUnitParam.add(param);
                }
            }
        });

        getMultiUnitPrice(multiUnitPrice, calcPriceByUnitParam);

        details.forEach(orderProduct->{
            String priceBookProductId = orderProduct.getOrDefault("price_book_product_id", "").toString();
            String productId = orderProduct.get("product_id").toString();
            String price = productPrice.get(productId);
            if(StringUtils.isNotEmpty(priceBookProductId)){
                price = priceBookProductPrice.get(priceBookProductId);
            }
            if(calcPriceByUnitParam.contains(productId)){
                price = String.valueOf(multiUnitPrice.get(productId));
            }
            orderProduct.put("product_price", price);
        });
    }


    private void getMultiUnitPrice(Map<String, Double>  idToPrice, List<MutipleUnitInfo.CalculateParam> calcPriceByUnitParam){
        MutipleUnitInfo.CalcUnitPriceArg arg = new MutipleUnitInfo.CalcUnitPriceArg(Lists.newArrayList(calcPriceByUnitParam));
        MutipleUnitInfo.CalculateResult calculateResult = mutipleUnitService.calcPriceByUnit(
                new ServiceContext(RequestContextManager.getContext(), null, null), arg);

        List<MutipleUnitInfo.CalculateInfo> result = calculateResult.getCaclResult();
        if(CollectionUtils.notEmpty(result)){
            result.forEach(data->{
                String productId = data.getProductId();
                String priceBookProductId = data.getPriceBookProductId();
                Double price = data.getPrice();
                if(StringUtils.isEmpty(priceBookProductId)){
                    idToPrice.put(productId, price);
                }else{
                    idToPrice.put(priceBookProductId,price);
                }
            });
        }
    }

}
