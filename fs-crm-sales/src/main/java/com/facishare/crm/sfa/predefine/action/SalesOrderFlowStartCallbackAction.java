package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.utilities.proxy.SalesOrderBizProxy;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.SalesOrderInterceptorModel;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.salesorderparam.BulkInvalidAfterModel;
import com.facishare.crm.sfa.utilities.util.SoCommonUtils;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ContextManager;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardFlowStartCallbackAction;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;

/**
 * Created by yuanjl on 2018/7/18.
 */
@Slf4j
public class SalesOrderFlowStartCallbackAction extends StandardFlowStartCallbackAction {
    private static final SalesOrderBizProxy salesOrderBizProxy = SpringUtil.getContext().getBean(SalesOrderBizProxy.class);

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        if (!arg.isTriggerSynchronous()) {
            if (Objects.equals(arg.getTriggerType(), ApprovalFlowTriggerType.INVALID.getTriggerTypeCode())) {
                afterInterceptor(actionContext.getTenantId(), arg.getDataId());
            }
        }
        return newResult;
    }

    private void afterInterceptor(String tenantId, String dataId) {
        List<IObjectData> orderDataList = serviceFacade.findObjectDataByIdsIncludeDeleted(new User(tenantId, User.SUPPER_ADMIN_USER_ID),
                Lists.newArrayList(dataId), objectDescribe.getApiName());
        if (CollectionUtils.notEmpty(orderDataList)) {
            BulkInvalidAfterModel.Arg serviceArg = new BulkInvalidAfterModel.Arg();
            List<BulkInvalidAfterModel.BulkObj> bulkObjs = Lists.newArrayList();
            ObjectDataExt objectDataExt = ObjectDataExt.of(orderDataList.get(0));
            BulkInvalidAfterModel.BulkObj bulkObj = new BulkInvalidAfterModel.BulkObj();
            bulkObj.setDataId(objectDataExt.getId());
            bulkObj.setBeforeLifeStatus(objectDataExt.getLifeStatusBeforeInvalid());
            bulkObj.setAfterLifeStatus(objectDataExt.getLifeStatus().getCode());
            bulkObjs.add(bulkObj);
            serviceArg.setBulkObjs(bulkObjs);
            ServiceContext context = ContextManager.buildServiceContext("SalesOrderInterceptor",
                    "BulkInvalidAfter");
//            salesOrderInterceptorService.bulkInvalidAfter(context, serviceArg);
            SalesOrderInterceptorModel.BulkInvalidAfterResult bulkInvalidAfterResult =  salesOrderBizProxy.bulkInvalidAfter(serviceArg, SoCommonUtils.getCrmHeader(context.getTenantId(), context.getUser()));
            if(!bulkInvalidAfterResult.isSuccess()){
                throw new ValidateException(bulkInvalidAfterResult.getMessage());
            }

        }
    }
}