package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.utilities.constant.AvailableConstants;
import com.facishare.crm.sfa.utilities.validator.AvailableRangeValidator;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardBulkRecoverAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.stream.Collectors;

public class AvailableRangeBulkRecoverAction extends StandardBulkRecoverAction {

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        if (CollectionUtils.notEmpty(dataList)) {
            int size = dataList.stream()
                    .filter(r -> AvailableConstants.AvailableRangePriority.CONDITION.getPriority() == r.get(AvailableConstants.AvailableRangeField.PRIORITY, Integer.class))
                    .collect(Collectors.toList())
                    .size();
            if (size > 0) {
                AvailableRangeValidator.validateLimitCount(actionContext.getTenantId(), size);
            }
        }
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return arg.getIdList();
    }

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        resetCalculateStatus();
        return result;
    }

    private void resetCalculateStatus() {
        if (CollectionUtils.empty(allHandledDataList)) {
            return;
        }
        User user = new User(actionContext.getTenantId(), User.SUPPER_ADMIN_USER_ID);
        for (IObjectData data : allHandledDataList) {
            data.set(AvailableConstants.AvailableRangeField.CALCULATE_STATUS,
                    AvailableConstants.CalculateStatus.CALCULATING.getStatus());
        }
        List<String> updateFields = Lists.newArrayList(AvailableConstants.AvailableRangeField.CALCULATE_STATUS);
        serviceFacade.batchUpdateByFields(user, allHandledDataList, updateFields);
    }
}
