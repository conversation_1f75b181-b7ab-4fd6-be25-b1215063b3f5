package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.utilities.constant.NewOppportunityConstants;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.paas.appframework.core.predef.action.StandardCloneAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.List;
import java.util.Map;

public class NewOpportunityCloneAction extends StandardCloneAction {
    @Override
    public void filterOrResetFieldValue(IObjectDescribe objectDescribe, IObjectData objectData, List<IObjectDescribe> detailDescribes, Map<String, List<IObjectData>> detailDataMap) {
        super.filterOrResetFieldValue(objectDescribe, objectData, detailDescribes, detailDataMap);
        if (!SFAConfigUtil.isNewOpportunityEnabled(actionContext.getTenantId())) {
            objectData.set(NewOppportunityConstants.NewOpportunityField.PRICEBOOKID.getApiName(), null);
        }
    }
}
