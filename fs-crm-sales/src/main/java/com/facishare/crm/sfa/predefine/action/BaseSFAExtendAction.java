package com.facishare.crm.sfa.predefine.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.model.SFAObjectPoolCommon;
import com.facishare.crm.sfa.predefine.enums.ActionCodeEnum;
import com.facishare.crm.sfa.predefine.exception.SFABusinessException;
import com.facishare.crm.sfa.predefine.exception.SFAErrorCode;
import com.facishare.crm.sfa.utilities.enums.LifeStatusEnum;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.flow.ApprovalFlowStartResult;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

import static com.facishare.crm.sfa.utilities.constant.AccountConstants.Field.EXPIRE_TIME;
import static com.facishare.crm.sfa.utilities.constant.AccountConstants.Field.EXTEND_DAYS;

/**
 * @IgnoreI18nFile
 */
@Slf4j
public abstract class BaseSFAExtendAction extends BaseObjectPoolAction<BaseSFAExtendAction.Arg, SFAObjectPoolCommon.Result> {

    private static final IObjectDataService objectDataService = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);


    @Override
    protected ObjectAction getObjectAction() {
        return ObjectAction.EXTEND_EXPIRETIME;
    }

    @Override
    protected String getObjectPoolId(Arg arg) {
        return null;
    }

    @Override
    protected String getUserId(Arg arg) {
        return actionContext.getUser().getUserId();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList("ExtendExpireTime");
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return arg.getObjectIDs();
    }

    @Override
    protected void doFunPrivilegeCheck() {
        if(arg.isSkipFunctionCheck()){
            return;
        }
        super.doFunPrivilegeCheck();
    }

    @Override
    protected boolean skipCheckButtonConditions() {
        return arg.isSkipButtonConditions() || super.skipCheckButtonConditions();
    }

    @Override
    protected boolean skipPreFunction() {
        return arg.isSkipPreAction() ||  super.skipPreFunction();
    }

    @Override
    protected boolean needSkipButtonConditions() {
        return arg.isSkipButtonConditions() ||super.needSkipButtonConditions();
    }

    @Override
    protected void doDataPrivilegeCheck() {
        if(arg.isSkipFunctionCheck()){
            return;
        }
        super.doDataPrivilegeCheck();
    }



    @Override
    protected Map<String, Map<String, Object>> approvalFlowTriggerMap() {
        Map<String, Map<String, Object>> result = Maps.newHashMap();
        for (IObjectData objectData : dataList) {
            Map<String, Object> fieldMap = Maps.newHashMap();
            Map<String, Object> params = Maps.newHashMap();
            if(arg.getArgs()!=null){
                params = arg.getArgs();
            }else {
                params.put("extendDays", arg.getExtendDays());
                params.put("extendReason", arg.getExtendReason());
            }
            fieldMap.put("params", JSON.toJSONString(params));
            fieldMap.put("buttonName", "申请延期");
            fieldMap.put("buttonDescription", "申请延期");
            result.put(objectData.getId(), fieldMap);
        }
        return result;
    }

    @Override
    protected Map<String, Map<String, Object>> approvalCallBackMap() {
        Map<String, Map<String, Object>> result = Maps.newHashMap();
        for (IObjectData objectData : dataList) {
            ObjectDataDocument data = arg.getArgs();
            if(data != null ){
                result.put(objectData.getId(), data);
            }else{
                Map<String, Object> fieldMap = Maps.newHashMap();
                fieldMap.put("extendDays", arg.getExtendDays());
                fieldMap.put("extendReason", arg.getExtendReason());
                result.put(objectData.getId(), fieldMap);
            }
        }
        return result;
    }

    @Override
    protected SFAObjectPoolCommon.Result doAct(Arg arg) {
        if (needTriggerApprovalFlow()) {
            String messageContent= "延期天数："+arg.getExtendDays()+"天；延期理由："+arg.getExtendReason();
            serviceFacade.logWithCustomMessage(actionContext.getUser(), EventType.MODIFY, ActionType.EXTEND_EXPIRETIME, objectDescribe, Lists.newArrayList(dataList.get(0)), messageContent);
            Map<String, ApprovalFlowStartResult> resultMap = this.startApprovalFlow(this.dataList, ApprovalFlowTriggerType.EXTEND_EXPIRETIME.getId(), approvalFlowTriggerMap(), approvalCallBackMap());
            if (!ApprovalFlowStartResult.getNeedTriggerChangeOwnerAfterActionEnum().contains(resultMap.getOrDefault((this.dataList.get(0)).getId(), ApprovalFlowStartResult.APPROVAL_NOT_EXIST))) {
                return SFAObjectPoolCommon.Result.builder().build();
            }else {// 无审批流
                arg.setPass(true);
                updateDataFields();
            }
        }else {
            if (CollectionUtils.empty(dataList)){
                return SFAObjectPoolCommon.Result.builder().build();
            }
            updateDataFields();
        }
        return SFAObjectPoolCommon.Result.builder().build();
    }

    @Override
    protected SFAObjectPoolCommon.Result after(Arg arg, SFAObjectPoolCommon.Result result) {
        log.info("BaseSFAExtendAction:arg:{},result:{}",arg,result);
        result =  super.after(arg, result);
        if (CollectionUtils.notEmpty(arg.getObjectIDs()) && arg.isPass()) {
            for (String s : arg.getObjectIDs()) {
                // 发送重算到期时间的task
                recalculateTaskService.send(actionContext.getTenantId(), s, actionContext.getObjectApiName(), ActionCodeEnum.EXTEND,arg.getExtendDays());
            }
        }
        return result;
    }


    @Override
    protected boolean needTriggerApprovalFlow() {
        return !arg.isSkipTriggerApprovalFlow();
    }

    private void updateDataFields() {
        List<IObjectData> updateObjectDataList = Lists.newArrayList();
        if (CollectionUtils.empty(dataList)){
            return;
        }
        IObjectData objectData  = dataList.get(0);
        if (StringUtils.isEmpty(objectData.get(EXPIRE_TIME, String.class))) {
            log.info("extendAction expire_time is null tenantId:{},objectId:{}",objectData.getTenantId(),objectData.getId());
            return;
        }
        IObjectData updateObjectData = new ObjectData();
        updateObjectData.setId(objectData.getId());
        updateObjectData.setTenantId(objectData.getTenantId());
        List<String> updateFieldList = Lists.newArrayList();
        if (!LifeStatusEnum.NORMAL.value.equals(objectData.get("life_status",String.class))) {
            updateObjectData.set("life_status", LifeStatusEnum.NORMAL.value);
            updateFieldList.add("life_status");
        }
        updateObjectData.setDescribeApiName(actionContext.getObjectApiName());
        updateObjectDataList.add(updateObjectData);
        if (GrayUtil.isExpireTimeExtend(objectData.getTenantId())) {
            Double extendDays;
            String extendDaysStr = objectData.get(EXTEND_DAYS, String.class);
            if (StringUtils.isNotBlank(extendDaysStr)){
                extendDays = Double.parseDouble(extendDaysStr) + arg.getExtendDays();
            }else {
                extendDays = arg.getExtendDays();
            }
            if (extendDays != null){
                updateObjectData.set(EXTEND_DAYS,extendDays);
                updateFieldList.add(EXTEND_DAYS);
            }
        }
        if (CollectionUtils.empty(updateFieldList)){
            return;
        }
        try {
            objectDataService.batchUpdateWithField(updateObjectDataList, updateFieldList, AccountUtil.getDefaultActionContextByApiName(actionContext.getUser(), actionContext.getObjectApiName()));
        } catch (Exception metadataError) {
            throw new SFABusinessException(metadataError.getMessage(), SFAErrorCode.ACCOUNT_COMMON_ERROR);
        }
    }


    @Override
    protected String getButtonApiName() {
        return ObjectAction.EXTEND_EXPIRETIME.getButtonApiName();
    }

    @Override
    protected IObjectData getPreObjectData() {
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIdsIncludeDeleted(actionContext.getUser(), arg.getObjectIDs(), actionContext.getObjectApiName());
        if (objectDataList != null && objectDataList.size() != 0) {
            return objectDataList.get(0);
        }
        return null;
    }

    @Override
    protected IObjectData getPostObjectData() {
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIdsIncludeDeleted(actionContext.getUser(), arg.getObjectIDs(), actionContext.getObjectApiName());
        if (objectDataList != null && objectDataList.size() != 0) {
            return objectDataList.get(0);
        }
        return null;
    }

    @Override
    protected Map<String, Object> getArgs() {
        if(arg.getArgs() != null) {
            return ObjectDataExt.toMap(arg.getArgs().toObjectData());
        }
        Map<String,Object> map = Maps.newHashMap();
        map.put("extendDays",arg.getExtendDays());
        map.put("extendReason",arg.getExtendReason());
        return map;
    }

    @Data
    @NoArgsConstructor
    public static class Arg extends SFAObjectPoolCommon.Arg {
        // 延期天数
        private Double extendDays;
        // 延期理由
        private String extendReason;
        // 回调用时审批通过
        private boolean pass;

        private ObjectDataDocument args;
    }
}
