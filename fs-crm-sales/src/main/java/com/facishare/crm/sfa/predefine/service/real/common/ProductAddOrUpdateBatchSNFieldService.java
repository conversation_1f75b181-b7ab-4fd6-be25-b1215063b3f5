package com.facishare.crm.sfa.predefine.service.real.common;

import com.facishare.crm.openapi.Utils;
import org.springframework.stereotype.Component;

@Component
public class ProductAddOrUpdateBatchSNFieldService extends AbstractObjectAddOrUpdatePredefinedFieldService {
    @Override
    public String getObjectApiName() {
        return Utils.PRODUCT_API_NAME;
    }

    @Override
    public String getFieldApiName() {
        return "batch_sn";
    }

    @Override
    public String getFieldDescribeJson() {
        return "{\n" +
                "      \"type\": \"select_one\",\n" +
                "      \"define_type\": \"package\",\n" +
                "      \"is_index\": true,\n" +
                "      \"is_active\": true,\n" +
                "      \"is_need_convert\": false,\n" +
                "      \"api_name\": \"batch_sn\",\n" +
                "      \"is_required\": true,\n" +
                "      \"status\": \"released\",\n" +
                "      \"label\": \"批次与序列号管理\",\n" +
                "      \"is_unique\": false,\n" +
                "      \"default_value\": \"1\",\n" +
                "      \"description\": \"批次与序列号管理\",\n" +
                "      \"is_extend\": false,\n" +
                "      \"config\": {\n" +
                "        \"edit\": 1,\n" +
                "        \"enable\": 0,\n" +
                "        \"display\": 1,\n" +
                "        \"remove\": 0,\n" +
                "        \"attrs\": {\n" +
                "          \"is_required\": 0\n" +
                "        }\n" +
                "      },\n" +
                "      \"options\": [\n" +
                "        {\n" +
                "          \"label\": \"不开启\",\n" +
                "          \"value\": \"1\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"label\": \"开启批次管理\",\n" +
                "          \"value\": \"2\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"label\": \"开启序列号管理\",\n" +
                "          \"value\": \"3\"\n" +
                "        }\n" +
                "      ]\n" +
                "    }";
    }
}
