package com.facishare.crm.sfa.predefine.service;

import com.facishare.crm.sfa.model.SFAObjectPoolCommon;
import com.facishare.crm.sfa.predefine.service.model.GetPoolMembers;
import com.facishare.crm.sfa.predefine.service.model.ObjectPoolModels;
import com.facishare.crm.sfa.predefine.service.model.ObjectPoolPermission;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;
import java.util.Map;

public interface IObjectPoolService {
    String getObjectPoolApiName();

    String getObjectApiName();

    String getObjectPoolKeyName();

    ObjectPoolPermission.ObjectPoolPermissions getPoolPermission(String tenantId, String userId, String objectPoolId, String outTenantId);

    List<IObjectData> getObjectPoolByIds(String tenantId, List<String> objectIds);

    IObjectData getObjectPoolById(String tenantId, String objectId);

    SFAObjectPoolCommon.Result choose(User user, String objectPoolId, List<String> objectIds, String eventId, String partnerId);

    SFAObjectPoolCommon.Result move(User user, String objectPoolId, List<String> objectIds, String eventId);

    SFAObjectPoolCommon.Result back(User user, String objectPoolId, List<String> objectIds, Integer operationType, String backReason, String backReasonOther, String eventId, boolean isPrmOpen);

    SFAObjectPoolCommon.Result allocate(User user, String objectPoolId, List<String> objectIds, String owner, String eventId, Long outTenantId, Long outOwnerId, String partnerId);

    SFAObjectPoolCommon.Result remove(User user, List<IObjectData> objectDataList, String owner, Boolean isKeepOwner, String eventId);

    Map<String, List<String>> getPoolMembersByIds(User user, List<String> poolIds);

    List<String> getPoolAdminById(User user, String poolId);

    Map<String, List<String>> getPoolAdminByIds(User user, List<String> poolIds);

    Map<String, List<String>> getInAndOutPoolAdminById(User user, String poolId);

    List<IObjectData> getAllObjectPoolList(String tenantId, String userId, String outTenantId);

    List<IObjectData> getAllVisiblePoolList(String tenantId, String userId, String outTenantId);

    boolean movePoolObjects(User user, String sourcePoolId, String targetPoolId);

    Map<String, ObjectPoolModels.PoolObjectsCountInfo> getPoolObjectCountInfo(User user, List<String> poolIds);

    SFAObjectPoolCommon.Result takeBack(User user, String objectPoolId, List<String> objectIds, String eventId, boolean isPrmOpen);

    Map<String, Integer> getPoolObjectUnDeletedCount(User user, List<String> poolIds);

    ObjectPoolPermission.ObjectPoolMemberType getPoolMemberType(String tenantId, String userId, String objectPoolId);

    Map<String, List<GetPoolMembers.PoolMember>> getPoolMembers(User user, List<String> poolIds);

    Map<String, Boolean> checkPoolInUse(User user, List<String> poolIds);

    /**
     * 根据传入的企业和人员查询公海/线索池列表
     *
     * @param tenantId
     * @param user
     * @return
     */
    List<IObjectData> getTargetObjectPoolList(String tenantId, User user, String action);

    /**
     * 根据传入的企业和人员查询公海/线索池列表
     *
     * @param tenantId
     * @param user
     * @return
     */
    List<IObjectData> getSortedLeadsPoolList(String tenantId, User user);

}
