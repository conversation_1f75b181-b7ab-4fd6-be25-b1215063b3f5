package com.facishare.crm.sfa.predefine.importobject;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.metadata.importobject.DefaultObjectImportProvider;
import com.facishare.paas.appframework.metadata.importobject.ImportObject;
import com.facishare.paas.appframework.metadata.importobject.ImportType;
import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.GetI18nKeyUtil;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.facishare.crm.sfa.predefine.SFAPreDefineObject.Refund;

/**
 * create by zhaoju on 2019/05/21
 */
@Component
public class RefundObjImportProvider extends DefaultObjectImportProvider {
    @Override
    public String getObjectCode() {
        return Refund.getApiName();
    }

    @Override
    public Optional<ImportObject> getImportObject(IObjectDescribe objectDescribe, IUniqueRule uniqueRule) {
        Optional<ImportObject> result= super.getImportObject(objectDescribe, uniqueRule);
        result.ifPresent(importObject -> {
            importObject.setObjectName(I18N.text(GetI18nKeyUtil.getDescribeDisplayNameKey(Refund.getApiName())));
            importObject.setOpenWorkFlow(true);
        });
        return result;
    }

    @Override
    protected ImportType getImportType(IObjectDescribe objectDescribe, IUniqueRule uniqueRule) {
        return ImportType.UNSUPPORT_UPDATE_IMPORT;
    }
}
