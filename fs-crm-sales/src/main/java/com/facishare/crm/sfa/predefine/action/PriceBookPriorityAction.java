package com.facishare.crm.sfa.predefine.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.constant.PriceBookConstants;
import com.facishare.crm.sfa.utilities.util.ConfigConstant;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.appframework.core.predef.action.StandardAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.stream.Collectors;

public class PriceBookPriorityAction extends PreDefineAction<PriceBookPriorityAction.Arg, PriceBookPriorityAction.Result> {

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(StandardAction.Priority.getFunPrivilegeCodes());
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        if (CollectionUtils.empty(arg.getObjectDataList())) {
            return Lists.newArrayList();
        } else {
            return arg.getObjectDataList().stream().map(ObjectDataDocument::getId).collect(Collectors.toList());
        }
    }

    //不校验数据权限
    @Override
    protected void doDataPrivilegeCheck() {

    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        validateMaxCount();
    }

    @Override
    protected Result doAct(Arg arg) {
        clearOriginalPriority();
        resetPriority();
        return PriceBookPriorityAction.Result.builder().build();
    }

    private void resetPriority() {
        if (CollectionUtils.empty(arg.getObjectDataList())) {
            return;
        }
        List<String> idList = dataList.stream().map(IObjectData::getId).collect(Collectors.toList());
        int priorityOrder = 1;
        List<IObjectData> toUpdateDataList = Lists.newArrayList();
        for (ObjectDataDocument objectDataDocument : arg.getObjectDataList()) {
            if (idList.contains(objectDataDocument.getId())) {
                IObjectData objectData = objectDataDocument.toObjectData();
                objectData.set(PriceBookConstants.Field.PRIORITY.getApiName(), priorityOrder);
                toUpdateDataList.add(objectData);
                priorityOrder++;
            }
        }
        if (CollectionUtils.notEmpty(toUpdateDataList)) {
            serviceFacade.batchUpdateByFields(actionContext.getUser(), toUpdateDataList,
                    Lists.newArrayList(PriceBookConstants.Field.PRIORITY.getApiName()));
        }
    }

    private void validateMaxCount() {
        if (CollectionUtils.notEmpty(arg.getObjectDataList())
                && arg.getObjectDataList().size() > ConfigConstant.MAX_PRIORITY_PRICE_BOOK_COUNT) {
            throw new ValidateException(String.format(I18N.text("sfa.over_max_count.warn"),
                    ConfigConstant.MAX_PRIORITY_PRICE_BOOK_COUNT));
        }
    }

    private void clearOriginalPriority() {
        SearchTemplateQuery searchTemplateQuery = buildQueryTemplateWithoutPermission();
        QueryResult<IObjectData> objectDataQueryResult = serviceFacade.findBySearchQuery(actionContext.getUser(),
                SFAPreDefineObject.PriceBook.getApiName(), searchTemplateQuery);
        if (objectDataQueryResult != null && CollectionUtils.notEmpty(objectDataQueryResult.getData())) {
            objectDataQueryResult.getData().forEach(data -> data.set(PriceBookConstants.Field.PRIORITY.getApiName(), null));
            serviceFacade.batchUpdateByFields(actionContext.getUser(), objectDataQueryResult.getData(),
                    Lists.newArrayList(PriceBookConstants.Field.PRIORITY.getApiName()));
        }
    }

    private SearchTemplateQuery buildQueryTemplateWithoutPermission() {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        List<IFilter> filters = Lists.newArrayList();
        IFilter filter = new Filter();
        filter.setFieldValues(Lists.newArrayList("0","1"));
        filter.setOperator(Operator.IN);
        filter.setFieldName("is_deleted");
        filters.add(filter);
        IFilter priorityFilter = new Filter();
        priorityFilter.setFieldName(PriceBookConstants.Field.PRIORITY.getApiName());
        priorityFilter.setOperator(Operator.ISN);
        priorityFilter.setFieldValues(Lists.newArrayList());
        filters.add(priorityFilter);
        searchTemplateQuery.setFilters(filters);
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(ConfigConstant.MAX_PRIORITY_PRICE_BOOK_COUNT);
        searchTemplateQuery.setPermissionType(0);
        searchTemplateQuery.setNeedReturnCountNum(Boolean.FALSE);
        searchTemplateQuery.setNeedReturnQuote(Boolean.FALSE);
        return searchTemplateQuery;
    }

    @Data
    public static class Arg {
        @JSONField(name = "M1")
        @JsonProperty("data_list")
        private List<ObjectDataDocument> objectDataList;
    }

    @Data
    @Builder
    @NoArgsConstructor
    public static class Result {

    }
}
