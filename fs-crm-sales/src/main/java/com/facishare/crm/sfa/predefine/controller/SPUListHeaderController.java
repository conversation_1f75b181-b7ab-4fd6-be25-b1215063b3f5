package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.ILayout;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/2/25 10:51
 * @instruction
 */
public class SPUListHeaderController extends StandardListHeaderController{


	@Override
	protected Result after(Arg arg, Result result) {
		super.after(arg, result);

		ILayout layout = result.getLayout().toLayout();
		List<IButton> buttons = LayoutExt.of(layout).getButtons().stream().filter(o -> !ObjectAction.DUPLICATECHECK.getActionCode().equals(o.getAction()))
				.collect(Collectors.toList());
		layoutExt.setButtons(buttons);

		return result;
	}
}
