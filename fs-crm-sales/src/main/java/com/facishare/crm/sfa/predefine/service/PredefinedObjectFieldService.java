package com.facishare.crm.sfa.predefine.service;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.model.ServiceResult;
import com.facishare.crm.sfa.predefine.service.real.common.AbstractDependenceAddOrUpdatePredefinedFieldService;
import com.facishare.crm.sfa.predefine.service.real.common.AbstractObjectAddOrUpdatePredefinedFieldService;
import com.facishare.crm.sfa.predefine.service.real.common.AddOrUpdateFieldServiceManager;
import com.facishare.crm.sfa.predefine.service.real.common.DependenceAddOrUpdateFieldManager;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/6/18 4:35 下午
 * @illustration
 */

@ServiceModule("predefined_object_field")
@Service
public class PredefinedObjectFieldService {
    @Autowired
    private AddOrUpdateFieldServiceManager addOrUpdateFieldServiceManager;

    @Autowired
    private DependenceAddOrUpdateFieldManager dependenceAddOrUpdateFieldManager;

    @ServiceMethod("add_or_update_predefined_field")
    public ServiceResult addOrUpdatePredefined(PredefinedObjectFieldModel.AddOrUpdatePredefinedArg arg, ServiceContext serviceContext) {
        if (Utils.SPU_API_NAME.equals(arg.getObjectApiName()) && "commodity_label".equals(arg.getFieldApiName())) {
            if (!SFAConfigUtil.isSpuOpen(serviceContext.getTenantId())) {
                arg.setObjectApiName(Utils.PRODUCT_API_NAME);
            }
        }
        AbstractObjectAddOrUpdatePredefinedFieldService targetService = addOrUpdateFieldServiceManager.getTargetService(arg.getObjectApiName(), arg.getFieldApiName());
        return targetService.updateOrInsertField(serviceContext.getUser());
    }


    @ServiceMethod("dependence_add_or_update_predefined_field")
    public ServiceResult dependenceAddOrUpdatePredefined(PredefinedObjectFieldModel.DependenceAddOrUpdatePredefinedArg arg, ServiceContext serviceContext) {
        AbstractDependenceAddOrUpdatePredefinedFieldService targetService = dependenceAddOrUpdateFieldManager.getTargetService(arg.getBeanName());
        return targetService.updateOrInsertField(serviceContext.getUser(), arg.getApiName(), arg.getFieldApiName(), arg.getFieldJson());
    }



}
