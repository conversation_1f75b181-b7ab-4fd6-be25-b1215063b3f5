package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.constant.TieredPriceConstants;

import java.util.Comparator;

import static com.facishare.crm.sfa.predefine.service.cpq.CPQTieredPriceServiceImpl.parseDouble;

/**
 * <AUTHOR> 2019-11-11
 * @instruction
 */
public class TieredPriceBookRuleDetailListController extends SODetailListController {
    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        after.getDataList().sort(Comparator.comparing(o -> parseDouble(o.get(TieredPriceConstants.TieredPriceRule.StartCount.getFieldName())), Comparator.nullsFirst(Double::compareTo)));

        return after;
    }
}
