package com.facishare.crm.sfa.predefine.privilege;

import com.facishare.crmcommon.constants.SystemConstants;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.model.ObjectPoolPermission;
import com.facishare.crm.sfa.utilities.constant.AccountConstants;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.service.dto.UserInfo;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Component
public class AccountDataPrivilegeProvider extends PoolDataPrivilegeProvider {
    @Autowired
    protected OrgService orgService;

    @Override
    public String getApiName() {
        return SFAPreDefineObject.Account.getApiName();
    }

    @Override
    public Map<String, Map<String, Permissions>> checkBusinessPrivilege(User user, Map<String, Permissions> dataPrivilegeMap,
                                                                        List<IObjectData> dataList,
                                                                        List<String> actionCodes) {
        Boolean isCrmAdmin = isCrmAdmin(user);
        Map<String, Map<String, Permissions>> resultMap = Maps.newHashMap();
        List<String> poolIds = AccountUtil.getPoolIds(dataList);
        List<IObjectData> poolDataList = getObjectPoolByIds(user.getTenantId(), poolIds);
        Map<String, ObjectPoolPermission.ObjectPoolPermissions> poolPermissionsMap = getPoolPermissionsMap(user,poolIds);
        actionCodes.forEach(actionCode -> {
            Map<String, Permissions> businessPrivilegeMap = Maps.newHashMap();
            for (IObjectData objectData : dataList) {
                Permissions permissions = dataPrivilegeMap.computeIfAbsent(objectData.getId(), x -> Permissions.NO_PERMISSION);
                ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
                String bizStatus = AccountUtil.getStringValue(objectData, AccountConstants.Field.BIZ_STATUS, AccountConstants.AccountBizStatus.UN_ALLOCATED.getValue());
                String poolId = AccountUtil.getStringValue(objectData, AccountConstants.Field.HIGH_SEAS_ID, "");
                String fillingChecker = AccountUtil.getEmployee(objectData, AccountConstants.Field.FILLING_CHECKER_ID);
                ObjectLifeStatus lifeStatus = objectDataExt.getLifeStatus();
                String lockStatus = objectDataExt.getLockStatus();
                if (ObjectAction.SALE_RECORD.getActionCode().equals(actionCode)) {
                    permissions = Permissions.READ_WRITE;
                    if (!isCrmAdmin && StringUtils.isNotEmpty(poolId)) {
                        if (AccountConstants.AccountBizStatus.UN_ALLOCATED.getValue().equals(bizStatus)) {
                            if (isPoolMember(poolId, poolPermissionsMap) && !isPoolAdmin(poolId, poolPermissionsMap)) {
                                Optional<IObjectData> poolData = poolDataList.stream().filter(x -> poolId.equals(x.getId())).findFirst();
                                if (poolData.isPresent()) {
                                    boolean allowMemberSendFeed = AccountUtil.getBooleanValue(poolData.get(),"allow_member_send_feed", false);
                                    if (!allowMemberSendFeed) {
                                        permissions = Permissions.NO_PERMISSION;
                                    }
                                }
                            }
                        }
                    }
                } else if (ObjectAction.EDIT_TEAM_MEMBER.getActionCode().equals(actionCode)) {
                    if (ObjectLifeStatus.INVALID.equals(lifeStatus)) {
                        permissions = Permissions.NO_PERMISSION;
                    } else if (StringUtils.isNotEmpty(poolId) && isPoolAdmin(poolId, poolPermissionsMap)
                            && AccountConstants.AccountBizStatus.UN_ALLOCATED.getValue().equals(bizStatus)) {
                        permissions = Permissions.READ_WRITE;
                    }
                } else if (ObjectAction.UNLOCK.getActionCode().equals(actionCode)) {
                    if (SystemConstants.LockStatus.UnLock.value.equals(lockStatus)
                            || ObjectLifeStatus.INVALID.equals(lifeStatus)
                            || ObjectLifeStatus.IN_CHANGE.equals(lifeStatus)) {
                        permissions = Permissions.NO_PERMISSION;
                    } else if (StringUtils.isNotEmpty(poolId) && isPoolAdmin(poolId, poolPermissionsMap)
                            && AccountConstants.AccountBizStatus.UN_ALLOCATED.getValue().equals(bizStatus)) {
                        permissions = Permissions.READ_WRITE;
                    }
                } else if (ObjectAction.LOCK.getActionCode().equals(actionCode)) {
                    if (SystemConstants.LockStatus.Locked.value.equals(lockStatus)
                            || ObjectLifeStatus.INVALID.equals(lifeStatus)
                            || ObjectLifeStatus.IN_CHANGE.equals(lifeStatus)) {
                        permissions = Permissions.NO_PERMISSION;
                    } else if (StringUtils.isNotEmpty(poolId) && isPoolAdmin(poolId, poolPermissionsMap)
                            && AccountConstants.AccountBizStatus.UN_ALLOCATED.getValue().equals(bizStatus)) {
                        permissions = Permissions.READ_WRITE;
                    }
                }else if (ObjectAction.UPDATE.getActionCode().equals(actionCode)) {
                    permissions = getUpdatePermissions(isCrmAdmin, poolPermissionsMap, permissions, bizStatus, poolId, lifeStatus, lockStatus);
                } else if (ObjectAction.RETURN.getActionCode().equals(actionCode)) {
                    if (!ObjectLifeStatus.NORMAL.equals(lifeStatus)
                            || SystemConstants.LockStatus.Locked.value.equals(lockStatus)
                            || !AccountConstants.AccountBizStatus.ALLOCATED.getValue().equals(bizStatus)) {
                        permissions = Permissions.NO_PERMISSION;
                    }
                } else if (ObjectAction.INVALID.getActionCode().equals(actionCode)) {
                    if (SystemConstants.LockStatus.Locked.value.equals(lockStatus)) {
                        permissions = Permissions.NO_PERMISSION;
                    } else{
                        if(ObjectLifeStatus.NORMAL.equals(lifeStatus)
                                || ObjectLifeStatus.INEFFECTIVE.equals(lifeStatus)){
                            if (StringUtils.isNotEmpty(poolId) && isPoolAdmin(poolId, poolPermissionsMap)
                                    && AccountConstants.AccountBizStatus.UN_ALLOCATED.getValue().equals(bizStatus)) {
                                permissions = Permissions.READ_WRITE;
                            }
                        } else if(ObjectLifeStatus.UNDER_REVIEW.equals(lifeStatus) && StringUtils.isNotEmpty(fillingChecker)){
                        }
                    }
                } else if (ObjectAction.UPLOAD_DELETE_ATTACH.getActionCode().equals(actionCode)) {
                    if(SystemConstants.LifeStatus.Invalid.equals(lifeStatus)){
                        permissions = Permissions.NO_PERMISSION;
                    } else if (StringUtils.isNotEmpty(poolId)
                            && !isPoolAdmin(poolId, poolPermissionsMap)
                            && AccountConstants.AccountBizStatus.UN_ALLOCATED.getValue().equals(bizStatus)) {
                        permissions = Permissions.NO_PERMISSION;
                    }
                } else if (ObjectAction.CHANGE_AUDITOR.getActionCode().equals(actionCode)) {
                    if (ObjectLifeStatus.UNDER_REVIEW.equals(lifeStatus)
                            && AccountConstants.AccountBizStatus.ALLOCATED.getValue().equals(bizStatus)
                            && (isFillingCheckerOrLeader(user, objectData) || isCrmAdmin)) {
                        permissions = Permissions.READ_WRITE;
                    } else {
                        permissions = Permissions.NO_PERMISSION;
                    }
                } else if (ObjectAction.CHANGE_OWNER.getActionCode().equals(actionCode)) {
                    if (ObjectLifeStatus.INVALID.equals(lifeStatus) || ObjectLifeStatus.IN_CHANGE.equals(lifeStatus)
                            || SystemConstants.LockStatus.Locked.value.equals(lockStatus)) {
                        permissions = Permissions.NO_PERMISSION;
                    }else if(StringUtils.isNotEmpty(poolId) && !isCrmAdmin){
                        String owner = AccountUtil.getOwner(objectData);
                        if(AccountConstants.AccountBizStatus.ALLOCATED.getValue().equals(bizStatus) && StringUtils.isNotBlank(owner)) {
                            Optional<IObjectData> poolData = poolDataList.stream().filter(x -> poolId.equals(x.getId())).findFirst();
                            if (poolData.isPresent()) {
                                boolean allowOwnerChangeOwner = AccountUtil.getBooleanValue(poolData.get(),"allow_admin_change_owner", false);
                                if (!allowOwnerChangeOwner ) {
                                    permissions = Permissions.NO_PERMISSION;
                                }
                            }
                        } else {
                            permissions = Permissions.NO_PERMISSION;
                        }
                    }
                } else if (ObjectAction.AUDIT.getActionCode().equals(actionCode)) {
                    if (ObjectLifeStatus.UNDER_REVIEW.equals(lifeStatus)
                            && AccountConstants.AccountBizStatus.ALLOCATED.getValue().equals(bizStatus)
                            && (isFillingCheckerOrLeader(user, objectData) || isCrmAdmin)) {
                        permissions = Permissions.READ_WRITE;
                    } else {
                        permissions = Permissions.NO_PERMISSION;
                    }
                } else if (ObjectAction.VIEW_DETAIL.getActionCode().equals(actionCode)) {
                    if (isCrmAdmin) {
                        permissions = Permissions.READ_WRITE;
                    } else if (ObjectLifeStatus.INVALID.equals(lifeStatus)) {
                        permissions = Permissions.NO_PERMISSION;
                    } else if ((ObjectLifeStatus.UNDER_REVIEW.equals(lifeStatus) || ObjectLifeStatus.INEFFECTIVE.equals(lifeStatus))
                            && AccountConstants.AccountBizStatus.ALLOCATED.getValue().equals(bizStatus)
                            && isFillingCheckerOrLeader(user, objectData)
                            && AccountUtil.isOpenAccountFillingCheck (user.getTenantId())) {
                        permissions = Permissions.READ_WRITE;
                    } else if (StringUtils.isNotEmpty(poolId)) {
                        if (isPoolAdmin(poolId, poolPermissionsMap)) {
                            permissions = Permissions.READ_WRITE;
                        } else if (isPoolMember(poolId, poolPermissionsMap)) {
                            if (AccountConstants.AccountBizStatus.UN_ALLOCATED.getValue().equals(bizStatus)) {
                                Optional<IObjectData> poolData = poolDataList.stream().filter(x -> poolId.equals(x.getId())).findFirst();
                                if (poolData.isPresent()) {
                                    boolean isVisibleToMember = AccountUtil.getBooleanValue(poolData.get(),"is_visible_to_member", false);
                                    if (isVisibleToMember) {
                                        permissions = Permissions.READ_WRITE;
                                    } else {
                                        permissions = Permissions.NO_PERMISSION;
                                    }
                                }
                            }
                        }
                    }
                } else if (ObjectAction.VIEW_FEED_CARD.getActionCode().equals(actionCode)) {
                    permissions = Permissions.READ_WRITE;
                    if (!isCrmAdmin && StringUtils.isNotEmpty(poolId)) {
                        if ((ObjectLifeStatus.IN_CHANGE.equals(lifeStatus) || ObjectLifeStatus.NORMAL.equals(lifeStatus))
                                && AccountConstants.AccountBizStatus.UN_ALLOCATED.getValue().equals(bizStatus)) {
                            if (isPoolMember(poolId, poolPermissionsMap) && !isPoolAdmin(poolId, poolPermissionsMap)) {
                                Optional<IObjectData> poolData = poolDataList.stream().filter(x -> poolId.equals(x.getId())).findFirst();
                                if (poolData.isPresent()) {
                                    boolean allowMemberSendFeed = AccountUtil.getBooleanValue(poolData.get(),"allow_member_view_feed", false);
                                    if (!allowMemberSendFeed) {
                                        permissions = Permissions.NO_PERMISSION;
                                    }
                                }
                            }
                        }
                    }
                } else if (ObjectAction.CHOOSE.getActionCode().equals(actionCode)) {
                    if (!ObjectLifeStatus.NORMAL.equals(lifeStatus)
                            || SystemConstants.LockStatus.Locked.value.equals(lockStatus)
                            || !AccountConstants.AccountBizStatus.UN_ALLOCATED.getValue().equals(bizStatus)
                            || StringUtils.isEmpty(poolId)) {
                        permissions = Permissions.NO_PERMISSION;
                    } else if (isPoolMember(poolId, poolPermissionsMap)) {
                        permissions = Permissions.READ_WRITE;
                    } else {
                        permissions = Permissions.NO_PERMISSION;
                    }
                } else if (ObjectAction.TAKE_BACK.getActionCode().equals(actionCode)) {
                    if (!ObjectLifeStatus.NORMAL.equals(lifeStatus)
                            || SystemConstants.LockStatus.Locked.value.equals(lockStatus)
                            || !AccountConstants.AccountBizStatus.ALLOCATED.getValue().equals(bizStatus)
                            || StringUtils.isEmpty(poolId)) {
                        permissions = Permissions.NO_PERMISSION;
                    } else if (isPoolAdmin(poolId, poolPermissionsMap)) {
                        permissions = Permissions.READ_WRITE;
                    } else {
                        permissions = Permissions.NO_PERMISSION;
                    }
                } else if (ObjectAction.MOVE.getActionCode().equals(actionCode)) {
                    if (!ObjectLifeStatus.NORMAL.equals(lifeStatus)
                            || SystemConstants.LockStatus.Locked.value.equals(lockStatus)) {
                        permissions = Permissions.NO_PERMISSION;
                    } else if (isCrmAdmin) {
                        permissions = Permissions.READ_WRITE;
                    } else if (StringUtils.isNotEmpty(poolId)) {
                        if (isPoolAdmin(poolId, poolPermissionsMap)) {
                            permissions = Permissions.READ_WRITE;
                        } else {
                            IObjectData poolData = objectPoolService.getObjectPoolById(getApiName(), user.getTenantId(), poolId);
                            if (poolData == null) {
                                permissions = Permissions.NO_PERMISSION;
                            } else {
                                boolean allowMemberMove = AccountUtil.getBooleanValue(poolData,"allow_member_move", false);
                                if (allowMemberMove) {
                                    permissions = Permissions.READ_WRITE;
                                } else {
                                    permissions = Permissions.NO_PERMISSION;
                                }
                            }
                        }
                    }
                } else if (ObjectAction.REMOVE.getActionCode().equals(actionCode)) {
                    if (!ObjectLifeStatus.NORMAL.equals(lifeStatus)
                            || SystemConstants.LockStatus.Locked.value.equals(lockStatus)) {
                        permissions = Permissions.NO_PERMISSION;
                    } else if (isCrmAdmin) {
                        permissions = Permissions.READ_WRITE;
                    } else if (StringUtils.isNotEmpty(poolId)) {
                        if (isPoolAdmin(poolId, poolPermissionsMap)) {
                            permissions = Permissions.READ_WRITE;
                        }else {
                            permissions = Permissions.NO_PERMISSION;
                        }
                    }
                } else if (ObjectAction.ALLOCATE.getActionCode().equals(actionCode)) {
                    if (!ObjectLifeStatus.NORMAL.equals(lifeStatus)
                            || SystemConstants.LockStatus.Locked.value.equals(lockStatus)
                            || StringUtils.isEmpty(poolId)) {
                        permissions = Permissions.NO_PERMISSION;
                    } else if (isPoolAdmin(poolId, poolPermissionsMap)) {
                        permissions = Permissions.READ_WRITE;
                    }else{
                        permissions = Permissions.NO_PERMISSION;
                    }
                } else if (ObjectAction.ADD_ATTACH.getActionCode().equals(actionCode)) {
                    if (ObjectLifeStatus.INVALID.equals(lifeStatus)
                            || SystemConstants.LockStatus.Locked.value.equals(lockStatus)) {
                        permissions = Permissions.NO_PERMISSION;
                    } else if (StringUtils.isNotEmpty(poolId) && isPoolAdmin(poolId, poolPermissionsMap)) {
                        permissions = Permissions.READ_WRITE;
                    }
                } else if (ObjectAction.START_BPM.getActionCode().equals(actionCode)) {
                     if (!ObjectLifeStatus.NORMAL.equals(lifeStatus)
                            && !AccountConstants.AccountBizStatus.ALLOCATED.getValue().equals(bizStatus)) {
                        permissions = Permissions.NO_PERMISSION;
                    }
                } else if (ObjectAction.UPDATE_DEAL_STATUS.getActionCode().equals(actionCode)) {
                   if (!ObjectLifeStatus.NORMAL.equals(lifeStatus)
                                || !AccountConstants.AccountBizStatus.ALLOCATED.getValue().equals(bizStatus)) {
                        permissions = Permissions.NO_PERMISSION;
                    }
                } else if (ObjectAction.VIEW_ATTACH.getActionCode().equals(actionCode)) {

                } else if (ObjectAction.EXTEND_EXPIRETIME.getActionCode().equals(actionCode)){
                    if (SystemConstants.LockStatus.Locked.value.equals(lockStatus)
                            || !isAllowExtend(objectData)
                            || RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_700)){
                        permissions = Permissions.NO_PERMISSION;
                    }
                }
                businessPrivilegeMap.put(objectData.getId(), permissions);
            }
            resultMap.put(actionCode, businessPrivilegeMap);
        });

        return resultMap;
    }

    public static Permissions getUpdatePermissions(Boolean isCrmAdmin, Map<String, ObjectPoolPermission.ObjectPoolPermissions> poolPermissionsMap, Permissions permissions, String bizStatus, String poolId, ObjectLifeStatus lifeStatus, String lockStatus) {
        if (ObjectLifeStatus.INVALID.equals(lifeStatus)) {
            permissions = Permissions.NO_PERMISSION;
        } else if(SystemConstants.LockStatus.UnLock.value.equals(lockStatus)){
            if(isCrmAdmin){
                permissions = Permissions.READ_WRITE;
            } else if (StringUtils.isNotEmpty(poolId)
                    && AccountConstants.AccountBizStatus.UN_ALLOCATED.getValue().equals(bizStatus)) {
                if (isPoolAdmin(poolId, poolPermissionsMap)) {
                    permissions = Permissions.READ_WRITE;
                } else {
                    permissions = Permissions.NO_PERMISSION;
                }
            }
        }

        return permissions;
    }

    public static boolean isPoolAdmin(String poolId, Map<String, ObjectPoolPermission.ObjectPoolPermissions> poolPermissionsMap) {
        if (CollectionUtils.empty(poolPermissionsMap)) {
            return false;
        }
        ObjectPoolPermission.ObjectPoolPermissions poolPermissions = poolPermissionsMap
                .computeIfAbsent(poolId, x -> ObjectPoolPermission.ObjectPoolPermissions.NO_PERMISSION);
        return poolPermissions.isPoolAdmin();
    }

    private boolean isPoolMember(String poolId, Map<String, ObjectPoolPermission.ObjectPoolPermissions> poolPermissionsMap) {
        if (CollectionUtils.empty(poolPermissionsMap)) {
            return false;
        }
        ObjectPoolPermission.ObjectPoolPermissions poolPermissions = poolPermissionsMap
                .computeIfAbsent(poolId, x -> ObjectPoolPermission.ObjectPoolPermissions.NO_PERMISSION);
        return poolPermissions.isPoolMember();
    }

    private boolean isFillingCheckerOrLeader(User user, IObjectData objectData) {
        String fillingChecker = AccountUtil.getEmployee(objectData, AccountConstants.Field.FILLING_CHECKER_ID);
        if (StringUtils.isEmpty(fillingChecker)) {
            return false;
        }
        if (fillingChecker.equals(user.getUserId())) {
            return true;
        }
        List<UserInfo> allSubEmployees = orgService.getSubordinatesByUserId(user.getTenantId(), user.getUserId(), user.getUserId(), true);//获取所有下级人员(级联到最下层）
        if (CollectionUtils.notEmpty(allSubEmployees) && allSubEmployees.contains(fillingChecker)) {
            return true;
        }

        return false;
    }
}
