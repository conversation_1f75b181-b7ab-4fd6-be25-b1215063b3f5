package com.facishare.crm.sfa.predefine.button;

import com.facishare.crm.sfa.utilities.util.ButtonUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.metadata.button.SpecialButtonProvider;
import com.facishare.paas.metadata.ui.layout.IButton;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by luxin on 2018/8/6.
 */
public abstract class AbstractSfaSpecialButtonProvider implements SpecialButtonProvider {

    @Override
    public List<IButton> getSpecialButtons() {
        List<IButton> buttons = new ArrayList<>(3);
        buttons.add(ButtonUtils.buildButton(ObjectAction.CHANGE_PARTNER));
        buttons.add(ButtonUtils.buildButton(ObjectAction.DELETE_PARTNER));
        buttons.add(ButtonUtils.buildButton(ObjectAction.CHANGE_PARTNER_OWNER));
        return buttons;
    }

}
