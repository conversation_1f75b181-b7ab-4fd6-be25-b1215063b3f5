package com.facishare.crm.sfa.utilities.proxy.model;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by renlb on 2019/1/17.
 */
public interface IsBalanceCreditEnough {
    @Data
    @Builder
    class Arg implements Serializable {
        String orderId;
        String customerId;
        double orderAmount;
        String settleType;
        double oldOrderAmount;
        Boolean forceCommit = Boolean.FALSE;
    }

    @Data
    @Builder
    class Result {
        CustomerAccountEnoughModel result;
    }

    @Data
    class CustomerAccountEnoughModel {
        Boolean isEnough;
        String message;
    }
}
