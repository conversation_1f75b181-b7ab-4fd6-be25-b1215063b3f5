package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.SpuSkuService;
import com.facishare.crm.sfa.predefine.service.model.MultiUnitData;
import com.facishare.crm.sfa.predefine.service.model.SearchUsedSpecModel;
import com.facishare.crm.sfa.predefine.service.real.MultiUnitService;
import com.facishare.crm.sfa.predefine.service.real.MultiUnitServiceImpl;
import com.facishare.crm.sfa.utilities.common.convert.ConvertUtil;
import com.facishare.crm.sfa.utilities.constant.ProductConstants;
import com.facishare.crm.sfa.utilities.util.JsonUtil;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.crm.sfa.utilities.validator.ProductValidator;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.exception.SystemErrorCode;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.license.LicenseServiceImpl;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.SelectOne;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by luxin on 2018/1/16.
 * @IgnoreI18nFile
 */
@Slf4j
public class ProductAddAction extends StandardAddAction {
    private SpuSkuService spuSkuService = SpringUtil.getContext().getBean(com.facishare.crm.sfa.predefine.service.SpuSkuService.class);
    private static final ConfigService configService = SpringUtil.getContext().getBean(ConfigService.class);
    private com.facishare.crm.sfa.predefine.service.real.SpuSkuService bizSpuSkuService = SpringUtil
            .getContext().getBean(com.facishare.crm.sfa.predefine.service.real.SpuSkuServiceImpl.class);
    private ObjectDataServiceImpl objectDataService = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);
//    private CPQService cpqService = SpringUtil.getContext().getBean(CPQService.class);
    private List<Map<String, Object>> spuSpecAndValueInfoList = Lists.newArrayList();
    private final MultiUnitService multiUnitService = SpringUtil.getContext().getBean(MultiUnitServiceImpl.class);
    private List<MultiUnitData.MultiUnitItem> multiUnitItems = null;
    private static final Joiner COLLECTION_JOINER = Joiner.on(" or ");
//    private BomCoreService bomCoreService = SpringUtil.getContext().getBean(BomCoreService.class);
    private boolean isSpuOpen;
    private LicenseService licenseService=SpringUtil.getContext().getBean(LicenseServiceImpl.class);


    @Override
    protected void before(Arg arg) {
        log.info("ProductAddAction>before()>arg={}" + JsonUtil.toJsonWithNullValues(arg));
        isSpuOpen = SFAConfigUtil.isSpuOpen(actionContext.getTenantId());
        super.before(arg);
        ProductValidator.validateSerialNumberAndMultiUnit(objectData);
        if (BooleanUtils.isTrue((Boolean) objectData.get("is_multiple_unit"))) {
            multiUnitItems = multiUnitService.preprocessMultiUnit(ObjectDataDocument.of(objectData));
            if (multiUnitItems == null) {
                throw new ValidateException("多单位产品,多单位信息不能为空");
            } else {
                Set<String> module = licenseService.getModule(actionContext.getTenantId());
                if (multiUnitItems != null &&module.contains("kx_peculiarity")&&multiUnitItems.size() > 3) {
                    throw new ValidateException("开启快销企业多单位数量超过上限3个");
                }
                if (multiUnitItems.size() > 20) {
                    throw new ValidateException("多单位数量超过上限20个");
                }
            }
        }
    }


    @Override
    protected void init() {
        super.init();

        String tenantId = actionContext.getTenantId();
        objectData.set("tenant_id", tenantId);
        arg.getDetails().forEach((k, v) -> {
            if (CollectionUtils.isNotEmpty(v)) {
                v.forEach(o -> o.put("tenant_id", tenantId));
            }
        });
        if (CollectionUtils.isEmpty(objectData.getOwner())) {
            ArrayList<String> owners = Lists.newArrayList(actionContext.getUser().getUserId());
            objectData.setOwner(owners);
            arg.getDetails().forEach((k, v) -> {
                if (CollectionUtils.isNotEmpty(v)) {
                    v.forEach(o -> o.put("owner", owners));
                }
            });
        }
    }


    @Override
    protected void validate() {
//        if (CollectionUtils.isNotEmpty(arg.getDetails().get("SubProductCatalogObj")) && CollectionUtils.isNotEmpty(arg.getDetails().get(Utils.SUBPRODUCT_API_NAME))) {
//            List<ObjectDataDocument> inaccurateSubProductCatalogData = cpqService.checkSubProdCountBySubProdCatalog(
//                    arg.getDetails().get("SubProductCatalogObj"),
//                    arg.getDetails().get(Utils.SUBPRODUCT_API_NAME)
//            );
//            if (inaccurateSubProductCatalogData.size() != 0) {
//                List<String> inaccurateCatalogNames = Lists.newArrayListWithCapacity(inaccurateSubProductCatalogData.size());
//                inaccurateSubProductCatalogData.forEach(o -> inaccurateCatalogNames.add((String) o.get("name")));
//                throw new ValidateException(I18N.text("product.catalog.validate.msg") + Joiner.on(",").join(inaccurateCatalogNames));
//            }
//        }
        if (multiUnitItems != null) {
            multiUnitService.checkMultiUnitAndGetUnitInfo(actionContext.getTenantId(), multiUnitItems, (SelectOne) objectDescribe.getFieldDescribe("unit"));
        }
        if (!isSpuOpen) {
            super.validate();
            return;
        }

        @SuppressWarnings("unchecked")
        List<Map<String, String>> inputSpecAndValueList = objectData.get("spec_and_value", List.class);
        // 校验数据是否有规格值
        if (CollectionUtils.isEmpty(inputSpecAndValueList)) {
            throw new ValidateException(I18N.text("product.specification.validate.msg"));
        }

        // 商品是否有规格
        String spuId = objectData.get("spu_id", String.class);
        if (StringUtils.isEmpty(spuId)) {
            throw new ValidateException(I18N.text("product.param.error.msg"));
        }
        IObjectData spuData = serviceFacade.findObjectData(actionContext.getUser(), spuId, Utils.SPU_API_NAME);
        if (spuData.get("is_spec") == null || !(boolean) spuData.get("is_spec")) {
            throw new ValidateException(I18N.text("product.associated.spu.error.msg"));
        }

        // 规格&规格值是否存在
        SearchUsedSpecModel.Arg searchSpecAndSpecArg = SearchUsedSpecModel.Arg.builder().spuId(spuId).isIncludeAll(Boolean.TRUE).build();
        SearchUsedSpecModel.Result searchSpecAndSpecResult = spuSkuService.searchUsedSpecValuesBySpuId(
                searchSpecAndSpecArg,
                new ServiceContext(actionContext.getRequestContext(), null, null)
        );

        List<SearchUsedSpecModel.SpecAndSpecValue> dataList = searchSpecAndSpecResult.getDataList();
        if (dataList.size() != inputSpecAndValueList.size()) {
            throw new ValidateException(I18N.text("product.spec.param.error.msg"));
        }

        ProductValidator.validateSpecStatusActive(actionContext.getTenantId(), Lists.newArrayList(objectData));

        String rawSpecSpecValueSqlItem = "(r.spec_id='%s' and spec_value_id='%s')";
        List<String> specSpecValueSqlPart = Lists.newArrayListWithCapacity(inputSpecAndValueList.size());

        List<String> productSpecStrList = Lists.newArrayList();
        List<String> specValueStrList = Lists.newArrayList();

        // 顺序是否正确,规格值组合是否被使用
        for (int i = 0; i < dataList.size(); i++) {
            Map<String, String> inputSpecAndValue = inputSpecAndValueList.get(i);
            SearchUsedSpecModel.SpecAndSpecValue specAndValue = dataList.get(i);

            if (Objects.equals(specAndValue.getSpecId(), inputSpecAndValue.get("spec_id"))) {
                Map<String, String> specValueIdAndSpecValueNameMapping = Maps.newHashMap();

                specAndValue.getSpecValueList().forEach(o -> specValueIdAndSpecValueNameMapping.put(o.getSpecValueId(), o.getSpecValueName()));

                String inputSpecValueId = inputSpecAndValue.get("spec_value_id");

                String specValueName = specValueIdAndSpecValueNameMapping.get(inputSpecValueId);

                if (specValueName == null) {
                    throw new ValidateException(I18N.text("product.specification.value.not.exist.msg"));
                }

                productSpecStrList.add(specAndValue.getSpecName() + ":" + specValueName);
                specValueStrList.add(specValueName);

                String specSpecValueSqlItem = String.format(rawSpecSpecValueSqlItem, specAndValue.getSpecId(), inputSpecValueId);
                specSpecValueSqlPart.add(specSpecValueSqlItem);

                Map<String, Object> skuSpecMapping = Maps.newHashMapWithExpectedSize(7);
                skuSpecMapping.put("spu_id", spuId);
                skuSpecMapping.put("spec_id", inputSpecAndValue.get("spec_id"));
                skuSpecMapping.put("spec_value_id", inputSpecAndValue.get("spec_value_id"));
                skuSpecMapping.put("order_field", inputSpecAndValue.get("order_field"));
                skuSpecMapping.put("object_describe_api_name", "SpuSkuSpecValueRelateObj");
                skuSpecMapping.put("object_describe_id", "XXXX");
                skuSpecMapping.put("tenant_id", actionContext.getTenantId());
                spuSpecAndValueInfoList.add(skuSpecMapping);
            } else {
                throw new ValidateException(I18N.text("product.specification.index.error.msg"));
            }
        }
        // 是否是当前规格的组合
        String rawCheckExistSpecValueCombinationSql = "select true as isExistSpecValueCombination\n" +
                "from spu_sku_spec_value_relate r\n" +
                "       inner join biz_product p on r.tenant_id = p.tenant_id and p.id = r.sku_id and p.is_deleted<>-1\n" +
                "where r.spu_id = '%s'\n" +
                "  and r.tenant_id = '%s'\n" +
                "  and (%s)\n" +
                "group by sku_id\n" +
                "having count(1)=%s;";
        String checkExistSpecValueCombinationSql = String.format(rawCheckExistSpecValueCombinationSql, spuId, actionContext.getTenantId(), COLLECTION_JOINER.join(specSpecValueSqlPart), dataList.size());
        try {
            List<Map> isExistSpecValueCombinationResult = objectDataService.findBySql(actionContext.getTenantId(), checkExistSpecValueCombinationSql);
            if (isExistSpecValueCombinationResult.size() >= 1) {
                throw new ValidateException(I18N.text("product.data.exists.msg"));
            }
        } catch (MetadataServiceException e) {
            log.error("SpecificationEditAction findBySql:{}", checkExistSpecValueCombinationSql, e);
            throw new MetaDataException(SystemErrorCode.METADATA_ERROR, e);
        }
        objectData.set("product_spec", Joiner.on(";").join(productSpecStrList));
        objectData.set("name", String.format("%s[%s]", objectData.getName(), Joiner.on("-").join(specValueStrList)));
    }

    @Override
    protected void doSaveData() {
        // 特殊处理字段
        ConvertUtil.convertOrderFieldValue2Number(objectData);
        resetShelfTime();
        //特殊处理OpenApi调用,创建产品时候，创建一个与产品数据相同的商品。
        super.doSaveData();
    }

    @Override
    protected Result after(Arg arg, Result result) {
        if (CollectionUtils.isNotEmpty(spuSpecAndValueInfoList)) {
            List<IObjectData> skuSpecRelateDataList = spuSpecAndValueInfoList.stream().map(o -> {
                o.put("sku_id", objectData.getId());
                return new ObjectData(o);
            }).collect(Collectors.toList());
            serviceFacade.bulkSaveObjectData(skuSpecRelateDataList, actionContext.getUser());
        }
        if (multiUnitItems != null) {
            multiUnitService.saveMultiUnit4SkuAdd(actionContext.getUser(), objectData, multiUnitItems);
        }
        // 同步生成价目表产品
        bizSpuSkuService.asynchronousCreatePriceBookProduct(actionContext.getUser(), Lists.newArrayList(objectData));
        return super.after(arg, result);
    }

    private void resetShelfTime() {
        long productStatusUpdateTime = System.currentTimeMillis();
        String productStatus = objectData.get("product_status", String.class);
        if (ProductConstants.Status.OFF.getStatus().equals(productStatus)) {
            objectData.set("off_shelves_time", productStatusUpdateTime);
        } else {
            objectData.set("on_shelves_time", productStatusUpdateTime);
        }
    }
}
