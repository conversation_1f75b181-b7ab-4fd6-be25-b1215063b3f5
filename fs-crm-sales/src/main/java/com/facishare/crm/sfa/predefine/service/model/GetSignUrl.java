package com.facishare.crm.sfa.predefine.service.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;


public interface GetSignUrl {
    @Data
    class Arg {
        @JSONField(name = "select_val")
        @JsonProperty("select_val")
        String selectVal;
    }

    @Data
    @Builder
    class Result {
        @JSONField(name = "sign_url")
        private String signUrl;
    }

    @Data
    @Builder
    class ElecreditDataResult {
        @JSONField(name = "sign")
        private String sign;
        @JSONField(name = "pk")
        private String pk;
    }

    @Data
    class QueryEnterpriseResult {
        String code;
        String data;
    }
}
