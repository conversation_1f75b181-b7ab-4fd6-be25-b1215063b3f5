package com.facishare.crm.sfa.predefine.service.cpq;

import com.facishare.crm.sfa.predefine.service.cpq.model.NodeListModel;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import groovy.json.internal.IO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * bom 接口类
 *
 * <AUTHOR>
 */
public interface BomCoreService {
    /**
     * 是否有配置BOM权限
     * @param user
     * @return
     */
    boolean haveConfigBOMPrivilege(User user);

    /**
     * 添加bom根节点
     *
     * @param user
     * @param productIds
     */
    List<IObjectData> bulkCreateBomRootNode(User user, List<String> productIds);

    /**
     * 批量删除bom根节点
     *
     * @param user
     * @param productIds
     */
    void bulkDeleteBomRootNode(User user, List<String> productIds);

    /**
     * 根据产品的筛选条件返回BOM节点
     *
     * @param searchTemplateQuery 产品的筛选条件
     * @param isQueryRoot         true：只返回根节点，false：返回符合条件的所有节点
     * @return
     */
    NodeListModel.Result nodeList(User user, SearchTemplateQuery searchTemplateQuery, Boolean isQueryRoot);


    /**
     * 翻译产品路径
     * @param user
     * @param nodeList
     */
    void transformProductPath(User user, List<IObjectData> nodeList);

    /**
     * 递归解析bom树结构
     * @param user
     * @param bomTree
     * @param rootProductId
     * @return
     */
    Map<String, List<ObjectDataDocument>> analyzeBOMTree(User user, List<ObjectDataDocument> bomTree, String rootProductId);

    /**
     * 获取下级产品个数
     * @param user
     * @param productIds
     * @return
     */
    List<Map> findChildCount(User user, List<String> productIds);


    /**
     * 获取下级分组个数
     * @param user
     * @param productIds
     * @return
     */
    List<Map> findChildGroupCount(User user, List<String> productIds);

    /**
     * 获取中间节点个数
     * @param user
     * @param productIds
     * @return
     */
    Map<String, Long> findMiddleNodeCount(User user, List<String> productIds);


    /**
     * 根据产品的id查找bom分组
     *  产品id都是bom组合
     *
     * @param user
     * @param productIds
     * @return
     */
    Map<String, Long> findNullGroup(User user, List<String> productIds);


    /**
     * 根据产品id，查询bom根节点下是否包含子节点
     *
     * @param user
     * @param rootProductIds
     * @return
     */
    Map<String, Long> findBomElement(User user, List<String> rootProductIds);


    /**
     * 根据RootProductId 查询bom信息
     *
     * @param user
     * @param query
     * @param productIds
     * @return
     */
    QueryResult<IObjectData> findBomByRootProductId(User user, SearchTemplateQuery query, List<String> productIds);


    /**
     *
     * 通过产品ID查询bom信息
     *
     * @param user
     * @param productIds
     * @return
     */
    Map<String, String> batchFindBomByProductId(User user, List<String> productIds);


    /**
     * 查询bom下的 一级分组。
     *
     * @param user
     * @param parentProductIds
     * @return
     */
    List<IObjectData> findOneLevelGroupByParentProductIds(User user, List<String> parentProductIds);

    /**
     * 批量删除bom根节点
     * 不删除bomPath
     *
     * @param user
     * @param productIds
     */
    void bulkDeletedRootBomElement(User user, List<String> productIds);

    /**
     * 更新产品的ispackage字段
     *
     * @param user
     * @param ids
     * @param isPackage
     */
    void updateProductIsPackage(User user, List<String> ids, boolean isPackage);

    /**
     * 作废删除 产品分组
     * @param user
     */
    void deleteProductGroupWithParentBomIdIsNull(User user);


    /**
     *
     * @param user
     * @param rootBomIds
     */
    List<IObjectData> findBomByRootId(User user, List<String> rootBomIds);


    List<IObjectData> findBomByRootProductId(User user, List<String> rootBomIds);


}
