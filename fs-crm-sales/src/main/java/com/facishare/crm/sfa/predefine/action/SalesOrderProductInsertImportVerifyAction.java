package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.service.ModuleCtrlConfigService;
import com.facishare.crm.sfa.predefine.service.modulectrl.IModuleInitService;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.crm.sfa.utilities.util.SalesOrderUtil;
import com.facishare.crm.sfa.utilities.util.imports.ImportSoUtil;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportVerifyAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.List;

/**
 * Created by renlb on 2019/4/17.
 */
public class SalesOrderProductInsertImportVerifyAction extends StandardInsertImportVerifyAction {

    private final ModuleCtrlConfigService moduleCtrlConfigService = SpringUtil.getContext().getBean(ModuleCtrlConfigService.class);


    @Override
    protected List<IFieldDescribe> getValidImportFields() {
        List<IFieldDescribe> fields = super.getValidImportFields();
        SalesOrderUtil.removeUnSupportedDetailFields(fields, actionContext.getTenantId());

        // 开启cpq 移除部分字段
        if (SFAConfigUtil.isCPQ(actionContext.getTenantId())) {
            ImportSoUtil.removeFields(fields, ImportSoUtil.SALES_ORDER_PRODUCT_OPEN_MULTI_UNIT_FILTER_FIELDS);
        }

        if (moduleCtrlConfigService.openStatus(IModuleInitService.MODULE_MULTIPLE_UNIT, actionContext.getUser(), actionContext)) {
            ImportSoUtil.removeFields(fields, ImportSoUtil.SALES_ORDER_PRODUCT_FILTER_FIELDS);
        }
        return fields;
    }
}
