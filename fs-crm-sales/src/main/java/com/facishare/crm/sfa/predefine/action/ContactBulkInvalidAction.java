package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.service.push.ContactSessionSandwichService;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collection;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 联系人作废 class
 *
 * <AUTHOR>
 * @date 2019/2/23
 */
@Slf4j
public class ContactBulkInvalidAction extends StandardBulkInvalidAction {
    ContactSessionSandwichService contactSessionSandwichService = SpringUtil.getContext()
            .getBean(ContactSessionSandwichService.class);

    @Override
    protected Result after(Arg arg, Result result) {
        Set<String> owners = this.dataList.stream().filter(m -> CollectionUtils.isNotEmpty(m.getOwner()))
                .map(IObjectData::getOwner).flatMap(Collection::stream).collect(Collectors.toSet());
        //发送消息
        contactSessionSandwichService.push(actionContext.getTenantId(), Lists.newArrayList(owners));
        return super.after(arg, result);
    }
}
