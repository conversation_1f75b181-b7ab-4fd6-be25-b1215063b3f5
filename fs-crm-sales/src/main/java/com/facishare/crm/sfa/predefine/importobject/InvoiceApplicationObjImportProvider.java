package com.facishare.crm.sfa.predefine.importobject;

import com.facishare.paas.appframework.metadata.importobject.DefaultObjectImportProvider;
import com.facishare.paas.appframework.metadata.importobject.ImportObject;
import com.facishare.paas.appframework.metadata.importobject.ImportType;
import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.facishare.crm.sfa.predefine.SFAPreDefineObject.InvoiceApplication;

/**
 * create by z<PERSON><PERSON> on 2019/05/21
 */
@Component
public class InvoiceApplicationObjImportProvider extends DefaultObjectImportProvider {
    @Override
    public String getObjectCode() {
        return InvoiceApplication.getApiName();
    }

    @Override
    public Optional<ImportObject> getImportObject(IObjectDescribe objectDescribe, IUniqueRule uniqueRule) {
        Optional<ImportObject> result= super.getImportObject(objectDescribe, uniqueRule);
        result.ifPresent(importObject -> {
            importObject.setObjectName(objectDescribe.getDisplayName());
            importObject.setOpenWorkFlow(true);
        });
        return result;
    }

    @Override
    protected ImportType getImportType(IObjectDescribe objectDescribe, IUniqueRule uniqueRule) {
        return ImportType.DEFAULT;
    }

    @Override
    protected boolean getOpenWorkFlow(IObjectDescribe objectDescribe) {
        return true;
    }

    @Override
    protected boolean getIsCheckOutOwner(IObjectDescribe objectDescribe) {
        return true;
    }

    @Override
    protected boolean getIsRemoveOutTeamMember(IObjectDescribe objectDescribe) {
        return true;
    }

}
