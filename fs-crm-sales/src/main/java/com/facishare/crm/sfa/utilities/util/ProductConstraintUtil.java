package com.facishare.crm.sfa.utilities.util;

import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.CPQConstraintConstants;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.IGroupByParameter;
import com.facishare.paas.metadata.impl.search.GroupByParameter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class ProductConstraintUtil {
    public final static String SEARCH_SQL = "SELECT COUNT\n" +
            "\t ( * ) as total \n" +
            "FROM \n" +
            "\t biz_bom p \n" +
            "WHERE \n" +
            "\t p.tenant_id = '%s' \n" +
            "\t AND ((\n " +
            "\t\t\t p.id = '%s' \n" +
            "\t\t AND p.parent_bom_id IN = '%s' ) \n" +
            "\t OR ( p.id = '%s'  AND p.parent_bom_id = '%s' ))";

    public static SearchTemplateQuery getQuery(String field,String value){
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        ArrayList<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, field,value);
        query.setFilters(filters);
        return query;
    }

    public static List<SearchTemplateQuery> getQuery(List<IObjectData> lines){
        List<SearchTemplateQuery> queryList= Lists.newArrayList();
        Map<String, List<IObjectData>> dataMap = lines.stream().collect(Collectors.groupingBy(x -> x.get(CPQConstraintConstants.CONSTRAINT_TYPE, String.class)));
        dataMap.forEach((type,dataList)->{
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            searchTemplateQuery.setLimit(0);
            searchTemplateQuery.setNeedReturnCountNum(false);
            Set<String> upProducts = dataList.stream().map(x -> x.get(CPQConstraintConstants.UP_PRODUCT, String.class)).collect(Collectors.toSet());
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterIn(filters,CPQConstraintConstants.UP_PRODUCT,upProducts);
            SearchUtil.fillFilterEq(filters,CPQConstraintConstants.CONSTRAINT_TYPE,type);
            searchTemplateQuery.setFilters(filters);
            IGroupByParameter groupByParameter = new GroupByParameter();
            groupByParameter.setGroupBy(Lists.newArrayList(CPQConstraintConstants.UP_PRODUCT));
            searchTemplateQuery.setGroupByParameter(groupByParameter);
            queryList.add(searchTemplateQuery);
        });
        return queryList;
    }

    public static SearchTemplateQuery getProductConstraintLinesQuery(String productConstraintId) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1000);
        ArrayList<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, CPQConstraintConstants.PRODUCT_CONSTRAINE,productConstraintId);
        query.setFilters(filters);
        return query;
    }

    public static List<SearchTemplateQuery> checkDuplicatePKQuery(List<IObjectData> lines){
        List<SearchTemplateQuery> queryList= Lists.newArrayList();
        lines.forEach(data->{
            SearchTemplateQuery query = new SearchTemplateQuery();
            query.setLimit(1);
            query.setNeedReturnCountNum(false);
            query.setPermissionType(0);
            SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, CPQConstraintConstants.UP_PRODUCT, data.get(CPQConstraintConstants.DOWN_PRODUCT,String.class));
            SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, CPQConstraintConstants.CONSTRAINT_TYPE, data.get(CPQConstraintConstants.CONSTRAINT_TYPE,String.class));
            SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, CPQConstraintConstants.DOWN_PRODUCT, data.get(CPQConstraintConstants.UP_PRODUCT,String.class));
            queryList.add(query);
        });
        return queryList;
    }

}
