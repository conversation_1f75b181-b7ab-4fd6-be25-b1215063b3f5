package com.facishare.crm.sfa.predefine.service;

import com.facishare.crm.openapi.Utils;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Iterator;
import java.util.Set;

/**
 * Created by luxin on 2018/5/2.
 * @IgnoreI18nFile
 */
@Service
@Slf4j
public class CasesContactObjSearchService extends AbstractPredefinedObjSearchService {
    @Override
    public String getApiName() {
        return Utils.CONTACT_API_NAME;
    }

    private String fuzzySearchSql = "(select id as _id, name\n" +
            " from biz_contact\n" +
            " where tenant_id = '%s'\n" +
            "   and name = '%s'\n" +
            "   and account_id = '%s'\n" +
            "   and is_deleted = 0)\n" +
            "union\n" +
            "    (select id as _id, name\n" +
            "     from biz_contact\n" +
            "     where tenant_id = '%s'\n" +
            "       and account_id = '%s'\n" +
            "       and name ilike '%%%s%%'\n" +
            "       and name <> '%s'\n" +
            "       and is_deleted=0\n" +
            "     limit 10);";

    String notFuzzySearchSql = "select id as _id, name\n" +
            " from biz_contact\n" +
            " where tenant_id = '%s'\n" +
            "   and name = '%s'\n" +
            "   and account_id = '%s'\n" +
            "   and is_deleted = 0";

    @Override
    protected String getSearchSql(String tenantId, String name, String accountId, boolean isFuzzySearch) {
        if (StringUtils.isBlank(accountId)) {
            return null;
        } else {
            if (isFuzzySearch) {
                return String.format(fuzzySearchSql, tenantId, name, accountId, tenantId, accountId, name, name);
            } else {
                return String.format(notFuzzySearchSql, tenantId, name, accountId);
            }
        }
    }

    @Override
    protected String getNamesAccurateSearchSql(String tenantId, Set<String> names) {
        StringBuilder contactSearchStr = new StringBuilder();
        Iterator<String> iterator = names.iterator();
        while (iterator.hasNext()) {
            contactSearchStr.append(Strings.surround(iterator.next(), '\'', '\''));
            if (iterator.hasNext()) {
                contactSearchStr.append(",");
            }
        }
        return "select id,id as contact_id,name, account_id from  "
                + "biz_contact"
                + String.format(" where tenant_id = '%s'", tenantId)
                + " and name "
                + String.format(" in(%s)", contactSearchStr.toString())
                + " and is_deleted=0 ";
    }

    @Override
    protected String findByIdsSql(String tenantId, Set<String> objectIds) {
        StringBuilder contactSearchStr = new StringBuilder();
        Iterator<String> iterator = objectIds.iterator();
        while (iterator.hasNext()) {
            contactSearchStr.append(Strings.surround(iterator.next(), '\'', '\''));
            if (iterator.hasNext()) {
                contactSearchStr.append(",");
            }
        }
        return "select id,id as contact_id,name, account_id from  "
                + "biz_contact"
                + String.format(" where tenant_id = '%s'", tenantId)
                + " and id "
                + String.format(" in(%s)", contactSearchStr.toString())
                + " and is_deleted=0 ";
    }

    public static void main(String[] args) {
        CasesContactObjSearchService casesContactObjSearchService = new CasesContactObjSearchService();
        System.out.println(casesContactObjSearchService.getSearchSql("633659", "张", "a7bedfa8bb7141579d23b887f2583e71", true));
    }

}
