package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.bizvalidator.BizValidator;
import com.facishare.crm.sfa.predefine.bizvalidator.ValidatorContext;
import com.facishare.crm.sfa.predefine.bizvalidator.validator.ProductInPriceBookValidator;
import com.facishare.crm.sfa.predefine.bizvalidator.validator.newopportunitylines.ProductIsRepeatedInNewOpportunityValidator;
import com.facishare.crm.sfa.predefine.service.PriceBookService;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.utilities.constant.NewOppportunityConstants;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class NewOpportunityLinesAddAction extends StandardAddAction {

    private final PriceBookService priceBookService = SpringUtil.getContext().getBean(PriceBookService.class);
    private static final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        ValidatorContext validatorContext = ValidatorContext.builder()
                .action(ObjectAction.CREATE)
                .user(actionContext.getUser())
                .describeApiName(objectDescribe.getApiName())
                .objectDescribes(objectDescribes)
                .objectData(objectData).detailObjectData(detailObjectData)
                .build();
        BizValidator.build()
                .withContext(validatorContext)
                .with(new ProductIsRepeatedInNewOpportunityValidator())
                .with(new ProductInPriceBookValidator())
                .putAttribute2Context("enableCheckParentProdPkgKey", false)
                .doValidate();

        //价目表字段调整为非必填，不再补标准价目表
//        setStandardPriceBook();
    }


    /**
     * 没有开启价目表时，商机明细自动填充标准价目表产品。
     */
    private void setStandardPriceBook() {
        if (!bizConfigThreadLocalCacheService.isPriceBookEnabled(actionContext.getTenantId())) {
            //价目表产品ID=产品ID+租户ID
            objectData.set(
                    NewOppportunityConstants.NewOpportunityLinesField.PRICEBOOKPRODUCTID.getApiName(),
                    String.valueOf(objectData.get(NewOppportunityConstants.NewOpportunityLinesField.PRODUCTID.getApiName()))
                            .concat(actionContext.getTenantId())
            );
            IObjectData standardPriceBook = priceBookService.getStandardPriceBook(actionContext.getUser());
            if (standardPriceBook == null) {
                log.error("standardPriceBook no found. actionContext {}", actionContext);
                return;
            }
            objectData.set(NewOppportunityConstants.NewOpportunityLinesField.PRICEBOOKID.getApiName(), standardPriceBook.getId());
        }
    }
}
