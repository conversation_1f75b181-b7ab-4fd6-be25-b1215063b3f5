package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.ModuleCtrlConfigService;
import com.facishare.crm.sfa.utilities.util.ButtonUtils;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.crm.sfa.utilities.util.LayoutUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.appframework.metadata.FormComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.*;

import static com.facishare.crm.sfa.predefine.service.modulectrl.IModuleInitService.MODULE_CPQ;

public class ProductListHeaderController extends StandardListHeaderController {
    private static final List<String> DEFAULT_FIELDS = Collections.unmodifiableList(Lists.newArrayList("name", "product_code", "product_status",
            "category", "price", "unit", "product_line", "barcode", "created_by",
            "last_modified_time", "owner", "lock_status", "batch_sn"));

    private static final List<String> NO_PERMISSION_FIELDS = Collections.unmodifiableList(Lists.newArrayList("spu_id","product_spec","is_package"));
    private boolean isSpuOpen = false;
    private ModuleCtrlConfigService moduleCtrlConfigService = SpringUtil.getContext().getBean(ModuleCtrlConfigService.class);


    @Override
    protected void before(Arg arg) {
        super.before(arg);
        if(SFAConfigUtil.isSpuOpen(controllerContext.getTenantId())){
            isSpuOpen = true;
        }
    }

    @Override
    protected Result doService(Arg arg) {
        Result ret = super.doService(arg);

        List<Map<String, Object>> fieldConfigList = infraServiceFacade.findFieldListConfig(controllerContext.getUser(),
                objectDescribeExt.getApiName(), arg.getExtendAttribute());
        if (CollectionUtils.notEmpty(fieldConfigList)) {
            ret.setFieldList(layoutExt.getFieldShowList(fieldConfigList));
        } else {
            List<DocumentBaseEntity> havePermissFields = getHavePermissFields(ret);
            ret.setFieldList(havePermissFields);
        }
        return ret;
    }

    @Override
    protected ILayout findLayout(Arg arg) {
        ILayout layout = super.findLayout(arg);
        LayoutExt layoutExt = LayoutExt.of(layout);
        Optional<FormComponentExt> formComponent = layoutExt.getFormComponent();
        if (formComponent.isPresent()) {
            LayoutUtils.removeFormComponentSpec4Product(formComponent.get());
        }
        return layout;
    }

    @Override
    protected List<ISearchTemplate> findTemplates() {
        List<ISearchTemplate> templates = super.findTemplates();
        templates.forEach(t -> {
            int productSpecCount = 0;
            int productSpecLastIndex = -1;
            List<Map> fieldList = t.getFieldList();

            if(fieldList!=null){
                for (int i = 0; i < fieldList.size(); i++) {
                    if (Objects.equals(fieldList.get(i).get("field_name"), "product_spec")) {
                        productSpecCount++;
                        productSpecLastIndex = i;
                    }
                }
                if (productSpecCount > 1) {
                    fieldList.remove(productSpecLastIndex);
                    t.setFieldList(fieldList);
                }
            }
        });
        return templates;
    }

    @Override
    protected List<IButton> queryBulkButton() {
        List<IButton> buttons = super.queryBulkButton();
        //底层返回的Collections.emptyList()，调用add方法会抛UnsupportedOperationException
        if (CollectionUtils.empty(buttons)) {
            buttons = Lists.newArrayList();
        }
        processShelfButtons(buttons);
        return buttons;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        boolean hasEditPrivilege = serviceFacade.funPrivilegeCheck(controllerContext.getUser(), Utils.PRODUCT_API_NAME, ObjectAction.UPDATE.getActionCode());
        if (hasEditPrivilege && moduleCtrlConfigService.isOpen(MODULE_CPQ, controllerContext.getUser())) {
            ILayout iLayout = after.getLayout().toLayout();
            List<IButton> buttons = iLayout.getButtons();
            buttons.add(ButtonUtils.buildButton(ObjectAction.CONFIGURE_PRODUCT.getActionCode(), I18N.text(I18NKey.CONFIGURE_SBUPRODUCT_BUTTON)));
            iLayout.setButtons(buttons);
        }

        return after;
    }

    /**
     * 如果有编辑权限,就额外下发上下架的按钮
     *
     * @param buttons 按钮列表
     */
    private void processShelfButtons(List<IButton> buttons) {
        boolean hasEditPrivilege = serviceFacade.funPrivilegeCheck(controllerContext.getUser(), Utils.PRODUCT_API_NAME, ObjectAction.UPDATE.getActionCode());
        if (hasEditPrivilege) {
            IButton onShelfButton = ButtonUtils.buildButton("OnShelf", I18N.text("so.product.on_shelf.button"));
            onShelfButton.set("is_batch", true);
            buttons.add(onShelfButton);

            IButton offShelfButton = ButtonUtils.buildButton("OffShelf", I18N.text("so.product.off_shelf.button"));
            offShelfButton.set("is_batch", true);
            buttons.add(offShelfButton);
        }
    }

    private List<DocumentBaseEntity> getHavePermissFields(Result ret) {
        List<DocumentBaseEntity> havePermissFields = havePermissionFieldConfigLists();
        List<DocumentBaseEntity> fieldList = ret.getFieldList();

        if (fieldList != null) {
            fieldList.removeIf(x -> {
                for (String fieldName : DEFAULT_FIELDS) {
                    if (Objects.equals(x.get(fieldName), Boolean.TRUE)) {
                        return true;
                    }
                }
                if(!isSpuOpen){
                    for(String fieldName : NO_PERMISSION_FIELDS){
                        if (Objects.equals(x.get(fieldName), Boolean.TRUE)) {
                            return true;
                        }
                    }
                }
                return false;
            });
            havePermissFields.addAll(fieldList);
        }
        return havePermissFields;
    }


    private List<DocumentBaseEntity> havePermissionFieldConfigLists() {
        Set<String> unauthorizedFields = serviceFacade.getUnauthorizedFields(controllerContext.getUser(), "ProductObj");
        List<DocumentBaseEntity> havePermissFields = Lists.newArrayList();
        for (String fieldName : DEFAULT_FIELDS) {
            if (!unauthorizedFields.contains(fieldName)) {
                havePermissFields.add(buildFieldConfig(fieldName));
            }
        }
        return havePermissFields;
    }


    private DocumentBaseEntity buildFieldConfig(String fieldName) {
        Map<String, Object> fieldConfig = Maps.newHashMap();
        fieldConfig.put(fieldName, true);
        return new DocumentBaseEntity(fieldConfig);
    }

}
