package com.facishare.crm.sfa.predefine.service.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * Create by baoxinxue
 */

public interface DataCleanInfo {
    @Data
    class Arg{
        @JSONField(name = "tenant_id")
        @JsonProperty("tenant_id")
        private String tenant_id;
    }
    @Data
    @Builder
    class Result{
        List<DataCleanInfoItem> dataCleanInfo;
    }
    @Data
    @Builder
    class DataCleanInfoItem{
        private String id;
        private String object_type;
        private String status;
        private String creator_id;
        private String create_time;
        private String update_time;

    }
}
