package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.utilities.constant.RefundConstants;
import com.facishare.crm.sfa.utilities.proxy.RefundProxy;
import com.facishare.crm.sfa.utilities.proxy.model.RefundDeleteModel;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.paas.appframework.core.predef.action.StandardBulkDeleteAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;


import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class RefundBulkDeleteAction extends StandardBulkDeleteAction {


    private RefundProxy refundProxy = SpringUtil.getContext().getBean(RefundProxy.class);
    @Override
    protected Result after(Arg arg, Result result){
        result = super.after(arg,result);

        if(dataList != null && dataList.size() > 0 && SFAConfigUtil.isCustomerAccountEnabled(actionContext.getTenantId())){
            List<IObjectData> proxyDatas = dataList.stream().filter(x->
                    x.get(RefundConstants.RefundField.REFUND_METHOD.getApiName()) != null && (x.get(RefundConstants.RefundField.REFUND_METHOD.getApiName()).toString()
                    .equals(RefundConstants.RefundType.PrePay.getValue()) || x.get(RefundConstants.RefundField.REFUND_METHOD.getApiName()).toString()
                    .equals(RefundConstants.RefundType.Rebate.getValue()))).collect(Collectors.toList());
            if(proxyDatas != null && proxyDatas.size()>0){
                Map<String, String> header = Maps.newHashMap();
                header.put("x-fs-Employee-Id", actionContext.getUser().getUserId());
                header.put("x-fs-Enterprise-Id", actionContext.getUser().getTenantId());
                header.put("x-fs-ei", actionContext.getUser().getTenantId());
                header.put("x-fs-userInfo", actionContext.getUser().getUserId());
                List<String> proxyIds = proxyDatas.stream().map(x-> x.getId()).collect(Collectors.toList());
                RefundDeleteModel.Result rst =  refundProxy.refundDelete(
                        RefundDeleteModel.Arg.builder().refundIds(proxyIds).build(),header);
                if(!rst.IsSuccess()){
                    log.warn(String.format("调用客户账户服务有误 refundDelete arg:%s 返回信息%s",String.join(",",proxyIds),
                            rst.errMessage));
                }
            }
        }


        return result;
    }
}
