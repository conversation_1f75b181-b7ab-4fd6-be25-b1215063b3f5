package com.facishare.crm.sfa.predefine.privilege;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.model.ObjectPoolPermission;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.crm.sfa.utilities.util.RecyclingRuleUtil;
import com.facishare.crmcommon.util.ObjectUtil;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

import static com.facishare.crm.sfa.utilities.constant.AccountConstants.Field.EXPIRE_TIME;
import static com.facishare.crm.sfa.utilities.constant.AccountConstants.Field.HIGH_SEAS_ID;

@Component
public class PoolDataPrivilegeProvider extends SFADefaultDataPrivilegeProvider {

    private static final ThreadLocal<Map<String, List<IObjectData>>> POOL_DATA_LIST = new ThreadLocal<>();
    private static final ThreadLocal<Map<String, Map<String, ObjectPoolPermission.ObjectPoolPermissions>>>
            POOL_PERMISSIONS_MAP = new ThreadLocal<>();

    @Autowired
    private RecyclingRuleUtil recyclingRuleUtil;

    static {
        RequestContextManager.addContextRemoveListener((c) -> {
            POOL_DATA_LIST.remove();
            POOL_PERMISSIONS_MAP.remove();
        });
    }

    public List<IObjectData> getObjectPoolByIds(String tenantId, List<String> poolIds) {
        Map<String, List<IObjectData>> map = Maps.newHashMap();
        String apiName = getApiName();
        if (POOL_DATA_LIST.get() == null) {
            map.put(apiName, objectPoolService.getObjectPoolByIds(apiName, tenantId, poolIds));
            POOL_DATA_LIST.set(map);
        } else {
            map = POOL_DATA_LIST.get();
            if (!map.containsKey(apiName)) {
                map.put(apiName, objectPoolService.getObjectPoolByIds(apiName, tenantId, poolIds));
            }
        }

        return map.get(apiName);
    }

    public Map<String, ObjectPoolPermission.ObjectPoolPermissions> getPoolPermissionsMap(User user, List<String> poolIds) {
        Map<String, Map<String, ObjectPoolPermission.ObjectPoolPermissions>> map = Maps.newHashMap();
        String apiName = getApiName();
        String userId = user.isOutUser() ? user.getOutUserId() : user.getUserId();
        if (POOL_PERMISSIONS_MAP.get() == null) {
            map.put(apiName, objectPoolService.batchGetPoolPermission(apiName, user.getTenantId(), userId, poolIds, user.getOutTenantId()));
            POOL_PERMISSIONS_MAP.set(map);
        } else {
            map = POOL_PERMISSIONS_MAP.get();
            if (!map.containsKey(apiName)) {
                map.put(apiName, objectPoolService.batchGetPoolPermission(apiName, user.getTenantId(),
                        userId, poolIds, user.getOutTenantId()));
            }
        }

        return map.get(apiName);
    }

    public Map<String, ObjectPoolPermission.ObjectPoolPermissions> getPoolPermissionsMap(User user, String apiName, List<String> poolIds) {
        Map<String, Map<String, ObjectPoolPermission.ObjectPoolPermissions>> map = Maps.newHashMap();
        String userId = user.isOutUser() ? user.getOutUserId() : user.getUserId();
        if (POOL_PERMISSIONS_MAP.get() == null) {
            map.put(apiName, objectPoolService.batchGetPoolPermission(apiName, user.getTenantId(), userId, poolIds, user.getOutTenantId()));
            POOL_PERMISSIONS_MAP.set(map);
        } else {
            map = POOL_PERMISSIONS_MAP.get();
            if (!map.containsKey(apiName)) {
                map.put(apiName, objectPoolService.batchGetPoolPermission(apiName, user.getTenantId(),
                        userId, poolIds, user.getOutTenantId()));
            }
        }

        return map.get(apiName);
    }

    protected String getPoolApiName(String apiName) {
        if (Utils.LEADS_API_NAME.equals(apiName)) {
            return Utils.LEADS_POOL_API_NAME;
        } else if (Utils.ACCOUNT_API_NAME.equals(apiName)) {
            return Utils.HIGHSEAS_API_NAME;
        }
        return "";
    }

    protected boolean isAllowExtend(IObjectData objectData) {
        String tenantId = objectData.getTenantId();

        if (StringUtils.isEmpty(objectData.get(EXPIRE_TIME, String.class))) {
            return false;
        }
        String owner = AccountUtil.getOwner(objectData);
        if (StringUtils.isBlank(owner)) {
            return false;
        }
        String poolApiName = getPoolApiName(objectData.getDescribeApiName());
        String dataId = "";
        if (Utils.LEADS_POOL_API_NAME.equals(poolApiName)) {
            dataId = objectData.get("leads_pool_id", String.class);
        } else if (Utils.HIGHSEAS_API_NAME.equals(poolApiName)) {
            dataId = objectData.get(HIGH_SEAS_ID, String.class);
            if (StringUtils.isBlank(dataId)) {
                List<Map> recyclingEmployeeRule = recyclingRuleUtil.getRecyclingEmployeeRule(tenantId, owner, "1");
                // 没有配置，取主属部门
                if (org.apache.commons.collections.CollectionUtils.isEmpty(recyclingEmployeeRule)) {
                    // 取主属部门的规则 todo 能否取数据上的主属部门
                    dataId = AccountUtil.getUserMainDepartId(tenantId, owner);
                } else {
                    dataId = ObjectUtil.getStringValue(recyclingEmployeeRule.get(0), "data_id", "");
                    List<String> userDepartIds = AccountUtil.getUserDepartIds(tenantId, owner);
                    // 用户不在配置的部门下面，取主属部门
                    if (!userDepartIds.contains(dataId)) {
                        dataId = AccountUtil.getUserMainDepartId(tenantId, owner);
                    }
                }
            }
        }
        if (StringUtils.isBlank(dataId)) {
            return false;
        }
        List<Map> recyclingRules = recyclingRuleUtil.getRecyclingRule(tenantId, dataId, poolApiName);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(recyclingRules)) {
            return false;
        } else {
            for (Map recyclingRule : recyclingRules) {
                return ObjectUtil.getBooleanValue(recyclingRule, "is_allow_extend", false);
            }
        }
        return false;
    }
}
