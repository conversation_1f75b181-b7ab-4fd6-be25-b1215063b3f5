package com.facishare.crm.sfa.predefine.controller;


import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.util.LeadsUtils;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.metadata.ButtonExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.api.describe.IObjectCluster;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

@Slf4j
public class NewOpportunityWebDetailController extends SFAWebDetailController {

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        if (newResult.getLayout() == null) {
            return newResult;
        }
        ILayout layout = newResult.getLayout().toLayout();
        IObjectCluster cluster = infraServiceFacade.find(controllerContext.getUser(), arg.getObjectDescribeApiName());
        if (Objects.isNull(cluster) || cluster.getIsActive()) {
            WebDetailLayout.of(layout).addButtons(Lists.newArrayList(ButtonExt.generateQinxinGroupButton()));
        }
        WebDetailLayout.of(layout).removeComponents(Lists.newArrayList("LeadsObj_new_opportunity_id_related_list"));
        return newResult;
    }

    @Override
    protected boolean defaultEnableQixinGroup() {
        return Boolean.TRUE;
    }
}
