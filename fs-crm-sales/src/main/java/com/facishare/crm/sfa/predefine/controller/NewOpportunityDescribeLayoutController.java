package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.constant.NewOppportunityConstants;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.sfa.utilities.util.PreDefLayoutUtil;
import com.facishare.crm.sfa.utilities.util.VersionUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.facishare.paas.common.util.UdobjConstants.LAYOUT_TYPE_ADD;
import static com.facishare.paas.common.util.UdobjConstants.LAYOUT_TYPE_EDIT;

public class NewOpportunityDescribeLayoutController extends SFADescribeLayoutController {

    @Override
    protected void handelDescribe(Arg arg, Result result) {
        super.handelDescribe(arg, result);
        IObjectDescribe describe = result.getObjectDescribe().toObjectDescribe();
        setCascadeParentApiName(describe);
    }

    @Override
    protected void promptUpgrade(Arg arg, Result result) {
        super.promptUpgrade(arg, result);
        if (GrayUtil.isGrayPriceBookRefactor(controllerContext.getTenantId())
                && VersionUtil.isVersionEarlierEqualThan715(controllerContext.getRequestContext())) {
            throw new ValidateException(I18N.text("sfa.CommonUtil.358.1"));
        }
    }

    @Override
    protected void handleLayout(Arg arg, Result result) {
        super.handleLayout(arg, result);
        if(arg.getLayout_type() ==null){
            return;
        }
        if(formComponent != null ) {
            switch (arg.getLayout_type()) {
                case LAYOUT_TYPE_EDIT:
                    PreDefLayoutUtil.setFormComponentFieldReadOnly(formComponent, Arrays.asList("account_id"));
                    PreDefLayoutUtil.setFormComponentFieldReadOnly(formComponent, Arrays.asList("sales_stage"));
                    //PreDefLayoutUtil.setFormComponentFieldReadOnly(formComponent, Arrays.asList("owner"));
                    break;
                case LAYOUT_TYPE_ADD:
                    PreDefLayoutUtil.setFormComponentFieldReadOnly(formComponent, Arrays.asList("sales_stage"));
                    //PreDefLayoutUtil.setFormComponentFieldReadOnly(formComponent, Arrays.asList("owner"));
                    break;
            }
        }
    }

    @Override
    protected boolean supportSaveDraft() {
        return false;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        if (LAYOUT_TYPE_ADD.equals(arg.getLayout_type()) || LAYOUT_TYPE_EDIT.equals(arg.getLayout_type())) {
            handleReadOnlyFieldsForDetailLayout(newResult, NewOppportunityConstants.NewOpportunityLinesField.NEW_OPPORTUNITY_ID.getApiName());
        }
        return newResult;
    }

    /**
     * 将价目表ID和商机ID的cascade_parent_api_name设置成account_id
     * @param describe
     */
    private void setCascadeParentApiName(IObjectDescribe describe){
        List<IFieldDescribe> fields = ObjectDescribeExt.of(describe)
                .getFieldDescribesSilently().stream().filter(field->
                        field.getApiName().equals(NewOppportunityConstants.NewOpportunityField.PRICEBOOKID.getApiName()))
                .collect(Collectors.toList());

        for (IFieldDescribe field: fields) {
            field.set("cascade_parent_api_name", Arrays.asList("account_id"));
        }
    }
}

