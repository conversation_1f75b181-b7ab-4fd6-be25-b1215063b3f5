package com.facishare.crm.sfa.predefine.service.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 校验某些联系人是否存在层级关系 class
 *
 * <AUTHOR>
 * @date 2020/3/30
 */
public interface ExistContactAtlas {

    @Data
    class Arg {
        @JSONField(name = "contact_ids")
        @JsonProperty("contact_ids")
        private List<String> contactIds;
    }

    @Data
    @Builder
    class Result {
        @JSONField(name = "has_contact_atlas")
        private Boolean hasContactAtlas;
        @JSONField(name = "prompt_content")
        private String promptContent;
    }
}
