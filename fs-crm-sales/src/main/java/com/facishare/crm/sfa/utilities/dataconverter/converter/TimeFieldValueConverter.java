package com.facishare.crm.sfa.utilities.dataconverter.converter;

import com.facishare.crm.sfa.utilities.dataconverter.FieldValueConvertContext;
import com.google.common.base.Joiner;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class TimeFieldValueConverter implements AbstractFieldValueConverter {

    @Override
    public String convert(FieldValueConvertContext context) {
        List<String> formatDateList = context.getFieldValues().stream()
                .map(this::formatDate).collect(Collectors.toList());
        return Joiner.on("-").join(formatDateList);
    }

    private String formatDate(Object o){
        if (o != null && !"".equals(o) && !"null".equals(o)) {
            try {
                Long time = Long.parseLong((String) o);
                SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
                return sdf.format(new Date(time));
            } catch (Exception e) {
                log.error("TimeFieldValueConverter error, data is {}", o, e);
            }
        }
        return "";
    }
}
