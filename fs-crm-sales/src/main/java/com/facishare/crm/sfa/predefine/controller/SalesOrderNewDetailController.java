package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.real.invoice.InvoiceService;
import com.facishare.crm.sfa.utilities.constant.InvoiceApplicationConstants;
import com.facishare.crm.sfa.utilities.util.SalesOrderUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2019-08-26 15:23
 * @instruction
 */
@Slf4j
public class SalesOrderNewDetailController extends SFANewDetailController {
    private FunctionPrivilegeService functionPrivilegeService = (FunctionPrivilegeService) SpringUtil.getContext().getBean("functionPrivilegeService");
    private InvoiceService invoiceService = SpringUtil.getContext().getBean(InvoiceService.class);

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        SalesOrderUtil.handlePaymentMoneyToConfirm(controllerContext.getTenantId(),
                Lists.newArrayList(result.getData().toObjectData()));
        SalesOrderUtil.handleBillMoneyToConfirm(controllerContext.getTenantId(),
                Lists.newArrayList(result.getData().toObjectData()));
        if (newResult.getLayout() == null) {
            return newResult;
        }
        ILayout layout = new Layout(newResult.getLayout());
        checkDeliveryNotePrivilege(layout);
        if (invoiceService.isNewInvoiceOpen(controllerContext.getUser())) {
            addInvoiceAppComponent(layout);
        }
        newResult.setLayout(LayoutDocument.of(layout));
        return newResult;
    }

    private void addInvoiceAppComponent(ILayout layout) {
        Optional<IComponent> componentOp = LayoutExt.of(layout).getComponentByApiName("middle_component");
        if (componentOp.isPresent()) {
            IComponent component = componentOp.get();
            List childComponents = component.get("child_components", List.class);

            childComponents.removeIf(o -> Objects.equals(((Map) o).get("api_name"), "InvoiceApplicationObj_order_id_related_list"));
            childComponents.add(InvoiceApplicationConstants.INVOICE_APP_INFO);
            component.set("child_components", childComponents);
        }
    }

    private void checkDeliveryNotePrivilege(ILayout layout) {
        List<IButton> buttonList = layout.getButtons();
        if (CollectionUtils.notEmpty(buttonList)) {
            if (buttonList.stream()
                    .anyMatch(iButton -> ObjectAction.ADD_DELIVERY_NOTE.getActionCode().equals(iButton.getAction()))) {
                List<String> actionCodes = Lists.newArrayList(ObjectAction.CREATE.getActionCode());
                Map<String, Boolean> funPrivilegeMap = functionPrivilegeService.funPrivilegeCheck(controllerContext.getUser(),
                        Utils.DELIVERY_NOTE_API_NAME, actionCodes);
                Boolean hasPrivilege = funPrivilegeMap.getOrDefault(ObjectAction.CREATE.getActionCode(), Boolean.FALSE);
                if (!Boolean.TRUE.equals(hasPrivilege)) {
                    buttonList.removeIf(iButton -> ObjectAction.ADD_DELIVERY_NOTE.getActionCode().equals(iButton.getAction()));
                    layout.setButtons(buttonList);
                }
            }
        }
    }


}
