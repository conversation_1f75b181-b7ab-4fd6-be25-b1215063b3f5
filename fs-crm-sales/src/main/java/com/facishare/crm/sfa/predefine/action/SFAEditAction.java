package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.utilities.util.JsonUtil;
import com.facishare.paas.appframework.common.util.ObjectAPINameMapping;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.Rule;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * Created by luo<PERSON><PERSON> on 2017/12/5.
 */
@Slf4j
public class SFAEditAction extends SFAObjectSaveAction {
    @Override
    protected void before(Arg arg) {
        super.before(arg);
    }

    @Override
    protected String getIRule() {
        return Rule.UPDATE;
    }

    @Override
    protected Result doAct(Arg arg) {
        super.doAct(arg);
        IObjectData updated = serviceFacade.updateObjectData(actionContext.getUser(), objectData);
        return StandardEditAction.Result.builder().objectData(ObjectDataDocument.of(updated)).build();
    }

    @Override
    protected Result after(Arg arg, Result result) {
        log.info("SFAEditAction>after()>arg={}" + JsonUtil.toJsonWithNullValues(arg));
        log.info("SFAEditAction>after()>result={}" + JsonUtil.toJsonWithNullValues(result));

//        if (result.getObjectData() != null && result.getObjectData().get("CRMResponse") != null) {
//            if (objectDescribe.getApiName().equals(ObjectAPINameMapping.ReturnedGoodsInvoice.getApiName())) {
//                Map<String, Object> tmpResult = Maps.newHashMap();
//                tmpResult.put("success", result.getObjectData().get("CRMResponse"));
//                tmpResult.put("_id", objectData.getId());
//                result.setObjectData(ObjectDataDocument.of(tmpResult));
//            } else {
//                Map<String, Object> response = (Map<String, Object>) result.getObjectData().get("CRMResponse");
//                IObjectData iObjectData = new ObjectData();
//                response.forEach((k, v) -> iObjectData.set(k, v));
//
//                result.setObjectData(ObjectDataDocument.of(iObjectData));
//            }
//        }
        Map<String, Object> response = (Map<String, Object>) result.getObjectData().get("CRMResponse");
        IObjectData iObjectData = new ObjectData();
        response.forEach((k, v) -> iObjectData.set(k, v));

        result.setObjectData(ObjectDataDocument.of(iObjectData));
        return result;
    }}
