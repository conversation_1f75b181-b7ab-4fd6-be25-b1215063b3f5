package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.utilities.util.SalesOrderUtil;
import com.facishare.paas.appframework.core.predef.action.StandardUnionInsertImportDataAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class SalesOrderUnionInsertImportDataAction extends StandardUnionInsertImportDataAction {
    private boolean priceBookEnabled = false;

    @Override
    protected void customValidate(List<ImportData> dataList){
        super.customValidate(dataList);
        List<ImportError> errorList = Lists.newArrayList();
        // 该逻辑是为了 查找关联的字段 没查到。name转id，会直接把name的值付给id，如果name有特殊字符，in id操作会报错
        Set<Integer> errorNo = allErrorList.stream().map(x -> x.getRowNo()).collect(Collectors.toSet());
        dataList.removeIf(o->errorNo.contains(o.getRowNo()));
        priceBookEnabled = SalesOrderUtil.masterImportValidate(actionContext, errorList,
                dataList, allErrorList,false);
        mergeErrorList(errorList);
    }

    @Override
    protected void customDefaultValue(List<IObjectData> validList) {
        super.customDefaultValue(validList);
        SalesOrderUtil.masterImportDefaultValue(actionContext.getUser(), validList, priceBookEnabled);
    }
}
