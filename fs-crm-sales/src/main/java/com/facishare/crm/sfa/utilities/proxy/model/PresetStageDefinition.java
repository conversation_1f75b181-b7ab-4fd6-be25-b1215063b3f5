package com.facishare.crm.sfa.utilities.proxy.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public interface PresetStageDefinition {
    @Data
    class Arg implements Serializable {
        private String entityId;
        private String stageFieldApiName;
        private String defName;
        List<String> objectIds;
    }

    @Data
    class Result {
        boolean success;
        String message;
        int code;
        Map<String,List<StagesWithProbability>> data;
    }
}
