package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.PriceBookCommonService;
import com.facishare.crm.sfa.predefine.service.QuoteService;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.cpq.BomCoreService;
import com.facishare.crm.sfa.predefine.service.model.ConfigCtrlModule;
import com.facishare.crm.sfa.utilities.constant.BomConstants;
import com.facishare.crm.sfa.utilities.constant.QuoteConstants;
import com.facishare.crm.sfa.utilities.util.ListsUtils;
import com.facishare.crm.sfa.utilities.util.PriceBookUtil;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.crm.sfa.utilities.validator.QuoteImportValidator;
import com.facishare.crm.sfa.utilities.validator.QuoteValidator;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseImportDataAction;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportDataAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Map;

/**
 * 报价单明细导入类
 * 开启CPQ时，导入不分批，且数据须按BOM结构顺序录入，遵循深度优先原则
 *
 * <AUTHOR>
 * @IgnoreI18nFile
 */
public class QuoteLinesInsertImportDataAction extends StandardInsertImportDataAction {

    private PriceBookCommonService priceBookCommonService = SpringUtil.getContext().getBean(PriceBookCommonService.class);
    private QuoteService quoteService = SpringUtil.getContext().getBean(QuoteService.class);
    private String cpqStatus = ConfigCtrlModule.OpenStatus.CLOSE.getStatusCode();
    private String priceBookStatus = ConfigCtrlModule.OpenStatus.CLOSE.getStatusCode();
    private final BomCoreService bomCoreService = SpringUtil.getContext().getBean(BomCoreService.class);
    private static final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);
    private String cpq_status = ConfigCtrlModule.OpenStatus.CLOSE.getStatusCode();
    private String price_book_status = ConfigCtrlModule.OpenStatus.CLOSE.getStatusCode();


    /**
     * 初始化列字段
     *
     * @param arg
     */
    private void initArgRow(Arg arg) {
        User user = User.builder().tenantId(actionContext.getTenantId()).userId(User.SUPPER_ADMIN_USER_ID).build();
        //初始化列以及数据
        cpq_status = quoteService.checkCpqstatus(user, actionContext);
        if (ConfigCtrlModule.OpenStatus.OPEN.getStatusCode().equals(cpq_status)) {
            for (ObjectDataDocument row : arg.getRows()) {
                String sub_product_name = "";
                String sub_product_code = "";
                if (row.get("子产品编码") != null && !row.get("子产品编码").toString().isEmpty()) {
                    sub_product_name = row.get("子产品编码").toString();

                }
                if (row.get("子产品名称") != null && !row.get("子产品名称").toString().isEmpty()) {
                    sub_product_code = row.get("子产品名称").toString();
                }
                if (!sub_product_name.isEmpty()) {
                    row.put("子产品明细", sub_product_name);
                    row.put("产品名称", sub_product_code);
                }
            }
        }

    }

    /**
     * 初始化子产品相关数据
     * excel中母子件必须按顺序录入
     *
     * @param dataList
     */
    private void initDataList(List<BaseImportDataAction.ImportData> dataList) {

        List<String> productIdList = Lists.newArrayList();
        String rootPackKey = "", parentPackKey = "", packKey = "";
        String parentProdPackageId = "";
        for (ImportData row : dataList) {
            if (Strings.isNullOrEmpty(row.getData()
                    .get(QuoteConstants.QuoteLinesField.PARENT_PROD_PACKAGE_ID.getApiName(), String.class))) {
                //所属产品组合为空的行表示根产品
                packKey = serviceFacade.generateId();
                rootPackKey = packKey;
                row.getData().set(QuoteConstants.QuoteLinesField.ROOT_PROD_PKG.getApiName(), rootPackKey);
                row.getData().set(QuoteConstants.QuoteLinesField.PROD_PKG.getApiName(), packKey);
            } else {
                if (!parentProdPackageId.equals(row.getData().
                        get(QuoteConstants.QuoteLinesField.PARENT_PROD_PACKAGE_ID.getApiName(), String.class))) {
                    parentProdPackageId = row.getData().
                            get(QuoteConstants.QuoteLinesField.PARENT_PROD_PACKAGE_ID.getApiName(), String.class);
                    parentPackKey = packKey;
                }
                row.getData().set(QuoteConstants.QuoteLinesField.ROOT_PROD_PKG.getApiName(), rootPackKey);
                row.getData().set(QuoteConstants.QuoteLinesField.PARENT_PROD_PKG.getApiName(), parentPackKey);
                packKey = serviceFacade.generateId();
                row.getData().set(QuoteConstants.QuoteLinesField.PROD_PKG.getApiName(), packKey);
                //子产品填写discount sales_price total_amount 不保存
                row.getData().set(QuoteConstants.QuoteLinesField.DISCOUNT.getApiName(), 0);
                row.getData().set(QuoteConstants.QuoteLinesField.SALES_PRICE.getApiName(), 0);
                row.getData().set(QuoteConstants.QuoteLinesField.TOTAL_AMOUNT.getApiName(), 0);
            }
            productIdList.add(row.getData().get(BomConstants.FIELD_PRODUCT_ID, String.class));
        }

        Map<String, String> productToBomId = bomCoreService.batchFindBomByProductId(actionContext.getUser(), productIdList);
        for (ImportData row : dataList) {
            IObjectData data = row.getData();
            String productId = data.get(BomConstants.FIELD_PRODUCT_ID, String.class);
            if (productToBomId.containsKey(productId)) {
                data.set(BomConstants.FIELD_BOM_ID, productToBomId.get(productId));
            }
        }
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        if (bizConfigThreadLocalCacheService.isPriceBookEnabled(actionContext.getTenantId())) {
            priceBookStatus = ConfigCtrlModule.OpenStatus.OPEN.getStatusCode();
        }
        cpqStatus = quoteService.checkCpqstatus(actionContext.getUser(), actionContext);
        //校验是否开启CPQ,1标志就是开启的状态(报价单改造不支持cpq导入)
        /*if (ConfigCtrlModule.OpenStatus.OPEN.getStatusCode().equals(cpqStatus)) {
            initDataList(dataList);
        }*/

    }

    @Override
    protected void customValidate(List<ImportData> dataList) {
        super.customValidate(dataList);
        List<ImportError> errorList = Lists.newArrayList();
        QuoteImportValidator.customValidate(objectDescribe
                , SFAPreDefineObject.Quote.getApiName()
                , QuoteConstants.QuoteField.QUOTEID.getApiName()
                , actionContext, errorList, dataList, Boolean.TRUE
        );

        if (ConfigCtrlModule.OpenStatus.OPEN.getStatusCode().equals(cpqStatus)) {
            QuoteImportValidator.validWhenCPQOpen(serviceFacade, actionContext, dataList, errorList, priceBookStatus);
        }
        if (QuoteValidator.enableTieredPriceBook(objectDescribe)) {
            QuoteImportValidator.validateProductInTieredPriceBook(actionContext.getUser(), dataList, errorList);
        }

        //QuoteImportValidator.checkCPQ(serviceFacade, actionContext, dataList, errorList);

        mergeErrorList(errorList);

    }

    public void checkCPQ(List<ImportData> dataList,List<ImportError> errorList){

    }

    @Override
    protected List<IObjectData> importData(List<IObjectData> validList) {
        List<List<IObjectData>> datas = ListsUtils.splitList(validList, 100);
        datas.forEach(x -> this.serviceFacade.bulkSaveObjectData(x, this.actionContext.getUser(),
                false, false));
        return validList;
    }

    @Override
    protected void customDefaultValue(List<IObjectData> validList) {
        super.customDefaultValue(validList);
        //价目表字段调整为非必填，不再补标准价目表
//        boolean priceBookEnabled = bizConfigThreadLocalCacheService.isPriceBookEnabled(actionContext.getTenantId());
//        PriceBookUtil.detailImportDefaultValue(actionContext.getTenantId(), validList, priceBookEnabled);
    }
}
