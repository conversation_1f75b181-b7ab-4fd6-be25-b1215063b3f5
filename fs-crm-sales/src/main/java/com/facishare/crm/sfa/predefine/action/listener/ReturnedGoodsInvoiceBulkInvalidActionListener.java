package com.facishare.crm.sfa.predefine.action.listener;

import com.facishare.crm.sfa.utilities.proxy.ReturnedGoodsInvoiceProxy;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.ReturnedGoodsInvoiceInterceptorModel;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.salesorderparam.BulkInvalidAfterModel;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.salesorderparam.BulkInvalidBeforeModel;
import com.facishare.crm.sfa.utilities.util.SoCommonUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.metadata.util.SpringUtil;
import org.springframework.stereotype.Component;

/**
 * Created by renlb on 2019/3/14.
 */
@Component
public class ReturnedGoodsInvoiceBulkInvalidActionListener extends StandardBulkInvalidActionListener {

    private static final ReturnedGoodsInvoiceProxy returnedGoodsInvoiceProxy = SpringUtil.getContext().getBean(ReturnedGoodsInvoiceProxy.class);


    @Override
    protected void callBeforeInterceptor(BulkInvalidBeforeModel.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext("ReturnedGoodsInvoiceInterceptor",
                "BulkInvalidBefore");
//        returnedGoodsInvoiceInterceptorService.bulkInvalidBefore(context, arg);
        ReturnedGoodsInvoiceInterceptorModel.BulkInvalidBeforeResult bulkInvalidBeforeResult = returnedGoodsInvoiceProxy.bulkInvalidBefore(arg, SoCommonUtils.getCrmHeader(context.getTenantId(), context.getUser()));
        if(!bulkInvalidBeforeResult.isSuccess()){
            throw new ValidateException(bulkInvalidBeforeResult.getMessage());
        }
    }

    @Override
    protected void callAfterInterceptor(BulkInvalidAfterModel.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext("ReturnedGoodsInvoiceInterceptor",
                "BulkInvalidAfter");
//        returnedGoodsInvoiceInterceptorService.bulkInvalidAfter(context, arg);
        ReturnedGoodsInvoiceInterceptorModel.BulkInvalidAfterResult bulkInvalidAfterResult = returnedGoodsInvoiceProxy.bulkInvalidAfter(arg, SoCommonUtils.getCrmHeader(context.getTenantId(), context.getUser()));
        if(!bulkInvalidAfterResult.isSuccess()){
            throw new ValidateException(bulkInvalidAfterResult.getMessage());
        }
    }
}
