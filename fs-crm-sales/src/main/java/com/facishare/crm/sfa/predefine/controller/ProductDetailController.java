package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.exception.SFABusinessException;
import com.facishare.crm.sfa.predefine.exception.SFAErrorCode;
import com.facishare.crm.sfa.predefine.service.ModuleCtrlConfigService;
import com.facishare.crm.sfa.predefine.service.cpq.BomCoreService;
import com.facishare.crm.sfa.predefine.service.model.ConfigCtrlModule;
import com.facishare.crm.sfa.predefine.service.real.MultiUnitService;
import com.facishare.crm.sfa.predefine.service.real.MultiUnitServiceImpl;
import com.facishare.crm.sfa.utilities.util.*;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.component.GroupComponent;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.facishare.paas.appframework.core.model.RequestContext.Android_CLIENT_INFO_PREFIX;
import static com.facishare.paas.appframework.core.model.RequestContext.IOS_CLIENT_INFO_PREFIX;

/**
 * Created by luxin on 2018/1/15.
 */
@Slf4j
// TODO 之前产品继承的是 SFADetailController，注意前后的影响
public class ProductDetailController extends NewStandardDetailController {
    private final ModuleCtrlConfigService moduleCtrlConfigService = SpringUtil.getContext().getBean(ModuleCtrlConfigService.class);
    private final MultiUnitService multiUnitService = SpringUtil.getContext().getBean(MultiUnitServiceImpl.class);
    private final BomCoreService bomCoreService = SpringUtil.getContext().getBean(BomCoreService.class);
    private boolean isSpuOpen = false;

    @Override
    protected void before(Arg arg) {
        multiUnitService.validateMobileClientVersion(controllerContext.getRequestContext());
        // 判断版本不为 H5且为移动端 是否小于650,如果小于,给强升提示
        if (!RequestUtil.isH5Request() && RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_650)) {
            throw new SFABusinessException(SFAErrorCode.CLIENT_UPGRADE_PROMPT.getMessage(), SFAErrorCode.CLIENT_UPGRADE_PROMPT);
        }
        super.before(arg);
        if(SFAConfigUtil.isSpuOpen(controllerContext.getTenantId())){
            isSpuOpen = true;
        }
    }

    @Override
    protected Result doService(Arg arg) {
        Result result = super.doService(arg);
        result = specialLogicForLayout(result);
        return result;
    }

    private Result specialLogicForLayout(Result result) {
        // 去掉详情页产品选配明细页签的新建按钮
        result = JsonObjectUtils.remove(result, Result.class, "$.layout.components[?(@.api_name=='relatedObject')]" +
                ".child_components[?(@.field_api_name=='parent_product_id')].buttons[?(@.action=='Add')]");
        //去掉详情页产品选配明细lookup的页签
        result = JsonObjectUtils.remove(result, Result.class, "$.layout.components[?(@.api_name=='relatedObject')]" +
                ".child_components[?(@.ref_object_api_name=='BOMObj' && @.field_api_name=='product_id')]");
//        if (RequestUtil.isMobileRequest()) {
//            //移动端，去掉详情页子产品明细页签的新建按钮
//            result = JsonObjectUtils.remove(result, Result.class, "$.layout.components[?(@.api_name=='relatedObject')]" +
//                    ".child_components[?(@.ref_object_api_name=='SubProductObj')].child_components[?(@.api_name=='SubProductObj_table_component')].buttons[?(@.action=='Add')]");
//        }
        return result;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        if (newResult.getLayout() == null) {
            return newResult;
        }
        ILayout layout = new Layout(newResult.getLayout());

        if (RequestUtil.isWebRequest()) {
            processSubProductButton(layout);
        } else {
            List<IButton> buttons = layout.getButtons();
            buttons.removeIf(b -> b.getAction().equals(ObjectAction.CONFIGURE_PRODUCT.getActionCode()));
            layout.setButtons(buttons);
        }

        LayoutExt layoutExt = LayoutExt.of(layout);
        LayoutUtils.removeProductSpecField(layoutExt, "detailInfo");

        // 移除创建，更新，更换负责人操作
        LayoutUtils.removeButtons4MobileOrH5(
                layoutExt,
                Lists.newArrayList(ObjectAction.CREATE.getActionCode(),
                        ObjectAction.UPDATE.getActionCode(),
                        ObjectAction.CHANGE_OWNER.getActionCode())
        );
        MultiUnitRelatedUtils.handleMultiUnitRelatedComponent(layoutExt, data);

        if (isSpuOpen) {
            removeChangeOwnerAndRelevantTeamButton4NoHaveSpecProduct(result, layout);
        }
        hideComponent(layout, layoutExt);
        PreDefLayoutUtil.invisibleReferenceObject(arg.getObjectDescribeApiName(), layout);
        PreDefLayoutUtil.invisibleRefObjectListAddButton(arg.getObjectDescribeApiName(), layout);
        PreDefLayoutUtil.invisibleRefObjectListRelationButton(arg.getObjectDescribeApiName(), layout);
        PreDefLayoutUtil.invisibleReferenceObject(arg.getObjectDescribeApiName(), layout);
        PreDefLayoutUtil.invisibleRefObjectListAllButtonForSpecifiedRelatedObject(layout);
        newResult.setLayout(LayoutDocument.of(layout));
        return newResult;
    }

    //隐藏产品选配明细页
    private void hideComponent(ILayout layout, LayoutExt layoutExt) {
        List<IComponent> components = layoutExt.getComponentsSilently();
        for (IComponent component : components) {
            if (Objects.equals("relatedObject", component.getName())) {
                GroupComponent groupComponent = (GroupComponent) component;
                List<IComponent> childComponents = null;
                try {
                    childComponents = groupComponent.getChildComponents();
                } catch (MetadataServiceException e) {
                    e.printStackTrace();
                }
                childComponents.removeIf(o -> Objects.equals(o.getName(), "BOMObj_parent_product_id_related_list"));
                groupComponent.setChildComponents(childComponents);
            }
        }
        layout.setComponents(components);
    }


    /**
     * 处理配置子产品按钮,当cpq开启且有编辑权限下发该按钮
     *
     * @param layout
     */
    private void processSubProductButton(ILayout layout) {
        List<IButton> buttons = layout.getButtons();
        Optional<IButton> editButtonOptional = buttons.stream().filter(o -> "Edit".equals(o.getAction())).findAny();
        Optional<IButton> configureProductButtonOptional = buttons.stream().filter(o -> "ConfigureProduct".equals(o.getAction())).findAny();

        if (configureProductButtonOptional.isPresent()) {
            configureProductButtonOptional.get().setLabel(I18N.text(I18NKey.CONFIGURE_SBUPRODUCT_BUTTON));
        }

        // 没有编辑按钮,删除配置子产品按钮; 如果没有子产品的权限,不下发按钮
        if (!editButtonOptional.isPresent() || !bomCoreService.haveConfigBOMPrivilege(controllerContext.getUser())) {
            buttons.removeIf(b -> b.getAction().equals(ObjectAction.CONFIGURE_PRODUCT.getActionCode()));
        } else {
            String openStatus = getCpqOpenStatus();

            // 未开启cpq删除配置子产品按钮,产品是子产品删除配置子产品按钮
            if (!ConfigCtrlModule.OpenStatus.OPEN.getStatusCode().equals(openStatus)) {
                buttons.removeIf(b -> b.getAction().equals(ObjectAction.CONFIGURE_PRODUCT.getActionCode()));
            } else if (!configureProductButtonOptional.isPresent()) {
                IButton editButton = editButtonOptional.get();
                IButton button = new Button();
                button.setLabel(I18N.text(I18NKey.CONFIGURE_SBUPRODUCT_BUTTON));
                button.setAction(ObjectAction.CONFIGURE_PRODUCT.getActionCode());
                button.setName(editButton.getName());
                button.setActionType(editButton.getActionType());
                buttons.add(button);
            }
        }
        layout.setButtons(buttons);
    }

    private String getCpqOpenStatus() {
        ServiceContext serviceContext = ContextConvertUtils.controllerContext2ServiceContext(controllerContext);

        ConfigCtrlModule.Arg configCtrlModuleArg = new ConfigCtrlModule.Arg();
        configCtrlModuleArg.setModuleCode("cpq");
        configCtrlModuleArg.setTenantId(controllerContext.getTenantId());

        ConfigCtrlModule.Result checkModuleStatus = moduleCtrlConfigService.checkModuleStatus(configCtrlModuleArg, serviceContext);
        ConfigCtrlModule.Value moduleStatusValue = checkModuleStatus.getValue();
        return moduleStatusValue.getOpenStatus();
    }


    /**
     * 无规格产品去掉更换负责人按钮
     */
    private void removeChangeOwnerAndRelevantTeamButton4NoHaveSpecProduct(Result result, ILayout layout) {
        if (null == result.getData().get("spu_id")) {
            return;
        }
        String spuId = result.getData().get("spu_id").toString();
        List<IObjectData> spuData = serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), Lists.newArrayList(spuId), Utils.SPU_API_NAME);

        if (spuData.stream().findFirst().isPresent()) {
            spuData.stream().findFirst().ifPresent(o -> {
                if (!o.get("is_spec", Boolean.class)) {
                    LayoutUtils.removeButtons(LayoutExt.of(layout), Lists.newArrayList(ObjectAction.CHANGE_OWNER.getActionCode()));
                    Result result1 = JsonObjectUtils.remove(result, Result.class, "$.layout.components[?(@.api_name=='relatedObject')]" +
                            ".child_components[?(@.api_name=='relevant_team_component')].buttons");
                    try {
                        layout.setComponents(result1.getLayout().toLayout().getComponents());
                    } catch (MetadataServiceException e) {
                        throw new MetaDataBusinessException(e);
                    }
                }
            });
        }
    }

    @Override
    protected ILayout getLayout() {
        ILayout layout = super.getLayout();

        try {
            specialLogicForLayout(layout);
            openCpqRemoveQuoteLineTabPage(layout);
        } catch (MetadataServiceException e) {
            log.error("getChildComponents error.", e);
        }
        removeRelatedObjBtns(layout);
        return layout;
    }

    private void openCpqRemoveQuoteLineTabPage(ILayout layout) throws MetadataServiceException {
        boolean cpqOpen = ConfigCtrlModule.OpenStatus.OPEN.getStatusCode().equals(getCpqOpenStatus());

        if (cpqOpen) {
            List<IComponent> components = layout.getComponents();

            for (IComponent component : components) {
                if (Objects.equals("relatedObject", component.getName())) {
                    GroupComponent groupComponent = (GroupComponent) component;
                    List<IComponent> childComponents = groupComponent.getChildComponents();
                    childComponents.removeIf(o -> Objects.equals(o.getName(), "QuoteLinesObj_parent_prod_package_id_related_list"));
                    groupComponent.setChildComponents(childComponents);
                }
            }
            layout.setComponents(components);
        }
    }


    private void removeRelatedObjBtns(ILayout layout) {
        final List<String> removeActionsRelatedObjs = Lists.newArrayList("PriceBookProductObj", "GoodsReceivedNoteProductObj", "DeliveryNoteProductObj");
        LayoutExt.of(layout).getRelatedComponent().ifPresent(x -> {
            try {
                x.getChildComponents().forEach(childComponent -> {
                    if (removeActionsRelatedObjs.contains(childComponent.get("ref_object_api_name", String.class))) {
                        List<IButton> buttons = Lists.newArrayList();
                        childComponent.setButtons(buttons);
                    }
                });
            } catch (MetadataServiceException ignored) {
            }
        });

    }

    private void specialLogicForLayout(ILayout layout) throws MetadataServiceException {
        List<IButton> buttons = layout.getButtons();
        if ((isMobileClient(controllerContext.getClientInfo()))) {
            //终端不下发销售记录，相关团队，附件 卡片,根据版本下发按钮
            if (RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_650)) {
                specialLogicBefore650(layout);
            }
            buttons.removeIf(k -> "AddSpec".equals(k.getAction()));
            buttons.removeIf(k -> ObjectAction.INVALID.getActionCode().equals(k.getAction()));
        }
        //需要屏蔽掉产品的复制按钮
        buttons.removeIf(k -> ObjectAction.CLONE.getActionCode().equals(k.getAction()));
        layout.setButtons(buttons);
    }

    private void specialLogicBefore650(ILayout layout) throws MetadataServiceException {
        LayoutExt.of(layout).getRelatedComponent().ifPresent(x -> {
            try {
                List<IComponent> components = x.getChildComponents();
                components.removeIf(component -> "sale_log".equals(component.getName())
                        || "relevant_team_component".equals(component.getName())
                        || "ProductAttObj_related_list".equals(component.getName()));

                x.setChildComponents(components);
            } catch (MetadataServiceException ignored) {
            }
        });
        List<IComponent> groupComponentList = layout.getComponents();
        List<IComponent> removeComponentList = Lists.newArrayList();
        for (IComponent component : groupComponentList) {
            if ("otherInfo".equals(component.getName())) {
                removeComponentList.add(component);
            }
        }
        groupComponentList.removeAll(removeComponentList);
        layout.setComponents(groupComponentList);
    }

    private boolean isMobileClient(String clientInfo) {
        return clientInfo != null && (clientInfo.startsWith(Android_CLIENT_INFO_PREFIX) || clientInfo.startsWith(IOS_CLIENT_INFO_PREFIX));
    }

}
