package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.service.NewOpportunityInitService;
import com.facishare.crm.sfa.utilities.constant.AccountConstants;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.crm.sfa.utilities.util.NewOpportunityUtil;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardIncrementUpdateAction;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class NewOpportunityIncrementUpdateAction extends StandardIncrementUpdateAction {
    NewOpportunityInitService newOpportunityInitService = SpringUtil.getContext().getBean(NewOpportunityInitService.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        //保证流程不变的情况下，阶段变更
        //阶段变更时修改数据
        if (dbObjectData.get("sales_stage") != null && objectData.get("sales_stage") != null) {
            if (!dbObjectData.get("sales_stage").equals(objectData.get("sales_stage"))) {
                NewOpportunityUtil.fillModelByChangeSalesStage(actionContext,objectData,dbObjectData);
            }
        }
    }
}
