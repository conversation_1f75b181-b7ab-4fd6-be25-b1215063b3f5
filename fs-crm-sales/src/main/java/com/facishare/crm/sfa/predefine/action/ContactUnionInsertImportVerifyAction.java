package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.utilities.util.ContactUtil;
import com.facishare.paas.appframework.core.predef.action.StandardUnionInsertImportVerifyAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;

import java.util.List;
import java.util.stream.Collectors;

public class ContactUnionInsertImportVerifyAction extends StandardUnionInsertImportVerifyAction {

    @Override
    public List<IFieldDescribe> getValidImportFields() {
        List<IFieldDescribe> fieldDescribes = super.getValidImportFields();
        fieldDescribes.removeIf(f -> ContactUtil.getImportTemplateRemoveFields().contains(f.getApiName()));
        return fieldDescribes;
    }

    /**
     * 校验唯一性规则字段不需要包含 电话、手机
     *
     * @param validFieldList
     * @param ruleFieldName
     * @return
     */
    @Override
    protected List<String> getNotValidFieldsByRuleField(List<IFieldDescribe> validFieldList, List<String> ruleFieldName) {
        List<String> ruleField = super.getNotValidFieldsByRuleField(validFieldList, ruleFieldName);
        return ruleField.stream().filter(x -> !x.equals("tel") && !x.equals("mobile")).collect(Collectors.toList());
    }
}
