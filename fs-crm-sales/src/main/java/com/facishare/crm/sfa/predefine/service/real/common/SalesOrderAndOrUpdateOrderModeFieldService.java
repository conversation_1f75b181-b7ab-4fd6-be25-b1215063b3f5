package com.facishare.crm.sfa.predefine.service.real.common;

import com.facishare.crm.sfa.predefine.service.model.ServiceResult;
import com.facishare.paas.appframework.core.model.User;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @date 2020/6/18 5:18 下午
 * @illustration
 */
@Component(value = "dependence_order_mode")
public class SalesOrderAndOrUpdateOrderModeFieldService extends AbstractDependenceAddOrUpdatePredefinedFieldService {

    @Override
    public ServiceResult updateOrInsertField(User user, String apiName, String fieldApiName, String fieldJson) {
        return super.updateOrInsertField(user, apiName, fieldApiName, fieldJson, "dependence_order_mode");
    }

}
