package com.facishare.crm.sfa.predefine.service.model;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.Builder;
import lombok.Data;

import javax.ws.rs.DefaultValue;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/2/12 3:38 下午
 * @illustration
 */
public interface InvoiceApplicationAndSalesOrder {

    @Data
    class SalesOrderDhtArg{
        private String accountId;
        private List<String> lifeStatus;
        private String resource;
        private String orderByField;
        private boolean isAsc;
        private int offset;
        private int limit;
    }

    @Data
    @Builder
    class SalesOrderDhtResult {
        private int totalCount;
        private List<IObjectData> data;
    }


    @Data
    class CountNoInvoiceAmountArg {
        private String accountId;
        private String resource;
    }

    @Data
    @Builder
    class CountNoInvoiceAmountResult {
        private String accountId;
        private Double sumAmount;
    }

    @Data
    @Builder
    class SalesOrderObjectData {
        private int count;
        private int removeCount;
        private int otherCount;
        private List<Map<String, Object>> map;
    }

    @Data
    @Builder
    class SalesOrderObjectDataResult {
        private int count;
        private List<IObjectData> dataList;
    }

    @Data
    class Arg implements Serializable {
        private List<String> orderIds;
        private List<OrderProductInfo> orderProductIds;
    }

    @Data
    class OrderProductInfo implements Serializable {
        private String orderId;
        private List<String> orderProductIds;
    }

    @Data
    @Builder
    class Result {
        private Map<String, List<ObjectDataDocument>> objectData;
    }
}
