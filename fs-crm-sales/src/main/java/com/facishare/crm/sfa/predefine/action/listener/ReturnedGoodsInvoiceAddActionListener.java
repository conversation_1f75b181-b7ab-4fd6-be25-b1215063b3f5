package com.facishare.crm.sfa.predefine.action.listener;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.proxy.ReturnedGoodsInvoiceProxy;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.ReturnedGoodsInvoiceInterceptorModel;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.returngoodsparam.ReturnedGoodsInvoiceAddAfterModel;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.returngoodsparam.ReturnedGoodsInvoiceAddBeforeModel;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.returngoodsparam.ReturnedGoodsInvoiceProductVo;
import com.facishare.crm.sfa.utilities.util.SoCommonUtils;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by renlb on 2019/3/14.
 */
@Component
public class ReturnedGoodsInvoiceAddActionListener implements ActionListener<BaseObjectSaveAction.Arg,BaseObjectSaveAction.Result> {

    private static final ReturnedGoodsInvoiceProxy returnedGoodsInvoiceProxy = SpringUtil.getContext().getBean(ReturnedGoodsInvoiceProxy.class);

    @Override
    public void before(ActionContext actionContext, BaseObjectSaveAction.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext("ReturnedGoodsInvoiceInterceptor",
                "AddBefore");
        IObjectData objectData = arg.getObjectData().toObjectData();
        ReturnedGoodsInvoiceAddBeforeModel.Arg serviceArg = new ReturnedGoodsInvoiceAddBeforeModel.Arg();
        ReturnedGoodsInvoiceAddBeforeModel.ReturnedGoodsInvoiceVo vo = new ReturnedGoodsInvoiceAddBeforeModel.ReturnedGoodsInvoiceVo();
        vo.setCustomerId(objectData.get("account_id",String.class));
        vo.setTradeId(objectData.get("order_id", String.class));
        vo.setWarehouseId(objectData.get("return_warehouse_id",String.class));
        List<ReturnedGoodsInvoiceProductVo> returnedGoodsInvoiceProductVoList = Lists.newArrayList();
        List<ObjectDataDocument> details = arg.getDetails().getOrDefault(Utils.RETURN_GOODS_INVOICE_Product_API_NAME, Lists.newArrayList());
        if(CollectionUtils.notEmpty(details)) {
            for(ObjectDataDocument objectDataDocument : details) {
                IObjectData data = objectDataDocument.toObjectData();
                ReturnedGoodsInvoiceProductVo returnedGoodsInvoiceProductVo = new ReturnedGoodsInvoiceProductVo();
                returnedGoodsInvoiceProductVo.setProductId(data.get("product_id", String.class));
                if(data.get("quantity", BigDecimal.class) == null) {
                    returnedGoodsInvoiceProductVo.setAmount(new BigDecimal(0));
                } else {
                    returnedGoodsInvoiceProductVo.setAmount(data.get("quantity", BigDecimal.class));
                }
                returnedGoodsInvoiceProductVoList.add(returnedGoodsInvoiceProductVo);
            }
            vo.setReturnedGoodsInvoiceProductVos(returnedGoodsInvoiceProductVoList);
        }
        serviceArg.setReturnedGoodsInvoiceVo(vo);
//        returnedGoodsInvoiceInterceptorService.addBefore(context, serviceArg);
        ReturnedGoodsInvoiceInterceptorModel.AddBeforeResult addBeforeResult = returnedGoodsInvoiceProxy.addBefore(serviceArg, SoCommonUtils.getCrmHeader(context.getTenantId(), context.getUser()));
        if(!addBeforeResult.isSuccess()){
            throw new ValidateException(addBeforeResult.getMessage());
        }
    }

    @Override
    public void after(ActionContext actionContext, BaseObjectSaveAction.Arg arg, BaseObjectSaveAction.Result result) {
        ServiceContext context = ContextManager.buildServiceContext("ReturnedGoodsInvoiceInterceptor",
                "AddAfter");
        ReturnedGoodsInvoiceAddAfterModel.Arg serviceArg = new ReturnedGoodsInvoiceAddAfterModel.Arg();
        IObjectData objectData = result.getObjectData().toObjectData();
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        serviceArg.setDataId(objectData.getId());
        serviceArg.setBeforeLifeStatus(ObjectLifeStatus.INEFFECTIVE.getCode());
        serviceArg.setAfterLifeStatus(objectDataExt.getLifeStatus().getCode());
//        returnedGoodsInvoiceInterceptorService.addAfter(context, serviceArg);
        ReturnedGoodsInvoiceInterceptorModel.AddAfterResult addAfterResult = returnedGoodsInvoiceProxy.addAfter(serviceArg, SoCommonUtils.getCrmHeader(context.getTenantId(), context.getUser()));
        if(!addAfterResult.isSuccess()){
            throw new ValidateException(addAfterResult.getMessage());
        }
    }
}
