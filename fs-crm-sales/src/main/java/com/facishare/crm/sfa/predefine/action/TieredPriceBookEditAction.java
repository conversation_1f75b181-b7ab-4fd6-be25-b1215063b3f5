package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.service.cpq.CPQTieredPriceService;
import com.facishare.crm.sfa.predefine.service.cpq.CPQTieredPriceServiceImpl;
import com.facishare.crm.sfa.utilities.constant.TieredPriceConstants;
import com.facishare.crm.sfa.utilities.util.SOI18NKeyUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.List;

/**
 * <AUTHOR> 2019-10-12
 * @instruction
 */
public class TieredPriceBookEditAction extends StandardEditAction {
    private CPQTieredPriceService cpqTieredPriceService = SpringUtil.getContext().getBean(CPQTieredPriceServiceImpl.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        if (!cpqTieredPriceService.checkActiveTime(arg.getObjectData())
                || !cpqTieredPriceService.checkAdaptationProduct(arg.getDetails().get(TieredPriceConstants.TieredPriceProduct.ApiName.getFieldName()))) {
            throw new ValidateException(I18N.text(SOI18NKeyUtils.SO_TIERED_PRICEBOOK_ACTIVETIME_CHECK));
        }
        List<ObjectDataDocument> ruleList = arg.getDetails().get(TieredPriceConstants.TieredPriceRule.ApiName.getFieldName());
        cpqTieredPriceService.checkRuleStartEndCount(ruleList);
        cpqTieredPriceService.checkRequiredRuleField(arg.getObjectData(), ruleList);
    }
}
