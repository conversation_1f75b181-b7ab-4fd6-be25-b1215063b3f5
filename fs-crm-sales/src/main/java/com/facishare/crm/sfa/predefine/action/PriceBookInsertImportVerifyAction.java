package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.utilities.util.PriceBookUtil;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportVerifyAction;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2018/5/18 15:39
 */
public class PriceBookInsertImportVerifyAction extends StandardInsertImportVerifyAction {

    @Override
    protected List<IFieldDescribe> getValidImportFields() {
        List<IFieldDescribe> fieldDescribes = super.getValidImportFields();
        Optional<IFieldDescribe> accountRangeField = Optional
                .ofNullable(ObjectDescribeExt.of(objectDescribe).getFieldDescribe("account_range"));
        accountRangeField.ifPresent(x -> fieldDescribes.add(0, x));
        PriceBookUtil.removeUnsupportedImportFields(fieldDescribes);
        return fieldDescribes;
    }

}
