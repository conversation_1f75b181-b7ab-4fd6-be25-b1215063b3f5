package com.facishare.crm.sfa.predefine.service.model.Feed;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public interface GetCrmObjectOwnerRangesResult {
    @Data
    class Arg implements Serializable {

        private List<CrmObjectRelation> relations;

        private Integer employeeId;
    }

    @Data
    class Result {
        List<String> Ranges;
    }

    @Data
    class CrmObjectRelation{
        String api_name;
        String data_id;
    }
    @Data
    class CrmObjectRelationInfo{
        List<Integer> Ranges;
    }
}
