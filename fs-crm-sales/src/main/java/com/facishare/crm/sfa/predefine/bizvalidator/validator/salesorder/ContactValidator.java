package com.facishare.crm.sfa.predefine.bizvalidator.validator.salesorder;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.bizvalidator.Validator;
import com.facishare.crm.sfa.predefine.bizvalidator.ValidatorContext;
import com.facishare.crm.sfa.utilities.constant.ContactConstants;
import com.facishare.crm.sfa.utilities.constant.SalesOrderConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import org.elasticsearch.common.Strings;

import java.util.Objects;

/**
 * 订单-联系人校验器
 *
 * <AUTHOR>
 */
public class ContactValidator implements Validator {
    ServiceFacade serviceFacade = SpringUtil.getContext().getBean(ServiceFacade.class);

    @Override
    public void validate(ValidatorContext context) {
        IObjectData objectData = context.getObjectData();
        String accountId = objectData.get(SalesOrderConstants.SalesOrderField.ACCOUNT_ID.getApiName(), String.class);
        String contactId = objectData.get(SalesOrderConstants.SalesOrderField.SHIP_TO_ID.getApiName(), String.class);
        if (!Strings.isNullOrEmpty(contactId)) {
            User user = User.builder().tenantId(context.getUser().getTenantId()).userId(User.SUPPER_ADMIN_USER_ID).build();
            IObjectData contactData = serviceFacade.findObjectDataIgnoreRelevantTeam(user, contactId, Utils.CONTACT_API_NAME);
            if (contactData != null) {
                String relatedAccountId = contactData.get(ContactConstants.Field.ACCOUNTID, String.class);
                if (!Objects.equals(accountId, relatedAccountId)) {
                    throw new ValidateException(String.format(I18N.text("sfa.a.not.match.b"),
                            I18N.text("SalesOrderObj.field.ship_to_id.label"),
                            I18N.text("AccountObj.attribute.self.display_name")));
                }
            }
        }
    }
}
