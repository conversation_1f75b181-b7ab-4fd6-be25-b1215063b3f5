package com.facishare.crm.sfa.utilities.util;

import com.facishare.crmcommon.rest.CrmRestApi;
import com.facishare.crmcommon.rest.dto.GetInvisibleFields;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.model.RequestContext.Android_CLIENT_INFO_PREFIX;
import static com.facishare.paas.appframework.core.model.RequestContext.IOS_CLIENT_INFO_PREFIX;

/**
 * Created by renlb on 2018/11/15.
 */
@Slf4j
public class InvisibleFieldUtil {
    private static final CrmRestApi CRM_REST_API = SpringUtil.getContext().getBean(CrmRestApi.class);
    private static final ServiceFacade serviceFacade = SpringUtil.getContext().getBean(ServiceFacade.class);
    private static final String INVISIBLE_TEXT = "*****";

    public static List<String> findInvisibleFields(String tenantId, String objType, String dataId) {
        List<String> invisibleFields;
        Map<String, String> headers = new HashMap<>();
        headers.put("x-fs-ei", tenantId);

        GetInvisibleFields.Result result = CRM_REST_API.getInvisibleFields(headers, objType, dataId);

        invisibleFields = result.getValue();
        return invisibleFields;
    }

    public static void handleMemberInvisibleFields(User user, List<IObjectData> objectDataList,
                                                   String objType, String dataId, String clientInfo) {
        if (CollectionUtils.notEmpty(objectDataList)) {
            List<String> invisibleFields = Lists.newArrayList();
            if (!StringUtils.isEmpty(dataId)) {
                try {
                    invisibleFields = ObjectPoolUtil.getHideFields(user, objType, dataId);
                } catch (Exception e) {
                    log.error("handleMemberInvisibleFields error, objType:{}, dataId:{},", objType, dataId, e);
                }
            }

            List<String> whiteFields=Lists.newArrayList("is_overtime","is_duplicated","leads_pool_id");
            invisibleFields = invisibleFields.stream().filter(h->!whiteFields.contains(h)).collect(Collectors.toList());
            if (CollectionUtils.notEmpty(invisibleFields)) {
                boolean isMobile = false;
                if (clientInfo.startsWith(Android_CLIENT_INFO_PREFIX) || clientInfo.startsWith(IOS_CLIENT_INFO_PREFIX)) {
                    isMobile = true;
                }
                for (IObjectData objectData : objectDataList) {
                    List<String> ownerList = objectData.getOwner();
                    if (CollectionUtils.empty(ownerList)) {
                        for (String field : invisibleFields) {
                            if (isMobile) {
                                objectData.set(field + "__s", INVISIBLE_TEXT);
                                ObjectDataDocument.of(objectData).remove(field);
                            } else {
                                objectData.set(field, INVISIBLE_TEXT);
                            }
                        }
                    }
                }
            }
        }
    }

    public static void handleInvisibleFields(User user, String objectApiName, List<IObjectData> objectDataList) {
        if (CollectionUtils.notEmpty(objectDataList)) {
            if (!serviceFacade.isAdmin(user)) {
                Set<String> invisibleFields = serviceFacade.getUnauthorizedFields(user, objectApiName);
                if (CollectionUtils.notEmpty(invisibleFields)) {
                    for (IObjectData objectData : objectDataList) {
                        for (String field : invisibleFields) {
                            objectData.set(field + "__s", INVISIBLE_TEXT);
                            ObjectDataDocument.of(objectData).remove(field);
                        }
                    }
                }
            }
        }
    }
}
