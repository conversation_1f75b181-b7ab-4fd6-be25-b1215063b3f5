package com.facishare.crm.sfa.predefine.service.model.QiXin;

import lombok.Data;

import java.util.List;

public interface QiXinUpdateNRCModel {
    @Data
    class Arg extends QiXinBaseModel.Arg {
        private Integer employeeId;

        private Integer outEmployeeId;

        private String bizType;

        private String appId;

        private Integer redNum;

        private boolean recovery;

        private List<QiXinBaseModel.QiXinTodoItemInfo> recoveryTodoItems;
    }

    @Data
    class Result extends QiXinBaseModel.Result {
        private QiXinUpdateNRCInfo data;

        public boolean IsSucess() {
            return "200".equals(data.getCode());
        }
    }

    @Data
    class QiXinUpdateNRCInfo{
        String code;
        String msg;
        Object content;
    }
}
