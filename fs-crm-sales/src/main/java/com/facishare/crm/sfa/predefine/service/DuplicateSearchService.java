package com.facishare.crm.sfa.predefine.service;

import com.facishare.crm.sfa.predefine.service.model.DataCleanInfo;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.metadata.exception.MetadataServiceException;

/**
 * Created by rensx on 2017/7/31.
 */
@ServiceModule("duplicate_search")
public interface DuplicateSearchService {

    @ServiceMethod("getLeatestDataCleanInfo")
    DataCleanInfo.Result getLeatestDataCleanInfo(DataCleanInfo.Arg arg) throws MetadataServiceException;
}
