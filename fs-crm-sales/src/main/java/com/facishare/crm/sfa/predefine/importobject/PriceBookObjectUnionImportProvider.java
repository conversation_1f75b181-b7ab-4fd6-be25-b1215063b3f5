package com.facishare.crm.sfa.predefine.importobject;

import com.facishare.paas.appframework.metadata.importobject.DefaultObjectImportProvider;
import com.facishare.paas.appframework.metadata.importobject.ImportObject;
import com.facishare.paas.appframework.metadata.importobject.ImportType;
import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.springframework.stereotype.Component;

import java.util.Optional;


@Component
public class PriceBookObjectUnionImportProvider extends DefaultObjectImportProvider {

    @Override
    public String getObjectCode() {
        return "multiimport_price_book_account_product";
    }

    @Override
    public Optional<ImportObject> getImportObject(IObjectDescribe objectDescribe, IUniqueRule uniqueRule) {
        return Optional.empty();
    }

    @Override
    protected ImportType getImportType(IObjectDescribe objectDescribe, IUniqueRule uniqueRule) {
        return ImportType.UNSUPPORT_UPDATE_IMPORT;
    }

    @Override
    protected boolean getIsNotSupportSaleEvent(IObjectDescribe objectDescribe) {
        return true;
    }

    @Override
    protected boolean supportImportId(IObjectDescribe objectDescribe) {
        return false;
    }
}