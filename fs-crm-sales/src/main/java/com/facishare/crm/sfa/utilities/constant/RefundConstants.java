package com.facishare.crm.sfa.utilities.constant;

/**
 * @IgnoreI18nFile
 */
public interface RefundConstants {

    String FINANCE_EMPLOYEE_ID = "finance_employee_id";
    String FINANCE_EMPLOYEE_ID__R = "finance_employee_id__r";
    String PIC_ADDR = "pic_addr";



    String QUERY_ORG_USER_SQL  = "select pic_addr from org_user where tenant_id = '%s' and user_id = '%s';";


    enum RefundField{
        ACCOUNT_ID("account_id","客户账户"),
        REFUNDED_AMOUNT("refunded_amount","退款金额(元)"),
        REFUND_TYPE("refunded_method","退款类型"),
        IS_FIXED_FLOW("is_fixed_flow","审批流种类"),
        FINANCE_EMPLOYEE_ID("finance_employee_id","退款财务Id"),
        FINANCE_CONFIRM_TIME("finance_confirm_time","退款确认时间"),
        SALES_ORDER_ID("order_id","销售订单Id"),
        BIZ_STATUS("biz_status", "新业务状态"),
        SUBMIT_TIME("submit_time","提交时间"),
        REFUND_METHOD("refunded_method", "退款方式");
        private String filedApiName;
        private String label;
        RefundField(String filedApiName, String label){
            this.filedApiName = filedApiName;
            this.label = label;
        }
        public String getApiName() {
            return this.filedApiName;
        }
    }
    enum RefundType{
        None("0"),
        //预存款存款
        PrePay("10000"),
        //返利
        Rebate("10001");
        private String value;
        RefundType(String value) {
            this.value = value;
        }
        public String getValue() {
            return this.value;
        }
    }
    enum RefundStatus{
        TBConfirmed("1"),
        Confirmed("3"),
        Reject("4"),
        Invalid("99");
        private String value;
        RefundStatus(String value) {
            this.value = value;
        }
        public String getValue() {
            return this.value;
        }
    }
    enum RefundBizStatus{
        UNREFUND("un_refund"),REFUNDED("refunded");
        private String value;
        RefundBizStatus(String value) {
            this.value = value;
        }
        public String getValue() {
            return this.value;
        }
    }
}
