package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.utilities.util.TeamMemberUtil;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportTemplateAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class BomInsertImportTemplateAction extends StandardInsertImportTemplateAction {


    private List<String> filterFields = Lists.newArrayList("parent_bom_id","bom_path","root_id");


    @Override
    protected void before(Arg arg) {
        super.before(arg);
    }

    @Override
    protected void customHeader(List<IFieldDescribe> headerFieldList) {
        super.customHeader(headerFieldList);
        headerFieldList.removeIf(field-> filterFields.contains(field.getApiName()));
    }
}
