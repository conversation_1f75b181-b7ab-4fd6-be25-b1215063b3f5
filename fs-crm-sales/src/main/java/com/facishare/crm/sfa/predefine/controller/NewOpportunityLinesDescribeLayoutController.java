package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.constant.NewOppportunityConstants;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.facishare.paas.common.util.UdobjConstants.LAYOUT_TYPE_ADD;
import static com.facishare.paas.common.util.UdobjConstants.LAYOUT_TYPE_EDIT;

public class NewOpportunityLinesDescribeLayoutController extends SFADescribeLayoutController {

    @Override
    protected void handleLayout(StandardDescribeLayoutController.Arg arg, StandardDescribeLayoutController.Result result) {
        super.handleLayout(arg, result);
        if (arg.getLayout_type() == null) {
            return;
        }
        if (formComponent != null) {
            switch (arg.getLayout_type()) {
                case LAYOUT_TYPE_EDIT:
                case LAYOUT_TYPE_ADD:
                    handleReadOnlyFields(result);
                    break;
            }
        }
    }

    @Override
    protected void handelDescribe(Arg arg, Result result) {
        super.handelDescribe(arg, result);
        IObjectDescribe describe = result.getObjectDescribe().toObjectDescribe();
        setCascadeParentApiName(describe);
    }

    @Override
    protected boolean supportSaveDraft() {
        return false;
    }

    @Override
    protected List<String> getReadOnlyFields(IObjectDescribe detailDescribe) {
        List<String> readOnlyFields = super.getReadOnlyFields(detailDescribe);
        if (bizConfigThreadLocalCacheService.isPriceBookEnabled(controllerContext.getTenantId())) {
            readOnlyFields.add(NewOppportunityConstants.NewOpportunityLinesField.PRODUCTID.getApiName());
        }
        return readOnlyFields;
    }

    /**
     * 将价目表产品ID的cascade_parent_api_name设置成quote_id
     *
     * @param describe
     */
    private void setCascadeParentApiName(IObjectDescribe describe) {
        List<IFieldDescribe> fields = ObjectDescribeExt.of(describe)
                .getFieldDescribesSilently().stream().filter(field ->
                        field.getApiName().equals(NewOppportunityConstants.NewOpportunityLinesField.PRICEBOOKPRODUCTID.getApiName()))
                .collect(Collectors.toList());

        for (IFieldDescribe field : fields) {
            field.set("cascade_parent_api_name", Arrays.asList("new_opportunity_id"));
        }
    }
}
