package com.facishare.crm.sfa.predefine.service.real.cpq;

import com.facishare.crm.sfa.utilities.constant.TieredPriceConstants;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
public class LadderPriceServiceImpl implements PriceService {
    @Override
    public <T extends IObjectData> PriceModel price(final BigDecimal count, String priceType, List<T> rules, BigDecimal originalPrice, BigDecimal priceBookDiscount) {

        if (!(Objects.equals(priceType, "1") || Objects.equals(priceType, "2"))) {
            log.warn("priceType wrong. priceType:{}", priceType);
            throw new ValidateException("priceType wrong. priceType" + priceType);
        } else {
            Optional<T> first = rules.stream().filter(r -> isInRange(count.doubleValue(), r)).findFirst();
            BigDecimal unitPrice = null;
            if (first.isPresent()) {
                T rule = first.get();

                if (TieredPriceConstants.isPricing(priceType)) {
                    int rulePriceType = rule.get("price_type", Integer.class);
                    if (1 == rulePriceType) {
                        unitPrice = rule.get(TieredPriceConstants.TieredPriceRule.Price.getFieldName(), BigDecimal.class);
                    }
                    if (2 == rulePriceType) {
                        return PriceModel.ofMatch(rule.get(TieredPriceConstants.TieredPriceRule.Price.getFieldName(), BigDecimal.class));
                    }
                } else if (TieredPriceConstants.isDiscount(priceType)) {
                    unitPrice = originalPrice.multiply(
                            new BigDecimal(rule.get(TieredPriceConstants.TieredPriceRule.Discount.getFieldName(), String.class))
                                    .divide(new BigDecimal("100"), 16, 5)
                    );
                }
                return PriceModel.ofMatch(unitPrice.multiply(count));
            } else {
                return PriceModel.ofNotMatch();
            }
        }
    }

    @Override
    public String ladderOrLayer() {
        return TieredPriceConstants.LADDER_TYPE;
    }


    /**
     * 计算数量是否在规则区间内
     *
     * @param count 产品数量
     * @param rule  规则
     * @return 在规则区间内 true
     */
    private boolean isInRange(double count, IObjectData rule) {
        Object tmpStartCount = rule.get(TieredPriceConstants.TieredPriceRule.StartCount.getFieldName());
        Double startCount;
        if (tmpStartCount == null) {
            startCount = 0D;
        } else {
            startCount = Double.valueOf(tmpStartCount.toString());
        }

        Object tmpEndCount = rule.get(TieredPriceConstants.TieredPriceRule.EndCount.getFieldName());
        Double endCount;
        if (tmpEndCount == null) {
            endCount = Double.MAX_VALUE;
        } else {
            endCount = Double.valueOf(tmpEndCount.toString());
        }

        // (startCount,endCount]这样的区间比较
        return startCount < count && count <= endCount;
    }

}
