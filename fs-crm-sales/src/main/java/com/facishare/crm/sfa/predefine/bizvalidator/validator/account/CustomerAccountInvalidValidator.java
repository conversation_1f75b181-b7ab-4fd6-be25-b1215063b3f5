package com.facishare.crm.sfa.predefine.bizvalidator.validator.account;

import com.facishare.crm.sfa.predefine.bizvalidator.Validator;
import com.facishare.crm.sfa.predefine.bizvalidator.ValidatorContext;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.utilities.proxy.CustomerAccountProxy;
import com.facishare.crm.sfa.utilities.proxy.model.CanInvalidByCustomerIdsProxyModel;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.metadata.util.SFAHeaderUtil;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.stream.Collectors;

public class CustomerAccountInvalidValidator implements Validator {
    BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext()
            .getBean(BizConfigThreadLocalCacheService.class);
    CustomerAccountProxy CUSTOMER_ACCOUNT_PROXY = SpringUtil.getContext().getBean(CustomerAccountProxy.class);
    @Override
    public void validate(ValidatorContext context) {
        if (bizConfigThreadLocalCacheService.isCustomerAccountEnabled(context.getUser().getTenantId())) {
            CanInvalidByCustomerIdsProxyModel.Arg customerProxyArg = CanInvalidByCustomerIdsProxyModel.Arg.builder().build();
            if(context.getObjectDataList() != null){
                customerProxyArg.setCustomerIds(context.getObjectDataList().stream().map(o-> o.getId()).collect(Collectors.toList()));
            }
            if(context.getObjectData() != null){
                customerProxyArg.setCustomerIds(Lists.newArrayList(context.getObjectData().getId()));
            }
            CanInvalidByCustomerIdsProxyModel.Result result = CUSTOMER_ACCOUNT_PROXY.canInvalidByCustomerIds(customerProxyArg,SFAHeaderUtil.getHeaders(context.getUser()));
            if(result.getResult() == null){
                throw new ValidateException(result.getErrMessage());
            }
            if(!result.getResult().isSuccess()){
                List<String> errMsg = Lists.newArrayList();
                result.getResult().getErrorReasons().keySet().forEach(e->{
                    errMsg.add(String.format("%s: %s", e,result.getResult().getErrorReasons().get(e)));
                });
                throw new ValidateException(String.join("\n",errMsg));
            }
        }
    }
}
