package com.facishare.crm.sfa.predefine.action;

import com.facishare.paas.appframework.core.predef.action.StandardUnlockAction;
import com.facishare.paas.common.util.UdobjConstants;

/**
 * Created by renlb on 2019/7/16.
 */
public class SalesOrderUnlockAction extends StandardUnlockAction {
    @Override
    protected void before(Arg arg) {
        arg.setDetailObjStrategy(UdobjConstants.LOCK_STRATEGY.CASCADE_DETAIL_OBJ.getValue());
        super.before(arg);
    }
}
