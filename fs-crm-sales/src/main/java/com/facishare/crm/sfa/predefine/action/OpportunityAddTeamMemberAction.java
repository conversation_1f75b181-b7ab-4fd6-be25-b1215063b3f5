package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.predef.action.StandardAddTeamMemberAction;
import com.facishare.paas.appframework.metadata.TeamMember;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class OpportunityAddTeamMemberAction extends StandardAddTeamMemberAction {

    private ObjectDataServiceImpl objectDataService = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class); ;

    @Override
    protected List<String> getAssociateIds(String otherObjectApiName) {
        List<String> result = Lists.newArrayList();
        IObjectDescribe objectDescribe = serviceFacade.findObject(actionContext.getTenantId(), otherObjectApiName);
        String sql = String.format("select id from %s where tenant_id='%s' and opportunity_id=any(array[%s])",
                objectDescribe.getStoreTableName(),
                actionContext.getTenantId(), Joiner.on(",").join(arg.getDataIDs().stream()
                        .map(id -> "'" + id + "'").collect(Collectors.toList())));
        try {
            List<Map> dataList = objectDataService.findBySql(actionContext.getTenantId(), sql);
            if (CollectionUtils.notEmpty(dataList)) {
                result = dataList.stream().map(x -> String.valueOf(x.get("id"))).collect(Collectors.toList());
            }
        } catch (MetadataServiceException e) {
            log.error("getAssociateIds->findBySql error", e);
        }

        return result;
    }

    @Override
    protected void doValidateTeamMemberRole(Arg arg) {
        if (StringUtils.isEmpty(arg.getTeamMemberRole()) && CollectionUtils.empty(arg.getTeamMemberRoleList())) {
            throw new ValidateException(I18N.text(I18NKey.TEAM_MEMBER_ROLE_IS_EMPTY));
        }
    }

    @Override
    protected Arg buildAddTeamMemberArg(Arg arg, String otherObjectApiName) {
        Arg actionArg = super.buildAddTeamMemberArg(arg, otherObjectApiName);
        if(otherObjectApiName.equals(SFAPreDefineObject.SalesOrder.getApiName())){
            actionArg.setTeamMemberRoleList(Lists.newArrayList(TeamMember.Role.NORMAL_STAFF.getValue()));
            actionArg.setTeamMemberRole(TeamMember.Role.NORMAL_STAFF.getValue());
        }
        return actionArg;
    }
}