package com.facishare.crm.sfa.utilities.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crmcommon.constants.SystemConstants;
import com.facishare.crm.sfa.model.Enum.LeadsBizStatusEnum;
import com.facishare.crm.sfa.model.ObjectLimitRuleModel;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.exception.SFABusinessException;
import com.facishare.crm.sfa.predefine.exception.SFAErrorCode;
import com.facishare.crm.sfa.utilities.constant.LeadsConstants;
import com.facishare.crmcommon.util.*;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.ServiceFacadeImpl;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.MetaDataMiscServiceImpl;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.expression.ExpressionService;
import com.facishare.paas.expression.ExpressionServiceImpl;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IObjectReferenceField;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.api.service.ICommonSqlService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.*;
import com.facishare.paas.metadata.service.impl.CommonSqlServiceImpl;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class ObjectLimitUtil {
    private static final ServiceFacade SERVICE_FACADE = SpringUtil.getContext().getBean(ServiceFacadeImpl.class);
    private static final ICommonSqlService commonSqlService = SpringUtil.getContext().getBean(CommonSqlServiceImpl.class);
    private static final FsGrayReleaseBiz gray = FsGrayRelease.getInstance("sfa");
    private static final ExpressionService expressionService = SpringUtil.getContext().getBean("expressionService", ExpressionServiceImpl.class);
    private static final ObjectLimitExpressionUtils objectLimitExpressionUtils = SpringUtil.getContext().getBean(ObjectLimitExpressionUtils.class);
    private static final MetaDataMiscServiceImpl metaDataMiscService = SpringUtil.getContext().getBean(MetaDataMiscServiceImpl.class);

    private static final String OBJECT_LIMIT_RULE_TABLE = "object_limit_rule";
    private static final String OBJECT_LIMIT_EMPLOYEE_RULE_TABLE = "object_limit_employee_rule";
    private static final String OBJECT_LIMIT_FILTER_TABLE = "object_limit_filter";
    private static final String OBJECT_LIMIT_GLOBAL_FILTER_TABLE = "object_limit_global_filter";
    private static final String OBJECT_LIMIT_OVER_RULE_TABLE = "object_limit_over_rule";
    public static final String ACCOUNT_LIMIT_RULE_TABLE = "account_limit_rule";
    public static final String EMPLOYEE_RULE_TABLE = "biz_employee_rule";

    private static StopWatch stopWatch = StopWatch.create("ObjectLimitUtil");

    public static CheckLimitResult checkObjectLimit(User user, String objectApiName, String owner, List<IObjectData> objectDataList, IObjectDescribe objectDescribe) {
        //todo 考虑是否有清凉的判断方式
        CheckLimitResult result = CheckLimitResult.builder().successIds(Lists.newArrayList())
                .failureIds(Lists.newArrayList()).build();

        List<String> objectIds = objectDataList.stream().map(x -> x.getId()).collect(Collectors.toList());
        List<ObjectLimitRuleModel.ObjectLimitRule> employeeLimitRuleList = getObjectLimitRuleByEmployeeId(user.getTenantId(), objectApiName, owner);
        if (CollectionUtils.empty(employeeLimitRuleList)) {
            result.getSuccessIds().addAll(objectIds);
            return result;
        }

        List<ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter> globalFilterList = getObjectLimitRuleGlobalFilter(user.getTenantId(), objectApiName);
        Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> limitFilterDataListMap = getLimitFilterDataListMap(globalFilterList, employeeLimitRuleList, objectDataList, objectDescribe);
        if (CollectionUtils.empty(limitFilterDataListMap)) {
            result.getSuccessIds().addAll(objectIds);
            return result;
        }
        List<String> failureIds = getFailureDataIds(user, owner, globalFilterList, limitFilterDataListMap, objectDescribe);

        if (CollectionUtils.empty(failureIds)) {
            result.getSuccessIds().addAll(objectIds);
            return result;
        }
        List<String> successIds = Lists.newArrayList(objectIds);
        successIds.removeIf(x -> failureIds.contains(x));
        result.getSuccessIds().addAll(successIds);
        result.getFailureIds().addAll(failureIds);

        return result;
    }

    public static CheckLimitResult checkObjectLimit(User user, String objectApiName, List<IObjectData> objectDataList, IObjectDescribe objectDescribe) {
        CheckLimitResult result = CheckLimitResult.builder().successIds(Lists.newArrayList())
                .failureIds(Lists.newArrayList()).build();
        if (CollectionUtils.empty(objectDataList) || StringUtils.isEmpty(objectApiName)) {
            return result;
        }
        Set<String> ownerIds = objectDataList.stream().filter(x -> AccountUtil.hasOwner(x))
                .map(x -> AccountUtil.getOwner(x)).collect(Collectors.toSet());
        if (CollectionUtils.empty(ownerIds)) {
            return result;
        }

        for (String owner : ownerIds) {
            List<IObjectData> dataList = objectDataList.stream()
                    .filter(x -> AccountUtil.hasOwner(x) && owner.equals(AccountUtil.getOwner(x)))
                    .collect(Collectors.toList());
            CheckLimitResult checkLimitResult = checkObjectLimit(user, objectApiName, owner, dataList, objectDescribe);
            if (CollectionUtils.notEmpty(checkLimitResult.getFailureIds())) {
                result.getFailureIds().addAll(checkLimitResult.getFailureIds());
                break;
            }
            if (CollectionUtils.notEmpty(checkLimitResult.getSuccessIds())) {
                result.getSuccessIds().addAll(checkLimitResult.getSuccessIds());
            }
        }

        return result;
    }

    public static CheckLimitResult checkOutUserObjectLimit(User user, String objectApiName, String outTenantId, String outOwner, List<IObjectData> objectDataList, IObjectDescribe objectDescribe, boolean checkOrganizationLimit) {
        CheckLimitResult result = CheckLimitResult.builder().successIds(Lists.newArrayList())
                .failureIds(Lists.newArrayList()).build();

        List<String> objectIds = objectDataList.stream().map(x -> x.getId()).collect(Collectors.toList());
        List<ObjectLimitRuleModel.ObjectLimitRule> allEmployeeLimitRuleList = getObjectLimitRuleByEmployeeId(user.getTenantId(), objectApiName, outOwner);
        if (CollectionUtils.empty(allEmployeeLimitRuleList)) {
            result.getSuccessIds().addAll(objectIds);
            return result;
        }

        List<ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter> globalFilterList = getObjectLimitRuleGlobalFilter(user.getTenantId(), objectApiName);

        List<ObjectLimitRuleModel.ObjectLimitRule> employeeLimitRuleList = allEmployeeLimitRuleList.stream()
                .filter(x -> ObjectLimitRuleModel.RuleTypeEnum.EMPLOYEE.getCode().equals(x.getRuleType()))
                .collect(Collectors.toList());

        Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> limitFilterDataListMap = getLimitFilterDataListMap(globalFilterList, employeeLimitRuleList, objectDataList, objectDescribe);
        if (CollectionUtils.empty(limitFilterDataListMap)) {
            if (!checkOrganizationLimit) {
                result.getSuccessIds().addAll(objectIds);
                return result;
            }
        }

        List<String> failureIds = getFailureDataIds(user, outTenantId, outOwner, globalFilterList, limitFilterDataListMap, objectDescribe, false);
        List<String> successIds = Lists.newArrayList(objectIds);
        if (CollectionUtils.notEmpty(failureIds)) {
            successIds.removeIf(x -> failureIds.contains(x));
        }
        if (CollectionUtils.empty(successIds)) {
            return result;
        }

        List<ObjectLimitRuleModel.ObjectLimitRule> organizationLimitRuleList = allEmployeeLimitRuleList.stream()
                .filter(x -> ObjectLimitRuleModel.RuleTypeEnum.ORGANIZATION.getCode().equals(x.getRuleType()))
                .collect(Collectors.toList());

        if (CollectionUtils.empty(organizationLimitRuleList)) {
            result.getSuccessIds().addAll(successIds);
            return result;
        }

        Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> organizationLimitFilterDataListMap = getLimitFilterDataListMap(globalFilterList, organizationLimitRuleList, objectDataList, objectDescribe);
        if (CollectionUtils.empty(organizationLimitFilterDataListMap)) {
            result.getSuccessIds().addAll(successIds);
            return result;
        }

        List<String> orgFailureIds = getFailureDataIds(user, outTenantId, outOwner, globalFilterList, organizationLimitFilterDataListMap, objectDescribe, true);

        Set<String> allFailureIds = Sets.newHashSet(failureIds);
        allFailureIds.addAll(orgFailureIds);
        successIds = Lists.newArrayList(objectIds);
        if (CollectionUtils.notEmpty(allFailureIds)) {
            successIds.removeIf(x -> allFailureIds.contains(x));
        }

        result.getSuccessIds().addAll(successIds);
        result.getFailureIds().addAll(Lists.newArrayList(allFailureIds));
        return result;
    }

    public static CheckLimitResult checkOutUserObjectLimit(User user, String objectApiName, List<IObjectData> objectDataList, IObjectDescribe objectDescribe, boolean checkOrganizationLimit) {
        CheckLimitResult result = CheckLimitResult.builder().successIds(Lists.newArrayList())
                .failureIds(Lists.newArrayList()).build();

        Set<String> outTenantIds = objectDataList.stream().filter(x ->
                StringUtils.isNotBlank(AccountUtil.getStringValue(x, SystemConstants.Field.OutTenantId.apiName, "")))
                .map(x -> AccountUtil.getStringValue(x, SystemConstants.Field.OutTenantId.apiName, "")).collect(Collectors.toSet());
        if (CollectionUtils.notEmpty(outTenantIds)) {
            for (String outTenantId : outTenantIds) {
                List<IObjectData> tempDataList = objectDataList.stream().filter(x -> AccountUtil.getStringValue(x, SystemConstants.Field.OutTenantId.apiName, "").equals(outTenantId)).collect(Collectors.toList());
                if (CollectionUtils.empty(tempDataList)) {
                    continue;
                }
                Set<String> outOwnerList = tempDataList.stream().filter(x ->
                        StringUtils.isNotBlank(AccountUtil.getOutOwner(x)))
                        .map(x -> AccountUtil.getOutOwner(x)).collect(Collectors.toSet());
                if (CollectionUtils.empty(outOwnerList)) {
                    continue;
                }

                for (String outOwner : outOwnerList) {
                    List<IObjectData> outOwnerDataList = tempDataList.stream().filter(x -> AccountUtil.getOutOwner(x).equals(outOwner)).collect(Collectors.toList());
                    CheckLimitResult checkLimitResult = checkOutUserObjectLimit(user, objectApiName, outTenantId, outOwner, outOwnerDataList, objectDescribe, checkOrganizationLimit);
                    if (CollectionUtils.notEmpty(checkLimitResult.getFailureIds())) {
                        result.getFailureIds().addAll(checkLimitResult.getFailureIds());
                        break;
                    }
                    if (CollectionUtils.notEmpty(checkLimitResult.getSuccessIds())) {
                        result.getSuccessIds().addAll(checkLimitResult.getSuccessIds());
                    }
                }
            }
        }
        return result;
    }

    private static Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> getLimitFilterDataListMap(List<ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter> globalFilterList, List<ObjectLimitRuleModel.ObjectLimitRule> employeeLimitRuleList, List<IObjectData> objectDataList, IObjectDescribe objectDescribe) {
        Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> result = Maps.newHashMap();
        if (CollectionUtils.empty(employeeLimitRuleList)) {
            return result;
        }
        String globalExpression = objectLimitExpressionUtils.getExpression(globalFilterList, objectDescribe);
        log.info("checkObjectLimit globalFilterList: {} ", globalFilterList);
        log.info("checkObjectLimit globalExpression: {} ", globalExpression);

        List<ObjectLimitRuleModel.ObjectLimitFilter> employeeLimitRuleFilterList = Lists.newArrayList();
        Optional<ObjectLimitRuleModel.ObjectLimitRule> defaultLimitRule = employeeLimitRuleList.stream()
                .filter(x -> x.isDefaultRule()).findFirst();
        if (defaultLimitRule.isPresent()) {
            employeeLimitRuleFilterList.addAll(defaultLimitRule.get().getObjectLimitFilterList());
        } else {
            for (ObjectLimitRuleModel.ObjectLimitRule objectLimitRule : employeeLimitRuleList) {
                employeeLimitRuleFilterList.addAll(objectLimitRule.getObjectLimitFilterList());
            }
        }

        employeeLimitRuleFilterList = employeeLimitRuleFilterList.stream()
                .sorted(Comparator.comparing(ObjectLimitRuleModel.ObjectLimitFilter::getLimitNumber)
                        .thenComparing(ObjectLimitRuleModel.ObjectLimitFilter::getCreateTime)
                        .thenComparing(ObjectLimitRuleModel.ObjectLimitFilter::getId))
                .collect(Collectors.toList());

        for (ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter globalFilter : globalFilterList) {
            convertData(objectDescribe, globalFilter.getWheres(), objectDataList);
        }

        for (ObjectLimitRuleModel.ObjectLimitFilter limitFilter : employeeLimitRuleFilterList) {
            convertData(objectDescribe, limitFilter.getWheres(), objectDataList);
            String expression = objectLimitExpressionUtils.getExpression(limitFilter, objectDescribe);
            expression = String.format("( %s ) && ( %s )", globalExpression, expression);
            log.info("checkObjectLimit limitFilter: {} ", limitFilter);
            log.info("checkObjectLimit expression: {} ", expression);

            List<String> limitObjectIds = Lists.newArrayList();
            for (IObjectData objectData : objectDataList) {
                Boolean evaluate = expressionService.evaluate(expression, ObjectDataExt.toMap(objectData));
                if (evaluate) {
                    limitObjectIds.add(objectData.getId());
                }
            }
            if (CollectionUtils.notEmpty(limitObjectIds)) {
                result.put(limitFilter, limitObjectIds);
            }
        }

        return result;
    }

    public static CheckLimitResult checkObjectLimitForEdit(User user, String objectApiName, String owner, List<IObjectData> oldObjectDataList, List<IObjectData> objectDataList, IObjectDescribe objectDescribe) {
        CheckLimitResult result = CheckLimitResult.builder().successIds(Lists.newArrayList())
                .failureIds(Lists.newArrayList()).build();

        List<String> objectIds = objectDataList.stream().map(x -> x.getId()).collect(Collectors.toList());
        List<ObjectLimitRuleModel.ObjectLimitRule> employeeLimitRuleList = getObjectLimitRuleByEmployeeId(user.getTenantId(), objectApiName, owner);
        if (CollectionUtils.empty(employeeLimitRuleList)) {
            result.getSuccessIds().addAll(objectIds);
            return result;
        }

        List<ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter> globalFilterList = getObjectLimitRuleGlobalFilter(user.getTenantId(), objectApiName);
        Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> limitFilterOldDataListMap = getLimitFilterDataListMap(globalFilterList, employeeLimitRuleList, oldObjectDataList, objectDescribe);
        Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> limitFilterDataListMap = getLimitFilterDataListMap(globalFilterList, employeeLimitRuleList, objectDataList, objectDescribe);
        if (CollectionUtils.empty(limitFilterDataListMap)) {
            result.getSuccessIds().addAll(objectIds);
            return result;
        }
        List<String> failureIds = getFailureDataIds(user, owner, globalFilterList, limitFilterOldDataListMap, limitFilterDataListMap, objectDescribe);

        if (CollectionUtils.empty(failureIds)) {
            result.getSuccessIds().addAll(objectIds);
            return result;
        }
        List<String> successIds = Lists.newArrayList(objectIds);
        successIds.removeIf(x -> failureIds.contains(x));
        result.getSuccessIds().addAll(successIds);
        result.getFailureIds().addAll(failureIds);

        return result;
    }

    public static CheckLimitResult checkOutUserObjectLimitForEdit(User user, String objectApiName, String outTenantId, String outOwner, List<IObjectData> oldObjectDataList, List<IObjectData> objectDataList, IObjectDescribe objectDescribe, boolean checkOrganizationLimit) {
        CheckLimitResult result = CheckLimitResult.builder().successIds(Lists.newArrayList())
                .failureIds(Lists.newArrayList()).build();

        List<String> objectIds = objectDataList.stream().map(x -> x.getId()).collect(Collectors.toList());
        List<ObjectLimitRuleModel.ObjectLimitRule> allEmployeeLimitRuleList = getObjectLimitRuleByEmployeeId(user.getTenantId(), objectApiName, outOwner);
        if (CollectionUtils.empty(allEmployeeLimitRuleList)) {
            result.getSuccessIds().addAll(objectIds);
            return result;
        }

        List<ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter> globalFilterList = getObjectLimitRuleGlobalFilter(user.getTenantId(), objectApiName);

        List<ObjectLimitRuleModel.ObjectLimitRule> employeeLimitRuleList = allEmployeeLimitRuleList.stream()
                .filter(x -> ObjectLimitRuleModel.RuleTypeEnum.EMPLOYEE.getCode().equals(x.getRuleType()))
                .collect(Collectors.toList());

        Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> limitFilterOldDataListMap = getLimitFilterDataListMap(globalFilterList, employeeLimitRuleList, oldObjectDataList, objectDescribe);
        Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> limitFilterDataListMap = getLimitFilterDataListMap(globalFilterList, employeeLimitRuleList, objectDataList, objectDescribe);
        if (CollectionUtils.empty(limitFilterDataListMap)) {
            if (!checkOrganizationLimit) {
                result.getSuccessIds().addAll(objectIds);
                return result;
            }
        }

        List<String> failureIds = getFailureDataIds(user, outTenantId, outOwner, globalFilterList, limitFilterOldDataListMap, limitFilterDataListMap, objectDescribe, false);
        List<String> successIds = Lists.newArrayList(objectIds);
        if (CollectionUtils.notEmpty(failureIds)) {
            successIds.removeIf(x -> failureIds.contains(x));
        }
        if (CollectionUtils.empty(successIds)) {
            return result;
        }

        List<ObjectLimitRuleModel.ObjectLimitRule> organizationLimitRuleList = allEmployeeLimitRuleList.stream()
                .filter(x -> ObjectLimitRuleModel.RuleTypeEnum.ORGANIZATION.getCode().equals(x.getRuleType()))
                .collect(Collectors.toList());

        if (CollectionUtils.empty(organizationLimitRuleList)) {
            result.getSuccessIds().addAll(successIds);
            return result;
        }

        Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> organizationLimitFilterOldDataListMap = getLimitFilterDataListMap(globalFilterList, organizationLimitRuleList, oldObjectDataList, objectDescribe);
        Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> organizationLimitFilterDataListMap = getLimitFilterDataListMap(globalFilterList, organizationLimitRuleList, objectDataList, objectDescribe);
        if (CollectionUtils.empty(organizationLimitFilterDataListMap)) {
            result.getSuccessIds().addAll(successIds);
            return result;
        }

        List<String> orgFailureIds = getFailureDataIds(user, outTenantId, outOwner, globalFilterList, organizationLimitFilterOldDataListMap, organizationLimitFilterDataListMap, objectDescribe, true);

        Set<String> allFailureIds = Sets.newHashSet(failureIds);
        allFailureIds.addAll(orgFailureIds);
        successIds = Lists.newArrayList(objectIds);
        if (CollectionUtils.notEmpty(allFailureIds)) {
            successIds.removeIf(x -> allFailureIds.contains(x));
        }

        result.getSuccessIds().addAll(successIds);
        result.getFailureIds().addAll(Lists.newArrayList(allFailureIds));
        return result;
    }

    private static List<String> getFailureDataIds(User user, String owner
            , List<ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter> globalFilterList
            , Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> limitFilterDataListMap
            , IObjectDescribe objectDescribe) {
        Set<String> failureIds = Sets.newHashSet();
        for (Map.Entry<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> entry : limitFilterDataListMap.entrySet()) {
            String objectApiName = entry.getKey().getObjectApiName();
            Map<String, String> filterValues = Maps.newHashMap();
            filterValues.put(SystemConstants.Field.Owner.apiName, owner);
            SearchTemplateQuery searchTemplateQuery = getSearchTemplateQuery(filterValues, globalFilterList, entry.getKey(), objectDescribe);
            log.info("checkObjectLimit searchTemplateQuery: {} ", searchTemplateQuery);

            QueryResult<IObjectData> queryResult = SERVICE_FACADE.findBySearchQuery(user, objectApiName, searchTemplateQuery);

            log.info("checkObjectLimit queryResult: {} ", queryResult);
            log.info("checkObjectLimit objectLimitFilter: {} ", entry.getKey());

            if (entry.getKey().getLimitNumber() > queryResult.getTotalNumber()) {
                int ownNumber = entry.getKey().getLimitNumber() - queryResult.getTotalNumber();
                if (ownNumber < entry.getValue().size()) {
                    failureIds.addAll(entry.getValue().subList(ownNumber, entry.getValue().size()));
                }
            } else {
                failureIds.addAll(entry.getValue());
            }
        }
        if (CollectionUtils.empty(failureIds)) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(failureIds);
    }

    private static List<String> getFailureDataIds(User user, String outTenantId, String outOwner
            , List<ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter> globalFilterList
            , Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> limitFilterDataListMap
            , IObjectDescribe objectDescribe, boolean isOrgCheck) {
        Set<String> failureIds = Sets.newHashSet();
        for (Map.Entry<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> entry : limitFilterDataListMap.entrySet()) {
            String objectApiName = entry.getKey().getObjectApiName();
            Map<String, String> filterValues = Maps.newHashMap();
            filterValues.put(SystemConstants.Field.OutTenantId.apiName, outTenantId);
            if (!isOrgCheck) {
                filterValues.put(SystemConstants.Field.OutOwner.apiName, outOwner);
            }

            SearchTemplateQuery searchTemplateQuery = getSearchTemplateQuery(filterValues, globalFilterList, entry.getKey(), objectDescribe);
            log.info("checkObjectLimit searchTemplateQuery: {} ", searchTemplateQuery);

            QueryResult<IObjectData> queryResult = SERVICE_FACADE.findBySearchQuery(user, objectApiName, searchTemplateQuery);

            log.info("checkObjectLimit queryResult: {} ", queryResult);
            log.info("checkObjectLimit objectLimitFilter: {} ", entry.getKey());

            if (entry.getKey().getLimitNumber() > queryResult.getTotalNumber()) {
                int ownNumber = entry.getKey().getLimitNumber() - queryResult.getTotalNumber();
                if (ownNumber < entry.getValue().size()) {
                    failureIds.addAll(entry.getValue().subList(ownNumber, entry.getValue().size()));
                }
            } else {
                failureIds.addAll(entry.getValue());
            }
        }
        if (CollectionUtils.empty(failureIds)) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(failureIds);
    }

    private static List<String> getFailureDataIds(User user, String owner
            , List<ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter> globalFilterList
            , Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> limitFilterOldDataListMap
            , Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> limitFilterDataListMap
            , IObjectDescribe objectDescribe) {
        Set<String> failureIds = Sets.newHashSet();
        stopWatch.lap("getFailureDataIds start");
        for (Map.Entry<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> entry : limitFilterDataListMap.entrySet()) {
            List<String> exclusiveIds = Lists.newArrayList();
            Optional<Map.Entry<ObjectLimitRuleModel.ObjectLimitFilter, List<String>>> optionalObjectLimitFilterListEntry = limitFilterOldDataListMap.entrySet().stream()
                    .filter(x -> x.getKey().getId().equals(entry.getKey().getId())).findFirst();
            if (optionalObjectLimitFilterListEntry.isPresent()) {
                exclusiveIds.addAll(optionalObjectLimitFilterListEntry.get().getValue());
                exclusiveIds.removeIf(x -> !entry.getValue().contains(x));
            }
            String objectApiName = entry.getKey().getObjectApiName();
            Map<String, String> filterValues = Maps.newHashMap();
            filterValues.put(SystemConstants.Field.Owner.apiName, owner);
            //todo 需要es解决性能问题
            SearchTemplateQuery searchTemplateQuery = getSearchTemplateQuery(filterValues, globalFilterList, entry.getKey(), objectDescribe);
            log.info("checkObjectLimit searchTemplateQuery: {} ", searchTemplateQuery);

            QueryResult<IObjectData> queryResult = SERVICE_FACADE.findBySearchQuery(user, objectApiName, searchTemplateQuery);

            log.info("checkObjectLimit queryResult: {} ", queryResult);
            log.info("checkObjectLimit objectLimitFilter: {} ", entry.getKey());

            Integer totalNumber = queryResult.getTotalNumber() - exclusiveIds.size();

            if (entry.getKey().getLimitNumber() > totalNumber) {
                int ownNumber = entry.getKey().getLimitNumber() - totalNumber;
                if (ownNumber < entry.getValue().size()) {
                    failureIds.addAll(entry.getValue().subList(ownNumber, entry.getValue().size()));
                }
            } else {
                failureIds.addAll(entry.getValue());
            }
        }
        stopWatch.lap("getFailureDataIds end");
        if (CollectionUtils.empty(failureIds)) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(failureIds);
    }

    private static List<String> getFailureDataIds(User user, String outTenantId, String outOwner
            , List<ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter> globalFilterList
            , Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> limitFilterOldDataListMap
            , Map<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> limitFilterDataListMap
            , IObjectDescribe objectDescribe, boolean isOrgCheck) {
        Set<String> failureIds = Sets.newHashSet();
        for (Map.Entry<ObjectLimitRuleModel.ObjectLimitFilter, List<String>> entry : limitFilterDataListMap.entrySet()) {
            List<String> exclusiveIds = Lists.newArrayList();
            Optional<Map.Entry<ObjectLimitRuleModel.ObjectLimitFilter, List<String>>> optionalObjectLimitFilterListEntry = limitFilterOldDataListMap.entrySet().stream()
                    .filter(x -> x.getKey().getId().equals(entry.getKey().getId())).findFirst();
            if (optionalObjectLimitFilterListEntry.isPresent()) {
                exclusiveIds.addAll(optionalObjectLimitFilterListEntry.get().getValue());
                exclusiveIds.removeIf(x -> !entry.getValue().contains(x));
            }

            String objectApiName = entry.getKey().getObjectApiName();
            Map<String, String> filterValues = Maps.newHashMap();
            filterValues.put(SystemConstants.Field.OutTenantId.apiName, outTenantId);
            if (!isOrgCheck) {
                filterValues.put(SystemConstants.Field.OutOwner.apiName, outOwner);
            }

            SearchTemplateQuery searchTemplateQuery = getSearchTemplateQuery(filterValues, globalFilterList, entry.getKey(), objectDescribe);
            log.info("checkObjectLimit searchTemplateQuery: {} ", searchTemplateQuery);

            QueryResult<IObjectData> queryResult = SERVICE_FACADE.findBySearchQuery(user, objectApiName, searchTemplateQuery);

            log.info("checkObjectLimit queryResult: {} ", queryResult);
            log.info("checkObjectLimit objectLimitFilter: {} ", entry.getKey());

            Integer totalNumber = queryResult.getTotalNumber() - exclusiveIds.size();

            if (entry.getKey().getLimitNumber() > totalNumber) {
                int ownNumber = entry.getKey().getLimitNumber() - totalNumber;
                if (ownNumber < entry.getValue().size()) {
                    failureIds.addAll(entry.getValue().subList(ownNumber, entry.getValue().size()));
                }
            } else {
                failureIds.addAll(entry.getValue());
            }
        }
        if (CollectionUtils.empty(failureIds)) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(failureIds);
    }

    private static SearchTemplateQuery getSearchTemplateQuery(Map<String, String> filterValues,
                                                              List<ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter> globalFilterList
            , ObjectLimitRuleModel.ObjectLimitFilter objectLimitFilter, IObjectDescribe objectDescribe) {
        List<Wheres> globalWheresList = Lists.newArrayList();
        for (ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter globalFilter : globalFilterList) {
            convert2WheresList(globalFilter.getWheres(), globalWheresList);
        }
        convertFilter(globalWheresList, objectDescribe);

        List<IFilter> filters = Lists.newArrayList();
        List<Wheres> wheresList = Lists.newArrayList();
        convert2WheresList(objectLimitFilter.getWheres(), wheresList);
        convertFilter(wheresList, objectDescribe);

        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(100);
        searchTemplateQuery.setFindExplicitTotalNum(true);
        searchTemplateQuery.setNeedReturnCountNum(true);
        searchTemplateQuery.setPermissionType(0);
        for (Map.Entry<String, String> entry : filterValues.entrySet()) {
            SearchUtil.fillFilterEq(filters, entry.getKey(), entry.getValue());
        }
        if (CollectionUtils.notEmpty(wheresList)) {
            String pattern = "";
            Integer index = 1;
            for (IFilter item : filters) {
                pattern = pattern + index.toString() + " AND ";
                index = index + 1;
            }
            pattern = pattern.substring(0, pattern.length() - 4);
            pattern = String.format(" (%s) AND (%s) ", pattern, getPattern(globalWheresList, filters, index));
            index = filters.size() + 1;
            pattern = String.format(" (%s) AND (%s) ", pattern, getPattern(wheresList, filters, index));

            searchTemplateQuery.setPattern(pattern);
        } else {
            searchTemplateQuery.setWheres(globalWheresList);
        }
        searchTemplateQuery.setFilters(filters);
        return searchTemplateQuery;
    }

    private static void convertData(IObjectDescribe objectDescribe, String wheresString, List<IObjectData> objectDataList) {
        if (objectDescribe == null || StringUtils.isBlank(wheresString) || CollectionUtils.empty(objectDataList)) {
            return;
        }
        List<Wheres> wheresList = Lists.newArrayList();
        convert2WheresList(wheresString, wheresList);

        for (Wheres wheres : wheresList) {
            if (CollectionUtils.empty(wheres.getFilters())) {
                continue;
            }
            Set<String> fieldList = wheres.getFilters().stream().map(x -> x.getFieldName()).collect(Collectors.toSet());
            List<IFieldDescribe> fieldDescribes = objectDescribe.getFieldDescribes().stream()
                    .filter(x -> fieldList.contains(x.getApiName())).collect(Collectors.toList());
            if (CollectionUtils.empty(fieldDescribes)) {
                continue;
            }
            for (IFieldDescribe fieldDescribe : fieldDescribes) {
                for (IObjectData objectData : objectDataList) {
                    if (!ObjectDataExt.of(objectData).containsField(fieldDescribe.getApiName())) {
                        objectData.set(fieldDescribe.getApiName(), null);
                        if (fieldDescribe.getType().equals(IFieldType.OBJECT_REFERENCE)) {
                            String referenceNameApi = String.format("%s__r", fieldDescribe.getApiName());
                            objectData.set(referenceNameApi, null);
                        }
                        continue;
                    }
                    switch (fieldDescribe.getType()) {
                        case IFieldType.NUMBER:
                        case IFieldType.CURRENCY:
                            String stringValue = AccountUtil.getStringValue(objectData, fieldDescribe.getApiName(), null);
                            if (StringUtils.isBlank(stringValue)) {
                                objectData.set(fieldDescribe.getApiName(), null);
                            } else {
                                try {
                                    Double doubleValue = new BigDecimal(stringValue).doubleValue();
                                    objectData.set(fieldDescribe.getApiName(), doubleValue);
                                } catch (Exception e) {
                                    log.info("checkObjectLimit error", e);
                                    objectData.set(fieldDescribe.getApiName(), null);
                                }
                            }
                            break;
                        case IFieldType.SELECT_MANY:
                            List<String> listValue = AccountUtil.getListValue(objectData, fieldDescribe.getApiName(), null);
                            ArrayList<String> arrayList = null;
                            if (CollectionUtils.empty(listValue)) {
                                objectData.set(fieldDescribe.getApiName(), null);
                            } else {
                                arrayList = new ArrayList(listValue);
                                Collections.sort(arrayList);
                                String[] strArray = new String[arrayList.size()];
                                for (int i = 0; i < arrayList.size(); i++) {
                                    strArray[i] = arrayList.get(i);
                                }
                                objectData.set(fieldDescribe.getApiName(), strArray);
                            }
                            break;
                        case IFieldType.OBJECT_REFERENCE:
                            String referenceNameApi = String.format("%s__r", fieldDescribe.getApiName());
                            String referenceValue = AccountUtil.getStringValue(objectData, fieldDescribe.getApiName(), null);
                            if (StringUtils.isBlank(referenceValue)) {
                                objectData.set(referenceNameApi, null);
                            } else {
                                IObjectReferenceField referenceField = null;
                                if (fieldDescribe instanceof IObjectReferenceField) {
                                    referenceField = (IObjectReferenceField) fieldDescribe;
                                }
                                if (referenceField == null) {
                                    objectData.set(referenceNameApi, null);
                                    continue;
                                }
                                User user = new User(objectData.getTenantId(), User.SUPPER_ADMIN_USER_ID);
                                //todo 考虑改为批量方法
                                List<IObjectData> referenceObjectDataList = SERVICE_FACADE.findObjectDataByIdsIncludeDeleted(user, Lists.newArrayList(referenceValue), referenceField.getTargetApiName());
                                if (CollectionUtils.empty(referenceObjectDataList)) {
                                    objectData.set(fieldDescribe.getApiName(), null);
                                } else {
                                    objectData.set(referenceNameApi, referenceObjectDataList.get(0).getName());
                                }
                            }
                            break;
                        case IFieldType.EMPLOYEE:
                        case IFieldType.DEPARTMENT:
                            String dataName = null;
                            Object value = objectData.get(fieldDescribe.getApiName());
                            if(value != null) {
                                String strValue = "";
                                if (value instanceof String) {
                                    strValue = (String) value;
                                } else {
                                    strValue = JSON.toJSONString(value);
                                }
                                if(!strValue.startsWith("[") && !strValue.endsWith("]")) {
                                    break;
                                }
                            }

                            if (fieldDescribe.getApiName().equals(SystemConstants.Field.OutOwner.apiName)) {
                                dataName = AccountUtil.getOutOwner(objectData);
//                                User user = new User(objectData.getTenantId(), User.SUPPER_ADMIN_USER_ID);
//                                fillOutUserInfo(objectData, objectDescribe, user);
//                                Object object = JSONObject.parseObject(objectData.get(fieldDescribe.getApiName() +"__r", String.class)).get("name");
//                                if (object != null){
//                                    dataName = object.toString();
//                                }else {
//                                    log.warn("{}__r is null objectId:{}",fieldDescribe.getApiName(),objectData.getId());
//                                }
//                                break;
                            } else {
                                List<String> lstValue = AccountUtil.getListValue(objectData, fieldDescribe.getApiName(), Lists.newArrayList());
                                if (CollectionUtils.notEmpty(lstValue)) {
                                    dataName = lstValue.get(0);
                                }
                            }
                            if (StringUtils.isBlank(dataName)) {
                                objectData.set(fieldDescribe.getApiName(), "");
                            } else {
                                dataName = String.format(",%s,", dataName);
                                objectData.set(fieldDescribe.getApiName(), dataName);
                            }
                            break;
                        case IFieldType.TRUE_OR_FALSE:
                            boolean booleanValue = AccountUtil.getBooleanValue(objectData, fieldDescribe.getApiName(), false);
                            objectData.set(fieldDescribe.getApiName(), String.valueOf(booleanValue));
                            break;
                    }
                }
            }
        }
    }

    private static String getPattern(List<Wheres> wheresList, List<IFilter> filters, Integer startIndex) {
        if (CollectionUtils.empty(wheresList)) {
            return " 1=1 ";
        }
        List<String> patternList = Lists.newArrayList();
        for (Wheres wheres : wheresList) {
            if (CollectionUtils.empty(wheres.getFilters())) {
                continue;
            }
            String pattern = "";
            for (IFilter filter : wheres.getFilters()) {
                pattern = pattern + startIndex.toString() + " AND ";
                startIndex = startIndex + 1;
                filters.add(filter);
            }
            pattern = pattern.substring(0, pattern.length() - 4);
            pattern = String.format(" (%s) ", pattern);
            patternList.add(pattern);
        }
        String pattern = String.join(" OR ", patternList);

        if (StringUtils.isBlank(pattern)) {
            pattern = " 1=1 ";
        }
        return pattern;
    }

    private static List<ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter> getObjectLimitRuleGlobalFilter(String tenantId, String objectApiName) {
        try {
            List<ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter> result = Lists.newArrayList();
            List<WhereParam> whereParams = Lists.newArrayList();
            whereParams.add(getWhereParam("tenant_id", CommonSqlOperator.EQ, Lists.newArrayList(tenantId)));
            whereParams.add(getWhereParam("object_api_name", CommonSqlOperator.IN, Lists.newArrayList(objectApiName)));
            whereParams.add(getWhereParam("is_deleted", CommonSqlOperator.EQ, Lists.newArrayList(0)));

            List<Map> queryResult = queryData(tenantId, OBJECT_LIMIT_GLOBAL_FILTER_TABLE, whereParams);
            if (CollectionUtils.empty(queryResult)) {
//                return getDefaultObjectLimitGlobalFilter(tenantId, objectApiName);
                return result;
            }
            for (Map dataMap : queryResult) {
                ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter globalFilter = ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter.builder()
                        .id(dataMap.get("id").toString())
                        .isDeleted(0)
                        .objectApiName(objectApiName)
                        .wheres(dataMap.getOrDefault("wheres", "[]").toString())
                        .build();
                result.add(globalFilter);
            }
            return result;
        } catch (Exception e) {
            log.error("getObjectLimitRuleGlobalFilter error", e);
            throw new SFABusinessException(SFAErrorCode.ACCOUNT_COMMON_ERROR);
        }
    }

    private static List<ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter> getDefaultObjectLimitGlobalFilter(String tenantId, String objectApiName) {
        List<ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter> result = Lists.newArrayList();
        if (StringUtils.isEmpty(objectApiName)) {
            return result;
        }
        ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter globalFilter = ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter.builder()
                .objectApiName(objectApiName).id(SERVICE_FACADE.generateId()).build();
        List<IFilter> filters = Lists.newArrayList();
        if (SFAPreDefineObject.Leads.getApiName().equals(objectApiName)) {
            SearchUtil.fillFilterNotIn(filters, LeadsConstants.Field.BIZ_STATUS.getApiName(),
                    Lists.newArrayList(LeadsBizStatusEnum.UN_ASSIGNED.getCode(), LeadsBizStatusEnum.TRANSFORMED.getCode(), LeadsBizStatusEnum.CLOSED.getCode()));
            SearchUtil.fillFilterNotIn(filters, SystemConstants.Field.LifeStatus.apiName,
                    Lists.newArrayList(ObjectLifeStatus.INEFFECTIVE.getCode(), ObjectLifeStatus.INVALID.getCode()));
        }
        Wheres wheres = new Wheres();
        wheres.setConnector(Where.CONN.OR.toString());
        wheres.setFilters(filters);
        List<Wheres> wheresList = Lists.newArrayList(wheres);
        globalFilter.setWheres(GsonUtil.GSON.toJson(wheresList));
        result.add(globalFilter);
        return result;
    }

    private static Map<String, List<ObjectLimitRuleModel.ObjectLimitRule>> getObjectLimitRuleByEmployeeIds(String tenantId, String objectApiName, List<String> employeeIds) {
        try {
            Map<String, List<ObjectLimitRuleModel.ObjectLimitRule>> result = Maps.newHashMap();

            List<WhereParam> whereParams = Lists.newArrayList();
            whereParams.add(getWhereParam("tenant_id", CommonSqlOperator.EQ, Lists.newArrayList(tenantId)));
            whereParams.add(getWhereParam("object_api_name", CommonSqlOperator.EQ, Lists.newArrayList(objectApiName)));
            whereParams.add(getWhereParam("employee_id", CommonSqlOperator.IN, Lists.newArrayList(employeeIds)));
            whereParams.add(getWhereParam("is_deleted", CommonSqlOperator.EQ, Lists.newArrayList(0)));

            List<Map> queryResult = queryData(tenantId, OBJECT_LIMIT_EMPLOYEE_RULE_TABLE, whereParams);
            if (CollectionUtils.empty(queryResult)) {
                return result;
            }
            List<ObjectLimitRuleModel.ObjectLimitEmployeeRule> employeeRuleList = Lists.newArrayList();
            convert2ObjectLimitEmployeeRule(employeeRuleList, queryResult);
            Set<String> groupIds = employeeRuleList.stream().map(x -> x.getGroupId()).collect(Collectors.toSet());
            List<ObjectLimitRuleModel.ObjectLimitRule> objectLimitRuleList = getObjectLimitRuleByGroupIds(tenantId, objectApiName, Lists.newArrayList(groupIds));
            for (String employeeId : employeeIds) {
                if (result.containsKey(employeeId)) {
                    continue;
                }
                List<ObjectLimitRuleModel.ObjectLimitRule> employeeLimitRuleList = Lists.newArrayList();
                result.put(employeeId, employeeLimitRuleList);
                Set<String> employeeGroupIds = employeeRuleList.stream()
                        .filter(x -> employeeId.equals(x.getEmployeeId()))
                        .map(x -> x.getGroupId()).collect(Collectors.toSet());

                if (CollectionUtils.notEmpty(employeeGroupIds)) {
                    Optional<ObjectLimitRuleModel.ObjectLimitRule> objectLimitRuleOptional = objectLimitRuleList.stream()
                            .filter(x -> employeeGroupIds.contains(x.getGroupId())).findFirst();
                    if (objectLimitRuleOptional.isPresent()) {
                        employeeLimitRuleList.add(objectLimitRuleOptional.get());
                    }
                }
            }

            return result;
        } catch (Exception e) {
            log.error("getObjectLimitRuleGlobalFilter error", e);
            throw new SFABusinessException(SFAErrorCode.ACCOUNT_COMMON_ERROR);
        }
    }

    public static List<ObjectLimitRuleModel.ObjectLimitRule> getObjectLimitRuleByEmployeeId(String tenantId, String objectApiName, String employeeId) {
        try {
            List<ObjectLimitRuleModel.ObjectLimitRule> result = Lists.newArrayList();

            List<WhereParam> whereParams = Lists.newArrayList();
            whereParams.add(getWhereParam("tenant_id", CommonSqlOperator.EQ, Lists.newArrayList(tenantId)));
            whereParams.add(getWhereParam("object_api_name", CommonSqlOperator.EQ, Lists.newArrayList(objectApiName)));
            whereParams.add(getWhereParam("employee_id", CommonSqlOperator.EQ, Lists.newArrayList(employeeId)));
            whereParams.add(getWhereParam("is_deleted", CommonSqlOperator.EQ, Lists.newArrayList(0)));

            List<Map> queryResult = queryData(tenantId, OBJECT_LIMIT_EMPLOYEE_RULE_TABLE, whereParams);
            if (CollectionUtils.empty(queryResult)) {
                return result;
            }
            List<ObjectLimitRuleModel.ObjectLimitEmployeeRule> employeeRuleList = Lists.newArrayList();
            convert2ObjectLimitEmployeeRule(employeeRuleList, queryResult);
            Set<String> groupIds = employeeRuleList.stream().map(x -> x.getGroupId()).collect(Collectors.toSet());
            Optional<ObjectLimitRuleModel.ObjectLimitEmployeeRule> defaultEmployeeRule = employeeRuleList.stream()
                    .filter(x -> x.isDefaultRule()).findFirst();

            String defaultGroupId = "";
            if (defaultEmployeeRule.isPresent()) {
                defaultGroupId = defaultEmployeeRule.get().getGroupId();
            }

            List<ObjectLimitRuleModel.ObjectLimitRule> allObjectLimitRuleList = getObjectLimitRuleByGroupIds(tenantId, objectApiName, Lists.newArrayList(groupIds));
            for (String groupId : groupIds) {
                Optional<ObjectLimitRuleModel.ObjectLimitRule> objectLimitRuleOptional = allObjectLimitRuleList.stream()
                        .filter(x -> groupId.equals(x.getGroupId())).findFirst();
                if (objectLimitRuleOptional.isPresent()) {
                    if (defaultGroupId.equals(groupId)) {
                        objectLimitRuleOptional.get().setDefaultRule(true);
                    }
                    result.add(objectLimitRuleOptional.get());
                }
            }
            return result;
        } catch (Exception e) {
            log.error("getObjectLimitRuleGlobalFilter error", e);
            throw new SFABusinessException(SFAErrorCode.ACCOUNT_COMMON_ERROR);
        }
    }

    public static List<ObjectLimitRuleModel.ObjectLimitRule> getObjectLimitRuleByGroupIds(String tenantId, String objectApiName, List<String> groupIds) {
        List<ObjectLimitRuleModel.ObjectLimitRule> result = Lists.newArrayList();
        if (CollectionUtils.empty(groupIds)) {
            return result;
        }
        try {
            List<WhereParam> whereParams = Lists.newArrayList();
            whereParams.add(getWhereParam("tenant_id", CommonSqlOperator.EQ, Lists.newArrayList(tenantId)));
            whereParams.add(getWhereParam("object_api_name", CommonSqlOperator.EQ, Lists.newArrayList(objectApiName)));
            whereParams.add(getWhereParam("is_deleted", CommonSqlOperator.EQ, Lists.newArrayList(0)));
            whereParams.add(getWhereParam("group_id", CommonSqlOperator.IN, Lists.newArrayList(groupIds)));

            List<Map> queryResult = queryData(tenantId, OBJECT_LIMIT_RULE_TABLE, whereParams);

            if (CollectionUtils.empty(queryResult)) {
                return result;
            }
            convert2ObjectLimitRule(result, queryResult);

            List<ObjectLimitRuleModel.ObjectLimitFilter> filterList = getObjectLimitFilterByGroupIds(tenantId, objectApiName, groupIds);

            for (ObjectLimitRuleModel.ObjectLimitRule objectLimitRule : result) {
                List<ObjectLimitRuleModel.ObjectLimitFilter> ruleFilterList = filterList.stream()
                        .filter(x -> x.getGroupId().equals(objectLimitRule.getGroupId())).collect(Collectors.toList());
                if (CollectionUtils.empty(ruleFilterList)) {
                    ruleFilterList = Lists.newArrayList();
                }
                objectLimitRule.setObjectLimitFilterList(ruleFilterList);
            }
            return result;
        } catch (Exception e) {
            log.error("getObjectLimitRuleByGroupIds error", e);
            throw new RuntimeException(e);
        }
    }

    public static List<ObjectLimitRuleModel.ObjectLimitFilter> getObjectLimitFilterByGroupIds(String tenantId, String objectApiName, List<String> groupIds) {
        List<ObjectLimitRuleModel.ObjectLimitFilter> result = Lists.newArrayList();
        if (CollectionUtils.empty(groupIds)) {
            return result;
        }
        try {
            List<WhereParam> whereParams = Lists.newArrayList();
            whereParams.add(getWhereParam("tenant_id", CommonSqlOperator.EQ, Lists.newArrayList(tenantId)));
            whereParams.add(getWhereParam("object_api_name", CommonSqlOperator.EQ, Lists.newArrayList(objectApiName)));
            whereParams.add(getWhereParam("is_deleted", CommonSqlOperator.EQ, Lists.newArrayList(0)));
            whereParams.add(getWhereParam("group_id", CommonSqlOperator.IN, Lists.newArrayList(groupIds)));

            List<Map> queryResult = queryData(tenantId, OBJECT_LIMIT_FILTER_TABLE, whereParams);

            if (CollectionUtils.empty(queryResult)) {
                return result;
            }
            convert2ObjectLimitFilter(result, queryResult);
            return result;
        } catch (Exception e) {
            log.error("getObjectLimitFilterByGroupIds error", e);
            throw new RuntimeException(e);
        }
    }

    public static ObjectLimitRuleModel.ObjectLimitFilter getObjectLimitFilterById(String tenantId, String objectApiName, String dataId) {
        if(StringUtils.isBlank(dataId)){
            return null;
        }
        try{
            List<WhereParam> whereParams = Lists.newArrayList();
            whereParams.add(getWhereParam("tenant_id", CommonSqlOperator.EQ, Lists.newArrayList(tenantId)));
            whereParams.add(getWhereParam("object_api_name", CommonSqlOperator.EQ, Lists.newArrayList(objectApiName)));
            whereParams.add(getWhereParam("is_deleted", CommonSqlOperator.EQ, Lists.newArrayList(0)));
            whereParams.add(getWhereParam("id", CommonSqlOperator.EQ, Lists.newArrayList(dataId)));

            List<Map> queryResult = queryData(tenantId, OBJECT_LIMIT_FILTER_TABLE, whereParams);

            if(CollectionUtils.empty(queryResult)){
                return null;
            }
            List<ObjectLimitRuleModel.ObjectLimitFilter> tempList = Lists.newArrayList();
            convert2ObjectLimitFilter(tempList, queryResult);
            if(CollectionUtils.notEmpty(tempList)) {
                return tempList.get(0);
            }
            return null;
        } catch (Exception e){
            log.error("getObjectLimitFilterByGroupIds error", e);
            throw new RuntimeException(e);
        }
    }

    public static ObjectLimitRuleModel.ObjectLimitOverRule getObjectLimitOverRule(String tenantId, String objectApiName) {
        ObjectLimitRuleModel.ObjectLimitOverRule result = ObjectLimitRuleModel.ObjectLimitOverRule.builder()
                .objectApiName(objectApiName).build();

        try {
            List<WhereParam> whereParams = Lists.newArrayList();
            whereParams.add(getWhereParam("tenant_id", CommonSqlOperator.EQ, Lists.newArrayList(tenantId)));
            whereParams.add(getWhereParam("object_api_name", CommonSqlOperator.EQ, Lists.newArrayList(objectApiName)));
            whereParams.add(getWhereParam("is_deleted", CommonSqlOperator.EQ, Lists.newArrayList(0)));

            List<Map> queryResult = queryData(tenantId, OBJECT_LIMIT_OVER_RULE_TABLE, whereParams);

            if (CollectionUtils.empty(queryResult)) {
                return result;
            }
            Map<String, Object> overRuleMapData = queryResult.get(0);
            result.setId(getStringValue(overRuleMapData, "id", ""));
            result.setObjectPoolId(getStringValue(overRuleMapData, "object_pool_id", ""));
            return result;
        } catch (Exception e) {
            log.error("getObjectLimitOverRule error", e);
            throw new RuntimeException(e);
        }
    }

    private static WhereParam getWhereParam(String columnName, CommonSqlOperator operator, List<Object> values) {
        WhereParam whereParam = new WhereParam();
        whereParam.setColumn(columnName);
        whereParam.setOperator(operator);
        whereParam.setValue(values);
        return whereParam;
    }

    private static WhereParam getNullWhereParam(String columnName) {
        WhereParam whereParam = new WhereParam();
        whereParam.setColumn(columnName);
        whereParam.setOperator(CommonSqlOperator.IS);
        whereParam.setValue(null);
        return whereParam;
    }

    private static WhereParam getNotNullWhereParam(String columnName) {
        WhereParam whereParam = new WhereParam();
        whereParam.setColumn(columnName);
        whereParam.setOperator(CommonSqlOperator.ISN);
        whereParam.setValue(null);
        return whereParam;
    }

    private static List<Map> queryData(String tenantId, String tableName, List<WhereParam> whereParams) throws MetadataServiceException {
        ActionContext actionContext = getActionContext(tenantId);
        return commonSqlService.select(tableName, whereParams, actionContext);
    }

    private static ActionContext getActionContext(String tenantId) {
        ActionContext actionContext = new ActionContext();
        actionContext.setEnterpriseId(tenantId);
        actionContext.setUserId(User.SUPPER_ADMIN_USER_ID);
        actionContext.setDbType("pg");
        actionContext.setAllowUpdateInvalid(true);
        actionContext.put("not_validate", true);
        actionContext.setPrivilegeCheck(false);
        return actionContext;
    }

    private static String getStringValue(Map<String, Object> dataMap, String key, String defaultValue) {
        if (dataMap.containsKey(key)) {
            Object objectValue = dataMap.get(key);
            if (objectValue == null) {
                return defaultValue;
            }
            return objectValue.toString();
        } else {
            return defaultValue;
        }
    }

    private static Long getLongValue(Map<String, Object> dataMap, String key, Long defaultValue) {
        String longStringValue = getStringValue(dataMap, key, String.valueOf(defaultValue));
        try {
            Long result = Long.valueOf(longStringValue);
            return result;
        } catch (Exception e) {
            return defaultValue;
        }
    }

    private static Integer getIntegerValue(Map<String, Object> dataMap, String key, Integer defaultValue) {
        String integerStringValue = getStringValue(dataMap, key, String.valueOf(defaultValue));
        try {
            Integer result = Integer.valueOf(integerStringValue);
            return result;
        } catch (Exception e) {
            return defaultValue;
        }
    }

    private static boolean getBooleanValue(Map<String, Object> dataMap, String key, boolean defaultValue) {
        if (dataMap.containsKey(key)) {
            Object objectValue = dataMap.get(key);
            if (objectValue == null) {
                return defaultValue;
            }
            try {
                return Boolean.valueOf(objectValue.toString());
            } catch (Exception e) {
                return defaultValue;
            }
        } else {
            return defaultValue;
        }
    }

    private static void convertFilter(List<Wheres> wheresList, IObjectDescribe objectDescribe) {
        if (CollectionUtils.empty(wheresList) || objectDescribe == null) {
            return;
        }

        for (Wheres wheres : wheresList) {
            if (CollectionUtils.empty(wheres.getFilters())) {
                continue;
            }
            for (IFilter filter : wheres.getFilters()) {
                IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(filter.getFieldName());
                if (fieldDescribe == null) {
                    continue;
                }
                if (IFieldType.OBJECT_REFERENCE.equals(fieldDescribe.getType())) {
                    filter.setFieldName(String.format("%s.name", filter.getFieldName()));
                } else if (IFieldType.SELECT_MANY.equals(fieldDescribe.getType())) {
                    if (Operator.IN.equals(filter.getOperator())) {
                        filter.setOperator(Operator.HASANYOF);
                    } else if (Operator.NIN.equals(filter.getOperator())) {
                        filter.setOperator(Operator.NHASANYOF);
                    }
                }
            }
        }

    }

    private static void convert2ObjectLimitRule(List<ObjectLimitRuleModel.ObjectLimitRule> objectLimitRuleList, List<Map> dataMapList) {
        for (Map<String, Object> data : dataMapList) {
            ObjectLimitRuleModel.ObjectLimitRule objectLimitRule = ObjectLimitRuleModel.ObjectLimitRule.builder()
                    .objectApiName(getStringValue(data, "object_api_name", ""))
                    .dataId(getStringValue(data, "data_id", ""))
                    .dataType(getStringValue(data, "data_type", ""))
                    .dataWheres(getStringValue(data, "data_wheres", ""))
                    .name(getStringValue(data, "name", ""))
                    .groupId(getStringValue(data, "group_id", ""))
                    .id(getStringValue(data, "id", ""))
                    .calculateId(getStringValue(data, "calculate_id", ""))
                    .isDeleted(getIntegerValue(data, "is_deleted", 0))
                    .defaultRule(false)
                    .ruleType(getStringValue(data, "rule_type", ObjectLimitRuleModel.RuleTypeEnum.EMPLOYEE.getCode()))
                    .createBy(getStringValue(data, "created_by", ""))
                    .build();
            objectLimitRuleList.add(objectLimitRule);
        }
    }

    private static void convert2ObjectLimitFilter(List<ObjectLimitRuleModel.ObjectLimitFilter> objectLimitRuleList, List<Map> dataMapList) {
        for (Map<String, Object> data : dataMapList) {
            ObjectLimitRuleModel.ObjectLimitFilter objectLimitFilter = ObjectLimitRuleModel.ObjectLimitFilter.builder()
                    .objectApiName(getStringValue(data, "object_api_name", ""))
                    .wheres(getStringValue(data, "wheres", ""))
                    .groupId(getStringValue(data, "group_id", ""))
                    .id(getStringValue(data, "id", ""))
                    .calculateId(getStringValue(data, "calculate_id", ""))
                    .isDeleted(getIntegerValue(data, "is_deleted", 0))
                    .limitNumber(getIntegerValue(data, "limit_number", 0))
                    .createTime(getLongValue(data, "create_time", 0L))
                    .build();
            objectLimitRuleList.add(objectLimitFilter);
        }
    }

    private static void convert2ObjectLimitEmployeeRule(List<ObjectLimitRuleModel.ObjectLimitEmployeeRule> objectLimitRuleList, List<Map> dataMapList) {
        for (Map<String, Object> data : dataMapList) {
            ObjectLimitRuleModel.ObjectLimitEmployeeRule objectLimitEmployeeRule = ObjectLimitRuleModel.ObjectLimitEmployeeRule.builder()
                    .objectApiName(getStringValue(data, "object_api_name", ""))
                    .groupId(getStringValue(data, "group_id", ""))
                    .id(getStringValue(data, "id", ""))
                    .calculateId(getStringValue(data, "calculate_id", ""))
                    .isDeleted(getIntegerValue(data, "is_deleted", 0))
                    .employeeId(getStringValue(data, "employee_id", ""))
                    .defaultRule(getBooleanValue(data, "is_default", false))
                    .build();
            objectLimitRuleList.add(objectLimitEmployeeRule);
        }
    }

    private static void convert2WheresList(String wheresString, List<Wheres> wheresList) {
        if (StringUtils.isEmpty(wheresString)) {
            return;
        }
        List<JSONObject> wheresJSONObjectList = JSON.parseObject(wheresString, List.class);
        if (CollectionUtils.notEmpty(wheresJSONObjectList)) {
            for (JSONObject jsonObject : wheresJSONObjectList) {
                Wheres wheres = JSON.parseObject(jsonObject.toJSONString(), Wheres.class);
                if (CollectionUtils.notEmpty(wheres.getFilters())) {
                    for (IFilter filter : wheres.getFilters()) {
                        if (filter.getFieldValues() == null) {
                            filter.setFieldValues(Lists.newArrayList());
                        }
                    }
                }
                wheresList.add(wheres);
            }
        }
    }

    public static class Comparators {
        //根据保有量进行排序
        public static Comparator<ObjectLimitRuleModel.ObjectLimitFilter> LIMIT_NUMBER = new Comparator<ObjectLimitRuleModel.ObjectLimitFilter>() {
            @Override
            public int compare(ObjectLimitRuleModel.ObjectLimitFilter o1, ObjectLimitRuleModel.ObjectLimitFilter o2) {
                return o1.getLimitNumber() - o2.getLimitNumber();
            }
        };
    }

    public static boolean isGrayLeadsLimit(String tenantId) {
        if (!gray.isAllow("leads_limit", tenantId)) {
            return false;
        }
        return true;
    }

    public static boolean isGrayAccountLimit(String tenantId) {
        if (!gray.isAllow("account_limit", tenantId)) {
            return false;
        }
        return true;
    }

    public static boolean isFromOverRuleApp(IObjectData objectData) {
        String objectCreateFrom = AccountUtil.getStringValue(objectData, "ObjectCreateFrom", "");
        if ("marketing".equals(objectCreateFrom)) {
            return true;
        }
        return false;
    }

    public static Integer getObjectCount(User user, String objectApiName, String owner, ObjectLimitRuleModel.ObjectLimitFilter objectLimitFilter) {
        IObjectDescribe objectDescribe = SERVICE_FACADE.findObject(user.getTenantId(), objectApiName);
        List<ObjectLimitRuleModel.ObjectLimitRuleGlobalFilter> globalFilterList = getObjectLimitRuleGlobalFilter(user.getTenantId(), objectApiName);

        Map<String, String> filterValues = Maps.newHashMap();
        filterValues.put(SystemConstants.Field.Owner.apiName, owner);
        SearchTemplateQuery searchTemplateQuery = getSearchTemplateQuery(filterValues, globalFilterList, objectLimitFilter, objectDescribe);
        QueryResult<IObjectData> queryResult = SERVICE_FACADE.findBySearchQuery(user, objectApiName, searchTemplateQuery);

        if (queryResult == null) {
            return 0;
        }
        return queryResult.getTotalNumber();
    }

    public static List<ObjectLimitRuleModel.AccountLimitRule> getAllAccountLimitRule(User user) {
        try{
            String tenantId = user.getTenantId();
            List<WhereParam> whereParamList = Lists.newArrayList();
            CommonSqlUtils.addWhereParam(whereParamList, ObjectFieldConstantsUtil.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(tenantId));
            CommonSqlUtils.addWhereParam(whereParamList, ObjectFieldConstantsUtil.IS_DELETED, CommonSqlOperator.EQ, Lists.newArrayList(0));
            List<Map> queryResult = CommonSqlUtils.queryData(tenantId, ACCOUNT_LIMIT_RULE_TABLE, whereParamList);
            List<ObjectLimitRuleModel.AccountLimitRule> result = convert2AccountLimitRule(queryResult);
            return result;
        } catch (Exception e) {
            log.error("getAllAccountLimitRule error", e);
            throw new SFABusinessException(SFAErrorCode.ACCOUNT_COMMON_ERROR);
        }
    }

    @NotNull
    private static List<ObjectLimitRuleModel.AccountLimitRule> convert2AccountLimitRule(List<Map> dbAccountLimitRule) {
        List<ObjectLimitRuleModel.AccountLimitRule> result = Lists.newArrayList();
        if(CollectionUtils.empty(dbAccountLimitRule)) {
            return result;
        }
        for(Map<String, Object> data : dbAccountLimitRule) {
            ObjectLimitRuleModel.AccountLimitRule limitRule = ObjectLimitRuleModel.AccountLimitRule.builder()
                    .groupId(ObjectUtil.getStringValue(data, "group_id", ""))
                    .name(ObjectUtil.getStringValue(data, ObjectFieldConstantsUtil.NAME, ""))
                    .limitNumber(ObjectUtil.getIntegerValue(data, "limit_number", 0))
                    .includeHighSeasCustomer(ObjectUtil.getBooleanValue(data, "include_high_seas_customer", false))
                    .includeDealCustomer(ObjectUtil.getBooleanValue(data, "include_deal_customer", false))
                    .dataId(ObjectUtil.getStringValue(data, "data_id", ""))
                    .dataType(ObjectUtil.getStringValue(data, "data_type", ""))
                    .id(ObjectUtil.getStringValue(data, ObjectFieldConstantsUtil.ID, ""))
                    .createTime(ObjectUtil.getLongValue(data, "create_time", 0L))
                    .build();
            result.add(limitRule);
        }
        return result;
    }

    public static List<ObjectLimitRuleModel.AccountLimitEmployeeRule> getAccountLimitEmployeeRule(User user, List<String> employeeIds) {
        try{
            String tenantId = user.getTenantId();
            List<WhereParam> whereParamList = Lists.newArrayList();
            CommonSqlUtils.addWhereParam(whereParamList, ObjectFieldConstantsUtil.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(tenantId));
            CommonSqlUtils.addWhereParam(whereParamList, "employee_id", CommonSqlOperator.IN, Lists.newArrayList(employeeIds));
            CommonSqlUtils.addWhereParam(whereParamList, "rule_type", CommonSqlOperator.EQ, Lists.newArrayList("2"));
            List<Map> queryResult = CommonSqlUtils.queryData(tenantId, EMPLOYEE_RULE_TABLE, whereParamList);
            List<ObjectLimitRuleModel.AccountLimitEmployeeRule> result = convert2AccountLimitEmployeeRule(queryResult);
            return result;
        } catch (Exception e) {
            log.error("getAccountLimitEmployeeRule error", e);
            throw new SFABusinessException(SFAErrorCode.ACCOUNT_COMMON_ERROR);
        }
    }

    @NotNull
    private static List<ObjectLimitRuleModel.AccountLimitEmployeeRule> convert2AccountLimitEmployeeRule(List<Map> dbAccountLimitEmployeeRule) {
        List<ObjectLimitRuleModel.AccountLimitEmployeeRule> result = Lists.newArrayList();
        if(CollectionUtils.empty(dbAccountLimitEmployeeRule)) {
            return result;
        }
        for(Map<String, Object> data : dbAccountLimitEmployeeRule) {
            ObjectLimitRuleModel.AccountLimitEmployeeRule employeeRule = ObjectLimitRuleModel.AccountLimitEmployeeRule
                    .builder()
                    .dataId(ObjectUtil.getStringValue(data, "data_id", ""))
                    .dataType(ObjectUtil.getStringValue(data, "data_type", ""))
                    .employeeId(ObjectUtil.getStringValue(data, "employee_id", ""))
                    .build();
            result.add(employeeRule);
        }
        return result;
    }

    @Nullable
    public static ObjectLimitRuleModel.AccountLimitRule getAccountLimitRuleByEmployeeId (User user, String employeeId) {
        List<ObjectLimitRuleModel.AccountLimitRule> accountLimitRuleList = ObjectLimitUtil.getAllAccountLimitRule(user);
        if(CollectionUtils.empty(accountLimitRuleList)) {
            return null;
        }
        Optional<ObjectLimitRuleModel.AccountLimitRule> limitRuleOptional = accountLimitRuleList.stream()
                .filter(x -> String.valueOf(employeeId).equals(x.getDataId()) && "2".equals(x.getDataType()))
                .findFirst();
        if(limitRuleOptional.isPresent()) {
            return limitRuleOptional.get();
        }

        List<ObjectLimitRuleModel.AccountLimitEmployeeRule> employeeRuleList = ObjectLimitUtil.getAccountLimitEmployeeRule(user, Lists.newArrayList(String.valueOf(employeeId)));
        if(CollectionUtils.notEmpty(employeeRuleList)) {
            ObjectLimitRuleModel.AccountLimitEmployeeRule employeeRule = employeeRuleList.get(0);
            limitRuleOptional =  accountLimitRuleList.stream()
                    .filter(x -> employeeRule.getDataId().equals(x.getDataId()) && employeeRule.getDataType().equals(x.getDataType()))
                    .findFirst();
        }

        if(limitRuleOptional.isPresent()) {
            return limitRuleOptional.get();
        }

        List<String> departmentIds = CommonBizOrgUtils.getDepartmentByUserId(user.getTenantId(), employeeId);
        if(CollectionUtils.notEmpty(departmentIds)) {
            List<ObjectLimitRuleModel.AccountLimitRule> tempList = accountLimitRuleList.stream()
                    .filter(x -> departmentIds.contains(x.getDataId()) && "1".equals(x.getDataType()))
                    .collect(Collectors.toList());

            if(CollectionUtils.notEmpty(tempList)) {
                tempList.sort((o1, o2) -> o2.getCreateTime().compareTo(o1.getCreateTime()));
                return tempList.get(0);
            }
        }
        List<String> userGroupIds = CommonBizOrgUtils.getUserGroupIdsByMemberId(user.getTenantId(), employeeId);
        if(CollectionUtils.notEmpty(userGroupIds)) {
            List<ObjectLimitRuleModel.AccountLimitRule> tempList = accountLimitRuleList.stream()
                    .filter(x -> userGroupIds.contains(x.getDataId()) && "3".equals(x.getDataType()))
                    .collect(Collectors.toList());

            if(CollectionUtils.notEmpty(tempList)) {
                tempList.sort((o1, o2) -> o2.getCreateTime().compareTo(o1.getCreateTime()));
                return tempList.get(0);
            }
        }

        return null;
    }

    @Data
    @Builder
    public static class CheckLimitResult {
        private List<String> successIds;
        private List<String> failureIds;
    }
}
