package com.facishare.crm.sfa.predefine.bizvalidator.validator;

import com.facishare.crm.sfa.predefine.bizvalidator.Validator;
import com.facishare.crm.sfa.predefine.bizvalidator.ValidatorContext;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.utilities.constant.BomConstants;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;

/**
 * 移动端不支持相关功能校验器
 *
 * <AUTHOR>
 */
public class MobileUnSupportValidator extends BaseValidator implements Validator {
    BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);

    @Override
    public void validate(ValidatorContext context) {

        if (bizConfigThreadLocalCacheService.isCPQEnabled(context.getUser().getTenantId())) {
            List<IObjectData> detailDataList = context.getDetailObjectData()
                    .getOrDefault(MASTER_DETAIL_API_NAME.get(context.getDescribeApiName()), Lists.newArrayList());
            if (CollectionUtils.notEmpty(detailDataList)) {
                if (detailDataList.stream().anyMatch(x -> !Objects.isNull(x.get(BomConstants.FIELD_BOM_ID)))) {
                    throw new ValidateException(I18N.text("sfa.cqp.mobile.edit.un_support"));
                }
            }
        }
    }
}
