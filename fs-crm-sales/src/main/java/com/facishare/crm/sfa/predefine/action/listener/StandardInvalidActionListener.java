package com.facishare.crm.sfa.predefine.action.listener;


import com.facishare.crm.sfa.utilities.proxy.model.salesorder.salesorderparam.SalesOrderInvalidAfterModel;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.salesorderparam.SalesOrderInvalidBeforeModel;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ActionListener;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2019/8/7 16:58
 */
public class StandardInvalidActionListener implements ActionListener<StandardInvalidAction.Arg, StandardInvalidAction.Result> {

    @Autowired
    ServiceFacade serviceFacade;

    @Override
    public void before(ActionContext actionContext, StandardInvalidAction.Arg arg) {
        String id = arg.getObjectDataId();
        if (!StringUtils.isEmpty(id)) {
            List<IObjectData> objectDataList = serviceFacade.findObjectDataByIdsIncludeDeleted(actionContext.getUser(),
                    Lists.newArrayList(id), actionContext.getObjectApiName());
            if (CollectionUtils.notEmpty(objectDataList)) {
                SalesOrderInvalidBeforeModel.Arg serivceArg = new SalesOrderInvalidBeforeModel.Arg();
                Optional<IObjectData> optionData = objectDataList.stream().findFirst();
                if (optionData.isPresent()) {
                    IObjectData data = optionData.get();
                    ObjectDataExt objectDataExt = ObjectDataExt.of(data);

                    String dataId = objectDataExt.getId();
                    String lifeStatus = objectDataExt.getLifeStatus().getCode();
                    serivceArg.setDataId(dataId);
                    serivceArg.setNowLifeStatus(lifeStatus);
                    callBeforeInterceptor(serivceArg);
                }
            }
        }
    }

    @Override
    public void after(ActionContext actionContext, StandardInvalidAction.Arg arg, StandardInvalidAction.Result result) {
        SalesOrderInvalidAfterModel.Arg serviceArg = new SalesOrderInvalidAfterModel.Arg();
        callAfterInterceptor(serviceArg);
    }


    protected void callBeforeInterceptor(SalesOrderInvalidBeforeModel.Arg arg) {
    }

    protected void callAfterInterceptor(SalesOrderInvalidAfterModel.Arg arg) {
    }

}
