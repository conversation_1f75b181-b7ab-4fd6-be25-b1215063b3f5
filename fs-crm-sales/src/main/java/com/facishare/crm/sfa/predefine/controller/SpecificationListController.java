package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/11/26 16:44
 */
@SuppressWarnings("Duplicates")
public class SpecificationListController extends StandardListController {

    @Override
    protected List<ILayout> findMobileLayouts() {
        List<ILayout> mobileLayouts = super.findMobileLayouts();
        for (ILayout layout : mobileLayouts) {
            layout.set("buttons", Lists.newArrayList());
        }
        return mobileLayouts;
    }
}
