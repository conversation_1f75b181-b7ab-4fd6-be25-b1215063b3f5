package com.facishare.crm.sfa.predefine.button;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.metadata.ui.layout.IButton;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class InvoiceApplicationSpecialButtonProvider extends AbstractSfaSpecialButtonProvider {
    @Override
    public String getApiName() {
        return Utils.INVOICE_APPLICATION_API_NAME;
    }

    @Override
    public List<IButton> getSpecialButtons() {
        List<IButton> buttons = super.getSpecialButtons();
        return buttons;
    }
}

