package com.facishare.crm.sfa.predefine.action;//package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.util.imports.SoImportService;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportDataAction;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Set;


/**
 * <AUTHOR>
 * @date 2018/11/9 16:12
 * @instruction
 * @IgnoreI18nFile
 */
@Slf4j
public class ProductGroupInsertImportDataAction extends StandardInsertImportDataAction {


    private SoImportService soImportService = SpringUtil.getContext().getBean(SoImportService.class);
    private Set<String> productNames = Sets.newHashSet();

    @Override
    protected void doFunPrivilegeCheck() {
        return;
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
    }

    @Override
    protected void convertFields(List<ImportData> dataList) {
        objectDescribe.removeFieldDescribe("record_type");
        super.convertFields(dataList);
    }

    @Override
    protected void customValidate(List<ImportData> dataList) {
        List<ImportError> errors = Lists.newArrayList();
        Map<String, Set<String>> parentProductSet = Maps.newHashMap();
        super.customValidate(dataList);
        dataList.stream().forEach(d->productNames.add(d.getData().get("parent_product_id",String.class)));
        Map<Object, List<String>> productCatalogData = soImportService.getProductGroupDataByName(actionContext.getUser(), productNames);
        Map<String, Permissions> checkEditDataPrivilege = soImportService.checkEditDataPrivilege(actionContext.getTenantId(),
                actionContext.getUser(),Utils.PRODUCT_API_NAME,productNames);
        dataList.forEach(x->{
            IObjectData data = x.getData();
            String name = data.getName();
            String parentProductId = data.get("parent_product_id",String.class);
            Integer maxProdCount = data.get("max_prod_count", Integer.class);
            Integer minProdCount = data.get("min_prod_count", Integer.class);
            Boolean groupControl = data.get("group_options_control", Boolean.class);


            // 如果是单选 最大子产品数量必须是1  最小子产品数量是0或1
            if(groupControl){

                if(minProdCount == null || minProdCount > 1){
                    errors.add(new ImportError(x.getRowNo(),"单选对应的最少子产品个数是0或1。"));
                }
                if(maxProdCount == null || maxProdCount != 1){
                    errors.add(new ImportError(x.getRowNo(),"单选对应的最大子产品个数必须是1。"));
                }
            }else {
                if (minProdCount != null && maxProdCount != null) {
                    if (minProdCount > maxProdCount) {
                        errors.add(new ImportError(x.getRowNo(), "最小子产品个数必须小于等于最大子产品个数。"));
                    }
                }
            }

            if(checkEditDataPrivilege.get(parentProductId) != Permissions.READ_WRITE){
                errors.add(new ImportError(x.getRowNo(),"对引用的产品没有编辑权限"));
            }
            if(productCatalogData.getOrDefault(parentProductId, Lists.newArrayList()).contains(name)){
                // 数据库重复
                errors.add(new ImportError(x.getRowNo(),name + ", 分组已经存在"));
            }else{
                if(parentProductSet.keySet().contains(parentProductId)){
                    Set<String> names = parentProductSet.get(parentProductId);
                    if(names.contains(name)){
                        errors.add(new ImportError(x.getRowNo(),name + ", 分组名称重复"));
                    }else {
                        names.add(name);
                        parentProductSet.put(parentProductId,names);
                    }
                }else{
                    parentProductSet.put(parentProductId, Sets.newHashSet(name));
                }
            }
        });
        mergeErrorList(errors);
    }

    @Override
    protected List<IObjectData> importData(List<IObjectData> validList) {
        List<String> parentProductIds = Lists.newArrayList();
        validList.forEach(o -> {
            o.setRecordType("default__c");
            parentProductIds.add(o.get("parent_product_id",String.class));
        });
        List<IObjectData> subProductCatalogData = serviceFacade.bulkSaveObjectData(validList, actionContext.getUser(), false, false);
        List<IObjectData> productData = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), parentProductIds, Utils.PRODUCT_API_NAME);
        productData.forEach(x->x.set("is_package",true));
        serviceFacade.batchUpdateByFields(actionContext.getUser(),productData,Lists.newArrayList("is_package"));
        return subProductCatalogData;
    }
}
