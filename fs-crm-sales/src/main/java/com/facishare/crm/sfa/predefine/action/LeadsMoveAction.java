package com.facishare.crm.sfa.predefine.action;

import com.facishare.crmcommon.constants.SystemConstants;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.model.Enum.LeadsBizStatusEnum;
import com.facishare.crm.sfa.model.Enum.SessionBOCItemKeys;
import com.facishare.crm.sfa.model.SFAObjectPoolCommon;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.LeadsPoolServiceImpl;
import com.facishare.crm.sfa.predefine.service.QiXinTodoService;
import com.facishare.crm.sfa.predefine.service.model.SFALogModels;
import com.facishare.crm.sfa.predefine.service.task.LeadsAllocateOverTimeTaskService;
import com.facishare.crm.sfa.predefine.service.task.LeadsAllocateTaskService;
import com.facishare.crm.sfa.predefine.service.task.LeadsOverTimeTaskService;
import com.facishare.crm.sfa.predefine.service.task.RecalculateTaskService;
import com.facishare.crm.sfa.utilities.constant.LeadsConstants;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.crm.sfa.utilities.util.JsonUtil;
import com.facishare.crm.sfa.utilities.util.LeadsUtils;
import com.facishare.crm.sfa.utilities.util.ObjectLimitUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppIdMapping;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.prm.util.PrmConstant;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.Strings;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.*;

@Slf4j
public class LeadsMoveAction extends SFAMoveAction {
    private static final RecalculateTaskService recalculateTaskService = SpringUtil.getContext().getBean(RecalculateTaskService.class);
    LeadsOverTimeTaskService leadsOverTimeTaskService = SpringUtil.getContext().getBean(LeadsOverTimeTaskService.class);
    LeadsAllocateTaskService leadsAllocateTaskService = SpringUtil.getContext().getBean(LeadsAllocateTaskService.class);
    LeadsPoolServiceImpl leadsPoolServiceImpl = SpringUtil.getContext().getBean(LeadsPoolServiceImpl.class);
    private static final QiXinTodoService qiXinTodoService = SpringUtil.getContext().getBean(QiXinTodoService.class);
    LeadsAllocateOverTimeTaskService leadsAllocateOverTimeTaskService = SpringUtil.getContext().getBean(LeadsAllocateOverTimeTaskService.class);

    String sourcePoolId;
    boolean cleanOwner = false;
    @Override
    protected void before(SFAObjectPoolCommon.Arg arg) {
        log.info("leads_pool move  Arg{}", JsonUtil.toJsonWithNullValues(arg));
        super.before(arg);
        log.info("leads_pool move  super before finished");
        if (CollectionUtils.isEmpty(dataList)) {
            dataList = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), arg.getObjectIDs(), SFAPreDefineObject.Leads.getApiName());
        }
        objectDataList = dataList;
        if(CollectionUtils.isEmpty(dataList)) {
            return;
        }
        sourcePoolId = LeadsUtils.getPoolId(dataList.get(0));
        failedList = Lists.newArrayList();
        List<String> tobeProcessIds = Lists.newArrayList();
        cleanOwner = AccountUtil.getBooleanValue(objectPoolData, "is_clean_owner", false);
        if(!cleanOwner) {
            List<String> ownerIds = dataList.stream().filter(x -> AccountUtil.hasOwner(x)).map(x -> AccountUtil.getOwner(x)).distinct().collect(Collectors.toList());
            String leadsPoolId = objectPoolData.getId();
            List<Map> countResult = LeadsUtils.getLeadsPoolCountByOwnerIdListAndPoolId(actionContext.getTenantId(), ownerIds, leadsPoolId);
            Iterator var1 = dataList.iterator();
            while (var1.hasNext()) {
                IObjectData objectData = (IObjectData) var1.next();
                String leadsId = objectData.getId();
                List<String> ownerList = objectData.getOwner();
                if (CollectionUtils.isNotEmpty(ownerList)) {
                    Integer count = 0;
                    Optional<Map> optionalCount = countResult.stream().filter(r -> leadsPoolId.
                            equals(r.get("leads_pool_id")) && ownerList.contains(r.get("owner").toString())).findFirst();
                    if (optionalCount.isPresent()) {
                        count = Integer.parseInt(optionalCount.get().get("count").toString());
                    }
                    if (objectPoolData.get("limit_count", Integer.class) <= count) {
                        String failedMsg = String.format(I18N.text(SFA_OVER_MOVE_LIMIT), I18N.text("LeadsObj.attribute.self.display_name"), objectData.getName());
                        failedList.add(failedMsg);
                        continue;
                    }
                }
                tobeProcessIds.add(leadsId);
            }
        } else {
            tobeProcessIds.addAll(dataList.stream().map(x -> x.getId()).collect(Collectors.toSet()));
        }
        if (dataList.size() == 1) {
            if (CollectionUtils.isNotEmpty(failedList)) {
                throw new ValidateException(failedList.get(0));
            }
        } else {
            if (failedList.size() == dataList.size()) {
                throw new ValidateException(failedList.get(0));
            }
        }
        if(!cleanOwner) {
            if (ObjectLimitUtil.isGrayLeadsLimit(actionContext.getTenantId())) {
                List<IObjectData> tobeProcessDataList = dataList.stream().filter(x -> tobeProcessIds.contains(x.getId())).collect(Collectors.toList());
                Set<String> ownerList = tobeProcessDataList.stream().filter(x ->
                        AccountUtil.hasOwner(x)).map(x -> AccountUtil.getOwner(x)).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(ownerList)) {
                    ownerList.forEach(owner -> {
                        List<IObjectData> tempDataList = tobeProcessDataList.stream().filter(x -> AccountUtil.getOwner(x).equals(owner)).collect(Collectors.toList());
                        List<IObjectData> oldDataList = ObjectDataExt.copyList(tempDataList);
                        List<IObjectData> checkLimitDataList = ObjectDataExt.copyList(tempDataList);
                        checkLimitDataList.forEach(x -> {
                            x.set(LeadsConstants.Field.LEADS_POOL_ID.getApiName(), arg.getObjectPoolId());
                            x.setLastModifiedBy(actionContext.getUser().getUserId());
                            x.setLastModifiedTime(System.currentTimeMillis());
                        });

                        ObjectLimitUtil.CheckLimitResult checkLimitResult = ObjectLimitUtil.checkObjectLimitForEdit(actionContext.getUser(), actionContext.getObjectApiName(), owner, oldDataList, checkLimitDataList, objectDescribe);
                        if (CollectionUtils.isNotEmpty(checkLimitResult.getFailureIds())) {
                            throw new ValidateException(String.format(I18N.text(SFA_REACH_LIMIT_OBJ),
                                    I18N.text("LeadsObj.attribute.self.display_name")));
                        }
                    });
                }

                if (AppIdMapping.appIdMapping.get(PrmConstant.PRM_APP_ID).equals(actionContext.getAppId())) {
                    Set<String> outTenantIds = tobeProcessDataList.stream().filter(x ->
                            org.apache.commons.lang3.StringUtils.isNotBlank(AccountUtil.getStringValue(x, SystemConstants.Field.OutTenantId.apiName, "")))
                            .map(x -> AccountUtil.getStringValue(x, SystemConstants.Field.OutTenantId.apiName, "")).collect(Collectors.toSet());
                    if (CollectionUtils.isNotEmpty(outTenantIds)) {
                        for (String outTenantId : outTenantIds) {
                            List<IObjectData> tempDataList = tobeProcessDataList.stream().filter(x -> AccountUtil.getStringValue(x, SystemConstants.Field.OutTenantId.apiName, "").equals(outTenantId)).collect(Collectors.toList());
                            if (CollectionUtils.isEmpty(tempDataList)) {
                                continue;
                            }
                            Set<String> outOwnerList = tempDataList.stream().filter(x ->
                                    org.apache.commons.lang3.StringUtils.isNotBlank(AccountUtil.getOutOwner(x)))
                                    .map(x -> AccountUtil.getOutOwner(x)).collect(Collectors.toSet());
                            if (CollectionUtils.isEmpty(outOwnerList)) {
                                continue;
                            }

                            outOwnerList.forEach(outOwner -> {
                                List<IObjectData> outOwnerDataList = tempDataList.stream().filter(x -> AccountUtil.getOutOwner(x).equals(outOwner)).collect(Collectors.toList());
                                if (CollectionUtils.isNotEmpty(outOwnerDataList)) {
                                    List<IObjectData> checkLimitDataList = ObjectDataExt.copyList(outOwnerDataList);
                                    List<IObjectData> oldDataList = ObjectDataExt.copyList(outOwnerDataList);
                                    checkLimitDataList.forEach(x -> {
                                        x.set(LeadsConstants.Field.LEADS_POOL_ID.getApiName(), arg.getObjectPoolId());
                                        x.setLastModifiedBy(actionContext.getUser().getUserId());
                                        x.setLastModifiedTime(System.currentTimeMillis());
                                    });
                                    ObjectLimitUtil.CheckLimitResult checkOutLimitResult = ObjectLimitUtil.checkOutUserObjectLimitForEdit(actionContext.getUser(), actionContext.getObjectApiName(), outTenantId, outOwner, oldDataList, checkLimitDataList, objectDescribe, true);
                                    if (CollectionUtils.isNotEmpty(checkOutLimitResult.getFailureIds())) {
                                        throw new ValidateException(String.format(I18N.text(SFA_REACH_LIMIT_OBJ),
                                                I18N.text("LeadsObj.attribute.self.display_name")));
                                    }
                                }
                            });
                        }
                    }
                }
            }
        }
        arg.setObjectIDs(tobeProcessIds);
    }

    @Override
    protected SFAObjectPoolCommon.Result doAct(SFAObjectPoolCommon.Arg arg) {
        SFAObjectPoolCommon.Result result = objectPoolService.move(actionContext.getObjectApiName(), actionContext.getUser(), arg.getObjectPoolId(), arg.getObjectIDs(), actionContext.getEventId());
        return result;
    }

    @Override
    protected SFAObjectPoolCommon.Result after(SFAObjectPoolCommon.Arg arg, SFAObjectPoolCommon.Result result) {
        result.setSuccessList(arg.getObjectIDs());
        result.setFailedList(failedList);
        log.info("leads_pool move after method begin");
        if (CollectionUtils.isEmpty(arg.getObjectIDs())) {
            return result;
        }
        log.info("leads_pool move super after method begin");
        result = super.after(arg, result);
        log.info("leads_pool move super after method end, result is {}", JsonUtil.toJsonWithNullValues(result));
        //待办
        if (!Strings.isNullOrEmpty(arg.getObjectPoolId())) {
            List<IObjectData> unAssignedLeads = Lists.newArrayList();
            if(cleanOwner) {
                unAssignedLeads.addAll(dataList);
            } else {
                unAssignedLeads.addAll(dataList.stream().filter(d -> d.get("biz_status", String.class).
                        equals(LeadsBizStatusEnum.UN_ASSIGNED.getCode())).collect(Collectors.toList()));
            }
            if(CollectionUtils.isNotEmpty(unAssignedLeads)) {
                Map<String, List<String>> inAndOutPoolAdminList = leadsPoolServiceImpl.getInAndOutPoolAdminById(actionContext.getUser(), arg.getObjectPoolId());
                if (!inAndOutPoolAdminList.isEmpty()) {
                    for (IObjectData leads : unAssignedLeads) {
                        qiXinTodoService.sendInAndOutTodo(actionContext.getTenantId(), SessionBOCItemKeys.TOBE_ASSIGNED_SALES_CLUE,
                                actionContext.getObjectApiName(), leads.getId(), actionContext.getUser().getUserId(),
                                inAndOutPoolAdminList.containsKey("in") ? inAndOutPoolAdminList.get("in") : Lists.newArrayList(),
                                inAndOutPoolAdminList.containsKey("out") ? inAndOutPoolAdminList.get("out") : Lists.newArrayList());
                    }
                }
            }
        }
        if(cleanOwner) {
            leadsOverTimeTaskService.deleteTask(actionContext.getTenantId(), arg.getObjectIDs());
            leadsAllocateOverTimeTaskService.createOrUpdateTask(actionContext.getTenantId(), arg.getObjectIDs(), objectPoolData);
        }
        addLeadsAllocateTask(arg);
        return result;
    }

    @Override
    protected void addLog() {
        List<String> poolIds = Lists.newArrayList(sourcePoolId, arg.getObjectPoolId());
        List<IObjectData> leadsPoolList = serviceFacade.findObjectDataByIds(actionContext.getTenantId(),
                poolIds, Utils.LEADS_POOL_API_NAME);
        String sourcePoolName;
        if (StringUtils.isNotEmpty(sourcePoolId)) {
            sourcePoolName = leadsPoolList.stream().
                    filter(p -> sourcePoolId.equals(p.getId()))
                    .findFirst().orElse(new ObjectData()).getName();
        } else {
            sourcePoolName = "--";
        }
        String targetPoolName = leadsPoolList.stream().
                filter(p -> arg.getObjectPoolId().equals(p.getId())).findFirst().orElse(new ObjectData()).getName();
        if (!CollectionUtils.isEmpty(arg.getObjectIDs())) {
            for (String processedId : arg.getObjectIDs()) {
                Optional<IObjectData> optionalData = dataList.stream().filter(d -> processedId.equals(d.getId())).findFirst();
                if (!optionalData.isPresent()) {
                    continue;
                }
                IObjectData objectData = optionalData.get();
                String company = objectData.get("company", String.class);
                if (StringUtils.isEmpty(company)) {
                    company = "";
                }
                String msg = String.format(I18N.text(SFA_POOL_MOVE_A_TO_B_MSG),
                        I18N.text("LeadsObj.attribute.self.display_name"),
                        objectData.getName(), company,
                        I18N.text("LeadsPoolObj.attribute.self.display_name"), sourcePoolName,
                        I18N.text("LeadsPoolObj.attribute.self.display_name"), targetPoolName
                );
                serviceFacade.logCustomMessageOnly(actionContext.getUser(), EventType.MODIFY, ActionType.MOVE, objectDescribe, objectData,
                        msg);

                SFALogModels.SFALogEntity logEntity = sfaLogService.buildLogEntity(objectPoolData,
                        String.format("%s ", I18N.text("LeadsObj.attribute.self.display_name")) + objectData.getName() + "," + msg,
                        false);
                List<SFALogModels.SFALogTextMessage> textMessageList = Lists.newArrayList();
                sfaLogService.appendNameLinkLogTextMessage(actionContext.getObjectApiName(), objectData, textMessageList);
                sfaLogService.appendLogTextMessage(actionContext.getObjectApiName(), objectData,
                        SFALogModels.LogLinkType.NO_LINK, msg + "," + objectPoolData.getName(), textMessageList);

                logEntity.setLogTextMessageList(textMessageList);
                sfaLogService.addLog(actionContext.getUser(), logEntity, "SalesCluePoolLog",
                        SFALogModels.LogOperationType.MOVE_LEADS_POOL_LEADS);
            }
        }
    }

    @Override
    protected boolean needTriggerWorkFlow() {
        return false;
    }

    private void addLeadsAllocateTask(SFAObjectPoolCommon.Arg arg) {
        Map<String, List<String>> maps = Maps.newHashMap();
        if (StringUtils.isEmpty(arg.getObjectPoolId()) || CollectionUtils.isEmpty(arg.getObjectIDs())) return;
        maps.put(arg.getObjectPoolId(), arg.getObjectIDs());
        leadsAllocateTaskService.createOrUpdateTask(actionContext.getTenantId(),
                maps, 3, 0);
    }
}