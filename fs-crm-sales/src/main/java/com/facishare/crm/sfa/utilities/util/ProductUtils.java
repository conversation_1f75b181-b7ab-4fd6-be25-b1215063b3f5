package com.facishare.crm.sfa.utilities.util;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.Set;

public class ProductUtils {
    private static ConfigService configService = SpringUtil.getContext().getBean(ConfigService.class);

    public static void filterProductObject(String tenantId, Set<String> apiNames) {
        if (CollectionUtils.empty(apiNames)) {
            return;
        }
        if (!isSpuOpen(tenantId)) {
            apiNames.removeAll(Lists.newArrayList(Utils.SPU_API_NAME, Utils.SPECIFICATION_API_NAME,
                    Utils.SPECIFICATION_VALUE_API_NAME));
        }
    }

    private static boolean isSpuOpen(String tenantId) {
        String configValue = configService.findTenantConfig(new User(tenantId, User.SUPPER_ADMIN_USER_ID), "spu");
        return "1".equals(configValue);
    }
}
