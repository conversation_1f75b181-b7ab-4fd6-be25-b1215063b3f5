package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.utilities.util.SearchListUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardExportAction;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IMultiLevelSelectOption;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.MultiLevelSelectOne;
import com.facishare.paas.metadata.impl.describe.MultiLevelSelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import org.elasticsearch.common.Strings;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Created by renlb on 2018/12/11.
 */
public class SFAExportAction extends StandardExportAction {


    @Override
    protected SearchTemplateQuery generateSearchQuery(User user, String searchTemplateId, String searchQuery, List<String> dataIdList) {
        SearchTemplateQuery query = super.generateSearchQuery(user, searchTemplateId, searchQuery, dataIdList);

        return query;
    }


    @Override
    protected QueryResult<IObjectData> findObjectByQuery(User user, IObjectDescribe describe, SearchTemplateQuery query) {
        QueryResult<IObjectData> queryResult = super.findObjectByQuery(user, describe, query);
        if (objectDescribe.getApiName().equals(describe.getApiName())) {
            handleCascadeField(queryResult.getData(), describe);
        }
        return queryResult;
    }

    protected void handleCascadeField(List<IObjectData> objectDataList, IObjectDescribe describe) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }

        List<IFieldDescribe> fieldDescribes = ObjectDescribeExt.of(describe).getFieldDescribes();
        List<IFieldDescribe> cascadeFields = fieldDescribes.stream()
                .filter(f -> f instanceof MultiLevelSelectOne).collect(Collectors.toList());

        if (CollectionUtils.empty(cascadeFields)) {
            return;
        }

        for (IFieldDescribe fieldDescribe : cascadeFields) {
            MultiLevelSelectOneFieldDescribe multiLevelSelectOneFieldDescribe = (MultiLevelSelectOneFieldDescribe) fieldDescribe;
            List<IMultiLevelSelectOption> multiLevelSelectOptions = multiLevelSelectOneFieldDescribe.getSelectOptions();
            for (IObjectData objectData : objectDataList) {
                String value = objectData.get(fieldDescribe.getApiName(), String.class);

                if (Strings.isNullOrEmpty(value)) {
                    continue;
                }

                for (IMultiLevelSelectOption multiLevelSelectOption : multiLevelSelectOptions) {
                    List<IMultiLevelSelectOption> childOptions = multiLevelSelectOption.getChildOptions();
                    Optional<IMultiLevelSelectOption> childOption = childOptions.stream()
                            .filter(c -> c.getValue().equals(value)).findFirst();
                    if (childOption.isPresent()) {
                        objectData.set(fieldDescribe.getApiName(), multiLevelSelectOption.getLabel() + "/" + childOption.get().getLabel());
                        break;
                    }
                }
            }
        }
    }
}
