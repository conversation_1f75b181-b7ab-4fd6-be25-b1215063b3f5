package com.facishare.crm.sfa.predefine.service.real;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.sfa.predefine.service.model.ServiceResult;
import com.facishare.crm.sfa.predefine.service.model.WeChatRegeditConfigModel;
import com.facishare.crm.sfa.predefine.service.model.WeChatRegisterPartner;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.model.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by luxin on 2018/9/12.
 */
@Service
@Slf4j
public class WeChatRegisterConfigServiceImpl implements WeChatRegisterConfigService {

    @Autowired
    private ConfigService configService;


    @Override
    public ServiceResult<WeChatRegisterPartner.RegisterStatusEnum> createOrUpdateConfig(User user, WeChatRegeditConfigModel arg) {
        String configKey = WeChatRegeditConfigModel.getConfigKey(arg);
        String tenantConfig = configService.findTenantConfig(user, configKey);
        if (StringUtils.isNotBlank(tenantConfig)) {
            log.warn("Arg json:{},updateTenantConfig:{}",JSON.toJSONString(arg),JSON.toJSONString(arg.getStatus()));
            configService.updateTenantConfig(user, configKey, JSON.toJSONString(arg.getStatus()), ConfigValueType.STRING);
        } else {
            log.warn("Arg json:{},createTenantConfig:{}",JSON.toJSONString(arg),JSON.toJSONString(arg.getStatus()));
            configService.createTenantConfig(user, configKey, JSON.toJSONString(arg.getStatus()), ConfigValueType.STRING);
        }
        return ServiceResult.ofSuccess(arg.getStatus());
    }

    @Override
    public ServiceResult<WeChatRegisterPartner.RegisterStatusEnum> getConfig(User user, WeChatRegeditConfigModel arg) {
        String configKey = WeChatRegeditConfigModel.getConfigKey(arg);
        String tenantConfig = configService.findTenantConfig(user, configKey);
        log.warn("Arg json:{},findTenantConfig:{}",JSON.toJSONString(arg),JSON.toJSONString(arg.getStatus()));
        if (StringUtils.isNotBlank(tenantConfig)) {
            WeChatRegisterPartner.RegisterStatusEnum registerStatusEnum = JSON.parseObject(tenantConfig, WeChatRegisterPartner.RegisterStatusEnum.class);
            return ServiceResult.ofSuccess(registerStatusEnum);
        }
        return ServiceResult.ofSuccess(WeChatRegisterPartner.RegisterStatusEnum.NONE);
    }
}
