package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.AccountPathSynchronizer;
import com.facishare.crm.sfa.utilities.constant.AccountConstants;
import com.facishare.crm.sfa.utilities.util.AccountPathUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.google.common.base.Strings;

public class AccountBulkAssociateAction extends BaseBulkAssociateSFAAction {

    @Override
    protected void validate() {
        super.validate();
        if (Utils.ACCOUNT_API_NAME.equals(arg.getAssociatedObjApiName()) &&
                "account_account_list".equals(arg.getAssociatedObjRelatedListName()) &&
                CollectionUtils.notEmpty(associatedDataList) && !Strings.isNullOrEmpty(arg.getAssociateObjId())) {
            associatedDataList.forEach(m -> {
                m.set(AccountConstants.Field.FIELD_PARENT_ACCOUNT_ID, arg.getAssociateObjId());
            });
            AccountPathUtil.checkIsHoop(actionContext.getUser(), associatedDataList);
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        if (Utils.ACCOUNT_API_NAME.equals(arg.getAssociatedObjApiName()) &&
                "account_account_list".equals(arg.getAssociatedObjRelatedListName()) &&
                CollectionUtils.notEmpty(associatedDataList)) {
            AccountPathSynchronizer.builder()
                    .user(actionContext.getUser())
                    .objectDataList(associatedDataList)
                    .build()
                    .dealData();
        }
        return result;
    }
}
