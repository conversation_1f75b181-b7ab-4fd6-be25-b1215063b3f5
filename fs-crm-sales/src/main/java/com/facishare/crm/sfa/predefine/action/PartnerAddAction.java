package com.facishare.crm.sfa.predefine.action;

import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;

/**
 * @author: SunDeYu
 * @date: 2020/7/13 14:07
 * @description:
 */
public class PartnerAddAction extends StandardAddAction {
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        Object dataFrom = arg.getObjectData().toObjectData().get("dataFrom");
        if (Objects.nonNull(dataFrom)){
            dataFrom = dataFrom.toString();
        }
        if ("1".equals(dataFrom)) {
            return Lists.newArrayList("TransferAdd");
        } else {
            return super.getFuncPrivilegeCodes();
        }
    }
}
