package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.service.ModuleCtrlConfigService;
import com.facishare.crm.sfa.predefine.service.modulectrl.IModuleInitService;
import com.facishare.crm.sfa.utilities.util.ReturnedGoodsInvoiceUtil;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportVerifyAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.List;

/**
 * Created by renlb on 2019/4/19.
 */
public class ReturnedGoodsInvoiceProductInsertImportVerifyAction extends StandardInsertImportVerifyAction {

    private final ModuleCtrlConfigService moduleCtrlConfigService = SpringUtil.getContext().getBean(ModuleCtrlConfigService.class);


    @Override
    protected List<IFieldDescribe> getValidImportFields() {
        List<IFieldDescribe> fields = super.getValidImportFields();
        ReturnedGoodsInvoiceUtil.removeUnSupportedDetailFields(fields);
        // 开启了cqp
        if (moduleCtrlConfigService.openStatus(IModuleInitService.MODULE_CPQ,actionContext.getUser(),actionContext)) {
            fields.removeIf(field->ReturnedGoodsInvoiceUtil.parentKeys.contains(field.getApiName()));
        }
        return fields;
    }
}
