package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.model.Enum.SessionBOCItemKeys;
import com.facishare.crm.sfa.predefine.enums.ConfirmStatusEnum;
import com.facishare.crm.sfa.predefine.enums.RemindRecordEnum;
import com.facishare.crm.sfa.utilities.proxy.SailAdminProxy;
import com.facishare.crm.sfa.utilities.proxy.model.OrderNotify;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_ORDER_EDIT_LOG_MSG;
import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_ORDER_EDIT_LOG_MSG_REASON;

public class SalesOrderSetStatusAction extends SFASetStatusAction {
    private static final SailAdminProxy SAIL_ADMIN_PROXY = SpringUtil.getContext().getBean(SailAdminProxy.class);
    private ThreadPoolTaskExecutor executor = (ThreadPoolTaskExecutor) SpringUtil.getContext().getBean("taskExecutor");


    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        if(finalStatus < 1) {
            return result;
        }

        if(finalStatus  == 7) {
            // 自由审批流完成，触发订单完成时间回填
            updateSalesOrderConfirmTime(arg.object_id);
            serviceFacade.sendActionMq(actionContext.getUser(), dataList, ObjectAction.CONFIRM);
            if ("322246".equals(actionContext.getTenantId())) {
                log.info("send action message,action:{},tenantId:{},apiName:{},dataId:{}",
                        new Object[]{ObjectAction.CONFIRM.getActionCode(), actionContext.getTenantId(),
                                actionContext.getObjectApiName(), arg.getObject_id()});
            }
        }

        //发货提醒
        if(CollectionUtils.notEmpty(shipperIds)) {
            List<String> strIds = Lists.newArrayList();
            shipperIds.forEach(x -> strIds.add(x.toString()));
            qiXinTodoService.sendTodo(actionContext.getTenantId(), SessionBOCItemKeys.TobeDeliveryCustomerOrder,
                    actionContext.getObjectApiName(), arg.getObject_id(), actionContext.getUser().getUserId(), strIds);
        }

        //订货通
        if(CollectionUtils.notEmpty(dataList)) {
            IObjectData objectData = dataList.get(0);
            String resource = objectData.get("resource", String.class);
            if(Objects.equals("1", resource)) {
                if(arg.getStatus() == ConfirmStatusEnum.CONFIRM.getValue() && finalStatus == 7) {
                    OrderNotify.Arg notifyArg = OrderNotify.Arg.builder()
                            .tenantId(actionContext.getTenantId())
                            .operatorId(actionContext.getUser().getUserId())
                            .orderId(objectData.getId())
                            .typeobjectid(objectData.getId())
                            .type(2)
                            .upstreamAccepterIds(shipperIds)
                            .build();
                    executor.execute(()->{
                        SAIL_ADMIN_PROXY.orderNotify(notifyArg);
                    });
                } else if(arg.getStatus() == ConfirmStatusEnum.REJECT.getValue()) {
                    OrderNotify.Arg notifyArg = OrderNotify.Arg.builder()
                            .tenantId(actionContext.getTenantId())
                            .operatorId(actionContext.getUser().getUserId())
                            .orderId(objectData.getId())
                            .typeobjectid(objectData.getId())
                            .type(5)
                            .build();
                    executor.execute(()->{
                        SAIL_ADMIN_PROXY.orderNotify(notifyArg);
                    });
                }
            }
        }

        return result;
    }

    @Override
    protected Integer getRemindRecordType(int confirmStatus) {
        if(confirmStatus == ConfirmStatusEnum.CONFIRM.getValue()) {
            return RemindRecordEnum.ORDER_CONFIRMED.getValue();
        } else if(confirmStatus == ConfirmStatusEnum.REJECT.getValue()) {
            return RemindRecordEnum.ORDER_REJECTED.getValue();
        } else if(confirmStatus == ConfirmStatusEnum.RECALL.getValue()) {
            return RemindRecordEnum.ORDER_RECALLED.getValue();
        }

        return 0;
    }

    @Override
    protected String getRemindContent(int confirmStatus, String rejectReason) {
        String msg = null;
        if(CollectionUtils.notEmpty(dataList)) {
            IObjectData objectData = dataList.get(0);
            String orderAmount = objectData.get("order_amount", String.class);
            if(Strings.isNullOrEmpty(orderAmount)) {
                orderAmount = "--";
            }
            SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyy-MM-dd");
            if(!Strings.isNullOrEmpty(rejectReason)) {
                msg = String.format(I18N.text(SFA_ORDER_EDIT_LOG_MSG_REASON), rejectReason, objectData.getName(),
                        orderAmount,
                        simpleDateFormat.format(objectData.get("order_time", Date.class)));
            } else {
                msg = String.format(I18N.text(SFA_ORDER_EDIT_LOG_MSG), objectData.getName(),
                        orderAmount,
                        simpleDateFormat.format(objectData.get("order_time", Date.class)));
            }
        }

        return msg;
    }

    private void updateSalesOrderConfirmTime(String dataId) {
        log.warn("SalesOrderSetStatusAction updateSalesOrderConfirmTime tenantId->{},dataId->{}",actionContext.getTenantId(),dataId);
        IObjectData objectData = new ObjectData();
        objectData.setTenantId(actionContext.getTenantId());
        objectData.setDescribeApiName(Utils.SALES_ORDER_API_NAME);
        objectData.setId(dataId);
        objectData.set("confirm_time", System.currentTimeMillis());
        List<IObjectData> objectDataList = Lists.newArrayList(objectData);
        List<String> toUpdateFields = Lists.newArrayList("confirm_time");
        serviceFacade.batchUpdateByFields(actionContext.getUser(), objectDataList, toUpdateFields);
    }

}
