package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.constant.NewOppportunityConstants;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.paas.appframework.core.predef.controller.StandardDetailController;
import com.facishare.paas.appframework.core.predef.controller.StandardNewDetailController;
import com.facishare.paas.metadata.api.IObjectData;

public class NewOpportunityLinesNewDetailController extends StandardNewDetailController {
    @Override
    protected StandardDetailController.Result after(StandardDetailController.Arg arg, StandardDetailController.Result result) {
        addPriceBookId();
        return super.after(arg, result);
    }

    /**
     * 商机明细详情中增加价目表ID
     */
    private void addPriceBookId(){
        if (GrayUtil.isGrayPriceBookRefactor(controllerContext.getTenantId())) {
            return;
        }
        if(arg.isFromRecycleBin()){
            return;
        }

        IObjectData newopportunityData = this.serviceFacade.findObjectData(
                getControllerContext().getUser(),
                data.get(NewOppportunityConstants.NewOpportunityField.NEWOPPORTUNITYID.getApiName()).toString(),
                SFAPreDefineObject.NewOpportunity.getApiName() );
        if(newopportunityData!=null){
            data.set(NewOppportunityConstants.NewOpportunityField.PRICEBOOKID.getApiName(),
                    String.valueOf(newopportunityData.get(NewOppportunityConstants.NewOpportunityField.PRICEBOOKID.getApiName())));
        }
    }
}
