package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.service.PartnerService;
import com.facishare.crm.sfa.predefine.service.model.OutInfoChangeModel;
import com.facishare.crm.sfa.utilities.constant.PartnerConstants;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.Rule;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

import java.util.Map;
import java.util.Objects;

/**
 * Created by luohuilong on 2017/12/5.
 */
@Slf4j
public class SFAAddAction extends SFAObjectSaveAction {
    protected PartnerService partnerService = SpringUtil.getContext().getBean(PartnerService.class);
    protected IObjectData argObjectData;

    @Override
    protected String getIRule() {
        return Rule.CREATE;
    }

    @Override
    protected void before(Arg arg) {
        this.argObjectData = arg.getObjectData().toObjectData();
        super.before(arg);
    }

    @Override
    protected Result doAct(Arg arg) {
        super.doAct(arg);
        IObjectData result = serviceFacade.saveObjectData(actionContext.getUser(), objectData);
        return Result.builder().objectData(ObjectDataDocument.of(result)).build();
    }

    @Override
    protected Result after(Arg arg, Result result) {
        if(result.getObjectData() != null && result.getObjectData().get("CRMResponse")!=null) {
            Map<String, Object> response = (Map<String, Object>) result.getObjectData().get("CRMResponse");
            IObjectData iObjectData;
            if (!ObjectUtils.isEmpty(response.get("_id"))) {
                iObjectData = serviceFacade.findObjectData(new User(actionContext.getTenantId(), "-10000"),
                        response.get("_id").toString(), objectDescribe);
            } else {
                iObjectData = new ObjectData();
            }
            response.forEach((k,v)->iObjectData.set(k,v));

            result.setObjectData(ObjectDataDocument.of(iObjectData));

            doChangePartnerAndOwner(response.get("_id") != null ? response.get("_id").toString() : "");
        }

        return result;
    }

    protected void doChangePartnerAndOwner(String dataId) {
        if (StringUtils.isEmpty(dataId)) {
            return;
        }
        //更换合作伙伴，获取外部企业和外部负责人填充
        try {
            ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
            parallelTask.submit(() -> {
                String partnerId = this.argObjectData.get(PartnerConstants.FIELD_PARTNER_ID, String.class);
                if (Objects.nonNull(partnerId)) {
                    Tuple<Integer, Long> newOutInfo = partnerService.changePartnerAndOwner(this.actionContext.getUser(),
                            this.objectDescribe.getApiName(), Sets.newHashSet(dataId), partnerId);

                    OutInfoChangeModel model = OutInfoChangeModel
                            .builder()
                            .dataId(dataId)
                            .dataName(argObjectData.getName())
                            .oldOutEI(0)
                            .oldOutUserId(0)
                            .newOutEI(newOutInfo.getKey())
                            .newOutUserId(newOutInfo.getValue().intValue())
                            .build();
                    partnerService.remindOutUser(actionContext.getUser(), actionContext.getObjectApiName(),
                            partnerService.getChangePartnerRemindRecordType(actionContext.getObjectApiName()),
                            Lists.newArrayList(model));
                }
            });
            parallelTask.run();
        } catch (Exception ex) {
            log.error("SFAAddAction execute changePartnerAndOwner error,arg {}", arg, ex);
        }
    }

}
