package com.facishare.crm.sfa.utilities.proxy.model;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

public interface ObjectFieldReference {
    @Data
    @Builder
    class Arg implements Serializable {
      List<ReferenceItem> items;
    }
    @Data
    @Builder
    class ReferenceItem
    {
        private String sourceType;
        private String sourceLabel;
        private String sourceValue;
        private String targetType;
        private String targetValue;
    }
    @Data
    class Result {
        boolean success;
        String message;
        String code;
    }

    @Data
    @Builder
    class DeleteArg implements Serializable {
        List<DeleteItem> items;
    }
    @Data
    @Builder
    class DeleteItem
    {
        private String sourceType;
        private String sourceValue;
        private String targetValue;
    }
}
