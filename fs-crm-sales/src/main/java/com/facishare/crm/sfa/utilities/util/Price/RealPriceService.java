package com.facishare.crm.sfa.utilities.util.Price;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.constant.AvailableConstants;
import com.facishare.crm.sfa.utilities.constant.PriceBookConstants;
import com.facishare.crm.sfa.utilities.util.AvailableRangeUtils;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.ServiceFacadeImpl;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class RealPriceService {

    protected static final ServiceFacade SERVICE_FACADE = SpringUtil.getContext().getBean(ServiceFacadeImpl.class);
    private final AvailableRangeUtils availableRangeUtils = SpringUtil.getContext().getBean(AvailableRangeUtils.class);

    /**
     * 标准价目表产品信息
     */
    protected List<IObjectData> standardPriceBookProductList = Lists.newArrayList();
    /**
     * 可售范围信息
     */
    protected List<String> availableRangeIdList = Lists.newArrayList();
    /**
     * 可售产品范围信息
     */
    protected List<IObjectData> availableProductResultList = Lists.newArrayList();
    /**
     * 适配价目表产品信息/标准产品信息/经过处理的价目表产品(带可售范围信息)
     */
    protected List<IObjectData> baseProductList = Lists.newArrayList();

    protected User user;
    public static final String PRICE_BOOK_FLAG = "S";
    public static final String AVAILABLE_RANGE_FLAG = "A";


    public RealPriceService(User user) {
        this.user = user;
    }

    private void init(Arg arg, List<String> flags) {
        if (flags.contains(AVAILABLE_RANGE_FLAG)) {
            availableRangeIdList = availableRangeUtils.getAvailableRange(user, arg.getAccountId(), arg.getPartnerId(), "");
            if (CollectionUtils.empty(availableRangeIdList)) {
                return;
            }
            availableProductResultList = availableRangeUtils.getAvailableProductDataList(user, availableRangeIdList, arg.getProductIdList());
        } else {
            if (flags.contains(PRICE_BOOK_FLAG)) {
                List<IObjectData> priceBookList = availableRangeUtils.getAllPriceBookList(user);
                if (CollectionUtils.empty(priceBookList)) {
                    return;
                }
                baseProductList = availableRangeUtils.getProductByPriceBookList(user, arg.getProductIdList(), priceBookList);
            } else {
                baseProductList = findStandardProductList(arg.getProductIdList());
            }
        }
    }

    /**
     * 提前获取需要的数据
     */
    public void fillList(List<String> flags, List<String> productIdList) {

        if (flags.contains(AVAILABLE_RANGE_FLAG)) {
            if (flags.contains(PRICE_BOOK_FLAG)) {
                List<IObjectData> availablePriceBookList = availableRangeUtils.getPriceBookListByRangeIds(user, availableRangeIdList);
                baseProductList = availableRangeUtils.getProductByPriceBookList(user, productIdList, availablePriceBookList);
                standardPriceBookProductList = availableRangeUtils.findStandardPriceBookProductList(user, productIdList);
            } else {
                baseProductList = findStandardProductList(productIdList);
            }
        }
    }

    /**
     * 拼装符合该产品id的产品/价目表产品信息
     *
     * @return
     */
    public List<IObjectData> specialOperation(List<String> flags, String productId) {
        if (flags.contains(AVAILABLE_RANGE_FLAG) && (flags.contains(PRICE_BOOK_FLAG))) {
            List<IObjectData> tmpPriceBookProductList = baseProductList.stream()
                    .filter(x -> x.get(PriceBookConstants.ProductField.PRODUCTID.getApiName()).toString().equals(productId)).collect(Collectors.toList());
            List<IObjectData> effectivePriceBookProductList = Lists.newArrayList();
            //和可售范围产品拍平表交叉验证
            for (IObjectData data : tmpPriceBookProductList) {
                List<String> rangIdList = availableRangeUtils.castList(data.get("available_range_id"), String.class);
                if (CollectionUtils.notEmpty(rangIdList)) {
                    List<String> prdResultList = availableProductResultList.stream().filter(x -> rangIdList.contains(x.get(AvailableConstants.ProductResultField.AVAILABLE_RANGE_ID, String.class)))
                            .map(x -> x.get(AvailableConstants.ProductResultField.PRODUCT_ID, String.class)).collect(Collectors.toList());
                    if (prdResultList.contains(AvailableConstants.PublicConstants.RANGE_ALL) || prdResultList.contains(data.get(PriceBookConstants.ProductField.PRODUCTID.getApiName(), String.class))) {
                        effectivePriceBookProductList.add(data);
                    }
                }
            }
            if (CollectionUtils.empty(effectivePriceBookProductList)) {
                //标准价目表里的数据为兜底数据
                Optional<IObjectData> standardProduct = standardPriceBookProductList.stream().filter(x -> x.get(PriceBookConstants.ProductField.PRODUCTID.getApiName(), String.class).equals(productId)).findFirst();
                if (standardProduct.isPresent()) {
                    return Lists.newArrayList(standardProduct.get());
                } else {
                    return Lists.newArrayList();
                }
            }
            return effectivePriceBookProductList;
        }

        return baseProductList.stream()
                .filter(x -> x.get(PriceBookConstants.ProductField.PRODUCTID.getApiName()).toString().equals(productId))
                .collect(Collectors.toList());

    }

    public final Result act(Arg arg, List<String> flags) {
        Result rst = new Result();
        rst.setRst(Maps.newHashMap());
        if (CollectionUtils.empty(arg.productIdList)) {
            return rst;
        }
        List<String> realProductIdList = arg.productIdList;

        init(arg, flags);
        fillList(flags, realProductIdList);

        Map<String, ObjectDataDocument> priceMap = Maps.newHashMap();
        for (String id : realProductIdList) {
            List<IObjectData> specialProductList = specialOperation(flags, id);
            //优先以priority正序，同priority以last_modified_time倒序
            Optional<IObjectData> tmpProduct = specialProductList.stream().min((x, y) -> {
                //TODO:空指针校验
                Integer firstPriority = x.get("price_book_priority", Integer.class);
                Integer secondPriority = y.get("price_book_priority", Integer.class);
                if (Objects.equals(firstPriority, secondPriority)) {
                    return y.getLastModifiedTime().compareTo(x.getLastModifiedTime());
                }
                return firstPriority.compareTo(secondPriority);
            });
            priceMap.put(id, ObjectDataDocument.of(tmpProduct.orElse(null)));
        }
        rst.setRst(priceMap);
        return rst;
    }

    public final List<IObjectData> findStandardProductList(List<String> productIdList) {
        //未考虑上下架
        List<IObjectData> tmp = SERVICE_FACADE.findObjectDataByIdsIgnoreFormula(user.getTenantId(), productIdList, Utils.PRODUCT_API_NAME);
        List<IObjectData> rst = Lists.newArrayList();
        for (IObjectData data : tmp) {
            ObjectDataExt objectDataExt = ObjectDataExt.of(data);
            if (!objectDataExt.getLifeStatus().equals(ObjectLifeStatus.INVALID)) {
                //特殊实体必须字段
                data.set("product_id", data.getId());
                data.set(PriceBookConstants.Field.PRIORITY.getApiName(), "0");
                rst.add(data);
            }
        }
        return rst;
    }

    @Data
    public static class Arg {
        List<String> productIdList;
        String accountId;
        String partnerId;
    }

    @Data
    public static class Result {
        /**
         * 是否适用标准价格体系
         * 当租户调整了订单 价格默认公式时返回false
         */
        Boolean applicablePriceSystem;
        Map<String, ObjectDataDocument> rst;
    }
}
