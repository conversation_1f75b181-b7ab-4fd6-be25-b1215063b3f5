package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.util.LeadsUtils;
import com.facishare.crm.sfa.utilities.util.OpportunityUtil;
import com.facishare.crm.sfa.utilities.util.PreDefLayoutUtil;
import com.facishare.crm.sfa.utilities.util.VersionUtil;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.facishare.paas.appframework.core.model.RequestContext.Android_CLIENT_INFO_PREFIX;
import static com.facishare.paas.appframework.core.model.RequestContext.CLIENT_INFO;
import static com.facishare.paas.appframework.core.model.RequestContext.IOS_CLIENT_INFO_PREFIX;

@Slf4j
public class OpportunityNewDetailController extends SFANewDetailController {
    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);

        List<ObjectDataDocument> resultDataList = new ArrayList<>();
        resultDataList.add(newResult.getData());
        OpportunityUtil.handSaleActionInfo(controllerContext.getTenantId(), controllerContext.getUser(), resultDataList);
        if (newResult.getLayout() == null) {
            return result;
        }
        ILayout layout = new Layout(newResult.getLayout());
        LeadsUtils.handleLeadsRelatedComponentsForNewDetail(layout, "LeadsObj_opportunity_id_related_list");
        return newResult;

    }
    @Override
    protected ILayout getLayout() {
        ILayout layout = super.getLayout();
        if (layout == null) {
            return null;
        }
        removeOutRelevantTeam(layout);
        return layout;
    }

    /**
     * 老商机移除out_relevant_team
     */
    private void removeOutRelevantTeam(ILayout layout) {
        LayoutExt layoutExt = LayoutExt.of(layout);
        if (layoutExt != null) {
            layoutExt.getRelatedComponent().ifPresent(relatedComponent -> {
                try {
                    List<IComponent> childComponents = relatedComponent.getChildComponents();
                    if (childComponents != null) {
                        childComponents.stream().forEach(childComponent -> {
                            if ("relevant_team_component".equals(childComponent.get("api_name", String.class))) {
                                List<Map> includeFields = childComponent.get("include_fields", List.class);
                                includeFields.removeIf(k -> "out_relevant_team".equals(k.get("field_name")));
                                childComponent.set("include_fields", includeFields);
                            }
                        });
                    }
                } catch (MetadataServiceException e) {
                    log.warn("OpportunityDetailController getChildComponents error", e);
                }
            });
        }
    }

}
