package com.facishare.crm.sfa.utilities.util;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.AccountPathSynchronizer;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.AccountConstants;
import com.facishare.crm.sfa.utilities.constant.ContactConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.ServiceFacadeImpl;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseImportAction;
import com.facishare.paas.appframework.core.predef.action.BaseImportDataAction;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.common.exception.CRMErrorCode.FS_CRM_DEFOBJ_CHECKED_CAN_NOT_LOOKUP_MYSELF;

/**
 * 客户层级关系公共方法 class
 *
 * <AUTHOR>
 * @date 2020/3/31
 */
@Slf4j
public class AccountPathUtil {
    private static final ServiceFacade SERVICE_FACADE = SpringUtil.getContext().getBean(ServiceFacadeImpl.class);

    /**
     * 根据条件获取客户列表
     *
     * @param user
     * @param filters
     * @return
     */
    public static List<IObjectData> getAccountListWithDeleted(User user, IObjectDescribe objectDescribe, List<IFilter> filters) {
        if (CollectionUtils.empty(filters)) {
            return Lists.newArrayList();
        }
        SearchTemplateQuery searchTemplateQuery = CommonSearchUtil.getSearchTemplateQuery(filters);
        searchTemplateQuery.setNeedReturnQuote(false);
        IActionContext actionContext = buildActionContext(user);
        QueryResult<IObjectData> accountList = SERVICE_FACADE.findBySearchQueryWithDeleted(actionContext, objectDescribe,
                searchTemplateQuery);
        if (CollectionUtils.empty(accountList.getData())) {
            return Lists.newArrayList();
        }
        return accountList.getData();
    }

    /**
     * 根据路径获取客户列表，包含作废删除的
     *
     * @param user
     * @param currentAccountIds
     * @return
     */
    public static List<IObjectData> getAccountListByPathWithDeleted(User user, IObjectDescribe objectDescribe,
                                                                    Set<String> currentAccountIds) {
        if (CollectionUtils.empty(currentAccountIds)) {
            return Lists.newArrayList();
        }
        List<IFilter> filters = Lists.newArrayList();
        String matchValue = String.format("*.%s.*", Joiner.on("|").join(currentAccountIds));
        SearchUtil.fillFilterMatch(filters, AccountConstants.Field.FIELD_ACCOUNT_PATH, matchValue);
        return getAccountListWithDeleted(user, objectDescribe, filters);
    }

    /**
     * 根据id获取客户列表，包含作废删除的
     *
     * @param user
     * @param currentAccountIds
     * @return
     */
    public static List<IObjectData> getAccountListByIdWithDeleted(User user, IObjectDescribe objectDescribe,
                                                                  List<String> currentAccountIds) {
        if (CollectionUtils.empty(currentAccountIds)) {
            return Lists.newArrayList();
        }
        List<IFilter> filters = Lists.newArrayList();
        if (currentAccountIds.size() > 1) {
            SearchUtil.fillFilterIn(filters, IObjectData.ID, currentAccountIds);
        } else {
            SearchUtil.fillFilterEq(filters, IObjectData.ID, currentAccountIds);
        }
        return getAccountListWithDeleted(user, objectDescribe, filters);
    }

    /**
     * 根据路径获取客户列表，不包含作废删除的
     *
     * @param user
     * @param currentAccountIds
     * @return
     */
    public static List<IObjectData> getAccountList(User user, Set<String> currentAccountIds) {
        if (CollectionUtils.empty(currentAccountIds)) {
            return Lists.newArrayList();
        }
        List<IFilter> filters = Lists.newArrayList();
        String matchValue = String.format("*.%s.*", Joiner.on("|").join(currentAccountIds));
        SearchUtil.fillFilterMatch(filters, AccountConstants.Field.FIELD_ACCOUNT_PATH, matchValue);
        SearchTemplateQuery searchTemplateQuery = CommonSearchUtil.getSearchTemplateQuery(filters);
        searchTemplateQuery.setOrders(Lists.newArrayList(new OrderBy(ContactConstants.Field.LASTMODIFIEDTIME, false)));
        IActionContext actionContext = buildActionContext(user);
        QueryResult<IObjectData> accountList = SERVICE_FACADE.findBySearchQuery(actionContext, Utils.ACCOUNT_API_NAME,
                searchTemplateQuery);
        if (CollectionUtils.empty(accountList.getData())) {
            return Lists.newArrayList();
        }
        return accountList.getData();
    }

    public static IActionContext buildActionContext(User user) {
        return ActionContextExt.of(user, RequestContextManager.getContext())
                .set("skip_relevantTeam", true)
                .getContext();
    }

    /**
     * 根据客户id获取所有叶子节点
     *
     * @param user
     * @param currentAccountIds
     * @return
     */
    public static List<IObjectData> getChildrenAccountList(User user, Set<String> currentAccountIds) {
        if (CollectionUtils.empty(currentAccountIds)) {
            return Lists.newArrayList();
        }

        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, AccountConstants.Field.FIELD_PARENT_ACCOUNT_ID, currentAccountIds);
        SearchTemplateQuery searchTemplateQuery = CommonSearchUtil.getSearchTemplateQuery(filters);
        searchTemplateQuery.setNeedReturnQuote(false);
        IActionContext actionContext = buildActionContext(user);
        QueryResult<IObjectData> accountList = SERVICE_FACADE.findBySearchQuery(actionContext, Utils.ACCOUNT_API_NAME,
                searchTemplateQuery);
        if (CollectionUtils.empty(accountList.getData())) {
            return Lists.newArrayList();
        }
        return accountList.getData();
    }

    /**
     * 检查是否成环
     *
     * @param user
     * @param objectDataList
     */
    public static void checkIsHoop(User user, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        changeAccountPath(user, objectDataList, Maps.newHashMap(), false, "", Lists.newArrayList());
    }

    /**
     * 检查是否成环,编辑导入专用
     *
     * @param user
     * @param objectDataList
     */
    public static List<BaseImportAction.ImportError> checkIsHoopForImport(User user, String importType, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return Lists.newArrayList();
        }
        List<BaseImportAction.ImportError> importErrors = Lists.newArrayList();
        changeAccountPath(user, objectDataList, Maps.newHashMap(), false, importType, importErrors);
        return importErrors;
    }

    /**
     * 组装数据并校验
     *
     * @param user
     * @param objectDataList
     * @param needChangeData
     * @param needSave
     */
    public static void changeAccountPath(User user, List<IObjectData> objectDataList, Map<String, IObjectData> needChangeData,
                                         Boolean needSave, String importType, List<BaseImportAction.ImportError> importErrors) {
        StopWatch stopWatch = StopWatch.create("changeAccountPath");
        if (CollectionUtils.empty(objectDataList)) {
            log.info("AccountPathChange objectDataList is null");
            return;
        }

        Set<String> parentAccountIds = Sets.newHashSet();
        parentAccountIds.addAll(objectDataList.stream()
                .filter(n -> !Strings.isNullOrEmpty(n.get(AccountConstants.Field.FIELD_PARENT_ACCOUNT_ID, String.class)))
                .map(m -> m.get(AccountConstants.Field.FIELD_PARENT_ACCOUNT_ID, String.class))
                .collect(Collectors.toSet()));

        Set<String> accountIds = objectDataList.stream().map(m -> m.getId()).collect(Collectors.toSet());
        parentAccountIds.addAll(accountIds);
        stopWatch.lap("findParentAccountIds");

        IObjectDescribe accountDescribe = getObjectDescribe(user);
        stopWatch.lap("getObjectDescribe");

        //获取所有可能受影响的数据
        List<IObjectData> allDataList = Lists.newArrayList();
        allDataList.addAll(AccountPathUtil.getAccountListByPathWithDeleted(user, accountDescribe, parentAccountIds));
        stopWatch.lap("findAllDataList");

        //先移除后添加保证数据是最新的，并且全面的
        allDataList.removeIf(m -> accountIds.contains(m.getId()));
        allDataList.addAll(objectDataList);

        //补充可能没有查到的根节点
        Set<String> allAccountIds = allDataList.stream().map(m -> m.getId()).collect(Collectors.toSet());
        parentAccountIds.removeAll(allAccountIds);
        if (CollectionUtils.notEmpty(parentAccountIds)) {
            List<IObjectData> accountRootList = AccountPathUtil.getAccountListByIdWithDeleted(user, accountDescribe,
                    Lists.newArrayList(parentAccountIds));
            stopWatch.lap("findAccountRootList");
            if (CollectionUtils.notEmpty(accountRootList)) {
                allDataList.addAll(accountRootList);
            }
        }
        if (CollectionUtils.empty(allDataList)) {
            log.info("AccountPathChange allDataList is null");
            return;
        }

        //获取在当前数据列表中算作根节点的数据
        List<IObjectData> rootAccountList = getRootObjectData(objectDataList, importType, importErrors, allDataList);
        stopWatch.lap("findRootAccountList");

        if (CollectionUtils.empty(rootAccountList)) {
            if (!Strings.isNullOrEmpty(importType)) {
                objectDataList.forEach(m -> {
                    setImportErrors(importErrors, m, I18N.text(SFAI18NKeyUtil.SFA_ACCOUNT_CHILDISFATHER),
                            true, objectDataList);
                });
                return;
            } else {
                throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_ACCOUNT_CHILDISFATHER));
            }
        }

        //处理根节点
        changeRootData(user, accountDescribe, objectDataList, needChangeData, needSave, importType, importErrors,
                allDataList, rootAccountList);
        stopWatch.lap("changeRootAccountList");

        stopWatch.logSlow(3000);
    }

    /**
     * 处理提供数据的根节点
     *
     * @param objectDataList
     * @param needChangeData
     * @param needSave
     * @param importType
     * @param importErrors
     * @param allDataList
     * @param rootAccountList
     */
    private static void changeRootData(User user, IObjectDescribe accountDescribe, List<IObjectData> objectDataList,
                                       Map<String, IObjectData> needChangeData,
                                       Boolean needSave, String importType, List<BaseImportAction.ImportError> importErrors,
                                       List<IObjectData> allDataList, List<IObjectData> rootAccountList) {
        Iterator<IObjectData> rootIt = rootAccountList.iterator();
        while (rootIt.hasNext()) {
            IObjectData child = rootIt.next();
            if (Strings.isNullOrEmpty(child.get(AccountConstants.Field.FIELD_PARENT_ACCOUNT_ID, String.class)) ||
                    "invalid".equals(child.get("life_status", String.class)) || Boolean.TRUE.equals(child.isDeleted())) {
                child.set(AccountConstants.Field.FIELD_ACCOUNT_PATH, child.getId());
                if (needSave) {
                    needChangeData.put(child.getId(), child);
                }
                recursionChangeData(allDataList, child, needChangeData, needSave, importType, importErrors,
                        0, objectDataList);
                continue;
            }
            Optional<IObjectData> parentAccountCurrentOptional = allDataList.stream()
                    .filter(n -> n.getId().equals(child.get(AccountConstants.Field.FIELD_PARENT_ACCOUNT_ID, String.class)))
                    .findAny();
            parentAccountCurrentOptional.ifPresent(parent -> {
                String accountPath = getOldAccountPath(parent);
                changeParentAccountPath(parent, needChangeData, needSave);
                if (accountPath.contains(child.getId())) {
                    if (!Strings.isNullOrEmpty(importType)) {
                        setImportErrors(importErrors, child, I18N.text(SFAI18NKeyUtil.SFA_ACCOUNT_CHILDISFATHER),
                                true, objectDataList);
                        allDataList.removeIf(n -> n.getId().equals(child.getId()));
                    } else {
                        throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_ACCOUNT_CHILDISFATHER));
                    }
                    recursionChangeData(allDataList, child, needChangeData, needSave, importType, importErrors,
                            0, objectDataList);
                    return;
                }
                String newAccountPath = accountPath + child.getId();
                if (newAccountPath.split("\\.").length > 10) {
                    if (!Strings.isNullOrEmpty(importType)) {
                        setImportErrors(importErrors, child, I18N.text(SFAI18NKeyUtil.SFA_ACCOUNT_LEVELCANNOTEXCEEDTEN),
                                true, objectDataList);
                        allDataList.removeIf(n -> n.getId().equals(child.getId()));
                    } else {
                        throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_ACCOUNT_LEVELCANNOTEXCEEDTEN));
                    }
                    recursionChangeData(allDataList, child, needChangeData, needSave, importType, importErrors,
                            0, objectDataList);
                    return;
                }
                child.set(AccountConstants.Field.FIELD_ACCOUNT_PATH, newAccountPath);
                if (needSave) {
                    needChangeData.put(child.getId(), child);
                }
                recursionChangeData(allDataList, child, needChangeData, needSave, importType, importErrors,
                        0, objectDataList);
                return;
            });
            if (!parentAccountCurrentOptional.isPresent()) {
                List<IObjectData> parentObect = AccountPathUtil.getAccountListByIdWithDeleted(user, accountDescribe,
                        Lists.newArrayList(child.get(AccountConstants.Field.FIELD_PARENT_ACCOUNT_ID, String.class)));
                if (CollectionUtils.notEmpty(parentObect)) {
                    changeParentAccountPath(parentObect.get(0), needChangeData, needSave);
                    allDataList.addAll(parentObect);
                    recursionChangeData(allDataList, parentObect.get(0), needChangeData, needSave, importType, importErrors,
                            0, objectDataList);
                    continue;
                }
                child.set(AccountConstants.Field.FIELD_ACCOUNT_PATH, child.getId());
                if (needSave) {
                    needChangeData.put(child.getId(), child);
                }
                recursionChangeData(allDataList, child, needChangeData, needSave, importType, importErrors,
                        0, objectDataList);
                continue;
            }
        }
    }

    @NotNull
    private static String getOldAccountPath(IObjectData parent) {
        String accountPath = parent.get(AccountConstants.Field.FIELD_ACCOUNT_PATH, String.class) + ".";
        if ("invalid".equals(parent.get("life_status", String.class)) || Boolean.TRUE.equals(parent.isDeleted())) {
            accountPath = "";
        } else if (Strings.isNullOrEmpty(parent.get(AccountConstants.Field.FIELD_ACCOUNT_PATH, String.class))) {
            accountPath = parent.getId() + ".";
        }
        return accountPath;
    }

    @NotNull
    private static void changeParentAccountPath(IObjectData parent, Map<String, IObjectData> needChangeData, Boolean needSave) {
        String accountPath = parent.get(AccountConstants.Field.FIELD_ACCOUNT_PATH, String.class);
        if ("invalid".equals(parent.get("life_status", String.class)) || Boolean.TRUE.equals(parent.isDeleted())) {
            return;
        }
        if (Strings.isNullOrEmpty(accountPath)) {
            accountPath = parent.getId();
            parent.set(AccountConstants.Field.FIELD_ACCOUNT_PATH, accountPath);
            if (needSave) {
                needChangeData.put(parent.getId(), parent);
            }
        }
    }

    /**
     * 获取在当前数据列表中算作根节点的数据
     *
     * @param objectDataList
     * @param importType
     * @param importErrors
     * @param allDataList
     * @return
     */
    @Nullable
    private static List<IObjectData> getRootObjectData(List<IObjectData> objectDataList, String importType,
                                                       List<BaseImportAction.ImportError> importErrors,
                                                       List<IObjectData> allDataList) {
        List<IObjectData> rootAccountList = Lists.newArrayList();
        //标记根节点
        Iterator<IObjectData> it = objectDataList.iterator();
        while (it.hasNext()) {
            IObjectData m = it.next();
            if (m.getId().equals(m.get(AccountConstants.Field.FIELD_PARENT_ACCOUNT_ID, String.class))) {
                if (!Strings.isNullOrEmpty(importType)) {
                    setImportErrors(importErrors, m, FS_CRM_DEFOBJ_CHECKED_CAN_NOT_LOOKUP_MYSELF.getMessage(),
                            true, objectDataList);
                    allDataList.removeIf(n -> n.getId().equals(m.getId()));
                } else {
                    throw new ValidateException(FS_CRM_DEFOBJ_CHECKED_CAN_NOT_LOOKUP_MYSELF.getMessage());
                }
            } else if (Strings.isNullOrEmpty(m.get(AccountConstants.Field.FIELD_PARENT_ACCOUNT_ID, String.class)) ||
                    !objectDataList.stream().anyMatch(x -> x.getId().equals(m.get(AccountConstants.Field.FIELD_PARENT_ACCOUNT_ID, String.class)))) {
                rootAccountList.add(m);
            }
        }
        return rootAccountList;
    }


    /**
     * 以当前节点为根节点处理下级节点数据
     *
     * @param parent
     */
    public static void recursionChangeData(List<IObjectData> allDataList, IObjectData parent,
                                           Map<String, IObjectData> needChangeData, Boolean needSave, String importType,
                                           List<BaseImportAction.ImportError> importErrors,
                                           int depth, List<IObjectData> objectDataList) {
        //depth避免无限递归
        if (parent == null || depth > 1000) {
            log.warn("递归循环超过1000次，请注意验证！");
            return;
        }
        depth++;

        List<IObjectData> leafNodeList = allDataList.stream()
                .filter(n -> parent.getId().equals(n.get(AccountConstants.Field.FIELD_PARENT_ACCOUNT_ID, String.class)))
                .collect(Collectors.toList());
        if (CollectionUtils.notEmpty(leafNodeList)) {
            Iterator<IObjectData> leafIt = leafNodeList.iterator();
            while (leafIt.hasNext()) {
                IObjectData child = leafIt.next();
                if ("invalid".equals(child.get("life_status", String.class)) || Boolean.TRUE.equals(child.isDeleted())) {
                    child.set(AccountConstants.Field.FIELD_ACCOUNT_PATH, child.getId());
                    if (needSave) {
                        needChangeData.put(child.getId(), child);
                    }
                    recursionChangeData(allDataList, child, needChangeData, needSave, importType,
                            importErrors, depth, objectDataList);
                    continue;
                }

                if (child.getId().equals(child.get(AccountConstants.Field.FIELD_PARENT_ACCOUNT_ID, String.class))) {
                    if (!Strings.isNullOrEmpty(importType)) {
                        //子节点中存在自关联，说明是脏数据，导入不处理
                        allDataList.removeIf(n -> n.getId().equals(child.getId()));
                    } else {
                        throw new ValidateException(FS_CRM_DEFOBJ_CHECKED_CAN_NOT_LOOKUP_MYSELF.getMessage());
                    }
                    recursionChangeData(allDataList, child, needChangeData, needSave, importType,
                            importErrors, depth, objectDataList);
                    continue;
                }

                String parentAccountPath = getOldAccountPath(parent);

                if (parentAccountPath.contains(child.getId())) {
                    if (!Strings.isNullOrEmpty(importType)) {
                        setImportErrors(importErrors, parent, I18N.text(SFAI18NKeyUtil.SFA_ACCOUNT_CHILDISFATHER),
                                false, objectDataList);
                        allDataList.removeIf(n -> n.getId().equals(child.getId()));
                    } else {
                        throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_ACCOUNT_CHILDISFATHER));
                    }
                    recursionChangeData(allDataList, child, needChangeData, needSave, importType,
                            importErrors, depth, objectDataList);
                    continue;
                }

                String newAccountPath = parentAccountPath + child.getId();
                if (newAccountPath.split("\\.").length > 10) {
                    if (!Strings.isNullOrEmpty(importType)) {
                        child.set(AccountConstants.Field.FIELD_ACCOUNT_PATH, newAccountPath);
                        setImportErrors(importErrors, child, I18N.text(SFAI18NKeyUtil.SFA_ACCOUNT_LEVELCANNOTEXCEEDTEN),
                                false, objectDataList);
                        allDataList.removeIf(n -> n.getId().equals(child.getId()));
                    } else {
                        throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_ACCOUNT_LEVELCANNOTEXCEEDTEN));
                    }
                    recursionChangeData(allDataList, child, needChangeData, needSave, importType,
                            importErrors, depth, objectDataList);
                    continue;
                }

                child.set(AccountConstants.Field.FIELD_ACCOUNT_PATH, newAccountPath);
                if (needSave) {
                    needChangeData.put(child.getId(), child);
                }

                recursionChangeData(allDataList, child, needChangeData, needSave, importType,
                        importErrors, depth, objectDataList);
                continue;
            }
        }
    }

    /**
     * 给导入错误赋值
     *
     * @param importErrors
     * @param child
     * @param text
     * @param isSelf
     * @param objectDataList
     */
    private static void setImportErrors(List<BaseImportAction.ImportError> importErrors, IObjectData child, String text,
                                        boolean isSelf, List<IObjectData> objectDataList) {
        //depth避免无限递归
        if (child == null) {
            return;
        }
        if (isSelf) {
            Integer rowNo = child.get("row_no", Integer.class);
            if (rowNo == null || rowNo == 0) {
                return;
            }
            if (importErrors.stream().anyMatch(x -> rowNo.equals(x.getRowNo()))) {
                return;
            }
            importErrors.add(new BaseImportAction.ImportError(child.get("row_no", Integer.class), text));
        } else {
            String accountPath = child.get(AccountConstants.Field.FIELD_ACCOUNT_PATH, String.class);
            Set<String> parentAccountIds = Sets.newHashSet(accountPath.split("\\."));
            objectDataList.forEach(m -> {
                if (parentAccountIds.contains(m.getId())) {
                    Integer rowNo = m.get("row_no", Integer.class);
                    if (rowNo == null || rowNo == 0) {
                        return;
                    }
                    if (importErrors.stream().anyMatch(x -> rowNo.equals(x.getRowNo()))) {
                        return;
                    }
                    importErrors.add(new BaseImportAction.ImportError(m.get("row_no", Integer.class), text));
                }
            });
        }
    }


    /**
     * 处理新增导入需要处理的数据
     *
     * @param user
     * @param objectData
     */
    public static void dealAccountPath(User user, List<IObjectData> objectData) {
        if (CollectionUtils.empty(objectData)) {
            return;
        }
        List<IObjectData> needDealData = objectData.stream()
                .filter(m -> !Strings.isNullOrEmpty(m.get(AccountConstants.Field.FIELD_PARENT_ACCOUNT_ID, String.class)))
                .collect(Collectors.toList());
        if (CollectionUtils.notEmpty(needDealData)) {
            AccountPathSynchronizer.builder()
                    .user(user)
                    .objectDataList(needDealData)
                    .build()
                    .asyncDealData();
        }
    }

    /**
     * 处理新增导入需要校验的数据
     *
     * @param user
     * @param dataList
     */
    public static List<BaseImportAction.ImportError> checkAccountPath(User user, List<BaseImportDataAction.ImportData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return Lists.newArrayList();
        }
        List<IObjectData> needCheckDataList = Lists.newArrayList();
        dataList.forEach(m -> {
            String parentAccountId = m.getData().get(AccountConstants.Field.FIELD_PARENT_ACCOUNT_ID, String.class);
            if (!Strings.isNullOrEmpty(parentAccountId)) {
                m.getData().set("row_no", m.getRowNo());
                if (Strings.isNullOrEmpty(m.getData().getId())) {
                    m.getData().setId(SERVICE_FACADE.generateId());
                }
                needCheckDataList.add(m.getData());
            }
        });
        if (CollectionUtils.empty(needCheckDataList)) {
            return Lists.newArrayList();
        }
        List<BaseImportAction.ImportError> errorHoopList = AccountPathUtil.checkIsHoopForImport(user,
                "addImport", needCheckDataList);
        return errorHoopList;
    }

    /**
     * 获取描述
     *
     * @param user
     * @return
     */
    public static IObjectDescribe getObjectDescribe(User user) {
        IObjectDescribe objectDescribe = SERVICE_FACADE.findObject(user.getTenantId(), Utils.ACCOUNT_API_NAME);
        if (objectDescribe == null) {
            log.warn("AccountPathUtil.getObjectDescribe>获取描述失败={},{}", user.getTenantId(), Utils.ACCOUNT_API_NAME);
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_ACCOUNT_GETDESCRIPTIONFAILED));
        }
        return objectDescribe;
    }

    /**
     * 处理需要处理的数据
     *
     * @param user
     * @param accountIds
     */
    public static void dealAccountPath(User user, Set<String> accountIds) {
        try {
            if (CollectionUtils.empty(accountIds)) {
                return;
            }
            AccountPathSynchronizer.builder()
                    .user(user)
                    .objectDataIds(Lists.newArrayList(accountIds))
                    .changeType("PatchingData")
                    .build()
                    .asyncDealData();
        } catch (Exception e) {
            log.warn("AccountPathUtil dealAccountPath error,user {}，accountIds {}, {}", user, accountIds, e);
        }
    }
}
