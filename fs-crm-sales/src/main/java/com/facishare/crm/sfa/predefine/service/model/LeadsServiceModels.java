package com.facishare.crm.sfa.predefine.service.model;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

public interface LeadsServiceModels {
    @Data
    class ProcessLeadsArg {
       private String leadsId;
       private String employeeId;
       private String  createTime;
    }
    @Data
    @Builder
    class ProcessLeadsResult {

    }

    @Data
    @Builder
    class GetLeadsLimitInfoResult {
        String limitNumber;
        String leadsCount;
        String limitInfoMessage;
        Boolean hasMultipleRules;
    }

    @Data
    class GetLeadsCountByLimitRuleArg {
        String dataId;
    }

    @Data
    @Builder
    class GetLeadsCountByLimitRuleResult {
        Integer totalCount;
    }

    @Data
    class BulkFindValidRecordTypeListArg {
        List<String> objectApiNameList;
    }

    @Data
    @Builder
    class BulkFindValidRecordTypeListResult {
        Map<String, List<Map>> recordTypeList;
    }

    @Data
    class GetWeChatUserDataArg {
        private String apiName;
        private String weChatUserId;
    }

    @Data
    @Builder
    class GetWeChatUserDataResult {
        private boolean hasData;
    }
}
