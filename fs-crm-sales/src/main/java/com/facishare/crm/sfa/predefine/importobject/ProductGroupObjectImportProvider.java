package com.facishare.crm.sfa.predefine.importobject;

import com.facishare.crm.sfa.predefine.service.ModuleCtrlConfigService;
import com.facishare.crm.sfa.predefine.service.modulectrl.IModuleInitService;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.crm.sfa.utilities.util.SoCommonUtils;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.importobject.DefaultObjectImportProvider;
import com.facishare.paas.appframework.metadata.importobject.ImportObject;
import com.facishare.paas.appframework.metadata.importobject.ImportType;
import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.facishare.crm.openapi.Utils.PRODUCT_GROUP_API_NAME;

/**
 * <AUTHOR>
 * @date 2019/12/9 4:34 下午
 */
@Component
public class ProductGroupObjectImportProvider extends DefaultObjectImportProvider {

    @Override
    public String getObjectCode() {
        return PRODUCT_GROUP_API_NAME;
    }

    @Override
    public Optional<ImportObject> getImportObject(IObjectDescribe objectDescribe, IUniqueRule uniqueRule) {
        return Optional.empty();
    }

    @Override
    protected boolean getIsNotSupportSaleEvent(IObjectDescribe objectDescribe) {
        return true;
    }

    @Override
    protected ImportType getImportType(IObjectDescribe objectDescribe, IUniqueRule uniqueRule) {
        return ImportType.UNSUPPORT_UPDATE_IMPORT;
    }

}
