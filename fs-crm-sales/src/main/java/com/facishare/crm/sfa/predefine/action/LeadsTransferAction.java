package com.facishare.crm.sfa.predefine.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.exception.SFABusinessException;
import com.facishare.crm.sfa.predefine.exception.SFAErrorCode;
import com.facishare.crm.sfa.predefine.service.LeadsObjTransferService;
import com.facishare.crm.sfa.predefine.service.model.LeadsObjTransferModel;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.crm.sfa.utilities.util.LeadsUtils;
import com.facishare.crmcommon.util.SearchUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.predef.action.BaseObjectApprovalAction;
import com.facishare.paas.appframework.flow.ApprovalFlowStartResult;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Slf4j
public class LeadsTransferAction extends BaseObjectApprovalAction<LeadsTransferAction.Arg,LeadsTransferAction.Result> {
    protected LeadsObjTransferService transferService = SpringUtil.getContext().getBean(LeadsObjTransferService.class);
    private ServiceFacade serviceFacade =SpringUtil.getContext().getBean(ServiceFacade.class);
    private IObjectData leadsData ;
    ServiceContext serviceContext ;
    private static final IObjectDataService objectDataService = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.TRANSFER.getActionCode());
    }

    @Override
    protected List<String> getDataPrivilegeIds(LeadsTransferAction.Arg arg) {
        return Lists.newArrayList(arg.getLeadsId());
    }


    @Override
    protected void doFunPrivilegeCheck() {
        if(arg.isSkipFunctionCheck()){
            return;
        }
        super.doFunPrivilegeCheck();
    }

    @Override
    protected void doDataPrivilegeCheck() {
        if(arg.isSkipFunctionCheck()){
            return;
        }
        super.doDataPrivilegeCheck();
    }

    @Override
    protected void before(LeadsTransferAction.Arg arg) {
        super.before(arg);
        serviceContext = new ServiceContext(actionContext.getRequestContext(), "leadsobj_transfer", "transfer");
        if (CollectionUtils.notEmpty(dataList)) {
            this.leadsData = dataList.get(0);
        } else {
            this.leadsData = serviceFacade.findObjectData(actionContext.getTenantId(), arg.getLeadsId(), objectDescribe);
        }
        validate(arg);
    }

    @Override
    protected LeadsTransferAction.Result buildValidateResult() {
        return super.buildValidateResult();
    }

    private void validate(LeadsTransferAction.Arg arg) {
        transferService.validateMustTransferObjects(serviceContext, arg.getDataList());
        transferService.validateNewOpportunityName(serviceContext, arg.getDataList());
    }

    @Override
    @Transactional
    protected LeadsTransferAction.Result doAct(LeadsTransferAction.Arg arg) {
        if(needTriggerApprovalFlow()) {
            Map<String, ObjectDataDocument> processedDataList = Maps.newHashMap();
            for(Map.Entry<String, ObjectDataDocument> transferData : arg.getDataList().entrySet()){
                ObjectDataDocument objectDataDocument = transferData.getValue();
                if(objectDataDocument != null && !objectDataDocument.isEmpty()) {
                    IObjectDescribe transferObjectDescribe = serviceFacade.findObject(actionContext.getTenantId(), transferData.getKey());
                    if(transferObjectDescribe != null){
                        IObjectData transferObjectData = transferData.getValue().toObjectData();
                        transferObjectData.setTenantId(actionContext.getTenantId());
                        serviceFacade.processData(transferObjectDescribe, Lists.newArrayList(transferObjectData));
                        processedDataList.put(transferData.getKey(), ObjectDataDocument.of(transferObjectData));
                    }
                }
            }

            arg.getDataList().putAll(processedDataList);

            if (dataList.size() > 1) {
                this.startApprovalFlowAsynchronous(this.dataList, ApprovalFlowTriggerType.TRANSFER, approvalFlowTriggerMap(), approvalCallBackMap());
                return LeadsTransferAction.Result.builder().build();
            }

            Map<String, ApprovalFlowStartResult> resultMap = this.startApprovalFlow(this.dataList, ApprovalFlowTriggerType.TRANSFER.getId(), approvalFlowTriggerMap(), approvalCallBackMap());
            if (!ApprovalFlowStartResult.APPROVAL_NOT_EXIST.equals(resultMap.getOrDefault(arg.getLeadsId(), ApprovalFlowStartResult.APPROVAL_NOT_EXIST))) {
                return LeadsTransferAction.Result.builder().build();
            }
        }

        LeadsObjTransferModel.Result result = transferService.transfer(serviceContext, leadsData,objectDescribe,arg.getDataList(),arg.isPutTeamMembersIntoCustomer(),arg.isCombineCRMFeed());
        return Result.builder().contactData(result.getContactData()).build();
    }

    @Override
    protected LeadsTransferAction.Result after(LeadsTransferAction.Arg arg, LeadsTransferAction.Result result) {
        if (isApprovalFlowStartSuccessOrAsynchronous(getDataPrivilegeIds(arg).get(0))) {
            return result;
        }

        result = super.after(arg, result);
        addFlowRecord();
        return result;
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.TRANSFER.getButtonApiName();
    }

    @Override
    protected IObjectData getPreObjectData() {
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIdsIncludeDeleted(actionContext.getUser(), Lists.newArrayList(arg.getLeadsId()), actionContext.getObjectApiName());
        if (objectDataList != null && objectDataList.size() != 0) {
            return objectDataList.get(0);
        }
        return null;
    }

    @Override
    protected IObjectData getPostObjectData() {
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIdsIncludeDeleted(actionContext.getUser(), Lists.newArrayList(arg.getLeadsId()), actionContext.getObjectApiName());
        if (objectDataList != null && objectDataList.size() != 0) {
            return objectDataList.get(0);
        }
        return null;
    }

    @Override
    protected boolean skipPreFunction() {
        return arg.isSkipPreAction() ||  super.skipPreFunction();
    }

    @Override
    protected boolean skipCheckButtonConditions() {
        return arg.isSkipButtonConditions() || super.skipCheckButtonConditions();
    }

    private void addFlowRecord() {
        List<IObjectData> oldFlowRecordDataList = Lists.newArrayList();
        for (IObjectData leadsData : dataList) {
            String oldOwnerId = LeadsUtils.getOwner(leadsData);
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            searchTemplateQuery.setLimit(1);
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, "leads_owner", oldOwnerId);
            SearchUtil.fillFilterEq(filters, "leads_id", leadsData.getId());
            searchTemplateQuery.setFilters(filters);
            List<OrderBy> orderByList = Lists.newArrayList();
            orderByList.add(new OrderBy("last_modified_time", false));
            searchTemplateQuery.setOrders(orderByList);
            searchTemplateQuery.setNeedReturnCountNum(false);
            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(actionContext.getUser(), SFAPreDefineObject.LeadsFlowRecord.getApiName(), searchTemplateQuery);
            if(queryResult != null && CollectionUtils.notEmpty(queryResult.getData())) {
                IObjectData oldFlowRecordData = queryResult.getData().get(0);
                oldFlowRecordData.set("leads_status", "transformed");
                oldFlowRecordData.set("leads_status_changed_time", System.currentTimeMillis());
                oldFlowRecordData.set("leads_stage", leadsData.get("leads_stage", String.class));
                oldFlowRecordData.set("leads_stage_changed_time", leadsData.get("leads_stage_changed_time"));
                oldFlowRecordData.set("account_id", leadsData.get("account_id", String.class));
                oldFlowRecordData.set("contact_id", leadsData.get("contact_id", String.class));
                oldFlowRecordData.set("new_opportunity_id", leadsData.get("new_opportunity_id", String.class));
                oldFlowRecordData.set("opportunity_id", leadsData.get("opportunity_id", String.class));
                oldFlowRecordData.set("last_modified_by", Lists.newArrayList(actionContext.getUser().getUserId()));
                oldFlowRecordData.set("last_modified_time", System.currentTimeMillis());
                oldFlowRecordDataList.add(oldFlowRecordData);
            }
        }
        if(CollectionUtils.notEmpty(oldFlowRecordDataList)) {
            List<String> updateFieldList = Lists.newArrayList("leads_status", "leads_status_changed_time", "leads_stage", "leads_stage_changed_time",
                    "account_id", "contact_id", "new_opportunity_id", "opportunity_id", "last_modified_by", "last_modified_time");
            try {
                objectDataService.batchUpdateWithField(oldFlowRecordDataList, updateFieldList, AccountUtil.getDefaultActionContext(actionContext.getUser(), ""));
            } catch (MetadataServiceException metadataError) {
                log.info("addFlowRecord warn", metadataError);
                throw new SFABusinessException(metadataError.getMessage(), SFAErrorCode.ACCOUNT_COMMON_ERROR);
            } catch (Exception e) {
                log.error("addFlowRecord error", e);
                throw new SFABusinessException(SFAErrorCode.ACCOUNT_COMMON_ERROR);
            }
        }
    }

    protected Map<String, Map<String, Object>> approvalFlowTriggerMap() {
        Map<String, Map<String, Object>> result = Maps.newHashMap();
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put("leadsId", arg.getLeadsId());
        List<String> transferApiNames = Lists.newArrayList();
        for(Map.Entry<String, ObjectDataDocument> transferData : arg.getDataList().entrySet()){
            ObjectDataDocument objectDataDocument = transferData.getValue();
            if(objectDataDocument != null && !objectDataDocument.isEmpty()) {
                transferApiNames.add(transferData.getKey());
            }
        }
        dataMap.put("transferApiNames", transferApiNames);
        result.put(arg.getLeadsId(), dataMap);
        return result;
    }

    protected Map<String, Map<String, Object>> approvalCallBackMap() {
        Map<String, Map<String, Object>> result=Maps.newHashMap();
        Map<String, Object> callbackData = Maps.newHashMap();
        callbackData.put("leadsId", arg.getLeadsId());
        callbackData.put("dataList", arg.getDataList());
        callbackData.put("combineCRMFeed", arg.isCombineCRMFeed());
        callbackData.put("putTeamMembersIntoCustomer", arg.isPutTeamMembersIntoCustomer());
        callbackData.put("useValidationRule", arg.isUseValidationRule());
        callbackData.put("addAccountInteractiveRecord", arg.isAddAccountInteractiveRecord());
        result.put(arg.getLeadsId(), callbackData);
        return result;
    }

    @Override
    protected Map<String, Object> getArgs() {
        Map<String, Object> result = Maps.newHashMap();
        result.put("leadsId", arg.getLeadsId());
        result.put("combineCRMFeed", arg.isCombineCRMFeed());
        result.put("putTeamMembersIntoCustomer", arg.isPutTeamMembersIntoCustomer());
        result.put("useValidationRule", arg.isUseValidationRule());
        result.put("addAccountInteractiveRecord", arg.isAddAccountInteractiveRecord());
        result.put("dataList", arg.getDataList());
        return result;
    }

    @Data
    @NoArgsConstructor
    static class Arg implements Serializable {
        @JSONField(name = "M1")
        private String leadsId;
        @JSONField(name = "M2")
        private Map<String, ObjectDataDocument> dataList;
        @JSONField(name = "M3")
        private boolean combineCRMFeed;
        @JSONField(name = "M4")
        private boolean putTeamMembersIntoCustomer;
        @JSONField(name = "M5")
        private boolean useValidationRule;
        @JSONField(name = "M6")
        private boolean addAccountInteractiveRecord;
        private boolean skipTriggerApprovalFlow;
        private boolean skipFunctionCheck;
        private boolean skipPreAction;
        private boolean skipButtonConditions;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        @JSONField(name = "M1")
        private  Map<String, Object> contactData;
    }
}
