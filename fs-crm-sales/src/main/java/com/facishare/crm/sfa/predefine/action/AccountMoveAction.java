package com.facishare.crm.sfa.predefine.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crmcommon.constants.SystemConstants;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.model.SFAObjectPoolCommon;
import com.facishare.crm.sfa.predefine.enums.ActionCodeEnum;
import com.facishare.crm.sfa.predefine.service.model.SFALogModels;
import com.facishare.crm.sfa.utilities.constant.AccountConstants;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.crm.sfa.utilities.util.ObjectLimitUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.common.util.AppIdMapping;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.appframework.prm.util.PrmConstant;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.*;

/**
 * Created by yuanjl on 2018/7/18.
 */
@Slf4j
public class AccountMoveAction extends SFAMoveAction {
    @Override
    protected void doFunPrivilegeCheck() {
    }

    @Override
    protected void before(SFAObjectPoolCommon.Arg arg) {
        super.before(arg);
        if (dataList.stream().anyMatch(x -> "-1".equals(x.get("is_deleted")))) {
            throw new ValidateException(I18N.text(I18NKey.action_delete));
        }
        if (dataList.stream().anyMatch(x -> ObjectLifeStatus.INVALID.getCode().equals(AccountUtil.getStringValue(x, "life_status", "")))) {
            throw new ValidateException(I18N.text(I18NKey.action_invalid));
        }
        if (dataList.stream().anyMatch(x -> ObjectLifeStatus.INEFFECTIVE.getCode().equals(AccountUtil.getStringValue(x, "life_status", "")))) {
            throw new ValidateException(String.format(I18N.text(SFA_FORBID_TRANSFERRING_EFFECTIVE), I18N.text("HighSeasObj.attribute.self.display_name"), I18N.text("AccountObj.attribute.self.display_name")));
        }
        if (dataList.stream().anyMatch(x -> ObjectLifeStatus.UNDER_REVIEW.getCode().equals(AccountUtil.getStringValue(x, "life_status", "")))) {
            throw new ValidateException(String.format(I18N.text(SFA_FORBID_REPORTING_TRANSFERRING_EFFECTIVE), I18N.text("AccountObj.attribute.self.display_name"), I18N.text("HighSeasObj.attribute.self.display_name")));
        }

        boolean cleanOwner = AccountUtil.getBooleanValue(objectPoolData, "is_clean_owner", false);
        if (!cleanOwner) {
            Set<String> ownerList = dataList.stream().filter(x ->
                    AccountUtil.hasOwner(x)).map(x -> AccountUtil.getOwner(x)).collect(Collectors.toSet());
            if (CollectionUtils.notEmpty(ownerList)) {
                ownerList.forEach(owner -> {
                    List<IObjectData> tempDataList = dataList.stream().filter(x -> AccountUtil.getOwner(x).equals(owner)).collect(Collectors.toList());
                    if (CollectionUtils.notEmpty(tempDataList)) {
                        AccountUtil.checkPoolAccountLimit(actionContext.getUser(), owner, arg.getObjectPoolId(), tempDataList);
                        if (ObjectLimitUtil.isGrayAccountLimit(actionContext.getTenantId())) {
                            List<IObjectData> checkLimitDataList = ObjectDataExt.copyList(tempDataList);
                            List<IObjectData> oldDataList = ObjectDataExt.copyList(tempDataList);
                            checkLimitDataList.forEach(x -> {
                                x.set(AccountConstants.Field.HIGH_SEAS_ID, arg.getObjectPoolId());
                                x.set("claimed_time", System.currentTimeMillis());
                                x.setLastModifiedBy(actionContext.getUser().getUserId());
                                x.setLastModifiedTime(System.currentTimeMillis());
                            });

                            ObjectLimitUtil.CheckLimitResult checkLimitResult = ObjectLimitUtil.checkObjectLimitForEdit(actionContext.getUser(), actionContext.getObjectApiName(), owner, oldDataList, checkLimitDataList, objectDescribe);
                            if (CollectionUtils.notEmpty(checkLimitResult.getFailureIds())) {
                                throw new ValidateException(String.format(I18N.text(SFA_REACH_LIMIT_OBJ),
                                        I18N.text("AccountObj.attribute.self.display_name")));
                            }
                        }
                    }
                });
            }

            if (ObjectLimitUtil.isGrayAccountLimit(actionContext.getTenantId())
                    && AppIdMapping.appIdMapping.get(PrmConstant.PRM_APP_ID).equals(actionContext.getAppId())) {
                Set<String> outTenantIds = dataList.stream().filter(x ->
                        StringUtils.isNotBlank(AccountUtil.getStringValue(x, SystemConstants.Field.OutTenantId.apiName, "")))
                        .map(x -> AccountUtil.getStringValue(x, SystemConstants.Field.OutTenantId.apiName, "")).collect(Collectors.toSet());
                if (CollectionUtils.notEmpty(outTenantIds)) {
                    for (String outTenantId : outTenantIds) {
                        List<IObjectData> tempDataList = dataList.stream().filter(x -> AccountUtil.getStringValue(x, SystemConstants.Field.OutTenantId.apiName, "").equals(outTenantId)).collect(Collectors.toList());
                        if (CollectionUtils.empty(tempDataList)) {
                            continue;
                        }
                        Set<String> outOwnerList = tempDataList.stream().filter(x ->
                                StringUtils.isNotBlank(AccountUtil.getOutOwner(x)))
                                .map(x -> AccountUtil.getOutOwner(x)).collect(Collectors.toSet());
                        if (CollectionUtils.empty(outOwnerList)) {
                            continue;
                        }

                        outOwnerList.forEach(outOwner -> {
                            List<IObjectData> outOwnerDataList = tempDataList.stream().filter(x -> AccountUtil.getOutOwner(x).equals(outOwner)).collect(Collectors.toList());
                            if (CollectionUtils.notEmpty(outOwnerDataList)) {
                                List<IObjectData> checkLimitDataList = ObjectDataExt.copyList(outOwnerDataList);
                                List<IObjectData> oldDataList = ObjectDataExt.copyList(outOwnerDataList);
                                checkLimitDataList.forEach(x -> {
                                    x.set(AccountConstants.Field.HIGH_SEAS_ID, arg.getObjectPoolId());
                                    x.set("claimed_time", System.currentTimeMillis());
                                    x.setLastModifiedBy(actionContext.getUser().getUserId());
                                    x.setLastModifiedTime(System.currentTimeMillis());
                                });
                                ObjectLimitUtil.CheckLimitResult checkOutLimitResult = ObjectLimitUtil.checkOutUserObjectLimitForEdit(actionContext.getUser(), actionContext.getObjectApiName(), outTenantId, outOwner, oldDataList, checkLimitDataList, objectDescribe, true);
                                if (CollectionUtils.notEmpty(checkOutLimitResult.getFailureIds())) {
                                    throw new ValidateException(String.format(I18N.text(SFA_REACH_LIMIT_OBJ),
                                            I18N.text("AccountObj.attribute.self.display_name")));
                                }
                            }
                        });
                    }
                }
            }
        }
    }

    @Override
    protected void asyncAfter(SFAObjectPoolCommon.Arg arg, SFAObjectPoolCommon.Result result) {
        super.asyncAfter(arg, result);
        sendActionMq();
        if (CollectionUtils.notEmpty(result.getSuccessList())){
            for (String s : result.getSuccessList()) {
                recalculateTaskService.send(actionContext.getTenantId(), s, Utils.ACCOUNT_API_NAME, ActionCodeEnum.MOVE);
            }
        }
        return;
    }

    @Override
    protected void addLog() {
        List<String> poolIds = AccountUtil.getPoolIds(dataList);
        List<IObjectData> poolObjectList = Lists.newArrayList();
        if (CollectionUtils.notEmpty(poolIds)) {
            poolObjectList = objectPoolService.getObjectPoolByIds(actionContext.getObjectApiName(),
                    actionContext.getTenantId(), poolIds);
        }

        for (IObjectData objectData : dataList) {
            String oriPoolId = AccountUtil.getPoolId(objectData);
            IObjectData oriPoolOjbectData = null;
            if (StringUtils.isNotEmpty(oriPoolId) && CollectionUtils.notEmpty(poolObjectList)) {
                Optional<IObjectData> tempData = poolObjectList.stream().filter(x -> oriPoolId.equals(x.getId())).findFirst();
                if (tempData.isPresent()) {
                    oriPoolOjbectData = tempData.get();
                }
            }
            String messageContent = "";
            if (oriPoolOjbectData == null) {
                messageContent = String.format(I18N.text(SFA_FROM_HS_TO_NHS),
                        I18N.text("HighSeasObj.attribute.self.display_name"),
                        I18N.text("HighSeasObj.attribute.self.display_name")
                ) + objectPoolData.getName();
            } else {
                messageContent = String.format(I18N.text(SFA_FROM), I18N.text("HighSeasObj.attribute.self.display_name")) + oriPoolOjbectData.getName() + String.format(I18N.text(SFA_TO), I18N.text("HighSeasObj.attribute.self.display_name")) + objectPoolData.getName();
            }

            serviceFacade.logWithCustomMessage(actionContext.getUser(), EventType.MODIFY, ActionType.MOVE, objectDescribe, Lists.newArrayList(objectData), "," + messageContent);

            SFALogModels.SFALogEntity logEntity = sfaLogService.buildLogEntity(objectPoolData,
                    String.format("%s ", I18N.text("AccountObj.attribute.self.display_name")) + objectData.getName() + "," + messageContent,
                    false);
            List<SFALogModels.SFALogTextMessage> textMessageList = Lists.newArrayList();
            sfaLogService.appendNameLinkLogTextMessage(actionContext.getObjectApiName(), objectData, textMessageList);
            sfaLogService.appendLogTextMessage(actionContext.getObjectApiName(), objectData,
                    SFALogModels.LogLinkType.NO_LINK, messageContent, textMessageList);

            logEntity.setLogTextMessageList(textMessageList);
            sfaLogService.addLog(actionContext.getUser(), logEntity, "HighSeasLog",
                    SFALogModels.LogOperationType.MOVE_HIGH_SEAS_CUSTOMER);

//            sfaLogService.addLog(actionContext.getUser(),logEntity, SFAPreDefineObject.Account.getApiName(),
//                    SFALogModels.LogOperationType.MOVE_HIGH_SEAS_CUSTOMER);

        }
    }

    private void sendActionMq() {
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIdsIncludeDeleted(actionContext.getUser(), arg.getObjectIDs(), actionContext.getObjectApiName());
        boolean isCleanOwner = AccountUtil.getBooleanValue(objectPoolData, "is_clean_owner", false);
        MoveHighSeasActionContent actionContent = MoveHighSeasActionContent.builder()
                .accountIds(arg.getObjectIDs()).poolName(objectPoolData.getName()).build();
        if (objectDataList.size() > 1) {
            sfaOpenApiMqService.sendOpenApiMq(actionContext.getUser(), "Transfer",
                    "HighSeasObj", arg.getObjectPoolId(), actionContent);
        } else {
            if (objectDataList.size() <= 0) {
                return;
            }
            IObjectData objectData = objectDataList.get(0);
            actionContent.setCustomerName(objectData.getName());
            AccountConstants.AccountBizStatus bizStatus = AccountConstants.AccountBizStatus.of(
                    AccountUtil.getStringValue(objectData, AccountConstants.Field.BIZ_STATUS, AccountConstants.AccountBizStatus.UN_ALLOCATED.getValue()));
            actionContent.setAccountStatus(bizStatus.getValue());
            actionContent.setAccountStatusDescription(bizStatus.getLabel());
            actionContent.setHouseNo(AccountUtil.getStringValue(objectData, AccountConstants.Field.ADDRESS, ""));

            sfaOpenApiMqService.sendOpenApiMq(actionContext.getUser(), "Transfer",
                    "HighSeasObj", arg.getObjectPoolId(), actionContent);
        }

        if (isCleanOwner) {
            serviceFacade.sendActionMq(this.actionContext.getUser(), AccountUtil.fillOldData(objectDataList, dataList), ObjectAction.TAKE_BACK);

//            for(IObjectData objectData : objectDataList){
//                ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
//                List<String> memberIds = objectDataExt.getTeamMembers().stream().map(x -> x.getEmployee()).collect(Collectors.toList());
//                AccountAllocateAction.TrustChangeAdminEvent trustChangeAdminEvent = AccountAllocateAction.TrustChangeAdminEvent.builder()
//                        .accountId(objectData.getId()).ea(gdsHandler.getEAByEI(actionContext.getTenantId()))
//                        .name(objectData.getName()).objectApiName(actionContext.getObjectApiName())
//                        .owner("0").teamMemberIds(memberIds).build();
//                AccountReturnAction.ReturnActionContent returnActionContent = AccountReturnAction.ReturnActionContent.builder()
//                        .accountId(objectData.getId()).name(objectData.getName())
//                        .trustChangeAdminEvent(trustChangeAdminEvent).build();
//                sfaOpenApiMqService.sendOpenApiMq(actionContext.getUser(), ObjectAction.TAKE_BACK.getActionCode(),
//                        actionContext.getObjectApiName(), objectData.getId(), returnActionContent);
//            }
        }
    }

    @Data
    @Builder
    static class MoveHighSeasActionContent {
        @JSONField(name = "Name")
        String poolName;
        @JSONField(name = "CustomerName")
        String customerName;
        @JSONField(name = "CustomerStatus")
        String accountStatus;
        @JSONField(name = "CustomerStatusDescription")
        String accountStatusDescription;
        @JSONField(name = "HouseNo")
        String houseNo;
        @JSONField(name = "CustomerIds")
        List<String> accountIds;
    }
}