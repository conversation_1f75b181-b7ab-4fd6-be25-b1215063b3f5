package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.model.Enum.SessionBOCItemKeys;
import com.facishare.crm.sfa.predefine.action.listener.ReturnedGoodsInvoiceEditActionListener;
import com.facishare.crm.sfa.predefine.service.QiXinTodoService;
import com.facishare.crmcommon.constants.ReturnedGoodsInvoiceConstants;
import com.facishare.crm.sfa.utilities.validator.ReturnedGoodsInvoiceValidator;
import com.facishare.crm.sfa.utilities.validator.SalesOrderValidator;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ActionListener;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.appframework.flow.ApprovalFlowStartResult;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.common.Strings;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by luxin on 2018/1/2.
 */
@Slf4j
public class ReturnedGoodsInvoiceEditAction extends StandardEditAction {
    private static final QiXinTodoService qiXinTodoService = SpringUtil.getContext().getBean(QiXinTodoService.class);
    private List<Integer> approverIds = Lists.newArrayList();
    private boolean restartApprovalFlow = false;
    private String newWorkFlowId;

    @Override
    public void before(Arg arg) {
        super.before(arg);
        String accountId = objectData.get(ReturnedGoodsInvoiceConstants.ReturnedGoodsInvoiceField.ACCOUNT_ID.getApiName(), String.class);
        String salesOrderId = objectData.get(ReturnedGoodsInvoiceConstants.ReturnedGoodsInvoiceField.ORDER_ID.getApiName(), String.class);
        ReturnedGoodsInvoiceValidator.validateSalesOrder(actionContext.getTenantId(), accountId, salesOrderId);
        ReturnedGoodsInvoiceValidator.validateReturnedGoodsInvoiceProduct(actionContext.getTenantId(), salesOrderId, detailObjectData, actionContext.isFromOpenAPI());

        ObjectDataExt objectDataExt = ObjectDataExt.of(dbMasterData);
        ObjectLifeStatus lifeStatus = objectDataExt.getLifeStatus();
        objectData.set("life_status", lifeStatus.getCode());

        Boolean requiredApproval = ReturnedGoodsInvoiceValidator.requiredApprovalFlow(actionContext);
        if (Objects.equals(Boolean.TRUE, requiredApproval)) {
            if (lifeStatus == ObjectLifeStatus.INEFFECTIVE) {
                String workFlowId = dbMasterData.get("work_flow_id", String.class);
                if (!Strings.isNullOrEmpty(workFlowId)) {
                    restartApprovalFlow = true;
                    ReturnedGoodsInvoiceValidator.validateWorkFlow(actionContext, objectData,
                            objectData.getOwner().get(0), true);
                    approverIds = (List<Integer>) objectData.get("approver");
                    newWorkFlowId = objectData.get("work_flow_id", String.class);
                }
            }
        }
    }


    @Override
    protected boolean needTriggerApprovalFlow() {
        if (SalesOrderValidator.softApprovalFlow(actionContext)) {
            return false;
        }
        return super.needTriggerApprovalFlow();
    }

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        //成功触发审批流不需要执行后续操作
        if (isEditApprovalFlowStartSuccess()) {
            return result;
        }

        if (restartApprovalFlow) {
            objectData.set("life_status", ObjectLifeStatus.UNDER_REVIEW.getCode());
            objectData.set("current_level", 0);
            objectData.set("work_flow_id", newWorkFlowId);
            List<String> toUpdateFields = Lists.newArrayList("current_level", "life_status", "work_flow_id");
            serviceFacade.batchUpdateByFields(actionContext.getUser(), Lists.newArrayList(objectData), toUpdateFields);
        }
        if (CollectionUtils.notEmpty(approverIds)) {
            List<String> strIds = Lists.newArrayList();
            approverIds.forEach(x -> strIds.add(x.toString()));
            qiXinTodoService.sendTodo(actionContext.getTenantId(), SessionBOCItemKeys.TobeConfirmedReturnOrder,
                    actionContext.getObjectApiName(), objectData.getId(), actionContext.getUser().getUserId(), strIds);
        }

        return result;
    }

    @Override
    protected Map<String, ApprovalFlowStartResult> startApprovalFlow(List<IObjectData> objectDataList,
                                                                     ApprovalFlowTriggerType approvalFlowTriggerType,
                                                                     Map<String, Map<String, Object>> updatedFieldMap) {
        if (restartApprovalFlow && approvalFlowTriggerType == ApprovalFlowTriggerType.CREATE) {
            Map<String, ApprovalFlowStartResult> map = Maps.newHashMap();
            map.put(objectData.getId(), ApprovalFlowStartResult.FAILED);
            return map;
        } else {
            return super.startApprovalFlow(objectDataList, approvalFlowTriggerType, updatedFieldMap);
        }
    }

    @Override
    public List<Class<? extends ActionListener<Arg, Result>>> getActionListenerClassList() {
        List<Class<? extends ActionListener<Arg, Result>>> classList = super.getActionListenerClassList();
        classList.add(ReturnedGoodsInvoiceEditActionListener.class);
        return classList;
    }

    @Override
    protected void tryTriggerMasterApproval(ApprovalFlowTriggerType triggerType, ObjectAction action,
                                            IObjectData objectData, Map<String, Object> updateData,
                                            Map<String, IObjectDescribe> describeMap,
                                            Map<String, Object> freeApprovalDef) {
        if (!ReturnedGoodsInvoiceValidator.requiredApprovalFlow(actionContext)) {
            super.tryTriggerMasterApproval(triggerType, action, objectData, updateData, describeMap, freeApprovalDef);
        }
    }
}

