package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.util.JsonObjectUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;

/**
 * <AUTHOR>
 * @date 2018/11/13 11:35
 */
public class SpecificationDescribeLayoutController extends SODescribeLayoutController {
    @Override
    protected Result doService(Arg arg) {
        Result result = super.doService(arg);
//        if ("edit".equals(arg.getLayout_type())){
//            result = JsonObjectUtils.update(result,Result.class,"$.layout.components[?(@.api_name=='form_component')]" +
//                    ".field_section[?(@.api_name=='base_field_section__c')].form_fields[?(@.field_name=='name')].is_readonly",true);
//        }
        return result;
    }
}
