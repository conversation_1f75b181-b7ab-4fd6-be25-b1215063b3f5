package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.util.InvisibleFieldUtil;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;

import java.util.List;

/**
 * Created by luxin on 2018/1/26.
 */
public class SFARelatedListController extends StandardRelatedListController {

    @Override
    protected QueryResult<IObjectData> findData(SearchTemplateQuery query) {
        //选择对象列表走PG，相关对象列表走REST
        //String dbType = Strings.isEmpty(arg.getTargetObjectDataId())  ? "pg" : "rest";
        IActionContext actionContext = ActionContextExt.of(this.controllerContext.getUser()).dbType("pg").getContext();
        return this.serviceFacade.findBySearchQuery(actionContext, this.objectDescribe.getApiName(), query);
    }

    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        return super.buildSearchTemplateQuery();
    }

    @Override
    protected Result after(Arg arg, Result result) {

        super.after(arg, result);
        List<IObjectData> objectDataList = ObjectDataDocument.ofDataList(result.getDataList());
        //TeamMemberUtil.handleTeamMember(controllerContext.getTenantId(), controllerContext.getUser().getUserId(), objectDataList);
        InvisibleFieldUtil.handleInvisibleFields(controllerContext.getUser(),
                controllerContext.getObjectApiName(), objectDataList);
        return result;
    }
}
