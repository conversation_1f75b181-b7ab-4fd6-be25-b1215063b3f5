package com.facishare.crm.sfa.predefine.importobject;


import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.metadata.importobject.DefaultObjectImportProvider;
import com.facishare.paas.appframework.metadata.importobject.ImportObject;
import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @instruction
 */
@Component
public class ProductConstraintLinesObjectImportProvider extends DefaultObjectImportProvider {


    @Override
    public String getObjectCode() {
        return Utils.PRODUCT_CONSTRAINT_LINES_API_NAME;
    }

    @Override
    public Optional<ImportObject> getImportObject(IObjectDescribe describe, IUniqueRule uniqueRule) {
        return Optional.empty();
    }
}
