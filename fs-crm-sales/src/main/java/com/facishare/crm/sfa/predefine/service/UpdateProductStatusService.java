package com.facishare.crm.sfa.predefine.service;

import com.facishare.crm.sfa.predefine.service.model.ProductCommonModel;
import com.facishare.crm.sfa.predefine.service.model.ServiceResult;
import com.facishare.crm.sfa.predefine.service.real.ProductService;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by luxin on 2018/12/12.
 */
@ServiceModule("product_status")
@Slf4j
@Service
public class UpdateProductStatusService {

    @Resource(name = "sfaProductService")
    private ProductService productService;


    @ServiceMethod("update")
    public ServiceResult<List<String>> updateProductStatus(ProductCommonModel.UpdateProductStatusArg arg, ServiceContext serviceContext) {
        return productService.updateProductStatus(serviceContext.getTenantId(), serviceContext.getUser().getUserId(), arg.getProductIds(), arg.getProductStatus());
    }

}
