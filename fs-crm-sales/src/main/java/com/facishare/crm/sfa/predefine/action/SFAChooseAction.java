package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.openapi.Utils;
import com.facishare.crmcommon.rest.dto.PrmEnterpriseModel;
import com.facishare.crm.sfa.model.SFAObjectPoolCommon;
import com.facishare.crm.sfa.predefine.exception.SFABusinessException;
import com.facishare.crm.sfa.predefine.exception.SFAErrorCode;
import com.facishare.crm.sfa.predefine.service.model.ObjectPoolPermission;
import com.facishare.crmcommon.util.PRMRestResult;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.AppIdMapping;
import com.facishare.paas.appframework.flow.ApprovalFlowStartResult;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.prm.util.PrmConstant;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * Created by yuanjl on 2018/7/18.
 */
@Transactional
public class SFAChooseAction extends BaseObjectPoolAction<SFAObjectPoolCommon.Arg, SFAObjectPoolCommon.Result> {
    protected String partnerId;
    @Override
    protected List<String> getDataPrivilegeIds(SFAObjectPoolCommon.Arg arg) {
        return arg.getObjectIDs();
    }

    @Override
    protected ObjectAction getObjectAction() {
        return ObjectAction.CHOOSE;
    }

    @Override
    protected String getObjectPoolId(SFAObjectPoolCommon.Arg arg) {
        String poolId = arg.getObjectPoolId();
        if(StringUtils.isEmpty(poolId)){
            if(CollectionUtils.isNotEmpty(dataList)){
                String poolKeyName = objectPoolService.getObjectPoolKeyName(actionContext.getObjectApiName());
                poolId = dataList.get(0).get(poolKeyName, String.class);
            }
        }
        arg.setObjectPoolId(poolId);
        return arg.getObjectPoolId();
    }

    @Override
    protected String getUserId(SFAObjectPoolCommon.Arg arg) {
        if(AppIdMapping.appIdMapping.get(PrmConstant.PRM_APP_ID).equals(actionContext.getAppId())) {
            return actionContext.getUser().getOutUserId();
        } else {
            return actionContext.getUser().getUserId();
        }
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList("Choose");
    }
    protected List<String>  failedList;

    @Override
    protected void doFunPrivilegeCheck() {
        if(arg.isSkipFunctionCheck()){
            return;
        }
        super.doFunPrivilegeCheck();
    }

    @Override
    protected void doDataPrivilegeCheck() {
        if(arg.isSkipFunctionCheck()){
            return;
        }
        super.doDataPrivilegeCheck();
    }

    @Override
    protected void before(SFAObjectPoolCommon.Arg arg) {
        super.before(arg);
        if (!ObjectPoolPermission.ObjectPoolPermissions.POOL_MEMBER.getValue().equals(objectPoolPermissions.getValue())
                && !ObjectPoolPermission.ObjectPoolPermissions.POOL_ALL.getValue().equals(objectPoolPermissions.getValue())) {
            throw new SFABusinessException(SFAErrorCode.CHOOSE_NO_PERMISSION);
        }
        dealPartnerId();
    }

    @Override
    protected SFAObjectPoolCommon.Result doAct(SFAObjectPoolCommon.Arg arg) {
        // 审批流
        if(needTriggerApprovalFlow())
        {
            if (dataList.size() > 1) {
                this.startApprovalFlowAsynchronous(this.dataList, ApprovalFlowTriggerType.CHOOSE, approvalFlowTriggerMap(), approvalCallBackMap());
                return SFAObjectPoolCommon.Result.builder().build();
            }

            Map<String, ApprovalFlowStartResult> resultMap = this.startApprovalFlow(this.dataList, ApprovalFlowTriggerType.CHOOSE.getId(), approvalFlowTriggerMap(), approvalCallBackMap());
            if (!ApprovalFlowStartResult.getNeedTriggerChangeOwnerAfterActionEnum().contains(resultMap.getOrDefault((this.dataList.get(0)).getId(), ApprovalFlowStartResult.APPROVAL_NOT_EXIST))) {
                return SFAObjectPoolCommon.Result.builder().build();
            }
        }

        SFAObjectPoolCommon.Result result = objectPoolService.choose(actionContext.getObjectApiName(), actionContext.getUser(), arg.getObjectPoolId(), arg.getObjectIDs(),actionContext.getEventId(),
                partnerId);
        return  result;
    }

    @Override
    protected SFAObjectPoolCommon.Result after(SFAObjectPoolCommon.Arg arg, SFAObjectPoolCommon.Result result) {
        super.after(arg, result);
        addLog();
        return result;
    }

    protected void addLog(){
        logAsync(dataList, EventType.MODIFY, ActionType.CHOOSE);
    }

    @Override
    protected Map<String, Map<String, Object>> approvalCallBackMap()
    {
        Map<String, Map<String, Object>> result=Maps.newHashMap();

        for (IObjectData objectData : dataList) {
            Map<String, Object> fieldMap=Maps.newHashMap();
            fieldMap.put("objectPoolId",arg.getObjectPoolId());
            result.put(objectData.getId(),fieldMap);
        }

        return result;
    }

    private void dealPartnerId() {
        if (AppIdMapping.appIdMapping.get(PrmConstant.PRM_APP_ID).equals(actionContext.getAppId())) {
            String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(actionContext.getTenantId()));
            PrmEnterpriseModel.GetMapperObjectIdArg getMapperObjectIdArg = new PrmEnterpriseModel.GetMapperObjectIdArg();
            getMapperObjectIdArg.setUpstreamEa(ea);
            getMapperObjectIdArg.setDownstreamOuterTenantId(Long.valueOf(actionContext.getUser().getOutTenantId()));
            getMapperObjectIdArg.setObjectApiName(Utils.PARTNER_API_NAME);
            PRMRestResult<String> restResult = initObjectsPermissionsAndLayoutProxy.getMapperObjectId(getHeader(), getMapperObjectIdArg);
            if(restResult != null && !Strings.isNullOrEmpty(restResult.getData())) {
                partnerId = restResult.getData();
            }
        }
    }

    private Map<String, String> getHeader() {
        Map<String, String> header = Maps.newHashMap();
        header.put("Content-Type", "application/json");
        header.put("x-eip-appid", "x_app_framework");
        return header;
    }

    private void sendActionMq(){
        for(IObjectData objectData : dataList){
            sfaOpenApiMqService.sendOpenApiMq(actionContext.getUser(), "Transfer",
                    actionContext.getObjectApiName(), objectData.getId(), new Object());
        }
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.CHOOSE.getButtonApiName();
    }

    @Override
    protected IObjectData getPreObjectData() {
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIdsIncludeDeleted(actionContext.getUser(), arg.getObjectIDs(), actionContext.getObjectApiName());
        if (objectDataList != null && objectDataList.size() != 0) {
            return objectDataList.get(0);
        }
        return null;
    }

    @Override
    protected IObjectData getPostObjectData() {
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIdsIncludeDeleted(actionContext.getUser(), arg.getObjectIDs(), actionContext.getObjectApiName());
        if (objectDataList != null && objectDataList.size() != 0) {
            return objectDataList.get(0);
        }
        return null;
    }

    @Override
    protected boolean skipPreFunction() {
        return arg.isSkipPreAction() ||  super.skipPreFunction();
    }

    @Override
    protected boolean skipCheckButtonConditions() {
        return arg.isSkipButtonConditions() || super.skipCheckButtonConditions();
    }

}