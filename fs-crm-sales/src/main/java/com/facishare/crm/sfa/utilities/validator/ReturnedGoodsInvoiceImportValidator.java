package com.facishare.crm.sfa.utilities.validator;

import com.facishare.crm.openapi.Utils;
import com.facishare.crmcommon.constants.ReturnedGoodsInvoiceConstants;
import com.facishare.crm.sfa.utilities.constant.SalesOrderConstants;
import com.facishare.crmcommon.util.SearchUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseImportAction;
import com.facishare.paas.appframework.core.predef.action.BaseImportDataAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.*;

@Slf4j
public class ReturnedGoodsInvoiceImportValidator {
    private static final ServiceFacade serviceFacade = SpringUtil.getContext().getBean(ServiceFacade.class);

    public static void validateSalesOrder(ActionContext actionContext,
                                           List<BaseImportAction.ImportError> errorList,
                                           List<BaseImportDataAction.ImportData> dataList) {
        List<IObjectData> objectDataList = dataList.stream()
                .map(BaseImportDataAction.ImportData::getData).collect(Collectors.toList());
        List<String> orderIds = objectDataList.stream()
                .filter(it -> !Strings.isNullOrEmpty(it.get(ReturnedGoodsInvoiceConstants.ReturnedGoodsInvoiceField.ORDER_ID.getApiName(), String.class)))
                .map(it -> it.get(ReturnedGoodsInvoiceConstants.ReturnedGoodsInvoiceField.ORDER_ID.getApiName(), String.class))
                .collect(Collectors.toList());

        if (CollectionUtils.notEmpty(orderIds)) {
            List<IObjectData> orderDatas = serviceFacade.findObjectDataByIdsIgnoreFormula(actionContext.getTenantId(),
                    orderIds, Utils.SALES_ORDER_API_NAME);
            dataList.forEach(data -> {
                IObjectData objectData = data.getData();
                if (!Strings.isNullOrEmpty(objectData.get(ReturnedGoodsInvoiceConstants.ReturnedGoodsInvoiceField.ACCOUNT_ID.getApiName(), String.class))
                        && !Strings.isNullOrEmpty(objectData.get(ReturnedGoodsInvoiceConstants.ReturnedGoodsInvoiceField.ORDER_ID.getApiName(), String.class))) {
                    String orderId = objectData.get(ReturnedGoodsInvoiceConstants.ReturnedGoodsInvoiceField.ORDER_ID.getApiName(), String.class);
                    Optional<IObjectData> order = orderDatas.stream()
                            .filter(it -> it.getId().equals(orderId))
                            .findAny();
                    if (order.isPresent()) {
                        if (!order.get().get(SalesOrderConstants.SalesOrderField.ACCOUNT_ID.getApiName(), String.class)
                                .equals(objectData.get(ReturnedGoodsInvoiceConstants.ReturnedGoodsInvoiceField.ACCOUNT_ID.getApiName(), String.class))) {
                            errorList.add(new BaseImportAction.ImportError(data.getRowNo(),
                                    String.format(I18N.text("sfa.a.not.match.b"),
                                            I18N.text("SalesOrderObj.attribute.self.display_name"),
                                            I18N.text("AccountObj.attribute.self.display_name"))));
                        }
                    }
                }
            });
        }

    }

    /**
     * 退货单产品需要校验是否超出订单产品销售数量，退货单产品->退货单->订单->订单产品，根据订单id和产品id确定对应的订单产品
     */
    public static void validateOrderProduct(ActionContext actionContext,
                                          List<BaseImportAction.ImportError> errorList,
                                          List<BaseImportDataAction.ImportData> dataList) {
        Map<Integer, String> relatedReturnIdMap = Maps.newHashMap();//行号、退货单id
        Map<Integer, String> relatedProductIdMap = Maps.newHashMap();//行号、产品id
        Map<Integer, BigDecimal> quantityMap = Maps.newHashMap();//行号、数量
        Map<Integer, BigDecimal> priceMap = Maps.newHashMap();//行号、价格
        List<String> returnedIds = Lists.newArrayList();//退货单id
        List<String> returnedProductIds = Lists.newArrayList();//退货单id+产品id，判断是否重复
        //获取关联的退货单id
        for(BaseImportDataAction.ImportData importData : dataList) {
            IObjectData objectData = importData.getData();
            String returnedId = objectData.get(ReturnedGoodsInvoiceConstants
                    .ReturnedGoodsInvoiceProductField.RETURNED_GOODS_INV_ID.getApiName(), String.class);
            String productId = objectData.get(ReturnedGoodsInvoiceConstants
                    .ReturnedGoodsInvoiceProductField.PRODUCT_ID.getApiName(), String.class);
            if(Strings.isNullOrEmpty(returnedId) || Strings.isNullOrEmpty(productId)) {
                continue;
            }
            BigDecimal quantity = objectData.get(ReturnedGoodsInvoiceConstants
            .ReturnedGoodsInvoiceProductField.QUANTITY.getApiName(), BigDecimal.class);
            BigDecimal price = objectData.get(ReturnedGoodsInvoiceConstants
                    .ReturnedGoodsInvoiceProductField.RETURNED_PRODUCT_PRICE.getApiName(), BigDecimal.class);
            relatedReturnIdMap.put(importData.getRowNo(), returnedId);
            relatedProductIdMap.put(importData.getRowNo(), productId);
            quantityMap.put(importData.getRowNo(), quantity);
            priceMap.put(importData.getRowNo(), price);
            if(!returnedIds.contains(returnedId)) {
                returnedIds.add(returnedId);
            }
            if(returnedProductIds.contains(String.join(returnedId, productId))) {
                errorList.add(new BaseImportAction.ImportError(importData.getRowNo(),String.format(I18N.text(SFA_OBJECT_REPEAT),
                        I18N.text("ReturnedGoodsInvoiceObj.attribute.self.display_name"))));
            } else {
                returnedProductIds.add(String.join(returnedId, productId));
            }
        }
        if(CollectionUtils.empty(returnedIds)) {
            return;
        }
        //获取退货单
        List<IObjectData> returnedDatas = serviceFacade.findObjectDataByIdsIgnoreFormula(actionContext.getTenantId(),
                returnedIds, Utils.RETURN_GOODS_INVOICE_API_NAME);
        if(CollectionUtils.empty(returnedDatas)) {
            return;
        }
        //根据退货单id获取已存在的退货单产品
        List<IObjectData> returnedProducts = queryData(actionContext.getTenantId(),
                ReturnedGoodsInvoiceConstants.ReturnedGoodsInvoiceProductField.RETURNED_GOODS_INV_ID.getApiName(),
                returnedIds, Utils.RETURN_GOODS_INVOICE_Product_API_NAME);
        //校验产品是否已存在于退货单
        if(CollectionUtils.notEmpty(returnedProducts)) {
             for(Map.Entry<Integer, String> entry : relatedReturnIdMap.entrySet()) {
                 String productId = relatedProductIdMap.get(entry.getKey());
                 Optional<IObjectData> objectData = returnedProducts.stream()
                         .filter(data -> Objects.equals(entry.getValue(),
                                         data.get(ReturnedGoodsInvoiceConstants.ReturnedGoodsInvoiceProductField
                                                 .RETURNED_GOODS_INV_ID.getApiName(), String.class))
                         && Objects.equals(productId,
                                 data.get(ReturnedGoodsInvoiceConstants.ReturnedGoodsInvoiceProductField
                                         .PRODUCT_ID.getApiName(), String.class)))
                         .findFirst();
                 if(objectData.isPresent()) {
                     errorList.add(new BaseImportAction.ImportError(entry.getKey(),
                             I18N.text(SFA_RETURNEDGOODSINVOICE_PRODUCT_EXISTS)));
                 }
             }
        }
        //获取退货单关联的订单id
        Map<String, String> returnedOrderMap = Maps.newHashMap();//退货单id、订单id，校验数量使用
        List<String> orderIds = Lists.newArrayList();
        for(IObjectData objectData : returnedDatas) {
            String orderId = objectData.get(ReturnedGoodsInvoiceConstants
                    .ReturnedGoodsInvoiceField.ORDER_ID.getApiName(), String.class);
            returnedOrderMap.put(objectData.getId(), orderId);
            if(!Strings.isNullOrEmpty(orderId) && !orderIds.contains(orderId)) {
                orderIds.add(orderId);
            }
        }
        if(CollectionUtils.empty(orderIds)) {
            dataList.forEach(data -> errorList.add(new BaseImportAction.ImportError(data.getRowNo(),
                    String.format(I18N.text(SFA_OBJECT_A_B_UNLIKED),
                            I18N.text("ReturnedGoodsInvoiceObj.attribute.self.display_name"),
                            I18N.text("SalesOrderObj.attribute.self.display_name")))));
            return;
        }
        //根据订单id获取订单产品
        List<IObjectData> orderProducts = queryData(actionContext.getTenantId(),
                SalesOrderConstants.SalesOrderProductField.ORDER_ID.getApiName(),
                orderIds, Utils.SALES_ORDER_PRODUCT_API_NAME);
        if(CollectionUtils.empty(orderProducts)) {
            dataList.forEach(data -> errorList.add(new BaseImportAction.ImportError(data.getRowNo(), String.format(I18N.text(SFA_NOT_EXISTS),
                    I18N.text("SalesOrderProductObj.attribute.self.display_name")) )));
            return;
        }

        //校验退货数量是否超出订单产品数量
        for(BaseImportDataAction.ImportData importData : dataList) {
            IObjectData objectData = importData.getData();
            String productId = relatedProductIdMap.get(importData.getRowNo());
            BigDecimal quantity = quantityMap.get(importData.getRowNo());
            if(!returnedOrderMap.containsKey(relatedReturnIdMap.get(importData.getRowNo()))) {

                errorList.add(new BaseImportAction.ImportError(importData.getRowNo(), String.format(I18N.text(SFA_OBJECT_A_B_UNLIKED),
                        I18N.text("ReturnedGoodsInvoiceObj.attribute.self.display_name"),
                        I18N.text("SalesOrderObj.attribute.self.display_name"))));
                continue;
            }
            String orderId = returnedOrderMap.get(relatedReturnIdMap.get(importData.getRowNo()));
            Optional<IObjectData> orderObjectData = orderProducts.stream()
                    .filter(data -> Objects.equals(orderId,
                            data.get(SalesOrderConstants.SalesOrderProductField.ORDER_ID.getApiName(), String.class))
                            && Objects.equals(productId,
                            data.get(SalesOrderConstants.SalesOrderProductField.PRODUCT_ID.getApiName(), String.class)))
                    .findFirst();
            if(orderObjectData.isPresent()) {
                BigDecimal salesQuantity = orderObjectData.get()
                        .get(SalesOrderConstants.SalesOrderProductField.QUANTITY.getApiName(), BigDecimal.class);
                if(quantity.compareTo(salesQuantity) == 1) {
                    errorList.add(new BaseImportAction.ImportError(importData.getRowNo(), I18N.text(SFA_OVER_REFUND_LIMIT_MSG)));
                } else {
                    objectData.set(ReturnedGoodsInvoiceConstants.ReturnedGoodsInvoiceProductField.ORDER_PRODUCT_ID.getApiName(),
                            orderObjectData.get().getId());
                }
            } else {
                errorList.add(new BaseImportAction.ImportError(importData.getRowNo(), String.format(I18N.text(SFA_OBJECT_CORRESPONDING),
                        I18N.text("SalesOrderProductObj.attribute.self.display_name"))));
            }
        }
        for(Map.Entry<Integer, BigDecimal> entry : quantityMap.entrySet()) {
            if(entry.getValue().compareTo(BigDecimal.ZERO) < 1) {
                errorList.add(new BaseImportAction.ImportError(entry.getKey(), I18N.text(SFA_REFUND_NUMBER_GREATER_0)));
            }
        }
        for(Map.Entry<Integer, BigDecimal> entry : priceMap.entrySet()) {
            if(entry.getValue().compareTo(BigDecimal.ZERO) < 1) {
                errorList.add(new BaseImportAction.ImportError(entry.getKey(), I18N.text(SFA_REFUND_AMOUNT_GREATER_0)));
            }
        }
    }

    public static void validateOrderSubProduct(ActionContext actionContext,
                                                List<BaseImportAction.ImportError> errorList,
                                                List<BaseImportDataAction.ImportData> dataList){
        dataList.forEach(importData -> {
            IObjectData orderProductData = importData.getData();
            // 获取订单产品ID
            String orderProductId = orderProductData.get("order_product_id", String.class);
            if(StringUtils.isEmpty(orderProductId)){
                return;
            }
            // 查询订单产品数据
            IObjectData salesOrderProduct = serviceFacade.findObjectData(actionContext.getUser(),
                    orderProductId, Utils.SALES_ORDER_PRODUCT_API_NAME);
            String subProductId = salesOrderProduct.get("sub_product_id", String.class);
            // 本来包的数量
            if (!StringUtils.isEmpty(subProductId)) {
                errorList.add(new BaseImportAction.ImportError(importData.getRowNo(), I18N.text(SFA_SALES_ORDER_PRODUCT_IS_NOT_SUB_PRODUCT)));
            }
        });

    }


    private static List<IObjectData> queryData(String tenantId, String fieldName,
                                               List<String> ids, String apiName) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(3000);
        searchTemplateQuery.setOffset(0);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, fieldName, ids);
        searchTemplateQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(
                new User(tenantId, User.SUPPER_ADMIN_USER_ID),
                apiName, searchTemplateQuery);
        List<IObjectData> objectDataList = queryResult.getData();
        return  objectDataList;
    }
}
