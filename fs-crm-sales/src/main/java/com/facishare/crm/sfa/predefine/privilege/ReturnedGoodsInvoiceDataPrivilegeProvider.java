package com.facishare.crm.sfa.predefine.privilege;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import com.facishare.paas.metadata.api.IObjectData;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * Created by renlb on 2019/2/23.
 */
@Component
public class ReturnedGoodsInvoiceDataPrivilegeProvider extends OrderDataPrivilegeProvider {

    @Override
    public String getApiName() {
        return Utils.RETURN_GOODS_INVOICE_API_NAME;
    }

    @Override
    protected Map<String, Permissions> checkUpdatePermissions(User user, Map<String, Permissions> dataPrivilegeMap, List<IObjectData> dataList) {
        Map<String, Permissions> permissionsMap = super.checkUpdatePermissions(user, dataPrivilegeMap, dataList);

//        if (SFAConfigUtil.isStockEnabled(user.getTenantId())) {
//            for (IObjectData objectData : dataList) {
//                ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
//                ObjectLifeStatus lifeStatus = objectDataExt.getLifeStatus();
//                if (ObjectLifeStatus.NORMAL.equals(lifeStatus) || ObjectLifeStatus.UNDER_REVIEW.equals(lifeStatus)) {
//                    permissionsMap.put(objectData.getId(), Permissions.NO_PERMISSION);
//                }
//            }
//        }

        return permissionsMap;
    }
}
