package com.facishare.crm.sfa.utilities.dataconverter.converter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.utilities.dataconverter.FieldValueConvertContext;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.impl.describe.*;
import com.facishare.paas.metadata.support.CountryAreaService;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

public class SelectOneFieldValueConverter implements AbstractFieldValueConverter {
    private CountryAreaService countryAreaService = SpringUtil.getContext().getBean(CountryAreaService.class);
    @Override
    public String convert(FieldValueConvertContext context) {
        IFieldDescribe fieldDescribe = context.getFieldDescribe();
        SelectOneFieldDescribe selectOneFieldDescribe;
        if (fieldDescribe instanceof CountryFieldDescribe) {
            CountryFieldDescribe countryFieldDescribe = new CountryFieldDescribe();
            countryFieldDescribe.fromJsonString(getCountryDescribeObj("country").toJSONString());
            selectOneFieldDescribe = countryFieldDescribe;
        } else if (fieldDescribe instanceof ProvinceFieldDescribe) {
            ProvinceFieldDescribe provinceFieldDescribe = new ProvinceFieldDescribe();
            provinceFieldDescribe.fromJsonString(getCountryDescribeObj("province").toJSONString());
            selectOneFieldDescribe = provinceFieldDescribe;
        } else if (fieldDescribe instanceof CityFiledDescribe) {
            ProvinceFieldDescribe cityFiledDescribe = new ProvinceFieldDescribe();
            cityFiledDescribe.fromJsonString(getCountryDescribeObj("city").toJSONString());
            selectOneFieldDescribe = cityFiledDescribe;
        } else if (fieldDescribe instanceof DistrictFieldDescribe) {
            ProvinceFieldDescribe districtFieldDescribe = new ProvinceFieldDescribe();
            districtFieldDescribe.fromJsonString(getCountryDescribeObj("district").toJSONString());
            selectOneFieldDescribe = districtFieldDescribe;
        } else {
            selectOneFieldDescribe = (SelectOneFieldDescribe) fieldDescribe;
        }

        if (selectOneFieldDescribe == null) {
            return "";
        }

        List<ISelectOption> selectOptions = selectOneFieldDescribe.getSelectOptions();
        Map<String, String> optionMap = Maps.newHashMap();
        for (ISelectOption k : selectOptions) {
            optionMap.put(k.getValue(), k.getLabel());
        }

        List<String> labelList = Lists.newArrayList();
        for (Object k : context.getFieldValues()) {
            if (k != null && StringUtils.isNotBlank(k.toString())) {
                if (optionMap.containsKey(k.toString())) {
                    labelList.add(optionMap.get(k.toString()));
                }
            }
        }
        if (CollectionUtils.notEmpty(labelList)) {
            return Joiner.on(",").join(labelList);
        }
        return "";
    }

    private JSONObject getCountryDescribeObj(String countryType) {
        String countryAreaJson = countryAreaService.getCountryCascadeJsonStringIncludeDeleted();
        return JSON.parseObject(countryAreaJson).getJSONObject(countryType);
    }
}
