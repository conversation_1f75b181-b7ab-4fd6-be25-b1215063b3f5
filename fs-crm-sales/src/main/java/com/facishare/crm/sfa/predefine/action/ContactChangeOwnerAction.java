package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.service.push.ContactSessionSandwichService;
import com.facishare.crm.sfa.utilities.constant.ContactConstants;
import com.facishare.crm.sfa.utilities.util.ContactUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.predef.action.StandardChangeOwnerAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 合同更换负责人 class
 *
 * <AUTHOR>
 * @date 2019/1/14
 */
@Slf4j
public class ContactChangeOwnerAction extends StandardChangeOwnerAction {
    ContactSessionSandwichService contactSessionSandwichService = SpringUtil.getContext()
            .getBean(ContactSessionSandwichService.class);

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        if (!result.isSuccess()) {
            return result;
        }
        if (CollectionUtils.empty(this.dataListToUpdate)) {
            return result;
        }
        //异步触发审批流或成功触发审批流不需要执行后续操作
        if (isApprovalFlowStartSuccessOrAsynchronous(this.dataListToUpdate.get(0).getId())) {
            return result;
        }
        if (CollectionUtils.notEmpty(this.dataListToUpdate)) {
            for (IObjectData data : this.dataListToUpdate) {
                // 更新联系人负责人变更时间
                data.set(ContactConstants.Field.OWNERCHANGEDTIME, System.currentTimeMillis());
            }
            this.serviceFacade.batchUpdateByFields(this.actionContext.getUser(), this.dataListToUpdate, Lists.newArrayList(ContactConstants.Field.OWNERCHANGEDTIME));
        }
        //发送消息
        Set<String> owners = dataListToUpdate.stream()
                .filter(m -> CollectionUtils.notEmpty(m.getOwner()))
                .map(IObjectData::getOwner).flatMap(Collection::stream).collect(Collectors.toSet());
        contactSessionSandwichService.push(actionContext.getTenantId(), Lists.newArrayList(owners));

        ContactUtil.recordOwnerChangeHistory(actionContext.getUser(),oldDataList);
        return result;
    }
}
