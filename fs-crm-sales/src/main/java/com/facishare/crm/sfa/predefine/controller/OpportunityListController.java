package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crmcommon.rest.dto.QueryObjectByIdsModel;
import com.facishare.crm.sfa.utilities.constant.OpportunityConstants;
import com.facishare.crm.sfa.utilities.util.OpportunityUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.MetaDataComputeServiceImpl;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.ISearchTemplateQuery;
import com.facishare.paas.metadata.impl.describe.CountFieldDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * Created by renlb on 2018/11/12.
 */
public class OpportunityListController extends SFAListController {

    private final MetaDataComputeServiceImpl metaDataComputeService = SpringUtil.getContext().getBean(MetaDataComputeServiceImpl.class);

    //特殊场景临时处理，待底层根据场景apiname设置DataRightsParameter改造完成后迁移过去
    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery query = super.buildSearchTemplateQuery();
        //ISearchTemplate searchTemplate = serviceFacade.findSearchTemplate(controllerContext.getUser(), this.getSearchTemplateId(), objectDescribe.getApiName());

        //if (!Strings.isNullOrEmpty(searchTemplate.getApiName())) {
            //setDataRightsParameter(query, searchTemplate);
        //}
        buildStageViewQuery(query);
        return query;
    }
    @Override
    protected NewOpportunityListResult buildResult(List<ILayout> layouts, ISearchTemplateQuery query, QueryResult<IObjectData> queryResult) {
        Result result=super.buildResult(layouts,query,queryResult);
        NewOpportunityListResult newOpportunityListResult=new NewOpportunityListResult();
        newOpportunityListResult.setObjectDescribe(result.getObjectDescribe());
        newOpportunityListResult.setListLayouts(result.getListLayouts());
        newOpportunityListResult.setDataList(result.getDataList());
        newOpportunityListResult.setTotal(result.getTotal());
        newOpportunityListResult.setLimit(result.getLimit());
        newOpportunityListResult.setOffset(result.getOffset());
        newOpportunityListResult.setUserInfos(result.getUserInfos());
        newOpportunityListResult.setButtonInfo(result.getButtonInfo());
        newOpportunityListResult.setObjectDescribeExt(result.getObjectDescribeExt());

        if (query.getFilters().stream().filter(x -> "sales_process_id".equals(x.getFieldName())).count()>0){
            //拼装totleAmount
            query.getOrders().clear();
            Count countFieldDescribe = new CountFieldDescribe();
            countFieldDescribe.setApiName(Utils.OPPORTUNITY_API_NAME);
            countFieldDescribe.setFieldApiName("amount0");
            countFieldDescribe.setSubObjectDescribeApiName(Utils.OPPORTUNITY_API_NAME);
            countFieldDescribe.setCountFieldApiName("expected_deal_amount");
            countFieldDescribe.setCountType(Count.TYPE_SUM);
            countFieldDescribe.setReturnType("number");
            countFieldDescribe.setDecimalPlaces(2);
            stopWatch.lap("buildResult getCountValue begin");
            Object obj=metaDataComputeService.getCountValue(controllerContext.getUser(),countFieldDescribe,query);
            if (obj!=null) {
                newOpportunityListResult.setTotalAmount(Double.parseDouble(obj.toString()));
            }
            stopWatch.lap("buildResult getCountValue end");
        }

        return newOpportunityListResult;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        if(CollectionUtils.empty(arg.getFieldProjection())) {
            stopWatch.lap("after handSaleActionInfo begin");
            OpportunityUtil.handSaleActionInfo(controllerContext.getTenantId(),controllerContext.getUser(),result.getDataList());
            stopWatch.lap("after handSaleActionInfo end");
        }

        return  result;
    }

    private QueryObjectByIdsModel.Arg buildQueryObjectByIdsModelArg(List<String> ids) {
        QueryObjectByIdsModel.Arg arg = new QueryObjectByIdsModel.Arg();
        arg.setProductIds(ids);
        arg.setIncludeCalculationFields(false);
        arg.setIncludeUserDefinedFields(false);
        arg.setObjectType(8);
        return arg;
    }

    private void buildStageViewQuery(SearchTemplateQuery query)
    {
        List<IFilter> filters = Lists.newArrayList();
        query.getFilters().forEach(f -> {
            String fieldName = f.getFieldName();
            if (StringUtils.isNotBlank(fieldName) && fieldName
                    .equals(OpportunityConstants.Field.OPPO_AFTER_STAGE_ID.getApiName())) {
                IFilter filter = new Filter();
                filter.setFieldName(OpportunityConstants.Field.AFTER_SALE_STAGE_STATUS.getApiName());
                List<String> fieldValues = Lists.newArrayList();
                fieldValues.add("1");
                filter.setFieldValues(fieldValues);
                filter.setOperator(Operator.EQ);
                filters.add(filter);
            }
        }
        );
        query.setFilters(filters);
    }
}
