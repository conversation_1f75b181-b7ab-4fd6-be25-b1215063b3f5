package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.AccountPathSynchronizer;
import com.facishare.paas.appframework.common.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

public class AccountBulkDisassociateAction extends BaseBulkDisassociateSFAAction {
    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        if (Utils.ACCOUNT_API_NAME.equals(arg.getAssociatedObjApiName()) &&
                "account_account_list".equals(arg.getAssociatedObjRelatedListName()) &&
                CollectionUtils.notEmpty(associatedDataList)) {
            List<String> accountIds = associatedDataList.stream().map(m -> m.getId()).collect(Collectors.toList());
            AccountPathSynchronizer.builder()
                    .user(actionContext.getUser())
                    .objectDataIds(accountIds)
                    .build()
                    .dealData();
        }
        return result;
    }
}
