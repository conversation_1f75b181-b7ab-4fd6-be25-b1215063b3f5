package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.utilities.validator.PriceBookImportValidator;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.predef.action.StandardUpdateImportDataAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 2019-11-05
 * @instruction
 */
public class PriceBookProductUpdateImportDataAction extends StandardUpdateImportDataAction {
    @Override
    protected void validReferenceID(List<ImportError> errorList, Map<IFieldDescribe, List<String>> defObjMap) {
        if (CollectionUtils.notEmpty(defObjMap)) {
            defObjMap.keySet().removeIf(iFieldDescribe -> "product_id".equals(iFieldDescribe.getApiName()));
        }
        super.validReferenceID(errorList, defObjMap);
    }

    @Override
    protected void customValidate(List<ImportData> dataList) {
        super.customValidate(dataList);
        List<ImportError> errorList = Lists.newArrayList();
        PriceBookImportValidator.validatePriceRange(errorList, dataList);
        mergeErrorList(errorList);
    }
}
