//package com.facishare.crm.sfa.predefine.controller;
//
//import com.facishare.paas.appframework.common.util.ObjectAction;
//import com.facishare.paas.appframework.core.predef.controller.StandardDetailController;
//import com.facishare.paas.appframework.core.util.RequestUtil;
//import com.facishare.paas.metadata.ui.layout.IButton;
//import com.facishare.paas.metadata.ui.layout.ILayout;
//
//import java.util.List;
//
//public class SubProductDetailController extends StandardDetailController {
//
//    @Override
//    protected ILayout getLayout() {
//        ILayout layout = super.getLayout();
//        specialLogicForLayout(layout);
//        return layout;
//    }
//
//    private void specialLogicForLayout(ILayout layout) {
//        List<IButton> buttons = layout.getButtons();
//        if (RequestUtil.isMobileRequest() || RequestUtil.isH5Request()) {
//            buttons.removeIf(k -> k.getAction().equals(ObjectAction.CREATE.getActionCode())
//                    || k.getAction().equals(ObjectAction.UPDATE.getActionCode())
//                    || k.getAction().equals(ObjectAction.DELETE.getActionCode())
//                    || k.getAction().equals(ObjectAction.RECOVER.getActionCode())
//                    || k.getAction().equals(ObjectAction.INVALID.getActionCode())
//                    || k.getAction().equals(ObjectAction.CHANGE_OWNER.getActionCode())
//                    || k.getAction().equals(ObjectAction.CLONE.getActionCode()));
//            layout.setButtons(buttons);
//        }
//    }
//}
//
