package com.facishare.crm.sfa.predefine.action;

import com.facishare.crmcommon.describebuilder.TextFieldDescribeBuilder;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.privilege.exception.CrmExceptionUtil;
import com.facishare.crm.sfa.predefine.service.SpuSkuImportExportService;
import com.facishare.crm.sfa.predefine.service.real.SpuSkuService;
import com.facishare.crm.sfa.predefine.service.real.SpuSkuServiceImpl;
import com.facishare.crm.sfa.utilities.common.convert.ConvertUtil;
import com.facishare.crm.sfa.utilities.constant.SpuSkuConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.dto.QueryDeptByName;
import com.facishare.paas.appframework.common.service.dto.UserInfo;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportDataAction;
import com.facishare.paas.appframework.core.predef.action.StandardUpdateImportDataAction;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.*;
import com.facishare.paas.metadata.api.search.IDataRightsParameter;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.IValidateResult;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.*;
import com.facishare.paas.metadata.impl.search.DataRightsParameter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.facishare.paas.metadata.validator.data.AbstractFieldValidator;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.Date;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.utilities.constant.SpuSkuConstants.IS_SPEC_NOT_NULL;
import static com.facishare.crm.sfa.utilities.constant.SpuSkuConstants.UNDER_SPU_SKU_ERROR;

/**
 * <AUTHOR>
 * @date 2018/11/9 16:11
 * @instruction
 */

/**
@Slf4j
public class SPUUpdateImportDataAction extends StandardUpdateImportDataAction {

    private final static List<String> NO_CHECK_DATA_PERMISSION_OBJECTS = Lists.newArrayList(
            Utils.PRICE_BOOK_API_NAME,
            Utils.PRICE_BOOK_PRODUCT_API_NAME,
            Utils.CUSTOMER_PAYMENT_API_NAME,
            Utils.GOAL_RULE_API_NAME
    );
    private final Map<String, ObjectDataExt> dataInStoreById = Maps.newHashMap();
    private final Map<Integer, ObjectDataExt> dataInStore = Maps.newHashMap();
    private final SpuSkuService spuSkuService = SpringUtil.getContext().getBean(SpuSkuServiceImpl.class);
    private final SpuSkuImportExportService importExportService = SpringUtil.getContext().getBean(SpuSkuImportExportService.class);
    private final Pattern compile = Pattern.compile(I18NKey.OTHER_AND_SYMBOL);
    private final List<IObjectData> actualList = Lists.newArrayList();
    private final List<ImportError> allErrorList = Lists.newArrayList();
    private final Set<String> noConvertRefFields = Sets.newHashSet();
    private final Map<String, IObjectDescribe> describeMap = Maps.newHashMapWithExpectedSize(2);
    private final Map<String, StandardInsertImportDataAction.ImportData> spuRowsMaps = Maps.newHashMap();
    private final Map<String, List<StandardInsertImportDataAction.ImportData>> skuIoDataMap = Maps.newHashMap();
    private final List<String> filterSpuFields = Lists.newArrayList();
    private final List<String> filterSkuFields = Lists.newArrayList();
    private boolean isCurrentAdmin;
    private List<IObjectData> validList = Lists.newArrayList();
    private IObjectDescribe skuDescribe;
    private IObjectDescribe spuDescribe;
    private List<StandardInsertImportDataAction.ImportData> spuDataList;
    private List<StandardInsertImportDataAction.ImportData> skuDataList;
    private Map<Integer,String> rowNoToSpuId = Maps.newHashMap();

    @Override
    protected void before(Arg arg) {
        //校验主对象的权限
        doFunPrivilegeCheck();
        //处理商品规格属性
        spuDescribe = serviceFacade.findObject(actionContext.getTenantId(), SpuSkuConstants.SPUOBJ);
        TextFieldDescribe specificationFieldDescribe = TextFieldDescribeBuilder.builder().apiName(SpuSkuConstants.CUSTOM_SPU_SPEC_FIELD).label(SpuSkuConstants.SPU_SPECIFICATION_PROPERTY).maxLength(256).build();
        spuDescribe.addFieldDescribe(specificationFieldDescribe);
        this.spuDescribe.getFieldDescribes().forEach(field -> {
            if (field.getApiName().equals(SpuSkuConstants.SPU_ID)) {
                field.setLabel(SpuSkuConstants.SPU_NUMBER);
                field.setRequired(true);
            }
            if (Objects.equals("image", field.getType()) || Objects.equals("file_attachment", field.getType())) {
                filterSpuFields.add(field.getApiName());
            }
        });


        //处理产品规格属性值
        skuDescribe = serviceFacade.findObject(actionContext.getTenantId(), SpuSkuConstants.PRODUCTOBJ);
        TextFieldDescribe producetFieldDescribe = TextFieldDescribeBuilder.builder().apiName(SpuSkuConstants.CUSTOM_SKU_SPEC_VALUE_FIELD).label(SpuSkuConstants.SKU_SPECIFICATION_VALUE_PROPERTY).maxLength(256).build();
        skuDescribe.addFieldDescribe(producetFieldDescribe);
        //SKU数据权限校验
        this.skuDescribe.getFieldDescribes().forEach(field -> field.set(SpuSkuConstants.FIELD_LABEL, SpuSkuConstants.CUSTOM_SKU_PREFIX_WITH_KEY + field.getLabel()));
        this.skuDescribe.getFieldDescribes().forEach(field -> {
            if (field.getApiName().equals(SpuSkuConstants.SKU_ID)) {
                field.setLabel(SpuSkuConstants.SKU_PREFIX_SKU_NUMBER);
                field.setRequired(true);
            }
            if (Objects.equals("image", field.getType()) || Objects.equals("file_attachment", field.getType())) {
                filterSkuFields.add(field.getApiName());
            }
        });


        describeMap.put(SpuSkuConstants.SPUOBJ, spuDescribe);
        describeMap.put(SpuSkuConstants.PRODUCTOBJ, skuDescribe);
        log.warn("SPUUpdateImportDataAction  before   tenantId -> {},ObjectDataDocument -> {}", actionContext.getTenantId(), arg.getRows());

        //循环处理数据
        arg.getRows().forEach(doc -> {
            if (!doc.isEmpty()) {
                //获取商品数据
                spuDataList = convertLabelToApiName(Lists.newArrayList(doc), spuDescribe, SpuSkuConstants.SPUOBJ);
                if (CollectionUtils.notEmpty(spuDataList)) {
                    int rowNoNUll = spuDataList.get(0).getRowNo();
                    IObjectData data = spuDataList.get(0).getData();
                    if (null != data) {
                        String spuName = data.getName();
                        if (!StringUtils.isEmpty(spuName)) {
                            //表示不存在spuName
                            if (!spuRowsMaps.containsKey(spuName)) {
                                spuRowsMaps.put(spuName, spuDataList.get(0));
                                skuIoDataMap.put(spuName, Lists.newArrayList());
                            }  //存在spuName

                            //获取产品数据
                            skuDataList = convertLabelToApiName(Lists.newArrayList(doc), skuDescribe, SpuSkuConstants.PRODUCTOBJ);
                            List<StandardInsertImportDataAction.ImportData> list = skuIoDataMap.get(spuName);
                            list.add(skuDataList.get(0));
                            skuIoDataMap.put(spuName, list);
                        } else {
                            List<ImportError> spuNullList = Lists.newArrayList();
                            ImportError importError = new ImportError();
                            importError.setRowNo(rowNoNUll);
                            importError.setErrorMessage(SpuSkuConstants.SPU_NAME_NOT_NULL);
                            spuNullList.add(importError);
                            mergeErrorList(spuNullList);
                        }
                    }
                }
            }
        });


        isCurrentAdmin = serviceFacade.isAdmin(actionContext.getUser());
    }

    private List<StandardInsertImportDataAction.ImportData> convertLabelToApiName(List<ObjectDataDocument> sourceDataList, IObjectDescribe describe, String apiName) {
        //参数中的data的key是label，需要转换成apiName
        if (CollectionUtils.empty(sourceDataList)) {
            return null;
        }
        List<StandardInsertImportDataAction.ImportData> resultList = Lists.newArrayList();
        for (ObjectDataDocument source : sourceDataList) {
            if (source.size() > 1) {
                StandardInsertImportDataAction.ImportData data = convertLabelToApiName(source, describe, apiName);
                resultList.add(data);
            }
        }
        return resultList;
    }

    private StandardInsertImportDataAction.ImportData convertLabelToApiName(ObjectDataDocument source, IObjectDescribe describe, String dataApiName) {
        //如果是商品数据，获取label不带   产品_   的数据
        //参数中的data的key是label，需要转换成apiName
        if (null == source) {
            return null;
        }

        IObjectData result = new ObjectData();
        StandardInsertImportDataAction.ImportData resultData = new StandardInsertImportDataAction.ImportData();
        Set<Map.Entry<String, Object>> entrySet = source.entrySet();
        if (dataApiName.equals(SpuSkuConstants.SPUOBJ)) {
            for (Map.Entry<String, Object> entry : entrySet) {
                if (entry.getKey().startsWith(SpuSkuConstants.CUSTOM_SKU_PREFIX_WITH_KEY)) {
                    continue;
                }
                if (ROW_NO.equalsIgnoreCase(entry.getKey())) {
                    resultData.setRowNo(Integer.valueOf(String.valueOf(entry.getValue())));
                    continue;
                }

                //将label转换成apiName
                List<IFieldDescribe> fieldDescribes = ObjectDescribeExt.of(describe).getFieldDescribesSilently();
                String apiName = getFieldApiName(entry.getKey(), fieldDescribes);
                String value = entry.getValue() == null ? "" : String.valueOf(entry.getValue());
                result.set(apiName, value);

                if (entry.getKey().contains(NO_CONVERT_SYMBOL)) {
                    noConvertRefFields.add(apiName);
                }
            }
        } else {
            for (Map.Entry<String, Object> entry : entrySet) {
                if (ROW_NO.equalsIgnoreCase(entry.getKey())) {
                    resultData.setRowNo(Integer.valueOf(String.valueOf(entry.getValue())));
                    continue;
                }

                if (!entry.getKey().startsWith(SpuSkuConstants.CUSTOM_SKU_PREFIX_WITH_KEY)) {
                    continue;
                }

                //将label转换成apiName
                List<IFieldDescribe> fieldDescribes = ObjectDescribeExt.of(describe).getFieldDescribesSilently();
                String apiName = getFieldApiName(entry.getKey(), fieldDescribes);
                String value = entry.getValue() == null ? "" : String.valueOf(entry.getValue());
                result.set(apiName, value);

                if (entry.getKey().contains(NO_CONVERT_SYMBOL)) {
                    noConvertRefFields.add(apiName);
                }
            }
        }

        resultData.setData(result);
        return resultData;
    }

    private String getFieldApiName(String key, List<IFieldDescribe> fieldDescribes) {
        if (Strings.isNullOrEmpty(key)) {
            return null;
        }

        for (IFieldDescribe fieldDescribe : fieldDescribes) {
            String newKey = getValidTitle(key, fieldDescribe);
            if (newKey.equals(fieldDescribe.getLabel())) {
                return fieldDescribe.getApiName();
            }
        }
        return null;
    }

    @Override
    protected Result doAct(Arg arg) {
        Result result = new Result();
        result.setSuccess(true);
        try {


            if (!arg.getIsEmptyValueToUpdate()) {
                List<IObjectData> spuDataList = Lists.newArrayList();
                spuRowsMaps.forEach((k, v) -> {
                    spuDataList.add(v.getData());
                });
                removeNonKeys(spuDataList);

                List<IObjectData> skuDataList = Lists.newArrayList();
                skuIoDataMap.forEach((k, v) -> {
                    for (ImportData data : v) {
                        skuDataList.add(data.getData());
                    }
                });
                removeNonKeys(skuDataList);
            }


            //校验spu
            List<StandardInsertImportDataAction.ImportData> spuImportData = Lists.newArrayList();
            spuImportData.addAll(spuRowsMaps.values());
            validateField(spuImportData, describeMap.get(SpuSkuConstants.SPUOBJ));
            validateEmployeeField(spuImportData, describeMap.get(SpuSkuConstants.SPUOBJ));
            validateDeptField(spuImportData, describeMap.get(SpuSkuConstants.SPUOBJ));
            validateUniqueData(spuImportData, describeMap.get(SpuSkuConstants.SPUOBJ));
            validateRelatedObject(spuImportData, describeMap.get(SpuSkuConstants.SPUOBJ));
            customSpuValidate(spuImportData, describeMap.get(SpuSkuConstants.SPUOBJ));
            customSpuExistValidate(spuImportData, describeMap.get(SpuSkuConstants.SPUOBJ));
            validList = filterValidDataList(spuImportData, allErrorList, describeMap.get(SpuSkuConstants.SPUOBJ));
            if (!CollectionUtils.empty(validList)) {
                fillPartnerInfo(validList, describeMap.get(SpuSkuConstants.SPUOBJ));
                customDefaultValue(validList, describeMap.get(SpuSkuConstants.SPUOBJ));
            }


            //校验sku
            List<StandardInsertImportDataAction.ImportData> skuImportData = Lists.newArrayList();
            skuIoDataMap.values().forEach(skuImportData::addAll);
            validateField(skuImportData, describeMap.get(SpuSkuConstants.PRODUCTOBJ));
            validateEmployeeField(skuImportData, describeMap.get(SpuSkuConstants.PRODUCTOBJ));
            validateDeptField(skuImportData, describeMap.get(SpuSkuConstants.PRODUCTOBJ));
            validateUniqueData(skuImportData, describeMap.get(SpuSkuConstants.PRODUCTOBJ));
            validateRelatedObject(skuImportData, describeMap.get(SpuSkuConstants.PRODUCTOBJ));
            customSkuValidate(skuImportData, describeMap.get(SpuSkuConstants.PRODUCTOBJ));
            customSkuExistValidate(skuImportData, describeMap.get(SpuSkuConstants.PRODUCTOBJ));

            validList = filterValidDataList(skuImportData, allErrorList, describeMap.get(SpuSkuConstants.PRODUCTOBJ));
            if (!CollectionUtils.empty(validList)) {
                fillPartnerInfo(validList, describeMap.get(SpuSkuConstants.PRODUCTOBJ));
                customDefaultValue(validList, describeMap.get(SpuSkuConstants.PRODUCTOBJ));
            }
            spuGenerateResult(result);
        } catch (MetaDataBusinessException serviceException) {
            log.warn("ServiceException in ImportParamData ofSuccess BulkImportDataService,arg:{}", arg, serviceException);
            result.setSuccess(false);
            result.setMessage(CrmExceptionUtil.formatErrorMessage(serviceException));
        } catch (Exception e) {
            log.error("Unexpected Error in ImportParamData ofSuccess BulkImportDataService,arg:{}", arg, e);
            result.setSuccess(false);
            Throwable rootCause = ExceptionUtils.getRootCause(e);
            if (rootCause instanceof org.springframework.dao.DuplicateKeyException) {
                result.setMessage(I18N.text(I18NKey.DO_NOT_INPUT_DUPLICATE_PRODUCT));
            } else {
                result.setMessage(I18N.text(I18NKey.UNKNOWN_EXCEPTION));
            }
        }


        Map<Integer, ImportError> row2ImportError = Maps.newHashMap();
        for (ImportError importError : allErrorList) {
            row2ImportError.put(importError.getRowNo(), importError);
        }
        spuRowsMaps.forEach((spuName, importSpuData) -> {
            IObjectData spuData = importSpuData.getData();
            List<IObjectData> skuData4SaveList = Lists.newArrayList();
            List<String> skuIdList = Lists.newArrayList();
            List<Integer> spuRows = Lists.newArrayList();
            skuIoDataMap.get(spuName).forEach(importSkuData -> {
                IObjectData skuData = importSkuData.getData();
                skuIdList.add(skuData.getId());
                skuData4SaveList.add(skuData);
                spuRows.add(importSkuData.getRowNo());
            });

            ImportError importError2 = row2ImportError.get(importSpuData.getRowNo());
            if (importError2 != null) {
                spuRows.forEach(rowNum -> {
                    if (importError2.getRowNo() != rowNum) {
                        ImportError importError = row2ImportError.get(rowNum);
                        if (importError != null) {
                            importError.setErrorMessage(importError.getErrorMessage() + "\n" + UNDER_SPU_SKU_ERROR);
                        } else {
                            ImportError importError1 = new ImportError(rowNum, UNDER_SPU_SKU_ERROR);
                            row2ImportError.put(rowNum, importError1);
                            allErrorList.add(importError1);
                        }
                    }
                });
                return;
            }

            //在db中查询excel中存在的sku
            QueryResult<IObjectData> dbQuerySkuINResult = importExportService.findByQueryInIds(actionContext.getUser(),
                    Lists.newArrayList(), spuData.getId(), Utils.PRODUCT_API_NAME,
                    skuIdList, 0, 0);
            //在db中查询在excel中不存在的sku
            QueryResult<IObjectData> dbQuerySkuNotINResult = importExportService.findByQueryNotInIds(actionContext.getUser(),
                    Lists.newArrayList(), spuData.getId(), Utils.PRODUCT_API_NAME,
                    skuIdList, 0, 0);

            //判断有无规格
            Boolean isSpec = spuData.get("is_spec", Boolean.class);
            // 为减少改动，这个位置判断db查询的id与excel中的id是否配置，存在不匹配的数据进行抛弃，不做修改。
            List<IObjectData> saveSkuDatas = Lists.newArrayList();
            if (!dbQuerySkuINResult.getData().isEmpty()) {
                Map<String, List<IObjectData>> reduceSkuData = dbQuerySkuINResult.getData().stream().collect(Collectors.groupingBy(DBRecord::getId, Collectors.toList()));
                skuData4SaveList.forEach(skuData -> {
                    String id = skuData.getId();
                    String newSkuName;
                    if (isSpec) {
                        if (reduceSkuData.containsKey(id)) {
                            String productSpec = reduceSkuData.get(id).get(0).get("product_spec", String.class);
                            newSkuName = builderSkuName(spuName, productSpec);
                            skuData.setName(newSkuName);
                            saveSkuDatas.add(skuData);
                        }
                    } else {
                        newSkuName = spuName;
                        skuData.setName(newSkuName);
                        saveSkuDatas.add(skuData);
                    }
                });
            }


            List<IObjectData> dbSkuDatas = Lists.newArrayList();
            if (!dbQuerySkuNotINResult.getData().isEmpty()) {
                dbQuerySkuNotINResult.getData().forEach(skuData -> {
                    String newSkuName;
                    if (isSpec) {
                        String productSpec = skuData.get("product_spec", String.class);
                        newSkuName = builderSkuName(spuName, productSpec);
                    } else {
                        newSkuName = spuName;
                    }
                    skuData.setName(newSkuName);
                    dbSkuDatas.add(skuData);
                });
            }
            saveSkuDatas.addAll(dbSkuDatas);
            skuData4SaveList.forEach(ConvertUtil::convertOrderFieldValue2Number);

            ObjectDataExt.of(spuData).remove("specification");

            List<IObjectData> oldSpuData = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), Lists.newArrayList(spuData.getId()), Utils.SPU_API_NAME);
            List<String> skuIds = skuData4SaveList.stream().map(IObjectData::getId).collect(Collectors.toList());
            List<IObjectData> oldSkuDataList = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), skuIds, Utils.PRODUCT_API_NAME);
            try {
                spuSkuService.updateSpuAndHandleSku4UpdateInsert(spuData, actionContext.getUser(), skuData4SaveList);
                spuSkuUpdateImportLog(spuData, oldSpuData, skuData4SaveList, oldSkuDataList);
            } catch (Exception e) {

                log.error("tenantId {},spuId {}", actionContext.getTenantId(), spuData.getId(), e);
                String errorMsg = e.getMessage();
                spuRows.forEach(rowNum -> {
                    ImportError importError = row2ImportError.get(rowNum);
                    if (importError != null) {
                        importError.setErrorMessage(importError.getErrorMessage() + "\n" + errorMsg);
                    } else {
                        ImportError importError1 = new ImportError(rowNum, errorMsg);
                        row2ImportError.put(rowNum, importError1);
                        allErrorList.add(importError1);
                    }
                });
            }
        });

        return result;
    }

    @Override
    protected void customInit(List<ImportData> dataList) {
        // do nothing
    }

    @Override
    protected void recordImportDataLog(List<IObjectData> actualList) {
        // do nothing
    }

    /**
     * 记录商品产品更新记录
     *
     * @param spuData
     * @param skuData4SaveList
     */
/**
    private void spuSkuUpdateImportLog(IObjectData spuData, List<IObjectData> oldSpuData, List<IObjectData> skuData4SaveList, List<IObjectData> oldSkuData4SaveList) {
        // 最后成功了,在填写修改记录
        recordImportDataLog(Lists.newArrayList(spuData), oldSpuData, spuDescribe);
        recordImportDataLog(skuData4SaveList, oldSkuData4SaveList, skuDescribe);
    }

    private String builderSkuName(String spuName, String productSpec) {
        String[] specs = importExportService.splitSkuProductSpec(productSpec);
        String skuNameSuffix = importExportService.aggregationSpecValue(specs);
        return spuName + skuNameSuffix;
    }

    private String generateSkuNameWithSpecValue(String productSpec) {
        if (StringUtils.isEmpty(productSpec)) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        sb.append("[");
        String[] specMap = productSpec.split("；");
        for (int i = 0; i < specMap.length; i++) {
            String key = specMap[i];
            String[] specValue = key.split("：");
            for (int j = 0; j < specValue.length; j++) {
                if (j == 1 && i != specMap.length - 1) {
                    sb.append(specValue[j]).append("-");
                } else if (j == 1) {
                    sb.append(specValue[j]);
                }
            }
        }
        sb.append("]");
        return sb.toString();
    }

    protected List<IObjectData> filterValidDataList(List<ImportData> dataList, List<ImportError> allErrorList, IObjectDescribe describe) {
        List<IObjectData> validList = Lists.newArrayList();
        for (ImportData importData : dataList) {
            boolean hasError = false;
            for (ImportError importError : allErrorList) {
                if (Objects.equals(importData.getRowNo(), importError.getRowNo())) {
                    hasError = true;
                    break;
                }
            }
            if (!hasError) {
                validList.add(importData.getData());
            }
        }
        return validList;
    }

    protected void customDefaultValue(List<IObjectData> validList, IObjectDescribe describe) {

        //处理为空的数据是否导入
        removeNonUpdateField(validList);
        removeNoIdData(validList);
//        mergeSystemField(validList);

    }

    private void removeNonUpdateField(List<IObjectData> validList) {
        if (arg.getIsEmptyValueToUpdate()) {
            return;
        }

        validList.forEach(data -> {
            ObjectDataDocument dataDocument = ObjectDataDocument.of(data);
            dataDocument.entrySet().removeIf(next -> {
                String value = getStringValue(data, next.getKey());
                return Strings.isNullOrEmpty(value);
            });
        });
    }


    private void removeNonKeys(List<IObjectData> dataCollection) {
        dataCollection.forEach(k -> {
            Iterator<Map.Entry<String, Object>> iterator = ObjectDataExt.of(k).toMap().entrySet().iterator();

            while (iterator.hasNext()) {
                Map.Entry<String, Object> next = iterator.next();

                Object data = next.getValue();
                if (data instanceof String && StringUtils.isBlank((String) data) || data instanceof List && CollectionUtils.empty((List) data)) {
                    iterator.remove();
                }
            }
        });
    }


    private void removeNoIdData(List<IObjectData> validList) {
        validList.removeIf(a -> Objects.isNull(a.getId()));
    }

    private void mergeSystemField(List<IObjectData> validList) {
        validList.forEach(data -> {
            data.setDescribeId(objectDescribeExt.getId());
            ObjectDataExt objectDataExt = dataInStoreById.get(data.getId());
            data.setVersion(objectDataExt.getVersion());
        });
    }

    private void customSkuValidate(List<ImportData> dataList, IObjectDescribe describe) {
        // 校验数据权限
        validateDataPermission(dataList, describe);
        //校验锁定
        validateLock(dataList);
        //校验主对象是否正确
        validateMasterData(dataList, describe);

    }

    private void customSkuExistValidate(List<ImportData> dataList, IObjectDescribe describe) {
        List<ImportError> errorList = Lists.newArrayList();
        List<String> skuIds = Lists.newArrayList();
        Map<String, Integer> skuRowNo = Maps.newHashMap();
        dataList.forEach(importData -> {
            String skuId = importData.getData().getId();
            String skuName = importData.getData().getName();
            if (StringUtils.isEmpty(skuId)) {
                String errorMessage = String.format(SpuSkuConstants.PRODUCT_NUMBER_IS_MUST_FILL_IN, skuName);
                errorList.add(new ImportError(importData.getRowNo(), errorMessage));
            } else {
                skuIds.add(skuId);
                skuRowNo.put(skuId, importData.getRowNo());
            }
        });
        if (!skuIds.isEmpty()) {
            List<String> dbSkuIds = Lists.newArrayList();
            List<IObjectData> dbSkuDatas = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), skuIds, Utils.PRODUCT_API_NAME);
            dbSkuDatas.forEach(skudata -> {
                String skuId = skudata.getId();
                String spuId = skudata.get("spu_id",String.class);
                String excelSpuId = rowNoToSpuId.get(skuRowNo.get(skuId));
                if(!Objects.equals(spuId, excelSpuId)){
                    errorList.add(new ImportError(skuRowNo.get(skuId), "该产品不存在不存在或商品产品的系统编号对应失败。"));
                }
                dbSkuIds.add(skuId);
            });

            skuIds.removeAll(dbSkuIds);
            if (!skuIds.isEmpty()) {
                skuIds.forEach(skuId -> {
                    String errorMessage = String.format(SpuSkuConstants.SKU_IS_NOT_EXIST, skuId);
                    errorList.add(new ImportError(skuRowNo.get(skuId), errorMessage));
                });
            }
        }
        mergeErrorList(errorList);
    }


    private void customSpuValidate(List<ImportData> dataList, IObjectDescribe describe) {
        // 校验数据权限
        validateDataPermission(dataList, describe);
        //校验锁定
        validateLock(dataList);
        //校验主对象是否正确
        validateMasterData(dataList, describe);

    }


    private void customSpuExistValidate(List<ImportData> dataList, IObjectDescribe describe) {
        List<ImportError> errorList = Lists.newArrayList();

        List<String> spuIds = Lists.newArrayList();
        Map<String, Boolean> isSpecMaps = Maps.newHashMap();
        Map<String, Integer> spuRowNo = Maps.newHashMap();
        Map<String, ImportData> spuId2ImportData = Maps.newHashMap();
        dataList.forEach(importData -> {
            String spuId = importData.getData().getId();
            String spuName = importData.getData().getName();
            if (StringUtils.isEmpty(spuId)) {
                String errorMessage = String.format(SpuSkuConstants.SPU_NUMBER_IS_MUST_FILL_IN, spuName);
                errorList.add(new ImportError(importData.getRowNo(), errorMessage));
            } else {
                spuIds.add(spuId);
                isSpecMaps.put(spuId, importData.getData().get("is_spec", Boolean.class));
                spuRowNo.put(spuId, importData.getRowNo());
                spuId2ImportData.put(spuId, importData);
                rowNoToSpuId.put(importData.getRowNo(),spuId);
            }
        });


        if (!spuIds.isEmpty()) {
            //查询spu数据是否存在

            List<IObjectData> spuDatas = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), spuIds, Utils.SPU_API_NAME);
            List<String> dbSpuIds = Lists.newArrayList();
            //循环判断spu是否存在
            spuDatas.forEach(spuData -> {
                String id = spuData.getId();
                Boolean dbIsSpec = spuData.get("is_spec", Boolean.class);

                // 如果为空不更新且填写数据不为空 或是为空校验才会校验
                if ((!arg.getIsEmptyValueToUpdate() && isSpecMaps.get(id) != null) || arg.getIsEmptyValueToUpdate()) {
                    if (!Objects.equals(isSpecMaps.get(id), dbIsSpec)) {

                        if (isSpecMaps.get(id) == null) {
                            errorList.add(new ImportError(spuRowNo.get(id), IS_SPEC_NOT_NULL));
                        } else {
                            String errorMessage = String.format(SpuSkuConstants.SPU_IS_SPEC_NOT_CHANGE, id);
                            errorList.add(new ImportError(spuRowNo.get(id), errorMessage));
                        }
                    }
                }

                ImportData importData = spuId2ImportData.get(id);
                importData.getData().set("is_spec", dbIsSpec);
                dbSpuIds.add(id);
            });

            spuIds.removeAll(dbSpuIds);
            if (!spuIds.isEmpty()) {
                spuIds.forEach(spuId -> {
                    String errorMessage = String.format(SpuSkuConstants.SPU_IS_NOT_EXIST, spuId);
                    errorList.add(new ImportError(spuRowNo.get(spuId), errorMessage));
                });
            }

        }
        mergeErrorList(errorList);
    }

    private void validateLock(List<ImportData> dataList) {
        List<ImportError> errorList = Lists.newArrayList();
        dataList.forEach(data -> {
            ObjectDataExt objectData = dataInStore.get(data.getRowNo());
            if (!Objects.isNull(objectData) && objectData.isLock()) {
                errorList.add(new ImportError(data.getRowNo(), I18N.text(I18NKey.DATA_LOCKED)));
            }
        });
        mergeErrorList(errorList);
    }

    private void validateMasterData(List<ImportData> dataList, IObjectDescribe describe) {
        if (!ObjectDescribeExt.of(describe).isSlaveObject() || CollectionUtils.empty(dataList)) {
            return;
        }

        Optional<MasterDetailFieldDescribe> masterDetailField = ObjectDescribeExt.of(describe).getMasterDetailFieldDescribe();
        if (!masterDetailField.isPresent() || !dataList.get(0).containsField(masterDetailField.get().getApiName())) {
            return;
        }

        String masterApiName = masterDetailField.get().getApiName();
        List<ImportError> errorList = Lists.newArrayList();
        dataList.forEach(data -> {
            ObjectDataExt objectData = dataInStore.get(data.getRowNo());
            if (!Objects.isNull(objectData) &&
                    !Objects.isNull(data.getData().get(masterApiName)) &&
                    !Objects.equals(objectData.get(masterApiName), data.getData().get(masterApiName))) {
                errorList.add(new ImportError(data.getRowNo(), I18N.text(I18NKey.RELATED_MUSTER_UNLIKE)));
            }
        });
        mergeErrorList(errorList);
    }

    private void validateDataPermission(List<ImportData> importDataList, IObjectDescribe describe) {
        List<IObjectData> dataLists = importDataList.stream()
                .map(a -> a.getData()).collect(Collectors.toList());
        if (CollectionUtils.empty(dataLists)) {
            return;
        }

        List<String> idList = dataLists.stream()
                .filter(r -> !Strings.isNullOrEmpty(r.getId()))
                .map(objectData -> objectData.getId())
                .collect(Collectors.toList());
        if(CollectionUtils.empty(idList)){
            return;
        }

        Map<String, Permissions> permissionsMap = serviceFacade.checkDataPrivilege(actionContext.getUser(),
                dataLists, ObjectDescribeExt.of(describe),ObjectAction.UPDATE.getActionCode());
        List<ImportError> errorList = Lists.newArrayList();
        importDataList.stream()
                .filter(a -> !Objects.isNull(a.getData().getId()))
                .forEach(data -> {
                    Permissions permissions = permissionsMap.get(data.getData().getId());
                    if (Objects.isNull(permissions) || !Objects.equals(Permissions.READ_WRITE, permissions)) {
                        errorList.add(new ImportError(data.getRowNo(), I18N.text(I18NKey.USER_NO_DATA_PRIVILEGE)));
                    }
                });
        mergeErrorList(errorList);
    }

    private void validateField(List<StandardInsertImportDataAction.ImportData> dataList, IObjectDescribe describe) {
        List<ImportError> errorList = Lists.newArrayList();
        for (ImportData importData : dataList) {
            String errorMessage = convertAndValidateFieldValue(importData, describe);
            if (Strings.isNullOrEmpty(errorMessage)) {
                continue;
            }

            ImportError importError = new ImportError();
            importError.setRowNo(importData.getRowNo());
            importError.setErrorMessage(errorMessage);
            errorList.add(importError);
        }
        mergeErrorList(errorList);
    }

    private void validateEmployeeField(List<StandardInsertImportDataAction.ImportData> dataList, IObjectDescribe describe) {
        //获取当前操作人可用的业务类型
        List<ImportError> errorList = Lists.newArrayList();
        List<IFieldDescribe> fields = ObjectDescribeExt.of(describe).filter(a -> Objects.equals(a.getType(), IFieldType.EMPLOYEE));
        if (CollectionUtils.empty(fields)) {
            return;
        }

        Set<String> userNames = getValueSet(fields, dataList);
        if (CollectionUtils.empty(userNames)) {
            return;
        }

        List<UserInfo> userList = serviceFacade.getUserByName(
                actionContext.getTenantId(), actionContext.getUser().getUserId(), Lists.newArrayList(userNames));
        Map<String, String> userMap = Maps.newHashMap();
        if (CollectionUtils.empty(userList)) {
            userList = Lists.newArrayList();
        }

        for (UserInfo userInfo : userList) {
            userMap.put(userInfo.getNickname(), userInfo.getId());
        }

        for (IFieldDescribe fieldDescribe : fields) {
            for (ImportData importData : dataList) {
                String deptName = getStringValue(importData.getData(), fieldDescribe);
                if (Strings.isNullOrEmpty(deptName)) {
                    continue;
                }
                if (!userMap.containsKey(deptName)) {
                    errorList.add(new ImportError(importData.getRowNo(), I18N.text(I18NKey.MEMBER_NOT_FIND, fieldDescribe.getLabel())));
                } else {
                    importData.getData().set(fieldDescribe.getApiName(), Lists.newArrayList(userMap.get(deptName)));
                }
            }
        }
        mergeErrorList(errorList);
    }

    private void validateDeptField(List<StandardInsertImportDataAction.ImportData> dataList, IObjectDescribe describe) {
        //获取当前操作人可用的业务类型
        List<ImportError> errorList = Lists.newArrayList();
        List<IFieldDescribe> fields = ObjectDescribeExt.of(describe).filter(a -> Objects.equals(a.getType(), IFieldType.DEPARTMENT));
        if (CollectionUtils.empty(fields)) {
            return;
        }

        Set<String> deptNames = getValueSet(fields, dataList);
        if (CollectionUtils.empty(deptNames)) {
            return;
        }

        List<QueryDeptByName.DeptInfo> deptList = serviceFacade.getDeptByName(
                actionContext.getTenantId(), actionContext.getUser().getUserId(), Lists.newArrayList(deptNames));
        Map<String, String> deptMap = Maps.newHashMap();
        if (CollectionUtils.empty(deptList)) {
            deptList = Lists.newArrayList();
        }

        for (QueryDeptByName.DeptInfo deptInfo : deptList) {
            deptMap.put(deptInfo.getName(), deptInfo.getId());
        }

        for (IFieldDescribe fieldDescribe : fields) {
            for (ImportData importData : dataList) {
                String deptNamesStr = getStringValue(importData.getData(), fieldDescribe);
                if (Strings.isNullOrEmpty(deptNamesStr)) {
                    continue;
                }

                convertDepDisplayName2DeptId(errorList, deptMap, fieldDescribe, importData, deptNamesStr);
            }
        }
        mergeErrorList(errorList);
    }

    protected void convertDepDisplayName2DeptId(List<ImportError> errorList, Map<String, String> deptMap, IFieldDescribe fieldDescribe, ImportData importData, String deptNamesStr) {
        String[] deptNameArray = deptNamesStr.replaceAll("；", ";").split(";");

        List<String> deptIds = Lists.newArrayList();
        for (String deptName : deptNameArray) {
            if (!deptMap.containsKey(deptName)) {
                errorList.add(new ImportError(importData.getRowNo(), I18N.text(I18NKey.DEPARTMENT_NOT_FIND, deptName)));
            } else {
                deptIds.add(deptMap.get(deptName));
            }
        }
        importData.getData().set(fieldDescribe.getApiName(), deptIds);
    }

    private Set<String> getValueSet(List<IFieldDescribe> fields, List<StandardInsertImportDataAction.ImportData> dataList) {
        Set<String> deptNames = Sets.newHashSet();
        for (IFieldDescribe fieldDescribe : fields) {
            for (ImportData importData : dataList) {
                String deptNamesStr = getStringValue(importData.getData(), fieldDescribe);
                if (Strings.isNullOrEmpty(deptNamesStr)) {
                    if (importData.containsField(fieldDescribe.getApiName())) {
                        importData.getData().set(fieldDescribe.getApiName(), null);
                    }
                    continue;
                }
                Collections.addAll(deptNames, deptNamesStr.replaceAll("；", ";").split(";"));
            }
        }
        return deptNames;
    }

    private void validateUniqueData(List<StandardInsertImportDataAction.ImportData> dataList, IObjectDescribe describe) {
        List<ImportError> errorList = Lists.newArrayList();

        Map<String, Map<String, List<ImportData>>> uniqueMap = Maps.newHashMap();
        for (ImportData importData : dataList) {
            Set<Map.Entry<String, Object>> entries = ObjectDataDocument.of(importData.getData()).entrySet();
            entries.forEach(entry -> {
                Optional<IFieldDescribe> fieldDescribeSilently = ObjectDescribeExt.of(describe).getFieldDescribeSilently(entry.getKey());
                fieldDescribeSilently.ifPresent(fieldDescribe -> {
                    String valueStr = getStringValue(importData.getData(), fieldDescribe);
                    if (fieldDescribe.isUnique() && !Strings.isNullOrEmpty(valueStr)) {
                        if (!checkUniqueInDB(importData.getData(), describe)) {
                            errorList.add(new ImportError(importData.getRowNo(), I18N.text(I18NKey.CONTENT_NOT_UNIQUE, fieldDescribe.getLabel())));
                        } else {
                            //添加到uniqueMap，判断在当前批次是否有重复的
                            if (uniqueMap.containsKey(fieldDescribe.getApiName())) {
                                Map<String, List<ImportData>> valueMap = uniqueMap.get(fieldDescribe.getApiName());
                                if (valueMap.containsKey(valueStr)) {
                                    valueMap.get(valueStr).add(importData);
                                } else {
                                    valueMap.put(valueStr, Lists.newArrayList(importData));
                                }
                            } else {
                                Map<String, List<ImportData>> valueMap = Maps.newHashMap();
                                valueMap.put(valueStr, Lists.newArrayList(importData));
                                uniqueMap.put(fieldDescribe.getApiName(), valueMap);
                            }
                        }
                    }
                });
            });
        }


        // 对价目表产品做特殊处理,不允许多个同一个产品多次出现在同一个价目表中
        if (Utils.PRICE_BOOK_PRODUCT_API_NAME.equals(describe.getApiName())) {
            Map<String, Set<Integer>> priceBookIdAndProductIdKey2Rows = Maps.newHashMap();

            for (ImportData importData : dataList) {
                String priceBookId = importData.getData().get("pricebook_id") != null ? importData.getData().get("pricebook_id").toString() : null;
                String productId = importData.getData().get("product_id") != null ? importData.getData().get("product_id").toString() : null;
                String priceBookIdAndProductIdKey = priceBookId + productId;
                if (priceBookId != null && productId != null) {
                    Set<Integer> tmpRows = priceBookIdAndProductIdKey2Rows.get(priceBookIdAndProductIdKey);
                    if (tmpRows != null) {
                        tmpRows.add(importData.getRowNo());
                    } else {
                        Set<Integer> rows = Sets.newLinkedHashSet();
                        rows.add(importData.getRowNo());
                        priceBookIdAndProductIdKey2Rows.put(priceBookIdAndProductIdKey, rows);
                    }
                }
            }
            priceBookIdAndProductIdKey2Rows.forEach((key, v) -> {
                if (v.size() > 1) {
                    //去除重复的行的数据
                    //validDataList.removeIf(o -> v.contains(o.getRowNo()));
                    v.forEach(row -> errorList.add(new ImportError(row, I18N.text(I18NKey.DO_NOT_ADD_DUPLICATE_PRODUCT_IN_SINGLE_PRICE_BOOK, v.toString())))
                    );
                }
            });
        }

        if (CollectionUtils.empty(uniqueMap)) {
            mergeErrorList(errorList);
            return;
        }

        Set<Map.Entry<String, Map<String, List<ImportData>>>> entrySet = uniqueMap.entrySet();
        for (Map.Entry<String, Map<String, List<ImportData>>> entry : entrySet) {
            Map<String, List<ImportData>> valueMap = entry.getValue();
            if (CollectionUtils.empty(valueMap)) {
                continue;
            }

            Set<Map.Entry<String, List<ImportData>>> valueEntrySet = valueMap.entrySet();
            for (Map.Entry<String, List<ImportData>> valueEntry : valueEntrySet) {
                List<ImportData> list = valueEntry.getValue();
                if (CollectionUtils.empty(list) || list.size() <= 1) {
                    continue;
                }

                //存在重复，在errorlis中加入，在validList中去掉
                StringBuilder lineNo = new StringBuilder();
                for (ImportData importData : list) {
                    lineNo.append(importData.getRowNo()).append(",");
                }
                IFieldDescribe fieldDescribe = describe.getFieldDescribe(entry.getKey());
                String message = I18N.text(I18NKey.ROW_VALUE_DUPLICATE, lineNo.toString(), fieldDescribe.getLabel());
                for (ImportData importData : list) {
                    errorList.add(new ImportError(importData.getRowNo(), message));
                }
            }
        }

        mergeErrorList(errorList);
    }

    private void validateRelatedObject(List<StandardInsertImportDataAction.ImportData> dataList, IObjectDescribe describe) {
        List<ImportError> errorList = Lists.newArrayList();

        Map<IFieldDescribe, List<String>> defObjMap = getFieldDefObjMap(ObjectDescribeExt.of(describe), dataList);
        //调用name转id服务
        batchHandleObjectReference(errorList, defObjMap, dataList);

        mergeErrorList(errorList);
    }

    private void batchHandleObjectReference(List<ImportError> errorList, Map<IFieldDescribe, List<String>> defObjMap, List<StandardInsertImportDataAction.ImportData> dataList) {
        if (CollectionUtils.empty(defObjMap)) {
            return;
        }

        Set<Map.Entry<IFieldDescribe, List<String>>> entries = defObjMap.entrySet();
        for (Map.Entry<IFieldDescribe, List<String>> entry : entries) {
            IFieldDescribe field = entry.getKey();
            String targetApiName;
            String label = field.getLabel();
            if (Objects.equals(IFieldType.OBJECT_REFERENCE, field.getType())) {
                ObjectReferenceFieldDescribe fieldDescribe = (ObjectReferenceFieldDescribe) field;
                targetApiName = fieldDescribe.getTargetApiName();
            } else {
                MasterDetailFieldDescribe fieldDescribe = (MasterDetailFieldDescribe) field;
                targetApiName = fieldDescribe.getTargetApiName();
            }
            Map<String, String> nameIdResult;

            nameIdResult = getRefDefObjData(entry.getValue(), targetApiName);

            //根据服务返回结果配置errorList和objectData的name替换成id
            boolean hasResult = !CollectionUtils.empty(nameIdResult);
            for (ImportData importData : dataList) {
                String name = getStringValue(importData.getData(), entry.getKey());
                if (Strings.isNullOrEmpty(name)) {
                    continue;
                }

                if (hasResult && nameIdResult.containsKey(name)) {
                    importData.getData().set(field.getApiName(), nameIdResult.get(name));
                } else {
                    errorList.add(new ImportError(importData.getRowNo(), I18N.text(I18NKey.RELATED_OBJECT_DATA_DELETE_OR_NO_PRIVILEGE, label)));
                }
            }
        }
    }

    private boolean checkDataPermission(String apiName) {
        return !isCurrentAdmin && !NO_CHECK_DATA_PERMISSION_OBJECTS.contains(apiName);
    }

    private Map<String, String> getRefDefObjData(List<String> nameList, String targetApiName) {
        if (CollectionUtils.empty(nameList)) {
            return null;
        }

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(nameList.size());
        query.setOffset(0);
        if (checkDataPermission(targetApiName)) {
            query.setPermissionType(1); //走数据权限

            IDataRightsParameter parameter = new DataRightsParameter();
            parameter.setSceneType("all");
            parameter.setRoleType("1");
            parameter.setCascadeDept(true);
            parameter.setCascadeSubordinates(true);
            IObjectDescribe describe = serviceFacade.findObject(actionContext.getTenantId(), targetApiName);
            ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
            parameter.setIsDetailObject(describeExt.isSlaveObject());
            if (describeExt.isSlaveObject()) {
                Optional<MasterDetailFieldDescribe> masterDetailFieldDescribe = describeExt.getMasterDetailFieldDescribe();
                masterDetailFieldDescribe.ifPresent(f -> {
                    parameter.setMasterObjectApiName(f.getTargetApiName());
                    parameter.setMasterIdFieldApiName(f.getApiName());
                });
            }
            query.setDataRightsParameter(parameter);
        }

        List<IFilter> filters = Lists.newArrayList();
        IFilter filter = new Filter();
        filter.setFieldName(IObjectData.IS_DELETED);
        filter.setFieldDefineType(IObjectDescribe.DEFINE_TYPE_SYSTEM);
        filter.setFieldValueType("string");
        filter.setOperator(Operator.EQ);
        filter.setIndexName(IObjectData.IS_DELETED);
        filter.setFieldValues(Collections.singletonList("0"));
        filters.add(filter);

        filter = new Filter();
        filter.setFieldName(IObjectData.DESCRIBE_API_NAME);
        filter.setFieldDefineType(IObjectDescribe.DEFINE_TYPE_SYSTEM);
        filter.setFieldValueType("string");
        filter.setOperator(Operator.EQ);
        filter.setIndexName(IObjectData.DESCRIBE_API_NAME);
        filter.setFieldValues(Collections.singletonList(targetApiName));
        filters.add(filter);

        filter = new Filter();
        filter.setFieldName(IObjectData.NAME);
        filter.setFieldDefineType(IObjectDescribe.DEFINE_TYPE_SYSTEM);
        filter.setFieldValueType("string");
        filter.setOperator(Operator.IN);
        filter.setIndexName(IObjectData.NAME);
        filter.setFieldValues(nameList);
        filters.add(filter);

        query.setFilters(filters);
        QueryResult<IObjectData> searchResult = serviceFacade.findBySearchQuery(actionContext.getUser(), targetApiName, query);
        if (null == searchResult || CollectionUtils.empty(searchResult.getData())) {
            return null;
        }

        List<IObjectData> dataList = searchResult.getData();
        //打日志 定位问题
        if (Objects.equals(targetApiName, Utils.CUSTOMER_PAYMENT_API_NAME)) {
            log.info("param,namelist:{}", nameList);
            log.info("result,datalist.size:{}, datalist.name:{}", dataList.size(),
                    dataList.stream().map(IObjectData::getName).collect(Collectors.toList()));
        }

        Map<String, String> result = Maps.newHashMap();
        for (String name : nameList) {
            for (IObjectData data : dataList) {
                if (Objects.equals(data.getName(), name)) {
                    result.put(name, data.getId());
                    break;
                }
            }
        }
        return result;
    }


    private void spuGenerateResult(Result result) {
        ImportResultValue resultValue = new ImportResultValue();
        if (!CollectionUtils.empty(actualList)) {
            log.info("Import data size:{}", actualList.size());
            resultValue.setImportSucceedCount(actualList.size());
        }
        //所有的错误的数据结果
        resultValue.setRowErrorList(allErrorList);
        result.setValue(resultValue);
    }

    private void fillPartnerInfo(List<IObjectData> validList, IObjectDescribe describe) {
        serviceFacade.fillOutEnterpriseInfo(actionContext.getUser(), validList, describe);
    }

    private String convertAndValidateFieldValue(ImportData data, IObjectDescribe describe) {
        //根据field类型校验
        StringBuilder stringBuilder = new StringBuilder();
        data.getData().setDescribeApiName(describe.getApiName());
        data.getData().setTenantId(describe.getTenantId());
        Map doc = ((ObjectData) data.getData()).getContainerDocument();
        List<String> fields = Lists.newArrayList(doc.keySet());
        try {
            for (String fieldApiName : fields) {
                IFieldDescribe fieldDescribe = describe.getFieldDescribe(fieldApiName);
                String errorMessage = doConvertAndValidateFieldValue(data, fieldDescribe);
                if (!Strings.isNullOrEmpty(errorMessage)) {
                    stringBuilder.append(errorMessage).append("\n");
                }
            }
            //检验级联单选的选项合法性
            String error = checkCascadedSelectOne(data.getData(), describe);
            if (!Strings.isNullOrEmpty(error)) {
                stringBuilder.append(error).append("\n");
            }
        } catch (Exception e) {
            log.error("Exception in ValidateData in BulkImportServiceImpl", e);
            stringBuilder.append(I18N.text(I18NKey.UNKNOWN_ERROR)).append("\n");
            return stringBuilder.toString();
        }

        return stringBuilder.toString();
    }

    private String doConvertAndValidateFieldValue(ImportData data, IFieldDescribe fieldDescribe) throws MetadataServiceException {
        String valueStr = getStringValue(data.getData(), fieldDescribe);
        if (fieldDescribe.isRequired() && Strings.isNullOrEmpty(valueStr)) {
            return I18N.text(I18NKey.PLEASE_INPUT, fieldDescribe.getLabel());
        }

        AbstractFieldValidator fieldValidator = getFieldValidator(fieldDescribe);
        IObjectData objectData = data.getData();
        switch (fieldDescribe.getType()) {
            case IFieldType.NUMBER:
                return doNumberFieldValidate((NumberFieldDescribe) fieldDescribe, objectData);
            case IFieldType.CURRENCY:
                return doCurrencyFieldValidate((CurrencyFieldDescribe) fieldDescribe, objectData);
            case IFieldType.DATE:
                return doDateFieldValidate(fieldValidator, (DateFieldDescribe) fieldDescribe, objectData);
            case IFieldType.DATE_TIME:
                return doDateTimeFieldValidate(fieldValidator, (DateTimeFieldDescribe) fieldDescribe, objectData);
            case IFieldType.TEXT:
                return doTextFieldValidate(fieldValidator, (TextFieldDescribe) fieldDescribe, objectData);
            case IFieldType.LONG_TEXT:
                return doLongTextFieldValidate(fieldValidator, (LongTextFieldDescribe) fieldDescribe, objectData);
            case IFieldType.SELECT_ONE:
                return doSelectOneFieldValidate((SelectOneFieldDescribe) fieldDescribe, objectData);
            case IFieldType.SELECT_MANY:
                return doSelectManyFieldValidate((SelectManyFieldDescribe) fieldDescribe, objectData);
            case IFieldType.PHONE_NUMBER:
                return doPhoneNumberFieldValidate(fieldValidator, (PhoneNumberFieldDescribe) fieldDescribe, objectData);
            case IFieldType.MULTI_LEVEL_SELECT_ONE:
                return doMultiLevelSelectOneFieldValidate((MultiLevelSelectOneFieldDescribe) fieldDescribe, objectData);
            case IFieldType.TIME:
                return doTimeFieldValidate((TimeFieldDescribe) fieldDescribe, objectData);
            case IFieldType.EMAIL:
                return doEmailFieldValidate(fieldValidator, (EmailFieldDescribe) fieldDescribe, objectData);
            case IFieldType.URL:
                return doUrlFieldValidate(fieldValidator, (URLFieldDescribe) fieldDescribe, objectData);
            case IFieldType.PERCENTILE:
                return doPercentileFieldValidate(fieldValidator, (PercentileFieldDescribe) fieldDescribe, objectData);
            case IFieldType.TRUE_OR_FALSE:
                return doBooleanFieldValidate(objectData, (BooleanFieldDescribe) fieldDescribe);
            case IFieldType.LOCATION:
                return doLocationFieldValidate(objectData, (LocationFieldDescribe) fieldDescribe);
            case IFieldType.COUNTRY:
            case IFieldType.PROVINCE:
            case IFieldType.CITY:
            case IFieldType.DISTRICT:
                return doCountryAreaFieldValidate(objectData, fieldDescribe);
            default:
                break;
        }
        return null;
    }

    private String checkCascadedSelectOne(IObjectData data, IObjectDescribe describe) {
        List<IFieldDescribe> fieldDescribes = describe.getFieldDescribes();
        if (CollectionUtils.empty(fieldDescribes)) {
            return null;
        }

        List<IFieldDescribe> selectOneList = fieldDescribes.stream().filter(item -> IFieldType.SELECT_ONE.equals(item.getType())).collect(Collectors.toList());
        if (CollectionUtils.empty(selectOneList)) {
            return null;
        }

        StringBuilder sb = new StringBuilder();
        for (IFieldDescribe fieldDescribe : selectOneList) {
            SelectOne selectOne = (SelectOneFieldDescribe) fieldDescribe;
            String parentApiName = selectOne.getCascadeParentApiName();
            if (Strings.isNullOrEmpty(parentApiName)) {
                continue;
            }

            IFieldDescribe parentField = describe.getFieldDescribe(parentApiName);
            if (!(parentField instanceof SelectOneFieldDescribe)) {
                continue;
            }

            String currentValue = String.valueOf(data.get(selectOne.getApiName()));
            String parentValue = getParentSelectOneValue(data, parentApiName);
            if (Strings.isNullOrEmpty(currentValue)) {
                continue;
            }

            if (Strings.isNullOrEmpty(parentValue)) {
                sb.append(I18N.text(I18NKey.OPTION_FALL_SHORT_SELECT_ONT_RULE, selectOne.getLabel())).append("\n");
                continue;
            }

            //父，子选项不空时
            List<ISelectOption> parentOptions = ((SelectOneFieldDescribe) parentField).getSelectOptions();
            if (CollectionUtils.empty(parentOptions)) {
                continue;
            }

            List<Map<String, List<String>>> childList = null;
            for (ISelectOption option : parentOptions) {
                if (option.getValue().equals(parentValue)) {
                    childList = option.getChildOptions();
                    break;
                }
            }

            if (CollectionUtils.empty(childList)) {
                continue;
            }


            boolean success = false;
            boolean isChildListEmpty = true;
            for (Map<String, List<String>> map : childList) {
                if (!map.containsKey(selectOne.getApiName())) {
                    continue;
                }

                List<String> validOptionList = map.get(selectOne.getApiName());
                if (CollectionUtils.empty(validOptionList)) {
                    success = true;
                    continue;
                }
                isChildListEmpty = false;
                if (validOptionList.contains(currentValue) || "null".equalsIgnoreCase(currentValue)) {
                    success = true;
                    break;
                }
            }


            //级联父选项没有依赖任何子选项，子选项填写了数据，不符合要求
            if (isChildListEmpty && !"null".equalsIgnoreCase(currentValue)) {
                sb.append(I18N.text(I18NKey.OPTION_FALL_SHORT_SELECT_ONT_RULE, selectOne.getLabel())).append("\n");
                continue;
            }

            if (success) {
                continue;
            }

            sb.append(I18N.text(I18NKey.OPTION_FALL_SHORT_SELECT_ONT_RULE, selectOne.getLabel())).append("\n");
        }

        return sb.toString();
    }

    private String doNumberFieldValidate(NumberFieldDescribe fieldDescribe, IObjectData objectData) {
        String stringValue = getStringValue(objectData, fieldDescribe);
        if (checkNumberDigit(fieldDescribe, objectData)) {
            if (Strings.isNullOrEmpty(stringValue)) {
                objectData.set(fieldDescribe.getApiName(), null);
            }
            return null;
        }

        if (fieldDescribe.getDecimalPlaces() == 0) {
            //整形
            return I18N.text(I18NKey.FORMAT_ERROR_PLEASE_INPUT_INTEGER, fieldDescribe.getLabel(), fieldDescribe.getLength());
        } else {
            return I18N.text(I18NKey.FORMAT_ERROR_PLEASE_INPUT_DECIMAL,
                    fieldDescribe.getLabel(), fieldDescribe.getLength(), fieldDescribe.getDecimalPlaces());
        }
    }

    private String doCurrencyFieldValidate(CurrencyFieldDescribe fieldDescribe, IObjectData objectData) {
        String stringValue = getStringValue(objectData, fieldDescribe);
        if (checkNumberDigit(fieldDescribe, objectData)) {
            if (Strings.isNullOrEmpty(stringValue)) {
                objectData.set(fieldDescribe.getApiName(), null);
            }
            return null;
        }

        return I18N.text(I18NKey.FORMAT_ERROR_PLEASE_INPUT_AMOUNT,
                fieldDescribe.getLabel(), fieldDescribe.getLength(), fieldDescribe.getDecimalPlaces());
    }

    private String doDateFieldValidate(AbstractFieldValidator fieldValidator, DateFieldDescribe fieldDescribe, IObjectData objectData) {
        String valueStr = getStringValue(objectData, fieldDescribe);
        if (Strings.isNullOrEmpty(valueStr)) {
            objectData.set(fieldDescribe.getApiName(), null);
            return null;
        }

        if (valueStr.contains("\n")) {
            return I18N.text(I18NKey.DATE_FORMAT_ERROR, fieldDescribe.getLabel());
        }

        Date date;
        try {
            date = sdf.parse(valueStr);
        } catch (ParseException e) {
            try {
                date = sdf2.parse(valueStr);
            } catch (ParseException ex) {
                return I18N.text(I18NKey.DATE_FORMAT_ERROR, fieldDescribe.getLabel());
            }
        }

        objectData.set(fieldDescribe.getApiName(), date.getTime());
        return null;
    }

    private String doDateTimeFieldValidate(AbstractFieldValidator fieldValidator, DateTimeFieldDescribe fieldDescribe, IObjectData objectData) {
        String valueStr = getStringValue(objectData, fieldDescribe);
        if (Strings.isNullOrEmpty(valueStr)) {
            objectData.set(fieldDescribe.getApiName(), null);
            return null;
        }

        if (valueStr.contains("\n")) {
            return I18N.text(I18NKey.DATE_TIME_FORMAT_ERROR, fieldDescribe.getLabel());
        }

        Date date;
        try {
            date = sdtf.parse(valueStr);
        } catch (ParseException e) {
            try {
                date = sdtf2.parse(valueStr);
            } catch (ParseException ex) {
                return I18N.text(I18NKey.DATE_TIME_FORMAT_ERROR, fieldDescribe.getLabel());
            }
        }
        objectData.set(fieldDescribe.getApiName(), date.getTime());

        return null;
    }

    private String doLongTextFieldValidate(AbstractFieldValidator fieldValidator, LongTextFieldDescribe fieldDescribe, IObjectData objectData) throws MetadataServiceException {
        return doTextFieldValidate(fieldValidator, fieldDescribe, objectData);
    }

    private String doTextFieldValidate(AbstractFieldValidator fieldValidator, TextFieldDescribe fieldDescribe, IObjectData objectData) throws MetadataServiceException {
        String valueStr = getStringValue(objectData, fieldDescribe);
        if (Strings.isNullOrEmpty(valueStr)) {
            objectData.set(fieldDescribe.getApiName(), null);
            return null;
        }

        IValidateResult validateResult = fieldValidator.doValidate(fieldDescribe, objectData, ActionContextExt.of(actionContext.getUser()).getContext());
        if (validateResult.isSuccess()) {
            return null;
        }

        return I18N.text(I18NKey.CONTENT_TOO_LONG, fieldDescribe.getLabel(), fieldDescribe.getMaxLength());
    }

    private String doSelectOneFieldValidate(SelectOneFieldDescribe fieldDescribe, IObjectData objectData) {
        String valueStr = getStringValue(objectData, fieldDescribe);
        if (Strings.isNullOrEmpty(valueStr)) {
            objectData.set(fieldDescribe.getApiName(), null);
            return null;
        }
        List<ISelectOption> options = fieldDescribe.getSelectOptions();
        if (CollectionUtils.empty(options)) {
            objectData.set(fieldDescribe.getApiName(), null);
            return null;
        }

        for (ISelectOption option : options) {
            if (option.getLabel().equals(valueStr)) {
                //隐藏选项不能导入
                if (option.isNotUsable()) {
                    return I18N.text(I18NKey.OPTION_NOT_USED_CAN_NOT_IMPORT, option.getLabel());
                }
                if (!option.getIsRequired()) {
                    objectData.set(fieldDescribe.getApiName(), option.getValue());
                    return null;
                }
            }
        }

        //如果是支持其他选项，且没有匹配的选项，则把值放到其他中
        boolean hasOther = false;
        for (ISelectOption option : options) {
            if (Objects.equals(option.getValue(), SelectOne.OPTION_OTHER_VALUE)
                    && Objects.equals(option.isNotUsable(), false)) {
                hasOther = true;
                break;
            }
        }
        if (hasOther) {
            objectData.set(fieldDescribe.getApiName(), SelectOne.OPTION_OTHER_VALUE);
            //如果输入的是 "其他:xxx", 取xxx
            Matcher matcher = Pattern.compile(I18N.text(I18NKey.OTHER_AND_SYMBOL)).matcher(valueStr);
            int end = 0;
            while (matcher.find()) {
                end = matcher.end();
            }
            //复选框必填但是没有填写(非 其他：xxx 形式)
            if (fieldDescribe.getOptionOtherIsRequired() && end == 0
                    && I18N.text(I18NKey.OTHER).equals(valueStr)) {
                return I18N.text(I18NKey.OTHER_OPTION_CAN_NOT_IS_NULL, fieldDescribe.getLabel());
            }
            String otherName = valueStr.substring(end);
            objectData.set(fieldDescribe.getApiName() + "__o", otherName);
            return null;
        }

        StringBuilder stringBuilder = new StringBuilder();
        for (ISelectOption option : options) {
            if (Objects.equals(option.isNotUsable(), true)) {
                continue;
            }
            stringBuilder.append("[").append(option.getLabel()).append("]");
        }

        return I18N.text(I18NKey.MUST_IS_ONE_OF, fieldDescribe.getLabel(), stringBuilder.toString());
    }

    private String doSelectManyFieldValidate(SelectManyFieldDescribe fieldDescribe, IObjectData objectData) {
        List<String> result = Lists.newArrayList();
        String valueStr = getStringValue(objectData, fieldDescribe);
        if (Strings.isNullOrEmpty(valueStr)) {
            objectData.set(fieldDescribe.getApiName(), Lists.newArrayList());
            return null;
        }
        String[] array = valueStr.split("\\|");
        List<String> list = Lists.newArrayList(array);
        List<ISelectOption> options = fieldDescribe.getSelectOptions();
        if (array.length == 0 || CollectionUtils.empty(options)) {
            objectData.set(fieldDescribe.getApiName(), Lists.newArrayList());
            return null;
        }

        for (ISelectOption option : options) {
            if (list.contains(option.getLabel())) {
                //隐藏选项不能导入
                if (option.isNotUsable()) {
                    return I18N.text(I18NKey.OPTION_NOT_USED_CAN_NOT_IMPORT, option.getLabel());
                }
                if (!option.getIsRequired() && !SelectMany.OPTION_OTHER_VALUE.equals(option.getValue())) {
                    result.add(option.getValue());
                }
            }
        }

        //如果是支持其他选项，且没有匹配的选项，则把值放到其他中
        boolean hasOther = false;
        for (ISelectOption option : options) {
            if (Objects.equals(option.getValue(), SelectMany.OPTION_OTHER_VALUE)
                    && Objects.equals(option.isNotUsable(), false)) {
                hasOther = true;
                break;
            }
        }
        StringBuilder stringBuilder = new StringBuilder();
        List<String> optionsLabelList = options.stream().map(ISelectOption::getLabel).collect(Collectors.toList());
        if (hasOther) {
            for (int i = 0; i < list.size(); i++) {
                Matcher matcher = compile.matcher(list.get(i));
                int end = 0;
                while (matcher.find()) {
                    end = matcher.end();
                }
                if (fieldDescribe.getOptionOtherIsRequired() && end == 0 && I18N.text(I18NKey.OTHER).equals(list.get(i))) {
                    return I18N.text(I18NKey.OTHER_OPTION_CAN_NOT_IS_NULL, fieldDescribe.getLabel());
                }
                if (optionsLabelList.contains(list.get(i))) {
                    continue;
                }
                if (end != 0) {
                    String value = list.get(i);
                    String otherName = value.substring(end);
                    if (fieldDescribe.getOptionOtherIsRequired() && Strings.isNullOrEmpty(otherName)) {
                        return I18N.text(I18NKey.OTHER_OPTION_CAN_NOT_IS_NULL, fieldDescribe.getLabel());
                    }
                    objectData.set(fieldDescribe.getApiName() + "__o", otherName);
                    list.set(i, value.substring(0, end - 1));
                    result.add(SelectMany.OPTION_OTHER_VALUE);
                    break;
                } else {
                    objectData.set(fieldDescribe.getApiName() + "__o", list.get(i));
                    list.set(i, I18N.text(I18NKey.OTHER));
                    result.add(SelectMany.OPTION_OTHER_VALUE);
                    break;
                }
            }
        }
        for (String item : list) {
            boolean isExist = false;
            for (ISelectOption option : options) {
                stringBuilder.append("[").append(option.getLabel()).append("]");
                if (option.getLabel().equals(item)) {
//                    result.add(option.getValue());
                    isExist = true;
                    break;
                }
            }

            if (!isExist) {
                return I18N.text(I18NKey.MUST_WITHIN_LIMITS, fieldDescribe.getLabel(), stringBuilder.toString());
            }
            stringBuilder.delete(0, stringBuilder.length());
        }

        objectData.set(fieldDescribe.getApiName(), result);
        return null;
    }

    private String doPhoneNumberFieldValidate(AbstractFieldValidator fieldValidator, PhoneNumberFieldDescribe fieldDescribe, IObjectData objectData) throws MetadataServiceException {
        String valueStr = getStringValue(objectData, fieldDescribe);
        if (Strings.isNullOrEmpty(valueStr)) {
            objectData.set(fieldDescribe.getApiName(), null);
            return null;
        }

        IValidateResult validateResult = fieldValidator.doValidate(fieldDescribe, objectData, ActionContextExt.of(actionContext.getUser()).getContext());
        if (validateResult.isSuccess()) {
            return null;
        }

        return I18N.text(I18NKey.FORMAT_ERROR_ONLY_INPUT_NUMBER_AND_SIGN_LESS_THAN_100_BYTE, fieldDescribe.getLabel());
    }

    private String doMultiLevelSelectOneFieldValidate(MultiLevelSelectOneFieldDescribe fieldDescribe, IObjectData objectData) {
        Object o = objectData.get(fieldDescribe.getApiName());
        if (null == o) {
            return null;
        }
        return null;
    }

    private String doTimeFieldValidate(TimeFieldDescribe fieldDescribe, IObjectData objectData) {
        String valueStr = getStringValue(objectData, fieldDescribe);
        if (Strings.isNullOrEmpty(valueStr)) {
            objectData.set(fieldDescribe.getApiName(), null);
            return null;
        }

        if (valueStr.contains("\n")) {
            return I18N.text(I18NKey.TIME_FORMAT_ERROR, fieldDescribe.getLabel());
        }

        Date date;
        try {
            date = stf.parse(valueStr);
        } catch (ParseException e) {
            return I18N.text(I18NKey.TIME_FORMAT_ERROR, fieldDescribe.getLabel());
        }
        objectData.set(fieldDescribe.getApiName(), date.getTime());
        return null;
    }

    private String doEmailFieldValidate(AbstractFieldValidator fieldValidator, EmailFieldDescribe fieldDescribe, IObjectData objectData) throws MetadataServiceException {
        String valueStr = getStringValue(objectData, fieldDescribe);
        if (Strings.isNullOrEmpty(valueStr)) {
            objectData.set(fieldDescribe.getApiName(), null);
            return null;
        }

        IValidateResult validateResult = fieldValidator.doValidate(fieldDescribe, objectData, ActionContextExt.of(actionContext.getUser()).getContext());
        if (validateResult.isSuccess()) {
            return null;
        }

        return I18N.text(I18NKey.FORMAT_ERROR, fieldDescribe.getLabel());
    }

    private String doUrlFieldValidate(AbstractFieldValidator fieldValidator, URLFieldDescribe fieldDescribe, IObjectData objectData) throws MetadataServiceException {
        String valueStr = getStringValue(objectData, fieldDescribe);
        if (Strings.isNullOrEmpty(valueStr)) {
            objectData.set(fieldDescribe.getApiName(), null);
            return null;
        }

        IValidateResult validateResult = fieldValidator.doValidate(fieldDescribe, objectData, ActionContextExt.of(actionContext.getUser()).getContext());
        if (validateResult.isSuccess()) {
            return null;
        }

        return I18N.text(I18NKey.FORMAT_ERROR, fieldDescribe.getLabel());
    }

    private String doPercentileFieldValidate(AbstractFieldValidator fieldValidator, PercentileFieldDescribe fieldDescribe, IObjectData objectData) throws MetadataServiceException {
        String valueStr = getStringValue(objectData, fieldDescribe);
        if (Strings.isNullOrEmpty(valueStr)) {
            objectData.set(fieldDescribe.getApiName(), null);
            return null;
        }

        String validStr = valueStr.replace("%", "");
        objectData.set(fieldDescribe.getApiName(), validStr);
        IValidateResult validateResult = fieldValidator.doValidate(fieldDescribe, objectData, ActionContextExt.of(actionContext.getUser()).getContext());
        if (validateResult.isSuccess()) {
            return null;
        }

        return I18N.text(I18NKey.FORMAT_ERROR, fieldDescribe.getLabel());
    }

    private String doBooleanFieldValidate(IObjectData data, BooleanFieldDescribe fieldDescribe) {
        String valueStr = getStringValue(data, fieldDescribe);
        if (Strings.isNullOrEmpty(valueStr)) {
            data.set(fieldDescribe.getApiName(), null);
            return null;
        }

        List<ISelectOption> selectOptions = fieldDescribe.getSelectOptions();
        if (CollectionUtils.notEmpty(selectOptions)) {
            Optional<ISelectOption> first = selectOptions.stream().filter(a -> Objects.equals(a.getLabel(), valueStr)).findFirst();
            if (first.isPresent()) {
                data.set(fieldDescribe.getApiName(), first.get().getBooleanValue());
                return null;
            }
        }

        if (I18N.text(I18NKey.TRUE).equals(valueStr)) {
            data.set(fieldDescribe.getApiName(), true);
            return null;
        } else if (I18N.text(I18NKey.FALSE).equals(valueStr)) {
            data.set(fieldDescribe.getApiName(), false);
            return null;
        }

        return I18N.text(I18NKey.FORMAT_ERROR_ONLY_INPUT_TRUE_OR_FALSE_OR_OPTION, fieldDescribe.getLabel());
    }

    private String doLocationFieldValidate(IObjectData data, LocationFieldDescribe fieldDescribe) {
        String valueStr = getStringValue(data, fieldDescribe);
        if (Strings.isNullOrEmpty(valueStr)) {
            data.set(fieldDescribe.getApiName(), null);
            return null;
        }
        data.set(fieldDescribe.getApiName(), String.format("0%s0%s%s", Location.LOCATION_SEPARATOR, Location.LOCATION_SEPARATOR, valueStr));
        return null;
    }

    private boolean checkNumberDigit(NumberFieldDescribe fieldDescribe, IObjectData objectData) {
        String stringValue = getStringValue(objectData, fieldDescribe);
        if (Strings.isNullOrEmpty(stringValue)) {
            return true;
        }
        try {
            BigDecimal bigDecimal = new BigDecimal(stringValue);
            String plain = bigDecimal.toPlainString();
            if (plain.contains(".")) {
                String[] num = plain.split("\\.");
                String intNumber = num[0];
                String decNumber = num[1];
                if (decNumber.length() <= fieldDescribe.getDecimalPlaces() && intNumber.length() <= fieldDescribe.getLength()) {
                    return true;
                }
            } else {
                //判断整数部分
                if (plain.length() <= fieldDescribe.getLength()) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.warn("Import data convert number， stringValue：{}", stringValue, e);
        }
        return false;
    }

    @Override
    protected void mergeErrorList(List<ImportError> errorList) {
        for (ImportError newError : errorList) {
            boolean isExist = false;
            for (ImportError error : allErrorList) {
                if (Objects.equals(error.getRowNo(), newError.getRowNo())) {
                    isExist = true;
                    error.setErrorMessage(String.format("%s\n%s", newError.getErrorMessage(), error.getErrorMessage()));
                    break;
                }
            }

            if (!isExist) {
                allErrorList.add(newError);
            }
        }
    }


    private void recordImportDataLog(List<IObjectData> objectData, List<IObjectData> beforeUpdateDataList, IObjectDescribe describe) {
        if (CollectionUtils.empty(objectData)) {
            return;
        }
        Map<String, IObjectData> id2DataMap = Maps.newHashMap();
        beforeUpdateDataList.forEach(o -> id2DataMap.put(o.getId(), o));

        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        parallelTask.submit(() ->
                serviceFacade.updateImportLog(actionContext.getUser(), EventType.MODIFY, ActionType.UpdateImport, describe, objectData, id2DataMap));
        parallelTask.run();
    }

}
        *
        */