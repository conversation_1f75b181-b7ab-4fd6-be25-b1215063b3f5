package com.facishare.crm.sfa.predefine.action.listener;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.ModuleCtrlConfigService;
import com.facishare.crm.sfa.predefine.service.modulectrl.IModuleInitService;
import com.facishare.crm.sfa.utilities.proxy.SalesOrderBizProxy;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.SalesOrderInterceptorModel;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.salesorderparam.SalesOrderAddBeforeModel;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.salesorderparam.SalesOrderProductVo;
import com.facishare.crm.sfa.utilities.util.SoCommonUtils;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
public class SalesOrderAddActionListener implements ActionListener<BaseObjectSaveAction.Arg,BaseObjectSaveAction.Result> {

    private static final SalesOrderBizProxy salesOrderBizProxy = SpringUtil.getContext().getBean(SalesOrderBizProxy.class);


    @Autowired
    ModuleCtrlConfigService moduleCtrlConfigService;

    @Override
    public void before(ActionContext actionContext, BaseObjectSaveAction.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext("SalesOrderInterceptor",
                "AddBefore");
        IObjectData objectData = arg.getObjectData().toObjectData();
        SalesOrderAddBeforeModel.Arg serviceArg = new SalesOrderAddBeforeModel.Arg();
        SalesOrderAddBeforeModel.SalesOrderVo vo = new SalesOrderAddBeforeModel.SalesOrderVo();
        vo.setCustomerId(objectData.get("account_id",String.class));
        vo.setWarehouseId(objectData.get("shipping_warehouse_id",String.class));
        vo.setOrderMode(objectData.get("order_mode", String.class));
        List<SalesOrderProductVo> salesOrderProductVoList = Lists.newArrayList();
        List<ObjectDataDocument> details = arg.getDetails().getOrDefault(Utils.SALES_ORDER_PRODUCT_API_NAME, Lists.newArrayList());
        if(CollectionUtils.notEmpty(details)) {
            for(ObjectDataDocument objectDataDocument : details) {
                IObjectData data = objectDataDocument.toObjectData();
                SalesOrderProductVo salesOrderProductVo = new SalesOrderProductVo();
                salesOrderProductVo.setProductId(data.get("product_id", String.class));
                salesOrderProductVo.setRecordType(data.getRecordType() == null ? "default__c" : data.getRecordType());
                if(data.get("quantity", BigDecimal.class) == null) {
                    salesOrderProductVo.setAmount(new BigDecimal(0));
                } else {
                    salesOrderProductVo.setAmount(data.get("quantity", BigDecimal.class));
                }
                /**
                 * 判断是否开启多单位
                 */
                if(moduleCtrlConfigService.openStatus(IModuleInitService.MODULE_MULTIPLE_UNIT,actionContext.getUser(),actionContext)){
                    salesOrderProductVo.setBaseUnitCount(data.get("base_unit_count", BigDecimal.class));
                }

                salesOrderProductVo.setRecordType(data.getRecordType());
                salesOrderProductVoList.add(salesOrderProductVo);
            }
            vo.setSalesOrderProductVos(salesOrderProductVoList);
        }
        serviceArg.setSalesOrderVo(vo);
        SalesOrderInterceptorModel.AddBeforeResult result = salesOrderBizProxy.addBefore(serviceArg, SoCommonUtils.getCrmHeader(context.getTenantId(), context.getUser()));
        if (result.isSuccess()){
            SalesOrderAddBeforeModel.Result data = result.getData();
            if(!Strings.isNullOrEmpty(data.getWarehouseId())) {
                arg.getObjectData().put("shipping_warehouse_id", data.getWarehouseId());
            }
        }else{
            throw new ValidateException(result.getMessage());
        }
    }

    @Override
    public void after(ActionContext actionContext, BaseObjectSaveAction.Arg arg, BaseObjectSaveAction.Result result) {
//        ServiceContext context = ContextManager.buildServiceContext("SalesOrderInterceptor",
//                "AddAfter");
//        IObjectData objectData = result.getObjectData().toObjectData();
//        IObjectData dbObjectData = serviceFacade.findObjectData(actionContext.getUser(), objectData.getId(),
//                Utils.SALES_ORDER_API_NAME);
//        SalesOrderAddAfterModel.Arg serviceArg = new SalesOrderAddAfterModel.Arg();
//        ObjectDataExt objectDataExt = ObjectDataExt.of(dbObjectData);
//        serviceArg.setDataId(objectData.getId());
//        serviceArg.setBeforeLifeStatus(ObjectLifeStatus.INEFFECTIVE.getCode());
//        serviceArg.setAfterLifeStatus(objectDataExt.getLifeStatus().getCode());
//        salesOrderInterceptorService.addAfter(context, serviceArg);
    }
}
