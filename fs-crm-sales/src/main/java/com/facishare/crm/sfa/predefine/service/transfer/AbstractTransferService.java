package com.facishare.crm.sfa.predefine.service.transfer;

import com.facishare.crmcommon.constants.SystemConstants;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.metadata.api.IObjectData;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

/**
 * @author: SunDeYu
 * @date: 2020/6/17 18:24
 * @description:
 */

public abstract class AbstractTransferService implements TransferService {

    @Autowired
    private ServiceFacade serviceFacade;

    /**
     * 保存转换的对象数据
     *
     * @param context    上下文
     * @param apiName    被转换的对象 apiName
     * @param actionCode action 类型
     * @param objectData 被保存的对象
     * @param leadsId    线索 id
     * @param partnerId  合作伙伴 id
     * @return 如果是新建数据返回新建后的数据，否则返回已存在的库中数据
     */
    protected IObjectData saveObjectData(ActionContext context, String apiName, String actionCode, IObjectData objectData, String leadsId, String partnerId) {
        ActionContext actionContext = new ActionContext(context.getRequestContext(),
                apiName, actionCode);
        IObjectData result;
        boolean isNew = actionCode.equals(SystemConstants.ActionCode.Add.getActionCode());
        if (isNew) {
            BaseObjectSaveAction.Arg arg = buildActionArg(objectData);
            BaseObjectSaveAction.Result saveResult = serviceFacade.triggerAction(actionContext, arg, BaseObjectSaveAction.Result.class);
            result = saveResult.getObjectData().toObjectData();
        } else {
            String id = objectData.getId();
            result = serviceFacade.findObjectData(context.getUser(), id, apiName);
        }
        return result;
    }

    /**
     * 封装触发 Action 的参数
     * 1. 不查重
     * 2. 不适用验证规则
     * 3. 需要权限验证
     *
     * @param objectData 被保存的数据
     * @return 返回封装好的 Action 参数
     */
    private BaseObjectSaveAction.Arg buildActionArg(IObjectData objectData) {
        BaseObjectSaveAction.Arg arg = new BaseObjectSaveAction.Arg();
        arg.setObjectData(ObjectDataDocument.of(objectData));
        BaseObjectSaveAction.OptionInfo optionInfo = new BaseObjectSaveAction.OptionInfo();
        optionInfo.setIsDuplicateSearch(false);
        optionInfo.setUseValidationRule(false);
        optionInfo.setSkipFuncValidate(true);
        arg.setOptionInfo(optionInfo);
        return arg;
    }

    /**
     * 检查 ObjectDataDocument 合法性
     *
     * @param document 待检查的对象
     * @return 如果提供的参数非空且有数据返回 true, 否则返回 false
     */
    protected boolean checkDocument(ObjectDataDocument document) {
        return Objects.nonNull(document) && document.size() > 0;
    }

    /**
     * 获取 actionCode
     *
     * @param objectData 对象数据
     * @return actionCode
     */
    protected String getActionCode(IObjectData objectData) {
        String id = objectData.getId();
        String actionCode;
        if (StringUtils.isBlank(id)) {
            actionCode = SystemConstants.ActionCode.Add.getActionCode();
        } else {
            actionCode = SystemConstants.ActionCode.Edit.getActionCode();
        }
        return actionCode;
    }


}
