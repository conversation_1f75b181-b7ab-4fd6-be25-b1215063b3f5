package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.util.SFAWorkflowUtil;
import com.facishare.paas.appframework.core.predef.action.StandardFlowCompletedAction;
import com.facishare.paas.appframework.metadata.restdriver.ObjectDataConverter;
import com.facishare.paas.appframework.metadata.restdriver.ObjectDataConverterManager;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by yuanjl on 2018/7/18.
 */
@Slf4j
public class SFAFlowCompletedAction extends StandardFlowCompletedAction {

    private static final ObjectDataConverterManager OBJECT_DATA_CONVERTER_MANAGER = SpringUtil.getContext().getBean(ObjectDataConverterManager.class);

    @Override
    protected Map<String, Object> parseCallbackData() {
        Map<String, Object> callbackData= (Map)((StandardFlowCompletedAction.Arg)this.arg).getCallbackData();
        if (arg.isPass()) {
            switch(((StandardFlowCompletedAction.Arg)this.arg).approvalFlowTriggerType()) {
                case CHOOSE:
                    if (arg.getDescribeApiName().equals(SFAPreDefineObject.Account.getApiName())) {
                        callbackData= SFAWorkflowUtil.parseAccountChooseCallbackData(callbackData);
                    } else if (arg.getDescribeApiName().equals(SFAPreDefineObject.Leads.getApiName())) {
                        callbackData= SFAWorkflowUtil.parseLeadsChooseCallbackData(callbackData);
                    }
                    break;
                case RETURN:
                    if (arg.getDescribeApiName().equals(SFAPreDefineObject.Account.getApiName())) {
                        callbackData= SFAWorkflowUtil.parseAccountReturnCallbackData(callbackData);
                    } else if (arg.getDescribeApiName().equals(SFAPreDefineObject.Leads.getApiName())) {
                        callbackData= SFAWorkflowUtil.parseLeadsReturnCallbackData(callbackData);
                    }
                    break;
                case UPDATE:
                    callbackData= parseUpdateCallbackData(callbackData);
                    break;
                case CHANGE_OWNER:
                    callbackData= SFAWorkflowUtil.parseChangeOwnerCallbackData(callbackData);
                    break;
                default:
                    break;
            }
        }

        return callbackData;
    }
    public Map<String, Object> parseUpdateCallbackData(Map<String, Object> callbackData) {
        //老审批流数据
        if (callbackData.get("ComparedFieldDict") != null) {
            Map<String, Object> newCallBackData = new HashMap<>();
            Map<String, Object> map = ((Map<String, Object>)callbackData.get("ComparedFieldDict"));
            ObjectDataConverter dataConverter = OBJECT_DATA_CONVERTER_MANAGER.getObjectDataConverter(actionContext.getObjectApiName());

            for (String key : map.keySet()) {
                newCallBackData.put(dataConverter.toNewFieldName(key), map.get(key));
            }

            callbackData = newCallBackData;
        }
        if (actionContext.getObjectApiName().equals(SFAPreDefineObject.SalesOrder.getApiName())&&callbackData.get("UDFieldDataLists") != null) {
            Map<String, Object> newCallBackData = new HashMap<>();
            Map<String, Object> map = ((Map<String, Object>)callbackData.get("UDFieldDataLists"));
            ObjectDataConverter dataConverter = OBJECT_DATA_CONVERTER_MANAGER.getObjectDataConverter(actionContext.getObjectApiName());

            for (String key : map.keySet()) {
                newCallBackData.put(dataConverter.toNewFieldName(key), map.get(key));
            }

            callbackData = newCallBackData;
        }
        return callbackData;
    }
}