package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.util.ButtonUtils;
import com.facishare.crm.sfa.utilities.util.SOI18NKeyUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

public class PriceBookProductListHeaderController extends StandardListHeaderController {
//    private ModuleCtrlConfigService moduleCtrlConfigService = SpringUtil.getContext().getBean(ModuleCtrlConfigService.class);

    @Override
    protected Result doService(Arg arg) {
        Result ret = super.doService(arg);
        List<DocumentBaseEntity> fieldLs = ret.getFieldList();
        if (CollectionUtils.isNotEmpty(fieldLs)) {
            List<DocumentBaseEntity> removeList = Lists.newArrayList();
            for (DocumentBaseEntity entity : fieldLs) {
                if (entity.keySet().contains("extend_obj_data_id")) {
                    removeList.add(entity);
                }
            }
            fieldLs.removeAll(removeList);
            ret.setFieldList(fieldLs);
        }

        List<IButton> btnList = ret.getLayout().toLayout().getButtons();
        btnList.removeIf((IButton btn) -> "IntelligentForm_button_default".equals(btn.getName()));
        ret.getLayout().toLayout().setButtons(btnList);
        return ret;
    }

    @Override
    protected List<IButton> queryBulkButton() {
        return addSpecialButtons(super.queryBulkButton());
    }

    private List<IButton> addSpecialButtons(List<IButton> buttons) {
        boolean hasEditPrivilege = serviceFacade.funPrivilegeCheck(controllerContext.getUser(), Utils.PRICE_BOOK_PRODUCT_API_NAME, ObjectAction.UPDATE.getActionCode());
        if (hasEditPrivilege) {
            buttons.addAll(Lists.newArrayList(
                    ButtonUtils.buildButton("BulkEditDiscount", I18N.text(SOI18NKeyUtils.SO_BULK_EDIT_DISCOUNT)),
                    ButtonUtils.buildButton("BulkEditSellingprice", I18N.text(SOI18NKeyUtils.SO_BULK_EDIT_SELLINGPRICE)),
                    ButtonUtils.buildButton("BulkEditPriceRange", I18N.text("PriceBookProductObj.action.bulk_edit_price_range"))
            ));
        }
        return buttons;
    }

    //    @Override
//    protected List<IButton> getButtons() {
//        List<IButton> buttons = super.getButtons();
//        //开启多单位之后删除价目表明细的新建按钮
//        if (moduleCtrlConfigService.isOpen(MODULE_MULTIPLE_UNIT, controllerContext.getUser())) {
//            buttons.removeIf(o -> "Add".equals(o.getAction()));
//        }
//        return buttons;
//    }
}
