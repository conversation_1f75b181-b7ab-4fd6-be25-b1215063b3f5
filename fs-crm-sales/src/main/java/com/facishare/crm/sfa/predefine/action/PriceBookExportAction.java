package com.facishare.crm.sfa.predefine.action;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.predef.action.StandardExportAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/5/23 17:03
 */
public class PriceBookExportAction extends StandardExportAction {
    @Override
    protected List<IFieldDescribe> findFields(String describeApiName, String recordType) {
        List<IFieldDescribe> fields = super.findFields(describeApiName, recordType);
        if (CollectionUtils.notEmpty(fields)) {
            fields.removeIf(x -> IFieldType.UseScope.equals(x.getType()));
        }
        return fields;
    }
}
