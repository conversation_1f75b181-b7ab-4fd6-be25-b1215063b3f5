package com.facishare.crm.sfa.model.Enum;


public enum DealDataTypeEnum {
    TOBE_ASSIGNED_SALES_CLUE(1, "待分配的线索",401),
    TOBE_PROCESSED_SALES_CLUE(2, "待处理的线索",402),
    TOBE_CHECKED_CUSTOMER(3, "待审核的客户报备",403),
    TOBE_CONFIRMED_CUSTOMER_TRADE(4, "待确认的成交/订单",404),
    TOBE_FINANCE_CONFIRMED_CUSTOMER_TRADE(5, "待财务确认的成交",409),
    TOBE_CONFIRMED_TRADE_REFUND(7, "待确认的退款",408),
    TOBE_FINANCE_CONFIRMED_TRADE_BILL(8, "待确认的开票",410),
    TOBE_CONFIRMED_SALE_ACTION_STAGE(9, "待确认的销售阶段",406),
    TOBE_CONFIRMED_RETURN_ORDER(10, "待确认的退单",411),
    TOBE_DELIVERY_CUSTOMER_ORDER(11, "待发货的订单",456),
    TOBE_CONFIRMED_OPPORTUNITY(12, "待确认的商机",460);


    private int code;
    private String text;
    private int sessionKey;

    DealDataTypeEnum(int code, String text,int sessionKey) {
        this.code = code;
        this.text = text;
        this.sessionKey=sessionKey;
    }

    public int getCode() {
        return code;
    }

    public int getSessionKey() {
        return sessionKey;
    }
}
