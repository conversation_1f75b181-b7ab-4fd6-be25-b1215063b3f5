package com.facishare.crm.sfa.predefine.service.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.metadata.api.IObjectData;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


public interface ValidatePromotionsForSalesOrder {
    @Data
    class Arg implements Serializable {
        @JSONField(name = "objectData")
        @JsonProperty("objectData")
        private ObjectDataDocument objectData;
        @JSONField(name = "details")
        @JsonProperty("details")
        private List<ObjectDataDocument> details;
    }

    @Data
    @Builder
    class Result {
        @JSONField(name = "status")
        @JsonProperty("status")
        private Boolean status;
        @JSONField(name = "invalidPromotionIds")
        @JsonProperty("invalidPromotionIds")
        private List<String> invalidPromotionIds;
        @JSONField(name = "productQuotas")
        @JsonProperty("productQuotas")
        private List<PromotionProductQuota> productQuotas;
        @JSONField(name = "variablePromotionIds")
        @JsonProperty("variablePromotionIds")
        private List<PromotionsRestModel.PromotionRestEntity> variablePromotions;
    }
    @Data
    @Builder
    class PromotionProductQuota {
        private String productId;
        private String promotionId;
        private String condition;
        private BigDecimal availableQuantity;
        private BigDecimal availableAmount;
    }
}
