package com.facishare.crm.sfa.predefine.service.modulectrl;

import com.facishare.crm.sfa.predefine.service.model.ConfigCtrlModule;
import com.facishare.paas.appframework.core.model.ServiceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class DefaultModuleInitService extends AbstractModuleInitService {
    @Override
    public String getModuleCode() {
        return DEFAULT_MODULE;
    }

    @Override
    public ConfigCtrlModule.Result initModule(String tenantId, String userId) {
        return ConfigCtrlModule.Result.builder()
                .errCode(ConfigCtrlModule.ResultInfo.Sucess.getErrCode())
                .errMessage(ConfigCtrlModule.ResultInfo.Sucess.getErrMessage())
                .value(
                        ConfigCtrlModule.Value.builder()
                                .openStatus(ConfigCtrlModule.OpenStatus.OPEN.toString()).build()
                ).build();
    }

    @Override
    public ConfigCtrlModule.Result initModuleRepair(ServiceContext context, String tenantId) {
        return ConfigCtrlModule.Result.builder()
                .errCode(ConfigCtrlModule.ResultInfo.Sucess.getErrCode())
                .errMessage(ConfigCtrlModule.ResultInfo.Sucess.getErrMessage())
                .value(
                        ConfigCtrlModule.Value.builder()
                                .openStatus(ConfigCtrlModule.OpenStatus.OPEN.toString()).build()
                ).build();
    }
}
