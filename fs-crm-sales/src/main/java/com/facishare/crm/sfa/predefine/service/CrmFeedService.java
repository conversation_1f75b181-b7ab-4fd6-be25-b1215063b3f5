package com.facishare.crm.sfa.predefine.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crmcommon.constants.CRMFeedConstants;
import com.facishare.crmcommon.enums.SearchTemplateEnum;
import com.facishare.crmcommon.rest.DataRightsProxy;
import com.facishare.crmcommon.rest.EsSearchProxy;
import com.facishare.crmcommon.rest.dto.AggSearchModel;
import com.facishare.crmcommon.rest.dto.DataRightsContext;
import com.facishare.crmcommon.rest.dto.FeedSearchModel;
import com.facishare.crmcommon.rest.dto.GetUserIdsSharedToMeModel;
import com.facishare.crm.sfa.model.CRMFeedModel;
import com.facishare.crm.sfa.model.Enum.DataPermissionDataKeyEnum;
import com.facishare.crm.sfa.model.Enum.DataPermissionDataRoleIDEnum;
import com.facishare.crm.sfa.model.Enum.FeedExtPermissionTypeEnum;
import com.facishare.crm.sfa.model.Enum.TeamMemberTypeEnum;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.model.Feed.CheckCrmFeedPermissionResult;
import com.facishare.crm.sfa.predefine.service.model.Feed.CheckFeedExtPermissionResult;
import com.facishare.crm.sfa.predefine.service.model.Feed.GetCrmObjectOwnerRangesResult;
import com.facishare.crm.sfa.predefine.service.model.Feed.GetFeedExtDataListResult;
import com.facishare.crm.sfa.predefine.service.model.FollowUpPermission;
import com.facishare.crm.sfa.predefine.service.real.FeedService;
import com.facishare.crm.sfa.utilities.util.*;
import com.facishare.paas.appframework.common.service.dto.QueryResponsibleDeptsByUserIds;
import com.facishare.paas.appframework.common.service.dto.UserInfo;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.APPException;
import com.facishare.paas.appframework.core.exception.ObjectDefNotFoundError;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import com.facishare.paas.appframework.privilege.model.DataPrivilegeProviderManager;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.helper.CollectionHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.Strings;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 线索一转三
 * @IgnoreI18nFile
 */
@ServiceModule("crm_feed")
@Component
@Slf4j
public class CrmFeedService {
    @Autowired
    private MetaDataFindService metaDataFindService;
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    EsSearchProxy esSearchProxy;
    @Autowired
    @Qualifier("sfaFeedService")
    private FeedService feedService;
    @Autowired
    private DataRightsProxy dataRightsProxy;

    private static final ObjectDataServiceImpl objectDataService = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);

    DataPrivilegeProviderManager providerManager = SpringUtil.getContext().getBean(DataPrivilegeProviderManager.class);


    @ServiceMethod("getCrmObjectOwnerRanges")
    public GetCrmObjectOwnerRangesResult.Result getCrmObjectOwnerRanges(ServiceContext context, GetCrmObjectOwnerRangesResult.Arg arg) {
        log.info("CrmFeedService>getCrmObjectOwnerRanges()arg=" + JsonUtil.toJsonWithNullValues(arg));

        //组织数据，根据apiname组织，获取owner
        Map<String, List<String>> maps = new HashMap<>();
        for (GetCrmObjectOwnerRangesResult.CrmObjectRelation relation : arg.getRelations()) {
            if (!maps.containsKey(relation.getApi_name())) {
                maps.put(relation.getApi_name(), Lists.newArrayList(Arrays.asList(relation.getData_id())));
            } else {
                maps.get(relation.getApi_name()).add(relation.getData_id());
            }
        }
        GetCrmObjectOwnerRangesResult.Result result = new GetCrmObjectOwnerRangesResult.Result();
        //循环取所有数据
        List<IObjectData> dataList = new ArrayList<>();
        maps.forEach((k, v) -> {
            dataList.addAll(metaDataFindService.findObjectDataByIds(context.getTenantId(), v, k));
        });
        List<String> ownerIds = new ArrayList<>();
        List<String> employeeIds = new ArrayList<>();
        dataList.forEach(r -> ownerIds.addAll(r.getOwner()));
        if (!ownerIds.isEmpty()) {
            Map<String, List<UserInfo>> leadersMap = serviceFacade.batchQuerySupervisorByUserId(context.getTenantId(), context.getUser().getUserId(), ownerIds);
            employeeIds.addAll(ownerIds);
            for (List<UserInfo> leader : leadersMap.values()) {
                employeeIds.addAll(leader
                        .stream()
                        .filter(r -> !StringUtil.isNullOrEmpty(r.getId()) && r.getId() != "0")
                        .map(UserInfo::getId)
                        .collect(Collectors.toList()));
            }
        }
        if (!employeeIds.isEmpty()) {
            result.setRanges(employeeIds);
        }

        return result;
    }

    @ServiceMethod("checkCrmFeedPermission")
    public CheckCrmFeedPermissionResult.Result checkCrmFeedPermission(ServiceContext context, CheckCrmFeedPermissionResult.Arg arg) {
        log.info("CrmFeedService>checkCrmFeedPermission()arg=" + JsonUtil.toJsonWithNullValues(arg));
        CheckCrmFeedPermissionResult.CheckCrmFeedPermissionInfo resultInfo = new CheckCrmFeedPermissionResult.CheckCrmFeedPermissionInfo();
        boolean isAdmin = serviceFacade.isAdmin(context.getUser());
        if (isAdmin) {
            resultInfo.setPermission(Integer.valueOf(Permissions.READ_ONLY.getValue()));
            return CheckCrmFeedPermissionResult.Result.builder().value(resultInfo).build();
        }

        resultInfo.setPermission(Integer.valueOf(Permissions.NO_PERMISSION.getValue()));
        //组织数据，根据apiname组织数据
        Map<String, List<String>> maps = new HashMap<>();
        for (CheckCrmFeedPermissionResult.CrmObjectRelation relation : arg.getRelations()) {
            if (relation.getApi_name().equals(SFAPreDefineObject.Visiting.getApiName())) {
                continue;
            }
            if (!maps.containsKey(relation.getApi_name())) {
                List arrayList = new ArrayList();
                arrayList.add(relation.getData_id());
                maps.put(relation.getApi_name(), arrayList);
            } else {
                maps.get(relation.getApi_name()).add(relation.getData_id());
            }
        }
        if (maps.isEmpty()) {
            return CheckCrmFeedPermissionResult.Result.builder().value(resultInfo).build();
        }

        //获取全部下属
        List<UserInfo> directSubordinates = serviceFacade.getSubordinatesByUserId(context.getTenantId(), context.getUser().getUserId(), context
                .getUser()
                .getUserId(), true);
        List<String> myLowerIds = directSubordinates.stream().map(r -> r.getId()).collect(Collectors.toList());

        //获取datapermission配置
        FollowUpPermission tempDataPermission = new FollowUpPermission();
        if (!isAdmin) {
            tempDataPermission = getDataPermission(context.getTenantId());
        }
        FollowUpPermission dataPermission = tempDataPermission;

        maps.forEach((k, v) -> {
            List<IObjectData> datas = metaDataFindService.findObjectDataByIdsIgnoreFormula(context.getTenantId(), v, k);
            IObjectDescribe describe = serviceFacade.findObject(context.getTenantId(), k);
            Map<String, Permissions> privMap = serviceFacade.checkPrivilege(context.getUser(), datas, describe, ObjectAction.VIEW_DETAIL.getActionCode());
            //处理客户、商机联合跟进人权限问题
            attachCombineSalerEnabledCheck(context.getTenantId(), dataPermission, arg.getEmployeeId(), k, privMap, datas, myLowerIds);
            privMap.forEach((dataId, permission) -> {
                if (permission == Permissions.READ_ONLY || permission == Permissions.READ_WRITE) {
                    resultInfo.setPermission(Integer.valueOf(Permissions.READ_ONLY.getValue()));
                    return;
                }
            });
        });

        //处理dataPermission获取商机、客户联合跟进人特殊权限
        return CheckCrmFeedPermissionResult.Result.builder().value(resultInfo).build();
    }

    @ServiceMethod("batchCheckCrmFeedPermission")
    public Map<String, Map<String, Permissions>> batchCheckCrmFeedPermission(ServiceContext context, CheckCrmFeedPermissionResult.Arg arg) {
        log.info("CrmFeedService>checkCrmFeedPermission()arg=" + JsonUtil.toJsonWithNullValues(arg));
        CheckCrmFeedPermissionResult.CheckCrmFeedPermissionInfo resultInfo = new CheckCrmFeedPermissionResult.CheckCrmFeedPermissionInfo();
        resultInfo.setPermission(Integer.valueOf(Permissions.NO_PERMISSION.getValue()));
        Map<String, Map<String, Permissions>> result = new HashMap<>();

        //组织数据，根据apiname组织数据
        Map<String, List<String>> maps = new HashMap<>();
        for (CheckCrmFeedPermissionResult.CrmObjectRelation relation : arg.getRelations()) {
            if (relation.getApi_name().equals(SFAPreDefineObject.Visiting.getApiName())) {
                continue;
            }
            if (!maps.containsKey(relation.getApi_name())) {
                List arrayList = new ArrayList();
                arrayList.add(relation.getData_id());
                maps.put(relation.getApi_name(), arrayList);
            } else {
                maps.get(relation.getApi_name()).add(relation.getData_id());
            }
        }

        if (maps.isEmpty()) {
            return Maps.newHashMap();
        }

        //获取全部下属
        List<UserInfo> directSubordinates = serviceFacade.getSubordinatesByUserId(context.getTenantId(), context.getUser().getUserId(), context
                .getUser()
                .getUserId(), true);
        List<String> myLowerIds = directSubordinates.stream().map(r -> r.getId()).collect(Collectors.toList());

        boolean isAdmin = serviceFacade.isAdmin(context.getUser());
        //获取datapermission配置
        FollowUpPermission tempDataPermission = new FollowUpPermission();
        if (!isAdmin) {
            tempDataPermission = getDataPermission(context.getTenantId());
        }
        FollowUpPermission dataPermission = tempDataPermission;

        maps.forEach((k, v) -> {
            List<IObjectData> datas = metaDataFindService.findObjectDataByIdsIgnoreFormula(context.getTenantId(), v, k);
            IObjectDescribe describe = serviceFacade.findObject(context.getTenantId(), k);
            Map<String, Permissions> privMap = new HashMap<>();
            if (isAdmin) {
                for (String dataId : v) {
                    privMap.put(dataId, Permissions.READ_WRITE);
                    result.put(k, privMap);
                }

            } else {
                privMap = serviceFacade.checkPrivilege(context.getUser(), datas, describe, ObjectAction.VIEW_DETAIL.getActionCode());
                //处理客户、商机联合跟进人权限问题
                attachCombineSalerEnabledCheck(context.getTenantId(), dataPermission, arg.getEmployeeId(), k, privMap, datas, myLowerIds);
                result.put(k, privMap);
            }
        });
        return result;
    }

    @ServiceMethod("checkFeedExtPermission")
    public CheckFeedExtPermissionResult.Result checkFeedExtPermission(ServiceContext context, CheckFeedExtPermissionResult.Arg arg) {
        log.info("CrmFeedService>checkFeedExtPermission()arg=" + JsonUtil.toJsonWithNullValues(arg));

        List<UserInfo> directSubordinates = serviceFacade.getSubordinatesByUserId(context.getTenantId(), context.getUser().getUserId(), arg
                .getEmployeeId()
                .toString(), true);
        List<String> myLowerIds = directSubordinates.stream().map(r -> r.getId()).collect(Collectors.toList());
        myLowerIds.add(arg.getEmployeeId().toString());

        List<String> customerIdList = new ArrayList();
        customerIdList.addAll(arg.getData().getCustomerIdList());
        if (arg.getFeedExtPermissionType() == FeedExtPermissionTypeEnum.ADD_CRM_CUSTOMER_EVENT.getCode() ||
                arg.getFeedExtPermissionType() == FeedExtPermissionTypeEnum.ADD_CRM_CUSTOMER_SERVICE.getCode() ||
                arg.getFeedExtPermissionType() == FeedExtPermissionTypeEnum.ADD_JOB_COMMON_FEED.getCode()) {
            List<String> contactIdList = new ArrayList();
            contactIdList.addAll(arg.getData().getContactIdList());
            if (!contactIdList.isEmpty()) {
                List<IObjectData> contactList = serviceFacade.findObjectDataByIds(context.getTenantId(), contactIdList, SFAPreDefineObject.Contact.getApiName());
                if (!contactIdList.isEmpty()) {
                    for (IObjectData contact : contactList) {
                        String accountID = contact.get("account_id").toString();
                        if (!StringUtil.isNullOrEmpty(accountID)) {
                            customerIdList.add(accountID);
                        }
                    }
                }
            }
        }
        CheckFeedExtPermissionResult.Result result = new CheckFeedExtPermissionResult.Result();
        if (customerIdList.isEmpty()) {
            result.setHavePermission(true);
            return result;
        }

        if (arg.getFeedExtPermissionType() == FeedExtPermissionTypeEnum.ADD_JOB_COMMON_FEED.getCode() ||
                arg.getFeedExtPermissionType() == FeedExtPermissionTypeEnum.ADD_CRM_CUSTOMER_EVENT.getCode()) {
            for (String customerId : customerIdList) {
                //todo liux
                //if (!CheckAddCrmCustomerEvent(extData.IsCRMMgr, currentEmployeeId, Adapter, Context, type, customerId, extData.EmployeeIDList))
                {
                    result.setHavePermission(false);
                    return result;
                }
            }
            result.setHavePermission(true);
        } else if (arg.getFeedExtPermissionType() == FeedExtPermissionTypeEnum.ADD_CRM_CUSTOMER_SERVICE.getCode()) {
            result.setHavePermission(true);
        } else if (arg.getFeedExtPermissionType() == FeedExtPermissionTypeEnum.GET_CRM_CUSTOMER_SERVICE_LIST.getCode()) {
            result.setHavePermission(true);
        } else if (arg.getFeedExtPermissionType() == FeedExtPermissionTypeEnum.GET_CRM_CUSTOMER_EVENT_LIST.getCode() ||
                arg.getFeedExtPermissionType() == FeedExtPermissionTypeEnum.GET_JOB_COMMON_FEED.getCode()) {
            for (String customerId : customerIdList) {
                //todo liux
                //if (CheckGetCrmCustomerEventList(currentEmployeeId, Adapter, Context, customerId, currentEmployeeId, extData.IsCRMMgr, extData.HighSeasID, extData.EmployeeIDList))
                {
                    result.setHavePermission(true);
                    return result;
                }
            }
            result.setHavePermission(false);
        }
        if (arg.getFeedExtPermissionType() == FeedExtPermissionTypeEnum.ADD_CRM_CUSTOMER_EVENT.getCode() ||
                arg.getFeedExtPermissionType() == FeedExtPermissionTypeEnum.ADD_CRM_CUSTOMER_SERVICE.getCode() ||
                arg.getFeedExtPermissionType() == FeedExtPermissionTypeEnum.ADD_JOB_COMMON_FEED.getCode()) {
            result.setData(getFeedExtPermissionResultData(context, customerIdList, arg.getFeedExtPermissionType()));
        }

        return result;
    }

    @ServiceMethod("getFeedExtDataList")
    public GetFeedExtDataListResult.Result getFeedExtDataList(ServiceContext context, GetFeedExtDataListResult.Arg arg) {
        log.info("CrmFeedService>getFeedExtDataList()arg=" + JsonUtil.toJsonWithNullValues(arg));
        StopWatch stopWatch = StopWatch.create("getFeedExtDataList create");

        List<GetFeedExtDataListResult.FeedExtDataKey> feedExtDataKeyList = arg.getFeedExtDataKeys();

        List<GetFeedExtDataListResult.FeedExtDataInfo> feedExtDataInfoList = new ArrayList<>();
        //按类型分组，分别查询
        Map<String, List<String>> map = Maps.newHashMap();
        feedExtDataKeyList.forEach(x -> {
            if (x == null) {
                return;
            }
            if (map.containsKey(x.getApi_name())) {
                map.get(x.getApi_name()).add(x.getData_id());
            } else {
                map.put(x.getApi_name(), Lists.newArrayList(x.getData_id()));
            }
        });

        IActionContext actionContext = ActionContextExt.of(context.getUser()).getContext();
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        Set<Map.Entry<String, List<String>>> entries = map.entrySet();
        //是否允许无客户权限的员工在工作流中看到完整客户名称 ，判断是否是客户负责人,如果不是，则需要处理客户的名称
        boolean isForbidNoRightSeeCompleteName = SFAConfigUtil.isAllowNoRightSeeCompleteCustomerName(context.getTenantId(), context.getUser().getUserId());
        boolean isChangeToNewOpportunity = NewOpportunityUtil.getIsChangeToNewOpportunity(context.getUser());
        GetFeedExtDataListResult.Result result = new GetFeedExtDataListResult.Result();
        for (Map.Entry<String, List<String>> entry : entries) {
            String apiName = entry.getKey();
            if (apiName.equals(SFAPreDefineObject.Visiting.getApiName())) {
                continue;
            }
            List<String> list = entry.getValue().stream().filter(a -> !Strings.isNullOrEmpty(a)).collect(Collectors.toList());
            if (org.springframework.util.CollectionUtils.isEmpty(list)) {
                return result;
            }

            if (apiName.equals(SFAPreDefineObject.CrmFeedTag.getApiName())) {
                List<Map> tagList = getCrmCustomerTagByQueryId(context, list);
                for (Map data : tagList) {
                    GetFeedExtDataListResult.FeedExtDataInfo feedExtDataInfo = new GetFeedExtDataListResult.FeedExtDataInfo();
                    feedExtDataInfo.setDataId(data.get("custom_tag_id").toString());
                    feedExtDataInfo.setApiName(SFAPreDefineObject.CrmFeedTag.getApiName());
                    feedExtDataInfo.setDataName(data.get("name").toString());
                    feedExtDataInfo.setObjectDisplayName("自定义Tag");
                    feedExtDataInfoList.add(feedExtDataInfo);
                }
            } else {
                try {
                    IObjectDescribe describe = serviceFacade.findObject(context.getTenantId(), apiName);
                    if (null == describe) {
                        return result;
                    }
                    String displayName = describe.getDisplayName();
                    stopWatch.lap("getFeedExtDataList findObjectDataByIds begin");
                    List<IObjectData> objectDataList = serviceFacade.findObjectDataByIds(context.getTenantId(), list, apiName);
                    stopWatch.lap("getFeedExtDataList findObjectDataByIds end");
                    //处理数据权限
                    Map<String, Permissions> permissionsMap = serviceFacade.checkDataPrivilege(context.getUser(), objectDataList, describe, ObjectAction.VIEW_DETAIL.getActionCode());

                    for (IObjectData data : objectDataList) {
                        GetFeedExtDataListResult.FeedExtDataInfo feedExtDataInfo = new GetFeedExtDataListResult.FeedExtDataInfo();
                        feedExtDataInfo.setDataId(data.getId());
                        feedExtDataInfo.setApiName(data.getDescribeApiName());
                        Permissions permission = permissionsMap.get(data.getId());
                        if (permission.getValue() == Permissions.NO_PERMISSION.getValue()) {
                            if (data.getDescribeApiName().equals(SFAPreDefineObject.Account.getApiName()) ||
                                    data.getDescribeApiName().equals(SFAPreDefineObject.Leads.getApiName()) ||
                                    data.getDescribeApiName().equals(SFAPreDefineObject.Opportunity.getApiName()) ||
                                    data.getDescribeApiName().equals(SFAPreDefineObject.Product.getApiName()) ||
                                    (
                                            data.getDescribeApiName().equals(SFAPreDefineObject.NewOpportunity.getApiName())
                                                    &&
                                                    isChangeToNewOpportunity
                                    )
                            ) {
                                if (isForbidNoRightSeeCompleteName) {
                                    feedExtDataInfo.setDataName(getMaskName(data.getName()));
                                } else {
                                    feedExtDataInfo.setDataName(data.getName());
                                }
                            } else {
                                feedExtDataInfo.setDataName(getMaskName(data.getName()));
                            }
                        } else {
                            feedExtDataInfo.setDataName(data.getName());
                        }
                        //feedExtDataInfo.setDataName(data.getName());
                        feedExtDataInfo.setObjectDisplayName(displayName);
                        feedExtDataInfoList.add(feedExtDataInfo);
                    }
                } catch (ObjectDefNotFoundError e) {
                    continue;
                }
            }
        }
       /* try {
            parallelTask.await(5000, TimeUnit.MILLISECONDS);
        } catch (TimeoutException e) {
            log.error("time out when execute query data in feedData", e);
        }*/
        GetFeedExtDataListResult.Arg argGetFeedExtDataList = new GetFeedExtDataListResult.Arg();
        argGetFeedExtDataList.setEmployeeId(arg.getEmployeeId());
        argGetFeedExtDataList.setFeedExtDataKeys(arg.getFeedExtDataKeys());
        String dataJson = JsonUtil.toJsonWithNullValues(argGetFeedExtDataList);
        log.info("CrmFeedService>getFeedExtDataList() call arg", dataJson);

        result.setFeedExtDataList(feedExtDataInfoList);
        return result;
    }

    private String getMaskName(String name) {
        if (com.google.common.base.Strings.isNullOrEmpty(name)) {
            return name;
        }

        int length = name.length();
        if (length > 4) {
            return String.format("%s********%s", name.substring(0, 2), name.substring(name.length() - 2));
        } else {
            return "****";
        }
    }

    private List<Map> getCrmCustomerTagByQueryId(ServiceContext context, List<String> customerTagIDs) {
        List<Map> queryResult;
        String sql = "select custom_tag_id,name from " + "custom_tag" + String.format(" where ei = '%s'", context.getTenantId()) +
                String.format(" and custom_tag_id in ('%s')", String.join("','", customerTagIDs));
        try {
            queryResult = objectDataService.findBySql(context.getTenantId(), sql);
        } catch (MetadataServiceException e) {
            throw new APPException("getCrmCustomerTagByQueryId exception", e);
        }
        return queryResult;
    }

    /**
     * 判断客户和商机的联合跟进人能否看到feed
     *
     * @param context
     * @return
     */
    private FollowUpPermission getFollowUpPermission(ServiceContext context) {
        FollowUpPermission result = new FollowUpPermission();
        //language=PostgreSQL
        String sql = String.format("select data_key, data_value from data_permission where ei=%s and ((data_role_id= 1 and data_key=1001) or (data_role_id=3 and data_key=2001))", context
                .getTenantId());
        try {
            List<Map> queryResult = objectDataService.findBySql(context.getTenantId(), sql);
            if (CollectionHelper.isNotEmpty(queryResult)) {
                queryResult.forEach(it -> {
                    Object datakey = it.get("data_key");
                    if (null != datakey && datakey.toString().equals("1001")) {
                        Object dataValue = it.get("data_value");
                        if (null != dataValue && !dataValue.toString().equals("0")) {
                            result.setCustomerFollowUpEnabled(true);
                        }
                    }

                    if (null != datakey && datakey.toString().equals("2001")) {
                        Object dataValue = it.get("data_value");
                        if (null != dataValue && !dataValue.toString().equals("0")) {
                            result.setOpportunityFollowUpEnabled(true);
                        }
                    }
                });
            }
            return result;
        } catch (MetadataServiceException e) {
            throw new APPException("getCrmCustomerTagByQueryId exception", e);
        }
    }

    /**
     * 判断客户和商机的联合跟进人能否看到feed
     *
     * @param tenantId
     * @return
     */
    private FollowUpPermission getDataPermission(String tenantId) {
        FollowUpPermission result = new FollowUpPermission();
        QueryResult<IObjectData> dataPermissionList = OpportunityUtil.getDataPermissionList(tenantId);

        if (dataPermissionList != null) {
            Iterator<IObjectData> iObjectDataIterator = dataPermissionList.getData().iterator();
            while (iObjectDataIterator.hasNext()) {
                IObjectData objectData = iObjectDataIterator.next();
                if (Objects.equals(DataPermissionDataRoleIDEnum.CUSTOMER_COMBINE_SALER.getCode(), objectData.get("data_role_id", Integer.class)) &&
                        Objects.equals(objectData.get("data_key", Integer.class), DataPermissionDataKeyEnum.VIEW_ALL_CUSTOMER_SALES_RECORDS.getCode())
                        && "1".equals(objectData.get("data_value", String.class))) {
                    result.setCustomerFollowUpEnabled(true);
                    continue;
                }
                if (Objects.equals(DataPermissionDataRoleIDEnum.OPPORTUNITY_COMBINE_SALER.getCode(), objectData.get("data_role_id", Integer.class)) &&
                        Objects.equals(objectData.get("data_key", Integer.class), DataPermissionDataKeyEnum.VIEW_ALL_OPPORTUNITY_SALES_RECORDS.getCode())
                        && "1".equals(objectData.get("data_value", String.class))) {
                    result.setOpportunityFollowUpEnabled(true);
                    continue;
                }
                if (GrayUtil.isGrayFollowUp(tenantId)) {
                    if (Objects.equals(DataPermissionDataRoleIDEnum.CUSTOMER_AFTER_SALES.getCode(), objectData.get("data_role_id", Integer.class)) &&
                            Objects.equals(objectData.get("data_key", Integer.class), DataPermissionDataKeyEnum.VIEW_ALL_CUSTOMER_SALES_RECORDS.getCode())
                            && "1".equals(objectData.get("data_value", String.class))) {
                        result.setCustomerAfterSalesEnabled(true);
                        continue;
                    }
                    if (Objects.equals(DataPermissionDataRoleIDEnum.CUSTOMER_OWNER.getCode(), objectData.get("data_role_id", Integer.class)) &&
                            Objects.equals(objectData.get("data_key", Integer.class), DataPermissionDataKeyEnum.VIEW_ALL_CUSTOMER_SALES_RECORDS.getCode())
                            && "1".equals(objectData.get("data_value", String.class))) {
                        result.setCustomerOwnerEnabled(true);
                        continue;
                    }
                    if (Objects.equals(DataPermissionDataRoleIDEnum.CUSTOMER_ORDINARY_MEMBER.getCode(), objectData.get("data_role_id", Integer.class)) &&
                            Objects.equals(objectData.get("data_key", Integer.class), DataPermissionDataKeyEnum.VIEW_ALL_CUSTOMER_SALES_RECORDS.getCode())
                            && "1".equals(objectData.get("data_value", String.class))) {
                        result.setCustomerOrdinaryMemberEnabled(true);
                        continue;
                    }
                    if (Objects.equals(DataPermissionDataRoleIDEnum.CONTACT_OWNER.getCode(), objectData.get("data_role_id", Integer.class)) &&
                            Objects.equals(objectData.get("data_key", Integer.class), DataPermissionDataKeyEnum.VIEW_ALL_CONTACT_SALES_RECORDS.getCode())
                            && "1".equals(objectData.get("data_value", String.class))) {
                        result.setContactOwnerEnabled(true);
                        continue;
                    }
                    if (Objects.equals(DataPermissionDataRoleIDEnum.CONTACT_ORDINARY_MEMBER.getCode(), objectData.get("data_role_id", Integer.class)) &&
                            Objects.equals(objectData.get("data_key", Integer.class), DataPermissionDataKeyEnum.VIEW_ALL_CONTACT_SALES_RECORDS.getCode())
                            && "1".equals(objectData.get("data_value", String.class))) {
                        result.setContactOrdinaryMemberEnabled(true);
                        continue;
                    }
                }
            }
        }
        if (!GrayUtil.isGrayFollowUp(tenantId)) {
            result.setCustomerAfterSalesEnabled(true);
            result.setCustomerOwnerEnabled(true);
            result.setCustomerOrdinaryMemberEnabled(true);
            result.setContactOwnerEnabled(true);
            result.setContactOrdinaryMemberEnabled(true);
        }
        return result;
    }

    /**
     * 处理客户、商机联合跟进人查看销售记录的权限控制
     */
    private Map<String, Permissions> attachCombineSalerEnabledCheck(String tenantId, FollowUpPermission followUpPermission,
                                                                    Integer employeeId,
                                                                    String apiName,
                                                                    Map<String, Permissions> haveRights,
                                                                    List<IObjectData> objectDataList,
                                                                    List<String> myLowerIDs) {
        if (apiName.equals(SFAPreDefineObject.Account.getApiName()) || apiName.equals(SFAPreDefineObject.Opportunity.getApiName())
                || apiName.equals(SFAPreDefineObject.Contact.getApiName())) {
            for (String dataId : haveRights.keySet()) {
                Optional<IObjectData> objectData = objectDataList.stream().filter(r -> r.getId().equals(dataId)).findFirst();
                if (objectData.isPresent()) {
                    String describeApiName = objectData.get().getDescribeApiName();
                    //获取相关团队
                    Object relevantTeamListObj = objectData.get().get(ObjectDataExt.RELEVANT_TEAM);
                    if (null != relevantTeamListObj) {
                        List<Map<String, Object>> relevantTeamList = (List<Map<String, Object>>) relevantTeamListObj;
                        if (SFAPreDefineObject.Opportunity.getApiName().equals(describeApiName) ||
                                (SFAPreDefineObject.Account.getApiName().equals(describeApiName) && !GrayUtil.isGrayFollowUp(tenantId))) {
                            for (Map<String, Object> relevantTeam : relevantTeamList) {
                                if (relevantTeam.get(TeamMember.TEAM_MEMBER_ROLE_API_NAME).toString().equals(String.valueOf(TeamMemberTypeEnum.Owner.getCode()))
                                        || relevantTeam.get(TeamMember.TEAM_MEMBER_ROLE_API_NAME).toString().equals(String.valueOf(TeamMemberTypeEnum.SalesWaiter.getCode()))
                                        || relevantTeam.get(TeamMember.TEAM_MEMBER_ROLE_API_NAME).toString().equals(String.valueOf(TeamMemberTypeEnum.GeneralStaff.getCode()))) {
                                    Boolean isTeamMemberWithOutCombineSaler = getIsTeamMemberWithOutCombineSaler(employeeId, myLowerIDs, relevantTeam);
                                    if (isTeamMemberWithOutCombineSaler) {
                                        return haveRights;
                                    }
                                }
                            }
                        }

                        for (Map<String, Object> relevantTeam : relevantTeamList) {
                            //过滤联合跟进人
                            if (Objects.equals(relevantTeam.get(TeamMember.TEAM_MEMBER_ROLE_API_NAME), String.valueOf(TeamMemberTypeEnum.Follower.getCode()))) {
                                if (SFAPreDefineObject.Account.getApiName().equals(describeApiName)) {
                                    //获取该对象联合跟进人列表
                                    Boolean isCustomerCombineSaler = getIsTeamMemberWithOutCombineSaler(employeeId, myLowerIDs, relevantTeam);
                                    if (isCustomerCombineSaler) {
                                        Permissions permission = haveRights.get(dataId);
                                        if (permission != Permissions.NO_PERMISSION && followUpPermission.isCustomerFollowUpEnabled()) {
                                            haveRights.replace(dataId, Permissions.READ_WRITE);
                                            break;
                                        } else {
                                            haveRights.replace(dataId, Permissions.NO_PERMISSION);
                                        }
                                    }
                                }
                                if (SFAPreDefineObject.Opportunity.getApiName().equals(describeApiName)) {
                                    //获取该对象联合跟进人列表
                                    Boolean isOpportunityCombineSaler = getIsTeamMemberWithOutCombineSaler(employeeId, myLowerIDs, relevantTeam);
                                    if (isOpportunityCombineSaler) {
                                        Permissions permission = haveRights.get(dataId);
                                        if (permission != Permissions.NO_PERMISSION && followUpPermission.isOpportunityFollowUpEnabled()) {
                                            haveRights.replace(dataId, Permissions.READ_WRITE);
                                            break;
                                        } else {
                                            haveRights.replace(dataId, Permissions.NO_PERMISSION);
                                        }
                                    }
                                }
                            }
                            if (GrayUtil.isGrayFollowUp(tenantId)) {
                                //过滤负责人
                                if (Objects.equals(relevantTeam.get(TeamMember.TEAM_MEMBER_ROLE_API_NAME), String.valueOf(TeamMemberTypeEnum.Owner.getCode()))) {
                                    if (SFAPreDefineObject.Account.getApiName().equals(describeApiName)) {
                                        Boolean isTeamMemberWithOutCombineSaler = getIsTeamMemberWithOutCombineSaler(employeeId, myLowerIDs, relevantTeam);
                                        if (isTeamMemberWithOutCombineSaler) {
                                            Permissions permission = haveRights.get(dataId);
                                            if (permission != Permissions.NO_PERMISSION && followUpPermission.isCustomerOwnerEnabled()) {
                                                haveRights.replace(dataId, Permissions.READ_WRITE);
                                                break;
                                            } else {
                                                haveRights.replace(dataId, Permissions.NO_PERMISSION);
                                            }
                                        }
                                    }
                                    if (SFAPreDefineObject.Contact.getApiName().equals(describeApiName)) {
                                        //获取该对象联合跟进人列表
                                        Boolean isTeamMemberWithOutCombineSaler = getIsTeamMemberWithOutCombineSaler(employeeId, myLowerIDs, relevantTeam);
                                        if (isTeamMemberWithOutCombineSaler) {
                                            Permissions permission = haveRights.get(dataId);
                                            if (permission != Permissions.NO_PERMISSION && followUpPermission.isContactOwnerEnabled()) {
                                                haveRights.replace(dataId, Permissions.READ_WRITE);
                                                break;
                                            } else {
                                                haveRights.replace(dataId, Permissions.NO_PERMISSION);
                                            }
                                        }
                                    }
                                }

                                //过滤普通成员
                                if (Objects.equals(relevantTeam.get(TeamMember.TEAM_MEMBER_ROLE_API_NAME), String.valueOf(TeamMemberTypeEnum.GeneralStaff.getCode()))) {
                                    if (SFAPreDefineObject.Account.getApiName().equals(describeApiName)) {
                                        Boolean isTeamMemberWithOutCombineSaler = getIsTeamMemberWithOutCombineSaler(employeeId, myLowerIDs, relevantTeam);
                                        if (isTeamMemberWithOutCombineSaler) {
                                            Permissions permission = haveRights.get(dataId);
                                            if (permission != Permissions.NO_PERMISSION && followUpPermission.isCustomerOrdinaryMemberEnabled()) {
                                                haveRights.replace(dataId, Permissions.READ_WRITE);
                                                break;
                                            } else {
                                                haveRights.replace(dataId, Permissions.NO_PERMISSION);
                                            }
                                        }
                                    }
                                    if (SFAPreDefineObject.Contact.getApiName().equals(describeApiName)) {
                                        //获取该对象联合跟进人列表
                                        Boolean isTeamMemberWithOutCombineSaler = getIsTeamMemberWithOutCombineSaler(employeeId, myLowerIDs, relevantTeam);
                                        if (isTeamMemberWithOutCombineSaler) {
                                            Permissions permission = haveRights.get(dataId);
                                            if (permission != Permissions.NO_PERMISSION && followUpPermission.isContactOrdinaryMemberEnabled()) {
                                                haveRights.replace(dataId, Permissions.READ_WRITE);
                                                break;
                                            } else {
                                                haveRights.replace(dataId, Permissions.NO_PERMISSION);
                                            }
                                        }
                                    }
                                }

                                //过滤售后人员
                                if (Objects.equals(relevantTeam.get(TeamMember.TEAM_MEMBER_ROLE_API_NAME), String.valueOf(TeamMemberTypeEnum.SalesWaiter.getCode()))) {
                                    if (SFAPreDefineObject.Account.getApiName().equals(describeApiName)) {
                                        Boolean isTeamMemberWithOutCombineSaler = getIsTeamMemberWithOutCombineSaler(employeeId, myLowerIDs, relevantTeam);
                                        if (isTeamMemberWithOutCombineSaler) {
                                            Permissions permission = haveRights.get(dataId);
                                            if (permission != Permissions.NO_PERMISSION && followUpPermission.isCustomerAfterSalesEnabled()) {
                                                haveRights.replace(dataId, Permissions.READ_WRITE);
                                                break;
                                            } else {
                                                haveRights.replace(dataId, Permissions.NO_PERMISSION);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return haveRights;
    }

    @NotNull
    private Boolean getIsTeamMemberWithOutCombineSaler(Integer employeeId, List<String> myLowerIDs, Map<String, Object> relevantTeam) {
        //获取该对象联合跟进人列表
        List<String> teamMemberIds = (List<String>) relevantTeam.get(TeamMember.TEAM_MEMBER_EMPLOYEE_API_NAME);
        return teamMemberIds.contains(String.valueOf(employeeId)) || teamMemberIds.stream().anyMatch(m -> myLowerIDs != null &&
                !myLowerIDs.isEmpty() && myLowerIDs.contains(m));
    }

    private List<CheckFeedExtPermissionResult.FeedExtPermissionResultData> getFeedExtPermissionResultData(ServiceContext context,
                                                                                                          List<String> customerIdList,
                                                                                                          int type) {
        List<CheckFeedExtPermissionResult.FeedExtPermissionResultData> dataList = new ArrayList<>();
        //List<int> crmMgrIDList = RoleUtil.GetCRMMgrIDList( Context );
        //获取客户的负责人、 联合跟进人的上级、CRM观察者、CRM管理员、（本版本不要 售后服务人员）
        List<IObjectData> customerList = serviceFacade.findObjectDataByIds(context.getTenantId(), customerIdList, SFAPreDefineObject.Account.getApiName());

        for (String customerId : customerIdList) {
            Optional<IObjectData> customer = customerList.stream().filter(r -> r.getId().equals(customerId)).findFirst();
            Object relevantTeamListObj = customer.get().get(ObjectDataExt.RELEVANT_TEAM);
            CheckFeedExtPermissionResult.FeedExtPermissionResultData resultData = new CheckFeedExtPermissionResult.FeedExtPermissionResultData();
            resultData.setCustomerId(customerId);
            if (relevantTeamListObj != null) {
                List<Map<String, Object>> ccsList = (List<Map<String, Object>>) relevantTeamListObj;
                //如果不是添加服务记录，则过滤掉服务人员
                if (type != FeedExtPermissionTypeEnum.ADD_CRM_CUSTOMER_SERVICE.getCode()) {
                    ccsList = ccsList.stream().filter(r -> (int) r.get("teamMemberRole") != TeamMemberTypeEnum.SalesWaiter.getCode()).collect(Collectors.toList());
                }
                List<String> employeeIdListTemp = new ArrayList<>();
                for (Map<String, Object> relevantTeam : ccsList) {
                    employeeIdListTemp.addAll((List<String>) relevantTeam.get(TeamMember.TEAM_MEMBER_EMPLOYEE_API_NAME));
                }

                if (!employeeIdListTemp.isEmpty()) {
                    Map<String, List<UserInfo>> leadersMap = serviceFacade.batchQuerySupervisorByUserId(context.getTenantId(), context
                            .getUser()
                            .getUserId(), employeeIdListTemp);

                    if (leadersMap != null && !leadersMap.isEmpty()) {
                        for (List<UserInfo> value : leadersMap.values()) {
                            employeeIdListTemp.addAll(value
                                    .stream()
                                    .filter(r -> !StringUtil.isNullOrEmpty(r.getId()) && r.getId() != "0")
                                    .map(UserInfo::getId)
                                    .collect(Collectors.toList()));
                        }
                    }
                    resultData.setEmployeeIdList(employeeIdListTemp);
                }
            }
            dataList.add(resultData);
        }
        return dataList;
    }

    @ServiceMethod("getFeedIdList2")
    public GetFeedIdListResult.Result getFeedIdList(ServiceContext context, GetFeedIdListResult.Arg arg) {
        GetFeedIdListResult.Result result = new GetFeedIdListResult.Result();
        result.setHasMore(false);
        result.setFeedIdList(Lists.newArrayList());
        SearchSourceBuilder argQuery = new SearchSourceBuilder();
        QueryBuilder filterQueryContainers = QueryBuilders.boolQuery();
        QueryBuilder mustNotQueryContainers = QueryBuilders.boolQuery();
        ObjectDescribeExt objectDescribe = feedService.findObject(context.getTenantId(), "CRMFeedObj");
        List<IFilter> filterList = Lists.newArrayList();
        String searchTemplateKey = feedService.getSearchTemplateKey(context.getUser(), objectDescribe, arg.getSearch_template_id(), arg.getSearch_query_info(), filterList);

        filterList.forEach(f -> {
            String fieldName = f.getFieldName().toLowerCase();
            if (fieldName.equals(CRMFeedConstants.Field.RELATED_TYPE)) {//销售记录关联对象类型
                String fieldValue = feedService.getFieldValue(f.getFieldValues());
                String value = String.valueOf(feedService.n2oSourceValue(fieldValue));
                f.setFieldValues(Lists.newArrayList(value));
            }
        });

        //拼接用户输入的条件
        feedService.fillQueryBuilderBySearchTemplateQuery(filterQueryContainers, filterList);

        //发送日期
        feedService.fillDateRange(filterQueryContainers, filterList, CRMFeedConstants.Field.SEND_TIME);//销售记录发送时间
        ((BoolQueryBuilder) filterQueryContainers).must(QueryBuilders.termQuery(CRMFeedConstants.OldField.IS_DELETED, false));//is_deleted
        ((BoolQueryBuilder) filterQueryContainers).must(QueryBuilders.termQuery(CRMFeedConstants.OldField.TENANT_ID, context.getTenantId()));//ei

        //        feedId
        if (arg.getSinceID() > 0) {
            RangeQueryBuilder rangequerybuilder = QueryBuilders.rangeQuery(CRMFeedConstants.OldField.SALEEVENT_FEED_ID);//feedId销售记录ID
            rangequerybuilder.lt(arg.getSinceID());
            ((BoolQueryBuilder) filterQueryContainers).must(rangequerybuilder);
        }

        //对象状态是否保持原逻辑不变 ，先保持不变，后续待调整为自定义对象标准状态
        Boolean isCRMMgr = serviceFacade.isAdmin(context.getUser());
        if (!isCRMMgr) {
            RangeQueryBuilder rangequerybuilder = QueryBuilders.rangeQuery(CRMFeedConstants.OldField.STATUS);//销售记录关联对象状态
            rangequerybuilder.lt(99);
            ((BoolQueryBuilder) filterQueryContainers).must(rangequerybuilder);
        }

        //拼接预设筛选场景条件
        feedService.fillAndQueryContainerByFilterKey(context, searchTemplateKey, isCRMMgr, filterQueryContainers, mustNotQueryContainers);
        QueryBuilder queryBuilder = QueryBuilders.boolQuery();
        ((BoolQueryBuilder) queryBuilder).filter(filterQueryContainers);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(((BoolQueryBuilder) mustNotQueryContainers).must())) {
            ((BoolQueryBuilder) queryBuilder).mustNot(mustNotQueryContainers);
        }
        argQuery.query(queryBuilder);
        argQuery
                .aggregation(AggregationBuilders.terms(CRMFeedConstants.OldField.SALEEVENT_AGG_NAME)//销售记录ES聚合名称标识
                        .field(CRMFeedConstants.OldField.SALEEVENT_FEED_ID)//销售记录ID(feedId)
                        .size(arg.getPageSize() + 1))
                .sort(new FieldSortBuilder(CRMFeedConstants.OldField.SALEEVENT_TERM_ORDER_KEY).order(SortOrder.ASC));
        //        order(org.elasticsearch.search.aggregations.bucket.terms.Terms.Order.aggregation(CRMFeedConstants.OldField.SALEEVENT_TERM_ORDER_KEY, false)))
        ;//销售记录ES聚合排序标识

        AggSearchModel.Arg restArg = new AggSearchModel.Arg();
        restArg.setEid(Integer.parseInt(context.getTenantId()));
        argQuery.sort(CRMFeedConstants.Field.SEND_TIME);
        String query = argQuery.toString().replace("\n", "").replace("\r", "").replace("\"aggregations\"", "\"aggs\"");
        log.info("argQuery:{}", query);
        restArg.setQuery(query);
        restArg.setType(CRMFeedConstants.OldField.SALEEVENT_TABLE_NAME);//销售记录ES搜索类型
        Map<String, Object> result1 = esSearchProxy.aggQuery(restArg);
        List<String> feedIdList = Lists.newArrayList();
        result1.forEach((key, val) -> feedIdList.addAll((ArrayList<String>) val));
        log.info("feedIDs:{}", StringUtils.join(feedIdList, ","));
        Boolean hasMore = false;

        if (feedIdList == null) {
            return result;
        }

        if (feedIdList.size() > arg.getPageSize()) {
            hasMore = true;
            feedIdList.remove(feedIdList.size() - 1);
        }
        result.setFeedIdList(feedIdList.stream().map(x -> Integer.parseInt(x)).collect(Collectors.toList()));
        result.setHasMore(hasMore);
        return result;
    }

    @ServiceMethod("getFeedIdList")
    public GetFeedIdListResult.Result getFeedIdList2(ServiceContext context, GetFeedIdListResult.Arg arg) {
        FeedSearchModel.Arg feedSearchArg = new FeedSearchModel.Arg();
        feedSearchArg.setTenantId(context.getTenantId());

        ObjectDescribeExt objectDescribe = feedService.findObject(context.getTenantId(), "CRMFeedObj");
        List<IFilter> filterList = Lists.newArrayList();
        String searchTemplateKey = feedService.getSearchTemplateKey(context.getUser(), objectDescribe, arg.getSearch_template_id(), arg.getSearch_query_info(), filterList);
        filterList.forEach(f -> {
            String fieldValue = feedService.getFieldValue(f.getFieldValues());
            if (StringUtils.isEmpty(fieldValue)) {
                return;
            }
            switch (f.getFieldName().toLowerCase()) {
                case CRMFeedConstants.Field.FEED_TYPE: {
                    if (!fieldValue.equals(CRMFeedConstants.OldRelatedApiName.ALL)) {
                        feedSearchArg.setFeedType(fieldValue);
                    }
                }
                break;
                case CRMFeedConstants.Field.NAME: {
                    feedSearchArg.setNameLike(fieldValue);
                }
                break;
                case CRMFeedConstants.Field.OWNER: {
                    CRMFeedModel.SearchEmployee searchEmployee = JSON.parseObject(fieldValue, CRMFeedModel.SearchEmployee.class);
                    feedSearchArg.setOwnerUserIdList(searchEmployee.getEmployee());
                }
                break;
                case CRMFeedConstants.Field.SEND_BY: {
                    CRMFeedModel.SearchEmployee searchEmployee = JSON.parseObject(fieldValue, CRMFeedModel.SearchEmployee.class);
                    feedSearchArg.setSendByUserIdList(searchEmployee.getEmployee());
                }
                break;
                case CRMFeedConstants.Field.RELATED_TYPE: {
                    String relatedType = feedService.n2oSourceValue(fieldValue);
                    if (!relatedType.equals(CRMFeedConstants.OldRelatedApiName.ALL)) {
                        feedSearchArg.setRelatedType(relatedType);
                    }
                }
                break;
                case CRMFeedConstants.Field.SEND_TIME: {
                    if (f.getOperator().equals(Operator.BETWEEN)) {
                        if (f.getFieldValues().size() == 2) {
                            feedSearchArg.setSendTimeStart(Long.parseLong(f.getFieldValues().get(0)));
                            feedSearchArg.setSendTimeEnd(Long.parseLong(f.getFieldValues().get(1)));
                        } else if (f.getFieldValues().size() == 1) {
                            Integer intValue = Integer.parseInt(f.getFieldValues().get(0));

                            if (null != intValue) {
                                long now = System.currentTimeMillis();//当前时间毫秒数
                                switch (intValue) {
                                    case 11://最近一天
                                        feedSearchArg.setSendTimeStart(now - CRMFeedConstants.Time.ONE_DAY_IN_MILLION_SECOND);
                                        break;
                                    case 2://最近一周
                                        feedSearchArg.setSendTimeStart(now - CRMFeedConstants.Time.ONE_WEEK_IN_MILLION_SECOND);
                                        break;
                                    case 4://最近一个月
                                        feedSearchArg.setSendTimeStart(now - CRMFeedConstants.Time.ONE_MONTH_IN_MILLION_SECOND);
                                        break;
                                    case 6://最近一年
                                        feedSearchArg.setSendTimeStart(now - CRMFeedConstants.Time.ONE_YEAR_IN_MILLON_SECOND);
                                        break;
                                }
                            }

                        }
                    } else if (f.getOperator().equals(Operator.GTE)) { //大于等于
                        feedSearchArg.setSendTimeStart(Long.parseLong(f.getFieldValues().get(0)));
                    } else if (f.getOperator().equals(Operator.LTE)) {//小于等于
                        feedSearchArg.setSendTimeEnd(Long.parseLong(f.getFieldValues().get(0)));
                    }
                }
                break;
                default:
                    //do nothing
            }
        });

        if (arg.getSinceID() != null) {
            feedSearchArg.setMaxFeedId(arg.getSinceID());
        }

        boolean isCRMMgr = serviceFacade.isAdmin(context.getUser());
        if (!isCRMMgr) {
            feedSearchArg.setNotStatus99(true);
        }


        if (searchTemplateKey == null) {
            searchTemplateKey = "";
        }

        if (StringUtils.isEmpty(searchTemplateKey) || searchTemplateKey.equalsIgnoreCase("all") || searchTemplateKey.equals(SearchTemplateEnum.ALL_CRMFEED.getApiName())) {
            if (!isCRMMgr) {
                List<String> userIds = getSubordinatesByUserId(context.getTenantId(), context.getUser().getUserId());
                userIds.addAll(getResponsibleDeptsByUserIds(context.getTenantId(), context.getUser()));
                userIds.add(context.getUser().getUserId());

                List<String> sourceList = Lists.newArrayList(CRMFeedConstants.OldRelatedApiName.ACCOUNTOBJ, CRMFeedConstants.OldRelatedApiName.LEADSOBJ, CRMFeedConstants.OldRelatedApiName.CONTACTOBJ, CRMFeedConstants.OldRelatedApiName.OPPORTUNITYOBJ);

                feedSearchArg.setAuthCheck(getAuthCheck(sourceList, context, userIds));
                feedSearchArg.setSourceOwners(findSourceOwners(context));
            }
        } else if (searchTemplateKey.equals(SearchTemplateEnum.IN_CHARGE_ACCOUNT_CRMFEED.getApiName())) {
            //我负责的客户CRM信息
            feedSearchArg.setOwnerUserIdList(Lists.newArrayList(context.getUser().getUserId()));
            feedSearchArg.setSourceList(Lists.newArrayList(CRMFeedConstants.OldRelatedApiName.ACCOUNTOBJ));
        } else if (searchTemplateKey.equals(SearchTemplateEnum.IN_CHARGE_CONTACT_CRMFEED.getApiName())) {
            //我负责的联系人CRM信息
            feedSearchArg.setOwnerUserIdList(Lists.newArrayList(context.getUser().getUserId()));
            feedSearchArg.setSourceList(Lists.newArrayList(CRMFeedConstants.OldRelatedApiName.CONTACTOBJ));
        } else if (searchTemplateKey.equals(SearchTemplateEnum.IN_CHARGE_OPPORTUNITY_CRMFEED.getApiName())) {
            //我负责的商机CRM信息
            feedSearchArg.setOwnerUserIdList(Lists.newArrayList(context.getUser().getUserId()));
            feedSearchArg.setSourceList(Lists.newArrayList(CRMFeedConstants.OldRelatedApiName.OPPORTUNITYOBJ));
        } else if (searchTemplateKey.equals(SearchTemplateEnum.IN_CHARGE_LEADS_CRMFEED.getApiName())) {
            //我负责的销售线索CRM信息
            feedSearchArg.setOwnerUserIdList(Lists.newArrayList(context.getUser().getUserId()));
            feedSearchArg.setSourceList(Lists.newArrayList(CRMFeedConstants.OldRelatedApiName.LEADSOBJ));
        } else if (searchTemplateKey.equals(SearchTemplateEnum.SUB_IN_CHARGE_CRMFEED.getApiName())) {
            //我下属负责的CRM信息
            feedSearchArg.setOwnerUserIdList(getSubordinatesByUserId(context.getTenantId(), context.getUser().getUserId()));
        } else if (searchTemplateKey.equals(SearchTemplateEnum.SUB_FOLLOW_UP_CRMFEED.getApiName())) {
            //我下属跟进、参与、或服务客户的CRM信息
            List<String> mySubordinateUserIdList = getSubordinatesByUserId(context.getTenantId(), context.getUser().getUserId());
            if (CollectionHelper.isEmpty(mySubordinateUserIdList)) {
                //如果没有下属，直接返回空结果
                return GetFeedIdListResult.Result.EmptyResult();
            }
            ArrayList<String> sourceList = Lists.newArrayList(CRMFeedConstants.OldRelatedApiName.ACCOUNTOBJ);
            feedSearchArg.setAuthCheck(getAuthCheck(sourceList, context, mySubordinateUserIdList));
            feedSearchArg.setNotOwnerUserIdList(mySubordinateUserIdList);

        } else if (searchTemplateKey.equals(SearchTemplateEnum.IN_CHARGE_DEPT_CRMFEED.getApiName())) {
            //我负责部门的CRM信息
            List<String> responsibleDeptsByUserIds = getResponsibleDeptsByUserIds(context.getTenantId(), context.getUser());
            if (CollectionHelper.isEmpty(responsibleDeptsByUserIds)) {
                //没有负责部门，直接返回即可
                return GetFeedIdListResult.Result.EmptyResult();
            }
            feedSearchArg.setSourceList(Lists.newArrayList(CRMFeedConstants.OldRelatedApiName.ACCOUNTOBJ, CRMFeedConstants.OldRelatedApiName.CONTACTOBJ, CRMFeedConstants.OldRelatedApiName.OPPORTUNITYOBJ, CRMFeedConstants.OldRelatedApiName.LEADSOBJ));
            feedSearchArg.setEmployeeUserIdList(getResponsibleDeptsByUserIds(context.getTenantId(), context.getUser()));
        } else {
            throw new RuntimeException("template not covered: query:" + JSON.toJSONString(arg));
        }
        if (null != arg.getPageSize()) {
            feedSearchArg.setSize(arg.getPageSize());
        }
        FeedSearchModel.Result result = esSearchProxy.feedSeach(feedSearchArg);
        return GetFeedIdListResult.Result.builder().hasMore(result.isHasMore()).feedIdList(result.getFeedIdList()).build();
    }

    /**
     * 获取我的所有下属，包含下属的下属
     *
     * @param tenantId
     * @param userId
     * @return
     */
    private List<String> getSubordinatesByUserId(String tenantId, String userId) {
        List<UserInfo> subordinatesByUserId = serviceFacade.getSubordinatesByUserId(tenantId, userId, userId, true);
        if (CollectionUtils.notEmpty(subordinatesByUserId)) {
            return subordinatesByUserId.stream().map(UserInfo::getId).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    /**
     * 获取我的下属部门（包含下属部门的下属部门）的所有员工Id
     *
     * @param tenantId
     * @param user
     * @return
     */
    private List<String> getResponsibleDeptsByUserIds(String tenantId, User user) {
        List<QueryResponsibleDeptsByUserIds.DeptInfo> deptInfoList = serviceFacade.getResponsibleDeptsByUserIds(tenantId, user.getUserId(), Lists.newArrayList(user.getUserId()), 0);
        if (CollectionHelper.isNotEmpty(deptInfoList)) {
            List<String> deptIdList = deptInfoList.stream().map(QueryResponsibleDeptsByUserIds.DeptInfo::getId).collect(Collectors.toList());
            List<String> userIdList = serviceFacade.getMembersByDeptIds(user, deptIdList);
            if (CollectionHelper.isNotEmpty(userIdList)) {
                return userIdList;
            }
        }
        return Lists.newArrayList();
    }

    /**
     * 其他人按对象共享给我的对象
     *
     * @param context
     * @return
     */
    private List<FeedSearchModel.SourceOwners> findSourceOwners(ServiceContext context) {
        List<String> apiNameList = Lists.newArrayList(CRMFeedConstants.RelatedApiName.ACCOUNTOBJ, CRMFeedConstants.RelatedApiName.OPPORTUNITYOBJ, CRMFeedConstants.RelatedApiName.CONTACTOBJ, CRMFeedConstants.RelatedApiName.LEADSOBJ);
        List<FeedSearchModel.SourceOwners> result = Lists.newArrayList();
        for (String apiName : apiNameList) {
            GetUserIdsSharedToMeModel.Arg arg = new GetUserIdsSharedToMeModel.Arg();
            DataRightsContext dataRightsContext = new DataRightsContext();
            dataRightsContext.setUserId(context.getUser().getUserId());
            dataRightsContext.setTenantId(context.getTenantId());
            dataRightsContext.setAppId(context.getAppId());
            arg.setContext(dataRightsContext);
            arg.setEntityIds(Lists.newArrayList(apiName));
            GetUserIdsSharedToMeModel.Result userIdsSharedToMe = dataRightsProxy.getUserIdsSharedToMe(arg);
            List<String> list = userIdsSharedToMe.getResult();
            if (CollectionHelper.isNotEmpty(list)) {
                result.add(FeedSearchModel.SourceOwners.builder().source(CRMFeedConstants.SOURCE_CODE_MAPPING.get(apiName)).ownerIds(list).build());
            }
        }
        return result;
    }

    private FeedSearchModel.AuthCheck getAuthCheck(List<String> sourceList, ServiceContext context, List<String> users) {
        FeedSearchModel.AuthCheck result = new FeedSearchModel.AuthCheck();
        result.setCommonSourceList(sourceList);
        result.setEmployeeIds(users);

        FollowUpPermission followUpPermission = getFollowUpPermission(context);
        //如果都允许就不用分对象了
        if (followUpPermission.isCustomerFollowUpEnabled() && followUpPermission.isOpportunityFollowUpEnabled()) {
            return result;
        }

        List<String> commonSourceList = result.getCommonSourceList();

        List<String> noFollowUpSourceList = Lists.newArrayList();
        if (!followUpPermission.isCustomerFollowUpEnabled() && commonSourceList.remove(CRMFeedConstants.OldRelatedApiName.ACCOUNTOBJ)) {
            noFollowUpSourceList.add(CRMFeedConstants.OldRelatedApiName.ACCOUNTOBJ);
        }
        if (!followUpPermission.isOpportunityFollowUpEnabled() && commonSourceList.remove(CRMFeedConstants.OldRelatedApiName.OPPORTUNITYOBJ)) {
            noFollowUpSourceList.add(CRMFeedConstants.OldRelatedApiName.OPPORTUNITYOBJ);
        }
        result.setNoFollowUpSourceList(noFollowUpSourceList);
        return result;
    }

}
