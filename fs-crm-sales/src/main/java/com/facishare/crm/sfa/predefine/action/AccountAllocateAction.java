package com.facishare.crm.sfa.predefine.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crmcommon.constants.SystemConstants;
import com.facishare.crm.sfa.model.SFAObjectPoolCommon;
import com.facishare.crm.sfa.predefine.enums.ActionCodeEnum;
import com.facishare.crm.sfa.predefine.service.model.ObjectPoolPermission;
import com.facishare.crm.sfa.predefine.service.model.SFALogModels;
import com.facishare.crm.sfa.utilities.constant.AccountConstants;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.crm.sfa.utilities.util.ObjectLimitUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.TeamMember;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.*;

/**
 * Created by yuanjl on 2018/8/8.
 * @IgnoreI18nFile
 */
@Slf4j
public class AccountAllocateAction extends BaseSFAAllocateAction {
    private User ownerUser;
    private Map<String, String> oldOwnerMap = Maps.newHashMap();

    @Override
    protected void before(BaseSFAAllocateAction.Arg arg) {
        log.info("account allocate context: {}, arg: {}", actionContext, arg);
        super.before(arg);
        ownerUser = serviceFacade.getUser(actionContext.getTenantId(), arg.getOwnerId());
        for (IObjectData objectData : objectDataList) {
            oldOwnerMap.put(objectData.getId(), AccountUtil.getOwner(objectData));
        }
        if (!ObjectLimitUtil.isGrayAccountLimit(actionContext.getTenantId())) {
            AccountUtil.checkAccountLimit(actionContext.getUser(), arg.getOwnerId(), arg.getObjectPoolId(), dataList,
                    objectPoolMemberType, arg.getOuterTenantId(), arg.getOuterOwnerId());
        } else {
            List<IObjectData> checkLimitDataList = ObjectDataExt.copyList(objectDataList);
            checkLimitDataList.forEach(x -> {
                x.set(SystemConstants.Field.LifeStatus.apiName, SystemConstants.LifeStatus.Normal.value);
                x.set(AccountConstants.Field.BIZ_STATUS, AccountConstants.AccountBizStatus.ALLOCATED.getValue());
                x.set("data_own_department", Lists.newArrayList(AccountUtil.getUserMainDepartId(actionContext.getTenantId(), arg.getOwnerId())));
                x.set("account_status", 3);
                x.set("claimed_time", System.currentTimeMillis());
                x.set("owner_modified_time", System.currentTimeMillis());
                x.setLastModifiedBy(actionContext.getUser().getUserId());
                x.setLastModifiedTime(System.currentTimeMillis());
                Integer transferCount = AccountUtil.getIntegerValue(x, "transfer_count", 0);
                x.set("transfer_count", transferCount + 1);
                String recyclingReason = AccountUtil.getRecyclingReason(actionContext.getUser(), ObjectAction.ALLOCATE, AccountUtil.getOwner(x));
                x.set("recycled_reason", recyclingReason);
            });

            if (objectPoolMemberType != null && (ObjectPoolPermission.ObjectPoolMemberType.OUTER_EMPLOYEE.getValue().equals(objectPoolMemberType.getValue())
                    || ObjectPoolPermission.ObjectPoolMemberType.OUTER_ENTERPRISE.getValue().equals(objectPoolMemberType.getValue()))) {
                List<IObjectData> tempDataList = ObjectDataExt.copyList(checkLimitDataList);
                checkLimitDataList.forEach(x -> {
                    x.set(SystemConstants.Field.OutOwner.apiName, Lists.newArrayList(arg.getOuterOwnerId()));
                    x.set(SystemConstants.Field.OutTenantId.apiName, arg.getOuterTenantId());
                    x.set(SystemConstants.Field.PartnerId.apiName, arg.getPartnerId());
                });

                tempDataList.removeIf(x -> String.valueOf(arg.getOuterOwnerId()).equals(AccountUtil.getOutOwner(x)));
                if (CollectionUtils.isNotEmpty(tempDataList)) {
                    tempDataList.forEach(x -> {
                        x.set(SystemConstants.Field.OutOwner.apiName, Lists.newArrayList(arg.getOuterOwnerId()));
                        x.set(SystemConstants.Field.OutTenantId.apiName, arg.getOuterTenantId());
                        x.set(SystemConstants.Field.PartnerId.apiName, arg.getPartnerId());
                        x.set(SystemConstants.Field.Owner.apiName, Lists.newArrayList(arg.getOwnerId()));
                    });
                    ObjectLimitUtil.CheckLimitResult checkOutLimitResult = ObjectLimitUtil.checkOutUserObjectLimit(actionContext.getUser(), actionContext.getObjectApiName(), String.valueOf(arg.getOuterTenantId()), String.valueOf(arg.getOuterOwnerId()), tempDataList, objectDescribe, true);
                    if (CollectionUtils.isNotEmpty(checkOutLimitResult.getFailureIds())) {
                        throw new ValidateException(String.format(I18N.text(SFA_REACH_LIMIT_OBJ),
                                I18N.text("AccountObj.attribute.self.display_name")));
                    }
                }
            }

            List<IObjectData> tempDataList = ObjectDataExt.copyList(checkLimitDataList);
            tempDataList.removeIf(x -> arg.getOwnerId().equals(AccountUtil.getOwner(x)));
            tempDataList.forEach(x -> x.set(SystemConstants.Field.Owner.apiName, Lists.newArrayList(arg.getOwnerId())));

            if (CollectionUtils.isNotEmpty(tempDataList)) {
                ObjectLimitUtil.CheckLimitResult checkLimitResult = ObjectLimitUtil.checkObjectLimit(actionContext.getUser(), actionContext.getObjectApiName(), arg.getOwnerId(), tempDataList, objectDescribe);
                if (CollectionUtils.isNotEmpty(checkLimitResult.getFailureIds())) {
                    throw new ValidateException(String.format(I18N.text(SFA_REACH_LIMIT_OBJ),
                            I18N.text("AccountObj.attribute.self.display_name")));
                }
            }

            AccountUtil.checkPoolAccountLimit(actionContext.getUser(), arg.getOwnerId(), arg.getObjectPoolId(), dataList,
                    objectPoolMemberType, arg.getOuterTenantId(), arg.getOuterOwnerId());
        }
    }

    @Override
    protected SFAObjectPoolCommon.Result after(Arg arg, SFAObjectPoolCommon.Result result) {
        if (isApprovalFlowStartSuccessOrAsynchronous(arg.getObjectIDs().get(0))) {
            return result;
        }
        result = super.after(arg, result);
        if (CollectionUtils.isNotEmpty(result.getSuccessList())) {
            for (String s : result.getSuccessList()) {
                // 发送重算到期时间的task
                recalculateTaskService.send(actionContext.getTenantId(), s, "AccountObj", ActionCodeEnum.ALLOCATE);
            }
        }

        try {
            ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
            task.submit(() -> {
                sendActionMq();
                sendCrmNotification();
            });
            task.run();
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

//        sendActionMq();
//        sendCrmNotification();
        return result;
    }

    @Override
    protected void addLog() {
        String newOwnerName = getOwnerName(); //ownerUser != null ? ownerUser.getUserName() : "--";

        for (IObjectData objectData : dataList) {
            String messageContent = "客户 " + objectData.getName() + " 给 " + newOwnerName + "，公海 " + objectPoolData.getName();

            SFALogModels.SFALogEntity logEntity = sfaLogService.buildLogEntity(objectPoolData, messageContent, false);
            List<SFALogModels.SFALogTextMessage> textMessageList = Lists.newArrayList();
            sfaLogService.appendNameLinkLogTextMessage(actionContext.getObjectApiName(), objectData, textMessageList);

            messageContent = " 给 " + newOwnerName + "，公海 " + objectPoolData.getName();
            sfaLogService.appendLogTextMessage(actionContext.getObjectApiName(), objectData,
                    SFALogModels.LogLinkType.NO_LINK, messageContent, textMessageList);

            logEntity.setLogTextMessageList(textMessageList);
            sfaLogService.addLog(actionContext.getUser(), logEntity, "HighSeasLog",
                    SFALogModels.LogOperationType.ALLOCATE);

            messageContent = " 给 " + newOwnerName + "，公海 " + objectPoolData.getName();
            serviceFacade.logWithCustomMessage(actionContext.getUser(), EventType.MODIFY, ActionType.ALLOCATE, objectDescribe, Lists.newArrayList(objectData), messageContent);

        }
    }

    private void sendActionMq() {
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIdsIncludeDeleted(actionContext.getUser(), arg.getObjectIDs(), actionContext.getObjectApiName());
        serviceFacade.sendActionMq(this.actionContext.getUser(), AccountUtil.fillOldData(objectDataList, dataList), ObjectAction.ALLOCATE);
    }

    private void sendCrmNotification() {
        List<String> oldOwnerIds = objectDataList.stream().filter(x -> AccountUtil.hasOwner(x))
                .map(x -> AccountUtil.getOwner(x)).collect(Collectors.toList());

        Map<String, String> oldOwnerUserNameMap = serviceFacade.getUserNameMapByIds(actionContext.getTenantId(),
                User.SUPPER_ADMIN_USER_ID, oldOwnerIds);

        String newOwnerId = arg.getOwnerId();
        User ownerUser = serviceFacade.getUser(actionContext.getTenantId(), newOwnerId);
        String newOwnerName = ownerUser != null ? ownerUser.getUserName() : I18N.text("无");

        for (IObjectData objectData : objectDataList) {
            String owner = AccountUtil.getOwner(objectData);
            String oldOwnerName = I18N.text("无");
            if (StringUtils.isNotEmpty(owner)) {
                if (oldOwnerUserNameMap.containsKey(owner)) {
                    oldOwnerName = oldOwnerUserNameMap.get(owner);
                }
            }
            List<TeamMember> teamMembers = AccountUtil.getTeamMember(objectData);
            List<String> receiverIds = teamMembers.stream().map(x -> x.getEmployee()).collect(Collectors.toList());
            receiverIds.add(newOwnerId);
            String remindContent = String.format("%s，%s：%s，%s：%s", objectData.getName(), I18N.text(SFA_ORIGINAL_MANAGER), oldOwnerName, I18N.text(SFA_NEW_DIRECTOR),
                    newOwnerName);

            AccountUtil.sendCRMNotification(actionContext.getUser(),
                    remindContent,
                    7, I18N.text(SFA_TITLE_CHANGE_OWNER_ACCOUNT), objectData.getId(), "", Lists.newArrayList(receiverIds));
        }
    }

    @Data
    @Builder
    static class ChangeOwnerActionContent {
        @JSONField(name = "OwnerID")
        String owner;
        @JSONField(name = "Title")
        String title;
        @JSONField(name = "CustomerName")
        String name;
        @JSONField(name = "CustomerID")
        String accountId;
        @JSONField(name = "OldOwnerID")
        String oldOwner;
        @JSONField(name = "Key")
        String key;
        @JSONField(name = "TrustChangeAdminEvent")
        TrustChangeAdminEvent trustChangeAdminEvent;
    }

    @Data
    @Builder
    static class TrustChangeAdminEvent {
        @JSONField(name = "EA")
        String ea;
        @JSONField(name = "ApiName")
        String objectApiName;
        @JSONField(name = "ObjectID")
        String accountId;
        @JSONField(name = "AdminID")
        String owner;
        @JSONField(name = "Name")
        String name;
        @JSONField(name = "EmployeeIds")
        List<String> teamMemberIds;
    }
}