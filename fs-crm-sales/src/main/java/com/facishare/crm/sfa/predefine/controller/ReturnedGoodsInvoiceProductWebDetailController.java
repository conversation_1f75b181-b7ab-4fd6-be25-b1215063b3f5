package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.util.LayoutUtils;
import com.facishare.crm.sfa.utilities.util.VersionUtil;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;

/**
 * <AUTHOR>
 * @date 2020/6/22 2:40 下午
 * @illustration
 */
public class ReturnedGoodsInvoiceProductWebDetailController extends StandardWebDetailController {

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        if (result.getLayout() != null) {
            WebDetailLayout.of(result.getLayout().toLayout()).setButtons(Lists.newArrayList(), "head_info");
            WebDetailLayout.of(result.getLayout().toLayout()).setButtons(Lists.newArrayList(), "sale_log");
        }
        return result;
    }
}
