package com.facishare.crm.sfa.predefine.action.listener;

import com.facishare.crm.sfa.utilities.proxy.SalesOrderBizProxy;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.SalesOrderInterceptorModel;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.salesorderparam.SalesOrderInvalidAfterModel;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.salesorderparam.SalesOrderInvalidBeforeModel;
import com.facishare.crm.sfa.utilities.util.SoCommonUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ContextManager;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.metadata.util.SpringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2019/8/7 16:55
 */
@Component
public class SalesOrderInvalidActionListener extends StandardInvalidActionListener {

    private static final SalesOrderBizProxy salesOrderBizProxy = SpringUtil.getContext().getBean(SalesOrderBizProxy.class);


    @Override
    protected void callBeforeInterceptor(SalesOrderInvalidBeforeModel.Arg arg) {
        ServiceContext context = ContextManager.buildServiceContext("SalesOrderInterceptor",
                "InvalidBefore");
//        salesOrderInterceptorService.invalidBefore(context,arg);
        SalesOrderInterceptorModel.InvalidBeforeResult invalidBeforeResult = salesOrderBizProxy.invalidBefore(arg, SoCommonUtils.getCrmHeader(context.getTenantId(), context.getUser()));
        if(!invalidBeforeResult.isSuccess()){
            throw new ValidateException(invalidBeforeResult.getMessage());
        }
    }

    @Override
    protected void callAfterInterceptor(SalesOrderInvalidAfterModel.Arg arg) {
    }
}
