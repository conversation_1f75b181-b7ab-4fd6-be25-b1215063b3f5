package com.facishare.crm.sfa.predefine.importobject;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.metadata.importobject.DefaultObjectImportProvider;
import com.facishare.paas.appframework.metadata.importobject.ImportObject;
import com.facishare.paas.appframework.metadata.importobject.ImportType;
import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

/**
 * Created by renlb on 2019/5/9.
 */
@Component
public class ReturnedGoodsInvoiceProductObjectImportProvider extends DefaultObjectImportProvider {
    @Autowired
    private ServiceFacade serviceFacade;
    @Override
    public String getObjectCode() {
        return SFAPreDefineObject.ReturnedGoodsInvoiceProduct.getApiName();
    }

    @Override
    public Optional<ImportObject> getImportObject(IObjectDescribe objectDescribe, IUniqueRule uniqueRule) {
        if (SFAConfigUtil.isStockEnabled(RequestContextManager.getContext().getTenantId())) {
            return Optional.empty();
        }
        Map<String, Boolean> map = serviceFacade.funPrivilegeCheck(RequestContextManager.getContext().getUser(),
                Utils.RETURN_GOODS_INVOICE_API_NAME, Lists.newArrayList(ObjectAction.BATCH_IMPORT.getActionCode()));
        if(!MapUtils.getBoolean(map,ObjectAction.BATCH_IMPORT.getActionCode(),false)){
            return Optional.empty();
        }
        Optional<ImportObject> result = super.getImportObject(objectDescribe, uniqueRule);
        return result;
    }

    @Override
    protected ImportType getImportType(IObjectDescribe objectDescribe, IUniqueRule uniqueRule) {
        return ImportType.UNSUPPORT_UPDATE_IMPORT;
    }
}
