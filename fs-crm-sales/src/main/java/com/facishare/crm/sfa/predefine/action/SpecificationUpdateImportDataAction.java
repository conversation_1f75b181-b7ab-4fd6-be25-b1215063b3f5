package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.service.CrmSpecService;
import com.facishare.crm.sfa.predefine.service.model.CrmDeletedSpecOrSpecValue;
import com.facishare.crm.sfa.utilities.util.SOI18NKeyUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.predef.action.BaseImportAction;
import com.facishare.paas.appframework.core.predef.action.StandardUpdateImportDataAction;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR> 2020-01-10
 * @instruction 更新的时候判断规格是否被引用
 */
public class SpecificationUpdateImportDataAction extends StandardUpdateImportDataAction {
//    private CrmSpecService crmSpecService = SpringUtil.getContext().getBean(CrmSpecService.class);
//
//
//    @Override
//    protected void customValidate(List<ImportData> dataList) {
//        super.customValidate(dataList);
//        List<ImportError> errorList = checkSpecOrValue(dataList);
//        mergeErrorList(errorList);
//    }
//
//    private List<ImportError> checkSpecOrValue(List<ImportData> dataList) {
//        List<ImportError> errorList = Lists.newArrayList();
//        CrmDeletedSpecOrSpecValue.Arg arg1 = new CrmDeletedSpecOrSpecValue.Arg();
//        arg1.setSpecList(dataList.stream().map(o -> o.getData().getId()).collect(Collectors.toList()));
//
//        Map<String, Integer> objIdToRowNo = dataList.stream().collect(Collectors.toMap(o -> o.getData().getId(), o -> o.getRowNo()));
//
//        CrmDeletedSpecOrSpecValue.Arg usedObject = crmSpecService.usedSpecOrSpecValue(arg1, actionContext.getUser());
//        List<String> usedSpecIdList = usedObject.getSpecList();
//        if (CollectionUtils.isEmpty(usedSpecIdList)) return errorList;
//        for (String usedSpecId : usedSpecIdList) {
//            errorList.add(new BaseImportAction.ImportError(objIdToRowNo.get(usedSpecId), I18N.text(SOI18NKeyUtils.SO_SPEC_CHECK_USED)));
//        }
//        return errorList;
//    }
}
