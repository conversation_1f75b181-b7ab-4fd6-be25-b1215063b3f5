package com.facishare.crm.sfa.predefine.action;

import com.facishare.crmcommon.constants.SystemConstants;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.model.Enum.LeadsBizStatusEnum;
import com.facishare.crm.sfa.model.Enum.SessionBOCItemKeys;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.exception.SFABusinessException;
import com.facishare.crm.sfa.predefine.exception.SFAErrorCode;
import com.facishare.crm.sfa.predefine.service.LeadsDuplicatedProcessingService;
import com.facishare.crm.sfa.predefine.service.LeadsPoolServiceImpl;
import com.facishare.crm.sfa.predefine.service.QiXinTodoService;
import com.facishare.crm.sfa.predefine.service.SFALogService;
import com.facishare.crm.sfa.predefine.service.model.SFALogModels;
import com.facishare.crm.sfa.predefine.service.task.LeadsAllocateTaskService;
import com.facishare.crm.sfa.predefine.service.task.LeadsDuplicatedProcessingTaskService;
import com.facishare.crm.sfa.predefine.service.task.LeadsOverTimeTaskService;
import com.facishare.crm.sfa.utilities.constant.LeadsConstants;
import com.facishare.crm.sfa.utilities.util.*;
import com.facishare.crmcommon.util.CommonSqlUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.predef.action.BaseImportAction;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportDataAction;
import com.facishare.paas.appframework.metadata.AutoNumberLogicService;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectMappingService;
import com.facishare.paas.appframework.metadata.ObjectMappingServiceImpl;
import com.facishare.paas.appframework.metadata.dto.UniqueRuleSearchResult;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IObjectMappingRuleInfo;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.ICommonSqlService;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.service.impl.CommonSqlServiceImpl;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.*;

@Slf4j
public class LeadsInsertImportDataAction extends StandardInsertImportDataAction {
    private static final LeadsPoolServiceImpl poolService = SpringUtil.getContext().getBean(LeadsPoolServiceImpl.class);
    private static final ObjectMappingService objectMappingService = SpringUtil.getContext().getBean(ObjectMappingServiceImpl.class);
    private static final ServiceFacade serviceFacade = SpringUtil.getContext().getBean(ServiceFacade.class);
    LeadsAllocateTaskService leadsAllocateTaskService = SpringUtil.getContext().getBean(LeadsAllocateTaskService.class);
    private static final QiXinTodoService qiXinTodoService = SpringUtil.getContext().getBean(QiXinTodoService.class);
    private static final SFALogService sfaLogService = SpringUtil.getContext().getBean(SFALogService.class);
    LeadsOverTimeTaskService leadsOverTimeTaskService = SpringUtil.getContext().getBean(LeadsOverTimeTaskService.class);
    LeadsDuplicatedProcessingTaskService processingTaskService = SpringUtil.getContext().getBean(LeadsDuplicatedProcessingTaskService.class);
    LeadsDuplicatedProcessingService processingService = SpringUtil.getContext().getBean(LeadsDuplicatedProcessingService.class);
    private static ICommonSqlService commonSqlService = SpringUtil.getContext().getBean(CommonSqlServiceImpl.class);
    private static AutoNumberLogicService autoNumberLogicService = SpringUtil.getContext().getBean(AutoNumberLogicService.class);

    @Override
    protected void findDescribe() {
        this.objectDescribe = serviceFacade.findObject(this.actionContext.getTenantId(), this.actionContext.getObjectApiName());
        LeadsUtils.setImportFields(arg.getObjectCode(), objectDescribe.getFieldDescribes());
    }

    @Override
    protected void getPhoneNumberInfo(List<ImportData> dataList) {
        for (ImportData data:dataList) {
            if(!org.springframework.util.StringUtils.isEmpty(data.getData().get("mobile"))){
                String phoneNum = data.getData().get("mobile").toString();
                PhoneUtil.Result result = PhoneUtil.getPhoneNumberInfo(phoneNum);
                if(!org.springframework.util.StringUtils.isEmpty(result)){
                    data.getData().set("phone_number_attribution_country",result.getCountry());
                    data.getData().set("phone_number_attribution_province",result.getProvince());
                    data.getData().set("phone_number_attribution_city",result.getCity());
                }
            }
        }
    }

    @Override
    protected void customValidate(List<ImportData> dataList) {
        super.customValidate(dataList);
        boolean isUnionDuplicateChecking = arg.getIsUnionDuplicateChecking();
        List<ImportError> errorList = Lists.newArrayList();
        List<String> poolIds = dataList.stream().
                map(d -> d.getData().get(LeadsConstants.Field.LEADS_POOL_ID.getApiName(), String.class))
                .filter(p -> StringUtils.isNotEmpty(p)).collect(Collectors.toList());
        log.info("import_leads_id: {}", poolIds);
        Map<String, List<String>> memberMap = poolService.getPoolMembersByIds(actionContext.getUser(), poolIds);
        List<IObjectMappingRuleInfo> mappingRuleInfos = objectMappingService.findByApiName(actionContext.getUser(), "rule_leadsobj2accountobj__c");
        List<IObjectData> accountDataList = Lists.newArrayList();
        for (ImportData d : dataList) {
            IObjectData leadsData = d.getData();
            List<String> ownerIds = leadsData.getOwner();
            String poolId = leadsData.get(LeadsConstants.Field.LEADS_POOL_ID.getApiName(), String.class);
            if (CollectionUtils.isNotEmpty(ownerIds) && StringUtils.isNotEmpty(poolId)) {
                if (!memberMap.containsKey(poolId) || !memberMap.get(poolId).stream().anyMatch(m -> ownerIds.contains(m))) {
                    errorList.add(new ImportError(d.getRowNo(), String.format(I18N.text(SFA_LEADS_OWNER_NOT_IN_LEADS_POOL),
                            I18N.text("LeadsObj.attribute.self.display_name"),
                            I18N.text("LeadsPoolObj.attribute.self.display_name"))));
                    continue;
                }
            }
            if (isUnionDuplicateChecking) {
                String uuId = UUID.randomUUID().toString();
                leadsData.set("unique_search_id", uuId);
                if (CollectionUtils.isNotEmpty(mappingRuleInfos)) {
                    IObjectMappingRuleInfo mappingRuleInfo = mappingRuleInfos.get(0);
                    if (mappingRuleInfo != null && CollectionUtils.isNotEmpty(mappingRuleInfo.getFieldMapping())) {
                        IObjectData accountData = new ObjectData();
                        mappingRuleInfo.getFieldMapping().forEach(r ->
                        {
                            String sourceFieldName = r.getSourceFieldName();
                            String targetFieldName = r.getTargetFieldName();
                            accountData.set(targetFieldName, leadsData.get(sourceFieldName));
                            accountData.setId(uuId);
                            accountDataList.add(accountData);
                        });
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(accountDataList)) {
            IObjectDescribe accountDescribe = serviceFacade.findObject(actionContext.getTenantId(), SFAPreDefineObject.Account.getApiName());
            List<UniqueRuleSearchResult.DuplicateData> duplicateDataList = serviceFacade.
                    findDuplicateData(actionContext.getUser(), accountDataList, accountDescribe);
            List<String> duplicateDataIdList = duplicateDataList.stream().filter(d -> CollectionUtils.isNotEmpty(d.getDataIds()))
                    .map(d -> d.getId()).collect(Collectors.toList());
            List<ImportData> duplidateDataList =
                    dataList.stream().filter(d -> duplicateDataIdList.contains(d.getData().get("unique_search_id"))).collect(Collectors.toList());
            duplidateDataList.forEach(d -> errorList.add(new ImportError(d.getRowNo(),
                    I18N.text(SFA_EXITS_MULTIPLE_OBJECT))));
        }

        if (ObjectLimitUtil.isGrayLeadsLimit(actionContext.getTenantId())) {
            Set<String> ownerList = dataList.stream()
                    .filter(x -> AccountUtil.hasOwner(x.getData())).map(x -> AccountUtil.getOwner(x.getData()))
                    .collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(ownerList)) {
                for (String owner : ownerList) {
                    List<IObjectData> ownerDataList = dataList.stream()
                            .filter(x -> owner.equals(AccountUtil.getOwner(x.getData()))).map(x -> x.getData())
                            .collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(ownerDataList)) {
                        continue;
                    }
                    ownerDataList.forEach(x -> x.set(SystemConstants.Field.Id.apiName, serviceFacade.generateId()));
                    List<IObjectData> checkLimitDataList = ObjectDataExt.copyList(ownerDataList);
                    checkLimitDataList.forEach(x -> {
                        x.set(SystemConstants.Field.LifeStatus.apiName, SystemConstants.LifeStatus.Normal.value);
                        x.set(LeadsConstants.Field.BIZ_STATUS.getApiName(), LeadsBizStatusEnum.UN_PROCESSED.getValue());
                        x.set("last_follow_time", System.currentTimeMillis());
                        x.set("last_follower", Lists.newArrayList(owner));
                        x.set("owner_change_time", System.currentTimeMillis());
                        x.set("owner_department", AccountUtil.getUserMainDepartName(actionContext.getTenantId(), owner));
                        x.set("created_by", Lists.newArrayList(actionContext.getUser().getUserId()));
                        x.set("create_time", System.currentTimeMillis());
                        List<String> ownDepartment = AccountUtil.getListValue(x, SystemConstants.Field.DataOwnDepartment.apiName, Lists.newArrayList());
                        if (CollectionUtils.isEmpty(ownDepartment)) {
                            x.set("data_own_department", Lists.newArrayList(AccountUtil.getUserMainDepartId(actionContext.getTenantId(), owner)));
                        }
                    });
                    ObjectLimitUtil.CheckLimitResult checkLimitResult = ObjectLimitUtil.checkObjectLimit(actionContext.getUser(), actionContext.getObjectApiName(), owner, checkLimitDataList, objectDescribe);
                    if (CollectionUtils.isNotEmpty(checkLimitResult.getFailureIds())) {
                        dataList.forEach(x -> {
                            if (checkLimitResult.getFailureIds().contains(x.getData().getId())) {
                                errorList.add(new BaseImportAction.ImportError(x.getRowNo(), String.format(I18N.text(SFA_REACH_LIMIT_OBJ),
                                        I18N.text("LeadsObj.attribute.self.display_name"))));
                            }
                        });
                    }
                }
            }
        }
        mergeErrorList(errorList);
    }

    @Override
    protected void customDefaultValue(List<IObjectData> validList) {
        super.customDefaultValue(validList);
        validList.forEach(x -> {
            x.setTenantId(this.objectDescribeExt.getTenantId());
            x.setDescribeApiName(this.objectDescribeExt.getApiName());
            x.setDescribeId(this.objectDescribeExt.getId());
            x.set("life_status", "normal");
            x.set("lock_status", "0");
            x.set("is_overtime", false);
            if (!StringUtils.isEmpty(x.get("owner", String.class))) {
                x.set(LeadsConstants.Field.BIZ_STATUS.getApiName(), LeadsBizStatusEnum.UN_PROCESSED.getCode());
                x.set("owner_change_time", System.currentTimeMillis());
            } else {
                x.set(LeadsConstants.Field.BIZ_STATUS.getApiName(), LeadsBizStatusEnum.UN_ASSIGNED.getCode());
            }
        });
    }

    @Override
    protected void customAfterImport(List<IObjectData> actualList) {
        super.customAfterImport(actualList);
        List<IObjectData> poolList = getPoolList();
        createLeadsAllocateTask(actualList);
        sendTobeAssignedNotice(actualList);
        sendTobeProcessNotice(actualList);
        createLeadsOverTimeTask(poolList);
        addPoolsLog(poolList);
        updatePoolCount(actualList);
        createLeadsDuplicatedProcessingTask();
        addFlowRecord(actualList);
//        IndustryEnterInfoUtil.updateIndustryEnterInfo(actionContext.getUser(), actionContext.getObjectApiName(),
//                arg.getIsVerifyEnterprise(), arg.getIsBackFillIndustrialAndCommercialInfo(),
//                arg.getIsBackFillOverwriteOldValue(), actualList);
    }

    private void addFlowRecord(List<IObjectData> dataList){
        if(CollectionUtils.isEmpty(dataList)) {
            return;
        }
        try {
            List<IObjectData> flowRecordDataList = Lists.newArrayList();
            IObjectDescribe flowRecordObjDescribe = serviceFacade.findObject(actionContext.getTenantId(), SFAPreDefineObject.LeadsFlowRecord.getApiName());
            IFieldDescribe nameFiled = flowRecordObjDescribe.getFieldDescribe("name");
            Map<String, String> ownerMap = Maps.newHashMap();
            for (IObjectData leadsData : dataList) {
                String owner = AccountUtil.getOwner(leadsData);
                if (StringUtils.isBlank(owner)) {
                    continue;
                }
                ownerMap.put(leadsData.getId(), owner);
                IObjectData objectData = new ObjectData();
                objectData.set("tenant_id", actionContext.getTenantId());
                objectData.set("record_type", "default__c");
                objectData.set("_id", serviceFacade.generateId());
                objectData.set("leads_id", leadsData.getId());
                objectData.set("leads_owner", Lists.newArrayList(owner));
                objectData.set("flow_type", "import");
                objectData.set("flow_time", System.currentTimeMillis());
                objectData.set("leads_status", leadsData.get("biz_status", String.class));
                objectData.set("leads_status_changed_time", System.currentTimeMillis());
                objectData.set("leads_stage", leadsData.get("leads_stage"));
                objectData.set("leads_stage_changed_time", System.currentTimeMillis());
                objectData.set("leads_back_reason", null);
                objectData.set("account_id", null);
                objectData.set("contact_id", null);
                objectData.set("new_opportunity_id", null);
                objectData.set("opportunity_id", null);
                objectData.set("owner", Lists.newArrayList(actionContext.getUser().getUserId()));
                objectData.set("life_status", "normal");
                objectData.set("lock_status", "0");
                objectData.set("package", "CRM");
                objectData.set("object_describe_id", flowRecordObjDescribe != null ? flowRecordObjDescribe.getId() : null);
                objectData.set("object_describe_api_name", SFAPreDefineObject.LeadsFlowRecord.getApiName());
                objectData.set("is_deleted", false);
                if(nameFiled != null && "text".equals(nameFiled.getType())) {
                    objectData.set("name", AccountUtil.getNameCode());
                }
                flowRecordDataList.add(objectData);
            }
            if (CollectionUtils.isNotEmpty(flowRecordDataList)) {
                autoNumberLogicService.calculateAutoNumberValue(flowRecordObjDescribe, flowRecordDataList);
//                flowRecordDataList = serviceFacade.bulkSaveObjectData(flowRecordDataList, actionContext.getUser());
                List<Map<String, Object>> insertDataList = Lists.newArrayList();
                List<String> ownerList = Lists.newArrayList(ownerMap.values().stream().collect(Collectors.toSet()));
                if(!ownerList.contains(actionContext.getUser().getUserId())) {
                    ownerList.add(actionContext.getUser().getUserId());
                }
                Map<String, String> ownerMainDepartMap = AccountUtil.getUserMainDepartIdMap(actionContext.getTenantId(), ownerList);
                for(IObjectData flowRecordData : flowRecordDataList) {
                    String leads_id = AccountUtil.getStringValue(flowRecordData, "leads_id", "");
                    String owner = ownerMap.containsKey(leads_id) ? ownerMap.get(leads_id) : null;
                    String owner_department = ownerMainDepartMap.containsKey(owner) ? ownerMainDepartMap.get(owner) : null;
                    String data_own_deparment = ownerMainDepartMap.containsKey(actionContext.getUser().getUserId()) ? ownerMainDepartMap.get(actionContext.getUser().getUserId()) : null;
                    Map<String, Object> dataMap = Maps.newHashMap();
                    dataMap.put("tenant_id", actionContext.getTenantId());
                    dataMap.put("record_type", "default__c");
                    dataMap.put("id", serviceFacade.generateId());
                    dataMap.put("name", flowRecordData.get("name"));
                    dataMap.put("leads_id", leads_id);
                    dataMap.put("leads_owner", owner);
                    dataMap.put("leads_owner_department", owner_department);
                    dataMap.put("flow_type", "import");
                    dataMap.put("flow_time", System.currentTimeMillis());
                    dataMap.put("leads_status", LeadsBizStatusEnum.UN_PROCESSED.getValue());
                    dataMap.put("leads_status_changed_time", System.currentTimeMillis());
                    dataMap.put("leads_stage", flowRecordData.get("leads_stage"));
                    dataMap.put("leads_stage_changed_time", flowRecordData.get("leads_stage_changed_time"));
                    dataMap.put("leads_back_reason", null);
                    dataMap.put("account_id", null);
                    dataMap.put("contact_id", null);
                    dataMap.put("new_opportunity_id", null);
                    dataMap.put("opportunity_id", null);
                    dataMap.put("owner", actionContext.getUser().getUserId());
                    dataMap.put("life_status", "normal");
                    dataMap.put("lock_status", "0");
                    dataMap.put("package", "CRM");
                    dataMap.put("object_describe_id", flowRecordObjDescribe != null ? flowRecordObjDescribe.getId() : null);
                    dataMap.put("object_describe_api_name", SFAPreDefineObject.LeadsFlowRecord.getApiName());
                    dataMap.put("is_deleted", 0);
                    dataMap.put("data_own_department", data_own_deparment);
                    dataMap.put("version", 1);
                    dataMap.put("created_by", actionContext.getUser().getUserId());
                    dataMap.put("create_time", System.currentTimeMillis());
                    dataMap.put("last_modified_by", actionContext.getUser().getUserId());
                    dataMap.put("last_modified_time", System.currentTimeMillis());
                    insertDataList.add(dataMap);
                }
                com.facishare.paas.metadata.api.action.ActionContext context = CommonSqlUtils.convert2ActionContext(actionContext);
                commonSqlService.insert("biz_leads_flow_record", insertDataList, context);
            }
        }catch (Exception e) {
            log.error("import data addFlowRecord error ", e);
            throw new SFABusinessException(SFAErrorCode.ACCOUNT_COMMON_ERROR);
        }
    }

    private void createLeadsDuplicatedProcessingTask() {
        if (!DuplicatedProcessingUtils.isGray(actionContext.getTenantId())) return;
        List<String> dataIdList = actualList.stream().filter(a -> StringUtils.isNotEmpty(a.getId())).map(a -> a.getId()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(dataIdList)) {
            int refreshVersion = processingService.getRefreshVersion(actionContext.getUser());
            processingTaskService.createOrUpdateTask(actionContext.getTenantId(), dataIdList, refreshVersion);
        }
    }

    private void updatePoolCount(List<IObjectData> actualList) {
        List<String> poolIds = LeadsUtils.getLeadsPoolIds(actualList);
        LeadsUtils.updateLeadsPoolsCount(actionContext.getUser(), poolIds);
    }

    private void createLeadsAllocateTask(List<IObjectData> actualList) {
        if(CollectionUtils.isEmpty(actualList)){
            return;
        }
        List<String> poolIds = LeadsUtils.getLeadsPoolIds(actualList);
        if(CollectionUtils.isEmpty(poolIds)){
            return;
        }
        Integer start_row_number = 0;
        if(CollectionUtils.isNotEmpty(dataList)) {
            start_row_number = dataList.get(0).getRowNo();
        }
        Integer sleep_time = start_row_number / 50 * 3 + 1;
        HashMap<String, List<String>> data_task = new HashMap<>();
        for (String poolId : poolIds) {
            List<IObjectData> poolDataList = actualList.stream().filter(x -> poolId.equals(LeadsUtils.getPoolId(x))).collect(Collectors.toList());
            List<String> allocateDataIdList = poolDataList.stream().map(x -> x.getId()).collect(Collectors.toList());
            Set<String> ownerIds = poolDataList.stream().map(x -> AccountUtil.getOwner(x)).collect(Collectors.toSet());
            ownerIds.forEach(ownerId -> {
                List<IObjectData> ownerDataList = poolDataList.stream().filter(x -> ownerId.equals(AccountUtil.getOwner(x))).collect(Collectors.toList());
                List<IObjectData> checkCleanOwnerDataList = ObjectDataExt.copyList(ownerDataList);
                Map<String, Boolean> cleanOwnerResult = PoolOwnerRuleUtil.cleanOwner(actionContext.getUser(), Utils.LEADS_API_NAME, poolId, checkCleanOwnerDataList, objectDescribe);
                cleanOwnerResult.entrySet().forEach(x -> {
                    if(!Boolean.TRUE.equals(x.getValue())) {
                        allocateDataIdList.remove(x.getKey());
                    }
                });
            });
            if(CollectionUtils.isNotEmpty(allocateDataIdList)){
                data_task.put(poolId, allocateDataIdList);
            }
        }

        if (!data_task.isEmpty()) {
            leadsAllocateTaskService.createOrUpdateTask(actionContext.getTenantId(), data_task, 1, sleep_time);
        }
    }

    private void createLeadsOverTimeTask(List<IObjectData> poolList) {
        if (CollectionUtils.isEmpty(poolList)) {
            return;
        }
        Map<IObjectData, List<String>> dataMap = Maps.newHashMap();
        poolList.forEach(pool -> {
            List<String> leadsIds = actualList.stream()
                    .filter(x -> pool.getId().equals(String.valueOf(x.get(LeadsConstants.Field.LEADS_POOL_ID.getApiName()))))
                    .map(x -> x.getId())
                    .collect(Collectors.toList());
            dataMap.put(pool, leadsIds);
        });
        log.info("createLeadsOverTimeTask,dataMap:{}", dataMap);
        leadsOverTimeTaskService.createOrUpdateTask(actionContext.getTenantId(), dataMap);
    }

    private List<IObjectData> getPoolList() {
        List<String> poolIds = actualList.stream().filter(x -> StringUtils.isNotEmpty(x.get(LeadsConstants.Field.LEADS_POOL_ID.getApiName(), String.class)))
                .map(x -> x.get(LeadsConstants.Field.LEADS_POOL_ID.getApiName()).toString()).distinct().collect(Collectors.toList());
        if (org.springframework.util.CollectionUtils.isEmpty(poolIds)) {
            return Lists.newArrayList();
        }
        return poolService.getObjectPoolByIds(actionContext.getTenantId(), poolIds);
    }

    private void addPoolsLog(List<IObjectData> poolList) {
        for (IObjectData objectData : actualList) {
            String poolId = objectData.get(LeadsConstants.Field.LEADS_POOL_ID.getApiName(), String.class);
            if (org.springframework.util.StringUtils.isEmpty(poolId)) {
                continue;
            }
            Optional<IObjectData> optionalData = poolList.stream()
                    .filter(x -> poolId.equals(x.getId())).findFirst();
            IObjectData poolObjectData = optionalData.isPresent() ? optionalData.get() : null;
            SFALogModels.SFALogEntity logEntity = sfaLogService.buildLogEntity(poolObjectData,
                    String.format("%s ", I18N.text("LeadsObj.attribute.self.display_name")) + objectData.getName(),
                    false);
            List<SFALogModels.SFALogTextMessage> textMessageList = Lists.newArrayList();
            sfaLogService.appendNameLinkLogTextMessage(actionContext.getObjectApiName(), objectData, textMessageList);

            logEntity.setLogTextMessageList(textMessageList);
            sfaLogService.addLog(actionContext.getUser(), logEntity, "SalesCluePoolLog",
                    SFALogModels.LogOperationType.IMPORT);
        }
    }

    private void sendTobeAssignedNotice(List<IObjectData> actualList) {
        String employeeId = actionContext.getUser().getUserId();
        List<String> leadsPoolIds = actualList.stream().filter(x -> StringUtils.isNotEmpty(x.get(LeadsConstants.Field.LEADS_POOL_ID.getApiName(),
                String.class))).map(x -> x.get(LeadsConstants.Field.LEADS_POOL_ID.getApiName(), String.class)).collect(Collectors.toList());
        List<String> hasRulePoolIdList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(leadsPoolIds)) {
            List<Map> ruleList = LeadsUtils.getPoolAllocateRules(actionContext.getTenantId(), leadsPoolIds);
            hasRulePoolIdList = ruleList.stream().
                    map(r -> r.get("pool_id").toString()).distinct().collect(Collectors.toList());
        }
        Map<String, List<IObjectData>> map = Maps.newHashMap();
        List<String> dataIds = Lists.newArrayList();
        for (IObjectData objectData : actualList) {
            List<String> ownerIds = objectData.getOwner();
            if (CollectionUtils.isNotEmpty(ownerIds) && ownerIds.stream().anyMatch(x -> StringUtils.isNotEmpty(x))) {
                continue;
            }
            String leadsPoolId = objectData.get(LeadsConstants.Field.LEADS_POOL_ID.getApiName(), String.class);
            if (StringUtils.isEmpty(leadsPoolId)) continue;
            if (hasRulePoolIdList.contains(leadsPoolId)) continue;
            dataIds.add(objectData.getId());
            if (!map.containsKey(leadsPoolId)) {
                map.put(leadsPoolId, Lists.newArrayList());
            }
            map.get(leadsPoolId).add(objectData);
        }
        for (Map.Entry<String, List<IObjectData>> entry : map.entrySet()) {
            String leadsPoolId = entry.getKey();
            List<IObjectData> dataList = entry.getValue();
            if (CollectionUtils.isNotEmpty(dataList)) {
                IObjectData data = dataList.get(0);
                if (data != null) {
                    List<String> employeeIds = poolService.getPoolAdminById(actionContext.getUser(), leadsPoolId);
                    employeeIds.remove(employeeId);
                    if (CollectionUtils.isNotEmpty(employeeIds)) {
                        qiXinTodoService.sendTodo(actionContext.getTenantId(), SessionBOCItemKeys.TOBE_ASSIGNED_SALES_CLUE,
                                actionContext.getObjectApiName(), data.getId(), actionContext.getUser().getUserId(),
                                employeeIds);
                    }
                }
            }
        }
    }

    private void sendTobeProcessNotice(List<IObjectData> actualList) {

        List<String> ownerIdList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(actualList)) return;
        for (IObjectData objectData : actualList) {
            List<String> ownerIds = objectData.getOwner();
            if (CollectionUtils.isEmpty(ownerIds) || ownerIds.stream().anyMatch(x -> x.equals(actionContext.getUser().getUserId()))) {
                continue;
            }
            ownerIdList.addAll(ownerIds);
        }
        for (String ownerId : ownerIdList) {
            List<IObjectData> dataList = actualList.stream().
                    filter(x -> x.getOwner().stream().anyMatch(y -> y.equals(ownerId))).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(dataList)) {
                IObjectData objectData = dataList.get(0);
                if (objectData != null) {
                    String dataId = objectData.getId();
                    qiXinTodoService.sendTodo(actionContext.getTenantId(), SessionBOCItemKeys.TobeProcessedSalesClue,
                            actionContext.getObjectApiName(), dataId, actionContext.getUser().getUserId(),
                            Lists.newArrayList(ownerId));
                }
            }
        }
    }

    @Override
    protected void validateOwner(List<ImportData> dataList){
        if(CollectionUtils.isEmpty(dataList)){
            return;
        }
        List<ImportError> errorList = Lists.newArrayList();
        dataList.forEach(data -> {
            String owner = AccountUtil.getOwner(data.getData());
            String poolId = LeadsUtils.getPoolId(data.getData());
            if(StringUtils.isBlank(owner) && StringUtils.isBlank(poolId)) {
                    errorList.add(new ImportError(data.getRowNo(),I18N.text(I18NKey.CANNOT_ADD_IF_NO_OWNER)));
                }
        });
        mergeErrorList(errorList);
    }

    @Override
    protected boolean isSyncOwnerAndOuterOwner() {
        if ("LeadsPoolObj".equals(arg.getObjectCode()) || "salescluepool".equals(arg.getObjectCode())) {
            return false;
        }
        return true;
    }
}
