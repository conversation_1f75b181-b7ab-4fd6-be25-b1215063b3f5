package com.facishare.crm.sfa.predefine.bizvalidator.validator;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.bizvalidator.Validator;
import com.facishare.crm.sfa.predefine.bizvalidator.ValidatorContext;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.modulectrl.IModuleInitService;
import com.facishare.crm.sfa.utilities.util.BomUtil;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * bom校验器
 *
 * <AUTHOR>
 */
public class BomValidator extends BaseValidator implements Validator {
    BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);

    @Override
    public void validate(ValidatorContext context) {
        boolean cpqOpen = bizConfigThreadLocalCacheService.isCPQEnabled(context.getUser().getTenantId());
        if (cpqOpen) {
            List<IObjectData> objectDataList = context.getDetailObjectData().getOrDefault(
                    MASTER_DETAIL_API_NAME.get(context.getDescribeApiName()), Lists.newArrayList());
            BomUtil.checkProductBom(
                    context.getUser(),
                    context.getObjectData(),
                    objectDataList);
        }
    }
}
