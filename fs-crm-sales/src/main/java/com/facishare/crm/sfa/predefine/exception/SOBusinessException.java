package com.facishare.crm.sfa.predefine.exception;

import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.exception.ErrorCode;

public class SOBusinessException extends AppBusinessException {
    public SOBusinessException(String message, ErrorCode errorCode) {
        super(message, errorCode);
    }


    public SOBusinessException(SOErrorCode errorCode) {
        super(errorCode.getMessage(), errorCode);
    }
}
