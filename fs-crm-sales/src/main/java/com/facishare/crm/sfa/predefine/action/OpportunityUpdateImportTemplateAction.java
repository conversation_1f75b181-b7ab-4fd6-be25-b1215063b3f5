package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.utilities.util.TeamMemberUtil;
import com.facishare.paas.appframework.core.predef.action.StandardUpdateImportTemplateAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.google.common.collect.Lists;

import java.util.List;

public class OpportunityUpdateImportTemplateAction extends StandardUpdateImportTemplateAction {

    private List<String> removeFields = Lists.newArrayList(
            "sales_stg_changed_time","status","leads_id","sales_process_id"
            ,"out_resources","oppo_stage_id","oppo_after_stage_id","last_followed_time"
            ,"biz_status","lost_reason","owner","partner_id","probability","sales_process_name","data_own_department"
            ,"record_type","after_sale_stage_status","account_id","probability"
    );

    @Override
    protected void customHeader(List<IFieldDescribe> headerFieldList) {
        super.customHeader(headerFieldList);
        headerFieldList.removeIf(f -> removeFields.contains(f.getApiName()));
        TeamMemberUtil.addRelevantTeam(headerFieldList);
        //headerFieldList.removeIf(f -> removeFields.contains(f.getExtendInfo().get("importType").equals("TEAM_MEMBER")));
    }
}
