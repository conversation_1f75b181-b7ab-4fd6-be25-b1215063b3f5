package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crmcommon.constants.SystemConstants;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.controller.StandardQuickEditLayoutController;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import org.apache.commons.lang.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2019/12/17 6:23 下午
 * @IgnoreI18nFile
 */
public class SalesOrderProductQuickEditLayoutController extends StandardQuickEditLayoutController {


    @Override
    protected void validateByData() {
        super.validateByData();
        String salesOrderId = data.get("order_id",String.class);
        IObjectData objectData = serviceFacade.findObjectData(controllerContext.getUser(), salesOrderId, Utils.SALES_ORDER_API_NAME);
        if(SFAConfigUtil.isSoftWorkflow(controllerContext.getTenantId(), Utils.SALES_ORDER_API_NAME)
                && Objects.equals(SystemConstants.LifeStatus.Ineffective, ObjectDataExt.of(objectData).getLifeStatus())
                && StringUtils.isNotEmpty(objectData.get("work_flow_id",String.class))){
            throw new ValidateException("自由审批流企业，订单数据被驳回或未生效不允许快速编辑订单产品。");
        }
    }
}
