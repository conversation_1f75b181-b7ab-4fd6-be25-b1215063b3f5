package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.proxy.CoordinationProxy;
import com.facishare.crm.sfa.utilities.proxy.model.SaleEventImport;
import com.facishare.crm.util.DateTimeUtils;
import com.facishare.crmcommon.util.SaleEventObjUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.dto.UserInfo;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportDataAction;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.predefine.action.SaleEventObjInsertImportTemplateAction.SALE_EVENT_TYPE;

/**
 * <AUTHOR>
 * @date 2019-05-14 19:36
 * @IgnoreI18nFile
 */
@Slf4j
public class SaleEventObjInsertImportDataAction extends StandardInsertImportDataAction {


    // ---------------------------------------- Variables

    /**
     * 销售记录关联的对象，最多两个
     */
    private Map<String, IObjectDescribe> relatedObjectDescribeMap;
    /**
     * 日期时间工具
     */
    private DateTimeUtils dateTimeUtils = DateTimeUtils.getInstance();
    /**
     * 关联对象是否支持使用ID导入,apiName:true
     */
    private Map<String, Boolean> relateObjectSupportIDMap;
    /**
     * 协同接口
     */
    private final CoordinationProxy coordinationProxy = SpringUtil
            .getContext()
            .getBean("coordinationProxy", CoordinationProxy.class);
    /**
     * ID标识
     */
    private final static String ID_LABEL = "<ID>";

    private final static String RELATE_OBJ_DUPLICATE = "paas.udobj.import_relate_duplicate";
    private final static String SALE_RECORD_CANNOT_EMPTY = "oaappsrv.oa.SalesRecordServlet.0010501";
    private final static String LOOKUP_IS_EMPTY = "paas.udobj.lookup_is_empty";
    private final static String CREATE_TIME = "paas.udobj.create_time_2";
    private final static String CREATE_TIME_CANNOT_LATE = "paas.udobj.create_time_cannot_late_than_now";

    @Override
    protected void before(Arg arg) {
        findDescribe();
        List<String> relatedApiNameList = arg.getRelatedApiNameList();
        // 查询关联对象描述
        relatedObjectDescribeMap = serviceFacade.findObjects(actionContext.getTenantId(), relatedApiNameList);
        // 是否支持使用对象ID导入
        relateObjectSupportIDMap = supportID(relatedApiNameList, arg.getRows());
        //Label转换成apiName
        objectDescribeExt = ObjectDescribeExt.of(objectDescribe);
        //将字段转为ApiName
        dataList = convertSaleEventLabelToApiName(arg.getRows(), objectDescribe);
    }

    /**
     * 支持使用ID导入
     */
    private Map<String, Boolean> supportID(List<String> relatedApiNameList, List<ObjectDataDocument> rows) {
        Map<String, Boolean> result = Maps.newHashMap();

        // 遍历表头，如果含有："xxxID"字样的字段，代表支持使用ID导入
        Set<Map.Entry<String, Object>> set = rows.get(0).entrySet();
        List<String> titleWithID = set.stream().filter(s -> s.getKey().contains(ID_LABEL)).map(Map.Entry::getKey)
                .collect(Collectors.toList());
        if (CollectionUtils.empty(titleWithID)) {
            for (String apiName : relatedApiNameList) {
                result.put(apiName, Boolean.FALSE);
            }
            return result;
        }

        relatedObjectDescribeMap.forEach((apiName, describe) -> {
            if (titleWithID.contains(describe.getDisplayName() + ID_LABEL)) {
                result.put(apiName, Boolean.TRUE);
            }
        });
        return result;
    }

    private List<ImportData> convertSaleEventLabelToApiName(List<ObjectDataDocument> sourceDataList,
                                                            IObjectDescribe describe) {
        //参数中的data的key是label，需要转换成apiName
        if (CollectionUtils.empty(sourceDataList)) {
            return null;
        }
        List<StandardInsertImportDataAction.ImportData> resultList = Lists.newArrayList();
        for (ObjectDataDocument source : sourceDataList) {
            StandardInsertImportDataAction.ImportData data = convert(source, describe);
            resultList.add(data);
        }
        return resultList;
    }

    private ImportData convert(ObjectDataDocument source, IObjectDescribe describe) {
        //参数中的data的key是label，需要转换成apiName
        if (null == source) {
            return null;
        }
        IObjectData result = new ObjectData();
        ImportData resultData = new ImportData();
        Set<Map.Entry<String, Object>> entrySet = source.entrySet();
        for (Map.Entry<String, Object> entry : entrySet) {
            if (ROW_NO.equalsIgnoreCase(entry.getKey())) {
                resultData.setRowNo(Integer.valueOf(String.valueOf(entry.getValue())));
                continue;
            }
            //将label转换成apiName
            String apiName = findApiName(entry.getKey(), describe);
            String value = entry.getValue() == null ? "" : String.valueOf(entry.getValue());
            if (!Strings.isNullOrEmpty(apiName)) {
                result.set(apiName, value);
            }

        }
        resultData.setData(result);
        return resultData;
    }

    private String findApiName(String key, IObjectDescribe describe) {
        if (I18N.text(I18NKey.SALE_RECORD).equals(key) ||
                (I18N.text(I18NKey.SALE_RECORD) + I18N.text(I18NKey.MUST_FILL_IN)).equals(key)) {
            //销售记录名称，返回name
            return SaleEventObjUtil.FieldDescribe.Content.apiName;
        }

        if (I18N.text(SALE_EVENT_TYPE).equals(key)) {
            return SaleEventObjUtil.FieldDescribe.SALE_EVENT_TYPE.apiName;
        }

        // 从销售记录对象字段中匹配
        List<IFieldDescribe> fieldList = ObjectDescribeExt.of(describe).getFieldDescribesSilently();
        for (IFieldDescribe field : fieldList) {
            String label = field.getLabel();
            if (key.trim().equals(label)) {
                return field.getApiName();
            }
        }

        // 从关联对象中匹配
        for (Map.Entry<String, IObjectDescribe> entry : relatedObjectDescribeMap.entrySet()) {
            // 如果该对象支持使用ID导入
            if (relateObjectSupportIDMap.get(entry.getKey()) == Boolean.TRUE) {
                if (key.trim().equals(entry.getValue().getDisplayName())) {
                    return null;
                } else if (key.trim().equals(entry.getValue().getDisplayName() + ID_LABEL)) {
                    return entry.getKey();
                }
            } else {
                // 不支持ID导入
                if (key.trim().equals(entry.getValue().getDisplayName())) {
                    return entry.getKey();
                }
            }
        }

        return null;
    }

    @Override
    protected Result doAct(Arg arg) {
        Result result = new Result();
        result.setSuccess(true);
        try {
            // 校验关联对象并转换为ID
            checkRelatedNameUnique();
            // 校验输入的数据
            checkRecordOneByOne();
            // 筛选出可导入数据
            validList = filterValidDataList(dataList, allErrorList);
            // 导入销售记录
            if (!CollectionUtils.empty(validList)) {
                // 调用协同的rest服务批量导入服务
                importData(validList);
            }
            generateResult(result);
        } catch (Exception e) {
            log.error("Unexpected Error in ImportParamData of BulkImportDataService,arg:{}", arg, e);
            result.setSuccess(false);
            Throwable rootCause = ExceptionUtils.getRootCause(e);
            if (rootCause != null && rootCause instanceof org.springframework.dao.DuplicateKeyException) {
                result.setMessage(I18N.text(I18NKey.DO_NOT_INPUT_DUPLICATE_PRODUCT));
            } else {
                result.setMessage(I18N.text(I18NKey.UNKNOWN_EXCEPTION));
            }
        }

        return result;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        return result;
    }

    /**
     * 逐行校验输入数据
     */
    private void checkRecordOneByOne() {
        List<ImportError> errorList = Lists.newArrayList();

        // 查询出导入数据所有创建的信息
        List<String> nameList =
                dataList.stream().map(d -> d.getData()
                        .get(SaleEventObjUtil.FieldDescribe.CREATE_BY.apiName, String.class))
                        .filter(StringUtils::isNotEmpty)
                        .collect(Collectors.toList());
        List<UserInfo> userInfoList = serviceFacade.getUserByName(
                actionContext.getTenantId(), actionContext.getUser().getUserId(), nameList);

        List<String> errorMessageList = Lists.newArrayList();
        for (ImportData importData : dataList) {
            errorMessageList.clear();
            errorMessageList.add(checkContent(importData));
            errorMessageList.add(checkAndConvertTime(importData));
            errorMessageList.add(checkAndConvertCreateUser(importData, userInfoList));
            errorMessageList.add(checkAndConvertSaleEventType(importData));
            errorMessageList.add(checkRelatedObjects(importData));
            // 错误信息
            addErrorList(importData, errorList, errorMessageList);
        }
        mergeErrorList(errorList);
    }

    /**
     * 校验销售记录是否填写
     */
    private String checkContent(ImportData importData) {
        String conetent = importData.getData().get(SaleEventObjUtil.FieldDescribe.Content.apiName, String.class);
        if (StringUtils.isEmpty(conetent)) {
            return I18N.text(SALE_RECORD_CANNOT_EMPTY);
        }
        return null;
    }

    /**
     * 关联对象不能都为空
     */
    private String checkRelatedObjects(ImportData importData) {
        boolean flag = false;
        for (String apiName : relatedObjectDescribeMap.keySet()) {
            String value = importData.getData().get(apiName, String.class);
            if (StringUtils.isNotBlank(value)) {
                flag = true;
                break;
            }
        }
        if (!flag) {
            return I18N.text(LOOKUP_IS_EMPTY);
        } else {
            return null;
        }
    }

    /**
     * 将各个方面校验结果，加入的错误信息
     */
    private void addErrorList(ImportData importData, List<ImportError> errorList, List<String> errorMessageList) {
        List<String> validErrorList = errorMessageList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.notEmpty(validErrorList)) {
            String error = StringUtils.join(validErrorList, "\n");
            errorList.add(new ImportError(importData.getRowNo(), error));
        }
    }

    /**
     * 检验销售记录类型并转换为ID
     */
    private String checkAndConvertSaleEventType(ImportData importData) {
        // 用户输入的类型
        String inputType = importData.getData().get(SaleEventObjUtil.FieldDescribe.SALE_EVENT_TYPE.apiName, String.class);
        if (StringUtils.isEmpty(inputType)) {
            // 销售类型可以为空
            return null;
        }
        SelectOneFieldDescribe saleEventTypeDescribe = (SelectOneFieldDescribe) objectDescribeExt
                .getFieldDescribe(SaleEventObjUtil.FieldDescribe.SALE_EVENT_TYPE.apiName);
        for (ISelectOption option : saleEventTypeDescribe.getSelectOptions()) {
            if (inputType.equals(option.getLabel())) {
                // 将类型文本转为ID
                importData.getData().set(SaleEventObjUtil.FieldDescribe.SALE_EVENT_TYPE.apiName, option.get("id"));
                return null;
            }
        }

        return "销售记录类型不存在";
    }

    /**
     * 检查创建人的存在性，并转换为userID
     */
    private String checkAndConvertCreateUser(ImportData importData, List<UserInfo> userInfoList) {
        String inputName = importData.getData().get(SaleEventObjUtil.FieldDescribe.CREATE_BY.apiName, String.class);
        if (StringUtils.isEmpty(inputName)) {
            // 创建人为空，取当前导入人
            importData.getData().set(SaleEventObjUtil.FieldDescribe.CREATE_BY.apiName,
                    actionContext.getUser().getUserId());
            return null;
        }
        String userId = getUserIdByName(inputName, userInfoList);
        if (Objects.isNull(userId)) {
            // 不存在
            return I18N.text("pass.udobj.user_not_exist", inputName);
        } else {
            // 将用户名转换为ID
            importData.getData().set(SaleEventObjUtil.FieldDescribe.CREATE_BY.apiName, userId);
            return null;
        }
    }

    /**
     * 根据用户名获取userId
     */
    private String getUserIdByName(String inputName, List<UserInfo> userInfoList) {
        for (UserInfo userInfo : userInfoList) {
            if (userInfo.getNickname().equals(inputName)) {
                return userInfo.getId();
            }
        }
        return null;
    }

    /**
     * 校验并转换创建时间字段为时间戳
     */
    private String checkAndConvertTime(ImportData importData) {
        List<IFieldDescribe> fieldDescribes = objectDescribeExt.getFieldDescribesSilently();
        Object obj = importData.getData().get(SaleEventObjUtil.FieldDescribe.CREATE_TIME.apiName);
        String str = Objects.isNull(obj) ? "" : String.valueOf(obj);
        int idx = str.indexOf("(");
        if (idx > 0) {
            str = str.substring(0, str.indexOf("("));
        }
        long time = -1;
        long now = LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli();
        if (StringUtils.isEmpty(str)) {
            // 创建时间为空，取当前时间
            importData.getData().set(SaleEventObjUtil.FieldDescribe.CREATE_TIME.apiName, now);
            return null;
        }
        time = dateTimeUtils.convertDateTimeToLong(str);
        if (time == -1) {
            return I18N.text(I18NKey.DATE_TIME_FORMAT_ERROR, I18N.text(CREATE_TIME));
        } else if (time > now) {
            return I18N.text(CREATE_TIME_CANNOT_LATE);
        } else {
            // 将时间转换为时间戳
            importData.getData().set(SaleEventObjUtil.FieldDescribe.CREATE_TIME.apiName, time);
            return null;
        }
    }

    /**
     * 关联对象的主属性不能重复
     */
    private void checkRelatedNameUnique() {
        List<ImportError> errorList = Lists.newArrayList();

        // 整理导入数据中，关联对象的数据
        relatedObjectDescribeMap.forEach((relatedObjectApiName, relatedObjectDescribe) -> {
            // 如果是使用对象ID导入，不进行校验
            if (relateObjectSupportIDMap.get(relatedObjectApiName) == Boolean.TRUE) {
                return;
            }

            List<String> nameList = dataList
                    .stream()
                    .map(d -> {
                        Object obj = d.getData().get(relatedObjectApiName);
                        return Objects.isNull(obj) ? "" : String.valueOf(obj);
                    }).collect(Collectors.toList());
            // 查询关联对象数据
            List<IObjectData> referenceDataList = getRelatedDataList(nameList, relatedObjectDescribe);
            //根据服务返回结果配置errorList和objectData的name替换成id
            boolean hasResult = !CollectionUtils.empty(referenceDataList);
            for (ImportData importData : dataList) {
                String name = getStringValue(importData.getData(), relatedObjectApiName);
                if (Strings.isNullOrEmpty(name)) {
                    // 如果没有填关联对象，略过
                    continue;
                }
                // 替换Name为ID， 校验
                List<IObjectData> matchedDataList = Lists.newArrayList();
                if (hasResult) {
                    matchedDataList = referenceDataList.stream()
                            .filter(data -> name.equals(data.getName())).collect(Collectors.toList());
                }
                if (hasResult && matchedDataList.size() == 1) {
                    importData.getData().set(relatedObjectApiName, matchedDataList.get(0).getId());
                } else if (hasResult && matchedDataList.size() > 1) {
                    //关联对象有重复
                    errorList.add(new ImportError(importData.getRowNo(),
                            I18N.text(RELATE_OBJ_DUPLICATE, relatedObjectDescribe.getDisplayName())));
                } else {
                    //没有权限或不存在
                    errorList.add(new ImportError(importData.getRowNo(),
                            I18N.text(I18NKey.RELATED_OBJECT_DATA_DELETE_OR_NO_PRIVILEGE,
                                    relatedObjectDescribe.getDisplayName())));
                }
            }

        });
        mergeErrorList(errorList);
    }

    /**
     * 根据主属性查询目标对象数据列表
     */
    private List<IObjectData> getRelatedDataList(List<String> nameList, IObjectDescribe relatedObjectDescribe) {
        if (CollectionUtils.empty(nameList)) {
            return null;
        }

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(nameList.size() * 3);
        query.setOffset(0);

        List<IFilter> filters = Lists.newArrayList();
        IFilter filter = new Filter();
        filter.setFieldName(IObjectData.IS_DELETED);
        filter.setFieldDefineType(IObjectDescribe.DEFINE_TYPE_SYSTEM);
        filter.setFieldValueType("string");
        filter.setOperator(Operator.EQ);
        filter.setIndexName(IObjectData.IS_DELETED);
        filter.setFieldValues(Arrays.asList("0"));
        filters.add(filter);

        filter = new Filter();
        filter.setFieldName(IObjectData.DESCRIBE_API_NAME);
        filter.setFieldDefineType(IObjectDescribe.DEFINE_TYPE_SYSTEM);
        filter.setFieldValueType("string");
        filter.setOperator(Operator.EQ);
        filter.setIndexName(IObjectData.DESCRIBE_API_NAME);
        filter.setFieldValues(Arrays.asList(relatedObjectDescribe.getApiName()));
        filters.add(filter);

        filter = new Filter();
        filter.setFieldName(IObjectData.NAME);
        filter.setFieldDefineType(IObjectDescribe.DEFINE_TYPE_SYSTEM);
        filter.setFieldValueType("string");
        filter.setOperator(Operator.IN);
        filter.setIndexName(IObjectData.NAME);
        filter.setFieldValues(nameList);
        filters.add(filter);

        query.setFilters(filters);
        QueryResult<IObjectData> searchResult =
                serviceFacade.findBySearchQuery(actionContext.getUser(), relatedObjectDescribe.getApiName(), query);
        if (null == searchResult || CollectionUtils.empty(searchResult.getData())) {
            return null;
        }

        List<IObjectData> dataList = searchResult.getData();
        return dataList;
    }

    /**
     * 销售记录调用协同接口导入
     */
    @Override
    protected List<IObjectData> importData(List<IObjectData> validList) {
        SaleEventImport.Arg arg = createImportArg(validList);
        Map<String, String> header = createHeader();
        SaleEventImport.Result result = coordinationProxy.importSaleEvent(arg, header);
        // 分析导入结果
        List<ImportError> errorList = Lists.newArrayList();
        if (0 != result.getCode()) {
            for (SaleEventImport.ResultItem resultItem : result.getResultItemList()) {
                if (StringUtils.isNotEmpty(resultItem.getMessage())) {
                    errorList.add(new ImportError(resultItem.getIndex() + 2, resultItem.getMessage()));
                }
            }
        }
        mergeErrorList(errorList);
        return null;
    }

    /**
     * 创建请求头
     */
    private Map<String, String> createHeader() {
        Map<String, String> result = Maps.newHashMap();
        result.put("X-fs-Enterprise-Id", actionContext.getTenantId());
        result.put("X-fs-Employee-Id", actionContext.getUser().getUserId());
        return result;
    }

    /**
     * 生成导入销售记录的参数
     */
    private SaleEventImport.Arg createImportArg(List<IObjectData> validList) {
        SaleEventImport.Arg arg = new SaleEventImport.Arg();
        List<SaleEventImport.SaleEvent> events = Lists.newArrayList();
        // 构造每条数据
        for (IObjectData data : validList) {
            SaleEventImport.SaleEvent saleEvent = new SaleEventImport.SaleEvent();
            // 销售记录内容
            saleEvent.setContent(data.get(SaleEventObjUtil.FieldDescribe.Content.apiName, String.class));
            // 创建时间
            saleEvent.setCreateTime(data.get(SaleEventObjUtil.FieldDescribe.CREATE_TIME.apiName, Long.class));
            // 创建人
            String userId = data.get(SaleEventObjUtil.FieldDescribe.CREATE_BY.apiName, String.class);
            Integer id = Integer.valueOf(userId);
            saleEvent.setSender(id);
            // 销售记录类型ID
            saleEvent.setTypeId(data.get(SaleEventObjUtil.FieldDescribe.SALE_EVENT_TYPE.apiName, String.class));
            // 关联对象
            saleEvent.setCustomerIdList(Lists.newArrayList());
            saleEvent.setContactIdList(Lists.newArrayList());
            final List<SaleEventImport.RelatedObject> relatedObjectList = Lists.newArrayList();
            relatedObjectDescribeMap.forEach((apiName, describe) -> {
                if (Utils.ACCOUNT_API_NAME.equals(apiName)) {
                    String accountId = data.get(Utils.ACCOUNT_API_NAME, String.class);
                    if (StringUtils.isNotBlank(accountId)) {
                        saleEvent.setCustomerIdList(Lists.newArrayList(accountId));
                    }
                } else if (Utils.CONTACT_API_NAME.equals(apiName)) {
                    String contactId = data.get(Utils.CONTACT_API_NAME, String.class);
                    if (StringUtils.isNotBlank(contactId)) {
                        saleEvent.setContactIdList(Lists.newArrayList(contactId));
                    }
                } else {
                    SaleEventImport.RelatedObject relatedObject = new SaleEventImport.RelatedObject();
                    relatedObject.setApiName(apiName);
                    relatedObject.setDataId(data.get(apiName, String.class));
                    relatedObjectList.add(relatedObject);
                }
            });
            saleEvent.setRelatedObjectList(relatedObjectList);
            events.add(saleEvent);
        }
        arg.setItems(events);
        return arg;
    }

}
