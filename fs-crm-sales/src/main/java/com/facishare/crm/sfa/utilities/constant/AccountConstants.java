package com.facishare.crm.sfa.utilities.constant;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.google.common.base.Strings;

/**
 * @IgnoreI18nFile
 */
public interface AccountConstants {

    enum AccountExtendAttribute {
        /*
        客户
         */
        CUSTOMER("customer"),
        /*
        客户（管理员）
         */
        CUSTOMER_CRM_MGR("customercrmmgr"),
        /*
       定位
        */
        LOCATION("location"),
        /*
      详细地址
       */
        ADDRESS("address"),
        /*
        地区全称
         */
        AREA_FULL_NAME("area_full_name"),
        /*
        地区
         */
        AREA("area"),
        /*
        公海（公海成员）
         */
        HS_CUSTOMER_MEMBER("hscustomermember"),
        /*
        公海（公海管理员）
         */
        HS_CUSTOMER_MGR("hscustomermgr");

        private String apiName;

        AccountExtendAttribute(String apiName) {
            this.apiName = apiName;
        }

        public String getApiName() {
            return this.apiName;
        }
    }

    enum AccountStatus {
        UN_FILING("1"),
        UN_ALLOCATE("2"),
        ALLOCATED("3"),
        INEFFECTIVE("4"),
        CHANGING("5"),
        INVALID("99");

        private String value;

        AccountStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }

        public static AccountStatus of(String accountStatusValue) {
            if (Strings.isNullOrEmpty(accountStatusValue)) {
                throw new ValidateException(I18N.text("paas.udobj.unknow_life_status", new Object[]{accountStatusValue}));
            } else {
                AccountStatus[] var2 = values();
                int var3 = var2.length;

                for(int var4 = 0; var4 < var3; ++var4) {
                    AccountStatus accountStatus = var2[var4];
                    if (accountStatus.getValue().equals(accountStatusValue)) {
                        return accountStatus;
                    }
                }

                throw new ValidateException(I18N.text("paas.udobj.unknow_life_status", new Object[]{accountStatusValue}));
            }
        }
    }

    enum AccountBizStatus {
        UN_ALLOCATED("unallocated", "未分配"),
        ALLOCATED("allocated","已分配");

        private String value;
        private String label;

        AccountBizStatus(String value, String label) {
            this.value = value;
            this.label = label;
        }

        public String getValue() {
            return this.value;
        }

        public String getLabel() {
            return this.label;
        }

        public static AccountBizStatus of(String accountStatusValue) {
            if (Strings.isNullOrEmpty(accountStatusValue)) {
                throw new ValidateException(I18N.text("paas.udobj.unknow_life_status", new Object[]{accountStatusValue}));
            } else {
                AccountBizStatus[] var2 = values();
                int var3 = var2.length;

                for(int var4 = 0; var4 < var3; ++var4) {
                    AccountBizStatus accountStatus = var2[var4];
                    if (accountStatus.getValue().equals(accountStatusValue)) {
                        return accountStatus;
                    }
                }

                throw new ValidateException(I18N.text("paas.udobj.unknow_life_status", new Object[]{accountStatusValue}));
            }
        }
    }

    enum DealStatus {
        UN_DEAL("1"),
        DEAL("2"),
        MULTI_DEAL("3");

        private String value;

        DealStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }

    class Field {
        public static final String ACCOUNT_STATUS = "account_status";
        public static final String OWNER = "owner";
        public static final String HIGH_SEAS_ID = "high_seas_id";
        public static final String RECORD_TYPE = "record_type";
        public static final String LOCATION = "location";
        public static final String COUNTRY = "country";
        public static final String PROVINCE = "province";
        public static final String CITY = "city";
        public static final String DISTRICT = "district";
        public static final String ADDRESS = "address";
        public static final String DEAL_STATUS = "deal_status";
        public static final String LAST_DEAL_TIME = "last_deal_closed_time";
        public static final String FILLING_CHECKER_ID = "filling_checker_id";
        public static final String BIZ_STATUS = "biz_status";
        public static final String BACK_REASON = "back_reason";
        public static final String EXPIRE_TIME = "expire_time";
        public static final String ACCOUNT_ID = "account_id";
        public static final String TRANSFER_COUNT = "transfer_count";
        public static final String ACCOUNT_NO = "account_no";

        public static final String LAST_FOLLOWED_TIME = "last_followed_time";
        public static final String OWNER_MODIFIED_TIME = "owner_modified_time";
        public static final String REMIND_DAYS = "remind_days";
        public static final String EXTEND_DAYS = "extend_days";
        public static final String TEL = "tel";
        public static final String FIELD_ACCOUNT_PATH = "account_path";
        public static final String FIELD_PARENT_ACCOUNT_ID = "parent_account_id";
        public static final String PHONE_NUMBER_ATTRIBUTION_COUNTRY = "phone_number_attribution_country";
        public static final String PHONE_NUMBER_ATTRIBUTION_LOCATION = "phone_number_attribution_location";
        public static final String PHONE_NUMBER_ATTRIBUTION_ADDRESS = "phone_number_attribution_address";
        public static final String PHONE_NUMBER_ATTRIBUTION_CITY = "phone_number_attribution_city";
        public static final String PHONE_NUMBER_ATTRIBUTION_PROVINCE = "phone_number_attribution_province";
        public static final String PHONE_NUMBER_ATTRIBUTION_DISTRICT = "phone_number_attribution_district";
    }
}
