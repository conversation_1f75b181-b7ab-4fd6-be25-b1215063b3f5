package com.facishare.crm.sfa.predefine.action;

import com.facishare.crmcommon.constants.SystemConstants;
import com.facishare.crm.sfa.utilities.constant.RefundConstants;
import com.facishare.crm.sfa.utilities.proxy.RefundProxy;
import com.facishare.crm.sfa.utilities.proxy.model.RefundFlowCompleteModel;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

import static com.facishare.crm.sfa.predefine.service.transfer.CrmPackageObjectConstants.FIELD_LIFE_STATUS;

/**
 * Create by baoxinxue
 */

@Slf4j
public class RefundFlowCompletedAction extends SFAFlowCompletedAction {

    private RefundProxy refundProxy = SpringUtil.getContext().getBean(RefundProxy.class);
    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        if(result.getSuccess()){
            if(data.get(FIELD_LIFE_STATUS).equals(SystemConstants.LifeStatus.Normal.value)){
                data.set(RefundConstants.RefundField.BIZ_STATUS.getApiName(),RefundConstants.RefundBizStatus.REFUNDED.getValue());
                serviceFacade.batchUpdateByFields(actionContext.getUser(), Lists.newArrayList(data),
                        Lists.newArrayList(RefundConstants.RefundField.BIZ_STATUS.getApiName()));
                //serviceFacade.updateObjectData(actionContext.getUser(),data);
            }
            if(data.get(RefundConstants.RefundField.REFUND_METHOD.getApiName()) != null) {
                if (data.get(RefundConstants.RefundField.REFUND_METHOD.getApiName()).toString()
                        .equals(RefundConstants.RefundType.PrePay.getValue()) || data.get(RefundConstants.RefundField.REFUND_METHOD.getApiName()).toString()
                        .equals(RefundConstants.RefundType.Rebate.getValue())) {
                    Map<String, String> header = Maps.newHashMap();
                    header.put("x-fs-Employee-Id", actionContext.getUser().getUserId());
                    header.put("x-fs-Enterprise-Id", actionContext.getUser().getTenantId());
                    header.put("x-fs-ei", actionContext.getUser().getTenantId());
                    header.put("x-fs-userInfo", actionContext.getUser().getUserId());
                    RefundFlowCompleteModel.Result rst = refundProxy.refundFlowComplete(
                            RefundFlowCompleteModel.Arg.builder()
                                    .dataId(data.getId())
                                    .lifeStatus(data.get(FIELD_LIFE_STATUS,String.class))
                                    .approvalType(ApprovalFlowTriggerType.getType(arg.getTriggerType()).getId()).build(), header);
                    if (!rst.IsSuccess()) {
                        log.warn("调用客户账户服务有误 RefundFlowCompletedAction arg:{}", data.getId());
                    }
                }
            }
        }
        return result;
    }
}
