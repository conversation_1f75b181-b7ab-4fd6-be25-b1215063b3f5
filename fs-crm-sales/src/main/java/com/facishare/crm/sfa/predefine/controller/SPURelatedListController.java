package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.PromotionService;
import com.facishare.crm.sfa.predefine.service.SpuSkuService;
import com.facishare.crm.sfa.predefine.service.real.HandleSearchQueryService;
import com.facishare.crm.sfa.predefine.service.real.MultiUnitService;
import com.facishare.crm.sfa.predefine.service.real.MultiUnitServiceImpl;
import com.facishare.crm.sfa.predefine.service.real.TransformData4ViewService;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.AvailableConstants;
import com.facishare.crm.sfa.utilities.constant.PriceBookConstants;
import com.facishare.crm.sfa.utilities.proxy.model.GetPromotionProductsByAccountIdModel;
import com.facishare.crm.sfa.utilities.util.AvailableRangeUtils;
import com.facishare.crm.sfa.utilities.util.ConcatenateSqlUtils;
import com.facishare.crm.sfa.utilities.util.GrayUtil;
import com.facishare.crm.sfa.utilities.util.LayoutUtils;
import com.facishare.crm.sfa.utilities.util.PromotionUtil;
import com.facishare.paas.appframework.core.exception.SystemErrorCode;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.ProductCategoryService;
import com.facishare.paas.appframework.metadata.ProductCategoryServiceImpl;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.metadata.api.INameCache;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IDataRightsParameter;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.DataRightsParameter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.support.SchemaHelper;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by luxin on 2018/10/27.
 */
public class SPURelatedListController extends StandardRelatedListController {
    private IObjectDataService objectDataService = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);
    private HandleSearchQueryService handleSearchQueryService = SpringUtil.getContext().getBean(HandleSearchQueryService.class);
    private TransformData4ViewService transformData4ViewService = SpringUtil.getContext().getBean(TransformData4ViewService.class);
    private ProductCategoryService productService = SpringUtil.getContext().getBean(ProductCategoryServiceImpl.class);
    private MultiUnitService multiUnitService = SpringUtil.getContext().getBean(MultiUnitServiceImpl.class);
    private SchemaHelper schemaHelper = SpringUtil.getContext().getBean(SchemaHelper.class);
    private final AvailableRangeUtils availableRangeUtils = SpringUtil.getContext().getBean(AvailableRangeUtils.class);
    private PromotionService promotionService = SpringUtil.getContext().getBean(PromotionService.class);
    private SpuSkuService spuSkuService = SpringUtil.getContext().getBean(SpuSkuService.class);

    private List<OrderBy> orders;
    private IObjectDescribe targetObjectDescribe;
    private boolean isRealNotLookSpu;
    private String priceBookId;
    private boolean isReturnNull = false;
    private boolean isPriceBookOpen;
    private List<IFilter> spuFilters;

    private String dataRightInnerJoinerSqlPart;
    private String dataRightWhereSqlPart;
    List<GetPromotionProductsByAccountIdModel.WrapProductPromotion> productPromotionList = null;
    protected String accountId = "";
    protected String partnerId = "";


    @Override
    protected void before(Arg arg) {
        ObjectDataDocument objectData = arg.getObjectData();
        isRealNotLookSpu = objectData != null && Objects.equals(objectData.get("is_real_lookup"), false);

        if (isRealNotLookSpu) {
            priceBookId = objectData.get("pricebook_id") == null ? null : objectData.get("pricebook_id").toString();
            isPriceBookOpen = Objects.equals(objectData.get("pricebook_open"), true);

            if (isPriceBookOpen) {
                if (priceBookId == null) {
                    isReturnNull = true;
                } else {
                    targetObjectDescribe = serviceFacade.findObject(controllerContext.getTenantId(), Utils.PRICE_BOOK_PRODUCT_API_NAME);
                }
            } else {
                targetObjectDescribe = serviceFacade.findObject(controllerContext.getTenantId(), Utils.PRODUCT_API_NAME);
            }
        }
        super.before(arg);
    }

    @Override
    protected void init() {
        if (arg.getObjectData() != null && arg.getObjectData().containsKey("account_id")) {
            accountId = Optional.ofNullable(arg.getObjectData().get("account_id")).orElse("").toString();
            partnerId = Optional.ofNullable(arg.getObjectData().get("partner_id")).orElse("").toString();
        }
        super.init();
    }

    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery query;
        if (isRealNotLookSpu && !isReturnNull) {
            String searchQueryInfo = getSearchQueryInfo();
            SearchTemplateQuery searchTemplateQuery = (SearchTemplateQuery) SearchTemplateQuery.fromJsonString(searchQueryInfo);

            orders = searchTemplateQuery.getOrders();

//            ObjectDescribeExt targetObjectDescribeExt = ObjectDescribeExt.of(targetObjectDescribe);
            query = serviceFacade.getSearchTemplateQuery(
                    controllerContext.getUser(),
                    objectDescribe,
                    getSearchTemplateId(),
                    getSearchQueryInfo(),
                    true
            );

            query.setPermissionType(1);
            IDataRightsParameter dataRightsParameter = new DataRightsParameter();
            dataRightsParameter.setRoleType("1");
            dataRightsParameter.setSceneType("all");
            dataRightsParameter.setCascadeDept(true);
            dataRightsParameter.setCascadeSubordinates(true);
            query.setDataRightsParameter(dataRightsParameter);

            handleFilters(query);
            modifyQueryByRefFieldName(query);
            handleDataRights(query);

            if (isPriceBookOpen) {
                dataRightInnerJoinerSqlPart = "";
                dataRightWhereSqlPart = "";
            } else {
                // 数据权限----------------------开始
                dataRightInnerJoinerSqlPart = handleSearchQueryService.getDataInnerJoinerSqlPart(controllerContext.getUser(), targetObjectDescribe, query);
                dataRightWhereSqlPart = handleSearchQueryService.getAuthPartSqlPart(controllerContext.getUser(), targetObjectDescribe, query, controllerContext);
                // 数据权限----------------------结束
            }

            if (CollectionUtils.isEmpty(orders)) {
                OrderBy orderBy = new OrderBy();
                orderBy.setFieldName("order_field");
                orderBy.setIsAsc(true);
                orderBy.setIndexName("order_field");
                this.orders = Lists.newArrayList(orderBy);
            }
        } else {
            query = super.buildSearchTemplateQuery();
        }

        // 处理query里面的filters
        productService.handleCategoryFilters(controllerContext.getTenantId(), controllerContext.getUser().getUserId(), query.getFilters());

        // 处理wheres里面的分类filters
        query.getWheres().stream()
                .filter(k -> CollectionUtils.isNotEmpty(k.getFilters()))
                .forEach(k -> productService.handleCategoryFilters(controllerContext.getTenantId(), controllerContext.getUser().getUserId(), k.getFilters()));

        spuFilters = query.getFilters();
        return query;
    }


    @Override
    protected QueryResult<IObjectData> findData(SearchTemplateQuery query) {
        if (isRealNotLookSpu && !isReturnNull) {

            String countSql;
            String getSpuListSql;

            boolean isSpecialSchema = schemaHelper.isSpecialSchema(controllerContext.getTenantId());

            String orderInfo = ConcatenateSqlUtils.getOrderPart(orders, "SPUObj",
                    isSpecialSchema ? "SPUObj" : "ext", objectDescribe);
            String pageInfo = ConcatenateSqlUtils.getPagePart(query);

            String spuDataRightInnerJoinerSqlPart, spuDataRightWhereSqlPart;
            if (!serviceFacade.isAdmin(controllerContext.getUser()) && !isPriceBookOpen) {

                SearchTemplateQuery spuSearchTemplateQuery = new SearchTemplateQuery();
                spuSearchTemplateQuery.setPermissionType(1);

                IDataRightsParameter dataRightsParameter = new DataRightsParameter();
                dataRightsParameter.setRoleType("1");
                dataRightsParameter.setSceneType("all");
                dataRightsParameter.setCascadeDept(true);
                dataRightsParameter.setCascadeSubordinates(true);
                query.setDataRightsParameter(dataRightsParameter);
                spuSearchTemplateQuery.setDataRightsParameter(dataRightsParameter);

                spuDataRightInnerJoinerSqlPart = handleSearchQueryService.getDataInnerJoinerSqlPart(controllerContext.getUser(), objectDescribe.getObjectDescribe(), spuSearchTemplateQuery);
                spuDataRightWhereSqlPart = handleSearchQueryService.getAuthPartSqlPart(controllerContext.getUser(), objectDescribe.getObjectDescribe(), spuSearchTemplateQuery, controllerContext);

            } else {
                spuDataRightInnerJoinerSqlPart = "";
                spuDataRightWhereSqlPart = "";

            }

            String spuFilterSqlPart = handleSearchQueryService.getFilterSqlPart(objectDescribe.getObjectDescribe(), spuFilters, "SPUObj");
//            灰度可售范围
            String availableRangeFilterSqlPart = "";
            //增加可售范围的校验
            if (GrayUtil.isGrayPriceBookRefactor(controllerContext.getTenantId())) {
//                String accountId = Optional.ofNullable(arg.getObjectData().get("account_id")).orElse("").toString();
                if (!StringUtils.isEmpty(accountId)) {
                    Set<String> rangeProductList = availableRangeUtils.getAvailableProductList(controllerContext.getUser(), accountId, partnerId, "", Lists.newArrayList());
                    if (org.apache.commons.collections.CollectionUtils.isEmpty(rangeProductList)) {
                        //给一个不存在的值,保证查不出任何数据
                        availableRangeFilterSqlPart = " and k.id='-99' ";
                    } else if (rangeProductList.contains(AvailableConstants.PublicConstants.RANGE_ALL)) {
                        availableRangeFilterSqlPart = "";
                    } else {
//                        SearchTemplateQueryExt.of(query).addFilter(Operator.IN, ProductConstant.FIELD_ID, Lists.newArrayList(rangeProductList));
                        availableRangeFilterSqlPart = String.format(" and k.id=any (array ['%s']) ", Joiner.on("','").join(rangeProductList));
                    }
                }
            }


            // 滤价目表
            if (isPriceBookOpen) {

                String wheresFilterSqlPart = handleSearchQueryService.getWheresSqlPart(
                        ObjectDescribeExt.of(targetObjectDescribe),
                        query.getWheres(),
                        "PriceBookProductObj")
                        .replaceAll("PriceBookProductObj\\.\"product_status\"", "k.product_status")
                        .replaceAll("ProductObj_product_id\\.\"product_status\"", "k.product_status");

                // TODO 自定义字段的 left join 处理
                countSql = ConcatenateSqlUtils.priceBookCountSql(
                        controllerContext.getTenantId(),
                        spuDataRightInnerJoinerSqlPart,
                        spuDataRightWhereSqlPart,
                        priceBookId,
                        dataRightInnerJoinerSqlPart,
                        dataRightWhereSqlPart,
                        wheresFilterSqlPart,
                        spuFilterSqlPart,
                        availableRangeFilterSqlPart,
                        isSpecialSchema
                );
                getSpuListSql = ConcatenateSqlUtils.priceBookChooseSql(
                        controllerContext.getTenantId(),
                        spuDataRightInnerJoinerSqlPart,
                        spuDataRightWhereSqlPart,
                        priceBookId,
                        dataRightInnerJoinerSqlPart,
                        dataRightWhereSqlPart,
                        wheresFilterSqlPart,
                        spuFilterSqlPart,
                        orderInfo,
                        pageInfo,
                        availableRangeFilterSqlPart,
                        isSpecialSchema
                );
            } else {
                availableRangeFilterSqlPart = availableRangeFilterSqlPart.replaceAll("k.id", "ProductObj.id");
                String spuCountSql = isSpecialSchema
                        ? ConcatenateSqlUtils.GET_UNDER_SPU_SKU_LIST_SQL_COUNT_SCHEMA
                        : ConcatenateSqlUtils.GET_UNDER_SPU_SKU_LIST_SQL_COUNT;
                String spuSql = isSpecialSchema
                        ? ConcatenateSqlUtils.GET_UNDER_SPU_SKU_LIST_SQL_SCHEMA
                        : ConcatenateSqlUtils.GET_UNDER_SPU_SKU_LIST_SQL;
                String whereSql = handleSearchQueryService.getWheresSqlPart(ObjectDescribeExt.of(targetObjectDescribe),
                        query.getWheres(), "ProductObj");
                countSql = ConcatenateSqlUtils.getUnderSpuSkuListCountSql(
                        spuCountSql,
                        controllerContext.getTenantId(),
                        spuDataRightInnerJoinerSqlPart,
                        availableRangeFilterSqlPart,
                        spuDataRightWhereSqlPart,
                        dataRightInnerJoinerSqlPart,
                        dataRightWhereSqlPart,
                        whereSql,
                        spuFilterSqlPart
                );
                getSpuListSql = ConcatenateSqlUtils.getUnderSpuSkuListSql(
                        spuSql,
                        controllerContext.getTenantId(),
                        spuDataRightInnerJoinerSqlPart,
                        availableRangeFilterSqlPart,
                        spuDataRightWhereSqlPart,
                        dataRightInnerJoinerSqlPart,
                        dataRightWhereSqlPart,
                        whereSql,
                        spuFilterSqlPart,
                        orderInfo,
                        pageInfo
                );
            }

            List<Map> countInfo;
            try {
                countSql = countSql.replaceAll("ProductObj_product_id", "k");
                log.warn("spu count sql {}", countSql);
                countInfo = objectDataService.findBySql(controllerContext.getTenantId(), countSql);
            } catch (MetadataServiceException e) {
                throw new RuntimeException();
            }

            QueryResult<IObjectData> result = new QueryResult<>();
            if (countInfo == null || !countInfo.stream().findFirst().isPresent()) {
                result.setData(Lists.newArrayListWithCapacity(0));

                result.setTotalNumber(0);
                return result;
            } else if (countInfo.stream().findFirst().get().get("count") == null) {
                result.setData(Lists.newArrayListWithCapacity(0));
                return result;
            } else {
                Integer count = Integer.parseInt(countInfo.stream().findFirst().get().get("count").toString());
                result.setTotalNumber(count);
            }
            try {
                getSpuListSql = getSpuListSql.replaceAll("ProductObj_product_id", "k");
                log.warn("spu find sql {}", getSpuListSql);
                List<Map> neededSpuIdsResult = objectDataService.findBySql(controllerContext.getTenantId(), getSpuListSql);
                List<String> spuIds = neededSpuIdsResult.stream().map(o -> o.get("_id").toString()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(spuIds)) {
                    List<IObjectData> tmpSpuDataList = serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), spuIds, Utils.SPU_API_NAME);
                    Map<String, IObjectData> spuIdDataMap = tmpSpuDataList.stream().collect(Collectors.toMap(IObjectData::getId, o -> o));

                    List<IObjectData> spuDataList = Lists.newArrayListWithCapacity(spuIds.size());
                    spuIds.forEach(o -> spuDataList.add(spuIdDataMap.get(o)));
                    result.setData(spuDataList);
                } else {
                    result.setData(Lists.newArrayListWithCapacity(0));
                }
                return result;
            } catch (MetadataServiceException e) {
                throw new MetaDataException(SystemErrorCode.METADATA_ERROR, e);
            }
        } else if (isRealNotLookSpu) {
            QueryResult<IObjectData> objectDataQueryResult = new QueryResult<>();
            objectDataQueryResult.setData(Lists.newArrayListWithCapacity(0));
            return objectDataQueryResult;
        } else {
            return super.findData(query);

        }
    }


    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        stopWatch.lap("after");

        // TODO 看看是否是必须的
        LayoutUtils.removeListLayoutUpdateTypeButtons4MobileOrH5(result.getListLayouts());

        stopWatch.lap("remove");
        if (isRealNotLookSpu) {
            // 无规格的数据<spuId,data>
            Map<String, ObjectDataDocument> noHaveSpecSpuIdDataMapping = Maps.newHashMap();

            result.getDataList().forEach(o -> {
                if (!Objects.equals(o.get("is_spec"), true)) {
                    noHaveSpecSpuIdDataMapping.put(o.getId(), o);
                }
            });
            if (MapUtils.isNotEmpty(noHaveSpecSpuIdDataMapping)) {
                fillProductData2NoSpecSpuSkuData(noHaveSpecSpuIdDataMapping);
            }
        }
        if (GrayUtil.isGrayOrderPromotion(controllerContext.getTenantId())) {
            if (PromotionUtil.getIsPromotionEnable(controllerContext.getUser(), controllerContext.getClientInfo())) {
                //叠加商品促销信息
                if (arg.getObjectData() != null && arg.getObjectData().get("account_id") != null) {
                    String accountId = arg.getObjectData().get("account_id").toString();
                    productPromotionList = promotionService.getProductPromotionList(controllerContext.getTenantId(), controllerContext.getUser(), accountId);
                    if (productPromotionList != null) {
                        List<String> productIds = new ArrayList<>();
                        productPromotionList.forEach(o -> {
                            String productId = o.getProductId();
                            if (!productIds.contains(productId)) {
                                productIds.add(productId);
                            }
                        });
                        if (!productIds.isEmpty()) {
                            List<IObjectData> productList = serviceFacade.findObjectDataByIdsIncludeDeleted(controllerContext.getUser(), productIds, Utils.PRODUCT_API_NAME);
                            if (productList != null && !productList.isEmpty()) {
                                result.getDataList().forEach(o -> {
                                    if (productList.stream().filter(r -> r.get("spu_id").equals(o.getId())).count() > 0) {
                                        o.put("have_promotion", true);
                                    }
                                });
                            }
                        }
                    }
                }
            }
        }

        stopWatch.logSlow(5000);
        return result;
    }


    /**
     * 将sku信息填充到无规格的spu上
     *
     * @param noHaveSpecSpuIdDataMapping key:spuId, value: data
     */
    private void fillProductData2NoSpecSpuSkuData(Map<String, ObjectDataDocument> noHaveSpecSpuIdDataMapping) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        IFilter filter = SearchUtil.filter("spu_id", Operator.IN, noHaveSpecSpuIdDataMapping.keySet());
        searchTemplateQuery.setFilters(Lists.newArrayList(filter));

        // limit 和 offset 都为0代表要取全部数据
        searchTemplateQuery.setLimit(1000);
        searchTemplateQuery.setOffset(0);

        // 获取无规格的sku数据列表
        QueryResult<IObjectData> skuDataQueryResult = serviceFacade.findBySearchQuery(controllerContext.getUser(), Utils.PRODUCT_API_NAME, searchTemplateQuery);

        stopWatch.lap("find sku");

        Map<String, IObjectData> skuIdDataMapping = Maps.newHashMap();
        Set<String> spuIds = Sets.newHashSet();

        Map<String, IObjectDescribe> apiNameDescribeMap = serviceFacade.findObjects(controllerContext.getTenantId(), Lists.newArrayList(Utils.PRODUCT_API_NAME, Utils.PRICE_BOOK_PRODUCT_API_NAME));

        stopWatch.lap("find describes");

        for (IObjectData skuData : skuDataQueryResult.getData()) {
            skuIdDataMapping.put(skuData.getId(), skuData);
            spuIds.add(skuData.get("spu_id", String.class));
        }

        // 价目表选择产品,处理单位为定价单位 价格处理成基准单位*定价单位转换比例
        ObjectDataDocument objectData1 = arg.getObjectData();
        if (objectData1 != null && Utils.PRICE_BOOK_API_NAME.equals(objectData1.get("source_object_api_name")) || isPriceBookOpen) {
            multiUnitService.handleUnitAndPrice(skuDataQueryResult.getData(), controllerContext.getTenantId());
        }
        transformData4ViewService.batchTransformDataForView(
                controllerContext.getUser(),
                apiNameDescribeMap.get(Utils.PRODUCT_API_NAME),
                skuDataQueryResult.getData()
        );

        stopWatch.lap("transform");

        if (isPriceBookOpen && isRealNotLookSpu) {
            Map<String, String> priceBookProductIds = getPriceBookProductIdAndSpuIdMapping(spuIds);

            List<IObjectData> priceBookProductDataList = serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), Lists.newArrayList(priceBookProductIds.keySet()), Utils.PRICE_BOOK_PRODUCT_API_NAME);
            //填充pricebook_id__r
            List<INameCache> recordName = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(priceBookProductDataList)) {
                Set<String> priceBookIdList = priceBookProductDataList.stream().map(x -> x.get(PriceBookConstants.ProductField.PRICEBOOKID.getApiName(), String.class)).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(priceBookIdList)) {
                    recordName.addAll(serviceFacade.findRecordName(ActionContextExt.of(controllerContext.getUser()).getContext()
                            , SFAPreDefineObject.Product.getApiName(), Lists.newArrayList(priceBookIdList)));
                }
            }
            priceBookProductDataList.forEach(o -> {
                ObjectDataDocument objectDataDocument = noHaveSpecSpuIdDataMapping.get(priceBookProductIds.get(o.getId()));
                if (objectDataDocument != null) {
                    IObjectData spuData = skuIdDataMapping.get(o.get("product_id", String.class));
                    Optional<INameCache> nameCache = recordName.stream().filter(x -> o.get(PriceBookConstants.ProductField.PRICEBOOKID.getApiName(), String.class).equals(x.getId())).findFirst();
                    o.set("product_id__ro", ObjectDataDocument.of(spuData));
                    o.set("pricebook_id__r", nameCache.isPresent() ? nameCache.get().getName() : "");
                    objectDataDocument.put("pricebook_product_id__ro", ObjectDataExt.of(o).toMap());
                }
            });

            stopWatch.lap("transform pricebookProduct");

        } else {
            skuDataQueryResult.getData().forEach(objectData -> {
                ObjectDataDocument objectDataDocument = noHaveSpecSpuIdDataMapping.get(objectData.get("spu_id", String.class));
                if (objectDataDocument != null) {
                    objectDataDocument.put("product_id__ro", ObjectDataExt.of(objectData).toMap());
                }
            });
        }
    }

    private Map<String, String> getPriceBookProductIdAndSpuIdMapping(Set<String> spuIds) {
        String findPriceBookProductSql = "select pb.id as _id,k.spu_id\n" +
                "from price_book_product as pb,\n" +
                "     biz_product as k\n" +
                "where k.spu_id in ('%s')\n" +
                "  and pb.tenant_id = k.tenant_id\n" +
                "  and k.id = pb.product_id\n" +
                "  and pb.tenant_id = '%s'\n" +
                "  and pb.pricebook_id = '%s';";
        String sql = String.format(findPriceBookProductSql, Joiner.on("','").join(spuIds), controllerContext.getTenantId(), priceBookId);
        try {
            return objectDataService.findBySql(controllerContext.getTenantId(), sql)
                    .stream()
                    .collect(Collectors.toMap(o -> o.get("_id").toString(), o -> o.get("spu_id").toString()));
        } catch (Exception e) {
            throw new RuntimeException();
        }
    }


}
