package com.facishare.crm.sfa.utilities.proxy;

import com.facishare.crm.sfa.utilities.proxy.model.SalesEventData;
import com.facishare.rest.core.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * Created by zhangtao on 2018/11/24.
 */
@RestResource(value = "CRM_SFA", desc = "CRM Rest API Call", contentType = "application/json")
public interface SalesEventProxy {
    @POST(value = "/crm/export/getsalesevents", desc = "根据客户ID获取销售记录列表")
    SalesEventData.Result getsalesevents(@HeaderMap Map<String, String> headers, @Body List<String> accountIds);
}
