package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.real.invoice.InvoiceService;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/5/26 10:34 上午
 * @illustration
 */
public class InvoiceApplicationListHeaderController extends StandardListHeaderController {

    private InvoiceService invoiceService = SpringUtil.getContext().getBean(InvoiceService.class);


    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        if(StringUtils.isNotEmpty(arg.getTargetObjectApiName()) && Objects.equals(arg.getTargetObjectApiName(), Utils.SALES_ORDER_API_NAME)){
            boolean newInvoiceOpen = invoiceService.isNewInvoiceOpen(controllerContext.getUser());
            if(newInvoiceOpen){
                ILayout iLayout = newResult.getLayout().toLayout();
                iLayout.setButtons(Lists.newArrayList());
                newResult.setLayout(LayoutDocument.of(iLayout));
            }
        }
        return newResult;
    }
}
