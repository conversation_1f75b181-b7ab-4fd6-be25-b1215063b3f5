package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.constant.AccountAddrConstants;
import com.facishare.crm.sfa.utilities.util.AccountAddrUtil;
import com.facishare.crm.sfa.utilities.validator.AccountAddrValidator;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;


/**
 * Created by wangmy on 2019/1/5 16:51.
 */
@Slf4j
public class AccountAddrEditAction extends StandardEditAction {
    protected IObjectData accountData;

    @Override
    protected void before(Arg arg) {
        AccountAddrValidator.beforeValidator(actionContext.getUser(), "Edit", arg.getObjectData().toObjectData());
        AccountAddrUtil.handleLocationField(arg.getObjectData().toObjectData());
        AccountAddrUtil.handleGeoPointField(arg.getObjectData().toObjectData());
        if (arg.getObjectData().containsKey("is_default_add")) {
            arg.getObjectData().remove("is_default_add");
        }
        if (arg.getObjectData().containsKey("is_ship_to_add")) {
            arg.getObjectData().remove("is_ship_to_add");
        }
        super.before(arg);
    }

    @Override
    @Transactional
    protected Result doAct(Arg arg) {
        Result result = super.doAct(arg);
        String accountId = arg.getObjectData().toObjectData().get(AccountAddrConstants.Field.ACCOUNT_ID.getApiName(), String.class);
        if (!Strings.isNullOrEmpty(accountId)) {
            accountData = serviceFacade.findObjectData(actionContext.getUser(), accountId, SFAPreDefineObject.Account.getApiName());
        }
        Boolean isMain = this.objectData.get(AccountAddrConstants.Field.IS_DEFAULT_ADD.getApiName(), Boolean.class);
        if (isMain && accountData != null) {
            //更新主地址时，更新biz_account表中的地址相关信息[兼容低版本客户端]
            AccountAddrUtil.bulkUpdateAccountLocation(actionContext.getUser(), Lists.newArrayList(accountData), Lists.newArrayList(result.getObjectData().toObjectData()));
        }
        return result;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        return result;
    }
}
