package com.facishare.crm.sfa.utilities.proxy.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

public interface NewOpportunityTriggerModel {
    @Data
    class Arg implements Serializable {
        private String entityId;
        private String objectId;
        private String stageId;
        private String sourceWorkflowId;
    }

    @Data
    class Result {
        boolean success;
        String message;
        int code;
        Object data;
    }
}
