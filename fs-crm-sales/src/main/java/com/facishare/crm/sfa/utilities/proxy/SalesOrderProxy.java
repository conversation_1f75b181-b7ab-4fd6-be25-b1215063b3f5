package com.facishare.crm.sfa.utilities.proxy;

import com.facishare.crm.sfa.utilities.proxy.model.ConfirmDeliveryData;
import com.facishare.crm.sfa.utilities.proxy.model.SetLogisticsStatusData;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

/**
 * Create by Baoxx 2018/12/27
 */
@RestResource(value = "CRM_SFA", desc = "CRM Rest API Call", contentType = "application/json")
public interface SalesOrderProxy {
//    @POST(value = "crm/customerorder/confirmreceive", desc = "确认收货")
//    ConfirmReceiveData.Result ConfirmReceive(@Body ConfirmReceiveData.Arg arg,
//                                             @HeaderMap Map<String, String> headers);
//
    @POST(value = "crm/customerorder/confirmdelivery", desc = "确认发货")
    ConfirmDeliveryData.Result ConfirmDelivery(@Body ConfirmDeliveryData.Arg arg,
                                               @HeaderMap Map<String, String> headers);
//    @POST(value = "crm/customerorder/setorderstatus",desc = "更新订单状态")
//    SetStatusData.Result SetStatus(@Body SetStatusData.Arg arg,
//                                   @HeaderMap Map<String, String> headers);
//    @POST(value = "/crm/customerorder/setlogisticsstatus",desc = "更新订单物流状态")
    SetLogisticsStatusData.Result SetLogisticsStatus(@Body SetLogisticsStatusData.Arg arg,
                                                     @HeaderMap Map<String, String> headers);
}