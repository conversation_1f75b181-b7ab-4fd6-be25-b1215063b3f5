package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.real.MultiUnitService;
import com.facishare.crm.sfa.predefine.service.real.MultiUnitServiceImpl;
import com.facishare.crm.sfa.utilities.util.LayoutUtils;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.List;
import java.util.Objects;

/**
 * @IgnoreI18nFile
 */
public class MultiUnitRelatedListController extends StandardListController {
    private final MultiUnitService multiUnitService = SpringUtil.getContext().getBean(MultiUnitServiceImpl.class);

    private String referenceObjectApiName;
    private boolean isOpenSpu = false;

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        isOpenSpu = SFAConfigUtil.isSpuOpen(controllerContext.getTenantId());
    }

    @Override
    protected void doFunPrivilegeCheck() {
        // not check funcPrivilege
    }

    @Override
    protected QueryResult<IObjectData> findData(SearchTemplateQuery query) {
        for (IFilter filter : query.getFilters()) {
            if ("spu_id".equals(filter.getFieldName())) {
                referenceObjectApiName = Utils.SPU_API_NAME;
                return multiUnitService.getDistinctMultiUnitDataBySpuId(filter.getFieldValues().stream().findFirst().get(), controllerContext.getTenantId());
            }

            if ("product_id".equals(filter.getFieldName())) {
                referenceObjectApiName = Utils.PRODUCT_API_NAME;
                return multiUnitService.getDistinctMultiUnitDataBySkuId(filter.getFieldValues().stream().findFirst().get(), controllerContext.getTenantId(), isOpenSpu);
            }
        }
        throw new ValidateException("入参错误");
    }

    @Override
    protected ILayout findLayout() {
        ILayout layout = super.findLayout();
        LayoutUtils.processMultiUnitField(layout, controllerContext.getTenantId());
        return layout;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        ObjectDescribeDocument objectDescribe = after.getObjectDescribe();

        ObjectDescribeExt describeExt;
        if (objectDescribe != null) {
            describeExt = ObjectDescribeExt.of(objectDescribe);
        } else {
            IObjectDescribe object = serviceFacade.findObject(controllerContext.getTenantId(), Utils.MULTI_UNIT_RELATED_API_NAME);
            describeExt = ObjectDescribeExt.of(object);
        }

        List<IFieldDescribe> fieldDescribes = describeExt.getFieldDescribes();
        if (Objects.equals(referenceObjectApiName, Utils.SPU_API_NAME)) {
            IObjectDescribe object = serviceFacade.findObject(controllerContext.getTenantId(), Utils.SPU_API_NAME);

            IFieldDescribe standardPrice = object.getFieldDescribe("standard_price");
            standardPrice.setApiName("price");
            standardPrice.set("describe_api_name", Utils.MULTI_UNIT_RELATED_API_NAME);
            fieldDescribes.add(standardPrice);
        } else {
            IObjectDescribe object = serviceFacade.findObject(controllerContext.getTenantId(), Utils.PRODUCT_API_NAME);

            IFieldDescribe standardPrice = object.getFieldDescribe("price");
            standardPrice.set("describe_api_name", Utils.MULTI_UNIT_RELATED_API_NAME);
            fieldDescribes.add(standardPrice);
        }

        describeExt.setFieldDescribes(fieldDescribes);
        after.setObjectDescribe(ObjectDescribeDocument.of(describeExt));

        return after;
    }
}
