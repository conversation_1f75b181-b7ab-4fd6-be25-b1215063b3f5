package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.util.AccountAddrUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardDetailListController;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;

import java.util.List;
import java.util.Optional;

import static com.facishare.crm.sfa.utilities.util.LayoutUtils.addEditForMastDetail;

public class AccountFinInfoDetailListController extends StandardDetailListController {
    @Override
    protected Result after(Arg arg, Result result){
        result = super.after(arg, result);
        if(result.getLayout() != null){
            ILayout layout = result.getLayout().toLayout();
            List<IComponent> componentList = null;
            try {
                componentList = layout.getComponents();
                if(CollectionUtils.notEmpty(componentList) && this.masterData != null){
                    Optional<IComponent> component = componentList.stream().filter(x-> x.get("api_name").equals("AccountFinInfoObj_md_group_component")).findFirst();
                    if(component.isPresent()){
                        AccountAddrUtil.getButtons(controllerContext.getUser(), Utils.ACCOUNT_FIN_INFO_API_NAME, component.get(), ObjectDataDocument.of(this.masterData));
                        layout.setComponents(componentList);
                        result.setLayout(LayoutDocument.of(layout));
                    }
                }
            } catch (MetadataServiceException e) {
                log.error(e.getMessage());
            }

        }
        return result;
    }
}
