package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.utilities.constant.NewOppportunityConstants;
import com.facishare.paas.appframework.core.predef.action.StandardUpdateImportVerifyAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.google.common.collect.Lists;

import java.util.List;

public class NewOpportunityLinesUpdateImportVerifyAction extends StandardUpdateImportVerifyAction {
    private List<String> removeFields = Lists.newArrayList(ObjectDataExt.EXTEND_OBJ_DATA_ID,
            NewOppportunityConstants.NewOpportunityLinesField.PRICEBOOKPRODUCTID.getApiName());

    @Override
    protected List<IFieldDescribe> getValidImportFields() {
        List<IFieldDescribe> fields = super.getValidImportFields();
        fields.removeIf(f -> removeFields.contains(f.getApiName()));
        return fields;
    }
}
