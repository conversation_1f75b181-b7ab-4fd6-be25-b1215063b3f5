package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.utilities.util.AccountAddrUtil;
import com.facishare.paas.appframework.core.predef.action.StandardUpdateImportDataAction;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;

public class AccountAddrUpdateImportDataAction extends StandardUpdateImportDataAction {
    @Override
    protected void customValidate(List<ImportData> dataList) {
        super.customValidate(dataList);
    }

    @Override
    protected void customDefaultValue(List<IObjectData> validList) {
        super.customDefaultValue(validList);
        AccountAddrUtil.bulkHandleLocationField(validList);
        AccountAddrUtil.bulkHandleGeoPointField(validList);
    }

    @Override
    protected void customAfterImport(List<IObjectData> actualList) {
        super.customAfterImport(actualList);
        AccountAddrUtil.bulkUpdateAccountLocation(actionContext.getUser(), actualList);
        AccountAddrUtil.sendLocationMq(actionContext.getUser(), SFAPreDefineObject.AccountAddr.getApiName(), actualList);
    }
}
