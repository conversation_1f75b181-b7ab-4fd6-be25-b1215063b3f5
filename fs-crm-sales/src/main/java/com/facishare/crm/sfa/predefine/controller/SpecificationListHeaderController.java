package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.metadata.ui.layout.IButton;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/11/20 15:21
 */
public class SpecificationListHeaderController extends StandardListHeaderController {
    @Override
    protected Result doService(Arg arg) {
        Result result = super.doService(arg);
        List<IButton> btnList = result.getLayout().toLayout().getButtons();
        btnList.removeIf(o -> "IntelligentForm_button_default".equals(o.getName()));
        result.getLayout().toLayout().setButtons(btnList);
        return result;
    }
}
