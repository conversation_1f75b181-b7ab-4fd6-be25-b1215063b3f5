package com.facishare.crm.sfa.predefine.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.NameFilter;
import com.alibaba.fastjson.serializer.SerializeConfig;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.util.ConcatenateSqlUtils;
import com.facishare.paas.appframework.core.exception.SystemErrorCode;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.StandardChangeOwnerAction;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.common.util.BulkOpResult;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by luxin on 2018/12/18.
 */
@Slf4j
public class SPUChangeOwnerAction extends StandardChangeOwnerAction {
    private ActionLocateService actionLocateService = SpringUtil.getContext().getBean(ActionLocateServiceImpl.class);
    private ObjectDataServiceImpl objectDataService = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);
    private Map<String, String> spuIdOwnerMap;

    @Override
    protected void before(Arg arg) {
        // 商品产品不需要审批流
        arg.setSkipTriggerApprovalFlow(true);
        actionContext.getRequestContext().setAttribute(RequestContext.Attributes.TRIGGER_FLOW, false);

        spuIdOwnerMap = getBeforeChangeOwnerSpuOwnerInfo(arg);
        super.before(arg);
    }


    @Override
    protected Result after(Arg arg, Result result) {
        Result result1 = super.after(arg, result);
        doProductDataChangeOwner(arg, bulkOpResult, actionContext.getRequestContext());
        return result1;
    }

    /**
     * 获取未更换负责人之前的商品的负责人信息
     *
     * @param arg
     */
    private Map<String, String> getBeforeChangeOwnerSpuOwnerInfo(Arg arg) {
        List<String> needChangeOwnerSpuIds = arg.getData().stream().map(ChangeOwnerData::getObjectDataId).distinct().collect(Collectors.toList());

        String tmpFindOwnerSql = "select id as _id, owner\n" +
                "from stand_prod_unit\n" +
                "where tenant_id = '%s'\n" +
                "  and id in('%s');";
        String findOwnerSql = String.format(tmpFindOwnerSql, actionContext.getTenantId(), ConcatenateSqlUtils.COLLECTION_JOINER.join(needChangeOwnerSpuIds));

        try {
            List<Map> spuIdOwnerInfoList = objectDataService.findBySql(actionContext.getTenantId(), findOwnerSql);
            return spuIdOwnerInfoList.stream().filter(o -> o.get("owner") != null).collect(Collectors.toMap(o -> o.get("_id").toString(), o -> o.get("owner").toString()));
        } catch (MetadataServiceException e) {
            throw new MetaDataException(SystemErrorCode.METADATA_ERROR, e);
        }
    }

    private void doProductDataChangeOwner(Arg arg, BulkOpResult bulkOpResult, RequestContext requestContext) {
        if (CollectionUtils.isNotEmpty(bulkOpResult.getSuccessObjectDataList())) {
            ActionContext actionContext1 = new ActionContext(requestContext, Utils.PRODUCT_API_NAME, actionContext.getActionCode());
            List<Map> productInfoList = getProductInfoList(bulkOpResult, actionContext1);

            Arg productChangeOwnerArg = new Arg();
            setChangeOwnerArgValue(arg, productChangeOwnerArg);
            productChangeOwnerArg.setNoSepcSpu(true);

            List<ChangeOwnerData> skuChangeOwnerDataList = Lists.newArrayList();

            Map<String, List<String>> spuIdOwnersMap = productChangeOwnerArg.getData().stream()
                    .collect(Collectors.toMap(ChangeOwnerData::getObjectDataId, ChangeOwnerData::getOwnerId, (a, b) -> b));

            productInfoList.forEach(map -> {
                String spuId = map.get("spu_id").toString();
                String owner = (String) map.get("owner");

                String beforeUpdateOwner = spuIdOwnerMap.get(spuId);
                // 负责人相同或是没有规格的都需要同步更新负责人
                if (Objects.equals(beforeUpdateOwner, owner) || !Objects.equals(map.get("is_spec"), true)) {
                    ChangeOwnerData changeOwnerData = ChangeOwnerData.builder().objectDataId(map.get("_id").toString()).ownerId(spuIdOwnersMap.get(spuId)).build();
                    skuChangeOwnerDataList.add(changeOwnerData);
                }
            });

            if (skuChangeOwnerDataList.size() > 0) {
                productChangeOwnerArg.setData(skuChangeOwnerDataList);
                String argJson = getArgJsonString(productChangeOwnerArg);
                Action action = actionLocateService.locateAction(actionContext1, argJson);
                action.act(action.getArg());
            }
        }
    }

    private List<Map> getProductInfoList(BulkOpResult bulkOpResult, ActionContext actionContext1) {
        String tmpFindUnderSkuSpuListSql = "select k.id as _id, k.spu_id, k.owner, s.is_spec\n" +
                "from biz_product k\n" +
                "       left join stand_prod_unit s on k.tenant_id = s.tenant_id and k.spu_id = s.id and s.is_deleted = k.is_deleted\n" +
                "where k.tenant_id = '%s'\n" +
                "  and k.is_deleted = 0\n" +
                "  and k.spu_id in('%s');";
        List<String> successSpuIds = bulkOpResult.getSuccessObjectDataList().stream().map(IObjectData::getId).collect(Collectors.toList());

        String findUnderSkuSpuListSql = String.format(
                tmpFindUnderSkuSpuListSql,
                actionContext.getTenantId(),
                ConcatenateSqlUtils.COLLECTION_JOINER.join(successSpuIds)
        );

        try {
            return objectDataService.findBySql(actionContext1.getTenantId(), findUnderSkuSpuListSql);
        } catch (MetadataServiceException e) {
            throw new MetaDataException(SystemErrorCode.METADATA_ERROR, e);
        }
    }

    private void setChangeOwnerArgValue(Arg arg, Arg productChangeOwnerArg) {
        productChangeOwnerArg.setSkipTriggerApprovalFlow(true);

        try {
            BeanUtils.copyProperties(productChangeOwnerArg, arg);
        } catch (Exception e) {
            log.error("copyProperties,arg", arg, e);
            throw new RuntimeException(e);
        }
    }

    @NotNull
    private String getArgJsonString(Arg productChangeOwnerArg) {
        NameFilter nameFilter = (o, s, o1) -> {
            if ("data".equals(s)) {
                return s.substring(0, 1).toUpperCase()
                        .concat(s.substring(1).toLowerCase());
            } else {
                return s;
            }
        };

        SerializeConfig serializeConfig = new SerializeConfig();
        serializeConfig.addFilter(Arg.class, nameFilter);
        return JSON.toJSONString(productChangeOwnerArg, serializeConfig);
    }

}
