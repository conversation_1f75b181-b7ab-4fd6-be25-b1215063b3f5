package com.facishare.crm.sfa.predefine.action.listener;

import com.facishare.crm.sfa.utilities.proxy.model.salesorder.salesorderparam.BulkRecoverAfterModel;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.salesorderparam.BulkRecoverBeforeModel;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.StandardBulkRecoverAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created by renlb on 2019/3/14.
 */
@Component
public class StandardBulkRecoverActionListener implements ActionListener<StandardBulkRecoverAction.Arg, StandardBulkRecoverAction.Result> {

    @Autowired
    ServiceFacade serviceFacade;

    @Override
    public void before(ActionContext actionContext, StandardBulkRecoverAction.Arg arg) {
        if(CollectionUtils.notEmpty(arg.getIdList())) {
            List<IObjectData> objectDataList = serviceFacade.findObjectDataByIdsIncludeDeleted(actionContext.getUser(),
                    arg.getIdList(), actionContext.getObjectApiName());
            if(CollectionUtils.notEmpty(objectDataList)) {
                BulkRecoverBeforeModel.Arg serviceArg = new BulkRecoverBeforeModel.Arg();
                List<BulkRecoverBeforeModel.BulkObj> bulkObjs = Lists.newArrayList();
                for(IObjectData objectData : objectDataList) {
                    ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
                    BulkRecoverBeforeModel.BulkObj bulkObj = new BulkRecoverBeforeModel.BulkObj();
                    bulkObj.setDataId(objectDataExt.getId());
                    bulkObj.setRecoverToStatus(objectDataExt.getLifeStatusBeforeInvalid());
                    bulkObjs.add(bulkObj);
                }

                serviceArg.setBulkObjs(bulkObjs);
                callBeforeInterceptor(serviceArg);
            }
        }
    }

    @Override
    public void after(ActionContext actionContext, StandardBulkRecoverAction.Arg arg, StandardBulkRecoverAction.Result result) {
        if(CollectionUtils.notEmpty(arg.getIdList())) {
            List<IObjectData> objectDataList = serviceFacade.findObjectDataByIdsIncludeDeleted(actionContext.getUser(),
                    arg.getIdList(), actionContext.getObjectApiName());
            if(CollectionUtils.notEmpty(objectDataList)) {
                BulkRecoverAfterModel.Arg serviceArg = new BulkRecoverAfterModel.Arg();
                List<BulkRecoverAfterModel.BulkObj> bulkObjs = Lists.newArrayList();
                for(IObjectData objectData : objectDataList) {
                    ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
                    BulkRecoverAfterModel.BulkObj bulkObj = new BulkRecoverAfterModel.BulkObj();
                    bulkObj.setDataId(objectDataExt.getId());
                    bulkObj.setBeforeLifeStatus(ObjectLifeStatus.INVALID.getCode());
                    bulkObj.setAfterLifeStatus(objectDataExt.getLifeStatus().getCode());
                    bulkObjs.add(bulkObj);
                }

                serviceArg.setBulkObjs(bulkObjs);
                callAfterInterceptor(serviceArg);
            }
        }
    }

    protected void callBeforeInterceptor(BulkRecoverBeforeModel.Arg arg) {

    }

    protected void callAfterInterceptor(BulkRecoverAfterModel.Arg arg) {

    }
}
