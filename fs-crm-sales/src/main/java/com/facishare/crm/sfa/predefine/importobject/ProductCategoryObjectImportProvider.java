package com.facishare.crm.sfa.predefine.importobject;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.paas.appframework.metadata.importobject.DefaultObjectImportProvider;
import com.facishare.paas.appframework.metadata.importobject.ImportObject;
import com.facishare.paas.appframework.metadata.importobject.ImportType;
import com.facishare.paas.appframework.metadata.importobject.MatchingType;
import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * @author: sundy
 * @date: 2020/11/24 10:50
 * @description:
 */
@Component
public class ProductCategoryObjectImportProvider extends DefaultObjectImportProvider {
    @Override
    public String getObjectCode() {
        return SFAPreDefineObject.ProductCategory.getApiName();
    }

    @Override
    public Optional<ImportObject> getImportObject(IObjectDescribe describe, IUniqueRule uniqueRule) {
        return super.getImportObject(describe, uniqueRule);
    }

    @Override
    protected ImportType getImportType(IObjectDescribe objectDescribe, IUniqueRule uniqueRule) {
        return ImportType.UNSUPPORT_UPDATE_IMPORT;
    }

    @Override
    protected List<MatchingType> getMatchingTypesByInsert(IObjectDescribe objectDescribe) {
        return Lists.newArrayList(MatchingType.ID);
    }

    @Override
    protected boolean getNoBatch() {
        return true;
    }

    @Override
    protected boolean getOpenWorkFlow(IObjectDescribe objectDescribe) {
        return false;
    }
}
