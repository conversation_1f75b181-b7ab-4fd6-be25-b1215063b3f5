package com.facishare.crm.sfa.predefine.action;

import com.facishare.crmcommon.constants.SystemConstants;
import com.facishare.crm.sfa.predefine.action.model.LeadsSaveArg;
import com.facishare.crm.sfa.predefine.action.model.LeadsSaveResult;
import com.facishare.crm.sfa.predefine.service.model.Duplicate.LeadsDuplicatedProcessing;
import com.facishare.crm.sfa.utilities.constant.AccountConstants;
import com.facishare.crm.sfa.utilities.constant.LeadsConstants;
import com.facishare.crm.sfa.utilities.util.*;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.AcceptableValidateException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.common.util.AppIdMapping;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardEditAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.appframework.prm.util.PrmConstant;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_REACH_LIMIT_OBJ;

/**
 * Created by zhaopx on 2018/3/21.
 */
@Slf4j
public class LeadsEditAction extends AbstractStandardEditAction<LeadsSaveArg> {
    private LeadsDuplicatedProcessing.ProcessSingleResult processSingleResult;

    private static List<String> editExceptFields = Lists.newArrayList(SystemConstants.Field.Owner.apiName
            , "expire_time", "last_followed_time", "owner_change_time", "remind_days", "returned_time", LeadsConstants.Field.LEADS_POOL_ID.getApiName(),
            LeadsConstants.Field.BIZ_STATUS.getApiName()
    );
    private IObjectData originalObjectData;

    @Override
    protected void before(LeadsSaveArg arg) {
        log.info("LeadsEditAction>before()arg=" + JsonUtil.toJsonWithNullValues(arg));
        super.before(arg);
        //获取手机归属地字段
        AccountUtil.getPhoneNumberInfo(arg.getObjectData(), "mobile");
        this.originalObjectData = serviceFacade.findObjectData(actionContext.getUser(), objectData.getId(), objectDescribe);
        ObjectDataDocument dataDocument = arg.getObjectData();
        if (ObjectLimitUtil.isGrayLeadsLimit(actionContext.getTenantId())) {
            List<IObjectData> oldDataList = ObjectDataExt.copyList(dataList);
            List<IObjectData> checkLimitDataList = ObjectDataExt.copyList(Lists.newArrayList(dataDocument.toObjectData()));
            checkLimitDataList.forEach(x -> {
                x.set(SystemConstants.Field.LifeStatus.apiName, ObjectLifeStatus.NORMAL.getCode());
                x.setLastModifiedBy(actionContext.getUser().getUserId());
                x.setLastModifiedTime(System.currentTimeMillis());
            });

            if (AppIdMapping.appIdMapping.get(PrmConstant.PRM_APP_ID).equals(actionContext.getAppId())) {
                String outOwner = AccountUtil.getOutOwner(dbMasterData);
                String outTenantId = AccountUtil.getStringValue(dbMasterData, SystemConstants.Field.OutTenantId.apiName, "");
                if (StringUtils.isNotBlank(outOwner) && StringUtils.isNotBlank(outTenantId)) {
                    ObjectLimitUtil.CheckLimitResult checkOutLimitResult = ObjectLimitUtil.checkOutUserObjectLimitForEdit(actionContext.getUser(), actionContext.getObjectApiName(), outTenantId, outOwner, oldDataList, checkLimitDataList, objectDescribe, true);
                    if (CollectionUtils.notEmpty(checkOutLimitResult.getFailureIds())) {
                        throw new ValidateException(String.format(I18N.text(SFA_REACH_LIMIT_OBJ),
                                I18N.text("AccountObj.attribute.self.display_name")));
                    }
                }
            }

            String owner = AccountUtil.getOwner(dbMasterData);
            if (StringUtils.isNotBlank(owner)) {
                ObjectLimitUtil.CheckLimitResult checkLimitResult = ObjectLimitUtil.checkObjectLimitForEdit(actionContext.getUser(), actionContext.getObjectApiName(), owner, oldDataList, checkLimitDataList, objectDescribe);
                if (CollectionUtils.notEmpty(checkLimitResult.getFailureIds())) {
                    throw new ValidateException(String.format(I18N.text(SFA_REACH_LIMIT_OBJ),
                            I18N.text("LeadsObj.attribute.self.display_name")));
                }
            }
        }
        LeadsDuplicatedProcessing.TriggerAction triggerAction = DuplicatedProcessingUtils.getTriggerAction(this.actionContext,
                LeadsDuplicatedProcessing.TriggerAction.EDIT);
        this.processSingleResult = DuplicatedProcessingUtils.processDuplicatedBefore(actionContext.getUser(), arg, triggerAction, objectData);
        if (this.processSingleResult.isNeedConfirm()) {
            throw new AcceptableValidateException(buildValidateResult());
        }
        for (String field : editExceptFields) {
            if (dataDocument.containsKey(field)) {
                dataDocument.remove(field);
            }
        }

    }

    @Override
    protected Result after(LeadsSaveArg arg, Result result) {
        log.info("LeadsAddAction>after()arg=" + JsonUtil.toJsonWithNullValues(arg));
        log.info("LeadsAddAction>after()result=" + JsonUtil.toJsonWithNullValues(result));
        super.after(arg, result);
        try {
            ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
            parallelTask.submit(() ->
            {
                IObjectData objectData = serviceFacade.findObjectData(actionContext.getUser(), this.objectData.getId(), objectDescribe);
                DuplicatedProcessingUtils.processDuplicatedAfter(actionContext.getUser(), objectData,
                        originalObjectData, processSingleResult);
            });
            parallelTask.run();
        } catch (Exception ex) {
            log.error("parallel execute error:" + ex.getMessage(), ex);
        }

        if (result.getObjectData() != null && result.getObjectData().get("CRMResponse") != null) {
            Map<String, Object> rstObjectData = (Map<String, Object>) result.getObjectData().get("CRMResponse");
            rstObjectData.put("_id", objectData.getId());
            result.setObjectData(ObjectDataDocument.of(rstObjectData));
        }
        return result;
    }

    @Override
    protected LeadsSaveResult buildValidateResult() {
        Result result = super.buildValidateResult();
        LeadsSaveResult saveResult = DuplicatedProcessingUtils.buildResult(result, processSingleResult);
        return saveResult;
    }

    @Override
    protected Set<String> getIgnoreFieldsForApproval() {
        Set<String> result = super.getIgnoreFieldsForApproval();
        result.add(AccountConstants.Field.EXPIRE_TIME);
        result.add(AccountConstants.Field.REMIND_DAYS);
        result.add("last_follow_time");
        return result;
    }
}
