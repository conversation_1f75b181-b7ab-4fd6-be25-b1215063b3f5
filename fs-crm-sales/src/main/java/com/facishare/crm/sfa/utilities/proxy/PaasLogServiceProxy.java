package com.facishare.crm.sfa.utilities.proxy;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.utilities.proxy.model.PaasLogServiceModel;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

/**
 * Created by yuanjl on 2020/06/04
 */
@RestResource(value = "Log", desc = "paas log search", contentType = "application/json")
public interface PaasLogServiceProxy {
    @POST(value = "/mob/search", desc = "手机端查询公海、线索池日志")
    PaasLogServiceModel.LogSearchResult getMobileLog(@HeaderMap Map<String, String> headers, @Body JSONObject arg);

    @POST(value = "/web/search", desc = "WEB端查询公海、线索池日志")
    PaasLogServiceModel.LogSearchResult getWebLog(@HeaderMap Map<String, String> headers, @Body JSONObject arg);

}
