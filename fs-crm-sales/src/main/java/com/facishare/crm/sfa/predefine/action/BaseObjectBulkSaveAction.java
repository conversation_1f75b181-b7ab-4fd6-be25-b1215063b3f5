package com.facishare.crm.sfa.predefine.action;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.AcceptableValidateException;
import com.facishare.paas.appframework.core.exception.RecordTypeNotFound;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.BaseObjectApprovalAction;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.TeamMember;
import com.facishare.paas.appframework.metadata.dto.RuleResult;
import com.facishare.paas.common.util.UdobjConstants;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.MultiRecordType;
import com.facishare.paas.metadata.api.describe.*;
import com.facishare.paas.metadata.impl.describe.PaymentFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SignInFieldDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.github.autoconf.ConfigFactory;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.util.RequestUtil.VERSION_670;

/**
 * Created with IntelliJ IDEA. User: quzhf Date: 2017/11/23 16:29 Description:
 * @IgnoreI18nFile
 */
@Slf4j
public abstract class BaseObjectBulkSaveAction extends BaseObjectApprovalAction<BaseObjectBulkSaveAction.Arg, BaseObjectBulkSaveAction.Result> {
    protected List<IObjectData> objectDataList;
    @Autowired
    protected ThreadPoolTaskExecutor executor = (ThreadPoolTaskExecutor) SpringUtil.getContext().getBean("taskExecutor");

    protected abstract String getIRule();

    protected abstract ObjectAction getObjectAction();

    protected ButtonExecutor.Result validatedFunctionResult;
    protected static Set<String> grayFunctionTenantIds = Sets.newHashSet();
    protected static Boolean grayFunctionAllTenantIds = Boolean.FALSE;

    static {
        ConfigFactory.getInstance().getConfig("fs-crm-java-console", config -> {
            grayFunctionTenantIds = Sets.newHashSet(config.get("grayFunctionTenantIds", "").split(","));
            grayFunctionAllTenantIds = Boolean.valueOf(config.get("grayFunctionAllTenantIds", Boolean.FALSE.toString()));
        });
    }

    @Override
    protected void before(BaseObjectBulkSaveAction.Arg arg) {
        super.before(arg);
        init();
        validate();
    }

    @Override
    protected void finallyDo() {
        stopWatch.logSlow(2000);
    }

    protected void init() {
        this.objectDataList = arg.getDataList().stream().map(k -> k.toObjectData()).collect(Collectors.toList());
        stopWatch.lap("init");
    }

    protected void validate() {
        stopWatch.lap("validate");
        objectDataList.forEach(objectData -> {
            //校验arg参数的完整性和合法性
            ObjectDataExt.of(objectData).validate(objectDescribe);
        });
//        validateLookupData(objectDataList, objectDescribe);
        //校验规则校验
        validateValidationRules(objectDataList, objectDescribe, getIRule());
        stopWatch.lap("validateRule");

        //自定义函数校验
        callValidationFunction();
        stopWatch.lap("validateCustomFunction");
    }

    protected void callValidationFunction() {
        // 跳过前置验证函数
        if (skipValidationFunction() || Objects.isNull(getButtonApiName())) {
            return;
        }
        batchTriggerFunction();
    }

    protected void batchTriggerFunction() {
        //标记：函数有没有拦截报错
        AtomicBoolean mark = new AtomicBoolean(Boolean.FALSE);
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createBackgroundTask();
        objectDataList.forEach(objectData -> parallelTask.submit(() -> {
            try {
                if (mark.get()) return;
                triggerFunction(objectData, mark);
            } catch (Exception e) {
                log.error("PriceBookProductBulkCreateAction batchTriggerFunction error.tenantId:{},traceId:{}", actionContext.getTenantId(), TraceContext.get().getTraceId(), e);
            }
        }));
        try {
            parallelTask.await(15, TimeUnit.SECONDS);

            if (!mark.get()) return;
            if (validationFunctionIsBlock() && (RequestUtil.isMobileRequestBeforeVersion(VERSION_670) || !RequestUtil.isCepRequest())) {
                String message = validatedFunctionResult.getReturnValue() == null ? "" : validatedFunctionResult.getReturnValue().toString();
                throw new ValidateException(I18N.text(I18NKey.FRONT_VALIDATE_FUNCTION_FAILED, message));
            }
            // 有返回值，抛出阻断异常
            if (validatedFunctionResult.isHasReturnValue() && RequestUtil.isCepRequest()) {
                throw new AcceptableValidateException(buildValidateResult());
            }
        } catch (TimeoutException e) {
            log.error("PriceBookProductBulkCreateAction batchTriggerFunction  time out.tenantId:{},traceId:{}", actionContext.getTenantId(), TraceContext.get().getTraceId(), e);
            throw new RuntimeException("运行超时");
        }
    }

    protected void triggerFunction(IObjectData objectData, AtomicBoolean mark) {
        ButtonExecutor.Arg executorArg = ButtonExecutor.Arg.builder()
                .buttonApiName(getButtonApiName())
                .describeApiName(actionContext.getObjectApiName())
                .objectDataId(objectData.getId())
                .args(Maps.newHashMap())
                .objectData(objectData)
                .details(Maps.newHashMap())
                .build();
        ButtonExecutor.Result result;
        log.debug("callValidationFunction arg:{}", executorArg);
        result = infraServiceFacade.triggerValidationFunction(actionContext.getUser(), executorArg);
        log.debug("callValidationFunction result:{}", validatedFunctionResult);
        if (result.isHasReturnValue()
                && result.isBlock()
                && (RequestUtil.isMobileRequestBeforeVersion(VERSION_670) || !RequestUtil.isCepRequest())) {
            mark.set(Boolean.TRUE);
            validatedFunctionResult = result;
        }

        // 有返回值，抛出阻断异常
        if (result.isHasReturnValue() && RequestUtil.isCepRequest()) {
            validatedFunctionResult = result;
            mark.set(Boolean.TRUE);
        }
    }

    protected boolean validationFunctionIsBlock() {
        return validatedFunctionResult.isHasReturnValue() && validatedFunctionResult.isBlock();
    }

    protected boolean skipValidationFunction() {
        return true;
    }

    protected void batchSetDefaultRecordType(List<IObjectData> objectDataList, ObjectDescribeExt describeExt) {
        objectDataList.forEach(objectData -> {
            objectData.setTenantId(describeExt.getTenantId());
            String recordType = objectData.getRecordType();
            if (Strings.isNullOrEmpty(recordType) || "default".equals(recordType) || "sail".equals(recordType)) {
                objectData.setRecordType(MultiRecordType.RECORD_TYPE_DEFAULT);
            } else {
                Optional<IRecordTypeOption> optional = describeExt.getRecordTypeOption(recordType);
                IRecordTypeOption option = optional.orElseThrow(() -> new RecordTypeNotFound("业务类型不存在"));
                if (!option.isActive()) {
                    throw new RecordTypeNotFound(String.format("业务类型[%s]已被禁用", option.getLabel()));
                }
            }
        });
    }

    protected void validateValidationRules(List objectDataList, IObjectDescribe objectDescribe, String ruleOperation) {
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        describeMap.put(objectDescribe.getApiName(), objectDescribe);
        HashMap<String, List<IObjectData>> dataMap = Maps.newHashMap();
        dataMap.put(objectDescribe.getApiName(), objectDataList);
        RuleResult ruleResult = infraServiceFacade.validateRule(actionContext.getUser(), ruleOperation, objectDescribe, objectDataList);

        if (ruleResult.isMatch()) {
            throw new ValidateException(ruleResult.getFailMessage());
        }
    }


    protected void batchModifyObjectDataBeforeCreate(List<IObjectData> objectDataList, IObjectDescribe describe) {
        ObjectDescribeExt objectDescribeExt = ObjectDescribeExt.of(objectDescribe);
        CountDownLatch latch = new CountDownLatch(objectDataList.size());
        objectDataList.stream().forEach(objectData -> {
            executor.execute(() -> {
                try {
                    ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
                    // 修改objectData,写入tenantId、创建人、最后修改人、最后修改时间等系统字段。
                    setDefaultSystemInfo(objectData);
                    //设置默认的相关团队成员的负责人
                    setDefaultTeamMember(objectDataExt);
                    //设置签到字段的相关默认值
                    setDefaultForSignIn(objectData, objectDescribeExt);
                    //设置支付字段的相关默认值
                    setDefaultForPayment(objectData, objectDescribeExt);
                } finally {
                    latch.countDown();
                }
            });
        });
        try {
            latch.await();
        } catch (InterruptedException e) {
            log.error("batchModifyObjectDataBeforeCreate list,CountDownLatch error.", e);
        }
        //设置默认业务类型
        batchSetDefaultRecordType(objectDataList, objectDescribeExt);
        //设置负责人
        batchSynchronizeOwnerWithMasterObjectData(objectDataList, objectDescribeExt);
    }

    protected void setDefaultSystemInfo(IObjectData objectData) {
        objectData.setTenantId(actionContext.getTenantId());
        objectData.setCreatedBy(actionContext.getUser().getUserId());
        objectData.setLastModifiedBy(actionContext.getUser().getUserId());
        objectData.set(UdobjConstants.LIFE_STATUS_API_NAME, UdobjConstants.LIFE_STATUS_VALUE_NORMAL);
    }


    protected void setDefaultTeamMember(ObjectDataExt objectDataExt) {
        objectDataExt.getOwnerId().ifPresent(x -> {
            TeamMember teamMember = new TeamMember(x, TeamMember.Role.OWNER, TeamMember.Permission.READANDWRITE);
            objectDataExt.addTeamMembers(Lists.newArrayList(teamMember));
        });
    }

    protected void setDefaultForPayment(IObjectData objectData, ObjectDescribeExt objectDescribeExt) {
        Optional<PaymentFieldDescribe> signInFieldDescribe = objectDescribeExt.getPaymentFieldDescribe();
        signInFieldDescribe.ifPresent(x -> {
            objectData.set(x.getPayStatusFieldApiName(), Payment.PAY_STATUS_INCOMPLETE);
        });
    }

    protected void setDefaultForSignIn(IObjectData objectData, ObjectDescribeExt objectDescribeExt) {
        Optional<SignInFieldDescribe> signInFieldDescribe = objectDescribeExt.getSignInFieldDescribe();
        signInFieldDescribe.ifPresent(x -> {
            objectData.set(x.getSignInInfoListFieldApiName(), null);
            objectData.set(x.getIntervalFieldApiName(), null);
            objectData.set(x.getVisitStatusFieldApiName(), SignIn.SIGN_STATUS_INCOMPLETE);
            objectData.set(x.getSignInStatusFieldApiName(), SignIn.SIGN_STATUS_INCOMPLETE);
            objectData.set(x.getSignInTimeFieldApiName(), null);
            objectData.set(x.getSignOutStatusFieldApiName(), SignIn.SIGN_STATUS_INCOMPLETE);
            objectData.set(x.getSignOutTimeFieldApiName(), null);
            objectData.set(x.getSignInLocationFieldApiName(), null);
            objectData.set(x.getSignOutLocationFieldApiName(), null);
        });
    }

    protected void batchSynchronizeOwnerWithMasterObjectData(List<IObjectData> objectDataList, ObjectDescribeExt describeExt) {
        describeExt.getMasterDetailFieldDescribe().ifPresent(masterDetailFieldDescribe -> {
            String masterDescribeApiName = masterDetailFieldDescribe.getTargetApiName();
            //任取其中一条
            IObjectData objectData = objectDataList.get(0);
            String masterId = objectData.get(masterDetailFieldDescribe.getApiName(), String.class);
            //值得注意的是,如果masterId==null也是正常的情况,因为此函数的进入场景可能是主从一起新建时的从对象,这时候就是没有masterId的。
            if (masterId != null) {
                IObjectDescribe masterDescribe = serviceFacade.findObject(objectData.getTenantId(), masterDescribeApiName);
                IObjectData masterObjectData = serviceFacade.findObjectData(objectData.getTenantId(), masterId, masterDescribe);
                Optional<String> ownerId = ObjectDataExt.of(masterObjectData).getOwnerId();
                //所有数据都新增负责人字段
                ownerId.ifPresent(x -> {
                    Objects.requireNonNull(ownerId);
                    objectDataList.forEach(k -> {
                        k.set(ObjectDataExt.OWNER, Arrays.asList(x));
                    });
                });

            }
        });
    }

    protected void logAsync(Map<String, IObjectDescribe> objectDescribes, List<IObjectData> allObjectData, EventType eventType, ActionType actionType) {
        Map<String, List<String>> idMap = Maps.newHashMap();
        for (IObjectData data : allObjectData) {
            if (idMap.containsKey(data.getDescribeApiName())) {
                idMap.get(data.getDescribeApiName()).add(data.getId());
            } else {
                idMap.put(data.getDescribeApiName(), Lists.newArrayList(data.getId()));
            }
        }

        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        Set<Map.Entry<String, List<String>>> entries = idMap.entrySet();
        for (Map.Entry<String, List<String>> entry : entries) {
            parallelTask.submit(() -> {
                List<IObjectData> dataList = serviceFacade.findObjectDataByIds(
                        actionContext.getTenantId(), entry.getValue(), entry.getKey());
                serviceFacade.log(actionContext.getUser(), eventType, actionType, objectDescribes, dataList);
            });
        }
        try {
            parallelTask.await(20, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            log.error("Time out for log in add", e);
        }
    }

    /**
     * @return true:跳过低层级校验
     */
    @Override
    protected boolean isBatchAction() {
        return true;
    }

    @Data
    public static class Arg {
        @JsonProperty("data_list")
        List<ObjectDataDocument> dataList;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        List<ObjectDataDocument> dataList;
    }
}
