package com.facishare.crm.sfa.predefine.action.listener;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.proxy.SalesOrderBizProxy;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.SalesOrderInterceptorModel;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.salesorderparam.SalesOrderAddFlowCompletedAfterModel;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.salesorderparam.SalesOrderEditFlowCompletedAfterModel;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.salesorderparam.SalesOrderInvalidFlowCompletedAfterModel;
import com.facishare.crm.sfa.utilities.util.SoCommonUtils;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.StandardFlowCompletedAction;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created by renlb on 2019/3/14.
 */
@Component
public class SalesOrderFlowCompletedActionListener implements ActionListener<StandardFlowCompletedAction.Arg, StandardFlowCompletedAction.Result> {

    @Autowired
    ServiceFacade serviceFacade;

    private static final SalesOrderBizProxy salesOrderBizProxy = SpringUtil.getContext().getBean(SalesOrderBizProxy.class);

    @Override
    public void before(ActionContext actionContext, StandardFlowCompletedAction.Arg arg) {

    }

    @Override
    public void after(ActionContext actionContext, StandardFlowCompletedAction.Arg arg, StandardFlowCompletedAction.Result result) {
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIdsIncludeDeletedIgnoreFormula(
                actionContext.getUser(),
                Lists.newArrayList(arg.getDataId()),
                Utils.SALES_ORDER_API_NAME);
        IObjectData objectData = null;
        if(CollectionUtils.notEmpty(objectDataList)) {
            objectData = objectDataList.get(0);
        }
        if(objectData != null) {
            ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
            if(ApprovalFlowTriggerType.CREATE.equals(arg.approvalFlowTriggerType())) {
                SalesOrderAddFlowCompletedAfterModel.Arg serviceArg = new SalesOrderAddFlowCompletedAfterModel.Arg();
                serviceArg.setDataId(objectDataExt.getId());
                serviceArg.setBeforeLifeStatus(ObjectLifeStatus.UNDER_REVIEW.getCode());
                serviceArg.setAfterLifeStatus(objectDataExt.getLifeStatus().getCode());
                ServiceContext context = ContextManager.buildServiceContext("SalesOrderInterceptor",
                        "AddFlowCompletedAfter");
//                salesOrderInterceptorService.addFlowCompletedAfter(context, serviceArg);
                SalesOrderInterceptorModel.AddFlowCompletedAfterResult interceptorResult = salesOrderBizProxy.addFlowCompletedAfter(serviceArg, SoCommonUtils.getCrmHeader(context.getTenantId(), context.getUser()));
                if(!interceptorResult.isSuccess()){
                    throw new ValidateException(interceptorResult.getMessage());
                }
            } else if(ApprovalFlowTriggerType.UPDATE.equals(arg.approvalFlowTriggerType())) {
                SalesOrderEditFlowCompletedAfterModel.Arg serviceArg = new SalesOrderEditFlowCompletedAfterModel.Arg();
                serviceArg.setDataId(objectDataExt.getId());
                serviceArg.setBeforeLifeStatus(ObjectLifeStatus.IN_CHANGE.getCode());
                serviceArg.setAfterLifeStatus(objectDataExt.getLifeStatus().getCode());
                ServiceContext context = ContextManager.buildServiceContext("SalesOrderInterceptor",
                        "EditFlowCompletedAfter");
//                salesOrderInterceptorService.editFlowCompletedAfter(context, serviceArg);
                SalesOrderInterceptorModel.EditFlowCompletedAfterResult editFlowCompletedAfterResult = salesOrderBizProxy.editFlowCompletedAfter(serviceArg, SoCommonUtils.getCrmHeader(context.getTenantId(), context.getUser()));
                if(!editFlowCompletedAfterResult.isSuccess()){
                    throw new ValidateException(editFlowCompletedAfterResult.getMessage());
                }
            } else if(ApprovalFlowTriggerType.INVALID.equals(arg.approvalFlowTriggerType())) {
                SalesOrderInvalidFlowCompletedAfterModel.Arg serviceArg = new SalesOrderInvalidFlowCompletedAfterModel.Arg();
                serviceArg.setDataId(objectDataExt.getId());
                serviceArg.setBeforeLifeStatus(ObjectLifeStatus.IN_CHANGE.getCode());
                serviceArg.setAfterLifeStatus(objectDataExt.getLifeStatus().getCode());
                ServiceContext context = ContextManager.buildServiceContext("SalesOrderInterceptor",
                        "InvalidFlowCompletedAfter");
//                salesOrderInterceptorService.invalidFlowCompletedAfter(context, serviceArg);
                SalesOrderInterceptorModel.InvalidFlowCompletedAfterResult  invalidFlowCompletedAfterResult = salesOrderBizProxy.invalidFlowCompletedAfter(serviceArg, SoCommonUtils.getCrmHeader(context.getTenantId(), context.getUser()));
                if(!invalidFlowCompletedAfterResult.isSuccess()){
                    throw new ValidateException(invalidFlowCompletedAfterResult.getMessage());
                }
            }
        }
    }
}
