package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.utilities.util.LeadsUtils;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportTemplateAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;

import java.util.List;

public class LeadsInsertImportTemplateAction extends StandardInsertImportTemplateAction {
    private List<String> removedFields = LeadsUtils.getImportRemovedFields();

    @Override
    protected Result doAct(Arg arg) {
        //根据权限获取人员可以查看的字段列表
        return super.doAct(arg);
    }

    @Override
    protected void customHeader(List<IFieldDescribe> headerFieldList) {
        headerFieldList.removeIf(f -> removedFields.contains(f.getApiName()));
        LeadsUtils.setImportFields(arg.getObjectCode(), headerFieldList);

    }
}
