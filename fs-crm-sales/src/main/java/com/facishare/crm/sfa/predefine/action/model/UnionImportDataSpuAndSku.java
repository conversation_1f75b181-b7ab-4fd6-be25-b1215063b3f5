package com.facishare.crm.sfa.predefine.action.model;

import com.facishare.paas.appframework.core.predef.action.BaseImportAction;
import com.facishare.paas.appframework.core.predef.action.BaseImportDataAction;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/6/26 11:06
 * @instruction
 */
@Data
public class UnionImportDataSpuAndSku {

    private IObjectData spuData;
    private List<BaseImportDataAction.ImportData> skuDatas;
    private List<BaseImportAction.ImportError> imporError;


}
