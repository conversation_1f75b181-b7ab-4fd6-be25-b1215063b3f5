package com.facishare.crm.sfa.predefine.importobject;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.metadata.importobject.DefaultObjectImportProvider;
import com.facishare.paas.appframework.metadata.importobject.ImportObject;
import com.facishare.paas.appframework.metadata.importobject.ImportType;
import com.facishare.paas.appframework.metadata.importobject.MatchingType;
import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2019-05-14 18:28
 */
@Component
public class SaleEventObjectImportProvider extends DefaultObjectImportProvider {
    @Override
    public String getObjectCode() {
        return Utils.SALE_EVENT_API_NAME;
    }

    @Override
    public Optional<ImportObject> getImportObject(IObjectDescribe objectDescribe, IUniqueRule uniqueRule) {
        Optional<ImportObject> result= super.getImportObject(objectDescribe, uniqueRule);
        result.ifPresent(importObject -> importObject.setNotSupportSaleEvent(true));
        return result;
    }

    @Override
    protected ImportType getImportType(IObjectDescribe objectDescribe, IUniqueRule uniqueRule) {
        return ImportType.UNSUPPORT_UPDATE_IMPORT;
    }

    @Override
    protected Map<String, List<MatchingType>> getMatchingTypeMap(IObjectDescribe objectDescribe, IUniqueRule uniqueRule) {
        Map<String, List<MatchingType>> result = Maps.newHashMap();
        result.put(INSERT_IMPORT, Lists.newArrayList(MatchingType.NAME));
        return result;
    }
}
