package com.facishare.crm.sfa.predefine.service;

import com.facishare.crm.sfa.predefine.exception.SFABusinessException;
import com.facishare.crm.sfa.predefine.exception.SFAErrorCode;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.TeamMember;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import groovy.util.logging.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TeamMemberService {
    @Autowired
    private ServiceFacade serviceFacade;

    @Autowired
    private ObjectDataServiceImpl objectDataService;

    public void changeOwner(User user, String owner, List<IObjectData> objectDataList){
        if(CollectionUtils.empty(objectDataList)){
            return;
        }
        objectDataList.forEach(objectData -> removeObjectTeamMember(Lists.newArrayList(owner), objectData));
        removeObjectOwner(objectDataList);
        objectDataList.forEach(objectData -> {
            addObjectTeamMember(Lists.newArrayList(owner), TeamMember.Role.OWNER.getValue(),
                    TeamMember.Permission.READANDWRITE.getValue(), objectData);
        });
        try {
            objectDataService.batchUpdateRelevantTeam(objectDataList, AccountUtil.getDefaultActionContext(user));
        }catch (Exception e){
            throw new SFABusinessException(SFAErrorCode.ACCOUNT_COMMON_ERROR);
        }
    }

    public void changeOwner(User user, String owner, List<IObjectData> objectDataList,
                            String outTenantId, String outOwner) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        List<String> toRemoveMemberIds = Lists.newArrayList(owner);
        if (!Strings.isNullOrEmpty(outOwner)) {
            toRemoveMemberIds.add(outOwner);
        }
        objectDataList.forEach(objectData -> removeObjectTeamMember(toRemoveMemberIds, objectData));
        removeObjectOwner(objectDataList);
        objectDataList.forEach(objectData -> {
            addObjectTeamMember(Lists.newArrayList(owner), TeamMember.Role.OWNER.getValue(),
                    TeamMember.Permission.READANDWRITE.getValue(), objectData);
            if (!Strings.isNullOrEmpty(outOwner)) {
                addOutOwner(objectData, outOwner, outTenantId);
            }
        });
        try {
            objectDataService.batchUpdateRelevantTeam(objectDataList, AccountUtil.getDefaultActionContext(user));
        } catch (Exception e) {
            throw new SFABusinessException(SFAErrorCode.ACCOUNT_COMMON_ERROR);
        }
    }

    private void addOutOwner(IObjectData objectData, String outOwner, String outTenantId) {
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        objectDataExt.addTeamMembers(Lists.newArrayList(
                new TeamMember(outOwner, TeamMember.Role.OWNER, TeamMember.Permission.READANDWRITE, outTenantId)));
    }

    private void addObjectTeamMember(List<String> employeeIDs, String teamMemberRole, String permissionType, IObjectData objectData) {
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        for (String teamMemberId : employeeIDs) {
            objectDataExt.addTeamMembers(Lists.newArrayList(
                    new TeamMember(teamMemberId, TeamMember.Role.of(teamMemberRole), TeamMember.Permission.of(permissionType))));
        }
    }

    private void removeObjectTeamMember(List<String> employeeIDs, IObjectData objectData) {
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        List<TeamMember> teamMembers = objectDataExt.getTeamMembers();
        for (String teamMemberId : employeeIDs) {
            List<TeamMember> deleteMember = teamMembers.stream()
                    .filter(f -> f.getEmployee().equals(teamMemberId)).collect(Collectors.toList());
            teamMembers.removeAll(deleteMember);
        }
        objectDataExt.setTeamMembers(teamMembers);
    }

    public void removeObjectAllTeamMember(User user, List<IObjectData> objectDataList){
        if(CollectionUtils.empty(objectDataList)){
            return;
        }
        objectDataList.forEach(objectData -> {
            ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
            List<TeamMember> teamMembers = Lists.newArrayList();
            objectDataExt.setTeamMembers(teamMembers);
        });
        serviceFacade.batchUpdateRelevantTeam(user, objectDataList, false);
    }

    public void removeObjectAllTeamMemberExceptOwner(User user, List<IObjectData> objectDataList){
        if(CollectionUtils.empty(objectDataList)){
            return;
        }
        objectDataList.forEach(objectData -> {
            ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
            List<TeamMember> teamMembers = objectDataExt.getTeamMembers();
            List<TeamMember> deleteMember = teamMembers.stream()
                    .filter(f -> !TeamMember.Role.OWNER.equals(f.getRole())).collect(Collectors.toList());
            teamMembers.removeAll(deleteMember);
            objectDataExt.setTeamMembers(teamMembers);
        });
        serviceFacade.batchUpdateRelevantTeam(user, objectDataList, false);
    }

    public void removeObjectOwner(User user, List<IObjectData> objectDataList){
        if(CollectionUtils.empty(objectDataList)){
            return;
        }
        removeObjectOwner(objectDataList);
        serviceFacade.batchUpdateRelevantTeam(user, objectDataList, false);
    }

    private void removeObjectOwner(List<IObjectData> objectDataList) {
        objectDataList.forEach(objectData -> {
            ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
            List<TeamMember> teamMembers = objectDataExt.getTeamMembers();
            List<TeamMember> deleteMember = teamMembers.stream()
                    .filter(f -> TeamMember.Role.OWNER.equals(f.getRole())).collect(Collectors.toList());
            teamMembers.removeAll(deleteMember);
            objectDataExt.setTeamMembers(teamMembers);
        });
    }
}
