package com.facishare.crm.sfa.predefine.service.real.salesorder.factory;

import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.real.salesorder.price.*;
import com.facishare.crm.sfa.utilities.util.PromotionUtil;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/7/13 2:08 下午
 * @illustration
 */
public class SalesOrderProductPrice extends AbsSalesOrder {

    public BizConfigThreadLocalCacheService configThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);

    @Override
    public void getPrice(String tenantId, List<ObjectDataDocument> data) {
        // 产品组合
        if(configThreadLocalCacheService.isCPQEnabled(tenantId)){
             CpqNewPrice.builder()
                     .tenantId(tenantId)
                     .data(data)
                     .build()
                     .getNewPrice();
             return;
        }
        // 多单位
        if(configThreadLocalCacheService.isMultiUnitEnabled(tenantId)){
            MultiUnitNewPrice.builder()
                    .tenantId(tenantId)
                    .data(data)
                    .build()
                    .getNewPrice();
            return;
        }
        // 价目表
        if(configThreadLocalCacheService.isPriceBookEnabled(tenantId)){
            PriceBookProductNewPrice.builder()
                    .tenantId(tenantId)
                    .data(data)
                    .build()
                    .getNewPrice();
            return;
        }
        // 促销
//        if(PromotionUtil.isOpenPromotion(new User(tenantId, "-10000"))){
//            ProductNewPrice.builder()
//                    .tenantId(tenantId)
//                    .data(data)
//                    .build()
//                    .getNewPrice();
//            return;
//        }

        // 正常
        ProductNewPrice.builder()
                .tenantId(tenantId)
                .data(data)
                .build()
                .getNewPrice();
    }

}
