package com.facishare.crm.sfa.utilities.util;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.service.ICommonSqlService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.service.impl.CommonSqlServiceImpl;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

@Slf4j
public class CommonSqlUtil {
    private static final ICommonSqlService commonSqlService = SpringUtil.getContext().getBean(CommonSqlServiceImpl.class);

    public static void insertDataBySql(User user, String tableName, List<Map<String, Object>> insertMap) {
        if (CollectionUtils.empty(insertMap)) {
            return;
        }
        IActionContext actionContext = buildContext(user);
        try {
            commonSqlService.insert(tableName, insertMap, actionContext);
        } catch (MetadataServiceException e) {
            log.error("{} insert error", tableName, e);
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_ACCOUNT_INSERTDATAERROR));
        }
    }

    public static void deleteDataBySql(User user, String tableName, List<Map<String, Object>> columnAndValueMapList, List<String> primaryKeyList) {
        if (CollectionUtils.empty(columnAndValueMapList)) {
            return;
        }
        IActionContext actionContext = buildContext(user);
        try {
            commonSqlService.batchDelete(tableName, columnAndValueMapList, primaryKeyList, actionContext);
        } catch (MetadataServiceException e) {
            log.error("{} delete error", tableName, e);
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_ACCOUNT_DELETEDATAERROR));
        }
    }

    public static IActionContext buildContext(User user) {
        return ActionContextExt.of(user, RequestContextManager.getContext()).getContext();
    }
}
