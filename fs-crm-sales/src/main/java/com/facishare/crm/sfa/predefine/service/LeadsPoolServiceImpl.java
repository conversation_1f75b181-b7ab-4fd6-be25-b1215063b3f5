package com.facishare.crm.sfa.predefine.service;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.model.Enum.LeadsBizStatusEnum;
import com.facishare.crm.sfa.model.Enum.LeadsPoolMemberTypeEnum;
import com.facishare.crm.sfa.model.Enum.LeadsStatusEnum;
import com.facishare.crm.sfa.model.ObjectLimitRuleModel;
import com.facishare.crm.sfa.model.SFAObjectPoolCommon;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.exception.SFABusinessException;
import com.facishare.crm.sfa.predefine.exception.SFAErrorCode;
import com.facishare.crm.sfa.predefine.service.model.GetLeadsPoolAdminResult;
import com.facishare.crm.sfa.predefine.service.model.GetLeadsPoolListResult;
import com.facishare.crm.sfa.predefine.service.model.GetPoolMembers;
import com.facishare.crm.sfa.predefine.service.model.ObjectPoolModels;
import com.facishare.crm.sfa.predefine.service.model.ObjectPoolPermission;
import com.facishare.crm.sfa.predefine.service.model.SyncLeadsPoolOption;
import com.facishare.crm.sfa.utilities.constant.LeadsConstants;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.crm.sfa.utilities.util.JsonUtil;
import com.facishare.crm.sfa.utilities.util.LeadsUtils;
import com.facishare.crm.sfa.utilities.util.ObjectLimitUtil;
import com.facishare.crm.sfa.utilities.util.ObjectPoolUtil;
import com.facishare.crmcommon.util.CommonSqlUtils;
import com.facishare.crmcommon.util.SearchUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.util.AppIdMapping;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.prm.util.PrmConstant;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOption;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @IgnoreI18nFile
 */
@Slf4j
@Service
@ServiceModule("leads_pool")
@Component
public class LeadsPoolServiceImpl implements IObjectPoolService {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    @Qualifier("objectDataPgService")
    private IObjectDataService objectDataService;
    @Autowired
    private OrgService orgService;
    @Autowired
    private TeamMemberService teamMemberService;
    @Autowired
    private SpecialTableMapper specialTableMapper;

    @Autowired
    private ObjectPoolServiceManager objectPoolServiceManager;

    @ServiceMethod("service_test")
    public void Test(ServiceContext context) {
        int a = 0;
    }

    @ServiceMethod("getLeadsPoolListByEmployeeId")
    public GetLeadsPoolListResult.Result getLeadsPoolListByEmployeeId(ServiceContext context, GetLeadsPoolListResult.Arg arg) {
        String employeeId = String.valueOf(arg.getEmployeeId());
        if (AppIdMapping.appIdMapping.get(PrmConstant.PRM_APP_ID).equals(context.getAppId())) {
            employeeId = context.getUser().getOutUserId();
        }

        IObjectPoolService leadsService = objectPoolServiceManager.getObjectPoolService(Utils.LEADS_API_NAME);
        List<IObjectData> leadsPool = Lists.newArrayList();
        if (leadsService != null) {
            leadsPool = leadsService.getAllObjectPoolList(context.getTenantId(), employeeId, context.getUser().getOutTenantId());
        }
        List<GetLeadsPoolListResult.LeadsPoolObj> LeadsPoolList = Lists.newArrayList();
        if (CollectionUtils.notEmpty(leadsPool)) {
            leadsPool.forEach(m -> {
                LeadsPoolList.add(GetLeadsPoolListResult.LeadsPoolObj.builder()
                        .leadsPoolID(m.getId())
                        .name(m.getName())
                        .overTimeHours(m.get("overtime_hours", Integer.class))
                        .leadsCount(m.get("leads_count", Integer.class))
                        .assignerId(m.get("assigner_id", Integer.class))
                        .limitCount(m.get("limit_count", Integer.class))
                        .isVisibleToMember(m.get("is_visible_to_member", Boolean.class))
                        .isChooseToNotify(m.get("is_choose_to_notify", Boolean.class))
                        .creatorId(Strings.isNullOrEmpty(m.getCreatedBy()) ? 0 : Integer.parseInt(m.getCreatedBy()))
                        .creatorTime(m.getCreateTime())
                        .updaterId(Strings.isNullOrEmpty(m.getLastModifiedBy()) ? 0 : Integer.parseInt(m.getLastModifiedBy()))
                        .updateTime(m.getLastModifiedTime())
                        .build());
            });
        }
        return GetLeadsPoolListResult.Result.builder().value(LeadsPoolList).build();
    }

    @ServiceMethod("getNewLeadsPoolListByEmployeeId")
    public GetLeadsPoolListResult.NewResult getNewLeadsPoolListByEmployeeId(ServiceContext context, GetLeadsPoolListResult.Arg arg) {
        String employeeId = String.valueOf(arg.getEmployeeId());
        if (AppIdMapping.appIdMapping.get(PrmConstant.PRM_APP_ID).equals(context.getAppId())) {
            employeeId = context.getUser().getOutUserId();
        }
        IObjectPoolService leadsService = objectPoolServiceManager.getObjectPoolService(Utils.LEADS_API_NAME);
        List<IObjectData> leadsPool = Lists.newArrayList();
        if (leadsService != null) {
            leadsPool = leadsService.getAllObjectPoolList(context.getTenantId(), employeeId, context.getUser().getOutTenantId());
        }
        return GetLeadsPoolListResult.NewResult.builder().objectDataList(ObjectDataDocument.ofList(leadsPool)).build();
    }

    @ServiceMethod("getLeadsPoolAdmins")
    public GetLeadsPoolAdminResult.Result getLeadsPoolAdmins(ServiceContext context, GetLeadsPoolAdminResult.Arg arg) {
        log.info("LeadsPoolService>getLeadsPoolAdmins()arg=" + JsonUtil.toJsonWithNullValues(arg));
        if (CollectionUtils.empty(arg.getLeadsPoolIdList())) {
            throw new ValidateException(I18N.text(I18NKey.REQUEST_PARAM_IS_NULL));
        }
        Map<String, List<String>> poolAdminMap = getPoolAdminByIds(context.getUser(), arg.getLeadsPoolIdList());
        return GetLeadsPoolAdminResult.Result.builder().value(poolAdminMap).build();
    }

    @Override
    public String getObjectPoolApiName() {
        return Utils.LEADS_POOL_API_NAME;
    }

    @Override
    public String getObjectApiName() {
        return SFAPreDefineObject.Leads.getApiName();
    }

    @Override
    public String getObjectPoolKeyName() {
        return LeadsConstants.Field.LEADS_POOL_ID.getApiName();
    }

    @Override
    public ObjectPoolPermission.ObjectPoolPermissions getPoolPermission(String tenantId, String userId, String objectPoolId, String outTenantId) {
        ObjectPoolPermission.ObjectPoolPermissions result = ObjectPoolPermission.ObjectPoolPermissions.NO_PERMISSION;
        List<Map> queryResult = LeadsUtils.findLeadsPoolPermissionByPoolIds(tenantId, Lists.newArrayList(objectPoolId));
        Boolean isAdmin = false;
        Boolean isMember = false;

        List<String> deptIds = getUserDepartIds(tenantId, userId);
        if (CollectionUtils.notEmpty(queryResult)) {
            List<Map> admins = queryResult.stream().filter(
                    a -> a.getOrDefault("is_admin", false).equals(true)).collect(Collectors.toList());
            isAdmin = admins.stream().anyMatch(a -> userId.equals(a.getOrDefault("data_id", "").toString())
                    && (a.getOrDefault("type", "").toString().equals(ObjectPoolPermission.ObjectPoolMemberType.EMPLOYEE.getValue())
                    || a.getOrDefault("type", "").toString().equals(ObjectPoolPermission.ObjectPoolMemberType.OUTER_EMPLOYEE.getValue())));
            if (!isAdmin && CollectionUtils.notEmpty(deptIds)) {
                isAdmin = admins.stream().anyMatch(x -> deptIds.contains(x.getOrDefault("data_id", "").toString())
                        && Objects.equals(ObjectPoolPermission.ObjectPoolMemberType.CIRCLE.getValue(), x.getOrDefault("type", "").toString()));
            }

            List<Map> members = queryResult.stream().filter(
                    a -> a.getOrDefault("is_admin", false).equals(false)).collect(Collectors.toList());
            isMember = members.stream().anyMatch(a -> userId.equals(a.getOrDefault("data_id", "").toString()) &&
                    (Objects.equals(ObjectPoolPermission.ObjectPoolMemberType.EMPLOYEE.getValue(), a.getOrDefault("type", "").toString())
                            || ObjectPoolPermission.ObjectPoolMemberType.OUTER_ENTERPRISE.getValue().equals(a.getOrDefault("type", "").toString())
                            || ObjectPoolPermission.ObjectPoolMemberType.OUTER_EMPLOYEE.getValue().equals(a.getOrDefault("type", "").toString())));
            if (!isMember && CollectionUtils.notEmpty(deptIds)) {
                isMember = members.stream().anyMatch(x -> deptIds.contains(x.getOrDefault("data_id", "").toString())
                        && Objects.equals(ObjectPoolPermission.ObjectPoolMemberType.CIRCLE.getValue(), x.getOrDefault("type", "").toString()));
            }
            if (!Strings.isNullOrEmpty(outTenantId) && !isMember) {
                isMember = members.stream().anyMatch(x -> Objects.equals(outTenantId, x.getOrDefault("data_id", "").toString())
                        && Objects.equals(ObjectPoolPermission.ObjectPoolMemberType.OUTER_ENTERPRISE.getValue(), x.getOrDefault("type", "").toString()));
            }
        }
        if (isAdmin && isMember) {
            return ObjectPoolPermission.ObjectPoolPermissions.POOL_ALL;
        }
        if (isAdmin) {
            return ObjectPoolPermission.ObjectPoolPermissions.POOL_ADMIN;
        }
        if (isMember) {
            return ObjectPoolPermission.ObjectPoolPermissions.POOL_MEMBER;
        }

        return result;
    }

    private List<String> getUserDepartIds(String tenantId, String userId) {
        //List<QueryDeptInfoByUserId.DeptInfo> deptInfos = serviceFacade.getDeptInfoByUserId(tenantId, userId, userId);
        List<String> departList = serviceFacade.queryAllSuperDeptByUserId(tenantId, userId, userId);
        List<String> deptIds = Lists.newArrayList();
//        if (CollectionUtils.notEmpty(deptInfos)) {
//            deptIds.addAll(deptInfos.stream().map(x -> x.getDeptId()).collect(Collectors.toList()));
//        }
        return departList;
    }

    @Override
    public List<IObjectData> getObjectPoolByIds(String tenantId, List<String> objectIds) {
        return serviceFacade.findObjectDataByIds(tenantId, objectIds, getObjectPoolApiName());
    }

    @Override
    public IObjectData getObjectPoolById(String tenantId, String objectId) {
        if (StringUtils.isEmpty(objectId)) {
            return null;
        }
        List<IObjectData> objectDataList = getObjectPoolByIds(tenantId, Lists.newArrayList(objectId));
        if (CollectionUtils.notEmpty(objectDataList)) {
            return objectDataList.get(0);
        }
        return null;
    }

    @Override
    @Transactional
    public SFAObjectPoolCommon.Result choose(User user, String objectPoolId, List<String> objectIds, String eventId, String partnerId) {
        SFAObjectPoolCommon.Result result = SFAObjectPoolCommon.Result.builder().build();
        if (!CollectionUtils.notEmpty(objectIds)) {
            return result;
        }
        IObjectData objectPoolData = getObjectPoolById(user.getTenantId(), objectPoolId);
        boolean cleanTeamMember = AccountUtil.getBooleanValue(objectPoolData, "is_recycling_team_member", false);
        String ownerDeptId = LeadsUtils.getUserMainDepartId(user.getTenantId(), user.getUserId());
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIds(user.getTenantId(), objectIds, getObjectApiName());
        if (CollectionUtils.notEmpty(objectDataList)) {
            for (IObjectData objectData : objectDataList) {
                List<String> ownerList = objectData.getOwner();
                if (CollectionUtils.notEmpty(ownerList) && ownerList.stream().anyMatch(o -> StringUtils.isNumeric(o) && Integer.valueOf(o) > 0)) {
                    throw new ValidateException("负责人已有值");
                }
                objectData.set("last_followed_time", System.currentTimeMillis());
                objectData.set("owner_change_time", System.currentTimeMillis());
                objectData.set("last_modified_time", System.currentTimeMillis());
                objectData.set("leads_status", LeadsStatusEnum.UN_DEAL.getCode());
                objectData.set("assigner_id", Lists.newArrayList());
                objectData.set("assigned_time", System.currentTimeMillis());
                objectData.set("owner", Lists.newArrayList(user.getUserId()));
                objectData.set("data_own_department", Lists.newArrayList(ownerDeptId));
                objectData.set("biz_status", LeadsBizStatusEnum.UN_PROCESSED.getCode());
                objectData.set("is_overtime", false);
                if (!Strings.isNullOrEmpty(partnerId)) {
                    objectData.set("partner_id", partnerId);
                }
                if (!Strings.isNullOrEmpty(user.getOutUserId())) {
                    objectData.setOutOwner(Lists.newArrayList(user.getOutUserId()));
                    objectData.setOutTenantId(user.getOutTenantId());
                }
            }
        }
        List<String> updateFieldList = Lists.newArrayList("last_followed_time", "owner_change_time", "last_modified_time", "leads_status", "owner", "data_own_department", "biz_status", "is_overtime");
        if (!Strings.isNullOrEmpty(partnerId)) {
            updateFieldList.add("partner_id");
        }
        String outTenant = null;
        String outOwner = null;
        if (!Strings.isNullOrEmpty(user.getOutUserId())) {
            updateFieldList.add("out_owner");
            updateFieldList.add("out_tenant_id");
            outTenant = user.getOutTenantId();
            outOwner = user.getOutUserId();
        }
        saveLeadsData(user, objectDataList, updateFieldList, true, user.getUserId(), eventId,
                outTenant, outOwner);
        if (cleanTeamMember) {
            teamMemberService.removeObjectAllTeamMember(user, objectDataList);
        }
        teamMemberService.changeOwner(user, user.getUserId(), objectDataList, outTenant, outOwner);
        result.setSuccessList(objectIds);
        return result;
    }

    private void saveLeadsData(User user, List<IObjectData> objectDataList, List<String> updateFieldList, Boolean changeOwner, String ownerId) {
        try {
            objectDataService.batchUpdateWithField(objectDataList, updateFieldList, getDefaultActionContext(user));
            if (changeOwner) {
                changeOwner(user, ownerId, objectDataList, null, null);
            }
        } catch (Exception e) {
            log.error("saveLeadsData error {}", user.getTenantId(), e);
            throw new SFABusinessException(SFAErrorCode.LEADS_COMMON_ERROR);
        }
    }

    private void saveLeadsData(User user, List<IObjectData> objectDataList, List<String> updateFieldList, Boolean changeOwner,
                               String ownerId, String eventId, String outTenant, String outOwner) {
        try {
            objectDataService.batchUpdateWithField(objectDataList, updateFieldList, getDefaultActionContext(user, eventId));
            if (changeOwner) {
                changeOwner(user, ownerId, objectDataList, outTenant, outOwner);
            }
        } catch (Exception e) {
            log.error("saveLeadsData error {}", user.getTenantId(), e);
            throw new SFABusinessException(SFAErrorCode.LEADS_COMMON_ERROR);
        }
    }

    private void changeOwner(User user, String ownerId, List<IObjectData> objectDataList,
                             String outTenantId, String outOwner) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        teamMemberService.changeOwner(user, ownerId, objectDataList, outTenantId, outOwner);
        List<String> leadIds = objectDataList.stream().map(l -> l.getId()).distinct()
                .collect(Collectors.toList());
        LeadsUtils.insertLeadsOwnerHistory(user.getTenantId(), user.getUserId(), leadIds, ownerId);
    }

    @Override
    public SFAObjectPoolCommon.Result move(User user, String objectPoolId, List<String> objectIds, String eventId) {
        SFAObjectPoolCommon.Result result = SFAObjectPoolCommon.Result.builder().build();
        log.info("leads_pool move save begin");
        if (!CollectionUtils.notEmpty(objectIds)) {
            return result;
        }
        log.info("leads_pool move save ,objectIds is not null");
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIds(user.getTenantId(), objectIds, getObjectApiName());
        IObjectData leadsPool = getObjectPoolById(user.getTenantId(), objectPoolId);
        String sourcePoolId = objectDataList.stream()
                .filter(l -> !StringUtils.isEmpty(l.get("leads_pool_id", String.class)))
                .map(l -> l.get("leads_pool_id", String.class)).distinct()
                .findFirst().orElse("");
        if (StringUtils.isEmpty(sourcePoolId)) {
            //throw new SFABusinessException(SFAErrorCode.LEADS_MOVE_NO_SOURCE_ERROR);
        }
        if (leadsPool.getId().equals(sourcePoolId)) {
            throw new SFABusinessException(SFAErrorCode.LEADS_MOVE_ERROR);
        }
        log.info("leads_pool move save ,begin construct data 1");
        boolean cleanOwner = AccountUtil.getBooleanValue(leadsPool, "is_clean_owner", false);
        boolean cleanTeamMember = AccountUtil.getBooleanValue(leadsPool, "is_recycling_team_member", false);

        if (CollectionUtils.notEmpty(objectDataList)) {
            for (IObjectData objectData : objectDataList) {
                objectData.set("leads_pool_id", objectPoolId);
                objectData.setLastModifiedBy(user.getUserId());
                objectData.setLastModifiedTime(System.currentTimeMillis());
                if (cleanOwner) {
                    objectData.set("owner_change_time", System.currentTimeMillis());
                    objectData.set("leads_status", LeadsStatusEnum.UN_ALLOCATE.getCode());
                    objectData.set("owner", Lists.newArrayList());
                    objectData.set("data_own_department", Lists.newArrayList());
                    objectData.set("biz_status", LeadsBizStatusEnum.UN_ASSIGNED.getCode());
                    objectData.setOutTenantId(null);
                    objectData.setOutOwner(Lists.newArrayList());
                    objectData.set("partner_id", null);
                    objectData.set("is_overtime", false);
                } else {
                    objectData.set("claimed_time", System.currentTimeMillis());
                }
            }
        }
        List<String> updateFieldList = Lists.newArrayList("leads_pool_id", "last_modified_by", "last_modified_time");
        if(cleanOwner) {
            updateFieldList.add("owner");
            updateFieldList.add("leads_status");
            updateFieldList.add("data_own_department");
            updateFieldList.add("owner_change_time");
            updateFieldList.add("biz_status");
            updateFieldList.add("out_tenant_id");
            updateFieldList.add("out_owner");
            updateFieldList.add("partner_id");
            updateFieldList.add("is_overtime");
        }
        log.info("leads_pool move save ,begin construct data 2");
        saveLeadsData(user, objectDataList, updateFieldList, false, null, eventId,
                null, null);
        log.info("leads_pool move save ,begin construct data 3");
        LeadsUtils.updateLeadsPoolsCount(user, Lists.newArrayList(objectPoolId, sourcePoolId));
        log.info("leads_pool move save ,save data finished");
        if (cleanTeamMember) {
            teamMemberService.removeObjectAllTeamMemberExceptOwner(user, objectDataList);
        }
        if (cleanOwner) {
            teamMemberService.removeObjectOwner(user, objectDataList);
        }
        result.setSuccessList(objectIds);
        return result;
    }

    private void setBackObjectData(User user, IObjectData objectData, String objectPoolId, boolean isPrmOpen) {
        objectData.set("owner", Lists.newArrayList());
        objectData.set("data_own_department", Lists.newArrayList());
        objectData.set("leads_status", LeadsStatusEnum.UN_ALLOCATE.getCode());
        objectData.set("assigner_id", Lists.newArrayList());
        objectData.set("assigned_time", null);
        objectData.setLastModifiedBy(user.getUserId());
        objectData.setLastModifiedTime(System.currentTimeMillis());
        objectData.set("owner_change_time", System.currentTimeMillis());
        objectData.set("is_overtime", false);
        objectData.set("leads_pool_id", objectPoolId);
        objectData.set("biz_status", LeadsBizStatusEnum.UN_ASSIGNED.getCode());
        objectData.set("returned_time", System.currentTimeMillis());
        if (isPrmOpen) {
            objectData.setOutTenantId(null);
            objectData.setOutOwner(Lists.newArrayList());
            objectData.set("partner_id", null);
        }
    }

    @Override
    public SFAObjectPoolCommon.Result back(User user, String objectPoolId, List<String> objectIds, Integer operationType,
                                           String backReason, String backReasonOther, String eventId, boolean isPrmOpen) {
        SFAObjectPoolCommon.Result result = SFAObjectPoolCommon.Result.builder().build();
        if (!CollectionUtils.notEmpty(objectIds)) {
            return result;
        }
        IObjectData objectPoolData = getObjectPoolById(user.getTenantId(), objectPoolId);
        boolean cleaTeamMember = AccountUtil.getBooleanValue(objectPoolData, "is_recycling_team_member", false);
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIds(user.getTenantId(), objectIds, getObjectApiName());
        List<Map<String, String>> claimLogList = Lists.newArrayList();
        if (CollectionUtils.notEmpty(objectDataList)) {
            boolean isOutUser = user.isOutUser();
            for (IObjectData objectData : objectDataList) {
                LeadsUtils.setClaimLogList(objectData, objectPoolId, claimLogList, isOutUser, false);
                setBackObjectData(user, objectData, objectPoolId, isPrmOpen);
                String backReasonAll = ("other".equals(backReason) && !Strings.isNullOrEmpty(backReasonOther)) ?
                        backReason + backReasonOther : backReason;
                objectData.set("back_reason", backReasonAll);
            }
        }
        List<String> updateFieldList = Lists.newArrayList("owner", "data_own_department", "leads_status",
                "assigner_id", "assigned_time", "last_modified_by", "last_modified_time"
                , "owner_change_time", "is_overtime", "leads_pool_id", "back_reason", "biz_status");
        if (isPrmOpen) {
            updateFieldList.add("out_tenant_id");
            updateFieldList.add("out_owner");
            updateFieldList.add("partner_id");
        }
        try {
            objectDataService.batchUpdateWithField(objectDataList, updateFieldList, getDefaultActionContext(user, eventId));
            if (cleaTeamMember) {
                teamMemberService.removeObjectAllTeamMember(user, objectDataList);
            }
            teamMemberService.removeObjectOwner(user, objectDataList);

            LeadsUtils.updateLeadsPoolsCount(user, Lists.newArrayList(objectPoolId));
            String userId = user.isOutUser() ? user.getOutUserId() : user.getUserId();
            LeadsUtils.insertLeadsClaimLog(user.getTenantId(), userId, claimLogList, ObjectAction.RETURN.getActionCode());
            result.setSuccessList(objectIds);
        } catch (Exception e) {
            log.error("back Leads error {}", user.getTenantId(), e);
            throw new SFABusinessException(SFAErrorCode.ACCOUNT_COMMON_ERROR);
        }
        return result;
    }

    @Override
    public SFAObjectPoolCommon.Result takeBack(User user, String objectPoolId, List<String> objectIds, String eventId, boolean isPrmOpen) {
        SFAObjectPoolCommon.Result result = SFAObjectPoolCommon.Result.builder().build();
        if (!CollectionUtils.notEmpty(objectIds)) {
            return result;
        }
        IObjectData objectPoolData = getObjectPoolById(user.getTenantId(), objectPoolId);
        boolean cleaTeamMember = AccountUtil.getBooleanValue(objectPoolData, "is_recycling_team_member", false);
        List<Map<String, String>> claimLogList = Lists.newArrayList();
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIds(user.getTenantId(), objectIds, getObjectApiName());
        if (CollectionUtils.notEmpty(objectDataList)) {
            for (IObjectData objectData : objectDataList) {
                LeadsUtils.setClaimLogList(objectData, objectPoolId, claimLogList, false, true);
                setBackObjectData(user, objectData, objectPoolId, isPrmOpen);
            }
        }
        List<String> updateFieldList = Lists.newArrayList("owner", "data_own_department", "leads_status",
                "assigner_id", "assigned_time", "last_modified_by", "last_modified_time"
                , "owner_change_time", "is_overtime", "leads_pool_id", "biz_status");
        if (isPrmOpen) {
            updateFieldList.add("out_tenant_id");
            updateFieldList.add("out_owner");
            updateFieldList.add("partner_id");
        }
        try {
            objectDataService.batchUpdateWithField(objectDataList, updateFieldList, getDefaultActionContext(user, eventId));
            if (cleaTeamMember) {
                teamMemberService.removeObjectAllTeamMember(user, objectDataList);
            }
            teamMemberService.removeObjectOwner(user, objectDataList);
            LeadsUtils.updateLeadsPoolsCount(user, Lists.newArrayList(objectPoolId));
            LeadsUtils.insertLeadsClaimLog(user.getTenantId(), user.getUserId(), claimLogList, ObjectAction.TAKE_BACK.getActionCode());
            result.setSuccessList(objectIds);
        } catch (Exception e) {
            log.error("takeBack Leads error {}", user.getTenantId(), e);
            throw new SFABusinessException(SFAErrorCode.CLIENT_UPGRADE_PROMPT);
        }
        return result;
    }

    @Override
    public Map<String, Integer> getPoolObjectUnDeletedCount(User user, List<String> poolIds) {
        return Maps.newHashMap();
    }

    @Override
    public SFAObjectPoolCommon.Result allocate(User user, String objectPoolId, List<String> objectIds, String ownerId, String eventId,
                                               Long outTenantId, Long outOwnerId, String partnerId) {
        SFAObjectPoolCommon.Result result = SFAObjectPoolCommon.Result.builder().build();
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIds(user.getTenantId(), objectIds, getObjectApiName());
        String ownerDeptId = LeadsUtils.getUserMainDepartId(user.getTenantId(), ownerId);
        boolean hasOutTenantId = false;
        boolean hasOutOwnerId = false;
        boolean hasPartnerId = false;
        String outTenant = null;
        String outOwner = null;
        IObjectData objectPoolData = getObjectPoolById(user.getTenantId(), objectPoolId);
        boolean cleaTeamMember = AccountUtil.getBooleanValue(objectPoolData, "is_recycling_team_member", false);
        if (outTenantId != null && outTenantId > 0) {
            hasOutTenantId = true;
            outTenant = String.valueOf(outTenantId);
        }
        if (outOwnerId != null && outOwnerId > 0) {
            hasOutOwnerId = true;
            outOwner = String.valueOf(outOwnerId);
        }
        if (!Strings.isNullOrEmpty(partnerId)) {
            hasPartnerId = true;
        }
        if (CollectionUtils.notEmpty(objectDataList)) {
            for (IObjectData objectData : objectDataList) {
                objectData.set("owner", Lists.newArrayList(ownerId));
                objectData.set("data_own_department", Lists.newArrayList(ownerDeptId));
                objectData.set("leads_status", LeadsStatusEnum.UN_DEAL.getCode());
                objectData.set("assigner_id", Lists.newArrayList(user.getUserId()));
                objectData.set("assigned_time", System.currentTimeMillis());
                if (objectData.get("resale_count") == null) {
                    objectData.set("resale_count", 1);
                } else {
                    objectData.set("resale_count", objectData.get("resale_count", Integer.class) + 1);
                }
                objectData.setLastModifiedBy(user.getUserId());
                objectData.setLastModifiedTime(System.currentTimeMillis());
                objectData.set("owner_change_time", System.currentTimeMillis());
                objectData.set("is_overtime", false);
                objectData.set("biz_status", LeadsBizStatusEnum.UN_PROCESSED.getCode());
                if (hasOutTenantId) {
                    objectData.setOutTenantId(outTenant);
                }
                if (hasOutOwnerId) {
                    objectData.setOutOwner(Lists.newArrayList(outOwner));
                }
                if (hasPartnerId) {
                    objectData.set("partner_id", partnerId);
                }
            }
        }
        List<String> updateFieldList = Lists.newArrayList("owner", "data_own_department", "leads_status",
                "assigner_id", "assigned_time", "resale_count", "last_modified_by", "last_modified_time", "owner_change_time", "is_overtime", "biz_status");
        if (hasOutTenantId) {
            updateFieldList.add("out_tenant_id");
        }
        if (hasOutOwnerId) {
            updateFieldList.add("out_owner");
        }
        if (hasPartnerId) {
            updateFieldList.add("partner_id");
        }
        saveLeadsData(user, objectDataList, updateFieldList, true, ownerId, eventId,
                outTenant, outOwner);
        if (cleaTeamMember) {
            teamMemberService.removeObjectAllTeamMember(user, objectDataList);
        }
        teamMemberService.changeOwner(user, ownerId, objectDataList, outTenant, outOwner);
        return result;
    }

    @Override
    public SFAObjectPoolCommon.Result remove(User user, List<IObjectData> objectDataList, String owner, Boolean isKeepOwner, String eventId) {
        return null;
    }

    public List<String> getLeadsIdListByAdminId(String tenantId, String employeeId) {
        List<String> result = Lists.newArrayList();
        List<Map> permissions = LeadsUtils.getAllPoolPermissions(tenantId);
        if (!CollectionUtils.notEmpty(permissions)) {
            return result;
        }
        result = permissions.stream().filter(p -> (p.get("type").toString().
                equals(ObjectPoolPermission.ObjectPoolMemberType.EMPLOYEE.getValue()) ||
                p.get("type").toString().
                        equals(ObjectPoolPermission.ObjectPoolMemberType.OUTER_EMPLOYEE.getValue())) &&
                p.getOrDefault("is_admin", false).equals(true)
                && p.getOrDefault("data_id", "").toString().equals(employeeId))
                .map(p -> p.get("pool_id").toString())
                .collect(Collectors.toList());
        List<String> circleIds = getUserDepartIds(tenantId, employeeId);
        if (!CollectionUtils.notEmpty(circleIds)) {
            return result;
        }

        List<String> circlePoolIds = permissions.stream().filter(p -> p.get("type").toString().
                equals(ObjectPoolPermission.ObjectPoolMemberType.CIRCLE.getValue()) &&
                p.getOrDefault("is_admin", false).equals(true)
                && circleIds.contains(p.getOrDefault("data_id", "").toString()))
                .map(p -> p.get("pool_id").toString())
                .collect(Collectors.toList());
        result.addAll(circlePoolIds);
        result = result.stream().distinct().collect(Collectors.toList());
        return result;
    }

    @Override
    public Map<String, List<String>> getPoolMembersByIds(User user, List<String> poolIds) {
        Map<String, List<String>> result = Maps.newHashMap();
        if (poolIds == null || poolIds.isEmpty()) {
            return result;
        }
        String tenantId = user.getTenantId();
        poolIds = poolIds.stream().filter(p -> StringUtils.isNotEmpty(p)).distinct().collect(Collectors.toList());
        Iterator iterator = poolIds.iterator();
        List<Map> queryResult = LeadsUtils.findLeadsPoolPermissionByPoolIds(tenantId, poolIds);
        if (!CollectionUtils.empty(queryResult)) {
            while (iterator.hasNext()) {
                String leadsPoolId = (String) iterator.next();
                if (StringUtils.isEmpty(leadsPoolId)) continue;
                Set<String> employeeIds = queryResult.stream().
                        filter(r -> leadsPoolId.equals(r.getOrDefault("pool_id", "")) &&
                                (ObjectPoolPermission.ObjectPoolMemberType.EMPLOYEE.getValue().equals(r.getOrDefault("type", "").toString())
                                        || ObjectPoolPermission.ObjectPoolMemberType.OUTER_ENTERPRISE.getValue().equals(r.getOrDefault("type", "").toString())
                                        || ObjectPoolPermission.ObjectPoolMemberType.OUTER_EMPLOYEE.getValue().equals(r.getOrDefault("type", "").toString())) &&
                                r.getOrDefault("is_admin", false).equals(false)).
                        map(r -> r.get("data_id").toString()).
                        collect(Collectors.toSet());
                List<String> circleIds = queryResult.stream().
                        filter(r -> leadsPoolId.equals(r.getOrDefault("pool_id", ""))
                                && (Integer) r.get("type") == LeadsPoolMemberTypeEnum.CIRCLE.getCode()
                                && r.getOrDefault("is_admin", false).equals(false)).
                        map(r -> r.get("data_id").toString()).
                        collect(Collectors.toList());
                List<String> circleMembers = orgService.getMembersByDeptIds(user, circleIds, 0);
                employeeIds.addAll(circleMembers);
                result.put(leadsPoolId, Lists.newArrayList(employeeIds));
            }
        }
        return result;
    }

    @Override
    public List<String> getPoolAdminById(User user, String poolId) {
        String tenantId = user.getTenantId();
        List<Map> queryResult = LeadsUtils.findLeadsPoolPermissionByPoolIds(tenantId, Lists.newArrayList(poolId));
        Set<String> adminIds = Sets.newHashSet();
        if (!CollectionUtils.empty(queryResult)) {
            adminIds = queryResult.stream().filter(x -> x.getOrDefault("is_admin", false).equals(true)
                    && x.getOrDefault("type", 0).equals(LeadsPoolMemberTypeEnum.EMPLOYEE.getCode()))
                    .map(x -> String.valueOf(x.get("data_id")))
                    .collect(Collectors.toSet());
            List<String> circleIds = queryResult.stream().filter(x -> x.getOrDefault("is_admin", false).equals(true)
                    && x.getOrDefault("type", 0).equals(LeadsPoolMemberTypeEnum.CIRCLE.getCode()))
                    .map(x -> String.valueOf(x.get("data_id")))
                    .collect(Collectors.toList());
            List<String> memberIds = orgService.getMembersByDeptIds(user, circleIds);
            adminIds.addAll(memberIds);
        }
        return new ArrayList<>(adminIds);
    }

    @Override
    public Map<String, List<String>> getPoolAdminByIds(User user, List<String> poolIds) {
        Map<String, List<String>> poolAdminMap = Maps.newHashMap();
        if (CollectionUtils.empty(poolIds)) {
            return poolAdminMap;
        }
        String tenantId = user.getTenantId();
        List<Map> queryResult = LeadsUtils.findLeadsPoolPermissionByPoolIds(tenantId, poolIds);

        if (!CollectionUtils.empty(queryResult)) {
            poolIds.forEach(m -> {
                Set<String> adminIds = Sets.newHashSet();
                adminIds = queryResult.stream().filter(x ->
                        x.getOrDefault("pool_id", "").equals(m) &&
                                x.getOrDefault("is_admin", false).equals(true) &&
                                x.getOrDefault("type", 0).equals(LeadsPoolMemberTypeEnum.EMPLOYEE.getCode()))
                        .map(x -> String.valueOf(x.get("data_id")))
                        .collect(Collectors.toSet());
                List<String> circleIds = queryResult.stream().filter(x ->
                        x.getOrDefault("pool_id", "").equals(m) &&
                                x.getOrDefault("is_admin", false).equals(true) &&
                                x.getOrDefault("type", 0).equals(LeadsPoolMemberTypeEnum.CIRCLE.getCode()))
                        .map(x -> String.valueOf(x.get("data_id")))
                        .collect(Collectors.toList());
                List<String> memberIds = orgService.getMembersByDeptIds(user, circleIds);
                adminIds.addAll(memberIds);
                poolAdminMap.put(m, Lists.newArrayList(adminIds));
            });
        }
        return poolAdminMap;
    }

    @Override
    public Map<String, List<String>> getInAndOutPoolAdminById(User user, String poolId) {
        Map<String, List<String>> inAndOutPoolAdminList = Maps.newHashMap();
        String tenantId = user.getTenantId();
        List<Map> queryResult = LeadsUtils.findLeadsPoolPermissionByPoolIds(tenantId, Lists.newArrayList(poolId));
        Set<String> inAdminIds = Sets.newHashSet();
        Set<String> outAdminIds = Sets.newHashSet();
        if (!CollectionUtils.empty(queryResult)) {
            inAdminIds = queryResult.stream().filter(x -> x.getOrDefault("is_admin", false).equals(true)
                    && x.getOrDefault("type", 0).equals(LeadsPoolMemberTypeEnum.EMPLOYEE.getCode()))
                    .map(x -> String.valueOf(x.get("data_id")))
                    .collect(Collectors.toSet());
            List<String> circleIds = queryResult.stream().filter(x -> x.getOrDefault("is_admin", false).equals(true)
                    && x.getOrDefault("type", 0).equals(LeadsPoolMemberTypeEnum.CIRCLE.getCode()))
                    .map(x -> String.valueOf(x.get("data_id")))
                    .collect(Collectors.toList());
            List<String> memberIds = orgService.getMembersByDeptIds(user, circleIds);
            inAdminIds.addAll(memberIds);
            if (!CollectionUtils.empty(inAdminIds)) {
                inAndOutPoolAdminList.put("in", new ArrayList<>(inAdminIds));
            }
            outAdminIds = queryResult.stream().filter(x -> x.getOrDefault("is_admin", false).equals(true)
                    && ObjectPoolPermission.ObjectPoolMemberType.OUTER_EMPLOYEE.getValue().equals(x.getOrDefault("type", "").toString()))
                    .map(x -> String.valueOf(x.get("data_id")))
                    .collect(Collectors.toSet());
            if (!CollectionUtils.empty(outAdminIds)) {
                inAndOutPoolAdminList.put("out", new ArrayList<>(outAdminIds));
            }
        }
        return inAndOutPoolAdminList;
    }

    @Override
    public List<IObjectData> getAllObjectPoolList(String tenantId, String userId, String outTenantId) {
        List<IObjectData> result = Lists.newArrayList();
        try {
            List<String> ids = getUserDepartIds(tenantId, userId); //解决 690 报错
            String queryString = "SELECT DISTINCT pool_id FROM biz_pool_permission WHERE tenant_id='"
                    + tenantId + "' and object_api_name='" + Utils.LEADS_POOL_API_NAME + "' and is_deleted=0 ";

            queryString += " AND ((data_id='" + userId + "' AND type in ('" +
                    ObjectPoolPermission.ObjectPoolMemberType.EMPLOYEE.getValue() + "','" +
                    ObjectPoolPermission.ObjectPoolMemberType.OUTER_EMPLOYEE.getValue() + "')) ";
            if (!Strings.isNullOrEmpty(outTenantId)) {
                queryString += " OR (type='" + ObjectPoolPermission.ObjectPoolMemberType.OUTER_ENTERPRISE.getValue() + "' AND data_id='"
                        + outTenantId + "')) ";
            } else {
                if (CollectionUtils.empty(ids)) {
                    queryString += ") ";
                } else {
                    String idString = LeadsUtils.getListQuerySql(ids);
                    queryString += " OR (type='" + ObjectPoolPermission.ObjectPoolMemberType.CIRCLE.getValue() + "' AND data_id in ("
                            + idString + "))) ";
                }
            }
            List<Map> queryResult = objectDataService.findBySql(tenantId, queryString);
            if (CollectionUtils.empty(queryResult)) {
                return result;
            }
            ids = queryResult.stream().map(x -> x.get("pool_id").toString()).collect(Collectors.toList());
            result = getObjectPoolByIds(tenantId, ids);
        } catch (Exception e) {
            log.error("leads pool service error", e);
            throw new SFABusinessException(SFAErrorCode.LEADS_COMMON_ERROR);
        }
        return result;
    }

    @Override
    public List<IObjectData> getAllVisiblePoolList(String tenantId, String userId, String outTenantId) {
        Set<IObjectData> result = Sets.newHashSet();
        List<IObjectData> adminPoolList = getPoolListWithAdmin(tenantId, userId);
        if (CollectionUtils.notEmpty(adminPoolList)) {
            result.addAll(adminPoolList);
        }
        List<IObjectData> allInvolvedPoolList = getAllObjectPoolList(tenantId, userId, outTenantId);
        if (CollectionUtils.notEmpty(allInvolvedPoolList)) {
            List<IObjectData> visiblePoolList = allInvolvedPoolList.stream()
                    .filter(objectData -> Boolean.TRUE.equals(objectData.get("is_visible_to_member", Boolean.class)))
                    .collect(Collectors.toList());
            if (CollectionUtils.notEmpty(visiblePoolList)) {
                result.addAll(visiblePoolList);
            }
        }
        return Lists.newArrayList(result);
    }

    private List<IObjectData> getPoolListWithAdmin(String tenantId, String userId) {
        List<IObjectData> objectDataList = Lists.newArrayList();
        List<String> ids = getUserDepartIds(tenantId, userId);
        String queryString = "SELECT DISTINCT pool_id FROM biz_pool_permission WHERE tenant_id='"
                + tenantId + "' and object_api_name='" + Utils.LEADS_POOL_API_NAME + "' and is_deleted=0 and is_admin='t' ";

        queryString += " AND ((data_id='" + userId + "' AND type in ('" +
                ObjectPoolPermission.ObjectPoolMemberType.EMPLOYEE.getValue() + "','" +
                ObjectPoolPermission.ObjectPoolMemberType.OUTER_EMPLOYEE.getValue() + "')) ";
        if (CollectionUtils.empty(ids)) {
            queryString += ") ";
        } else {
            String idString = LeadsUtils.getListQuerySql(ids);
            queryString += " OR (type='" + ObjectPoolPermission.ObjectPoolMemberType.CIRCLE.getValue() + "' AND data_id in ("
                    + idString + "))) ";
        }
        try {
            List<Map> queryResult = objectDataService.findBySql(tenantId, queryString);
            if (CollectionUtils.empty(queryResult)) {
                return objectDataList;
            }
            Set<String> poolIds = queryResult.stream().map(x -> x.get("pool_id").toString()).collect(Collectors.toSet());
            objectDataList = getObjectPoolByIds(tenantId, Lists.newArrayList(poolIds));
        } catch (Exception e) {
            log.error("leads pool service error", e);
            throw new SFABusinessException(SFAErrorCode.LEADS_COMMON_ERROR);
        }
        return objectDataList;
    }

    @Override
    public boolean movePoolObjects(User user, String sourcePoolId, String targetPoolId) {
        List<IObjectData> poolList = getObjectPoolByIds(user.getTenantId(), Lists.newArrayList(sourcePoolId, targetPoolId));
        if (CollectionUtils.empty(poolList) || poolList.size() < 2) {
            throw new ValidateException("线索池不存在！");
        }
        try {
            ParallelUtils.ParallelTask backgroundTask = ParallelUtils.createBackgroundTask();
            backgroundTask.submit(() -> {
                try {
                    String objectApiName = getObjectApiName();
                    int executeCount = 0;
                    while (true) {
                        ++executeCount;
                        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
                        searchTemplateQuery.setLimit(200);
                        List<IFilter> filters = Lists.newArrayList();
                        SearchUtil.fillFilterEq(filters, "leads_pool_id", sourcePoolId);
                        SearchUtil.fillFilterGTE(filters, "is_deleted", 0);
                        searchTemplateQuery.setFilters(filters);
                        searchTemplateQuery.setNeedReturnCountNum(false);
                        searchTemplateQuery.setPermissionType(0);
                        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, objectApiName, searchTemplateQuery);
                        if (queryResult == null || CollectionUtils.empty(queryResult.getData())) {
                           break;
                        }
                        List<IObjectData> dataList = queryResult.getData();
                        dataList.forEach(x -> x.set("leads_pool_id", targetPoolId));
                        objectDataService.batchUpdateWithField(dataList, Lists.newArrayList("leads_pool_id"), getDefaultActionContext(user, ""));
                        if (executeCount >= 10000) {
                            log.warn("movePoolObjects error: objectapi:{}, sourcePoolId:{}, targetPoolId:{}", objectApiName, sourcePoolId, targetPoolId);
                            break;
                        }
                    }
                    LeadsUtils.updateLeadsPoolsCount(user, Lists.newArrayList(sourcePoolId, targetPoolId));
                }
                catch (Exception e){
                    log.error("leads pool service error", e);
                    throw new SFABusinessException(SFAErrorCode.LEADS_COMMON_ERROR);
                }
            });
            backgroundTask.run();

//            String queryString = "SELECT * FROM biz_leads WHERE tenant_id='" + user.getTenantId() +
//                    "' AND leads_pool_id='" + sourcePoolId + "' AND is_deleted>=0 ";
//            List<Map> queryResult = objectDataService.findBySql(user.getTenantId(), queryString);
//            if (CollectionUtils.empty(queryResult)) {
//                return true;
//            }
//            String updateSql = "update biz_leads set leads_pool_id = '%s' where tenant_id = '%s' and leads_pool_id = '%s' AND is_deleted>=0;";
//
//            updateSql = String.format(updateSql, targetPoolId, user.getTenantId(), sourcePoolId);
//            specialTableMapper.setTenantId(user.getTenantId()).batchUpdateBySql(updateSql);
//            LeadsUtils.updateLeadsPoolsCount(user, Lists.newArrayList(sourcePoolId, targetPoolId));
        } catch (Exception e) {
            log.error("leads pool service error", e);
            throw new SFABusinessException(SFAErrorCode.LEADS_COMMON_ERROR);
        }
        return true;
    }

    @ServiceMethod("sync_leads_pool_option")
    public SyncLeadsPoolOption.Result syncLeadsPoolOption(ServiceContext serviceContext, SyncLeadsPoolOption.Arg arg) {
        IObjectDescribe objectDescribe = serviceFacade.findObject(serviceContext.getTenantId(), Utils.LEADS_API_NAME);
        SelectOneFieldDescribe fieldDescribe = (SelectOneFieldDescribe) objectDescribe.getFieldDescribe("leads_pool_id");
        if (fieldDescribe != null) {
            List<ISelectOption> originalSelectOptions = fieldDescribe.getSelectOptions();
            List<ISelectOption> selectOptions = Lists.newArrayList();
            arg.getOptions().forEach(option -> {
                String v = option.getValue();
                ISelectOption selectOption = new SelectOption();
                selectOption.setValue(option.getValue());
                selectOption.setLabel(option.getLabel());
                if (CollectionUtils.notEmpty(originalSelectOptions)) {
                    Optional<ISelectOption> optional = originalSelectOptions.stream().filter(o -> v.equals(o.getValue())).findFirst();
                    if (optional.isPresent()) {
                        ISelectOption original = optional.get();
                        selectOption.setChildOptions(original.getChildOptions());
                        //selectOption.setIsRequired(original.getIsRequired());
                        //selectOption.setNotUsable(original.isNotUsable());
                        //selectOption.setBooleanValue(original.getBooleanValue());
                    }
                }
                selectOptions.add(selectOption);
            });

            fieldDescribe.setSelectOptions(selectOptions);
        }
        serviceFacade.updateFieldDescribe(objectDescribe, Arrays.asList(fieldDescribe));
        return new SyncLeadsPoolOption.Result();
    }

    private ActionContext getDefaultActionContext(User user) {
        ActionContext actionContext = new ActionContext();
        actionContext.setEnterpriseId(user.getTenantId());
        actionContext.setUserId(user.getUserId());
        actionContext.setDbType("pg");
        actionContext.setPrivilegeCheck(false);
        return actionContext;
    }

    private ActionContext getDefaultActionContext(User user, String eventId) {
        ActionContext actionContext = new ActionContext();
        actionContext.setEnterpriseId(user.getTenantId());
        actionContext.setUserId(user.getUserId());
        actionContext.setDbType("pg");
        actionContext.setPrivilegeCheck(false);
        if (StringUtils.isNotBlank(eventId)) {
            actionContext.put("eventId", eventId);
        }
        return actionContext;
    }

    @Override
    public Map<String, ObjectPoolModels.PoolObjectsCountInfo> getPoolObjectCountInfo(User user, List<String> poolIds) {
        Map<String, ObjectPoolModels.PoolObjectsCountInfo> result = Maps.newHashMap();
        if (CollectionUtils.empty(poolIds)) {
            return result;
        }
        Map<String, Integer> poolCountMap = getPoolTotalCount(user.getTenantId(), poolIds);
        Map<String, Integer> poolUnallocatedCountMap = getPoolUnAllocatedCount(user.getTenantId(), poolIds);
        for (String poolId : poolIds) {
            int totalCount = 0, unallocatedCount = 0;
            if (poolCountMap.containsKey(poolId)) {
                if (poolCountMap.get(poolId) != null) {
                    totalCount = poolCountMap.get(poolId);
                }
            }
            if (poolUnallocatedCountMap.containsKey(poolId)) {
                if (poolUnallocatedCountMap.get(poolId) != null) {
                    unallocatedCount = poolUnallocatedCountMap.get(poolId);
                }
            }
            ObjectPoolModels.PoolObjectsCountInfo countInfo = ObjectPoolModels.PoolObjectsCountInfo.builder()
                    .objectPoolId(poolId).totalCount(String.valueOf(totalCount))
                    .unallocatedCount(String.valueOf(unallocatedCount))
                    .allocatedCount(String.valueOf(totalCount - unallocatedCount)).build();
            result.put(poolId, countInfo);
        }

        return result;
    }

    private Map<String, Integer> getPoolUnAllocatedCount(String tenantId, List<String> poolIds) {
        if(AccountUtil.isGrayPoolGroupBy(tenantId)) {
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, "tenant_id", tenantId);
            SearchUtil.fillFilterIn(filters, LeadsConstants.Field.LEADS_POOL_ID.getApiName(), poolIds);
            SearchUtil.fillFilterEq(filters, "is_deleted", 0);
            SearchUtil.fillFilterEq(filters, "biz_status", LeadsBizStatusEnum.UN_ASSIGNED.getCode());
            Map<String, Integer> result = CommonSqlUtils.getGroupByResult(tenantId, SFAPreDefineObject.Leads.getApiName(), LeadsConstants.Field.LEADS_POOL_ID.getApiName(), filters);
            return result;
        } else {
            String idString = buildSqlInString(poolIds);
            String querySql = "SELECT leads_pool_id, COUNT(*) AS totalcount FROM biz_leads WHERE tenant_id='"
                    + tenantId + "' AND is_deleted=0 AND leads_pool_id = any(array[" + idString + "]) AND biz_status='"
                    + LeadsBizStatusEnum.UN_ASSIGNED.getCode() + "' " +
                    "GROUP BY tenant_id, leads_pool_id";

            return getTotalMap(tenantId, querySql);
        }
    }

    private String buildSqlInString(List<String> ids) {
        if (CollectionUtils.empty(ids)) {
            return "''";
        }
        ids = ids.stream().distinct().collect(Collectors.toList());
        StringBuilder builder = new StringBuilder();
        for (String id : ids) {
            builder.append("'" + id + "',");
        }
        String idString = builder.toString();
        idString = idString.substring(0, idString.length() - 1);
        return idString;
    }

    private Map<String, Integer> getPoolTotalCount(String tenantId, List<String> poolIds) {
        if(AccountUtil.isGrayPoolGroupBy(tenantId)) {
            List<IFilter> filters = Lists.newArrayList();
            SearchUtil.fillFilterEq(filters, "tenant_id", tenantId);
            SearchUtil.fillFilterIn(filters, LeadsConstants.Field.LEADS_POOL_ID.getApiName(), poolIds);
            SearchUtil.fillFilterEq(filters, "is_deleted", 0);
            Map<String, Integer> result = CommonSqlUtils.getGroupByResult(tenantId, SFAPreDefineObject.Leads.getApiName(), LeadsConstants.Field.LEADS_POOL_ID.getApiName(), filters);
            return result;
        } else {
            String idString = buildSqlInString(poolIds);
            String querySql = "SELECT leads_pool_id, COUNT(*) AS totalcount FROM biz_leads WHERE tenant_id='"
                    + tenantId + "' AND is_deleted=0 AND leads_pool_id = any(array[" + idString + "]) " +
                    "GROUP BY tenant_id, leads_pool_id";

            return getTotalMap(tenantId, querySql);
        }
    }

    private Map<String, Integer> getTotalMap(String tenantId, String querySql) {
        Map<String, Integer> result = Maps.newHashMap();
        try {
            List<Map> queryResult = objectDataService.findBySql(tenantId, querySql);
            if (CollectionUtils.notEmpty(queryResult)) {
                for (Map data : queryResult) {
                    String id = data.get("leads_pool_id").toString();
                    Integer totalCount = Integer.valueOf(data.get("totalcount").toString());
                    result.put(id, totalCount);
                }
            }
        } catch (Exception e) {
            throw new SFABusinessException(SFAErrorCode.LEADS_COMMON_ERROR);
        }
        return result;
    }

    @Override
    public ObjectPoolPermission.ObjectPoolMemberType getPoolMemberType(String tenantId, String userId, String objectPoolId) {
        List<Map> queryResult = LeadsUtils.findLeadsPoolPermissionByPoolIds(tenantId, Lists.newArrayList(objectPoolId));
        if (CollectionUtils.notEmpty(queryResult)) {
            List<Map> members = queryResult.stream().filter(
                    a -> a.getOrDefault("is_admin", false).equals(false)).collect(Collectors.toList());
            if (CollectionUtils.notEmpty(members)) {
                Optional<Map> member = members.stream()
                        .filter(a -> userId.equals(a.getOrDefault("data_id", "").toString()))
                        .findFirst();
                if (member.isPresent()) {
                    String memberType = member.get().getOrDefault("type", "").toString();
                    Optional<ObjectPoolPermission.ObjectPoolMemberType> objectPoolMemberType = Arrays.stream(ObjectPoolPermission.ObjectPoolMemberType.values())
                            .filter(arr -> arr.getValue().equals(memberType))
                            .findFirst();
                    if (objectPoolMemberType.isPresent()) {
                        return objectPoolMemberType.get();
                    }
                }
            }
        }
        return null;
    }

    @Override
    public Map<String, List<GetPoolMembers.PoolMember>> getPoolMembers(User user, List<String> poolIds) {
        Map<String, List<GetPoolMembers.PoolMember>> result = Maps.newHashMap();
        if (CollectionUtils.empty(poolIds)) {
            return result;
        }
        String tenantId = user.getTenantId();
        poolIds = poolIds.stream().filter(p -> StringUtils.isNotEmpty(p)).distinct().collect(Collectors.toList());
        List<Map> queryResult = LeadsUtils.findLeadsPoolPermissionByPoolIds(tenantId, poolIds);
        if (CollectionUtils.empty(queryResult)) {
            return result;
        }
        return ObjectPoolUtil.dealPoolMember(user, poolIds, queryResult, "pool_id");
    }

    @Override
    public Map<String, Boolean> checkPoolInUse(User user, List<String> poolIds) {
        Map<String, Boolean> result = Maps.newHashMap();
        poolIds.forEach(x -> result.put(x, false));
        ObjectLimitRuleModel.ObjectLimitOverRule overRule = ObjectLimitUtil.getObjectLimitOverRule(user.getTenantId(), getObjectApiName());
        if (overRule != null && StringUtils.isNotBlank(overRule.getObjectPoolId())) {
            if (poolIds.contains(overRule.getObjectPoolId())) {
                result.put(overRule.getObjectPoolId(), true);
            }
        }

        return result;
    }

    @Override
    public List<IObjectData> getTargetObjectPoolList(String tenantId, User user, String action) {
        String userId = user.getUserId();
        List<String> ids = AccountUtil.getUserDepartIds(tenantId, userId);
        List<IObjectData> result = AccountUtil.getAllObjectPools(tenantId, userId,getObjectPoolApiName());

        if(LeadsUtils.isCrmAdmin(user)) {
            return result;
        }

        try {
            String queryString = "SELECT DISTINCT pool_id,is_admin FROM biz_pool_permission WHERE tenant_id='"
                    + tenantId + "' and object_api_name='" + Utils.LEADS_POOL_API_NAME + "' and is_deleted=0 ";

            queryString += "AND ((data_id='" + userId + "' AND type in ('" +
                    ObjectPoolPermission.ObjectPoolMemberType.EMPLOYEE.getValue() + "')) ";
            if (CollectionUtils.empty(ids)) {
                queryString += ") ";
            } else {
                String idString = LeadsUtils.getListQuerySql(ids);
                queryString += " OR (type='" + ObjectPoolPermission.ObjectPoolMemberType.CIRCLE.getValue() + "' AND data_id in ("
                        + idString + "))) ";
            }
            List<Map> queryResult = objectDataService.findBySql(tenantId, queryString);
            if (CollectionUtils.empty(queryResult)) {
                if ("move".equals(action)) {
                    result.removeIf(x -> AccountUtil.getAllowMemberMove(x));
                }
                if ("return".equals(action)) {
                    result.removeIf(x -> AccountUtil.getAllowMemberReturn(x));
                }
                return result;
            }
            final List<String> poolIds = queryResult.stream().map(x -> x.get("pool_id").toString()).collect(Collectors.toList());
            if (CollectionUtils.empty(poolIds)) {
                return Lists.newArrayList();
            }

            // 不在此部门 && (only_allow_member_move == true) 则移除该公海
            if ("move".equals(action)) {
                result.removeIf(x -> !poolIds.contains(x.getId()) && AccountUtil.getAllowMemberMove(x));
            }
            if ("return".equals(action)) {
                result.removeIf(x -> !poolIds.contains(x.getId()) && AccountUtil.getAllowMemberReturn(x));
            }
            result = mergeLeadsPoolIds(result,queryResult,poolIds);
        } catch (Exception e) {
            log.error("leads getTargetObjectPoolList error", e);
            throw new SFABusinessException(SFAErrorCode.ACCOUNT_COMMON_ERROR);
        }
        return result;
    }

    /**
     * 安装需求排序线索池
     * http://wiki.firstshare.cn/pages/viewpage.action?pageId=*********
     * @param result
     * @param poolIds
     * @return
     */
    private List<IObjectData> mergeLeadsPoolIds(List<IObjectData> result,List<Map> queryResult,List<String> poolIds){
        Map<String,Boolean> map = new HashedMap();
        queryResult.stream().forEach(x -> {
            map.put(String.valueOf(x.get("pool_id")), (Boolean) x.get("is_admin"));
        });
        List<IObjectData> newResult = new ArrayList<>();
        List<IObjectData> list = new ArrayList<>();
        List<IObjectData> list2 = new ArrayList<>();
        List<IObjectData> list3 = new ArrayList<>();
        List<IObjectData> list4 = new ArrayList<>();
        List<IObjectData> list5 = new ArrayList<>();
        for (IObjectData x : result) {
            if(poolIds.contains(x.getId()) && !map.get(x.getId())){
                list.add(x);
            } else if (poolIds.contains(x.getId()) && map.get(x.getId())) {
                list2.add(x);
            }else if (!AccountUtil.getAllowMemberMove(x)){
                list3.add(x);
            }else if (!AccountUtil.getAllowMemberReturn(x)){
                list4.add(x);
            }else {
                list5.add(x);
            }
        }
        newResult.addAll(list);
        newResult.addAll(list2);
        newResult.addAll(list3);
        newResult.addAll(list4);
        newResult.addAll(list5);
        return newResult;
    }

    @Override
    public List<IObjectData> getSortedLeadsPoolList(String tenantId, User user) {
        return getTargetObjectPoolList(tenantId,user,null);
    }
}

