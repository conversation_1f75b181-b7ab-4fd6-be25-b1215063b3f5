package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.predefine.service.ProductCategoryService;
import com.facishare.crm.sfa.predefine.service.SpuSkuImportExportService;
import com.facishare.crm.sfa.predefine.service.model.CategoryObject;
import com.facishare.crm.sfa.utilities.constant.SpuImoprtConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.Pair;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.action.StandardUnionInsertImportTemplateAction;
import com.facishare.paas.appframework.core.util.ImportExportExt;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.poi.ss.usermodel.Workbook;

import java.util.List;
import java.util.Objects;

import static com.facishare.paas.appframework.core.util.ImportExportExt.IMPORT_TYPE;

/**
 * <AUTHOR>
 * @date 2019/6/14 15:42
 * @instruction
 */
public class SPUUnionInsertImportTemplateAction extends StandardUnionInsertImportTemplateAction {

	private static final ProductCategoryService productCategoryService = SpringUtil.getContext().getBean(ProductCategoryService.class);
	private static final SpuSkuImportExportService improtService = SpringUtil.getContext().getBean(SpuSkuImportExportService.class);
	private boolean openManyUnitStatus = false;
	@Override
	protected void before(Arg arg) {
		super.before(arg);
		openManyUnitStatus = improtService.getConfigStatus(actionContext.getTenantId(),actionContext.getRequestContext(),"unit");
	}

	/**
	 * 只过滤非主对象字段
	 * 过滤产品上的同步字段及不支持导入字段
	 * @param detailFieldList
	 */
	@Override
	protected void customDetailHeader(List<IFieldDescribe> detailFieldList) {
		super.customDetailHeader(detailFieldList);
		detailFieldList.removeIf(field-> SpuImoprtConstants.REMOVE_FIELDS_SKU_TEMPLATE.contains(field.getApiName()));
	}


	@Override
	protected Workbook createAndFillWorkbook(List<IFieldDescribe> sortedFieldList, IObjectDescribe describe, Workbook workbook) {
		//key: 标题名称， value: 字段类型
		List<Pair<String, IFieldDescribe>> labelList = Lists.newArrayList();
		List<List<String>> sampleRows = Lists.newArrayList();
		List<String> sampleList = Lists.newArrayList();
		for (IFieldDescribe field : sortedFieldList) {
			String sampleValue = "";
			//业务类型字段改为必填
			if (field.getType().equals(IFieldType.RECORD_TYPE)) {
				field.setRequired(Boolean.TRUE);
			}
			// 处理分类
			if(Objects.equals(field.getApiName(),"category")){
				CategoryObject categoryObject = productCategoryService.treeList(new ServiceContext(actionContext.getRequestContext(),null,null));
				sampleValue = improtService.getCategroyTree(new ServiceContext(actionContext.getRequestContext(),
						null,null));
			}else{
				sampleValue = getFieldSampleValue(field);
			}
			// 从对象模板中，主对象ID字段不填值
			if (Objects.nonNull(field.getExtendInfo())) {
				if (Objects.equals(ImportExportExt.UNION_IMPORT_ID_MARK,field.getExtendInfo().get(IMPORT_TYPE))) {
					sampleValue = "";
				}
			}
			sampleList.add(sampleValue);

			String title = field.getLabel();
			if (IFieldType.PERCENTILE.equals(field.getType())) {
				//百分比类型要在表头加（%）
				title = title + PERCENTAGE_SYMBOL;
			}
			// ID字段不加"（必填）"符号
			if (Objects.equals(Boolean.TRUE, field.isRequired())) {
				title = title + I18N.text(REQUIRED_SYMBOL);
			}
			labelList.add(Pair.of(title, field));
		}
		sampleRows.add(sampleList);
		sampleRows = customSampleList(sampleRows);

		return serviceFacade.generateUnionTemplate(actionContext.getUser(),
				describe.getDisplayName(), labelList, sampleRows,workbook);
	}
}
