package com.facishare.crm.sfa.utilities.proxy.model;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.metadata.api.IObjectData;
import joptsimple.internal.Strings;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @IgnoreI18nFile
 */
public interface IndustryEnterInfoModel {
    @Data
    class SearchIndustryEnterInfoResult {
        private Integer Status;
        private String Message;
        private List<IndustryEnterInfo> Result;
    }

    @Data
    class GetIndustryEnterDetailInfoResult {
        private Integer Status;
        private String Message;
        private IndustryEnterInfo Result;
    }

    @Data
    class IndustryEnterInfo {
        private String KeyNo;
        private String TermStart;
        private String TeamEnd;
        private String CheckDate;
        private String Name;
        private String No;
        private String BelongOrg;
        private String OperName;
        private String StartDate;
        private String EndDate;
        private String Status;
        private String Province;
        private String UpdatedDate;
        private String CreditCode;
        private String RegistCapi;
        private String RegistCapiDesc;
        private String EconKind;
        private String Address;
        private String Scope;
        private IndustryContactInfo ContactInfo;

        private String getWebSiteUrl(){
            if(ContactInfo != null && CollectionUtils.notEmpty(ContactInfo.WebSite)){
                return getContactInfo().getWebSite().get(0).getUrl();
            }
            return Strings.EMPTY;
        }

        private String getPhoneNumber(){
            if(ContactInfo != null){
                return getContactInfo().getPhoneNumber();
            }
            return Strings.EMPTY;
        }

        private String getEmail(){
            if(ContactInfo != null){
                return getContactInfo().getEmail();
            }
            return Strings.EMPTY;
        }

        private String getTermDate(){
            String startDate = StringUtils.isBlank(getTermStart()) ? "--" : getTermStart();
            String endDate = StringUtils.isBlank(getTeamEnd()) ? "--" : getTeamEnd();
            return String.format("%s 至 %s", startDate, endDate);
        }

        public IObjectData toObjectData(){
            ObjectDataDocument objectDataDocument = new ObjectDataDocument();
            objectDataDocument.put("KeyNo", getKeyNo());
            objectDataDocument.put("TermStart", getTermStart());
            objectDataDocument.put("TeamEnd", getTeamEnd());
            objectDataDocument.put("CheckDate", getCheckDate());
            objectDataDocument.put("Status", getStatus());
            objectDataDocument.put("RegistCapi", getRegistCapi());
            objectDataDocument.put("No", getNo());
            objectDataDocument.put("BelongOrg", getBelongOrg());
            objectDataDocument.put("CreditCode", getCreditCode());
            objectDataDocument.put("OperName", getOperName());
            objectDataDocument.put("Email", getEmail());
            objectDataDocument.put("EconKind", getEconKind());
            objectDataDocument.put("Address", getAddress());
            objectDataDocument.put("EndDate", getEndDate());
            objectDataDocument.put("Province", getProvince());
            objectDataDocument.put("CompanyStartDate", getStartDate());
            objectDataDocument.put("Name", getName());
            objectDataDocument.put("StartDate", getStartDate());
            objectDataDocument.put("Scope", getScope());
            objectDataDocument.put("WebSiteUrl", getWebSiteUrl());
            objectDataDocument.put("PhoneNumber", getPhoneNumber());
            objectDataDocument.put("TermDate", getTermDate());
            objectDataDocument.put("CheckDate", getCheckDate());
            objectDataDocument.put("ShortStatus", getStatus());
            objectDataDocument.put("object_describe_api_name", "BizQueryObj");
            return objectDataDocument.toObjectData();
        }
    }

    @Data
    class IndustryContactInfo {
        private String PhoneNumber;
        private String Email;
        private List<IndustryWebSiteInfo> WebSite;
    }

    @Data
    class IndustryWebSiteInfo {
        private String Url;
        private String Name;
    }
}
