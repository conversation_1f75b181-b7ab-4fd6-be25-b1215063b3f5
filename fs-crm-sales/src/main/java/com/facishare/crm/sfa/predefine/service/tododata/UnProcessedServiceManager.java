package com.facishare.crm.sfa.predefine.service.tododata;

import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Demo class
 *
 * <AUTHOR>
 * @date 2019/10/23
 */
@Component
public class UnProcessedServiceManager implements ApplicationContextAware {
    private Map<String, IUnProcessedService> unProcessedService = Maps.newHashMap();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        initObjectPoolServiceMap(applicationContext);
    }

    private void initObjectPoolServiceMap(ApplicationContext applicationContext) {
        Map<String, IUnProcessedService> springBeanMap = applicationContext.getBeansOfType(IUnProcessedService.class);
        springBeanMap.values().forEach(provider -> {
            if (StringUtils.isNotEmpty(provider.getObjectApiName())) {
                unProcessedService.put(provider.getObjectApiName(), provider);
            }
        });
    }

    public IUnProcessedService getUnProcessedService(String apiName) {
        return unProcessedService.getOrDefault(apiName, null);
    }
}