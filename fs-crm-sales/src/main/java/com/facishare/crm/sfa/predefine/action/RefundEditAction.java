package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.utilities.constant.RefundConstants;
import com.facishare.crm.sfa.utilities.proxy.RefundProxy;
import com.facishare.crm.sfa.utilities.proxy.model.SfaEditModel;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.crm.sfa.utilities.validator.RefundValidator;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

import static com.facishare.crm.sfa.predefine.service.transfer.CrmPackageObjectConstants.FIELD_LIFE_STATUS;

@Slf4j
public class RefundEditAction extends StandardEditAction {

    private RefundProxy refundProxy = SpringUtil.getContext().getBean(RefundProxy.class);

    @Override
    public void before(Arg arg) {
        ObjectDataDocument dataDocument = arg.getObjectData();
        this.remove_spec_package_field_value(dataDocument);
        super.before(arg);
        RefundValidator.validateEdit(serviceFacade, actionContext.getTenantId(),
                arg.getObjectData().toObjectData(), this.dbMasterData);
    }


    private void remove_spec_package_field_value(ObjectDataDocument dataDocument) {
        if (dataDocument.containsKey(RefundConstants.RefundField.BIZ_STATUS.getApiName())) {
            dataDocument.remove(dataDocument.get(RefundConstants.RefundField.BIZ_STATUS.getApiName()));
        }
    }


    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        if (result.getObjectData() != null) {
            if (SFAConfigUtil.isCustomerAccountEnabled(actionContext.getTenantId())) {
                Map<String, String> header = Maps.newHashMap();
                header.put("x-fs-Employee-Id", actionContext.getUser().getUserId());
                header.put("x-fs-Enterprise-Id", actionContext.getUser().getTenantId());
                header.put("x-fs-ei", actionContext.getUser().getTenantId());
                header.put("x-fs-userInfo", actionContext.getUser().getUserId());
                if (objectData.get(RefundConstants.RefundField.REFUND_METHOD.getApiName()) != null) {
                    if (isRebateAndPrePayData(objectData)) {
                        SfaEditModel.Result rst = refundProxy.refundEdit(
                                new SfaEditModel.Arg(objectData.getId(), objectData.get(FIELD_LIFE_STATUS).toString()), header
                        );
                        if (!rst.IsSuccess()) {
                            log.warn(String.format("%s调用深研RefundEdit失败,msg:%s", objectData.getId(), rst.getErrMessage()));
                        }
                    }
                }
            }
        }
        return after;
    }

    private boolean isRebateAndPrePayData(IObjectData data) {
        String refundMethod = data.get(RefundConstants.RefundField.REFUND_METHOD.getApiName(), String.class);
        return RefundConstants.RefundType.PrePay.getValue().equals(refundMethod)
                || RefundConstants.RefundType.Rebate.getValue().equals(refundMethod);
    }
}
