package com.facishare.crm.sfa.predefine.action;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/11/27 17:27
 * @instruction 退货单 退货单产品  导出
 */
public class ReturnedGoodsInvoiceExportAction extends SFAExportAction {
    private IObjectDescribe returnedGoodsInvoiceProductDescribe;
    private static final String RETURNED_GOODS_INVOICE_OBJ = "ReturnedGoodsInvoiceObj";
    private static final String RETURNED_GOODS_INVOICE_PRODUCT_OBJ = "ReturnedGoodsInvoiceProductObj";
    protected SearchTemplateQuery returnedGoodsInvoiceProductSearchQuery;
    public static final String FIELD_RETURNED_GOODS_INVOICE_ID = "returned_goods_inv_id";

    @Override
    protected int validateThrottle() {
        int count = super.validateThrottle();
        //查询退货单产品描述
        returnedGoodsInvoiceProductDescribe = serviceFacade.findObject(actionContext.getTenantId(), RETURNED_GOODS_INVOICE_PRODUCT_OBJ);
        returnedGoodsInvoiceProductSearchQuery = this.handleSearchQuery(this.searchQuery, true);
        QueryResult<IObjectData> returnedGoodsInvoiceProductData = findObjectByQuery(this.actionContext.getUser(),
                this.returnedGoodsInvoiceProductDescribe, this.returnedGoodsInvoiceProductSearchQuery);
        int returnedGoodsInvoiceProductCount = returnedGoodsInvoiceProductData.getTotalNumber();
        if (count + returnedGoodsInvoiceProductCount > getExportRowsThrottle()) {
            throw new ValidateException(buildThrottleExceedMessage());
        }
        return count + returnedGoodsInvoiceProductCount;
    }

    protected SearchTemplateQuery handleSearchQuery(SearchTemplateQuery query, boolean isVerification) {

        SearchTemplateQuery newQuery = JSON.parseObject(query.toJsonString(), SearchTemplateQuery.class);
        newQuery.resetFilters(Lists.newArrayList(query.getFilters()));
        if (newQuery.getDataRightsParameter() != null) {
            newQuery.getDataRightsParameter().setIsDetailObject(true);
            newQuery.getDataRightsParameter().setMasterIdFieldApiName(FIELD_RETURNED_GOODS_INVOICE_ID);
            newQuery.getDataRightsParameter().setMasterObjectApiName(RETURNED_GOODS_INVOICE_OBJ);
        }
        if (isVerification) {
            newQuery.setLimit(1);
            newQuery.setOffset(0);
        }
        return newQuery;
    }

    @Override
    protected void initDescribeMapToExport() {
        super.initDescribeMapToExport();
        describeMap.put(returnedGoodsInvoiceProductDescribe.getApiName(), returnedGoodsInvoiceProductDescribe);
    }

    @Override
    protected Map<String, List<IObjectData>> getRelatedDataMap(QueryResult<IObjectData> queryResult) {
        Map<String, List<IObjectData>> dataMap = Maps.newLinkedHashMap();
        List<IObjectData> detailData = serviceFacade.findDetailObjectDataList(actionContext.getUser(),
                returnedGoodsInvoiceProductDescribe, queryResult.getData());
        dataMap.put(returnedGoodsInvoiceProductDescribe.getApiName(), detailData);

        return dataMap;
    }
}
