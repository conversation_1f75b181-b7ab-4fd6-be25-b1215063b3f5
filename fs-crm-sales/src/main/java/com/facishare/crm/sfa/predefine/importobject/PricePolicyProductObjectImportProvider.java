package com.facishare.crm.sfa.predefine.importobject;

import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.paas.appframework.metadata.importobject.DefaultObjectImportProvider;
import com.facishare.paas.appframework.metadata.importobject.ImportObject;
import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class PricePolicyProductObjectImportProvider extends DefaultObjectImportProvider {
    @Override
    public String getObjectCode() {
        return SFAPreDefineObject.PricePolicyProduct.getApiName();
    }

    @Override
    public Optional<ImportObject> getImportObject(IObjectDescribe describe, IUniqueRule uniqueRule) {
        return Optional.empty();
    }
}
