package com.facishare.crm.sfa.predefine.action;

import com.facishare.common.proxy.helper.StringUtils;
import com.facishare.crmcommon.describebuilder.TextFieldDescribeBuilder;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.EnterpriseInitService;
import com.facishare.crm.sfa.predefine.service.SpuSkuImportExportService;
import com.facishare.crm.sfa.utilities.constant.SpuSkuConstants;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardExportAction;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.ProductCategoryService;
import com.facishare.paas.appframework.metadata.ProductCategoryServiceImpl;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.TextFieldDescribe;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018/11/2 17:27
 * @instruction 商品 规格  规格指导出
 */
@Slf4j
public class SPUExportAction extends StandardExportAction {
    private static final String PRODUCT_OBJ = "ProductObj";
    private static final String NOT_TRANS_SPU_ID = "not_trans_spu_id";
    protected SearchTemplateQuery skuSearchQuery;
    @Autowired
    EnterpriseInitService enterpriseInitService;
    private SpuSkuImportExportService spuSkuImportExportService = SpringUtil.getContext().getBean(SpuSkuImportExportService.class);
    private ProductCategoryService productService = SpringUtil.getContext().getBean(ProductCategoryServiceImpl.class);
    private IObjectDescribe skuDescribe;

    @Override
    protected SearchTemplateQuery generateSearchQuery(User user, String searchTemplateId, String searchQuery, List<String> dataIdList) {
        SearchTemplateQuery searchTemplateQuery = super.generateSearchQuery(user, searchTemplateId, searchQuery, dataIdList);
        productService.handleCategoryFilters(actionContext.getTenantId(), actionContext.getUser().getUserId(), searchTemplateQuery.getFilters());

        // 处理wheres里面的分类filters
        searchTemplateQuery.getWheres().stream()
                .filter(k -> org.apache.commons.collections.CollectionUtils.isNotEmpty(k.getFilters()))
                .forEach(k -> productService.handleCategoryFilters(actionContext.getTenantId(), actionContext.getUser().getUserId(), k.getFilters()));
        return searchTemplateQuery;
    }

    @Override
    protected int validateThrottle() {
        //查询规格值描述
        skuDescribe = serviceFacade.findObject(actionContext.getTenantId(), PRODUCT_OBJ);

        //初始化查询条件
        skuSearchQuery = this.generateSearchQuery(actionContext.getUser(), skuDescribe, arg.getSearch_template_id(),
                arg.getSearch_query_info(), arg.getDataIdList());

        skuSearchQuery.setLimit(1);
        skuSearchQuery.setOffset(0);
        QueryResult<IObjectData> data = findObjectByQuery(actionContext.getUser(), skuDescribe,
                skuSearchQuery);
        if (data.getTotalNumber() > getExportRowsThrottle()) {
            throw new ValidateException(buildThrottleExceedMessage());
        }
        return data.getTotalNumber();
    }

    @Override
    protected Map<String, List<IFieldDescribe>> findFieldMap(String recordType) {
        TextFieldDescribe specificationFieldDescribe = TextFieldDescribeBuilder.builder().apiName(SpuSkuConstants.CUSTOM_SPU_SPEC_FIELD).label(SpuSkuConstants.SPU_SPECIFICATION_PROPERTY).maxLength(256).build();
        TextFieldDescribe productFieldDescribe = TextFieldDescribeBuilder.builder().apiName(SpuSkuConstants.CUSTOM_SKU_SPEC_VALUE_FIELD).label(SpuSkuConstants.SKU_SPECIFICATION_VALUE_PROPERTY).maxLength(256).build();


        TextFieldDescribe spuIdFieldDescribe = TextFieldDescribeBuilder.builder().apiName("_id").label(SpuSkuConstants.SPU_NUMBER).maxLength(256).build();
        TextFieldDescribe skuIdFieldDescribe = TextFieldDescribeBuilder.builder().apiName("_id").label(SpuSkuConstants.SKU_NUMBER).maxLength(256).build();
        TextFieldDescribe productSpuIdDescribe = TextFieldDescribeBuilder.builder().apiName(NOT_TRANS_SPU_ID).label(SpuSkuConstants.SPU_NUMBER).maxLength(256).build();


        Map<String, List<IFieldDescribe>> describeFieldMap = super.findFieldMap(recordType);
        describeFieldMap.forEach((k, v) -> {
            //商品
            if (Objects.equals(k, Utils.SPU_API_NAME)) {
                v.add(spuIdFieldDescribe);
                v.add(specificationFieldDescribe);
                //产品
            } else {
                v.add(skuIdFieldDescribe);
                v.add(productFieldDescribe);
                v.add(productSpuIdDescribe);
            }
        });
        return describeFieldMap;
    }

    @Override
    protected void initDescribeMapToExport() {
        super.initDescribeMapToExport();
        describeMap.put(skuDescribe.getApiName(), skuDescribe);
    }

    @Override
    protected Map<String, List<IObjectData>> getRelatedDataMap(QueryResult<IObjectData> queryResult) {
        Map<String, IObjectData> spuDataMap = queryResult.getData().stream().collect(Collectors.toMap(x -> x.getId(), x -> x));

        Map<String, List<IObjectData>> dataMap = Maps.newHashMap();
        List<IObjectData> skuDatas = Lists.newArrayList();
        List<String> spuIds = queryResult.getData().stream().map(x -> x.getId()).collect(Collectors.toList());
        List<List<String>> spuIdGroups = Lists.partition(spuIds, 10);
        spuIdGroups.forEach(spuIdGroup -> {
            QueryResult<IObjectData> skuQueryResult = spuSkuImportExportService.findByQueryIdAndStatus(actionContext.getUser(), Lists.newArrayList(), spuIdGroup,
                    Utils.PRODUCT_API_NAME, 0, 5000);
            skuQueryResult.getData().forEach(skuData -> {
                String product_spec = skuData.get(SpuSkuConstants.SKU_SPEC_FIELD, String.class);
                if (StringUtils.isNotEmpty(product_spec)) {
                    Map<String, String> maps = generateSpecAndSpecValue(product_spec);
                    IObjectData spuData = spuDataMap.get(skuData.get(SpuSkuConstants.SKU_RELATE_SPU_ID));
                    spuData.set(SpuSkuConstants.CUSTOM_SPU_SPEC_FIELD, maps.get(Utils.SPU_API_NAME));
                    skuData.set(SpuSkuConstants.CUSTOM_SKU_SPEC_VALUE_FIELD, maps.get(Utils.PRODUCT_API_NAME));
                }
                skuData.set(NOT_TRANS_SPU_ID, skuData.get("spu_id", String.class));
            });
            skuDatas.addAll(skuQueryResult.getData());
        });
        dataMap.put(skuDescribe.getApiName(), skuDatas);

        return dataMap;
    }

    protected SearchTemplateQuery generateSearchQuery(User user, IObjectDescribe objectDescribe, String searchTemplateId, String searchQuery, List<String> dataIdList) {
        SearchTemplateQuery query = serviceFacade
                .getSearchTemplateQuery(user, ObjectDescribeExt.of(objectDescribe), searchTemplateId,
                        searchQuery);
        //导出，按照创建时间升序排列
        for (OrderBy order : query.getOrders()) {
            if (IObjectData.LAST_MODIFIED_TIME.equals(order.getFieldName())) {
                order.setFieldName(IObjectData.CREATE_TIME);
                order.setIsAsc(Boolean.TRUE);
                break;
            }
        }
        if (CollectionUtils.notEmpty(dataIdList)) {
            SearchTemplateQueryExt.of(query).addFilter(Operator.IN, IObjectData.ID, dataIdList);
        }
        return query;
    }

    private Map<String, String> generateSpecAndSpecValue(String productSpec) {
        Map<String, String> maps = Maps.newHashMap();
        StringBuilder specValue = new StringBuilder();
        StringBuilder spec = new StringBuilder();
        String[] specMap = productSpec.split(";");
        for (int i = 0; i < specMap.length; i++) {
            String key = specMap[i];
            String[] specValues = key.split(":");
            for (int j = 0; j < specValues.length; j++) {
                if (j == 1) {
                    specValue.append(specValues[j] + ";");
                }
                if (j == 0) {
                    spec.append(specValues[j] + ";");
                }
            }
        }
        maps.put(Utils.SPU_API_NAME, spec.toString());
        maps.put(Utils.PRODUCT_API_NAME, specValue.toString());
        return maps;
    }

}
