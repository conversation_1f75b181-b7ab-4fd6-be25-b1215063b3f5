package com.facishare.crm.sfa.predefine.service.real;

import com.facishare.crm.sfa.predefine.service.SalesOrderService;
import com.facishare.crm.sfa.utilities.proxy.SalesOrderProxy;
import com.facishare.crm.sfa.utilities.proxy.model.ConfirmDeliveryData;
import com.facishare.crm.sfa.utilities.proxy.model.ConfirmReceiveData;
import com.facishare.crm.sfa.utilities.proxy.model.SetLogisticsStatusData;
import com.facishare.crm.sfa.utilities.proxy.model.SetStatusData;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
@Component
@Service("sfaSalesOrderService")
public class SalesOrderServiceImpl implements SalesOrderService {
    private SalesOrderProxy salesOrderProxy = SpringUtil.getContext().getBean("salesOrderProxy", SalesOrderProxy.class);

    @Override
    public SetLogisticsStatusData.Result SetLogisticsStatus(String tenantId, String userId, String id, Integer status,
                                                            Long deliveryDate, Long receiveDate) {
        Map<String, String> headers = Maps.newHashMap();
        headers.put("Content-Type", "application/json");
        headers.put("x-fs-ei", tenantId);
        headers.put("x-fs-userInfo", userId);
        SetLogisticsStatusData.Result result;
        SetLogisticsStatusData.Arg arg = new SetLogisticsStatusData.Arg();
        arg.LogisticsStatus = status;
        arg.ObjectID = id;
        arg.DeliveryDate = deliveryDate;
        arg.ReceiveDate = receiveDate;
        result = salesOrderProxy.SetLogisticsStatus(arg,headers);
        return result;
    }
}
