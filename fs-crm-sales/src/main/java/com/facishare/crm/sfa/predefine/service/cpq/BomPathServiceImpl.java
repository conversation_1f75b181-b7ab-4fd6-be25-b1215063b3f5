package com.facishare.crm.sfa.predefine.service.cpq;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.utilities.constant.BomConstants;
import com.facishare.crm.sfa.utilities.util.SoCommonUtils;
import com.facishare.crmcommon.util.SearchUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.impl.search.Where;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * BomPath实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class BomPathServiceImpl implements BomPathService {
    @Autowired
    private ServiceFacade serviceFacade;

    @Autowired
    private ObjectDataServiceImpl objectDataService;

    @Override
    public List<IObjectData> findDataListByRootProductId(User user, String productId) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(1000);
        searchTemplateQuery.setNeedReturnCountNum(false);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, BomConstants.FIELD_ROOT_PRODUCT_ID, productId);
        searchTemplateQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user
                , Utils.BOM_PATH_API_NAME, searchTemplateQuery);

        return queryResult.getData();
    }

    @Override
    public List<IObjectData> findDataListByBomIds(User user, List<String> bomIds) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(1000);
        searchTemplateQuery.setNeedReturnCountNum(false);
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, BomConstants.FIELD_BOM_ID, bomIds);
        searchTemplateQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user
                , Utils.BOM_PATH_API_NAME, searchTemplateQuery);

        return queryResult.getData();
    }

    @Override
    public List<IObjectData> findBomRootDataList(User user, List<String> productIds) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(200);
        List<IFilter> filters = Lists.newArrayList();
        com.facishare.crm.sfa.utilities.common.convert.SearchUtil.fillFilterIn(filters, BomConstants.FIELD_PRODUCT_ID, productIds);
        com.facishare.crm.sfa.utilities.common.convert.SearchUtil.fillFilterIsNull(filters, BomConstants.FIELD_PARENT_BOM_ID);
        searchTemplateQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user
                , BomConstants.DESC_API_NAME, searchTemplateQuery);
        return queryResult.getData();
    }

    @Override
    public void bulkDeleteBomRootNode(User user, List<String> productIds) {
        List<IObjectData> dataList = findBomRootDataList(user, productIds);
        serviceFacade.bulkInvalidAndDeleteWithSuperPrivilege(dataList, user);
    }


    @Override
    public List<IObjectData> findBomPathAllByRootPath(User user, Map<String,String> idToPath) {
        if(CollectionUtils.empty(idToPath)){
            return Lists.newArrayList();
        }

        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(1000);
        List<Wheres> wheres = Lists.newArrayList();
        idToPath.forEach((bomId,path)->{
            IFilter filter = new Filter();
            filter.setFieldName(BomConstants.FIELD_PRODUCT_PATH);
            filter.setFieldValues(Lists.newArrayList(path));
            filter.setOperator(Operator.MATCH);

            IFilter filter1 = new Filter();
            filter1.setFieldName(BomConstants.FIELD_BOM_ID);
            filter1.setFieldValues(Lists.newArrayList(bomId));
            filter1.setOperator(Operator.EQ);

            Wheres where = new Wheres();
            where.setFilters(Lists.newArrayList(filter,filter1));
            where.setConnector(Where.CONN.OR.toString());
            wheres.add(where);
        });

        searchTemplateQuery.setWheres(wheres);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user
                , BomConstants.DESC_BOM_PATH_API_NAME, searchTemplateQuery);
        return queryResult.getData();
    }


    @Override
    public void deletedBomPathByRootProductId(User user, List<String> rootProductId) {
        if(CollectionUtils.empty(rootProductId)){
            return;
        }

        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(1000);

        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters,BomConstants.FIELD_ROOT_PRODUCT_ID,rootProductId);
        searchTemplateQuery.setFilters(filters);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user
                , BomConstants.DESC_BOM_PATH_API_NAME, searchTemplateQuery);

        if(CollectionUtils.notEmpty(queryResult.getData())){
            List<IObjectData> data = queryResult.getData();
            data.removeIf(o->(o.get(BomConstants.FIELD_PARENT_PRODUCT_ID,String.class) == null || Objects.equals(o.get(BomConstants.FIELD_PARENT_PRODUCT_ID,String.class),"")));
            serviceFacade.bulkDeleteWithInternalDescribe(data, user);
        }
    }


    @Override
    public List<IObjectData> findDuplicateBomPath(User user, List<IObjectData> bomPathList) {
        List<IObjectData> newBomPathList = Lists.newArrayList();
        if(CollectionUtils.empty(bomPathList)){
            return Lists.newArrayList();
        }

        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(1000);

        List<Wheres> wheres = Lists.newArrayList();
        bomPathList.forEach(data->{
            List<IFilter> filters = Lists.newArrayList();
            Wheres where = new Wheres();
            String bomPath = data.get("product_path",String.class);
            String rootProductId = data.get("root_product_id",String.class);
            String bomId = data.get("bom_id",String.class);
            if(StringUtils.isNotEmpty(bomPath) && StringUtils.isNotEmpty(rootProductId) && StringUtils.isNotEmpty(bomId)){
                SearchUtil.fillFilterMatch(filters,"product_path",Lists.newArrayList(bomPath));
                SearchUtil.fillFilterEq(filters,"root_product_id",Lists.newArrayList(rootProductId));
                SearchUtil.fillFilterEq(filters,"bom_id",Lists.newArrayList(bomId));
                where.setFilters(filters);
                where.setConnector(Where.CONN.OR.toString());
                wheres.add(where);
            }
        });
        searchTemplateQuery.setWheres(wheres);
        searchTemplateQuery.setNeedReturnQuote(false);
        searchTemplateQuery.setNeedReturnCountNum(false);
        searchTemplateQuery.setPermissionType(0);
        QueryResult<IObjectData> queryResult = null;
        try {
            // 不调用serviceFacade 因为方法内部会自动补充isDeleted字段，该字段在path表里是不存在的。
            // 直接改为调用元数据的方法
            queryResult = objectDataService.findBySearchQuery(user.getTenantId(), Utils.BOM_PATH_API_NAME, searchTemplateQuery, SoCommonUtils.getIActionContext(user));
        } catch (MetadataServiceException e) {
            log.error("findDuplicateBomPath findBySearchQuery tenantId->{}",user.getTenantId(), e);
        }

        List<IObjectData> duplicatePath = queryResult.getData();
        List<String> newStrList = Lists.newArrayList();
        duplicatePath.forEach(duplicatePathData->{
            String bomPath = duplicatePathData.get("product_path",String.class);
            String rootProductId = duplicatePathData.get("root_product_id",String.class);
            String bomId = duplicatePathData.get("bom_id",String.class);
            String newStr = rootProductId + bomId + bomPath;
            newStrList.add(newStr);
        });
        bomPathList.forEach(bomPathData->{
            String bomPath = bomPathData.get("product_path",String.class);
            String rootProductId = bomPathData.get("root_product_id",String.class);
            String bomId = bomPathData.get("bom_id",String.class);
            String newStr = rootProductId + bomId + bomPath;
            // 表示重复了，不进行插入
            if(!newStrList.contains(newStr)){
                newBomPathList.add(bomPathData);
            }
        });
        return newBomPathList;
    }


}
