package com.facishare.crm.sfa.predefine.action;

import com.facishare.paas.appframework.core.predef.action.StandardInsertImportTemplateAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.google.common.collect.Lists;

import java.util.List;

public class ContractInsertImportTemplateAction extends StandardInsertImportTemplateAction {
    private List<String> removeFields = Lists.newArrayList(
            "status", "life_status", "owner_department", "confirm_time"
    );

    @Override
    protected void customHeader(List<IFieldDescribe> headerFieldList) {
        super.customHeader(headerFieldList);
        headerFieldList.removeIf(f -> removeFields.contains(f.getApiName()));
    }
}
