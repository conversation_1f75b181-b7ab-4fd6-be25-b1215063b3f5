package com.facishare.crm.sfa.predefine.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.predefine.exception.SOBusinessException;
import com.facishare.crm.sfa.predefine.exception.SOErrorCode;
import com.facishare.crm.sfa.predefine.service.model.CrmMenuListArg;
import com.facishare.crm.sfa.predefine.version.VersionService;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.util.ProductUtils;
import com.facishare.crm.sfa.utilities.util.SOI18NKeyUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.ObjectDuplicatedSearchService;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.appframework.metadata.menu.MenuConstants;
import com.facishare.paas.appframework.metadata.menu.MenuConstants665;
import com.facishare.paas.appframework.metadata.menu.model.MenuItemConfigObject;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeProxy;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.privilege.UserRoleInfoService;
import com.facishare.paas.appframework.privilege.dto.AuthContext;
import com.facishare.paas.appframework.privilege.dto.CheckFunctionPrivilege;
import com.facishare.paas.metadata.api.DELETE_STATUS;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.*;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.predefine.service.CrmMenuAdminService.emptyElse;
import static com.facishare.crm.sfa.predefine.service.CrmMenuAdminService.getSpecialDisPlayName;
import static com.facishare.paas.appframework.core.model.RequestContext.Android_CLIENT_INFO_PREFIX;
import static com.facishare.paas.appframework.core.model.RequestContext.IOS_CLIENT_INFO_PREFIX;

/**
 * 个人菜单请求接口类
 * @IgnoreI18nFile
 */
@ServiceModule("crm_menu")
@Component
@Slf4j
/**
 * 菜单已迁移，请勿使用该内部的接口，接口将要下线
 */
public class CrmMenuService {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private CrmMenuAdminService crmMenuAdminService;
    @Autowired
    private CrmWorkBenchService crmWorkBenchService;
    @Autowired
    private FunctionPrivilegeService functionPrivilegeService;
    @Autowired
    private FunctionPrivilegeProxy functionPrivilegeProxy;
    @Autowired
    IObjectDescribeService objectDescribeService;
    @Autowired
    private CrmMenuActionService crmMenuActionService;
    @Autowired
    private ObjectDataServiceImpl objectDataService;
    @Autowired
    VersionService versionService;
    @Autowired
    UserRoleInfoService userRoleInfoService;
    @Autowired
    ConfigService configService;
    @Autowired
    ObjectDuplicatedSearchService objectDuplicatedSearchService;
    @Autowired
    private LicenseService licenseService;

    private FsGrayReleaseBiz menuGray = FsGrayRelease.getInstance("menu");


    /**
     * 终端/WEB端使用的菜单&菜单项大接口
     */
    @ServiceMethod("all_menu")
    public CrmMenuListArg.Result allMenu(ServiceContext context) {
        StopWatch stopWatch = StopWatch.create("CrmMenuService all_menu");
        User user = context.getUser();
        //1、获取所有的对象apiname集合、需要验证的权限对象数据
        stopWatch.lap("allMenu start");
        List<IObjectData> supportIObjectData = this.findAllMenuItemApiName(user);
        stopWatch.lap("findAllMenuItemApiName finished");
        Set<String> supportApiNameList = supportIObjectData.stream()
                .map(k -> k.get(MenuConstants.MenuItemField.REFERENCEAPINAME.getApiName(), String.class)).collect(Collectors.toSet());
        supportApiNameList.remove(null);
        ProductUtils.filterProductObject(context.getTenantId(), supportApiNameList);
        if (CollectionUtils.isEmpty(supportApiNameList)) {
            throw new ValidateException(I18N.text(SOI18NKeyUtils.SO_MENU_CRMMENUNOTEXIST));
        }
        //根据版本过滤到不支持的对象
        versionService.filterSupportObj(user.getTenantId(), supportApiNameList);
        stopWatch.lap("filterSupportObj finished");
        Map<String, MenuItemConfigObject> configMenuItemMap = crmMenuAdminService.getConfigMenuItemMapByTenantId(user.getTenantId());
        stopWatch.lap("getConfigMenuItemMapByTenantId finished");
        Set<String> needCheckPrivilegeApiNames = Sets.newHashSet();
        supportApiNameList.stream().forEach(apiName -> {
            MenuItemConfigObject configObject = configMenuItemMap.get(apiName);
            if (configObject != null) {
                if (configObject.getValidatePrivilege()) {
                    needCheckPrivilegeApiNames.add(apiName);
                }
            } else {
                //自定义对象
                needCheckPrivilegeApiNames.add(apiName);
            }
        });
        Map<String, IObjectDescribe> describeMap = crmMenuAdminService.findDescribeListByApiNamesWithoutFields(context.getUser(), new ArrayList(supportApiNameList), ObjectAction.VIEW_LIST.getActionCode());
        stopWatch.lap("findDescribeListByApiNamesWithoutFields finished");

        Map<String, Map<String, Boolean>> needCheckPrivilegeDescribe = batchFunPrivilegeCheck(user, new ArrayList<>(needCheckPrivilegeApiNames),
                Lists.newArrayList(ObjectAction.VIEW_LIST.getActionCode(), ObjectAction.CREATE.getActionCode()));
        stopWatch.lap("get allApiName funcPrivilege finished");

        CrmMenuListArg.Result result = CrmMenuListArg.Result.builder().build();

        //其他信息
        Map<String, Object> homePermissions = crmMenuAdminService.getHomePermissions(context, stopWatch);

        //增加是否开启合作夥伴
        String configPartnerIsOpen = configService.findTenantConfig(context.getUser(), "config_partner_open");
        stopWatch.lap("findTenantConfig finished");
        if (!StringUtils.isBlank(configPartnerIsOpen) && "open".equals(configPartnerIsOpen)) {
            homePermissions.put("IsOpenPartner", true);
        } else {
            homePermissions.put("IsOpenPartner", false);
        }

        result.setConfiginfo(homePermissions);

        List<IObjectData> userMenuList = findUserMenuList(user);
        //2、获取当前登录人可以查看的所有菜单列表,同时转成输出参数
        List<String> userMenuIdList = userMenuList.stream().map(k -> {
            CrmMenuListArg.Menu menu = new CrmMenuListArg.Menu();
            menu.setDisplayName(k.getName());
            menu.setId(k.getId());
            menu.setIsSystem(k.get(MenuConstants.MenuField.ISSTYTEM.getApiName(), Boolean.class));
            result.getMenus().add(menu);
            return k.getId();
        }).collect(Collectors.toList());

        //3、根据所有的菜单，获取所有的菜单项,建立菜单和菜单项的索引集合
        List<IObjectData> menuItemList = supportIObjectData.stream().filter(o -> userMenuIdList.contains(o.get("menu_id").toString())).collect(Collectors.toList());
        Map<String, List<CrmMenuListArg.MenuItem>> menuToMenuItemArgMap = Maps.newHashMap();
        String deviceType = getDeviceType(context);

        Map<String, Map<String, String>> iconPaths = crmMenuAdminService.getIconPaths(context);

        //4、依据配置的预设对象、对象功能权限和其他配置项，把所有菜单项转换成可输出的菜单项集合CrmMenuListArg.MenuItem
        menuItemList.stream()
                .map(menuItem -> {
                    String apiName = getMenuItemApiName(menuItem);
                    //获取配置项，如果配置项相应的对象为空，是自定义对象
                    MenuItemConfigObject menuItemConfigObject = configMenuItemMap.getOrDefault(apiName,
                            new MenuItemConfigObject(true, true, MenuConstants.DEVICE_TYPE_ALL));
                    ConvertMenuItemInfo convertMenuItemInfo = ConvertMenuItemInfo.builder()
                            .menuItem(menuItem).apiName(apiName)
                            .menuItemConfigObject(menuItemConfigObject)
                            .objectDescribe(describeMap.getOrDefault(apiName, new ObjectDescribe()))
                            .privilegeMap(needCheckPrivilegeDescribe.getOrDefault(apiName, Maps.newHashMap()))
                            .deviceType(deviceType)
                            .pid(menuItem.get(MenuConstants.MenuItemField.PID.getApiName(), String.class))
                            .type(Strings.isBlank(menuItem.get(MenuConstants.MenuItemField.TYPE.getApiName(), String.class)) ? "menu" : (menuItem.get("type", String.class)))
                            .is_hidden(Optional.ofNullable(menuItem.get(MenuConstants.MenuItemField.ISHIDDEN.getApiName(), Boolean.class)).orElse(Boolean.FALSE))
                            .build();
                    return convertMenuItemInfo;
                })
                .filter(k -> !getSpecialApiName().contains(k.getApiName()) || specialTreatment(user, k.getApiName()))
                //supportApiNameList为版本过滤后的apiName，过滤分组（分组中的apiName是null）
                .filter(k -> supportApiNameList.contains(k.apiName))
                //验证describe
                .filter(this::validateDescribe)
                //验证请求端类型
                .filter(k -> validateDeviceType(k, k.getDeviceType()))
                //验证功能权限
                .filter(k -> validatePrivilege(k, k.getPrivilegeMap()))
                .filter(k -> filterPoolAndHighSea(getMenuItemApiName(k.getMenuItem()),homePermissions))
                .forEach(convertMenuItemInfo -> {
                    //转换
                    CrmMenuListArg.MenuItem menuItemArg = convertMenuItemToArgMenuItem(convertMenuItemInfo);
                    //按版本获取自定义对象图标,<670 png;670:svg
                    menuItemArg = convertIconPath(menuItemArg, convertMenuItemInfo, iconPaths);
                    if (menuItemArg != null) {
                        menuToMenuItemArgMap.computeIfAbsent(menuItemArg.getMenuId(), k -> Lists.newArrayList());
                        menuToMenuItemArgMap.get(menuItemArg.getMenuId()).add(menuItemArg);
                    }
                });
        stopWatch.lap("convertMenuItemToArgMenuItem  list finished");

        //6、处理web有工作台的个人数据，合并工作台数据返回，终端没有个人工作台
        Set<String> needAddGroupTenantMenuId = Sets.newHashSet();
        if (MenuConstants.DEVICE_TYPE_WEB.equals(deviceType) || !RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_635)) {
            List<IObjectData> workBenchList = crmWorkBenchService.findUserMenuWorkBenchList(user, userMenuIdList);
            needAddGroupTenantMenuId = fillWorkBenchToMenu(workBenchList, menuToMenuItemArgMap);
            stopWatch.lap("fillWorkBenchToMenu  list finished");

            //给租户级菜单加上分组
            if (!needAddGroupTenantMenuId.isEmpty()) {
                fillGroupToMenu(needAddGroupTenantMenuId, menuToMenuItemArgMap, supportIObjectData);
            }
        }
        //6.3.5之后终端加上CRM提醒，查重工具，附近客户，销售记录，6.3.5之前要删除报表和数据看板
        if (RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_635)) {
            menuToMenuItemArgMap.forEach((menuId, menuItems) -> {
                menuItems.removeIf(o -> "Report".equals(o.getReferenceApiname()) || "DataBoard".equals(o.getReferenceApiname()));
            });
        }
        if (MenuConstants.DEVICE_TYPE_MOBILE.equals(deviceType) && !RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_635)) {
            List<CrmMenuListArg.MenuItem> mobileMenuItemList = getMobileMenuItemList(context);
            menuToMenuItemArgMap.forEach((menuId, menuItems) -> {
                mobileMenuItemList.forEach(o -> o.setMenuId(menuId));
                menuItems.addAll(0, deepCopyList(mobileMenuItemList));
                Boolean addNearByCustomerMenuItem = Boolean.FALSE;
                String pid = null;
                Integer number = 10000;
                for (CrmMenuListArg.MenuItem menuItem : menuItems) {
                    if ("AccountObj".equals(Optional.ofNullable(menuItem.getReferenceApiname()).orElse("false"))
                            && !Optional.ofNullable(menuItem.getIsHidden()).orElse(false)) {
                        addNearByCustomerMenuItem = true;
                        number = menuItem.getNumber();
                        pid = menuItem.getPid();
                        break;
                    }
                }
                if (addNearByCustomerMenuItem) {
                    CrmMenuListArg.MenuItem nearByCustomerMenuItem = getNearByCustomerMenuItem(context);
                    if(ObjectUtils.isNotEmpty(nearByCustomerMenuItem)){
                        if (pid != null) {
                            nearByCustomerMenuItem.setPid(pid);
                        }
                        nearByCustomerMenuItem.setNumber(number);
                        menuItems.add(nearByCustomerMenuItem);
                    }
                }
            });
        }

        //7、把上一步处理好的菜单和菜单项的索引引入到输出menu中,只有web端返回
        String userMenuCurrentId = crmWorkBenchService.getUserMenuCurrentId(user);
        if (result.getMenus().size() > 1 && crmMenuAdminService.queryTenantConfig(context).isResult()) {
            userMenuIdList.removeIf(menuId ->
                    Objects.equals(menuId, result.getMenus().stream().filter(CrmMenuListArg.Menu::getIsSystem).map(CrmMenuListArg.Menu::getId).findFirst().orElse("")));
            result.getMenus().removeIf(CrmMenuListArg.Menu::getIsSystem);
            if ((StringUtils.isEmpty(userMenuCurrentId) || !userMenuIdList.contains(userMenuCurrentId))) {
                result.getMenus().stream().findFirst().get().setIsSystem(Boolean.TRUE);
            }
        }
        result.getMenus().forEach(menu -> {
            menu.setItems(menuToMenuItemArgMap.get(menu.getId()));
            //登录人最近一次选择默认的crm菜单
            if ((StringUtils.isEmpty(userMenuCurrentId) || !userMenuIdList.contains(userMenuCurrentId))) {
                menu.setIsCurrent(menu.getIsSystem());
            } else {
                menu.setIsCurrent(menu.getId().equals(userMenuCurrentId));
            }
        });
        stopWatch.lap("result menu finished");
        
        stopWatch.logSlow(5000L);
        return result;
    }

    private void fillGroupToMenu(Set<String> needAddGroupTenantMenuId,
                                 Map<String, List<CrmMenuListArg.MenuItem>> menuToMenuItemArgMap,
                                 List<IObjectData> supportIObjectData) {
        supportIObjectData.stream().filter(o -> needAddGroupTenantMenuId.contains(o.get(MenuConstants.MenuItemField.MENUID.getApiName(), String.class)))
                .forEach(o -> {
                    if (MenuConstants.MenuItemField.TYPE_GROUP.getApiName().equals(o.get(MenuConstants.MenuItemField.TYPE.getApiName()))) {
                        CrmMenuListArg.MenuItem menuItemArg = CrmMenuListArg.MenuItem.builder().build();
                        menuItemArg.setIsHidden(Optional.ofNullable(o.get(MenuConstants.MenuItemField.ISHIDDEN.getApiName(), Boolean.class)).orElse(Boolean.FALSE));
                        menuItemArg.setNumber(o.get(MenuConstants.MenuItemField.NUMBER.getApiName(), Integer.class));
                        menuItemArg.setId(o.get("id", String.class));
                        menuItemArg.setDisplayName(o.get(MenuConstants.MenuItemField.DISPLAYNAME.getApiName(), String.class));
                        menuItemArg.setMenuId(o.get(MenuConstants.MenuItemField.MENUID.getApiName(), String.class));
                        //分组类型
                        menuItemArg.setType(MenuConstants.MenuItemField.TYPE_GROUP.getApiName());
                        menuToMenuItemArgMap.get(o.get(MenuConstants.MenuItemField.MENUID.getApiName(), String.class)).add(menuItemArg);
                    }
                });

    }

    private String getMenuItemApiName(IObjectData menuItem) {
        return menuItem.get(MenuConstants.MenuItemField.REFERENCEAPINAME.getApiName(), String.class);
    }

    //是否可以查看服务管理菜单
    public Boolean isShowServiceManager(User user) {
        List<String> allRoleCodeList = userRoleInfoService.getUserRole(user);
        return allRoleCodeList.contains("00000000000000000000000000000006") || allRoleCodeList.contains("00000000000000000000000000000010");
    }

    //是否可以查看报表日志
    public Boolean isShowBI(String apiName, User user) {
        List<String> allRoleCodeList = userRoleInfoService.getUserRole(user);
        Boolean b;
        switch (apiName) {
            case "ReportLog":
                b = allRoleCodeList.contains("00000000000000000000000000000006") || allRoleCodeList.contains("00000000000000000000000000000017");
                break;
            case "StatThemeMgr":
                b = allRoleCodeList.contains("00000000000000000000000000000006") || allRoleCodeList.contains("00000000000000000000000000000017");
                break;
            default:
                b = Boolean.FALSE;
                break;
        }
        return b;
    }

    //是否可以查看报表
    public Boolean isShowReport(User user) {
        CheckFunctionPrivilege.Arg arg = CheckFunctionPrivilege.Arg.builder()
                .authContext(AuthContext.builder().appId("CRM").userId(user.getUserId()).tenantId(user.getTenantId()).build())
                .funcCodeLists(Lists.newArrayList(MenuConstants.showReportByFunctionNo.split(","))).build();
        CheckFunctionPrivilege.Result privilegeResult = functionPrivilegeProxy.checkFunctionPrivilege(arg, FunctionPrivilegeProxy.HeaderUtil.buildHeaders(user.getTenantId()));
        return privilegeResult.getResult().values().contains(Boolean.TRUE);
    }

    //查看bi的权限:是否可以查看报表权限管理,是否可以查看订阅管理,是否可以查看报表
    // TODO: 2018/9/5
    public Boolean isShowBI(User user, String apiName) {
        String functionNos;
        apiName = Optional.ofNullable(apiName).orElse("");
        switch (apiName) {
            case "Report":
                functionNos = MenuConstants.showReportByFunctionNo;
                break;
            case "ReportPermissionMgr":
                functionNos = "100102,100202,100302,100402,100502,100602,100702,109002,109902,100802";
                break;
            case "SubscMgr":
                functionNos = "100107,100207,100307,100407,100507,100607,100707,109007,109907,100807";
                break;
            default:
                functionNos = "";
                break;
        }
        CheckFunctionPrivilege.Arg arg = CheckFunctionPrivilege.Arg.builder()
                .authContext(AuthContext.builder().appId("CRM").userId(user.getUserId()).tenantId(user.getTenantId()).build())
                .funcCodeLists(Lists.newArrayList(functionNos.split(","))).build();
        CheckFunctionPrivilege.Result privilegeResult = functionPrivilegeProxy.checkFunctionPrivilege(arg, FunctionPrivilegeProxy.HeaderUtil.buildHeaders(user.getTenantId()));
        return privilegeResult.getResult().values().contains(Boolean.TRUE);
    }

    /**
     * 获取请求端类型，web或终端
     */
    private String getDeviceType(ServiceContext context) {
        String clientInfo = context.getRequestContext().getClientInfo();
        if (clientInfo != null && (clientInfo.startsWith(Android_CLIENT_INFO_PREFIX) ||
                clientInfo.startsWith(IOS_CLIENT_INFO_PREFIX))) {
            return MenuConstants.DEVICE_TYPE_MOBILE;
        }
        return MenuConstants.DEVICE_TYPE_WEB;
    }


    /**
     * 把工作台数据信息 合并到输出的arg菜单项中
     */
    private Set<String> fillWorkBenchToMenu(List<IObjectData> workBenchList, Map<String, List<CrmMenuListArg.MenuItem>> menuToMenuItemArgMap) {
        if (CollectionUtils.isEmpty(workBenchList)) {
            Set<String> needAddGroupTenantIds = Sets.newHashSet();
            menuToMenuItemArgMap.forEach((k, v) -> needAddGroupTenantIds.add(k));
            return needAddGroupTenantIds;
        }
        //key:菜单id value中：key是菜单项id，value是工作台节点数据
        Map<String, Map<String, IObjectData>> workBenchMenuAndItemMap = Maps.newHashMap();
        //1、建立工作台菜单项id和工作台节点信息的索引
        //2、把工作台中是分组类型的合并到menuToMenuItemArgMap输出对象中
        workBenchList.stream().filter(k -> menuToMenuItemArgMap.get(k.get(MenuConstants.MenuWorkBenchField.MENUID.getApiName(), String.class)) != null)
                .forEach(workbench -> {
                    String menuId = workbench.get(MenuConstants.MenuWorkBenchField.MENUID.getApiName(), String.class);
                    String menuItemId = workbench.get(MenuConstants.MenuWorkBenchField.MENUITEMID.getApiName(), String.class);
                    workBenchMenuAndItemMap.computeIfAbsent(menuId, k -> Maps.newHashMap());
                    Map<String, IObjectData> menuItemMap = workBenchMenuAndItemMap.get(menuId);

                    if (StringUtils.isNotBlank(menuItemId)) {
                        menuItemMap.put(menuItemId, workbench);
                    } else {
                        //如果menuItem为空,则说明是分组类型
                        CrmMenuListArg.MenuItem menuItemArg = CrmMenuListArg.MenuItem.builder().build();
                        menuItemArg.setIsHidden(workbench.get(MenuConstants.MenuWorkBenchField.ISHIDDEN.getApiName(), Boolean.class));
                        menuItemArg.setNumber(workbench.get(MenuConstants.MenuWorkBenchField.NUMBER.getApiName(), Integer.class));
                        menuItemArg.setId(workbench.getId());
                        menuItemArg.setDisplayName(workbench.get(MenuConstants.MenuWorkBenchField.DISPLAYNAME.getApiName(), String.class));
                        menuItemArg.setMenuId(menuId);
                        //分组类型
                        menuItemArg.setType(MenuConstants.MenuWorkBenchField.TYPE_GROUP.getApiName());
                        menuToMenuItemArgMap.get(menuId).add(menuItemArg);
                    }
                });
        //3、把工作台节点的信息插入到输出集合中
        Set<String> needAddBiGroup = Sets.newHashSet();
        menuToMenuItemArgMap.forEach((menuId, menuItemArgList) -> menuItemArgList.forEach(menuItemArg -> {
            //过滤分组
            Map<String, IObjectData> workBenchMenuItemMap = workBenchMenuAndItemMap.get(menuItemArg.getMenuId());
            if (workBenchMenuItemMap != null && !menuItemArg.getType().equals(MenuConstants.MenuWorkBenchField.TYPE_GROUP.getApiName())) {
                String menuItemId = menuItemArg.getMenuItemId();
                IObjectData workbench = workBenchMenuItemMap.get(menuItemId);
                if (workbench != null) {
                    menuItemArg.setIsHidden(workbench.get(MenuConstants.MenuWorkBenchField.ISHIDDEN.getApiName(), Boolean.class));
                    menuItemArg.setNumber(workbench.get(MenuConstants.MenuWorkBenchField.NUMBER.getApiName(), Integer.class));
                    menuItemArg.setId(workbench.getId());
                    menuItemArg.setPid(workbench.get(MenuConstants.MenuWorkBenchField.PID.getApiName(), String.class));
                } else {
                    Integer number;
                    String pid = null;
                    switch (menuItemArg.getReferenceApiname()) {
                        case "DataBoard":
                            number = -5;
                            break;
                        case "Report": {
                            number = -4;
                            pid = "123";
                            needAddBiGroup.add(menuId);
                        }
                        break;
                        case "ReportPermissionMgr": {
                            number = -3;
                            pid = "123";
                            needAddBiGroup.add(menuId);
                        }
                        break;
                        case "SubscMgr": {
                            number = -2;
                            pid = "123";
                            needAddBiGroup.add(menuId);
                        }
                        break;
                        case "ReportLog": {
                            number = -1;
                            pid = "123";
                            needAddBiGroup.add(menuId);
                        }
                        break;
                        default:
                            number = menuItemArg.getNumber() + 10000;
                            break;
                    }
                    //如果工作台节点不存在该菜单项，则此菜单项可能是后来新建的菜单项，需要改变order排在最后面,直接在原来order基础的上加10000保证有序的在最后面
                    menuItemArg.setNumber(number);
                    menuItemArg.setIsHidden(false);
                    menuItemArg.setPid(pid);
                }
            }
        }));
        if (!needAddBiGroup.isEmpty()) {
            menuToMenuItemArgMap.forEach((menuId, menuItemArgList) -> {
                if (needAddBiGroup.contains(menuId)) {
                    CrmMenuListArg.MenuItem menuItemArg = CrmMenuListArg.MenuItem.builder().build();
                    menuItemArg.setIsHidden(false);
                    menuItemArg.setNumber(-4);
                    menuItemArg.setId("123");
                    menuItemArg.setDisplayName("数据分析");
                    menuItemArg.setMenuId(menuId);
                    //分组类型
                    menuItemArg.setType(MenuConstants.MenuWorkBenchField.TYPE_GROUP.getApiName());
                    menuItemArgList.add(menuItemArg);
                }
            });
        }
        Set<String> allMenuIds = Sets.newHashSet(menuToMenuItemArgMap.keySet());
        Set<String> workMenuIds = workBenchMenuAndItemMap.keySet();
        allMenuIds.removeAll(workMenuIds);
        return allMenuIds;
    }

    private Boolean validateDescribe(ConvertMenuItemInfo convertMenuItemInfo) {
        IObjectDescribe objectDescribe = convertMenuItemInfo.getObjectDescribe();
        //如果配置项相应的对象为空，是自定义对象
        MenuItemConfigObject menuItemConfigObject = convertMenuItemInfo.getMenuItemConfigObject();
        //预制的crm对象，并且配置中不存在该对象时，则过滤
        if (IObjectDescribe.DEFINE_TYPE_PACKAGE.equals(objectDescribe.getDefineType()) && StringUtils.isEmpty(menuItemConfigObject.getApiName())) {
            return false;
        }
        //自定义对象默认校验describe
        Boolean needValidateDescribe = menuItemConfigObject == null ? true : menuItemConfigObject.getValidateDescribe();
        //验证describe
        if (needValidateDescribe && (objectDescribe.get(IObjectDescribe.IS_ACTIVE, Boolean.class) == null || !objectDescribe.isActive())) {
            return false;
        }
        return true;
    }

    private Boolean validatePrivilege(ConvertMenuItemInfo convertMenuItemInfo, Map<String, Boolean> privilegeMap) {
        MenuItemConfigObject menuItemConfigObject = convertMenuItemInfo.getMenuItemConfigObject();
        //自定义对象默认校验权限
        Boolean needValidatePrivilege = menuItemConfigObject == null ? true : menuItemConfigObject.getValidatePrivilege();
        //判断权限
        if (needValidatePrivilege && !privilegeMap.get(ObjectAction.VIEW_LIST.getActionCode())) {
            return false;
        }
        return true;
    }

    private Boolean validateDeviceType(ConvertMenuItemInfo convertMenuItemInfo, String deviceType) {
        MenuItemConfigObject menuItemConfigObject = convertMenuItemInfo.getMenuItemConfigObject();
        //判断请求端类型,判断是否需要显示菜单
        String itemConfigDeviceType = menuItemConfigObject == null ? MenuConstants.DEVICE_TYPE_ALL : menuItemConfigObject.getDeviceType();
        if (!MenuConstants.DEVICE_TYPE_ALL.equals(itemConfigDeviceType) && !deviceType.equals(itemConfigDeviceType)) {
            return false;
        }
        return true;
    }

    private Boolean filterPoolAndHighSea(String apiName, Map<String, Object> homePermissions){

        if ("LeadsPoolObj".equals(apiName)){
            return filterPoolSea(homePermissions, "SalesCluePoolShortInfoList", "ShowSalesCluePool");
        }else if ("HighSeasObj".equals(apiName)){
            return filterPoolSea(homePermissions, "HighSeasList", "ShowHighSeas");
        }

        return true;
    }

    private boolean filterPoolSea(Map<String, Object> homePermissions, String webListName, String terminalName){
        Object webListObject = homePermissions.get(webListName);
        if (webListObject != null){
            String webListObjectJson = JSONArray.toJSONString(webListObject);
            JSONArray webList = JSONArray.parseArray(webListObjectJson);
            if (webList == null || webList.size() == 0){
                return false;
            }
        }
        Object terminalNameObject = homePermissions.get(terminalName);
        if (terminalNameObject != null){
            Boolean show = (Boolean) terminalNameObject;
            if (!show){
                return false;
            }
        }
        return true;
    }

    /**
     * 把数据库中查出的菜单项转成端可用的arg menuItem数据 填充项：名称、图标、url、排序号、类型等 依赖：配置中心配置的预设对象配置、元数据底层数据
     */
    public CrmMenuListArg.MenuItem convertMenuItemToArgMenuItem(ConvertMenuItemInfo convertMenuItemInfo) {
        //需要使用的数据
        String apiName = convertMenuItemInfo.getApiName();
        IObjectDescribe objectDescribe = convertMenuItemInfo.getObjectDescribe();
        MenuItemConfigObject menuItemConfigObject = convertMenuItemInfo.getMenuItemConfigObject();
        IObjectData menuItem = convertMenuItemInfo.getMenuItem();
        Map<String, Boolean> privilegeMap = convertMenuItemInfo.getPrivilegeMap();
        String deviceType = convertMenuItemInfo.getDeviceType();
        //核心处理
        CrmMenuListArg.MenuItem menuItemArg = CrmMenuListArg.MenuItem.builder()
                .displayName(getSpecialDisPlayName(apiName, objectDescribe.getDisplayName(), menuItemConfigObject.getDisplayName()))
                .iconIndex((Integer) emptyElse(menuItemConfigObject.getIconIndex(), objectDescribe.getIconIndex()))
                .iconPathHome(String.valueOf(emptyElse(menuItemConfigObject.getIconPathHome(), objectDescribe.getIconPath())))
                .iconPathMenu(String.valueOf(emptyElse(menuItemConfigObject.getIconPathMenu(), objectDescribe.getIconPath())))
                .menuItemId(menuItem.get("id").toString())
                .referenceApiname(apiName)
                .number(menuItem.get(MenuConstants.MenuItemField.NUMBER.getApiName(), Integer.class))
                .type(convertMenuItemInfo.getType())
                .menuId(menuItem.get(MenuConstants.MenuItemField.MENUID.getApiName()).toString())
                .url(menuItemConfigObject.getUrl())
                .pid(convertMenuItemInfo.getPid())
                .isHidden(convertMenuItemInfo.getIs_hidden())
                .useDefaultUrl(menuItemConfigObject.getUseDefaultUrl())
                .build();
        //填充权限标识
        menuItemArg.setPrivilegeAction(Lists.newArrayList(ObjectAction.VIEW_LIST.getActionCode()));
        //终端下发不同的action，支持h5、weex等
        if (MenuConstants.DEVICE_TYPE_MOBILE.equals(deviceType) && menuItemConfigObject.getMobileConfig() != null) {
            MenuItemConfigObject.MobileConfig mobileConfig = menuItemConfigObject.getMobileConfig();
            CrmMenuListArg.MobileConfig mobileConfigArg = new CrmMenuListArg.MobileConfig();
            mobileConfigArg.setListAction(mobileConfig.getMobileListAction());
            mobileConfigArg.setAddAction(mobileConfig.getMobileAddAction());
            menuItemArg.setMobileConfig(mobileConfigArg);
        }
        //验证是否需要赋予add权限，比如终端不需要价目表的Add权限（快速新建）
        String quickAddExcludeApiConfig = MenuConstants.quickAddExcludeDevice.getOrDefault(apiName, MenuConstants.DEVICE_TYPE_ALL);
        if (!MenuConstants.DEVICE_TYPE_ALL.equals(quickAddExcludeApiConfig) && !deviceType.equals(quickAddExcludeApiConfig)) {
            return menuItemArg;
        }
        Boolean addAction = privilegeMap.get(ObjectAction.CREATE.getActionCode());
        if (Objects.nonNull(addAction) && addAction) {
            if (ObjectUtils.isNotEmpty(objectDescribe) && ObjectDescribeExt.of(objectDescribe).isHideButton()) {

            } else {
                menuItemArg.getPrivilegeAction().add(ObjectAction.CREATE.getActionCode());
            }
        }
        return menuItemArg;
    }

    private CrmMenuListArg.MenuItem convertIconPath(CrmMenuListArg.MenuItem menuItemArg, ConvertMenuItemInfo convertMenuItemInfo, Map<String, Map<String, String>> iconPaths) {
        //自定义对象
        if (ObjectUtils.isNotEmpty(convertMenuItemInfo.getObjectDescribe())
                && ObjectUtils.isNotEmpty(convertMenuItemInfo.getObjectDescribe().getDefineType())
                && convertMenuItemInfo.getObjectDescribe().getDefineType().equals(IObjectDescribe.DEFINE_TYPE_CUSTOM)) {
            Integer index = convertMenuItemInfo.getObjectDescribe().getIconIndex();
            if (ObjectUtils.isEmpty(index)) {
                return menuItemArg;
            }
            Map<String, String> iconPath = iconPaths.get(String.valueOf(index));
            menuItemArg.setIconPathHome(MapUtils.getString(iconPath,"iconHomePath",""));
            menuItemArg.setIconPathMenu(MapUtils.getString(iconPath,"iconMenuPath",""));
        } else {
            Map<String, String> iconPath = iconPaths.get(convertMenuItemInfo.getApiName());
            menuItemArg.setIconPathHome(MapUtils.getString(iconPath,"iconHomePath",""));
            menuItemArg.setIconPathMenu(MapUtils.getString(iconPath,"iconMenuPath",""));
        }
        return menuItemArg;
    }


    /**
     * 个人CRM菜单列表接口
     */
    @ServiceMethod("menu_list")
    public List<IObjectData> objectList(ServiceContext context) {
        return this.findUserMenuList(context.getUser());
    }

    /**
     * 查询可用的菜单列表
     */
    private List<IObjectData> findUserMenuList(User user) {
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(1000);
        //用户的角色
        List<String> roleIdList = serviceFacade.getUserRole(user);
        if (CollectionUtils.isEmpty(roleIdList)) {
            log.warn("findUserMenuList user role not exist,tenantId {},userId {}", user.getTenantId(), user.getUserId());
            throw new ValidateException(I18N.text(SOI18NKeyUtils.SO_MENU_ROLENOTEXIST));
        }
        List<IFilter> filters = searchQuery.getFilters();
        SearchUtil.fillFilterIn(filters, MenuConstants.RoleSourceField.ROLE_ID.getApiName(), roleIdList);
        //查询菜单资源类型的
        SearchUtil.fillFilterEq(filters, MenuConstants.RoleSourceField.SOURCETYPE.getApiName(), MenuConstants.RoleSourceField.SOURCETYPE_MENU.getApiName());
        QueryResult<IObjectData> roleSourceList = serviceFacade.findBySearchQuery(user, MenuConstants.ROLE_SOURCE_API_NAME, searchQuery);
        List<String> menuIdsList = roleSourceList.getData().stream()
                .map(k -> k.get(MenuConstants.RoleSourceField.SOURCEID.getApiName(), String.class)).collect(Collectors.toList());
        List<IObjectData> menuList = Lists.newArrayList();
        //默认增加当前企业默认的crm菜单
        IObjectData defaultCrmMenu = crmMenuAdminService.findDefaultCrmMenu(user);
        if (Objects.isNull(defaultCrmMenu)) {
            throw new SOBusinessException(SOErrorCode.SO_MENU_CRMMENUNOTFIND_ERROR);
        }
        menuList.add(defaultCrmMenu);
        if (CollectionUtils.isNotEmpty(menuIdsList)) {
            menuList.addAll(this.findMenuByIds(user, menuIdsList));
        }
        return menuList;
    }

    /**
     * 根据menuId查找启用状态的菜单
     */
    private List<IObjectData> findMenuByIds(User user, List menuIdsList) {
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(menuIdsList.size());
        List<IFilter> filters = searchQuery.getFilters();
        SearchUtil.fillFilterIn(filters, IObjectData.ID, menuIdsList);
        //启用的
        SearchUtil.fillFilterEq(filters, MenuConstants.MenuField.ACTIVESTATUS.getApiName(), MenuConstants.MenuField.ACTIVESTATUS_ON.getApiName());
        searchQuery.setOrders(Lists.newArrayList(new OrderBy("create_time", true)));
        crmMenuAdminService.fillMenuQueryAddOrder(searchQuery);
        QueryResult<IObjectData> menuDatalist = serviceFacade.findBySearchQuery(user, MenuConstants.MENU_API_NAME, searchQuery);
        return menuDatalist.getData();
    }

    public List<IObjectData> findAllMenuItemApiName(User user) {
        String sql = String.format("select * from menu_item where tenant_id='%s' and is_deleted='%s' and is_hidden=false order by number", user.getTenantId()
                , DELETE_STATUS.NORMAL.getValue());
        try {
            QueryResult<IObjectData> queryResult = objectDataService.findBySql(sql, user.getTenantId(), MenuConstants.MENU_ITEM_API_NAME);
            return queryResult.getData();
        } catch (MetadataServiceException e) {
            log.error("findAllMenuApiName error,tanantId {}", user.getTenantId(), e);
            throw new MetaDataException(e.getMessage());
        }
    }

    public List<IObjectData> findMenuItemByMenuId(User user,String menuId) {
        String sql = String.format("select * from menu_item where tenant_id='%s' and is_deleted='%s' and menu_id='%s' and is_hidden=false order by number", user.getTenantId()
                , DELETE_STATUS.NORMAL.getValue() ,menuId);
        try {
            QueryResult<IObjectData> queryResult = objectDataService.findBySql(sql, user.getTenantId(), MenuConstants.MENU_ITEM_API_NAME);
            return queryResult.getData();
        } catch (MetadataServiceException e) {
            log.error("findAllMenuApiName error,tanantId {}", user.getTenantId(), e);
            throw new MetaDataException(e.getMessage());
        }
    }

    public List<IObjectData> findAllMenu(User user) {
        String sql = String.format("select * from menu where tenant_id='%s' and is_deleted='%s'", user.getTenantId()
                , DELETE_STATUS.NORMAL.getValue());
        try {
            QueryResult<IObjectData> queryResult = objectDataService.findBySql(sql, user.getTenantId(), MenuConstants.MENU_API_NAME);
            return queryResult.getData();
        } catch (MetadataServiceException e) {
            log.error("findAllMenu error,tanantId {}", user.getTenantId(), e);
            throw new MetaDataException(e.getMessage());
        }
    }

    @ServiceMethod("check_version_privilege")
    public Map<String, Map<String, Boolean>> batchFunPrivilegeCheck(ServiceContext context, Set<String> apiNames) {
        ProductUtils.filterProductObject(context.getTenantId(), apiNames);
        //过滤分版
        versionService.filterSupportObj(context.getTenantId(), apiNames);
        //获取权限
        Map<String, Map<String, Boolean>> checkPrivilege = functionPrivilegeService.batchFunPrivilegeCheck(context.getUser(), new ArrayList<>(apiNames),
                Lists.newArrayList(ObjectAction.VIEW_LIST.getActionCode(), ObjectAction.CREATE.getActionCode()));

        return checkPrivilege;
    }


    private Map<String, Map<String, Boolean>> batchFunPrivilegeCheck(User user, List apiNames, List actionCodes) {
        Map<String, Map<String, Boolean>> map = functionPrivilegeService.batchFunPrivilegeCheck(user, apiNames, actionCodes);
        JSONObject privilegeActionListDepends = MenuConstants.privilegeActionListDepends;
        privilegeActionListDepends.keySet().forEach(key -> {
            Optional.ofNullable(map.get(key)).filter(k -> k.get(ObjectAction.VIEW_LIST.getActionCode())).ifPresent(m -> {
                JSONArray needDependsApiNames = privilegeActionListDepends.getJSONArray(key);
                Optional.ofNullable(needDependsApiNames).get().forEach(needDependsApiName -> {
                    Map<String, Boolean> newHashMap = Maps.newHashMap();
                    newHashMap.put(ObjectAction.VIEW_LIST.getActionCode(), true);
                    map.put(needDependsApiName.toString(), newHashMap);
                });
            });
        });
        return map;
    }

    private Boolean specialTreatment(User user, String apiName) {
        Boolean isShow;
        switch (apiName) {
            case "CrmServiceManager":
                isShow = isShowServiceManager(user);
                break;
            case "Report":
                isShow = isShowBI(user, apiName);
                break;
            case "ReportPermissionMgr":
                isShow = isShowBI(user, apiName);
                break;
            case "SubscMgr":
                isShow = isShowBI(user, apiName);
                break;
            case "ReportLog":
                isShow = isShowBI(apiName, user);
                break;
            case "StatThemeMgr":
                isShow = isShowBI(apiName, user);
                break;
            default:
                isShow = false;
        }
        return isShow;
    }

    private List<String> getSpecialApiName() {
        return Lists.newArrayList("Report", "CrmServiceManager", "ReportPermissionMgr", "ReportLog", "SubscMgr", "StatThemeMgr");
    }

    private List<CrmMenuListArg.MenuItem> getMobileMenuItemList(ServiceContext context) {
        List<CrmMenuListArg.MenuItem> mobileSpecialMenuItemList = getMobileSpecialMenuItemList(context);
        if (CollectionUtils.isNotEmpty(mobileSpecialMenuItemList)) {
            mobileSpecialMenuItemList = mobileSpecialMenuItemList.stream().filter(x -> !StringUtils.equals(x.getReferenceApiname(), "NearByCustomer")).collect(Collectors.toList());
        }
        return mobileSpecialMenuItemList;
    }

    private CrmMenuListArg.MenuItem getNearByCustomerMenuItem(ServiceContext context) {
        return getMobileSpecialMenuItemByApiName(context,"NearByCustomer");
    }

    public List<CrmMenuListArg.MenuItem> getMobileSpecialMenuItemList(ServiceContext context) {
        List<CrmMenuListArg.MenuItem> menuItems;
        if (RequestUtil.isMobileRequestBeforeVersion("670000")) {
            menuItems = JSON.parseArray(MenuConstants665.specialConfigMenuItem, CrmMenuListArg.MenuItem.class);
        }else {
            menuItems = JSON.parseArray(MenuConstants.specialConfigMenuItem, CrmMenuListArg.MenuItem.class);
        }
        if (!RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_650) && menuGray.isAllow("SaleRecord_rule", context.getUser().getTenantId())) {
            menuItems = menuItems.stream().filter(x -> ObjectUtils.notEqual(x.getReferenceApiname(), "SaleRecord")).collect(Collectors.toList());
        }
        menuItems.forEach(o -> {
            if (Strings.isNotBlank(getSpecialDisPlayName(o.getReferenceApiname()))) {
                o.setDisplayName(getSpecialDisPlayName(o.getReferenceApiname()));
            }
        });
        return menuItems;
    }

    public CrmMenuListArg.MenuItem getMobileSpecialMenuItemByApiName(ServiceContext context,String apiName) {
        List<CrmMenuListArg.MenuItem> menuItems = getMobileSpecialMenuItemList(context);
        for (int i = 0; i < menuItems.size(); i++) {
            CrmMenuListArg.MenuItem menuItem = menuItems.get(i);
            if (StringUtils.equals(apiName,menuItem.getReferenceApiname())) {
                return menuItem;
            }
        }
        return null;
    }


    public static <T> List<T> deepCopyList(List<T> src) {
        List<T> dest = null;
        try {
            ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
            ObjectOutputStream out = new ObjectOutputStream(byteOut);
            out.writeObject(src);
            ByteArrayInputStream byteIn = new ByteArrayInputStream(byteOut.toByteArray());
            ObjectInputStream in = new ObjectInputStream(byteIn);
            dest = (List<T>) in.readObject();
        } catch (IOException | ClassNotFoundException ignored) {

        }
        return dest;
    }

    public List<IObjectData> deleteAllMenu(User toUser) {
        String sql = String.format("delete from menu where tenant_id='%s'", toUser.getTenantId());
        try {
            QueryResult<IObjectData> queryResult = objectDataService.findBySql(sql, toUser.getTenantId(), MenuConstants.MENU_API_NAME);
            return queryResult.getData();
        } catch (MetadataServiceException e) {
            log.error("deleteAllMenu error,tanantId {}", toUser.getTenantId(), e);
            throw new MetaDataException(e.getMessage());
        }
    }

    public List<IObjectData> deleteAllMenuItem(User toUser) {
        String sql = String.format("delete  from menu_item where tenant_id='%s'", toUser.getTenantId());
        try {
            QueryResult<IObjectData> queryResult = objectDataService.findBySql(sql, toUser.getTenantId(), MenuConstants.MENU_ITEM_API_NAME);
            return queryResult.getData();
        } catch (MetadataServiceException e) {
            log.error("deleteAllMenuItem error,tanantId {}", toUser.getTenantId(), e);
            throw new MetaDataException(e.getMessage());
        }
    }

    public List<IObjectData>  deleteAllWorkbenchMenu(User toUser) {
        String sql = String.format("delete  from menu_workbench where tenant_id='%s'", toUser.getTenantId());
        try {
            QueryResult<IObjectData> queryResult = objectDataService.findBySql(sql, toUser.getTenantId(), MenuConstants.MENU_WORKBENCH_API_NAME);
            return queryResult.getData();
        } catch (MetadataServiceException e) {
            log.error("deleteAllWorkbenchMenu error,tanantId {}", toUser.getTenantId(), e);
            throw new MetaDataException(e.getMessage());
        }
    }

    @Builder
    @Data
    public static class ConvertMenuItemInfo {
        private String apiName;
        private MenuItemConfigObject menuItemConfigObject;
        private IObjectData menuItem;
        private IObjectDescribe objectDescribe;
        Map<String, Boolean> privilegeMap;
        private String deviceType;
        private String type;
        private String pid;
        private Boolean is_hidden;
    }
}
