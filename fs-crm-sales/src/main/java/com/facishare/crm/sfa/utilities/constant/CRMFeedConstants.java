package com.facishare.crm.sfa.utilities.constant;

public interface CRMFeedConstants {
    class Field {
        public static final String NAME = "name";//销售记录关联对象主属性
        public static final String FEED_TYPE = "feed_type";//销售记录信息类型
        public static final String SEND_TIME = "send_time";//销售记录发送时间
        public static final String SEND_BY = "send_by";//销售记录发送人ID
        public static final String RELATED_SOURCE = "related_source";//销售记录关联对象类型
        public static final String OWNER = "owner";

    }
    class OldField {
        public static final String NAME = "name";//销售记录关联对象主属性
        public static final String FEED_TYPE = "feedType";//销售记录信息类型
        public static final String SEND_TIME = "sendTime";//销售记录发送时间
        public static final String SEND_BY = "senderId";//销售记录发送人ID
        public static final String RELATED_SOURCE = "source";//销售记录关联对象类型
        public static final String OWNER = "ownerid";
        public static final String IS_DELETED = "isDeleted";
        public static final String TENANT_ID = "ei";
        public static final String SALEEVENT_FEED_ID = "feedId";//销售记录ID
        public static final String STATUS = "status";//销售记录关联对象状态
        public static final String SALEEVENT_EMPLOYEE_IDS_NO_FOLLOWER = "noFollowerIds";//销售记录关联对象销售团队(无联合跟进人)
        public static final String SALEEVENT_EMPLOYEE_IDS = "employeeIds";//销售记录关联对象销售团队
        public static final String SALEEVENT_TERM_ORDER_KEY = "_term";//销售记录ES聚合排序标识
        public static final String SALEEVENT_AGG_NAME = "unique";//销售记录ES聚合名称标识
        public static final String SALEEVENT_TABLE_NAME = "feedcrmcoop";//销售记录ES搜索类型
    }

    class RelatedApiName {
        public static final String ALL = "all";
        public static final String LEADSOBJ = "LeadsObj";
        public static final String ACCOUNTOBJ = "AccountObj";
        public static final String CONTACTOBJ = "ContactObj";
        public static final String OPPORTUNITYOBJ = "OpportunityObj";

    }
    class OldRelatedApiName {
        public static final String ALL = "0";
        public static final String LEADSOBJ = "101";
        public static final String ACCOUNTOBJ = "102";
        public static final String CONTACTOBJ = "103";
        public static final String OPPORTUNITYOBJ = "108";
    }
}
