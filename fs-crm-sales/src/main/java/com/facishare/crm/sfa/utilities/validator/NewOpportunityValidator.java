package com.facishare.crm.sfa.utilities.validator;

import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

@Slf4j
public class NewOpportunityValidator extends BaseValidator {

    /**
     * 根据商机所在阶段字段获取到赢率和阶段状态字段的值
     */
    public static Map<String, String> salesStageToProbability(String value, IObjectDescribe objectDescribe) {
        log.info("salesStageToProbability value{}" + value);
        Map<String, String> map = new HashMap<>();
        if (value != null) {

            IFieldDescribe fields = objectDescribe.getFieldDescribe("sales_stage");
            Map extendInfo = fields.getExtendInfo();
            extendInfo.forEach((k, v) -> {
                if (k.equals(value)) {
                    Map status = (Map) v;
                    map.put("sales_status", status.get("sales_status").toString());
                    map.put("probability", status.get("probability").toString());
                }
            });
        }
        log.info("salesStageToProbability map{}", map);
        return map;
    }
}
