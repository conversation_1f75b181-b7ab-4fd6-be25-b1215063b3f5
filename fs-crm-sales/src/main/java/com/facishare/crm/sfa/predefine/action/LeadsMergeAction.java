package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.model.Enum.LeadsBizStatusEnum;
import com.facishare.crm.sfa.predefine.service.task.RecalculateTaskService;
import com.facishare.crm.sfa.utilities.constant.SFAConstants;
import com.facishare.crm.sfa.utilities.proxy.MergeJobProxy;
import com.facishare.crm.sfa.utilities.proxy.model.MergeJobModel;
import com.facishare.crm.sfa.utilities.util.AccountUtil;
import com.facishare.crm.sfa.utilities.util.SFARestHeaderUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.PermissionError;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardMergeAction;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_CANT_MERGE_INV_LEADS_;
import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_CANT_MERGE_VOVERTED_LEADS;

/**
 * Created by yuanjl on 2019/1/29
 * @IgnoreI18nFile
 */
@Slf4j
public class LeadsMergeAction extends StandardMergeAction {

    private static final RecalculateTaskService recalculateTaskService = SpringUtil.getContext().getBean(RecalculateTaskService.class);
    private MergeJobProxy mergeJobProxy = SpringUtil.getContext().getBean(MergeJobProxy.class);
    @Override
    protected List<String> getMergeRelationObjectApiNames(){
        return Lists.newArrayList();
    }

    @Override
    protected void before(Arg arg) {
        try {
            super.before(arg);
        } catch (ValidateException e) {
            errorCrmNotification(e.getMessage());
            mergeJobMessage("-1",e.getMessage(),arg.getJobId());
            throw e;
        } catch (PermissionError e){
            errorCrmNotification(e.getMessage());
            mergeJobMessage("-1",e.getMessage(),arg.getJobId());
            throw e;
        }
        Optional<IObjectData> optionalIObjectData =  dataList.stream().filter(x ->
                x.getId().equals(arg.getTargetDataId())).findFirst();
        if(!optionalIObjectData.isPresent()){
            errorCrmNotification("数据不存在或已删除");
            mergeJobMessage("-1","数据不存在或已删除",arg.getJobId());
            throw new ValidateException( "数据不存在或已删除");
        }
        targetObjectData = optionalIObjectData.get();
        sourceObjectDataList = dataList.stream().filter(x -> !(x.getId().equals(arg.getTargetDataId())))
                .collect(Collectors.toList());
        ObjectDataExt targetObjectDataExt = ObjectDataExt.of(targetObjectData);
        if(targetObjectDataExt.getLifeStatus().equals(ObjectLifeStatus.INEFFECTIVE)){
            errorCrmNotification("未生效状态对象不允许合并");
            mergeJobMessage("-1","未生效状态对象不允许合并",arg.getJobId());
            throw new ValidateException("未生效状态对象不允许合并");
        }
        if(targetObjectDataExt.getLifeStatus().equals(ObjectLifeStatus.INVALID)){
            if(sourceObjectDataList.stream().anyMatch(x -> !(ObjectDataExt.of(x).getLifeStatus().equals(ObjectLifeStatus.INVALID)))){
                errorCrmNotification("不能将状态正常的对象合并为已作废对象");
                mergeJobMessage("-1","不能将状态正常的对象合并为已作废对象",arg.getJobId());
                throw new ValidateException("不能将状态正常的对象合并为已作废对象");
            }
        }
        if(org.apache.commons.lang.StringUtils.isNotEmpty(targetObjectDataExt.getPartnerId()) ||
                sourceObjectDataList.stream().anyMatch(x -> org.apache.commons.lang.StringUtils.isNotEmpty(ObjectDataExt.of(x).getPartnerId()))){
            errorCrmNotification("对象关联了合作伙伴不能进行合并操作");
            mergeJobMessage("-1","对象关联了合作伙伴不能进行合并操作",arg.getJobId());
            throw new ValidateException("对象关联了合作伙伴不能进行合并操作");
        }
        String bizStatus = targetObjectData.get("biz_status", String.class);
        String lifeStatus= ObjectDataExt.of(targetObjectData).getLifeStatus().getCode();
        if(SFAConstants.LifeStatusConstant.INVALID.equals(lifeStatus)
                && !sourceObjectDataList.stream().anyMatch(x -> SFAConstants.LifeStatusConstant.INVALID.
                equals(ObjectDataExt.of(x).getLifeStatus().getCode()))){
                errorCrmNotification("不能将状态正常的销售线索合并为已作废的销售线索");
                mergeJobMessage("-1", "不能将状态正常的销售线索合并为已作废的销售线索",arg.getJobId());
            throw new ValidateException(String.format(I18N.text(SFA_CANT_MERGE_INV_LEADS_),
                    I18N.text("LeadsObj.attribute.self.display_name"),
                    I18N.text("LeadsObj.attribute.self.display_name")));
        }
        if(!LeadsBizStatusEnum.TRANSFORMED.getCode().equals(bizStatus)
                && sourceObjectDataList.stream().anyMatch(x ->LeadsBizStatusEnum.TRANSFORMED.getCode().
                equals(x.get("biz_status", String.class)))){
            errorCrmNotification("不能将已转换的销售线索合并为其它状态的销售线索");
            mergeJobMessage("-1", "不能将已转换的销售线索合并为其它状态的销售线索",arg.getJobId());
            throw new ValidateException(String.format(I18N.text(SFA_CANT_MERGE_VOVERTED_LEADS),
                    I18N.text("LeadsObj.attribute.self.display_name"),
                    I18N.text("LeadsObj.attribute.self.display_name")));
        }
    }

    @Override
    protected Result doAct(Arg arg) {
        try {
            return super.doAct(arg);
        }catch (Exception e){
            errorCrmNotification("合并销售线索失败");
            mergeJobMessage("-1","因系统异常合并失败",arg.getJobId());
            log.error("合并销售线索失败", e);
            throw e;
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        try {
            // 删除线索数据
            for (IObjectData objectData : sourceObjectDataList) {
                recalculateTaskService.delete(objectData.getTenantId(), objectData.getId(), Utils.LEADS_API_NAME);
            }
            // 发送【合并】的计算跟进时间的消息
            this.serviceFacade.sendActionMq(this.actionContext.getUser(), Lists.newArrayList(targetObjectData), ObjectAction.FOLLOW_UP);
            addMergeLeadsLog();
            sendCrmNotification();
            mergeJobMessage("0", "合并销售线索成功", arg.getJobId());
            return super.after(arg, result);
        }catch (Exception e){
            errorCrmNotification("合并销售线索失败");
            mergeJobMessage("-1","因系统异常合并失败",arg.getJobId());
            log.error("合并销售线索失败", e);
            throw e;
        }
    }

    private void addMergeLeadsLog(){
        String sourceUsers = "";
        for (IObjectData users:sourceObjectDataList ){
            sourceUsers += users.getName()+",";
        }
        String messageContent =  String.format("销售线索：%s 合并到销售线索：%s",sourceUsers,targetObjectData.getName());
        if (arg.getNeedMergeRelationObjects()){
            messageContent += ",并合并相关对象信息到目标销售线索";
        }
        serviceFacade.logWithCustomMessage(actionContext.getUser(), EventType.MODIFY, ActionType.MERGE,objectDescribe, targetObjectData, "," + messageContent);
    }

    private void mergeJobMessage(String code,String message,String jobId){
        Map<String,String> header = SFARestHeaderUtil.getCrmHeader(actionContext.getTenantId(),actionContext.getUser().getUserId());
        MergeJobModel.Status status = mergeJobProxy.mergeComplete(
                MergeJobModel.MergeComplete.builder().
                        code(code).
                        message(message).
                        jobId(jobId).
                        build(),
                header
        );
    }

    private void sendCrmNotification(){
        List<String> sourceObjectNameList = Lists.newArrayList();
        sourceObjectDataList.forEach(x -> {
            sourceObjectNameList.add(x.getName());
        });
        AccountUtil.sendCRMNotification(actionContext.getUser(),
                String.format("您合并了销售线索：%s，到销售线索：%s",
                        String.join(",", sourceObjectNameList),targetObjectData.getName()),
                130, "合并销售线索", targetObjectData.getId(), "",
                Lists.newArrayList(actionContext.getUser().getUserId()),false,Utils.LEADS_API_NAME);
    }

    private void errorCrmNotification(String error){
        AccountUtil.sendCRMNotification(actionContext.getUser(),
                String.format("失败原因：%s", error),
                130, "合并失败","", "",
                Lists.newArrayList(actionContext.getUser().getUserId()),false,Utils.LEADS_API_NAME);
    }
}
