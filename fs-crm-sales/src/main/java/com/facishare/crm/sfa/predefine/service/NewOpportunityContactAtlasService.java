package com.facishare.crm.sfa.predefine.service;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.model.NewOpportunityContactAtlasModel;
import com.facishare.crm.sfa.utilities.util.AccountAddrUtil;
import com.facishare.crm.sfa.utilities.util.CommonSqlUtil;
import com.facishare.crm.sfa.utilities.util.ContactAtlasUtil;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 商机联系人图谱接口 class
 *
 * <AUTHOR>
 * @date 2019/9/26
 */
@ServiceModule("new_opportunity_contact_atlas")
@Component
@Slf4j
public class NewOpportunityContactAtlasService {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private FunctionPrivilegeService functionPrivilegeService;

    /**
     * 获取
     * new_opportunity_contact_atlas/service/get_new_opportunity_contact_atlas
     */
    @ServiceMethod("get_new_opportunity_contact_atlas")
    public NewOpportunityContactAtlasModel.Result getNewOpportunityContactAtlas(NewOpportunityContactAtlasModel.Arg arg,
                                                                                ServiceContext serviceContext) {
        if (Strings.isNullOrEmpty(arg.getNewOpportunityId())) {
            throw new ValidateException(I18N.text(I18NKey.REQUEST_PARAM_IS_NULL));
        }

        LinkedHashMap<String, Object> whereMap = getWhereMap(serviceContext.getUser(), arg.getNewOpportunityId());
        List<Map> cardList = ContactAtlasUtil.selectDataBySql(serviceContext.getUser(),
                "new_opportunity_contact_card", whereMap);
        List<Map> cardRelationMapList = ContactAtlasUtil.selectDataBySql(serviceContext.getUser(),
                "new_opportunity_contact_card_relation", whereMap);
        List<Map> svgPathList = ContactAtlasUtil.selectDataBySql(serviceContext.getUser(),
                "new_opportunity_svg_path", whereMap);

        List<NewOpportunityContactAtlasModel.NewOpportunityContactCard> newOpportunityContactCardList = Lists.newArrayList();
        IObjectDescribe newOpportunityContactsDescribe = serviceFacade.findObject(serviceContext.getTenantId(), Utils.NEW_OPPORTUNITY_CONTACTS_API_NAME);
        if (CollectionUtils.notEmpty(cardList)) {
            Set<String> newOpportunityContactsIds = cardList.stream()
                    .filter(m -> m.containsKey("new_opportunity_contact_id") && !Objects.isNull(m.get("new_opportunity_contact_id")))
                    .map(n -> n.get("new_opportunity_contact_id").toString()).collect(Collectors.toSet());
            List<IObjectData> newOpportunityContactsTempList = Lists.newArrayList();
            List<IObjectData> contactTempList = Lists.newArrayList();
            if (CollectionUtils.notEmpty(newOpportunityContactsIds)) {
                Map<String, Map<String, Boolean>> objApiNameAndActionCodePrivilegeMapping =
                        functionPrivilegeService.batchFunPrivilegeCheck(serviceContext.getUser(),
                                Lists.newArrayList(Utils.CONTACT_API_NAME, Utils.NEW_OPPORTUNITY_CONTACTS_API_NAME),
                                Lists.newArrayList(ObjectAction.VIEW_DETAIL.getActionCode(), ObjectAction.VIEW_LIST.getActionCode()));
                if (objApiNameAndActionCodePrivilegeMapping.get(Utils.NEW_OPPORTUNITY_CONTACTS_API_NAME).get(ObjectAction.VIEW_LIST.getActionCode())) {
                    newOpportunityContactsTempList = ContactAtlasUtil.findBySearchQueryWithDeletedForIds(serviceContext.getUser(),
                            new ArrayList<>(newOpportunityContactsIds), newOpportunityContactsDescribe);
                    if (CollectionUtils.notEmpty(newOpportunityContactsTempList)) {
                        serviceFacade.fillObjectDataWithRefObject(newOpportunityContactsDescribe, newOpportunityContactsTempList, serviceContext.getUser());
                        Set<String> contactIds = newOpportunityContactsTempList.stream().filter(m -> !Strings.isNullOrEmpty(m.get("contact_id", String.class)))
                                .map(n -> n.get("contact_id", String.class)).collect(Collectors.toSet());
                        if (CollectionUtils.notEmpty(contactIds) &&
                                objApiNameAndActionCodePrivilegeMapping.get(Utils.CONTACT_API_NAME).get(ObjectAction.VIEW_DETAIL.getActionCode())) {
                            contactTempList = ContactAtlasUtil.findBySearchQueryForIds(serviceContext.getUser(),
                                    new ArrayList<>(contactIds), Utils.CONTACT_API_NAME);
                        }
                    }
                }
            }
            List<IObjectData> newOpportunityContactsList = newOpportunityContactsTempList;
            List<IObjectData> contactList = contactTempList;
            cardList.forEach(m -> {
                IObjectData cardData = ObjectDataDocument.of(m).toObjectData();
                NewOpportunityContactAtlasModel.NewOpportunityContactCard newOpportunityContactCard =
                        NewOpportunityContactAtlasModel.NewOpportunityContactCard.builder()
                                .cardId(cardData.get("id", String.class))
                                .orderBy(cardData.get("order_by", Integer.class))
                                .build();
                //卡片上商机联系人不为空
                if (!Strings.isNullOrEmpty(cardData.get("new_opportunity_contact_id", String.class))) {
                    if (CollectionUtils.notEmpty(newOpportunityContactsList)) {
                        Optional<IObjectData> newOpportunityContactsData = newOpportunityContactsList.stream()
                                .filter(n -> n.getId().equals(cardData.get("new_opportunity_contact_id", String.class))).findFirst();
                        newOpportunityContactsData.ifPresent(x -> {
                            //该数据有没有被删除或作废
                            if (!x.isDeleted()) {
                                newOpportunityContactCard.setContactId(x.get("contact_id", String.class));
                                newOpportunityContactCard.setNewOpportunityContactId(cardData.get("new_opportunity_contact_id", String.class));
                                setModelFields(newOpportunityContactCard, x);
                                newOpportunityContactCard.setContactName(x.get("contact_id__r", String.class));
                                //联系人信息只有正常的会被赋值，作废删除的以及没有权限的统一处理不下发
                                if (CollectionUtils.notEmpty(contactList)) {
                                    Optional<IObjectData> contactData = contactList.stream()
                                            .filter(n -> n.getId().equals(x.get("contact_id", String.class))).findFirst();
                                    if (contactData.isPresent()) {
                                        newOpportunityContactCard.setContactInfo(ObjectDataDocument.of(contactData.get()));
                                    }
                                }
                            }
                            //被删除或作废的为空卡片,空卡片赋值
                            else {
                                setModelFields(newOpportunityContactCard, cardData);
                            }
                        });
                        //没有权限，根据id没有获取到，没有权限只有id没有内容
                        if (!newOpportunityContactsData.isPresent()) {
                            newOpportunityContactCard.setNewOpportunityContactId(cardData.get("new_opportunity_contact_id", String.class));
                            newOpportunityContactCard.setShowNewOpportunityContact(false);
                        }
                    }
                    //没有权限，根据id没有获取到，没有权限只有id没有内容
                    else {
                        newOpportunityContactCard.setNewOpportunityContactId(cardData.get("new_opportunity_contact_id", String.class));
                        newOpportunityContactCard.setShowNewOpportunityContact(false);
                    }
                }
                //空卡片赋值
                else {
                    setModelFields(newOpportunityContactCard, cardData);
                }
                newOpportunityContactCardList.add(newOpportunityContactCard);
            });
        }
        List<NewOpportunityContactAtlasModel.CardRelation> cardRelationList = Lists.newArrayList();
        if (CollectionUtils.notEmpty(cardRelationMapList)) {
            cardRelationMapList.forEach(k -> {
                IObjectData objectData = ObjectDataDocument.of(k).toObjectData();
                NewOpportunityContactAtlasModel.CardRelation cardRelation = NewOpportunityContactAtlasModel.CardRelation.builder()
                        .source(objectData.get("source", String.class))
                        .target(objectData.get("target", String.class))
                        .build();
                cardRelationList.add(cardRelation);
            });
        }
        String svgPath = "";
        if (CollectionUtils.notEmpty(svgPathList)) {
            svgPath = Objects.isNull(svgPathList.get(0).get("svg_path")) ? "" : svgPathList.get(0).get("svg_path").toString();
        }
        boolean hasEditFunction = functionPrivilegeService.funPrivilegeCheck(serviceContext.getUser(),
                Utils.NEW_OPPORTUNITY_CONTACTS_API_NAME, ObjectAction.UPDATE.getActionCode());
        Set<String> invisibleFields = serviceFacade.getUnauthorizedFields(serviceContext.getUser(), Utils.NEW_OPPORTUNITY_CONTACTS_API_NAME);
        Set<String> readonlyFields = serviceFacade.getReadonlyFields(serviceContext.getUser(), Utils.NEW_OPPORTUNITY_CONTACTS_API_NAME);
        readonlyFields.removeAll(invisibleFields);
        return NewOpportunityContactAtlasModel.Result.builder()
                .newOpportunityId(arg.getNewOpportunityId())
                .newOpportunityContactCard(newOpportunityContactCardList)
                .cardRelation(cardRelationList)
                .svgPath(svgPath)
                .invisibleFields(Lists.newArrayList(invisibleFields))
                .readonlyFields(Lists.newArrayList(readonlyFields))
                .editPermissions(hasEditFunction)
                .build();
    }

    @NotNull
    private LinkedHashMap<String, Object> getWhereMap(User user, String newOpportunityId) {
        LinkedHashMap<String, Object> whereMap = new LinkedHashMap<>();
        whereMap.put("tenant_id", user.getTenantId());
        whereMap.put("new_opportunity_id", newOpportunityId);
        return whereMap;
    }

    private void setModelFields(NewOpportunityContactAtlasModel.NewOpportunityContactCard newOpportunityContactCard, IObjectData objectData) {
        newOpportunityContactCard.setRole(objectData.get("role", String.class));
        newOpportunityContactCard.setPosition(objectData.get("position", String.class));
        newOpportunityContactCard.setContactStatus(objectData.get("contact_status", String.class));
        newOpportunityContactCard.setCustomerRelationship(objectData.get("customer_relationship", String.class));
        newOpportunityContactCard.setShowNewOpportunityContact(true);
    }

    /**
     * 保存
     * new_opportunity_contact_atlas/service/save_new_opportunity_contact_atlas
     */
    @ServiceMethod("save_new_opportunity_contact_atlas")
    @Transactional
    public NewOpportunityContactAtlasModel.SaveResult saveNewOpportunityContactAtlas(NewOpportunityContactAtlasModel.SaveArg arg, ServiceContext serviceContext) {
        if (Strings.isNullOrEmpty(arg.getNewOpportunityId()) || CollectionUtils.empty(arg.getCardRelation()) ||
                CollectionUtils.empty(arg.getNewOpportunityContactCard()) || Strings.isNullOrEmpty(arg.getSvgPath()) ||
                Strings.isNullOrEmpty(arg.getFileExtension())) {
            throw new ValidateException(I18N.text(I18NKey.REQUEST_PARAM_IS_NULL));
        }
        changeCardId(arg.getNewOpportunityContactCard(), arg.getCardRelation());
        insertCardBySql(serviceContext.getUser(), arg.getNewOpportunityId(), arg.getNewOpportunityContactCard());
        insertCardRelationBySql(serviceContext.getUser(), arg.getNewOpportunityId(), arg.getCardRelation());
        ContactAtlasUtil.updateOrInsertSvgPath(serviceContext.getUser(), arg.getNewOpportunityId(), Utils.NEW_OPPORTUNITY_API_NAME, arg.getSvgPath(), arg.getFileExtension());
        return NewOpportunityContactAtlasModel.SaveResult.builder().build();
    }

    /**
     * 卡片相关校验
     *
     * @param contactCards
     * @param cardRelations
     */
    private void changeCardId(List<NewOpportunityContactAtlasModel.NewOpportunityContactCardModel> contactCards, List<NewOpportunityContactAtlasModel.CardRelationModel> cardRelations) {
        Set<String> cardIdSet = contactCards.stream().map(m -> m.getCardId())
                .collect(Collectors.toSet());
        Set<String> cardRelationIdSet = cardRelations.stream().map(m -> m.getSource())
                .collect(Collectors.toSet());
        cardRelationIdSet.addAll(cardRelations.stream().map(m -> m.getTarget())
                .collect(Collectors.toSet()));
        contactCards.forEach(m -> {
            if ((Strings.isNullOrEmpty(m.getContactId()) && !Strings.isNullOrEmpty(m.getNewOpportunityContactId())) ||
                    (!Strings.isNullOrEmpty(m.getContactId()) && Strings.isNullOrEmpty(m.getNewOpportunityContactId()))) {
                throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
            }
        });
        cardRelations.forEach(m -> {
            if (Strings.isNullOrEmpty(m.getSource()) || Strings.isNullOrEmpty(m.getTarget()) ||
                    !cardIdSet.contains(m.getSource()) || !cardIdSet.contains(m.getTarget())) {
                throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
            }
        });
        for (String key : cardIdSet) {
            if (!cardRelationIdSet.contains(key)) {
                throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_NEWOPPORTUNITYCONTACT_EXISTFREEOFNODES));
            }
        }
    }

    /**
     * 插入商机联系人卡片表
     *
     * @param user
     * @param newOpportunityId
     * @param newOpportunityContactCardList
     */
    private void insertCardBySql(User user, String newOpportunityId, List<NewOpportunityContactAtlasModel.NewOpportunityContactCardModel> newOpportunityContactCardList) {
        if (CollectionUtils.empty(newOpportunityContactCardList)) {
            return;
        }
        String tableName = "new_opportunity_contact_card";
        Map<String, NewOpportunityContactAtlasModel.NewOpportunityContactCardModel> cardNewOpportunityMaps = Maps.newHashMap();
        List<Map<String, Object>> insertMap = Lists.newArrayList();
        Integer orderBy = 1;
        Iterator iterator = newOpportunityContactCardList.iterator();
        while (iterator.hasNext()) {
            NewOpportunityContactAtlasModel.NewOpportunityContactCardModel m = (NewOpportunityContactAtlasModel.NewOpportunityContactCardModel) iterator.next();
            Map<String, Object> insertData = Maps.newHashMap();
            insertData.put("id", m.getCardId());
            insertData.put("tenant_id", user.getTenantId());
            insertData.put("type", 1);
            insertData.put("new_opportunity_id", newOpportunityId);
            insertData.put("object_id", newOpportunityId);
            insertData.put("object_api_name", Utils.NEW_OPPORTUNITY_API_NAME);
            if (!Strings.isNullOrEmpty(m.getNewOpportunityContactId())) {
                insertData.put("new_opportunity_contact_id", m.getNewOpportunityContactId());
                insertData.put("contact_id", m.getContactId());
                insertData.put("role", null);
                insertData.put("position", null);
                insertData.put("contact_status", null);
                insertData.put("customer_relationship", null);
                if (!cardNewOpportunityMaps.containsKey(m.getNewOpportunityContactId())) {
                    cardNewOpportunityMaps.put(m.getNewOpportunityContactId(), m);
                }
            } else {
                insertData.put("new_opportunity_contact_id", null);
                insertData.put("contact_id", null);
                insertData.put("role", m.getRole());
                insertData.put("position", m.getPosition());
                insertData.put("contact_status", m.getContactStatus());
                insertData.put("customer_relationship", m.getCustomerRelationship());
            }
            insertData.put("created_by", user.getUserId());
            insertData.put("create_time", System.currentTimeMillis());
            insertData.put("last_modified_by", user.getUserId());
            insertData.put("last_modified_time", System.currentTimeMillis());
            insertData.put("is_deleted", 0);
            insertData.put("order_by", orderBy++);
            insertMap.add(insertData);
        }
        if (CollectionUtils.notEmpty(insertMap)) {
            LinkedHashMap<String, Object> whereMap = getWhereMap(user, newOpportunityId);
            ContactAtlasUtil.deletedBySql(user, tableName, whereMap);
            CommonSqlUtil.insertDataBySql(user, tableName, insertMap);
        }
        if (CollectionUtils.notEmpty(cardNewOpportunityMaps)) {
            updateNewOpportunityContactField(user, cardNewOpportunityMaps);
        }
    }

    /**
     * 插入商机联系人卡片关系表
     *
     * @param user
     * @param newOpportunityId
     * @param cardRelationList
     */
    private void insertCardRelationBySql(User user, String newOpportunityId, List<NewOpportunityContactAtlasModel.CardRelationModel> cardRelationList) {
        if (CollectionUtils.empty(cardRelationList)) {
            return;
        }
        String tableName = "new_opportunity_contact_card_relation";
        List<Map<String, Object>> insertMap = Lists.newArrayList();
        cardRelationList.forEach(m -> {
            Map<String, Object> insertData = Maps.newHashMap();
            insertData.put("id", serviceFacade.generateId());
            insertData.put("tenant_id", user.getTenantId());
            insertData.put("type", 1);
            insertData.put("new_opportunity_id", newOpportunityId);
            insertData.put("object_id", newOpportunityId);
            insertData.put("object_api_name", Utils.NEW_OPPORTUNITY_API_NAME);
            insertData.put("source", m.getSource());
            insertData.put("target", m.getTarget());
            insertData.put("relation_type", 1);
            insertData.put("created_by", user.getUserId());
            insertData.put("create_time", System.currentTimeMillis());
            insertData.put("last_modified_by", user.getUserId());
            insertData.put("last_modified_time", System.currentTimeMillis());
            insertData.put("is_deleted", 0);
            insertMap.add(insertData);
        });
        LinkedHashMap<String, Object> whereMap = getWhereMap(user, newOpportunityId);
        ContactAtlasUtil.deletedBySql(user, tableName, whereMap);
        CommonSqlUtil.insertDataBySql(user, tableName, insertMap);
    }

    /**
     * 更新商机联系人属性
     *
     * @param user
     * @param cardNewOpportunityMaps
     */
    private void updateNewOpportunityContactField(User user, Map<String, NewOpportunityContactAtlasModel.NewOpportunityContactCardModel> cardNewOpportunityMaps) {
        List<IObjectData> newOpportunityContactsList = serviceFacade.findObjectDataByIds(user.getTenantId(),
                new ArrayList<>(cardNewOpportunityMaps.keySet()), Utils.NEW_OPPORTUNITY_CONTACTS_API_NAME);
        newOpportunityContactsList.forEach(m -> {
            NewOpportunityContactAtlasModel.NewOpportunityContactCardModel newOpportunityContactCardModel = cardNewOpportunityMaps.get(m.getId());
            m.set("role", newOpportunityContactCardModel.getRole());
            m.set("position", newOpportunityContactCardModel.getPosition());
            m.set("contact_status", newOpportunityContactCardModel.getContactStatus());
            m.set("customer_relationship", newOpportunityContactCardModel.getCustomerRelationship());
        });
        AccountAddrUtil.batchUpdateIgnoreOther(user, newOpportunityContactsList,
                Lists.newArrayList("role", "position", "contact_status", "customer_relationship"));
    }

}
