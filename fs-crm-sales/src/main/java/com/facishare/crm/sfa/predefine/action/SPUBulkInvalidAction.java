package com.facishare.crm.sfa.predefine.action;

import com.google.common.collect.Lists;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import com.facishare.crm.sfa.predefine.service.real.SpuSkuService;
import com.facishare.crm.sfa.predefine.service.real.SpuSkuServiceImpl;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by luxin on 2018/12/3.
 */
public class SPUBulkInvalidAction extends StandardBulkInvalidAction {

    private SpuSkuService spuSkuService = SpringUtil.getContext().getBean(SpuSkuServiceImpl.class);

    @Override
    protected Result doAct(Arg arg) {
        checkHaveAnySkuUnderSpu(arg);
        return super.doAct(arg);
    }

    /**
     * 检测是在spu下是否还有正常状态的sku,如果有spu不能作废
     *
     * @param arg
     */
    private void checkHaveAnySkuUnderSpu(Arg arg) {
        List<String> spuIds = getDataPrivilegeIds(arg);

        Boolean existInvalidAnySkuUnderSpu = spuSkuService.isExistSkuUnderAnySpuByIsDeleteStatusNotWithDataPermiss(actionContext.getUser(), spuIds, Lists.newArrayList("0"));
        if (existInvalidAnySkuUnderSpu) {
            throw new ValidateException(I18N.text("so.spu.invalid.check"));
        }
    }
}
