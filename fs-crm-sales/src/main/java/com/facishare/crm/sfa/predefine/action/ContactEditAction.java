package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.push.ContactSessionSandwichService;
import com.facishare.crm.sfa.utilities.util.*;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2018/3/19 16:10
 */
@Slf4j
public class ContactEditAction extends StandardEditAction {
    ContactSessionSandwichService contactSessionSandwichService = SpringUtil.getContext()
            .getBean(ContactSessionSandwichService.class);

    @Override
    protected void before(Arg arg) {
        ObjectDataDocument objectData = arg.getObjectData();
        if (actionContext.isFromOpenAPI()) {
            ContactUtil.handlePhoneFields(objectData);
        } else {
            PhoneUtil.dealPhone(objectData);
        }
        ContactUtil.handleContactFields(objectData);
        //不能放到上面方法中，只编辑需要排除该字段。
        LeadsUtils.removeLeadsId(objectData);
        //获取手机归属地字段
        AccountUtil.getPhoneNumberInfo(arg.getObjectData(), "mobile1");
        super.before(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        contactSessionSandwichService.push(actionContext.getTenantId(), result.getObjectData().toObjectData().getOwner());
        //更新 out_tenant_id，out_owner
        ContactUtil.updateContactOutInfo(actionContext.getUser(), Lists.newArrayList(result.getObjectData().toObjectData()));
        super.after(arg, result);
        sendActionMq(fillOldData(updatedDataList), ObjectAction.RELATE);
        return result;
    }


    private List<IObjectData> fillOldData(List<IObjectData> updatedDataList) {
        if (!Objects.equals(Utils.NEW_OPPORTUNITY_API_NAME, objectDescribe.getApiName())) {
            return updatedDataList;
        }

        return ObjectDataDocument.fillOldData(updatedDataList, Lists.newArrayList(dbMasterData));
    }

    @Override
    protected void recordLog() {
        List<IObjectData> dataToUpdate = this.getAllDataToUpdate();
        LogUtil.recordEditSpecailLog(actionContext.getUser(), dataToUpdate, objectData, objectDescribe);
        logAsync(detailsToAdd, EventType.ADD, ActionType.Add);
    }
}
