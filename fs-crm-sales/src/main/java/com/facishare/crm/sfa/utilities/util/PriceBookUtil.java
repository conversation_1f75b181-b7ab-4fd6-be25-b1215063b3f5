package com.facishare.crm.sfa.utilities.util;

import com.facishare.crm.sfa.predefine.service.PriceBookService;
import com.facishare.crm.sfa.utilities.constant.PriceBookConstants;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.List;

public class PriceBookUtil {
    private static final PriceBookService priceBookService = SpringUtil.getContext().getBean(PriceBookService.class);
    private static final List<String> UNSUPPORTED_IMPORT_FIELDS = Lists.newArrayList(
            PriceBookConstants.Field.ISSTANDARD.getApiName(),
            PriceBookConstants.Field.PARTNERRANGE.getApiName(),
            PriceBookConstants.Field.PRIORITY.getApiName(),
            ObjectDataExt.RECORD_TYPE,
            ObjectDataExt.EXTEND_OBJ_DATA_ID);

    public static void removeUnsupportedImportFields(List<IFieldDescribe> fieldDescribeList) {
        if (CollectionUtils.empty(fieldDescribeList)) {
            return;
        }
        fieldDescribeList.removeIf(field -> UNSUPPORTED_IMPORT_FIELDS.contains(field.getApiName()));
    }
}
