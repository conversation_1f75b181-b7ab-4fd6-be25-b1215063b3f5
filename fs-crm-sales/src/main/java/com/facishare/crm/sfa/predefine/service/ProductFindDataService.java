package com.facishare.crm.sfa.predefine.service;

import com.facishare.crm.sfa.predefine.service.model.ProductFindDataModel;
import com.facishare.crm.sfa.predefine.service.real.ProductService;
import com.facishare.crm.sfa.predefine.service.real.SpuSkuService;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.metadata.api.IObjectData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by luxin on 2018/11/29.
 */
@ServiceModule("product_find_data")
@Component
public class ProductFindDataService {

    @Resource(name = "sfaProductService")
    private ProductService productService;

    @Autowired
    private SpuSkuService spuSkuService;


    @ServiceMethod("invalid_data_list")
    public List<IObjectData> findInvalidProductDataListBySpuId(ProductFindDataModel.InvalidDataArg arg, ServiceContext context) {
        List<IObjectData> invalidProductDataListBySpuId = productService.findInvalidProductDataListBySpuId(context.getUser(), arg.getSpuId());

        Map<String, ObjectDataDocument> skuIdAndDataMapping = invalidProductDataListBySpuId.stream()
                .collect(Collectors.toMap(IObjectData::getId, o -> ObjectDataDocument.of(o)));

        spuSkuService.fillSpecAndValue4Skus(context.getTenantId(), skuIdAndDataMapping);

        return invalidProductDataListBySpuId;
    }


}
