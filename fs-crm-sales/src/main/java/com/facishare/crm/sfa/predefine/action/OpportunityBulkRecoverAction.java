package com.facishare.crm.sfa.predefine.action;

import com.facishare.paas.appframework.core.predef.action.StandardBulkRecoverAction;

/**
 * 合同恢复接口 class
 *
 * <AUTHOR>
 * @date 2019/1/14
 */
public class OpportunityBulkRecoverAction extends StandardBulkRecoverAction {
    @Override
    public Result doAct(Arg arg) {
        Result result =super.doAct(arg);
        return  result;
    }


    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        //todo 日志记录
        //todo 汇聚发Queue
        return result;
    }
}
