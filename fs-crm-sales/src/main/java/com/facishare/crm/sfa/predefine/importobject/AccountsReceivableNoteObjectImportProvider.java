package com.facishare.crm.sfa.predefine.importobject;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.metadata.importobject.DefaultObjectImportProvider;
import com.facishare.paas.appframework.metadata.importobject.ImportObject;
import com.facishare.paas.appframework.metadata.importobject.ImportType;
import com.facishare.paas.appframework.metadata.importobject.MatchingType;
import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2019-05-14 18:28
 */
@Component
public class AccountsReceivableNoteObjectImportProvider extends DefaultObjectImportProvider {
    @Override
    public String getObjectCode() {
        return "AccountsReceivableNoteObj";
    }

    @Override
    protected ImportType getImportType(IObjectDescribe objectDescribe, IUniqueRule uniqueRule) {
        return ImportType.UNSUPPORT_UPDATE_IMPORT;
    }
}
