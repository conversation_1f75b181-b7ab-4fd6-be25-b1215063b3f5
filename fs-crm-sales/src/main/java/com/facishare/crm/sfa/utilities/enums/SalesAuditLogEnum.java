package com.facishare.crm.sfa.utilities.enums;

/**
 * <AUTHOR>
 * @date 2019/11/15 2:55 下午
 */
public enum SalesAuditLogEnum {

    NORMAL("normal", "正常"),

    ERROR("error", "错误"),

    TIMEOUT("timeout", "超时");

    public String status;
    public String error;

    SalesAuditLogEnum(String status, String error) {
        this.status = status;
        this.error = error;
    }

    public String getStatus() {
        return status;
    }

    public String getError() {
        return error;
    }
}
