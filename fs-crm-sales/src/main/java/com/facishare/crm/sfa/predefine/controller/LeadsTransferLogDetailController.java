package com.facishare.crm.sfa.predefine.controller;

import com.facishare.paas.appframework.core.predef.controller.StandardDetailController;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.component.GroupComponent;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @IgnoreI18nFile
 */
@Slf4j
public class LeadsTransferLogDetailController extends SFADetailController {
    @Override
    protected void doFunPrivilegeCheck() {
    }

    @Override
    protected void doDataPrivilegeCheck(StandardDetailController.Arg arg) {
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        if (newResult.getLayout() == null || newResult.getData() == null) {
            return newResult;
        }
        List<IFieldDescribe> fieldDescribes = newResult.getDescribe().toObjectDescribe().getFieldDescribes();
        Optional<IFieldDescribe> opField = fieldDescribes.stream().filter(f -> "created_by".equals(f.getApiName())).findFirst();
        if (opField.isPresent()) {
            opField.get().setLabel("转换者");
        }
        opField = fieldDescribes.stream().filter(f -> "create_time".equals(f.getApiName())).findFirst();
        if (opField.isPresent()) {
            opField.get().setLabel("转换时间");
        }
        ILayout layout = new Layout(newResult.getLayout());
        layout.setButtons(Lists.newArrayList());
        try {
            Optional<GroupComponent> optionalCom = layout.getComponents().stream().filter(x -> "detailInfo".equals(x.getName())).map(x ->
                    (GroupComponent) x
            ).findFirst();
            layout.getComponents().clear();
            if (optionalCom.isPresent()) {
                IComponent component = optionalCom.get();
                List<IComponent> componentList = Lists.newArrayList(component);
                Optional<GroupComponent> optional = layout.getComponents().stream().filter(x -> "relatedObject".equals(x.getName())).map(x ->
                        (GroupComponent) x
                ).findFirst();
                if (optional.isPresent()) {
                    GroupComponent groupComponent = optional.get();
                    groupComponent.setButtons(Lists.newArrayList());
                    groupComponent.get("child_components", ArrayList.class).clear();
                    componentList.add(groupComponent);
                }
                layout.setComponents(componentList);
            }
        } catch (MetadataServiceException e) {
            log.error("getChildComponents error", e);
        }
        return newResult;
    }
}
