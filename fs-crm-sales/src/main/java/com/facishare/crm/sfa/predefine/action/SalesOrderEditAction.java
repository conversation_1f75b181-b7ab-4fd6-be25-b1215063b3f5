package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.model.Enum.SessionBOCItemKeys;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.action.listener.SalesOrderEditActionListener;
import com.facishare.crm.sfa.predefine.bizvalidator.BizValidator;
import com.facishare.crm.sfa.predefine.bizvalidator.ValidatorContext;
import com.facishare.crm.sfa.predefine.bizvalidator.validator.*;
import com.facishare.crm.sfa.predefine.bizvalidator.validator.salesorder.*;
import com.facishare.crm.sfa.predefine.enums.RemindRecordEnum;
import com.facishare.crm.sfa.predefine.service.ModuleCtrlConfigService;
import com.facishare.crm.sfa.predefine.service.PriceBookService;
import com.facishare.crm.sfa.predefine.service.QiXinTodoService;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.predefine.service.modulectrl.IModuleInitService;
import com.facishare.crm.sfa.utilities.constant.SalesOrderConstants;
import com.facishare.crm.sfa.utilities.proxy.SalesOrderBizProxy;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.SalesOrderInterceptorModel;
import com.facishare.crm.sfa.utilities.proxy.model.salesorder.salesorderparam.SalesOrderEditAfterModel;
import com.facishare.crm.sfa.utilities.util.*;
import com.facishare.crm.sfa.utilities.validator.SalesOrderValidator;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.CRMNotificationServiceImpl;
import com.facishare.paas.appframework.common.service.model.CRMNotification;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAPINameMapping;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ObjectDataNotFoundException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.flow.ApprovalFlowStartResult;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.INameCache;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.common.Strings;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.text.SimpleDateFormat;
import java.util.*;

import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.SFA_ORDER_EDIT_LOG_MSG;

/**
 * 客户编辑操作
 * <p>
 * Created by liyiguang on 2017/7/13.
 */
@Slf4j
public class SalesOrderEditAction extends StandardEditAction {

    private CRMNotificationServiceImpl crmNotificationService = (CRMNotificationServiceImpl) SpringUtil.getContext()
            .getBean("crmNotificationService");
    private static final ModuleCtrlConfigService moduleCtrlConfigService = SpringUtil.getContext().getBean(ModuleCtrlConfigService.class);
    private static final QiXinTodoService qiXinTodoService = SpringUtil.getContext().getBean(QiXinTodoService.class);
    private static final SalesOrderBizProxy salesOrderBizProxy = SpringUtil.getContext().getBean(SalesOrderBizProxy.class);
    private static final BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService = SpringUtil.getContext().getBean(BizConfigThreadLocalCacheService.class);
    private ThreadPoolTaskExecutor executor = (ThreadPoolTaskExecutor) SpringUtil.getContext().getBean("taskExecutor");

    private List<Integer> approverIds = Lists.newArrayList();
    private boolean restartApprovalFlow = false;
    boolean isMultipleUnit = false;

    private void initOpenKeyStatus() {
        isMultipleUnit = moduleCtrlConfigService.isOpen(IModuleInitService.MODULE_MULTIPLE_UNIT, actionContext.getUser());
    }

    private void prepareWorkFlowData(ObjectLifeStatus lifeStatus){
        if (!Boolean.FALSE.equals(actionContext.getAttribute(RequestContext.Attributes.TRIGGER_FLOW))) {
            if (lifeStatus == ObjectLifeStatus.INEFFECTIVE) {
                String workFlowId = dbMasterData.get("work_flow_id", String.class);
                if (!Strings.isNullOrEmpty(workFlowId)) {
                    restartApprovalFlow = true;
                    approverIds = (List<Integer>) objectData.get("approver");
                    String newWorkFlowId = objectData.get("work_flow_id", String.class);
                    objectData.set("life_status", ObjectLifeStatus.UNDER_REVIEW.getCode());
                    objectData.set("current_level", 0);
                    objectData.set("work_flow_id", newWorkFlowId);
                }
            }
        }
    }

    @Override
    protected void init() {
        super.init();
        initOpenKeyStatus();
        ObjectDataExt objectDataExt = ObjectDataExt.of(dbMasterData);
        ObjectLifeStatus lifeStatus = objectDataExt.getLifeStatus();
        objectData.set("life_status", lifeStatus.getCode());
        prepareWorkFlowData(lifeStatus);
        //价目表字段调整为非必填，不再补标准价目表
//        if(!bizConfigThreadLocalCacheService.isPriceBookEnabled(actionContext.getTenantId())) {
//            setStandardPriceBook();
//        }
        SalesOrderValidator.removeFields(objectData);
    }

    @Override
    public void before(Arg arg) {
        super.before(arg);

        ValidatorContext validatorContext = ValidatorContext.builder()
                .action(ObjectAction.UPDATE)
                .user(actionContext.getUser())
                .describeApiName(objectDescribe.getApiName())
                .objectDescribes(objectDescribes)
                .dbMasterData(dbMasterData).objectData(objectData).detailObjectData(detailObjectData)
                .detailsToAdd(detailsToAdd).detailsToUpdate(detailsToUpdate).detailsToDelete(detailsToDelete)
                .build();
        BizValidator.build()
                .withContext(validatorContext)
//                .with(new MobileUnSupportValidator())
//                .when(RequestUtil.isMobileOrH5Request())
                .with(new NewInvoiceValidator())
                .with(new MultiUnitValidator())
                .with(new MultiUnitQuantityDecimalValidator())
                .with(new SalesOrderNameValidator())
                .with(new CustomerAccountValidator())
                .with(new OrderProductValidator())
                .when(!actionContext.isFromOpenAPI() && detailObjectData.containsKey(Utils.SALES_ORDER_PRODUCT_API_NAME))
                .with(new OpportunityValidator())
                .when(!NewOpportunityUtil.getIsChangeToNewOpportunity(actionContext.getUser()))
                .with(new ContactValidator())
                .with(new ProductIsRepeatedValidator())
                .with(new WorkFlowValidator())
                .when(restartApprovalFlow)
                .with(new PriceBookValidator())
                .when(isValidatePriceBook())
                .with(new ProductRangeValidator())
                .when(!bizConfigThreadLocalCacheService.isPriceBookEnabled(actionContext.getTenantId()))
                .with(new PromotionValidator())
                .when(PromotionUtil.getIsPromotionEnable(actionContext.getUser(), actionContext.getClientInfo()))
                .doValidate();
    }

    private boolean isValidatePriceBook(){
        boolean needValidate = false;
        boolean skipPriceBookValidate = false;
        if (bizConfigThreadLocalCacheService.isPriceBookEnabled(actionContext.getTenantId())) {
            if (objectData.get(SalesOrderConstants.SKIP_PRICE_BOOK_VALIDATE) != null) {
                skipPriceBookValidate = objectData.get(SalesOrderConstants.SKIP_PRICE_BOOK_VALIDATE, Boolean.class);
            }
            if (!skipPriceBookValidate) {
                if (bizConfigThreadLocalCacheService.isAvailableRangeEnabled(actionContext.getTenantId())) {
                    needValidate = true;
                } else {
                    String priceBookId = objectData.get(SalesOrderConstants.SalesOrderField.PRICE_BOOK_ID.getApiName(), String.class);
                    if (StringUtils.isNotEmpty(priceBookId)) {
                        IObjectData priceBookData = null;
                        try {
                            priceBookData = serviceFacade.findObjectDataIgnoreRelevantTeam(actionContext.getUser(), priceBookId, Utils.PRICE_BOOK_API_NAME);
                        } catch (ObjectDataNotFoundException e) {
                            priceBookData = null;
                        }
                        // 表示被作废或删除了
                        if (null != priceBookData) {
                            needValidate = true;
                        }
                    }
                }
            }
        }
        return needValidate;
    }

//    private void setStandardPriceBook() {
//        String standardPriceBookId = priceBookService.getStandardPriceBookId(actionContext.getUser());
//        if (Strings.isNullOrEmpty(standardPriceBookId)) {
//            log.warn("standardPriceBook no found. actionContext {}", actionContext);
//        } else {
//            objectData.set(SalesOrderConstants.SalesOrderField.PRICE_BOOK_ID.getApiName(), standardPriceBookId);
//        }
//
//        List<IObjectData> details = detailObjectData.getOrDefault(SFAPreDefineObject.SalesOrderProduct.getApiName()
//                , Lists.newArrayList());
//        if (CollectionUtils.notEmpty(details)) {
//            details.forEach(data ->
//                    data.set(SalesOrderConstants.SalesOrderProductField.PRICE_BOOK_PRODUCT_ID.getApiName(),
//                            String.valueOf(data.get(SalesOrderConstants.SalesOrderProductField.PRODUCT_ID.getApiName()))
//                                    .concat(actionContext.getTenantId())));
//        }
//    }

    @Override
    protected boolean needTriggerApprovalFlow() {
        if (SalesOrderValidator.softApprovalFlow(actionContext) || restartApprovalFlow) {
            return false;
        }
        return super.needTriggerApprovalFlow();
    }

    @Override
    public Result after(Arg arg, Result result) {
        super.after(arg, result);
        if (!restartApprovalFlow) {
            afterInterceptor(result);
        }
        //成功触发审批流不需要执行后续操作
        if (isEditApprovalFlowStartSuccess()) {
            return result;
        }

        if (restartApprovalFlow) {
            List<String> toUpdateFields = Lists.newArrayList("current_level", "life_status", "work_flow_id");
            serviceFacade.batchUpdateByFields(actionContext.getUser(), Lists.newArrayList(objectData), toUpdateFields);

            afterInterceptor(result);
        }
        if (CollectionUtils.notEmpty(approverIds)) {
            List<String> strIds = Lists.newArrayList();
            approverIds.forEach(x -> strIds.add(x.toString()));
            qiXinTodoService.sendTodo(actionContext.getTenantId(), SessionBOCItemKeys.TobeConfirmedCustomerTrade,
                    actionContext.getObjectApiName(), objectData.getId(), actionContext.getUser().getUserId(), strIds);
        }
        if (SoCommonUtils.isGraySalesOrderEditCRMNotice(actionContext.getTenantId())) {
            notifyOwner(arg);
        }
        return result;
    }

    @Override
    protected Map<String, ApprovalFlowStartResult> startApprovalFlow(List<IObjectData> objectDataList,
                                                                     ApprovalFlowTriggerType approvalFlowTriggerType,
                                                                     Map<String, Map<String, Object>> updatedFieldMap) {
        if (restartApprovalFlow && approvalFlowTriggerType == ApprovalFlowTriggerType.CREATE) {
            Map<String, ApprovalFlowStartResult> map = Maps.newHashMap();
            map.put(objectData.getId(), ApprovalFlowStartResult.FAILED);
            return map;
        } else {
            return super.startApprovalFlow(objectDataList, approvalFlowTriggerType, updatedFieldMap);
        }
    }

    @Override
    public List<Class<? extends ActionListener<Arg, Result>>> getActionListenerClassList() {
        List<Class<? extends ActionListener<Arg, Result>>> classList = super.getActionListenerClassList();
        classList.add(SalesOrderEditActionListener.class);
        return classList;
    }

    private void notifyOwner(Arg arg) {
        String accountId = dbMasterData.get(SalesOrderConstants.SalesOrderField.ACCOUNT_ID.getApiName(), String.class);
        List<INameCache> nameCaches = serviceFacade.findRecordName(ActionContextExt.of(actionContext.getUser()).getContext(),
                Utils.ACCOUNT_API_NAME, Lists.newArrayList(accountId));
        if (CollectionUtils.notEmpty(nameCaches) && !Strings.isNullOrEmpty(nameCaches.get(0).getName())) {
            String orderId = dbMasterData.getId();
            IObjectData orderData = serviceFacade.findObjectDataIncludeDeletedIgnoreFormula(actionContext.getUser(),
                    orderId, ObjectAPINameMapping.SalesOrder.getApiName());
            serviceFacade.fillMaskFieldValue(actionContext.getUser(), Lists.newArrayList(orderData), objectDescribe, false);
            String orderAmount = orderData.get(SalesOrderConstants.SalesOrderField.ORDER_AMOUNT.getApiName(), String.class);
            String orderAmountMaskValue = orderData.get("order_amount__s", String.class);
            if (StringUtils.isEmpty(orderAmountMaskValue)) {
                if (Strings.isNullOrEmpty(orderAmount)) {
                    orderAmount = "--";
                }
            } else {
                // 掩码展示
                orderAmount = orderAmountMaskValue;
            }
            Date orderDate = orderData.get(SalesOrderConstants.SalesOrderField.ORDER_TIME.getApiName(), Date.class);
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String remindContent = String.format(I18N.text(SFA_ORDER_EDIT_LOG_MSG), nameCaches.get(0).getName(),
                    orderAmount, simpleDateFormat.format(orderDate));
            Set<Integer> receiverIds = Sets.newHashSet();
            List<String> owners = orderData.getOwner();
            for (String owner : owners) {
                receiverIds.add(Integer.valueOf(owner));
            }
            sendNotification(orderId, receiverIds, remindContent, "");
        }
    }

    private void sendNotification(String orderId, Set<Integer> receiverIds, String remindContent, String title) {
        if (CollectionUtils.notEmpty(receiverIds)) {
            CRMNotification crmNotification = CRMNotification.builder()
                    .sender(actionContext.getUser().getUserId())
                    .remindRecordType(RemindRecordEnum.ORDER_MODIFIED.getValue())
                    .title(title)
                    .content(remindContent)
                    .dataId(orderId)
                    .content2Id(actionContext.getUser().getUserId())
                    .receiverIds(receiverIds)
                    .build();
            executor.execute(() -> {
                crmNotificationService.sendCRMNotification(actionContext.getUser(), crmNotification);
            });
        }
    }

    @Override
    protected void recordLog() {
        List<IObjectData> dataToUpdate = this.getAllDataToUpdate();
        LogUtil.recordEditSpecailLog(actionContext.getUser(), dataToUpdate, objectData, objectDescribe);
        dataToUpdate.removeIf(it -> Utils.SALES_ORDER_API_NAME.equals(it.getDescribeApiName()));
        if (CollectionUtils.notEmpty(dataToUpdate)) {
            logAsync(dataToUpdate, EventType.MODIFY, ActionType.Modify);
        }
        logAsync(detailsToAdd, EventType.ADD, ActionType.Add);
        logAsync(detailsToDelete, EventType.MODIFY, ActionType.Delete);
    }

    private void afterInterceptor(Result result) {
        ServiceContext context = ContextManager.buildServiceContext("SalesOrderInterceptor",
                "EditAfter");
        SalesOrderEditAfterModel.Arg serviceArg = new SalesOrderEditAfterModel.Arg();
        IObjectData objectData = result.getObjectData().toObjectData();
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        serviceArg.setDataId(objectData.getId());
        serviceArg.setBeforeLifeStatus(arg.getObjectData().toObjectData().get("original_life_status", String.class));
        serviceArg.setAfterLifeStatus(objectDataExt.getLifeStatus().getCode());
        SalesOrderEditAfterModel.SalesOrderVo salesOrderVo = new SalesOrderEditAfterModel.SalesOrderVo();
        salesOrderVo.setCustomerId(objectData.get("account_id", String.class));
        salesOrderVo.setTradeId(objectData.getId());
        salesOrderVo.setWarehouseId(objectData.get("shipping_warehouse_id", String.class));
        List<ObjectDataDocument> details = arg.getDetails().getOrDefault(Utils.SALES_ORDER_PRODUCT_API_NAME, Lists.newArrayList());
        salesOrderVo.setSalesOrderProductVos(SalesOrderUtil.handleSalesOrderProductVos(details, isMultipleUnit));
        serviceArg.setSalesOrderVo(salesOrderVo);
        SalesOrderInterceptorModel.EditAfterResult editAfterResult = salesOrderBizProxy.editAfter(serviceArg, SoCommonUtils.getCrmHeader(context.getTenantId(), context.getUser()));
        if (!editAfterResult.isSuccess()) {
            throw new ValidateException(editAfterResult.getMessage());
        }
    }

    @Override
    protected void tryTriggerMasterApproval(ApprovalFlowTriggerType triggerType, ObjectAction action,
                                            IObjectData objectData, Map<String, Object> updateData,
                                            Map<String, IObjectDescribe> describeMap, Map<String, Object> freeApprovalDef) {
        if (!SalesOrderValidator.softApprovalFlow(actionContext)) {
            super.tryTriggerMasterApproval(triggerType, action, objectData, updateData, describeMap, freeApprovalDef);
        }
    }
}
