package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.utilities.constant.BomConstants;
import com.facishare.paas.appframework.core.predef.action.StandardExportAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/15 2:46 下午
 * @illustration
 */
public class BomExportAction extends StandardExportAction {

    @Override
    protected void before(Arg arg) {
        // 默认把唯一性id导出
        arg.setInclude_id(true);
        super.before(arg);
    }

    @Override
    protected List<IFieldDescribe> findFields(String describeApiName, String recordType) {
        List<IFieldDescribe> fields = super.findFields(describeApiName, recordType);
        fields.removeIf(field-> BomConstants.FILTER_THREE_FIELD.contains(field.getApiName()));
        return fields;
    }
}
