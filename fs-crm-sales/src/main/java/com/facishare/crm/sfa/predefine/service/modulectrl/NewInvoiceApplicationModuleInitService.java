package com.facishare.crm.sfa.predefine.service.modulectrl;

import com.facishare.converter.EIEAConverter;
import com.facishare.crm.openapi.Utils;
import com.facishare.crmcommon.rest.TemplateApi;
import com.facishare.crm.sfa.predefine.service.CrmMenuInitService;
import com.facishare.crm.sfa.predefine.service.DescService;
import com.facishare.crm.sfa.predefine.service.EnterpriseInitService;
import com.facishare.crm.sfa.predefine.service.OutApiService;
import com.facishare.crm.sfa.predefine.service.model.ConfigCtrlModule;
import com.facishare.crm.sfa.predefine.service.model.InitObjectByApiNameModel;
import com.facishare.crm.sfa.predefine.service.model.InitObjectInfo;
import com.facishare.crm.sfa.predefine.service.real.invoice.InvoiceLinesTransferService;
import com.facishare.crm.sfa.utilities.proxy.InitObjectsPermissionsAndLayoutProxy;
import com.facishare.paas.appframework.common.util.AppIdMapping;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.prm.util.PrmConstant;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.ILayoutService;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.component.TableComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
@Slf4j
public class NewInvoiceApplicationModuleInitService extends AbstractModuleInitService {
    //开票新增字段
    private final String taxAmountSumFieldStr = "{\"describe_api_name\":\"InvoiceApplicationObj\",\"return_type\":\"currency\",\"description\":\"税金\",\"is_unique\":false,\"type\":\"count\",\"decimal_places\":4,\"sub_object_describe_apiname\":\"InvoiceApplicationLinesObj\",\"is_required\":false,\"wheres\":[],\"define_type\":\"package\",\"is_single\":false,\"field_api_name\":\"invoice_id\",\"max_length\":14,\"is_index\":true,\"is_active\":true,\"count_type\":\"sum\",\"count_field_api_name\":\"tax_amount\",\"label\":\"税金\",\"count_to_zero\":true,\"is_need_convert\":false,\"api_name\":\"tax_amount_sum\",\"count_field_type\":\"currency\",\"is_index_field\":false,\"config\":{\"add\":0,\"edit\":1,\"enable\":1,\"display\":1,\"remove\":0,\"attrs\":{\"return_type\":0,\"count_type\":0,\"count_field_api_name\":0,\"label\":1,\"decimal_places\":1,\"sub_object_describe_apiname\":0,\"count_to_zero\":0,\"wheres\":1,\"is_required\":0,\"fe_target_object_field\":0,\"api_name\":0,\"count_field_type\":0,\"text_split_line\":0,\"help_text\":1,\"max_length\":1,\"field_api_name\":0}},\"help_text\":\"\",\"status\":\"released\"}";
    private final String newInvoiceAppliedAmountFieldStr = "{\"describe_api_name\":\"InvoiceApplicationObj\",\"return_type\":\"currency\",\"description\":\"新开票金额(元)\",\"is_unique\":false,\"type\":\"count\",\"decimal_places\":2,\"sub_object_describe_apiname\":\"InvoiceApplicationLinesObj\",\"is_required\":false,\"wheres\":[],\"define_type\":\"package\",\"is_single\":false,\"field_api_name\":\"invoice_id\",\"is_index\":true,\"is_active\":true,\"count_type\":\"sum\",\"length\":18,\"count_field_api_name\":\"invoiced_amount\",\"label\":\"开票金额(元)\",\"currency_unit\":\"￥\",\"count_to_zero\":true,\"is_need_convert\":false,\"api_name\":\"new_invoice_applied_amount\",\"count_field_type\":\"currency\",\"is_index_field\":false,\"config\":{\"add\":0,\"edit\":1,\"enable\":0,\"display\":1,\"remove\":0,\"attrs\":{\"return_type\":0,\"count_type\":0,\"default_value\":1,\"count_field_api_name\":0,\"label\":1,\"decimal_places\":1,\"sub_object_describe_apiname\":0,\"count_to_zero\":0,\"wheres\":0,\"is_required\":0,\"fe_target_object_field\":0,\"api_name\":0,\"count_field_type\":0,\"text_split_line\":0,\"help_text\":1,\"max_length\":1,\"field_api_name\":0}},\"help_text\":\"\",\"status\":\"released\"}";
    //订单新增字段
    private final String invoicedAmountFieldStr = "{ \"describe_api_name\": \"SalesOrderObj\", \"return_type\": \"currency\", \"description\": \"\", \"is_unique\": false, \"where_type\": \"field\", \"type\": \"count\", \"decimal_places\": 2, \"sub_object_describe_apiname\": \"SalesOrderProductObj\", \"is_required\": false, \"wheres\": [], \"define_type\": \"package\", \"is_single\": false, \"field_api_name\": \"order_id\", \"max_length\": 16, \"is_index\": true, \"is_active\": true, \"count_type\": \"sum\", \"length\": 18, \"count_field_api_name\": \"invoiced_amount\", \"label\": \"已开票金额\", \"currency_unit\": \"￥\", \"is_abstract\": null, \"count_to_zero\": true, \"is_need_convert\": false, \"api_name\": \"invoiced_amount\", \"count_field_type\": \"currency\", \"default_value\": \"0\", \"is_index_field\": false, \"config\": { \"add\": 0, \"edit\": 1, \"enable\": 0, \"display\": 1, \"remove\": 0, \"attrs\": { \"return_type\": 0, \"count_type\": 0, \"count_field_api_name\": 0, \"label\": 1, \"decimal_places\": 1, \"sub_object_describe_apiname\": 0, \"count_to_zero\": 0, \"wheres\": 0, \"is_required\": 1, \"fe_target_object_field\": 0, \"api_name\": 0, \"count_field_type\": 0, \"text_split_line\": 0, \"help_text\": 1, \"field_api_name\": 0 } }, \"help_text\": \"\", \"status\": \"new\" }";
    private final String noInvoiceAmountFieldStr = "{\"describe_api_name\":\"SalesOrderObj\",\"expression_type\":\"js\",\"return_type\":\"currency\",\"is_unique\":false,\"type\":\"formula\",\"decimal_places\":2,\"default_to_zero\":true,\"is_required\":false,\"define_type\":\"package\",\"is_single\":false,\"max_length\":16,\"is_index\":true,\"expression\":\"$order_amount$-$invoiced_amount$\",\"is_active\":true,\"length\":18,\"label\":\"待开票金额\",\"currency_unit\":\"￥\",\"is_abstract\":null,\"api_name\":\"no_invoice_amount\",\"is_index_field\":false,\"config\":{\"add\":0,\"edit\":1,\"enable\":0,\"display\":1,\"remove\":0,\"attrs\":{\"is_required\":1,\"api_name\":0,\"formula\":0,\"label\":1,\"help_text\":1,\"decimal_places\":1}},\"help_text\":\"\",\"status\":\"new\"}";
    private final String invoiceStatusFieldStr = "{\"describe_api_name\":\"SalesOrderObj\",\"is_use_value\":true,\"is_index\":true,\"expression\":\"IF($invoiced_amount$>=$order_amount$,\\\"1\\\",IF($invoiced_amount$>0,\\\"2\\\",\\\"3\\\"))\",\"is_active\":true,\"default_value\":\"\",\"is_unique\":false,\"label\":\"开票完成状态\",\"type\":\"select_one\",\"is_need_convert\":false,\"is_need_calculate\":true,\"is_required\":false,\"api_name\":\"invoice_status\",\"options\":[{\"not_usable\":false,\"label\":\"已开票\",\"value\":\"1\",\"config\":{\"edit\":1,\"enable\":1,\"remove\":1}},{\"not_usable\":false,\"label\":\"部分开票\",\"value\":\"2\",\"config\":{\"edit\":1,\"enable\":1,\"remove\":1}},{\"not_usable\":false,\"label\":\"未开票\",\"value\":\"3\",\"config\":{\"edit\":1,\"enable\":1,\"remove\":1}}],\"define_type\":\"system\",\"is_index_field\":false,\"is_single\":false,\"config\":{\"add\":0,\"edit\":0,\"enable\":0,\"display\":1,\"remove\":0,\"attrs\":{\"is_required\":0,\"api_name\":0,\"options\":0,\"is_unique\":0,\"default_value\":0,\"label\":0,\"help_text\":1}},\"help_text\":\"\",\"status\":\"new\"}";
    //订单产品新增字段
    private final String orderProductAmountFieldStr = "{\"describe_api_name\":\"SalesOrderProductObj\",\"expression_type\":\"js\",\"return_type\":\"number\",\"is_index\":true,\"expression\":\"$subtotal$*$order_id__r.discount$\",\"is_active\":true,\"is_unique\":false,\"label\":\"折后金额\",\"type\":\"formula\",\"decimal_places\":2,\"default_to_zero\":true,\"is_required\":false,\"api_name\":\"order_product_amount\",\"define_type\":\"package\",\"is_single\":false,\"is_index_field\":false,\"default_value\":\"0\",\"config\":{\"add\":0,\"edit\":0,\"enable\":0,\"display\":1,\"remove\":0,\"attrs\":{\"is_required\":1,\"api_name\":0,\"formula\":0,\"label\":1,\"help_text\":0,\"decimal_places\":1}},\"help_text\":\"\",\"status\":\"new\"}";
    private final String invoicedQuantityFieldStr = "{\"describe_api_name\":\"SalesOrderProductObj\",\"return_type\":\"number\",\"is_unique\":false,\"where_type\":\"field\",\"type\":\"count\",\"decimal_places\":4,\"sub_object_describe_apiname\":\"InvoiceApplicationLinesObj\",\"is_required\":false,\"wheres\":[{\"connector\":\"OR\",\"filters\":[{\"value_type\":0,\"operator\":\"EQ\",\"field_name\":\"life_status\",\"field_values\":[\"normal\"]}]},{\"connector\":\"OR\",\"filters\":[{\"value_type\":0,\"operator\":\"EQ\",\"field_name\":\"life_status\",\"field_values\":[\"in_change\"]}]}],\"define_type\":\"package\",\"is_single\":false,\"field_api_name\":\"order_product_id\",\"is_index\":true,\"is_active\":true,\"count_type\":\"sum\",\"count_field_api_name\":\"invoiced_quantity\",\"label\":\"已开票数量\",\"count_to_zero\":true,\"default_value\":\"0\",\"api_name\":\"invoiced_quantity\",\"count_field_type\":\"number\",\"is_index_field\":false,\"config\":{\"add\":0,\"edit\":1,\"enable\":0,\"display\":1,\"remove\":0,\"attrs\":{\"return_type\":0,\"count_type\":0,\"count_field_api_name\":0,\"label\":1,\"decimal_places\":1,\"sub_object_describe_apiname\":0,\"count_to_zero\":1,\"wheres\":0,\"fe_target_object_field\":0,\"api_name\":0,\"count_field_type\":0,\"text_split_line\":0,\"help_text\":0,\"field_api_name\":0}},\"help_text\":\"\",\"status\":\"new\"}";
    private final String invoicedAmountDetailFieldStr = "{\"describe_api_name\":\"SalesOrderProductObj\",\"return_type\":\"currency\",\"is_unique\":false,\"type\":\"count\",\"decimal_places\":2,\"default_value\":\"0\",\"sub_object_describe_apiname\":\"InvoiceApplicationLinesObj\",\"is_required\":false,\"wheres\":[{\"connector\":\"OR\",\"filters\":[{\"value_type\":0,\"operator\":\"EQ\",\"field_name\":\"life_status\",\"field_values\":[\"normal\"]}]},{\"connector\":\"OR\",\"filters\":[{\"value_type\":0,\"operator\":\"EQ\",\"field_name\":\"life_status\",\"field_values\":[\"in_change\"]}]}],\"define_type\":\"package\",\"is_single\":false,\"field_api_name\":\"order_product_id\",\"is_index\":true,\"is_active\":true,\"count_type\":\"sum\",\"count_field_api_name\":\"invoiced_amount\",\"label\":\"已开票金额\",\"count_to_zero\":true,\"api_name\":\"invoiced_amount\",\"count_field_type\":\"number\",\"is_index_field\":false,\"config\":{\"add\":0,\"edit\":1,\"enable\":0,\"display\":1,\"remove\":0,\"attrs\":{\"return_type\":0,\"count_type\":0,\"count_field_api_name\":0,\"label\":0,\"decimal_places\":1,\"sub_object_describe_apiname\":0,\"count_to_zero\":0,\"wheres\":0,\"fe_target_object_field\":0,\"api_name\":0,\"count_field_type\":0,\"text_split_line\":0,\"help_text\":0,\"field_api_name\":0}},\"help_text\":\"\",\"status\":\"new\"}";
    private final String noInvoiceQuantityFieldStr = "{\"describe_api_name\":\"SalesOrderProductObj\",\"expression_type\":\"js\",\"return_type\":\"number\",\"is_index\":true,\"expression\":\"$quantity$-$invoiced_quantity$\",\"is_active\":true,\"is_unique\":false,\"label\":\"待开票数量\",\"type\":\"formula\",\"decimal_places\":4,\"default_to_zero\":true,\"is_required\":false,\"api_name\":\"no_invoice_quantity\",\"define_type\":\"package\",\"is_single\":false,\"default_value\":\"0\",\"is_index_field\":false,\"config\":{\"add\":0,\"edit\":1,\"enable\":0,\"display\":1,\"remove\":0,\"attrs\":{\"api_name\":0,\"formula\":0,\"label\":1,\"help_text\":0}},\"help_text\":\"\",\"status\":\"new\"}";
    private final String noInvoiceAmountDetailFieldStr = "{\"describe_api_name\":\"SalesOrderProductObj\",\"expression_type\":\"js\",\"return_type\":\"currency\",\"is_index\":true,\"expression\":\"$order_product_amount$-$invoiced_amount$\",\"is_active\":true,\"is_unique\":false,\"label\":\"待开票金额\",\"type\":\"formula\",\"decimal_places\":2,\"default_to_zero\":true,\"is_required\":false,\"api_name\":\"no_invoice_amount\",\"define_type\":\"package\",\"default_value\":\"0\",\"is_single\":false,\"is_index_field\":false,\"config\":{\"add\":0,\"edit\":1,\"enable\":0,\"display\":1,\"remove\":0,\"attrs\":{\"api_name\":0,\"formula\":0,\"label\":1,\"help_text\":0}},\"help_text\":\"\",\"status\":\"new\"}";
    private final String invoiceStatusDetailFieldStr = "{\"describe_api_name\":\"SalesOrderProductObj\",\"is_use_value\":true,\"is_index\":true,\"expression\":\"IF($invoiced_amount$>=$order_product_amount$, \\\"1\\\",IF($invoiced_amount$>0, \\\"2\\\", \\\"3\\\"))\",\"is_active\":true,\"default_value\":\"\",\"is_unique\":false,\"label\":\"开票完成状态\",\"type\":\"select_one\",\"is_need_convert\":false,\"is_need_calculate\":true,\"is_required\":false,\"api_name\":\"invoice_status\",\"options\":[{\"not_usable\":false,\"label\":\"全部开票\",\"value\":\"1\",\"config\":{\"edit\":1,\"enable\":1,\"remove\":1}},{\"not_usable\":false,\"label\":\"部分开票\",\"value\":\"2\",\"config\":{\"edit\":1,\"enable\":1,\"remove\":1}},{\"not_usable\":false,\"label\":\"未开票\",\"value\":\"3\",\"config\":{\"edit\":1,\"enable\":1,\"remove\":1}}],\"define_type\":\"system\",\"is_index_field\":false,\"is_single\":false,\"config\":{\"add\":0,\"edit\":0,\"enable\":0,\"display\":1,\"remove\":0,\"attrs\":{\"is_required\":0,\"api_name\":0,\"options\":0,\"is_unique\":0,\"default_value\":0,\"label\":0,\"help_text\":1}},\"help_text\":\"\",\"status\":\"new\"}";
    private final String DESC_API_NAME = "InvoiceApplicationLinesObj";

    @Autowired
    IObjectDescribeService objectDescribeService;
    @Autowired
    private EnterpriseInitService enterpriseInitService;
    @Autowired
    ConfigService configService;

    @Autowired
    ServiceFacade serviceFacade;

    @Autowired
    CrmMenuInitService crmMenuInitService;

    @Autowired
    InvoiceLinesTransferService invoiceLinesTransferService;

    @Autowired
    private TemplateApi templateApi;

    @Autowired
    private DescService descService;

    @Override
    public String getModuleCode() {
        return IModuleInitService.MODULE_NEW_INVOICE;
    }

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private InitObjectsPermissionsAndLayoutProxy initObjectsPermissionsAndLayoutProxy;

    @Autowired
    private OutApiService outApiService;

    @Override
    public ConfigCtrlModule.Result initModule(String tenantId, String userId) {
        try {
            initDescribe(tenantId);
            addNewObjItem(new User(tenantId, "-10000"));
            updateMasterDesc(tenantId);
            updateSalesOrderProductDesc(tenantId);
            updateSalesOrderDesc(tenantId);
            initPrintTemplate(tenantId);
            addPRM(tenantId);
            invoiceLinesTransfer(tenantId, userId);

        } catch (Exception e) {
            log.error("InitNewInvoice failed:{}", e);
            sendAuditLog(tenantId, userId, e.toString(), "InitNewInvoiceFailed");
            return ConfigCtrlModule.Result.builder()
                    .errCode(ConfigCtrlModule.ResultInfo.Fail.getErrCode())
                    .errMessage(e.getMessage())
                    .value(
                            ConfigCtrlModule.Value.builder()
                                    .openStatus(ConfigCtrlModule.OpenStatus.CLOSE.toString()).build()
                    ).build();
        }
        return ConfigCtrlModule.Result.builder()
                .errCode(ConfigCtrlModule.ResultInfo.Sucess.getErrCode())
                .errMessage(ConfigCtrlModule.ResultInfo.Sucess.getErrMessage())
                .value(
                        ConfigCtrlModule.Value.builder()
                                .openStatus(ConfigCtrlModule.OpenStatus.OPEN.toString()).build()
                ).build();

    }

    private void invoiceLinesTransfer(String tenantId, String userId) {
        int result = invoiceLinesTransferService.invoiceLinesTransfer(tenantId, userId);
        if (result==0){
            // TODO: 2020-03-16 多语
            throw new ValidateException("正在迁移数据请稍等。");
        }
        if (result==1){
            throw new ValidateException("数据较多，已经创建任务晚上迁移，请明天使用。");
        }

    }

    private void addPRM(String tenantId) {
        try {
            if (!outApiService.isPrmOpen(new ServiceContext(RequestContext.builder().tenantId(tenantId).build(), null, null)))
                return;
            List<InitObjectInfo> objectInfoList = Lists.newArrayList();
            String apiName = DESC_API_NAME;
            List<String> permissions = Lists.newArrayList();
            permissions.add(apiName);
            permissions.add(apiName + "||View");
            permissions.add(apiName + "||Add");
            permissions.add(apiName + "||Edit");
            permissions.add(apiName + "||Abolish");
            permissions.add(apiName + "||Relate");
            permissions.add(apiName + "||Lock");
            permissions.add(apiName + "||Unlock");
            permissions.add(apiName + "||ChangePartnerOwner");
            permissions.add(apiName + "||ViewEntireBPM");
            permissions.add(apiName + "||StartBPM");
            permissions.add(apiName + "||ChangeBPMApprover");
            permissions.add(apiName + "||StopBPM");
            objectInfoList.add(InitObjectInfo.builder().objectApiName(apiName).permissions(permissions).build());

            String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(tenantId));

            InitObjectByApiNameModel model = InitObjectByApiNameModel.builder()
                    .appId(AppIdMapping.appIdMapping.get(PrmConstant.PRM_APP_ID))
                    .appType(1)
                    .objectInfos(objectInfoList)
                    .upstreamEas(Lists.newArrayList(ea))
                    .build();
            Map<String, String> headers = Maps.newHashMap();
            headers.put("Content-Type", "application/json");
            initObjectsPermissionsAndLayoutProxy.initObjectsPermissionsAndLayout(headers, model);
        } catch (Exception e) {
            log.error("new_invoice addPRM error,tenantId:{}", tenantId, e);
        }


    }


    /**
     * 开票新加两个字段，禁用order_id
     */
    private void updateMasterDesc(String tenantId) {
        IObjectDescribe invoiceApplicationDesc = serviceFacade.findObject(tenantId, Utils.INVOICE_APPLICATION_API_NAME);
        IFieldDescribe invoiceAppliedAmount = invoiceApplicationDesc.getFieldDescribe("invoice_applied_amount");
        if (invoiceAppliedAmount != null) {
            invoiceAppliedAmount.setLabel("开票金额（元）（原）");
        }
        serviceFacade.update(invoiceApplicationDesc);

        try {
            descService.updateField(tenantId, Utils.INVOICE_APPLICATION_API_NAME, Lists.newArrayList(), Lists.newArrayList("invoice_applied_amount"));
            descService.hideField(tenantId, Utils.INVOICE_APPLICATION_API_NAME, Lists.newArrayList("invoice_applied_amount"));
            descService.addAndRemoveField(tenantId, Utils.INVOICE_APPLICATION_API_NAME,
                    Lists.newArrayList(newInvoiceAppliedAmountFieldStr, taxAmountSumFieldStr), Lists.newArrayList("order_id"));

            ILayoutService layoutService = SpringUtil.getContext().getBean(ILayoutService.class);
            List<Layout> byTypes = layoutService.findByTypes(tenantId, Lists.newArrayList(ILayout.LIST_LAYOUT_TYPE),
                    Utils.INVOICE_APPLICATION_API_NAME);
            for (Layout byType : byTypes) {
                TableComponent iComponent = (TableComponent) byType.getComponents().get(0);
                iComponent.getFieldSections().get(0).getFields().forEach(o -> {
                    if ("invoice_applied_amount".equals(o.get("api_name"))) {
                        o.setFieldName("new_invoice_applied_amount");
                        o.set("api_name", "new_invoice_applied_amount");
                    }
                    if ("invoice_applied_amount".equals(o.getFieldName())) {
                        o.setFieldName("new_invoice_applied_amount");
                    }
                });
                layoutService.replace(byType);
            }
        } catch (MetadataServiceException e) {
            log.error("NewInvoiceApplicationModuleInitService updateMasterDesc,tenantId:{}", tenantId, e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 订单新加三个字段
     */
    private void updateSalesOrderDesc(String tenantId) {
        try {
            IObjectDescribe describe = serviceFacade.findObject(tenantId, Utils.SALES_ORDER_API_NAME);
            describe.getFieldDescribe("invoice_amount").setLabel(describe.getFieldDescribe("invoice_amount").getLabel()+"（原）");
            serviceFacade.update(describe);
            descService.addAndRemoveField(tenantId, Utils.SALES_ORDER_API_NAME,
                    Lists.newArrayList(invoicedAmountFieldStr, noInvoiceAmountFieldStr, invoiceStatusFieldStr), Lists.newArrayList("invoice_amount"));
        } catch (MetadataServiceException e) {
            log.error("NewInvoiceApplicationModuleInitService updateSalesOrderDesc,tenantId:{}", tenantId, e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 订单产品新加六个字段，禁用一个
     */
    private void updateSalesOrderProductDesc(String tenantId) {
        try {
            descService.addAndRemoveField(tenantId, Utils.SALES_ORDER_PRODUCT_API_NAME,
                    Lists.newArrayList(orderProductAmountFieldStr, invoiceStatusDetailFieldStr, noInvoiceAmountDetailFieldStr, noInvoiceQuantityFieldStr, invoicedAmountDetailFieldStr, invoicedQuantityFieldStr),
                    Lists.newArrayList());
        } catch (MetadataServiceException e) {
            log.error("NewInvoiceApplicationModuleInitService updateSalesOrderProductDesc,tenantId:{}", tenantId, e);
            throw new RuntimeException(e);
        }
    }


    private void initDescribe(String tenantId) {
        IObjectDescribe describe = null;
        try {
            describe = serviceFacade.findObject(tenantId, DESC_API_NAME);
        } catch (Exception e) {
            log.error("查询 开票明细描述error tenantId:{}", tenantId, e);
        }
        if (describe == null) {
            enterpriseInitService.initDescribeForTenant(tenantId, DESC_API_NAME);
        } else if (!describe.isActive()) {
            serviceFacade.enableDescribe(RequestContextManager.getContext().getUser(), DESC_API_NAME);
        }
        enterpriseInitService.initPrivilegeRelate(Lists.newArrayList(DESC_API_NAME), new User(tenantId, "-10000"), null, null, null);
        enterpriseInitService.initMultiLayoutForOneTenant(Lists.newArrayList(DESC_API_NAME), tenantId);
    }


    /**
     * 刷菜单
     */
    private void addNewObjItem(User user) {
        crmMenuInitService.createMenuItem(user, Lists.newArrayList("InvoiceApplicationLinesObj"), "InvoiceApplicationObj");
    }

    @Override
    public ConfigCtrlModule.Result initModuleRepair(ServiceContext context, String tenantId) {
        String moduleConfig = configService.findTenantConfig(context.getUser(), getModuleCode());
        if (StringUtils.isEmpty(moduleConfig)) {
            configService.createTenantConfig(context.getUser(), getModuleCode(), ConfigCtrlModule.OpenStatus.OPEN.toString(), ConfigValueType.STRING);
        } else {
            configService.updateTenantConfig(context.getUser(), getModuleCode(), ConfigCtrlModule.OpenStatus.OPEN.toString(), ConfigValueType.STRING);
        }

        return ConfigCtrlModule.Result.builder()
                .errCode(ConfigCtrlModule.ResultInfo.Sucess.getErrCode())
                .errMessage(ConfigCtrlModule.ResultInfo.Sucess.getErrMessage())
                .value(
                        ConfigCtrlModule.Value.builder()
                                .openStatus(ConfigCtrlModule.OpenStatus.OPEN.toString()).build()
                ).build();
    }


    private void initPrintTemplate(String tenantId) {
        Map headers = new HashMap();
        headers.put("x-tenant-id", tenantId);
        headers.put("x-user-id", User.SUPPER_ADMIN_USER_ID);
        headers.put("Content-Type", "application/json");

        Map pathMap = new HashMap();
        pathMap.put("tenantId", tenantId);

        Map queryMap = new HashMap();
        queryMap.put("initDescribeApiNames", "new_InvoiceApplicationObj");

        try {
            Object result = templateApi.init(pathMap, queryMap, headers);
        } catch (Exception e) {
            log.error("NewInvoiceApplicationModuleInitService templateApi.init (pathMap:{}, queryMap:{}, headers:{}", pathMap, queryMap, headers, e);
        }
    }


    @Override
    public ConfigCtrlModule.Result closeModule(String tenantId) {
        //1.删掉开票明细的描述
        IObjectDescribe describe = null;
        try {
            describe = serviceFacade.findObject(tenantId, DESC_API_NAME);
        } catch (Exception e) {
            log.error("查询 开票明细描述error tenantId:{}", tenantId, e);
        }
        if (Objects.nonNull(describe)) {
            serviceFacade.disableDescribe(RequestContextManager.getContext().getUser(), DESC_API_NAME);
//            serviceFacade.deleteDescribe(RequestContextManager.getContext().getUser(), DESC_API_NAME);
//            serviceFacade.deleteDescribeDirect()
        }
        //2.复原开票的描述，a.激活order_id字段；b.new_invoice_applied_amount字段复原；c.tax_amount_sum字段删除
        IObjectDescribe invoiceApplicationDesc = serviceFacade.findObject(tenantId, Utils.INVOICE_APPLICATION_API_NAME);
        IFieldDescribe orderIdFieldDescribe = invoiceApplicationDesc.getFieldDescribe("order_id");
        if (orderIdFieldDescribe != null) {
            orderIdFieldDescribe.setActive(true);
        }
        serviceFacade.update(invoiceApplicationDesc);
        try {
            descService.addAndRemoveField(tenantId, Utils.INVOICE_APPLICATION_API_NAME,
                    Lists.newArrayList(),
                    Lists.newArrayList("tax_amount_sum", "new_invoice_applied_amount"));
        } catch (MetadataServiceException e) {
            log.error("NewInvoiceApplicationModuleInitService closeModule 2,tenantId:{}", tenantId, e);
            throw new RuntimeException(e);
        }

        //3.复原订单描述，删除三个字段：invoiced_amount、no_invoice_amount、invoice_status
        try {
            descService.addAndRemoveField(tenantId, Utils.SALES_ORDER_API_NAME,
                    Lists.newArrayList(),
                    Lists.newArrayList("invoiced_amount", "no_invoice_amount", "invoice_status"));
        } catch (MetadataServiceException e) {
            log.error("NewInvoiceApplicationModuleInitService closeModule 3,tenantId:{}", tenantId, e);
            throw new RuntimeException(e);
        }

        //4.复原订单产品描述，a.激活一个字段discount b.删除六个字段：invoiced_amount、no_invoice_amount、invoice_status
//        IObjectDescribe salesOrderProduct = serviceFacade.findObject(tenantId, Utils.SALES_ORDER_PRODUCT_API_NAME);
//        IFieldDescribe discount = salesOrderProduct.getFieldDescribe("discount");
//        if (discount != null) {
//            discount.setActive(true);
//        }
        try {
            descService.addAndRemoveField(tenantId, Utils.SALES_ORDER_PRODUCT_API_NAME,
                    Lists.newArrayList(),
                    Lists.newArrayList("order_product_amount", "invoiced_quantity", "invoiced_amount",
                            "no_invoice_quantity", "no_invoice_amount", "invoice_status"));
        } catch (MetadataServiceException e) {
            log.error("NewInvoiceApplicationModuleInitService closeModule 4,tenantId:{}", tenantId, e);
            throw new RuntimeException(e);
        }

        return null;
    }

    @Override
    public String openStatus() {
        return ConfigCtrlModule.OpenStatus.OPENING.getStatusCode();
    }
}

