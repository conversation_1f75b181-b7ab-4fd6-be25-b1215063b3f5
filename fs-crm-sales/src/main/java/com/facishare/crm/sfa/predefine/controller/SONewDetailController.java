package com.facishare.crm.sfa.predefine.controller;

import com.facishare.crm.sfa.utilities.util.ButtonUtils;
import com.facishare.crm.sfa.utilities.util.LayoutUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardNewDetailController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;

/**
 * <AUTHOR> 2019-11-28
 * @instruction
 */
public class SONewDetailController extends StandardNewDetailController {

    @Override
    protected ILayout getLayout() {
        ILayout layout = super.getLayout();
        ButtonUtils.removeMobileEditButton(layout);
        LayoutExt layoutExt = LayoutExt.of(layout);
        LayoutUtils.removeButtons(layoutExt, Lists.newArrayList(ObjectAction.CLONE.getActionCode()));
        return layoutExt.getLayout();
    }
}
