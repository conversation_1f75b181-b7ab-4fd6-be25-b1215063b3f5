package com.facishare.crm.sfa.utilities.proxy.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

public interface GetPromotionByAccountIdModel {
    @Data
    @Builder
    class Arg {
        private String customerId;
    }
    @Data
    class Result{
        String errCode;
        String errMessage;
        ResultData result;
    }

    @Data
    class ResultData{
        private List<PromotionModel.PromotionEntity> promotions;
    }
}
