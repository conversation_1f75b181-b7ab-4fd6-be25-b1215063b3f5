package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.sfa.model.Enum.LeadsBizStatusEnum;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardCloneAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Map;

public class LeadsCloneAction extends StandardCloneAction {

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        return result;
    }

    private static List<String> leadsResetNullToField = Lists.newArrayList("_id", "collected_to", "is_collected",
            "expire_time", "last_follow_time", "owner_change_time", "remind_days", "close_reason", "is_overtime",
            "assigned_time","returned_time", "back_reason", "partner_id", "out_resources", "out_tenant_id", "out_owner",
            "picture_path", "assigner_id", "completed_result", "last_follower", "transform_time", "conversion_probability",
            "enterprise_wechat_user_id","leads_stage_changed_time"
    );

    @Override
    public void filterOrResetFieldValue(IObjectDescribe objectDescribe, IObjectData objectData, List<IObjectDescribe> detailDescribes, Map<String, List<IObjectData>> detailDataMap) {
        super.filterOrResetFieldValue(objectDescribe, objectData, detailDescribes, detailDataMap);
        leadsResetNullToField.forEach(o->{
            objectData.set(o, null);
        });
    }
}
