package com.facishare.crm.sfa.predefine.service.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.sfa.utilities.proxy.model.PromotionModel;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


public interface GetPromotionsBySalesOrderProductIdsRest {
    @Data
    class Arg implements Serializable {
        @JSONField(name = "account_id")
        @JsonProperty("account_id")
        private String accountId;
        @JSONField(name = "product_ids")
        @JsonProperty("product_ids")
        private List<String> product_ids;
    }

    @Data
    @Builder
    class DetailResult {
        @JSONField(name = "product_id")
        @JsonProperty("product_id")
        private String product_id;
        @JSONField(name = "promotions")
        @JsonProperty("promotions")
        private List<PromotionsRestModel.PromotionRestEntity> promotions;
    }

    @Data
    @Builder
    class Result {
        @JSONField(name = "result")
        @JsonProperty("result")
        private List<DetailResult> promotions;
    }
}
