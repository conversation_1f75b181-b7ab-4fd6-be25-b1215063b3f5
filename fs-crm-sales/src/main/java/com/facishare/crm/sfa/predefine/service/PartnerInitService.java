package com.facishare.crm.sfa.predefine.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.crmcommon.constants.CommonConstants;
import com.facishare.crmcommon.describebuilder.UseScopeFieldDescribeBuilder;
import com.facishare.crm.openapi.Utils;
import com.facishare.crmcommon.rest.ApprovalInitProxy;
import com.facishare.crmcommon.rest.CrmRestApi;
import com.facishare.crmcommon.rest.dto.ApprovalInitModel;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.service.model.AssignRecordAndLayoutModel;
import com.facishare.crm.sfa.predefine.service.model.InitObjectByApiNameModel;
import com.facishare.crm.sfa.predefine.service.model.InitObjectInfo;
import com.facishare.crm.sfa.utilities.common.convert.SearchUtil;
import com.facishare.crm.sfa.utilities.constant.PriceBookConstants;
import com.facishare.crm.sfa.utilities.proxy.InitObjectsPermissionsAndLayoutProxy;
import com.facishare.crm.sfa.utilities.util.JsonObjectUtils;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.crm.sfa.utilities.util.SOI18NKeyUtils;
import com.facishare.crmcommon.util.GsonUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.common.util.AppIdMapping;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.DescribeResult;
import com.facishare.paas.appframework.metadata.dto.RecordTypeResult;
import com.facishare.paas.appframework.metadata.dto.auth.*;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.privilege.RoleService;
import com.facishare.paas.appframework.privilege.model.role.ChannelManagerRoleProvider;
import com.facishare.paas.appframework.privilege.model.role.Role;
import com.facishare.paas.appframework.privilege.util.PrivilegeConstants;
import com.facishare.paas.appframework.prm.util.PrmConstant;
import com.facishare.paas.auth.model.RoleViewPojo;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefAction;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.UdefAction;
import com.facishare.paas.metadata.impl.UdefButton;
import com.facishare.paas.metadata.impl.describe.*;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.impl.ui.layout.FieldSection;
import com.facishare.paas.metadata.impl.ui.layout.FormField;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.facishare.paas.metadata.service.impl.LayoutServiceImpl;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

import static com.facishare.crmcommon.constants.LayoutConstants.BASE_FIELD_SECTION_API_NAME;
import static com.facishare.paas.appframework.core.model.User.SUPPER_ADMIN_USER_ID;
import static com.facishare.paas.appframework.metadata.ObjectDescribeExt.PREDEFINE_CUSTOM_OBJECTS;
import static com.facishare.paas.appframework.privilege.util.Headers.PAAS_PRIVILEGE_HEADDER;

/**
 * @IgnoreI18nFile
 */
@ServiceModule("partner_init")
@Service
@Slf4j
public class PartnerInitService {
    @Autowired
    ServiceFacade serviceFacade;
    @Autowired
    RecordTypeLogicServiceImpl recordTypeLogicService;
    @Autowired
    EnterpriseInitService enterpriseInitService;
    @Autowired
    ConfigService configService;
    @Resource(type = ApprovalInitProxy.class)
    ApprovalInitProxy approvalInitProxy;
    @Autowired
    RoleService roleService;
    @Autowired
    ChannelManagerRoleProvider channelManagerRoleProvider;
    @Autowired
    private IObjectDescribeService objectDescribeService;
    @Autowired
    LayoutServiceImpl layoutService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    InitObjectsPermissionsAndLayoutProxy initObjectsPermissionsAndLayoutProxy;
    @Autowired
    private CrmMenuInitService crmMenuInitService;
    @Autowired
    private RecordTypeAuthProxy recordTypeAuthProxy;// 分配业务类型和默认布局
    @Autowired
    private CustomButtonServiceImpl buttonService;
    @Autowired
    private PostActionService actionService;
    @Autowired
    private FunctionPrivilegeService functionPrivilegeService;

    private String configPartnerIsOpenKey = "config_partner_open";
    private String statusOpen = "open";
    private String statusClose = "close";

    public static final List<String> PrmUnSupportApiName = Lists.newArrayList(new String[]
            {"AccountFinInfoObj", "AccountAddrObj", "SalesOrderProductObj", "ReturnedGoodsInvoiceProductObj"}
    );

    /**
     * 合作伙伴开启
     */
    @ServiceMethod("status_open")
    public Map openStatus(ServiceContext context) {
        String configPartnerIsOpen = configService.findTenantConfig(context.getUser(), configPartnerIsOpenKey);
        Map map = Maps.newHashMap();
        map.put("result", StringUtils.isEmpty(configPartnerIsOpen) ? statusClose : configPartnerIsOpen);
        return map;
    }

    /**
     * 合作伙伴开启
     * 该方法已迁移，不在使用
     * 迁移后为：com.facishare.crm.management.service.config.OpenPartnerBizConfigProvider#setConfigValue(com.facishare.paas.appframework.core.model.User, java.lang.String, java.lang.String, java.lang.String)
     */
    @ServiceMethod("open")
    public Map openService(ServiceContext context) {
        String msg = this.open(context.getUser(), context);
        Map map = Maps.newHashMap();
        map.put("message", I18N.text(SOI18NKeyUtils.SO_PARTNER_OPENSUCCESS));
        map.put("code", 0);
        map.put("processMsg", msg);
        return map;
    }


    /**
     * 自定义对象绑定合作伙伴
     */
    @ServiceMethod("openObject")
    public boolean openServiceForObject(ServiceContext context, List<String> apiNames) {

        String openApiNamesString = configService.findTenantConfig(context.getUser(), PrmConstant.CONFIG_PARTNER_KEY);
        if (!StringUtils.isEmpty(openApiNamesString)) {
            List<String> openApiNames = JSON.parseArray(openApiNamesString, String.class);
            apiNames.removeAll(openApiNames);
        }
        if (!apiNames.isEmpty()) {
            openObject(context.getUser(), apiNames);
        }
        return true;
    }

    /**
     * 获取未绑定合作伙伴的自定义对象
     */
    @ServiceMethod("getDescribeListNoPartner")
    public List<ObjectDescribeDocument> getDescribeListNoPartner(ServiceContext context) {
        //用crm管理员权限获取自定义对象
        User user = new User(context.getTenantId(), User.SUPPER_ADMIN_USER_ID);
        List<String> users = serviceFacade.getUsersByRole(user, CommonConstants.CRM_MANAGER_ROLE);
        if (!users.isEmpty()) {
            user = new User(context.getTenantId(), users.get(0));
        }
        List<IObjectDescribe> describeList = serviceFacade.findDescribeByPrivilegeAndModule(user,
                ObjectAction.VIEW_LIST.getActionCode(), false, true, false, true);
//        describeList.removeIf(f -> PrmConstant.predefineCustomObjects.contains(f.getApiName()));
        describeList.removeIf(r -> !IObjectDescribe.DEFINE_TYPE_CUSTOM.equals(r.getDefineType()));
        describeList.removeIf(f -> PREDEFINE_CUSTOM_OBJECTS.contains(f.getApiName()));
        describeList.removeIf(f -> PrmConstant.menuApiNames.contains(f.getApiName()));
        describeList.removeIf(f -> PrmUnSupportApiName.contains(f.getApiName()));
        String openApiNamesString = configService.findTenantConfig(context.getUser(), PrmConstant.CONFIG_PARTNER_KEY);
        if (!StringUtils.isEmpty(openApiNamesString)) {
            List<String> openApiNames = JSON.parseArray(openApiNamesString, String.class);
            describeList.removeIf(f -> openApiNames.contains(f.getApiName()));
        }
        for (IObjectDescribe describe : describeList) {
            describe.setFieldDescribes(Lists.newArrayList());
            describe.setActions(Lists.newArrayList());
            describe.setConfig(Maps.newHashMap());
        }

        return ObjectDescribeDocument.ofList(describeList);
    }

    /**
     * 获取绑定合作伙伴的所有对象
     */
    @ServiceMethod("getDescribeListHasPartner")
    public List<ObjectDescribeDocument> getDescribeListHasPartner(ServiceContext context) {
        List<IObjectDescribe> describeList = Lists.newArrayList();
        String openApiNamesString = configService.findTenantConfig(context.getUser(), PrmConstant.CONFIG_PARTNER_KEY);
        List<String> openApiNames = Lists.newArrayList();
        openApiNames.addAll(PrmConstant.menuApiNames);
        if (!StringUtils.isEmpty(openApiNamesString)) {
            openApiNames.addAll(JSON.parseArray(openApiNamesString, String.class));
        }
        Map<String, IObjectDescribe> objects = serviceFacade.findObjects(context.getTenantId(), openApiNames);

        describeList.addAll(objects.values());

        //处理对象排序 老对象在上面
        List<IObjectDescribe> retdescribeList = Lists.newArrayList();
        for (String apiName : openApiNames) {
            for (IObjectDescribe iObjectDescribe : describeList) {
                if (iObjectDescribe.getApiName().equals(apiName)) {
                    retdescribeList.add(iObjectDescribe);
                }
            }
        }


        for (IObjectDescribe describe : retdescribeList) {
            describe.setFieldDescribes(Lists.newArrayList());
            describe.setActions(Lists.newArrayList());
            describe.setConfig(Maps.newHashMap());
        }
        return ObjectDescribeDocument.ofList(retdescribeList);
    }

    /**
     * 开启prm应用是,初始化下游老对象的布局和角色绑定。
     *
     * @param tenantId
     * @return
     */
    @Deprecated
    public String initLayoutAssignRecordTypeForDownstreamTenants(String tenantId) {
        User user = new User(tenantId, SUPPER_ADMIN_USER_ID);
        StringBuilder sb = new StringBuilder();
        //TODO 给5个对象初始化下游专用的布局JSON。
        //1.初始化客户布局
        try {
            String layOutJsonAccount = enterpriseInitService.getLayoutJsonFromResourceByApiName(Utils.ACCOUNT_API_NAME, "prm_downstream");
            ILayout layoutAccount = new Layout(Document.parse(layOutJsonAccount));
            serviceFacade.createLayout(user, layoutAccount);
            sb.append(" \n createAccountPartnerLayout success");
        } catch (Exception e) {
            sb.append(" \n createAccountPartnerLayout error:" + e.getMessage());
            log.error("Account_init open createAccountPartnerLayout error,tenantId {}", tenantId, e);
        }
        //2.初始化线索布局
        try {
            String layOutJsonLeads = enterpriseInitService.getLayoutJsonFromResourceByApiName(Utils.LEADS_API_NAME, "prm_downstream");
            ILayout layoutLeads = new Layout(Document.parse(layOutJsonLeads));
            serviceFacade.createLayout(user, layoutLeads);
            sb.append(" \n createLeadsPartnerLayout success");
        } catch (Exception e) {
            sb.append(" \n createLeadsPartnerLayout error:" + e.getMessage());
            log.error("Leads_init open createLeadsPartnerLayout error,tenantId {}", tenantId, e);
        }
        //3.初始化商机布局
        try {
            String layOutJsonOpportunity = enterpriseInitService.getLayoutJsonFromResourceByApiName(Utils.OPPORTUNITY_API_NAME, "prm_downstream");
            ILayout layoutOpportunity = new Layout(Document.parse(layOutJsonOpportunity));
            serviceFacade.createLayout(user, layoutOpportunity);
            sb.append(" \n createOpportunityPartnerLayout success");
        } catch (Exception e) {
            sb.append(" \n createOpportunityPartnerLayout error:" + e.getMessage());
            log.error("Opportunity_init open createOpportunityPartnerLayout error,tenantId {}", tenantId, e);
        }
        //4.初始化联系人布局
        try {
            String layOutJsonContact = enterpriseInitService.getLayoutJsonFromResourceByApiName(Utils.CONTACT_API_NAME, "prm_downstream");
            ILayout layoutContact = new Layout(Document.parse(layOutJsonContact));
            serviceFacade.createLayout(user, layoutContact);
            sb.append(" \n createContactPartnerLayout success");
        } catch (Exception e) {
            sb.append(" \n createContactPartnerLayout error:" + e.getMessage());
            log.error("Contact_init open createContactPartnerLayout error,tenantId {}", tenantId, e);
        }
        //5.初始化订单布局
        try {
            String layOutJsonSalesOrder = enterpriseInitService.getLayoutJsonFromResourceByApiName(Utils.SALES_ORDER_API_NAME, "prm_downstream");
            ILayout layoutSalesOrder = new Layout(Document.parse(layOutJsonSalesOrder));
            serviceFacade.createLayout(user, layoutSalesOrder);
            sb.append(" \n createSalesOrderPartnerLayout success");
        } catch (Exception e) {
            sb.append(" \n createSalesOrderPartnerLayout error:" + e.getMessage());
            log.error("SalesOrder_init open createSalesOrderPartnerLayout error,tenantId {}", tenantId, e);
        }
        //6.初始化订单产品布局
        try {
            String layOutJsonSalesOrderProduct = enterpriseInitService.getLayoutJsonFromResourceByApiName(Utils.SALES_ORDER_PRODUCT_API_NAME, "prm_downstream");
            ILayout layoutSalesOrderProduct = new Layout(Document.parse(layOutJsonSalesOrderProduct));
            serviceFacade.createLayout(user, layoutSalesOrderProduct);
            sb.append(" \n createSalesOrderProductPartnerLayout success");
        } catch (Exception e) {
            sb.append(" \n createSalesOrderProductPartnerLayout error:" + e.getMessage());
            log.error("SalesOrderProduct_init open createSalesOrderProductPartnerLayout error,tenantId {}", tenantId, e);
        }
        //把布局，预设业务类型，分配给下游的预设角色
        saveLayoutAssign(Utils.ACCOUNT_API_NAME, "{\"describeApiName\": \"AccountObj\",\"role_list\":\"[{\"roleCode\":\"5b6817bbe4b066655a6397e4\",\"label\":\"PRM代理商销售布局-客户\",\"record_layout\":[{\"record_api_name\":\"default__c\",\"layout_api_name\":\"default_crm_AccountObj_prm_downstream_layout\"}]\"}", user);
        sb.append(" \n AccountObj_Layout_role success");
        saveLayoutAssign(Utils.LEADS_API_NAME, "{\"describeApiName\": \"LeadsObj\",\"role_list\":\"[{\"roleCode\":\"5b6817bbe4b066655a6397e4\",\"label\":\"PRM代理商销售布局-线索\",\"record_layout\":[{\"record_api_name\":\"default__c\",\"layout_api_name\":\"default_crm_LeadsObj_prm_downstream_layout\"}]\"}", user);
        sb.append(" \n LeadsObj_Layout_role success");
        saveLayoutAssign(Utils.OPPORTUNITY_API_NAME, "{\"describeApiName\": \"OpportunityObj\",\"role_list\":\"[{\"roleCode\":\"5b6817bbe4b066655a6397e4\",\"label\":\"PRM代理商销售布局-商机\",\"record_layout\":[{\"record_api_name\":\"default__c\",\"layout_api_name\":\"default_crm_OpportunityObj_prm_downstream_layout\"}]\"}", user);
        sb.append(" \n OpportunityObj_Layout_role success");
        saveLayoutAssign(Utils.CONTACT_API_NAME, "{\"describeApiName\": \"ContactObj\",\"role_list\":\"[{\"roleCode\":\"5b6817bbe4b066655a6397e4\",\"label\":\"PRM代理商销售布局-联系人\",\"record_layout\":[{\"record_api_name\":\"default__c\",\"layout_api_name\":\"default_crm_ContactObj_prm_downstream_layout\"}]\"}", user);
        sb.append(" \n ContactObj_Layout_role success");
        saveLayoutAssign(Utils.SALES_ORDER_API_NAME, "{\"describeApiName\": \"SalesOrderObj\",\"role_list\":\"[{\"roleCode\":\"5b6817bbe4b066655a6397e4\",\"label\":\"PRM代理商销售布局-订单\",\"record_layout\":[{\"record_api_name\":\"default__c\",\"layout_api_name\":\"default_crm_SalesOrderObj_prm_downstream_layout\"}]\"}\n", user);
        sb.append(" \n SalesOrderObj_Layout_role success");
        saveLayoutAssign(Utils.SALES_ORDER_PRODUCT_API_NAME, "{\"describeApiName\": \"SalesOrderProductObj\",\"role_list\":\"[{\"roleCode\":\"5b6817bbe4b066655a6397e4\",\"label\":\"PRM代理商销售布局-订单产品\",\"record_layout\":[{\"record_api_name\":\"default__c\",\"layout_api_name\":\"default_crm_SalesOrderProductObj_prm_downstream_layout\"}]\"}", user);
        sb.append(" \n SalesOrderProductObj_Layout_role success");
        return sb.toString();
    }

    /**
     * 开启prm应用是,初始化下游老对象的布局和角色绑定。
     *
     * @param tenantId
     * @return
     */
    public void newInitLayoutAssignRecordTypeForDownstreamTenants(String tenantId) {
        User user = new User(tenantId, SUPPER_ADMIN_USER_ID);
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        parallelTask.submit(() -> {
            //1.初始化客户布局
            initPRMLayout(tenantId, user, Utils.ACCOUNT_API_NAME,
                    "{\"describeApiName\": \"AccountObj\",\"role_list\":\"[{\"roleCode\":\"5b6817bbe4b066655a6397e4\",\"label\":\"PRM代理商销售布局-客户\",\"record_layout\":[{\"record_api_name\":\"default__c\",\"layout_api_name\":\"default_crm_AccountObj_prm_downstream_layout\"}]\"}");
        });
        parallelTask.submit(() -> {
            //2.初始化线索布局
            initPRMLayout(tenantId, user, Utils.LEADS_API_NAME,
                    "{\"describeApiName\": \"LeadsObj\",\"role_list\":\"[{\"roleCode\":\"5b6817bbe4b066655a6397e4\",\"label\":\"PRM代理商销售布局-线索\",\"record_layout\":[{\"record_api_name\":\"default__c\",\"layout_api_name\":\"default_crm_LeadsObj_prm_downstream_layout\"}]\"}");
        });
        parallelTask.submit(() -> {
            //3.初始化商机布局
            initPRMLayout(tenantId, user, Utils.OPPORTUNITY_API_NAME,
                    "{\"describeApiName\": \"OpportunityObj\",\"role_list\":\"[{\"roleCode\":\"5b6817bbe4b066655a6397e4\",\"label\":\"PRM代理商销售布局-商机\",\"record_layout\":[{\"record_api_name\":\"default__c\",\"layout_api_name\":\"default_crm_OpportunityObj_prm_downstream_layout\"}]\"}");

        });
        parallelTask.submit(() -> {
            //4.初始化联系人布局
            initPRMLayout(tenantId, user, Utils.CONTACT_API_NAME,
                    "{\"describeApiName\": \"ContactObj\",\"role_list\":\"[{\"roleCode\":\"5b6817bbe4b066655a6397e4\",\"label\":\"PRM代理商销售布局-联系人\",\"record_layout\":[{\"record_api_name\":\"default__c\",\"layout_api_name\":\"default_crm_ContactObj_prm_downstream_layout\"}]\"}");

        });
        parallelTask.submit(() -> {
            //5.初始化订单布局
            initPRMLayout(tenantId, user, Utils.SALES_ORDER_API_NAME,
                    "{\"describeApiName\": \"SalesOrderObj\",\"role_list\":\"[{\"roleCode\":\"5b6817bbe4b066655a6397e4\",\"label\":\"PRM代理商销售布局-订单\",\"record_layout\":[{\"record_api_name\":\"default__c\",\"layout_api_name\":\"default_crm_SalesOrderObj_prm_downstream_layout\"}]\"}\n");

        });
        parallelTask.submit(() -> {
            //6.初始化订单产品布局
            initPRMLayout(tenantId, user, Utils.SALES_ORDER_PRODUCT_API_NAME,
                    "{\"describeApiName\": \"SalesOrderProductObj\",\"role_list\":\"[{\"roleCode\":\"5b6817bbe4b066655a6397e4\",\"label\":\"PRM代理商销售布局-订单产品\",\"record_layout\":[{\"record_api_name\":\"default__c\",\"layout_api_name\":\"default_crm_SalesOrderProductObj_prm_downstream_layout\"}]\"}");

        });
        try {
            parallelTask.await(6, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            log.error("newInitLayoutAssignRecordTypeForDownstreamTenants parallelTask error", e);
        }
    }

    private void initPRMLayout(String tenantId, User user, String apiName, String layoutAssignJson) {
        try {
            String layOutJsonAccount = enterpriseInitService.getLayoutJsonFromResourceByApiName(apiName, "prm_downstream");
            ILayout layoutAccount = new Layout(Document.parse(layOutJsonAccount));
            serviceFacade.createLayout(user, layoutAccount);
            saveLayoutAssign(apiName, layoutAssignJson, user);
        } catch (Exception e) {
            log.error("{}_init open create{}PartnerLayout error,tenantId {}", apiName, apiName, tenantId, e);
        }
    }

    /**
     * 该方法已迁移，不在使用
     * 迁移后为：com.facishare.crm.management.service.config.OpenPartnerBizConfigProvider#setConfigValue(com.facishare.paas.appframework.core.model.User, java.lang.String, java.lang.String, java.lang.String)
     *
     * @param user
     * @param context
     * @return
     */
    @Deprecated
    public String open(User user, ServiceContext context) {
        String tenantId = user.getTenantId();
        String partnerApiName = Utils.PARTNER_API_NAME;
        log.warn("partner_init open all start,tenantId {}", tenantId);
        StringBuilder sb = new StringBuilder();
        Boolean isEnablePriceBook = enterpriseInitService.getIsEnablePriceBook(tenantId);

        //刷describe
        sb.append(enterpriseInitService.initDescribeForTenant(tenantId, partnerApiName));
        //先刷合作伙伴的功能权限
        String initPrivilegeMsg = enterpriseInitService.initPrivilegeRelate(Lists.newArrayList(partnerApiName), user, null, null, null);
        sb.append(" \n initPrivilegeRelate result:" + initPrivilegeMsg);
        //刷布局
        try {
            List<ILayout> layouts = layoutService.findByObjectDescribeApiNameAndTenantId(partnerApiName, tenantId);
            if (layouts == null || layouts.isEmpty()) {
                String initLayoutMsg = enterpriseInitService.initMultiLayoutForOneTenant(Lists.newArrayList(partnerApiName), user.getTenantId());
                sb.append(" \n initMultiLayoutForOneTenant result:" + initLayoutMsg);
            }
        } catch (MetadataServiceException e) {
            sb.append(" \n findByObjectDescribeApiNameAndTenantId error");
        }
        //初始化合伙伙伴自注册布局
        try {
            String layoutJson = enterpriseInitService.getLayoutJsonFromResourceByApiName(partnerApiName, "detailprmzzc");
            ILayout layout = new Layout(Document.parse(layoutJson));
            serviceFacade.createLayout(user, layout);
            sb.append(" \n createPartnerZzcLayout success");
        } catch (Exception e) {
            sb.append(" \n createPartnerZzcLayout error:" + e.getMessage());
            log.error("partner_ZZc_init open createPartnerZzcLayout error,tenantId {}", tenantId, e);
        }

        //工商查询映射规则
        try {
            enterpriseInitService.initObjectMappingRule(context, "rule_bizqueryobj2partnerobj__c");
            sb.append(" \n initObjectMappingRule success");
        } catch (Exception e) {
            sb.append(" \n initObjectMappingRule error:" + e.getMessage());
            log.error("partner_init open initObjectMappingRule error,tenantId {}", tenantId, e);
        }

        //新增渠道经理角色
        try {
            roleService.addPredefinedRole(tenantId, Role.CHANNEL_MANAGER);
            sb.append(" \n channelManagerRole add role success");
        } catch (Exception e) {
            sb.append(" \n channelManagerRole add role error:" + e.getMessage());
            log.error("partner_init open channelManagerRole add role error,tenantId {},msg {}", tenantId, e.getMessage());
        }

        //预制对象新增合作伙伴、外部来源字段
        //预制是 否合作伙伴可见 按钮
        SFAConfigUtil.setConfigValue(tenantId, user.getUserId(), "37", "1");

        //预置对象刷功能权限，更换合作伙伴、移除合作伙伴、更换外部负责人
        String batchInitPartnerRelateMsg = batchInitPartnerRelateAction(user, Lists.newArrayList());
        if (StringUtils.isNotEmpty(batchInitPartnerRelateMsg)) {
            sb.append(" \n batchInitPartnerRelateAction error:" + batchInitPartnerRelateMsg);
            log.error("partner_init open batchInitPartnerRelateAction error,tenantId {},msg {}", tenantId, batchInitPartnerRelateMsg);
        } else {
            sb.append(" \n batchInitPartnerRelateAction success");
        }

        try {
            serviceFacade.updatePreDefinedFuncAccess(user, Role.CHANNEL_MANAGER.getRoleCode(),
                    Lists.newArrayList(channelManagerRoleProvider.getHavePermissFuncCodes()), null);
            sb.append(" \n channelManagerRole roleFuncAccess success");
        } catch (Exception e) {
            sb.append(" \n channelManagerRole roleFuncAccess error:" + e.getMessage());
            log.error("partner_init open channelManagerRole roleFuncAccess error,tenantId {},msg {}", tenantId, e.getMessage());
        }
        //初始化合作伙伴员工布局
        try {
            String layoutJson = enterpriseInitService.getLayoutJsonFromResourceByApiName(Utils.CONTACT_API_NAME, "partnerdetail");
            ILayout layout = new Layout(Document.parse(layoutJson));
            serviceFacade.createLayout(user, layout);
            sb.append(" \n createContactPartnerLayout success");
        } catch (Exception e) {
            sb.append(" \n createContactPartnerLayout error:" + e.getMessage());
            log.error("partner_init open createContactPartnerLayout error,tenantId {}", tenantId, e);
        }

        //初始化自注册合作伙伴员工布局
        try {
            String layoutZzcJson = enterpriseInitService.getLayoutJsonFromResourceByApiName(Utils.CONTACT_API_NAME, "partnerdetailprmzzc");
            ILayout layoutZzc = new Layout(Document.parse(layoutZzcJson));
            serviceFacade.createLayout(user, layoutZzc);
            sb.append(" \n createContactPartnerZzcLayout success");
        } catch (Exception e) {
            sb.append(" \n createContactPartnerZzcLayout error:" + e.getMessage());
            log.error("partner_Zzc_init open createContactPartnerZzcLayout error,tenantId {}", tenantId, e);
        }

        //初始化合作伙伴联系人业务类型
        try {
            RecordTypeResult createRecordTypeResult = createContactPartnerRecordType(user);
            if (createRecordTypeResult.isSuccess()) {
                sb.append(" \n createContactPartnerRecordType success");
            } else {
                sb.append(" \n createContactPartnerRecordType error:" + createRecordTypeResult.getFailMessage());
            }
        } catch (Exception e) {
            sb.append(" \n createContactPartnerRecordType error:" + e.getMessage());
            log.error("partner_init open createContactPartnerRecordType error,tenantId {}", tenantId, e);
        }
        // 初始化合作伙伴自注册业务类型
        try {
            RecordTypeResult createRecordTypeZzcResult = createContactPartnerZzcRecordType(user);
            if (createRecordTypeZzcResult.isSuccess()) {
                sb.append(" \n createRecordTypeZzcResult success");
            } else {
                sb.append(" \n createRecordTypeZzcResult error:" + createRecordTypeZzcResult.getFailMessage());
            }
        } catch (Exception e) {
            sb.append(" \n createContactPartnerZzcRecordType error:" + e.getMessage());
            log.error("partner_init open createContactPartnerZzcRecordType error,tenantId {}", tenantId, e);
        }


        //2018/07/02 价目表新增适用合作伙伴字段
        try {
            priceBookAddField(tenantId);
            priceBookLayoutAddPartner(user);
            sb.append(" \n priceBookAddField and layout partner_range success");
        } catch (Exception e) {
            sb.append(" \n priceBookAddField and layout partner_range error:" + e.getMessage());
            log.error("priceBookAddField and layout partner_range error,tenantId {}", tenantId, e);
        }
        try {
            partnerDefault(user);
            sb.append(" \n priceBookAddpartnerDefault partner_range success");
        } catch (Exception e) {
            sb.append(" \n priceBookAddpartnerDefault partner_range error:" + e.getMessage());
            log.error("priceBookAddpartnerDefault partner_range error,tenantId {}", tenantId, e);
        }

        //预设审批,代理通二期上线
        StringBuilder initApprovalResult = new StringBuilder();
        initApprovalResult.append(" initApproval accountObj:" + initApproval(user, Utils.ACCOUNT_API_NAME));
        //initApprovalResult.append(" initApproval opportunityobj:" + initApproval(user, Utils.OPPORTUNITY_API_NAME));
        initApprovalResult.append(" initApproval salesOrderObj:" + initApproval(user, Utils.SALES_ORDER_API_NAME));
        if (StringUtils.isEmpty(initApprovalResult)) {
            sb.append(" \n initApproval all  success");
        } else {
            sb.append(" \n initApproval all  error:" + initApprovalResult.toString());
            log.error("partner_init open createContactPartnerRecordType error,tenantId {},message {}", tenantId, initApprovalResult.toString());
        }

        //默认CRM菜单中新增合作伙伴菜单
        crmMenuInitService.createMenuItem(user, Lists.newArrayList(partnerApiName), "");
        sb.append(" \n createMenuItem success");

        //根据用户查找企业联系人布局和描述
        DescribeResult describeResult = serviceFacade.findDescribeAndLayout(user, "ContactObj", true, "ContactObj_layout_generate_by_UDObjectServer__c");
        //更新联系人布局，刷合作伙伴是否可见字段，默认不隐藏
        try {
            ILayout contactObjLayOut = describeResult.getLayout();
            updateContactObjLayout(user, contactObjLayOut);
            sb.append(" \n updateaContactObjectLayout success");
        } catch (Exception e) {
            sb.append(" \n updateaContactObjectLayout error:" + e.getMessage());
            log.error("updateaContactObjectLayout open updateaContactObjectLayout error,tenantId {}", tenantId, e);
        }

        //更新联系人布局，刷合作伙伴是否可见字段，提示文本
        try {
            IObjectDescribe objectDescribe = describeResult.getObjectDescribe();
            updateContactObjDescribe(objectDescribe, tenantId);
            sb.append(" \n updateContactObjDescribe success");
        } catch (Exception e) {
            sb.append(" \n updateContactObjDescribe error:" + e.getMessage());
            log.error("updateContactObjDescribe open updateContactObjDescribe error,tenantId {}", tenantId, e);
        }


        //更改合作伙伴的启用状态标识
        String configPartnerIsOpen = configService.findTenantConfig(user, configPartnerIsOpenKey);
        if (StringUtils.isEmpty(configPartnerIsOpen)) {
            configService.createTenantConfig(user, configPartnerIsOpenKey, statusOpen, ConfigValueType.STRING);
        } else {
            configService.updateTenantConfig(user, configPartnerIsOpenKey, statusOpen, ConfigValueType.STRING);
        }
        sb.append(" \n createOpenConfig success");

        log.warn("partner_init open all success,tenantId {}，msg {}", tenantId, sb.toString());
        return sb.toString();
    }

//    public String newOpen(User user, ServiceContext context) {
//        String tenantId = user.getTenantId();
//        String partnerApiName = Utils.PARTNER_API_NAME;
//        log.warn("partner_init open all start,tenantId {}", tenantId);
//        StringBuilder rst = new StringBuilder();
//
//        //刷describe
//        rst.append(enterpriseInitService.initDescribeForTenant(tenantId, partnerApiName));
//
//        //先刷合作伙伴的功能权限
//        String initPrivilegeMsg = enterpriseInitService.initPrivilegeRelate(Lists.newArrayList(partnerApiName), user, null, null, null);
//        rst.append(" \n initPrivilegeRelate result:" + initPrivilegeMsg);
//        //刷布局
//        rst.append(createPrmLayout(user, partnerApiName));
//
//        //工商查询映射规则
//        try {
//            enterpriseInitService.initObjectMappingRule(context, "rule_bizqueryobj2partnerobj__c");
//            rst.append(" \n initObjectMappingRule success");
//        } catch (Exception e) {
//            rst.append(" \n initObjectMappingRule error:" + e.getMessage());
//            log.error("partner_init open initObjectMappingRule error,tenantId {}", tenantId, e);
//        }
//
//        //预制是 否合作伙伴可见 按钮
//        SFAConfigUtil.setConfigValue(tenantId, user.getUserId(), "37", "1");
//
//        //预置对象刷功能权限，更换合作伙伴、移除合作伙伴、更换外部负责人
//        rst.append(batchInitPartnerRelateAction(user));
//
//        //新增渠道经理角色
//        rst.append(addPrmRole(user));
////todo newopen
//        //创建业务类型
//        rst.append(createPrmContactRecordType(user) ? " \n createPrmContactRecordType success" : " \n createPrmContactRecordType error");
//
//        //2018/07/02 价目表新增适用合作伙伴字段
//        try {
//            priceBookAddField(tenantId);
//            priceBookLayoutAddPartner(user);
//            rst.append(" \n priceBookAddField and layout partner_range success");
//        } catch (Exception e) {
//            rst.append(" \n priceBookAddField and layout partner_range error:" + e.getMessage());
//            log.error("priceBookAddField and layout partner_range error,tenantId {}", tenantId, e);
//        }
//        try {
//            //todo 改为异步
//            partnerDefault(user);
//            rst.append(" \n priceBookAddpartnerDefault partner_range success");
//        } catch (Exception e) {
//            rst.append(" \n priceBookAddpartnerDefault partner_range error:" + e.getMessage());
//            log.error("priceBookAddpartnerDefault partner_range error,tenantId {}", tenantId, e);
//        }
//
//        //预设审批,代理通二期上线
//        rst.append(" \n " + initApproval(user, Lists.newArrayList(Utils.ACCOUNT_API_NAME, Utils.SALES_ORDER_API_NAME)));
//
//        //默认CRM菜单中新增合作伙伴菜单
//        menuCommonService.createMenuItem(user, partnerApiName);
//        rst.append(" \n createMenuItem success");
//
//        //根据用户查找企业联系人布局和描述
//        DescribeResult describeResult = serviceFacade.findDescribeAndLayout(user, "ContactObj", true, "ContactObj_layout_generate_by_UDObjectServer__c");
//        //更新联系人布局，刷合作伙伴是否可见字段，默认不隐藏
//        try {
//            ILayout contactObjLayOut = describeResult.getLayout();
//            updateContactObjLayout(user, contactObjLayOut);
//            rst.append(" \n updateaContactObjectLayout success");
//        } catch (Exception e) {
//            rst.append(" \n updateaContactObjectLayout error:" + e.getMessage());
//            log.error("updateaContactObjectLayout open updateaContactObjectLayout error,tenantId {}", tenantId, e);
//        }
//
//        //更新联系人布局，刷合作伙伴是否可见字段，提示文本
//        try {
//            IObjectDescribe objectDescribe = describeResult.getObjectDescribe();
//            updateContactObjDescribe(objectDescribe, tenantId);
//            rst.append(" \n updateContactObjDescribe success");
//        } catch (Exception e) {
//            rst.append(" \n updateContactObjDescribe error:" + e.getMessage());
//            log.error("updateContactObjDescribe open updateContactObjDescribe error,tenantId {}", tenantId, e);
//        }
//
//        //更改合作伙伴的启用状态标识
//        String configPartnerIsOpen = configService.findTenantConfig(user, configPartnerIsOpenKey);
//        if (StringUtils.isEmpty(configPartnerIsOpen)) {
//            configService.createTenantConfig(user, configPartnerIsOpenKey, statusOpen, ConfigValueType.STRING);
//        } else {
//            configService.updateTenantConfig(user, configPartnerIsOpenKey, statusOpen, ConfigValueType.STRING);
//        }
//        rst.append(" \n createOpenConfig success");
//
//        log.warn("partner_init open all success,tenantId {}，msg {}", tenantId, rst.toString());
//        return rst.toString();
//    }

    public void updateContactObjDescribe(IObjectDescribe objectDescribe, String tenantId) throws Exception {
        String enableParterHelpText = "1.联系人与所属客户关联的合作伙伴保持一致；" +
                "2.当选择“是”后，该联系人将会被其所属客户的合作伙伴（外部负责人）可见；" +
                "3.联系人依旧遵循联系人外部数据权限；" +
                "4.下游合作伙伴创建联系人时必须关联自身已有客户；";

        IFieldDescribe enablePartner = objectDescribe.getFieldDescribe("enable_partner_view");
        enablePartner.setHelpText(enableParterHelpText);

        List<IFieldDescribe> completeIFieldList = Lists.newArrayList();
        completeIFieldList.add(enablePartner);
        objectDescribeService.updateOrInsertFieldsForOnline(tenantId, "ContactObj", completeIFieldList);
    }

    public void updateContactObjLayout(User user, ILayout contactObjLayOut) {
        try {
            List<IComponent> components = contactObjLayOut.getComponents();
            for (IComponent component : components) {
                if (Objects.equals(component.getName(), "form_component")) {
                    List<Map> fieldSections = component.get("field_section", List.class);
                    // 只保留基本信息的fieldSection
                    fieldSections.stream().findFirst().ifPresent(x -> {
                        List<Map> fields = (List<Map>) x.get("form_fields");
                        Map map = Maps.newHashMap();
                        map.put("is_readonly", "false");
                        map.put("is_required", "false");
                        map.put("render_type", "true_or_false");
                        map.put("field_name", "enable_partner_view");
                        fields.add(map);
                        x.put("form_fields", fields);
                    });
                    component.set("field_section", fieldSections);
                }
            }

            contactObjLayOut.setComponents(components);
            serviceFacade.updateLayout(user, contactObjLayOut);
        } catch (MetadataServiceException e) {
            log.error("PartnerInitService Open updateContactObjLayout error {}", "Fail");
        }
    }

    public void openObject(User user, List<String> apiNames) {

        if (!apiNames.isEmpty()) {
            //自定义对象新增字段
            Map<String, IObjectDescribe> describeMap = serviceFacade.findObjects(user.getTenantId(), apiNames);
            try {
                for (String apiName : apiNames) {
                    addFieldByApiName(user, apiName, describeMap.get(apiName));
                }
            } catch (MetadataServiceException e) {
                log.error(e.getMessage());
                throw new ValidateException(e.getMessage());
            }

            //自定义对象分配权限
            List<String> actionCodeList = Lists.newArrayList(ObjectAction.CHANGE_PARTNER.getActionCode(), ObjectAction.DELETE_PARTNER.getActionCode()
                    , ObjectAction.CHANGE_PARTNER_OWNER.getActionCode());
            for (String needInitApiName : apiNames) {
                try {
                    serviceFacade.batchCreateFunc(user, needInitApiName, actionCodeList);
                    serviceFacade.updateUserDefinedFuncAccess(user,
                            PrivilegeConstants.ADMIN_ROLE_CODE, needInitApiName, actionCodeList, Lists.newArrayList());
                    serviceFacade.updateUserDefinedFuncAccess(user,
                            Role.CHANNEL_MANAGER.getRoleCode(), needInitApiName, actionCodeList, Lists.newArrayList());
                } catch (Exception e) {
                    log.error("batchInitPartnerRelateAction addRoleFunc error,apiName {}", needInitApiName, e);
                }
            }

            //调深研接口，分配布局，业务类型
            List<InitObjectInfo> objectInfoList = Lists.newArrayList();
            for (String apiName : apiNames) {
                List<String> permissions = Lists.newArrayList();
                permissions.add(apiName);
                permissions.add(apiName + "||View");
                permissions.add(apiName + "||Add");
                permissions.add(apiName + "||Edit");
                permissions.add(apiName + "||Abolish");
                permissions.add(apiName + "||Relate");
                permissions.add(apiName + "||Lock");
                permissions.add(apiName + "||Unlock");
                permissions.add(apiName + "||ChangePartnerOwner");
                permissions.add(apiName + "||ViewEntireBPM");
                permissions.add(apiName + "||StartBPM");
                permissions.add(apiName + "||ChangeBPMApprover");
                permissions.add(apiName + "||StopBPM");
                objectInfoList.add(InitObjectInfo.builder().objectApiName(apiName).permissions(permissions).build());
            }
            String ea = eieaConverter.enterpriseIdToAccount(Integer.parseInt(user.getTenantId()));

            InitObjectByApiNameModel model = InitObjectByApiNameModel.builder()
                    .appId(AppIdMapping.appIdMapping.get(PrmConstant.PRM_APP_ID))
                    .appType(1)
                    .objectInfos(objectInfoList)
                    .upstreamEas(Lists.newArrayList(ea))
                    .build();
            Map<String, String> headers = Maps.newHashMap();
            headers.put("Content-Type", "application/json");
            initObjectsPermissionsAndLayoutProxy.initObjectsPermissionsAndLayout(headers, model);

            String openApiNamesString = configService.findTenantConfig(user, PrmConstant.CONFIG_PARTNER_KEY);
            if (StringUtils.isEmpty(openApiNamesString)) {
                configService.createTenantConfig(user, PrmConstant.CONFIG_PARTNER_KEY, JSON.toJSONString(apiNames), ConfigValueType.STRING);
            } else {
                List<String> openApiNames = JSON.parseArray(openApiNamesString, String.class);
                openApiNames.addAll(apiNames);
                configService.updateTenantConfig(user, PrmConstant.CONFIG_PARTNER_KEY, JSON.toJSONString(Sets.newHashSet(openApiNames)), ConfigValueType.STRING);
            }
        }
    }


    public void addFieldByApiName(User user, String apiName, IObjectDescribe objectDescribe) throws MetadataServiceException {
        List<IFieldDescribe> addFields = Lists.newArrayList();
        List<String> addFieldName = Lists.newArrayList();
        //合作伙伴field
        IFieldDescribe partnerField = new ObjectReferenceFieldDescribe();
        partnerField.fromJsonString(enterpriseInitService.getPartnerJsonFromFieldName("partnerId"));
        partnerField.setDescribeApiName(apiName);
        partnerField.set("target_related_list_label", objectDescribe.getDisplayName());
        partnerField.set("target_related_list_name", "partner_" + apiName.toLowerCase() + "_list");
        //外部来源
        IFieldDescribe outResourcesField = new SelectOneFieldDescribe();
        outResourcesField.fromJsonString(enterpriseInitService.getPartnerJsonFromFieldName("outResources"));
        outResourcesField.setDescribeApiName(apiName);

        if (!objectDescribe.getFieldDescribeMap().containsKey("partner_id")) {
            addFields.add(partnerField);
            addFieldName.add("partner_id");
        }
        if (!objectDescribe.getFieldDescribeMap().containsKey("out_resources")) {
            addFields.add(outResourcesField);
            addFieldName.add("out_resources");
        }


        if (addFields.isEmpty()) {
            return;
        }

        objectDescribeService.addCustomFieldDescribe(objectDescribe, addFields);

        //字段放到布局里
        FieldLayoutPojo partnerLayoutPojo = new FieldLayoutPojo();
        partnerLayoutPojo.setShow(true);
        partnerLayoutPojo.setReadonly(false);
        partnerLayoutPojo.setRequired(false);
        partnerLayoutPojo.setApiName(partnerField.getApiName());
        partnerLayoutPojo.setRenderType(partnerField.getType());

        FieldLayoutPojo outResourcesLayoutPojo = new FieldLayoutPojo();
        outResourcesLayoutPojo.setShow(true);
        outResourcesLayoutPojo.setReadonly(false);
        outResourcesLayoutPojo.setRequired(false);
        outResourcesLayoutPojo.setApiName(outResourcesField.getApiName());
        outResourcesLayoutPojo.setRenderType(outResourcesField.getType());

        List<ILayout> layouts = serviceFacade.findLayoutByObjectApiName(user.getTenantId(), apiName);
        if (!layouts.isEmpty()) {
            for (ILayout layout : layouts) {
                if (addFieldName.contains("partner_id")) {
                    LayoutExt.of(layout).addField(partnerField, partnerLayoutPojo);
                }
                if (addFieldName.contains("out_resources")) {
                    LayoutExt.of(layout).addField(outResourcesField, outResourcesLayoutPojo);
                }
                Optional<IFormField> formField = LayoutExt.of(layout).getField("out_owner");
                if (!formField.isPresent()) {
                    //添加外部负责人
                    IFieldDescribe iFieldDescribe = objectDescribe.getFieldDescribe("out_owner");
                    if (iFieldDescribe != null) {
                        FieldLayoutPojo outOwnerPojo = new FieldLayoutPojo();
                        outOwnerPojo.setShow(true);
                        outOwnerPojo.setReadonly(true);
                        outOwnerPojo.setRequired(false);
                        outOwnerPojo.setApiName(iFieldDescribe.getApiName());
                        outOwnerPojo.setRenderType(iFieldDescribe.getType());
                        LayoutExt.of(layout).addField(iFieldDescribe, outOwnerPojo);
                    }
                }
                serviceFacade.updateLayout(user, layout);
            }
        }
    }


    /**
     * 初始化更换合作伙伴三个操作
     */
    public String batchInitPartnerRelateAction(User user, List<String> apiNames) {
        StringBuilder stringBuilder = new StringBuilder();
        if (CollectionUtils.empty(apiNames)) {
            return stringBuilder.toString();
        }
        apiNames.remove(Utils.CONTACT_API_NAME);
        apiNames.remove("ActiveRecordObj");
        List<String> actionCodeList = Lists.newArrayList(ObjectAction.CHANGE_PARTNER.getActionCode(), ObjectAction.DELETE_PARTNER.getActionCode()
                , ObjectAction.CHANGE_PARTNER_OWNER.getActionCode());
        for (String needInitApiName : apiNames) {
            try {
                serviceFacade.batchCreateFunc(user, needInitApiName, actionCodeList);
                serviceFacade.updateUserDefinedFuncAccess(user, PrivilegeConstants.ADMIN_ROLE_CODE, needInitApiName, actionCodeList, Lists.newArrayList());
            } catch (Exception e) {
                stringBuilder.append(String.format("batchInitPartnerRelateAction error,apiName %s,failMsg %s", needInitApiName, e.getMessage()));
                log.error("batchInitPartnerRelateAction error,apiName {} {}", needInitApiName, e.toString());
            }
        }
        return StringUtils.isNotEmpty(stringBuilder.toString()) ? " \n batchInitPartnerRelateAction success" : stringBuilder.toString();
    }

    private String initApproval(User user, String apiName) {
        Map<String, String> headerMap = getApprovalInitHeaders(user.getTenantId(), user.getUserId());
        ApprovalInitModel.Arg arg = new ApprovalInitModel.Arg();
        arg.setEntityId(apiName);
        ApprovalInitModel.Result result = approvalInitProxy.init(arg, headerMap);
        if (result.isData()) {
            return "success";
        } else {
            return result.getMessage();
        }
    }

    public String initApproval(User user, List<String> apiNameList) {
        Map<String, String> headerMap = getApprovalInitHeaders(user.getTenantId(), user.getUserId());
        StringBuilder rst = new StringBuilder();
        for (String apiName : apiNameList) {
            ApprovalInitModel.Arg arg = new ApprovalInitModel.Arg();
            arg.setEntityId(apiName);
            ApprovalInitModel.Result result = approvalInitProxy.init(arg, headerMap);
            if (result.isData()) {
                rst.append(String.format(" \n %s initApproval success", apiName));
            } else {
                rst.append(String.format(" \n %s initApproval error:%s", apiName, result.getMessage()));
                log.warn("partner_init open initApproval error,tenantId {},apiName:{},message {}", user.getTenantId(), apiName, result.getMessage());
            }
        }
        return rst.toString();
    }

    public String createPrmLayout(User user, String partnerApiName) {
        StringBuilder rst = new StringBuilder();
        try {
            List<ILayout> layouts = layoutService.findByObjectDescribeApiNameAndTenantId(partnerApiName, user.getTenantId());
            if (layouts == null || layouts.isEmpty()) {
                rst.append(enterpriseInitService.initMultiLayoutForOneTenant(Lists.newArrayList(partnerApiName), user.getTenantId()));
            }
        } catch (MetadataServiceException e) {
            throw new RuntimeException(e);
        }

        List<Tuple<String, String>> layoutList = Lists.newArrayList();

        layoutList.add(Tuple.of(partnerApiName, "detailprmzzc"));//初始化合伙伙伴自注册布局
        layoutList.add(Tuple.of(Utils.CONTACT_API_NAME, "partnerdetail"));//初始化合作伙伴员工布局
        layoutList.add(Tuple.of(Utils.CONTACT_API_NAME, "partnerdetailprmzzc"));//初始化自注册合作伙伴员工布局
        rst.append(batchCreateLayout(user, layoutList));
        return rst.toString();
    }

    private String batchCreateLayout(User user, List<Tuple<String, String>> layoutKeyList) {
        if (CollectionUtils.empty(layoutKeyList)) {
            return "";
        }
        StringBuilder rst = new StringBuilder();
        for (Tuple<String, String> entry : layoutKeyList) {
            String apiName = entry.getKey();
            String layoutType = entry.getValue();
            String layoutJson = enterpriseInitService.getLayoutJsonFromResourceByApiName(apiName, layoutType);
            ILayout layout = new Layout(Document.parse(layoutJson));
            try {
                serviceFacade.createLayout(user, layout);
                rst.append(String.format(" \n %s_%s createPrmLayout success", apiName, layoutType));
            } catch (Exception e) {
                rst.append(" \n createPrmLayout error:" + e.getMessage());
                log.error("partner_init open createPrmLayout error,tenantId {}", user.getTenantId(), e);
            }
        }
        return rst.toString();
    }

    public String addPrmRole(User user) {
        String result = "";
        //新增渠道经理角色
        try {
            roleService.addPredefinedRole(user.getTenantId(), Role.CHANNEL_MANAGER);
            result = " \n addPredefinedRole success";
        } catch (Exception e) {
            log.warn("partner_init open channelManagerRole add role error,tenantId {},msg {}", user.getTenantId(), e.getMessage());
            result = " \n addPredefinedRole error:" + e.getMessage();

        }
        //渠道经理添加默认功能权限
        try {
            serviceFacade.updatePreDefinedFuncAccess(user, Role.CHANNEL_MANAGER.getRoleCode(),
                    Lists.newArrayList(channelManagerRoleProvider.getHavePermissFuncCodes()), null);
            result += " \n updatePreDefinedFuncAccess success";
        } catch (Exception e) {
            log.warn("partner_init updatePreDefinedFuncAccess error,tenantId {},msg {}", user.getTenantId(), e.getMessage());
            result += " \n updatePreDefinedFuncAccess error:" + e.getMessage();

        }
        return result;
    }

    public String createPrmContactRecordType(User user) {
        List<String> jsonList = Lists.newArrayList();
        //初始化合作伙伴联系人业务类型
        jsonList.add("{\"label\":\"合作伙伴联系人\",\"api_name\":\"default_contact_partner__c\",\"description\":\"合作伙伴联系人\",\"config\":{\"remove\":0},\"is_active\":true,\"roles\":[{\"role_code\":\"00000000000000000000000000000025\",\"is_default\":true,\"is_used\":true,\"layout_api_name\":\"default_crm_ContactObj_partnerdetail_layout_by_UDObjectServer__c\"},{\"role_code\":\"00000000000000000000000000000006\",\"is_default\":false,\"is_used\":true,\"layout_api_name\":\"default_crm_ContactObj_partnerdetail_layout_by_UDObjectServer__c\"}]}");
        // 初始化合作伙伴自注册业务类型
        jsonList.add("{\"label\":\"合作伙伴自注册联系人\",\"api_name\":\"default_contact_partner_zzc__c\",\"description\":\"合作伙伴联系人\",\"config\":{\"remove\":0},\"is_active\":true,\"roles\":[{\"role_code\":\"00000000000000000000000000000025\",\"is_default\":true,\"is_used\":true,\"layout_api_name\":\"default_crm_ContactObj_partnerdetailprmzzc_layout_by_UDObjectServer__c\"},{\"role_code\":\"00000000000000000000000000000006\",\"is_default\":false,\"is_used\":true,\"layout_api_name\":\"default_crm_ContactObj_partnerdetailprmzzc_layout_by_UDObjectServer__c\"}]}");

        StringBuilder rst = new StringBuilder();
        for (String json : jsonList) {
            try {
                RecordTypeRoleViewPojo pojo = JSON.parseObject(json, RecordTypeRoleViewPojo.class);
                RecordTypeResult result = recordTypeLogicService.createRecordType(user.getTenantId(), Utils.CONTACT_API_NAME, pojo, user);
                if (!result.isSuccess()) {
                    rst.append("createPrmContactRecordType:" + json.substring(0, 65) + " error:" + result.getFailMessage());
                    log.warn("partner_init createPrmContactRecordType:" + json.substring(0, 65) + " error,tenantId {}", user.getTenantId(), result.getFailMessage());
                } else {
                    rst.append(" \n createPrmContactRecordType:" + json.substring(0, 65) + " success");
                }
            } catch (Exception ex) {
                rst.append(" \n createPrmContactRecordType:" + json.substring(0, 65) + " error:" + ex.getMessage());
                log.warn("partner_init createPrmContactRecordType:" + json.substring(0, 65) + " error,tenantId {}", user.getTenantId(), ex);
            }
        }
        return rst.toString();
    }

    /**
     * 创建合作伙伴联系人业务类型
     */
    public RecordTypeResult createContactPartnerRecordType(User user) {
        String json = "{\"label\":\"合作伙伴联系人\",\"api_name\":\"default_contact_partner__c\",\"description\":\"合作伙伴联系人\",\"config\":{\"remove\":0},\"is_active\":true,\"roles\":[{\"role_code\":\"00000000000000000000000000000025\",\"is_default\":true,\"is_used\":true,\"layout_api_name\":\"default_crm_ContactObj_partnerdetail_layout_by_UDObjectServer__c\"},{\"role_code\":\"00000000000000000000000000000006\",\"is_default\":false,\"is_used\":true,\"layout_api_name\":\"default_crm_ContactObj_partnerdetail_layout_by_UDObjectServer__c\"}]}";
        RecordTypeRoleViewPojo pojo = JSON.parseObject(json, RecordTypeRoleViewPojo.class);
        RecordTypeResult result = recordTypeLogicService.createRecordType(user.getTenantId(), Utils.CONTACT_API_NAME, pojo, user);
        return result;
    }

    /**
     * 创建合作伙伴联系人业务类型
     *
     * @param user
     * @return
     */
    public RecordTypeResult createContactPartnerZzcRecordType(User user) {
        String json = "{\"label\":\"合作伙伴自注册联系人\",\"api_name\":\"default_contact_partner_zzc__c\",\"description\":\"合作伙伴联系人\",\"config\":{\"remove\":0},\"is_active\":true,\"roles\":[{\"role_code\":\"00000000000000000000000000000025\",\"is_default\":true,\"is_used\":true,\"layout_api_name\":\"default_crm_ContactObj_partnerdetailprmzzc_layout_by_UDObjectServer__c\"},{\"role_code\":\"00000000000000000000000000000006\",\"is_default\":false,\"is_used\":true,\"layout_api_name\":\"default_crm_ContactObj_partnerdetailprmzzc_layout_by_UDObjectServer__c\"}]}";
        RecordTypeRoleViewPojo pojo = JSON.parseObject(json, RecordTypeRoleViewPojo.class);
        RecordTypeResult result = recordTypeLogicService.createRecordType(user.getTenantId(), Utils.CONTACT_API_NAME, pojo, user);
        return result;
    }


    public Map<String, String> getHeaders(String tenantId, String userId) {
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-fs-ei", tenantId);
        headers.put("x-fs-userInfo", userId);
        headers.put("Expect", "100-continue");
        return headers;
    }

    private Map<String, String> getApprovalInitHeaders(String tenantId, String fsUserId) {
        Map<String, String> headers = Maps.newHashMap();
        headers.put("x-user-id", fsUserId);
        headers.put("x-tenant-id", tenantId);
        return headers;
    }

    /**
     * 价目表中添加适用合作伙伴字段
     */
    public void priceBookAddField(String tenantId) {
        IObjectDescribe priceBookDescribe = serviceFacade.findObject(tenantId, Utils.PRICE_BOOK_API_NAME);
        IFieldDescribe partnerRangeFieldDescribe = buildPriceBookPartnerRangeField();
        if (Objects.nonNull(priceBookDescribe.getFieldDescribe(partnerRangeFieldDescribe.getApiName()))) {
            return;
        }
        addCustomFieldDescribe(priceBookDescribe, Lists.newArrayList(partnerRangeFieldDescribe));
    }

    public void addPartner2AvailableRange(String tenantId) {
        IObjectDescribe objectDescribe = serviceFacade.findObject(tenantId, SFAPreDefineObject.AvailableRange.getApiName());
        if (objectDescribe == null || objectDescribe.getFieldDescribe("") != null) {
            return;
        }
        List<IFieldDescribe> fieldDescribeList = Lists.newArrayList();
        IFieldDescribe rangeFieldDescribe = new UseRangeFieldDescribe();
        rangeFieldDescribe.fromJsonString(enterpriseInitService.getAvailableJsonFromFieldName("partnerrange"));
        rangeFieldDescribe.setDescribeApiName(SFAPreDefineObject.AvailableRange.getApiName());
        fieldDescribeList.add(rangeFieldDescribe);
//        IFieldDescribe idFieldDescribe = new TagFieldDescribe();
//        idFieldDescribe.fromJsonString(enterpriseInitService.getAvailableJsonFromFieldName("partnerid"));
//        idFieldDescribe.setDescribeApiName(SFAPreDefineObject.AvailableRange.getApiName());
//        fieldDescribeList.add(idFieldDescribe);
        addCustomFieldDescribe(objectDescribe, fieldDescribeList);
    }

    public void addPartner2AvailableRangeLayout(String tenantId) throws MetadataServiceException {
        List<ILayout> layouts = layoutService.findByObjectDescribeApiNameAndTenantId(SFAPreDefineObject.AvailableRange.getApiName(), tenantId);
        if (CollectionUtils.empty(layouts)) {
            return;
        }
        layouts = layouts.stream()
                .filter(x -> ILayout.DETAIL_LAYOUT_TYPE.equals(x.getLayoutType()))
                .collect(Collectors.toList());
        if (CollectionUtils.empty(layouts)) {
            return;
        }
        IFieldDescribe fieldDescribe = new UseRangeFieldDescribe();
        fieldDescribe.setApiName("partner_range");
        fieldDescribe.setRequired(Boolean.FALSE);
        List<IFieldDescribe> toAddFields = Lists.newArrayList(fieldDescribe);
        for (ILayout iLayout : layouts) {
            addField2DetailLayout(iLayout, toAddFields);
            layoutService.replace(iLayout);
        }
    }

    private void addField2DetailLayout(ILayout layout, List<IFieldDescribe> toAddFieldList) throws MetadataServiceException {
        List<IFieldSection> refreshedFieldSections = Lists.newArrayList();
        List<IComponent> oldComponents = layout.getComponents();
        List<IComponent> newComponents = Lists.newArrayList();
        oldComponents.forEach(iComponent -> {
            if (iComponent.getName().startsWith("form_component")) {
                FormComponent formComponent = (FormComponent) iComponent;
                for (IFieldSection iFieldSection : formComponent.getFieldSections()) {
                    if (iFieldSection.getName().equals(BASE_FIELD_SECTION_API_NAME)) {
                        List<IFormField> formFields = iFieldSection.getFields();
                        if (CollectionUtils.empty(formFields)) {
                            continue;
                        }
                        List<String> existFieldNames = iFieldSection.getFields().stream()
                                .map(x -> x.getFieldName()).collect(Collectors.toList());
                        for (IFieldDescribe fieldDescribe : toAddFieldList) {
                            String fieldName = fieldDescribe.getApiName();
                            if (existFieldNames.contains(fieldName)) {
                                continue;
                            }
                            IFormField formField = new FormField();
                            formField.setFieldName(fieldDescribe.getApiName());
                            formField.setRenderType(fieldDescribe.getType());
                            formField.setRequired(fieldDescribe.isRequired());
                            formField.setReadOnly(false);
                            formFields.add(formField);
                        }
                        iFieldSection.setFields(formFields);
                    }
                    refreshedFieldSections.add(iFieldSection);
                }
                formComponent.setFieldSections(refreshedFieldSections);
                newComponents.add(formComponent);
            } else {
                newComponents.add(iComponent);
            }
        });
        layout.setComponents(newComponents);
    }

    public void priceBookLayoutAddPartner(User user) throws MetadataServiceException {
        ILayout layout = this.layoutService.findByNameAndObjectDescribeApiNameAndTenantId("PriceBookObj_layout_generate_by_UDObjectServer__c",
                Utils.PRICE_BOOK_API_NAME, user.getTenantId());

        LayoutDocument layoutDocument = LayoutDocument.of(layout);
        //如果布局中已经有适用合作伙伴，则不进行初始化
        Map searchResult = JsonObjectUtils.get(layoutDocument, Map.class, "$.components[?(@.api_name=='form_component')].field_section[*].form_fields.[?(@.field_name=='partner_range')]");
        if (searchResult != null) {
            return;
        }
        //布局中新增适用合作伙伴字段，默认插在系统信息前
        LayoutExt layoutExt = LayoutExt.of(layout);
        FormComponentExt formComponent = layoutExt.getFormComponent().get();
        List<IFieldSection> fieldSectionList = formComponent.getFieldSections();
        JSONObject partnerFieldJson = JSON.parseObject("{\"form_fields\":[{\"is_readonly\":false, \"is_required\":false, \"render_type\":\"use_scope\", \"field_name\":\"partner_range\"} ], \"api_name\":\"partner_range_section__c\", \"tab_index\":\"ltr\", \"column\":2, \"header\":\"适用合作伙伴\", \"is_show\":true }");
        IFieldSection partnerFieldSection = new FieldSection(partnerFieldJson);
        Integer sysInfoSectionIndex = null;
        for (int i = 0; i < fieldSectionList.size(); i++) {
            IFieldSection fieldSection = fieldSectionList.get(i);
            if ("sysinfo_section__c".equals(fieldSection.getName())) {
                sysInfoSectionIndex = i;
            }
        }
        fieldSectionList.add(sysInfoSectionIndex != null ? sysInfoSectionIndex : fieldSectionList.size(), partnerFieldSection);
        formComponent.setFieldSections(fieldSectionList);
        layoutService.update(layoutExt.getLayout());
    }

    private void addCustomFieldDescribe(IObjectDescribe objectDescribe, List<IFieldDescribe> fieldDescribeList) {
        try {
            objectDescribeService.addCustomFieldDescribe(objectDescribe, fieldDescribeList);
        } catch (MetadataServiceException e) {
            log.error("addCustomFieldDescribe error,tenantId {} ", objectDescribe.getTenantId(), e);
        }
    }

    private IFieldDescribe buildPriceBookPartnerRangeField() {
        UseScopeFieldDescribe useScopeFieldDescribeBuilder = UseScopeFieldDescribeBuilder.builder()
                .apiName(PriceBookConstants.Field.PARTNERRANGE.getApiName())
                .expressionType(IFieldType.LONG_TEXT)
                .defaultIsExpression(false)
                .index(false)
                .targetApiName(Utils.PARTNER_API_NAME)
                .label(I18N.text(SOI18NKeyUtils.SO_PARTNER_APPLICABLEPARTNER))
                .required(false)
                .helpText(I18N.text(SOI18NKeyUtils.SO_PARTNER_PARTNERRANGE))
                .defaultValue("{\"type\":\"noCondition\",\"value\":\"NONE\"}")
                .build();

        return useScopeFieldDescribeBuilder;
    }

    public void partnerDefault(User user) {
        Integer offset = 0, limit = 500, count = 0;
        while (true) {
            QueryResult<IObjectData> pricebookResult = findPricebookIsNotDefault(user, Lists.newLinkedList(), offset, limit);
            if (pricebookResult.getData().size() <= 0) {
                break;
            }
            count += pricebookResult.getData().size();
            Map map = Maps.newHashMap();
            map.put(PriceBookConstants.Field.PARTNERRANGE.getApiName(), "{\"type\":\"noCondition\",\"value\":\"NONE\"}");
            serviceFacade.batchUpdateWithMap(user, pricebookResult.getData(), map);
            //底层的查询是有缓存的，同一线程同一查询参数返回的结果数据是一样的,所以此处在每次查询时，改变查询数据的页数据条数
            limit++;
        }
        log.info("fs-crm:initStandardPartner:count ,tenantId {},count {}", user.getTenantId(), count);
    }

    private QueryResult<IObjectData> findPricebookIsNotDefault(User user, List<IFilter> filters, Integer offset, Integer limit) {
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(limit);
        searchQuery.setOffset(offset);
        SearchUtil.fillFiltersWithUser(user, filters);
        filters.add(SearchUtil.filter(PriceBookConstants.Field.PARTNERRANGE.getApiName(), Operator.IS, Lists.newArrayList()));
        searchQuery.setFilters(filters);
        //用pg的查询方式
        return serviceFacade.findBySearchQuery(ActionContextExt.of(user).pgDbType().getContext(), Utils.PRICE_BOOK_API_NAME, searchQuery);
    }


    /**
     * copy 自定义对象的分配布局方法
     * reason 下层方法 appId 写死成了 CRM，不能覆写
     */
    public RecordTypeResult saveLayoutAssign(String describeApiName, String json, User user) {
        log.debug("Entering RecordTypeService saveLayoutAssign(tenantId = {}, objectDescribeApiName = {}, json={})",
                user.getTenantId(), describeApiName, json);
        RecordTypeResult result = new RecordTypeResult();
        JSONArray jsonArray = JSONObject.parseArray(json);
        if (null == jsonArray || jsonArray.isEmpty()) {
            result.setSuccess(false);
            return result;
        }

        AddRoleViewModel.Arg arg = new AddRoleViewModel.Arg();
        arg.setAuthContext(user);
        String tenantId = user.getTenantId();
        List<RoleViewPojo> list = Lists.newArrayList();
        RoleViewPojo roleViewPojo;
        JSONObject object;
        for (int index = 0; index < jsonArray.size(); index++) {
            object = jsonArray.getJSONObject(index);
            String roleCode = object.getString("roleCode");

            JSONArray recordLayout = object.getJSONArray("record_layout");
            String recordTypeApiName;
            String layoutApiName;
            if (null == recordLayout || recordLayout.isEmpty()) {
                continue;
            }

            for (Object obj : recordLayout) {
                JSONObject jsonObject = (JSONObject) obj;
                recordTypeApiName = jsonObject.getString("record_api_name");
                layoutApiName = jsonObject.getString("layout_api_name");
                roleViewPojo = new RoleViewPojo();
                // appId 要读取配置
                roleViewPojo.setAppId(AppIdMapping.appIdMapping.get(PrmConstant.PRM_APP_ID));
                roleViewPojo.setEntityId(describeApiName);
                roleViewPojo.setRecordTypeId(recordTypeApiName);
                roleViewPojo.setRoleCode(roleCode);
                roleViewPojo.setTenantId(tenantId);
                roleViewPojo.setViewId(layoutApiName);
                roleViewPojo.setViewType(LayoutTypes.DETAIL);
                list.add(roleViewPojo);
            }
        }

        recordTypeLogicService.upsertRoleViewList(user, list);
        return result;
    }


    /**
     * copy 自定义对象的分配业务类型方法
     * reason 下层方法 appId 写死成了 CRM，不能覆写
     */
    public RecordTypeResult assignRecord(String tenantId, String describeApiName, String json, User user) {
        log.debug("Entering RecordTypeService assignRecord(tenantId = {}, objectDescribeApiName = {}, " +
                "roleListJson={})", tenantId, describeApiName, json);
        RecordTypeResult result = new RecordTypeResult();
        JSONArray roleListJsonArray = JSONObject.parseArray(json);
        if (null == roleListJsonArray || roleListJsonArray.isEmpty()) {
            log.warn("role list is empty, json={}", json);
            return result;
        }

        JSONObject object;
        AddRoleRecordTypeModel.Arg arg = new AddRoleRecordTypeModel.Arg();
        List<RecordTypePojo> recordTypePojoList = Lists.newArrayList();
        for (int index = 0; index < roleListJsonArray.size(); index++) {
            object = roleListJsonArray.getJSONObject(index);
            Object recordTypeObject = object.get("records");
            if (!(recordTypeObject instanceof List)) {
                continue;
            }
            List<String> list = (List) recordTypeObject;
            String roleCode = object.getString("roleCode");
            String defaultRecord = object.getString("default_record");
            for (String apiName : list) {
                RecordTypePojo recordTypePojo = new RecordTypePojo();
                recordTypePojo.setAppId(AppIdMapping.appIdMapping.get(PrmConstant.PRM_APP_ID));
                recordTypePojo.setEntityId(describeApiName);
                recordTypePojo.setRecordTypeId(apiName);
                recordTypePojo.setRoleCode(roleCode);
                recordTypePojo.setTenantId(tenantId);
                recordTypePojo.setDefaultType(apiName.equals(defaultRecord));
                recordTypePojoList.add(recordTypePojo);
            }
        }


        arg.setRecordTypePojos(recordTypePojoList);
        arg.setEntityId(describeApiName);
        arg.setAuthContext(user);
        arg.setRecordTypeId("default__c");
        AddRoleRecordTypeModel.Result addRoleRecordTypeResult = recordTypeAuthProxy.addRoleRecordType(arg, PAAS_PRIVILEGE_HEADDER.buildHeader(user.getTenantId()));
        result.setSuccess(addRoleRecordTypeResult.isSuccess());
        return result;

    }

    @ServiceMethod("assignRecordAndLayout")
    public AssignRecordAndLayoutModel.Result assignRecordAndLayout(ServiceContext context, AssignRecordAndLayoutModel.Arg arg) {
        log.warn("PartnerInitService assignRecordAndLayout tenantId->{}", context.getTenantId());

        String layoutName = arg.getApiName() + "_layout_generate_by_UDObjectServer__c";
        String layoutJson = "[{\"roleCode\":\"%s\",\"label\":\"地址管理\",\"record_layout\":[{\"record_api_name\":\"default__c\",\"layout_api_name\":\"%s\"}]}]";
        layoutJson = String.format(layoutJson, arg.getRoleCode(), layoutName);


        String recordJson = "[{\"default_record\":\"default__c\",\"roleCode\":\"%s\",\"records\":[\"default__c\"]}]";
        recordJson = String.format(recordJson, arg.getRoleCode());


        User user = context.getUser();


        AssignRecordAndLayoutModel.Result result = new AssignRecordAndLayoutModel.Result();
        try {
            assignRecord(context.getTenantId(), arg.getApiName(), recordJson, user);
            saveLayoutAssign(arg.getApiName(), layoutJson, user);
        } catch (Exception e) {
            log.warn("PartnerInitService assignRecordAndLayout error tenantId->{}", context.getTenantId(), e);
            result.setResult(Boolean.FALSE);
            return result;
        }
        result.setResult(Boolean.TRUE);
        return result;
    }

    public void addButtonAndFun(ServiceContext context) {
        List<IUdefAction> actionRst = createAction(context);
        createButton(context, actionRst);
        createFun(context, "ContactObj");
    }

    private void createFun(ServiceContext context, String apiName) {
        StringBuilder stringBuilder = new StringBuilder();
        List<String> actionCodeList = Lists.newArrayList("enable_partner_view__c");
        try {
            functionPrivilegeService.createFuncCode(context.getUser(), apiName, "enable_partner_view__c", "是否合作伙伴可见");
            serviceFacade.updateUserDefinedFuncAccess(context.getUser(), PrivilegeConstants.ADMIN_ROLE_CODE, apiName, actionCodeList, Lists.newArrayList());
        } catch (Exception e) {
            stringBuilder.append(String.format("batchInitPartnerRelateAction error,apiName %s,failMsg %s", apiName, e.getMessage()));
            log.error("batchInitPartnerRelateAction error,apiName {} {}", apiName, e.toString());
        }
    }

    private void createButton(ServiceContext context, List<IUdefAction> actionRst) {
        User user = new User(context.getTenantId(), context.getUser().getUserId());
        IUdefButton button = new UdefButton();
        button.setDescribeApiName("ContactObj");
        button.setApiName("enable_partner_view__c");
        String json = "[{\"type\": \"true_or_false\", \"label\": \"是否可见\", \"remark\": \"\", \"api_name\": \"form_enable_partner_view\", \"is_required\": false, \"object_api_name\": \"ContactObj\"}]";
        List ParamForm = GsonUtil.json2object(json, List.class);
        button.setParamForm(ParamForm);
        button.setUsePages(Lists.newArrayList("detail"));
        button.setLabel("是否合作伙伴可见");
        button.setButtonType("common");
        button.setActions(actionRst.stream().map(x -> x.getId()).collect(Collectors.toList()));
        button.setIsActive(true);
        button.setDeleted(false);
        buttonService.createCustomButton(user, button);
    }

    private List<IUdefAction> createAction(ServiceContext context) {
        IUdefAction action = new UdefAction();
        action.setActionType("updates");
        action.setLabel("更新是否合作伙伴可见");
        action.setActionParamter("{\"fields\":[{\"field\":\"enable_partner_view\",\"value\":\"$form_enable_partner_view$\",\"var_type\":\"variable\"}]}");
        action.setRemark("");
        action.setDescribeApiName("ContactObj");
        action.setTenantId(context.getTenantId());
        return actionService.bulkCreateAction(context.getUser(), Lists.newArrayList(action));
    }

    /**
     * 初始化线索对象转换合作伙伴功能与角色权限
     *
     * @param user
     * @param apiNames
     * @return
     */
    public String batchInitPartnerTransferAction(User user, List<String> apiNames) {
        StringBuilder sb = new StringBuilder();
        if (CollectionUtils.empty(apiNames)) {
            return sb.toString();
        }
        List<String> actionCodeList = Lists.newArrayList(ObjectAction.TRANSFER_PARTNER.getActionCode());
        for (String needInitApiName : apiNames) {
            try {
                serviceFacade.batchCreateFunc(user, needInitApiName, actionCodeList);
                serviceFacade.updateUserDefinedFuncAccess(user, PrivilegeConstants.ADMIN_ROLE_CODE, needInitApiName, actionCodeList, Lists.newArrayList());
                // 转换新建
                serviceFacade.batchCreateFunc(user, SFAPreDefineObject.Partner.getApiName(), Lists.newArrayList(ObjectAction.TRANSFER_ADD.getActionCode()));
                serviceFacade.updateUserDefinedFuncAccess(user, PrivilegeConstants.ADMIN_ROLE_CODE, SFAPreDefineObject.Partner.getApiName(), Lists.newArrayList(ObjectAction.TRANSFER_ADD.getActionCode()), Lists.newArrayList());
            } catch (Exception e) {
                sb.append(String.format("batchInitPartnerTransferAction error,apiName %s,failMsg %s", needInitApiName, e.getMessage()));
                log.error("batchInitPartnerTransferAction error,apiName {} {}", needInitApiName, e.toString());
            }
        }
        return StringUtils.isNotEmpty(sb.toString()) ? " \n batchInitPartnerTransferAction success" : sb.toString();
    }
}
