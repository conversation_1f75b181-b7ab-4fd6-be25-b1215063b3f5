package com.facishare.crm.sfa.predefine.service.model.Feed;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public interface GetFeedExtDataListResult {
    @Data
    class Arg implements Serializable {
        private Integer employeeId;

        /*
         * FeedExtDataType
         * */
        private List<FeedExtDataKey> feedExtDataKeys;
    }

    @Data
    class Result {
        private List<FeedExtDataInfo> feedExtDataList = Lists.newArrayList();
    }
    @Data
    class FeedExtDataKey{
        String api_name;
        String data_id;
    }
    @Data
    class FeedExtDataInfo{
        String dataId;
        String dataName;
        String apiName;
        String objectDisplayName;
    }
}
