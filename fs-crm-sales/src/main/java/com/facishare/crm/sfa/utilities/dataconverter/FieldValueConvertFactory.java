package com.facishare.crm.sfa.utilities.dataconverter;

import com.facishare.crm.sfa.utilities.dataconverter.converter.*;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.google.common.collect.Maps;
import lombok.NonNull;

import java.util.Map;

import static com.facishare.paas.metadata.api.describe.IFieldType.*;

/**
 * 字段值转换工厂类
 * <AUTHOR>
 */
public class FieldValueConvertFactory {
    private static Map<String, AbstractFieldValueConverter> CONVERT_MAP = Maps.newHashMap();

    static {
        CONVERT_MAP.put(MULTI_LEVEL_SELECT_ONE, new MultiLevelSelectOneFieldValueConverter());
        CONVERT_MAP.put(RECORD_TYPE, new RecordTypeFieldValueConverter());
        CONVERT_MAP.put(SELECT_ONE, new SelectOneFieldValueConverter());
        CONVERT_MAP.put(SELECT_MANY, new SelectManyFieldValueConverter());
        CONVERT_MAP.put(DATE, new DateFieldValueConverter());
        CONVERT_MAP.put(DATE_TIME, new DateTimeFieldValueConverter());
        CONVERT_MAP.put(TIME, new TimeFieldValueConverter());
        CONVERT_MAP.put(TRUE_OR_FALSE, new BooleanFieldValueConverter());
        CONVERT_MAP.put(LOCATION, new LocationFieldValueConverter());
        CONVERT_MAP.put(EMPLOYEE, new EmployeeFieldValueConverter());
        CONVERT_MAP.put(DEPARTMENT, new DepartmentFieldValueConverter());
        CONVERT_MAP.put(PERCENTILE, new PercentFieldValueConverter());
        CONVERT_MAP.put(COUNTRY, new SelectOneFieldValueConverter());
        CONVERT_MAP.put(PROVINCE, new SelectOneFieldValueConverter());
        CONVERT_MAP.put(CITY, new SelectOneFieldValueConverter());
        CONVERT_MAP.put(DISTRICT, new SelectOneFieldValueConverter());
    }

    public static AbstractFieldValueConverter getConverter(@NonNull IFieldDescribe fieldDescribe) {
        String fieldType = fieldDescribe.getType();
        if (ObjectDataExt.OWNER_DEPARTMENT.equals(fieldDescribe.getApiName())) {
            fieldType = DEPARTMENT;
        }
        if (CONVERT_MAP.containsKey(fieldType)) {
            return CONVERT_MAP.get(fieldType);
        } else {
            return UnHandleFieldValueConverter.instance();
        }
    }
}
