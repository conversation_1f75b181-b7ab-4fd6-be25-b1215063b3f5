package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.model.Enum.SessionBOCItemKeys;
import com.facishare.crm.sfa.predefine.action.listener.ReturnedGoodsInvoiceAddActionListener;
import com.facishare.crm.sfa.predefine.service.QiXinTodoService;
import com.facishare.crmcommon.constants.ReturnedGoodsInvoiceConstants;
import com.facishare.crm.sfa.utilities.constant.SalesOrderConstants;
import com.facishare.crm.sfa.utilities.util.SFAConfigUtil;
import com.facishare.crm.sfa.utilities.validator.ReturnedGoodsInvoiceValidator;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAPINameMapping;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ActionListener;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;

/**
 * Created by luxin on 2018/1/2.
 * @IgnoreI18nFile
 */
@Slf4j
public class ReturnedGoodsInvoiceAddAction extends StandardAddAction {
    private static final QiXinTodoService qiXinTodoService = SpringUtil.getContext().getBean(QiXinTodoService.class);
    private List<Integer> approverIds = Lists.newArrayList();
    protected boolean softApprovalFlow = false;

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        initOpenKeyStatus();
        String accountId = objectData.get(ReturnedGoodsInvoiceConstants.ReturnedGoodsInvoiceField.ACCOUNT_ID.getApiName(), String.class);
        String salesOrderId = objectData.get(ReturnedGoodsInvoiceConstants.ReturnedGoodsInvoiceField.ORDER_ID.getApiName(), String.class);
        ReturnedGoodsInvoiceValidator.validateSalesOrder(actionContext.getTenantId(), accountId, salesOrderId);
        ReturnedGoodsInvoiceValidator.validateReturnedGoodsInvoiceProduct(actionContext.getTenantId(), salesOrderId, detailObjectData, false);


        // 处理为与订单相同逻辑的结构
        if(needTriggerApprovalFlow()){
            validateWorkFlow();
        }else{
            objectData.set(ObjectLifeStatus.LIFE_STATUS_API_NAME, ObjectLifeStatus.NORMAL.getCode());
        }

        Boolean requiredApproval = ReturnedGoodsInvoiceValidator.requiredApprovalFlow(actionContext);
        if(Objects.equals(requiredApproval, Boolean.TRUE)) {
            boolean isSoft = SFAConfigUtil.isSoftWorkflow(actionContext.getTenantId(), ObjectAPINameMapping.ReturnedGoodsInvoice.getApiName());
            ReturnedGoodsInvoiceValidator.validateWorkFlow(actionContext, objectData, objectData.getOwner().get(0), isSoft);
            approverIds = (List<Integer>) objectData.get("approver");
        } else {
            objectData.set(ObjectLifeStatus.LIFE_STATUS_API_NAME, ObjectLifeStatus.NORMAL.getCode());
        }

        setDefaultValue();
    }

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg,result);

        if(CollectionUtils.notEmpty(approverIds)) {
            List<String> strIds = Lists.newArrayList();
            approverIds.forEach(x -> strIds.add(x.toString()));
            qiXinTodoService.sendTodo(actionContext.getTenantId(), SessionBOCItemKeys.TobeConfirmedReturnOrder,
                    actionContext.getObjectApiName(), objectData.getId(), actionContext.getUser().getUserId(), strIds);
        }

        return result;
    }

    @Override
    protected void triggerApprovalFlow() {
        if(needTriggerApprovalFlow()){
            if (ReturnedGoodsInvoiceValidator.requiredApprovalFlow(actionContext)) {
                if(SFAConfigUtil.isSoftWorkflow(actionContext.getTenantId(), Utils.RETURN_GOODS_INVOICE_API_NAME)) {
                    objectData.set("current_level", 0);
                    objectData.set("life_status", ObjectLifeStatus.UNDER_REVIEW.getCode());
                    List<String> toUpdateFields = Lists.newArrayList("current_level", "life_status");
                    serviceFacade.batchUpdateByFields(actionContext.getUser(), Lists.newArrayList(objectData), toUpdateFields);
                }  else {
                    super.triggerApprovalFlow();
                }
            }
        }
    }

    @Override
    public List<Class<? extends ActionListener<Arg, Result>>> getActionListenerClassList() {
        List<Class<? extends ActionListener<Arg, Result>>> classList = super.getActionListenerClassList();
        classList.add(ReturnedGoodsInvoiceAddActionListener.class);
        return classList;
    }

    private void setDefaultValue() {
        objectData.set(ReturnedGoodsInvoiceConstants.ReturnedGoodsInvoiceField.STATUS.getApiName()
                , SalesOrderConstants.OrderStatus.TO_BE_CONFIRMED.getValue());
        objectData.set(SalesOrderConstants.SalesOrderField.SUBMIT_TIME.getApiName()
                , System.currentTimeMillis());
    }

    protected void validateWorkFlow(){
        List<String> owner = objectData.getOwner();
        if(CollectionUtils.empty(owner)){
            throw new ValidateException("数据负责人没填写，请从新填写数据");
        }
        ReturnedGoodsInvoiceValidator.validateWorkFlow(actionContext, objectData, objectData.getOwner().get(0), softApprovalFlow);
        approverIds = (List<Integer>) objectData.get("approver");
    }

    protected void initOpenKeyStatus(){
        softApprovalFlow = SFAConfigUtil.isSoftWorkflow(actionContext.getTenantId(), ObjectAPINameMapping.ReturnedGoodsInvoice.getApiName());
    }
}
