package com.facishare.crm.sfa.predefine.controller;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.sfa.predefine.controller.SFAWebDetailController;
import com.facishare.crm.sfa.utilities.util.LayoutUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.appframework.metadata.layout.LayoutStructure;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.TabSection;
import com.facishare.paas.metadata.impl.ui.layout.component.TabsComponent;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2019/12/11 11:49 上午
 */
@Slf4j
public class SalesOrderProductWebDetailController extends SFAWebDetailController {

    private List<String> layoutStructures = Lists.newArrayList("BPM_related_list","tab_BPM_related_list",
            "Approval_related_list","tab_Approval_related_list",
            "ReturnedGoodsInvoiceProductObj_order_product_id_related_list","tab_ReturnedGoodsInvoiceProductObj_order_product_id_related_list");

    private List<String> removeActionList = Lists.newArrayList(
            ObjectAction.INVALID.getActionCode(),
            ObjectAction.LOCK.getActionCode(),
            ObjectAction.UNLOCK.getActionCode(),
            ObjectAction.CONFIRM.getActionCode(),
            ObjectAction.REJECT.getActionCode(),
            ObjectAction.UPDATE.getActionCode(),
            ObjectAction.CLONE.getActionCode(),
            ObjectAction.DELETE.getActionCode(),
            ObjectAction.CREATE.getActionCode(),
            ObjectAction.PRINT.getActionCode(),
            ObjectAction.CHANGE_PARTNER_OWNER.getActionCode(),
            ObjectAction.CHANGE_PARTNER.getActionCode(),
            ObjectAction.DELETE_PARTNER.getActionCode());

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        if (newResult.getLayout() == null) {
            return newResult;
        }
        ILayout layout = new Layout(newResult.getLayout());
        removeLayoutRelateObj(layout);
        removeDetailButtons(layout);
        if(Objects.equals(arg.getLayoutAgentType(), "mobile")){
            removeMobileButtons(layout);
        }
//        removeDetailLayoutStructure(layout);
        return newResult;
    }

    public void removeDetailLayoutStructure(ILayout layout) {
        Map<String, Object> layoutStructure = layout.getLayoutStructure();
        List<Map> lefts = (List) ((Map)layoutStructure.get("body")).get("left");
        lefts.forEach(map -> {
            if (map.containsKey("children")) {
                List<Map> keys = (List) map.get("children");
                keys.removeIf(x -> layoutStructures.contains(x.get("api_name").toString()));
            }
        });
    }

    public void removeLayoutRelateObj(ILayout layout) {
        WebDetailLayout.of(layout).removeComponents(layoutStructures);
    }

    private  void  removeMobileButtons(ILayout layout){
        List<IButton> buttons = layout.getButtons();
        buttons.removeIf(button -> removeActionList.contains(button.getAction()));
        layout.setButtons(buttons);
    }



    private void removeDetailButtons(ILayout layout) {
        try {
            List<IComponent> components1 = layout.getComponents();
            components1.forEach(x -> {
                if (Objects.equals(x.getName(), "head_info")) {
                    List<IButton> buttons = x.getButtons();
                    buttons.removeIf(button->removeActionList.contains(button.getAction()));
                    x.setButtons(buttons);
                }
            });
        } catch (MetadataServiceException e) {
            log.warn("removeDetailButtons",e);
        }

    }

}
