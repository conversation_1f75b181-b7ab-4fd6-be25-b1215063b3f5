package com.facishare.crm.sfa.predefine.service.model;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface DataCleanModel {

    @Data
    public class Arg{
        String tenantID;
        String employeeID;
        String dataCleanID;
        Integer pageSize;
        Integer pageNumber;
        String objectApiName;
    }
    @Data
    @Builder
    public class Result{
        Integer groupCount;
        String objectApiName;
        PageInfo pageInfo;
        List<ObjectDataDocument> dataList;
    }
    @Data
    @Builder
    public class PageInfo{
        Integer pageSize;
        Integer pageNumber;
        Integer pageCount;
        long totalCount;
    }
}
