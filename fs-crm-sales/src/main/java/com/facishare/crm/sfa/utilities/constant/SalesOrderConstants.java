package com.facishare.crm.sfa.utilities.constant;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import java.util.List;
import java.util.Set;

/**
 * Create by baoxinxue 2019/01/10
 * @IgnoreI18nFile
 */
public interface SalesOrderConstants {

    /**
     * 订单财务
     */
    String ORDER_FINANCE_ROLE = "00000000000000000000000000000003";

    /**
     * 订单管理员
     */
    String ORDER_MANAGER_ROLE = "00000000000000000000000000000016";

    /**
     * 针对特殊价目表校验，不走订单的价目表校验，会走对接方的前置校验
     */
    String SKIP_PRICE_BOOK_VALIDATE = "skipPriceBookValidate";

    /**
     * 发货人员
     */
    String GOODS_SENDING_PERSON_ROLE = "00000000000000000000000000000020";

    /**
     * 订单产品的   delivered_count，delivery_amount
     * 订单的   delivered_amount_sum，confirmed_delivery_date，delivery_comment，logistics_status，confirmed_receive_date
     * 应该就这些了  @翟羽佳
     */

    Set<String> newInvoiceSalesOrderFields =  Sets.newHashSet("invoiced_amount", "invoice_amount", "no_invoice_amount", "invoice_status","order_mode");
    Set<String> newInvoiceSalesOrderProductFields =  Sets.newHashSet("invoiced_quantity", "invoiced_amount", "no_invoice_quantity", "no_invoice_amount", "invoice_status", "order_product_amount");

    public static List<String> salesOrderProductResetNullToField = Lists.newArrayList("delivered_count", "delivery_amount", "lock_status", "actual_refunded_amount"
            , "invoiced_quantity", "invoiced_amount", "no_invoice_quantity", "no_invoice_amount", "invoice_status", "order_product_amount","quantity_returned");

    public static List<String> salesOrderProductResetNullToZeroField = Lists.newArrayList("invoiced_quantity", "invoiced_amount","order_product_amount");

    public static List<String> salesOrderResetNullToField = Lists.newArrayList("name", "product_amount", "order_amount", "returned_goods_amount", "payment_amount",
            "refund_amount", "invoiced_amount", "no_invoice_amount", "invoice_status", "receivable_amount", "delivery_date", "delivery_comment", "logistics_status", "order_time", "data_own_department",
            "data_own_department__r", "delivered_amount_sum", "confirmed_delivery_date", "confirmed_receive_date", "confirm_time", "lock_status", "plan_payment_amount", "quantity_returned");

    public static String queryPromotionSql = "select temp.promotion_id, %s\n" +
            "from mt_data as mt\n" +
            "       inner join (select bsop.order_id, bsop.extend_obj_data_id, bsop.promotion_id\n" +
            "                   from biz_sales_order as bso\n" +
            "                          left join biz_sales_order_product bsop\n" +
            "                            on bso.id = bsop.order_id and bso.tenant_id = bsop.tenant_id\n" +
            "                   where bso.tenant_id = '%s'\n" +
            "                     and bso.life_status in ('normal', 'under_review', 'in_change')\n" +
            "                     and bso.is_deleted = '0'\n" +
            "                     and bsop.is_deleted = '0'\n" +
            "                     and coalesce(bsop.is_giveaway, '0') <> '1'\n" +
            "                     and bsop.promotion_id in('%s'))temp on mt.id = temp.extend_obj_data_id\n" +
            "where mt.tenant_id = '%s'\n" +
            "  and %s is not null;";


    public static String queryInnerJoinSql = "SELECT\n" +
            "\tID\n" +
            "FROM\n" +
            "\tbiz_sales_order AS SalesOrderObj\n" +
            "INNER JOIN work_flow_data ON SalesOrderObj. ID = work_flow_data.data_id\n" +
            "AND SalesOrderObj.current_level + 1 = work_flow_data. LEVEL\n" +
            "AND SalesOrderObj.work_flow_id = work_flow_data.work_flow_id\n" +
            "AND SalesOrderObj.tenant_id = work_flow_data.ei :: TEXT\n" +
            "WHERE\n" +
            "\t1 = 1\n" +
            "AND SalesOrderObj.\"life_status\" = 'under_review'\n" +
            "AND SalesOrderObj.\"tenant_id\" = '%s'\n" +
            "AND work_flow_data.status = 1\n" +
            "AND work_flow_data.employee_id = %s\n" +
            "UNION\n" +
            "\tSELECT\n" +
            "\t\tID\n" +
            "\tFROM\n" +
            "\t\tbiz_sales_order AS SalesOrderObj\n" +
            "\tINNER JOIN work_flow_data ON SalesOrderObj. ID = work_flow_data.data_id\n" +
            "\tAND SalesOrderObj.current_level + 1 = work_flow_data. LEVEL\n" +
            "\tAND SalesOrderObj.work_flow_id = work_flow_data.work_flow_id\n" +
            "\tAND SalesOrderObj.tenant_id = work_flow_data.ei :: TEXT\n" +
            "\tWHERE\n" +
            "\t\t1 = 1\n" +
            "\tAND SalesOrderObj.\"life_status\" = 'under_review'\n" +
            "\tAND SalesOrderObj.\"tenant_id\" = '%s'\n" +
            "\tAND work_flow_data.status = 1\n" +
            "\tAND work_flow_data.employee_id = 0\n" +
            "\tAND SalesOrderObj. OWNER IN ('%s');\n" +
            "\n";

    public static String queryInnerJoinNoRolesSql = "SELECT\n" +
            "\tID\n" +
            "FROM\n" +
            "\tbiz_sales_order AS SalesOrderObj\n" +
            "INNER JOIN work_flow_data ON SalesOrderObj. ID = work_flow_data.data_id\n" +
            "AND SalesOrderObj.current_level + 1 = work_flow_data. LEVEL\n" +
            "AND SalesOrderObj.work_flow_id = work_flow_data.work_flow_id\n" +
            "AND SalesOrderObj.tenant_id = work_flow_data.ei :: TEXT\n" +
            "WHERE\n" +
            "\t1 = 1\n" +
            "AND SalesOrderObj.\"life_status\" = 'under_review'\n" +
            "AND SalesOrderObj.\"tenant_id\" = '%s'\n" +
            "AND work_flow_data.status = 1\n" +
            "AND work_flow_data.employee_id = %s;\n" +
            "\n";

    public static String queryOwnerCustomFlowSql = "SELECT\n" +
            "\tID\n" +
            "FROM\n" +
            "\tbiz_sales_order AS SalesOrderObj\n" +
            "INNER JOIN work_flow_data ON SalesOrderObj. ID = work_flow_data.data_id\n" +
            "AND SalesOrderObj.current_level + 1 = work_flow_data. LEVEL\n" +
            "AND SalesOrderObj.work_flow_id = work_flow_data.work_flow_id\n" +
            "AND SalesOrderObj.tenant_id = work_flow_data.ei :: TEXT\n" +
            "WHERE\n" +
            "\t1 = 1\n" +
            "AND SalesOrderObj.\"life_status\" = 'under_review'\n" +
            "AND SalesOrderObj.\"tenant_id\" = '%s'\n" +
            "AND work_flow_data.status = 1\n" +
            "AND work_flow_data.employee_id = %s ;";

    public static String queryorderManagerFlowSql = "SELECT\n" +
            "\tID\n" +
            "FROM\n" +
            "\tbiz_sales_order AS SalesOrderObj\n" +
            "INNER JOIN work_flow_data ON SalesOrderObj. ID = work_flow_data.data_id\n" +
            "AND SalesOrderObj.current_level + 1 = work_flow_data. LEVEL\n" +
            "AND SalesOrderObj.work_flow_id = work_flow_data.work_flow_id\n" +
            "AND SalesOrderObj.tenant_id = work_flow_data.ei :: TEXT\n" +
            "WHERE\n" +
            "\t1 = 1\n" +
            "AND SalesOrderObj.\"life_status\" = 'under_review'\n" +
            "AND SalesOrderObj.\"tenant_id\" = '%s'\n" +
            "AND work_flow_data.status = 1\n" +
            "AND work_flow_data.employee_id = 0\n" +
            "AND work_flow_data.approver_type = 3\n" +
            "AND SalesOrderObj. OWNER IN ('%s');\n" +
            "\n";

    public static String queryfinanceFlowSql = "SELECT\n" +
            "\tID\n" +
            "FROM\n" +
            "\tbiz_sales_order AS SalesOrderObj\n" +
            "INNER JOIN work_flow_data ON SalesOrderObj. ID = work_flow_data.data_id\n" +
            "AND SalesOrderObj.current_level + 1 = work_flow_data. LEVEL\n" +
            "AND SalesOrderObj.work_flow_id = work_flow_data.work_flow_id\n" +
            "AND SalesOrderObj.tenant_id = work_flow_data.ei :: TEXT\n" +
            "WHERE\n" +
            "\t1 = 1\n" +
            "AND SalesOrderObj.\"life_status\" = 'under_review'\n" +
            "AND SalesOrderObj.\"tenant_id\" = '%s'\n" +
            "AND work_flow_data.status = 1\n" +
            "AND work_flow_data.employee_id = 0\n" +
            "AND work_flow_data.approver_type = 2\n" +
            "AND SalesOrderObj. OWNER IN ('%s');\n" +
            "\n";


    String queryCountSalesOrderProduct = "select order_id, id, no_invoice_amount \n" +
            "from biz_sales_order_product\n" +
            "where tenant_id = '%s'\n" +
            "  and is_deleted = 0\n" +
            "  and life_status != 'invalid'\n" +
            "  and order_id in\n" +
            "      ('%s');";


    // 不可以超额开票
    String queryCountSalesOrderProductNewInvoice = "select *\n" +
            "from biz_sales_order_product\n" +
            "       left join (select order_product_id as orderProductId, tenant_id, sum(invoiced_amount :: numeric) as sumAmount\n" +
            "                  from invoice_line\n" +
            "                  where tenant_id = '%s'\n" +
            "                    and is_deleted = '0'\n" +
            "                    and invoice_line.life_status = 'under_review'\n" +
            "                  group by order_product_id, tenant_id) as newTable\n" +
            "         on biz_sales_order_product.id = newTable.orderProductId\n" +
            "              and biz_sales_order_product.tenant_id = newTable.tenant_id\n" +
            "where biz_sales_order_product.tenant_id = '%s'\n" +
            "  and biz_sales_order_product.is_deleted = 0\n" +
            "  and biz_sales_order_product.life_status != 'invalid'\n" +
            "  and biz_sales_order_product.invoice_status in ('2', '3')\n" +
            "  and biz_sales_order_product.no_invoice_amount :: numeric > 0\n" +
            "  and biz_sales_order_product.no_invoice_amount :: numeric > COALESCE(sumAmount, 0)\n" +
            "  and order_id in\n" +
            "      ('%s');";



    String queryOrderProductGroupByOrder = "select order_id, count(*)\n" +
            "from biz_sales_order_product\n" +
            "where tenant_id = '%s'\n" +
            "  and is_deleted = 0\n" +
            "  and life_status != 'invalid'\n" +
            "  and order_id in ('%s')\n" +
            "group by order_id;";

    // 不可以超额开票
    String queryOrderProductGroupByOrderNewInvoice = "select order_id, count(*)\n" +
            "from biz_sales_order_product as SalesOrderProductObj\n" +
            "       left join (select order_product_id                as orderProductId,\n" +
            "                         tenant_id                       as tenantId,\n" +
            "                         sum(invoiced_amount :: numeric) as sumAmount\n" +
            "                  from invoice_line\n" +
            "                  where tenant_id = '%s'\n" +
            "                    and is_deleted = '0'\n" +
            "                    and invoice_line.life_status = 'under_review'\n" +
            "                  group by order_product_id, tenant_id) as newTable\n" +
            "         on SalesOrderProductObj.id = newTable.orderProductId and\n" +
            "            SalesOrderProductObj.tenant_id = newTable.tenantId\n" +
            "where 1 = 1\n" +
            "  and SalesOrderProductObj.\"invoice_status\" IN ('2', '3')\n" +
            "  and SalesOrderProductObj.\"no_invoice_amount\" :: numeric > 0\n" +
            "  and SalesOrderProductObj.\"order_id\" in('%s')\n" +
            "  and SalesOrderProductObj.\"tenant_id\" = '%s'\n" +
            "  and SalesOrderProductObj.\"is_deleted\" = '0'\n" +
            "  and SalesOrderProductObj.no_invoice_amount :: numeric > COALESCE(sumAmount, 0)\n" +
            "group by SalesOrderProductObj.order_id;";

    String queryOrderProductFromInvoiceLineNewInvoice = "select order_id, id\n" +
            "from biz_sales_order_product as SalesOrderProductObj\n" +
            "       left join (select order_product_id                as orderProductId,\n" +
            "                         tenant_id                       as tenantId,\n" +
            "                         sum(invoiced_amount :: numeric) as sumAmount\n" +
            "                  from invoice_line\n" +
            "                  where tenant_id = '%s'\n" +
            "                    and is_deleted = '0'\n" +
            "                    and invoice_line.invoice_id = '%s'\n" +
            "                    and invoice_line.life_status = 'under_review'\n" +
            "                  group by order_product_id, tenant_id) as newTable\n" +
            "         on SalesOrderProductObj.id = newTable.orderProductId and\n" +
            "            SalesOrderProductObj.tenant_id = newTable.tenantId\n" +
            "where 1 = 1\n" +
            "  and SalesOrderProductObj.\"invoice_status\" IN ('2', '3')\n" +
            "  and SalesOrderProductObj.\"no_invoice_amount\" :: numeric > 0\n" +
            "  and SalesOrderProductObj.\"order_id\" in('%s')\n" +
            "  and SalesOrderProductObj.\"tenant_id\" = '%s'\n" +
            "  and SalesOrderProductObj.\"is_deleted\" = '0'\n" +
            "  and SalesOrderProductObj.no_invoice_amount :: numeric > COALESCE(sumAmount, 0);";

    // 查询订单下是否有在审核中的数据
    String queryInvoiceBySalesOrderId = "select COALESCE(sum(invoiced_amount :: numeric),0) as orderInvoiceAmount\n" +
            "from invoice_line\n" +
            "where tenant_id = '%s'\n" +
            "  and order_id = '%s'\n" +
            "  and life_status = 'under_review';";



    enum SalesOrderField {
        ACCOUNT_ID("account_id", "客户名称"),
        PARTNER_ID("partner_id", "合作伙伴名称"),
        SETTLE_TYPE("settle_type", "结算方式"),
        ORDER_AMOUNT("order_amount", "销售订单金额"),
        FORCE_COMMIT("IsForceCommit", "是否强制提交"),
        OPPORTUNITY_ID("opportunity_id", "商机名称"),
        NEW_OPPORTUNITY_ID("new_opportunity_id", "商机2.0"),
        SHIP_TO_ID("ship_to_id", "收货人"),
        WORK_FLOW("WorkFlowInfo", "自由流程"),
        PRICE_BOOK_ID("price_book_id", "价目表"),
        ORDER_STATUS("order_status", "状态"),
        LOGISTICS_STATUS("logistics_status", "发货状态"),
        SUBMIT_TIME("submit_time", "提交时间"),
        RECEIVABLE_AMOUNT("receivable_amount", "待回款金额"),
        ORDER_TIME("order_time", "下单日期"),
        PAYMENT_AMOUNT("payment_amount", "已回款金额"),
        INVOICE_AMOUNT("invoice_amount", "开票申请总额"),
        REFUND_AMOUNT("refund_amount", "已退款金额"),
        INVOICED_AMOUNT("invoiced_amount", "已开票金额"),
        NO_INVOICE_AMOUNT("no_invoice_amount", "待开票金额"),
        RETURNED_GOODS_AMOUNT("returned_goods_amount", "退货单金额");

        private String filedApiName;
        private String label;

        SalesOrderField(String filedApiName, String label) {
            this.filedApiName = filedApiName;
            this.label = label;
        }

        public String getApiName() {
            return this.filedApiName;
        }
    }

    //被其他字段lookup字段名
    String REF_SALES_ORDER_FIELD = "order_id";
    //客户账户余额不足用ErrorCode
    int ACCOUNT_NOT_ENOUGH_CODE = ********;

    enum SettleType {
        PRE_PAY("1", "预付"),
        CASH("2", "现付"),
        CREDIT("3", "赊销");

        private String value;
        private String label;

        SettleType(String value, String label) {
            this.value = value;
            this.label = label;
        }

        public String getValue() {
            return this.value;
        }
    }

    enum OrderStatus {
        TO_BE_CONFIRMED("6", "确认中"),
        CONFIRMED("7", "已确认"),
        REJECTED("8", "已驳回"),
        RECALLED("9", "已撤回"),
        DELIVERED("10", "已发货"),
        RECEIVED("11", "已收货"),
        INVALID("99", "作废");

        private String value;
        private String label;

        OrderStatus(String value, String label) {
            this.value = value;
            this.label = label;
        }

        public String getValue() {
            return this.value;
        }
    }

    enum LogisticsStatus {
        TO_BE_SHIPPED("1", "待发货"),
        PARTIAL_DELIVERY("2", "部分发货"),
        CONSIGNED("3", "已发货"),
        PARTIAL_RECEIPT("4", "部分收货"),
        RECEIVED("5", "已收货");

        private String value;
        private String label;

        LogisticsStatus(String value, String label) {
            this.value = value;
            this.label = label;
        }

        public String getValue() {
            return this.value;
        }
    }

    enum SalesOrderProductField {
        PRICE_BOOK_PRODUCT_ID("price_book_product_id", "价目表明细"),
        PRODUCT_ID("product_id", "产品名称"),
        QUANTITY("quantity", "数量"),
        PRICE_BOOK_ID("price_book_id", "价目表"),
        ORDER_ID("order_id", "订单"),
        PROD_PKG("prod_pkg_key","bom虚拟key"),
        PARENT_PROD_PKG("parent_prod_pkg_key","父bom虚拟key"),
        ROOT_PROD_PKG("root_prod_pkg_key","根bom虚拟key"),
        SALES_PRICE("sales_price", "销售单价"),
        PRODUCT_PRICE("product_price", "价格");

        private String filedApiName;
        private String label;

        SalesOrderProductField(String filedApiName, String label) {
            this.filedApiName = filedApiName;
            this.label = label;
        }

        public String getApiName() {
            return this.filedApiName;
        }
    }
}
