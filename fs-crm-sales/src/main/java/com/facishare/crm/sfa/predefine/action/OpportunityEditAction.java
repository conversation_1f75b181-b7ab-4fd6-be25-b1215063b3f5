package com.facishare.crm.sfa.predefine.action;

import com.facishare.crmcommon.constants.SystemConstants;
import com.facishare.crm.sfa.utilities.util.JsonUtil;
import com.facishare.crm.sfa.utilities.util.LogUtil;
import com.facishare.crm.sfa.utilities.util.OpportunityUtil;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 客户编辑操作
 * <p>
 * Created by liyigua<PERSON> on 2017/7/13.
 */
@Slf4j
public class OpportunityEditAction extends StandardEditAction {
    private static List<String> editExceptFields = Lists.newArrayList(SystemConstants.Field.Owner.apiName
            ,"sales_process_id","sales_process_name");
    @Override
    protected void before(Arg arg) {
        log.info("OpportunityEditAction>before()arg=" + JsonUtil.toJsonWithNullValues(arg));
        ObjectDataDocument dataDocument = arg.getObjectData();
        for(String field : editExceptFields){
            if(dataDocument.containsKey(field)){
                dataDocument.remove(field);
            }
        }
        super.before(arg);

    }

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        String opportunityID = result.getObjectData().getId();
        long expectedDealTime = objectData.get("expected_deal_closed_date", Long.class);
        if (expectedDealTime > System.currentTimeMillis()) {
            OpportunityUtil.addOrModifyOpportunityTimeOutTask(actionContext.getTenantId(),actionContext.getUser(),opportunityID,expectedDealTime,false);
        }

        //todo 汇聚埋点
        //todo 日志记录
        //todo 机会到期提醒任务
        return  result;
    }

    @Override
    protected void recordLog() {
        List<IObjectData> dataToUpdate = this.getAllDataToUpdate();
        LogUtil.recordEditSpecailLog(actionContext.getUser(), dataToUpdate, objectData, objectDescribe);
        logAsync(detailsToAdd, EventType.ADD, ActionType.Add);
    }
}
