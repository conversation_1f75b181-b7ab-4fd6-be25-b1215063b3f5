package com.facishare.crm.sfa.utilities.proxy.model.salesorder;


import com.facishare.crm.sfa.utilities.proxy.model.salesorder.salesorderparam.*;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/10/14 18:10
 */
public interface SalesOrderInterceptorModel {

    @Data
    class AddBeforeResult {
        private int code;
        private String message;
        private SalesOrderAddBeforeModel.Result data;

        public boolean isSuccess() {
           return code == 0;
        }
    }

    @Data
    class AddAfterResult {
        private int code;
        private String message;
        private  SalesOrderAddAfterModel.Result data;

        public boolean isSuccess() {
            return code == 0;
        }
    }

    @Data
    class AddFlowCompletedAfterResult{
        private int code;
        private String message;
        private  SalesOrderAddAfterModel.Result data;

        public boolean isSuccess() {
            return code == 0;
        }
    }

    @Data
    class EditBeforeResult{
        private int code;
        private String message;
        private  SalesOrderEditBeforeModel.Result data;

        public boolean isSuccess() {
            return  code == 0;
        }
    }


    @Data
    class EditAfterResult{
        private int code;
        private String message;
        private  SalesOrderEditAfterModel.Result data;

        public boolean isSuccess() {
            return  code == 0;
        }
    }

    @Data
    class EditFlowCompletedAfterResult{
        private int code;
        private String message;
        private  SalesOrderEditFlowCompletedAfterModel.Result data;

        public boolean isSuccess() {
            return  code == 0;
        }
    }


    @Data
    class InvalidBeforeResult{
        private int code;
        private String message;
        private  SalesOrderInvalidBeforeModel.Result data;

        public boolean isSuccess() {
            return  code == 0;
        }
    }


    @Data
    class InvalidAfterResult{
        private int code;
        private String message;
        private  SalesOrderInvalidAfterModel.Result data;

        public boolean isSuccess() {
            return  code == 0;
        }
    }


    @Data
    class InvalidFlowCompletedAfterResult{
        private int code;
        private String message;
        private  SalesOrderInvalidFlowCompletedAfterModel.Result data;

        public boolean isSuccess() {
            return  code == 0;
        }
    }


    @Data
    class RecoverBeforeResult{
        private int code;
        private String message;
        private  SalesOrderRecoverBeforeModel.Result data;

        public boolean isSuccess() {
            return  code == 0;
        }
    }


    @Data
    class RecoverAfterResult{
        private int code;
        private String message;
        private  SalesOrderRecoverAfterModel.Result data;

        public boolean isSuccess() {
            return  code == 0;
        }
    }


    @Data
    class BulkInvalidBeforeResult{
        private int code;
        private String message;
        private  BulkInvalidBeforeModel.Result data;

        public boolean isSuccess() {
            return  code == 0;
        }
    }


    @Data
    class BulkInvalidAfterResult{
        private int code;
        private String message;
        private  BulkInvalidAfterModel.Result data;

        public boolean isSuccess() {
            return  code == 0;
        }
    }


    @Data
    class BulkRecoverBeforeResult{
        private int code;
        private String message;
        private  BulkRecoverBeforeModel.Result data;

        public boolean isSuccess() {
            return  code == 0;
        }
    }


    @Data
    class BulkRecoverAfterResult{
        private int code;
        private String message;
        private  BulkRecoverAfterModel.Result data;

        public boolean isSuccess() {
            return  code == 0;
        }
    }


    @Data
    class BulkAddBeforeResult{
        private int code;
        private String message;
        private  SalesOrderBulkAddBeforeModel.Result data;

        public boolean isSuccess() {
            return  code == 0;
        }
    }

    @Data
    class BulkAddAfterResult{
        private int code;
        private String message;
        private  SalesOrderBulkAddAfterModel.Result data;

        public boolean isSuccess() {
            return  code == 0;
        }
    }

    @Data
    class BulkAddTransactionAfterResult{
        private int code;
        private String message;
        private  SalesOrderBulkAddTransactionAfterModel.Result data;

        public boolean isSuccess() {
            return  code == 0;
        }
    }


}
