package com.facishare.crm.sfa.predefine.service.real.salesorder;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.predefine.service.config.BizConfigThreadLocalCacheService;
import com.facishare.crm.sfa.utilities.constant.AvailableConstants;
import com.facishare.crm.sfa.utilities.util.AvailableRangeUtils;
import com.facishare.crm.sfa.utilities.util.SalesOrderUtil;
import com.facishare.crmcommon.util.SearchUtil;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.FilterExt;
import com.facishare.paas.appframework.metadata.dto.DescribeResult;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * Created by zhaiyj
 * date 2019/11/11 10:57 上午
 */
@Component
@Slf4j
public class SalesOrderHistoryProductServiceImpl implements SalesOrderHistoryProductService {

    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    public ObjectDataServiceImpl objectDataService;
    @Autowired
    public AvailableRangeUtils availableRangeUtils;
    @Autowired
    private BizConfigThreadLocalCacheService bizConfigThreadLocalCacheService;


    @Override
    public SearchTemplateQuery dealQueryFilters(SearchTemplateQuery query, String tenantId, String userId) {
        boolean isEnablePriceBook = false;
        String accountId = "";
        String priceBookId = "";
        String querySql = "";

        if (bizConfigThreadLocalCacheService.isPriceBookEnabled(tenantId)) {
            isEnablePriceBook = true;
        }
        List<IFilter> quertFilters = query.getFilters();
        for (IFilter filter : quertFilters) {
            if (Objects.equals(filter.getFieldName(), "order_id")) {
                accountId = filter.getFieldValues().stream().findFirst().get();
            }
            if (Objects.equals(filter.getFieldName(), "price_book_product_id")) {
                priceBookId = filter.getFieldValues().stream().findFirst().get();
            }
        }
        if (bizConfigThreadLocalCacheService.isAvailableRangeEnabled(tenantId)) {
            priceBookId = null;
        }
        Map<String, String> idProductIdMap = Maps.newHashMap();
        try {
            if (isEnablePriceBook) {
                if (StringUtils.isNotEmpty(priceBookId)) {
                    querySql = String.format(SalesOrderUtil.queryIdsWithPriceBookSql, tenantId, tenantId, accountId, priceBookId, tenantId);
                } else {
                    querySql = String.format(SalesOrderUtil.queryIdsWithNoPriceBookSql, tenantId, tenantId, accountId, tenantId);
                }
            } else {
                querySql = String.format(SalesOrderUtil.queryIdsWithNoPriceBookSql, tenantId, tenantId, accountId, tenantId);
            }
            log.info("SalesOrderProductHistoryListController buildSearchTemplateQuery tenantId->{} sql->{}", tenantId, querySql);
            List<Map> salesOrderProducts = objectDataService.findBySql(tenantId, querySql);
            if (CollectionUtils.notEmpty(salesOrderProducts)) {
                salesOrderProducts.forEach(o -> idProductIdMap.put(o.get("id").toString(), o.get("product_id").toString()));
            }
        } catch (MetadataServiceException e) {
            log.error("SalesOrderProductHistoryListController buildSearchTemplateQuery fail tenantId->{},sql->{}", tenantId, querySql, e);
            throw new ValidateException("请求失败。");
        }

        query.getFilters().removeIf(filter -> Objects.equals(filter.getFieldName(), "order_id"));
        query.getFilters().removeIf(filter -> Objects.equals(filter.getFieldName(), "price_book_product_id"));
        query.setWheres(Lists.newArrayList());
        List<IFilter> filters = Lists.newArrayList();
        User user = new User(tenantId, userId);
        List<String> idList = retainAvailableProducts(user, accountId, idProductIdMap);
        if (CollectionUtils.empty(idList)) {
            idList.add("");
        }
        log.warn("SalesOrderHistoryProductServiceImpl dealQueryFilters ids tenantId->{}, size->{}", tenantId, idList.size());
        SearchUtil.fillFilterIn(filters, IObjectData.ID, new ArrayList<>(idList));
        query.setFilters(filters);
        return query;
    }

    @Override
    public SearchTemplateQuery getSearchTemplateQuery(SearchTemplateQuery query, String tenantId, User user) {
        boolean isEnablePriceBook = bizConfigThreadLocalCacheService.isPriceBookEnabled(tenantId);
        String querySql = "";

        List<String> filterValues = getFilterValues(query);
        if (CollectionUtils.empty(query.getWheres())) {
            if (isEnablePriceBook) {
                querySql = String.format(SalesOrderUtil.queryIdsWithPriceBookSql, tenantId, tenantId, filterValues.get(0), filterValues.get(1), tenantId);
            } else {
                querySql = String.format(SalesOrderUtil.queryIdsWithNoPriceBookSql, tenantId, tenantId, filterValues.get(0), tenantId);
            }
        } else {
            String searchQuerySql = getSearchQuerySql(query, tenantId, user);
            if (isEnablePriceBook) {
                querySql = String.format(newQueryIdsWithPriceBookWheresSql, tenantId, searchQuerySql, tenantId, filterValues.get(0), filterValues.get(1), tenantId);
            } else {
                querySql = String.format(newQueryIdsNoPriceBookWheresSql, tenantId, searchQuerySql, tenantId, filterValues.get(0), tenantId);
            }
        }
        return this.searchTemplateQuery(querySql, query, tenantId, user, filterValues.get(0));
    }


    private List<String> getFilterValues(SearchTemplateQuery query) {
        String accountId = "";
        String priceBookId = "";
        List<IFilter> filters = query.getFilters();
        for (IFilter filter : filters) {
            if (Objects.equals(filter.getFieldName(), "order_id")) {
                accountId = filter.getFieldValues().stream().findFirst().get();
            }
            if (Objects.equals(filter.getFieldName(), "price_book_product_id")) {
                priceBookId = filter.getFieldValues().stream().findFirst().get();
            }
        }
        return Lists.newArrayList(accountId, priceBookId);
    }


    private SearchTemplateQuery searchTemplateQuery(String querySql, SearchTemplateQuery query, String tenantId,
                                                    User user, String accountId) {
        Map<String, String> idProductIdMap = Maps.newHashMap();
        try {
            List<Map> salesOrderProducts = objectDataService.findBySql(tenantId, querySql);
            if (CollectionUtils.notEmpty(salesOrderProducts)) {
                salesOrderProducts.forEach(o -> idProductIdMap.put(o.get("id").toString(), o.get("product_id").toString()));
            }
        } catch (MetadataServiceException e) {
            log.error("SalesOrderProductHistoryListController buildSearchTemplateQuery fail tenantId->{},sql->{}", tenantId, querySql, e);
            throw new ValidateException("请求失败。");
        }

        query.getFilters().removeIf(filter -> {
            if (Objects.equals(filter.getFieldName(), "order_id")
                    || Objects.equals(filter.getFieldName(), "price_book_product_id")) {
                return true;
            }
            return false;
        });
        List<IFilter> filters = Lists.newArrayList();
        List<String> idList = retainAvailableProducts(user, accountId, idProductIdMap);
        if (CollectionUtils.empty(idList)) {
            idList.add("");
        }
        log.warn("SalesOrderHistoryProductServiceImpl dealQueryFilters ids tenantId->{}, size->{}", tenantId, idList.size());
        SearchUtil.fillFilterIn(filters, IObjectData.ID, new ArrayList<>(idList));
        query.setFilters(filters);
        query.setWheres(Lists.newArrayList());
        return query;
    }

    private List<String> retainAvailableProducts(User user, String accountId, Map<String, String> idProductIdMap) {
        List<String> idList = Lists.newArrayList();
        if (CollectionUtils.empty(idProductIdMap)) {
            return idList;
        }
        Set<String> availableProductList = availableRangeUtils.getAvailableProductList(user, accountId);
        if (availableProductList.contains(AvailableConstants.PublicConstants.RANGE_ALL)) {
            idList.addAll(idProductIdMap.keySet());
            return idList;
        }
        for (Map.Entry<String, String> entry : idProductIdMap.entrySet()) {
            if (availableProductList.contains(entry.getValue())) {
                idList.add(entry.getKey());
            }
        }
        return idList;
    }

    private List<String> getProductWithWheres(SearchTemplateQuery query, String tenantId, User user) {
        List<String> productIds = Lists.newArrayList();
        int limit = 1000;
        int offset = 0;
        Integer totalNumber = 0;
        while (true) {
            List<Wheres> wheres = query.getWheres();
            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            searchTemplateQuery.setWheres(wheres);
            searchTemplateQuery.setLimit(limit);
            searchTemplateQuery.setOffset(offset);
            searchTemplateQuery.setPermissionType(1);
            if (totalNumber == 0) {
                searchTemplateQuery.setNeedReturnCountNum(true);
            } else {
                searchTemplateQuery.setNeedReturnCountNum(false);
            }
            List<OrderBy> orders = new ArrayList<>();
            orders.add(new OrderBy(IObjectData.ID, Boolean.FALSE));
            searchTemplateQuery.setOrders(orders);

            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, Utils.PRODUCT_API_NAME, searchTemplateQuery);
            queryResult.getData().forEach(o -> productIds.add(o.getId()));
            if (totalNumber == 0) {
                totalNumber = queryResult.getTotalNumber();
            }
            if (offset < totalNumber) {
                offset += limit;
            } else {
                break;
            }
        }
        return productIds;
    }

    private String getSearchQuerySql(SearchTemplateQuery query, String tenantId, User user) {
        removeFiltersByValueType(query.getWheres());
        String newSql = "select ProductObj.id from ";
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setWheres(query.getWheres());
        searchTemplateQuery.setLimit(0);
        searchTemplateQuery.setOffset(0);
        DescribeResult describeAndLayout = serviceFacade.findDescribeAndLayout(user, Utils.PRODUCT_API_NAME, false, null);
        IActionContext context = ActionContextExt.of(user).getContext();
        try {
            String sql = objectDataService.getSqlBySearchQuery(tenantId, describeAndLayout.getObjectDescribe(), searchTemplateQuery, true, context);
            newSql = newSql + sql.split("from")[1];
        } catch (MetadataServiceException e) {
            log.warn("getSearchQuerySql sql, tenantId->{} userId->{}", tenantId, user.getUserId(), e);
        }
        return newSql;
    }

    /**
     * 不支持三角关系筛选
     * @param wheres
     */
    private void removeFiltersByValueType(List<Wheres> wheres) {
        Iterator<Wheres> iterator = wheres.iterator();
        while (iterator.hasNext()) {
            Wheres where = iterator.next();
            List<IFilter> filters = where.getFilters();
            if (CollectionUtils.empty(filters)) {
                continue;
            }
            filters.removeIf(filter -> filter != null && (FilterExt.of(filter).hasRefObjectVariableValueType() ||
                    FilterExt.of(filter).hasRelatedChainObjectVariableValueType()));
            if (CollectionUtils.empty(filters)) {
                iterator.remove();
            } else {
                where.setFilters(filters);
            }
        }
    }


    private String newQueryIdsWithPriceBookWheresSql = "SELECT *\n" +
            "FROM (SELECT ROW_NUMBER() OVER (\n" +
            "  PARTITION BY order_product.product_id\n" +
            "  ORDER BY\n" +
            "    order_product.last_modified_time DESC\n" +
            "  ) AS s_rank, order_product.create_time, order_product.ID, order_product.product_id\n" +
            "      FROM (SELECT *\n" +
            "            FROM biz_sales_order_product\n" +
            "            WHERE tenant_id = '%s'\n" +
            "              AND is_deleted = 0\n" +
            "              and product_id in (%s)\n" +
            "              AND order_id IN (SELECT ID\n" +
            "                               FROM biz_sales_order\n" +
            "                               WHERE tenant_id = '%s'\n" +
            "                                 AND account_id = '%s'\n" +
            "                                 AND price_book_id = '%s'\n" +
            "                                 AND is_deleted = 0)) AS order_product\n" +
            "      where order_product.tenant_id = '%s'\n" +
            "      AND order_product.parent_prod_pkg_key is NULL) AS TEMP\n" +
            "WHERE s_rank = 1;";


    private String newQueryIdsNoPriceBookWheresSql = "SELECT *\n" +
            "FROM (SELECT ROW_NUMBER() OVER (\n" +
            "  PARTITION BY order_product.product_id\n" +
            "  ORDER BY\n" +
            "    order_product.last_modified_time DESC\n" +
            "  ) AS s_rank, order_product.create_time, order_product.ID, order_product.product_id\n" +
            "      FROM (SELECT *\n" +
            "            FROM biz_sales_order_product\n" +
            "            WHERE tenant_id = '%s'\n" +
            "              AND is_deleted = 0\n" +
            "              and product_id in (%s)\n" +
            "              AND order_id IN (SELECT ID\n" +
            "                               FROM biz_sales_order\n" +
            "                               WHERE tenant_id = '%s'\n" +
            "                                 AND account_id = '%s'\n" +
            "                                 AND is_deleted = 0)) AS order_product\n" +
            "      where order_product.tenant_id = '%s'\n" +
            "      AND order_product.parent_prod_pkg_key is NULL) AS TEMP\n" +
            "WHERE s_rank = 1;";

}
