package com.facishare.crm.sfa.utilities.proxy.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * Create by baoxinxue
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StagesWithProbability {
    private  String stageId;
    private Map<String,String> extension;
    private  String name;

}
