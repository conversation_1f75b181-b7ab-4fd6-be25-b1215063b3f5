<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:c="http://www.springframework.org/schema/c" xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-4.1.xsd
       http://code.alibabatech.com/schema/dubbo
       http://code.alibabatech.com/schema/dubbo/dubbo.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <import resource="classpath:spring/metadata.xml"/>
    <import resource="classpath:spring/common.xml"/>
    <import resource="classpath:spring/log.xml"/>
    <import resource="classpath:spring/flow.xml"/>
    <import resource="classpath:spring/privilege.xml"/>
    <import resource="classpath:spring/restdriver.xml"/>
    <import resource="classpath:spring/licence.xml"/>
    <import resource="classpath:spring/fsi.xml"/>
<!--    <import resource="classpath:spring/core-dubbo.xml"/>-->
    <import resource="classpath:spring/dubbo.xml"/>
    <import resource="classpath:spring/fs-crm-sales.xml"/>
    <import resource="classpath:spring/payment.xml"/>
    <import resource="classpath:spring/stock-spring.xml"/>
    <import resource="classpath*:spring/ei-ea-converter.xml"/>
    <import resource="classpath*:spring/function-service.xml"/>
    <import resource="classpath*:/META-INF/spring/pod-api-client.xml"/>


    <bean id="datastore" class="com.github.mongo.support.MongoDataStoreFactoryBean" p:configName="checkin-v2-mongo"/>
    <bean id="nomonProducer" class="com.fxiaoke.paas.gnomon.api.NomonProducer"/>

    <import resource="classpath:/fs-paas-bizconf-client.xml"/>
    <context:component-scan base-package="com.facishare.paas.appframework com.facishare.crm com.fxiaoke.metadata"/>
    <context:annotation-config/>
    <bean class="com.fxiaoke.metadata.option.api.OptionApiClient"/>
    <bean id="optionRedisCache" class="com.github.jedis.support.JedisFactoryBean"
          p:configName="fs-metadata-option-redis"/>
    <bean id="optionService" class="com.facishare.restful.client.FRestApiProxyFactoryBean">
        <property name="type" value="com.fxiaoke.metadata.option.api.OptionService"/>
    </bean>

    <bean id="autoConf"
          class="com.github.autoconf.spring.reloadable.ReloadablePropertySourcesPlaceholderConfigurer"
          p:fileEncoding="UTF-8"
          p:ignoreResourceNotFound="true"
          p:ignoreUnresolvablePlaceholders="false"
          p:location="classpath:application.properties"
          p:configName="dubbo-common,fs-paas-metadata-mongo,fs-paas-appframework-rest,fs-crm-java-config
            ,fs-crm-printconfig,fs-metadata,fs-crm-icon-path,fs-crm-java-detailpage-layout-setting"/>

    <bean class="com.github.autoconf.spring.reloadable.ReloadablePropertyPostProcessor"
          c:placeholderConfigurer-ref="autoConf"/>
    <bean id="expressionService" class="com.facishare.paas.expression.ExpressionServiceImpl"/>

    <!--临时-->
    <bean class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.TestProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <!--privilege temp-->
    <import resource="classpath:privilege-temp.xml"/>
    <bean class="com.github.autoconf.spring.reloadable.ReloadablePropertyPostProcessor"
          c:placeholderConfigurer-ref="autoConf"/>

    <!--临时-->
    <bean class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.TestProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean class="com.facishare.fcp.service.FcpServiceBeanPostProcessor"
          id="fcpServiceBeanPostProcessor"/>

    <bean class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.rest.CrmRestApi">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.rest.ApprovalInitProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.rest.TemplateApi">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.rest.SendCrmMessageProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.GetHomePermissionsProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.CustomerAccountProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.SailAdminProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>


    <bean class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.rest.FunctionProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.rest.EsSearchProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.rest.DataRightsProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.LeadsObjTransferProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.NewOpportunityProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.QiXinProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean id="leadsObjPoolProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.LeadsObjPoolProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.InitObjectsPermissionsAndLayoutProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean id="highSeasProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.HighSeasProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean id="passDataPrivilegeProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.PassDataPrivilegeProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean id="orgServiceProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean">
        <property name="factory" ref="restServiceProxyFactory"/>
        <property name="type" value="com.facishare.paas.appframework.common.service.OrgServiceProxy"/>
    </bean>
    <bean id="salesEventProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.SalesEventProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean id="salesOrderProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.SalesOrderProxy">
        <property name="factory" ref="restServiceProxyFactory" />
    </bean>
    <bean id="saleActionProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.SaleActionProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean id="geoLocationProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.GeoLocationProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean id="sfaDuplicateSearchProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.SFADuplicateSearchProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean id="refundProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.RefundProxy">
        <property name="factory" ref="restServiceProxyFactory" />
    </bean>

    <bean id="dataCleanProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.DataCleanProxy">
        <property name="factory" ref="restServiceProxyFactory" />
    </bean>

    <bean id="crmRemindRecordProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.CRMRemindRecordProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean id="sfaRecyclingService" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.SFARecyclingProxy">
        <property name="factory" ref="restServiceProxyFactory" />
    </bean>
    <bean id="stockProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.StockProxy">
        <property name="factory" ref="restServiceProxyFactory" />
    </bean>

    <bean id="returnedGoodsInvoiceProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.ReturnedGoodsInvoiceProxy">
        <property name="factory" ref="restServiceProxyFactory" />
    </bean>

    <bean id="salesOrderBizProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.SalesOrderBizProxy">
        <property name="factory" ref="restServiceProxyFactory" />
    </bean>

    <bean id="coordinationProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.CoordinationProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>


    <bean id="connectionService" class="com.fxiaoke.transfer.service.ConnectionService">
        <property name="biz" value="metadata-transfer"/>
    </bean>

    <bean id="dbOperationService" class="com.facishare.restful.client.FRestApiProxyFactoryBean">
        <property name="type" value="com.fxiaoke.transfer.api.DbOperationService"/>
    </bean>
    <bean id="settingRestApi" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.rest.SettingRestApi">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <import resource="classpath*:/META-INF/spring/pod-api-client.xml"/>
    <bean class="com.fxiaoke.transfer.service.TableSchemeService">
        <property name="configName" value="db-transfer-scheme"/>
    </bean>
    <bean id="deliveryNoteOKHttpSupport" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean" p:configName="fs-crm-delivery-note-http-client-conf"/>

    <bean class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.CompanyLyricalProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.IndustryEnterInfoProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.FeedsProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.PaasWorkFlowProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.PaasLogServiceProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.deliverynote.predefine.rest.SalesOrderInnerRestProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean id="objectFieldReferenceProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.ObjectFieldReferenceProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean id="mergeJobProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.MergeJobProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean id="cRMRestV2ServiceProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.CRMRestV2ServiceProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean id="approvalProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.ApprovalProxy">
        <property name="factory" ref="restServiceProxyFactory" />
    </bean>
    <bean id="webPageProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.WebPageProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.erpstock.util.ErpStockInnerRestProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.sfa.utilities.proxy.PromotionProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <dubbo:reference interface="com.facishare.uc.api.service.EnterpriseEditionService"
                     id="enterpriseEditionService" protocol="dubbo"/>

</beans>
