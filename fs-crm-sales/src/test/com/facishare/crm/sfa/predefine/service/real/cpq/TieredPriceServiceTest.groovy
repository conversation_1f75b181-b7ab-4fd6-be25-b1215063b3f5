package com.facishare.crm.sfa.predefine.service.real.cpq

import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.metadata.impl.ObjectData
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

@ContextConfiguration(value = "classpath:cpq-price.xml")
class TieredPriceServiceTest extends Specification {

    @Autowired
    TieredPriceService tieredPriceService


    def "分层定价"() {
        given:

        Map<String, Object> priceBookMap = Maps.newHashMap();
        priceBookMap.put("tiered_type", "1")
        priceBookMap.put("price_type", "1")
        def tieredPriceBook = new ObjectData(priceBookMap)


        //(1,10}
        Map<String, Object> rule1Map = Maps.newHashMap();
        rule1Map.put("start_count", "1")
        rule1Map.put("end_count", "10")
        rule1Map.put("price", "5")

        def rule1 = new ObjectData(rule1Map)

        //(20,30]
        Map<String, Object> rule2Map = Maps.newHashMap();
        rule2Map.put("start_count", "20")
        rule2Map.put("end_count", "30")
        rule2Map.put("price", "10")
        def rule2 = new ObjectData(rule2Map)

        def rules = Lists.newArrayList();
        rules.add(rule1)
        rules.add(rule2)
        when:
        def price = tieredPriceService.price(count, tieredPriceBook, rules, new BigDecimal("30"))

        then:
        price == myPrice
        where:
        count || myPrice
        1     || 30 //没找到规则,使用原价
        5     || 25 //(1,10]的定价规则
        10    || 50 //(1,10]的定价规则
        20    || 600 //没找到规则,使用原价
        25    || 250 //(20,30]定价规则
        40    || 1200 //没找到规则,使用原价
    }


    def "分层折扣"() {
        given:

        Map<String, Object> priceBookMap = Maps.newHashMap();
        priceBookMap.put("tiered_type", "3")
        priceBookMap.put("price_type", "2")
        def tieredPriceBook = new ObjectData(priceBookMap)


        //(1,10}
        Map<String, Object> rule1Map = Maps.newHashMap();
        rule1Map.put("start_count", "1")
        rule1Map.put("end_count", "10")
        rule1Map.put("discount", "50")

        def rule1 = new ObjectData(rule1Map)

        //(20,30]
        Map<String, Object> rule2Map = Maps.newHashMap();
        rule2Map.put("start_count", "20")
        rule2Map.put("end_count", "30")
        rule2Map.put("discount", "10")
        def rule2 = new ObjectData(rule2Map)

        def rules = Lists.newArrayList();
        rules.add(rule1)
        rules.add(rule2)
        when:
        def price = tieredPriceService.price(count, tieredPriceBook, rules, new BigDecimal("100"))

        then:
        price == myPrice
        where:
        count || myPrice
        1     || 100 //没找到规则,使用原价
        5     || 250 //(1,10]的定价规则
        10    || 500 //(1,10]的定价规则
        20    || 2000 //没找到规则,使用原价
        25    || 250 //(20,30]定价规则
        40    || 4000 //没找到规则,使用原价
    }

    def "分层规格异常"() {
        given:

        Map<String, Object> priceBookMap = Maps.newHashMap();
        priceBookMap.put("tiered_type", "3")
        priceBookMap.put("price_type", "3")
        def tieredPriceBook = new ObjectData(priceBookMap)


        //(1,10}
        Map<String, Object> rule1Map = Maps.newHashMap();
        rule1Map.put("start_count", "1")
        rule1Map.put("end_count", "10")
        rule1Map.put("discount", "50")

        def rule1 = new ObjectData(rule1Map)

        //(20,30]
        Map<String, Object> rule2Map = Maps.newHashMap();
        rule2Map.put("start_count", "20")
        rule2Map.put("end_count", "30")
        rule2Map.put("discount", "10")
        def rule2 = new ObjectData(rule2Map)

        def rules = Lists.newArrayList();
        rules.add(rule1)
        rules.add(rule2)
        when:
        tieredPriceService.price(1, tieredPriceBook, rules, new BigDecimal("100"))

        then:
        thrown(ValidateException)
    }

    def "阶梯定价单价一口价"() {
        given:

        Map<String, Object> priceBookMap = Maps.newHashMap();
        priceBookMap.put("tiered_type", "2")
        priceBookMap.put("price_type", "1")
        def tieredPriceBook = new ObjectData(priceBookMap)


        // rule price_type //1-单价，2-打包价

        //(1,10]
        Map<String, Object> rule1Map = Maps.newHashMap();
        rule1Map.put("start_count", "1")
        rule1Map.put("end_count", "10")
        rule1Map.put("price_type", "1") //单价
        rule1Map.put("price", "5")

        def rule1 = new ObjectData(rule1Map)

        //(20,30]
        Map<String, Object> rule2Map = Maps.newHashMap();
        rule2Map.put("start_count", "20")
        rule2Map.put("end_count", "30")
        rule2Map.put("price_type", "2") //打包价
        rule2Map.put("price", "10")
        def rule2 = new ObjectData(rule2Map)

        def rules = Lists.newArrayList();
        rules.add(rule1)
        rules.add(rule2)
        when:
        def price = tieredPriceService.price(count, tieredPriceBook, rules, new BigDecimal("30"))

        then:
        price == myPrice
        where:
        count || myPrice
        1     || 30 //没找到规则,使用原价
        5     || 50 //(1,10]的定价规则
        10    || 75 //(1,10]的定价规则
        20    || 375 //没找到规则,使用原价
        25    || 385 //(20,30]定价规则
        40    || 685 //没找到规则,使用原价
    }

    def "阶梯折扣单价"() {
        given:

        Map<String, Object> priceBookMap = Maps.newHashMap();
        priceBookMap.put("tiered_type", "4")
        priceBookMap.put("price_type", "2")
        def tieredPriceBook = new ObjectData(priceBookMap)


        // rule price_type //1-单价，2-打包价

        //(1,10]
        Map<String, Object> rule1Map = Maps.newHashMap();
        rule1Map.put("start_count", "1")
        rule1Map.put("end_count", "10")
        rule1Map.put("price_type", "1") //单价
        rule1Map.put("discount", "50")

        def rule1 = new ObjectData(rule1Map)

        //(20,30]
        Map<String, Object> rule2Map = Maps.newHashMap();
        rule2Map.put("start_count", "20")
        rule2Map.put("end_count", "30")
        rule2Map.put("price_type", "1") //打包价
        rule2Map.put("discount", "40")
        def rule2 = new ObjectData(rule2Map)

        def rules = Lists.newArrayList();
        rules.add(rule1)
        rules.add(rule2)
        when:
        def price = tieredPriceService.price(count, tieredPriceBook, rules, new BigDecimal("100"))

        then:
        price == myPrice
        where:
        count || myPrice
        1     || 100 //没找到规则,使用原价
        5     || 300 //(1,10]的定价规则
        10    || 550 //(1,10]的定价规则
        20    || 1550 //没找到规则,使用原价
        25    || 1750 //(20,30]定价规则
        40    || 2950 //没找到规则,使用原价
    }


    def "阶梯折扣类型不对"() {
        given:

        Map<String, Object> priceBookMap = Maps.newHashMap();
        priceBookMap.put("tiered_type", "4")
        priceBookMap.put("price_type", "2")
        def tieredPriceBook = new ObjectData(priceBookMap)


        // rule price_type //1-单价，2-打包价

        //(1,10]
        Map<String, Object> rule1Map = Maps.newHashMap();
        rule1Map.put("start_count", "1")
        rule1Map.put("end_count", "10")
        rule1Map.put("price_type", "2") //单价
        rule1Map.put("discount", "50")

        def rule1 = new ObjectData(rule1Map)

        //(20,30]
        Map<String, Object> rule2Map = Maps.newHashMap();
        rule2Map.put("start_count", "20")
        rule2Map.put("end_count", "30")
        rule2Map.put("price_type", "1") //打包价
        rule2Map.put("discount", "40")
        def rule2 = new ObjectData(rule2Map)

        def rules = Lists.newArrayList();
        rules.add(rule1)
        rules.add(rule2)
        when:
        tieredPriceService.price(3, tieredPriceBook, rules, new BigDecimal("100"))

        then:
        thrown(ValidateException)
    }


}
