package com.facishare.crm.sfa.predefine.service.cpq

import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.RequestContextManager
import com.facishare.paas.appframework.core.model.ServiceContext
import com.facishare.paas.appframework.core.model.User
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

/**
 * <AUTHOR> @date 2020/7/14 6:43 下午
 * @illustration
 */
@ContextConfiguration(value = "classpath:sales_order_applicationContext.xml")
class BaseSpock extends Specification{

    static {
        System.setProperty("spring.profiles.active", "fstest")
    }

    def userId = "-10000"

    def setContext(String tenantId){
        RequestContext context =  RequestContext.builder()
                .tenantId(tenantId)
                .user(Optional.of(new User(tenantId, userId)))
                .build()

        RequestContextManager.setContext(context)
    }

    def getContext(String tenantId, userId) {
        RequestContext requestContext = RequestContext.builder()
                .tenantId(tenantId)
                .user(java.util.Optional.of(new User(tenantId, userId))).build();
        ServiceContext serviceContext = new ServiceContext(requestContext, null, null);
        return serviceContext
    }

}