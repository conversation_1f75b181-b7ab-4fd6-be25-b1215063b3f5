package groovy.com.facishare.crm.util

import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.ServiceContext
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.Lang
import spock.lang.Specification

class ServiceGenerator extends Specification {

    def getContext(tenantId, userId) {
        ServiceContext context = Mock(ServiceContext)
        context.getTenantId() >> tenantId
        def user = Mock(User)
        context.getUser() >> user
        user.getUserId() >> userId
        user.getTenantId() >> tenantId

        def requestContext = Mock(RequestContext)
        requestContext.getTenantId() >> tenantId
        requestContext.getUser() >> user
        requestContext.getAppId() >> "CRM"
        context.getRequestContext() >> requestContext
        requestContext.getLang() >> Lang.zh_CN
        return context
    }


    def "getContextTest"() {
        given:
        def context = getContext("71568", myUserId)
        when:
        def userId = context.getUser().getUserId()
        then:
        isTr == (userId == myUserId)
        where:
        myUserId || isTr
        "1"      || true
    }
}
