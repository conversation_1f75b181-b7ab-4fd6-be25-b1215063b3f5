import com.facishare.crm.sfa.BaseTest
import com.facishare.crm.sfa.predefine.service.model.DataCleanCreateModel
import com.facishare.crm.sfa.predefine.service.model.DataCleanInfoFieldModel
import com.facishare.crm.sfa.predefine.service.model.DataCleanModel
import com.facishare.crm.sfa.predefine.service.model.LastestDataCleanInfoModel
import org.springframework.beans.factory.annotation.Autowired
import com.facishare.crm.sfa.predefine.service.DataCleanService;
import com.facishare.paas.appframework.core.model.ServiceContext;
import spock.lang.Shared
import spock.lang.Specification
import spock.lang.Title

@Title("数据清洗相关单元测试")
class DataCleanServiceTest extends BaseTest{
    @Autowired
    DataCleanService dataCleanService;

    @Shared
    ServiceContext serviceContext;

    def setup(){
        def tenant_id = '74745'
        def user_id = '1033'
        serviceContext = getServiceContext(tenant_id,user_id)
    }

    def "getDataCleanInfo"(){
        given:
        def arg = getDataClenInfoArg("23739c96912c439187fcbd89844aee06")
        when:
        def result = dataCleanService.getDataCleanInfo(arg)
        then:
        result.dataList.count() > 0
    }
    def "getDataCleanInfoField"(){
        given:
        def arg = getDataCleanInfoFieldArg("AccountObj")
        when:
        def result = dataCleanService.getDataCleanInfoField(serviceContext, arg)
        then:
        result.getDuplicateSearchFields() != null
    }
    def "create"(){
        given:
        def arg = getCreateArg("AccountObj")
        when:
        def result = dataCleanService.data_clean_create(serviceContext, arg)
        then:
        result.getData_clean_id() != ""
    }
    def "status"(){

    }
    def "lastestInfos"(){
        given:
        def arg = getLastestDataCleanInfoArg()
        when:
        def result = dataCleanService.lastest_tata_clean_infos(serviceContext,arg)
        then:
        result.getDataCleanInfos() != null
    }

    def getCreateArg(String objectName){
        def arg = Mock(DataCleanCreateModel.Arg)
        arg.getObjectApiName() >> objectName
        return arg
    }

    def getDataCleanInfoFieldArg(String objectName){
        def arg = Mock(DataCleanInfoFieldModel.Arg)
        arg.getObjectApiName() >> objectName
        return arg
    }

    def getLastestDataCleanInfoArg(){
        def arg = Mock(LastestDataCleanInfoModel.Arg)
        return arg
    }

    def getDataClenInfoArg(String dataCleanId){
        def arg = Mock(DataCleanModel.Arg)
        arg.getDataCleanID() >> dataCleanId
        return arg
    }
}