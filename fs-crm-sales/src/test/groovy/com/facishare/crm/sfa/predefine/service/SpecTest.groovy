package com.facishare.crm.sfa.predefine.service

import com.facishare.crm.sfa.predefine.service.model.CheckDeleteCategoryModel
import com.facishare.paas.appframework.core.model.ServiceContext
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.QueryResult
import com.facishare.paas.metadata.exception.ErrorCode
import com.facishare.paas.metadata.exception.MetadataServiceException
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl
import spock.lang.Specification

//@ContextConfiguration(value = "classpath:applicationContext-test.xml")
class SpecTest extends Specification {
    static {
        System.setProperty("spring.profiles.active", "fstest")
    }

//    @Autowired
//    SpuSkuService spuSkuService

    def "specTest"() {
        given:
        def result = new CheckDeleteCategoryModel.Result()
        def context = getContext(tenantId, userId)
        CheckDeleteCategoryModel.Arg arg = new CheckDeleteCategoryModel.Arg(categoryCodes)
        SpuSkuService spuSkuService = new SpuSkuService()
        ObjectDataServiceImpl objectDataService = Mock()

        def queryResult = new QueryResult<IObjectData>()
        queryResult.setData(data)
        objectDataService.findBySql(_, "74735", _) >> queryResult
        objectDataService.findBySql(sql, '74735', _) >> queryResult
        objectDataService.findBySql(_, "747351", _) >> {
            throw new MetadataServiceException(ErrorCode.DESCRIBE_NOT_EXIST, "")
        }

        spuSkuService.objectDataService = objectDataService
        when:
        try {
            result = spuSkuService.checkDeleteCategory(arg, context)
        } catch (Exception e) {
            if (tenantId.equals("747351")) result.setResult(Boolean.TRUE)
        }
        then:
        result.getResult() == result1
        where:
        tenantId | userId | categoryCodes | data  | sql | result1
        "74735"  | "1000" | ["1"]         | ["1"] | ""  | false
        "74735"  | "1000" | ["1"]         | []    | ""  | true
        "74735"  | "1000" | []            | ["1"] | ""  | false
        "747351" | "1000" | ["1"]         | ["1"] | ""  | true

    }


    def getContext(tenantId, userId) {
        ServiceContext context = Mock(ServiceContext)
        context.getTenantId() >> tenantId
        def user = Mock(User)
        context.getUser() >> user
        user.getUserId() >> userId
        user.getTenantId() >> tenantId
        return context
    }
}
