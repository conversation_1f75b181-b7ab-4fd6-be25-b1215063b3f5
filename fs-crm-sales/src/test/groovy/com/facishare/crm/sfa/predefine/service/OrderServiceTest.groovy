package com.facishare.crm.sfa.predefine.service

import com.facishare.crm.sfa.predefine.service.model.InvoiceApplicationAndSalesOrder
import com.facishare.crm.sfa.predefine.service.model.UpdateDeliveryData
import com.facishare.crm.sfa.predefine.service.model.UpdateLogisticsStatus
import com.facishare.paas.appframework.core.model.ServiceContext
import com.facishare.paas.appframework.core.model.User
import com.google.common.collect.Lists
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

/**
 * Created by zhaiyj
 * date 2019/10/24 11:11 上午
 */
@ContextConfiguration(value = "classpath:applicationContext.xml")
class OrderServiceTest extends Specification {

    @Autowired
    private OrderService orderService;

    def "UpdateLogisticsStatus"() {
        given:
        def coutext = getContext(tenantid, userId);
        UpdateLogisticsStatus.Arg arg = new UpdateLogisticsStatus.Arg();
        arg.setDeliveryDate(System.currentTimeMillis());
        arg.setLogisticsStatus("3");
        arg.setReceiveDate(System.currentTimeMillis() + 1000);
        arg.setSalesOrderId("6f86f15438554196b57f78df41960d73")
        when:
        UpdateLogisticsStatus.Result result = orderService.updateLogisticsStatus(arg, coutext);
        then:
        result.value == true
        where:
        tenantid | userId
        "71570"  | "1000"
    }


    def "UpdateLogisticsStatus-noLogstatus"() {
        given:
        def coutext = getContext(tenantid, userId);
        UpdateLogisticsStatus.Arg arg = new UpdateLogisticsStatus.Arg();
        arg.setDeliveryDate(System.currentTimeMillis());
        arg.setLogisticsStatus("209");
        arg.setReceiveDate(System.currentTimeMillis() + 1000);
        arg.setSalesOrderId("6f86f15438554196b57f78df41960d73")
        when:
        UpdateLogisticsStatus.Result result = orderService.updateOrderDeliveredData(arg, coutext);
        then:
        result.value == true
        where:
        tenantid | userId
        "71570"  | "1000"
    }


    def "updateOrderDeliveredData"() {
        given:
        def coutext = getContext(tenantid, userId);

        UpdateDeliveryData.DetailData data = new UpdateDeliveryData.DetailData();
        data.setDeliveredAmount(new BigDecimal("10"))
        data.setDeliveredCount(new BigDecimal("1"))
        data.setSalesOrderProductId("09465af8ae4c4e258957021c1d0479f4")

        UpdateDeliveryData.Arg arg = new UpdateDeliveryData.Arg();
        arg.setDeliveredAmount(new BigDecimal("10"))
        arg.setDetailData(Lists.newArrayList(data))
        arg.setSalesOrderId("00f91b5b25c34d87bbad20faae026305")
        when:
        UpdateDeliveryData.Result result = orderService.updateOrderDeliveredData(arg, coutext);
        then:
        result.result == true
        where:
        tenantid | userId
        "71570"  | "1000"
    }


    def "getOrderProductData"() {
        given:
        def coutext = getContext(tenantid, userId);
        InvoiceApplicationAndSalesOrder.OrderProductInfo info1 = new InvoiceApplicationAndSalesOrder.OrderProductInfo();
        info1.setOrderId("5d3e6edae558e200019ab8cd");
        info1.setOrderProductIds(Lists.newArrayList("5d3e6edae558e200019ab960"));

        InvoiceApplicationAndSalesOrder.Arg arg = new InvoiceApplicationAndSalesOrder.Arg();
        arg.setOrderIds(Lists.newArrayList("5d391b0d29f6b5000126905d",
                "5d3e6edae558e200019ab8cd",
                "5d789a5bf8917a0b8856059c",
                "5d789c65f8917a0b88560638",
                "5d78a28af8917a11c8b951ef",
                "5d901d669af955000167e533"));
        arg.setOrderProductIds(Lists.newArrayList(info1));
        when:
        InvoiceApplicationAndSalesOrder.Result result = orderService.getOrderProductData(arg, coutext);
        then:
        result.objectData.size() > 0
        where:
        tenantid | userId
        "71570"  | "1000"
    }


    def "sumNoInvoiceAmount"() {
        given:
        def coutext = getContext(tenantid, userId);
        InvoiceApplicationAndSalesOrder.CountNoInvoiceAmountArg arg = new InvoiceApplicationAndSalesOrder.CountNoInvoiceAmountArg();
        arg.setAccountId(accountId)
        when:
        InvoiceApplicationAndSalesOrder.CountNoInvoiceAmountResult result = orderService.sumSalesOrderNoInvoiceAmount(arg, coutext);
        then:
        result.sumAmount != 0.0
        where:
        tenantid | userId | accountId
        "71570"  | "1000" | "5d65f9da790a660001079fb3"
    }


    def getContext(tenantId, userId) {
        ServiceContext context = Mock(ServiceContext)
        context.getTenantId() >> tenantId
        def user = Mock(User)
        context.getUser() >> user
        user.getUserId() >> userId
        user.getTenantId() >> tenantId
        return context
    }

}
