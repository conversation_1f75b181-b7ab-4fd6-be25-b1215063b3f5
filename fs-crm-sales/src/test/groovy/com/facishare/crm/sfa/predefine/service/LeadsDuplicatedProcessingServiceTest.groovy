import spock.lang.Title

//@Title("线索重复数据识别相关单元测试")
//class DataCleanServiceTest extends BaseTest {
////    @Autowired
////    LeadsDuplicatedProcessingService processingService;
////
////    @Shared
////    ServiceContext serviceContext;
////
////    def setup(){
////        def tenant_id = '74745'
////        def user_id = '1033'
////        serviceContext = getServiceContext(tenant_id,user_id)
////    }
////
////    def "save_object_field_reference_test"(){
////
////    }
//
//}