package com.facishare.crm.sfa.predefine.service.cpq

import com.alibaba.fastjson.JSONObject
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.ObjectDataDocument
import com.google.common.collect.Lists
import spock.lang.Specification

/**
 * <AUTHOR> 2019-12-10
 * @instruction
 */
class ProductConstraintServiceImplTest extends Specification {
    def "CheckScope_OK"() {
        given:
        def productConstraintService = new ProductConstraintServiceImpl()
        def arg = JSONObject.parseObject(objectDataDocument)
        when:
        productConstraintService.checkScope(Lists.newArrayList(ObjectDataDocument.of(arg)))
        then:
        noExceptionThrown()
        where:
        objectDataDocument                                              | correctResult
        "{ \"constraint_scope\" : \"1\" , \"product_pkg_id\" : \"1\" }" | true
        "{ \"constraint_scope\" : null , \"product_pkg_id\" : \"\" }"   | true
        "{ \"constraint_scope\" : \"2\" , \"product_pkg_id\" : \"\" }"  | true
    }

    def "CheckScope_error"() {
        given:
        def productConstraintService = new ProductConstraintServiceImpl()
        def arg = JSONObject.parseObject(objectDataDocument)
        when:
        productConstraintService.checkScope(Lists.newArrayList(ObjectDataDocument.of(arg)))
        then:
        def e = thrown(ValidateException)
        e.message == msg
        where:
        objectDataDocument                                              | msg
        "{ \"constraint_scope\" : \"1\" , \"product_pkg_id\" : null }"  | "约束范围中的单选“产品组合“和引用字段“产品组合“是绑定关系。"
        "{ \"constraint_scope\" : null , \"product_pkg_id\" : \"1\" }"  | "约束范围中的单选“产品组合“和引用字段“产品组合“是绑定关系。"
        "{ \"constraint_scope\" : \"2\" , \"product_pkg_id\" : \"2\" }" | "约束范围中的单选“产品组合“和引用字段“产品组合“是绑定关系。"
    }
}
