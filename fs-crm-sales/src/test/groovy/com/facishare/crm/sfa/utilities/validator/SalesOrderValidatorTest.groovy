package com.facishare.crm.sfa.utilities.validator

import com.alibaba.fastjson.JSON
import com.facishare.crm.openapi.Utils
import com.facishare.paas.appframework.core.model.ObjectDataDocument
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

/**
 * Created by zhaiyj
 * date 2019/10/29 5:18 下午
 */
@ContextConfiguration(value = "classpath:applicationContext.xml")
class SalesOrderValidatorTest extends Specification {


    def "GetProductNewPrice"() {
        given:
        def str = "{\"lock_rule\":null,\"UDSText5__c\":null,\"UDRef4__c\":\"个\",\"discount\":\"100.0000\",\"UDCal2__c\":\"12.00\",\"is_multiple_unit\":\"否\",\"UDAttach1__c\":[],\"UDMoney1__c\":null,\"life_status_before_invalid\":null,\"product_id\":\"5c6a7c89a5083d5444ecfed1\",\"owner_department_id\":\"1001\",\"UDLookUp7__c\":null,\"UDSText2__c\":null,\"UDRef4__c__r\":\"个\",\"UDAgg1__c\":null,\"UDRef4__c__v\":\"1\",\"stat_unit_count\":null,\"UDMail1__c\":null,\"UDLookUp1__c__r\":\"1\",\"UDRef7__c\":null,\"field_kW01n__c\":\"22.00\",\"field_8Sy9m__c\":\"22.00\",\"version\":\"12\",\"UDInt3__c\":\"12\",\"subtotal\":\"11.00\",\"UDCal5__c\":\"13.00\",\"UDDate1__c\":null,\"tenant_id\":\"71570\",\"UDAgg4__c\":null,\"UDRef6__c\":\"1.0000\",\"product_id__r\":\"2019有规格[1寸]\",\"UDLookUp9__c\":null,\"product_id__relation_ids\":\"5c6a7c89a5083d5444ecfed1\",\"UDSSel1__c\":null,\"UDLookUp6__c__relation_ids\":\"5c0b6b58a5083d77f3c4b5b6\",\"delivered_count\":\"\",\"UDCal4__c\":null,\"UDDate2__c\":null,\"price_book_product_id__relation_ids\":\"5c6a7c89a5083d5444ecfed171570\",\"UDLookUp1__c\":\"5c02312da5083d79ec12cd2d\",\"UDRef10__c\":\"11.0000\",\"UDRef1__c\":null,\"UDLookUp6__c__r\":\"利客来\",\"last_modified_time\":null,\"UDSText6__c\":\"水电费\",\"field_6792n__c\":null,\"unit__r\":\"个\",\"life_status\":\"normal\",\"actual_unit\":null,\"out_tenant_id\":null,\"order_id__r\":\"234\",\"UDLookUp6__c\":\"5c0b6b58a5083d77f3c4b5b6\",\"UDCal7__c\":\"0.11\",\"UDRef9__c\":\"尺寸:1寸\",\"sales_price\":\"11.00\",\"unit__v\":\"1\",\"UDSText3__c\":null,\"base_unit_count\":null,\"price_book_product_id__r\":\"PBProdCode20190218000056\",\"UDAgg2__c\":null,\"price_book_product_id\":\"5c6a7c89a5083d5444ecfed171570\",\"product_price\":\"11.00\",\"UDCal6__c\":\"11.00\",\"UDImg1__c\":[],\"UDRef8__c\":null,\"UDLookUp3__c\":null,\"owner_department\":\"销售部门\",\"promotion_id__r\":null,\"package\":\"CRM\",\"lock_status\":\"\",\"UDRef3__c\":null,\"is_giveaway\":\"\",\"create_time\":null,\"UDAgg5__c\":null,\"UDCal1__c\":\"11.00\",\"UDCSSel1__c\":null,\"UDAttach2__c\":[],\"created_by\":null,\"UDInt2__c\":null,\"relevant_team\":[],\"UDLookUp1__c__relation_ids\":\"5c02312da5083d79ec12cd2d\",\"unit\":\"个\",\"data_own_department\":[\"1001\"],\"UDMoney2__c\":\"6.00\",\"UDCal9__c\":\"23.00\",\"promotion_id__relation_ids\":\"\",\"field_73Gte__c\":null,\"UDLookUp8__c\":null,\"object_describe_id\":null,\"name\":null,\"UDSText1__c\":\"11.00个\",\"UDSSel2__c\":null,\"UDRef2__c\":null,\"field_shZ4a__c\":null,\"UDTel1__c\":\"1122342342\",\"promotion_id\":\"\",\"remark\":null,\"field_02wqd__c\":null,\"delivery_amount\":\"\",\"field_1nlSG__c\":null,\"lock_user\":null,\"UDInt1__c\":null,\"L1_UDCSSel1__c\":null,\"UDMoney3__c\":\"11.00\",\"is_deleted\":false,\"UDLookUp5__c\":null,\"object_describe_api_name\":\"SalesOrderProductObj\",\"UDCal8__c\":null,\"UDSText4__c\":\"2019-02-19\",\"order_id__relation_ids\":\"89c587add942442eb5c9b7fcfd324f2c\",\"out_owner\":null,\"UDMSel1__c\":null,\"owner\":[\"1000\"],\"quantity\":\"1.00\",\"UDMText1__c\":\"水电费\",\"UDAgg3__c\":null,\"UDRef5__c\":null,\"last_modified_by\":null,\"record_type\":\"default__c\",\"is_multiple_unit__v\":false,\"UDCal3__c\":\"1.00\",\"conversion_ratio\":null,\"UDLookUp2__c\":null,\"UDDate3__c\":null}";
        Map<String,Object> map = JSON.parseObject(str,Map.class);
        ObjectDataDocument doc = ObjectDataDocument.of(map);
        Map<String,List<ObjectDataDocument>> details = Maps.newHashMap();
        details.put(Utils.SALES_ORDER_PRODUCT_API_NAME, Lists.newArrayList(doc))
        when:
        def getProductNewPrice = SalesOrderValidator.getProductNewPrice(details, tenantId);
        then:
        1==1
        where:
        tenantId|userId
        "71570" | "1000"
    }
}
