package com.facishare.crm.sfa.predefine.service

import com.alibaba.fastjson.JSON
import com.facishare.crm.sfa.predefine.service.model.MutipleUnitInfo
import com.facishare.paas.appframework.core.model.ServiceContext
import com.facishare.paas.appframework.core.model.User
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

/**
 * Created by zhaiyj
 * date 2019/10/24 2:57 下午
 */
@ContextConfiguration(value = "classpath:applicationContext.xml")
class MutipleUnitServiceTest extends Specification {

    @Autowired
    private MutipleUnitService mutipleUnitService;


    def "CalcPriceByUnit"() {

        given:
        def context = getContext(tenantid,userid)
        def str = "{\"params\":[{\"count\":1,\"priceBookProductId\":\"\",\"productId\":\"5dad5c91f533190001cc2a65\",\"unitId\":\"5d4a9116a5083dd717e37b50\"},{\"count\":1,\"priceBookProductId\":\"\",\"productId\":\"5dad5c91f533190001cc2a65\",\"unitId\":\"8\"}]}";
        MutipleUnitInfo.CalcUnitPriceArg arg = JSON.parseObject(str,MutipleUnitInfo.CalcUnitPriceArg.class);
        when:
        MutipleUnitInfo.CalculateResult result = mutipleUnitService.calcPriceByUnit(context, arg)
        then:
        result.caclResult.size() == 2
        where:
        tenantid | userid|info
        "71652"  |  "1000"   | ""
    }

    def getContext(tenantId, userId) {
        ServiceContext context = Mock(ServiceContext)
        context.getTenantId() >> tenantId
        def user = Mock(User)
        context.getUser() >> user
        user.getUserId() >> userId
        user.getTenantId() >> tenantId
        return context
    }

}
