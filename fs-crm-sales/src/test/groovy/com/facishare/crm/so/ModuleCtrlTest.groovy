package com.facishare.crm.so


import com.facishare.crm.sfa.predefine.service.model.ConfigCtrlModule
import com.facishare.crm.sfa.predefine.service.modulectrl.CPQModuleInitService
import com.facishare.crm.sfa.predefine.service.modulectrl.MultiUnitModuleInitService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

@ContextConfiguration(value = "classpath:applicationContext.xml")

class ModuleCtrlTest extends Specification {
    @Autowired
    private CPQModuleInitService moduleInitService;

    @Autowired
    private MultiUnitModuleInitService multiUnitModuleInitService;

    def "开启CPQ"() {
        given:
        when:
        ConfigCtrlModule.Result result = moduleInitService.initModule(tenantId,userId)
        then:
        result.success == success
        where:
        tenantId | userId | success
        "78868"  | "1000" | true
    }

    def "关闭CPQ"() {
        given:
        when:
        ConfigCtrlModule.Result result = moduleInitService.closeModule(tenantId,userId)
        then:
        result.success == success
        where:
        tenantId | userId | success
        "78868"  | "1000" | true
    }

    def "补刷CPQ"() {
        given:
        when:
        ConfigCtrlModule.Result result = moduleInitService.initModuleRepair(context, tenantId)
        then:
        result.success == success
        where:
        tenantId | userId | success
        "71651"  | "1000" | true
    }

//
//    def "开启大小单位"() {
//        given:
//        when:
//        ConfigCtrlModule.Result result = moduleInitService.initModule(tenantId,userId)
//        then:
//        result.success == success
//        where:
//        tenantId | userId | success
//        "74625"  | "1000" | true
//    }
//
//    def "关闭大小单位"() {
//        given:
//        when:
//        ConfigCtrlModule.Result result = moduleInitService.closeModule(tenantId,userId)
//        then:
//        result.success == success
//        where:
//        tenantId | userId | success
//        "74625"  | "1000" | true
//    }
//
//    def "补刷大小单位"() {
//        given:
//        when:
//        ConfigCtrlModule.Result result = moduleInitService.initModuleRepair(tenantId)
//        then:
//        result.success == success
//        where:
//        tenantId | userId | success
//        "74625"  | "1000" | true
//    }


}
