package com.facishare.crmcommon.rest.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/11/12
 */
public class MappingRulerModel {
    @Data
    public static class FindArg {
        @JsonProperty("rule_api_name")
        @SerializedName("rule_api_name")
        private String rulerApiName;
    }

    @Data
    public static class FindResult extends MappingRulerResult<FindDataResult> {
    }

    @Data
    public static class FindDataResult {
        private List<Map<String, Object>> ruleList;
    }

    @Data
    public static class CreateArg {
        @JSONField(name="describe_api_name")
        @JsonProperty("describe_api_name")
        @SerializedName("describe_api_name")
        private String describeApiName;

        @JSONField(name="rule_list")
        @JsonProperty("rule_list")
        @SerializedName("rule_list")
        private List<ObjectMappingRuleVO> ruleList;

        @JSONField(name="button")
        @JsonProperty("button")
        @SerializedName("button")
        ButtonVO button;

        @JSONField(name="roles")
        @JsonProperty("roles")
        @SerializedName("roles")
        List<String> roles;
    }

    @Data
    public static class CreateResult extends MappingRulerResult<Map> {

    }
}