package com.facishare.crmcommon.rest;

import com.facishare.open.msg.model.SendAppToCMessageVO;
import com.facishare.open.msg.result.MessageResult;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

/**
 * @IgnoreI18nFile
 */
@RestResource(value = "OPEN_MSG_DHT", codec = "com.facishare.paas.appframework.common.service.codec.RestResultCodeC",  contentType = "application/json")
public interface SendOpenMessageServiceProxy {
    @POST(value = "/send/open/sendAppToCMessage", desc = "图文消息")
    MessageResult sendAppToCMessage(@Body SendAppToCMessageVO arg, @HeaderMap Map<String, String> headers);
}
