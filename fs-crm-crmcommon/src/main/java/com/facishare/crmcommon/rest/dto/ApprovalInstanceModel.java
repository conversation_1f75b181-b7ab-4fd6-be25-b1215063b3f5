package com.facishare.crmcommon.rest.dto;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

public class ApprovalInstanceModel {
    @Data
    public static class Arg {
        String objectId;
    }

    @Data
    public static class DetailArg {
        String instanceId;
    }

    @Data
    public static class Result {
        private int code;
        private String message;
        private List<Instance> data;

        public boolean success() {
            return code == 0;
        }
    }

    @Data
    public static class DetailResult {
        private int code;
        private String message;
        private InstanceDetail data;

        public boolean success() {
            return code == 0;
        }
    }

    @Data
    public static class Instance {
        private String instanceId;
        private String instanceName;
        private String objectId;
        private String triggerType;
        private String state;
        private long createTime;
        private long lastModifyTime;
        private long endTime;
        private String apiName;
        private String applicantId;
        private long cancelTime;
        @SerializedName("linkApp")
        private String linkAppId;
        private Boolean linkAppEnable;
        private Integer linkAppType;
    }

    @Data
    public static class Opinion {
        private Integer userId;
        private String actionType;
        private String opinion;
        private long replyTime;
    }

    @Data
    public static class Task {
        private String id;
        private String taskName;
        private String type;
        private String state;
        private List<Opinion> opinions;
        private List<Long> completePersons;
        private List<Long> unCompletePersons;
        @SerializedName("linkApp")
        private String linkAppId;
        private Boolean linkAppEnable;
        private Integer linkAppType;
        private Long createTime;
        private Long modifyTime;
        private Long endTime;
    }

    @Data
    public static class InstanceDetail {
        private Instance instance;
        private List<Task> tasks;
    }

    public enum ApprovalFlowState {
        IN_PROGRESS("in_progress"),

        PASS("pass"),

        ERROR("error"),

        CANCEL("cancel"),

        REJECT("reject");

        private String value;

        ApprovalFlowState(String value) {
            this.value = value;
        }

        public static ApprovalFlowState of(String value) {
            for (ApprovalFlowState approvalFlowState : values()) {
                if (value.equals(approvalFlowState.value)) {
                    return approvalFlowState;
                }
            }
            return null;
        }
    }
}
