package com.facishare.crmcommon.rest;

import com.facishare.crmcommon.rest.dto.CheckPaymentMatchNoteModel;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

/**
 * @IgnoreI18nFile
 */
@RestResource(value = "FS_CRM_SFA", contentType = "application/json")
public interface AccountReceiveNoteProxy {
    @POST(value = "/accounts_receivable/service/check_payment_match_note", desc = "新建回款时校验核销数据")
    CheckPaymentMatchNoteModel.Result checkPaymentMatchNode(@HeaderMap Map<String, String> headers, @Body CheckPaymentMatchNoteModel.Arg arg);
}
