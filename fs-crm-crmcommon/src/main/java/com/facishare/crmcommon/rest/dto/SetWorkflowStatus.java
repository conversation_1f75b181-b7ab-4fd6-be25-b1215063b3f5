package com.facishare.crmcommon.rest.dto;

import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * Created by renlb on 2019/3/5.
 */
public class SetWorkflowStatus {

    @Data
    @Builder
    public static class Arg {
        @SerializedName("DataID")
        String dataId;

        @SerializedName("WorkFlowID")
        String work_flow_id;

        @SerializedName("OwnerID")
        int ownerId;

        @SerializedName("CurrentLevel")
        int current_level;

        @SerializedName("ObjectType")
        int object_type;

        @SerializedName("ConfirmStatus")
        int confirm_status;

        @SerializedName("Name")
        String name;

        @SerializedName("RejectReason")
        String reject_reason;

        @SerializedName("Money")
        Double money;

        @SerializedName("Date")
        Long date;
    }

    @Data
    @Builder
    public static class Result {
        @SerializedName("success")
        private boolean success;
        @SerializedName("message")
        private String message;
        @SerializedName("errorCode")
        private int errorCode;
        @SerializedName("value")
        private StatusLevel value;
    }

    @Data
    @Builder
    public static class StatusLevel {
        @SerializedName("Status")
        int status;

        @SerializedName("CurrentLevel")
        int current_level;

        @SerializedName("NextApproverIDs")
        List<Integer> next_approver_ids;

        @SerializedName("ShipperIDs")
        List<Integer> shipper_ids;
    }
}
