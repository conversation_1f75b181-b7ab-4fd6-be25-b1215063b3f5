package com.facishare.crmcommon.rest;

import com.facishare.crmcommon.rest.dto.DailyBillModel;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

/**
 * <AUTHOR>
 * Created on 2020/3/24.
 * @IgnoreI18nFile
 */
@RestResource(value = "SAIL_ADMIN", desc = "订货通", contentType = "application/json")
public interface DailyBillProxy {

    @POST(value = "/dailyBill/create", desc = "创建日账单")
    DailyBillModel.Result create(@Body DailyBillModel.CreateArg arg);

    @POST(value = "/dailyBill/getLatestDailyBill", desc = "获取最近一天的日账单数据")
    DailyBillModel.Result getLatestDailyBill(@Body DailyBillModel.GetLatestDailyBillArg arg);

}
