//package com.facishare.crm.rest.dto;
//
//import com.google.gson.annotations.SerializedName;
//import lombok.Data;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// * @date 2018/6/26
// */
//public class ModifyUserDefinedFieldModel {
////    @Data
////    public static class Arg {
////        @SerializedName("UpdateUserDefinedFieldList")
////        List<ModifyUserDefinedFieldModel.UserDefinedField> updateUserDefinedFieldList;
////    }
//
////    @Data
////    public static class Result {
////        @SerializedName("value")
////        private boolean value;
////        @SerializedName("success")
////        private boolean success;
////        @SerializedName("message")
////        private String message;
////        @SerializedName("errorCode")
////        private Integer errorCode;
////    }
//
////    @Data
////    public static class UserDefinedField {
////        @SerializedName("UserDefinedFieldID")
////        private String userDefinedFieldId;
////
////        @SerializedName("UserDefinedFieldValues")
////        private UserDefinedFieldValue userDefinedFieldValue;
////    }
//
////    @Data
////    public static class UserDefinedFieldValue {
////        @SerializedName("IsNotNull")
////        private Integer isNotNull;
////    }
//
//}
