package com.facishare.crmcommon.rest;

import com.facishare.crmcommon.rest.dto.CustomerAccountConfigModel;
import com.facishare.crmcommon.rest.dto.SailAdminResult;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

/**
 * <AUTHOR>
 * Created on 2020/3/20.
 * @IgnoreI18nFile
 */
@RestResource(value = "SAIL_ADMIN", desc = "订货通", contentType = "application/json")
public interface CustomerAccountConfigProxy {

    @POST(value = "/customerAccountConfig/save", desc = "保存客户账户开启配置")
    SailAdminResult save(@Body CustomerAccountConfigModel.SaveArg arg);

    @POST(value = "/customerAccountConfig/get", desc = "查询客户账户开启配置详情")
    CustomerAccountConfigModel.DetailResult get(@Body CustomerAccountConfigModel.DetailArg arg);

    @POST(value = "/customerAccountConfig/list", desc = "查询客户账户开启配置列表")
    CustomerAccountConfigModel.ListResult list(@Body CustomerAccountConfigModel.ListArg arg);

}
