package com.facishare.crmcommon.rest;

import com.facishare.crmcommon.rest.dto.DailyAbnormalCustomerAccountModel;
import com.facishare.crmcommon.rest.dto.SailAdminResult;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

/**
 * <AUTHOR>
 * Created on 2020/3/24.
 * @IgnoreI18nFile
 */
@RestResource(value = "SAIL_ADMIN", desc = "订货通", contentType = "application/json")
public interface DailyAbnormalCustomerAccountProxy {

    @POST(value = "/dailyAbnormalCustomerAccount/create", desc = "创建每日异常账户")
    SailAdminResult create(@Body DailyAbnormalCustomerAccountModel.CreateArg arg);

    @POST(value = "/dailyAbnormalCustomerAccount/count", desc = "统计昨日企业和账户异常数据")
    DailyAbnormalCustomerAccountModel.CountResult count(@Body DailyAbnormalCustomerAccountModel.CountArg arg);

    @POST(value = "/dailyAbnormalCustomerAccount/list", desc = "查询指定企业日期下异常账户数据")
    DailyAbnormalCustomerAccountModel.ListResult list(@Body DailyAbnormalCustomerAccountModel.ListArg arg);

}
