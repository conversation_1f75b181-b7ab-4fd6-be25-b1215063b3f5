package com.facishare.crmcommon.rest.dto;

import com.facishare.rest.core.codec.IRestCodeC;
import com.facishare.rest.core.exception.RestProxyBusinessException;
import com.facishare.rest.core.exception.RestProxyRuntimeException;
import com.facishare.rest.core.util.JacksonUtil;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

import static com.facishare.rest.core.exception.RestProxyExceptionCode.REST_PROXY_INVOKE_ROMOTE_SERVICE;

@Slf4j
public class MappingRulerCodec implements IRestCodeC {
    @Override
    public <T> byte[] encodeArg(T obj) {
        if (obj instanceof String) {
            return (obj + "").getBytes(StandardCharsets.UTF_8);
        } else {
            log.debug("请求元数据映射规则接口,请求参数:{}", JacksonUtil.toJsonUseProperty(obj));
            return JacksonUtil.toJsonUseProperty(obj).getBytes(StandardCharsets.UTF_8);
        }
    }

    @Override
    public <T> T decodeResult(int statusCode, Map<String, List<String>> headers, byte[] bytes, Class<T> clazz) {
        T ret ;
        String content = null;
        try {
            content = new String(bytes, StandardCharsets.UTF_8);
            ret = JacksonUtil.fromJson(content, clazz);
        } catch (Exception e) {
            throw new RestProxyRuntimeException(REST_PROXY_INVOKE_ROMOTE_SERVICE, content);
        }
        validate(ret);
        return ret;
    }

    private void validate(Object result) {
        if (result instanceof MappingRulerResult) {
            MappingRulerResult sfaBaseResult = (MappingRulerResult) result;
            if (!sfaBaseResult.isSuccess()) {
                throw new RestProxyBusinessException(sfaBaseResult.getCode(), sfaBaseResult.getMessage());
            }
        } else {
            throw new RestProxyRuntimeException(REST_PROXY_INVOKE_ROMOTE_SERVICE);
        }
    }
}
