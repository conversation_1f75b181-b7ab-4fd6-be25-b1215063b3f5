package com.facishare.crmcommon.rest.dto;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/12/17 11:48
 * @instruction
 */
public class ProductCatoryFieldModel {

    @Data
    public static class Result {
        @SerializedName("success")
        private boolean success;
        @SerializedName("message")
        private String message;
        @SerializedName("errorCode")
        private int errorCode;
        @SerializedName("value")
        private ProductCatoryItems value;
    }

    @Data
    public static class ProductCatoryItems{
        @SerializedName("Items")
        private List<ProductCatory> Items;
    }

    @Data
    public static class ProductCatory{
        @SerializedName("CategoryID")
        private String CategoryID;
        @SerializedName("CategoryCode")
        private String CategoryCode;
        @SerializedName("Name")
        private String Name;
        @SerializedName("CategoryOrder")
        private Integer CategoryOrder;
        @SerializedName("ParentID")
        private String ParentID;
        @SerializedName("IsDeleted")
        private Boolean IsDeleted;
        @SerializedName("Children")
        private List<ProductCatory> Children;

    }




}
