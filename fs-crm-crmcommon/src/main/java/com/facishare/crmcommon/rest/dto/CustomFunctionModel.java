package com.facishare.crmcommon.rest.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
public class CustomFunctionModel {
    @Data
    public static class Arg{
        private String apiName;
        private List<Parameter> parameters;
    }

    @Data
    public static class Result{
        private int errCode;
        private String errorInfo;
        private FunctionResult result;

        public boolean success (){
            return errCode == 0 && this.result != null && this.result.success;
        }
    }

    @Data
    public static class Parameter {
        private String name;
        private String type;
        private Object value;
    }

    @Data
    public static class FunctionResult {
        private boolean success;
        private Object functionResult;
        private String errorInfo;
    }
}
