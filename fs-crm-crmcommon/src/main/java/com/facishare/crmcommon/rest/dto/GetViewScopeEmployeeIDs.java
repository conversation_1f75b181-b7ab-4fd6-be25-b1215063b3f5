package com.facishare.crmcommon.rest.dto;

import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

public interface GetViewScopeEmployeeIDs {

    @Data
    @Builder
    public class Arg
    {
        int EmployeeID;
        String RoleCode;
    }
    @Data
    public class Result
    {
        @SerializedName("success")
        boolean success;

        @SerializedName("message")
        String message;

        @SerializedName("errorCode")
        int errorCode;

        @SerializedName("value")
        ResultValue value;
    }
    @Data
    public class ResultValue{
        List<Integer> EmployeeIDs;
    }

}
