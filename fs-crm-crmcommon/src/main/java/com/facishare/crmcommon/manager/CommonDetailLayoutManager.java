package com.facishare.crmcommon.manager;

import com.facishare.crmcommon.constants.LayoutConstants;
import com.facishare.crmcommon.constants.SystemConstants;
import com.facishare.crmcommon.exception.CommonBusinessException;
import com.facishare.crmcommon.exception.CommonErrorCode;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
@Slf4j
public class CommonDetailLayoutManager {

    @Autowired
    protected ServiceFacade serviceFacade;
    @Resource
    private CommonLayoutManager commonLayoutManager;

    public void detailLayoutAddField(User user, String objectApiName, String addFieldApiName, IFormField formField) {
        ILayout detailLayout = serviceFacade.findDefaultLayout(user, SystemConstants.LayoutType.Detail.layoutType, objectApiName);
        detailLayoutAddField(user, detailLayout, addFieldApiName, formField);
    }

    public void detailLayoutAddField(User user, ILayout detailLayout, String addFieldApiName, IFormField formField) {
        if (detailLayout == null) {
            return;
        }

        List<String> addFieldApiNames = Lists.newArrayList(addFieldApiName);

        Map<String, IFormField> fieldApiName2IFormField = new HashMap<>();
        fieldApiName2IFormField.put(addFieldApiName, formField);

        addFields(user, detailLayout, addFieldApiNames, fieldApiName2IFormField);
    }

    public void addField(User user, ILayout detailLayout, String addFieldApiName, IFormField formField) {
        List<String> addFieldApiNames = Lists.newArrayList(addFieldApiName);

        Map<String, IFormField> fieldApiName2IFormField = new HashMap<>();
        fieldApiName2IFormField.put(addFieldApiName, formField);

        addFields(user, detailLayout, addFieldApiNames, fieldApiName2IFormField);
    }

    /**
     * 回款 detailLayout：加字段
     */
    public void addFields(User user, ILayout detailLayout, List<String> addFieldApiNames, Map<String, IFormField> fieldApiName2IFormField) {
        log.info("addFields, user[{}], detailLayout[{}]", user, detailLayout);

        if (detailLayout == null) {
            return;
        }

        IFieldSection baseFieldSection = getBaseFieldSection(detailLayout);
        if (baseFieldSection == null) {
            log.warn("addFields, baseFieldSection =null,  user[{}], detailLayout[{}]", user, detailLayout);
            return;
        }

        List<IFormField> oldFormFields = baseFieldSection.getFields();
        boolean needUpdate = false;
        for (String addFieldApiName : addFieldApiNames) {
            Optional<IFormField> fieldOpt = oldFormFields.stream().filter(oldFormField -> Objects.equals(oldFormField.getFieldName(), addFieldApiName)).findFirst();
            if (!fieldOpt.isPresent()) {
                IFormField formField = fieldApiName2IFormField.get(addFieldApiName);
                log.info("addFields, user[{}], formField[{}]", user, formField);
                if (formField != null) {
                    oldFormFields.add(formField);
                    needUpdate = true;
                }
            }
        }

        if (needUpdate) {
            baseFieldSection.setFields(oldFormFields);
            log.info("addFields, detailLayout[{}]", detailLayout);
            commonLayoutManager.replace(detailLayout);
        }
    }


    /**
     * 修改detailLayout上字段的is_required
     */
    public void updateFieldRequired(User user, ILayout detailLayout, String fieldApiName, boolean required) {
        log.info("updateFieldRequired, user[{}], detailLayout[{}]", user, detailLayout);

        if (detailLayout == null) {
            return;
        }

        IFieldSection baseFieldSection = getBaseFieldSection(detailLayout);
        if (baseFieldSection == null) {
            log.warn("updateFieldRequired, baseFieldSection =null,  user[{}], detailLayout[{}]", user, detailLayout);
            return;
        }

        List<IFormField> oldFormFields = baseFieldSection.getFields();
        Optional<IFormField> fieldOpt = oldFormFields.stream().filter(oldFormField -> Objects.equals(oldFormField.getFieldName(), fieldApiName)).findFirst();
        if (!fieldOpt.isPresent()) {
            return;
        }
        IFormField formField = fieldOpt.get();
        if (Objects.equals(formField.get("is_required"), required)) {
            return;
        }
        formField.setRequired(required);

        baseFieldSection.setFields(oldFormFields);
        log.info("updateFieldRequired, detailLayout[{}]", detailLayout);
        commonLayoutManager.replace(detailLayout);
    }

    private IFieldSection getBaseFieldSection(ILayout defaultLayout) {
        List<IComponent> components;
        FormComponent formComponent = null;
        try {
            components = defaultLayout.getComponents();
        } catch (MetadataServiceException e) {
            log.warn("layout.getComponents failed, layout:{}", defaultLayout);
            throw new CommonBusinessException((CommonErrorCode.BUSINESS_ERROR), e.getMessage());
        }
        for (IComponent iComponent : components) {
            if (Objects.equals(iComponent.getName(), LayoutConstants.FORM_COMPONENT_API_NAME)) {
                formComponent = (FormComponent) iComponent;
                break;
            }
        }

        //获取formFields
        IFieldSection baseFieldSection = null;
        if (formComponent != null) {
            List<IFieldSection> fieldSections = formComponent.getFieldSections();
            for (IFieldSection fieldSection : fieldSections) {
                if (Objects.equals(fieldSection.getName(), LayoutConstants.BASE_FIELD_SECTION_API_NAME)) {
                    baseFieldSection = fieldSection;
                    break;
                }
            }
        }

        return baseFieldSection;
    }
}