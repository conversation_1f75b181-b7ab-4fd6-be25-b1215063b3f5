package com.facishare.crmcommon.manager;

import com.facishare.privilege.api.UserPrivilegeRestService;
import com.facishare.privilege.api.module.PrivilegeContext;
import com.facishare.privilege.api.module.user.GetUsersByRoleCodesVo;
import com.facishare.privilege.api.module.user.UserRoleVo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CommonRoleManager {

    @Autowired
    private UserPrivilegeRestService userPrivilegeService;

    public List<String> getRoleUserIds(String tenantId, String operatorId, List<String> roleCodes) {
        PrivilegeContext context = PrivilegeContext.builder().tenantId(Integer.valueOf(tenantId)).operatorId(Integer.valueOf(operatorId)).appId("CRM").build();

        GetUsersByRoleCodesVo.Argument arg = new GetUsersByRoleCodesVo.Argument();
        arg.setRoleCodes(roleCodes);
        List<UserRoleVo> userRoleVos = userPrivilegeService.getUsersByRoleCodes(context, arg);
        log.info("userPrivilegeService.getUsersByRoleCodes, context[{}], arg[{}], result[{}]", context, arg, userRoleVos);
        if (CollectionUtils.isEmpty(userRoleVos)) {
            return Lists.newArrayList();
        }
        return userRoleVos.stream().map(UserRoleVo::getUserId).collect(Collectors.toList());
    }
}
