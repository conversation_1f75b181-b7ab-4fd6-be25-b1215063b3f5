package com.facishare.crmcommon.manager;

import com.facishare.crmcommon.exception.CommonBusinessException;
import com.facishare.crmcommon.exception.CommonErrorCode;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.ObjectLayoutService;
import com.facishare.paas.appframework.core.predef.service.dto.layout.FindByObjDescribeApiName;
import com.facishare.paas.metadata.api.service.ILayoutService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by chenzs on 2018/6/6.
 */
@Service
@Slf4j
public class CommonLayoutManager {
    @Resource
    private ILayoutService layoutService;
    @Autowired
    private ObjectLayoutService objectLayoutService;

    public void replace(ILayout layout) {
        try {
            ILayout result = layoutService.replace(layout);
            log.info("layoutService.replace success, layout:{}, result:{}", layout, result);
        } catch (MetadataServiceException e) {
            log.warn("layoutService.replace failed, layout:{}", layout, e);
            throw new CommonBusinessException(() -> e.getErrorCode().getCode(), e.getMessage());
        } catch (Exception e) {
            log.warn("layoutService.replace failed, layout:{}", layout, e);
            throw new CommonBusinessException(CommonErrorCode.REPLACE_LAYOUT_FAILED, e.getMessage());
        }
    }

    /**
     * 查所有的'新建编辑页布局'
     * object/layout/service/findByObjDescribeApiName
     * {"objectDescribeApiName":"SalesOrderObj","layoutType":"edit","sourceInfo":"object_management"}
     */
    public List<ILayout> getLayouts(ServiceContext serviceContext, String objectDescribeApiName) {
        /**
         * object/layout/service/findByObjDescribeApiName
         * {"objectDescribeApiName":"SalesOrderObj","layoutType":"edit","sourceInfo":"object_management"}
         */
        FindByObjDescribeApiName.Arg findByObjDescribeApiNameArg = new FindByObjDescribeApiName.Arg();
        findByObjDescribeApiNameArg.setObjectDescribeApiName(objectDescribeApiName);
        findByObjDescribeApiNameArg.setLayoutType("edit");
        findByObjDescribeApiNameArg.setSourceInfo("object_management");
        FindByObjDescribeApiName.Result result = objectLayoutService.findByObjectDescribeApiName(findByObjDescribeApiNameArg, serviceContext);
        if (CollectionUtils.isEmpty(result.getLayouts())) {
            return Lists.newArrayList();
        }

        return result.getLayouts().stream().map(LayoutDocument::toLayout).collect(Collectors.toList());
    }
}
