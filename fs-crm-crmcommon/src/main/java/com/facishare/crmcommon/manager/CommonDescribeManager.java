package com.facishare.crmcommon.manager;

import com.facishare.crmcommon.exception.CommonBusinessException;
import com.facishare.crmcommon.exception.CommonErrorCode;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOption;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by chenzs on 2018/6/6.
 */
@Service
@Slf4j
public class CommonDescribeManager {
    @Autowired
    private IObjectDescribeService objectDescribeService;

    @Autowired
    private DescribeLogicService describeLogicService;

    @Autowired
    private ServiceFacade serviceFacade;

    public void deleteField(String tenantId, String objectApiName, String fieldApiName) throws MetadataServiceException {
        IObjectDescribe objectDescribe = findByTenantIdAndDescribeApiName(tenantId, objectApiName);
        List<IFieldDescribe> fieldDescribes = objectDescribe.getFieldDescribes();

        boolean exist = false;
        List<IFieldDescribe> newFieldDescribes = Lists.newArrayList();
        for (IFieldDescribe fieldDescribe : fieldDescribes) {
            if (Objects.equals(fieldDescribe.getApiName(), fieldApiName)) {
                exist = true;
            } else {
                newFieldDescribes.add(fieldDescribe);
            }
        }
        if (!exist) {
            return;
        }
        objectDescribe.setFieldDescribes(newFieldDescribes);
        IObjectDescribe result = objectDescribeService.replace(objectDescribe,  false);
    }

    /**
     * 添加字段（有则不添加）
     */
    public void addFieldDescribe(String tenantId, String objectApiName, String fieldApiName, IFieldDescribe fieldDescribe) {
        IObjectDescribe objectDescribe = findByTenantIdAndDescribeApiName(tenantId, objectApiName);

        addFieldDescribe(tenantId, objectApiName, objectDescribe, fieldApiName, fieldDescribe);
    }

    /**
     * 添加字段（有则不添加）
     */
    public void addFieldDescribe(String tenantId, String objectApiName, IObjectDescribe objectDescribe, String fieldApiName, IFieldDescribe fieldDescribe) {
        List<String> fieldApiNames = Lists.newArrayList(fieldApiName);

        Map<String, IFieldDescribe> fieldApiName2FieldDescribe = new HashMap<>();
        fieldApiName2FieldDescribe.put(fieldApiName, fieldDescribe);

        addFieldDescribes(tenantId, objectApiName, objectDescribe, fieldApiNames, fieldApiName2FieldDescribe);
    }

    /**
     * 添加字段（有则不添加）
     */
    public void addFieldDescribes(String tenantId, String objectApiName, List<String> fieldApiNames, Map<String, IFieldDescribe> fieldApiName2FieldDescribe) {
        if (CollectionUtils.isEmpty(fieldApiNames)) {
            return;
        }

        //查询describe
        IObjectDescribe objectDescribe = findByTenantIdAndDescribeApiName(tenantId, objectApiName);

        //添加字段（添加之前，判断是否已存在对应的字段）
        addFieldDescribes(tenantId, objectApiName, objectDescribe, fieldApiNames, fieldApiName2FieldDescribe);
    }

    /**
     * 添加字段（有则不添加）
     * 不适合用来加自定义字段，在企业隔离的企业，会出现问题 ; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: column salesorderobj.value179 does not exist
     * 这个最后用的 com.facishare.paas.metadata.service.impl.ObjectDescribeServiceImpl#replace(com.facishare.paas.metadata.api.describe.IObjectDescribe, boolean)，不适合用来加自定义对象
     * 可以用
     *   com.facishare.paas.metadata.api.service.IObjectDescribeService#addFieldDescribe
     *   或者 com.facishare.paas.metadata.api.service.IObjectDescribeService#update(com.facishare.paas.metadata.api.describe.IObjectDescribe)
     */
    public void addFieldDescribes(String tenantId, String objectApiName, IObjectDescribe objectDescribe, List<String> fieldApiNames, Map<String, IFieldDescribe> fieldApiName2FieldDescribe) {
        log.info("addFieldDescribes, tenantId[{}], objectApiName[{}], objectDescribe[{}],  fieldApiNames[{}], fieldApiName2FieldDescribe[{}]", tenantId, objectApiName, objectDescribe,  fieldApiNames, fieldApiName2FieldDescribe);
        if (CollectionUtils.isEmpty(fieldApiNames)) {
            return;
        }

        //原来的describe是否有要添加的所有字段
        boolean hasAllField = true;
        for (String fieldApiName : fieldApiNames) {
            boolean hasFieldDescribe = hasFieldDescribe(objectDescribe, fieldApiName);
            if (!hasFieldDescribe) {
                hasAllField = false;
                objectDescribe.addFieldDescribe(fieldApiName2FieldDescribe.get(fieldApiName));
            }
        }

        //要加的字段中，有的原来的describe是没有的，则更新describe
        if (!hasAllField) {
            log.info("addFieldDescribes, objectDescribe[{}]", objectDescribe);
            replace(objectDescribe);
        }
    }


    public boolean isDescribeHasField(User user, String objectApiName, String fieldApiName, int queryTimes, long sleepMillis) {
        log.info("isDescribeHasField, user[{}], objectApiName[{}], fieldApiName[{}], queryTimes[{}], sleepMillis[{}]", user, objectApiName, fieldApiName, queryTimes, sleepMillis);

        int hasQueryTimes = 0;

        while (hasQueryTimes < queryTimes) {
            boolean hasFieldDescribe = isDescribeHasField(user, objectApiName, fieldApiName);
            if (hasFieldDescribe) {
                return true;
            }

            hasQueryTimes ++;

            log.info("isDescribeHasField, user[{}], objectApiName[{}], fieldApiName[{}], queryTimes[{}], sleepMillis[{}], hasQueryTimes[{}]",
                    user, objectApiName, fieldApiName, queryTimes, sleepMillis, hasQueryTimes);

            try {
                Thread.sleep(sleepMillis);
            } catch (InterruptedException e) {
                log.warn("isDescribeHasField ", e);
            }
        }

        return false;
    }

    public void updateFieldDescribeIsRequired(User user, String objectApiName, String fieldApiName, boolean isRequired) {
        IObjectDescribe objectDescribe = findByTenantIdAndDescribeApiName(user.getTenantId(), objectApiName);
        IFieldDescribe targetField = getFieldDescribe(objectDescribe, fieldApiName);
        if (targetField == null) {
            return;
        }

        if (Objects.equals(targetField.isRequired(), isRequired)) {
            return;
        }

        targetField.setRequired(isRequired);
        serviceFacade.updateFieldDescribe(objectDescribe, Lists.newArrayList(targetField));
    }

    private IFieldDescribe getFieldDescribe(IObjectDescribe objectDescribe, String fieldApiName) {
        List<IFieldDescribe> fieldDescribes = objectDescribe.getFieldDescribes();
        if (CollectionUtils.isEmpty(fieldDescribes)) {
            return null;
        }

        for (IFieldDescribe fieldDescribe : fieldDescribes) {
            if (Objects.equals(fieldDescribe.getApiName(), fieldApiName)) {
                return fieldDescribe;
            }
        }
        return null;
    }

    public List<IFieldDescribe> getFieldDescribes(String tenantId, String objectApiName, List<String> fieldApiNames, List<String> fieldTypes) {
        List<IFieldDescribe> fields = Lists.newArrayList();
        if (CollectionUtils.isEmpty(fieldApiNames) || CollectionUtils.isEmpty(fieldTypes)) {
            return fields;
        }

        IObjectDescribe objectDescribe = findByTenantIdAndDescribeApiName(tenantId, objectApiName);
        if (objectDescribe == null) {
            return fields;
        }
        List<IFieldDescribe> fieldDescribes = objectDescribe.getFieldDescribes();
        if (CollectionUtils.isEmpty(fieldDescribes)) {
            return fields;
        }

        for (IFieldDescribe fieldDescribe : fieldDescribes) {
            if (fieldTypes.contains(fieldDescribe.getType()) && fieldApiNames.contains(fieldDescribe.getApiName())) {
                fields.add(fieldDescribe);
            }
        }
        return fields;
    }

    public boolean isDescribeHasField(User user, String objectApiName, String fieldApiName) {
        IObjectDescribe objectDescribe = findByTenantIdAndDescribeApiName(user.getTenantId(), objectApiName);
        return hasFieldDescribe(objectDescribe, fieldApiName);
    }

    /**
     * 是否存在某个字段的定义
     */
    public boolean hasFieldDescribe(IObjectDescribe objectDescribe, String fieldApiName) {
        //获取字段定义
        List<IFieldDescribe> fieldDescribes = null;
        fieldDescribes = objectDescribe.getFieldDescribes();

        //判断是否有字段
        for (IFieldDescribe f : fieldDescribes) {
            if (Objects.equals(f.getApiName(), fieldApiName)) {
                return true;
            }
        }
        return false;
    }

    public IObjectDescribe findByTenantIdAndDescribeApiName(String tenantId, String describeApiName) {
        IObjectDescribe objectDescribe;
        try {
            IObjectDescribe result = objectDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, describeApiName);
            log.info("objectDescribeService.findByTenantIdAndDescribeApiName success, tenantId:{}, objectDescribeApiName:{}, result:{}", tenantId, describeApiName, result);
        } catch (MetadataServiceException e) {
            log.warn("objectDescribeService.findByTenantIdAndDescribeApiName failed ,tenantId:{}, objectDescribeApiName:{}", tenantId, describeApiName, e);
            throw new CommonBusinessException(() -> e.getErrorCode().getCode(), e.getMessage());
        }
        return objectDescribe;
    }

    /**
     * 更新定义
     * 不要用来加自定义字段，加自定义字段可以用： 【封金运】
     *     com.facishare.paas.metadata.api.service.IObjectDescribeService#addFieldDescribe
     *     或者 com.facishare.paas.metadata.api.service.IObjectDescribeService#update(com.facishare.paas.metadata.api.describe.IObjectDescribe)
     */
    public void replace(IObjectDescribe objectDescribe) {
        try {
            IObjectDescribe result = objectDescribeService.replace(objectDescribe,  false);
            log.info("objectDescribeService.replace success, objectDescribe:{}, isAllowLabelRepeat:{}, result:{}", objectDescribe, false, result);
        } catch (MetadataServiceException e) {
            log.warn("objectDescribeService.replace failed, objectDescribe:{} , isAllowLabelRepeat:{}", objectDescribe, false, e);
            throw new CommonBusinessException(() -> e.getErrorCode().getCode(), e.getMessage());
        } catch (Exception e) {
            log.warn("objectDescribeService.replace failed, objectDescribe:{} ,isAllowLabelRepeat:{}", objectDescribe, false, e);
            throw new CommonBusinessException(CommonErrorCode.REPLACE_DESCRIBE_FAILED, e.getMessage());
        }
    }

    /**
     * 单选字段加选项
     */
    public void selectOneFieldAddOption(String tenantId, String objectApiName, String fieldApiName, String optionValue, String optionLabel) {
        Map<String, IObjectDescribe> describeMap = serviceFacade.findObjects(tenantId, Lists.newArrayList(objectApiName));
        if (describeMap == null || describeMap.size() == 0) {
            return;
        }

        IObjectDescribe objectDescribe = describeMap.get(objectApiName);

        String type = objectDescribe.getFieldDescribe(fieldApiName).getType();
        if (!Objects.equals("select_one", type)) {
            return;
        }

        SelectOneFieldDescribe fieldDescribe = (SelectOneFieldDescribe) objectDescribe.getFieldDescribe(fieldApiName);
        List<ISelectOption> selectOptions = fieldDescribe.getSelectOptions();

        for (ISelectOption selectOption : selectOptions) {
            if (Objects.equals(selectOption.getValue(), optionValue)) {
                //已经有了
                return;
            }
        }

        ISelectOption selectOption = new SelectOption();
        selectOption.setLabel(optionLabel);
        selectOption.setValue(optionValue);

        selectOptions.add(selectOption);
        fieldDescribe.setSelectOptions(selectOptions);
        serviceFacade.updateFieldDescribe(objectDescribe, Lists.newArrayList(fieldDescribe));
    }

    public Map<String, String> getSourceFieldApiName2TargetFieldApiName(String sourceTenantId, String targetTenantId, String objectApiName, Set<String> sourceFieldApiNames) {
        if (CollectionUtils.isEmpty(sourceFieldApiNames)) {
            return new HashMap<>();
        }
        IObjectDescribe sourceObjectDescribe = findByTenantIdAndDescribeApiName(sourceTenantId, objectApiName);
        List<IFieldDescribe> sourceFieldDescribes = sourceObjectDescribe.getFieldDescribes();
        Map<String, String> sourceFieldApiName2Label = sourceFieldDescribes.stream().filter(d -> sourceFieldApiNames.contains(d.getApiName())).collect(Collectors.toMap(IFieldDescribe::getApiName, IFieldDescribe::getLabel));
        List<String> fieldLabels = Lists.newArrayList(sourceFieldApiName2Label.values());

        IObjectDescribe targetObjectDescribe = findByTenantIdAndDescribeApiName(targetTenantId, objectApiName);
        List<IFieldDescribe> targetFieldDescribes = targetObjectDescribe.getFieldDescribes();
        Map<String, String> targetFieldLabel2ApiName = targetFieldDescribes.stream().filter(d -> fieldLabels.contains(d.getLabel())).collect(Collectors.toMap(IFieldDescribe::getLabel, IFieldDescribe::getApiName));

        Map<String, String> sourceFieldApiName2TargetFieldApiName = new HashMap<>();
        for (String sourceFieldApiName : sourceFieldApiNames) {
            String label = sourceFieldApiName2Label.get(sourceFieldApiName);
            String targetFieldApiName = targetFieldLabel2ApiName.get(label);
            if (!Strings.isNullOrEmpty(targetFieldApiName)) {
                sourceFieldApiName2TargetFieldApiName.put(sourceFieldApiName, targetFieldApiName);
            }
        }
        return sourceFieldApiName2TargetFieldApiName;
    }

    public String getFieldLabel(String tenantId, String objectApiName, String fieldApiName) {
        if (Strings.isNullOrEmpty(objectApiName) || Strings.isNullOrEmpty(fieldApiName)) {
            return null;
        }
        Map<String, String> fieldLabelMap = getFieldLabels(tenantId, objectApiName, Lists.newArrayList(fieldApiName));
        return fieldLabelMap.get(fieldApiName);
    }

    public List<String> getFieldLabelList(String tenantId, String objectApiName, List<String> fieldApiNames) {
        Map<String, String> fieldLabels = getFieldLabels(tenantId, objectApiName, fieldApiNames);
        return Lists.newArrayList(fieldLabels.values());
    }

    public List<String> getNotExistFieldApiNames(String tenantId, String objectApiName, List<String> fieldApiNames) {
        if (CollectionUtils.isEmpty(fieldApiNames)) {
            return Lists.newArrayList();
        }

        //查定义
        IObjectDescribe objectDescribe = findByTenantIdAndDescribeApiName(tenantId, objectApiName);
        List<IFieldDescribe> fieldDescribes = objectDescribe.getFieldDescribes();
        if (CollectionUtils.isEmpty(fieldDescribes)) {
            return fieldApiNames;
        }
        List<String> existFieldApiNames = fieldDescribes.stream().map(IFieldDescribe::getApiName).collect(Collectors.toList());

        return fieldApiNames.stream().filter(l -> !existFieldApiNames.contains(l)).collect(Collectors.toList());
    }

    public List<String> getNotExistFieldLabelList(String tenantId, String objectApiName, List<String> fieldLabels) {
        if (CollectionUtils.isEmpty(fieldLabels)) {
            return Lists.newArrayList();
        }

        //查定义
        IObjectDescribe objectDescribe = findByTenantIdAndDescribeApiName(tenantId, objectApiName);
        List<IFieldDescribe> fieldDescribes = objectDescribe.getFieldDescribes();
        if (CollectionUtils.isEmpty(fieldDescribes)) {
            return fieldLabels;
        }
        List<String> existFieldLabelList = fieldDescribes.stream().map(IFieldDescribe::getLabel).collect(Collectors.toList());

        return fieldLabels.stream().filter(l -> !existFieldLabelList.contains(l)).collect(Collectors.toList());
    }

    /**
     * 获取字段label
     */
    public Map<String, String> getFieldLabels(String tenantId, String objectApiName, List<String> fieldApiNames) {
        if (CollectionUtils.isEmpty(fieldApiNames)) {
            return new HashMap<>();
        }

        //查定义
        IObjectDescribe objectDescribe = findByTenantIdAndDescribeApiName(tenantId, objectApiName);

        List<IFieldDescribe> fieldDescribes = objectDescribe.getFieldDescribes();
        if (CollectionUtils.isEmpty(fieldDescribes)) {
            return new HashMap<>();
        }

        Map<String, String> fieldLabelMap = new HashMap<>();

        for (IFieldDescribe fieldDescribe : fieldDescribes) {
            if (fieldApiNames.contains(fieldDescribe.getApiName())) {
                fieldLabelMap.put(fieldDescribe.getApiName(), fieldDescribe.getLabel());
            }
        }

        return fieldLabelMap;
    }

    public Map<String, String> getLabel2ApiNameMap(String tenantId, String objectApiName, List<String> labels) {
        if (CollectionUtils.isEmpty(labels)) {
            return new HashMap<>();
        }

        //查定义
        IObjectDescribe objectDescribe = findByTenantIdAndDescribeApiName(tenantId, objectApiName);

        List<IFieldDescribe> fieldDescribes = objectDescribe.getFieldDescribes();
        if (CollectionUtils.isEmpty(fieldDescribes)) {
            return new HashMap<>();
        }

        Map<String, String> fieldLabelMap = new HashMap<>();

        for (IFieldDescribe fieldDescribe : fieldDescribes) {
            if (labels.contains(fieldDescribe.getLabel())) {
                fieldLabelMap.put(fieldDescribe.getLabel(), fieldDescribe.getApiName());
            }
        }

        return fieldLabelMap;
    }

    /**
     * 查主对象的所有从对象apiName
     */
    public List<String> getDetailObjectApiNames(String tenantId, String objectApiName) {
        List<String> detailObjectApiNames = new ArrayList<>();
        if (Strings.isNullOrEmpty(tenantId) || Strings.isNullOrEmpty(objectApiName)) {
            return detailObjectApiNames;
        }

        List<IObjectDescribe> detailDescribes = describeLogicService.findDetailDescribes(tenantId, objectApiName);
        if (CollectionUtils.isEmpty(detailDescribes)) {
            return detailObjectApiNames;
        }

        return detailDescribes.stream().map(IObjectDescribe::getApiName).collect(Collectors.toList());
    }

    /**
     * 返回sourceObjApiNames中，有查找关联targetObjectApiName的对象
     */
    public Set<String> getHasObjectReferenceObjectApiNames(String tenantId, String targetObjectApiName, Set<String> sourceObjApiNames) {
        if (Strings.isNullOrEmpty(targetObjectApiName) || CollectionUtils.isEmpty(sourceObjApiNames)) {
            return new HashSet<>();
        }

        Set<String> hasObjectReferenceObjectApiNames = new HashSet<>();
        for (String sourceObjApiName : sourceObjApiNames) {
            boolean isObjectReference = isObjectReference(tenantId, sourceObjApiName, targetObjectApiName);
            if (isObjectReference) {
                hasObjectReferenceObjectApiNames.add(sourceObjApiName);
            }
        }

        return hasObjectReferenceObjectApiNames;
    }

    /**
     * fromObjectApiName 是否查找关联toObjectApiName
     */
    public boolean isObjectReference(String tenantId, String sourceObjApiName, String toObjectApiName) {
        IObjectDescribe sourceObjDesc = serviceFacade.findObject(tenantId, sourceObjApiName);
        Set<String> targetObjApiNames = ObjectDescribeExt.of(sourceObjDesc).findTargetObj();
        if (CollectionUtils.isEmpty(targetObjApiNames)) {
            return false;
        }
        return targetObjApiNames.contains(toObjectApiName);
    }

    public Set<String> getExistObjectApiNames(String tenantId, Collection<String> objectApiNames) {
        List<IObjectDescribe> describes = serviceFacade.findDescribeListWithoutFields(tenantId, objectApiNames);
        return describes.stream().map(IObjectDescribe::getApiName).collect(Collectors.toSet());
    }
}
