package com.facishare.crmcommon.util;

import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;

public class GrayUtil {

    private static final FsGrayReleaseBiz gray = FsGrayRelease.getInstance("sfa");

    private static final String ACCOUNTS_RECEIVABLE_OLD_TENANTS = "accounts_receivable_old_tenants";

    private static final String ACCOUNTS_RECEIVABLE_RED_PAYMENT = "accounts_receivable_red_payment";
    private static final String RED_PAYMENT_MAX_PAYMENT_AMOUNT_VALIDATE = "red_payment_max_payment_amount_validate";
    private static final String PAYMENT_IMPORT_UNCHECK = "payment_import_uncheck";

    private static final String PAYMENT_ALLOW_NEGATIVE = "payment_allow_amount_LT_zero";

    /**
     * 应收历史企业灰度名单
     */
    public static boolean isAccountsReceivableOldTenants(String tenantId) {
        return gray.isAllow(ACCOUNTS_RECEIVABLE_OLD_TENANTS, tenantId);
    }

    /**
     * 蒙牛二期，支持红字回款
     */
    public static boolean isAccountsReceivableRedPayment(String tenantId) {
        return gray.isAllow(ACCOUNTS_RECEIVABLE_RED_PAYMENT, tenantId);
    }

    public static boolean redPaymentMaxPaymentAmountValidate(String tenantId) {
        return gray.isAllow(RED_PAYMENT_MAX_PAYMENT_AMOUNT_VALIDATE, tenantId);
    }

    public static boolean isPaymentImportUncheck(String tenantId) {
        return gray.isAllow(PAYMENT_IMPORT_UNCHECK, tenantId);
    }

    public static boolean supportPaymentNegative(String tenantId) {
        return gray.isAllow(PAYMENT_ALLOW_NEGATIVE, tenantId);
    }
}
