package com.facishare.crmcommon.util;

import com.facishare.crmcommon.constants.CommonI18NKey;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.*;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

@Slf4j
public class DataUtil {

    public static <T> T parseObjectData(IObjectData iObjectData, Class<T> targetClass) {
        try {
//            // 数字或金额字段转换（因为关联字段值为空时，元数据会返回“N/A”，见com.facishare.paas.metadata.util.JsonFieldHandle#bulkHandleQuoteField）
            Gson gson = new GsonBuilder()
                    .registerTypeAdapter(BigDecimal.class, new TypeAdapter<BigDecimal>() {
                        @Override
                        public void write(JsonWriter out, BigDecimal value) throws IOException {
                            out.value(String.valueOf(value));
                        }

                        @Override
                        public BigDecimal read(JsonReader in) throws IOException {
                            try {
                                return new BigDecimal(in.nextString());
                            } catch (NumberFormatException e) {
                                return null;
                            }
                        }
                    })
                    .create();


            return gson.fromJson(ObjectDataExt.of(iObjectData).toJsonString(), targetClass);
        } catch (Exception e) {
            log.error("iObjectData[{}], targetClass[{}]", iObjectData, targetClass, e);
            throw new RuntimeException(e);
        }
    }

    public static void checkNotZero(BigDecimal value, String errMsg) {
        if (value.compareTo(BigDecimal.ZERO) == 0) {
            throw new ValidateException(errMsg);
        }
    }

    public static void checkNotNull(Object object, String errMsg) {
        if (Objects.isNull(object)) {
            throw new ValidateException(errMsg);
        }
    }

    public static <T> void checkNotEmpty(Collection<T> list, String errMsg) {
        if (CollectionUtils.empty(list)) {
            throw new ValidateException(errMsg);
        }
    }

    public static void checkNotEmpty(String value, String errMsg) {
        if (StringUtils.isEmpty(value)) {
            throw new ValidateException(errMsg);
        }
    }

    public static <T> void checkEmpty(Collection<T> list, String errMsg) {
        if (CollectionUtils.notEmpty(list)) {
            throw new ValidateException(errMsg);
        }
    }


    public static boolean fieldChanged(IObjectDescribe objectDescribe, IObjectData dbData, IObjectData data, String fieldName) {
        IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(fieldName);
        if (!data.containsField(fieldName) || Objects.isNull(fieldDescribe)) {
            return false;
        }
        boolean changed;
        if (fieldDescribe instanceof NumberFieldDescribe) {
            BigDecimal value = data.get(fieldName, BigDecimal.class, BigDecimal.ZERO);
            BigDecimal dbValue = dbData.get(fieldName, BigDecimal.class, BigDecimal.ZERO);
            changed = value.compareTo(dbValue) != 0;
        } else if (fieldDescribe instanceof BooleanFieldDescribe) {
            Boolean valueBool = data.get(fieldName, Boolean.class);
            Boolean dbValueBool = dbData.get(fieldName, Boolean.class);
            if (Objects.isNull(valueBool) && Objects.isNull(dbValueBool)) {
                changed = false;
            } else if (Objects.nonNull(valueBool) && Objects.nonNull(dbValueBool)) {
                changed = dbValueBool.booleanValue() != valueBool.booleanValue();
            } else {
                changed = true;
            }
        } else if (fieldDescribe instanceof FileAttachmentFieldDescribe || fieldDescribe instanceof OutEmployeeFieldDescribe) {
            List value = data.get(fieldName, List.class);
            List dbValue = dbData.get(fieldName, List.class);
            if (CollectionUtils.size(value) != CollectionUtils.size(dbValue)) {
                changed = true;
            } else if (CollectionUtils.notEmpty(value)) {
                changed = value.stream().anyMatch(x -> !dbValue.contains(x));
            } else {
                changed = false;
            }
        } else {
            String value = data.get(fieldName, String.class);
            String dbValue = dbData.get(fieldName, String.class);
            if (StringUtils.isEmpty(value) && StringUtils.isEmpty(dbValue)) {
                return false;
            }
            changed = !StringUtils.equals(value, dbValue);
        }
        return changed;
    }

    public static void fieldEditCheck(IObjectDescribe objectDescribe, IObjectData dbData, IObjectData data, String fieldName) {
        if (fieldChanged(objectDescribe, dbData, data, fieldName)) {
            IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(fieldName);
            log.warn("field not support edit,fieldName:{},dbValue:{},newValue:{}", fieldName, dbData.get(fieldName), data.get(fieldName));
            throw new ValidateException(I18N.text(CommonI18NKey.FILED_NOT_SUPPORT_EDIT, fieldDescribe.getLabel()));
        }
    }

    public static void fieldEditCheck(IObjectDescribe objectDescribe, IObjectData dbData, IObjectData data, Collection<String> fieldNames) {
        if (org.springframework.util.CollectionUtils.isEmpty(fieldNames)) {
            return;
        }
        fieldNames.forEach(fieldName -> fieldEditCheck(objectDescribe, dbData, data, fieldName));
    }

    public static <R> R get(String fieldName, IObjectData dbObjectData, IObjectData objectData, Class<R> clazz, R defaultValue) {
        if (objectData.containsField(fieldName)) {
            return objectData.get(fieldName, clazz, defaultValue);
        }
        return dbObjectData.get(fieldName, clazz, defaultValue);
    }

    public static <R> R get(String fieldName, IObjectData dbObjectData, IObjectData objectData, Class<R> clazz) {
        return get(fieldName, dbObjectData, objectData, clazz, null);
    }

}
