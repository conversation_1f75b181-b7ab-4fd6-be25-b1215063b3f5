package com.facishare.crmcommon.util;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.google.common.collect.Lists;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;

public class SearchUtil {

    public static void fillFiltersWithUser(User user, List filters) {
        fillFilterEq(filters, IObjectDescribe.TENANT_ID, user.getTenantId());
    }

    public static void fillFilterEq(List filters, String name, Object value) {
        filters.add(filter(name, Operator.EQ, value));
    }

    public static void fillFilterMasterFieldEq(List filters, String name, Object value) {
        Filter filter = filter(name, Operator.EQ, value);
        filter.setIsMasterField(true);
        filters.add(filter);
    }

    public static void fillFilterEq(List filters, String name, Object value, Integer valueType) {
        Filter filter = filter(name, Operator.EQ, value);
        filter.setValueType(valueType);
        filters.add(filter);
    }

    public static void fillFilterStartWith(List filters, String name, Object value) {
        filters.add(filter(name, Operator.STARTWITH, value));
    }

    public static void fillFilterNotEq(List filters, String name, Object value) {
        filters.add(filter(name, Operator.N, value));
    }

    public static void fillFilterLike(List filters, String name, Object value) {
        filters.add(filter(name, Operator.LIKE, value));
    }

    public static void fillFilterIn(List filters, String name, Object value) {
        filters.add(filter(name, Operator.IN, value));
    }

    public static void fillFilterIn(List filters, String name, Object value, Integer valueType) {
        Filter filter = filter(name, Operator.IN, value);
        filter.setValueType(valueType);
        filters.add(filter);
    }

    public static void fillFilterNotIn(List filters, String name, Object value) {
        filters.add(filter(name, Operator.NIN, value));
    }

    public static void fillFilterHasAnyOf(List filters, String name, Object value) {
        filters.add(filter(name, Operator.HASANYOF, value));
    }

    public static void fillFilterNotNull(List filters, String name) {
        Filter filter = filter(name, Operator.ISN, Lists.newArrayList());
        filters.add(filter);
    }

    public static void fillFilterNotIn(List filters, String name, String fieldValueType, Object value) {
        Filter filter = filter(name, Operator.NIN, value);
        filter.setFieldValueType(fieldValueType);
        filters.add(filter);
    }

    public static void fillFilterGTE(List filters, String name, Object value) {
        filters.add(filter(name, Operator.GTE, value));
    }

    public static void fillFilterGT(List filters, String name, Object value) {
        filters.add(filter(name, Operator.GT, value));
    }

    public static void fillFilterLT(List filters, String name, Object value) {
        filters.add(filter(name, Operator.LT, value));
    }

    public static void fillFilterLTE(List filters, String name, Object value) {
        filters.add(filter(name, Operator.LTE, value));
    }

    public static void fillFilterIsNull(List filters, String name) {
        filters.add(filter(name, Operator.IS, Lists.newArrayList()));
    }

    public static void fillFilterMatch(List filters, String name, Object value) {
        filters.add(filter(name, Operator.MATCH, value));
    }

    public static void fillFilterBySql(List filters, String name, Operator operator, Object value) {
        Filter filter = filter(name, operator, value);
        filter.setFieldValueType("sql");//支持sql方式嵌套
        filters.add(filter);
    }

    public static Filter filter(String name, Operator operator, Object value) {
        Filter filter = new Filter();
        filter.setFieldName(name);
        if (value instanceof List) {
            filter.setFieldValues((List<String>) value);
        } else {
            filter.setFieldValues(Arrays.asList(value.toString()));
        }
        filter.setOperator(operator);
        return filter;
    }

    public static void fillOrderBy(List orders, String name, Boolean isAsc) {
        orders.add(order(name, isAsc));
    }

    public static void fillOrderBy(List orders, String name, String value, Boolean isAsc) {
        orders.add(order(name, value, isAsc));
    }

    public static OrderBy order(String name, Boolean isAsc) {
        OrderBy orderBy = new OrderBy();
        orderBy.setFieldName(name);
        orderBy.setIsAsc(isAsc);
        return orderBy;
    }

    public static OrderBy order(String name, String value, Boolean isAsc) {
        OrderBy orderBy = new OrderBy();
        orderBy.setFieldName(name);
        orderBy.setValue(value);
        orderBy.setIsAsc(isAsc);
        return orderBy;
    }

    public static Filter idFilter(Collection<String> ids) {
        Filter filter = new Filter();
        filter.setFieldName("_id");
        filter.setOperator(Operator.IN);
        filter.setFieldValues(Lists.newArrayList(ids));
        return filter;
    }
}
