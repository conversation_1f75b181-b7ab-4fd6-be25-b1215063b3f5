package com.facishare.crmcommon.util;

import com.github.autoconf.ConfigFactory;

/**
 * Created by xujf on 2017/10/16.
 */
public class ConfigCenter {

    /**
     * 元数据查询:最大分页大小
     */
    public static Integer METADATA_MAX_QUERY_LIMIT = 1000;
    /**
     * 元数据查询:查询所有数据时的分页大小
     */
    public static Integer METADATA_ALL_QUERY_LIMIT = 100000;

    /**
     * RedisLock Expire Time
     */
    public static Integer APPROVAL_FLOW_REQUEST_ID_LOCK_TTL = 15;

    /**
     * 客户账户消费"客户","回款明细"mq, 幂等需要，eventId放在redis的时间
     * 10分钟
     */
    public static Integer CUSTOMER_ACCOUNT_CONSUME_OBJECT_DATA_LOCK_TTL = 10 * 60;

    /**
     * 出库单与发货单批量导入最大数量
     */
    public static Integer IMPORT_DATA_MAX_NUM = 200;

    public static String channelDistributionAppId;

    static {
        ConfigFactory.getInstance().getConfig("fs-crm-deliverynote", config -> {
            METADATA_MAX_QUERY_LIMIT = config.getInt("METADATA_MAX_QUERY_LIMIT", METADATA_MAX_QUERY_LIMIT);
            METADATA_ALL_QUERY_LIMIT = config.getInt("METADATA_ALL_QUERY_LIMIT", METADATA_ALL_QUERY_LIMIT);
        });

        ConfigFactory.getConfig("fs-sail-provider", config -> {
            channelDistributionAppId = config.get("channelDistributionAppId");
        });
    }

    static {
        ConfigFactory.getInstance().getConfig("fs-crm-stock", config -> {
            APPROVAL_FLOW_REQUEST_ID_LOCK_TTL = config.getInt("APPROVAL_FLOW_REQUEST_ID_LOCK_TTL", APPROVAL_FLOW_REQUEST_ID_LOCK_TTL);
            CUSTOMER_ACCOUNT_CONSUME_OBJECT_DATA_LOCK_TTL = config.getInt("CUSTOMER_ACCOUNT_CONSUME_OBJECT_DATA_LOCK_TTL", CUSTOMER_ACCOUNT_CONSUME_OBJECT_DATA_LOCK_TTL);
        });
    }
}
