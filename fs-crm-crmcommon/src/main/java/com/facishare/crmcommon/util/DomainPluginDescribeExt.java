package com.facishare.crmcommon.util;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.domain.SimpleDomainPluginDescribe;
import com.facishare.paas.appframework.metadata.repository.model.DomainPluginParam;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * Created by zhaopx on 2021/10/29
 *
 * SFA拷贝过来 com.facishare.crm.util.DomainPluginDescribeExt
 */
@Slf4j
public class DomainPluginDescribeExt {
    private final String objectApiName;
    private String defaultDetailObjectApiName;
    private String domainApiName;
    private final Map<String, String> objectFieldMapping;
    private final Map<String, String> defaultDetailFieldMapping;
    private final Map<String, Map<String, String>> detailFieldMapping;
    private final Set<String> detailObjectApiNames = Sets.newHashSet();
    private Map<String, Object> customParam;

    private DomainPluginDescribeExt(String objectApiName, SimpleDomainPluginDescribe describe) {
        objectFieldMapping = Maps.newHashMap();
        defaultDetailFieldMapping = Maps.newHashMap();
        detailFieldMapping = Maps.newHashMap();
        this.objectApiName = objectApiName;
        domainApiName = describe.getApiName();
        DomainPluginParam params = describe.getParams();
        if (params == null) return;

        if (!Objects.isNull(params.getFieldMapping())) {
            params.getFieldMapping().forEach(objectFieldMapping::put);
        }

        if (CollectionUtils.empty(params.getDetails())) return;

        defaultDetailObjectApiName = params.getDetails().get(0).getObjectApiName();
        params.getDetails().get(0).getFieldMapping().forEach(defaultDetailFieldMapping::put);

        params.getDetails().forEach(v1 -> {
            Map<String, String> tmp = Maps.newHashMap();
            v1.getFieldMapping().forEach(tmp::put);
            detailFieldMapping.put(v1.getObjectApiName(), tmp);
            detailObjectApiNames.add(v1.getObjectApiName());
            customParam = v1.getCustomParam();
        });
    }

    private DomainPluginDescribeExt(String objectApiName, DomainPluginParam param) {
        objectFieldMapping = Maps.newHashMap();
        defaultDetailFieldMapping = Maps.newHashMap();
        detailFieldMapping = Maps.newHashMap();
        this.objectApiName = objectApiName;

        param.getFieldMapping().forEach(objectFieldMapping::put);

        if (CollectionUtils.empty(param.getDetails())) return;

        defaultDetailObjectApiName = param.getDetails().get(0).getObjectApiName();
        param.getDetails().get(0).getFieldMapping().forEach(defaultDetailFieldMapping::put);

        param.getDetails().forEach(v1 -> {
            Map<String, String> tmp = Maps.newHashMap();
            v1.getFieldMapping().forEach(tmp::put);
            detailFieldMapping.put(v1.getObjectApiName(), tmp);
            customParam = v1.getCustomParam();
        });
    }

    private DomainPluginDescribeExt(String objectApiName, Field[] masterFields, String detailObjectApiName, Field[]
            detailFields) {
        objectFieldMapping = Maps.newHashMap();
        defaultDetailFieldMapping = Maps.newHashMap();
        detailFieldMapping = Maps.newHashMap();
        this.objectApiName = objectApiName;
        this.defaultDetailObjectApiName = detailObjectApiName;
        try {
            if (masterFields != null) {
                for (Field field : masterFields) {
                    objectFieldMapping.put(field.get(null).toString(), field.get(null).toString());
                }
            }
            if (detailFields != null) {
                for (Field field : detailFields) {
                    defaultDetailFieldMapping.put(field.get(null).toString(), field.get(null).toString());
                }
            }
        } catch (Exception ignored) {

        }
    }

    private DomainPluginDescribeExt(String objectApiName, Map<String, String> masterFields, String detailObjectApiName, Map<String, String>
            detailFields) {
        objectFieldMapping = Maps.newHashMap();
        defaultDetailFieldMapping = Maps.newHashMap();
        detailFieldMapping = Maps.newHashMap();
        this.objectApiName = objectApiName;
        this.defaultDetailObjectApiName = detailObjectApiName;
        try {
            if (CollectionUtils.notEmpty(masterFields)) {
                masterFields.forEach(objectFieldMapping::put);
            }
            if (CollectionUtils.notEmpty(detailFields)) {
                detailFields.forEach(defaultDetailFieldMapping::put);
            }
        } catch (Exception ignored) {

        }
    }

    public static DomainPluginDescribeExt of(String objectApiName, SimpleDomainPluginDescribe describe) {
        if (describe == null) {
            return null;
        }
        return new DomainPluginDescribeExt(objectApiName, describe);
    }

    public static DomainPluginDescribeExt of(String objectApiName, DomainPluginParam param) {
        return new DomainPluginDescribeExt(objectApiName, param);
    }

    public static DomainPluginDescribeExt of(String objectApiName, Field[] masterFields, String
            detailObjectApiName, Field[] detailFields) {
        return new DomainPluginDescribeExt(objectApiName, masterFields, detailObjectApiName, detailFields);
    }

    public static DomainPluginDescribeExt of(String objectApiName, Map<String, String> masterFields, String detailObjectApiName, Map<String, String>
            detailFields) {
        return new DomainPluginDescribeExt(objectApiName, masterFields, detailObjectApiName, detailFields);
    }

    public String getObjectApiName() {
        return objectApiName;
    }

    public String getDefaultDetailObjectApiName() {
        return defaultDetailObjectApiName;
    }

    public String getFieldApiName(String fieldKey) {
        return objectFieldMapping.get(fieldKey);
    }

    public String getDefaultDetailFieldApiName(String fieldKey) {
        return defaultDetailFieldMapping.get(fieldKey);
    }

    public String getDefaultObjectFieldApiName(String fieldKey) {
        return objectFieldMapping.get(fieldKey);
    }


    public Map<String, String> getDefaultDetailFieldMap() {
        return defaultDetailFieldMapping;
    }

    public Map<String, Object> getCustomParam() {
        return customParam;
    }

    public String getDetailFieldApiName(String detailApiName, String key) {
        return detailFieldMapping.get(detailApiName).get(key);
    }

    public Set<String> getDetailObjectApiName(){
        return detailObjectApiNames ;
    }

}
