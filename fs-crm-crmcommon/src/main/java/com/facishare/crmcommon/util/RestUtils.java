package com.facishare.crmcommon.util;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/8/15
 */
public class RestUtils {
    public static Map<String, String> getHeaders(String tenantId, String userId) {
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("x-fs-ei", tenantId);
        headers.put("x-fs-userInfo", userId);
        headers.put("Expect", "100-continue");
        return headers;
    }
}
