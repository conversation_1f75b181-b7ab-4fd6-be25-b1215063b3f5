package com.facishare.crmcommon.util;

import com.google.common.base.Joiner;
import com.google.common.base.Preconditions;
import java.util.List;
import java.util.Objects;

public class SqlUtil {
    public static String getInString(List<String> conditions) {
        Preconditions.checkArgument(Objects.nonNull(conditions) && conditions.size() > 0);
        return "'" + Joiner.on("','").join(conditions) + "'";
    }
}