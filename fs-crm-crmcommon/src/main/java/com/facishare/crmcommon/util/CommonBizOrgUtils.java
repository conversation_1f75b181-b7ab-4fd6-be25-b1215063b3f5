package com.facishare.crmcommon.util;

import com.alibaba.druid.util.StringUtils;
import com.facishare.crmcommon.rest.PaasUserGroupProxy;
import com.facishare.crmcommon.rest.PaasUserRoleProxy;
import com.facishare.crmcommon.rest.dto.PAASContext;
import com.facishare.crmcommon.rest.dto.UserGroupModel;
import com.facishare.crmcommon.rest.dto.UserRoleModel;
import com.facishare.paas.appframework.common.service.dto.*;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
public class CommonBizOrgUtils {
    private static final ServiceFacade serviceFacade = SpringUtil.getContext().getBean(ServiceFacade.class);
    private static final PaasUserGroupProxy paasUserGroupProxy = SpringUtil.getContext().getBean(PaasUserGroupProxy.class);
    private static final PaasUserRoleProxy paasUserRoleProxy = SpringUtil.getContext().getBean(PaasUserRoleProxy.class);

    private static final String CRM_APP_ID = "CRM";
    private static final String ALL_COMPANY_ID = "999999";

    public static List<String> getMembersByDeptIds(String tenantId, List<String> deptIds, boolean excludeStopUser) {
        if(CollectionUtils.empty(deptIds)){
            return Lists.newArrayList();
        }
        List<String> membersByDeptIds;
        try {
            if(excludeStopUser) {
                membersByDeptIds = serviceFacade.getMembersByDeptIds(buildUser(tenantId), deptIds,0);
            }else {
                membersByDeptIds = serviceFacade.getMembersByDeptIds(buildUser(tenantId), deptIds);
            }
        } catch (Exception e) {
            log.error("find ObjectFollowDealSettingObj throw exception {},tenantId:{},", e.getMessage(), tenantId);
            throw new RuntimeException();
        }
        return membersByDeptIds;
    }

    public static String getMainDepartment(String tenantId, String userId) {
        Map<String, QueryDeptInfoByUserIds.MainDeptInfo> deptInfo = serviceFacade.getMainDeptInfo(tenantId, User.SUPPER_ADMIN_USER_ID, Lists.newArrayList(userId));
        if (deptInfo.get(userId) == null) {
            log.warn("deptInfo is empty :{},userId:{}", tenantId, userId);
        }
        if (deptInfo.get(userId) != null) {
            return deptInfo.get(userId).getDeptId();
        }
        return null;
    }

    public static List<String> getDepartmentByUserId(String tenantId, String userId) {
        List<QueryDeptInfoByUserId.DeptInfo> deptInfos = serviceFacade.getDeptInfoByUserId(tenantId, User.SUPPER_ADMIN_USER_ID, userId);
        List<String> result = deptInfos.stream().map(x -> x.getDeptId()).collect(Collectors.toList());
        return result;
    }

    public static List<String> getAllSuperDepartmentByIds(String tenantId, List<String> departmentIds) {
        Map<String, List<QueryAllSuperDeptsByDeptIds.DeptInfo>> deptInfosMap = serviceFacade.getAllSuperDeptsByDeptIds(tenantId, User.SUPPER_ADMIN_USER_ID, departmentIds);
        Set<String> result = Sets.newHashSet();
        for(Map.Entry<String, List<QueryAllSuperDeptsByDeptIds.DeptInfo>> keyValue : deptInfosMap.entrySet()){
            result.addAll(keyValue.getValue().stream().filter(x-> !ALL_COMPANY_ID.equals(x.getId())).map(x -> x.getId()).collect(Collectors.toList()));
        }

        return Lists.newArrayList(result);
    }

    public static List<String> getAllSubDepartmentByIds(String tenantId, List<String> departmentIds) {
        Map<String, List<QueryDeptByName.DeptInfo>> deptInfosMap = serviceFacade.getSubDeptsByDeptIds(tenantId, User.SUPPER_ADMIN_USER_ID, departmentIds, 0);
        Set<String> result = Sets.newHashSet();
        for(Map.Entry<String, List<QueryDeptByName.DeptInfo>> keyValue : deptInfosMap.entrySet()){
            result.addAll(keyValue.getValue().stream().map(x -> x.getId()).collect(Collectors.toList()));
        }

        return Lists.newArrayList(result);
    }

    public static Map<String, String> getDepartmentNameByIds(String tenantId, List<String> deptIds) {
        Map<String, String> result = Maps.newHashMap();
        List<QueryDeptInfoByDeptIds.DeptInfo> deptInfoList = serviceFacade.getDeptInfoNameByIds(tenantId, User.SUPPER_ADMIN_USER_ID, deptIds);
        if(CollectionUtils.empty(deptInfoList)) {
            return result;
        }
        deptInfoList.forEach(x -> result.put(x.getDeptId(), x.getDeptName()));
        return result;
    }

    public static Map<String, String> getUserName(String tenantId, List<String> userIds) {
        Map<String, String> result = serviceFacade.getUserNameMapByIds(tenantId, User.SUPPER_ADMIN_USER_ID, userIds);
        return result;
    }

    public static Map<String, List<String>> getMembersByUserGroupIds(String tenantId, List<String> userGroupIds) {
        if(CollectionUtils.empty(userGroupIds)){
            return Maps.newHashMap();
        }
        Map<String, List<String>> members;
        try {
            PAASContext contextArg = buildPAASContext(tenantId);
            UserGroupModel.UserGroupListWithContextArg arg = UserGroupModel.UserGroupListWithContextArg.builder().context(contextArg)
                    .groupIdList(userGroupIds).filterByUser(false).publicFlag(true).status(0).build();
            UserGroupModel.UserGroupListResult rstResult = paasUserGroupProxy.getUserGroupMembers(ProxyUtils.getCrmHeader(tenantId), arg);
            members = rstResult.getGroupMems();
        } catch (Exception e) {
            log.error("getMembersByUserGroupIds throw exception {},tenantId:{}, userGroupIds:{}", e.getMessage(), tenantId, userGroupIds);
            throw new RuntimeException();
        }
        return members;
    }

    public static List<String> getUserGroupIdsByMemberId(String tenantId, String memberId) {
        if(StringUtils.isEmpty(memberId)){
            return Lists.newArrayList();
        }
        List<String> userGroups;
        try {
            UserGroupModel.UserGroupPageInfo pageInfo = UserGroupModel.UserGroupPageInfo.builder()
                    .currentPage(0).pageSize(0).total(0).totalPage(0).build();
            UserGroupModel.UserGroupUserListArg arg = UserGroupModel.UserGroupUserListArg.builder().appId(CRM_APP_ID)
                    .filterByUser(false).isPublic(true).status(0).userId(User.SUPPER_ADMIN_USER_ID)
                    .userIdList(Lists.newArrayList(memberId)).tenantId(tenantId).page(null).build();
            UserGroupModel.UserGroupListPageResult rstResult = paasUserGroupProxy.getUserGroupByMemberIds(ProxyUtils.getCrmHeader(tenantId), arg);
            userGroups = rstResult.getResult();
        } catch (Exception e) {
            log.error("getUserGroupIdsByMemberId throw exception {},tenantId:{}, memberId:{}", e.getMessage(), tenantId, memberId);
            throw new RuntimeException();
        }
        return userGroups;
    }

    public static List<UserGroupModel.UserGroupPoto> getUserGroupByIds(String tenantId, List<String> userGroupIds) {
        if(CollectionUtils.empty(userGroupIds)){
            return Lists.newArrayList();
        }
        List<UserGroupModel.UserGroupPoto> userGroups;
        try {
            UserGroupModel.UserGroupPageInfo pageInfo = UserGroupModel.UserGroupPageInfo.builder()
                    .currentPage(0).pageSize(0).total(0).totalPage(0).build();
            UserGroupModel.UserGroupListArg arg = UserGroupModel.UserGroupListArg.builder().appId(CRM_APP_ID)
                    .filterByUser(false).isPublic(true).userId(User.SUPPER_ADMIN_USER_ID)
                    .groupIdList(userGroupIds).tenantId(tenantId).page(null).build();
            UserGroupModel.UserGroupPageResult rstResult = paasUserGroupProxy.getUserGroupByIds(ProxyUtils.getCrmHeader(tenantId), arg);
            userGroups = rstResult.getResult();
        } catch (Exception e) {
            log.error("getUserGroupIdsByMemberId throw exception {},tenantId:{}, userGroupIds:{}", e.getMessage(), tenantId, userGroupIds);
            throw new RuntimeException();
        }
        return userGroups;
    }

    public static Map<String, String> getUserGroupName(String tenantId, List<String> userGroupIds) {
        Map<String, String> result = Maps.newHashMap();;
        if(CollectionUtils.empty(userGroupIds)){
            return result;
        }
        List<UserGroupModel.UserGroupPoto> userGroups = getUserGroupByIds(tenantId, userGroupIds);
        userGroups.forEach(x -> result.put(x.getId(), x.getName()));
        return result;
    }

    public static List<String> batchGetMembersByUserGroupIds(String tenantId, List<String> userGroupIds) {
        if(CollectionUtils.empty(userGroupIds)){
            return Lists.newArrayList();
        }
        List<String> members;
        try {
            UserGroupModel.GetUserGroupMembersArg arg = UserGroupModel.GetUserGroupMembersArg.builder().appId(CRM_APP_ID)
                    .userId(User.SUPPER_ADMIN_USER_ID).groupIdList(userGroupIds).tenantId(tenantId).build();
            UserGroupModel.GetUserGroupMembersResult rstResult = paasUserGroupProxy.batchGetUserGroupMembers(ProxyUtils.getCrmHeader(tenantId), arg);
            members = rstResult.getResult();
        } catch (Exception e) {
            log.error("getUserGroupIdsByMemberId throw exception {},tenantId:{}, userGroupIds:{}", e.getMessage(), tenantId, userGroupIds);
            throw new RuntimeException();
        }
        return members;
    }

    public static Map<String,List<String>> getRoleUsersByRoleIds(String tenantId, List<String> userRoleIds) {
        if(CollectionUtils.empty(userRoleIds)){
            return Maps.newHashMap();
        }
        Map<String, List<String>> members;
        try {
            PAASContext contextArg = buildPAASContext(tenantId);
            UserRoleModel.UserRoleListWithContextArg arg = UserRoleModel.UserRoleListWithContextArg.builder().authContext(contextArg)
                    .roles(userRoleIds).build();
            UserRoleModel.UserRoleMapResult rstResult = paasUserRoleProxy.queryRoleUsersByRoles(ProxyUtils.getCrmHeader(tenantId), arg);
            members = rstResult.getResult();
        } catch (Exception e) {
            log.error("getRoleUsersByRoleIds throw exception {},tenantId:{}, userRoleIds:{}", e.getMessage(), tenantId, userRoleIds);
            throw new RuntimeException();
        }
        return members;
    }

    public static List<String> batchGetRoleUsersByRoleIds(String tenantId, List<String> userRoleIds) {
        if(CollectionUtils.empty(userRoleIds)){
            return Lists.newArrayList();
        }

        Map<String,List<String>> result = getRoleUsersByRoleIds(tenantId, userRoleIds);

        Set<String> members = Sets.newHashSet();
        for(Map.Entry<String, List<String>> keyValue : result.entrySet()){
            members.addAll(keyValue.getValue());
        }

        return Lists.newArrayList(members);
    }

    public static Map<String,List<String>> getUserRoleIdsByUserIds(String tenantId, List<String> userIds) {
        if(CollectionUtils.empty(userIds)){
            return Maps.newHashMap();
        }
        Map<String, List<String>> members;
        try {
            PAASContext contextArg = buildPAASContext(tenantId);
            UserRoleModel.UserRoleUsersWithContextArg arg = UserRoleModel.UserRoleUsersWithContextArg.builder().authContext(contextArg)
                    .users(userIds).build();
            UserRoleModel.UserRoleMapResult rstResult = paasUserRoleProxy.queryUserRoleCodesByUsers(ProxyUtils.getCrmHeader(tenantId), arg);
            members = rstResult.getResult();
        } catch (Exception e) {
            log.error("getUserRoleIdsByUserIds throw exception {},tenantId:{}, userIds:{}", e.getMessage(), tenantId, userIds);
            throw new RuntimeException();
        }
        return members;
    }

    public static List<String> getUserRoleIdsByUserId(String tenantId, String userId) {
        if(StringUtils.isEmpty(userId)){
            return Lists.newArrayList();
        }
        List<String> members = Lists.newArrayList();
        try {
            PAASContext contextArg = buildPAASContext(tenantId);
            UserRoleModel.UserRoleUsersWithContextArg arg = UserRoleModel.UserRoleUsersWithContextArg.builder().authContext(contextArg)
                    .users(Lists.newArrayList(userId)).build();
            UserRoleModel.UserRoleMapResult rstResult = paasUserRoleProxy.queryUserRoleCodesByUsers(ProxyUtils.getCrmHeader(tenantId), arg);

            if(rstResult.getResult().containsKey(userId)){
                members.addAll(rstResult.getResult().getOrDefault(userId, Lists.newArrayList()));
            }
        } catch (Exception e) {
            log.error("getUserRoleIdsByUserIds throw exception {},tenantId:{}, userId:{}", e.getMessage(), tenantId, userId);
            throw new RuntimeException();
        }
        return members;
    }

    private static User buildUser(String tenantId) {
        return new User(tenantId, User.SUPPER_ADMIN_USER_ID, "","","");
    }

    private static PAASContext buildPAASContext(String tenantId) {
        PAASContext context = PAASContext.builder().appId(CRM_APP_ID)
                .tenantId(tenantId).userId(User.SUPPER_ADMIN_USER_ID).build();
        return context;
    }
}
