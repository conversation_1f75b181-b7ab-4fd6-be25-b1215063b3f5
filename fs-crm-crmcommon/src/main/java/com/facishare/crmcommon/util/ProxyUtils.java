package com.facishare.crmcommon.util;

import com.facishare.paas.appframework.core.model.User;
import com.google.common.collect.Maps;

import java.util.Map;

public class ProxyUtils {
    private static final String CRM_APP_ID = "CRM";

    public static Map<String, String> getCrmHeader(String tenantId) {
        return getCrmHeader(tenantId, User.SUPPER_ADMIN_USER_ID, CRM_APP_ID, "", "");
    }

    public static Map<String, String> getCrmHeader(String tenantId, String userId, String appId, String outTenantId, String outUserId) {
        Map<String, String> headers = Maps.newHashMap();
        headers.put("x-fs-ei", tenantId);
        headers.put("x-fs-userInfo", userId);
        headers.put("x-app-id", appId);
        headers.put("x-out-tenant-id", outTenantId);
        headers.put("x-out-user-id", outUserId);
        return headers;
    }
}
