package com.facishare.crmcommon.util;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import de.lab4inf.math.util.Strings;
import org.apache.commons.lang3.RandomStringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class ObjectFieldUtil {

    /**
     * 参考 com.facishare.paas.appframework.metadata.FieldDescribeExt#generateTargetRelatedListName
     */
    public static String generateFieldApiName() {
        return String.format("field_%s__c", RandomStringUtils.randomAlphanumeric(5));
    }

    public static List<String> generateFieldApiName(int number, IObjectDescribe describe) {
        List<String> newFieldApiNames = Lists.newArrayList();

        List<String> existFieldApiNames = describe.getFieldDescribes().stream().map(IFieldDescribe::getLabel).collect(Collectors.toList());

        for (int i = 0; i < number; ) {
            String fieldApiName = generateFieldApiName();
            if (!existFieldApiNames.contains(fieldApiName) && !newFieldApiNames.contains(fieldApiName)) {
                i++;
                newFieldApiNames.add(fieldApiName);
            }
        }

        return newFieldApiNames;
    }


    /**
     * 获取不存在的FieldApiName
     */
    public static List<String> getNotExistFieldApiNames(IObjectDescribe objectDescribe, List<String> fieldApiNames) {
        if (CollectionUtils.empty(fieldApiNames)) {
            return Lists.newArrayList();
        }

        List<String> existFieldApiNames = objectDescribe.getFieldDescribes().stream().map(IFieldDescribe::getApiName).collect(Collectors.toList());

        return fieldApiNames.stream().filter(f -> !existFieldApiNames.contains(f)).collect(Collectors.toList());
    }

    /**
     * 获取不是fieldType的FieldApiName
     */
    public static List<String> getNotFieldTypeFieldApiNames(IObjectDescribe objectDescribe, List<String> fieldApiNames, String fieldType) {
        if (CollectionUtils.empty(fieldApiNames)) {
            return Lists.newArrayList();
        }

        return objectDescribe.getFieldDescribes().stream()
                .filter(f -> fieldApiNames.contains(f.getApiName()))
                .filter(f -> !Objects.equals(fieldType, f.getType()))
                .map(IFieldDescribe::getApiName).collect(Collectors.toList());
    }


    /**
     * 是否存在fieldLabel
     */
    public static boolean hasFieldLabel(IObjectDescribe objectDescribe, String fieldLabel) {
        if (Strings.isNullOrEmpty(fieldLabel)) {
            return false;
        }

        for (IFieldDescribe fieldDescribe : objectDescribe.getFieldDescribes()) {
            if (Objects.equals(fieldDescribe.getLabel(), fieldLabel)) {
                return true;
            }
        }

        return false;
    }
}