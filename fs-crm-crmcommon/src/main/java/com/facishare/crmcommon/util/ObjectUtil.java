package com.facishare.crmcommon.util;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 21/03/2018
 */
@Slf4j
public class ObjectUtil {

    //http://wiki.firstshare.cn/pages/viewpage.action?pageId=59344930
    public static Map<String, Object> buildConfigMap() {
        Map<String, Object> configMap = Maps.newHashMap();
        //
        // Map<String, Object> recordTypeConfigMap = Maps.newHashMap();
        // recordTypeConfigMap.put("add", 0);
        // recordTypeConfigMap.put("assign", 0);
        // configMap.put("record_type", recordTypeConfigMap);

        return configMap;
    }

    public static Map<String, Object> buildConfigMap(boolean isNotModifyField,
                                                     boolean isNotModifyLayout,
                                                     boolean isNotModifyRecordType,
                                                     boolean isNotModifyCascade,
                                                     boolean isNotModifyEdit) {
        Map<String, Object> configMap = Maps.newHashMap();
/*
        if (isNotModifyField) {
            Map<String, Object> fieldsConfigMap = Maps.newHashMap();
            fieldsConfigMap.put("add", 0);
            configMap.put("fields", fieldsConfigMap);
        }

        if (isNotModifyLayout) {
            Map<String, Object> layoutConfigMap = Maps.newHashMap();
            layoutConfigMap.put("add", 0);
            layoutConfigMap.put("assign", 0);
            configMap.put("layout", layoutConfigMap);
        }

        if (isNotModifyRecordType) {
            Map<String, Object> recordTypeConfigMap = Maps.newHashMap();
            recordTypeConfigMap.put("add", 0);
            recordTypeConfigMap.put("assign", 0);
            configMap.put("record_type", recordTypeConfigMap);
        }

        if (isNotModifyCascade) {
            Map<String, Object> cascadeConfigMap = Maps.newHashMap();
            cascadeConfigMap.put("add", 0);
            configMap.put("cascade", cascadeConfigMap);
        }

        if (isNotModifyEdit) {
            configMap.put("edit", 0);
        }*/
        return configMap;
    }

    public static Map<String, Object> buildFieldOptionConfigMap() {
        Map<String, Object> configMap = Maps.newHashMap();

        configMap.put("edit", 1);
        configMap.put("remove", 0);
        configMap.put("enable", 0);

        return configMap;
    }

    public static String getStringValue(Map<String, Object> dataMap, String key, String defaultValue) {
        if(dataMap.containsKey(key)){
            Object objectValue =  dataMap.get(key);
            if(objectValue == null){
                return defaultValue;
            }
            return objectValue.toString();
        } else {
            return  defaultValue;
        }
    }

    public static Long getLongValue(Map<String, Object> dataMap, String key, Long defaultValue) {
        String longStringValue = getStringValue(dataMap, key, String.valueOf(defaultValue));
        try{
            Long result = Long.valueOf(longStringValue);
            return result;
        } catch (Exception e){
            return  defaultValue;
        }
    }

    public static Integer getIntegerValue(Map<String, Object> dataMap, String key, Integer defaultValue) {
        String integerStringValue = getStringValue(dataMap, key, String.valueOf(defaultValue));
        try{
            Integer result = Integer.valueOf(integerStringValue);
            return result;
        } catch (Exception e){
            return  defaultValue;
        }
    }

    public static boolean getBooleanValue(Map<String, Object> dataMap, String key, boolean defaultValue) {
        String boolStringValue = getStringValue(dataMap, key, String.valueOf(defaultValue));
        try{
            Boolean result = Boolean.valueOf(boolStringValue);
            return result;
        } catch (Exception e){
            return  defaultValue;
        }
    }

}
