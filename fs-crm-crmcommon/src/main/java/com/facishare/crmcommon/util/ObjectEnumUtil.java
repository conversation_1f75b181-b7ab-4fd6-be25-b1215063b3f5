package com.facishare.crmcommon.util;

import com.facishare.crmcommon.enums.ObjectTypeEnum;

public class ObjectEnumUtil {
    public static ObjectTypeEnum getObjectEnum(String api_name){
        ObjectTypeEnum rst = ObjectTypeEnum.None;
        switch (api_name){
            case "LeadsObj":
                rst =  ObjectTypeEnum.LeadsObj;
                break;
            case "AccountObj":
                rst =  ObjectTypeEnum.AccountObj;
                break;
            case "ContactObj":
                rst =  ObjectTypeEnum.ContactObj;
                break;
        }
        return rst;
    }
}
