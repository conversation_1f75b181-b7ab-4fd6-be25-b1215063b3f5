package com.facishare.crmcommon.enums;

import java.util.Objects;

public enum MetaDataOpEnum {
    CREATE("i", "CREATE"),
    UPDATE("u", "UPDATE"),
    DELETE("D", "DELETE"),
    INVALID("invalid", "INVALID"),
    RECOVER("recover", "RECOVER"),
    ;

    private final String value;
    private final String label;

    MetaDataOpEnum(String type, String label) {
        this.value = type;
        this.label = label;
    }

    public static MetaDataOpEnum getByCode(String status) {
        for (MetaDataOpEnum srcType : values()) {
            if (Objects.equals(status, srcType.value)) {
                return srcType;
            }
        }
        throw new IllegalArgumentException("status error");
    }

    public String getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }
}