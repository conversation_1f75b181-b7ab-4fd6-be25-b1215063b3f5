package com.facishare.crmcommon.enums;

public enum ObjectTypeEnum {
    None("",-1),
    Leads<PERSON>bj("LeadsObj",1),
    AccountObj("AccountObj",2),
    ContactObj("ContactObj",3);

    private String api_name;
    private Integer configType;
    ObjectTypeEnum(String api_name,Integer configType){
        this.api_name = api_name;
        this.configType = configType;
    }

    public String getApi_name(){
        return this.api_name;
    }
    public Integer getConfigType(){
        return configType;
    }
}
