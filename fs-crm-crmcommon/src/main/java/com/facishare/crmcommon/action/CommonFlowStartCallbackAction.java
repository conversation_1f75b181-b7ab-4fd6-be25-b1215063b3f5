package com.facishare.crmcommon.action;

import com.facishare.paas.appframework.core.predef.action.StandardFlowStartCallbackAction;

/**
 * <AUTHOR>
 * @date 22/05/2018
 */
public class CommonFlowStartCallbackAction extends StandardFlowStartCallbackAction {

    @Override
    protected Result doAct(Arg arg) {
        if (!arg.isTriggerSynchronous()) {
            return super.doAct(arg);
        } else {
            return new Result(true);
        }

    }
}
