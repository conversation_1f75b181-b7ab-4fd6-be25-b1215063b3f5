package com.facishare.crmcommon.constants;

/**
 * <AUTHOR>
 * @date 2019/3/13
 * @IgnoreI18nFile
 */
public interface CommonSalesOrderConstants {
    String API_NAME = "SalesOrderObj";

    enum Field {
        Name("name", "销售订单编号"),
        Account("account_id", "客户名称"),
        Warehouse("shipping_warehouse_id", "订货仓库"),
        OrderAmount("order_amount", "销售订单金额"),
        CreateTime("create_time", "创建时间"),
        OrderStatus("order_status", "状态"),
        ConfirmReceiveTime("confirmed_receive_date", "收货时间"),
        ConfirmDeliveryTime("confirmed_delivery_date", "发货时间"),
        LogisticsStatus("logistics_status", "发货状态"),
        Resource("resource", "来源"),
        OrderMode("order_mode", "订货模式"),
        NoArTagAmount("no_ar_tag_amount", "待立应收金额"),
        ArTagAmount("ar_tag_amount", "已立应收金额"),
        ActualRefundedAmount("actual_refunded_amount", "实际退款金额"),
        PayType("pay_type", "支付方式"),

        RECEIVABLE_AMOUNT("receivable_amount", "待回款金额"),
        ;

        public String apiName;
        public String label;


        Field(String apiName, String label) {
            this.apiName = apiName;
            this.label = label;
        }
    }
}
