package com.facishare.crmcommon.constants;

/**
 * @IgnoreI18nFile
 */
public interface AccountsReceivableNoteObjConstants {
    String API_NAME = "AccountsReceivableNoteObj";
    String DISPLAY_NAME = "应收单";
    String DEFAULT_LAYOUT_API_NAME = "AccountsReceivableNoteObj_default_layout__c";
    String DEFAULT_LAYOUT_DISPLAY_NAME = "默认布局";
    String LIST_LAYOUT_API_NAME = "AccountsReceivableNoteObj_list_layout__c";
    String LIST_LAYOUT_DISPLAY_NAME = "移动端默认列表页";

    String STORE_TABLE_NAME = "accounts_receivable_note";
    int ICON_INDEX = 18;//待定
    String LIFE_STATUS_HELP_TEXT = "";//待定

    enum Field {
        Name("name", "应收单编号"),

        AccountId("account_id", "客户名称", "target_related_list_accounts_receivable_note_account", "应收单"),

        PriceTaxTotalAmount("price_tax_total_amount", "价税合计"),
        CalculateTaxMethod("calculate_tax_method", "计税方式"),

        NoteDate("note_date", "单据日期"),
        DueDate("due_date", "到期日期"),

        OverdueDays("overdue_days", "逾期天数"),
        IsOverdue("is_overdue", "是否逾期"),

        SettledAmount("settled_amount", "已结算金额"),
        NoSettledAmount("no_settled_amount", "待结算金额"),

        VirThisMatchAmount("virThisMatchAmount", "本次核销金额"),

        SettledStatus("settled_status", "结算状态"),

        InvoicedAmount("invoiced_amount", "已开票金额"),
        NoInvoiceAmount("no_invoice_amount", "待开票金额"),

        Attachment("attachment", "附件"),

        Remarks("remarks", "备注"),

        OpeningBalance("opening_balance", "期初"),
        ObjectReceivable("object_receivable", "立应收单据"),

        ;

        public String apiName;
        public String label;
        public String targetRelatedListName;
        public String targetRelatedListLabel;

        Field(String apiName, String label) {
            this.apiName = apiName;
            this.label = label;
        }

        Field(String apiName, String label, String targetRelatedListName, String targetRelatedListLabel) {
            this.apiName = apiName;
            this.label = label;
            this.targetRelatedListName = targetRelatedListName;
            this.targetRelatedListLabel = targetRelatedListLabel;
        }
    }
}
