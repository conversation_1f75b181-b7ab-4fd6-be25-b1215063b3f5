package com.facishare.crmcommon.describebuilder;

import com.facishare.paas.metadata.impl.describe.WhatFieldDescribe;

public class WhatFieldDescribeBuilder {
    private WhatFieldDescribe whatFieldDescribe;

    private WhatFieldDescribeBuilder() {
        this.whatFieldDescribe = new WhatFieldDescribe();
        this.whatFieldDescribe.setDefineType("package");
    }

    public static WhatFieldDescribeBuilder builder(){
        return new WhatFieldDescribeBuilder();
    }

    public WhatFieldDescribeBuilder apiName(String apiName){
        whatFieldDescribe.setApiName(apiName);
        return this;
    }

    public WhatFieldDescribeBuilder label(String label){
        whatFieldDescribe.setLabel(label);
        return this;
    }

    public WhatFieldDescribeBuilder apiNameField(String apiNameField){
        whatFieldDescribe.setApiNameFieldApiName(apiNameField);
        return this;
    }

    public WhatFieldDescribeBuilder idField(String idField){
        whatFieldDescribe.setIdFieldApiName(idField);
        return this;
    }

    public WhatFieldDescribeBuilder isRequired(boolean required){
        whatFieldDescribe.setRequired(required);
        return this;
    }

    public WhatFieldDescribe build(){
        return whatFieldDescribe;
    }
}
