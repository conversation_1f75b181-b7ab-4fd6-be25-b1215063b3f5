package com.facishare.crmcommon.describebuilder;

import java.util.List;

import com.facishare.paas.metadata.impl.IRule;
import com.facishare.paas.metadata.impl.Rule;

public class RuleBuilder {
    private IRule rule;

    private RuleBuilder() {
        rule = new Rule();
        rule.setCreateTime(System.currentTimeMillis());
        rule.setLastModifiedTime(System.currentTimeMillis());
    }

    public static RuleBuilder builder() {
        return new RuleBuilder();
    }

    public RuleBuilder tenantId(String tenantId) {
        rule.setTenantId(tenantId);
        return this;
    }

    public RuleBuilder ruleApiName(String ruleApiName) {
        rule.setApiName(ruleApiName);
        return this;
    }

    public RuleBuilder refObjectApiName(String objectApiName) {
        rule.setDescribeApiName(objectApiName);
        return this;
    }

    public RuleBuilder ruleName(String ruleName) {
        rule.setRuleName(ruleName);
        return this;
    }

    public RuleBuilder condition(String condition) {
        rule.setCondition(condition);
        return this;
    }

    public RuleBuilder createBy(String createBy) {
        rule.setCreatedBy(createBy);
        return this;
    }

    public RuleBuilder description(String description) {
        rule.setDescription(description);
        return this;
    }

    public RuleBuilder message(String message) {
        rule.setMessage(message);
        return this;
    }

    public RuleBuilder active(boolean active) {
        rule.setIsActive(active);
        return this;
    }

    public RuleBuilder scene(List<String> scene) {
        rule.setScene(scene);
        return this;
    }

    public IRule build() {
        return rule;
    }
}
