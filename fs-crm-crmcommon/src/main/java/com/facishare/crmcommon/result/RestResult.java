package com.facishare.crmcommon.result;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2019/10/14
 */
@Data
public class RestResult<T> implements Serializable {
    private static final long serialVersionUID = 1535964835070237507L;
    private int errCode;
    private String errMessage;
    private T result;

    public boolean isSuccess() {
        if (errCode == 0) {
            return true;
        }
        return false;
    }

}
