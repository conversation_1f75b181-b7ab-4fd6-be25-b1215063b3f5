<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns="http://www.springframework.org/schema/beans"
       xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/aop
         http://www.springframework.org/schema/aop/spring-aop.xsd
         http://www.springframework.org/schema/beans
         http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="sfainterceptorAccessLog" class="com.facishare.crm.sfainterceptor.interceptor.LogInterceptor">
        <constructor-arg type="java.lang.String" value=""/>
        <constructor-arg type="java.lang.String" value=""/>
    </bean>

    <aop:config>
        <aop:aspect id="logMonitor" ref="sfainterceptorAccessLog" order="1">
            <aop:pointcut id="logAround"
                          expression="(execution(* com.facishare.crm.sfainterceptor.predefine.*.*.*(..)))"/>
            <aop:around pointcut-ref="logAround" method="around"/>
        </aop:aspect>
    </aop:config>

    <bean id="restServiceProxyFactory" class="com.facishare.rest.core.RestServiceProxyFactory"
        p:configName="fs-paas-appframework-rest" init-method="init"/>

    <bean class="com.facishare.rest.core.RestServiceProxyFactoryBean"
        p:type="com.facishare.crm.sfainterceptor.utils.proxy.StockSalesOrderInterceptorServiceProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
</beans>
