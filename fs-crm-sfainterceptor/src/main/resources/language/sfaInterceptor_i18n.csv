key,zh-C<PERSON>,zh-TW,en
sfa-interceptor.errorinfo.ReturnedGoodsInvoiceInterceptorServiceImpl.101,开启库存后，退货单确认后不能编辑,开启库存后，退货单确认后不能编辑,开启库存后，退货单确认后不能编辑
sfa-interceptor.errorinfo.ReturnedGoodsInvoiceInterceptorServiceImpl.106,停用库存后，退货单退货仓库不能填写,停用库存后，退货单退货仓库不能填写,停用库存后，退货单退货仓库不能填写
sfa-interceptor.errorinfo.ReturnedGoodsInvoiceInterceptorServiceImpl.111,开启库存后，退货单退货仓库不能为空,开启库存后，退货单退货仓库不能为空,开启库存后，退货单退货仓库不能为空
sfa-interceptor.errorinfo.ReturnedGoodsInvoiceInterceptorStockManager.409,由于库存设置，订货仓库不能有值,由于库存设置，订货仓库不能有值,由于库存设置，订货仓库不能有值
sfa-interceptor.errorinfo.SalesOrderInterceptorServiceImpl.208,订单已关联发货单，订单产品种类、数量、销售单价不可修改,订单已关联发货单，订单产品种类、数量、销售单价不可修改,订单已关联发货单，订单产品种类、数量、销售单价不可修改
sfa-interceptor.errorinfo.SalesOrderInterceptorServiceImpl.218,订单产品id重复,订单产品id重复,订单产品id重复
sfa-interceptor.errorinfo.SalesOrderInterceptorServiceImpl.229,库存已停用，不能填写订货仓库,库存已停用，不能填写订货仓库,库存已停用，不能填写订货仓库
sfa-interceptor.errorinfo.SalesOrderInterceptorServiceImpl.235,库存已停用，不能填写订货仓库。若布局中看不到订货仓库，请联系管理员将字段在布局中显示出来。,库存已停用，不能填写订货仓库。若布局中看不到订货仓库，请联系管理员将字段在布局中显示出来。,库存已停用，不能填写订货仓库。若布局中看不到订货仓库，请联系管理员将字段在布局中显示出来。
sfa-interceptor.errorinfo.SalesOrderInterceptorServiceImpl.240,单一仓库订货，仓库不能为空,单一仓库订货，仓库不能为空,单一仓库订货，仓库不能为空
sfa-interceptor.errorinfo.SalesOrderInterceptorServiceImpl.287,该订单暂不支持手动编辑，如需编辑请联系纷享客服：400-1869-000,该订单暂不支持手动编辑，如需编辑请联系纷享客服：400-1869-000,该订单暂不支持手动编辑，如需编辑请联系纷享客服：400-1869-000
sfa-interceptor.errorinfo.SalesOrderInterceptorServiceImpl.302,库存未开启，不能填写订货仓库,库存未开启，不能填写订货仓库,库存未开启，不能填写订货仓库
sfa-interceptor.errorinfo.SalesOrderInterceptorStockManager.107,已关联有效发货单，订单产品种类、数量不可修改。,已关联有效发货单，订单产品种类、数量不可修改。,已关联有效发货单，订单产品种类、数量不可修改。
sfa-interceptor.errorinfo.SalesOrderInterceptorStockManager.116,订单产品数量不可小于已发货数。,订单产品数量不可小于已发货数。,订单产品数量不可小于已发货数。
sfa-interceptor.errorinfo.SalesOrderInterceptorStockManager.309,客户不能为空,客户不能为空,客户不能为空
sfa-interceptor.errorinfo.SalesOrderInterceptorStockManager.513,合并仓库订货条件下，只能单条订单恢复，不支持批量恢复,合并仓库订货条件下，只能单条订单恢复，不支持批量恢复,合并仓库订货条件下，只能单条订单恢复，不支持批量恢复
sfa-interceptor.errorinfo.SalesOrderInterceptorStockManager.675,获取订单信息失败,获取订单信息失败,获取订单信息失败
sfa-interceptor.errorinfo.SalesOrderInterceptorStockManager.694,仓库不能修改,仓库不能修改,仓库不能修改
sfa-interceptor.errorinfo.SalesOrderInterceptorStockManager.695,订货模式不能修改,订货模式不能修改,订货模式不能修改
sfa-interceptor.errorinfo.SalesOrderInterceptorStockManager.747,客户没有适用的仓库,客户没有适用的仓库,客户没有适用的仓库
sfa-interceptor.errorinfo.SalesOrderInterceptorStockManager.89,已关联有效发货单，不可修改销售单价。,已关联有效发货单，不可修改销售单价。,已关联有效发货单，不可修改销售单价。
sfa-interceptor.errorinfo.SalesOrderInterceptorStockManager.90,订单产品已关联了有效的发货单，不可变更为其他产品。,订单产品已关联了有效的发货单，不可变更为其他产品。,订单产品已关联了有效的发货单，不可变更为其他产品。
sfa-interceptor.errorinfo.SalesOrderInterceptorStockManager.91,已关联有效发货单，不可修改单位。,已关联有效发货单，不可修改单位。,已关联有效发货单，不可修改单位。

sfa-interceptor.errorinfo.SalesOrderInterceptorStockManager.94,已关联有效退换货单，不可修改产品。,已关联有效退换货单，不可修改产品。,已关联有效退换货单，不可修改产品。
sfa-interceptor.errorinfo.SalesOrderInterceptorStockManager.95,已关联有效退换货单，不可修改销售单价。,已关联有效退换货单，不可修改销售单价。,已关联有效退换货单，不可修改销售单价。
sfa-interceptor.errorinfo.SalesOrderInterceptorStockManager.96,订单产品已关联了有效的退换货单，不可变更为其他产品。,订单产品已关联了有效的退换货单，不可变更为其他产品。,订单产品已关联了有效的退换货单，不可变更为其他产品。
sfa-interceptor.errorinfo.SalesOrderInterceptorStockManager.97,已关联有效退换货单，不可修改单位。,已关联有效退换货单，不可修改单位。,已关联有效退换货单，不可修改单位。
sfa-interceptor.errorinfo.checkKTYAvailableStock.notBTypeStock,不是B类库存企业,不是B类库存企业,不是B类库存企业

sfa-interceptor.errorinfo.SalesOrderInterceptorAccountsReceivableManager.30,该订单产品已创建了应收，不支持删除！,该订单产品已创建了应收，不支持删除！,该订单产品已创建了应收，不支持删除！
sfa-interceptor.errorinfo.SalesOrderInterceptorAccountsReceivableManager.41,订单产品的已立应收金额大于折后金额，无法保存,订单产品的已立应收金额大于折后金额，无法保存,订单产品的已立应收金额大于折后金额，无法保存
sfa-interceptor.errorinfo.SalesOrderInterceptorAccountsReceivableManager.62,该订单已创建了应收单，不支持作废！,该订单已创建了应收单，不支持作废！,该订单已创建了应收单，不支持作废！