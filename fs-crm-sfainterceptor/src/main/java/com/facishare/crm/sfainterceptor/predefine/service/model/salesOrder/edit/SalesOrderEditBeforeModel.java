package com.facishare.crm.sfainterceptor.predefine.service.model.salesOrder.edit;

import com.facishare.crm.sfainterceptor.predefine.service.model.common.SalesOrderProductVo;
import com.facishare.crm.sfainterceptor.predefine.service.model.edit.EditBeforeModel;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date on 2018/1/9.
 */
@Data
public class SalesOrderEditBeforeModel extends EditBeforeModel {

    @Data
    @ToString
    public static class Arg {
        private SalesOrderVo salesOrderVo;
        String nowLifeStatus;
    }

    @Data
    @ToString
    public static class SalesOrderVo {
        /**
         * 订单id
         */
        private String tradeId;
        /**
         * 客户id
         */
        private String customerId;
        /**
         * 仓库id
         */
        private String warehouseId;
        /**
         * 订单金额
         */
        private BigDecimal orderAmount;
        /**
         * 订货模式
         */
        private String orderMode;
        /**
         * 送达方
         */
        private String shipToParty;
        private String partnerId;

        private List<SalesOrderProductVo> salesOrderProductVos;
    }

    @Data
    @ToString
    public static class Result {
        private String info = "info";
        private String warehouseId;
    }
}
