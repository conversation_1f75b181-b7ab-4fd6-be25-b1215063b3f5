package com.facishare.crm.sfainterceptor.predefine.service.model.salesOrder.bulkAdd;

import com.facishare.crm.sfainterceptor.predefine.service.model.common.SalesOrderProductVo;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date on 2018/1/13.
 */
@Data
@ToString
public class SalesOrderBulkAddAfterModel {

    @Data
    @ToString
    public static class Arg {
        private List<String> tradeIds;
        Map<String, List<SalesOrderProductVo>> salesOrderProductVosMap = Maps.newHashMap();
    }

    @Data
    @ToString
    public static class Result {
        //无意义，满足.net的需求
        private String info = "info";
    }


}
