package com.facishare.crm.sfainterceptor.predefine.service.model.returnedGoodsInvoiceProduct;

import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/6/21
 */
public class ReturnedGoodsInvoiceProductBulkAddAfterModel {
    @Data
    @ToString
    public static class Arg {
        private List<MixtureVo> mixtureVos;//成功导入的退货单产品信息
    }


    @Data
    @ToString
    public static class MixtureVo {
        private String id;//标识id
        private String dataId;//退货单id
        private String productId;//产品id
        private String productName;//产品名称
        private BigDecimal amount;//产品数量
    }

    @Data
    @ToString
    public static class Result {
        //无意义，满足.net的需求
        private String info = "info";
    }
}
