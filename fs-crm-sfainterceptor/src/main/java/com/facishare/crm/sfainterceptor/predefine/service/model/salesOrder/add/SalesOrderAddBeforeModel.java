package com.facishare.crm.sfainterceptor.predefine.service.model.salesOrder.add;

import com.facishare.crm.sfainterceptor.predefine.service.model.CommonModel;
import com.facishare.crm.sfainterceptor.predefine.service.model.common.SalesOrderProductVo;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date on 2018/1/9.
 */
@Data
public class SalesOrderAddBeforeModel extends CommonModel {
    @Data
    @ToString
    public static class Arg {
        private SalesOrderVo salesOrderVo;
    }

    @Data
    @ToString
    public static class SalesOrderVo {
        /**
         * 客户id
         */
        private String customerId;
        /**
         * 仓库id
         */
        private String warehouseId;
        /**
         * 订货模式
         */
        private String orderMode;
        /**
         * 业务类型
         */
        private String recordType;
        /**
         * 合作伙伴id
         */
        private String partnerId;

        private List<SalesOrderProductVo> salesOrderProductVos;
    }

    @Data
    @ToString
    public static class Result {
        private String info = "info";
        private String warehouseId;
        // 订单模式
        private String orderMode;
        private Boolean needFillDetailWarehouse;
    }
}
