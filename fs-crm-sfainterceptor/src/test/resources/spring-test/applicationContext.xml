<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:context="http://www.springframework.org/schema/context"
    xmlns:p="http://www.springframework.org/schema/p"
    xmlns:c="http://www.springframework.org/schema/c"
    xmlns="http://www.springframework.org/schema/beans"
    xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
    xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-4.1.xsd
       http://code.alibabatech.com/schema/dubbo
       http://code.alibabatech.com/schema/dubbo/dubbo.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <import resource="classpath:spring/metadata.xml"/>
    <import resource="classpath:spring/common.xml"/>
    <import resource="classpath:spring/log.xml"/>
    <import resource="classpath:spring/flow.xml"/>
    <import resource="classpath:spring/privilege.xml"/>
    <import resource="classpath:spring/restdriver.xml"/>
    <import resource="classpath:spring/dubbo.xml"/>
    <import resource="classpath:spring/deliverynote-spring.xml"/>
    <!--<import resource="classpath:spring/core-dubbo.xml"/>-->

    <import resource="classpath:spring/licence.xml"/>
    <import resource="classpath:spring/fsi.xml"/>
    <import resource="classpath:spring/payment.xml"/>
    <import resource="classpath:spring/stock-spring.xml"/>
    <import resource="classpath:spring/erp-stock-spring.xml"/>
    <import resource="classpath:spring/function-service.xml"/>


    <context:component-scan base-package="com.facishare.paas.appframework com.facishare.crm"/>
    <context:annotation-config/>

    <bean id="autoConf"
        class="com.github.autoconf.spring.reloadable.ReloadablePropertySourcesPlaceholderConfigurer"
        p:fileEncoding="UTF-8"
        p:ignoreResourceNotFound="true"
        p:ignoreUnresolvablePlaceholders="false"
        p:location="classpath:application.properties"
        p:configName="dubbo-common,fs-paas-metadata-mongo,fs-paas-appframework-rest,fs-crm-java-config
            ,fs-crm-printconfig,fs-metadata,fs-crm-icon-path,fs-crm-java-detailpage-layout-setting"/>

    <bean class="com.facishare.rest.core.RestServiceProxyFactoryBean"
        p:type="com.facishare.crm.rest.SendCrmMessageProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean id="connectionService" class="com.fxiaoke.transfer.service.ConnectionService">
        <property name="biz" value="metadata-transfer"/>
    </bean>

    <bean id="dbOperationService" class="com.facishare.restful.client.FRestApiProxyFactoryBean">
        <property name="type" value="com.fxiaoke.transfer.api.DbOperationService"/>
    </bean>
    <bean class="com.fxiaoke.transfer.service.TableSchemeService">
        <property name="configName" value="db-transfer-scheme"/>
    </bean>
    <bean class="com.github.autoconf.spring.reloadable.ReloadablePropertyPostProcessor"
        c:placeholderConfigurer-ref="autoConf"/>

    <bean id="expressionService" class="com.facishare.paas.expression.ExpressionServiceImpl"/>

    <bean class="com.facishare.fcp.service.FcpServiceBeanPostProcessor" id="fcpServiceBeanPostProcessor"/>
    <!--privilege temp-->
    <import resource="classpath:privilege-temp.xml"/>

    <bean class="com.facishare.rest.core.RestServiceProxyFactoryBean"
        p:type="com.facishare.crm.rest.CrmRestApi">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean class="com.facishare.rest.core.RestServiceProxyFactoryBean"
        p:type="com.facishare.crm.rest.ApprovalInitProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean class="com.facishare.rest.core.RestServiceProxyFactoryBean"
        p:type="com.facishare.crm.rest.FunctionProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean class="com.facishare.rest.core.RestServiceProxyFactoryBean"
        p:type="com.facishare.crm.rest.TemplateApi">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <import resource="classpath:/fs-paas-bizconf-client.xml"/>

    <bean id="settingRestApi" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
        p:type="com.facishare.crm.rest.SettingRestApi">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <!--<dubbo:reference interface="com.facishare.uc.api.service.EnterpriseEditionService"-->
    <!--id="enterpriseEditionService" protocol="dubbo"/>-->

</beans>
