<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.facishare</groupId>
    <artifactId>fs-crm</artifactId>
    <version>9.5.5-RECON-SNAPSHOT</version>
  </parent>
  <groupId>com.facishare</groupId>
  <artifactId>fs-crm-sfainterceptor</artifactId>
  <version>9.5.5-RECON-SNAPSHOT</version>
  <name>fs-crm-sfainterceptor</name>
  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>
  <dependencies>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-paas-app-core</artifactId>
      <exclusions>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>slf4j-log4j12</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.squareup.okhttp</groupId>
          <artifactId>okhttp</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.springframework</groupId>
          <artifactId>spring-webmvc</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.codehaus.groovy</groupId>
          <artifactId>groovy-all</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-paas-app-web</artifactId>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-paas-app-metadata-restdriver</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-crm-crmcommon</artifactId>
      <version>${project.version}</version>
    </dependency>
  </dependencies>
</project>
