package com.facishare.crm.advertisement.predefine.action;

import com.facishare.crm.advertisement.predefine.manager.NewAdvertisementManager;
import com.facishare.crm.consts.AdI18NKey;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.metadata.util.SpringUtil;

public class NewAdvertisementAddAction extends StandardAddAction {

    private final NewAdvertisementManager checkManager = SpringUtil.getContext().getBean(NewAdvertisementManager.class);
    @Override
    protected void before(Arg arg) {
        super.before(arg);
        checkBySource();
//        AdvertisementUtil.checkAdvertisement(serviceFacade, this.objectDescribe, this.objectData);
        checkManager.validateJumpType(actionContext.getUser(), objectData, actionContext.getLang().getValue());
    }

    private void checkBySource() {
        if (RequestUtil.isMobileOrH5Request()) {
            throw new ValidateException(I18N.text(AdI18NKey.CLIENT_NOT_SUPPORT_ADD));
        }
    }
}
