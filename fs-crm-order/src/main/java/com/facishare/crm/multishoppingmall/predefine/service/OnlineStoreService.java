package com.facishare.crm.multishoppingmall.predefine.service;

import com.facishare.crm.multishoppingmall.predefine.service.dto.GetByLinkAppIdModel;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;

/**
 * 商店管理
 */
@ServiceModule("online_store")
public interface OnlineStoreService {

    /**
     * 根据互联应用ID，查询关联可售范围列表和适用价目表列表
     *
     * SFA用到（配件商城拉产品列表Product/controller/RelateList，配件商城提交订单校验）
     */
    @ServiceMethod("get_by_link_app_id")
    GetByLinkAppIdModel.Result getByLinkAppId(ServiceContext serviceContext, GetByLinkAppIdModel.Arg arg);
}