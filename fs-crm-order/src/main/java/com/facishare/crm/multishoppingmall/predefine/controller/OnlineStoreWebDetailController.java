package com.facishare.crm.multishoppingmall.predefine.controller;

import com.facishare.crm.multishoppingmall.predefine.manager.OnlineStoreManager;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class OnlineStoreWebDetailController extends StandardWebDetailController {

    @Override
    public Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        OnlineStoreManager manager = SpringUtil.getContext().getBean(OnlineStoreManager.class);
        manager.fillObjectDataField(controllerContext.getUser(), controllerContext.getLang(), data);
        return result;
    }
}
