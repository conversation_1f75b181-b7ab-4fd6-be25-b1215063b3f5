package com.facishare.crm.multishoppingmall.predefine.manager;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.facishare.crm.multishoppingmall.consts.MSMI18NKey;
import com.facishare.crm.multishoppingmall.consts.OnlineStoreConst;
import com.facishare.crm.multishoppingmall.predefine.service.dto.RecordTypeEntity;
import com.facishare.crm.multishoppingmall.util.MultiShopMallConfigCenter;
import com.facishare.crmcommon.constants.CommonConstants;
import com.facishare.crmcommon.constants.CommonSalesOrderConstants;
import com.facishare.crmcommon.util.HeaderUtil;
import com.facishare.enterprise.common.model.paas.SimpleRecordTypeVO;
import com.facishare.er.api.model.vo.AddRoleRecordTypeVO;
import com.facishare.er.api.model.vo.AllocatedRecordTypeVO;
import com.facishare.er.api.model.vo.RoleAssociationRecordTypeVO;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.rest.core.util.JsonUtil;
import com.facishare.webpage.customer.api.model.arg.AllocateRecordTypeByObjectRestArg;
import com.facishare.webpage.customer.api.model.arg.GetPaaSAppInfoArg;
import com.facishare.webpage.customer.api.model.arg.ListAllocatedRecordTypeByObjectRestArg;
import com.facishare.webpage.customer.api.model.result.GetPaaSAppInfoResult;
import com.facishare.webpage.customer.api.service.LinkAppRestService;
import com.facishare.webpage.customer.api.service.PaaSAppRestService;
import com.google.common.base.Objects;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class OnlineStoreManager {

    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private PaaSAppRestService paasAppRestService;
    @Autowired
    private SalesOrderRecordTypeManager salesOrderRecordTypeManager;
    @Autowired
    private LinkAppRestService linkAppRestService;

    public IObjectData queryOnlineStoreObj(String tenantId, String linkAppId) {
        List<IFilter> filterList = Lists.newArrayList();

        IFilter authorizedObjectFilter = new Filter();
        authorizedObjectFilter.setFieldName(OnlineStoreConst.Field.LinkAppId.apiName);
        authorizedObjectFilter.setFieldValues(Lists.newArrayList(linkAppId));
        authorizedObjectFilter.setOperator(Operator.EQ);
        filterList.add(authorizedObjectFilter);

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(filterList);
        query.setOffset(0);
        query.setLimit(1);

        User user = new User(tenantId, "-10000");
        List<IObjectData> objectDatas = serviceFacade.findBySearchQuery(user, OnlineStoreConst.OBJECT_API_NAME, query).getData();
        if (CollectionUtils.empty(objectDatas)) {
            return null;
        }
        return objectDatas.get(0);
    }

    public void batchFillObjectDataField(User user, Lang lang, List<ObjectDataDocument> objectDataList){
        if (CollectionUtils.notEmpty(objectDataList)){
            objectDataList.stream().map(ObjectDataDocument::toObjectData).forEach(x -> fillObjectDataField(user, lang, x));
        }
    }

    public void fillObjectDataField(User user, Lang lang, IObjectData objectData){
        // fill link_app_id__r
        String linkAppId = objectData.get(OnlineStoreConst.Field.LinkAppId.apiName, String.class);
        String linkAppName = queryLinkAppName(user, lang, linkAppId);
        objectData.set("link_app_id__r", linkAppName);

        // fill sales_order_record_type__r
        String salesOrderRecordType = objectData.get(OnlineStoreConst.Field.SalesOrderRecordType.apiName, String.class);
        List<String> recordTypeLabelList = querySalesOrderRecordType(user, disassembleRecordType(salesOrderRecordType));
        objectData.set("sales_order_record_type__r", assembleRecordType(recordTypeLabelList));
    }

    public void validateEditParam(User user, IObjectData objectData){
        String dataId = objectData.getId();
        IObjectData objectDataDb = serviceFacade.findObjectData(user, dataId, OnlineStoreConst.OBJECT_API_NAME);
        // 1. 部分字段不能编辑【互联应用，来源】
        String linkAppId = objectData.get(OnlineStoreConst.Field.LinkAppId.apiName).toString();
        String origin = objectData.get(OnlineStoreConst.Field.Origin.apiName).toString();
        if (linkAppId == null || !Objects.equal(linkAppId, objectDataDb.get(OnlineStoreConst.Field.LinkAppId.apiName, String.class))){
            throw new ValidateException(I18N.text(MSMI18NKey.PARAMS_ERROR_CAN_NOT_BE_EDIT, I18N.text(MSMI18NKey.ONLINESTOREOBJ_LINK_APP_ID)));
        }
        if (origin == null || !Objects.equal(origin, objectDataDb.get(OnlineStoreConst.Field.Origin.apiName, String.class))){
            throw new ValidateException(I18N.text(MSMI18NKey.PARAMS_ERROR_CAN_NOT_BE_EDIT, I18N.text(MSMI18NKey.ONLINESTOREOBJ_ORIGIN)));
        }
        // 2. 勾选的订单业务类型必须有效
        String recordTypeInput = objectData.get(OnlineStoreConst.Field.SalesOrderRecordType.apiName, String.class);
        List<String> disabledRecordTypeList = validateSalesOrderRecordType(user, disassembleRecordType(recordTypeInput));
        if (CollectionUtils.notEmpty(disabledRecordTypeList)){
            throw new ValidateException(I18N.text(MSMI18NKey.PARAMS_ERROR_RECORD_TYPE_NOT_AVAIL, assembleRecordType(disabledRecordTypeList)));
        }
    }

    /**
     * 如果商店管理对象的订单业务类型发生了变更，则需要同步到paas平台
     * @param user
     * @param objectData
     */
    public void updateSalesOrderRecordType(User user, IObjectData objectData){
        String dataId = objectData.getId();
        IObjectData objectDataDb = serviceFacade.findObjectData(user, dataId, OnlineStoreConst.OBJECT_API_NAME);
        String salesOrderRecordType = objectData.get(OnlineStoreConst.Field.SalesOrderRecordType.apiName, String.class);
        // 判断订单业务类型是否发生了变更
        if (Objects.equal(salesOrderRecordType, objectDataDb.get(OnlineStoreConst.Field.SalesOrderRecordType.apiName, String.class))){
            return;
        }
        log.info("OnlineStoreManager.updateSalesOrderRecordType. salesOrderRecordType changed. old: {}, new: {}", objectDataDb.get(OnlineStoreConst.Field.SalesOrderRecordType.apiName, String.class), salesOrderRecordType);
        // 将业务类型变更信息同步到paas平台
        allocateRecordTypeToPaas(user, salesOrderRecordType, objectData.get(OnlineStoreConst.Field.LinkAppId.apiName, String.class));
    }

    public String queryLinkAppName(User user, Lang lang, String linkAppId){
        Map<String, String> header = new HashMap<>();
        header.put("x-fs-ei", user.getTenantId());

        GetPaaSAppInfoArg arg = new GetPaaSAppInfoArg();
        arg.setTenantId(Integer.parseInt(user.getTenantId()));
        arg.setAppId(linkAppId);
        arg.setLocale(Locale.forLanguageTag(lang.getValue()));

        GetPaaSAppInfoResult paasAppInfo = paasAppRestService.getPaaSAppInfo(header, arg);
        return paasAppInfo.getPaaSAppVO().getName();
    }

    // 仅修改订货人员角色绑定的业务类型，不会覆盖其他角色权限
    public void allocateRecordTypeToPaas(User user, String salesOrderRecordType, String linkAppId) {
        String ea = serviceFacade.getEAByEI(user.getTenantId());
        Map<String, String> headers = HeaderUtil.fsEiHeader(user.getTenantId());
        AllocateRecordTypeByObjectRestArg arg = new AllocateRecordTypeByObjectRestArg();
        arg.setAppId(linkAppId);
        arg.setObjectApiName(CommonSalesOrderConstants.API_NAME);
        arg.setEnterpriseId(Integer.valueOf(user.getTenantId()));
        arg.setEnterpriseAccount(ea);
        arg.setEmployeeId(user.getUserIdInt());
        AddRoleRecordTypeVO vo = new AddRoleRecordTypeVO();
        vo.setRoleCode(CommonConstants.ORDERING_PERSON_ROLE);
        vo.setRecordTypeApiNames(disassembleRecordType(salesOrderRecordType));
        arg.setRoleList(Collections.singletonList(vo));
        try {
            linkAppRestService.allocateRecordTypeByObject(headers, arg);
        } catch (Exception e) {
            log.warn("allocateRecordTypeByObject, headers[{}], arg[{}]", headers, arg);
            throw new ValidateException(I18N.text(MSMI18NKey.ALLOCATE_RECORD_TYPE_FAIL, OnlineStoreConst.OBJECT_API_NAME));
        }
    }

    public void syncSalesOrderRecordTypeFromPaas(User user, String appId) {
        // 校验appId的正确性
        String appName = queryLinkAppName(user, Lang.defaultLang(), appId);
        if (StringUtils.isEmpty(appName)){
            log.warn("syncSalesOrderRecordTypeFromPaas, appId is not exist. tenantId[{}], appId[{}]", user.getTenantId(), appId);
            return;
        }
        // 查询paas销售订单对象业务类型
        String ea = serviceFacade.getEAByEI(user.getTenantId());
        Map<String, String> headers = HeaderUtil.fsEiHeader(user.getTenantId());
        ListAllocatedRecordTypeByObjectRestArg arg = new ListAllocatedRecordTypeByObjectRestArg();
        arg.setAppId(appId);
        arg.setObjectApiName(CommonSalesOrderConstants.API_NAME);
        arg.setEnterpriseId(user.getTenantIdInt());
        arg.setEnterpriseAccount(ea);
        arg.setEmployeeId(User.SUPPER_ADMIN_USER_ID_INT);
        AllocatedRecordTypeVO salesOrderAllocatedRecordTypeVO = linkAppRestService.listAllocatedRecordTypeByObject(headers, arg);

        List<SimpleRecordTypeVO> existRecordTypeList = salesOrderAllocatedRecordTypeVO.getRecordTypeList();
        // 判断是否包含【配件订单】业务类型
        boolean includeAccessoriesRecordType = existRecordTypeList.stream().anyMatch(x -> com.google.common.base.Objects.equal("accessories__c", x.getApi_name()));
        if (com.facishare.paas.appframework.common.util.CollectionUtils.empty(existRecordTypeList) || !includeAccessoriesRecordType) {
            log.warn("syncSalesOrderRecordTypeFromPaas, existRecordTypeList is empty. tenantId[{}], ea[{}], employeeId[{}], linkAppId[{}], objectApiName[{}], existRecordTypeList: [{}]", user.getTenantId(), ea, User.SUPPER_ADMIN_USER_ID_INT, appId, CommonSalesOrderConstants.API_NAME, existRecordTypeList);
            return;
        }

        List<RoleAssociationRecordTypeVO> roleList = salesOrderAllocatedRecordTypeVO.getRoleList();
        // 获取到订货人员角色配置的业务类型
        Optional<RoleAssociationRecordTypeVO> orderingPersonRoleRecordTypeOpt = roleList.stream().filter(x -> com.google.common.base.Objects.equal(CommonConstants.ORDERING_PERSON_ROLE, x.getRoleCode())).findFirst();
        List<String> paasRecordTypeApiNameList;
        if (orderingPersonRoleRecordTypeOpt.isPresent()){
            RoleAssociationRecordTypeVO orderingPersonRoleRecordType = orderingPersonRoleRecordTypeOpt.get();
            // 获取到paas平台最新的业务类型
            paasRecordTypeApiNameList = orderingPersonRoleRecordType.getRecordTypeApiNames();
        } else {
            log.warn("syncSalesOrderRecordTypeFromPaas, RoleList not contain orderingPersonRole. tenantId[{}], ea[{}], employeeId[{}], linkAppId[{}], objectApiName[{}], roleList: [{}]", user.getTenantId(), ea, User.SUPPER_ADMIN_USER_ID_INT, appId, CommonSalesOrderConstants.API_NAME, roleList);
            return;
        }

        // 查询商店管理对象，获取到当前关联的订单业务类型
        IObjectData onlineStoreObjectData = queryOnlineStoreObj(user.getTenantId(), appId);
        String currentRecordType = onlineStoreObjectData.get(OnlineStoreConst.Field.SalesOrderRecordType.apiName, String.class);
        List<String> currentRecordTypeList = disassembleRecordType(currentRecordType);
        // 判断商店管理对象关联的订单业务类型是否与paas平台一致
        if (isRecordTypeChanged(currentRecordTypeList, paasRecordTypeApiNameList)){
            log.info("syncSalesOrderRecordTypeFromPaas, RecordTypeList has changed. tenantId[{}], ea[{}], employeeId[{}], linkAppId[{}], objectApiName[{}], from [{}] to [{}]", user.getTenantId(), ea, User.SUPPER_ADMIN_USER_ID_INT, appId, CommonSalesOrderConstants.API_NAME, currentRecordTypeList, paasRecordTypeApiNameList);
        } else {
            return;
        }

        // 更新商店管理对象
        onlineStoreObjectData.set(OnlineStoreConst.Field.SalesOrderRecordType.apiName, assembleRecordType(paasRecordTypeApiNameList));
        List<String> updateFieldList = Lists.newArrayList(OnlineStoreConst.Field.SalesOrderRecordType.apiName);
        serviceFacade.batchUpdateByFields(User.systemUser(user.getTenantId()), Lists.newArrayList(onlineStoreObjectData), updateFieldList);
    }

    /**
     * 判断recordType是否发生了变更
     * @param list1 原始recordType列表
     * @param list2 新的recordType列表
     * @return 如果发生变更返回true，否则返回false
     */
    private boolean isRecordTypeChanged(List<String> list1, List<String> list2) {
        if (org.springframework.util.CollectionUtils.isEmpty(list1) && org.springframework.util.CollectionUtils.isEmpty(list2)) {
            return false;
        }
        if (org.springframework.util.CollectionUtils.isEmpty(list1) || org.springframework.util.CollectionUtils.isEmpty(list2)) {
            return true;
        }
        boolean isSameSize = list1.size() == list2.size();
        boolean isSameContent = new HashSet<>(list1).equals(new HashSet<>(list2));
        return !(isSameSize && isSameContent);
    }

    private List<String> querySalesOrderRecordType(User user, List<String> recordTypeList){
        List<RecordTypeEntity> recordTypeEntityList = salesOrderRecordTypeManager.queryRecordTypeList(user.getTenantId());
        return recordTypeEntityList.stream().filter(x -> recordTypeList.contains(x.getApiName())).map(RecordTypeEntity::getLabel).collect(Collectors.toList());
    }

    private List<String> validateSalesOrderRecordType(User user, List<String> recordTypeList){
        List<RecordTypeEntity> recordTypeEntityList = salesOrderRecordTypeManager.queryRecordTypeList(user.getTenantId());
        List<String> recordTypeApiNameList = recordTypeEntityList.stream().map(RecordTypeEntity::getApiName).collect(Collectors.toList());
        return recordTypeList.stream().filter(x -> !recordTypeApiNameList.contains(x)).collect(Collectors.toList());
    }

    public String assembleRecordType(List<String> recordTypeList){
        return JsonUtil.toJson(recordTypeList);
    }

    public List<String> disassembleRecordType(String recordType){
        return JsonUtil.fromJson(recordType, List.class);
    }
}