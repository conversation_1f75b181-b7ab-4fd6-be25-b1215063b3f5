package com.facishare.manager;

import com.facishare.converter.EIEAConverter;
import com.facishare.crm.marketing.enums.ObjectInitStatusEnum;
import com.facishare.crm.marketing.predefine.manager.ConfigUserMarketingAccountManager;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeProxy;
import com.facishare.paas.appframework.privilege.dto.AuthContext;
import com.facishare.paas.appframework.privilege.dto.DelFuncCodeRoles;
import com.facishare.paas.appframework.privilege.model.DefaultFunctionPrivilegeProvider;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 26/03/2019
 */
@Slf4j
@Component
public class ClearManager {

    @Autowired
    private ObjectDataServiceImpl objectDataService;
    @Autowired
    private ConfigUserMarketingAccountManager configUserMarketingAccountManager;
    @Autowired
    private FunctionPrivilegeProxy functionPrivilegeProxy;
    @Autowired
    private EIEAConverter eieaConverter;

    public void clearAll(String ea,Integer fsUsreId, DefaultFunctionPrivilegeProvider privilegeProvider,Class constantClazz) throws Exception {
        String tenantId = eieaConverter.enterpriseAccountToId(ea) + "";
        System.out.println("tenantId:==:" + tenantId);
        User user = User.builder().userId(String.valueOf(fsUsreId)).tenantId(tenantId).build();
        RequestContextManager.setContext(RequestContext.builder().postId(UUID.randomUUID().toString()).tenantId(tenantId).user(user).build());
        clearApiName(user, (String)constantClazz.getField("API_NAME").get(null), (String)constantClazz.getField("STORE_TABLE_NAME").get(null), privilegeProvider);
        configUserMarketingAccountManager.createOrUpdateTenantConfig(user, String.valueOf(ObjectInitStatusEnum.NOT_OPEN.getStatus()));
    }

    private void clearApiName(User user, String apiName, String tableName, DefaultFunctionPrivilegeProvider privilegeProvider) throws Exception {
        String tenantId = user.getTenantId();
        objectDataService.deleteBySql(tenantId, "delete from mt_data where tenant_id ='" + tenantId + "' and object_describe_api_name ='" + apiName + "'");
        objectDataService.deleteBySql(tenantId, "delete from mt_unique where tenant_id ='" + tenantId + "' and describe_api_name ='" + apiName + "'");
        objectDataService.deleteBySql(tenantId, "delete from mt_index where tenant_id ='" + tenantId + "' and describe_api_name ='" + apiName + "'");
        objectDataService.deleteBySql(tenantId, "delete from mt_auto_number where tenant_id ='" + tenantId + "' and describe_api_name ='" + apiName + "'");
        objectDataService.deleteBySql(tenantId, "delete from " + tableName + " where tenant_id ='" + tenantId + "'");
        objectDataService.deleteBySql(tenantId, "delete from mt_describe where tenant_id='" + tenantId + "' and describe_api_name='" + apiName + "'");
        objectDataService.deleteBySql(tenantId, "delete from mt_field where tenant_id='" + tenantId + "' and describe_api_name='" + apiName + "'");
        objectDataService.deleteBySql(tenantId, "delete from mt_ui_component where tenant_id='" + tenantId + "' and ref_object_api_name='" + apiName + "'");
        deleteFunctionCode(user, privilegeProvider);
    }

    private void deleteFunctionCode(User user, DefaultFunctionPrivilegeProvider privilegeProvider) {
        AuthContext authContext = AuthContext.builder().tenantId(user.getTenantId()).userId(user.getUserId()).appId("CRM").build();
        List<String> funcset = privilegeProvider.getSupportedActionCodes();
        DelFuncCodeRoles.Arg arg = DelFuncCodeRoles.Arg.builder().authContext(authContext).funcSet(funcset).build();
        DelFuncCodeRoles.Result result = functionPrivilegeProxy.delFuncCodes(arg, FunctionPrivilegeProxy.HeaderUtil.buildHeaders(user.getTenantId()));
        System.out.println("--------------" + result);
    }
}
