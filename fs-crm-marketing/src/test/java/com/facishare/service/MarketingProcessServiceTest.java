package com.facishare.service;

import com.facishare.base.BaseServiceTest;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.marketing.constants.MarketingProcessConstants;
import com.facishare.crm.marketing.entity.MarketingProcessData;
import com.facishare.crm.marketing.predefine.manager.ConfigMarketingProcessManager;
import com.facishare.crm.marketing.predefine.service.MarketingProcessService;
import com.facishare.crm.marketing.result.InitMarketingProcessResult;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 09/04/2019
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/applicationContext.xml")
public class MarketingProcessServiceTest extends BaseServiceTest {
    static {
        System.setProperty("spring.profiles.active", "ceshi113");
    }

    @Autowired
    private ConfigMarketingProcessManager configMarketingProcessManager;
    @Autowired
    private MarketingProcessService marketingProcessService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private ServiceFacade serviceFacade;

    public MarketingProcessServiceTest() {
        super(MarketingProcessConstants.API_NAME);
    }

    @Test
    public void initMarketingProcess() {
        String ea = "2";// 61362，61363
        String tenantId = eieaConverter.enterpriseAccountToId(ea) + "";
        RequestContext requestContext = RequestContext.builder().tenantId(tenantId).user(new User(tenantId, "-10000")).build();
        ServiceContext serviceContext = newServiceContext();
        serviceContext.setRequestContext(requestContext);
        InitMarketingProcessResult result = marketingProcessService.initMarketingProcess(serviceContext);
        log.info("InitMarketingProcessResult result[{}]", result);
    }

    @Test
    public void addMarketingProcess() {
        String ea = "2";
        String tenantId = eieaConverter.enterpriseAccountToId(ea) + "";
        RequestContext requestContext = RequestContext.builder().tenantId(tenantId).user(new User(tenantId, "1000")).build();
        ServiceContext serviceContext = newServiceContext();
        serviceContext.setRequestContext(requestContext);
        log.info("user[{}]", user);
        MarketingProcessData data = new MarketingProcessData();
        data.setName("5c9aed73624a6c5b0d2c5d4c");
        IObjectDescribe iObjectDescribe = serviceFacade.findObject(serviceContext.getRequestContext().getUser().getTenantId(), MarketingProcessConstants.API_NAME);
        data.fillRequired(user, iObjectDescribe);
        IObjectData result = serviceFacade.saveObjectData(serviceContext.getRequestContext().getUser(), data.getIObjectData());
        log.info("addMarketingProcess result[{}]", result);
    }
}
