package com.facishare.service;

import com.alibaba.fastjson.JSONObject;
import com.facishare.base.BaseServiceTest;
import com.facishare.converter.EIEAConverter;
import com.facishare.crmcommon.describebuilder.SelectOneFieldDescribeBuilder;
import com.facishare.crmcommon.describebuilder.SelectOptionBuilder;
import com.facishare.crm.marketing.arg.AddMarketingActivityArg;
import com.facishare.crm.marketing.arg.GetMarketingActivityArg;
import com.facishare.crm.marketing.arg.UpdateMarketingActivityArg;
import com.facishare.crm.marketing.constants.MarketingActivityConstants;
import com.facishare.crm.marketing.enums.MarketingActivitySendStatusEnums;
import com.facishare.crm.marketing.enums.ObjectInitStatusEnum;
import com.facishare.crm.marketing.predefine.manager.ConfigMarketingManager;
import com.facishare.crm.marketing.predefine.privilege.MarketingActivityFunctionPrivilegeProvider;
import com.facishare.crm.marketing.predefine.service.MarketingActivityService;
import com.facishare.crm.marketing.result.MarketingActivityResult;
import com.facishare.crm.marketing.vo.AddMarketingActivityVO;
import com.facishare.crm.marketing.vo.GetMarketingActivityVO;
import com.facishare.crm.marketing.vo.UpdateMarketingActivityVO;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeProxy;
import com.facishare.paas.appframework.privilege.dto.AuthContext;
import com.facishare.paas.appframework.privilege.dto.DelFuncCodeRoles;
import com.facishare.paas.appframework.privilege.model.DefaultFunctionPrivilegeProvider;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.google.common.collect.Sets;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * @author: dongzhb
 * @date: 2019/2/21
 * @Description:
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/applicationContext.xml")
public class MarketingActivityServiceTest extends BaseServiceTest {
    @Autowired
    private ConfigMarketingManager configMarketingManager;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private ObjectDataServiceImpl objectDataService;
    @Autowired
    private FunctionPrivilegeProxy functionPrivilegeProxy;
    @Autowired
    private MarketingActivityService marketingActivityService;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private ServiceFacade serviceFacade;

    public MarketingActivityServiceTest() {
        super(MarketingActivityConstants.API_NAME);
    }

    static {
        System.setProperty("spring.profiles.active", "ceshi113");
    }


    @Test
    public void findTenantConfig() {
        String tenantId = "61362";
        User user = User.builder().userId("-10000").tenantId(tenantId).build();
        String value = configMarketingManager.findTenantConfig(user);
        System.out.println("value:" + value);
        // configMarketingManager.deleteTenantConfig(user);
    }

    @Test
    public void initMarketingActivity() {
        String ea = "61375";// 61362，61363
        String tenantId = eieaConverter.enterpriseAccountToId(ea) + "";
        RequestContext requestContext = RequestContext.builder().tenantId(tenantId).user(new User(tenantId, "-10000")).build();
        ServiceContext serviceContext = newServiceContext();
        serviceContext.setRequestContext(requestContext);
        MarketingActivityResult result = marketingActivityService.initMarketingActivity(serviceContext);
        log.info("MarketingActivityResult result[{}]", result);
    }

    @Test
    public void updateLayoutAndUpdateDescribe() {
        String ea = "2";
        String tenantId = eieaConverter.enterpriseAccountToId(ea) + "";
        User user = new User(tenantId, "1000");
        Set<String> apiNames = Sets.newHashSet(MarketingActivityConstants.API_NAME);
        Map<String, IObjectDescribe> describeMap = describeLogicService.findObjects(user.getTenantId(), apiNames);
        if (describeMap.containsKey(MarketingActivityConstants.API_NAME)) {
//            List<ISelectOption> spreadTypeSelectOptions = Arrays.stream(MarketingActivityTypeEnums.values())
//                .map(typeEnum -> SelectOptionBuilder.builder().value(typeEnum.getValue()).label(typeEnum.getLabel()).build()).collect(Collectors.toList());
//            SelectOneFieldDescribe spreadTypeSelectOneFieldDescribe = SelectOneFieldDescribeBuilder.builder().apiName(MarketingActivityConstants.Field.SpreadType.apiName)
//                .label(MarketingActivityConstants.Field.SpreadType.label).selectOptions(spreadTypeSelectOptions).required(true).build();
            /**推广状态*/
            List<ISelectOption> sendStatusSelectOptions = Arrays.stream(MarketingActivitySendStatusEnums.values())
                .map(typeEnum -> SelectOptionBuilder.builder().value(typeEnum.getValue()).label(typeEnum.getLabel()).build()).collect(Collectors.toList());
            SelectOneFieldDescribe sendStatusSelectOneFieldDescribe = SelectOneFieldDescribeBuilder.builder().apiName(MarketingActivityConstants.Field.Status.apiName)
                .label(MarketingActivityConstants.Field.Status.label).selectOptions(sendStatusSelectOptions).required(true).build();
            serviceFacade.updateCustomFieldDescribe(user, MarketingActivityConstants.API_NAME, sendStatusSelectOneFieldDescribe.toJsonString(), null, null);
        }
    }

    @Test
    public void findObjects() {
//        String ea = "61250";
//        String tenantId = eieaConverter.enterpriseAccountToId(ea) + "";
        tenantId="55487";
        log.info("企业EA,{}", tenantId);
        Set<String> apiNames = Sets.newHashSet(MarketingActivityConstants.API_NAME);
        Map<String, IObjectDescribe> describeMap = describeLogicService.findObjects(tenantId, apiNames);
        //       log.info("initMarketingActivity status [{}]", describeMap.get(MarketingActivityConstants.API_NAME));
        log.info("initMarketingActivity status [{}]", !describeMap.containsKey(MarketingActivityConstants.API_NAME));
    }

    @Test
    public void getByIdMarketingActivity() {
        String ea = "61250";
        String tenantId = eieaConverter.enterpriseAccountToId(ea) + "";
        RequestContext requestContext = RequestContext.builder().tenantId("55487").user(new User(tenantId, "1000")).build();
        ServiceContext serviceContext = newServiceContext();
        serviceContext.setRequestContext(requestContext);
        GetMarketingActivityArg activityArg = new GetMarketingActivityArg();
        activityArg.setId("5c6f71296ece9b00010905b5");
        GetMarketingActivityVO result = marketingActivityService.getByIdMarketingActivity(activityArg, serviceContext);
        log.info("getByIdMarketingActivity result[{}]", result);
    }

    @Test
    public void addMarketingActivity() {
        String ea = "2";
        String tenantId = eieaConverter.enterpriseAccountToId(ea) + "";
        RequestContext requestContext = RequestContext.builder().tenantId(tenantId).user(new User(tenantId, "1000")).build();
        ServiceContext serviceContext = newServiceContext();
        serviceContext.setRequestContext(requestContext);
        log.info("user[{}]", user);
        String json = "{\"spreadType\":\"1\",\"marketingEventId\":\"000e471ec2384537837d565e1e6b6acc\",\"name\":\"企业数字化擎，2019第四届Sa123456\",\"status\":\"3\"}";
        AddMarketingActivityArg arg = JSONObject.parseObject(json, AddMarketingActivityArg.class);
   /*   AddMarketingActivityArg  AddMarketingActivityArg arg = new AddMarketingActivityArg();
        arg.setName("测试营销活动" + user.getTenantId());
        arg.setSpreadType("1");
        arg.setStatus("1");*/
        AddMarketingActivityVO result = marketingActivityService.addMarketingActivity(arg, serviceContext);
        log.info("addMarketingActivity result[{}]", result);
    }

    @Test
    public void updateMarketingActivity() {
        String ea = "61250";
        String tenantId = eieaConverter.enterpriseAccountToId(ea) + "";
        RequestContext requestContext = RequestContext.builder().tenantId(tenantId).user(new User(tenantId, "1000")).build();
        ServiceContext serviceContext = newServiceContext();
        serviceContext.setRequestContext(requestContext);
        UpdateMarketingActivityArg arg = new UpdateMarketingActivityArg();
        arg.setName("营销活动-活动营销" + user.getTenantId());
        arg.setSpreadType("2");
        arg.setStatus("2");
        arg.setId("5c6f71296ece9b00010905b5");
        UpdateMarketingActivityVO result = marketingActivityService.updateMarketingActivity(arg, serviceContext);
        log.info("updateMarketingActivity result[{}]", result);
    }



    @Test
    public void clearAll() throws Exception {
        String ea = "61364";
        String tenantId = eieaConverter.enterpriseAccountToId(ea) + "";
        System.out.println("tenantId:==:" + tenantId);
        User user = User.builder().userId("1000").tenantId(tenantId).build();
        RequestContextManager.setContext(RequestContext.builder().postId(UUID.randomUUID().toString()).tenantId(tenantId).user(user).build());
        clearApiName(user, MarketingActivityConstants.API_NAME, MarketingActivityConstants.STORE_TABLE_NAME, new MarketingActivityFunctionPrivilegeProvider());
        configMarketingManager.createOrUpdateTenantConfig(user, String.valueOf(ObjectInitStatusEnum.NOT_OPEN.status));
    }

    private void clearApiName(User user, String apiName, String tableName, DefaultFunctionPrivilegeProvider privilegeProvider) throws Exception {
        String tenantId = user.getTenantId();
        objectDataService.deleteBySql(tenantId, "delete from mt_data where tenant_id ='" + tenantId + "' and object_describe_api_name ='" + apiName + "'");
        objectDataService.deleteBySql(tenantId, "delete from mt_unique where tenant_id ='" + tenantId + "' and describe_api_name ='" + apiName + "'");
        objectDataService.deleteBySql(tenantId, "delete from mt_index where tenant_id ='" + tenantId + "' and describe_api_name ='" + apiName + "'");
        objectDataService.deleteBySql(tenantId, "delete from mt_auto_number where tenant_id ='" + tenantId + "' and describe_api_name ='" + apiName + "'");
        objectDataService.deleteBySql(tenantId, "delete from " + tableName + " where tenant_id ='" + tenantId + "'");
        objectDataService.deleteBySql(tenantId, "delete from mt_describe where tenant_id='" + tenantId + "' and describe_api_name='" + apiName + "'");
        objectDataService.deleteBySql(tenantId, "delete from mt_field where tenant_id='" + tenantId + "' and describe_api_name='" + apiName + "'");
        objectDataService.deleteBySql(tenantId, "delete from mt_ui_component where tenant_id='" + tenantId + "' and ref_object_api_name='" + apiName + "'");
        deleteFunctionCode(tenantId, privilegeProvider);
    }

    private void deleteFunctionCode(String tenantId, DefaultFunctionPrivilegeProvider privilegeProvider) {
        User user = User.builder().userId("1000").tenantId(tenantId).build();
        AuthContext authContext = AuthContext.builder().tenantId(tenantId).userId(user.getUserId()).appId("CRM").build();
        List<String> funcset = privilegeProvider.getSupportedActionCodes();
        DelFuncCodeRoles.Arg arg = DelFuncCodeRoles.Arg.builder().authContext(authContext).funcSet(funcset).build();
        DelFuncCodeRoles.Result result = functionPrivilegeProxy.delFuncCodes(arg, FunctionPrivilegeProxy.HeaderUtil.buildHeaders(tenantId));
        System.out.println("--------------" + result);
    }
}
