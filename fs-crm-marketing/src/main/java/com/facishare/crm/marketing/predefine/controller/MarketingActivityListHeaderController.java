package com.facishare.crm.marketing.predefine.controller;

import com.facishare.crm.marketing.util.CheckingUtils;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @author: dongzhb
 * @date: 2019/3/6
 * @Description:
 */
@Slf4j
@Component
public class MarketingActivityListHeaderController extends StandardListHeaderController {
    @Override
    protected Result after(Arg arg, Result result) {
        //layout去除添加按纽
        LayoutDocument layoutDocument = result.getLayout();
        layoutDocument = CheckingUtils.clearLayoutDocument(layoutDocument);
        if (null != layoutDocument) {
            result.setLayout(layoutDocument);
        }
        ObjectDescribeDocument objectDescribeDocument = result.getObjectDescribe();
        objectDescribeDocument = CheckingUtils.clearObjectDescribeDocument(objectDescribeDocument);
        if (null != objectDescribeDocument) {
            result.setObjectDescribe(objectDescribeDocument);
        }
        super.after(arg, result);
        return result;
    }
}
