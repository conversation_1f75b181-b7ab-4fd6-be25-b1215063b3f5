package com.facishare.crm.marketing.predefine;

import com.facishare.crm.marketing.constants.MarketingActivityConstants;
import com.facishare.paas.appframework.core.model.ActionClassInfo;
import com.facishare.paas.appframework.core.model.ControllerClassInfo;
import com.facishare.paas.appframework.core.model.PreDefineObject;
import com.facishare.paas.appframework.core.model.PreDefineObjectRegistry;

/**
 * @author: dongzhb
 * @date: 2019/2/21
 * @Description:
 */
public enum MarketingActivityPredefineObject implements PreDefineObject {
    /**营销活动*/
    MarketingActivity(MarketingActivityConstants.API_NAME);
    private String apiName;
    private static String PACKAGE_NAME = MarketingActivityPredefineObject.class.getPackage().getName();

    MarketingActivityPredefineObject(String apiName) {
        this.apiName = apiName;
    }

    @Override
    public String getApiName() {
        return apiName;
    }

    @Override
    public String getPackageName() {
        return PACKAGE_NAME;
    }

    @Override
    public ActionClassInfo getDefaultActionClassInfo(String actionCode) {
        String className = PACKAGE_NAME + ".action." + this + actionCode + "Action";
        return new ActionClassInfo(className);
    }

    @Override
    public ControllerClassInfo getControllerClassInfo(String methodName) {
        String className = PACKAGE_NAME + ".controller." + this + methodName + "Controller";
        return new ControllerClassInfo(className);
    }

    public static void init() {
        for (MarketingActivityPredefineObject object : MarketingActivityPredefineObject.values()) {
            PreDefineObjectRegistry.register(object);
        }
    }
}
