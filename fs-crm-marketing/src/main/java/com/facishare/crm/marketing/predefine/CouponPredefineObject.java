package com.facishare.crm.marketing.predefine;

import com.facishare.crm.marketing.constants.CouponConstants;
import com.facishare.paas.appframework.core.model.ActionClassInfo;
import com.facishare.paas.appframework.core.model.ControllerClassInfo;
import com.facishare.paas.appframework.core.model.PreDefineObject;
import com.facishare.paas.appframework.core.model.PreDefineObjectRegistry;

public enum CouponPredefineObject implements PreDefineObject {
    Coupon(CouponConstants.API_NAME);
    private static String PACKAGE_NAME = CouponPredefineObject.class.getPackage().getName();
    private String apiName;

    CouponPredefineObject(String apiName) {
        this.apiName = apiName;
    }
    @Override
    public String getApiName() {
        return apiName;
    }

    @Override
    public String getPackageName() {
        return PACKAGE_NAME;
    }

    @Override
    public ActionClassInfo getDefaultActionClassInfo(String actionCode) {
        String className = PACKAGE_NAME + ".action." + this + actionCode + "Action";
        return new ActionClassInfo(className);
    }

    @Override
    public ControllerClassInfo getControllerClassInfo(String methodName) {
        String className = PACKAGE_NAME + ".controller." + this + methodName + "Controller";
        return new ControllerClassInfo(className);
    }

    public static void init() {
        for (CouponPredefineObject object : CouponPredefineObject.values()) {
            PreDefineObjectRegistry.register(object);
        }
    }
}
