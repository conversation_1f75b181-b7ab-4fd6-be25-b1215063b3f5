package com.facishare.crm.marketing.predefine.privilege;

import com.facishare.crmcommon.constants.CommonConstants;
import com.facishare.crm.marketing.constants.MarketingActivityConstants;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.privilege.model.DefaultFunctionPrivilegeProvider;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;

/**
 * @author: dongzhb
 * @date: 2019/2/20
 * @Description:
 */
@Component
public class MarketingActivityFunctionPrivilegeProvider extends DefaultFunctionPrivilegeProvider {

    private final static List<String> supportActionCodes = Lists.newArrayList(

        ObjectAction.VIEW_LIST.getActionCode(),

        ObjectAction.VIEW_DETAIL.getActionCode(),

        ObjectAction.CREATE.getActionCode(),

        ObjectAction.UPDATE.getActionCode(),

        ObjectAction.DELETE.getActionCode(),

        ObjectAction.INVALID.getActionCode(),

        ObjectAction.RECOVER.getActionCode(),

        ObjectAction.BATCH_EXPORT.getActionCode(),

        ObjectAction.BATCH_IMPORT.getActionCode(),

        ObjectAction.EDIT_TEAM_MEMBER.getActionCode(),

        ObjectAction.LOCK.getActionCode(),

        ObjectAction.UNLOCK.getActionCode(),

        ObjectAction.PRINT.getActionCode(),

        ObjectAction.VIEW_ENTIRE_BPM.getActionCode(),

        ObjectAction.STOP_BPM.getActionCode(),

        ObjectAction.CHANGE_BPM_APPROVER.getActionCode(),

        ObjectAction.START_BPM.getActionCode()

    );

    private final static List<String> observerSupportActionCodes = Lists.newArrayList(

        ObjectAction.VIEW_LIST.getActionCode(),

        ObjectAction.VIEW_DETAIL.getActionCode()

    );

    @Override
    public String getApiName() {
        return MarketingActivityConstants.API_NAME;
    }

    @Override
    public List<String> getSupportedActionCodes() {
        return Collections.unmodifiableList(supportActionCodes);
    }

    @Override
    public Map<String, List<String>> getCustomInitRoleActionCodes() {
        Map<String, List<String>> actionCodeMap = Maps.newHashMap();
        /**CRM管理者*/
        actionCodeMap.put(CommonConstants.CRM_MANAGER_ROLE, Collections.unmodifiableList(supportActionCodes));
        /**CRM观察者*/
        actionCodeMap.put(CommonConstants.CRM_OBSERVER_ROLE, Collections.unmodifiableList(observerSupportActionCodes));
        return Collections.unmodifiableMap(actionCodeMap);
    }
}
