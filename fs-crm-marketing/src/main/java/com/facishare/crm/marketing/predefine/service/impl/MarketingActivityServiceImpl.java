package com.facishare.crm.marketing.predefine.service.impl;

import com.facishare.crmcommon.constants.LayoutConstants;
import com.facishare.crmcommon.constants.SystemConstants;
import com.facishare.crmcommon.constants.SystemConstants.RenderType;
import com.facishare.crmcommon.describebuilder.FieldSectionBuilder;
import com.facishare.crmcommon.describebuilder.FormComponentBuilder;
import com.facishare.crmcommon.describebuilder.FormFieldBuilder;
import com.facishare.crmcommon.describebuilder.LayoutBuilder;
import com.facishare.crmcommon.describebuilder.ObjectDescribeBuilder;
import com.facishare.crmcommon.describebuilder.ObjectReferenceFieldDescribeBuilder;
import com.facishare.crmcommon.describebuilder.SelectOneFieldDescribeBuilder;
import com.facishare.crmcommon.describebuilder.SelectOptionBuilder;
import com.facishare.crmcommon.describebuilder.TableColumnBuilder;
import com.facishare.crmcommon.describebuilder.TableComponentBuilder;
import com.facishare.crmcommon.describebuilder.TextFieldDescribeBuilder;
import com.facishare.crm.marketing.arg.AddMarketingActivityArg;
import com.facishare.crm.marketing.arg.GetMarketingActivityArg;
import com.facishare.crm.marketing.arg.UpdateMarketingActivityArg;
import com.facishare.crm.marketing.constants.MarketingActivityConstants;
import com.facishare.crm.marketing.constants.MarketingActivityConstants.Field;
import com.facishare.crm.marketing.entity.MarketingActivityData;
import com.facishare.crm.marketing.enums.MarketingActivitySendStatusEnums;
import com.facishare.crm.marketing.enums.MarketingActivityTypeEnums;
import com.facishare.crm.marketing.enums.ObjectInitStatusEnum;
import com.facishare.crm.marketing.exception.MarketingActivityBusinessException;
import com.facishare.crm.marketing.predefine.manager.ConfigMarketingManager;
import com.facishare.crm.marketing.predefine.service.MarketingActivityService;
import com.facishare.crm.marketing.result.MarketingActivityResult;
import com.facishare.crm.marketing.vo.AddMarketingActivityVO;
import com.facishare.crm.marketing.vo.GetMarketingActivityVO;
import com.facishare.crm.marketing.vo.UpdateMarketingActivityVO;
import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.describe.TextFieldDescribe;
import com.facishare.paas.metadata.impl.ui.layout.FieldSection;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.facishare.paas.metadata.impl.ui.layout.component.TableComponent;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.ui.layout.ITableColumn;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: dongzhb
 * @date: 2019/2/20
 * @Description:
 */
@Service
@Slf4j
public class MarketingActivityServiceImpl implements MarketingActivityService {
    @Autowired
    private ConfigMarketingManager configMarketingManager;
    @Autowired
    private IObjectDescribeService objectDescribeService;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private ServiceFacade serviceFacade;

    @Override
    public MarketingActivityResult initMarketingActivity(ServiceContext serviceContext) {
        User user = serviceContext.getUser();
        String value = configMarketingManager.findTenantConfig(user);
        if (value != null && value.equals(String.valueOf(ObjectInitStatusEnum.OPENED.status))) {
            return new MarketingActivityResult(ObjectInitStatusEnum.OPENED.status, ObjectInitStatusEnum.OPENED.message);
        }
        //校验对象名字是否存在
        Set<String> existDisplayNames = checkDisplayName(user.getTenantId());
        if (CollectionUtils.isNotEmpty(existDisplayNames)) {
            return new MarketingActivityResult(ObjectInitStatusEnum.EXIST.status, Joiner.on(",").join(existDisplayNames).concat(ObjectInitStatusEnum.EXIST.message));
        }
        Set<String> apiNames = Sets.newHashSet(MarketingActivityConstants.API_NAME);
        Map<String, IObjectDescribe> describeMap = describeLogicService.findObjects(user.getTenantId(), apiNames);
        log.info("initMarketingActivity tenantId ,[{}] , status [{}]", user.getTenantId(), describeMap.containsKey(MarketingActivityConstants.API_NAME));
        if (!describeMap.containsKey(MarketingActivityConstants.API_NAME)) {
            IObjectDescribe describeDraft = generateDescsribeDraft(user.getTenantId(), user.getUserId());
            ILayout defaultLayout = generateDefaultLayout(user.getTenantId(), user.getUserId());
            ILayout listLayout = generateListLayout(user.getTenantId(), user.getUserId());
            describeLogicService.createDescribe(user, describeDraft.toJsonString(), defaultLayout.toJsonString(), listLayout.toJsonString(), true, true);
            configMarketingManager.createOrUpdateTenantConfig(serviceContext.getUser(), String.valueOf(ObjectInitStatusEnum.OPENED.status));
        }
        return new MarketingActivityResult(ObjectInitStatusEnum.OPENED.status, ObjectInitStatusEnum.OPENED.message);
    }

    @Override
    public GetMarketingActivityVO getByIdMarketingActivity(GetMarketingActivityArg arg, ServiceContext serviceContext) {
        IObjectData iObjectData = serviceFacade.findObjectData(serviceContext.getRequestContext().getUser(), arg.getId(), MarketingActivityConstants.API_NAME);
        GetMarketingActivityVO getMarketingActivityVO = new GetMarketingActivityVO();
        MarketingActivityData marketingActivityData = new MarketingActivityData(iObjectData);
        getMarketingActivityVO.setId(marketingActivityData.getId());
        getMarketingActivityVO.setMarketingEventId(marketingActivityData.getMarketingEventId());
        getMarketingActivityVO.setSpreadType(marketingActivityData.getSpreadType());
        getMarketingActivityVO.setName(marketingActivityData.getName());
        getMarketingActivityVO.setStatus(marketingActivityData.getStatus());
        return getMarketingActivityVO;
    }

    @Override
    public AddMarketingActivityVO addMarketingActivity(AddMarketingActivityArg arg, ServiceContext serviceContext) {
        MarketingActivityData data = new MarketingActivityData();
        data.setName(arg.getName());
        if (StringUtils.isNotBlank(arg.getMarketingEventId())) {
            data.setMarketingEventId(arg.getMarketingEventId());
        }
        data.setSpreadType(arg.getSpreadType());
        data.setStatus(arg.getStatus());
        IObjectDescribe iObjectDescribe = serviceFacade.findObject(serviceContext.getRequestContext().getUser().getTenantId(), MarketingActivityConstants.API_NAME);
        data.fillRequired(serviceContext.getUser(), iObjectDescribe);
        IObjectData iObjectData = data.getIObjectData();
        IObjectData iObjectDataResult = serviceFacade.saveObjectData(serviceContext.getRequestContext().getUser(), iObjectData);
        return new AddMarketingActivityVO(iObjectDataResult.getId());
    }

    @Override
    public UpdateMarketingActivityVO updateMarketingActivity(UpdateMarketingActivityArg arg, ServiceContext serviceContext) {
        IObjectDescribe iObjectDescribe = serviceFacade.findObject(serviceContext.getRequestContext().getUser().getTenantId(), MarketingActivityConstants.API_NAME);
        IObjectData updateData = new ObjectData();
        updateData.setId(arg.getId());
        updateData.set(Field.Name.apiName,arg.getName());
        updateData.set(Field.SpreadType.apiName,arg.getSpreadType());
        updateData.set(Field.Status.apiName,arg.getStatus());
        updateData.setDescribeId(iObjectDescribe.getId());
        updateData.setDescribeApiName(MarketingActivityConstants.API_NAME);
        updateData.setTenantId(serviceContext.getTenantId());
        if (StringUtils.isNotBlank(arg.getMarketingEventId())) {
            updateData.set(Field.MarketingEventId.apiName,arg.getMarketingEventId());
        }
        IObjectData iObjectDataResult = serviceFacade.updateObjectData(serviceContext.getRequestContext().getUser(), updateData);
        return new UpdateMarketingActivityVO(iObjectDataResult.getId());
    }

    /**
     * 1.查询是否已存在API_NAME 2.判断API_NAME是否相同 3.如果不同则返回结果
     **/
    private Set<String> checkDisplayName(String tenantId) {
        try {
            Set<String> existDisplayNames = Sets.newHashSet();
            List<String> existMemberApiNames = objectDescribeService.checkDisplayNameExist(tenantId, MarketingActivityConstants.DISPLAY_NAME, "CRM");
            existMemberApiNames.forEach(x -> {
                if (!MarketingActivityConstants.API_NAME.equals(x)) {
                    existDisplayNames.add(MarketingActivityConstants.DISPLAY_NAME);
                }
            });
            return existDisplayNames;
        } catch (MetadataServiceException e) {
            log.warn(" MarketingActivityConstants checkDisplayName tenantId,{},error,{}", tenantId, e);
            throw new MarketingActivityBusinessException(e.getMessage(), e.getErrorCode().getCode());
        }
    }

    private IObjectDescribe generateDescsribeDraft(String tenantId, String fsUserId) {
        List<IFieldDescribe> fieldDescribeList = Lists.newArrayList();
        /**推广内容*/
        TextFieldDescribe nameFieldDescribe = TextFieldDescribeBuilder.builder().apiName(MarketingActivityConstants.Field.Name.apiName).label(MarketingActivityConstants.Field.Name.label)
            .required(true).unique(false).maxLength(1000).build();
        fieldDescribeList.add(nameFieldDescribe);
        /**市场活动*/
        ObjectReferenceFieldDescribe marketingEventFieldDescribe = ObjectReferenceFieldDescribeBuilder.builder().apiName(MarketingActivityConstants.Field.MarketingEventId.apiName)
            .label(MarketingActivityConstants.Field.MarketingEventId.label).targetApiName(Utils.MARKETING_EVENT_API_NAME)
            .targetRelatedListName(MarketingActivityConstants.Field.MarketingEventId.targetRelatedListName)
            .targetRelatedListLabel(MarketingActivityConstants.Field.MarketingEventId.targetRelatedListLabel).build();
        fieldDescribeList.add(marketingEventFieldDescribe);

        /**推广方式*/
        List<ISelectOption> spreadTypeSelectOptions = Arrays.stream(MarketingActivityTypeEnums.values())
            .map(typeEnum -> SelectOptionBuilder.builder().value(typeEnum.getValue()).label(typeEnum.getLabel()).build()).collect(Collectors.toList());
        SelectOneFieldDescribe spreadTypeSelectOneFieldDescribe = SelectOneFieldDescribeBuilder.builder().apiName(MarketingActivityConstants.Field.SpreadType.apiName)
            .label(MarketingActivityConstants.Field.SpreadType.label).selectOptions(spreadTypeSelectOptions).required(true).build();
        fieldDescribeList.add(spreadTypeSelectOneFieldDescribe);
        /**推广状态*/
        List<ISelectOption> sendStatusSelectOptions = Arrays.stream(MarketingActivitySendStatusEnums.values())
            .map(typeEnum -> SelectOptionBuilder.builder().value(typeEnum.getValue()).label(typeEnum.getLabel()).build()).collect(Collectors.toList());
        SelectOneFieldDescribe sendStatusSelectOneFieldDescribe = SelectOneFieldDescribeBuilder.builder().apiName(MarketingActivityConstants.Field.Status.apiName)
            .label(MarketingActivityConstants.Field.Status.label).selectOptions(sendStatusSelectOptions).required(true).build();
        fieldDescribeList.add(sendStatusSelectOneFieldDescribe);

        return ObjectDescribeBuilder.builder().apiName(MarketingActivityConstants.API_NAME).displayName(MarketingActivityConstants.DISPLAY_NAME).tenantId(tenantId).createBy(fsUserId)
            .fieldDescribes(fieldDescribeList).storeTableName(MarketingActivityConstants.STORE_TABLE_NAME).iconIndex(MarketingActivityConstants.ICON_INDEX).build();
    }

    private ILayout generateDefaultLayout(String tenantId, String fsUserId) {
        List<IFieldSection> fieldSections = Lists.newArrayList();
        List<IFormField> formFields = Lists.newArrayList();
        /**推广内容*/
        formFields
            .add(FormFieldBuilder.builder().fieldName(MarketingActivityConstants.Field.Name.apiName).readOnly(false).required(true).renderType(SystemConstants.RenderType.Text.renderType).build());
        /**市场活动*/
        formFields.add(FormFieldBuilder.builder().fieldName(Field.MarketingEventId.apiName).readOnly(false).required(false).renderType(RenderType.ObjectReference.renderType).build());
        /**推广方式*/
        formFields.add(FormFieldBuilder.builder().fieldName(Field.SpreadType.apiName).readOnly(false).required(true).renderType(SystemConstants.RenderType.SelectOne.renderType).build());
        /**推广状态*/
        formFields.add(FormFieldBuilder.builder().fieldName(Field.Status.apiName).readOnly(false).required(true).renderType(RenderType.SelectOne.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(SystemConstants.Field.Owner.apiName).readOnly(false).required(true).renderType(SystemConstants.RenderType.Employee.renderType).build());
        FieldSection fieldSection = FieldSectionBuilder.builder().name(LayoutConstants.BASE_FIELD_SECTION_API_NAME).header(LayoutConstants.BASE_FIELD_SECTION_DISPLAY_NAME).showHeader(true)
            .fields(formFields).build();
        fieldSections.add(fieldSection);
        FormComponent formComponent = FormComponentBuilder.builder().name(LayoutConstants.FORM_COMPONENT_API_NAME).buttons(null).fieldSections(fieldSections).build();
        List<IComponent> components = Lists.newArrayList(formComponent);
        return LayoutBuilder.builder().tenantId(tenantId).createBy(fsUserId).
            name(MarketingActivityConstants.DEFAULT_LAYOUT_API_NAME).displayName(MarketingActivityConstants.DEFAULT_LAYOUT_DISPLAY_NAME).isDefault(true)
            .refObjectApiName(MarketingActivityConstants.API_NAME).layoutType(SystemConstants.LayoutType.Detail.layoutType).components(components).build();
    }

    private ILayout generateListLayout(String tenantId, String fsUserId) {
        List<ITableColumn> tableColumns = Lists.newArrayList();
        /**推广内容*/
        tableColumns.add(
            TableColumnBuilder.builder().name(MarketingActivityConstants.Field.Name.apiName).lableName(MarketingActivityConstants.Field.Name.label).renderType(RenderType.Text.renderType).build());
        /**市场活动*/
        tableColumns.add(TableColumnBuilder.builder().name(MarketingActivityConstants.Field.MarketingEventId.apiName).lableName(MarketingActivityConstants.Field.MarketingEventId.label)
            .renderType(RenderType.ObjectReference.renderType).build());
        /**推广方式*/
        tableColumns.add(TableColumnBuilder.builder().name(MarketingActivityConstants.Field.SpreadType.apiName).lableName(MarketingActivityConstants.Field.SpreadType.label)
            .renderType(RenderType.SelectOne.renderType).build());
        /**推广状态*/
        tableColumns.add(
            TableColumnBuilder.builder().name(MarketingActivityConstants.Field.Status.apiName).lableName(MarketingActivityConstants.Field.Status.label).renderType(RenderType.SelectOne.renderType)
                .build());

        TableComponent tableComponent = TableComponentBuilder.builder().refObjectApiName(MarketingActivityConstants.API_NAME).includeFields(tableColumns).buttons(null).build();
        List<IComponent> components = Lists.newArrayList(tableComponent);
        return LayoutBuilder.builder().tenantId(tenantId).createBy(fsUserId).name(MarketingActivityConstants.LIST_LAYOUT_API_NAME).displayName(MarketingActivityConstants.LIST_LAYOUT_DISPLAY_NAME)
            .isDefault(false).refObjectApiName(MarketingActivityConstants.API_NAME).layoutType(SystemConstants.LayoutType.List.layoutType).components(components).build();
    }
}
