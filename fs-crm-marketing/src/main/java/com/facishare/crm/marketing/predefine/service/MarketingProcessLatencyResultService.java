package com.facishare.crm.marketing.predefine.service;

import com.facishare.crm.marketing.result.InitMarketingProcessResult;
import com.facishare.crm.marketing.result.InitResult;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 09/04/2019
 */
@ServiceModule("marketingProcessLatencyResult")
public interface MarketingProcessLatencyResultService {
    /**
     * 营销活动初始化
     *
     * @return InitUserMarketingAccountResult
     **/
    @ServiceMethod("init_marketing_process_latency_result")
    InitResult initMarketingProcess(ServiceContext serviceContext);
}
