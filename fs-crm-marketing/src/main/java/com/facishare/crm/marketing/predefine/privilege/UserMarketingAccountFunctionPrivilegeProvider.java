package com.facishare.crm.marketing.predefine.privilege;

import com.facishare.crmcommon.constants.CommonConstants;
import com.facishare.crm.marketing.constants.UserMarketingAccountConstants;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.privilege.model.DefaultFunctionPrivilegeProvider;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Component;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 21/03/2019
 */
@Component
public class UserMarketingAccountFunctionPrivilegeProvider extends DefaultFunctionPrivilegeProvider {

    private final static List<String> supportActionCodes = Lists.newArrayList(

        ObjectAction.DELETE.getActionCode(),

        ObjectAction.VIEW_DETAIL.getActionCode(),

        ObjectAction.VIEW_LIST.getActionCode(),

        ObjectAction.UPDATE.getActionCode(),

        ObjectAction.BATCH_EXPORT.getActionCode(),

        ObjectAction.INVALID.getActionCode(),

        ObjectAction.RECOVER.getActionCode(),

        ObjectAction.PRINT.getActionCode(),

        ObjectAction.CHANGE_OWNER.getActionCode(),

        ObjectAction.ADD_TEAM_MEMBER.getActionCode(),

        ObjectAction.EDIT_TEAM_MEMBER.getActionCode(),

        ObjectAction.DELETE_TEAM_MEMBER.getActionCode(),

        ObjectAction.RELATE.getActionCode(),

        ObjectAction.BULK_RELATE.getActionCode(),

        ObjectAction.BULK_DISRELATE.getActionCode(),

        ObjectAction.START_BPM.getActionCode(),

        ObjectAction.VIEW_ENTIRE_BPM.getActionCode(),

        ObjectAction.STOP_BPM.getActionCode(),

        ObjectAction.CHANGE_BPM_APPROVER.getActionCode(),

        ObjectAction.ADD_EVENT.getActionCode(),

        ObjectAction.LOCK.getActionCode(),

        ObjectAction.UNLOCK.getActionCode(),

        ObjectAction.SALE_RECORD.getActionCode(),

        ObjectAction.DIAL.getActionCode(),

        ObjectAction.SEND_MAIL.getActionCode(),

        ObjectAction.DISCUSS.getActionCode(),

        ObjectAction.SCHEDULE.getActionCode(),

        ObjectAction.REMIND.getActionCode()

    );

    private final static List<String> observerSupportActionCodes = Lists.newArrayList(

        ObjectAction.VIEW_LIST.getActionCode(),

        ObjectAction.VIEW_DETAIL.getActionCode()

    );

    @Override
    public String getApiName() {
        return UserMarketingAccountConstants.API_NAME;
    }

    @Override
    public List<String> getSupportedActionCodes() {
        return Collections.unmodifiableList(supportActionCodes);
    }

    @Override
    public Map<String, List<String>> getCustomInitRoleActionCodes() {
        Map<String, List<String>> actionCodeMap = Maps.newHashMap();
        /**CRM管理者*/
        actionCodeMap.put(CommonConstants.CRM_MANAGER_ROLE, Collections.unmodifiableList(supportActionCodes));
        /**CRM观察者*/
        actionCodeMap.put(CommonConstants.CRM_OBSERVER_ROLE, Collections.unmodifiableList(observerSupportActionCodes));
        return Collections.unmodifiableMap(actionCodeMap);
    }
}
