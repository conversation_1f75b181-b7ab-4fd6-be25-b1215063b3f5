package com.facishare.crm.marketing.predefine.manager;

import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: dongzhb
 * @date: 2019/2/20
 * @Description: 记录初始化配置
 */
@Slf4j
@Component
public class ConfigMarketingManager {
    private static final String marketingActivityStatuskey = ":marketing_status";
    @Autowired
    private ConfigService configService;

    public void createOrUpdateTenantConfig(User user, String value) {
        String key = user.getTenantId() + marketingActivityStatuskey;
        String configValue = configService.findTenantConfig(user, key);
        if (configValue == null) {
            configService.createTenantConfig(user, key, value, ConfigValueType.STRING);
        } else {
            configService.updateTenantConfig(user, key, value, ConfigValueType.STRING);
        }
    }

    public String findTenantConfig(User user) {
        String config = configService.findTenantConfig(user, user.getTenantId() + marketingActivityStatuskey);
        return config;
    }
}
