package com.facishare.crm.marketing.predefine.manager;

import com.facishare.converter.EIEAConverter;
import com.facishare.crm.marketing.arg.PaasQueryArg;
import com.facishare.crmcommon.util.SearchQueryBuilder;
import com.facishare.marketing.common.contstant.OperatorConstants;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.rest.core.util.JsonUtil;
import com.fxiaoke.crmrestapi.arg.FindByQueryV3Arg;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.InnerPage;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.QueryBySearchTemplateV3Result;
import com.fxiaoke.crmrestapi.service.ObjectDataServiceV3;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class CouponValidator {
    @Autowired
    private ObjectDataServiceV3 objectDataServiceV3;

    private static final String COUPON_NO_CURRENT_OPERATION_POSSIBLE = "marketing.coupon_no_current_operation_possible";

    public  void validateHasCouponInstance(User user, List<IObjectData> objectDataList) {
        List<String> ids = objectDataList.stream().map(IObjectData::getId).collect(Collectors.toList());
        Map<String, Integer> queryCouponInstanceCountMap = queryCouponInstanceCountMap(user, ids);
        queryCouponInstanceCountMap.forEach((id, count) -> {
            if (count > 0) {
                throw new ValidateException(I18N.text(COUPON_NO_CURRENT_OPERATION_POSSIBLE));
            }
        });
    }

    private  Map<String, Integer> queryCouponInstanceCountMap(User user, List<String> ids) {
        Map<String, Integer> queryCouponInstanceCountMap = Maps.newHashMap();
        ids.forEach(id -> {
            InnerPage<ObjectData> pageResult = findCrmObjectsByFilterV3(user, id);
            int totalRelationCount = pageResult.getTotalCount() == null ? 0 : pageResult.getTotalCount();
            queryCouponInstanceCountMap.put(id, totalRelationCount);
        });
        return queryCouponInstanceCountMap;
    }

    private InnerPage<ObjectData> findCrmObjectsByFilterV3(User user, String id) {
        Result<QueryBySearchTemplateV3Result> result = null;
        try {
            PaasQueryArg paasQueryFilterArg = new PaasQueryArg(0,1);
            paasQueryFilterArg.addFilter("coupon_stock_id", OperatorConstants.EQ, Lists.newArrayList(id));
            FindByQueryV3Arg findByQueryV3Arg = new FindByQueryV3Arg();
            findByQueryV3Arg.setDescribeApiName(CrmObjectApiNameEnum.COUPON_INST_OBJ.getName());
            findByQueryV3Arg.setSearchQueryInfoJson(JsonUtil.toJson(paasQueryFilterArg));
            findByQueryV3Arg.setSelectFields(Lists.newArrayList("_id"));
            result = objectDataServiceV3.findByQuery(HeaderObj.newInstance(user.getTenantIdInt(), user.getUserIdInt()), findByQueryV3Arg);
        } catch (Exception e) {
            log.warn("objectDataServiceV3.findByQuery fail , error result ");
            InnerPage<ObjectData> innerPage = new InnerPage<>();
            innerPage.setDataList(Lists.newArrayList());
            innerPage.setTotalCount(0);
            return innerPage;
        }
        //没有初始化对象
        if(result.getCode() == 201111055){
            InnerPage<ObjectData> innerPage = new InnerPage<>();
            innerPage.setDataList(Lists.newArrayList());
            innerPage.setTotalCount(0);
            return innerPage;
        }
        if (result.getCode() != 0) {
            log.warn("listCrmObjectsByFilter fail , error result " + result);
            InnerPage<ObjectData> innerPage = new InnerPage<>();
            innerPage.setDataList(Lists.newArrayList());
            innerPage.setTotalCount(0);
            return innerPage;
        }
        if(result.getData() == null || result.getData().getQueryResult() == null){
            InnerPage<ObjectData> innerPage = new InnerPage<>();
            innerPage.setDataList(Lists.newArrayList());
            innerPage.setTotalCount(0);
            return innerPage;
        }
        InnerPage<ObjectData> innerPage = new InnerPage<>();
        innerPage.setDataList(result.getData().getQueryResult().getDataList());
        innerPage.setTotalCount(result.getData().getQueryResult().getTotalNumber());
        return innerPage;
    }

}
