package com.facishare.crm.marketing.predefine.manager;

import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 22/03/2019
 */

@Slf4j
@Component
public class ConfigUserMarketingAccountManager {
    private static final String userMarketingAccountStatuskey = ":user_marketing_account_status";
    @Autowired
    private ConfigService configService;

    public void createOrUpdateTenantConfig(User user, String value) {
        String key = user.getTenantId() + userMarketingAccountStatuskey;
        String configValue = configService.findTenantConfig(user, key);
        if (configValue == null) {
            configService.createTenantConfig(user, key, value, ConfigValueType.STRING);
        } else {
            configService.updateTenantConfig(user, key, value, ConfigValueType.STRING);
        }
    }

    public String findTenantConfig(User user) {
        String config = configService.findTenantConfig(user, user.getTenantId() + userMarketingAccountStatuskey);
        return config;
    }
}
