package com.facishare.crm.marketing.predefine.action;

import com.facishare.crm.marketing.predefine.manager.CouponValidator;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CouponBulkInvalidAction extends StandardBulkInvalidAction {

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        log.info("CouponBulkInvalidAction before");
        CouponValidator couponValidator = SpringUtil.getContext().getBean(CouponValidator.class);
        couponValidator.validateHasCouponInstance(actionContext.getUser(),objectDataList);
    }
}
