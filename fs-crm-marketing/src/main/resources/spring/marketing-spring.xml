<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns="http://www.springframework.org/schema/beans"
       xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/aop
         http://www.springframework.org/schema/aop/spring-aop.xsd
         http://www.springframework.org/schema/beans
         http://www.springframework.org/schema/beans/spring-beans.xsd">
    <import resource="classpath*:/META-INF/spring/pod-api-client.xml"/>

    <bean id="marketingAccessLog" class="com.facishare.crm.marketing.interceptor.LogInterceptor">
        <constructor-arg type="java.lang.String" value="marketingAccess"/>
        <constructor-arg type="java.lang.String" value=""/>
    </bean>

    <bean id="nomonProducer" class="com.fxiaoke.paas.gnomon.api.NomonProducer"/>

    <aop:config>
        <aop:aspect id="marketingLogMonitor" ref="marketingAccessLog" order="1">
            <aop:pointcut id="marketingLogAround"
                expression="(execution(* com.facishare.crm.marketing.predefine.*.*.*(..)))"/>
            <aop:around pointcut-ref="marketingLogAround" method="around"/>
        </aop:aspect>
    </aop:config>

</beans>
