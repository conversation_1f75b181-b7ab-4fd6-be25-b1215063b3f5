package com.facishare.crm.promotion.manager;

import com.facishare.crm.promotion.predefine.manager.SalesOrderManager;
import com.facishare.crm.promotion.util.PromotionValidateUtil;
import com.facishare.crmcommon.rest.dto.BatchGetPromotionProductQuantity;
import com.facishare.paas.appframework.core.model.User;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/applicationContext.xml")
public class SalesOrderManagerTest {
    @Autowired
    private SalesOrderManager salesOrderManager;

    static {
        System.setProperty("spring.profiles.active", "fstest");
    }

    @Test
    public void listByPromtionIdsTest() {
        List list = salesOrderManager.listRelatedPromotionObjectsByPromotionIds(new User("2", "1000"), Lists.newArrayList("5a55abea830bdbc4a5fa0a44"));
        System.out.println(list);
    }

    @Test
    public void testGetPromotionQuantity() {
        List<BatchGetPromotionProductQuantity.PromotionProductArg> promotionProductArgs = Lists.newArrayList();
        BatchGetPromotionProductQuantity.PromotionProductArg arg = new BatchGetPromotionProductQuantity.PromotionProductArg();
        arg.setProductId("5c886b648fccf23eb8072ac2");
        arg.setPromotionId("5cbd32467cfed9ea0cca5b80");
        promotionProductArgs.add(arg);

        arg = new BatchGetPromotionProductQuantity.PromotionProductArg();
        arg.setProductId("5ca180a2a5083db8bac4179b");
        arg.setPromotionId("5cbd31b77cfed9ea0cca5335");
        promotionProductArgs.add(arg);

        List<BatchGetPromotionProductQuantity.PromotionProductQuantity> result = salesOrderManager.getPromotionQuantity(new User("74735", "1000"), promotionProductArgs, null);
        System.out.println(result);
    }

    @Test
    public void invalidValidateTest(){
        Map<String,String> promotionIdNameMap = Maps.newHashMap();
        promotionIdNameMap.put("5cb5beeda5083da17fdd83f5","aaaaa");
        promotionIdNameMap.put("5cbd31b77cfed9ea0cca5335","bbbbb");
        PromotionValidateUtil.invalidValidateOpt(new User("74735","1000"),promotionIdNameMap);
        System.out.println();
    }

    @Test
    public void batchGetPromotionPackageTest(){
        User user = User.builder().tenantId("78437").userId("-10000").build();
        Map<String, BigDecimal> packageMap = salesOrderManager.batchGetUsedPromotionPackageNum(user, Sets.newHashSet("5da17ec60ea77e000167adf8","5de4b424b40a440001041d46","5de4b3f5b40a440001041c49"));
        System.out.println("packageMap="+packageMap);
    }

}
