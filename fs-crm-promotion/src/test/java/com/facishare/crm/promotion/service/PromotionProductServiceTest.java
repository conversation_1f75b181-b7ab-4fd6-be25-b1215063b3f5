package com.facishare.crm.promotion.service;

import com.facishare.crm.promotion.base.BaseServiceTest;
import com.facishare.crm.promotion.constants.AdvertisementConstants;
import com.facishare.crm.promotion.predefine.service.PromotionProductService;
import com.facishare.crm.promotion.predefine.service.dto.PageArg;
import com.facishare.crm.promotion.predefine.service.dto.PromotionProductModel;
import com.facishare.paas.appframework.core.model.ServiceContext;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * @author: dongzhb
 * @date: 2019/12/20
 * @Description:
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/applicationContext.xml")
public class PromotionProductServiceTest extends BaseServiceTest {

    public PromotionProductServiceTest() {
        super("");
    }

    static {
        System.setProperty("spring.profiles.active", "fstest");
    }

    @Autowired
    private PromotionProductService promotionProductService;

    @Test
    public void listPromotionProduct() {
        ServiceContext serviceContext = newServiceContext();
        PromotionProductModel.Arg arg = new PromotionProductModel.Arg();
        arg.setPriceBookId("5d0a1427a5083d5d682bf0f1");
//        arg.setWarehouseId("5b0780eea5083dd1fdb65942");
        arg.setPageNumber(1);
        arg.setPageSize(100);
        PromotionProductModel.PromotionProductList promotionProductList = promotionProductService.listPromotionProduct(serviceContext, arg);
        log.info("promotionProductList:{}", promotionProductList);
    }


}
