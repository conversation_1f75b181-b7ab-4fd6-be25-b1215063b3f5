package com.facishare.crm.promotion.service;

import com.facishare.crm.promotion.base.BaseServiceTest;
import com.facishare.crm.promotion.constants.PromotionConstants;
import com.facishare.crm.promotion.constants.PromotionProductConstants;
import com.facishare.crm.promotion.constants.PromotionRuleConstants;
import com.facishare.crm.promotion.enums.PromotionRecordTypeEnum;
import com.facishare.crm.promotion.enums.PromotionRuleRecordTypeEnum;
import com.facishare.crm.promotion.predefine.service.ProductPromotionService;
import com.facishare.crm.promotion.predefine.service.PromotionInitService;
import com.facishare.crm.promotion.predefine.service.PromotionService;
import com.facishare.crm.promotion.predefine.service.dto.BatchGetProductQuotaByProductIdsModel;
import com.facishare.crm.promotion.predefine.service.dto.PromotionProductModel;
import com.facishare.crm.promotion.predefine.service.dto.PromotionType;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.RecordTypeAuthProxy;
import com.facishare.paas.appframework.metadata.dto.auth.RoleInfoModel;
import com.facishare.paas.appframework.privilege.UserRoleInfoServiceImpl;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.Where;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static com.facishare.paas.appframework.privilege.util.Headers.PAAS_PRIVILEGE_HEADDER;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/applicationContext.xml")
public class PromotionServiceTest extends BaseServiceTest {
    @Autowired
    private PromotionService promotionService;
    @Autowired
    private PromotionInitService promotionInitService;
    @Autowired
    private RecordTypeAuthProxy recordTypeAuthApi;
    @Autowired
    private IObjectDescribeService objectDescribeService;
    @Autowired
    private UserRoleInfoServiceImpl userRoleInfoService;
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private ProductPromotionService productPromotionService;
    @Autowired
    private ConfigService configService;

    public PromotionServiceTest() {
        super(PromotionConstants.API_NAME);
    }

    static {
        System.setProperty("spring.profiles.active", "fstest");
    }

    @Test
    public void updateConfigTest() {
        User user = User.systemUser("81367");
        configService.updateTenantConfig(user, "is_customer_account_enable", "2", ConfigValueType.STRING);
        System.out.println();
    }

    @Test
    public void findDetailDataListTest() {
        List<IObjectDescribe> objectDescribeList = serviceFacade.findDetailDescribes(tenantId, PromotionConstants.API_NAME);
        IObjectData masterData = new ObjectData();
        masterData.setDescribeApiName(PromotionConstants.API_NAME);
        masterData.setId("5a6a9ee7a5083dfd9a1a563f");
        masterData.setTenantId(tenantId);
        Map<String, List<IObjectData>> dataMap = serviceFacade.findDetailObjectDataList(objectDescribeList, masterData, new User(tenantId, fsUserId));
        System.out.println(dataMap);
    }

    @Test
    public void roleInfoTest() {
        userRoleInfoService.getMainRoleLayoutAPIName(new User("2", "-10000"), "CustomerPaymentObj", "default__c");
    }

    @Test
    public void describeFixTest1() throws MetadataServiceException {
        List<LinkedHashMap> wheres = Lists.newArrayList();
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        map.put("connector", Where.CONN.OR);
        List<Filter> filters = Lists.newArrayList();
        Filter filter = new Filter();
        filter.setFieldName("is_giveaway");
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList("0"));//0表示是非赠品
        filters.add(filter);
        map.put("filters", filters);
        wheres.add(map);

        IObjectDescribe objectDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(PromotionProductConstants.API_NAME, tenantId);
        ObjectDescribeExt objectDescribeExt = ObjectDescribeExt.of(objectDescribe);
        ObjectReferenceFieldDescribe objectReferenceFieldDescribe = (ObjectReferenceFieldDescribe) objectDescribeExt.getFieldDescribe(PromotionProductConstants.Field.Product.apiName);
        objectReferenceFieldDescribe.setWheres(wheres);
        IObjectDescribe updateDescribe = objectDescribeService.updateFieldDescribe(objectDescribe, Lists.newArrayList(objectReferenceFieldDescribe));
        //        objectDescribeService.update(objectDescribe,true);
        System.out.println(updateDescribe);

    }


    //TODO  CPQ适配多促销
    @Test
    public void updatePromotionProductAttributesValues() {
//        String moduleConfig = configService.findTenantConfig(new User("74327", "-10000"), "cpq");
//        log.info("moduleConfig:{}",moduleConfig);
        ServiceContext serviceContext = new ServiceContext(requestContext, null, null);
        PromotionProductModel.Arg arg = new PromotionProductModel.Arg();
        arg.setTenantIds(Lists.newArrayList("74327"));
        PromotionProductModel.Result result = productPromotionService.batchUpdatePromotionProductAdaptiveCPQ(serviceContext, arg);
        log.info("result:{}", result);
    }


    @Test
    public void queryProductDescribeTest() throws MetadataServiceException {
        IObjectDescribe objectDescribe = objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, "ProductObj");
        System.out.print(objectDescribe);
    }

    @Test
    public void enablePromotionTest() {
        PromotionType.EnableResult enableResult = promotionService.enablePromotion(newServiceContext());
        System.out.println(enableResult);
    }

    @Test
    public void recordTypeTest() {
        User user = new User(tenantId, fsUserId);
        RoleInfoModel.Arg roleInfoModelArg = new RoleInfoModel.Arg();
        roleInfoModelArg.setAuthContext(user);
        RoleInfoModel.Result result = recordTypeAuthApi.roleInfo(roleInfoModelArg, PAAS_PRIVILEGE_HEADDER.buildHeader(user.getTenantId()));
        System.out.println(result);
        promotionInitService
                .initProductRecordType(user, PromotionConstants.API_NAME, PromotionRecordTypeEnum.ProductPromotion.getApiName(), PromotionConstants.DEFAULT_LAYOUT_API_NAME, result.getResult().getRoles());
        promotionInitService.initProductRecordType(user, PromotionRuleConstants.API_NAME, PromotionRuleRecordTypeEnum.ProductPromotion.getApiName(), PromotionRuleConstants.DEFAULT_LAYOUT_API_NAME,
                result.getResult().getRoles());
        System.out.println(result);
    }

    @Test
    public void getById() {
        ServiceContext serviceContext = newServiceContext();
        PromotionType.IdModel idModel = new PromotionType.IdModel();
        idModel.setId("5a55b20d830bdbc4a5fa0a5f");
        PromotionType.DetailResult detailResult = promotionService.getById(serviceContext, idModel);
        Assert.assertNotNull(detailResult);

    }

    @Test
    public void getByIds() {
        ServiceContext serviceContext = newServiceContext();
        PromotionType.IdsModel idModel = new PromotionType.IdsModel();
        idModel.setIds(Lists.newArrayList("5a55b20d830bdbc4a5fa0a5f"));
        List<PromotionType.DetailResult> detailResult = promotionService.getByIds(serviceContext, idModel);
        Assert.assertNotNull(detailResult);

    }

    @Test
    public void listByProductIds() {
        PromotionType.ProductPromotionListArg listProductsArg = new PromotionType.ProductPromotionListArg();
        listProductsArg.setCustomerId("5d0b35b68f37110001a53a2d");
        listProductsArg.setProductIds(
                Lists.newArrayList(
//                        "5d0b2c163d51c600017bdd7e",
//                        "5d0b36298f37110001a53b81",
//                        "5d0b379c8f37110001a54814",
//                        "5d109767aba80b000156e919",
//                        "5d1097caaba80b000156eb74",
//                        "5d14836e673c25000160a814",
//                        "5d14838d673c25000160a92b",
//                        "5d19c78d65f7b30001555d45",
//                        "5d6f61c2810464000141a127",
//                        "5dd74df7d598f50001330101"
                        "5d0b018d3d51c600017bd5f3",
                        "5dd74df8d598f5000133020c",
                        "5e041d14ab17b40001fc096b",
                        "5e1427e636dca30001270905",
                        "5e14280436dca300012709ec",
                        "5e1592a7b7b40c00019269e0",
                        "5e1592cfb7b40c0001927268",
                        "5e15930fb7b40c0001928da3",
                        "5e15b935df5f1200018b4d43",
                        "5e169a33b2da130001ce8844"
                ));
        PromotionType.ProductPromotionResult productPromotionResult = promotionService.listByProductIds(newServiceContext(), listProductsArg);
        log.info("productPromotionResult :{}", productPromotionResult);
    }

    @Test
    public void listByCustomerId() {
        PromotionType.CustomerIdArg customerIdArg = new PromotionType.CustomerIdArg();
        customerIdArg.setCustomerId("5d0b35b68f37110001a53a2d");
        PromotionType.PromotionRuleResult promotionRuleResult = promotionService.listByCustomerId(newServiceContext(), customerIdArg);
        Assert.assertNotNull(promotionRuleResult);
    }

    @Test
    public void isPromotionEnable() {
        ServiceContext serviceContext = newServiceContext();
        Boolean b = promotionService.isPromotionEnable(serviceContext).getEnable();
        Assert.assertNotNull(b);

    }

    @Test
    public void batchGetProductQuotaByProductIdsTest() {
        ServiceContext serviceContext = newServiceContext();
        BatchGetProductQuotaByProductIdsModel.Arg arg = new BatchGetProductQuotaByProductIdsModel.Arg();
        BatchGetProductQuotaByProductIdsModel.PromotionProductIdArg promotionProductIdArg = new BatchGetProductQuotaByProductIdsModel.PromotionProductIdArg();
        promotionProductIdArg.setAmount(BigDecimal.valueOf(3.0));
        promotionProductIdArg.setPromotionId("5ad96492bab09c993bded543");
        promotionProductIdArg.setProductId("5ad96493bab09c993bded584");
        arg.setPromotionProductIdArgs(Lists.newArrayList(promotionProductIdArg));
        promotionService.batchGetProductQuotaByProductIds(serviceContext, arg);
    }

    @Test
    public void testBatchQueryPromotionBySpuIds() {
        ServiceContext serviceContext = newServiceContext();
        List<String> spuIds = Lists.newArrayList("5c497e45a5083d518b51d0c0");
        PromotionType.SpuIdsArg spuIdsArg = new PromotionType.SpuIdsArg();
        spuIdsArg.setCustomerId("bef14ef4d9c9448daf7767d8264175da");
        spuIdsArg.setSpuIds(spuIds);
        try {
            PromotionType.SpuToPromotionFlagResult result = promotionService.batchQueryPromotionBySpuIds(serviceContext, spuIdsArg);
            System.out.println(result);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void findByIdsTest() {
        List<IObjectData> dataList = serviceFacade.findObjectDataByIds("78437", Lists.newArrayList("5e0d88fa9adf74287c08c903"), "PromotionGiftObj");
        System.out.println(dataList);
    }

    @Test
    public void testListPromotionProductsByCustomerId() {
        ServiceContext serviceContext = newServiceContext();
        PromotionType.ListProductsArg productsArg = new PromotionType.ListProductsArg();
        productsArg.setCustomerId("5d0b35b68f37110001a53a2d");

        PromotionType.PromotionProductResult result = promotionService.listPromotionProductsByCustomerId(serviceContext, productsArg);
//        log.info("promotionProducts={}", result.getPromotionProducts());
        log.info("result={}", result);
    }

    @Test
    public void testValidatePromotionsForSalesOrder_1() {
        // 促销按全部产品规则设置
        ServiceContext serviceContext = newServiceContext();
        Map<String, Object> objectData = Maps.newHashMap();
        objectData.put("object_describe_api_name", "SalesOrderObj");
        objectData.put("record_type", "default__c");
        objectData.put("account_id", "5d0b35b68f37110001a53a2d");
        objectData.put("order_amount", "5147.70");
        objectData.put("product_amount", "5147.7");
        objectData.put("discount", "100");
        objectData.put("promotion_id", "5ede0eb2bb21910001b4d416");

        List<Map<String, Object>> details = Lists.newArrayList();
        Map<String, Object> orderProduct = Maps.newHashMap();
        orderProduct.put("object_describe_api_name", "SalesOrderProductObj");
        orderProduct.put("record_type", "default__c");
        orderProduct.put("product_id", "5e14280436dca300012709ec");
        orderProduct.put("product_id__r", "2号水泥");
        orderProduct.put("product_price", "1287.990");
        orderProduct.put("discount", 90);
        orderProduct.put("sales_price", "1159.19");
        orderProduct.put("quantity", "2");
        orderProduct.put("subtotal", "2318.4");
        orderProduct.put("is_giveaway", "0");
        orderProduct.put("promotion_id", "5e16c34da371ed0001c54460");
        orderProduct.put("order_product_amount", "2318.4");
        details.add(orderProduct);

        orderProduct = Maps.newHashMap();
        orderProduct.put("object_describe_api_name", "SalesOrderProductObj");
        orderProduct.put("record_type", "default__c");
        orderProduct.put("product_id", "5e1427e636dca30001270905");
        orderProduct.put("product_id__r", "1号水泥");
        orderProduct.put("product_price", "1178.890");
        orderProduct.put("discount", 80);
        orderProduct.put("sales_price", "943.11");
        orderProduct.put("quantity", "3");
        orderProduct.put("subtotal", "2829.3");
        orderProduct.put("is_giveaway", "0");
        orderProduct.put("promotion_id", "5e16c34da371ed0001c54460");
        orderProduct.put("order_product_amount", "2829.30");
        details.add(orderProduct);

        Map<String, Object> orderProductGift = Maps.newHashMap();
        orderProductGift.put("object_describe_api_name", "SalesOrderProductObj");
        orderProductGift.put("record_type", "default__c");
        orderProductGift.put("product_id", "5ec63743dc74ad0001d647d0");
        orderProductGift.put("sales_price", 100);
        orderProductGift.put("quantity", 1);
        orderProductGift.put("is_giveaway", "1");
        orderProductGift.put("promotion_id", "5ede0eb2bb21910001b4d416");
        details.add(orderProductGift);

        orderProductGift = Maps.newHashMap();
        orderProductGift.put("object_describe_api_name", "SalesOrderProductObj");
        orderProductGift.put("record_type", "default__c");
        orderProductGift.put("product_id", "5dd74de8d598f5000132e7bf");
        orderProductGift.put("sales_price", 50);
        orderProductGift.put("quantity", 1);
//        orderProductGift.put("quantity", 2);  // wrong
        orderProductGift.put("is_giveaway", "1");
        orderProductGift.put("promotion_id", "5ede0eb2bb21910001b4d416");
        details.add(orderProductGift);

        orderProductGift = Maps.newHashMap();
        orderProductGift.put("object_describe_api_name", "SalesOrderProductObj");
        orderProductGift.put("record_type", "default__c");
        orderProductGift.put("product_id", "5dd74de3d598f5000132e072");
        orderProductGift.put("sales_price", 50);
        orderProductGift.put("quantity", 1);
        orderProductGift.put("is_giveaway", "1");
        orderProductGift.put("promotion_id", "5ede0eb2bb21910001b4d416");
        details.add(orderProductGift);

        orderProductGift = Maps.newHashMap();
        orderProductGift.put("object_describe_api_name", "SalesOrderProductObj");
        orderProductGift.put("record_type", "default__c");
        orderProductGift.put("product_id", "5dd74df3d598f5000132fc23");
        orderProductGift.put("sales_price", 50);
        orderProductGift.put("quantity", 1);
        orderProductGift.put("is_giveaway", "1");
        orderProductGift.put("promotion_id", "5ede0eb2bb21910001b4d416");
        details.add(orderProductGift);

        orderProductGift = Maps.newHashMap();
        orderProductGift.put("object_describe_api_name", "SalesOrderProductObj");
        orderProductGift.put("record_type", "default__c");
        orderProductGift.put("product_id", "5e15930fb7b40c0001928da3");
        orderProductGift.put("sales_price", 25);
        orderProductGift.put("quantity", 1);
        orderProductGift.put("is_giveaway", "1");
        orderProductGift.put("promotion_id", "5ede0eb2bb21910001b4d416");
        details.add(orderProductGift);

        orderProductGift = Maps.newHashMap();
        orderProductGift.put("object_describe_api_name", "SalesOrderProductObj");
        orderProductGift.put("record_type", "default__c");
        orderProductGift.put("product_id", "5dd74df2d598f5000132fb71");
        orderProductGift.put("sales_price", 50);
        orderProductGift.put("quantity", 1);
        orderProductGift.put("is_giveaway", "1");
        orderProductGift.put("promotion_id", "5ede0eb2bb21910001b4d416");
        details.add(orderProductGift);

        PromotionType.ValidatePromotionsArg arg = new PromotionType.ValidatePromotionsArg();
        arg.setObjectData(objectData);
        arg.setDetails(details);
        PromotionType.ValidatePromotionsResult result = promotionService.validatePromotionsForSalesOrder(serviceContext, arg);
        log.info("result={}", result);
    }

    @Test
    public void testValidatePromotionsForSalesOrder_2() {
        // 促销按单一产品规则设置
        ServiceContext serviceContext = newServiceContext();
        Map<String, Object> objectData = Maps.newHashMap();
        objectData.put("object_describe_api_name", "SalesOrderObj");
        objectData.put("record_type", "default__c");
        objectData.put("account_id", "5d0b35b68f37110001a53a2d");
        objectData.put("order_amount", "300");
        objectData.put("product_amount", "300.0");
        objectData.put("discount", "100");

        List<Map<String, Object>> details = Lists.newArrayList();
        Map<String, Object> orderProduct = Maps.newHashMap();
        orderProduct.put("object_describe_api_name", "SalesOrderProductObj");
        orderProduct.put("record_type", "default__c");
        orderProduct.put("product_id", "5e132326c881a10001caad04");
        orderProduct.put("product_id__r", "多规格规格[寸v恢复个]");
        orderProduct.put("product_price", "100.00");
        orderProduct.put("discount", 100);
        orderProduct.put("sales_price", "100.00");
        orderProduct.put("quantity", "3");
        orderProduct.put("subtotal", "300.0");
        orderProduct.put("is_giveaway", "0");
        orderProduct.put("promotion_id", "5ea4ff6f47d944000138faab");
        orderProduct.put("order_product_amount", "300.00");
        details.add(orderProduct);

        Map<String, Object> orderProductGift = Maps.newHashMap();
        orderProductGift.put("object_describe_api_name", "SalesOrderProductObj");
        orderProductGift.put("record_type", "default__c");
        orderProductGift.put("product_id", "5e169a33b2da130001ce8844");
        orderProductGift.put("sales_price", "0.00");
        orderProductGift.put("quantity", 2);
        orderProductGift.put("is_giveaway", "1");
        orderProductGift.put("promotion_id", "5ea4ff6f47d944000138faab");
        details.add(orderProductGift);

        PromotionType.ValidatePromotionsArg arg = new PromotionType.ValidatePromotionsArg();
        arg.setObjectData(objectData);
        arg.setDetails(details);
        PromotionType.ValidatePromotionsResult result = promotionService.validatePromotionsForSalesOrder(serviceContext, arg);
        log.info("result={}", result);
    }


    @Test
    public void testValidatePromotionsForSalesOrder_3() {
        // 促销按单一产品规则设置
        ServiceContext serviceContext = newServiceContext();
        Map<String, Object> objectData = Maps.newHashMap();
        objectData.put("object_describe_api_name", "SalesOrderObj");
        objectData.put("record_type", "default__c");
        objectData.put("account_id", "5eff1b8546216e0001962c5e");
        objectData.put("order_amount", "775.00");
        objectData.put("product_amount", "775.00");
        objectData.put("discount", "100");
        objectData.put("_id", "5f0bf949fc30830001cdfd34");

        List<Map<String, Object>> details = Lists.newArrayList();
        Map<String, Object> orderProduct = Maps.newHashMap();
        orderProduct.put("object_describe_api_name", "SalesOrderProductObj");
        orderProduct.put("record_type", "default__c");
        orderProduct.put("product_id", "5e5f271fddb6b40001a71fb0");
        orderProduct.put("product_id__r", "小柿子");
        orderProduct.put("product_price", "25");
        orderProduct.put("discount", 100);
        orderProduct.put("sales_price", "25");
        orderProduct.put("quantity", "31.0");
        orderProduct.put("subtotal", "775.00");
//        orderProduct.put("is_giveaway", "0");
        orderProduct.put("promotion_id", "5eff2125b418c50001ed594a");
//        orderProduct.put("promotion_id", "5eff2660b418c50001ed6816");
        orderProduct.put("order_product_amount", "775.00");
        details.add(orderProduct);

        Map<String, Object> orderProductGift = Maps.newHashMap();
        orderProductGift.put("object_describe_api_name", "SalesOrderProductObj");
        orderProductGift.put("record_type", "default__c");
        orderProductGift.put("product_id", "5e5f271fddb6b40001a71fb0");
        orderProductGift.put("sales_price", "0.00");
        orderProductGift.put("quantity", 10);
        orderProductGift.put("is_giveaway", "1");
        orderProductGift.put("promotion_id", "5eff2125b418c50001ed594a");
//        orderProductGift.put("promotion_id", "5eff2660b418c50001ed6816");
        details.add(orderProductGift);

        PromotionType.ValidatePromotionsArg arg = new PromotionType.ValidatePromotionsArg();
        arg.setObjectData(objectData);
        arg.setDetails(details);
        PromotionType.ValidatePromotionsResult result = promotionService.validatePromotionsForSalesOrder(serviceContext, arg);
        log.info("result={}", result);
    }

    @Test
    public void testValidatePromotionsForSalesOrder_4() {
        ServiceContext serviceContext = newServiceContext();
        Map<String, Object> objectData = Maps.newHashMap();
        objectData.put("object_describe_api_name", "SalesOrderObj");
        objectData.put("record_type", "default__c");
        objectData.put("account_id", "5ef955cabb86140001c052a0");
        objectData.put("order_amount", "500.00");
        objectData.put("product_amount", "500.00");
        objectData.put("discount", "100");
        objectData.put("promotion_id", "5efda1d12f066a000127a3cd");

        List<Map<String, Object>> details = Lists.newArrayList();
        Map<String, Object> orderProduct = Maps.newHashMap();
        orderProduct.put("object_describe_api_name", "SalesOrderProductObj");
        orderProduct.put("record_type", "default__c");
        orderProduct.put("product_id", "5e5f271fddb6b40001a71fb0");
        orderProduct.put("product_id__r", "小柿子");
        orderProduct.put("product_price", "25");
        orderProduct.put("discount", 100);
        orderProduct.put("sales_price", "25.00");
        orderProduct.put("quantity", "20.00");
        orderProduct.put("subtotal", "500.00");
        //        orderProduct.put("is_giveaway", "0");
        orderProduct.put("promotion_id", "5eff2660b418c50001ed6816");
        //        orderProduct.put("promotion_id", "5eff2660b418c50001ed6816");
        orderProduct.put("order_product_amount", "500.00");
        details.add(orderProduct);

        Map<String, Object> orderProductGift = Maps.newHashMap();
        orderProductGift.put("object_describe_api_name", "SalesOrderProductObj");
        orderProductGift.put("record_type", "default__c");
        orderProductGift.put("product_id", "5efbfb1b1fa0b6000199220a");
        orderProductGift.put("sales_price", "0.00");
        orderProductGift.put("quantity", 1);
        orderProductGift.put("is_giveaway", "1");
        orderProductGift.put("promotion_id", "5efda1d12f066a000127a3cd");
        //        orderProductGift.put("promotion_id", "5eff2660b418c50001ed6816");
        details.add(orderProductGift);

        orderProductGift = Maps.newHashMap();
        orderProductGift.put("object_describe_api_name", "SalesOrderProductObj");
        orderProductGift.put("record_type", "default__c");
        orderProductGift.put("product_id", "5e5f271fddb6b40001a71fb0");
        orderProductGift.put("sales_price", "0.00");
        orderProductGift.put("quantity", 10);
        orderProductGift.put("is_giveaway", "1");
        orderProductGift.put("promotion_id", "5eff2660b418c50001ed6816");
        //        orderProductGift.put("promotion_id", "5eff2660b418c50001ed6816");
        details.add(orderProductGift);

        PromotionType.ValidatePromotionsArg arg = new PromotionType.ValidatePromotionsArg();
        arg.setObjectData(objectData);
        arg.setDetails(details);
        PromotionType.ValidatePromotionsResult result = promotionService.validatePromotionsForSalesOrder(serviceContext, arg);
        log.info("result={}", result);
    }


    @Test
    public void testValidatePromotionsForSalesOrder_5() {
        ServiceContext serviceContext = newServiceContext();
        Map<String, Object> objectData = Maps.newHashMap();
        objectData.put("object_describe_api_name", "SalesOrderObj");
        objectData.put("record_type", "default__c");
        objectData.put("account_id", "5ef955cabb86140001c052a0");
        objectData.put("order_amount", "2600.00");
        objectData.put("product_amount", "2600.00");
        objectData.put("discount", "100");
        objectData.put("promotion_id", "5efc31e999e842000160f544");

        List<Map<String, Object>> details = Lists.newArrayList();
        Map<String, Object> orderProduct = Maps.newHashMap();
        orderProduct.put("object_describe_api_name", "SalesOrderProductObj");
        orderProduct.put("record_type", "default__c");
        orderProduct.put("product_id", "5f004b5dd0e9380001971e0e");
        orderProduct.put("product_id__r", "小柿子");
        orderProduct.put("product_price", "200.00");
        orderProduct.put("discount", 100);
        orderProduct.put("sales_price", "200.00");
        orderProduct.put("quantity", "13.00");
        orderProduct.put("subtotal", "2600.00");
        //        orderProduct.put("is_giveaway", "0");
//        orderProduct.put("promotion_id", "5eff2660b418c50001ed6816");
        //        orderProduct.put("promotion_id", "5eff2660b418c50001ed6816");
//        orderProduct.put("order_product_amount", "500.00");
        details.add(orderProduct);


        Map<String, Object> orderProductGift = Maps.newHashMap();
        orderProductGift.put("object_describe_api_name", "SalesOrderProductObj");
        orderProductGift.put("record_type", "default__c");
        orderProductGift.put("product_id", "5ef2fdcf20816e00014c169e");
        orderProductGift.put("sales_price", "0.00");
        orderProductGift.put("quantity", 3);
        orderProductGift.put("is_giveaway", "1");
        orderProductGift.put("promotion_id", "5efc31e999e842000160f544");
        //        orderProductGift.put("promotion_id", "5eff2660b418c50001ed6816");
        details.add(orderProductGift);

        PromotionType.ValidatePromotionsArg arg = new PromotionType.ValidatePromotionsArg();
        arg.setObjectData(objectData);
        arg.setDetails(details);
        PromotionType.ValidatePromotionsResult result = promotionService.validatePromotionsForSalesOrder(serviceContext, arg);
        log.info("result={}", result);
    }

    @Test
    public void testValidatePromotionsForSalesOrder_6() {
        ServiceContext serviceContext = newServiceContext();
        Map<String, Object> objectData = Maps.newHashMap();
        objectData.put("object_describe_api_name", "SalesOrderObj");
        objectData.put("record_type", "default__c");
        objectData.put("_id", "5f0d6b12ec95da00017cd1b3");
        objectData.put("name", "********-000047");
        objectData.put("account_id", "5f0d60dd7288ab000196e1fb");
        objectData.put("order_amount", "569.05");
        objectData.put("product_amount", "569.05");
        objectData.put("discount", "100");
        objectData.put("promotion_id", "5f0c30c41265560001767013");


        List<Map<String, Object>> details = Lists.newArrayList();
        Map<String, Object> orderProduct = Maps.newHashMap();
        orderProduct.put("object_describe_api_name", "SalesOrderProductObj");
        orderProduct.put("record_type", "default__c");
        orderProduct.put("product_id", "5f03e42b6217dc00016a1b0a");
        orderProduct.put("product_id__r", "【限量5个】Nike腰包");
        orderProduct.put("product_price", "599.00");
        orderProduct.put("discount", 95.000000);
        orderProduct.put("sales_price", "569.05");
        orderProduct.put("quantity", "1.00");
        orderProduct.put("subtotal", "569.05");
        orderProduct.put("promotion_id", "5f0d6acd12655600017e5eaa");
        details.add(orderProduct);


        Map<String, Object> orderProductGift = Maps.newHashMap();
        orderProductGift.put("object_describe_api_name", "SalesOrderProductObj");
        orderProductGift.put("record_type", "default__c");
        orderProductGift.put("product_id", "5f0533779f198000013e143b");
        orderProductGift.put("product_id__r", "洗衣液");
        orderProductGift.put("sales_price", "0.00");
        orderProductGift.put("quantity", 2.00);
        orderProductGift.put("is_giveaway", "1");
        orderProductGift.put("promotion_id", "5f0c30c41265560001767013");
        details.add(orderProductGift);

        PromotionType.ValidatePromotionsArg arg = new PromotionType.ValidatePromotionsArg();
        arg.setObjectData(objectData);
        arg.setDetails(details);
        PromotionType.ValidatePromotionsResult result = promotionService.validatePromotionsForSalesOrder(serviceContext, arg);
        log.info("result={}", result);
    }

    @Test
    public void testValidatePromotionsForSalesOrder_7() {
        ServiceContext serviceContext = newServiceContext();

        Map<String, Object> objectData = Maps.newHashMap();
        objectData.put("object_describe_api_name", "SalesOrderObj");
        objectData.put("record_type", "default__c");
        objectData.put("account_id", "5f0d60dd7288ab000196e1fb");
        objectData.put("order_amount", "780.00");
        objectData.put("product_amount", "780.00");
        objectData.put("discount", "100");
        objectData.put("promotion_id", "5f0c30c41265560001767013");

        List<Map<String, Object>> details = Lists.newArrayList();
        Map<String, Object> orderProduct = Maps.newHashMap();
        orderProduct.put("object_describe_api_name", "SalesOrderProductObj");
        orderProduct.put("record_type", "default__c");
        orderProduct.put("product_id", "5f0533c09f198000013e1554");
        orderProduct.put("product_id__r", "毛巾");
        orderProduct.put("product_price", "29.00");
        orderProduct.put("discount", 100.0000);
        orderProduct.put("sales_price", "29.00");
        orderProduct.put("quantity", "20.00");
        orderProduct.put("subtotal", "290.00");
        orderProduct.put("promotion_id", "5f48bd8d1e79e300013efde5");
        details.add(orderProduct);


        orderProduct = Maps.newHashMap();
        orderProduct.put("object_describe_api_name", "SalesOrderProductObj");
        orderProduct.put("record_type", "default__c");
        orderProduct.put("product_id", "5f0533989f198000013e14c7");
        orderProduct.put("product_id__r", "零食大礼包");
        orderProduct.put("product_price", "49.00");
        orderProduct.put("discount", 100.0000);
        orderProduct.put("sales_price", "49.00");
        orderProduct.put("quantity", "10.00");
        orderProduct.put("subtotal", "490.00");
        orderProduct.put("promotion_id", "5f48bd8d1e79e300013efde5");
        details.add(orderProduct);

        Map<String, Object> orderProductGift = Maps.newHashMap();
        orderProductGift.put("object_describe_api_name", "SalesOrderProductObj");
        orderProductGift.put("record_type", "default__c");
        orderProductGift.put("product_id", "5f0533779f198000013e143b");
        orderProductGift.put("product_id__r", "洗衣液");
        orderProductGift.put("sales_price", "0.00");
        orderProductGift.put("quantity", 5.00);
        orderProductGift.put("is_giveaway", "1");
        orderProductGift.put("promotion_id", "5f0c30c41265560001767013");
        details.add(orderProductGift);

        orderProductGift = Maps.newHashMap();
        orderProductGift.put("object_describe_api_name", "SalesOrderProductObj");
        orderProductGift.put("record_type", "default__c");
        orderProductGift.put("product_id", "5f0533c09f198000013e1554");
        orderProductGift.put("product_id__r", "毛巾");
        orderProductGift.put("sales_price", "0.00");
        orderProductGift.put("quantity", 2.00);
        orderProductGift.put("is_giveaway", "1");
        orderProductGift.put("promotion_id", "5f48bd8d1e79e300013efde5");
        details.add(orderProductGift);


        orderProductGift = Maps.newHashMap();
        orderProductGift.put("object_describe_api_name", "SalesOrderProductObj");
        orderProductGift.put("record_type", "default__c");
        orderProductGift.put("product_id", "5f0533c09f198000013e1554");
        orderProductGift.put("product_id__r", "毛巾");
        orderProductGift.put("sales_price", "0.00");
        orderProductGift.put("quantity", 4.00);
        orderProductGift.put("is_giveaway", "1");
        orderProductGift.put("promotion_id", "5f48bd8d1e79e300013efde5");
        details.add(orderProductGift);


        PromotionType.ValidatePromotionsArg arg = new PromotionType.ValidatePromotionsArg();
        arg.setObjectData(objectData);
        arg.setDetails(details);
        PromotionType.ValidatePromotionsResult result = promotionService.validatePromotionsForSalesOrder(serviceContext, arg);
        log.info("result={}", result);
    }

    @Test
    public void testValidatePromotionsForSalesOrder_8() {
        ServiceContext serviceContext = newServiceContext();

        Map<String, Object> objectData = Maps.newHashMap();
        objectData.put("object_describe_api_name", "SalesOrderObj");
        objectData.put("record_type", "default__c");
        objectData.put("account_id", "5f0d60dd7288ab000196e1fb");
        objectData.put("order_amount", "780.00");
        objectData.put("product_amount", "780.00");
        objectData.put("discount", "100");
        objectData.put("promotion_id", "5f0c30c41265560001767013");

        List<Map<String, Object>> details = Lists.newArrayList();
        Map<String, Object> orderProduct = Maps.newHashMap();
        orderProduct.put("object_describe_api_name", "SalesOrderProductObj");
        orderProduct.put("record_type", "default__c");
        orderProduct.put("product_id", "5f0533c09f198000013e1554");
        orderProduct.put("product_id__r", "毛巾");
        orderProduct.put("product_price", "29.00");
        orderProduct.put("discount", 100.0000);
        orderProduct.put("sales_price", "29.00");
        orderProduct.put("quantity", "20.00");
        orderProduct.put("subtotal", "290.00");
        orderProduct.put("promotion_id", "5f48bd8d1e79e300013efde5");
        details.add(orderProduct);

        orderProduct = Maps.newHashMap();
        orderProduct.put("object_describe_api_name", "SalesOrderProductObj");
        orderProduct.put("record_type", "default__c");
        orderProduct.put("product_id", "5f0533989f198000013e14c7");
        orderProduct.put("product_id__r", "零食大礼包");
        orderProduct.put("product_price", "49.00");
        orderProduct.put("discount", 100.0000);
        orderProduct.put("sales_price", "49.00");
        orderProduct.put("quantity", "10.00");
        orderProduct.put("subtotal", "490.00");
        orderProduct.put("promotion_id", "5f48bd8d1e79e300013efde5");
        details.add(orderProduct);

        Map<String, Object> orderProductGift = Maps.newHashMap();
//        orderProductGift.put("object_describe_api_name", "SalesOrderProductObj");
//        orderProductGift.put("record_type", "default__c");
//        orderProductGift.put("product_id", "5f0533779f198000013e143b");
//        orderProductGift.put("product_id__r", "洗衣液");
//        orderProductGift.put("sales_price", "0.00");
//        orderProductGift.put("quantity", 3.00);
//        orderProductGift.put("is_giveaway", "1");
//        orderProductGift.put("promotion_id", "5f0c30c41265560001767013");
//        details.add(orderProductGift);

        orderProductGift = Maps.newHashMap();
        orderProductGift.put("object_describe_api_name", "SalesOrderProductObj");
        orderProductGift.put("record_type", "default__c");
        orderProductGift.put("product_id", "5f0533c09f198000013e1554");
        orderProductGift.put("product_id__r", "毛巾");
        orderProductGift.put("sales_price", "0.00");
        orderProductGift.put("quantity", 2.00);
        orderProductGift.put("is_giveaway", "1");
        orderProductGift.put("promotion_id", "5f48bd8d1e79e300013efde5");
        details.add(orderProductGift);


        orderProductGift = Maps.newHashMap();
        orderProductGift.put("object_describe_api_name", "SalesOrderProductObj");
        orderProductGift.put("record_type", "default__c");
        orderProductGift.put("product_id", "5f0533c09f198000013e1554");
        orderProductGift.put("product_id__r", "毛巾");
        orderProductGift.put("sales_price", "0.00");
        orderProductGift.put("quantity", 4.00);
        orderProductGift.put("is_giveaway", "1");
        orderProductGift.put("promotion_id", "5f48bd8d1e79e300013efde5");
        details.add(orderProductGift);

        orderProductGift = Maps.newHashMap();
        orderProductGift.put("object_describe_api_name", "SalesOrderProductObj");
        orderProductGift.put("record_type", "default__c");
        orderProductGift.put("product_id", "5f0533989f198000013e14c7");
        orderProductGift.put("product_id__r", "零食大礼包");
        orderProductGift.put("sales_price", "0.00");
        orderProductGift.put("quantity", 4.00);
        orderProductGift.put("is_giveaway", "1");
        orderProductGift.put("promotion_id", "5f48bd8d1e79e300013efde5");
        details.add(orderProductGift);

        PromotionType.ValidatePromotionsArg arg = new PromotionType.ValidatePromotionsArg();
        arg.setObjectData(objectData);
        arg.setDetails(details);
        PromotionType.ValidatePromotionsResult result = promotionService.validatePromotionsForSalesOrder(serviceContext, arg);
        log.info("result={}", result);
    }

    @Test
    public void testValidatePromotionsForSalesOrder_9() {
        ServiceContext serviceContext = newServiceContext();

        Map<String, Object> objectData = Maps.newHashMap();
        objectData.put("object_describe_api_name", "SalesOrderObj");
        objectData.put("record_type", "default__c");
        objectData.put("account_id", "5f0d60dd7288ab000196e1fb");
        objectData.put("order_amount", "780.00");
        objectData.put("product_amount", "780.00");
        objectData.put("discount", "100");
        objectData.put("promotion_id", "5f0c30c41265560001767013");

        List<Map<String, Object>> details = Lists.newArrayList();
        Map<String, Object> orderProduct = Maps.newHashMap();
        orderProduct.put("object_describe_api_name", "SalesOrderProductObj");
        orderProduct.put("record_type", "default__c");
        orderProduct.put("product_id", "5f0533c09f198000013e1554");
        orderProduct.put("product_id__r", "毛巾");
        orderProduct.put("product_price", "29.00");
        orderProduct.put("discount", 100.0000);
        orderProduct.put("sales_price", "29.00");
        orderProduct.put("quantity", "20.00");
        orderProduct.put("subtotal", "290.00");
        orderProduct.put("promotion_id", "5f48bd8d1e79e300013efde5");
        details.add(orderProduct);

        orderProduct = Maps.newHashMap();
        orderProduct.put("object_describe_api_name", "SalesOrderProductObj");
        orderProduct.put("record_type", "default__c");
        orderProduct.put("product_id", "5f0533989f198000013e14c7");
        orderProduct.put("product_id__r", "零食大礼包");
        orderProduct.put("product_price", "49.00");
        orderProduct.put("discount", 100.0000);
        orderProduct.put("sales_price", "49.00");
        orderProduct.put("quantity", "10.00");
        orderProduct.put("subtotal", "490.00");
        orderProduct.put("promotion_id", "5f48bd8d1e79e300013efde5");
        details.add(orderProduct);

        Map<String, Object> orderProductGift = Maps.newHashMap();
        orderProductGift.put("object_describe_api_name", "SalesOrderProductObj");
        orderProductGift.put("record_type", "default__c");
        orderProductGift.put("product_id", "5f0533779f198000013e143b");
        orderProductGift.put("product_id__r", "洗衣液");
        orderProductGift.put("sales_price", "0.00");
        orderProductGift.put("quantity", 3.00);
        orderProductGift.put("is_giveaway", "1");
        orderProductGift.put("promotion_id", "5f0c30c41265560001767013");
        details.add(orderProductGift);

        orderProductGift = Maps.newHashMap();
        orderProductGift.put("object_describe_api_name", "SalesOrderProductObj");
        orderProductGift.put("record_type", "default__c");
        orderProductGift.put("product_id", "5f0533c09f198000013e1554");
        orderProductGift.put("product_id__r", "毛巾");
        orderProductGift.put("sales_price", "0.00");
        orderProductGift.put("quantity", 4.00);
        orderProductGift.put("is_giveaway", "1");
        orderProductGift.put("promotion_id", "5f48bd8d1e79e300013efde5");
        details.add(orderProductGift);

        orderProductGift = Maps.newHashMap();
        orderProductGift.put("object_describe_api_name", "SalesOrderProductObj");
        orderProductGift.put("record_type", "default__c");
        orderProductGift.put("product_id", "5f0533989f198000013e14c7");
        orderProductGift.put("product_id__r", "零食大礼包");
        orderProductGift.put("sales_price", "0.00");
        orderProductGift.put("quantity", 4.00);
        orderProductGift.put("is_giveaway", "1");
        orderProductGift.put("promotion_id", "5f48bd8d1e79e300013efde5");
        details.add(orderProductGift);

        PromotionType.ValidatePromotionsArg arg = new PromotionType.ValidatePromotionsArg();
        arg.setObjectData(objectData);
        arg.setDetails(details);
        PromotionType.ValidatePromotionsResult result = promotionService.validatePromotionsForSalesOrder(serviceContext, arg);
        log.info("result={}", result);
    }

    @Test
    public void testValidatePromotionsForSalesOrder_10() {
        ServiceContext serviceContext = newServiceContext();

        Map<String, Object> objectData = Maps.newHashMap();
        objectData.put("object_describe_api_name", "SalesOrderObj");
        objectData.put("record_type", "default__c");
        objectData.put("account_id", "5f0d60dd7288ab000196e1fb");
        objectData.put("order_amount", "780.00");
        objectData.put("product_amount", "780.00");
        objectData.put("discount", "100");
        objectData.put("promotion_id", "5f0c30c41265560001767013");

        List<Map<String, Object>> details = Lists.newArrayList();
        Map<String, Object> orderProduct = Maps.newHashMap();
        orderProduct.put("object_describe_api_name", "SalesOrderProductObj");
        orderProduct.put("record_type", "default__c");
        orderProduct.put("product_id", "5f0533c09f198000013e1554");
        orderProduct.put("product_id__r", "毛巾");
        orderProduct.put("product_price", "29.00");
        orderProduct.put("discount", 100.0000);
        orderProduct.put("sales_price", "29.00");
        orderProduct.put("quantity", "20.00");
        orderProduct.put("subtotal", "290.00");
        orderProduct.put("promotion_id", "5f48bd8d1e79e300013efde5");
        details.add(orderProduct);

        orderProduct = Maps.newHashMap();
        orderProduct.put("object_describe_api_name", "SalesOrderProductObj");
        orderProduct.put("record_type", "default__c");
        orderProduct.put("product_id", "5f0533989f198000013e14c7");
        orderProduct.put("product_id__r", "零食大礼包");
        orderProduct.put("product_price", "49.00");
        orderProduct.put("discount", 100.0000);
        orderProduct.put("sales_price", "49.00");
        orderProduct.put("quantity", "10.00");
        orderProduct.put("subtotal", "490.00");
        orderProduct.put("promotion_id", "5f48bd8d1e79e300013efde5");
        details.add(orderProduct);

        Map<String, Object> orderProductGift = Maps.newHashMap();
        orderProductGift.put("object_describe_api_name", "SalesOrderProductObj");
        orderProductGift.put("record_type", "default__c");
        orderProductGift.put("product_id", "5f0533779f198000013e143b");
        orderProductGift.put("product_id__r", "洗衣液");
        orderProductGift.put("sales_price", "0.00");
        orderProductGift.put("quantity", 3.00);
        orderProductGift.put("is_giveaway", "1");
        orderProductGift.put("promotion_id", "5f0c30c41265560001767013");
        details.add(orderProductGift);

        orderProductGift = Maps.newHashMap();
        orderProductGift.put("object_describe_api_name", "SalesOrderProductObj");
        orderProductGift.put("record_type", "default__c");
        orderProductGift.put("product_id", "5f0533c09f198000013e1554");
        orderProductGift.put("product_id__r", "毛巾");
        orderProductGift.put("sales_price", "0.00");
        orderProductGift.put("quantity", 4.00);
        orderProductGift.put("is_giveaway", "1");
        orderProductGift.put("promotion_id", "5f48bd8d1e79e300013efde5");
        details.add(orderProductGift);

        orderProductGift = Maps.newHashMap();
        orderProductGift.put("object_describe_api_name", "SalesOrderProductObj");
        orderProductGift.put("record_type", "default__c");
        orderProductGift.put("product_id", "5f0533989f198000013e14c7");
        orderProductGift.put("product_id__r", "零食大礼包");
        orderProductGift.put("sales_price", "0.00");
        orderProductGift.put("quantity", 2.00);
        orderProductGift.put("is_giveaway", "1");
        orderProductGift.put("promotion_id", "5f48bd8d1e79e300013efde5");
        details.add(orderProductGift);

        PromotionType.ValidatePromotionsArg arg = new PromotionType.ValidatePromotionsArg();
        arg.setObjectData(objectData);
        arg.setDetails(details);
        PromotionType.ValidatePromotionsResult result = promotionService.validatePromotionsForSalesOrder(serviceContext, arg);
        log.info("result={}", result);
    }

    @Test
    public void testValidatePromotionsForSalesOrder_11() {
        ServiceContext serviceContext = newServiceContext();

        Map<String, Object> objectData = Maps.newHashMap();
        objectData.put("object_describe_api_name", "SalesOrderObj");
        objectData.put("record_type", "default__c");
        objectData.put("account_id", "5f0d60dd7288ab000196e1fb");
        objectData.put("order_amount", "780.00");
        objectData.put("product_amount", "780.00");
        objectData.put("discount", "100");
        objectData.put("promotion_id", "5f0c30c41265560001767013");

        List<Map<String, Object>> details = Lists.newArrayList();
        Map<String, Object> orderProduct = Maps.newHashMap();
        orderProduct.put("object_describe_api_name", "SalesOrderProductObj");
        orderProduct.put("record_type", "default__c");
        orderProduct.put("product_id", "5f0533989f198000013e14c7");
        orderProduct.put("product_id__r", "零食大礼包");
        orderProduct.put("product_price", "49.00");
        orderProduct.put("discount", 100.0000);
        orderProduct.put("sales_price", "49.00");
        orderProduct.put("quantity", "10.00");
        orderProduct.put("subtotal", "490.00");
        orderProduct.put("promotion_id", "5f48bd8d1e79e300013efde5");
        details.add(orderProduct);

        orderProduct = Maps.newHashMap();
        orderProduct.put("object_describe_api_name", "SalesOrderProductObj");
        orderProduct.put("record_type", "default__c");
        orderProduct.put("product_id", "5f0533c09f198000013e1554");
        orderProduct.put("product_id__r", "毛巾");
        orderProduct.put("product_price", "29.00");
        orderProduct.put("discount", 100.0000);
        orderProduct.put("sales_price", "29.00");
        orderProduct.put("quantity", "20.00");
        orderProduct.put("subtotal", "290.00");
        orderProduct.put("promotion_id", "5f48bd8d1e79e300013efde5");
        details.add(orderProduct);

        Map<String, Object> orderProductGift = Maps.newHashMap();
        orderProductGift.put("object_describe_api_name", "SalesOrderProductObj");
        orderProductGift.put("record_type", "default__c");
        orderProductGift.put("product_id", "5f0533779f198000013e143b");
        orderProductGift.put("product_id__r", "洗衣液");
        orderProductGift.put("sales_price", "0.00");
        orderProductGift.put("quantity", 3.00);
        orderProductGift.put("is_giveaway", "1");
        orderProductGift.put("promotion_id", "5f0c30c41265560001767013");
        details.add(orderProductGift);

        orderProductGift = Maps.newHashMap();
        orderProductGift.put("object_describe_api_name", "SalesOrderProductObj");
        orderProductGift.put("record_type", "default__c");
        orderProductGift.put("product_id", "5f0533c09f198000013e1554");
        orderProductGift.put("product_id__r", "毛巾");
        orderProductGift.put("sales_price", "0.00");
        orderProductGift.put("quantity", 2.00);
        orderProductGift.put("is_giveaway", "1");
        orderProductGift.put("promotion_id", "5f48bd8d1e79e300013efde5");
        details.add(orderProductGift);

        orderProductGift = Maps.newHashMap();
        orderProductGift.put("object_describe_api_name", "SalesOrderProductObj");
        orderProductGift.put("record_type", "default__c");
        orderProductGift.put("product_id", "5f0533989f198000013e14c7");
        orderProductGift.put("product_id__r", "零食大礼包");
        orderProductGift.put("sales_price", "0.00");
        orderProductGift.put("quantity", 4.00);
        orderProductGift.put("is_giveaway", "1");
        orderProductGift.put("promotion_id", "5f48bd8d1e79e300013efde5");
        details.add(orderProductGift);

        PromotionType.ValidatePromotionsArg arg = new PromotionType.ValidatePromotionsArg();
        arg.setObjectData(objectData);
        arg.setDetails(details);
        PromotionType.ValidatePromotionsResult result = promotionService.validatePromotionsForSalesOrder(serviceContext, arg);
        log.info("result={}", result);
    }

    @Test
    public void testListCombinePromotions() {
        ServiceContext serviceContext = newServiceContext();
        String customerId = "5d0b35b68f37110001a53a2d";

        PromotionType.CustomerIdArg arg = new PromotionType.CustomerIdArg();
        arg.setCustomerId(customerId);
        arg.setOffset(0);
        arg.setLimit(2);

        PromotionType.PromotionListResult result = promotionService.listCombinePromotions(serviceContext, arg);
        log.info("result={}", result);
    }


}
