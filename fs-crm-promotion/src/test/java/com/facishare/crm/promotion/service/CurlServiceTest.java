package com.facishare.crm.promotion.service;

import com.facishare.crmcommon.constants.CommonProductConstants;
import com.facishare.crmcommon.constants.CommonProductConstants.Field;
import com.facishare.crm.promotion.base.BaseServiceTest;
import com.facishare.crm.promotion.constants.PromotionConstants;
import com.facishare.crm.promotion.constants.PromotionProductConstants;
import com.facishare.crm.promotion.enums.PromotionProductRecordTypeEnum;
import com.facishare.crm.promotion.predefine.service.PromotionCurlService;
import com.facishare.crm.promotion.predefine.service.dto.EmptyResult;
import com.facishare.crm.promotion.predefine.service.dto.RecordTypeLayoutInfo;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;
import java.util.Map;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/applicationContext.xml")
public class CurlServiceTest extends BaseServiceTest {
    @Autowired
    private PromotionCurlService curlService;

    public CurlServiceTest() {
        super(PromotionConstants.API_NAME);
    }

    static {
        System.setProperty("spring.profiles.active", "fstest");//fstest
    }

    @Test
    public void addGiftTypeInPromotionRuleTest() {
        PromotionCurlService.TenantIdModel.Arg arg = new PromotionCurlService.TenantIdModel.Arg();
        arg.setTenantIds(Lists.newArrayList("55910"));
        EmptyResult emptyResult = curlService.addGiftTypeInPromotionRule(arg, null);
        System.out.println(emptyResult);
    }

    @Test
    public void fillGiftTypeTest() {
        PromotionCurlService.TenantIdModel.Arg arg = new PromotionCurlService.TenantIdModel.Arg();
        arg.setTenantIds(Lists.newArrayList("2"));
        PromotionCurlService.TenantIdModel.Result result = curlService.fillGiftType(null, arg);
        System.out.println(result);
    }

    @Test
    public void initAdvertisementTest() {
        PromotionCurlService.TenantIdModel.Arg arg = new PromotionCurlService.TenantIdModel.Arg();
        arg.setTenantIds(Lists.newArrayList("55910"));
        curlService.initAdvertisement(null, arg);
    }

    @Test
    public void addRecordTypeTest() {
        List<String> tennatIds = Lists.newArrayList("56020");
        RecordTypeLayoutInfo recordTypeLayoutInfo = RecordTypeLayoutInfo.builder().objectApiName(PromotionProductConstants.API_NAME)
                .recordTypeApiName(PromotionProductRecordTypeEnum.CombinePromotion.getApiName()).recordTypeLabel(PromotionProductRecordTypeEnum.CombinePromotion.getLabel())
                .layoutApiName(PromotionProductConstants.COMBINE_PROMOTION_LAYOUT_API_NAME).build();
        curlService.addRecordType(tennatIds, recordTypeLayoutInfo);
    }

    @Test
    public void testAdd653FieldInPromotion() {
        PromotionCurlService.TenantIdModel.Arg arg = new PromotionCurlService.TenantIdModel.Arg();
        arg.setTenantIds(Lists.newArrayList("71609"));

        PromotionCurlService.TenantIdModel.Result result = curlService.add653FieldInPromotion(null, arg);
        System.out.println(result);
    }

    @Test
    public void testAdd653FieldInPromotionProduct() {
        PromotionCurlService.TenantIdModel.Arg arg = new PromotionCurlService.TenantIdModel.Arg();
        arg.setTenantIds(Lists.newArrayList("71609"));

        PromotionCurlService.TenantIdModel.Result result = curlService.add653FieldInPromotionProduct(null, arg);
        System.out.println(result);
    }

    @Test
    public void testUpdate653PromotionData() {
        PromotionCurlService.TenantIdModel.Arg arg = new PromotionCurlService.TenantIdModel.Arg();
        arg.setTenantIds(Lists.newArrayList("71609"));

        PromotionCurlService.TenantIdModel.Result result = curlService.update653PromotionData(null, arg);
        System.out.println(result);
    }

    @Test
    public void testAdd654FieldInPromotion() {
        PromotionCurlService.TenantIdModel.Arg arg = new PromotionCurlService.TenantIdModel.Arg();
        arg.setTenantIds(Lists.newArrayList("74735"));

        PromotionCurlService.TenantIdModel.Result result = curlService.add654FieldInPromotion(null, arg);
        System.out.println(result);
    }

    @Test
    public void testAdd660FieldInPromotion() {
        PromotionCurlService.TenantIdModel.Arg arg = new PromotionCurlService.TenantIdModel.Arg();
        arg.setTenantIds(Lists.newArrayList("74203"));

        PromotionCurlService.TenantIdModel.Result result = curlService.add660FieldInPromotion(null, arg);
        System.out.println(result);
    }

    @Test
    public void testAdd660FieldInPromotionProduct() {
        PromotionCurlService.TenantIdModel.Arg arg = new PromotionCurlService.TenantIdModel.Arg();
        arg.setTenantIds(Lists.newArrayList("74203"));

        PromotionCurlService.TenantIdModel.Result result = curlService.add660FieldInPromotionProduct(null, arg);
        System.out.println(result);
    }

    @Test
    public void testUpdate660PromotionData() {
        PromotionCurlService.TenantIdModel.Arg arg = new PromotionCurlService.TenantIdModel.Arg();
        arg.setTenantIds(Lists.newArrayList("74203"));

        PromotionCurlService.TenantIdModel.Result result = curlService.update660PromotionData(null, arg);
        System.out.println(result);
    }

    @Test
    public void testAdd660GrayFieldInPromotion() {
        PromotionCurlService.TenantIdModel.Arg arg = new PromotionCurlService.TenantIdModel.Arg();
        arg.setTenantIds(Lists.newArrayList("74203"));

        PromotionCurlService.TenantIdModel.Result result = curlService.add660GrayFieldInPromotion(null, arg);
        System.out.println(result);
    }

    @Test
    public void testUpdate660GrayPromotionData() {
        PromotionCurlService.TenantIdModel.Arg arg = new PromotionCurlService.TenantIdModel.Arg();
        arg.setTenantIds(Lists.newArrayList("74203"));

        PromotionCurlService.TenantIdModel.Result result = curlService.update660GrayPromotionData(null, arg);
        System.out.println(result);
    }

    @Test
    public void testAdd665Fields() {
        PromotionCurlService.TenantIdModel.Arg arg = new PromotionCurlService.TenantIdModel.Arg();
        arg.setTenantIds(Lists.newArrayList("78436"));

        PromotionCurlService.TenantIdModel.Result result = curlService.add665Fields(null, arg);
        System.out.println(result);
    }

    @Test
    public void adjustRuleMethodSetting() {
        PromotionCurlService.TenantIdModel.Arg arg = new PromotionCurlService.TenantIdModel.Arg();
        arg.setTenantIds(Lists.newArrayList("78437"));

        PromotionCurlService.TenantIdModel.Result result = curlService.adjustRuleMethodSetting(null, arg);
        System.out.println(result);
    }

    @Test
    public void testUpdate665PromotionData() {
        PromotionCurlService.TenantIdModel.Arg arg = new PromotionCurlService.TenantIdModel.Arg();
        arg.setTenantIds(Lists.newArrayList("55732"));

        PromotionCurlService.TenantIdModel.Result result = curlService.update665PromotionData(null, arg);
        System.out.println(result);
    }

    @Test
    public void testUpdate665PromotionRuleData() {
        PromotionCurlService.TenantIdModel.Arg arg = new PromotionCurlService.TenantIdModel.Arg();
        arg.setTenantIds(Lists.newArrayList("55732"));

        PromotionCurlService.TenantIdModel.Result result = curlService.update665PromotionRuleData(null, arg);
        System.out.println(result);
    }

    @Autowired
    private ServiceFacade serviceFacade;

    @Test
    public void finaObjectData() {
        User user = User.builder().userId("1000").tenantId("78437").build();
//        IObjectData iObjectData = serviceFacade.findObjectData(user, "5d0b018d3d51c600017bd5f3", SystemConstants.ProductApiName);
//        String unit = iObjectData.get(PromotionProductConstants.Field.Unit.apiName, String.class);
        IObjectDescribe iObjectDescribe = serviceFacade.findObject(user.getTenantId(), CommonProductConstants.ProductApiName);
        IFieldDescribe fieldDescribe = iObjectDescribe.getFieldDescribe(Field.Unit.apiName);
//        log.info("result={}", iObjectData);
        log.info("fieldDescribe={}", fieldDescribe.get("options"));
        List<Map> optionsList = fieldDescribe.get("options", List.class);
        if (optionsList != null) {
            for (Map option : optionsList) {
                if (String.valueOf(option.get("value")).equals("6")) {
                    System.out.println(option.get("label"));
                }
            }
        }
    }


    @Test
    public void add671() {
        PromotionCurlService.TenantIdModel.Arg arg = new PromotionCurlService.TenantIdModel.Arg();
        arg.setTenantIds(Lists.newArrayList("71609"));
        PromotionCurlService.TenantIdModel.Result result = curlService.add671PromotionAndAdvertisementDeptRange(null, arg);
        log.info("result:{}", result);
    }

    @Test
    public void update6771Promotion(){
        PromotionCurlService.TenantIdModel.Arg arg = new PromotionCurlService.TenantIdModel.Arg();
        arg.setTenantIds(Lists.newArrayList("71609"));
        PromotionCurlService.TenantIdModel.Result result = curlService.update671PromotionDeptRangeData(null, arg);
    }

    @Test
    public void update6771Advertisement(){
        PromotionCurlService.TenantIdModel.Arg arg = new PromotionCurlService.TenantIdModel.Arg();
        arg.setTenantIds(Lists.newArrayList("71609"));
        PromotionCurlService.TenantIdModel.Result result = curlService.update671AdvertisementDeptRangeData(null, arg);
    }

    @Test
    public void update705AdvertisementFieldsTest() {
        PromotionCurlService.TenantIdModel.Arg arg = new PromotionCurlService.TenantIdModel.Arg();
        arg.setTenantIds(Lists.newArrayList("78824"));

        PromotionCurlService.TenantIdModel.Result result = curlService.update705AdvertisementFields(null, arg);
        log.info("result:{}", result);
    }

    @Test
    public void delete705AdvertisementSpuFieldTest() {
        PromotionCurlService.TenantIdModel.Arg arg = new PromotionCurlService.TenantIdModel.Arg();
        arg.setTenantIds(Lists.newArrayList("79382"));

        PromotionCurlService.TenantIdModel.Result result = curlService.delete705AdvertisementSpuField(null, arg);
        log.info("result:{}", result);
    }

}
