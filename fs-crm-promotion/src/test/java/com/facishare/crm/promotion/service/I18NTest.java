package com.facishare.crm.promotion.service;

import com.facishare.crm.promotion.base.BaseServiceTest;
import com.facishare.crm.promotion.constants.ProI18NKey;
import com.facishare.crm.promotion.constants.PromotionConstants;
import com.facishare.crm.promotion.enums.PromotionConditionEnum;
import com.facishare.crm.promotion.enums.PromotionTypeEnum;
import com.facishare.paas.I18N;
import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.i18n.client.api.Localization;
import com.google.common.collect.Lists;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * @author: dongzhb
 * @date: 2019/6/17
 * @Description:
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/applicationContext.xml")
public class I18NTest extends BaseServiceTest {
    public I18NTest() {
        super(PromotionConstants.API_NAME);
    }

    static {
        System.setProperty("spring.profiles.active", "ceshi113");//fstest
    }

    private static final String FULL_PIECE = PromotionConditionEnum.FullPiece.toString();
    private static final String FULL_AMOUNT = PromotionConditionEnum.FullAmount.toString();
    private static final String IS_STAIR_PROMOTION = "STAIR";

    @PostConstruct
    public void init() {
        //设置服务关注的标签。如果需要自定义对象或者标签改名后的数据, initWithTags方法中添加 pre_object以及custom_object
        I18nClient.getInstance().initWithTags("server");
    }

    public void getKeys(List<String> keyList, long tenantId) {
        Map<String, Localization> keyMaps = I18nClient.getInstance().get(keyList, tenantId);
        keyMaps.forEach((k, v) -> log.info(k + "--->" + v.getZhCN()));
    }

    public void getKeys(List<String> keyList, long tenantId, String lang) {
        Map<String, String> keyMaps = I18nClient.getInstance().get(keyList, tenantId, lang);
        keyMaps.forEach((k, v) -> log.info(k + "--->" + v));
    }

    @Test
    public void getKeys() {
        List<String> keyList = Lists
            .newArrayList(ProI18NKey.FULLAMOUNT_ISSTAIRPROMOTION_PROMOTION_PRODUCT_RULE_EXPLAIN_NUMBERREACHEDGIFT, ProI18NKey.FULLAMOUNT_PROMOTION_PRODUCT_RULE_EXPLAIN_NUMBERREACHEDGIFT,
                ProI18NKey.FULLAMOUNT_PROMOTION_PRODUCT_RULE_EXPLAIN_FIXEDPRICE);
        getKeys(keyList, Long.valueOf(55732));
    }

    @Test
    public void getValue() {
        Map<String, Object> mapFullPiece = new HashMap<>(5);
        mapFullPiece.put(FULL_PIECE + PromotionTypeEnum.PriceDiscount.getValue(), I18N.text(ProI18NKey.FULLPIECE_PROMOTION_PRODUCT_RULE_EXPLAIN_PRICEDISCOUNT, "10", "85"));
        mapFullPiece.put(FULL_PIECE + PromotionTypeEnum.DerateMoney.getValue(), I18N.text(ProI18NKey.FULLPIECE_PROMOTION_PRODUCT_RULE_EXPLAIN_DERATEMONEY));
        mapFullPiece.put(FULL_PIECE + PromotionTypeEnum.FixedPrice.getValue(), I18N.text(ProI18NKey.FULLPIECE_PROMOTION_PRODUCT_RULE_EXPLAIN_FIXEDPRICE));
        mapFullPiece.put(FULL_PIECE + PromotionTypeEnum.NumberReachedGift.getValue(), I18N.text(ProI18NKey.FULLPIECE_PROMOTION_PRODUCT_RULE_EXPLAIN_NUMBERREACHEDGIFT));
        mapFullPiece
            .put(FULL_PIECE + IS_STAIR_PROMOTION + PromotionTypeEnum.NumberReachedGift.getValue(), I18N.text(ProI18NKey.FULLPIECE_ISSTAIRPROMOTION_PROMOTION_PRODUCT_RULE_EXPLAIN_NUMBERREACHEDGIFT));

        Map<String, Object> mapFullAmount = new HashMap<>(5);
        mapFullAmount.put(FULL_AMOUNT + PromotionTypeEnum.PriceDiscount.getValue(), I18N.text(ProI18NKey.FULLAMOUNT_PROMOTION_PRODUCT_RULE_EXPLAIN_PRICEDISCOUNT));
        mapFullAmount.put(FULL_AMOUNT + PromotionTypeEnum.DerateMoney.getValue(), I18N.text(ProI18NKey.FULLAMOUNT_PROMOTION_PRODUCT_RULE_EXPLAIN_DERATEMONEY));
        mapFullAmount.put(FULL_AMOUNT + PromotionTypeEnum.FixedPrice.getValue(), I18N.text(ProI18NKey.FULLAMOUNT_PROMOTION_PRODUCT_RULE_EXPLAIN_FIXEDPRICE));
        mapFullAmount.put(FULL_AMOUNT + PromotionTypeEnum.NumberReachedGift.getValue(), I18N.text(ProI18NKey.FULLAMOUNT_PROMOTION_PRODUCT_RULE_EXPLAIN_NUMBERREACHEDGIFT));
        mapFullAmount
            .put(FULL_AMOUNT + IS_STAIR_PROMOTION + PromotionTypeEnum.NumberReachedGift.getValue(), I18N.text(ProI18NKey.FULLAMOUNT_ISSTAIRPROMOTION_PROMOTION_PRODUCT_RULE_EXPLAIN_NUMBERREACHEDGIFT));

        for (String key : mapFullPiece.keySet()) {
            String vlaue = mapFullPiece.get(FULL_PIECE + PromotionTypeEnum.PriceDiscount.getValue()).toString();
            System.out.println("value==" + vlaue);
        }
    }
}
