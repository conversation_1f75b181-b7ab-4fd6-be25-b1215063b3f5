package com.facishare.crm.promotion.predefine.service.impl;

import com.facishare.crm.promotion.constants.*;
import com.facishare.crmcommon.constants.LayoutConstants;
import com.facishare.crmcommon.constants.SystemConstants;
import com.facishare.crmcommon.describebuilder.AutoNumberFieldDescribeBuilder;
import com.facishare.crmcommon.describebuilder.BooleanFieldDescribeBuilder;
import com.facishare.crmcommon.describebuilder.CurrencyFieldDescribeBuilder;
import com.facishare.crmcommon.describebuilder.DateTimeFieldDescribeBuilder;
import com.facishare.crmcommon.describebuilder.DepartmentFieldDescribeBuilder;
import com.facishare.crmcommon.describebuilder.FieldConfig;
import com.facishare.crmcommon.describebuilder.FieldSectionBuilder;
import com.facishare.crmcommon.describebuilder.FormComponentBuilder;
import com.facishare.crmcommon.describebuilder.FormFieldBuilder;
import com.facishare.crmcommon.describebuilder.ImageFieldDescribeBuilder;
import com.facishare.crmcommon.describebuilder.LayoutBuilder;
import com.facishare.crmcommon.describebuilder.LongTextFieldDescribeBuilder;
import com.facishare.crmcommon.describebuilder.MasterDetailFieldDescribeBuilder;
import com.facishare.crmcommon.describebuilder.NumberFieldDescribeBuilder;
import com.facishare.crmcommon.describebuilder.ObjectDescribeBuilder;
import com.facishare.crmcommon.describebuilder.ObjectReferenceFieldDescribeBuilder;
import com.facishare.crmcommon.describebuilder.PercentileFieldDescribeBuilder;
import com.facishare.crmcommon.describebuilder.QuoteFieldDescribeBuilder;
import com.facishare.crmcommon.describebuilder.RecordTypeFieldDescribeBuilder;
import com.facishare.crmcommon.describebuilder.RecordTypeOptionBuilder;
import com.facishare.crmcommon.describebuilder.RuleBuilder;
import com.facishare.crmcommon.describebuilder.SelectOneFieldDescribeBuilder;
import com.facishare.crmcommon.describebuilder.SelectOptionBuilder;
import com.facishare.crmcommon.describebuilder.SimpleComponentBuilder;
import com.facishare.crmcommon.describebuilder.TableColumnBuilder;
import com.facishare.crmcommon.describebuilder.TableComponentBuilder;
import com.facishare.crmcommon.describebuilder.TextFieldDescribeBuilder;
import com.facishare.crmcommon.describebuilder.UrlFieldDescribeBuilder;
import com.facishare.crmcommon.describebuilder.UseScopeFieldDescribeBuilder;
import com.facishare.crmcommon.manager.MultiOrgManager;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.promotion.enums.AdvertisementStatusEnum;
import com.facishare.crm.promotion.enums.GiftMethodEnum;
import com.facishare.crm.promotion.enums.GiftTypeEnum;
import com.facishare.crm.promotion.enums.JumpTypeEnum;
import com.facishare.crm.promotion.enums.PromotionConditionEnum;
import com.facishare.crm.promotion.enums.PromotionProductRecordTypeEnum;
import com.facishare.crm.promotion.enums.PromotionRecordTypeEnum;
import com.facishare.crm.promotion.enums.PromotionRuleRecordTypeEnum;
import com.facishare.crm.promotion.enums.PromotionTypeEnum;
import com.facishare.crm.promotion.enums.RuleMethodEnum;
import com.facishare.crm.promotion.exception.PromotionBusinessException;
import com.facishare.crm.promotion.exception.PromotionErrorCode;
import com.facishare.crm.promotion.predefine.manager.ProOrderProductManager;
import com.facishare.crm.promotion.predefine.service.PromotionInitService;
import com.facishare.crm.promotion.util.HeaderUtil;
import com.facishare.crm.promotion.util.PromotionUtil;
import com.facishare.crmcommon.rest.SettingRestApi;
import com.facishare.crmcommon.rest.dto.GetConfigValueByKeyModel;
import com.facishare.organization.adapter.api.config.model.GetConfigDto;
import com.facishare.organization.adapter.api.config.service.EnterpriseConfigService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.model.InfraServiceFacade;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.RecordTypeAuthProxy;
import com.facishare.paas.appframework.metadata.RecordTypeLogicServiceImpl;
import com.facishare.paas.appframework.metadata.dto.DescribeResult;
import com.facishare.paas.appframework.metadata.dto.RecordTypeResult;
import com.facishare.paas.appframework.metadata.dto.RuleResult;
import com.facishare.paas.appframework.metadata.dto.auth.AddRoleRecordTypeModel;
import com.facishare.paas.appframework.metadata.dto.auth.AddRoleViewModel;
import com.facishare.paas.appframework.metadata.dto.auth.RecordTypePojo;
import com.facishare.paas.appframework.metadata.dto.auth.RecordTypeRoleViewPojo;
import com.facishare.paas.appframework.metadata.dto.auth.RoleInfoModel;
import com.facishare.paas.appframework.metadata.dto.auth.RoleInfoPojo;
import com.facishare.paas.appframework.metadata.dto.auth.RoleViewForWebPojo;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.auth.model.RoleViewPojo;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IMetadataMultiOrgService;
import com.facishare.paas.metadata.impl.IRule;
import com.facishare.paas.metadata.impl.LayoutRuleInfo;
import com.facishare.paas.metadata.impl.Rule;
import com.facishare.paas.metadata.impl.describe.AutoNumberFieldDescribe;
import com.facishare.paas.metadata.impl.describe.BooleanFieldDescribe;
import com.facishare.paas.metadata.impl.describe.CurrencyFieldDescribe;
import com.facishare.paas.metadata.impl.describe.DateTimeFieldDescribe;
import com.facishare.paas.metadata.impl.describe.DepartmentFieldDescribe;
import com.facishare.paas.metadata.impl.describe.ImageFieldDescribe;
import com.facishare.paas.metadata.impl.describe.LongTextFieldDescribe;
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe;
import com.facishare.paas.metadata.impl.describe.NumberFieldDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.facishare.paas.metadata.impl.describe.PercentileFieldDescribe;
import com.facishare.paas.metadata.impl.describe.QuoteFieldDescribe;
import com.facishare.paas.metadata.impl.describe.RecordTypeFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.describe.TextFieldDescribe;
import com.facishare.paas.metadata.impl.describe.URLFieldDescribe;
import com.facishare.paas.metadata.impl.describe.UseScopeFieldDescribe;
import com.facishare.paas.metadata.impl.ui.layout.FieldSection;
import com.facishare.paas.metadata.impl.ui.layout.FormField;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.facishare.paas.metadata.impl.ui.layout.component.SimpleComponent;
import com.facishare.paas.metadata.impl.ui.layout.component.TableComponent;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.ui.layout.ITableColumn;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.privilege.util.Headers.PAAS_PRIVILEGE_HEADDER;

/**
 * IgnoreI18nFile
 */
@Service
@Slf4j
public class PromotionInitServiceImpl implements PromotionInitService {
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private RecordTypeAuthProxy recordTypeAuthApi;
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private InfraServiceFacade infraServiceFacade;
    @Autowired
    RecordTypeLogicServiceImpl recordTypeLogicService;
    @Resource(type = SettingRestApi.class)
    private SettingRestApi settingRestApi;
    @Autowired
    private EnterpriseConfigService enterpriseConfigService;
    @Autowired
    private IMetadataMultiOrgService iMetadataMultiOrgService;
    @Autowired
    private MultiOrgManager multiOrgManager;
    @Autowired
    private ProOrderProductManager proOrderProductManager;

    @Override
    public boolean init(User user) {
        //是否开启多组织
        boolean isMultiOrgOpen = multiOrgManager.isMultiOrgOpen(user);

        Set<String> apiNames = Sets.newHashSet(PromotionConstants.API_NAME, PromotionRuleConstants.API_NAME, PromotionProductConstants.API_NAME, AdvertisementConstants.API_NAME);
        Map<String, IObjectDescribe> describeMap = describeLogicService.findObjects(user.getTenantId(), apiNames);
        log.info("findByApiNames,user:{},apiNames:{},result:{}", user, JsonUtil.toJson(apiNames), describeMap);
        RoleInfoModel.Result result = roleInfo(user);
        if (!result.isSuccess()) {
            log.warn("getRoleInfo error,user:{},result:{}", user, result);
            throw new PromotionBusinessException(PromotionErrorCode.QUERY_ROLE_ERROR, result.getErrMessage());
        }
        List<RoleInfoPojo> roleInfoPojos = result.getResult().getRoles();
        if (!describeMap.containsKey(PromotionConstants.API_NAME)) {
            IObjectDescribe promotionDescribeDraft = generatePromotionDescsribeDraft(user.getTenantId(), user.getUserId(), isMultiOrgOpen);
            ILayout defaultLayout = generatePromotionDefaultLayout(user.getTenantId(), user.getUserId(), isMultiOrgOpen);
            ILayout listLayout = generatePromotionListLayout(user.getTenantId(), user.getUserId());
            log.info("user:{},PromotionObj defaultLayout:{}", user, defaultLayout.toString());
            DescribeResult describeResult = describeLogicService.createDescribe(user, promotionDescribeDraft.toJsonString(), defaultLayout.toJsonString(), listLayout.toJsonString(), true, true);
            log.info("user:{},PromotionObj describeResult:{}", user, describeResult.toString());
            //业务类型
            initProductRecordType(user, PromotionConstants.API_NAME, PromotionRecordTypeEnum.ProductPromotion.getApiName(), PromotionConstants.DEFAULT_LAYOUT_API_NAME, roleInfoPojos);
            initProductRecordType(user, PromotionConstants.API_NAME, PromotionRecordTypeEnum.CombinePromotion.getApiName(), PromotionConstants.DEFAULT_LAYOUT_API_NAME, roleInfoPojos);
            //校验规则
            initPromotionRule(user);
        }
        if (!describeMap.containsKey(PromotionProductConstants.API_NAME)) {
            IObjectDescribe promotionDescribeDraft = generatePromotionProductDescribeDraft(user.getTenantId(), user.getUserId(), isMultiOrgOpen);
            ILayout defaultLayout = generatePromotionProductDefaultLayout(user.getTenantId(), user.getUserId(), isMultiOrgOpen);
            ILayout listLayout = generatePromotionProductListLayout(user.getTenantId(), user.getUserId());
            DescribeResult describeResult = describeLogicService.createDescribe(user, promotionDescribeDraft.toJsonString(), defaultLayout.toJsonString(), listLayout.toJsonString(), true, true);
            log.info("user:{},PromotionProductObj describeResult:{}", user, describeResult.toString());
            addPromotionProductCombineRecordTypeAndLayout(user, roleInfoPojos);
        }
        if (!describeMap.containsKey(PromotionRuleConstants.API_NAME)) {
            IObjectDescribe promotionDescribeDraft = generatePromotionRuleDescribeDraft(user.getTenantId(), user.getUserId(), isMultiOrgOpen);
            ILayout defaultLayout = generatePromotionRuleDefaultLayout(user.getTenantId(), user.getUserId(), isMultiOrgOpen);
            ILayout listLayout = generatePromotionRuleListLayout(user.getTenantId(), user.getUserId());
            DescribeResult describeResult = describeLogicService.createDescribe(user, promotionDescribeDraft.toJsonString(), defaultLayout.toJsonString(), listLayout.toJsonString(), true, true);
            log.info("user:{},PromotionRuleObj describeResult:{}", user, describeResult.toString());
            //业务类型
            initProductRecordType(user, PromotionRuleConstants.API_NAME, PromotionRuleRecordTypeEnum.ProductPromotion.getApiName(), PromotionRuleConstants.DEFAULT_LAYOUT_API_NAME, roleInfoPojos);
            initProductRecordType(user, PromotionRuleConstants.API_NAME, PromotionRuleRecordTypeEnum.CombinePromotion.getApiName(), PromotionRuleConstants.DEFAULT_LAYOUT_API_NAME, roleInfoPojos);
        }
        if (!describeMap.containsKey(AdvertisementConstants.API_NAME)) {
            initAdvertisement(user);
        }

        proOrderProductManager.addPromotionDiscountField(user.getTenantId(), false);

        return true;
    }

    private boolean isMultiOrgOpen(User user) {
        GetConfigDto.Argument argument = new GetConfigDto.Argument();
        argument.setEnterpriseId(user.getTenantIdInt());
        argument.setKey("openOrganization");
        argument.setEmployeeId(user.getUserIdInt());
        argument.setCurrentEmployeeId(user.getUserIdInt());
        GetConfigDto.Result result = enterpriseConfigService.getConfig(argument);
        log.info("isMultiOrgOpen getConfig, argument[{}], result[{}]", argument, result);
        return Objects.equals(result.getValue(), "1");  //0关闭，1开启
    }

    //促销产品对象 新增组合促销业务类型和对应的layout
    @Override
    public void addPromotionProductCombineRecordTypeAndLayout(User user, List<RoleInfoPojo> roleInfoPojos) {
        ILayout promotionProductLayout = serviceFacade.createLayout(user, generatePromotionProductCombinePromotionLayout(user.getTenantId(), user.getUserId()));
        log.info("addPromotionProductRecordTypeAndLayout:{}", promotionProductLayout.toJsonString());
        initProductRecordType(user, PromotionProductConstants.API_NAME, PromotionProductRecordTypeEnum.CombinePromotion.getApiName(), PromotionProductConstants.COMBINE_PROMOTION_LAYOUT_API_NAME, roleInfoPojos);
    }

    //新增业务类型
    @Override
    public void addRecordType(User user, String objectApiName, String recordTypeApiName, String recordTypeLabel, String layoutApiName, List<RoleInfoPojo> roleInfoPojos) {
        RecordTypeRoleViewPojo recordTypeRoleViewPojo = new RecordTypeRoleViewPojo();
        recordTypeRoleViewPojo.setApi_name(recordTypeApiName);
        recordTypeRoleViewPojo.setIs_active(true);
        recordTypeRoleViewPojo.setLabel(recordTypeLabel);
        List<RoleViewForWebPojo> roleViewForWebPojos = Lists.newArrayList();
        roleInfoPojos.forEach(roleInfoPojo -> {
            RoleViewForWebPojo roleViewForWebPojo = new RoleViewForWebPojo();
            roleViewForWebPojo.setIs_used(true);
            roleViewForWebPojo.setLayout_api_name(layoutApiName);
            roleViewForWebPojo.setRoleCode(roleInfoPojo.getRoleCode());
            roleViewForWebPojo.setIs_default(false);
            roleViewForWebPojos.add(roleViewForWebPojo);
        });
        recordTypeRoleViewPojo.setRoles(roleViewForWebPojos);
        RecordTypeResult recordTypeResult = recordTypeLogicService.createRecordType(user.getTenantId(), objectApiName, recordTypeRoleViewPojo, user);
        log.info("addRecordType result:{},user:{}", recordTypeResult, user);
    }

    @Override
    public DescribeResult initAdvertisement(User user) {
        boolean spuMode = isSpuMode(user.getTenantId());
        IObjectDescribe advertisementDescribe = generateAdvertisementDescribe(user.getTenantId(), user.getUserId(), spuMode);
        ILayout defaultLayout = generateAdvertisementDefaultLayout(user.getTenantId(), user.getUserId(), spuMode);
        ILayout listLayout = generateAdvertisementListLayout(user.getTenantId(), user.getUserId(), spuMode);
        DescribeResult describeResult = describeLogicService.createDescribe(user, advertisementDescribe.toJsonString(), defaultLayout.toJsonString(), listLayout.toJsonString(), true, true);
        log.info("user:{},advertisement describeResult:{}", user, describeResult);
        try {
            initAdvertisementLayoutRule(user, spuMode);
        } catch (Exception e) {
            log.warn("init advertisement layout rule fail,user:{}", user, e);
        }
        return describeResult;
    }

    @Override
    public void initAdvertisementLayoutRule(User user, boolean spuMode) {
        Map<String, Object> layoutRuleMap = Maps.newHashMap();
        layoutRuleMap.put("api_name", "advertisement_default_layout_rule__c");
        layoutRuleMap.put("description", "");
        layoutRuleMap.put("label", "预设布局规则");
        layoutRuleMap.put("layout_api_name", AdvertisementConstants.DEFAULT_LAYOUT_API_NAME);
        layoutRuleMap.put("main_field", AdvertisementConstants.Field.JumpType.apiName);
        List<Map<String, Object>> mainFieldBranches = Lists.newArrayList();
        mainFieldBranches.add(PromotionUtil.getJumpTypeBranch(JumpTypeEnum.PromotionDetail.getValue(), Lists.newArrayList(AdvertisementConstants.Field.Promotion.apiName), Lists.newArrayList(AdvertisementConstants.Field.Promotion.apiName)));
        if (spuMode) {
            mainFieldBranches.add(PromotionUtil.getJumpTypeBranch(JumpTypeEnum.SpuDetail.getValue(), Lists.newArrayList(AdvertisementConstants.Field.Spu.apiName), Lists.newArrayList(AdvertisementConstants.Field.Spu.apiName)));
        } else {
            mainFieldBranches.add(PromotionUtil.getJumpTypeBranch(JumpTypeEnum.SkuDetail.getValue(), Lists.newArrayList(AdvertisementConstants.Field.Product.apiName), Lists.newArrayList(AdvertisementConstants.Field.Product.apiName)));
        }
        mainFieldBranches.add(PromotionUtil.getJumpTypeBranch(JumpTypeEnum.ExternalLink.getValue(), Lists.newArrayList(AdvertisementConstants.Field.JumpAddress.apiName), Lists.newArrayList(AdvertisementConstants.Field.JumpAddress.apiName)));
        layoutRuleMap.put("main_field_branches", mainFieldBranches);
        layoutRuleMap.put("object_describe_api_name", AdvertisementConstants.API_NAME);
        LayoutRuleInfo layoutRuleInfo = new LayoutRuleInfo(layoutRuleMap);
        infraServiceFacade.createLayoutRule(user, layoutRuleInfo);
    }

    private void initPromotionRule(User user) {
        IRule rule = new Rule();
        rule.setApiName(PromotionConstants.START_END_TIME_RULE_API_NAME);
        rule.setDescribeApiName(PromotionConstants.API_NAME);
        rule.setRuleName(PromotionConstants.START_END_TIME_RULE_DISPLAY_NAME);
        rule.setCondition(String.format("$%s$>$%s$", PromotionConstants.Field.StartTime.apiName, PromotionConstants.Field.EndTime.apiName));
        rule.setIsActive(true);
        rule.setTenantId(user.getTenantId());
        rule.setCreatedBy(user.getUserId());
        long curTime = System.currentTimeMillis();
        rule.setCreateTime(curTime);
        rule.setLastModifiedBy(user.getUserId());
        rule.setLastModifiedTime(curTime);
        rule.setDescription(PromotionConstants.START_END_TIME_RULE_DESCRIPTION);
        rule.setMessage(PromotionConstants.START_END_TIME_RULE_DESCRIPTION);
        rule.setDefaultToZero(true);
        rule.setScene(Lists.newArrayList("create", "update"));
        try {
            RuleResult result = infraServiceFacade.create(rule);
            if (!result.isSuccess()) {
                log.warn("initPromotionRule user:{},rule:{},result:{} ", user, rule, result);
            }
        } catch (Exception e) {
            log.warn("initPromotionRule user:{},rule:{}", user, rule, e);
        }
    }

    public void initPromotionProductRule(User user) {
        String condition = "$derate_money$>=$product_id__r.price$&$combine_amount$<=0";
        IRule rule = RuleBuilder.builder().tenantId(user.getTenantId()).createBy(user.getUserId()).ruleApiName(PromotionProductConstants.COMBINE_RULE_API_NAME).ruleName(PromotionProductConstants.COMBINE_RULE_DISPLAY_NAME).description(PromotionProductConstants.COMBINE_RULE_DISPLAY_NAME).refObjectApiName(PromotionProductConstants.API_NAME).message(PromotionProductConstants.COMBINE_RULE_RULE_DESCRIPTION).condition(condition).build();
        try {
            RuleResult result = infraServiceFacade.create(rule);
            if (!result.isSuccess()) {
                log.warn("initPromotionProductRule user:{},rule:{},result:{} ", user, rule, result);
            }
        } catch (Exception e) {
            log.warn("initPromotionProductRule user:{},rule:{}", user, rule, e);
        }
    }

    @Override
    public void initProductRecordType(User user, String objectApiName, String recordTypeId, String viewId, List<RoleInfoPojo> roleInfoPojos) {
        initAssignRecord(user, roleInfoPojos, objectApiName, Lists.newArrayList(recordTypeId));
        Map<String, String> recordViewMap = Maps.newHashMap();
        recordViewMap.put(recordTypeId, viewId);
        initAssignLayout(user, roleInfoPojos, objectApiName, recordViewMap);
    }

    private void initAssignRecord(User user, List<RoleInfoPojo> roleInfoPojos, String entityId, List<String> recordTypeIds) {
        AddRoleRecordTypeModel.Result result;
        RecordTypePojo recordTypePojo;
        for (String recordTypeId : recordTypeIds) {
            List<RecordTypePojo> recordTypePojos = Lists.newArrayList();
            for (RoleInfoPojo roleInfoPojo : roleInfoPojos) {
                recordTypePojo = new RecordTypePojo();
                recordTypePojo.setAppId("CRM");
                recordTypePojo.setEntityId(entityId);
                recordTypePojo.setTenantId(user.getTenantId());
                recordTypePojo.setRecordTypeId(recordTypeId);
                recordTypePojo.setRoleCode(roleInfoPojo.getRoleCode());
                recordTypePojo.setDefaultType(false);
                recordTypePojos.add(recordTypePojo);
            }
            AddRoleRecordTypeModel.Arg arg = new AddRoleRecordTypeModel.Arg();
            arg.setRecordTypePojos(recordTypePojos);
            arg.setRecordTypeId(recordTypeId);
            arg.setEntityId(entityId);
            arg.setAuthContext(user);
            result = recordTypeAuthApi.addRoleRecordType(arg, PAAS_PRIVILEGE_HEADDER.buildHeader(user.getTenantId()));
            log.info("entityId:{},result{}", entityId, result);
        }
    }

    private AddRoleViewModel.Result initAssignLayout(User user, List<RoleInfoPojo> roleInfoPojos, String entityId, Map<String, String> recordTypeViewMap) {
        List<RoleViewPojo> roleViewPojos = Lists.newArrayList();
        RoleViewPojo roleViewPojo;
        for (RoleInfoPojo roleInfoPojo : roleInfoPojos) {
            for (Map.Entry<String, String> entry : recordTypeViewMap.entrySet()) {
                roleViewPojo = new RoleViewPojo();
                roleViewPojo.setAppId("CRM");
                roleViewPojo.setEntityId(entityId);
                roleViewPojo.setTenantId(user.getTenantId());
                roleViewPojo.setRecordTypeId(entry.getKey());
                roleViewPojo.setRoleCode(roleInfoPojo.getRoleCode());
                roleViewPojo.setViewId(entry.getValue());
                roleViewPojo.setViewType(LayoutTypes.DETAIL);
                roleViewPojos.add(roleViewPojo);
            }
        }
        recordTypeLogicService.upsertRoleViewList(user, roleViewPojos);
        return new AddRoleViewModel.Result();
    }

    @Override
    public RoleInfoModel.Result roleInfo(User user) {
        RoleInfoModel.Arg roleInfoModelArg = new RoleInfoModel.Arg();
        roleInfoModelArg.setAuthContext(user);
        RoleInfoModel.Result result = recordTypeAuthApi.roleInfo(roleInfoModelArg, PAAS_PRIVILEGE_HEADDER.buildHeader(user.getTenantId()));
        return result;
    }

    private IObjectDescribe generatePromotionDescsribeDraft(String tenantId, String fsUserId, boolean isMultiOrgOpen) {
        List<IFieldDescribe> fieldDescribeList = Lists.newArrayList();
        TextFieldDescribe nameFieldDescribe = TextFieldDescribeBuilder.builder().apiName(PromotionConstants.Field.Name.apiName).label(PromotionConstants.Field.Name.label).maxLength(1000).build();
        fieldDescribeList.add(nameFieldDescribe);

        DateTimeFieldDescribe startTimeFieldDescribe = DateTimeFieldDescribeBuilder.builder().apiName(PromotionConstants.Field.StartTime.apiName).label(PromotionConstants.Field.StartTime.label).index(true).required(true).unique(false).build();
        DateTimeFieldDescribe endTimeFieldDescribe = DateTimeFieldDescribeBuilder.builder().apiName(PromotionConstants.Field.EndTime.apiName).label(PromotionConstants.Field.EndTime.label).index(true).required(true).unique(false).build();
        fieldDescribeList.add(startTimeFieldDescribe);
        fieldDescribeList.add(endTimeFieldDescribe);

        ImageFieldDescribe imageFieldDescribe = ImageFieldDescribeBuilder.builder().apiName(PromotionConstants.Field.Images.apiName).label(PromotionConstants.Field.Images.label).fileAmountLimit(1).required(false).build();
        fieldDescribeList.add(imageFieldDescribe);

        BooleanFieldDescribe statusBooleanFieldDescribe = BooleanFieldDescribeBuilder.builder().apiName(PromotionConstants.Field.Status.apiName).label(PromotionConstants.Field.Status.label).defaultValue(true).required(true).unique(false).index(true).build();
        fieldDescribeList.add(statusBooleanFieldDescribe);

        //6.3.2
        BooleanFieldDescribe isStairPromotionBooleanFieldDescribe = BooleanFieldDescribeBuilder.builder().apiName(PromotionConstants.Field.IsStairPromotion.apiName).label(PromotionConstants.Field.IsStairPromotion.label).defaultValue(true).required(true).index(true).build();
        fieldDescribeList.add(isStairPromotionBooleanFieldDescribe);

        List<ISelectOption> typeSelectOptions = Arrays.stream(PromotionTypeEnum.values()).map(typeEnum -> SelectOptionBuilder.builder().value(typeEnum.getValue()).label(typeEnum.getLabel()).build()).collect(Collectors.toList());
        SelectOneFieldDescribe typeSelectOneFieldDescribe = SelectOneFieldDescribeBuilder.builder().apiName(PromotionConstants.Field.Type.apiName).label(PromotionConstants.Field.Type.label).selectOptions(typeSelectOptions).required(true).build();
        fieldDescribeList.add(typeSelectOneFieldDescribe);

        UseScopeFieldDescribe customerRangeUseScopeFieldDescribe = UseScopeFieldDescribeBuilder.builder().apiName(PromotionConstants.Field.CustomerRange.apiName).label(PromotionConstants.Field.CustomerRange.label).targetApiName(Utils.ACCOUNT_API_NAME).defaultValue(PromotionConstants.customerRangeDefaultValue).expressionType(PromotionConstants.expressionType).build();
        fieldDescribeList.add(customerRangeUseScopeFieldDescribe);

        List<IRecordTypeOption> recordTypeOptions = Arrays.stream(PromotionRecordTypeEnum.values()).map(recordType -> RecordTypeOptionBuilder.builder().apiName(recordType.getApiName()).label(recordType.getLabel()).build()).collect(Collectors.toList());
        RecordTypeFieldDescribe recordTypeSelectOneFieldDescribe = RecordTypeFieldDescribeBuilder.builder().apiName(SystemConstants.Field.RecordType.apiName).label(I18N.text(ProI18NKey.PROMOTIONOBJ_RECORD_TYPE)).recordTypeOptions(recordTypeOptions).build();
        fieldDescribeList.add(recordTypeSelectOneFieldDescribe);

        //6.6.0
        List<ISelectOption> conditionSelectOptions = Arrays.stream(PromotionConditionEnum.values()).map(condition -> SelectOptionBuilder.builder().value(condition.getValue()).label(condition.getLabel()).build()).collect(Collectors.toList());
        SelectOneFieldDescribe conditionSelectOneFieldDescribe = SelectOneFieldDescribeBuilder.builder().apiName(PromotionConstants.Field.Condition.apiName).label(PromotionConstants.Field.Condition.label).selectOptions(conditionSelectOptions).required(true).build();
        fieldDescribeList.add(conditionSelectOneFieldDescribe);

        Map<String, Object> descriptionFieldConfig = FieldConfig.builder().edit(1).attrs(Lists.newArrayList("is_readonly", "is_required"), 0).attrs(Lists.newArrayList("max_length"), 1).build();
        LongTextFieldDescribe descriptionLongTextFieldDescribe = LongTextFieldDescribeBuilder.builder().apiName(PromotionConstants.Field.Description.apiName).label(PromotionConstants.Field.Description.label).maxLength(500).required(false).config(descriptionFieldConfig).build();
        fieldDescribeList.add(descriptionLongTextFieldDescribe);

        //6.6.5
        Map<String, Object> ruleMethodFieldConfig = FieldConfig.builder().edit(1).attrs(Lists.newArrayList("is_readonly", "is_required"), 0).attrs(Lists.newArrayList("default_value"), 1).build();
        List<ISelectOption> ruleMethodSelectOptions = Arrays.stream(RuleMethodEnum.values()).map(setting -> SelectOptionBuilder.builder().value(setting.getValue()).label(setting.getLabel()).build()).collect(Collectors.toList());
        SelectOneFieldDescribe ruleMethodSelectOneFieldDescribe = SelectOneFieldDescribeBuilder.builder().apiName(PromotionConstants.Field.RuleMethod.apiName).label(PromotionConstants.Field.RuleMethod.label).selectOptions(ruleMethodSelectOptions).required(false).defaultValud(RuleMethodEnum.UnifiedSetting.getValue()).config(ruleMethodFieldConfig).build();
        fieldDescribeList.add(ruleMethodSelectOneFieldDescribe);

        //6.7.1  适配部门
        DepartmentFieldDescribe departmentFieldDescribe = DepartmentFieldDescribeBuilder.builder().apiName(PromotionConstants.Field.DeptRange.apiName).label(PromotionConstants.Field.DeptRange.label).required(true).single(false).defaultValue(PromotionConstants.DEPT_RANGE_DEFAULT_VALUE).build();
        fieldDescribeList.add(departmentFieldDescribe);

        if (isMultiOrgOpen) {
            DepartmentFieldDescribe dataOwnOrgField = iMetadataMultiOrgService.getDataOwnOrgField();
            dataOwnOrgField.setRequired(true);
            fieldDescribeList.add(dataOwnOrgField);
        }

        Map<String, Object> config = getDisableRecordTypeLayoutConfig();
        return ObjectDescribeBuilder.builder().apiName(PromotionConstants.API_NAME).displayName(PromotionConstants.DISPLAY_NAME).tenantId(tenantId).createBy(fsUserId).fieldDescribes(fieldDescribeList).storeTableName(PromotionConstants.STORE_TABLE_NAME).iconIndex(PromotionConstants.ICON_INDEX).config(config).build();
    }

    private ILayout generatePromotionDefaultLayout(String tenantId, String fsUserId, boolean isMultiOrgOpen) {
        List<IFieldSection> fieldSections = Lists.newArrayList();
        List<IFormField> formFields = Lists.newArrayList();
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionConstants.Field.Name.apiName).readOnly(false).renderType(SystemConstants.RenderType.Text.renderType).required(true).build());
        String renderType = SystemConstants.RenderType.DateTime.renderType;
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionConstants.Field.StartTime.apiName).readOnly(false).renderType(renderType).required(true).build());
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionConstants.Field.EndTime.apiName).readOnly(false).renderType(renderType).required(true).build());
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionConstants.Field.Status.apiName).readOnly(false).renderType(SystemConstants.RenderType.TrueOrFalse.renderType).required(true).build());
        //6.3.2
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionConstants.Field.IsStairPromotion.apiName).readOnly(false).renderType(SystemConstants.RenderType.TrueOrFalse.renderType).required(true).build());
        formFields.add(FormFieldBuilder.builder().fieldName(SystemConstants.Field.Owner.apiName).readOnly(false).required(true).renderType(SystemConstants.RenderType.Employee.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionConstants.Field.Images.apiName).readOnly(false).renderType(SystemConstants.RenderType.Image.renderType).required(false).build());
        //6.6.0
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionConstants.Field.Condition.apiName).readOnly(false).renderType(SystemConstants.RenderType.SelectOne.renderType).required(true).build());
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionConstants.Field.Description.apiName).readOnly(false).renderType(SystemConstants.RenderType.LongText.renderType).required(false).build());
        //6.6.5
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionConstants.Field.RuleMethod.apiName).readOnly(false).renderType(SystemConstants.RenderType.SelectOne.renderType).required(false).build());

        //6.7.1 适配部门
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionConstants.Field.DeptRange.apiName).readOnly(false).renderType(SystemConstants.RenderType.Department.renderType).required(false).build());

        log.info("generatePromotionDefaultLayout isMultiOrgOpen[{}] ", isMultiOrgOpen);
        if (isMultiOrgOpen) {
            formFields.add(FormFieldBuilder.builder().fieldName(SystemConstants.Field.DataOwnOrganization.apiName).renderType(SystemConstants.RenderType.Department.renderType).readOnly(false).required(true).build());
            log.info("formFields formFields[{}] ", formFields);
        }

        FieldSection fieldSection = FieldSectionBuilder.builder().name(LayoutConstants.BASE_FIELD_SECTION_API_NAME).header(LayoutConstants.BASE_FIELD_SECTION_DISPLAY_NAME).showHeader(true).fields(formFields).build();

        List<IFormField> typeFormFields = Lists.newArrayList();
        typeFormFields.add(FormFieldBuilder.builder().fieldName(PromotionConstants.Field.Type.apiName).readOnly(false).renderType(SystemConstants.RenderType.SelectOne.renderType).required(true).build());
        FieldSection typeFieldSection = FieldSectionBuilder.builder().header(PromotionConstants.Field.Type.label).showHeader(true).name(PromotionConstants.TYPE_SECTION_API_NAME).fields(typeFormFields).build();

        List<IFormField> customerRangeFormFields = Lists.newArrayList();
        customerRangeFormFields.add(FormFieldBuilder.builder().fieldName(PromotionConstants.Field.CustomerRange.apiName).readOnly(false).renderType(SystemConstants.RenderType.UseScope.renderType).required(true).build());
        FieldSection customerRangeFieldSection = FieldSectionBuilder.builder().header(PromotionConstants.Field.CustomerRange.label).name(PromotionConstants.CUSTOMER_RANGE_SECTION_API_NAME).showHeader(true).fields(customerRangeFormFields).build();


        fieldSections.add(fieldSection);
        fieldSections.add(typeFieldSection);
        fieldSections.add(customerRangeFieldSection);
        FormComponent formComponent = FormComponentBuilder.builder().name(LayoutConstants.FORM_COMPONENT_API_NAME).buttons(null).fieldSections(fieldSections).build();
        List<IComponent> components = Lists.newArrayList(formComponent);

        //摘要初始化
        SimpleComponent topInfo = getPromotionTopInfo(tenantId);

        return LayoutBuilder.builder().createBy(fsUserId).tenantId(tenantId).name(PromotionConstants.DEFAULT_LAYOUT_API_NAME).displayName(PromotionConstants.DEFAULT_LAYOUT_DISPLAY_NAME).isDefault(true).refObjectApiName(PromotionConstants.API_NAME).components(components).layoutType(SystemConstants.LayoutType.Detail.layoutType).topInfo(topInfo).build();
    }

    @Override
    public SimpleComponent getPromotionTopInfo(String tenantId) {
        List<IFieldSection> topFieldSections = Lists.newArrayList();
        List<IFormField> topFormFields = Lists.newArrayList();
        FormField recordTypeField = FormFieldBuilder.builder().fieldName(SystemConstants.Field.RecordType.apiName).renderType(SystemConstants.RenderType.RecordType.renderType).readOnly(true).required(true).build();
        topFormFields.add(recordTypeField);
        FormField typeField = FormFieldBuilder.builder().fieldName(PromotionConstants.Field.Type.apiName).renderType(SystemConstants.RenderType.SelectOne.renderType).readOnly(true).required(true).build();
        topFormFields.add(typeField);
        FormField statusField = FormFieldBuilder.builder().fieldName(PromotionConstants.Field.Status.apiName).renderType(SystemConstants.RenderType.TrueOrFalse.renderType).readOnly(true).required(true).build();
        topFormFields.add(statusField);
        String renderType = SystemConstants.RenderType.DateTime.renderType;
        FormField startTimeField = FormFieldBuilder.builder().fieldName(PromotionConstants.Field.StartTime.apiName).renderType(renderType).readOnly(true).required(true).build();
        topFormFields.add(startTimeField);
        FormField endTimeField = FormFieldBuilder.builder().fieldName(PromotionConstants.Field.EndTime.apiName).renderType(renderType).readOnly(true).required(true).build();
        topFormFields.add(endTimeField);
        FormField lifeStatusField = FormFieldBuilder.builder().fieldName(SystemConstants.Field.LifeStatus.apiName).renderType(SystemConstants.RenderType.SelectOne.renderType).readOnly(true).required(true).build();
        topFormFields.add(lifeStatusField);
        FieldSection topInfoFieldSection = FieldSectionBuilder.builder().name(LayoutConstants.DETAIL).fields(topFormFields).build();
        topFieldSections.add(topInfoFieldSection);
        return SimpleComponentBuilder.builder().header(LayoutConstants.TOP_INFO_DISPLAY_NAME).isHidden(false).order(0).name(LayoutConstants.HEADER_API_NAME).fieldSections(topFieldSections).build();
    }

    private ILayout generatePromotionListLayout(String tenantId, String fsUserId) {
        List<ITableColumn> tableColumns = Lists.newArrayList();
        tableColumns.add(TableColumnBuilder.builder().name(PromotionConstants.Field.Name.apiName).lableName(PromotionConstants.Field.Name.label).renderType(SystemConstants.RenderType.Text.renderType).build());
        tableColumns.add(TableColumnBuilder.builder().name(SystemConstants.Field.RecordType.apiName).lableName(I18N.text(ProI18NKey.PROMOTIONOBJ_RECORD_TYPE)).renderType(SystemConstants.RenderType.RecordType.renderType).build());
        tableColumns.add(TableColumnBuilder.builder().name(PromotionConstants.Field.Type.apiName).lableName(PromotionConstants.Field.Type.label).renderType(SystemConstants.RenderType.SelectOne.renderType).build());
        tableColumns.add(TableColumnBuilder.builder().name(PromotionConstants.Field.StartTime.apiName).lableName(PromotionConstants.Field.StartTime.label).renderType(SystemConstants.RenderType.Date.renderType).build());
        tableColumns.add(TableColumnBuilder.builder().name(PromotionConstants.Field.EndTime.apiName).lableName(PromotionConstants.Field.EndTime.label).renderType(SystemConstants.RenderType.Date.renderType).build());
        tableColumns.add(TableColumnBuilder.builder().name(PromotionConstants.Field.Status.apiName).lableName(PromotionConstants.Field.Status.label).renderType(SystemConstants.RenderType.TrueOrFalse.renderType).build());
        //6.3.2
        tableColumns.add(TableColumnBuilder.builder().name(PromotionConstants.Field.IsStairPromotion.apiName).lableName(PromotionConstants.Field.IsStairPromotion.label).renderType(SystemConstants.RenderType.TrueOrFalse.renderType).build());

        TableComponent tableComponent = TableComponentBuilder.builder().refObjectApiName(PromotionConstants.API_NAME).includeFields(tableColumns).buttons(null).build();
        List<IComponent> components = Lists.newArrayList(tableComponent);

        return LayoutBuilder.builder().name(PromotionConstants.LIST_LAYOUT_API_NAME).refObjectApiName(PromotionConstants.API_NAME).displayName(PromotionConstants.LIST_LAYOUT_DISPLAY_NAME).tenantId(tenantId).createBy(fsUserId).layoutType(SystemConstants.LayoutType.List.layoutType).isDefault(false).agentType(LayoutConstants.AGENT_TYPE).isShowFieldName(true).components(components).build();
    }

    private IObjectDescribe generatePromotionProductDescribeDraft(String tenantId, String fsUserId, boolean isMultiOrgOpen) {
        List<IFieldDescribe> fieldDescribeList = Lists.newArrayList();
        AutoNumberFieldDescribe nameAutoNumberFieldDescribe = AutoNumberFieldDescribeBuilder.builder().apiName(PromotionProductConstants.Field.Name.apiName).label(PromotionProductConstants.Field.Name.label).required(true).serialNumber(4).startNumber(1).prefix("PP{yyyy}-{mm}-{dd}_").postfix("").required(true).unique(true).index(true).build();
        fieldDescribeList.add(nameAutoNumberFieldDescribe);
        MasterDetailFieldDescribe promotionMasterDetailFieldDescribe = MasterDetailFieldDescribeBuilder.builder().isCreateWhenMasterCreate(true).isRequiredWhenMasterCreate(false).apiName(PromotionProductConstants.Field.Promotion.apiName).label(PromotionProductConstants.Field.Promotion.label).index(true).required(true).targetApiName(PromotionConstants.API_NAME).unique(false).targetRelatedListName(PromotionProductConstants.Field.Promotion.targetRelatedListName)
                .targetRelatedListLabel(PromotionProductConstants.Field.Promotion.targetRelatedListLabel).build();
        fieldDescribeList.add(promotionMasterDetailFieldDescribe);

        ObjectReferenceFieldDescribe productObjectReferenceFieldDescribe = ObjectReferenceFieldDescribeBuilder.builder().apiName(PromotionProductConstants.Field.Product.apiName).label(PromotionProductConstants.Field.Product.label).targetApiName(Utils.PRODUCT_API_NAME).targetRelatedListLabel(PromotionProductConstants.Field.Product.targetRelatedListLabel).targetRelatedListName(PromotionProductConstants.Field.Product.targetRelatedListName).unique(false).required(true).build();
        fieldDescribeList.add(productObjectReferenceFieldDescribe);

        QuoteFieldDescribe priceQuoteFieldDescribe = QuoteFieldDescribeBuilder.builder().apiName(PromotionProductConstants.Field.Price.apiName).label(PromotionProductConstants.Field.Price.label).unique(false).required(false).quoteField(PromotionProductConstants.Field.Product.apiName.concat("__r.price")).quoteFieldType("currency").build();
        fieldDescribeList.add(priceQuoteFieldDescribe);
        //6.3
        NumberFieldDescribe quotaNumberFieldDescribe = NumberFieldDescribeBuilder.builder().apiName(PromotionProductConstants.Field.Quota.apiName).label(PromotionProductConstants.Field.Quota.label).defaultValue("0").decimalPalces(0).length(12).maxLength(14).build();
        fieldDescribeList.add(quotaNumberFieldDescribe);

        Map<String, Object> fieldConfig = FieldConfig.builder().edit(0).attrs(Lists.newArrayList("is_readonly", "is_required"), 0).build();
        NumberFieldDescribe availableQuotaField = NumberFieldDescribeBuilder.builder().apiName(PromotionProductConstants.Field.LeftQuota.apiName).label(PromotionProductConstants.Field.LeftQuota.label).required(false).config(fieldConfig).defaultValue("0").decimalPalces(0).length(12).maxLength(14).build();
        fieldDescribeList.add(availableQuotaField);
        //6.3.2 新增四个字段
        NumberFieldDescribe combineAmountNumberFieldDescribe = NumberFieldDescribeBuilder.builder().apiName(PromotionProductConstants.Field.CombineAmount.apiName).label(PromotionProductConstants.Field.CombineAmount.label).required(false).decimalPalces(0).length(12).maxLength(12).build();
        fieldDescribeList.add(combineAmountNumberFieldDescribe);

        PercentileFieldDescribe priceDiscountPercentileFieldDescribe = PercentileFieldDescribeBuilder.builder().apiName(PromotionProductConstants.Field.PriceDiscount.apiName).label(PromotionProductConstants.Field.PriceDiscount.label).required(false).build();
        fieldDescribeList.add(priceDiscountPercentileFieldDescribe);

        CurrencyFieldDescribe derateMoneyCurrencyFieldDescribe = CurrencyFieldDescribeBuilder.builder().apiName(PromotionProductConstants.Field.DerateMoney.apiName).label(PromotionProductConstants.Field.DerateMoney.label).currencyUnit("￥").decimalPlaces(2).length(12).maxLength(12).required(false).roundMode(4).build();
        fieldDescribeList.add(derateMoneyCurrencyFieldDescribe);

        CurrencyFieldDescribe fixedPriceCurrencyFieldDescribe = CurrencyFieldDescribeBuilder.builder().apiName(PromotionProductConstants.Field.FixedPrice.apiName).label(PromotionProductConstants.Field.FixedPrice.label).currencyUnit("￥").decimalPlaces(2).length(12).maxLength(12).required(false).roundMode(4).build();
        fieldDescribeList.add(fixedPriceCurrencyFieldDescribe);

        QuoteFieldDescribe specificationQuoteFieldDescribe = QuoteFieldDescribeBuilder.builder().apiName(PromotionProductConstants.Field.Specification.apiName).label(PromotionProductConstants.Field.Specification.label).unique(false).required(false).quoteField(PromotionProductConstants.Field.Product.apiName.concat("__r.product_spec")).quoteFieldType("text").build();
        fieldDescribeList.add(specificationQuoteFieldDescribe);

        QuoteFieldDescribe unitQuoteFieldDescribe = QuoteFieldDescribeBuilder.builder().apiName(PromotionProductConstants.Field.Unit.apiName).label(PromotionProductConstants.Field.Unit.label).unique(false).required(false).quoteField(PromotionProductConstants.Field.Product.apiName.concat("__r.unit")).quoteFieldType("select_one").build();
        fieldDescribeList.add(unitQuoteFieldDescribe);

        QuoteFieldDescribe statusQuoteFieldDescribe = QuoteFieldDescribeBuilder.builder().apiName(PromotionProductConstants.Field.ProductStatus.apiName).label(PromotionProductConstants.Field.ProductStatus.label).unique(false).required(false).quoteField(PromotionProductConstants.Field.Product.apiName.concat("__r.product_status")).quoteFieldType("select_one").build();
        fieldDescribeList.add(statusQuoteFieldDescribe);

        //6.3.2新增组合促销业务类型
        List<IRecordTypeOption> recordTypeOptions = Arrays.stream(PromotionProductRecordTypeEnum.values()).map(recordType -> RecordTypeOptionBuilder.builder().apiName(recordType.getApiName()).label(recordType.getLabel()).build()).collect(Collectors.toList());
        RecordTypeFieldDescribe recordTypeSelectOneFieldDescribe = RecordTypeFieldDescribeBuilder.builder().apiName(SystemConstants.Field.RecordType.apiName).label(I18N.text(ProI18NKey.PROMOTIONOBJ_RECORD_TYPE)).recordTypeOptions(recordTypeOptions).build();
        fieldDescribeList.add(recordTypeSelectOneFieldDescribe);

        //6.6.0
        CurrencyFieldDescribe amountQuotaCurrencyFieldDescribe = CurrencyFieldDescribeBuilder.builder().apiName(PromotionProductConstants.Field.AmountQuota.apiName).label(PromotionProductConstants.Field.AmountQuota.label).currencyUnit("￥").decimalPlaces(2).length(12).maxLength(12).required(false).roundMode(4).build();
        fieldDescribeList.add(amountQuotaCurrencyFieldDescribe);

        CurrencyFieldDescribe leftAmountQuotaCurrencyFieldDescribe = CurrencyFieldDescribeBuilder.builder().apiName(PromotionProductConstants.Field.LeftAmountQuota.apiName).label(PromotionProductConstants.Field.LeftAmountQuota.label).currencyUnit("￥").decimalPlaces(2).length(12).maxLength(12).required(false).roundMode(4).build();
        fieldDescribeList.add(leftAmountQuotaCurrencyFieldDescribe);

        //6.6.5
        Map<String, Object> purchaseNumConfig = FieldConfig.builder().edit(1).attrs(Lists.newArrayList("decimal_places"), 1).attrs(Lists.newArrayList("is_required"), 0).build();
        NumberFieldDescribe purchaseNumNumberFieldDescribe = NumberFieldDescribeBuilder.builder().apiName(PromotionProductConstants.Field.PurchaseNum.apiName).label(PromotionProductConstants.Field.PurchaseNum.label).config(purchaseNumConfig).decimalPalces(0).length(12).maxLength(14).build();
        fieldDescribeList.add(purchaseNumNumberFieldDescribe);

        CurrencyFieldDescribe purchaseAmountFieldDescribe = CurrencyFieldDescribeBuilder.builder().apiName(PromotionProductConstants.Field.PurchaseAmount.apiName).label(PromotionProductConstants.Field.PurchaseAmount.label).currencyUnit("￥").decimalPlaces(2).length(12).maxLength(14).required(false).roundMode(4).build();
        fieldDescribeList.add(purchaseAmountFieldDescribe);

        List<ISelectOption> giftTypeSelectOptions = Arrays.stream(GiftTypeEnum.values()).map(typeEnum -> SelectOptionBuilder.builder().value(typeEnum.getValue()).label(typeEnum.getLabel()).build()).collect(Collectors.toList());
        SelectOneFieldDescribe giftTypeSelectOneFieldDescribe = SelectOneFieldDescribeBuilder.builder().apiName(PromotionProductConstants.Field.GiftType.apiName).label(PromotionProductConstants.Field.GiftType.label).selectOptions(giftTypeSelectOptions).required(false).build();
        fieldDescribeList.add(giftTypeSelectOneFieldDescribe);

        ObjectReferenceFieldDescribe giftProductObjectReferenceFieldDescribe = ObjectReferenceFieldDescribeBuilder.builder().apiName(PromotionProductConstants.Field.GiftProduct.apiName).label(PromotionProductConstants.Field.GiftProduct.label).targetApiName(Utils.PRODUCT_API_NAME).targetRelatedListLabel(PromotionProductConstants.Field.GiftProduct.targetRelatedListLabel).targetRelatedListName(PromotionProductConstants.Field.GiftProduct.targetRelatedListName).unique(false).required(false).build();
        fieldDescribeList.add(giftProductObjectReferenceFieldDescribe);

        Map<String, Object> giftProductNumConfig = FieldConfig.builder().edit(1).attrs(Lists.newArrayList("decimal_places"), 1).attrs(Lists.newArrayList("is_required"), 0).build();
        NumberFieldDescribe giftProductNumNumberFieldDescribe = NumberFieldDescribeBuilder.builder().apiName(PromotionProductConstants.Field.GiftProductNum.apiName).label(PromotionProductConstants.Field.GiftProductNum.label).config(giftProductNumConfig).decimalPalces(0).length(12).maxLength(14).build();
        fieldDescribeList.add(giftProductNumNumberFieldDescribe);

        TextFieldDescribe ruleDescriptionFieldDescribe = TextFieldDescribeBuilder.builder().apiName(PromotionProductConstants.Field.RuleDescription.apiName).label(PromotionProductConstants.Field.RuleDescription.label).maxLength(500).build();
        fieldDescribeList.add(ruleDescriptionFieldDescribe);

        if (isMultiOrgOpen) {
            DepartmentFieldDescribe dataOwnOrgField = iMetadataMultiOrgService.getDataOwnOrgField();
            dataOwnOrgField.setRequired(true);
            fieldDescribeList.add(dataOwnOrgField);
        }

        Map<String, Object> config = getDisableRecordTypeLayoutConfig();
        return ObjectDescribeBuilder.builder().apiName(PromotionProductConstants.API_NAME).displayName(PromotionProductConstants.DISPLAY_NAME).createBy(fsUserId).tenantId(tenantId).iconIndex(PromotionProductConstants.ICON_INDEX).storeTableName(PromotionProductConstants.STORE_TABLE_NAME).fieldDescribes(fieldDescribeList).config(config).build();
    }

    private ILayout generatePromotionProductDefaultLayout(String tenantId, String fsUserId, boolean isMultiOrgOpen) {
        List<IFieldSection> fieldSections = Lists.newArrayList();
        List<IFormField> formFields = Lists.newArrayList();

        formFields.add(FormFieldBuilder.builder().fieldName(PromotionProductConstants.Field.Name.apiName).readOnly(false).required(true).renderType(SystemConstants.RenderType.AutoNumber.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionProductConstants.Field.Product.apiName).readOnly(false).required(true).renderType(SystemConstants.RenderType.ObjectReference.renderType).build());
        //6.6.5
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionProductConstants.Field.RuleDescription.apiName).readOnly(true).required(false).renderType(SystemConstants.RenderType.Text.renderType).build());

        formFields.add(FormFieldBuilder.builder().fieldName(PromotionProductConstants.Field.ProductStatus.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.Quote.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionProductConstants.Field.Price.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.Quote.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionProductConstants.Field.Specification.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.Quote.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionProductConstants.Field.Unit.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.Quote.renderType).build());
        //6.3
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionProductConstants.Field.Quota.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.Number.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionProductConstants.Field.LeftQuota.apiName).readOnly(true).required(false).renderType(SystemConstants.RenderType.Number.renderType).build());
        //6.6.0
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionProductConstants.Field.AmountQuota.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.Currency.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionProductConstants.Field.LeftAmountQuota.apiName).readOnly(true).required(false).renderType(SystemConstants.RenderType.Currency.renderType).build());
        //6.6.5
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionProductConstants.Field.PriceDiscount.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.Percentile.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionProductConstants.Field.DerateMoney.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.Currency.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionProductConstants.Field.FixedPrice.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.Currency.renderType).build());

        formFields.add(FormFieldBuilder.builder().fieldName(PromotionProductConstants.Field.PurchaseNum.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.Number.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionProductConstants.Field.PurchaseAmount.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.Currency.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionProductConstants.Field.GiftType.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.SelectOne.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionProductConstants.Field.GiftProduct.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.ObjectReference.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionProductConstants.Field.GiftProductNum.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.Number.renderType).build());

        if (isMultiOrgOpen) {
            formFields.add(FormFieldBuilder.builder().fieldName(SystemConstants.Field.DataOwnOrganization.apiName).renderType(SystemConstants.RenderType.Department.renderType).readOnly(false).required(true).build());
        }

        FieldSection fieldSection = FieldSectionBuilder.builder().name(LayoutConstants.BASE_FIELD_SECTION_API_NAME).header(LayoutConstants.BASE_FIELD_SECTION_DISPLAY_NAME).showHeader(true).fields(formFields).build();
        fieldSections.add(fieldSection);

        FormComponent formComponent = FormComponentBuilder.builder().name(LayoutConstants.FORM_COMPONENT_API_NAME).buttons(null).fieldSections(fieldSections).build();
        List<IComponent> components = Lists.newArrayList(formComponent);

        return LayoutBuilder.builder().tenantId(tenantId).createBy(fsUserId).displayName(PromotionProductConstants.DEFAULT_LAYOUT_DISPLAY_NAME).name(PromotionProductConstants.DEFAULT_LAYOUT_API_NAME).isDefault(true).layoutType(SystemConstants.LayoutType.Detail.layoutType).refObjectApiName(PromotionProductConstants.API_NAME).components(components).build();
    }

    //6.3.2 组合促销对应的业务类型
    private ILayout generatePromotionProductCombinePromotionLayout(String tenantId, String fsUserId) {
        List<IFieldSection> fieldSections = Lists.newArrayList();
        List<IFormField> formFields = Lists.newArrayList();

        formFields.add(FormFieldBuilder.builder().fieldName(PromotionProductConstants.Field.Name.apiName).readOnly(false).required(true).renderType(SystemConstants.RenderType.AutoNumber.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionProductConstants.Field.Product.apiName).readOnly(false).required(true).renderType(SystemConstants.RenderType.ObjectReference.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionProductConstants.Field.ProductStatus.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.Quote.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionProductConstants.Field.Price.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.Quote.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionProductConstants.Field.Specification.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.Quote.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionProductConstants.Field.Unit.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.Quote.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionProductConstants.Field.CombineAmount.apiName).readOnly(false).required(true).renderType(SystemConstants.RenderType.Number.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionProductConstants.Field.PriceDiscount.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.Percentile.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionProductConstants.Field.DerateMoney.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.Currency.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionProductConstants.Field.FixedPrice.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.Currency.renderType).build());
        FieldSection fieldSection = FieldSectionBuilder.builder().name(LayoutConstants.BASE_FIELD_SECTION_API_NAME).header(LayoutConstants.BASE_FIELD_SECTION_DISPLAY_NAME).showHeader(true).fields(formFields).build();
        fieldSections.add(fieldSection);

        FormComponent formComponent = FormComponentBuilder.builder().name(LayoutConstants.FORM_COMPONENT_API_NAME).buttons(null).fieldSections(fieldSections).build();
        List<IComponent> components = Lists.newArrayList(formComponent);
        Map<String, Object> config = Maps.newHashMap();
        config.put("remove", 0);
        return LayoutBuilder.builder().tenantId(tenantId).createBy(fsUserId).displayName(PromotionProductConstants.COMBINE_PROMOTION_DISPLAY_NAME).name(PromotionProductConstants.COMBINE_PROMOTION_LAYOUT_API_NAME).isDefault(false).layoutType(SystemConstants.LayoutType.Detail.layoutType).refObjectApiName(PromotionProductConstants.API_NAME).components(components).config(config).build();
    }

    private ILayout generatePromotionProductListLayout(String tenantId, String fsUserId) {
        List<ITableColumn> tableColumns = Lists.newArrayList();
        tableColumns.add(TableColumnBuilder.builder().name(PromotionProductConstants.Field.Product.apiName).lableName(PromotionProductConstants.Field.Product.label).renderType(SystemConstants.RenderType.ObjectReference.renderType).build());
        tableColumns.add(TableColumnBuilder.builder().name(PromotionProductConstants.Field.Specification.apiName).lableName(PromotionProductConstants.Field.Specification.label).renderType(SystemConstants.RenderType.Quote.renderType).build());
        tableColumns.add(TableColumnBuilder.builder().name(PromotionProductConstants.Field.Unit.apiName).lableName(PromotionProductConstants.Field.Unit.label).renderType(SystemConstants.RenderType.Quote.renderType).build());
        tableColumns.add(TableColumnBuilder.builder().name(PromotionProductConstants.Field.RuleDescription.apiName).lableName(PromotionProductConstants.Field.RuleDescription.label).renderType(SystemConstants.RenderType.Text.renderType).build());

        TableComponent tableComponent = TableComponentBuilder.builder().refObjectApiName(PromotionProductConstants.API_NAME).includeFields(tableColumns).buttons(null).build();
        List<IComponent> components = Lists.newArrayList(tableComponent);
        return LayoutBuilder.builder().tenantId(tenantId).createBy(fsUserId).refObjectApiName(PromotionProductConstants.API_NAME).layoutType(SystemConstants.LayoutType.List.layoutType).isDefault(false).name(PromotionProductConstants.LIST_LAYOUT_API_NAME).displayName(PromotionProductConstants.LIST_LAYOUT_DISPLAY_NAME).isShowFieldName(true).agentType(LayoutConstants.AGENT_TYPE).components(components).build();
    }

    private IObjectDescribe generatePromotionRuleDescribeDraft(String tenantId, String fsUserId, boolean isMultiOrgOpen) {
        List<IFieldDescribe> fieldDescribeList = Lists.newArrayList();
        AutoNumberFieldDescribe nameAutoNumberFieldDescribe = AutoNumberFieldDescribeBuilder.builder().apiName(PromotionRuleConstants.Field.Name.apiName).label(PromotionRuleConstants.Field.Name.getLabel()).index(true).unique(true).required(true).prefix("PR{yyyy}-{mm}-{dd}_").postfix("").startNumber(1).serialNumber(4).build();
        fieldDescribeList.add(nameAutoNumberFieldDescribe);

        MasterDetailFieldDescribe promotionMasterDetailFieldDescribe = MasterDetailFieldDescribeBuilder.builder().isCreateWhenMasterCreate(true).isRequiredWhenMasterCreate(true).apiName(PromotionRuleConstants.Field.Promotion.apiName).label(PromotionRuleConstants.Field.Promotion.getLabel()).index(true).required(true).targetApiName(PromotionConstants.API_NAME).unique(false).targetRelatedListName(PromotionRuleConstants.Field.Promotion.targetRelatedListName)
                .targetRelatedListLabel(PromotionRuleConstants.Field.Promotion.targetRelatedListLabel).build();
        fieldDescribeList.add(promotionMasterDetailFieldDescribe);

        Map<String, Object> purchaseNumConfig = FieldConfig.builder().edit(1).attrs(Lists.newArrayList("decimal_places"), 1).attrs(Lists.newArrayList("is_required"), 0).build();
        NumberFieldDescribe purchaseNumNumberFieldDescribe = NumberFieldDescribeBuilder.builder().apiName(PromotionRuleConstants.Field.PurchaseNum.apiName).label(PromotionRuleConstants.Field.PurchaseNum.getLabel()).config(purchaseNumConfig).decimalPalces(0).length(12).maxLength(14).build();
        fieldDescribeList.add(purchaseNumNumberFieldDescribe);

        CurrencyFieldDescribe fixedPriceCurrencyFieldDescribe = CurrencyFieldDescribeBuilder.builder().apiName(PromotionRuleConstants.Field.FixedPrice.apiName).label(PromotionRuleConstants.Field.FixedPrice.getLabel()).currencyUnit("￥").decimalPlaces(2).length(12).maxLength(14).required(false).roundMode(4).build();
        fieldDescribeList.add(fixedPriceCurrencyFieldDescribe);

        CurrencyFieldDescribe derateMoneyCurrencyFieldDescribe = CurrencyFieldDescribeBuilder.builder().apiName(PromotionRuleConstants.Field.DerateMoney.apiName).label(PromotionRuleConstants.Field.DerateMoney.getLabel()).currencyUnit("￥").decimalPlaces(2).length(12).maxLength(14).required(false).roundMode(4).build();
        fieldDescribeList.add(derateMoneyCurrencyFieldDescribe);

        PercentileFieldDescribe priceDiscountPrecentileFieldDescribe = PercentileFieldDescribeBuilder.builder().apiName(PromotionRuleConstants.Field.PriceDiscount.apiName).label(PromotionRuleConstants.Field.PriceDiscount.getLabel()).build();
        fieldDescribeList.add(priceDiscountPrecentileFieldDescribe);

        CurrencyFieldDescribe orderMoneyCurrencyFieldDescribe = CurrencyFieldDescribeBuilder.builder().apiName(PromotionRuleConstants.Field.OrderMoney.apiName).label(PromotionRuleConstants.Field.OrderMoney.getLabel()).currencyUnit("￥").decimalPlaces(2).length(12).maxLength(14).required(false).roundMode(4).build();
        fieldDescribeList.add(orderMoneyCurrencyFieldDescribe);

        PercentileFieldDescribe orderDiscountPrecentileFieldDescribe = PercentileFieldDescribeBuilder.builder().apiName(PromotionRuleConstants.Field.OrderDiscount.apiName).label(PromotionRuleConstants.Field.OrderDiscount.getLabel()).build();
        fieldDescribeList.add(orderDiscountPrecentileFieldDescribe);

        CurrencyFieldDescribe orderDerateMoneyCurrencyFieldDescribe = CurrencyFieldDescribeBuilder.builder().apiName(PromotionRuleConstants.Field.OrderDerateMoney.apiName).label(PromotionRuleConstants.Field.OrderDerateMoney.getLabel()).currencyUnit("￥").decimalPlaces(2).length(12).maxLength(14).required(false).roundMode(4).build();
        fieldDescribeList.add(orderDerateMoneyCurrencyFieldDescribe);
        //6.3.2
        CurrencyFieldDescribe combineDerateMoneyCurrencyFieldDescribe = CurrencyFieldDescribeBuilder.builder().apiName(PromotionRuleConstants.Field.CombineDereateMoney.apiName).label(PromotionRuleConstants.Field.CombineDereateMoney.getLabel()).currencyUnit("￥").decimalPlaces(2).length(12).maxLength(12).required(false).roundMode(4).build();
        fieldDescribeList.add(combineDerateMoneyCurrencyFieldDescribe);

        CurrencyFieldDescribe combineFixedPriceCurrencyFieldDescribe = CurrencyFieldDescribeBuilder.builder().apiName(PromotionRuleConstants.Field.CombineFixedPrice.apiName).label(PromotionRuleConstants.Field.CombineFixedPrice.getLabel()).currencyUnit("￥").decimalPlaces(2).length(12).maxLength(12).required(false).roundMode(4).build();
        fieldDescribeList.add(combineFixedPriceCurrencyFieldDescribe);

        PercentileFieldDescribe combineDiscountPercentileFieldDescribe = PercentileFieldDescribeBuilder.builder().apiName(PromotionRuleConstants.Field.CombineDiscount.apiName).label(PromotionRuleConstants.Field.CombineDiscount.getLabel()).required(false).build();
        fieldDescribeList.add(combineDiscountPercentileFieldDescribe);

        /*//6.3
        List<ISelectOption> giftTypeSelectOptions = Arrays.stream(GiftTypeEnum.values()).map(typeEnum -> SelectOptionBuilder.builder().value(typeEnum.getValue()).label(typeEnum.getLabel()).build()).collect(Collectors.toList());
        SelectOneFieldDescribe giftTypeSelectOneFieldDescribe = SelectOneFieldDescribeBuilder.builder().apiName(PromotionRuleConstants.Field.GiftType.apiName).label(PromotionRuleConstants.Field.GiftType.getLabel()).selectOptions(giftTypeSelectOptions).required(false).build();
        fieldDescribeList.add(giftTypeSelectOneFieldDescribe);

        //6.3去掉wheres，赠品可以是非赠品
        ObjectReferenceFieldDescribe giftProductObjectReferenceFieldDescribe = ObjectReferenceFieldDescribeBuilder.builder().apiName(PromotionRuleConstants.Field.GiftProduct.apiName).label(PromotionRuleConstants.Field.GiftProduct.getLabel()).targetApiName(Utils.PRODUCT_API_NAME).targetRelatedListLabel(PromotionRuleConstants.Field.GiftProduct.targetRelatedListLabel).targetRelatedListName(PromotionRuleConstants.Field.GiftProduct.targetRelatedListName).unique(false).required(false).build();
        fieldDescribeList.add(giftProductObjectReferenceFieldDescribe);

        NumberFieldDescribe giftProductNumNumberFieldDescribe = NumberFieldDescribeBuilder.builder().apiName(PromotionRuleConstants.Field.GiftProductNum.apiName).label(PromotionRuleConstants.Field.GiftProductNum.getLabel()).decimalPalces(0).length(12).maxLength(14).build();
        fieldDescribeList.add(giftProductNumNumberFieldDescribe);*/

        List<ISelectOption> giftMethodSelectOptions = Arrays.stream(GiftMethodEnum.values()).map(x -> SelectOptionBuilder.builder().value(x.getValue()).label(x.getLabel()).build()).collect(Collectors.toList());
        SelectOneFieldDescribe giftMethodSelectOneFieldDescribe = SelectOneFieldDescribeBuilder.builder().apiName(PromotionRuleConstants.Field.GiftMethod.apiName).label(PromotionRuleConstants.Field.GiftMethod.getLabel()).selectOptions(giftMethodSelectOptions).required(false).build();
        fieldDescribeList.add(giftMethodSelectOneFieldDescribe);

        NumberFieldDescribe optionGiftNumNumberFieldDescribe = NumberFieldDescribeBuilder.builder().apiName(PromotionRuleConstants.Field.OptionGiftNum.apiName).label(PromotionRuleConstants.Field.OptionGiftNum.getLabel()).decimalPalces(0).length(12).maxLength(14).build();
        fieldDescribeList.add(optionGiftNumNumberFieldDescribe);

        List<IRecordTypeOption> recordTypeOptions = Arrays.stream(PromotionRuleRecordTypeEnum.values()).map(recordType -> RecordTypeOptionBuilder.builder().apiName(recordType.getApiName()).label(recordType.getLabel()).build()).collect(Collectors.toList());
        RecordTypeFieldDescribe recordTypeSelectOneFieldDescribe = RecordTypeFieldDescribeBuilder.builder().apiName(SystemConstants.Field.RecordType.apiName).label(I18N.text(ProI18NKey.PROMOTIONOBJ_RECORD_TYPE)).recordTypeOptions(recordTypeOptions).build();
        fieldDescribeList.add(recordTypeSelectOneFieldDescribe);

        //6.6.5
        CurrencyFieldDescribe purchaseAmountFieldDescribe = CurrencyFieldDescribeBuilder.builder().apiName(PromotionRuleConstants.Field.PurchaseAmount.apiName).label(PromotionRuleConstants.Field.PurchaseAmount.getLabel()).currencyUnit("￥").decimalPlaces(2).length(12).maxLength(14).required(false).roundMode(4).build();
        fieldDescribeList.add(purchaseAmountFieldDescribe);

        Map<String, Object> orderProductNumConfig = FieldConfig.builder().edit(1).attrs(Lists.newArrayList("decimal_places"), 1).attrs(Lists.newArrayList("is_required"), 0).build();
        NumberFieldDescribe orderProductNumNumberFieldDescribe = NumberFieldDescribeBuilder.builder().apiName(PromotionRuleConstants.Field.OrderProductNum.apiName).label(PromotionRuleConstants.Field.OrderProductNum.getLabel()).config(orderProductNumConfig).decimalPalces(0).length(12).maxLength(14).build();
        fieldDescribeList.add(orderProductNumNumberFieldDescribe);

        if (isMultiOrgOpen) {
            DepartmentFieldDescribe dataOwnOrgField = iMetadataMultiOrgService.getDataOwnOrgField();
            dataOwnOrgField.setRequired(true);
            fieldDescribeList.add(dataOwnOrgField);
        }

        //禁止添加字段
        Map<String, Object> fieldConfigMap = Maps.newHashMap();
        fieldConfigMap.put("add", 0);
        Map<String, Object> config = getDisableRecordTypeLayoutConfig();
        config.put("fields", fieldConfigMap);
        return ObjectDescribeBuilder.builder().tenantId(tenantId).createBy(fsUserId).apiName(PromotionRuleConstants.API_NAME).displayName(PromotionRuleConstants.DISPLAY_NAME).fieldDescribes(fieldDescribeList).storeTableName(PromotionRuleConstants.STORE_TABLE_NAME).iconIndex(PromotionRuleConstants.ICON_INDEX).config(config).build();
    }

    private ILayout generatePromotionRuleDefaultLayout(String tenantId, String fsUserId, boolean isMultiOrgOpen) {
        List<IFieldSection> fieldSections = Lists.newArrayList();
        List<IFormField> formFields = Lists.newArrayList();

        formFields.add(FormFieldBuilder.builder().fieldName(PromotionRuleConstants.Field.Name.apiName).readOnly(false).required(true).renderType(SystemConstants.RenderType.AutoNumber.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionRuleConstants.Field.Promotion.apiName).readOnly(false).required(true).renderType(SystemConstants.RenderType.MasterDetail.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionRuleConstants.Field.PurchaseNum.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.Number.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionRuleConstants.Field.FixedPrice.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.Currency.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionRuleConstants.Field.DerateMoney.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.Currency.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionRuleConstants.Field.PriceDiscount.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.Percentile.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionRuleConstants.Field.OrderMoney.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.Currency.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionRuleConstants.Field.OrderDiscount.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.Percentile.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionRuleConstants.Field.OrderDerateMoney.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.Currency.renderType).build());
        //6.3.2
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionRuleConstants.Field.CombineDereateMoney.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.Currency.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionRuleConstants.Field.CombineFixedPrice.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.Currency.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionRuleConstants.Field.CombineDiscount.apiName).readOnly(true).required(false).renderType(SystemConstants.RenderType.Percentile.renderType).build());
        //6.3
//        formFields.add(FormFieldBuilder.builder().fieldName(PromotionRuleConstants.Field.GiftType.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.SelectOne.renderType).build());
//        formFields.add(FormFieldBuilder.builder().fieldName(PromotionRuleConstants.Field.GiftProduct.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.ObjectReference.renderType).build());
//        formFields.add(FormFieldBuilder.builder().fieldName(PromotionRuleConstants.Field.GiftProductNum.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.Number.renderType).build());
        //6.9
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionRuleConstants.Field.GiftMethod.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.SelectOne.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionRuleConstants.Field.OptionGiftNum.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.Number.renderType).build());

        //6.6.5
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionRuleConstants.Field.PurchaseAmount.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.Currency.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(PromotionRuleConstants.Field.OrderProductNum.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.Number.renderType).build());

        if (isMultiOrgOpen) {
            formFields.add(FormFieldBuilder.builder().fieldName(SystemConstants.Field.DataOwnOrganization.apiName).renderType(SystemConstants.RenderType.Department.renderType).readOnly(false).required(true).build());
        }

        FieldSection fieldSection = FieldSectionBuilder.builder().name(LayoutConstants.BASE_FIELD_SECTION_API_NAME).header(LayoutConstants.BASE_FIELD_SECTION_DISPLAY_NAME).showHeader(true).fields(formFields).build();
        fieldSections.add(fieldSection);

        FormComponent formComponent = FormComponentBuilder.builder().name(LayoutConstants.FORM_COMPONENT_API_NAME).buttons(null).fieldSections(fieldSections).build();
        List<IComponent> components = Lists.newArrayList(formComponent);
        return LayoutBuilder.builder().tenantId(tenantId).createBy(fsUserId).displayName(PromotionRuleConstants.DEFAULT_LAYOUT_DISPLAY_NAME).name(PromotionRuleConstants.DEFAULT_LAYOUT_API_NAME).isDefault(true).layoutType(SystemConstants.LayoutType.Detail.layoutType).refObjectApiName(PromotionRuleConstants.API_NAME).components(components).build();

    }

    private ILayout generatePromotionRuleListLayout(String tenantId, String fsUserId) {
        List<ITableColumn> tableColumns = Lists.newArrayList();
        tableColumns.add(TableColumnBuilder.builder().name(PromotionRuleConstants.Field.Name.apiName).lableName(PromotionRuleConstants.Field.Name.getLabel()).renderType(SystemConstants.RenderType.AutoNumber.renderType).build());
        tableColumns.add(TableColumnBuilder.builder().name(PromotionRuleConstants.Field.Promotion.apiName).lableName(PromotionRuleConstants.Field.Promotion.getLabel()).renderType(SystemConstants.RenderType.MasterDetail.renderType).build());
        tableColumns.add(TableColumnBuilder.builder().name(PromotionRuleConstants.Field.PurchaseNum.apiName).lableName(PromotionRuleConstants.Field.PurchaseNum.getLabel()).renderType(SystemConstants.RenderType.Number.renderType).build());
        tableColumns.add(TableColumnBuilder.builder().name(PromotionRuleConstants.Field.FixedPrice.apiName).lableName(PromotionRuleConstants.Field.FixedPrice.getLabel()).renderType(SystemConstants.RenderType.Currency.renderType).build());
        tableColumns.add(TableColumnBuilder.builder().name(PromotionRuleConstants.Field.DerateMoney.apiName).lableName(PromotionRuleConstants.Field.DerateMoney.getLabel()).renderType(SystemConstants.RenderType.Currency.renderType).build());
        tableColumns.add(TableColumnBuilder.builder().name(PromotionRuleConstants.Field.PriceDiscount.apiName).lableName(PromotionRuleConstants.Field.PriceDiscount.getLabel()).renderType(SystemConstants.RenderType.Percentile.renderType).build());
        tableColumns.add(TableColumnBuilder.builder().name(PromotionRuleConstants.Field.OrderMoney.apiName).lableName(PromotionRuleConstants.Field.OrderMoney.getLabel()).renderType(SystemConstants.RenderType.Currency.renderType).build());
        tableColumns.add(TableColumnBuilder.builder().name(PromotionRuleConstants.Field.OrderDiscount.apiName).lableName(PromotionRuleConstants.Field.OrderDiscount.getLabel()).renderType(SystemConstants.RenderType.Percentile.renderType).build());
        tableColumns.add(TableColumnBuilder.builder().name(PromotionRuleConstants.Field.OrderDerateMoney.apiName).lableName(PromotionRuleConstants.Field.OrderDerateMoney.getLabel()).renderType(SystemConstants.RenderType.Currency.renderType).build());
//        tableColumns.add(TableColumnBuilder.builder().name(PromotionRuleConstants.Field.GiftProduct.apiName).lableName(PromotionRuleConstants.Field.GiftProduct.getLabel()).renderType(SystemConstants.RenderType.ObjectReference.renderType).build());
//        tableColumns.add(TableColumnBuilder.builder().name(PromotionRuleConstants.Field.GiftProductNum.apiName).lableName(PromotionRuleConstants.Field.GiftProductNum.getLabel()).renderType(SystemConstants.RenderType.Number.renderType).build());
        tableColumns.add(TableColumnBuilder.builder().name(PromotionRuleConstants.Field.GiftMethod.apiName).lableName(PromotionRuleConstants.Field.GiftMethod.getLabel()).renderType(SystemConstants.RenderType.SelectOne.renderType).build());
        tableColumns.add(TableColumnBuilder.builder().name(PromotionRuleConstants.Field.OptionGiftNum.apiName).lableName(PromotionRuleConstants.Field.OptionGiftNum.getLabel()).renderType(SystemConstants.RenderType.Number.renderType).build());

        TableComponent tableComponent = TableComponentBuilder.builder().refObjectApiName(PromotionRuleConstants.API_NAME).includeFields(tableColumns).buttons(null).build();
        List<IComponent> components = Lists.newArrayList(tableComponent);

        return LayoutBuilder.builder().tenantId(tenantId).createBy(fsUserId).refObjectApiName(PromotionRuleConstants.API_NAME).layoutType(SystemConstants.LayoutType.List.layoutType).isDefault(false).name(PromotionRuleConstants.LIST_LAYOUT_API_NAME).displayName(PromotionRuleConstants.LIST_LAYOUT_DISPLAY_NAME).isShowFieldName(true).agentType(LayoutConstants.AGENT_TYPE).components(components).build();
    }

    private IObjectDescribe generateAdvertisementDescribe(String tenantId, String fsUserId, boolean spuMode) {
        List<IFieldDescribe> fieldDescribeList = Lists.newArrayList();
        AutoNumberFieldDescribe nameAutoNumberFieldDescribe = AutoNumberFieldDescribeBuilder.builder().apiName(AdvertisementConstants.Field.Name.apiName).label(AdvertisementConstants.Field.Name.label).index(true).unique(true).required(true).prefix("AD{yyyy}-{mm}-{dd}_").postfix("").startNumber(1).serialNumber(4).build();
        fieldDescribeList.add(nameAutoNumberFieldDescribe);

        Map<String, Object> config = Maps.newHashMap();
        Map<String, Object> attrMap = Maps.newHashMap();
        attrMap.put("is_required", 0);
        attrMap.put("is_readonly", 0);
        attrMap.put("default_value", 1);
        config.put("attrs", attrMap);
        ImageFieldDescribe imageFieldDescribe = ImageFieldDescribeBuilder.builder().apiName(AdvertisementConstants.Field.AdPictures.apiName).label(AdvertisementConstants.Field.AdPictures.label).config(config).fileAmountLimit(1).required(true).build();
        fieldDescribeList.add(imageFieldDescribe);

        List<ISelectOption> jumpTypeSelectOptions = null;
        if (spuMode) {
            jumpTypeSelectOptions = Arrays.stream(JumpTypeEnum.values()).filter(typeEnum -> typeEnum != JumpTypeEnum.SkuDetail).map(typeEnum -> SelectOptionBuilder.builder().value(typeEnum.getValue()).label(typeEnum.getLabel()).build()).collect(Collectors.toList());
        } else {
            jumpTypeSelectOptions = Arrays.stream(JumpTypeEnum.values()).filter(typeEnum -> typeEnum != JumpTypeEnum.SpuDetail).map(typeEnum -> SelectOptionBuilder.builder().value(typeEnum.getValue()).label(typeEnum.getLabel()).build()).collect(Collectors.toList());
        }
        SelectOneFieldDescribe jumpTypeSelectOneFieldDescribe = SelectOneFieldDescribeBuilder.builder().apiName(AdvertisementConstants.Field.JumpType.apiName).label(AdvertisementConstants.Field.JumpType.label).selectOptions(jumpTypeSelectOptions).required(true).build();
        fieldDescribeList.add(jumpTypeSelectOneFieldDescribe);

        URLFieldDescribe jumpAddressFieldDescribe = UrlFieldDescribeBuilder.builder().apiName(AdvertisementConstants.Field.JumpAddress.apiName).label(AdvertisementConstants.Field.JumpAddress.label).build();
        fieldDescribeList.add(jumpAddressFieldDescribe);

        if (spuMode) {
            ObjectReferenceFieldDescribe spuObjectReferenceFieldDescribe = ObjectReferenceFieldDescribeBuilder.builder().apiName(AdvertisementConstants.Field.Spu.apiName).label(AdvertisementConstants.Field.Spu.label).targetApiName(Utils.SPU_API_NAME).targetRelatedListLabel(AdvertisementConstants.Field.Spu.targetRelatedListLabel).targetRelatedListName(AdvertisementConstants.Field.Spu.targetRelatedListName).unique(false).required(false).build();
            fieldDescribeList.add(spuObjectReferenceFieldDescribe);
        } else {
            ObjectReferenceFieldDescribe productObjectReferenceFieldDescribe = ObjectReferenceFieldDescribeBuilder.builder().apiName(AdvertisementConstants.Field.Product.apiName).label(AdvertisementConstants.Field.Product.label).targetApiName(Utils.PRODUCT_API_NAME).targetRelatedListLabel(AdvertisementConstants.Field.Product.targetRelatedListLabel).targetRelatedListName(AdvertisementConstants.Field.Product.targetRelatedListName).unique(false).required(false).build();
            fieldDescribeList.add(productObjectReferenceFieldDescribe);
        }

        ObjectReferenceFieldDescribe promotionObjectReferenceFieldDescribe = ObjectReferenceFieldDescribeBuilder.builder().apiName(AdvertisementConstants.Field.Promotion.apiName).label(AdvertisementConstants.Field.Promotion.label).targetApiName(PromotionConstants.API_NAME).targetRelatedListLabel(AdvertisementConstants.Field.Promotion.targetRelatedListLabel).targetRelatedListName(AdvertisementConstants.Field.Promotion.targetRelatedListName).unique(false).required(false).build();
        fieldDescribeList.add(promotionObjectReferenceFieldDescribe);

        List<ISelectOption> statusSelectOptions = Arrays.stream(AdvertisementStatusEnum.values()).map(typeEnum -> SelectOptionBuilder.builder().value(typeEnum.getValue()).label(typeEnum.getLabel()).build()).collect(Collectors.toList());
        SelectOneFieldDescribe statusSelectOneFieldDescribe = SelectOneFieldDescribeBuilder.builder().apiName(AdvertisementConstants.Field.Status.apiName).label(AdvertisementConstants.Field.Status.label).selectOptions(statusSelectOptions).required(true).build();
        fieldDescribeList.add(statusSelectOneFieldDescribe);

        NumberFieldDescribe sortNumberFieldDescribe = NumberFieldDescribeBuilder.builder().apiName(AdvertisementConstants.Field.Sort.apiName).label(AdvertisementConstants.Field.Sort.label).decimalPalces(0).length(12).maxLength(14).build();
        fieldDescribeList.add(sortNumberFieldDescribe);

        //6.7.1 适配部门
        DepartmentFieldDescribe departmentFieldDescribe = DepartmentFieldDescribeBuilder.builder()
                .apiName(AdvertisementConstants.Field.DeptRange.apiName)
                .label(AdvertisementConstants.Field.DeptRange.label)
                .required(true)
                .single(false)
                .defaultValue(AdvertisementConstants.DEPT_RANGE_DEFAULT_VALUE)
                .build();
        fieldDescribeList.add(departmentFieldDescribe);
        //适配客户
        UseScopeFieldDescribe useScopeFieldDescribe = UseScopeFieldDescribeBuilder.
                builder().apiName(AdvertisementConstants.Field.CustomerRange.apiName)
                .label(AdvertisementConstants.Field.CustomerRange.label)
                .targetApiName(Utils.ACCOUNT_API_NAME)
                .defaultValue(AdvertisementConstants.CUSTOMER_RANGE_DEFAULT_VALUE)
                .expressionType(AdvertisementConstants.expressionType).build();
        fieldDescribeList.add(useScopeFieldDescribe);

        return ObjectDescribeBuilder.builder().apiName(AdvertisementConstants.API_NAME).displayName(AdvertisementConstants.DISPLAY_NAME).tenantId(tenantId).createBy(fsUserId).fieldDescribes(fieldDescribeList).storeTableName(AdvertisementConstants.STORE_TABLE_NAME).iconIndex(AdvertisementConstants.ICON_INDEX).build();
    }

    private ILayout generateAdvertisementDefaultLayout(String tenantId, String fsUserId, boolean spuMode) {
        List<IFieldSection> fieldSections = Lists.newArrayList();
        List<IFormField> formFields = Lists.newArrayList();

        formFields.add(FormFieldBuilder.builder().fieldName(AdvertisementConstants.Field.Name.apiName).readOnly(false).required(true).renderType(SystemConstants.RenderType.AutoNumber.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(AdvertisementConstants.Field.AdPictures.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.Image.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(AdvertisementConstants.Field.JumpType.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.SelectOne.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(AdvertisementConstants.Field.JumpAddress.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.Url.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(AdvertisementConstants.Field.Promotion.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.ObjectReference.renderType).build());
        if (spuMode) {
            formFields.add(FormFieldBuilder.builder().fieldName(AdvertisementConstants.Field.Spu.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.ObjectReference.renderType).build());
        } else {
            formFields.add(FormFieldBuilder.builder().fieldName(AdvertisementConstants.Field.Product.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.ObjectReference.renderType).build());
        }
        formFields.add(FormFieldBuilder.builder().fieldName(AdvertisementConstants.Field.Status.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.SelectOne.renderType).build());
        formFields.add(FormFieldBuilder.builder().fieldName(AdvertisementConstants.Field.Sort.apiName).readOnly(false).required(false).renderType(SystemConstants.RenderType.Number.renderType).build());

        //6.7.1 适配客户和部门
        List<IFormField> customerRangeFormFields = Lists.newArrayList();
        customerRangeFormFields.add(FormFieldBuilder.builder().fieldName(AdvertisementConstants.Field.CustomerRange.apiName).readOnly(false).renderType(SystemConstants.RenderType.UseScope.renderType).required(true).build());
        FieldSection customerRangeFieldSection = FieldSectionBuilder.builder().header(AdvertisementConstants.Field.CustomerRange.label).name(AdvertisementConstants.CUSTOMER_RANGE_SECTION_API_NAME).showHeader(true).fields(customerRangeFormFields).build();

        formFields.add(FormFieldBuilder.builder().fieldName(AdvertisementConstants.Field.DeptRange.apiName).readOnly(false).renderType(SystemConstants.RenderType.Department.renderType).required(false).build());

        FieldSection fieldSection = FieldSectionBuilder.builder().name(LayoutConstants.BASE_FIELD_SECTION_API_NAME).header(LayoutConstants.BASE_FIELD_SECTION_DISPLAY_NAME).showHeader(true).fields(formFields).build();
        fieldSections.add(fieldSection);
        fieldSections.add(customerRangeFieldSection);

        FormComponent formComponent = FormComponentBuilder.builder().name(LayoutConstants.FORM_COMPONENT_API_NAME).buttons(null).fieldSections(fieldSections).build();
        List<IComponent> components = Lists.newArrayList(formComponent);
        return LayoutBuilder.builder().tenantId(tenantId).createBy(fsUserId).displayName(AdvertisementConstants.DEFAULT_LAYOUT_DISPLAY_NAME).name(AdvertisementConstants.DEFAULT_LAYOUT_API_NAME).isDefault(true).layoutType(SystemConstants.LayoutType.Detail.layoutType).refObjectApiName(AdvertisementConstants.API_NAME).components(components).build();
    }

    private ILayout generateAdvertisementListLayout(String tenantId, String fsUserId, boolean spuMode) {
        List<ITableColumn> tableColumns = Lists.newArrayList();
        tableColumns.add(TableColumnBuilder.builder().name(AdvertisementConstants.Field.Name.apiName).lableName(AdvertisementConstants.Field.Name.label).renderType(SystemConstants.RenderType.AutoNumber.renderType).build());
        tableColumns.add(TableColumnBuilder.builder().name(AdvertisementConstants.Field.JumpType.apiName).lableName(AdvertisementConstants.Field.JumpType.label).renderType(SystemConstants.RenderType.SelectOne.renderType).build());
        tableColumns.add(TableColumnBuilder.builder().name(AdvertisementConstants.Field.JumpAddress.apiName).lableName(AdvertisementConstants.Field.JumpAddress.label).renderType(SystemConstants.RenderType.Url.renderType).build());
        tableColumns.add(TableColumnBuilder.builder().name(AdvertisementConstants.Field.Promotion.apiName).lableName(AdvertisementConstants.Field.Promotion.label).renderType(SystemConstants.RenderType.ObjectReference.renderType).build());
        if (spuMode) {
            tableColumns.add(TableColumnBuilder.builder().name(AdvertisementConstants.Field.Spu.apiName).lableName(AdvertisementConstants.Field.Spu.label).renderType(SystemConstants.RenderType.ObjectReference.renderType).build());
        } else {
            tableColumns.add(TableColumnBuilder.builder().name(AdvertisementConstants.Field.Product.apiName).lableName(AdvertisementConstants.Field.Product.label).renderType(SystemConstants.RenderType.ObjectReference.renderType).build());
        }
        tableColumns.add(TableColumnBuilder.builder().name(AdvertisementConstants.Field.Status.apiName).lableName(AdvertisementConstants.Field.Status.label).renderType(SystemConstants.RenderType.SelectOne.renderType).build());
        tableColumns.add(TableColumnBuilder.builder().name(AdvertisementConstants.Field.Sort.apiName).lableName(AdvertisementConstants.Field.Sort.label).renderType(SystemConstants.RenderType.Number.renderType).build());

        TableComponent tableComponent = TableComponentBuilder.builder().refObjectApiName(AdvertisementConstants.API_NAME).includeFields(tableColumns).buttons(null).build();
        List<IComponent> components = Lists.newArrayList(tableComponent);

        return LayoutBuilder.builder().tenantId(tenantId).createBy(fsUserId).refObjectApiName(AdvertisementConstants.API_NAME).layoutType(SystemConstants.LayoutType.List.layoutType).isDefault(false).name(AdvertisementConstants.LIST_LAYOUT_API_NAME).displayName(AdvertisementConstants.LIST_LAYOUT_DISPLAY_NAME).isShowFieldName(true).agentType(LayoutConstants.AGENT_TYPE).components(components).build();
    }

    private Map<String, Object> getDisableRecordTypeLayoutConfig() {
        Map<String, Object> config = Maps.newHashMap();
        Map<String, Object> layoutConfig = Maps.newHashMap();
        Map<String, Object> recordConfig = Maps.newHashMap();
        layoutConfig.put("add", 0);
        recordConfig.put("add", 0);
        config.put("layout", layoutConfig);
        config.put("record_type", recordConfig);
        return config;
    }

    private boolean isSpuMode(String tenantId) {
        GetConfigValueByKeyModel.Arg arg = GetConfigValueByKeyModel.Arg.builder().key("spu").build();
        GetConfigValueByKeyModel.Result result = settingRestApi.getConfigValueByKey(arg, HeaderUtil.getCrmHeader(tenantId, "-10000"));
        if (result.getResult() != null && "1".equals(result.getResult().getValue())) {
            return true;
        }
        return false;
    }

}
