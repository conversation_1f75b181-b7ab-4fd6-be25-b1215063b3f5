package com.facishare.crm.promotion.predefine.service;

import com.facishare.crm.promotion.predefine.service.dto.*;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;

import java.util.List;

@ServiceModule("promotion")
public interface PromotionService {

    @ServiceMethod("enable_promotion")
    PromotionType.EnableResult enablePromotion(ServiceContext serviceContext);

    @ServiceMethod("is_promotion_enable")
    PromotionType.IsEnableResult isPromotionEnable(ServiceContext serviceContext);

    @ServiceMethod("in_promotion_white_list")
    PromotionType.IsEnableResult inPromotionWhiteList(ServiceContext serviceContext);

    @ServiceMethod("get_by_id")
    PromotionType.DetailResult getById(ServiceContext serviceContext, PromotionType.IdModel idModel);

    @ServiceMethod("get_by_ids")
    List<PromotionType.DetailResult> getByIds(ServiceContext serviceContext, PromotionType.IdsModel idsModel);

    @ServiceMethod("get_by_promotion_and_product_ids")
    List<PromotionType.DetailResult> getByPromotionAndProductIds(ServiceContext serviceContext, PromotionType.PromotionAndProductIdsModel promotionAdnProductIdsModel);

    /**
     * 根据客户id查询订单促销列表
     *
     * @param serviceContext
     * @param customerIdArg
     * @return
     */
    @ServiceMethod("list_by_customer_id")
    PromotionType.PromotionRuleResult listByCustomerId(ServiceContext serviceContext, PromotionType.CustomerIdArg customerIdArg);

    /**
     * 促销产品列表接口
     * @param serviceContext
     * @param productsArg
     * @return
     */
    @ServiceMethod("list_promotion_products_by_customer_id")
    PromotionType.PromotionProductResult listPromotionProductsByCustomerId(ServiceContext serviceContext, PromotionType.ListProductsArg productsArg);

    /**
     * 根据客户id和产品id列表，查询产品促销列表
     *
     * @param serviceContext
     * @param productPromotionListArg
     * @return
     */
    @ServiceMethod("list_by_product_ids")
    PromotionType.ProductPromotionResult listByProductIds(ServiceContext serviceContext, PromotionType.ProductPromotionListArg productPromotionListArg);

    @ServiceMethod("batch_get_product_quota")
    BatchGetProductQuotaByProductIdsModel.Result batchGetProductQuotaByProductIds(ServiceContext serviceContext, BatchGetProductQuotaByProductIdsModel.Arg arg);

    /**
     * 查询促销列表, 该接口不支持分页
     *
     * @param serviceContext
     * @param customerIdArg
     * @return
     */
    @ServiceMethod("list_promotions")
    PromotionType.PromotionListResult listPromotions(ServiceContext serviceContext, PromotionType.CustomerIdArg customerIdArg);

    /**
     * 组合促销查询列表
     * @param serviceContext
     * @param arg
     * @return
     */
    @ServiceMethod("list_combine_promotions")
    PromotionType.PromotionListResult listCombinePromotions(ServiceContext serviceContext, PromotionType.CustomerIdArg arg);

    @ServiceMethod("batchQueryPromotionBySpuIds")
    PromotionType.SpuToPromotionFlagResult batchQueryPromotionBySpuIds(ServiceContext serviceContext, PromotionType.SpuIdsArg spuIdsArg) throws Exception;

    @ServiceMethod("listPromotionProductsGroupBySpuId")
    QueryProductInPromotionModel.Result listPromotionProductsGroupBySpuId(ServiceContext serviceContext, QueryProductInPromotionModel.Arg arg) throws Exception;

    /**
     * 促销编辑时，前端判断当前促销是否被使用
     *
     * @param serviceContext
     * @param idModel
     * @return
     */
    @ServiceMethod("is_promotion_used")
    PromotionType.PromotionUsed isPromotionUsed(ServiceContext serviceContext, PromotionType.IdModel idModel);

    /**
     * 订单提交时促销校验: 暂只支持订单促销和商品促销
     * @param serviceContext
     * @param arg
     * @return
     */
    @ServiceMethod("validate_promotions_for_sales_order")
    PromotionType.ValidatePromotionsResult validatePromotionsForSalesOrder(ServiceContext serviceContext, PromotionType.ValidatePromotionsArg arg);

    @ServiceMethod("list_promotion_product")
    ListPromotionProductModel.Result listPromotionProduct(ServiceContext serviceContext, ListPromotionProductModel.Arg arg);


    @ServiceMethod("add_field_for_open_multi_org")
    AddFieldForOpenMultiOrgModel.Result addFieldForOpenMultiOrg(ServiceContext serviceContext, AddFieldForOpenMultiOrgModel.Arg arg);

    @ServiceMethod("add_field_promotion_discount_for_order_product")
    AddFieldPromotionDiscountForOrderProductModel.Result addFieldPromotionDiscountForOrderProduct(ServiceContext serviceContext, AddFieldPromotionDiscountForOrderProductModel.Arg arg);

    /**
     * 测试用的
     */
    @ServiceMethod("update_promotion_discount_for_order_product_temp")
    UpdatePromotionDiscountForOrderProductModel.Result updatePromotionDiscountForOrderProductTemp(ServiceContext serviceContext, UpdatePromotionDiscountForOrderProductModel.Arg arg);

    /**
     * promotion_discount__c is_extend 改为true
     */
    @ServiceMethod("update_promotion_discount_is_extend")
    UpdatePromotionDiscountForOrderProductModel.Result updatePromotionDiscountIsExtend(ServiceContext serviceContext, UpdatePromotionDiscountForOrderProductModel.Arg arg);
}
