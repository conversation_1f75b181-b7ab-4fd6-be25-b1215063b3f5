package com.facishare.crm.promotion.predefine.service.impl;

import com.facishare.crm.promotion.predefine.service.PromotionProductService;
import com.facishare.crm.promotion.predefine.service.dto.PromotionProductModel;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @author: dongzhb
 * @date: 2019/12/18
 * @Description:
 */
@Slf4j
@Service
public class PromotionProductServiceImpl implements PromotionProductService {


    @Autowired
    private ObjectDataServiceImpl objectDataService;

    /**
     * 查询促销产品
     *
     * @param serviceContext
     * @param arg
     **/
    @Override
    public PromotionProductModel.PromotionProductList listPromotionProduct(ServiceContext serviceContext, PromotionProductModel.Arg arg) {
        log.info("query promotion product ,arg:{}", arg);
        String sbSql =
                "SELECT DISTINCT bi.ID  FROM  ( SELECT DISTINCT bi.ID FROM biz_product bi, promotion_product AS pr, promotion AS pro " +
                        " WHERE  bi.ID = pr.product_id " +
                        " AND pr.promotion_id = pro.ID  AND bi.tenant_id = '" + serviceContext.getTenantId() + "' AND bi.life_status = 'normal' " +
                        " AND bi.product_status = '1' AND pro.life_status = 'normal'  AND pro.status = TRUE  AND pro.is_deleted = '0' " +
                        " AND pro.record_type='" + arg.getRecordType() + "' AND pro.start_time < '" + System.currentTimeMillis() + "'   AND   pro.end_time >= '" + System.currentTimeMillis() + "') AS bi " +
                        " LEFT JOIN price_book_product AS pbp ON pbp.product_id = bi.id " +
                        " LEFT JOIN price_book AS pb ON pbp.pricebook_id = pb.ID " +
                        " LEFT JOIN stock AS st ON st.product_id = bi.ID " +
                        " LEFT JOIN warehouse AS wh ON st.warehouse_id = wh.ID " +
                        " WHERE 1=1 ";

        String sbCount =
                "SELECT count(DISTINCT(bi.id))  FROM  ( SELECT DISTINCT bi.ID FROM biz_product bi, promotion_product AS pr, promotion AS pro " +
                        " WHERE  bi.ID = pr.product_id " +
                        " AND pr.promotion_id = pro.ID  AND bi.tenant_id = '" + serviceContext.getTenantId() + "' AND bi.life_status = 'normal' " +
                        " AND bi.product_status = '1' AND pro.life_status = 'normal'  AND pro.status = TRUE  AND pro.is_deleted = '0' " +
                        " AND pro.record_type='" + arg.getRecordType() + "' AND pro.start_time < '" + System.currentTimeMillis() + "'   AND   pro.end_time >= '" + System.currentTimeMillis() + "') AS bi " +
                        " LEFT JOIN price_book_product AS pbp ON pbp.product_id = bi.id " +
                        " LEFT JOIN price_book AS pb ON pbp.pricebook_id = pb.ID " +
                        " LEFT JOIN stock AS st ON st.product_id = bi.ID " +
                        " LEFT JOIN warehouse AS wh ON st.warehouse_id = wh.ID " +
                        " WHERE 1=1 ";

        if (arg.getPriceBookId() != null) {
            sbSql += "  AND pb.ID = '" + arg.getPriceBookId() + "' ";
            sbCount += " AND pb.ID = '" + arg.getPriceBookId() + "' ";
        }

        if (arg.getWarehouseId() != null) {
            sbSql += " AND wh.ID = '" + arg.getWarehouseId() + "' ";
            sbCount += " AND wh.ID = '" + arg.getWarehouseId() + "' ";
        }

        sbSql += "  LIMIT " + arg.getLimit() + " OFFSET " + arg.getOffset() + " ";

        PromotionProductModel.PromotionProductList promotionProductList = new PromotionProductModel.PromotionProductList();

        List<Map> dataMap = null;
        List<Map> countMap = null;
        log.info("query sql:{}", sbSql);
        try {
            dataMap = objectDataService.findBySql(serviceContext.getTenantId(), sbSql.toString());
            countMap = objectDataService.findBySql(serviceContext.getTenantId(), sbCount.toString());
            promotionProductList.setDataList(dataMap);
            promotionProductList.setTotal((long) countMap.get(0).getOrDefault("count", 0));
        } catch (MetadataServiceException e) {
            e.printStackTrace();
        }

        return promotionProductList;
    }
}
