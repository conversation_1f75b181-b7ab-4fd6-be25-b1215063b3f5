package com.facishare.crm.promotion.predefine.service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.facishare.crm.promotion.constants.AdvertisementConstants;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.pod.client.PodApiClient;
import com.fxiaoke.transfer.dto.OpType;
import com.fxiaoke.transfer.dto.Record;
import com.fxiaoke.transfer.dto.RequestData;
import com.fxiaoke.transfer.dto.ResponseData;
import com.fxiaoke.transfer.dto.SourceData;
import com.fxiaoke.transfer.dto.SourceItem;
import com.fxiaoke.transfer.dto.TableSchema;
import com.fxiaoke.transfer.dto.columns.StringColumn;
import com.fxiaoke.transfer.service.BaseTransformerService;
import com.fxiaoke.transfer.service.TableSchemeService;
import com.fxiaoke.transfer.utils.ConverterUtil;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class PromotionDataTransferService extends BaseTransformerService {
    @Autowired
    protected TableSchemeService tableSchemeService;
    @Autowired
    private PodApiClient podApiClient;
    @Autowired
    private ObjectDataServiceImpl objectDataService;

    public RequestData buildRequestData(String tenantId, String tableName, List<Map> dataList) throws MetadataServiceException {
        RequestData requestData = new RequestData();
        List<SourceData> sourceDataList = Lists.newArrayList();
        Set<String> columnNames = tableSchemeService.getTableSchema(tableName, "promotion").getColumnNameSet();
        log.info("buildRequestData->columnNames:{}", columnNames);
        List<String> skuIds = dataList.stream().map(data -> (String) data.get(AdvertisementConstants.Field.Product.apiName)).filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, String> skuToSpuMap = skuToSpuMap(tenantId, skuIds);
        for (Map dataMap : dataList) {
            SourceData sourceData = new SourceData();
            sourceData.setTenantId(tenantId);
            sourceData.setTable(tableName);
            Map<String, Object> data = Maps.newHashMap();
            boolean isSkuIdExist = false;
            for (String columnName : columnNames) {
                if (AdvertisementConstants.Field.Spu.apiName.equals(columnName)) {
                    continue;
                }
                if (AdvertisementConstants.Field.Product.apiName.equals(columnName)) {
                    String skuId = (String) dataMap.get(columnName);
                    if (StringUtils.isNotEmpty(skuId)) {
                        isSkuIdExist = true;
                        data.put("spu_id", skuToSpuMap.get(skuId));
                    }
                }
                data.put(columnName, dataMap.get(columnName));
            }
            if (!isSkuIdExist) {
                continue;
            }
            data.put("id", dataMap.get("id"));
            sourceData.setData(data);
            sourceDataList.add(sourceData);
        }
        requestData.setSourceDataList(sourceDataList);
        return requestData;
    }

    private Map<String, String> skuToSpuMap(String tenantId, List<String> skuIds) throws MetadataServiceException {
        Map<String, String> map = Maps.newHashMap();
        String selectSkuSql = String.format("select id,spu_id from biz_product where tenant_id='%s' and id in(%s)", tenantId, Joiner.on(",").join(skuIds.stream().map(skuId -> "'" + skuId + "'").collect(Collectors.toList())));
        List<Map> skuDataList = objectDataService.findBySql(tenantId, selectSkuSql);
        if (CollectionUtils.isNotEmpty(skuDataList)) {
            skuDataList.forEach(skuData -> {
                String skuId = (String) skuData.get("id");
                String spuId = (String) skuData.get("spu_id");
                map.put(skuId, spuId);
            });
        }
        return map;
    }

    @Override
    protected void transfer(RequestData requestData) {
        ResponseData responseData = new ResponseData();
        List<SourceData> sourceDataList = requestData.getSourceDataList();
        if (CollectionUtils.isEmpty(sourceDataList)) {
            log.warn("Source data to be transferred is empty.");
            return;
        }
        List<SourceItem> sourceItemList = Lists.newArrayList();
        for (SourceData sourceData : sourceDataList) {
            SourceItem sourceItem = SourceItem.builder().dbUrl(podApiClient.getResource(sourceData.getTenantId(), PKG, MODULE, "pg")).table(sourceData.getTable()).tenantId(sourceData.getTenantId()).build();
            TableSchema tableSchema = tableSchemeService.getTableSchema(sourceData.getTable(), "promotion");
            sourceItem.setRecordList(parseRecord(sourceData, tableSchema));
            sourceItemList.add(sourceItem);
        }
        responseData.setSourceItemList(sourceItemList);
        responseData.setOperationJob(requestData.getOperationJob());
        log.info("promotion responseData:{}", responseData);
        //发送数据给刷库中心
        try {
            sendData(responseData);
        } catch (Exception e) {
            log.error("Send data error: ", e);
        }
    }

    @Override
    protected List<Record> parseRecord(SourceData sourceData, TableSchema tableSchema) {
        List<Record> recordList = Lists.newArrayList();
        String id = ConverterUtil.convert2String(sourceData.getData().get("id").toString());
        Record upsertRecord = new Record();
        upsertRecord.addUpsertColumnName(AdvertisementConstants.Field.Spu.apiName);
        upsertRecord.setOpType(OpType.UPSERT);
        upsertRecord.setTable(AdvertisementConstants.STORE_TABLE_NAME);
        upsertRecord.addIdColumn(new StringColumn("id", id));
        upsertRecord.addIdColumn(new StringColumn("tenant_id", sourceData.getTenantId()));
        String spuId = ConverterUtil.convert2String(sourceData.getData().get(AdvertisementConstants.Field.Spu.apiName));
        upsertRecord.addStringColumn(AdvertisementConstants.Field.Spu.apiName, spuId);
        recordList.add(upsertRecord);
        return recordList;
    }
}
