package com.facishare.crm.promotion.predefine.controller;

import com.facishare.crm.promotion.util.PromotionUtil;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.google.common.collect.Lists;

import java.util.List;

public class PromotionProductWebDetailController extends StandardWebDetailController {

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        //屏蔽促销产品详情页的新建，编辑按钮
        PromotionUtil.webDetailRemoveButtons(result,Lists.newArrayList(ObjectAction.CREATE.getActionCode(), ObjectAction.UPDATE.getActionCode()));
        //查询可用促销
        List<ObjectDataDocument> objectDataDocumentList = PromotionUtil.caculateLeftQuota(controllerContext.getUser(), Lists.newArrayList(result.getData()), serviceFacade);
        result.setData(objectDataDocumentList.get(0));
        return result;
    }
}
