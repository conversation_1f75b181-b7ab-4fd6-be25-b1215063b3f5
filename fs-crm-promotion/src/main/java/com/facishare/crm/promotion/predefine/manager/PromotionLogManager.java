package com.facishare.crm.promotion.predefine.manager;

import com.facishare.crm.promotion.constants.PromotionGiftConstants;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.AsyncLogSender;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.dto.LogInfo;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fxiaoke.api.IdGenerator;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/1/9
 */
@Slf4j
@Component
public class PromotionLogManager {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private AsyncLogSender asyncLogSender;

    public String masterRelatedInfoModifyLog(User user, IObjectData masterData, Map<String, IObjectDescribe> objectDescribeMap) {
        IObjectDescribe promotionGiftDescribe = objectDescribeMap.get(PromotionGiftConstants.API_NAME);
        List<LogInfo.DetailInfo> detailInfos = Lists.newArrayList(LogInfo.DetailInfo.builder().objectApiName(promotionGiftDescribe.getApiName()).objectLabel(promotionGiftDescribe.getDisplayName()).build());
        String masterLogId = CollectionUtils.empty(detailInfos) ? null : IdGenerator.get();
        IObjectDescribe masterDescribe = objectDescribeMap.get(masterData.getDescribeApiName());
        Map<String, IObjectData> dbMasterDataMap = Maps.newHashMap();
        dbMasterDataMap.put(masterData.getId(), masterData);
        LogInfo.ObjectSnapshot snapshot = LogInfo.createSnapshot(masterDescribe, masterData, Maps.newHashMap(), dbMasterDataMap);
        snapshot.setDetailInfo(detailInfos);
        LogInfo info = this.getLogInfo(user, EventType.MODIFY, ActionType.Modify, masterDescribe.getApiName(), masterDescribe.getApiName(), snapshot, snapshot.getMessage(), masterData.getId());
        info.setMasterLogId(masterLogId);
        asyncLogSender.offer(info);
        return masterLogId;
    }

    public void relatedInfoModifyLog(User user, IObjectDescribe objectDescribe, List<IObjectData> dataList, Map<String, Map<String, Object>> updatedFieldMap, List<IObjectData> dbDetailDataList, String masterDataId, String masterLogId) {
        if (!CollectionUtils.empty(dataList) && !CollectionUtils.empty(dbDetailDataList) && !CollectionUtils.empty(updatedFieldMap)) {
            log.info("relatedInfoModifyLog updateFieldMap:{}", updatedFieldMap);
            List<IObjectData> modifyDataList = ObjectDataExt.copyList(dataList);
            modifyDataList.forEach((x) -> {
                Map<String, Object> updateField = updatedFieldMap.get(x.getId());
                if (!CollectionUtils.empty(updateField)) {
                    Map<String, IObjectData> dbDataMap = CollectionUtils.nullToEmpty(dbDetailDataList).stream().collect(Collectors.toMap(DBRecord::getId, y -> y));
                    LogInfo.ObjectSnapshot snapshot = LogInfo.createSnapshot(objectDescribe, x, updateField, dbDataMap);
                    LogInfo info = this.getLogInfo(user, EventType.MODIFY, ActionType.Modify, objectDescribe.getApiName(), objectDescribe.getApiName(), snapshot, snapshot.getMessage(), x.getId());
                    info.setMasterLogId(masterLogId);
                    if (!Strings.isNullOrEmpty(masterDataId)) {
                        info.setMasterId(masterDataId);
                    }
                    this.asyncLogSender.offer(info);
                }
            });
        }
    }

    public void promotionGiftAddAuditLog(User user, IObjectDescribe objectDescribe, List<IObjectData> dataList, String masterDataId, String masterLogId) {
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        describeMap.put(objectDescribe.getApiName(), objectDescribe);
        this.serviceFacade.log(user, EventType.ADD, ActionType.Add, describeMap, dataList, masterLogId);
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        dataList.forEach(data -> logRelated(user, EventType.ADD, ActionType.Add, objectDescribe, data, masterDataId, masterLogId));
    }

    public void promotionGiftDeleteAuditLog(User user, IObjectDescribe objectDescribe, List<IObjectData> dataList, String masterDataId, String masterLogId) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        dataList.forEach(data -> logRelated(user, EventType.DELETE, ActionType.Delete, objectDescribe, data, masterDataId, masterLogId));
    }

    private void logRelated(User user, EventType eventType, ActionType actionType, IObjectDescribe describe, IObjectData data, String masterId, String masterLogId) {
        LogInfo.ObjectSnapshot snapshot = LogInfo.createSnapshot(describe, data);
        LogInfo info = this.getLogInfo(user, eventType, actionType, describe.getApiName(), describe.getApiName(), snapshot, snapshot.getMessage(), data.getId());
        if (StringUtils.isNotEmpty(masterId)) {
            info.setMasterId(masterId);
        }
        if (StringUtils.isNotEmpty(masterLogId)) {
            info.setMasterLogId(masterLogId);
        }
        this.asyncLogSender.offer(info);
    }

    private LogInfo getLogInfo(User user, EventType eventType, ActionType actionType, String apiName, String module, LogInfo.ObjectSnapshot snapshot, String textMessage, String objectId) {
        User newUser = user;
        if (user.isOutUser() || Strings.isNullOrEmpty(user.getUserName())) {
            newUser = serviceFacade.getUser(user.getTenantId(), user.getUserIdOrOutUserIdIfOutUser());
        }
        return LogInfo.builder().operationTime(System.currentTimeMillis()).operation(eventType.getId()).bizOperationName(actionType.getId()).corpId(newUser.getTenantId()).outTenantId(user.getOutTenantId()).userId(newUser.getUserId()).userName(newUser.getUserName()).appId("CRM").module(module).objectName(apiName).textMessage(textMessage).snapshot(snapshot).objectId(Objects.isNull(objectId) ? "" : objectId).peerName("").build();
    }
}
