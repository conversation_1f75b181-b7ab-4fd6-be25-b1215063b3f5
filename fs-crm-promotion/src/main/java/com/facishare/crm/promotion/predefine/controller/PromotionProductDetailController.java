package com.facishare.crm.promotion.predefine.controller;

import com.facishare.crm.promotion.predefine.manager.SalesOrderManager;
import com.facishare.crm.promotion.util.PromotionUtil;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardDetailController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;

@Slf4j
public class PromotionProductDetailController extends StandardDetailController {
    private SalesOrderManager salesOrderManager;

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        salesOrderManager = SpringUtil.getContext().getBean(SalesOrderManager.class);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        Boolean includeLayout = arg.getIncludeLayout();
        if (Objects.nonNull(includeLayout) && includeLayout) {
            LayoutDocument layoutDocument = result.getLayout();
            List<IButton> buttonList = LayoutExt.of(new Layout(layoutDocument)).getButtons();
            buttonList.removeIf(button -> button.getAction().equals(ObjectAction.UPDATE.getActionCode()) || button.getAction().equals(ObjectAction.CLONE.getActionCode()));
            LayoutExt.of(new Layout(layoutDocument)).setButtons(buttonList);
        }
        //查询可用促销
        List<ObjectDataDocument> objectDataDocumentList = PromotionUtil.caculateLeftQuota(controllerContext.getUser(), Lists.newArrayList(result.getData()), serviceFacade);
        result.setData(objectDataDocumentList.get(0));
        return result;
    }
}
