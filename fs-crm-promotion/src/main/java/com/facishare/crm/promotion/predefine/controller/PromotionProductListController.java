package com.facishare.crm.promotion.predefine.controller;

import com.facishare.crm.promotion.util.PromotionUtil;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;

import java.util.List;

public class PromotionProductListController extends StandardListController {

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        //计算可用促销数量
        List<ObjectDataDocument> objectDataDocumentList = result.getDataList();
        result.setDataList(PromotionUtil.caculateLeftQuota(controllerContext.getUser(), objectDataDocumentList, serviceFacade));
        return result;
    }
}
