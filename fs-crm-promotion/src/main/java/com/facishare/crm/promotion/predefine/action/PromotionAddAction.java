package com.facishare.crm.promotion.predefine.action;

import com.facishare.crm.promotion.constants.ProI18NKey;
import com.facishare.crm.promotion.constants.PromotionConstants;
import com.facishare.crm.promotion.constants.PromotionGiftConstants;
import com.facishare.crm.promotion.constants.PromotionProductConstants;
import com.facishare.crm.promotion.constants.PromotionRuleConstants;
import com.facishare.crm.promotion.enums.PromotionRecordTypeEnum;
import com.facishare.crm.promotion.enums.PromotionRuleTypeEnum;
import com.facishare.crm.promotion.enums.RuleMethodEnum;
import com.facishare.crm.promotion.predefine.manager.PromotionLogManager;
import com.facishare.crm.promotion.predefine.manager.PromotionManager;
import com.facishare.crm.promotion.predefine.service.PromotionCheckRuleService;
import com.facishare.crm.promotion.util.ConfigCenter;
import com.facishare.crm.promotion.util.PromotionValidateUtil;
import com.facishare.crm.promotion.util.ValidateUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.appframework.metadata.dto.SaveMasterAndDetailData;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

/**
 * 促销 create Action
 */
@Slf4j
public class PromotionAddAction extends StandardAddAction {
    private PromotionManager promotionManager;
    private List<IObjectData> promotionGiftDataList;
    private IObjectDescribe promotionGiftDescribe;

    @Override
    protected void validateDetail(String detailApiName, List<IObjectData> detailDataList) {
        String ruleType = objectData.get(PromotionConstants.Field.RuleType.apiName, String.class);
        if (PromotionRecordTypeEnum.ProductPromotion.getApiName().equals(objectData.getRecordType())) {
            String ruleMethod = objectData.get(PromotionConstants.Field.RuleMethod.apiName, String.class);
            if (Objects.nonNull(ruleMethod) && RuleMethodEnum.SingleSetting.getValue().equals(ruleMethod)) {
                /**单个产品去掉默认校验规则*/
                return;
            }
        }

        if (StringUtils.isEmpty(ruleType) || PromotionRuleTypeEnum.Standard.getValue().equals(ruleType)) {
            super.validateDetail(detailApiName, detailDataList);
        }
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        promotionManager = serviceFacade.getBean(PromotionManager.class);
        String recordType = objectData.getRecordType();
        if (StringUtils.isEmpty(recordType)) {
            throw new ValidateException(I18N.text(ProI18NKey.PROMOTION_RECORD_TYPE_NOT_EXIST));
        }
        ValidateUtil.resetAndValidateField(actionContext.getUser(), this.objectData, detailObjectData, objectDescribes, serviceFacade);
        promotionGiftDataList = ValidateUtil.validateGetPromotionGift(this.objectData, detailObjectData, serviceFacade);
        if (CollectionUtils.notEmpty(promotionGiftDataList)) {
            this.promotionGiftDescribe = serviceFacade.findObject(actionContext.getTenantId(), PromotionGiftConstants.API_NAME);
            this.objectDescribes.put(PromotionGiftConstants.API_NAME, promotionGiftDescribe);
        }
    }

    @Override
    protected void doSaveData() {
        SaveMasterAndDetailData.Result saveResult = promotionManager.savePromotion(actionContext.getUser(), this.objectData, this.detailObjectData, this.promotionGiftDataList, this.objectDescribes);
        this.objectData = saveResult.getMasterObjectData();
        this.detailObjectData = saveResult.getDetailObjectData();
    }

    @Override
    protected void recordLog() {
        if (CollectionUtils.notEmpty(promotionGiftDataList)) {
            try {
                User user = actionContext.getUser();
                PromotionLogManager promotionLogManager = serviceFacade.getBean(PromotionLogManager.class);
                ParallelUtils.ParallelTask parallelTask = ParallelUtils.createBackgroundTask();
                parallelTask.submit(() -> {
                    Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
                    describeMap.put(PromotionConstants.API_NAME, this.objectDescribe);
                    describeMap.put(PromotionGiftConstants.API_NAME, promotionGiftDescribe);
                    String logId = promotionLogManager.masterRelatedInfoModifyLog(user, this.objectData, describeMap);
                    promotionLogManager.promotionGiftAddAuditLog(user, promotionGiftDescribe, promotionGiftDataList, this.objectData.getId(), logId);
                });
                parallelTask.run();
            } catch (Exception e) {
                log.warn("logPromotionGift error while add", e);
            }
        }
        super.recordLog();
    }
}
