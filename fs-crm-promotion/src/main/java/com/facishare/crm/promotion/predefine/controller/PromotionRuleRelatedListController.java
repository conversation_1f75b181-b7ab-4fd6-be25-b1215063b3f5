package com.facishare.crm.promotion.predefine.controller;

import com.facishare.crm.promotion.predefine.manager.PromotionManager;
import com.facishare.crm.promotion.util.PromotionUtil;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;
import java.util.Map;

public class PromotionRuleRelatedListController extends StandardRelatedListController {
    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        List<ObjectDataDocument> objectDataList = result.getDataList();
        PromotionManager promotionManager = serviceFacade.getBean(PromotionManager.class);
        Map<String, List<IObjectData>> promotionRuleIdGiftMap = promotionManager.getPromotionGift(controllerContext.getUser(), objectDataList);
        PromotionUtil.promotionRule(controllerContext.getUser(), this.objectDescribe, objectDataList, promotionRuleIdGiftMap, serviceFacade);
        return result;
    }
}
