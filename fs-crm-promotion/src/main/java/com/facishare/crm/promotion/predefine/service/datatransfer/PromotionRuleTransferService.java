package com.facishare.crm.promotion.predefine.service.datatransfer;

import com.facishare.crmcommon.describebuilder.NumberFieldDescribeBuilder;
import com.facishare.crmcommon.describebuilder.SelectOneFieldDescribeBuilder;
import com.facishare.crmcommon.describebuilder.SelectOptionBuilder;
import com.facishare.crm.promotion.constants.PromotionGiftConstants;
import com.facishare.crm.promotion.constants.PromotionRuleConstants;
import com.facishare.crm.promotion.enums.GiftMethodEnum;
import com.facishare.crm.promotion.enums.GiftTypeEnum;
import com.facishare.crm.promotion.predefine.service.CommonTransFormerService;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ObjectDefNotFoundError;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FieldLayoutPojo;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.NumberFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.fxiaoke.transfer.dto.OpType;
import com.fxiaoke.transfer.dto.Record;
import com.fxiaoke.transfer.dto.RequestData;
import com.fxiaoke.transfer.dto.SourceData;
import com.fxiaoke.transfer.dto.TableSchema;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/12/26
 */
@Slf4j
@ServiceModule("promotion_rule_transfer")
@Component
public class PromotionRuleTransferService extends CommonTransFormerService {
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private IObjectDescribeService objectDescribeService;
    @Autowired
    private ObjectDataServiceImpl objectDataService;

    @ServiceMethod("multi_gift")
    public Map<String, Object> promotionMultiGift(ServiceContext serviceContext, List<String> tenantIds) {
        Map<String, Object> result = Maps.newHashMap();
        if (CollectionUtils.empty(tenantIds)) {
            return result;
        }
        Set<String> errorTenantIds = Sets.newHashSet();
        Set<String> transferDataErrorEis = Sets.newHashSet();
        for (String tenantId : tenantIds) {
            User user = User.builder().tenantId(tenantId).userId(User.SUPPER_ADMIN_USER_ID).build();
            try {
                //新增字段
                IObjectDescribe promotionRuleDescribe = serviceFacade.findObject(tenantId, PromotionRuleConstants.API_NAME);
                ObjectDescribeExt objectDescribeExt = ObjectDescribeExt.of(promotionRuleDescribe);
                List<IFieldDescribe> toAddFieldDescribe = Lists.newArrayList();
                if (!objectDescribeExt.getFieldDescribeSilently(PromotionRuleConstants.Field.GiftMethod.apiName).isPresent()) {
                    List<ISelectOption> giftMethodSelectOptions = Arrays.stream(GiftMethodEnum.values()).map(x -> SelectOptionBuilder.builder().value(x.getValue()).label(x.getLabel()).build()).collect(Collectors.toList());
                    SelectOneFieldDescribe giftMethodSelectOneFieldDescribe = SelectOneFieldDescribeBuilder.builder().apiName(PromotionRuleConstants.Field.GiftMethod.apiName).label(PromotionRuleConstants.Field.GiftMethod.getLabel()).selectOptions(giftMethodSelectOptions).required(false).build();
                    toAddFieldDescribe.add(giftMethodSelectOneFieldDescribe);
                }
                if (!objectDescribeExt.getFieldDescribeSilently(PromotionRuleConstants.Field.OptionGiftNum.apiName).isPresent()) {
                    NumberFieldDescribe optionGiftNumNumberFieldDescribe = NumberFieldDescribeBuilder.builder().apiName(PromotionRuleConstants.Field.OptionGiftNum.apiName).label(PromotionRuleConstants.Field.OptionGiftNum.getLabel()).decimalPalces(0).length(12).maxLength(14).build();
                    toAddFieldDescribe.add(optionGiftNumNumberFieldDescribe);
                }

                if (CollectionUtils.notEmpty(toAddFieldDescribe)) {
                    ILayout layout = serviceFacade.findLayoutByApiName(user, PromotionRuleConstants.DEFAULT_LAYOUT_API_NAME, promotionRuleDescribe.getApiName());
                    LayoutExt layoutExt = LayoutExt.of(layout);
                    for (IFieldDescribe fieldDescribe : toAddFieldDescribe) {
                        FieldLayoutPojo fieldLayoutPojo = new FieldLayoutPojo();
                        fieldLayoutPojo.setReadonly(false);
                        fieldLayoutPojo.setRequired(false);
                        fieldLayoutPojo.setRenderType(fieldDescribe.getType());
                        layoutExt.addField(fieldDescribe, fieldLayoutPojo);
                    }
                    promotionRuleDescribe = objectDescribeService.addCustomFieldDescribe(promotionRuleDescribe, toAddFieldDescribe);
                    log.info("add tenantId:{},field describe success", tenantId);
                    serviceFacade.updateLayout(user, layout);
                    log.info("updated tenantId:{} promotionRuleLayout success", tenantId);
                }
                //刷数据促销规则数据 销赠品数据
                boolean transferDataSuccess = transferGiftMethodAndOptionGiftNum(tenantId);
                if (!transferDataSuccess) {
                    transferDataErrorEis.add(tenantId);
                }
                //禁用字段
                List<IFieldDescribe> toDeletedFieldList = ObjectDescribeExt.of(promotionRuleDescribe).filter(x ->
                        Sets.newHashSet(PromotionRuleConstants.Field.GiftProduct.apiName, PromotionRuleConstants.Field.GiftProductNum.apiName, PromotionRuleConstants.Field.GiftType.apiName).contains(x.getApiName())
                );
                objectDescribeService.deleteCustomFieldDescribe(promotionRuleDescribe, toDeletedFieldList);
            } catch (ObjectDefNotFoundError e) {
                log.warn("promotion un enable tenantId:{}", tenantId, e);
            } catch (Exception e) {
                errorTenantIds.add(tenantId);
                log.warn("promotionMultiGift tenantId:{}", tenantId, e);
            }
        }
        result.put("transferDataErrorEis", transferDataErrorEis);
        result.put("errorEis", errorTenantIds);
        log.info("multiGiftTransferResult={}", result);
        return result;
    }

    private boolean transferGiftMethodAndOptionGiftNum(String tenantId) {
        String sqlFormat = "select * from promotion_rule where tenant_id='%s' and is_deleted <> -1 and (gift_type is not null or gift_product_id is not null) order by id limit %d offset %d";
        int limit = 500;
        int offset = 0;
        int size = 0;
        boolean flag = true;
        do {
            try {
                String sql = String.format(sqlFormat, tenantId, limit, offset);
                List<Map> dataList = objectDataService.findBySql(tenantId, sql);
                if (CollectionUtils.empty(dataList)) {
                    break;
                }
                offset += limit;
                size = dataList.size();

                Set<String> promotionIds = dataList.stream().map(x -> String.valueOf(x.get(PromotionRuleConstants.Field.Promotion.apiName))).collect(Collectors.toSet());
                String promotionSql = String.format("select id from promotion where tenant_id='%s' and type in('4','13','24') and id in(%s)", tenantId, Joiner.on(",").join(promotionIds.stream().map(x -> String.format("'%s'", x)).collect(Collectors.toSet())));
                List<Map> promotionDataList = objectDataService.findBySql(tenantId, promotionSql);
                Set<String> promotionIdsOfGift = Sets.newHashSet();
                if (CollectionUtils.notEmpty(promotionDataList)) {
                    promotionDataList.forEach(x -> {
                        String promotionId = (String) x.get("id");
                        promotionIdsOfGift.add(promotionId);
                    });
                }
                dataList.removeIf(x -> {
                    String promotionId = (String) x.get("promotion_id");
                    return !promotionIdsOfGift.contains(promotionId);
                });

                if (CollectionUtils.notEmpty(dataList)) {
                    RequestData requestData = getRequestDataByMap(tenantId, PromotionGiftConstants.STORE_TABLE, dataList);
                    transfer(requestData);
                    boolean toPromotionGiftSuccess = bulkCreatePromotionGift(tenantId, dataList);
                    if (!toPromotionGiftSuccess) {
                        log.warn("tenantId:{} bulkCreatePromotionGift error,offset:{}", tenantId, offset - limit);
                        flag = false;
                    }
                }
            } catch (Exception e) {
                size = 0;
                flag = false;
                log.warn("tenantId:{},query promotionRule error", tenantId, e);
            }
        } while (size == limit);
        return flag;
    }

    private boolean bulkCreatePromotionGift(String tenantId, List<Map> promotionRuleList) {
        if (CollectionUtils.empty(promotionRuleList)) {
            return true;
        }
        try {
            Set<String> promotionRuleIds = Sets.newHashSet();
            Set<String> giftProductIds = Sets.newHashSet();
            promotionRuleList.forEach(x -> {
                String id = String.valueOf(x.get("id"));
                promotionRuleIds.add(id);
                ObjectDataExt promotionRuleExt = ObjectDataExt.of(x);
                String giftProductId = promotionRuleExt.get("gift_product_id", String.class);
                if (StringUtils.isNotEmpty(giftProductId)) {
                    giftProductIds.add(giftProductId);
                }
            });
            String promotionGiftSql = String.format("select * from promotion_gift where tenant_id='%s' and promotion_rule_id in(%s)", tenantId, Joiner.on(",").join(promotionRuleIds.stream().map(x -> String.format("'%s'", x)).collect(Collectors.toList())));
            List<Map> dbPromotionGiftList = objectDataService.findBySql(tenantId, promotionGiftSql);
            Set<String> dbPromotionRuleIds = Sets.newHashSet();
            if (CollectionUtils.notEmpty(dbPromotionGiftList)) {
                dbPromotionGiftList.forEach(x -> {
                    String promotionRuleId = (String) x.get(PromotionGiftConstants.Field.PromotionRule.apiName);
                    dbPromotionRuleIds.add(promotionRuleId);
                });
            }
            //查询赠品是否存在
            Set<String> dbGiftProductIds = Sets.newHashSet();
            if (CollectionUtils.notEmpty(giftProductIds)) {
                String giftProductSql = String.format("select id from biz_product where tenant_id='%s' and is_deleted <> -1 and id in (%s)", tenantId, Joiner.on(",").join(giftProductIds.stream().map(x -> String.format("'%s'", x)).collect(Collectors.toSet())));
                List<Map> dbGiftProductList = objectDataService.findBySql(tenantId, giftProductSql);
                if (CollectionUtils.notEmpty(dbGiftProductList)) {
                    dbGiftProductList.forEach(x -> {
                        String productId = String.valueOf(x.get("id"));
                        dbGiftProductIds.add(productId);
                    });
                }
            }

            List<IObjectData> promotionGiftList = promotionRuleList.stream().map(x -> {
                IObjectData objectData = new ObjectData();
                objectData.set("tenant_id", x.get("tenant_id"));
                objectData.set("object_describe_api_name", PromotionGiftConstants.API_NAME);
                objectData.set(PromotionGiftConstants.Field.Promotion.apiName, x.get("promotion_id"));
                objectData.set(PromotionGiftConstants.Field.PromotionRule.apiName, x.get("id"));
                objectData.setPackage("CRM");
                ObjectDataExt promotionRuleExt = ObjectDataExt.of(x);
                String giftProductId = promotionRuleExt.get("gift_product_id", String.class);
                BigDecimal giftProductNum = promotionRuleExt.get("gift_product_num", BigDecimal.class);
                String giftType = promotionRuleExt.get("gift_type", String.class);
                if (StringUtils.isEmpty(giftType)) {
                    giftType = StringUtils.isEmpty(giftProductId) ? GiftTypeEnum.Self.getValue() : GiftTypeEnum.NormalProduct.getValue();
                }
                objectData.set(PromotionGiftConstants.Field.GiftType.apiName, giftType);
                objectData.set(PromotionGiftConstants.Field.GiftProduct.apiName, giftProductId);
                objectData.set(PromotionGiftConstants.Field.GiftProductNum.apiName, giftProductNum);
                objectData.setCreatedBy((String) x.get("created_by"));
                objectData.setLastModifiedBy(objectData.getCreatedBy());
                objectData.setCreateTime(System.currentTimeMillis());
                objectData.setLastModifiedTime(objectData.getCreateTime());
                return objectData;
            }).filter(p -> {
                //过滤赠送普通产品时，产品不存在的数据
                String giftProductId = p.get(PromotionGiftConstants.Field.GiftProduct.apiName, String.class);
                if (StringUtils.isNotEmpty(giftProductId)) {
                    return dbGiftProductIds.contains(giftProductId);
                } else {
                    return true;
                }
            }).filter(x -> {
                String promotionRuleId = x.get(PromotionGiftConstants.Field.PromotionRule.apiName, String.class);
                return !dbPromotionRuleIds.contains(promotionRuleId);
            }).collect(Collectors.toList());
            serviceFacade.bulkSaveObjectData(promotionGiftList, User.builder().tenantId(tenantId).userId(User.SUPPER_ADMIN_USER_ID).build());
        } catch (Exception e) {
            log.warn("transfer gift data error,tenantId:{}", tenantId, e);
            return false;
        }
        return true;
    }

    @Override
    protected List<Record> parseRecord(SourceData sourceData, TableSchema tableSchema) {
        Map<String, Object> updateMap = Maps.newHashMap();
        updateMap.put(PromotionRuleConstants.Field.GiftMethod.apiName, GiftMethodEnum.SINGLE_GIFT.getValue());
        updateMap.put(PromotionRuleConstants.Field.OptionGiftNum.apiName, 1);
        return getUpdateRecord(sourceData, PromotionRuleConstants.STORE_TABLE_NAME, updateMap);
    }

    @Override
    protected String getOpType() {
        return OpType.UPDATE;
    }
}
