package com.facishare.crm.promotion.predefine.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crmcommon.constants.SystemConstants;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.promotion.constants.AdvertisementConstants;
import com.facishare.crm.promotion.constants.ProI18NKey;
import com.facishare.crm.promotion.constants.PromotionConstants;
import com.facishare.crm.promotion.constants.PromotionConstants.Field;
import com.facishare.crm.promotion.constants.PromotionGiftConstants;
import com.facishare.crm.promotion.constants.PromotionProductConstants;
import com.facishare.crm.promotion.constants.PromotionRuleConstants;
import com.facishare.crm.promotion.enums.PromotionConditionEnum;
import com.facishare.crm.promotion.enums.PromotionRecordTypeEnum;
import com.facishare.crm.promotion.enums.PromotionRuleRecordTypeEnum;
import com.facishare.crm.promotion.enums.PromotionRuleTypeEnum;
import com.facishare.crm.promotion.enums.PromotionTypeEnum;
import com.facishare.crm.promotion.enums.RuleMethodEnum;
import com.facishare.crm.promotion.exception.PromotionBusinessException;
import com.facishare.crm.promotion.predefine.manager.ProOrderProductManager;
import com.facishare.crm.promotion.predefine.manager.PromotionConfigManager;
import com.facishare.crm.promotion.predefine.manager.PromotionManager;
import com.facishare.crm.promotion.predefine.manager.SalesOrderManager;
import com.facishare.crm.promotion.predefine.mq.event.PromotionOpenEvent;
import com.facishare.crm.promotion.predefine.service.ProductPromotionService;
import com.facishare.crm.promotion.predefine.service.PromotionInitService;
import com.facishare.crm.promotion.predefine.service.PromotionService;
import com.facishare.crm.promotion.predefine.service.dto.*;
import com.facishare.crm.promotion.util.ConfigCenter;
import com.facishare.crm.promotion.util.DhtUtil;
import com.facishare.crm.promotion.util.PromotionUtil;
import com.facishare.crmcommon.manager.CustomerRangeManager;
import com.facishare.crmcommon.rest.dto.BatchGetPromotionProductQuantity;
import com.facishare.crmcommon.util.RangeVerify;
import com.facishare.crmcommon.util.SearchUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.mq.RocketMQMessageSender;
import com.facishare.paas.appframework.common.util.ObjectAPINameMapping;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.ISearchTemplateQuery;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.util.SpringUtil;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.base.Joiner;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.reflect.TypeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PromotionServiceImpl implements PromotionService {

    private static final String CPQ = "cpq";

    private static final String ADMIN_ID = "-10000";

    private static final String MODULE_CONFIG_DEFAULT_CPQ_VALUE = "1";

    @Autowired
    private PromotionConfigManager promotionConfigManager;
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private PromotionInitService promotionInitService;
    @Autowired
    private IObjectDescribeService objectDescribeService;
    @Autowired
    private SalesOrderManager salesOrderManager;
    @Autowired
    private PromotionManager promotionManager;
    @Autowired
    private ObjectDataServiceImpl objectDataService;
    @Autowired
    private ProductPromotionService productPromotionService;
    @Autowired
    private ConfigService configService;
    @Autowired
    private ProOrderProductManager proOrderProductManager;


    private static final String PROMOTION_VERSION_KEY = "promotion_app";

    @Override
    public PromotionType.EnableResult enablePromotion(ServiceContext serviceContext) {
        throw new ValidateException(I18N.text(ProI18NKey.PROMOTION_NOT_SUPPORT));
//        String tenantId = serviceContext.getTenantId();
//        Set<String> moduleList = serviceFacade.getModule(tenantId);
//        if (CollectionUtils.isEmpty(moduleList) || !moduleList.contains(PROMOTION_VERSION_KEY)) {
//            throw new ValidateException(I18N.text(ProI18NKey.PROMOTION_LICENSE_VALIDATE));
//        }
//        // 730版本先加上CPQ开启与促销开启的互斥校验
//        String moduleConfig = configService.findTenantConfig(User.systemUser(tenantId), CPQ);
//        if (Objects.nonNull(moduleConfig) && MODULE_CONFIG_DEFAULT_CPQ_VALUE.equals(moduleConfig)) {
//            throw new ValidateException(I18N.text(ProI18NKey.PROMOTION_CPQ_MUTUAL_EXCLUSION));
//        }
//        PromotionType.EnableResult enableResult = new PromotionType.EnableResult();
//        enableResult.setEnableStatus(PromotionType.PromotionSwitchEnum.OPENED.status);
//        enableResult.setMessage(PromotionType.PromotionSwitchEnum.OPENED.message);
//        PromotionType.PromotionSwitchEnum promotionSwitchEnum = promotionConfigManager.getPromotionStatus(tenantId);
//        if (promotionSwitchEnum == PromotionType.PromotionSwitchEnum.OPENED) {
//            return enableResult;
//        }
//        Set<String> existDisplayNames = checkDisplayName(tenantId);
//        if (CollectionUtils.isNotEmpty(existDisplayNames)) {
//            enableResult.setEnableStatus(PromotionType.PromotionSwitchEnum.PROMOTION_FAIL.status);
//            enableResult.setMessage(I18N.text(ProI18NKey.ALREADY_EXIST, Joiner.on(",").join(existDisplayNames).concat("名称已存在")));
//            return enableResult;
//        }
//        boolean success = false;
//        try {
//            success = promotionInitService.init(serviceContext.getUser());
//        } catch (Exception e) {
//            enableResult.setEnableStatus(PromotionType.PromotionSwitchEnum.PROMOTION_FAIL.status);
//            enableResult.setMessage(PromotionType.PromotionSwitchEnum.PROMOTION_FAIL.message);
//            log.warn("enablePromotion error,user:{}", serviceContext.getUser(), e);
//            promotionConfigManager.updatePromotionStatus(serviceContext.getUser(), PromotionType.PromotionSwitchEnum.PROMOTION_FAIL);
//        }
//        if (success) {
//            promotionConfigManager.updatePromotionStatus(serviceContext.getUser(), PromotionType.PromotionSwitchEnum.OPENED);
//            boolean result = promotionConfigManager.syncPromotionSwitchToCrm(serviceContext.getUser());//crmRestApi.syncTenantSwitch(arg, headers);
//            log.info("sync promotion status ,tenantId:{},result:{}", tenantId, result);
//            if (!result) {
//                promotionConfigManager.updatePromotionStatus(serviceContext.getUser(), PromotionType.PromotionSwitchEnum.SALESORDER_FAIL);
//                enableResult.setEnableStatus(PromotionType.PromotionSwitchEnum.SALESORDER_FAIL.status);
//                enableResult.setMessage(PromotionType.PromotionSwitchEnum.SALESORDER_FAIL.message);
//            } else {
//                //如果状态为open,促销开启
//                log.info(" sendDhtOpenPromotionMq  tenantId:{}", serviceContext.getTenantId());
//                sendDhtOpenPromotionMq(serviceContext.getTenantId(), true);
//                //开启CPQ
//                verifyCPQStatusAndAdaptiveCPQ(serviceContext.getTenantId());
//
//            }
//        }
//        return enableResult;
    }


    public void verifyCPQStatusAndAdaptiveCPQ(String tenantId) {
        //验证CPQ是否开启
        String moduleConfig = configService.findTenantConfig(new User(tenantId, ADMIN_ID), CPQ);
        if (Objects.isNull(moduleConfig) && !MODULE_CONFIG_DEFAULT_CPQ_VALUE.equals(moduleConfig)) {
            log.warn("CPQ is not open");
            return;
        }
        PromotionProductModel.Result updateResult = productPromotionService.updatePromotionProductAdaptiveCPQ(tenantId);
        log.info("CPQ status:{},msg:{}", updateResult.isSuccess(), updateResult.getMsg());
    }


    @Override
    public PromotionType.IsEnableResult isPromotionEnable(ServiceContext serviceContext) {
        PromotionType.PromotionSwitchEnum promotionStatus = promotionConfigManager.getPromotionStatus(serviceContext.getTenantId());
        PromotionType.IsEnableResult isEnableResult = new PromotionType.IsEnableResult();
        isEnableResult.setEnable(promotionStatus.status == PromotionType.PromotionSwitchEnum.OPENED.status);
        return isEnableResult;
    }

    @Override
    public PromotionType.IsEnableResult inPromotionWhiteList(ServiceContext serviceContext) {
        String tenantId = serviceContext.getTenantId();
        PromotionType.IsEnableResult isEnableResult = new PromotionType.IsEnableResult();
        isEnableResult.setEnable(false);
        Map<String, String> configs = configService.queryTenantConfigs(User.systemUser(tenantId), Lists.newArrayList("49", "promotion_mobile_h5"));
        String pcIndustryFlag = configs.getOrDefault("49", "0");
        String h5IndustryFlag = configs.getOrDefault("promotion_mobile_h5", "0");
        if (StringUtils.equals(pcIndustryFlag, "1") || StringUtils.equals(h5IndustryFlag, "1") || ConfigCenter.promotionWhitListTenantIds.contains(tenantId)) {
            isEnableResult.setEnable(true);
        }
        return isEnableResult;
    }

    @Override
    public PromotionType.DetailResult getById(ServiceContext serviceContext, PromotionType.IdModel idModel) {
        PromotionType.DetailResult detailResult = new PromotionType.DetailResult();
        IObjectDescribe objectDescribe = serviceFacade.findObject(serviceContext.getUser().getTenantId(), PromotionConstants.API_NAME);
        IObjectData promotionObjectData = serviceFacade.findObjectData(serviceContext.getUser(), idModel.getId(), objectDescribe);
        List<IFilter> iFilters = new ArrayList<>();
        SearchUtil.fillFilterEq(iFilters, PromotionProductConstants.Field.Promotion.apiName, idModel.getId());
        List<IObjectData> promotionProducts = searchQuery(serviceContext.getUser(), PromotionProductConstants.API_NAME, iFilters, Lists.newArrayList(), 0, 500).getData();
        List<IObjectData> promotionRules = searchQuery(serviceContext.getUser(), PromotionRuleConstants.API_NAME, iFilters, Lists.newArrayList(), 0, 500).getData();
        if (CollectionUtils.isNotEmpty(promotionProducts)) {
            detailResult.setPromotionProducts(ObjectDataDocument.ofList(promotionProducts));
        }
        if (PromotionUtil.isMultiGift(promotionObjectData) && CollectionUtils.isNotEmpty(promotionRules)) {
            Map<String, List<IObjectData>> promotionGiftMap = promotionManager.getPromotionGift(serviceContext.getUser(), ObjectDataDocument.ofList(promotionRules));
            detailResult.setPromotionGifts(promotionGiftMap.values().stream().flatMap(x -> x.stream().map(ObjectDataDocument::of)).collect(Collectors.toList()));
        }
        detailResult.setPromotionRules(ObjectDataDocument.ofList(promotionRules));
        detailResult.setPromotion(ObjectDataDocument.of(promotionObjectData));
        return detailResult;
    }

    @Override
    public List<PromotionType.DetailResult> getByPromotionAndProductIds(ServiceContext serviceContext, PromotionType.PromotionAndProductIdsModel promotionAndProductIdsModel) {
        List<PromotionType.DetailResult> detailResults = new ArrayList<>();
        List<PromotionType.PromotionProductId> promotionProductIds = promotionAndProductIdsModel.getPromotionProductIds();
        if (CollectionUtils.isEmpty(promotionProductIds)) {
            return detailResults;
        }
        List<String> promotionIds = Lists.newArrayList();
        Map<String, List<String>> promotionId2productIdsMap = Maps.newHashMap();
        promotionProductIds.forEach(promotionProductId -> {
            String promotionId = promotionProductId.getPromotionId();
            promotionIds.add(promotionId);
            if (CollectionUtils.isNotEmpty(promotionProductId.getProductIds())) {
                promotionId2productIdsMap.put(promotionId, promotionProductId.getProductIds());
            }
        });
        List<IObjectData> promotionDatas = serviceFacade.findObjectDataByIds(serviceContext.getTenantId(), promotionIds, PromotionConstants.API_NAME);
        boolean deleteInvalidFlag = Objects.isNull(promotionAndProductIdsModel.getDeleteInvalidFlag()) ? false : promotionAndProductIdsModel.getDeleteInvalidFlag();
        if (deleteInvalidFlag) {
            promotionDatas.removeIf(promotion -> {
                Boolean status = promotion.get(Field.Status.apiName, Boolean.class);
                return !status;
            });
        }
        List<IObjectData> productPromotions = Lists.newArrayList();
        List<IObjectData> combinePromotions = Lists.newArrayList();
        promotionDatas.forEach(promotion -> {
            String recordType = promotion.getRecordType();
            if (PromotionRecordTypeEnum.ProductPromotion.getApiName().equals(recordType)) {
                productPromotions.add(promotion);
            } else if (PromotionRecordTypeEnum.CombinePromotion.getApiName().equals(recordType)) {
                combinePromotions.add(promotion);
            }
        });
        List<IObjectData> promotionProductList = Lists.newArrayList();
        List<IObjectData> promotionRuleList = Lists.newArrayList();
        List<IFilter> filterList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(productPromotions)) {
            List<String> productPromotionIds = productPromotions.stream().map(IObjectData::getId).collect(Collectors.toList());
            List<String> productIds = productPromotionIds.stream().map(promotionId -> promotionId2productIdsMap.get(promotionId).stream()).flatMap(Function.identity()).collect(Collectors.toList());
            SearchUtil.fillFilterIn(filterList, PromotionRuleConstants.Field.Promotion.apiName, productPromotionIds);
            List<IObjectData> ruleList = searchQuery(serviceContext.getUser(), PromotionRuleConstants.API_NAME, filterList, Lists.newArrayList(), 0, ConfigCenter.queryPromotionRuleLimit).getData();
            promotionRuleList.addAll(ruleList);
            SearchUtil.fillFilterIn(filterList, PromotionProductConstants.Field.Product.apiName, productIds);
            List<IObjectData> promotionProductDataList = searchQuery(serviceContext.getUser(), PromotionProductConstants.API_NAME, filterList, Lists.newArrayList(), 0,
                    ConfigCenter.queryPromotionProductLimit).getData();
            promotionProductDataList.removeIf(promotionProduct -> {
                String promotionId = promotionProduct.get(PromotionProductConstants.Field.Promotion.apiName, String.class);
                String productId = promotionProduct.get(PromotionProductConstants.Field.Product.apiName, String.class);
                List<String> productArgIds = promotionId2productIdsMap.getOrDefault(promotionId, Lists.newArrayList());
                return !productArgIds.contains(productId);
            });
            promotionProductList.addAll(promotionProductDataList);
        }
        if (CollectionUtils.isNotEmpty(combinePromotions)) {
            List<String> combinePromotionIds = combinePromotions.stream().map(IObjectData::getId).collect(Collectors.toList());
            filterList.clear();
            SearchUtil.fillFilterIn(filterList, PromotionRuleConstants.Field.Promotion.apiName, combinePromotionIds);
            List<IObjectData> combinePromotionRuleList = searchQuery(serviceContext.getUser(), PromotionRuleConstants.API_NAME, filterList, Lists.newArrayList(), 0,
                    ConfigCenter.queryPromotionRuleLimit).getData();
            promotionRuleList.addAll(combinePromotionRuleList);
            List<IObjectData> combinePromotionProductList = searchQuery(serviceContext.getUser(), PromotionProductConstants.API_NAME, filterList, Lists.newArrayList(), 0,
                    ConfigCenter.queryPromotionProductLimit).getData();
            promotionProductList.addAll(combinePromotionProductList);
        }
        Map<String, List<IObjectData>> promotion2RuleListMap = promotionRuleList.stream().collect(Collectors.groupingBy(x -> x.get(PromotionRuleConstants.Field.Promotion.apiName, String.class)));
        Map<String, List<IObjectData>> promotion2PromotionProductMap = promotionProductList.stream()
                .collect(Collectors.groupingBy(x -> x.get(PromotionProductConstants.Field.Promotion.apiName, String.class)));
        List<ObjectDataDocument> allPromotionRuleDocumentList = Lists.newArrayList();
        promotionDatas.forEach(promotion -> {
            String promotionId = promotion.getId();
            PromotionType.DetailResult detailResult = new PromotionType.DetailResult();
            detailResult.setPromotion(ObjectDataDocument.of(promotion));
            List<ObjectDataDocument> promotionRuleDocumentList = ObjectDataDocument.ofList(promotion2RuleListMap.get(promotionId));
            detailResult.setPromotionRules(promotionRuleDocumentList);
            if (PromotionUtil.isMultiGift(promotion) && CollectionUtils.isNotEmpty(promotionRuleDocumentList)) {
                allPromotionRuleDocumentList.addAll(promotionRuleDocumentList);
            }
            detailResult.setPromotionProducts(ObjectDataDocument.ofList(promotion2PromotionProductMap.get(promotionId)));
            detailResults.add(detailResult);
        });
        Map<String, List<ObjectDataDocument>> promotionIdGiftListMap = getPromotionGiftMapByRules(serviceContext.getUser(), allPromotionRuleDocumentList);
        detailResults.forEach(x -> {
            String promotionId = x.getPromotion().getId();
            x.setPromotionGifts(promotionIdGiftListMap.get(promotionId));
        });
        return detailResults;
    }

    @Override
    public List<PromotionType.DetailResult> getByIds(ServiceContext serviceContext, PromotionType.IdsModel idsModel) {
        List<PromotionType.DetailResult> detailResults = new ArrayList<>();
        List<IObjectData> promotionDatas = serviceFacade.findObjectDataByIds(serviceContext.getTenantId(), idsModel.getIds(), PromotionConstants.API_NAME);
        //禁用的数据过滤
        if (idsModel.getDeleteInvalidFlag() != null && idsModel.getDeleteInvalidFlag()) {
            promotionDatas.removeIf(o -> {
                Boolean status = o.get(PromotionConstants.Field.Status.apiName, Boolean.class);
                if (!status) {
                    return true;
                }
                return false;
            });
        }
        if (CollectionUtils.isNotEmpty(promotionDatas)) {
            Map<String, IObjectData> promotionMap = promotionDatas.stream().collect(Collectors.toMap(IObjectData::getId, Function.identity()));
            List<String> promotionIds = promotionDatas.stream().map(IObjectData::getId).collect(Collectors.toList());
            List<IFilter> filters = new ArrayList<>();
            SearchUtil.fillFilterIn(filters, PromotionRuleConstants.Field.Promotion.apiName, promotionIds);
            List<IObjectData> promotionProducts = searchQuery(serviceContext.getUser(), PromotionProductConstants.API_NAME, filters, Lists.newArrayList(), 0, 500).getData();
            Map<String, List<IObjectData>> promotionProductsMap = promotionProducts.stream()
                    .collect(Collectors.groupingBy(o -> o.get(PromotionProductConstants.Field.Promotion.apiName, String.class)));
            List<IObjectData> promotionRules = searchQuery(serviceContext.getUser(), PromotionRuleConstants.API_NAME, filters, Lists.newArrayList(), 0, 500).getData();
            Map<String, List<IObjectData>> promotionRuleMap = promotionRules.stream().collect(Collectors.groupingBy(o -> o.get(PromotionRuleConstants.Field.Promotion.apiName, String.class)));
            List<ObjectDataDocument> allPromotionRuleDocumentList = Lists.newArrayList();
            promotionIds.forEach(o -> {
                if (promotionMap.containsKey(o)) {
                    PromotionType.DetailResult detailResult = new PromotionType.DetailResult();
                    IObjectData promotionData = promotionMap.get(o);
                    List<ObjectDataDocument> promotionRuleList = ObjectDataDocument.ofList(promotionRuleMap.get(o));
                    if (PromotionUtil.isMultiGift(promotionData) && CollectionUtils.isNotEmpty(promotionRuleList)) {
                        allPromotionRuleDocumentList.addAll(promotionRuleList);
                    }
                    detailResult.setPromotion(ObjectDataDocument.of(promotionData));
                    detailResult.setPromotionRules(promotionRuleList);
                    detailResult.setPromotionProducts(ObjectDataDocument.ofList(promotionProductsMap.get(o)));
                    detailResults.add(detailResult);
                }
            });
            Map<String, List<ObjectDataDocument>> promotionIdGiftMap = getPromotionGiftMapByRules(serviceContext.getUser(), allPromotionRuleDocumentList);
            detailResults.forEach(x -> {
                String promotionId = x.getPromotion().getId();
                x.setPromotionGifts(promotionIdGiftMap.get(promotionId));
            });
        }
        return detailResults;
    }

    @Override
    public PromotionType.ProductPromotionResult listByProductIds(ServiceContext serviceContext, PromotionType.ProductPromotionListArg productPromotionListArg) {
        Preconditions.checkNotNull(productPromotionListArg.getCustomerId(), "customerId is null");
        PromotionType.ProductPromotionResult productPromotionResult = new PromotionType.ProductPromotionResult();
        if (CollectionUtils.isEmpty(productPromotionListArg.getProductIds())) {
            return productPromotionResult;
        }
        List<IFilter> filters = new ArrayList<>();
        SearchUtil.fillFilterIn(filters, SystemConstants.Field.RecordType.apiName, Lists.newArrayList(PromotionRecordTypeEnum.ProductPromotion.getApiName(), PromotionRecordTypeEnum.CombinePromotion.getApiName()));
        List<IObjectData> promotionDataList = findPromotionsByCustomerId(serviceContext, productPromotionListArg.getCustomerId(), true, filters).getData();
        if (CollectionUtils.isEmpty(promotionDataList)) {
            return productPromotionResult;
        }

        List<String> promotionIds = promotionDataList.stream().map(IObjectData::getId).collect(Collectors.toList());
        List<IObjectData> allProductPromotions = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(promotionIds)) {
            filters.clear();
            SearchUtil.fillFilterIn(filters, PromotionProductConstants.Field.Promotion.apiName, promotionIds);
            SearchUtil.fillFilterIn(filters, PromotionProductConstants.Field.Product.apiName, productPromotionListArg.getProductIds());
            allProductPromotions = searchQueryIgnoreAll(serviceContext.getUser(), PromotionProductConstants.API_NAME, filters, Lists.newArrayList(), 0, ConfigCenter.queryPromotionProductLimit).getData();
        }
        Set<String> combinePromotionIds = Sets.newHashSet();
        Set<String> matchedPromotionIds = allProductPromotions.stream().map(x -> x.get(PromotionProductConstants.Field.Promotion.apiName, String.class)).collect(Collectors.toSet());
        List<IObjectData> promotions = promotionDataList.stream().filter(iObjectData -> matchedPromotionIds.contains(iObjectData.getId())).peek(x -> {
            String type = x.get(Field.Type.apiName, String.class);
            if (PromotionTypeEnum.isCombinePromotion(type)) {
                String promotionId = x.getId();
                combinePromotionIds.add(promotionId);
            }
        }).collect(Collectors.toList());
        Map<String, IObjectData> promotionMap = promotions.stream().collect(Collectors.toMap(IObjectData::getId, Function.identity()));
        if (CollectionUtils.isNotEmpty(combinePromotionIds)) {
            filters.clear();
            SearchUtil.fillFilterIn(filters, PromotionProductConstants.Field.Promotion.apiName, Lists.newArrayList(combinePromotionIds));
            SearchUtil.fillFilterNotIn(filters, PromotionProductConstants.Field.Product.apiName, productPromotionListArg.getProductIds());
            List<IObjectData> combinePromotionProductList = searchQueryIgnoreAll(serviceContext.getUser(), PromotionProductConstants.API_NAME, filters, Lists.newArrayList(), 0, ConfigCenter.queryPromotionProductLimit).getData();
            allProductPromotions.addAll(combinePromotionProductList);
        }
        //促销id  to  促销产品列表
        Map<String, List<IObjectData>> promotionId2ProductPromotionsMap = allProductPromotions.stream().collect(Collectors.groupingBy(o -> o.get(PromotionProductConstants.Field.Promotion.apiName, String.class)));
        List<IObjectData> promotionRules = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(matchedPromotionIds)) {
            filters.clear();
            SearchUtil.fillFilterIn(filters, PromotionRuleConstants.Field.Promotion.apiName, Lists.newArrayList(matchedPromotionIds));
            promotionRules = searchQueryIgnoreAll(serviceContext.getUser(), PromotionRuleConstants.API_NAME, filters, Lists.newArrayList(), 0, ConfigCenter.queryPromotionRuleLimit).getData();
        }
        Map<String, List<IObjectData>> productId2productPromotionsMap = allProductPromotions.stream().collect(Collectors.groupingBy(o -> o.get(PromotionProductConstants.Field.Product.apiName, String.class)));
        //promotionId 2 List<promotionRules>
        Map<String, List<IObjectData>> promotionRuleMap = promotionRules.stream().collect(Collectors.groupingBy(o -> o.get(PromotionRuleConstants.Field.Promotion.apiName, String.class)));
        Map<String, PromotionType.WrapProductPromotion> wrapProductPromotionMap = new HashMap<>();
        List<ObjectDataDocument> allPromotionRuleList = Lists.newArrayList();
        for (String productId : productPromotionListArg.getProductIds()) {
            List<PromotionType.DetailResult> detailResults = new ArrayList<>();
            List<IObjectData> productPromotions = productId2productPromotionsMap.get(productId);
            if (CollectionUtils.isEmpty(productPromotions)) {
                continue;
            }
            Set<String> promotionIds1 = productPromotions.stream().map(o -> o.get(PromotionProductConstants.Field.Promotion.apiName, String.class)).collect(Collectors.toSet());
            for (String promotionId : promotionIds1) {
                PromotionType.DetailResult detailResult = new PromotionType.DetailResult();
                IObjectData promotionData = promotionMap.get(promotionId);
                List<ObjectDataDocument> promotionRuleDocumentList = ObjectDataDocument.ofList(promotionRuleMap.get(promotionId));
                List<IObjectData> promotionProducts = promotionId2ProductPromotionsMap.get(promotionId);
                String promotionTypeTmp = promotionData.get(Field.Type.apiName, String.class);
                detailResult.setPromotionProducts(ObjectDataDocument.ofList(promotionProducts.stream().filter(x -> {
                    if (PromotionTypeEnum.isCombinePromotion(promotionTypeTmp)) {
                        return true;
                    } else {
                        String pid = x.get(PromotionProductConstants.Field.Product.apiName, String.class);
                        return productId.equals(pid);
                    }
                }).collect(Collectors.toList())));
                detailResult.setPromotion(ObjectDataDocument.of(promotionData));
                detailResult.setPromotionRules(promotionRuleDocumentList);
                if (PromotionUtil.isMultiGift(promotionData) && CollectionUtils.isNotEmpty(promotionRuleDocumentList)) {
                    allPromotionRuleList.addAll(promotionRuleDocumentList);
                }
                if (wrapProductPromotionMap.containsKey(productId)) {
                    PromotionType.WrapProductPromotion wrapProductPromotion1 = wrapProductPromotionMap.get(productId);
                    List<PromotionType.DetailResult> tmp = wrapProductPromotion1.getPromotions();
                    tmp.add(detailResult);
                } else {
                    detailResults.add(detailResult);
                    PromotionType.WrapProductPromotion wrapProductPromotion = new PromotionType.WrapProductPromotion();
                    wrapProductPromotion.setPromotions(detailResults);
                    wrapProductPromotion.setProductId(productId);
                    wrapProductPromotionMap.put(productId, wrapProductPromotion);
                }
            }
        }
        Map<String, List<ObjectDataDocument>> promotionIdGiftMap = getPromotionGiftMapByRules(serviceContext.getUser(), allPromotionRuleList);
        List<PromotionType.WrapProductPromotion> resultList = Lists.newArrayList(wrapProductPromotionMap.values());
        resultList.forEach(wrapProductPromotion -> {
            List<PromotionType.DetailResult> promotionTempList = wrapProductPromotion.getPromotions();
            if (CollectionUtils.isNotEmpty(promotionTempList)) {
                promotionTempList.forEach(x -> {
                    String promotionId = x.getPromotion().getId();
                    x.setPromotionGifts(promotionIdGiftMap.get(promotionId));
                });
            }
        });
        productPromotionResult.setPromotions(Lists.newArrayList(wrapProductPromotionMap.values()));
        return productPromotionResult;
    }

    //6.3 订货通首页促销列表,只查主数据
    @Override
    public PromotionType.PromotionListResult listPromotions(ServiceContext serviceContext, PromotionType.CustomerIdArg customerIdArg) {
        List<IFilter> filterList = Lists.newArrayList();
        List<OrderBy> orderBys = null;
        if (StringUtils.isNotBlank(customerIdArg.getSearchQueryInfo())) {
            ISearchTemplateQuery query = SearchTemplateQuery.fromJsonString(customerIdArg.getSearchQueryInfo());
            orderBys = query.getOrders();
            filterList.addAll(query.getFilters());
        }
        PromotionType.PromotionListResult promotionListResult = new PromotionType.PromotionListResult();
        if (customerIdArg.getDeleteIneffective() != null && customerIdArg.getDeleteIneffective()) {
            long curTime = System.currentTimeMillis();
            SearchUtil.fillFilterGT(filterList, Field.EndTime.apiName, curTime);
        }
        List<IObjectData> objectDataList = findPromotionsByCustomerId(serviceContext, customerIdArg.getCustomerId(), false, filterList, orderBys).getData();
        if (CollectionUtils.isEmpty(objectDataList)) {
            return promotionListResult;
        }
        List<PromotionType.DetailResult> detailResults = Lists.newArrayList();
        for (IObjectData o : objectDataList) {
            PromotionType.DetailResult detailResult = new PromotionType.DetailResult();
            // 隐藏自定义函数促销的促销规则
            String ruleType = o.get(PromotionConstants.Field.RuleType.apiName, String.class);
            if (PromotionRuleTypeEnum.Custom.getValue().equals(ruleType)) {
                o.set(PromotionConstants.Field.Type.apiName, "--");
            }
            detailResult.setPromotion(ObjectDataDocument.of(o));
            detailResults.add(detailResult);
        }
        List<String> promotionIds = objectDataList.stream().filter(x -> {
            String recordType = x.getRecordType();
            if (PromotionRuleRecordTypeEnum.ProductPromotion.getApiName().equals(recordType)) {
                String ruleMethod = x.get(Field.RuleMethod.apiName, String.class);
                return StringUtils.isEmpty(ruleMethod) || RuleMethodEnum.UnifiedSetting.getValue().equals(ruleMethod);
            } else {
                return true;
            }
        }).map(IObjectData::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(promotionIds)) {//dht h5 展示促销规则
            List<IFilter> promotionRuleFilter = Lists.newArrayList();
            SearchUtil.fillFilterIn(promotionRuleFilter, PromotionRuleConstants.Field.Promotion.apiName, promotionIds);
            QueryResult<IObjectData> promotionRuleQueryResult = searchQuery(serviceContext.getUser(), PromotionRuleConstants.API_NAME, promotionRuleFilter, Lists.newArrayList(), 0, ConfigCenter.queryPromotionRuleLimit);
            List<IObjectData> promotionRuleDataList = promotionRuleQueryResult.getData();
            Map<String, List<ObjectDataDocument>> promotionIdGiftMap = getPromotionGiftMapByRules(serviceContext.getUser(), ObjectDataDocument.ofList(promotionRuleDataList));
            Map<String, List<IObjectData>> promotionIdRuleListMap = promotionRuleDataList.stream().collect(Collectors.groupingBy(x -> x.get(PromotionRuleConstants.Field.Promotion.apiName, String.class)));
            for (PromotionType.DetailResult detailResult : detailResults) {
                String promotionId = detailResult.getPromotion().getId();
                detailResult.setPromotionRules(ObjectDataDocument.ofList(promotionIdRuleListMap.get(promotionId)));
                detailResult.setPromotionGifts(promotionIdGiftMap.get(promotionId));
            }
        }
        promotionListResult.setPromotions(detailResults);
        return promotionListResult;
    }

    /**
     * 拉取组合促销，并返回促销规则和促销产品
     *
     * @param serviceContext
     * @param arg
     * @return
     */
    @Override
    public PromotionType.PromotionListResult listCombinePromotions(ServiceContext serviceContext, PromotionType.CustomerIdArg arg) {
        if (arg.getOffset() != null && arg.getOffset() < 0) {
            throw new ValidateException("invalid parameter: offset");
        }
        if (arg.getLimit() != null && arg.getLimit() < 0) {
            throw new ValidateException("invalid parameter: limit");
        }

        PromotionType.PromotionListResult promotionListResult = new PromotionType.PromotionListResult();
        List<IFilter> filterList = Lists.newArrayList();
        SearchUtil.fillFilterEq(filterList, SystemConstants.Field.RecordType.apiName, PromotionRecordTypeEnum.CombinePromotion.getApiName());
        List<IObjectData> objectDataList = findPromotionsByCustomerId(serviceContext, arg.getCustomerId(), true, filterList).getData();
        if (CollectionUtils.isEmpty(objectDataList)) {
            return promotionListResult;
        }

        int totalNumber = objectDataList.size();
        Integer offset = ObjectUtils.defaultIfNull(arg.getOffset(), 0);
        Integer limit = ObjectUtils.defaultIfNull(arg.getLimit(), totalNumber);

        List<PromotionType.DetailResult> detailResults = Lists.newArrayList();
        List<String> promotionIds = Lists.newArrayList();
        objectDataList.stream().skip(offset).limit(limit).forEach(objectData -> {
            PromotionType.DetailResult detailResult = new PromotionType.DetailResult();
            detailResult.setPromotion(ObjectDataDocument.of(objectData));
            detailResults.add(detailResult);

            String ruleType = objectData.get(Field.RuleType.apiName, String.class);
            if (StringUtils.equals(ruleType, PromotionRuleTypeEnum.Standard.getValue())) {
                promotionIds.add(objectData.getId());
            }
        });
        if (CollectionUtils.isNotEmpty(promotionIds)) {
            List<IFilter> promotionRuleFilter = Lists.newArrayList();
            SearchUtil.fillFilterIn(promotionRuleFilter, PromotionRuleConstants.Field.Promotion.apiName, promotionIds);
            QueryResult<IObjectData> promotionRuleQueryResult = searchQuery(serviceContext.getUser(), PromotionRuleConstants.API_NAME, promotionRuleFilter, Lists.newArrayList(), 0, ConfigCenter.queryPromotionRuleLimit);
            Map<String, List<IObjectData>> promotionRuleMap = promotionRuleQueryResult.getData().stream().collect(Collectors.groupingBy(x -> x.get(PromotionRuleConstants.Field.Promotion.apiName, String.class)));

            List<IFilter> promotionProductFilter = Lists.newArrayList();
            SearchUtil.fillFilterIn(promotionProductFilter, PromotionProductConstants.Field.Promotion.apiName, promotionIds);
            QueryResult<IObjectData> promotionProductQueryResult = searchQuery(serviceContext.getUser(), PromotionProductConstants.API_NAME, promotionProductFilter, Lists.newArrayList(), 0, ConfigCenter.queryPromotionProductLimit);
            Map<String, List<IObjectData>> promotionProductMap = promotionProductQueryResult.getData().stream().collect(Collectors.groupingBy(x -> x.get(PromotionProductConstants.Field.Promotion.apiName, String.class)));
            Map<String, List<ObjectDataDocument>> promotionGiftMap = getPromotionGiftMapByRules(serviceContext.getUser(), ObjectDataDocument.ofList(promotionRuleQueryResult.getData()));
            detailResults.forEach(detailResult -> {
                ObjectDataDocument dataDocument = detailResult.getPromotion();
                String promotionId = dataDocument.getId();
                detailResult.setPromotionRules(ObjectDataDocument.ofList(promotionRuleMap.get(promotionId)));
                detailResult.setPromotionProducts(ObjectDataDocument.ofList(promotionProductMap.get(promotionId)));
                detailResult.setPromotionGifts(promotionGiftMap.get(promotionId));
            });
        }
        promotionListResult.setTotalNumber(totalNumber);
        promotionListResult.setPromotions(detailResults);
        return promotionListResult;
    }

    @Override
    public QueryProductInPromotionModel.Result listPromotionProductsGroupBySpuId(ServiceContext serviceContext, QueryProductInPromotionModel.Arg arg) throws Exception {
        String customerId = arg.getCustomerId();
        QueryProductInPromotionModel.Result result = new QueryProductInPromotionModel.Result();
        String priceBookId = arg.getPriceBookId();
        if (StringUtils.isEmpty(customerId) || !ConfigCenter.usePromotionProductOpt) {
            PromotionType.PromotionProductList promotionProductList = listPromotionProductsGroupBySpuId(serviceContext, priceBookId);
            if (Objects.nonNull(promotionProductList.getDataList())) {
                result.setDataList(promotionProductList.getDataList().stream().map(x -> ObjectDataDocument.of((Map<String, Object>) x)).collect(Collectors.toList()));
            }
            return result;
        }
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, SystemConstants.Field.RecordType.apiName, Lists.newArrayList(PromotionRecordTypeEnum.ProductPromotion.getApiName(), PromotionRecordTypeEnum.CombinePromotion.getApiName()));
        List<IObjectData> promotions = findPromotionsByCustomerId(serviceContext, customerId, true, filters, Lists.newArrayList()).getData();
        Set<String> promotionIds = promotions.stream().map(IObjectData::getId).collect(Collectors.toSet());
        String tenantId = serviceContext.getTenantId();
        String moduleConfig = configService.findTenantConfig(new User(tenantId, ADMIN_ID), CPQ);

        List<IFilter> promotionProductFilters = Lists.newArrayList();
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        SearchUtil.fillFilterIn(promotionProductFilters, PromotionProductConstants.Field.Promotion.apiName, Lists.newArrayList(promotionIds));
        SearchUtil.fillFilterEq(promotionProductFilters, "is_deleted", Lists.newArrayList(Boolean.FALSE));
        searchTemplateQuery.setFilters(promotionProductFilters);
        List<String> subFilterValues = Lists.newArrayList(JsonUtil.toJson(searchTemplateQuery), "PromotionProductObj", "product_id");

        List<IFilter> productFilters = Lists.newArrayList();
        User superUser = User.systemUser(tenantId);
        if (StringUtils.isNotEmpty(priceBookId)) {
            SearchTemplateQuery priceBookProductQuery = new SearchTemplateQuery();
            priceBookProductQuery.setLimit(1000);
            priceBookProductQuery.setOffset(0);
            List<IFilter> priceBookFilters = Lists.newArrayList();
            SearchUtil.fillFilterEq(priceBookFilters, "pricebook_id", Lists.newArrayList(priceBookId));
            SearchUtil.fillFilterEq(priceBookFilters, "is_deleted", Lists.newArrayList(Boolean.FALSE.toString()));
            SearchUtil.fillFilterIn(priceBookFilters, "product_id", subFilterValues, 10);
            priceBookProductQuery.setFilters(priceBookFilters);
            List<IObjectData> priceBookProductList = serviceFacade.findBySearchQuery(superUser, Utils.PRICE_BOOK_PRODUCT_API_NAME, priceBookProductQuery).getData();
            Set<String> productIds = priceBookProductList.stream().map(x -> x.get("product_id", String.class)).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(productIds)) {
                return result;
            }
            SearchUtil.fillFilterIn(productFilters, "_id", Lists.newArrayList(productIds));

        } else {
            SearchUtil.fillFilterIn(productFilters, "_id", subFilterValues, 10);
        }

        SearchUtil.fillFilterEq(productFilters, "product_status", Lists.newArrayList("1"));
        SearchUtil.fillFilterEq(productFilters, "life_status", Lists.newArrayList("normal"));
        if (MODULE_CONFIG_DEFAULT_CPQ_VALUE.equals(moduleConfig)) {
            SearchUtil.fillFilterEq(productFilters, "is_saleable", Lists.newArrayList(Boolean.TRUE.toString()));
        }

        SearchTemplateQuery productSearchQuery = new SearchTemplateQuery();
        productSearchQuery.setFilters(productFilters);
        productSearchQuery.setOffset(0);
        productSearchQuery.setLimit(1000);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(superUser, Utils.PRODUCT_API_NAME, productSearchQuery);
        result.setDataList(ObjectDataDocument.ofList(queryResult.getData()));
        return result;
    }

    /**
     * 1.查询促销产品列表，按照spu分页
     */
    public PromotionType.PromotionProductList listPromotionProductsGroupBySpuId(ServiceContext serviceContext, String priceBookId) throws Exception {
        PromotionType.PromotionProductList promotionProductList = new PromotionType.PromotionProductList();
        String tenantId = serviceContext.getTenantId();
        Long currentMillis = System.currentTimeMillis();
        Long endCompare = currentMillis;
        StringBuilder stringBuilder = new StringBuilder("(");
        /**
         * 验证CPQ是否开启
         **/
        String moduleConfig = configService.findTenantConfig(new User(tenantId, ADMIN_ID), CPQ);
        int limit = 1000;
        int offset = 0;
        String sqlOfProducts = null;
        if (StringUtils.isEmpty(priceBookId)) {
            String sql = "SELECT bi.spu_id FROM biz_product AS bi,promotion_product AS pr,promotion AS pro where bi.tenant_id='%s' and bi.\"id\"=pr.product_id and pr.promotion_id=pro.\"id\" and bi.life_status='normal' and bi.product_status='1' and pro.start_time<%s and pro.end_time>%s GROUP BY bi.spu_id LIMIT %s OFFSET %s;";
            String countSql = "SELECT count(DISTINCT bi.spu_id) FROM biz_product AS bi,promotion_product AS pr,promotion AS pro where bi.tenant_id='%s' and bi.\"id\"=pr.product_id and pr.promotion_id=pro.\"id\" and bi.life_status='normal' and bi.product_status='1' and pro.start_time<%s and pro.end_time>%s;";
            sql = String.format(sql, tenantId, currentMillis, endCompare, limit, offset);
            countSql = String.format(countSql, tenantId, currentMillis, endCompare);
            List<Map> spuIdMap = objectDataService.findBySql(serviceContext.getTenantId(), sql);//findBySql 有坑，可能会返回null元素（limit > 总数的情况）
            if (CollectionUtils.isEmpty(spuIdMap)) {
                return promotionProductList;
            }
            List<String> hasPromotionSpuId = Lists.newArrayList();
            spuIdMap.forEach(o -> {
                if (o != null) {
                    hasPromotionSpuId.add((String) o.get("spu_id"));
                }
            });
            hasPromotionSpuId.forEach(o -> {
                stringBuilder.append("'").append(o).append("'").append(",");
            });
            stringBuilder.replace(stringBuilder.length() - 1, stringBuilder.length(), ")");
            List<Map> count = objectDataService.findBySql(serviceContext.getTenantId(), countSql);
            //是否开启CPQ
            if (Objects.nonNull(moduleConfig) && MODULE_CONFIG_DEFAULT_CPQ_VALUE.equals(moduleConfig)) {
                sqlOfProducts = "SELECT DISTINCT bi.\"id\",bi.category,bi.is_giveaway,bi.is_saleable,bi.life_status,bi.\"name\",bi.picture_path,bi.price,bi.product_code,bi.product_spec,bi.product_status,bi.spu_id,bi.tenant_id,bi.unit,bi.record_type,bi.remark,bi.is_multiple_unit FROM biz_product AS bi,promotion_product AS pr,promotion AS pro " +
                        "where bi.tenant_id='%s' and bi.\"id\"=pr.product_id and pr.promotion_id=pro.\"id\" " +
                        //条件：可独立销售+上架产品
                        "and bi.life_status='normal' and bi.product_status='1' and  bi.is_saleable=true " +
                        "and pro.start_time<%s and pro.end_time>%s and bi.spu_id IN %s;";
            } else {
                sqlOfProducts = "SELECT DISTINCT bi.\"id\",bi.category,bi.is_giveaway,bi.life_status,bi.\"name\",bi.picture_path,bi.price,bi.product_code,bi.product_spec,bi.product_status,bi.spu_id,bi.tenant_id,bi.unit,bi.record_type,bi.remark,bi.is_multiple_unit FROM biz_product AS bi,promotion_product AS pr,promotion AS pro where bi.tenant_id='%s' and bi.\"id\"=pr.product_id and pr.promotion_id=pro.\"id\" and bi.life_status='normal' and bi.product_status='1' and pro.start_time<%s and pro.end_time>%s and bi.spu_id IN %s;";
            }
            sqlOfProducts = String.format(sqlOfProducts, tenantId, currentMillis, endCompare, stringBuilder.toString(), limit, offset);
            log.info("listPromotionProductsGroupBySpuId sqlOfProducts:{}", sqlOfProducts);
            List<Map> products = objectDataService.findBySql(serviceContext.getTenantId(), sqlOfProducts);
            for (Map map : count) {
                Long size = (Long) map.get("count");
                promotionProductList.setTotal(size);
            }
            promotionProductList.setDataList(products);
        } else {
            String sql = "SELECT bi.spu_id FROM biz_product AS bi,promotion_product AS pr,promotion AS pro,price_book AS pb,price_book_product As pbp where bi.tenant_id='%s' and pb.\"id\"='%s' and bi.\"id\"=pbp.product_id and pbp.pricebook_id=pb.\"id\" and bi.\"id\"=pr.product_id and pr.promotion_id=pro.\"id\" and bi.life_status='normal' and bi.product_status='1' and pro.start_time<%s and pro.end_time>%s GROUP BY bi.spu_id LIMIT %s OFFSET %s;";
            String countSql = "SELECT count(DISTINCT bi.spu_id) FROM biz_product AS bi,promotion_product AS pr,promotion AS pro,price_book AS pb,price_book_product As pbp where bi.tenant_id='%s' and pb.\"id\"='%s' and bi.\"id\"=pbp.product_id and pbp.pricebook_id=pb.\"id\" and bi.\"id\"=pr.product_id and pr.promotion_id=pro.\"id\" and bi.life_status='normal' and bi.product_status='1' and pro.start_time<%s and pro.end_time>%s;";
            sql = String.format(sql, tenantId, priceBookId, currentMillis, endCompare, limit, offset);
            countSql = String.format(countSql, tenantId, priceBookId, currentMillis, endCompare);
            List<Map> spuIdMap = objectDataService.findBySql(serviceContext.getTenantId(), sql);
            if (CollectionUtils.isEmpty(spuIdMap)) {
                return promotionProductList;
            }
            List<String> hasPromotionSpuId = Lists.newArrayList();
            spuIdMap.forEach(o -> {
                if (o != null) {
                    hasPromotionSpuId.add((String) o.get("spu_id"));
                }
            });
            hasPromotionSpuId.forEach(o -> {
                stringBuilder.append("'").append(o).append("'").append(",");
            });
            stringBuilder.replace(stringBuilder.length() - 1, stringBuilder.length(), ")");
            List<Map> count = objectDataService.findBySql(serviceContext.getTenantId(), countSql);

            //是否开启CPQ
            if (Objects.nonNull(moduleConfig) && MODULE_CONFIG_DEFAULT_CPQ_VALUE.equals(moduleConfig)) {
                sqlOfProducts = "SELECT DISTINCT bi.\"id\",bi.category,bi.is_giveaway,bi.is_saleable,bi.life_status,bi.\"name\",bi.picture_path,bi.price,bi.product_code,bi.product_spec,bi.product_status,bi.spu_id,bi.tenant_id,bi.unit,bi.record_type,bi.remark,bi.is_multiple_unit,pbp.pricebook_price,pbp.pricebook_sellingprice,pbp.discount " +
                        "FROM biz_product AS bi,promotion_product AS pr,promotion AS pro,price_book AS pb,price_book_product As pbp " +
                        "where bi.tenant_id='%s' and pb.\"id\"='%s' and bi.\"id\"=pbp.product_id and pbp.pricebook_id=pb.\"id\" and bi.\"id\"=pr.product_id and pr.promotion_id=pro.\"id\" " +
                        //条件：可独立销售+上架产品
                        "and bi.life_status='normal' and bi.product_status='1' and  bi.is_saleable=true " +
                        "and pro.start_time<%s and pro.end_time>%s and bi.spu_id IN %s;";
            } else {
                sqlOfProducts = "SELECT DISTINCT bi.\"id\",bi.category,bi.is_giveaway,bi.life_status,bi.\"name\",bi.picture_path,bi.price,bi.product_code,bi.product_spec,bi.product_status,bi.spu_id,bi.tenant_id,bi.unit,bi.record_type,bi.remark,bi.is_multiple_unit,pbp.pricebook_price,pbp.pricebook_sellingprice,pbp.discount FROM biz_product AS bi,promotion_product AS pr,promotion AS pro,price_book AS pb,price_book_product As pbp where bi.tenant_id='%s' and pb.\"id\"='%s' and bi.\"id\"=pbp.product_id and pbp.pricebook_id=pb.\"id\" and bi.\"id\"=pr.product_id and pr.promotion_id=pro.\"id\" and bi.life_status='normal' and bi.product_status='1' and pro.start_time<%s and pro.end_time>%s and bi.spu_id IN %s;";
            }
            sqlOfProducts = String.format(sqlOfProducts, tenantId, priceBookId, currentMillis, endCompare, stringBuilder.toString(), limit, offset);
            log.info("listPromotionProductsGroupBySpuId sqlOfProducts:{}", sqlOfProducts);
            List<Map> products = objectDataService.findBySql(serviceContext.getTenantId(), sqlOfProducts);
            for (Map map : count) {
                Long size = (Long) map.get("count");
                promotionProductList.setTotal(size);
            }
            promotionProductList.setDataList(products);
        }
        promotionProductList.getDataList().forEach(o -> {
            String pictruePath = (String) o.get("picture_path");
            if (StringUtils.isNotEmpty(pictruePath)) {
                Type type = TypeUtils.parameterize(List.class, PromotionType.PicturePath.class);
                List<PromotionType.PicturePath> picturePaths = JsonUtil.fromJson(pictruePath, type);
                o.put("picture_path", picturePaths);
            }
        });
        return promotionProductList;
    }

    @Override
    public PromotionType.SpuToPromotionFlagResult batchQueryPromotionBySpuIds(ServiceContext serviceContext, PromotionType.SpuIdsArg spuIdsArg) throws Exception {
        if (CollectionUtils.isEmpty(spuIdsArg.getSpuIds()) || StringUtils.isBlank(spuIdsArg.getCustomerId())) {
            throw new ValidateException(I18N.text(ProI18NKey.MISSING_PARAMETER, StringUtils.isEmpty(spuIdsArg.getCustomerId()) ? "customerId" : "spuIds"));
        }
        String tenantId = serviceContext.getTenantId();
        List<String> hasPromotionSpuId = null;
        List<IObjectData> objectDataList = findPromotionsByCustomerId(serviceContext, spuIdsArg.getCustomerId(), true).getData();
        List<String> promotionIds = objectDataList.stream().map(IObjectData::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(promotionIds)) {
            Long currentMillis = System.currentTimeMillis();
            StringBuilder stringBuilder = new StringBuilder("(");
            spuIdsArg.getSpuIds().forEach(o -> {
                stringBuilder.append("'").append(o).append("'").append(",");
            });
            stringBuilder.replace(stringBuilder.length() - 1, stringBuilder.length(), ")");
            String promotionIdsSuffix = Joiner.on(",").join(promotionIds.stream().map(o -> Joiner.on("").join("'", o, "'")).collect(Collectors.toList()));
            String sql = "SELECT DISTINCT st.\"id\"  FROM stand_prod_unit AS st,biz_product AS bi,promotion_product AS pr,promotion AS pro where st.tenant_id='%s' and st.\"id\"=bi.spu_id and bi.\"id\"=pr.product_id and pr.promotion_id=pro.\"id\" and bi.life_status='normal' and pro.status = true and pro.is_deleted = 0 and pr.is_deleted = 0 and pro.start_time< %s and pro.end_time> %s and st.\"id\" IN %s and pro.\"id\" IN (%s);";
            sql = String.format(sql, tenantId, currentMillis, currentMillis, stringBuilder.toString(), promotionIdsSuffix);
            List<Map> spuIdMap = objectDataService.findBySql(tenantId, sql);
            hasPromotionSpuId = spuIdMap.stream().map(o -> (String) o.get("id")).collect(Collectors.toList());
        }
        PromotionType.SpuToPromotionFlagResult spuToPromotionFlagResult = new PromotionType.SpuToPromotionFlagResult();
        spuToPromotionFlagResult.setHavePromotionSpuList(hasPromotionSpuId == null ? Lists.newArrayList() : hasPromotionSpuId);
        return spuToPromotionFlagResult;
    }

    @Override
    public PromotionType.PromotionRuleResult listByCustomerId(ServiceContext serviceContext, PromotionType.CustomerIdArg customerIdArg) {
        List<IFilter> filters = new ArrayList<>();
        SearchUtil.fillFilterEq(filters, SystemConstants.Field.RecordType.apiName, PromotionRecordTypeEnum.OrderPromotion.getApiName());
        List<IObjectData> orderPromotions = findPromotionsByCustomerId(serviceContext, customerIdArg.getCustomerId(), true, filters).getData();

        List<PromotionType.DetailResult> orderRuleResults = new ArrayList<>();
        List<IObjectData> customFunctionPromotions = orderPromotions.stream().filter(o -> PromotionRuleTypeEnum.Custom.getValue().equals(o.get(PromotionConstants.Field.RuleType.apiName, String.class))).collect(Collectors.toList());
        customFunctionPromotions.forEach(promotion -> {
            PromotionType.DetailResult detailResult = new PromotionType.DetailResult();
            detailResult.setPromotion(ObjectDataDocument.of(promotion));
            orderRuleResults.add(detailResult);
        });
        List<String> promotionIds = orderPromotions.stream().filter(o -> {
            String ruleType = o.get(PromotionConstants.Field.RuleType.apiName, String.class);
            if (ruleType == null) { //ruleType字段全网刷库后，则可移除该判断逻辑
                return true;
            } else if (PromotionRuleTypeEnum.Standard.getValue().equals(ruleType)) {
                return true;
            }
            return false;
        }).map(IObjectData::getId).collect(Collectors.toList());
        Map<String, IObjectData> promotionMap = orderPromotions.stream().collect(Collectors.toMap(IObjectData::getId, Function.identity()));
        List<IFilter> iFilters = new ArrayList<>();
        List<IObjectData> promotionRules = null;
        if (CollectionUtils.isNotEmpty(promotionIds)) {
            SearchUtil.fillFilterIn(iFilters, PromotionRuleConstants.Field.Promotion.apiName, promotionIds);
            promotionRules = searchQueryIgnoreAll(serviceContext.getUser(), PromotionRuleConstants.API_NAME, iFilters, new ArrayList<>(), 0, ConfigCenter.queryPromotionRuleLimit).getData();
        }
        Map<String, List<IObjectData>> promotionRuleMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(promotionRules)) {
            promotionRuleMap = promotionRules.stream().collect(Collectors.groupingBy(o -> o.get(PromotionRuleConstants.Field.Promotion.apiName, String.class)));
        }
        List<ObjectDataDocument> allPromotionRuleList = Lists.newArrayList();
        for (Map.Entry<String, IObjectData> o : promotionMap.entrySet()) {
            if (promotionRuleMap.containsKey(o.getKey())) {
                PromotionType.DetailResult detailResult = new PromotionType.DetailResult();
                IObjectData promotionData = o.getValue();
                List<ObjectDataDocument> promotionRuleList = ObjectDataDocument.ofList(promotionRuleMap.get(o.getKey()));
                detailResult.setPromotionRules(promotionRuleList);
                detailResult.setPromotion(ObjectDataDocument.of(promotionData));
                if (PromotionUtil.isMultiGift(promotionData) && CollectionUtils.isNotEmpty(promotionRuleList)) {
                    allPromotionRuleList.addAll(promotionRuleList);
                }
                orderRuleResults.add(detailResult);
            }
        }
        Map<String, List<ObjectDataDocument>> promotionGiftMap = getPromotionGiftMapByRules(serviceContext.getUser(), allPromotionRuleList);
        orderRuleResults.forEach(detailResult -> {
            String promotionId = detailResult.getPromotion().getId();
            detailResult.setPromotionGifts(promotionGiftMap.get(promotionId));
        });
        PromotionType.PromotionRuleResult promotionRuleResult = new PromotionType.PromotionRuleResult();
        promotionRuleResult.setPromotions(orderRuleResults);
        return promotionRuleResult;
    }

    @Override
    public PromotionType.PromotionProductResult listPromotionProductsByCustomerId(ServiceContext serviceContext, PromotionType.ListProductsArg productsArg) {
        String customerId = productsArg.getCustomerId();
        Preconditions.checkNotNull(customerId, "customerId is null");

        // 查询有效的商品促销列表
        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterEq(filters, SystemConstants.Field.RecordType.apiName, PromotionRecordTypeEnum.ProductPromotion.getApiName());
        List<IObjectData> promotions = findPromotionsByCustomerId(serviceContext, customerId, true, filters).getData();
        if (CollectionUtils.isEmpty(promotions)) {
            return new PromotionType.PromotionProductResult();
        }

        // 目前商品促销不支持自定义函数，可不用过滤
        List<String> promotionIds = promotions.stream().map(IObjectData::getId).collect(Collectors.toList());
        Map<String/**promotionId**/, IObjectData> promotionMap = promotions.stream().collect(Collectors.toMap(IObjectData::getId, Function.identity()));

        // 商品促销产品列表, 按促销产品创建时间升序
        User user = serviceContext.getUser();
        List<IObjectData> promotionsProducts = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(promotionIds)) {
            filters.clear();
            SearchUtil.fillFilterIn(filters, PromotionProductConstants.Field.Promotion.apiName, promotionIds);
            List<OrderBy> orders = Lists.newArrayList();
            SearchUtil.fillOrderBy(orders, SystemConstants.Field.Id.apiName, true);

            int offset = 0;
            List<IObjectData> pagingPromotionsProducts = null;
            do {
                pagingPromotionsProducts = searchQuery(user, PromotionProductConstants.API_NAME, filters, orders, offset, ConfigCenter.queryPromotionProductLimit).getData();
                offset = offset + ConfigCenter.queryPromotionProductLimit;
                promotionsProducts.addAll(pagingPromotionsProducts);
            } while (CollectionUtils.isNotEmpty(pagingPromotionsProducts));
        }
        Map<String/**promotionId**/, List<IObjectData>> promotionId2ProductPromotionsMap = promotionsProducts.stream().collect(Collectors.groupingBy(o -> o.get(PromotionProductConstants.Field.Promotion.apiName, String.class)));

        // 调用方需要做排序，此处建议productId2ProductPromotionsMap按照LinkedHashMap输出
        Map<String/**productId**/, List<IObjectData>> productId2PromotionProductsMap = promotionsProducts.stream().collect(Collectors.groupingBy(o -> o.get(PromotionProductConstants.Field.Product.apiName, String.class), LinkedHashMap::new, Collectors.toList()));
        // 根据productId2ProductPromotionsMap分页
        int totalNumber = productId2PromotionProductsMap.keySet().size();

        Set<String> pagingPromotionIds = Sets.newHashSet();
        List<PromotionType.PromotionProduct> promotionProductTypes = Lists.newArrayList();
        Map<String, PromotionType.DetailResult> promotionDetailMap = Maps.newHashMap();

        productId2PromotionProductsMap.keySet().stream().forEach(productId -> {
            List<IObjectData> promotionProducts = productId2PromotionProductsMap.get(productId);
            List<String> productPromotionIds = promotionProducts.stream().map(o -> o.get(PromotionProductConstants.Field.Promotion.apiName, String.class)).collect(Collectors.toList());

            PromotionType.PromotionProduct promotionProductType = new PromotionType.PromotionProduct();
            promotionProductType.setProductId(productId);
            promotionProductType.setPromotionIds(productPromotionIds);
            promotionProductTypes.add(promotionProductType);

            pagingPromotionIds.addAll(productPromotionIds);
        });

        if (CollectionUtils.isNotEmpty(pagingPromotionIds)) {
            // 查询促销规则列表
            filters.clear();
            SearchUtil.fillFilterIn(filters, PromotionRuleConstants.Field.Promotion.apiName, Lists.newArrayList(pagingPromotionIds));
            List<IObjectData> promotionRules = Lists.newArrayList();
            List<IObjectData> pagingPromotionRules = null;
            int offset = 0;
            do {
                pagingPromotionRules = searchQuery(user, PromotionRuleConstants.API_NAME, filters, Lists.newArrayList(), offset, ConfigCenter.queryPromotionRuleLimit).getData();
                offset = offset + ConfigCenter.queryPromotionRuleLimit;
                promotionRules.addAll(pagingPromotionRules);
            } while (CollectionUtils.isNotEmpty(pagingPromotionRules));

            Map<String/**promotionId**/, List<IObjectData>> promotionRuleMap = promotionRules.stream().collect(Collectors.groupingBy(o -> o.get(PromotionRuleConstants.Field.Promotion.apiName, String.class)));
            List<ObjectDataDocument> allPromotionRules = Lists.newArrayList();
            for (String promotionId : pagingPromotionIds) {
                IObjectData promotionData = promotionMap.get(promotionId);
                List<ObjectDataDocument> promotionRuleDocumentList = ObjectDataDocument.ofList(promotionRuleMap.get(promotionId));

                if (PromotionUtil.isMultiGift(promotionData) && CollectionUtils.isNotEmpty(promotionRuleDocumentList)) {
                    allPromotionRules.addAll(promotionRuleDocumentList);
                }

                PromotionType.DetailResult detailResult = new PromotionType.DetailResult();
                detailResult.setPromotion(ObjectDataDocument.of(promotionData));
                detailResult.setPromotionProducts(ObjectDataDocument.ofList(promotionId2ProductPromotionsMap.get(promotionId)));
                detailResult.setPromotionRules(promotionRuleDocumentList);

                promotionDetailMap.put(promotionId, detailResult);
            }

            //根据促销规则批量查询促销赠品
            Map<String/**promotionId**/, List<ObjectDataDocument>> promotionIdGiftMap = getPromotionGiftMapByRules(user, allPromotionRules);
            promotionDetailMap.forEach((promotionId, promotionDetail) -> {
                promotionDetail.setPromotionGifts(promotionIdGiftMap.get(promotionId));
            });
        }

        PromotionType.PromotionProductResult promotionProductResult = new PromotionType.PromotionProductResult();
        promotionProductResult.setTotalNumber(totalNumber);
        promotionProductResult.setPromotionProducts(promotionProductTypes);
        promotionProductResult.setPromotions(promotionDetailMap);
        return promotionProductResult;
    }

    @Override
    public BatchGetProductQuotaByProductIdsModel.Result batchGetProductQuotaByProductIds(ServiceContext serviceContext, BatchGetProductQuotaByProductIdsModel.Arg arg) {
        return batchGetProductQuotaByProductIds(serviceContext, arg, null);
    }

    private BatchGetProductQuotaByProductIdsModel.Result batchGetProductQuotaByProductIds(ServiceContext serviceContext, BatchGetProductQuotaByProductIdsModel.Arg arg, String orderId) {
        List<BatchGetProductQuotaByProductIdsModel.PromotionProductIdArg> promotionProductIdArgs = arg.getPromotionProductIdArgs();
        Set<String> promotionIds = Sets.newHashSet();
        Set<String> productIds = Sets.newHashSet();
        List<BatchGetPromotionProductQuantity.PromotionProductArg> promotionProductArgList = Lists.newArrayList();
        Map<String, BigDecimal> toBeUsedAmountMap = Maps.newHashMap();
        Map<String, BigDecimal> toBeUsedSubTotalMap = Maps.newHashMap();
        promotionProductIdArgs.forEach(x -> {
            BatchGetPromotionProductQuantity.PromotionProductArg promotionProductArg = new BatchGetPromotionProductQuantity.PromotionProductArg();
            promotionProductArg.setPromotionId(x.getPromotionId());
            promotionProductArg.setProductId(x.getProductId());
            promotionProductArgList.add(promotionProductArg);
            productIds.add(x.getProductId());
            promotionIds.add(x.getPromotionId());
            toBeUsedAmountMap.put(x.getPromotionId() + "." + x.getProductId(), x.getAmount());
            toBeUsedSubTotalMap.put(x.getPromotionId() + "." + x.getProductId(), x.getSubTotal());
        });

        List<IFilter> filters = Lists.newArrayList();
        SearchUtil.fillFilterIn(filters, PromotionProductConstants.Field.Promotion.apiName, Lists.newArrayList(promotionIds));
        SearchUtil.fillFilterIn(filters, PromotionProductConstants.Field.Product.apiName, Lists.newArrayList(productIds));
        QueryResult<IObjectData> queryResult = searchQueryIgnoreAll(serviceContext.getUser(), PromotionProductConstants.API_NAME, filters, Lists.newArrayList(), 0, ConfigCenter.queryPromotionProductLimit);
        List<IObjectData> objectDataList = queryResult.getData();
        log.debug("promotionProductDataList:{}", JsonUtil.toJson(objectDataList));

        Map<String, BigDecimal> promotionProductQuotaMap = Maps.newHashMap();
        Map<String, BigDecimal> promotionProductAmountQuotaMap = Maps.newHashMap();
        objectDataList.forEach(promotionProduct -> {
            String promotionId = getReferenceId(promotionProduct, PromotionProductConstants.Field.Promotion.apiName);
            String productId = getReferenceId(promotionProduct, PromotionProductConstants.Field.Product.apiName);
            BigDecimal quota = promotionProduct.get(PromotionProductConstants.Field.Quota.apiName, BigDecimal.class);
            if (quota == null) {
                quota = BigDecimal.ZERO;
            }
            BigDecimal amountQuota = promotionProduct.get(PromotionProductConstants.Field.AmountQuota.apiName, BigDecimal.class);
            if (amountQuota == null) {
                amountQuota = BigDecimal.ZERO;
            }
            promotionProductQuotaMap.put(promotionId + "." + productId, quota);
            promotionProductAmountQuotaMap.put(promotionId + "." + productId, amountQuota);
        });

        // TODO: 优化点：改为仅获取指定字段
        List<IObjectData> promotions = serviceFacade.findObjectDataByIdsIgnoreAll(serviceContext.getUser().getTenantId(), Lists.newArrayList(promotionIds), PromotionConstants.API_NAME);
        Map<String, String> promotionIdConditionMap = promotions.stream().collect(Collectors.toMap(IObjectData::getId, objectData -> {
            String condition = objectData.get(Field.Condition.apiName, String.class);
            return StringUtils.isBlank(condition) ? PromotionConditionEnum.FullPiece.getValue() : condition;
        }));

        log.info("promotionProductQuotaMap:{},promotionProductAmountQuotaMap:{},amountMap:{} subTotalMap:{}", promotionProductQuotaMap, promotionProductAmountQuotaMap, toBeUsedAmountMap,
                toBeUsedSubTotalMap);
        List<BatchGetPromotionProductQuantity.PromotionProductQuantity> promotionProductQuantityLisList = salesOrderManager.getPromotionQuantity(serviceContext.getUser(), promotionProductArgList, orderId);
        log.debug("promotionProductQuantityLisList:{}", JsonUtil.toJson(promotionProductQuantityLisList));
        Map<String, BigDecimal> promotionProductUsedQuantityMap = promotionProductQuantityLisList.stream()
                .collect(Collectors.toMap(x -> x.getPromotionId() + "." + x.getProductId(), BatchGetPromotionProductQuantity.PromotionProductQuantity::getQuantity));
        Map<String, BigDecimal> promotionProductUsedSubTotalMap = promotionProductQuantityLisList.stream()
                .collect(Collectors.toMap(x -> x.getPromotionId() + "." + x.getProductId(), BatchGetPromotionProductQuantity.PromotionProductQuantity::getSubTotal));
        log.info("promotionProductQuantityMap:{},promotionProductUsedSubTotalMap:{}", promotionProductUsedQuantityMap, promotionProductUsedSubTotalMap);

        List<BatchGetProductQuotaByProductIdsModel.PromotionProductQuota> promotionProductQuotaList = promotionProductIdArgs.stream().map(promotionProductIdArg -> {
            BatchGetProductQuotaByProductIdsModel.PromotionProductQuota promotionProductQuota = new BatchGetProductQuotaByProductIdsModel.PromotionProductQuota();
            String promotionId = promotionProductIdArg.getPromotionId();
            String productId = promotionProductIdArg.getProductId();
            String key = promotionId + "." + productId;
            boolean sales = false;

            String condition = promotionIdConditionMap.get(promotionId);
            if (PromotionConditionEnum.FullPiece.getValue().equals(condition)) {
                BigDecimal quota = promotionProductQuotaMap.getOrDefault(key, BigDecimal.ZERO);
                BigDecimal usedNum = promotionProductUsedQuantityMap.getOrDefault(key, BigDecimal.ZERO);
                BigDecimal toBeUsedAmount = toBeUsedAmountMap.get(key);
                BigDecimal leftQuota = BigDecimal.valueOf(-1);//表示没限额
                if (quota.compareTo(BigDecimal.ZERO) == 0) {
                    sales = true;
                } else {
                    leftQuota = quota.subtract(usedNum);
                    if (leftQuota.compareTo(BigDecimal.ZERO) < 0) {
                        log.warn("user:{},promotionId:{},productId:{},quota:{},usedNum:{}", serviceContext.getUser(), promotionId, productId, quota, usedNum);
                    }
                    if (quota.subtract(usedNum).compareTo(toBeUsedAmount) >= 0) {
                        sales = true;
                    }
                }
                promotionProductQuota.setQuota(quota);
                promotionProductQuota.setLeftQuota(leftQuota);
            } else if (PromotionConditionEnum.FullAmount.getValue().equals(condition)) {
                BigDecimal amountQuota = promotionProductAmountQuotaMap.getOrDefault(key, BigDecimal.ZERO);
                BigDecimal usedSubtotal = promotionProductUsedSubTotalMap.getOrDefault(key, BigDecimal.ZERO);
                BigDecimal toBeUsedSubTotal = toBeUsedSubTotalMap.get(key);
                BigDecimal leftAmountQuota = null;
                if (BigDecimal.ZERO.compareTo(amountQuota) == 0) {
                    sales = true;
                } else {
                    leftAmountQuota = amountQuota.subtract(usedSubtotal);
                    if (BigDecimal.ZERO.compareTo(leftAmountQuota) > 0) {
                        log.warn("user:{},promotionId:{},productId:{},amountQuota:{},usedSubtotal:{}", serviceContext.getUser(), promotionId, productId, amountQuota, usedSubtotal);
                    }
                    if (leftAmountQuota.compareTo(toBeUsedSubTotal) >= 0) {
                        sales = true;
                    }
                }
                promotionProductQuota.setAmountQuota(amountQuota);
                promotionProductQuota.setLeftAmountQuota(leftAmountQuota);
            }
            promotionProductQuota.setCondition(condition);
            promotionProductQuota.setPromotionId(promotionId);
            promotionProductQuota.setProductId(productId);
            promotionProductQuota.setSales(sales);
            return promotionProductQuota;
        }).collect(Collectors.toList());
        BatchGetProductQuotaByProductIdsModel.Result result = new BatchGetProductQuotaByProductIdsModel.Result();
        result.setPromotionProductQuotas(promotionProductQuotaList);
        return result;
    }

    private String getReferenceId(IObjectData data, String key) {
        Object value = data.get(key);
        if (value == null) {
            return null;
        }
        if (value instanceof Map) {
            return String.valueOf(((Map) value).get(IObjectData.ID));
        }
        return String.valueOf(value);
    }

    private QueryResult<IObjectData> findPromotionsByCustomerId(ServiceContext serviceContext, String customerId, Boolean activeTime) {
        return findPromotionsByCustomerId(serviceContext, customerId, activeTime, null, null);
    }

    private QueryResult<IObjectData> findPromotionsByCustomerId(ServiceContext serviceContext, String customerId, Boolean activeTime, List<IFilter> filters) {
        return findPromotionsByCustomerId(serviceContext, customerId, activeTime, filters, null);
    }

    public QueryResult<IObjectData> findPromotionsByCustomerId(ServiceContext serviceContext, String customerId, Boolean activeTime, List<IFilter> filters, List<OrderBy> orderBys) {
        if (Objects.isNull(filters)) {
            filters = Lists.newArrayList();
        }
        if (activeTime) {
            SearchUtil.fillFilterLT(filters, PromotionConstants.Field.StartTime.apiName, System.currentTimeMillis());
            SearchUtil.fillFilterGTE(filters, PromotionConstants.Field.EndTime.apiName, System.currentTimeMillis());
        }
        SearchUtil.fillFilterEq(filters, SystemConstants.Field.LifeStatus.apiName, SystemConstants.LifeStatus.Normal.value);
        SearchUtil.fillFilterEq(filters, SystemConstants.Field.TennantID.apiName, serviceContext.getTenantId());
        SearchUtil.fillFilterEq(filters, PromotionConstants.Field.Status.apiName, true);

        List<OrderBy> orderByList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(orderBys)) {
            OrderBy idOrderBy = SearchUtil.order(SystemConstants.Field.Id.apiName, false);
            orderByList.add(idOrderBy);
        } else {
            orderByList = orderBys;
        }
        QueryResult<IObjectData> dataQueryResult = new QueryResult<>();
        List<IObjectData> filteredData = Lists.newArrayListWithCapacity(100);
        Set<String> promotionIdSet = Sets.newHashSetWithExpectedSize(100);
        IObjectDescribe customerDescribe = serviceFacade.findObject(serviceContext.getTenantId(), ObjectAPINameMapping.Account.getApiName());
        IObjectData customerData = serviceFacade.findObjectDataIgnoreRelevantTeam(serviceContext.getUser(), customerId, ObjectAPINameMapping.Account.getApiName());
        List<String> customerIdList = customerData.getOwner();
        List<String> deptList = null;
        //下游订货通都是以客户负责人作为身份查询适用部门的数据。
        if (serviceContext.getUser().isOutUser() && DhtUtil.isDhtRequest(RequestContextManager.getContext().getPeerName()) && CollectionUtils.isNotEmpty(customerIdList)) {
            deptList = serviceFacade.queryAllSuperDeptByUserId(serviceContext.getUser().getTenantId(), User.SUPPER_ADMIN_USER_ID, customerIdList.get(0));
        }
        if (!serviceContext.getUser().isOutUser() && Objects.nonNull(serviceContext.getUser().getUserId())) {
            deptList = serviceFacade.queryAllSuperDeptByUserId(serviceContext.getUser().getTenantId(), User.SUPPER_ADMIN_USER_ID, serviceContext.getUser().getUserId());
        }
        int offset = 0;
        int limit = ConfigCenter.queryPromotionPageSize;
        int size;
        do {
            QueryResult<IObjectData> result = searchQueryIgnoreAll(serviceContext.getUser(), PromotionConstants.API_NAME, filters, orderByList, offset, limit);
            List<IObjectData> dataList = result.getData();
            if (CollectionUtils.isEmpty(dataList)) {
                break;
            }
            size = dataList.size();
            offset += limit;
            log.info("findPromotionsByCustomerId tenantId:{},peerName{},user info:{} ", serviceContext.getUser().getTenantId(), RequestContextManager.getContext().getPeerName(), serviceContext.getUser());
            for (IObjectData objectData : dataList) {
                //取适用客户和适用部门的交集数据
                if (CollectionUtils.isNotEmpty(deptList)) {
                    objectData = batchValidateDept(objectData, deptList);
                }

                if (null == objectData) {
                    continue;
                }
                if (validatePromotion(customerDescribe, customerData, objectData)) {
                    String promotionId = objectData.getId();
                    if (!promotionIdSet.contains(promotionId)) {
                        filteredData.add(objectData);
                        promotionIdSet.add(promotionId);
                        if (filteredData.size() >= ConfigCenter.queryPromotionLimit) {
                            log.warn("find promotions by customerId size more than 500, user:{},customerId:{},activeTime:{}", serviceContext.getUser(), customerId, activeTime);
                            dataQueryResult.setData(filteredData);
                            return dataQueryResult;
                        }
                    }
                }
            }
        } while (size == limit);
        dataQueryResult.setData(filteredData);
        return dataQueryResult;
    }

    private Map<String, List<ObjectDataDocument>> getPromotionGiftMapByRules(User user, List<ObjectDataDocument> promotionRules) {
        List<IObjectData> promotionGiftList = promotionManager.getPromotionGiftByRules(user, promotionRules, true);
        return ObjectDataDocument.ofList(promotionGiftList).stream().collect(Collectors.groupingBy(x -> ObjectDataExt.of(x).get(PromotionGiftConstants.Field.Promotion.apiName, String.class)));
    }

    public QueryResult<IObjectData> searchQuery(User user, String objectApiName, List<IFilter> filters, List<OrderBy> orders, int offset, int limit) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setOffset(offset);
        searchTemplateQuery.setLimit(limit);
        searchTemplateQuery.setFilters(filters);
        searchTemplateQuery.setOrders(orders);
        searchTemplateQuery.setWheres(Lists.newArrayList());
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, objectApiName, searchTemplateQuery);
        return queryResult;
    }

    public QueryResult<IObjectData> searchQueryIgnoreAll(User user, String objectApiName, List<IFilter> filters, List<OrderBy> orders, int offset, int limit) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setOffset(offset);
        searchTemplateQuery.setLimit(limit);
        searchTemplateQuery.setFilters(filters);
        searchTemplateQuery.setOrders(orders);
        searchTemplateQuery.setWheres(Lists.newArrayList());
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQueryIgnoreAll(user, objectApiName, searchTemplateQuery);
        return queryResult;
    }

    private boolean validatePromotion(IObjectDescribe customerDescribe, IObjectData customerObjData, IObjectData promotionData) {
        // 根据当前客户的适用范围过滤符合条件的促销表
        String customerRange = promotionData.get("customer_range", String.class);
        return CustomerRangeManager.validateCustomerRange(customerDescribe, customerObjData, customerRange);
    }

    /**
     * 广告按适用部过滤数据
     *
     * @param iObjectData
     * @param departList
     **/
    private IObjectData batchValidateDept(IObjectData iObjectData, List<String> departList) {
        Object deptRangeObj = iObjectData.get("dept_range");
        if (Objects.isNull(deptRangeObj)) {
            return null;
        }
        List<String> deptRange = (List<String>) deptRangeObj;

        if (CollectionUtils.isEmpty(deptRange)) {
            return null;
        }

        if (deptRange.contains("999999")) {
            return iObjectData;
        }

        for (String dept : deptRange) {
            if (departList.contains(dept)) {
                return iObjectData;
            }
        }
        return null;
    }

    private Set<String> checkDisplayName(String tenantId) {
        try {
            Set<String> existDisplayNames = Sets.newHashSet();
            List<String> existPromotionApiNames = objectDescribeService.checkDisplayNameExist(tenantId, PromotionConstants.DISPLAY_NAME, "CRM");
            existPromotionApiNames.forEach(x -> {
                if (!PromotionConstants.API_NAME.equals(x)) {
                    existDisplayNames.add(PromotionConstants.DISPLAY_NAME);
                }
            });
            List<String> existPromotionProductApiNames = objectDescribeService.checkDisplayNameExist(tenantId, PromotionProductConstants.DISPLAY_NAME, "CRM");
            existPromotionProductApiNames.forEach(x -> {
                if (!PromotionProductConstants.API_NAME.equals(x)) {
                    existDisplayNames.add(PromotionProductConstants.DISPLAY_NAME);
                }
            });

            List<String> existPromotionRuleApiNames = objectDescribeService.checkDisplayNameExist(tenantId, PromotionRuleConstants.DISPLAY_NAME, "CRM");
            existPromotionRuleApiNames.forEach(x -> {
                if (!PromotionRuleConstants.API_NAME.equals(x)) {
                    existDisplayNames.add(PromotionRuleConstants.DISPLAY_NAME);
                }
            });

            List<String> existAdvertisementApiNames = objectDescribeService.checkDisplayNameExist(tenantId, AdvertisementConstants.DISPLAY_NAME, "CRM");
            existAdvertisementApiNames.forEach(x -> {
                if (!AdvertisementConstants.API_NAME.equals(x)) {
                    existDisplayNames.add(AdvertisementConstants.API_NAME);
                }
            });
            log.info("checkDisplayName tenantId:{},Result:{}", tenantId, existDisplayNames);
            return existDisplayNames;
        } catch (MetadataServiceException e) {
            log.warn("checkDisplayName error,tenantId:{}", tenantId, e);
            throw new PromotionBusinessException(() -> e.getErrorCode().getCode(), e.getMessage());
        }
    }

    @Override
    public PromotionType.PromotionUsed isPromotionUsed(ServiceContext serviceContext, PromotionType.IdModel idModel) {
        String promotionId = idModel.getId();
        if (StringUtils.isEmpty(promotionId)) {
            throw new ValidateException("id is empty");
        }
        IFilter filter = new Filter();
        filter.setFieldValues(Lists.newArrayList(promotionId));
        filter.setFieldName("promotion_id");
        filter.setOperator(Operator.EQ);
        User user = User.builder().tenantId(serviceContext.getTenantId()).userId(User.SUPPER_ADMIN_USER_ID).build();
        QueryResult<IObjectData> orderQueryResult = searchQuery(user, Utils.SALES_ORDER_API_NAME, Lists.newArrayList(filter), Lists.newArrayList(), 0, 10);
        boolean used = true;
        if (CollectionUtils.isEmpty(orderQueryResult.getData())) {
            QueryResult<IObjectData> orderProductQueryResult = searchQuery(user, Utils.SALES_ORDER_PRODUCT_API_NAME, Lists.newArrayList(filter), Lists.newArrayList(), 0, 10);
            used = CollectionUtils.isNotEmpty(orderProductQueryResult.getData());
        }
        PromotionType.PromotionUsed promotionUsed = new PromotionType.PromotionUsed();
        promotionUsed.setUsed(used);
        return promotionUsed;
    }

    @Override
    public ListPromotionProductModel.Result listPromotionProduct(ServiceContext serviceContext, ListPromotionProductModel.Arg arg) {
        String customerId = arg.getCustomerId();
        if (StringUtils.isEmpty(customerId)) {
            throw new ValidateException("customerId is null");
        }
        List<IFilter> filters = arg.toFilters(arg.getPromotionFilters());
        QueryResult<IObjectData> queryResult = findPromotionsByCustomerId(serviceContext, customerId, true, filters);
        List<IObjectData> promotionList = queryResult.getData();
        Set<String> promotionIds = Sets.newHashSet();
        if (CollectionUtils.isNotEmpty(promotionList)) {
            promotionList.forEach(x -> promotionIds.add(x.getId()));
        }
        ListPromotionProductModel.Result result = new ListPromotionProductModel.Result();
        if (CollectionUtils.isEmpty(promotionIds)) {
            return result;
        }
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(arg.getLimit());
        searchTemplateQuery.setOffset(arg.getOffset());
        List<IFilter> promotionProductFilters = arg.toFilters(arg.getPromotionProductFilters());
        if (Objects.isNull(promotionProductFilters)) {
            promotionProductFilters = Lists.newArrayList();
        }
        IFilter promotionFilter = new Filter();
        promotionFilter.setFieldName(PromotionProductConstants.Field.Promotion.apiName);
        promotionFilter.setOperator(Operator.IN);
        promotionFilter.setFieldValues(Lists.newArrayList(promotionIds));
        promotionProductFilters.add(promotionFilter);
        searchTemplateQuery.setFilters(promotionProductFilters);
        searchTemplateQuery.setOrders(Lists.newArrayList(SearchUtil.order(SystemConstants.Field.Id.apiName, false)));
        QueryResult<IObjectData> promotionProductQueryResult = serviceFacade.findBySearchQuery(serviceContext.getUser(), PromotionProductConstants.API_NAME, searchTemplateQuery);
        result.setProductIds(promotionProductQueryResult.getData().stream().map(x -> ObjectDataExt.of(x).get(PromotionProductConstants.Field.Product.apiName, String.class, "")).filter(StringUtils::isNotEmpty).collect(Collectors.toSet()));
        result.setTotal(promotionProductQueryResult.getTotalNumber());
        return result;
    }

    @Override
    public AddFieldForOpenMultiOrgModel.Result addFieldForOpenMultiOrg(ServiceContext serviceContext, AddFieldForOpenMultiOrgModel.Arg arg) {
        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return new AddFieldForOpenMultiOrgModel.Result();
        }

        for (int i = 0; i < arg.getTenantIds().size(); i++) {
            log.info("addFieldForOpenMultiOrg  i[{}], size[{}]", i, arg.getTenantIds().size());
            User user = new User(arg.getTenantIds().get(i), "-10000");
            promotionManager.addFieldForOpenMultiOrg(user);
        }

        return new AddFieldForOpenMultiOrgModel.Result();
    }

    @Override
    public AddFieldPromotionDiscountForOrderProductModel.Result addFieldPromotionDiscountForOrderProduct(ServiceContext serviceContext, AddFieldPromotionDiscountForOrderProductModel.Arg arg) {
        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return new AddFieldPromotionDiscountForOrderProductModel.Result();
        }

        for (int i = 0; i < arg.getTenantIds().size(); i++) {
            String tenantId = arg.getTenantIds().get(i);
            log.info("addFieldPromotionDiscountForOrderProduct  i[{}], size[{}]", i, arg.getTenantIds().size());
            proOrderProductManager.addPromotionDiscountField(tenantId, true);
        }
        log.info("addFieldPromotionDiscountForOrderProduct success, arg[{}]", arg);

        return new AddFieldPromotionDiscountForOrderProductModel.Result();
    }

    @Override
    public UpdatePromotionDiscountForOrderProductModel.Result updatePromotionDiscountForOrderProductTemp(ServiceContext serviceContext, UpdatePromotionDiscountForOrderProductModel.Arg arg) {
        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return new UpdatePromotionDiscountForOrderProductModel.Result();
        }

        for (int i = 0; i < arg.getTenantIds().size(); i++) {
            String tenantId = arg.getTenantIds().get(i);
            log.info("updatePromotionDiscountForOrderProductTemp  i[{}], size[{}]", i, arg.getTenantIds().size());
            proOrderProductManager.updatePromotionDiscountField(tenantId);
        }

        return new UpdatePromotionDiscountForOrderProductModel.Result();
    }

    @Override
    public UpdatePromotionDiscountForOrderProductModel.Result updatePromotionDiscountIsExtend(ServiceContext serviceContext, UpdatePromotionDiscountForOrderProductModel.Arg arg) {
        if (CollectionUtils.isEmpty(arg.getTenantIds())) {
            return new UpdatePromotionDiscountForOrderProductModel.Result();
        }

        for (int i = 0; i < arg.getTenantIds().size(); i++) {
            String tenantId = arg.getTenantIds().get(i);
            log.info("updatePromotionDiscountIsExtend  i[{}], size[{}], label[{}]", i, arg.getTenantIds().size(), arg.getLabel());
            proOrderProductManager.updatePromotionDiscountIsExtend(tenantId);
        }
        log.info("updatePromotionDiscountIsExtend success, label[{}]", arg.getLabel());

        return new UpdatePromotionDiscountForOrderProductModel.Result();
    }

    @Override
    public PromotionType.ValidatePromotionsResult validatePromotionsForSalesOrder(ServiceContext serviceContext, PromotionType.ValidatePromotionsArg arg) {
        PromotionType.ValidatePromotionsResult validateResult = new PromotionType.ValidatePromotionsResult();
        validateResult.setStatus(true);
        return validateResult;
    }

    private int getDecimalPlaces(IObjectDescribe describe, String fieldApiName) {
        //默认2位小数
        int decimalPlaces = 2;
        IFieldDescribe fieldDescribe = describe.getFieldDescribe(fieldApiName);
        return Objects.isNull(fieldDescribe) ? decimalPlaces : fieldDescribe.get("decimal_places", Integer.class);
    }

}
