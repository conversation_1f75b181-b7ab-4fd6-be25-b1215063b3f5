package com.facishare.crm.promotion.predefine.service;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import java.util.Map;

/**
 * @author: dongzhb
 * @date: 2019/6/5
 * @Description:
 */
public interface PromotionRecordTypeService {
    /**
     * 执行检查规则
     * @param objectData  主对象
     * @param iObjectData  包含对象
     * */
    Object executeCheckRule(IObjectData objectData, IObjectData iObjectData);

    /**
     * 更改对象值（包含默认值）
     * @param objectData  促销对象
     * @param iObjectData  包含对象
     * */
    void  updateObjectDataFiledValue(User user,IObjectData objectData, IObjectData iObjectData, Map<String, String> giftProductMap);
}
