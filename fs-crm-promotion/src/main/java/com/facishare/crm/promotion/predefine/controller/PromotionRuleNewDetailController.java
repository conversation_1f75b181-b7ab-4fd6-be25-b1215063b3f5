package com.facishare.crm.promotion.predefine.controller;

import com.facishare.crm.promotion.util.PromotionUtil;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardNewDetailController;
import com.google.common.collect.Sets;

public class PromotionRuleNewDetailController extends StandardNewDetailController {
    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        PromotionUtil.removeButtonFromDetail(arg, result, Sets.newHashSet(ObjectAction.UPDATE.getActionCode(), ObjectAction.CLONE.getActionCode()));
        return result;
    }
}
