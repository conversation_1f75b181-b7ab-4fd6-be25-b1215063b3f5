package com.facishare.crm.promotion.predefine.service;

import com.facishare.crm.promotion.predefine.service.dto.AdvertisementModel;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ServiceContext;

/**
 * @author: dongzhb
 * @date: 2019/9/23
 * @Description: 广告
 */
@ServiceModule("advertisement")
public interface AdvertisementService {

    /**
     * 获取广告列表
     * @param serviceContext
     * @param arg
     * */
    @ServiceMethod("listAdvertisement")
    public AdvertisementModel.ListAdvertisementResult listAdvertisement(ServiceContext serviceContext, AdvertisementModel.Arg arg);
}
