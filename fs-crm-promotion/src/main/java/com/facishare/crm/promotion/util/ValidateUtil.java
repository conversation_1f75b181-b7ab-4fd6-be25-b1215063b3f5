package com.facishare.crm.promotion.util;

import com.facishare.crmcommon.constants.CommonProductConstants;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.promotion.constants.ProI18NKey;
import com.facishare.crm.promotion.constants.PromotionConstants;
import com.facishare.crm.promotion.constants.PromotionGiftConstants;
import com.facishare.crm.promotion.constants.PromotionProductConstants;
import com.facishare.crm.promotion.constants.PromotionRuleConstants;
import com.facishare.crm.promotion.enums.GiftMethodEnum;
import com.facishare.crm.promotion.enums.GiftTypeEnum;
import com.facishare.crm.promotion.enums.PromotionConditionEnum;
import com.facishare.crm.promotion.enums.PromotionProductRecordTypeEnum;
import com.facishare.crm.promotion.enums.PromotionRecordTypeEnum;
import com.facishare.crm.promotion.enums.PromotionRuleTypeEnum;
import com.facishare.crm.promotion.enums.PromotionTypeEnum;
import com.facishare.crm.promotion.enums.RuleMethodEnum;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/9/26
 */
@Slf4j
public class ValidateUtil {
    private static Set<String> RULE_NULL_FIELDS_FOR_PRODUCT_PROMOTION = Sets.newHashSet();
    private static Set<String> RULE_NULL_FIELDS_FOR_ORDER_PROMOTION = Sets.newHashSet();
    private static Set<String> RULE_NULL_FIELDS_FOR_COMBINE_PROMOTION = Sets.newHashSet();
    private static Map<String, Set<String>> TYPE_TO_NULL_FIELDS_OF_PROMOTION_RULE_MAP = Maps.newHashMap();
    private static Map<String, Set<String>> TYPE_TO_NULL_FIELDS_OF_PROMOTION_PRODUCT_MAP = Maps.newHashMap();


    public static List<IObjectData> validateGetPromotionGift(IObjectData objectData, Map<String, List<IObjectData>> detailObjectData, ServiceFacade serviceFacade) {
        List<IObjectData> promotionGiftDataResultList = Lists.newArrayList();
        if (!PromotionUtil.isMultiGift(objectData)) {
            return promotionGiftDataResultList;
        }
        String promotionType = objectData.get(PromotionConstants.Field.Type.apiName, String.class);
        List<IObjectData> promotionRuleDataList = detailObjectData.get(PromotionRuleConstants.API_NAME);
        if (CollectionUtils.isEmpty(promotionRuleDataList)) {
            return promotionGiftDataResultList;
        }
        List<IObjectData> promotionProductDataList = detailObjectData.getOrDefault(PromotionProductConstants.API_NAME, Lists.newArrayList());
        Set<String> productIdsOfPromotionProduct = promotionProductDataList.stream().map(x -> x.get(PromotionProductConstants.Field.Product.apiName, String.class)).collect(Collectors.toSet());
        String promotionId = objectData.getId();
        promotionRuleDataList.forEach(promotionRule -> {
            String promotionRuleId = promotionRule.getId();
            if (StringUtils.isEmpty(promotionRuleId)) {
                promotionRuleId = serviceFacade.generateId();
                promotionRule.setId(promotionRuleId);
            }
            String promotionRuleGiftMethod = promotionRule.get(PromotionRuleConstants.Field.GiftMethod.apiName, String.class);
            List promotionGiftList = promotionRule.get(PromotionGiftConstants.PROMOTION_GIFT_LIST, List.class);
            ObjectDataDocument.of(promotionRule).remove(PromotionGiftConstants.PROMOTION_GIFT_LIST);
            if (CollectionUtils.isEmpty(promotionGiftList)) {
                log.warn("promotionGiftList:{}", promotionGiftList);
                throw new ValidateException(I18N.text(ProI18NKey.PROMOTION_RULE_PARAMS_ERROR));
            }
            Integer optionGiftNum = promotionRule.get(PromotionRuleConstants.Field.OptionGiftNum.apiName, Integer.class);
            if (Objects.isNull(optionGiftNum) || optionGiftNum <= 0) {
                log.warn("promotionRule:{},optionGiftNum:{} is error", promotionRule, optionGiftNum);
                throw new ValidateException(I18N.text(ProI18NKey.PROMOTION_RULE_PARAMS_ERROR));
            }
            if (GiftMethodEnum.SINGLE_GIFT.getValue().equals(promotionRuleGiftMethod)) {
                if (promotionGiftList.size() > 1) {
                    log.warn("single gift optionGiftNum:{},promotionGiftList:{}", optionGiftNum, promotionGiftList);
                    throw new ValidateException(I18N.text(ProI18NKey.PROMOTION_RULE_PARAMS_ERROR));
                }
                promotionRule.set(PromotionRuleConstants.Field.OptionGiftNum.apiName, 1);
            } else if (GiftMethodEnum.MULTI_GIFT.getValue().equals(promotionRuleGiftMethod)) {
                if (promotionGiftList.size() < optionGiftNum) {
                    log.warn("multi gift optionGiftNum:{},promotionGiftList:{}", optionGiftNum, promotionGiftList);
                    throw new ValidateException(I18N.text(ProI18NKey.PROMOTION_RULE_PARAMS_ERROR));
                }
            }
            Set<String> giftProductIdSet = Sets.newHashSet();
            promotionGiftList.forEach(promotionGift -> {
                IObjectData promotionGiftData = ObjectDataExt.of((Map<String, Object>) promotionGift).getObjectData();
                promotionGiftData.set(PromotionGiftConstants.Field.Promotion.apiName, promotionId);
                promotionGiftData.set(PromotionGiftConstants.Field.PromotionRule.apiName, promotionRule.getId());
                BigDecimal giftProductNum = promotionGiftData.get(PromotionGiftConstants.Field.GiftProductNum.apiName, BigDecimal.class);
                if (Objects.isNull(giftProductNum) || giftProductNum.compareTo(BigDecimal.ZERO) <= 0) {
                    log.warn("promotionGiftData giftProductNum:{} error", giftProductNum);
                    throw new ValidateException(I18N.text(ProI18NKey.PROMOTION_RULE_PARAMS_ERROR));
                }
                String giftType = promotionGiftData.get(PromotionGiftConstants.Field.GiftType.apiName, String.class);
                if (GiftTypeEnum.Self.getValue().equals(giftType)) {
                    promotionGiftData.set(PromotionGiftConstants.Field.GiftProduct.apiName, null);
                    PromotionTypeEnum promotionTypeEnum = PromotionTypeEnum.get(promotionType);
                    if (PromotionTypeEnum.NumberReachedGift != promotionTypeEnum) {
                        log.warn("promotionGiftData param error,promotionType:{}", promotionType);
                        throw new ValidateException(I18N.text(ProI18NKey.PROMOTION_RULE_PARAMS_ERROR));
                    }
                    if (giftProductIdSet.contains("self")) {
                        log.warn("promotionGiftData selfGift error,giftProductIdSet:{}", giftProductIdSet);
                        throw new ValidateException(I18N.text(ProI18NKey.PROMOTION_RULE_PARAMS_ERROR));
                    } else {
                        giftProductIdSet.add("self");
                        productIdsOfPromotionProduct.forEach(x -> {
                            if (giftProductIdSet.contains(x)) {
                                log.warn("赠品包含促销产品,且还送了本品,giftProductIdSet:{},productIdOfPromotionProduct:{}", giftProductIdSet, x);
                                throw new ValidateException(I18N.text(ProI18NKey.PROMOTION_GIFT_NOT_DUPLICATE));
                            }
                        });
                        giftProductIdSet.addAll(productIdsOfPromotionProduct);
                    }
                } else if (GiftTypeEnum.NormalProduct.getValue().equals(giftType)) {
                    String giftProductId = promotionGiftData.get(PromotionGiftConstants.Field.GiftProduct.apiName, String.class);
                    if (StringUtils.isEmpty(giftProductId) || giftProductIdSet.contains(giftProductId)) {
                        log.warn("promotionGiftData giftProductId:{} error,giftProductIdSet:{}", giftProductId, giftProductIdSet);
                        throw new ValidateException(I18N.text(ProI18NKey.PROMOTION_GIFT_NOT_DUPLICATE));
                    }
                    giftProductIdSet.add(giftProductId);
                } else {
                    log.warn("promotionGiftData giftType:{} error", giftType);
                    throw new ValidateException(I18N.text(ProI18NKey.PROMOTION_RULE_PARAMS_ERROR));
                }
                String promotionGiftId = promotionGiftData.getId();
                if (StringUtils.isEmpty(promotionGiftId)) {
                    promotionGiftData.setId(serviceFacade.generateId());
                }
                promotionGiftDataResultList.add(promotionGiftData);
            });
        });
        return promotionGiftDataResultList;
    }

    public static void resetAndValidateField(User user, IObjectData objectData, Map<String, List<IObjectData>> detailObjectData, Map<String, IObjectDescribe> objectDescribes, ServiceFacade serviceFacade) {
        String recordType = objectData.getRecordType();
        IObjectDescribe objectDescribe = objectDescribes.get(PromotionConstants.API_NAME);
        String type = objectData.get(PromotionConstants.Field.Type.apiName, String.class);
        List<IObjectData> promotionRuleDataList = detailObjectData.get(PromotionRuleConstants.API_NAME);
        List<IObjectData> promotionProductDataList = detailObjectData.get(PromotionProductConstants.API_NAME);
        IObjectDescribe promotionRuleDescribe = objectDescribes.get(PromotionRuleConstants.API_NAME);
        if (PromotionRecordTypeEnum.OrderPromotion.getApiName().equals(recordType)) {
            String ruleType = objectData.get(PromotionConstants.Field.RuleType.apiName, String.class);
            detailObjectData.remove(PromotionProductConstants.API_NAME);
            if (PromotionRuleTypeEnum.Custom.getValue().equals(ruleType)) {
                detailObjectData.remove(PromotionRuleConstants.API_NAME);
                objectData.set(PromotionConstants.Field.Type.apiName, PromotionTypeEnum.OrderDiscount.getValue());
                objectData.set(PromotionConstants.Field.Condition.apiName, PromotionConditionEnum.FullAmount.getValue());
                objectData.set(PromotionConstants.Field.IsStairPromotion.apiName, true);
                objectData.set(PromotionConstants.Field.RuleMethod.apiName, null);
                String function = objectData.get(PromotionConstants.Field.CustomFunction.apiName, String.class);
                if (StringUtils.isEmpty(function)) {
                    throw new ValidateException(I18N.text(ProI18NKey.MISSING_PARAMETER, PromotionConstants.Field.CustomFunction.label));
                }
            } else {
                //校验type
                if (!PromotionTypeEnum.isOrderPromotion(type)) {
                    throw new ValidateException(I18N.text(ProI18NKey.PROMOTION_FIELD_ERROR, ObjectDescribeExt.of(objectDescribe).getFieldLabelByName(PromotionConstants.Field.Type.apiName)));
                }
                if (CollectionUtils.isEmpty(promotionRuleDataList)) {
                    throw new ValidateException(I18N.text(ProI18NKey.PROMOTION_RULE_NOT_NULL));
                }
                objectData.set(PromotionConstants.Field.CustomFunction.apiName, null);
                ValidateUtil.resetPromotionRuleField(objectData, promotionRuleDataList);
                ValidateUtil.checkPromotionRuleNotDuplicate(objectDescribe, objectData, promotionRuleDataList);
                ValidateUtil.validatePromotionRuleField(objectData, promotionRuleDescribe, promotionRuleDataList);
            }
        } else if (PromotionRecordTypeEnum.ProductPromotion.getApiName().equals(recordType)) {
            //校验type
            if (!PromotionTypeEnum.isProductPromotion(type)) {
                throw new ValidateException(I18N.text(ProI18NKey.PROMOTION_FIELD_ERROR, ObjectDescribeExt.of(objectDescribe).getFieldLabelByName(PromotionConstants.Field.Type.apiName)));
            }
            String ruleMethod = objectData.get(PromotionConstants.Field.RuleMethod.apiName, String.class);
            if (StringUtils.isEmpty(ruleMethod)) {
                throw new ValidateException(I18N.text(ProI18NKey.PROMOTION_RULE_METHOD_NOT_EXIST, ObjectDescribeExt.of(objectDescribe).getFieldLabelByName(PromotionConstants.Field.RuleMethod.apiName)));
            }
            Boolean isStairPromotion = objectData.get(PromotionConstants.Field.IsStairPromotion.apiName, Boolean.class);
            if (Objects.isNull(isStairPromotion)) {
                throw new ValidateException(I18N.text(ProI18NKey.PROMOTION_IS_STAIR_PROMOTION, ObjectDescribeExt.of(objectDescribe).getFieldLabelByName(PromotionConstants.Field.IsStairPromotion.apiName)));
            }
            String ruleType = objectData.get(PromotionConstants.Field.RuleType.apiName, String.class);
            if (StringUtils.isEmpty(ruleType) || PromotionRuleTypeEnum.Standard.getValue().equals(ruleType)) {
                String condition = objectData.get(PromotionConstants.Field.Condition.apiName, String.class);
                if (StringUtils.isEmpty(condition)) {
                    throw new ValidateException(I18N.text(ProI18NKey.PROMOTION_CONDITION_NOT_EXIST, ObjectDescribeExt.of(objectDescribe).getFieldLabelByName(PromotionConstants.Field.Condition.apiName)));
                }
                if (CollectionUtils.isEmpty(promotionProductDataList)) {
                    throw new ValidateException(I18N.text(ProI18NKey.PROMOTION_PRODUCT_NOT_NULL));
                }
                if (RuleMethodEnum.UnifiedSetting.getValue().equals(ruleMethod)) {
                    if (CollectionUtils.isEmpty(promotionRuleDataList)) {
                        throw new ValidateException(I18N.text(ProI18NKey.PROMOTION_RULE_LIST_IS_EMPTY));
                    }
                    ValidateUtil.checkPromotionRuleNotDuplicate(objectDescribe, objectData, promotionRuleDataList);
                    ValidateUtil.validatePromotionRuleField(objectData, promotionRuleDescribe, promotionRuleDataList);
                    ValidateUtil.resetPromotionRuleField(objectData, promotionRuleDataList);
                } else if (RuleMethodEnum.SingleSetting.getValue().equals(ruleMethod)) {
                    detailObjectData.remove(PromotionRuleConstants.API_NAME);
                    ValidateUtil.checkSingleSettingPromotionProductField(objectData, objectDescribes.get(PromotionProductConstants.API_NAME), promotionProductDataList);
                    ValidateUtil.resetSingleSettingPromotionProductField(objectData, promotionProductDataList);
                    //设置ruleDescription字段
                    ValidateUtil.setRuleDescriptionField(user, objectData, detailObjectData, serviceFacade);
                }
            }
        } else if (PromotionRecordTypeEnum.CombinePromotion.getApiName().equals(recordType)) {
            //校验type
            if (!PromotionTypeEnum.isCombinePromotion(type)) {
                throw new ValidateException(I18N.text(ProI18NKey.PROMOTION_FIELD_ERROR, ObjectDescribeExt.of(objectDescribe).getFieldLabelByName(PromotionConstants.Field.Type.apiName)));
            }
            objectData.set(PromotionConstants.Field.Condition.apiName, PromotionConditionEnum.FullPiece.getValue());
            String ruleType = objectData.get(PromotionConstants.Field.RuleType.apiName, String.class);
            //ruleType为空，当标准规则处理（兼容老数据）
            if (!StringUtils.equals(PromotionRuleTypeEnum.Custom.getValue(), ruleType)) {
                if (CollectionUtils.isEmpty(promotionRuleDataList)) {
                    throw new ValidateException(I18N.text(ProI18NKey.PROMOTION_RULE_NOT_NULL));
                } else if (promotionRuleDataList.size() > 1) {
                    throw new ValidateException(I18N.text(ProI18NKey.COMBINE_PROMOTION_RULE_UNIQUE));
                }
                ValidateUtil.validatePromotionRuleField(objectData, promotionRuleDescribe, promotionRuleDataList);
                ValidateUtil.resetPromotionRuleField(objectData, promotionRuleDataList);
                //校验组合促销的促销产品
                IObjectData combinePromotionRuleData = promotionRuleDataList.get(0);
                ValidateUtil.validateCombinePromotionProductField(user, type, promotionProductDataList, serviceFacade);
                ValidateUtil.resetCombineProductFieldAndComputeRule(type, combinePromotionRuleData, promotionProductDataList);
            } else {
                detailObjectData.clear();
            }
        } else {
            throw new ValidateException(I18N.text(ProI18NKey.PROMOTION_RECORD_TYPE_NOT_EXIST));
        }
    }


    public static void resetPromotionRuleField(IObjectData promotionData, List<IObjectData> promotionRuleDataList) {
        String promotionRecordType = promotionData.getRecordType();
        String promotionType = promotionData.get(PromotionConstants.Field.Type.apiName, String.class);
        String condition = promotionData.get(PromotionConstants.Field.Condition.apiName, String.class);
        promotionRuleDataList.forEach(promotionRuleData -> {
            if (PromotionRecordTypeEnum.OrderPromotion.getApiName().equals(promotionRecordType)) {
                String conditionNullFieldName = PromotionConditionEnum.FullAmount.getValue().equals(condition) ? PromotionRuleConstants.Field.OrderProductNum.apiName : PromotionRuleConstants.Field.OrderMoney.apiName;
                promotionRuleData.set(conditionNullFieldName, null);
                /*if (PromotionTypeEnum.OrderMoneyReachedGift.getValue().equals(promotionType)) {
                    promotionRuleData.set(PromotionRuleConstants.Field.GiftType.apiName, GiftTypeEnum.NormalProduct.getValue());
                }*/
                RULE_NULL_FIELDS_FOR_ORDER_PROMOTION.forEach(x -> promotionRuleData.set(x, null));
            } else if (PromotionRecordTypeEnum.ProductPromotion.getApiName().equals(promotionRecordType)) {
                String conditionNullFieldName = PromotionConditionEnum.FullPiece.getValue().equals(condition) ? PromotionRuleConstants.Field.PurchaseAmount.apiName : PromotionRuleConstants.Field.PurchaseNum.apiName;
                promotionRuleData.set(conditionNullFieldName, null);
                RULE_NULL_FIELDS_FOR_PRODUCT_PROMOTION.forEach(x -> promotionRuleData.set(x, null));
            } else if (PromotionRecordTypeEnum.CombinePromotion.getApiName().equals(promotionRecordType)) {
                promotionData.set(PromotionConstants.Field.Condition.apiName, PromotionConditionEnum.FullPiece.getValue());
                RULE_NULL_FIELDS_FOR_COMBINE_PROMOTION.forEach(x -> promotionRuleData.set(x, null));
            }
            TYPE_TO_NULL_FIELDS_OF_PROMOTION_RULE_MAP.getOrDefault(promotionType, Sets.newHashSet()).forEach(x -> promotionRuleData.set(x, null));
        });
    }

    public static void resetCombineProductFieldAndComputeRule(String promotionType, IObjectData promotionRuleData, List<IObjectData> promotionProductDataList) {
        //分子
        BigDecimal molecule = BigDecimal.ZERO;
        //分母
        BigDecimal denominator = BigDecimal.ZERO;
        //
        BigDecimal tempDerateOrFixedMoney = BigDecimal.ZERO;
        for (IObjectData promotionProductData : promotionProductDataList) {
            promotionProductData.setRecordType(PromotionProductRecordTypeEnum.CombinePromotion.getApiName());
            TYPE_TO_NULL_FIELDS_OF_PROMOTION_PRODUCT_MAP.getOrDefault(promotionType, Sets.newHashSet()).forEach(x -> promotionProductData.set(x, null));
            //计算组合促销规则
            if (PromotionTypeEnum.CombineDiscount.getValue().equals(promotionType)) {
                BigDecimal combineDiscount = promotionProductData.get(PromotionProductConstants.Field.PriceDiscount.apiName, BigDecimal.class);
                Integer productNum = promotionProductData.get(PromotionProductConstants.Field.CombineAmount.apiName, Integer.class);
                BigDecimal productPrice = getQuoteCurrencyField(promotionProductData, PromotionProductConstants.Field.Price.apiName);
                BigDecimal productTotalPrice = productPrice.multiply(BigDecimal.valueOf(productNum));
                molecule = molecule.add(productTotalPrice.multiply(combineDiscount));
                denominator = denominator.add(productTotalPrice);
                BigDecimal tempDiscount = molecule.divide(denominator, BigDecimal.ROUND_HALF_EVEN);
                promotionRuleData.set(PromotionRuleConstants.Field.CombineDiscount.apiName, tempDiscount);
            } else if (PromotionTypeEnum.CombineDerateMoney.getValue().equals(promotionType)) {
                BigDecimal combineDerateMoney = promotionProductData.get(PromotionProductConstants.Field.DerateMoney.apiName, BigDecimal.class);
                Integer combineAmount = promotionProductData.get(PromotionProductConstants.Field.CombineAmount.apiName, Integer.class);
                tempDerateOrFixedMoney = tempDerateOrFixedMoney.add(combineDerateMoney.multiply(BigDecimal.valueOf(combineAmount)));
                promotionRuleData.set(PromotionRuleConstants.Field.CombineDereateMoney.apiName, tempDerateOrFixedMoney);
            } else if (PromotionTypeEnum.CombineFixedPrice.getValue().equals(promotionType)) {
                BigDecimal combineFixedPrice = promotionProductData.get(PromotionProductConstants.Field.FixedPrice.apiName, BigDecimal.class);
                Integer combineAmount = promotionProductData.get(PromotionProductConstants.Field.CombineAmount.apiName, Integer.class);
                tempDerateOrFixedMoney = tempDerateOrFixedMoney.add(combineFixedPrice.multiply(BigDecimal.valueOf(combineAmount)));
                promotionRuleData.set(PromotionRuleConstants.Field.CombineFixedPrice.apiName, tempDerateOrFixedMoney);
            }
        }
    }

    public static void validateCombinePromotionProductField(User user, String promotionType, List<IObjectData> promotionProductDataList, ServiceFacade serviceFacade) {
        if (CollectionUtils.isEmpty(promotionProductDataList)) {
            throw new ValidateException(I18N.text(ProI18NKey.PROMOTION_PRODUCT_NOT_NULL));
        }
        List<String> productIds = promotionProductDataList.stream().map(x -> x.get(PromotionProductConstants.Field.Product.apiName, String.class)).collect(Collectors.toList());
        List<IObjectData> productObjectDataList = serviceFacade.findObjectDataByIds(user.getTenantId(), productIds, Utils.PRODUCT_API_NAME);
        Map<String, BigDecimal> productIdToPriceMap = productObjectDataList.stream().collect(Collectors.toMap(IObjectData::getId, x -> x.get("price", BigDecimal.class)));
        promotionProductDataList.forEach(promotionProductData -> {
            String productId = promotionProductData.get(PromotionProductConstants.Field.Product.apiName, String.class);
            BigDecimal combineAmount = promotionProductData.get(PromotionProductConstants.Field.CombineAmount.apiName, BigDecimal.class);
            if (Objects.isNull(combineAmount) || combineAmount.compareTo(BigDecimal.ZERO) < 0) {
                throw new ValidateException(I18N.text(ProI18NKey.PROMOTION_PRODUCT_COMBINE_AMOUNT_MUST_GT_ZERO));
            }
            if (PromotionTypeEnum.CombineDiscount.getValue().equals(promotionType)) {
                Object combineDiscount = promotionProductData.get(PromotionProductConstants.Field.PriceDiscount.apiName);
                if (Objects.isNull(combineDiscount)) {
                    throw new ValidateException(I18N.text(ProI18NKey.PRICE_DISCOUNT_NOT_NULL));
                }
            } else if (PromotionTypeEnum.CombineDerateMoney.getValue().equals(promotionType)) {
                BigDecimal combineDerateMoney = promotionProductData.get(PromotionProductConstants.Field.DerateMoney.apiName, BigDecimal.class);
                if (Objects.isNull(combineDerateMoney)) {
                    throw new ValidateException(I18N.text(ProI18NKey.PROMOTION_PRODUCT_DERATE_MONEY_NOT_NULL));
                }
                if (combineDerateMoney.compareTo(BigDecimal.ZERO) <= 0) {
                    throw new ValidateException(I18N.text(ProI18NKey.DERATE_MONEY_MUST_GT_ZERO));
                }
                if (combineDerateMoney.compareTo(productIdToPriceMap.get(productId)) > 0) {
                    throw new ValidateException(I18N.text(ProI18NKey.DERATE_MONEY_MUST_LT_PRODUCT_PRICE));
                }
            } else if (PromotionTypeEnum.CombineFixedPrice.getValue().equals(promotionType)) {
                BigDecimal combineFixedPrice = promotionProductData.get(PromotionProductConstants.Field.FixedPrice.apiName, BigDecimal.class);
                if (Objects.isNull(combineFixedPrice)) {
                    throw new ValidateException(I18N.text(ProI18NKey.FIXED_PRICE_NOT_NULL));
                }
                if (combineFixedPrice.compareTo(BigDecimal.ZERO) < 0) {
                    throw new ValidateException(I18N.text(ProI18NKey.FIXED_PRICE_MUST_GT_ZERO));
                }
                if (combineFixedPrice.compareTo(productIdToPriceMap.get(productId)) > 0) {
                    throw new ValidateException(I18N.text(ProI18NKey.FIXED_PRICE_MUST_LT_PRODUCT_PRICE));
                }
            }
        });
    }

    /**
     * 标准规则订单促销与全部商品设置的商品促销，校验促销规则唯一
     * 订单促销、按全部产品设置的商品促销 促销规则的校验
     */
    public static void checkPromotionRuleNotDuplicate(IObjectDescribe promotionDescribe, IObjectData promotionData, List<IObjectData> promotionRuleDataList) {
        String promotionRecordType = promotionData.getRecordType();
        String condition = promotionData.get(PromotionConstants.Field.Condition.apiName, String.class);
        Boolean isStairPromotion = promotionData.get(PromotionConstants.Field.IsStairPromotion.apiName, Boolean.class);
        Set<Object> keys = Sets.newHashSet();
        promotionRuleDataList.forEach(promotionRuleData -> {
            String keyFieldName = null;
            if (PromotionRecordTypeEnum.OrderPromotion.getApiName().equals(promotionRecordType)) {
                keyFieldName = PromotionConditionEnum.FullPiece.getValue().equals(condition) ? PromotionRuleConstants.Field.OrderProductNum.apiName : PromotionRuleConstants.Field.OrderMoney.apiName;
            } else if (PromotionRecordTypeEnum.ProductPromotion.getApiName().equals(promotionRecordType)) {
                keyFieldName = PromotionConditionEnum.FullPiece.getValue().equals(condition) ? PromotionRuleConstants.Field.PurchaseNum.apiName : PromotionRuleConstants.Field.PurchaseAmount.apiName;
            }
            if (StringUtils.isNotEmpty(keyFieldName)) {
                String key = promotionRuleData.get(keyFieldName, String.class);
                if (keys.contains(key)) {
                    throw new ValidateException(I18N.text(ProI18NKey.PROMOTION_RULE_MUST_UNIQUE, ObjectDescribeExt.of(promotionDescribe).getFieldLabelByName(keyFieldName)));
                }
                if (!isStairPromotion && keys.size() > 1) {
                    throw new ValidateException(I18N.text(ProI18NKey.NOT_STAIR_PROMOTION_RULE_UNIQUE));
                }
                keys.add(key);
            }
        });
    }

    public static void resetSingleSettingPromotionProductField(IObjectData promotionData, List<IObjectData> promotionProductDataList) {
        String condition = promotionData.get(PromotionConstants.Field.Condition.apiName, String.class);
        String type = promotionData.get(PromotionConstants.Field.Type.apiName, String.class);
        promotionProductDataList.forEach(promotionProductData -> {
            promotionProductData.setRecordType(PromotionProductRecordTypeEnum.DefaultRecordType.getApiName());
            promotionProductData.set(PromotionProductConstants.Field.CombineAmount.apiName, null);
            if (PromotionConditionEnum.FullPiece.getValue().equals(condition)) {
                promotionProductData.set(PromotionProductConstants.Field.PurchaseAmount.apiName, null);
                promotionProductData.set(PromotionProductConstants.Field.AmountQuota.apiName, null);
            } else if (PromotionConditionEnum.FullAmount.getValue().equals(condition)) {
                promotionProductData.set(PromotionProductConstants.Field.PurchaseNum.apiName, null);
                promotionProductData.set(PromotionProductConstants.Field.Quota.apiName, null);
            }
            Set<String> fieldNames = TYPE_TO_NULL_FIELDS_OF_PROMOTION_PRODUCT_MAP.getOrDefault(type, Sets.newHashSet());
            fieldNames.forEach(x -> promotionProductData.set(x, null));
        });

    }

    public static void checkSingleSettingPromotionProductField(IObjectData promotionData, IObjectDescribe promotionProductDescribe, List<IObjectData> promotionProductDataList) {
        Boolean isStairPromotion = promotionData.get(PromotionConstants.Field.IsStairPromotion.apiName, Boolean.class);
        String condition = promotionData.get(PromotionConstants.Field.Condition.apiName, String.class);
        String type = promotionData.get(PromotionConstants.Field.Type.apiName, String.class);
        Set<String> keys = Sets.newHashSet();
        promotionProductDataList.forEach(promotionProductData -> {
            String key;
            String productId = promotionProductData.get(PromotionProductConstants.Field.Product.apiName, String.class);
            if (BooleanUtils.isTrue(isStairPromotion)) {
                if (PromotionConditionEnum.FullPiece.getValue().equals(condition)) {
                    String purchaseNumStr = promotionProductData.get(PromotionProductConstants.Field.PurchaseNum.apiName, String.class);
                    key = productId + "#" + purchaseNumStr;
                } else {
                    String purchaseAmountStr = promotionProductData.get(PromotionProductConstants.Field.PurchaseAmount.apiName, String.class);
                    key = productId + "#" + purchaseAmountStr;
                }
            } else {
                key = productId;
            }
            if (keys.contains(key)) {
                throw new ValidateException(I18N.text(ProI18NKey.PROMOTION_RULE_MUST_UNIQUE, ObjectDescribeExt.of(promotionProductDescribe).getFieldLabelByName(PromotionProductConstants.Field.Product.apiName)));
            }
            keys.add(key);

            List<String> fieldNames = Lists.newArrayList();
            if (PromotionConditionEnum.FullPiece.getValue().equals(condition)) {
                fieldNames.add(PromotionProductConstants.Field.PurchaseNum.apiName);
            } else if (PromotionConditionEnum.FullAmount.getValue().equals(condition)) {
                fieldNames.add(PromotionProductConstants.Field.PurchaseAmount.apiName);
            }

            if (PromotionTypeEnum.PriceDiscount.getValue().equals(type)) {
                fieldNames.add(PromotionProductConstants.Field.PriceDiscount.apiName);
            } else if (PromotionTypeEnum.DerateMoney.getValue().equals(type)) {
                fieldNames.add(PromotionProductConstants.Field.DerateMoney.apiName);
            } else if (PromotionTypeEnum.FixedPrice.getValue().equals(type)) {
                fieldNames.add(PromotionProductConstants.Field.FixedPrice.apiName);
            } else if (PromotionTypeEnum.NumberReachedGift.getValue().equals(type)) {
                String giftType = promotionProductData.get(PromotionProductConstants.Field.GiftType.apiName, String.class);
                if (GiftTypeEnum.NormalProduct.getValue().equals(giftType)) {
                    fieldNames.add(PromotionProductConstants.Field.GiftProductNum.apiName);
                    fieldNames.add(PromotionProductConstants.Field.GiftProduct.apiName);
                } else if (GiftTypeEnum.Self.getValue().equals(giftType)) {
                    fieldNames.add(PromotionProductConstants.Field.GiftProductNum.apiName);
                }
            }
            checkFields(promotionProductDescribe, promotionProductData, fieldNames);
        });
    }

    private static void checkFields(IObjectDescribe detailDescribe, IObjectData detailObjectData, List<String> fieldNames) {
        if (CollectionUtils.isEmpty(fieldNames)) {
            return;
        }
        fieldNames.forEach(fieldName -> {
            Object fieldValue = detailObjectData.get(fieldName);
            if (Objects.isNull(fieldValue) || StringUtils.isEmpty(String.valueOf(fieldValue))) {
                String fieldLabel = ObjectDescribeExt.of(detailDescribe).getFieldLabelByName(fieldName);
                String errMsgFormat = detailDescribe.getApiName().equals(PromotionRuleConstants.API_NAME) ? ProI18NKey.PROMOTION_RULE_FIELD_ERROR : ProI18NKey.PROMOTION_PRODUCT_FIELD_ERROR;
                throw new ValidateException(I18N.text(errMsgFormat, fieldLabel));
            }
        });
    }

    /**
     * 标准规则的订单促销、组合促销、商品促销
     */
    public static void validatePromotionRuleField(IObjectData promotionData, IObjectDescribe promotionRuleDescribe, List<IObjectData> promotionRuleDataList) {
        String promotionRecordType = promotionData.getRecordType();
        String condition = promotionData.get(PromotionConstants.Field.Condition.apiName, String.class);
        String promotionType = promotionData.get(PromotionConstants.Field.Type.apiName, String.class);
        promotionRuleDataList.forEach(promotionRuleData -> {
            String validateConditionRelateFieldName = null;
            if (PromotionRecordTypeEnum.OrderPromotion.getApiName().equals(promotionRecordType)) {
                validateConditionRelateFieldName = PromotionConditionEnum.FullAmount.getValue().equals(condition) ? PromotionRuleConstants.Field.OrderMoney.apiName : PromotionRuleConstants.Field.OrderProductNum.apiName;
            } else if (PromotionRecordTypeEnum.ProductPromotion.getApiName().equals(promotionRecordType)) {
                validateConditionRelateFieldName = PromotionConditionEnum.FullPiece.getValue().equals(condition) ? PromotionRuleConstants.Field.PurchaseNum.apiName : PromotionRuleConstants.Field.PurchaseAmount.apiName;
            }
            List<String> fieldNames = Lists.newArrayList();
            if (StringUtils.isNotEmpty(validateConditionRelateFieldName)) {
                fieldNames.add(validateConditionRelateFieldName);
            }
            fieldNames.addAll(getRelateRuleFieldNamesByType(promotionType, promotionRuleData));
            checkFields(promotionRuleDescribe, promotionRuleData, fieldNames);
        });
    }


    public static void setRuleDescriptionField(User user, IObjectData promotionData, Map<String, List<IObjectData>> detailObjectData, ServiceFacade serviceFacade) {
        String type = promotionData.get(PromotionConstants.Field.Type.apiName, String.class);
        String condition = promotionData.get(PromotionConstants.Field.Condition.apiName, String.class);
        List<IObjectData> promotionProductDataList = detailObjectData.get(PromotionProductConstants.API_NAME);
        List<IObjectData> giftProductList = Lists.newArrayList();
        IObjectDescribe productDescribe = null;
        if (PromotionTypeEnum.NumberReachedGift.getValue().equals(type)) {
            List<String> giftProductIds = Lists.newArrayList();
            promotionProductDataList.forEach(promotionProductData -> {
                giftProductIds.add(promotionProductData.get(PromotionProductConstants.Field.GiftProduct.apiName, String.class));
            });
            productDescribe = serviceFacade.findObject(user.getTenantId(), CommonProductConstants.ProductApiName);
            ActionContextExt actionContextExt = ActionContextExt.of(user);
            actionContextExt.setObjectDescribe(productDescribe);
            giftProductList = serviceFacade.findObjectDataByIds(actionContextExt.getContext(), giftProductIds, Utils.PRODUCT_API_NAME);
        }
        Map<String, IObjectData> productIdNameMap = giftProductList.stream().collect(Collectors.toMap(IObjectData::getId, x -> x));
        for (IObjectData promotionProductData : promotionProductDataList) {
            if (PromotionConditionEnum.FullPiece.getValue().equals(condition)) {
                fullPieceDefaultSettingValueToRuleDescription(promotionData, promotionProductData, productIdNameMap, productDescribe);
            } else if (PromotionConditionEnum.FullAmount.getValue().equals(condition)) {
                fullAmountDefaultSettingValueToRuleDescription(promotionData, promotionProductData, productIdNameMap, productDescribe);
            }
        }
    }

    /**
     * 满件-规则说明
     */
    public static void fullPieceDefaultSettingValueToRuleDescription(IObjectData objectData, IObjectData promotionProductData, Map<String, IObjectData> giftProductMap, IObjectDescribe productDescribe) {
        //满数量
        BigDecimal purchaseNum = promotionProductData.get(PromotionProductConstants.Field.PurchaseNum.apiName, BigDecimal.class);
        String unit = promotionProductData.get(PromotionProductConstants.Field.Unit.apiName, String.class);
        String ruleDescriptionValue = purchaseNum + unit;
        String type = objectData.get(PromotionConstants.Field.Type.apiName, String.class);
        String apiName = PromotionProductConstants.Field.RuleDescription.apiName;
        if (PromotionTypeEnum.PriceDiscount.getValue().equals(type)) {
            //打折--价格折扣
            BigDecimal priceDiscount = promotionProductData.get(PromotionProductConstants.Field.PriceDiscount.apiName, BigDecimal.class);
            promotionProductData.set(apiName, I18N.text(ProI18NKey.FULLPIECE_PROMOTION_PRODUCT_RULE_EXPLAIN_PRICEDISCOUNT, ruleDescriptionValue, priceDiscount));
        } else if (PromotionTypeEnum.DerateMoney.getValue().equals(type)) {
            //减免--单价减免(元)
            BigDecimal derateMoney = promotionProductData.get(PromotionProductConstants.Field.DerateMoney.apiName, BigDecimal.class);
            promotionProductData.set(apiName, I18N.text(ProI18NKey.FULLPIECE_PROMOTION_PRODUCT_RULE_EXPLAIN_DERATEMONEY, ruleDescriptionValue, derateMoney));
        } else if (PromotionTypeEnum.FixedPrice.getValue().equals(type)) {
            //一口价--一口价(元)
            BigDecimal fixedPrice = promotionProductData.get(PromotionProductConstants.Field.FixedPrice.apiName, BigDecimal.class);
            promotionProductData.set(apiName, I18N.text(ProI18NKey.FULLPIECE_PROMOTION_PRODUCT_RULE_EXPLAIN_FIXEDPRICE, ruleDescriptionValue, fixedPrice));
        } else if (PromotionTypeEnum.NumberReachedGift.getValue().equals(type)) {
            //是否阶梯
            Boolean isStairPromotion = objectData.get(PromotionConstants.Field.IsStairPromotion.apiName, Boolean.class);
            List<String> placeHolders = getGiftRuleDesc(promotionProductData, productDescribe, giftProductMap);
            /**普通商品*/
            if (!isStairPromotion) {
                promotionProductData.set(apiName, I18N.text(ProI18NKey.FULLPIECE_PROMOTION_PRODUCT_RULE_EXPLAIN_NUMBERREACHEDGIFT, ruleDescriptionValue, placeHolders.get(0), placeHolders.get(1)));
            } else {
                promotionProductData.set(apiName, I18N.text(ProI18NKey.FULLPIECE_ISSTAIRPROMOTION_PROMOTION_PRODUCT_RULE_EXPLAIN_NUMBERREACHEDGIFT, ruleDescriptionValue, placeHolders.get(0), placeHolders.get(1)));
            }
        }
    }

    /**
     * 满额-规则说明
     */
    public static void fullAmountDefaultSettingValueToRuleDescription(IObjectData objectData, IObjectData promotionProductData, Map<String, IObjectData> giftProductMap, IObjectDescribe productDescribe) {
        //满数量
        BigDecimal purchaseAmount = promotionProductData.get(PromotionProductConstants.Field.PurchaseAmount.apiName, BigDecimal.class);
        String type = objectData.get(PromotionConstants.Field.Type.apiName, String.class);
        String apiName = PromotionProductConstants.Field.RuleDescription.apiName;
        if (PromotionTypeEnum.PriceDiscount.getValue().equals(type)) {
            //打折--价格折扣
            BigDecimal priceDiscount = promotionProductData.get(PromotionProductConstants.Field.PriceDiscount.apiName, BigDecimal.class);
            promotionProductData.set(apiName, I18N.text(ProI18NKey.FULLAMOUNT_PROMOTION_PRODUCT_RULE_EXPLAIN_PRICEDISCOUNT, purchaseAmount, priceDiscount));
        } else if (PromotionTypeEnum.DerateMoney.getValue().equals(type)) {
            //减免--单价减免(元)
            BigDecimal derateMoney = promotionProductData.get(PromotionProductConstants.Field.DerateMoney.apiName, BigDecimal.class);
            promotionProductData.set(apiName, I18N.text(ProI18NKey.FULLAMOUNT_PROMOTION_PRODUCT_RULE_EXPLAIN_DERATEMONEY, purchaseAmount, derateMoney));
        } else if (PromotionTypeEnum.FixedPrice.getValue().equals(type)) {
            //一口价--一口价(元)
            BigDecimal fixedPrice = promotionProductData.get(PromotionProductConstants.Field.FixedPrice.apiName, BigDecimal.class);
            promotionProductData.set(apiName, I18N.text(ProI18NKey.FULLAMOUNT_PROMOTION_PRODUCT_RULE_EXPLAIN_FIXEDPRICE, purchaseAmount, fixedPrice));
        } else if (PromotionTypeEnum.NumberReachedGift.getValue().equals(type)) {
            //是否阶梯
            Boolean isStairPromotion = objectData.get(PromotionConstants.Field.IsStairPromotion.apiName, Boolean.class);
            List<String> placeHolders = getGiftRuleDesc(promotionProductData, productDescribe, giftProductMap);
            /**普通商品*/
            if (!isStairPromotion) {
                promotionProductData.set(apiName, I18N.text(ProI18NKey.FULLAMOUNT_PROMOTION_PRODUCT_RULE_EXPLAIN_NUMBERREACHEDGIFT, purchaseAmount, placeHolders.get(0), placeHolders.get(1)));
            } else {
                promotionProductData.set(apiName, I18N.text(ProI18NKey.FULLAMOUNT_ISSTAIRPROMOTION_PROMOTION_PRODUCT_RULE_EXPLAIN_NUMBERREACHEDGIFT, purchaseAmount, placeHolders.get(0), placeHolders.get(1)));
            }
        }
    }

    private static List<String> getGiftRuleDesc(IObjectData promotionProductData, IObjectDescribe productDescribe, Map<String, IObjectData> giftProductMap) {
        List<String> placeHolders = Lists.newArrayList();
        //赠品ID
        String giftProductId = promotionProductData.get(PromotionProductConstants.Field.GiftProduct.apiName, String.class);
        BigDecimal giftProductNum = promotionProductData.get(PromotionProductConstants.Field.GiftProductNum.apiName, BigDecimal.class);
        //本品ID
        String productId = promotionProductData.get(PromotionProductConstants.Field.Product.apiName, String.class);
        String unit = promotionProductData.get(PromotionProductConstants.Field.Unit.apiName, String.class);
        IObjectData giftProductData = giftProductMap.get(giftProductId);
        String giftProductValue;
        String unitLabel = null;
        if (!productId.equals(giftProductId)) {
            unitLabel = queryFieldOptionLabel(giftProductData, productDescribe);
            giftProductValue = giftProductNum + unitLabel;
        } else {
            giftProductValue = giftProductNum + unit;
        }
        log.info("productId={},giftProductId={},unit={},label={}", productId, giftProductId, unit, unitLabel);
        placeHolders.add(giftProductData.getName());
        placeHolders.add(giftProductValue);
        return placeHolders;
    }

    private static String queryFieldOptionLabel(IObjectData productData, IObjectDescribe productDescribe) {
        String giftUnit = productData.get(PromotionProductConstants.Field.Unit.apiName, String.class);
        IFieldDescribe fieldDescribe = ObjectDescribeExt.of(productDescribe).getFieldDescribe(CommonProductConstants.Field.Unit.apiName);
        List<Map> optionsList = fieldDescribe.get("options", List.class);
        if (optionsList != null) {
            for (Map option : optionsList) {
                if (String.valueOf(option.get("value")).equals(giftUnit)) {
                    return option.get("label").toString();
                }
            }
        }
        return null;
    }

    private static List<String> getRelateRuleFieldNamesByType(String promotionType, IObjectData promotionRuleData) {
        PromotionTypeEnum promotionTypeEnum = PromotionTypeEnum.get(promotionType);
        List<String> fieldNames = Lists.newArrayList();
        switch (promotionTypeEnum) {
            case DerateMoney:
                fieldNames.add(PromotionRuleConstants.Field.DerateMoney.apiName);
                break;
            case FixedPrice:
                fieldNames.add(PromotionRuleConstants.Field.FixedPrice.apiName);
                break;
            case NumberReachedGift:
            case CombineNumberReachedGift:
            case OrderMoneyReachedGift:
                /*String giftType = promotionRuleData.get(PromotionRuleConstants.Field.GiftType.apiName, String.class);
                if (GiftTypeEnum.NormalProduct.getValue().equals(giftType)) {
                    fieldNames.add(PromotionRuleConstants.Field.GiftProduct.apiName);
                }
                fieldNames.add(PromotionRuleConstants.Field.GiftProductNum.apiName);*/
                fieldNames.add(PromotionRuleConstants.Field.GiftMethod.apiName);
                fieldNames.add(PromotionRuleConstants.Field.OptionGiftNum.apiName);
                break;
            case PriceDiscount:
                fieldNames.add(PromotionRuleConstants.Field.PriceDiscount.apiName);
                break;
            case CombineDerateMoney:
                fieldNames.add(PromotionRuleConstants.Field.CombineDereateMoney.apiName);
                break;
            case CombineDiscount:
                fieldNames.add(PromotionRuleConstants.Field.CombineDiscount.apiName);
                break;
            case CombineFixedPrice:
                fieldNames.add(PromotionRuleConstants.Field.CombineFixedPrice.apiName);
                break;
            /*case CombineNumberReachedGift:
                fieldNames.add(PromotionRuleConstants.Field.GiftProduct.apiName);
                fieldNames.add(PromotionRuleConstants.Field.GiftProductNum.apiName);
                break;*/
            case OrderDerateMoney:
                fieldNames.add(PromotionRuleConstants.Field.OrderDerateMoney.apiName);
                break;
            /*case OrderMoneyReachedGift:
                fieldNames.add(PromotionRuleConstants.Field.GiftProduct.apiName);
                fieldNames.add(PromotionRuleConstants.Field.GiftProductNum.apiName);
                break;*/
            case OrderDiscount:
                fieldNames.add(PromotionRuleConstants.Field.OrderDiscount.apiName);
                break;
            default:
                break;
        }
        return fieldNames;
    }

    private static BigDecimal getQuoteCurrencyField(IObjectData objectData, String fieldApiName) {
        Object value = objectData.get(fieldApiName);
        if (Objects.isNull(value)) {
            return BigDecimal.ZERO;
        }
        String valueStr = String.valueOf(value);
        valueStr = valueStr.replaceAll(",", "");
        return new BigDecimal(valueStr);
    }

    static {
        RULE_NULL_FIELDS_FOR_PRODUCT_PROMOTION.add(PromotionRuleConstants.Field.OrderDerateMoney.apiName);
        RULE_NULL_FIELDS_FOR_PRODUCT_PROMOTION.add(PromotionRuleConstants.Field.OrderMoney.apiName);
        RULE_NULL_FIELDS_FOR_PRODUCT_PROMOTION.add(PromotionRuleConstants.Field.OrderDiscount.apiName);
        RULE_NULL_FIELDS_FOR_PRODUCT_PROMOTION.add(PromotionRuleConstants.Field.CombineDereateMoney.apiName);
        RULE_NULL_FIELDS_FOR_PRODUCT_PROMOTION.add(PromotionRuleConstants.Field.CombineFixedPrice.apiName);
        RULE_NULL_FIELDS_FOR_PRODUCT_PROMOTION.add(PromotionRuleConstants.Field.CombineDiscount.apiName);

        RULE_NULL_FIELDS_FOR_ORDER_PROMOTION.add(PromotionRuleConstants.Field.PurchaseNum.apiName);
        RULE_NULL_FIELDS_FOR_ORDER_PROMOTION.add(PromotionRuleConstants.Field.DerateMoney.apiName);
        RULE_NULL_FIELDS_FOR_ORDER_PROMOTION.add(PromotionRuleConstants.Field.PriceDiscount.apiName);
        RULE_NULL_FIELDS_FOR_ORDER_PROMOTION.add(PromotionRuleConstants.Field.FixedPrice.apiName);
        RULE_NULL_FIELDS_FOR_ORDER_PROMOTION.add(PromotionRuleConstants.Field.CombineDereateMoney.apiName);
        RULE_NULL_FIELDS_FOR_ORDER_PROMOTION.add(PromotionRuleConstants.Field.CombineFixedPrice.apiName);
        RULE_NULL_FIELDS_FOR_ORDER_PROMOTION.add(PromotionRuleConstants.Field.CombineDiscount.apiName);

//        RULE_NULL_FIELDS_FOR_COMBINE_PROMOTION.add(PromotionRuleConstants.Field.GiftType.apiName);
        RULE_NULL_FIELDS_FOR_COMBINE_PROMOTION.add(PromotionRuleConstants.Field.PurchaseNum.apiName);
        RULE_NULL_FIELDS_FOR_COMBINE_PROMOTION.add(PromotionRuleConstants.Field.DerateMoney.apiName);
        RULE_NULL_FIELDS_FOR_COMBINE_PROMOTION.add(PromotionRuleConstants.Field.FixedPrice.apiName);
        RULE_NULL_FIELDS_FOR_COMBINE_PROMOTION.add(PromotionRuleConstants.Field.PriceDiscount.apiName);
        RULE_NULL_FIELDS_FOR_COMBINE_PROMOTION.add(PromotionRuleConstants.Field.OrderDerateMoney.apiName);
        RULE_NULL_FIELDS_FOR_COMBINE_PROMOTION.add(PromotionRuleConstants.Field.OrderMoney.apiName);
        RULE_NULL_FIELDS_FOR_COMBINE_PROMOTION.add(PromotionRuleConstants.Field.OrderDiscount.apiName);

        //订单促销
        Set<String> ruleNullFieldsForOrderDiscount = Sets.newHashSet();
//        ruleNullFieldsForOrderDiscount.add(PromotionRuleConstants.Field.GiftProduct.apiName);
//        ruleNullFieldsForOrderDiscount.add(PromotionRuleConstants.Field.GiftProductNum.apiName);
        ruleNullFieldsForOrderDiscount.add(PromotionRuleConstants.Field.GiftMethod.apiName);
        ruleNullFieldsForOrderDiscount.add(PromotionRuleConstants.Field.OptionGiftNum.apiName);
        ruleNullFieldsForOrderDiscount.add(PromotionRuleConstants.Field.OrderDerateMoney.apiName);
        TYPE_TO_NULL_FIELDS_OF_PROMOTION_RULE_MAP.put(PromotionTypeEnum.OrderDiscount.getValue(), ruleNullFieldsForOrderDiscount);
        Set<String> ruleNullFieldsForOrderDerateMoney = Sets.newHashSet();
//        ruleNullFieldsForOrderDerateMoney.add(PromotionRuleConstants.Field.GiftProduct.apiName);
//        ruleNullFieldsForOrderDerateMoney.add(PromotionRuleConstants.Field.GiftProductNum.apiName);
        ruleNullFieldsForOrderDerateMoney.add(PromotionRuleConstants.Field.GiftMethod.apiName);
        ruleNullFieldsForOrderDerateMoney.add(PromotionRuleConstants.Field.OptionGiftNum.apiName);
        ruleNullFieldsForOrderDerateMoney.add(PromotionRuleConstants.Field.OrderDiscount.apiName);
        TYPE_TO_NULL_FIELDS_OF_PROMOTION_RULE_MAP.put(PromotionTypeEnum.OrderDerateMoney.getValue(), ruleNullFieldsForOrderDerateMoney);
        Set<String> ruleNullFieldsForOrderMoneyReachGift = Sets.newHashSet();
        ruleNullFieldsForOrderMoneyReachGift.add(PromotionRuleConstants.Field.OrderDerateMoney.apiName);
        ruleNullFieldsForOrderMoneyReachGift.add(PromotionRuleConstants.Field.OrderDiscount.apiName);
        TYPE_TO_NULL_FIELDS_OF_PROMOTION_RULE_MAP.put(PromotionTypeEnum.OrderMoneyReachedGift.getValue(), ruleNullFieldsForOrderMoneyReachGift);

        //组合促销
        Set<String> ruleNullFieldsForCombineDerateMoney = Sets.newHashSet();
        ruleNullFieldsForCombineDerateMoney.add(PromotionRuleConstants.Field.CombineFixedPrice.apiName);
        ruleNullFieldsForCombineDerateMoney.add(PromotionRuleConstants.Field.CombineDiscount.apiName);
//        ruleNullFieldsForCombineDerateMoney.add(PromotionRuleConstants.Field.GiftProduct.apiName);
//        ruleNullFieldsForCombineDerateMoney.add(PromotionRuleConstants.Field.GiftProductNum.apiName);
        ruleNullFieldsForCombineDerateMoney.add(PromotionRuleConstants.Field.GiftMethod.apiName);
        ruleNullFieldsForCombineDerateMoney.add(PromotionRuleConstants.Field.OptionGiftNum.apiName);
        TYPE_TO_NULL_FIELDS_OF_PROMOTION_RULE_MAP.put(PromotionTypeEnum.CombineDerateMoney.getValue(), ruleNullFieldsForCombineDerateMoney);
        Set<String> ruleNullFieldsForCombineDiscount = Sets.newHashSet();
        ruleNullFieldsForCombineDiscount.add(PromotionRuleConstants.Field.CombineDereateMoney.apiName);
        ruleNullFieldsForCombineDiscount.add(PromotionRuleConstants.Field.CombineFixedPrice.apiName);
//        ruleNullFieldsForCombineDiscount.add(PromotionRuleConstants.Field.GiftProduct.apiName);
//        ruleNullFieldsForCombineDiscount.add(PromotionRuleConstants.Field.GiftProductNum.apiName);
        ruleNullFieldsForCombineDiscount.add(PromotionRuleConstants.Field.GiftMethod.apiName);
        ruleNullFieldsForCombineDiscount.add(PromotionRuleConstants.Field.OptionGiftNum.apiName);
        TYPE_TO_NULL_FIELDS_OF_PROMOTION_RULE_MAP.put(PromotionTypeEnum.CombineDiscount.getValue(), ruleNullFieldsForCombineDiscount);
        Set<String> ruleNullFieldsForCombineFixedPrice = Sets.newHashSet();
        ruleNullFieldsForCombineFixedPrice.add(PromotionRuleConstants.Field.CombineDereateMoney.apiName);
        ruleNullFieldsForCombineFixedPrice.add(PromotionRuleConstants.Field.CombineDiscount.apiName);
//        ruleNullFieldsForCombineFixedPrice.add(PromotionRuleConstants.Field.GiftProduct.apiName);
//        ruleNullFieldsForCombineFixedPrice.add(PromotionRuleConstants.Field.GiftProductNum.apiName);
        ruleNullFieldsForCombineFixedPrice.add(PromotionRuleConstants.Field.GiftMethod.apiName);
        ruleNullFieldsForCombineFixedPrice.add(PromotionRuleConstants.Field.OptionGiftNum.apiName);
        TYPE_TO_NULL_FIELDS_OF_PROMOTION_RULE_MAP.put(PromotionTypeEnum.CombineFixedPrice.getValue(), ruleNullFieldsForCombineFixedPrice);
        Set<String> ruleNullFieldsForCombineNumberReachedGift = Sets.newHashSet();
        ruleNullFieldsForCombineNumberReachedGift.add(PromotionRuleConstants.Field.CombineDereateMoney.apiName);
        ruleNullFieldsForCombineNumberReachedGift.add(PromotionRuleConstants.Field.CombineFixedPrice.apiName);
        ruleNullFieldsForCombineNumberReachedGift.add(PromotionRuleConstants.Field.CombineDiscount.apiName);
        TYPE_TO_NULL_FIELDS_OF_PROMOTION_RULE_MAP.put(PromotionTypeEnum.CombineNumberReachedGift.getValue(), ruleNullFieldsForCombineNumberReachedGift);

        //商品促销
        Set<String> ruleNullFieldsForPriceDiscount = Sets.newHashSet();
        /*ruleNullFieldsForPriceDiscount.add(PromotionRuleConstants.Field.GiftType.apiName);
        ruleNullFieldsForPriceDiscount.add(PromotionRuleConstants.Field.GiftProduct.apiName);
        ruleNullFieldsForPriceDiscount.add(PromotionRuleConstants.Field.GiftProductNum.apiName);*/
        ruleNullFieldsForPriceDiscount.add(PromotionRuleConstants.Field.GiftMethod.apiName);
        ruleNullFieldsForPriceDiscount.add(PromotionRuleConstants.Field.OptionGiftNum.apiName);
        ruleNullFieldsForPriceDiscount.add(PromotionRuleConstants.Field.DerateMoney.apiName);
        ruleNullFieldsForPriceDiscount.add(PromotionRuleConstants.Field.FixedPrice.apiName);
        TYPE_TO_NULL_FIELDS_OF_PROMOTION_RULE_MAP.put(PromotionTypeEnum.PriceDiscount.getValue(), ruleNullFieldsForPriceDiscount);
        Set<String> ruleNullFieldsForDerateMoney = Sets.newHashSet();
        /*ruleNullFieldsForDerateMoney.add(PromotionRuleConstants.Field.GiftType.apiName);
        ruleNullFieldsForDerateMoney.add(PromotionRuleConstants.Field.GiftProduct.apiName);
        ruleNullFieldsForDerateMoney.add(PromotionRuleConstants.Field.GiftProductNum.apiName);*/
        ruleNullFieldsForPriceDiscount.add(PromotionRuleConstants.Field.GiftMethod.apiName);
        ruleNullFieldsForPriceDiscount.add(PromotionRuleConstants.Field.OptionGiftNum.apiName);
        ruleNullFieldsForDerateMoney.add(PromotionRuleConstants.Field.PriceDiscount.apiName);
        ruleNullFieldsForDerateMoney.add(PromotionRuleConstants.Field.FixedPrice.apiName);
        TYPE_TO_NULL_FIELDS_OF_PROMOTION_RULE_MAP.put(PromotionTypeEnum.DerateMoney.getValue(), ruleNullFieldsForDerateMoney);
        Set<String> ruleNullFieldsForFixedPrice = Sets.newHashSet();
        /*ruleNullFieldsForFixedPrice.add(PromotionRuleConstants.Field.GiftType.apiName);
        ruleNullFieldsForFixedPrice.add(PromotionRuleConstants.Field.GiftProduct.apiName);
        ruleNullFieldsForFixedPrice.add(PromotionRuleConstants.Field.GiftProductNum.apiName);*/
        ruleNullFieldsForPriceDiscount.add(PromotionRuleConstants.Field.GiftMethod.apiName);
        ruleNullFieldsForPriceDiscount.add(PromotionRuleConstants.Field.OptionGiftNum.apiName);
        ruleNullFieldsForFixedPrice.add(PromotionRuleConstants.Field.PriceDiscount.apiName);
        ruleNullFieldsForFixedPrice.add(PromotionRuleConstants.Field.DerateMoney.apiName);
        TYPE_TO_NULL_FIELDS_OF_PROMOTION_RULE_MAP.put(PromotionTypeEnum.FixedPrice.getValue(), ruleNullFieldsForFixedPrice);
        Set<String> ruleNullFieldsForNumberReachedGift = Sets.newHashSet();
        ruleNullFieldsForNumberReachedGift.add(PromotionRuleConstants.Field.PriceDiscount.apiName);
        ruleNullFieldsForNumberReachedGift.add(PromotionRuleConstants.Field.DerateMoney.apiName);
        ruleNullFieldsForNumberReachedGift.add(PromotionRuleConstants.Field.FixedPrice.apiName);
        TYPE_TO_NULL_FIELDS_OF_PROMOTION_RULE_MAP.put(PromotionTypeEnum.NumberReachedGift.getValue(), ruleNullFieldsForNumberReachedGift);

        //促销产品  组合促销
        Set<String> productNullFieldsForCombineFixedPrice = Sets.newHashSet();
        productNullFieldsForCombineFixedPrice.add(PromotionProductConstants.Field.DerateMoney.apiName);
        productNullFieldsForCombineFixedPrice.add(PromotionProductConstants.Field.PriceDiscount.apiName);
        TYPE_TO_NULL_FIELDS_OF_PROMOTION_PRODUCT_MAP.put(PromotionTypeEnum.CombineFixedPrice.getValue(), productNullFieldsForCombineFixedPrice);

        Set<String> productNullFieldsForCombineDerateMoney = Sets.newHashSet();
        productNullFieldsForCombineDerateMoney.add(PromotionProductConstants.Field.PriceDiscount.apiName);
        productNullFieldsForCombineDerateMoney.add(PromotionProductConstants.Field.FixedPrice.apiName);
        TYPE_TO_NULL_FIELDS_OF_PROMOTION_PRODUCT_MAP.put(PromotionTypeEnum.CombineDerateMoney.getValue(), productNullFieldsForCombineDerateMoney);

        Set<String> productNullFieldsForCombineDiscount = Sets.newHashSet();
        productNullFieldsForCombineDiscount.add(PromotionProductConstants.Field.DerateMoney.apiName);
        productNullFieldsForCombineDiscount.add(PromotionProductConstants.Field.FixedPrice.apiName);
        TYPE_TO_NULL_FIELDS_OF_PROMOTION_PRODUCT_MAP.put(PromotionTypeEnum.CombineDiscount.getValue(), productNullFieldsForCombineDiscount);

        Set<String> productNullFieldsForPriceDiscount = Sets.newHashSet();
        productNullFieldsForPriceDiscount.add(PromotionProductConstants.Field.GiftType.apiName);
        productNullFieldsForPriceDiscount.add(PromotionProductConstants.Field.GiftProduct.apiName);
        productNullFieldsForPriceDiscount.add(PromotionProductConstants.Field.GiftProductNum.apiName);
        productNullFieldsForPriceDiscount.add(PromotionProductConstants.Field.DerateMoney.apiName);
        productNullFieldsForPriceDiscount.add(PromotionProductConstants.Field.FixedPrice.apiName);
        TYPE_TO_NULL_FIELDS_OF_PROMOTION_PRODUCT_MAP.put(PromotionTypeEnum.PriceDiscount.getValue(), productNullFieldsForPriceDiscount);

        Set<String> productNullFieldsForDerateMoney = Sets.newHashSet();
        productNullFieldsForDerateMoney.add(PromotionProductConstants.Field.GiftType.apiName);
        productNullFieldsForDerateMoney.add(PromotionProductConstants.Field.GiftProduct.apiName);
        productNullFieldsForDerateMoney.add(PromotionProductConstants.Field.GiftProductNum.apiName);
        productNullFieldsForDerateMoney.add(PromotionProductConstants.Field.PriceDiscount.apiName);
        productNullFieldsForDerateMoney.add(PromotionProductConstants.Field.GiftType.apiName);
        TYPE_TO_NULL_FIELDS_OF_PROMOTION_PRODUCT_MAP.put(PromotionTypeEnum.DerateMoney.getValue(), productNullFieldsForDerateMoney);

        Set<String> productNullFieldsForFixedPrice = Sets.newHashSet();
        productNullFieldsForFixedPrice.add(PromotionProductConstants.Field.GiftType.apiName);
        productNullFieldsForFixedPrice.add(PromotionProductConstants.Field.GiftProduct.apiName);
        productNullFieldsForFixedPrice.add(PromotionProductConstants.Field.GiftProductNum.apiName);
        productNullFieldsForFixedPrice.add(PromotionProductConstants.Field.PriceDiscount.apiName);
        productNullFieldsForFixedPrice.add(PromotionProductConstants.Field.DerateMoney.apiName);
        TYPE_TO_NULL_FIELDS_OF_PROMOTION_PRODUCT_MAP.put(PromotionTypeEnum.FixedPrice.getValue(), productNullFieldsForFixedPrice);

        Set<String> productNullFieldsForNumberReachedGift = Sets.newHashSet();
        productNullFieldsForNumberReachedGift.add(PromotionProductConstants.Field.PriceDiscount.apiName);
        productNullFieldsForNumberReachedGift.add(PromotionProductConstants.Field.DerateMoney.apiName);
        productNullFieldsForNumberReachedGift.add(PromotionProductConstants.Field.FixedPrice.apiName);
        TYPE_TO_NULL_FIELDS_OF_PROMOTION_PRODUCT_MAP.put(PromotionTypeEnum.NumberReachedGift.getValue(), productNullFieldsForNumberReachedGift);

    }
}
