package com.facishare.crm.promotion.predefine.manager;

import com.facishare.crm.promotion.predefine.service.ProductPromotionService;
import com.facishare.crm.promotion.predefine.service.PromotionService;
import com.facishare.crm.promotion.predefine.service.dto.PromotionProductModel;
import com.facishare.crm.promotion.predefine.service.dto.PromotionType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: dongzhb
 * @date: 2019/11/22
 * @Description:
 */
@Slf4j
@Component
public class PromotionProductManager {

    @Autowired
    private PromotionConfigManager  promotionConfigManager;

    @Autowired
    private ProductPromotionService productPromotionService;

    /**
     * 监听CPQ开启后，适配促销
     *   验证是否开通促销，促销适配CPQ
     *
     * @param tenantId
     *
     * **/
    public  PromotionProductModel.Result verifyPromotionOpenStatusAndAdaptiveCPQ(String tenantId){
        PromotionType.PromotionSwitchEnum promotionStatus = promotionConfigManager.getPromotionStatus(tenantId);
        if(promotionStatus.status != PromotionType.PromotionSwitchEnum.OPENED.status){
            log.warn("tenantId:{} is not open promotion ",tenantId);
                    return  new PromotionProductModel.Result();
        };
        PromotionProductModel.Result result = productPromotionService.updatePromotionProductAdaptiveCPQ(tenantId);
        return  result;
    }



}
