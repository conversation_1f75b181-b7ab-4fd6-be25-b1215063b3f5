package com.facishare.crm.promotion.predefine.controller;

import com.facishare.crmcommon.manager.CustomerRangeManager;
import com.facishare.crm.promotion.constants.PromotionConstants;
import com.facishare.paas.appframework.core.predef.controller.StandardNewDetailController;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.Objects;

public class AdvertisementNewDetailController extends StandardNewDetailController {
    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        if (Objects.nonNull(result.getData())) {
            CustomerRangeManager customerRangeManager = SpringUtil.getContext().getBean(CustomerRangeManager.class);
            customerRangeManager.packData(controllerContext.getUser(), result.getData(), PromotionConstants.Field.CustomerRange.apiName);
        }
        return result;
    }
}
