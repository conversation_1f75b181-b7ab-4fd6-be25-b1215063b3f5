package com.facishare.crm.promotion.predefine.controller;

import com.facishare.crm.promotion.util.PromotionUtil;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;

public class PromotionWebDetailController extends StandardWebDetailController {
    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        PromotionUtil.promotionWebDetail(controllerContext.getUser(), arg, result);
        return result;
    }
}
