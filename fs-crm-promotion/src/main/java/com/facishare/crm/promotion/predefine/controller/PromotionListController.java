package com.facishare.crm.promotion.predefine.controller;

import com.facishare.crm.promotion.constants.PromotionConstants;
import com.facishare.crm.promotion.enums.PromotionRuleTypeEnum;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;

import java.util.List;

/**
 *
 * <AUTHOR>
 * Created on 2019/3/25.
 */
public class PromotionListController extends StandardListController {

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        List<ObjectDataDocument> objectDatas = result.getDataList();
        objectDatas.forEach(o -> {
            String ruleType = o.toObjectData().get(PromotionConstants.Field.RuleType.apiName, String.class);
            if (PromotionRuleTypeEnum.Custom.getValue().equals(ruleType)) {
                o.put(PromotionConstants.Field.Condition.apiName, "--");
                o.put(PromotionConstants.Field.Type.apiName, "--");
            }
        });
        return result;
    }

}
