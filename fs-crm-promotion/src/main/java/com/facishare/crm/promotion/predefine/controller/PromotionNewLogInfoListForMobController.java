package com.facishare.crm.promotion.predefine.controller;

import com.facishare.crm.promotion.constants.PromotionConstants;
import com.facishare.crm.promotion.predefine.manager.PromotionManager;
import com.facishare.crmcommon.manager.CustomerRangeManager;
import com.facishare.paas.appframework.core.predef.controller.StandardNewLogInfoListForMobController;
import com.facishare.paas.appframework.core.predef.service.dto.log.LogRecord;
import com.facishare.paas.appframework.log.dto.ModifyRecord;
import com.facishare.paas.metadata.util.SpringUtil;

/**
 * <AUTHOR>
 * @date 2019/11/11
 */
public class PromotionNewLogInfoListForMobController extends StandardNewLogInfoListForMobController {

    @Override
    protected LogRecord modifyRecordToLogRecord(ModifyRecord record) {
        CustomerRangeManager customerRangeManager = SpringUtil.getContext().getBean(CustomerRangeManager.class);
        LogRecord logRecord = super.modifyRecordToLogRecord(record);
        return customerRangeManager.modifyRecordLog(controllerContext.getUser(), PromotionConstants.API_NAME, logRecord);
    }
}
