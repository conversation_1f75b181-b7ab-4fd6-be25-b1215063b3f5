package com.facishare.crm.promotion.predefine.controller;

import com.facishare.crmcommon.manager.CustomerRangeManager;
import com.facishare.crm.promotion.constants.OrderAgreementConstants;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.metadata.util.SpringUtil;

public class OrderAgreementWebDetailController extends StandardWebDetailController {

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        if (!controllerContext.getUser().isOutUser() && !serviceFacade.isAdmin(controllerContext.getUser())) {
            throw new ValidateException("Must Crm Admin");
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        CustomerRangeManager customerRangeManager = SpringUtil.getContext().getBean(CustomerRangeManager.class);
        customerRangeManager.packData(controllerContext.getUser(), result.getData(), OrderAgreementConstants.Field.CustomerRange.apiName);
        return result;
    }
}
