package com.facishare.crm.promotion.predefine.controller;

import com.facishare.crm.promotion.constants.AdvertisementConstants;
import com.facishare.crm.promotion.predefine.manager.PromotionManager;
import com.facishare.crmcommon.manager.CustomerRangeManager;
import com.facishare.paas.appframework.core.predef.controller.StandardNewLogInfoListForMobController;
import com.facishare.paas.appframework.core.predef.service.dto.log.LogRecord;
import com.facishare.paas.appframework.log.dto.ModifyRecord;
import com.facishare.paas.metadata.util.SpringUtil;

/**
 * <AUTHOR>
 * @date 2019/11/12
 */
public class AdvertisementNewLogInfoListForMobController extends StandardNewLogInfoListForMobController {
    @Override
    protected LogRecord modifyRecordToLogRecord(ModifyRecord record) {
        CustomerRangeManager customerRangeManager = SpringUtil.getContext().getBean(CustomerRangeManager.class);
        LogRecord logRecord = super.modifyRecordToLogRecord(record);
        return customerRangeManager.modifyRecordLog(controllerContext.getUser(), AdvertisementConstants.API_NAME, logRecord);
    }
}
